// Diagnostic script to check Node.js environment and output handling

// Test basic output
console.log('=== Basic Output Test ===');
console.log('Hello from diagnostic script');
console.log(`Current directory: ${process.cwd()}`);

// Test error output
console.error('=== Error Output Test ===');
console.error('This is an error message');

// Test environment
console.log('\n=== Environment Information ===');
console.log(`Node.js version: ${process.version}`);
console.log(`Platform: ${process.platform} (${process.arch})`);
console.log(`Current user: ${process.env.USER || process.env.USERNAME || 'unknown'}`);

// Test file system access
const fs = require('fs');
console.log('\n=== File System Test ===');
const files = fs.readdirSync('.');
console.log(`Found ${files.length} files in current directory`);
console.log('First 5 files:', files.slice(0, 5).join(', '));

// Test async operation
console.log('\n=== Async Operation Test ===');
setTimeout(() => {
  console.log('Async operation completed after 1 second');
  
  // Test process exit
  console.log('\n=== Process Exit Test ===');
  console.log('Exiting with code 0');
  process.exit(0);
}, 1000);

// Keep process alive
console.log('Diagnostic script running...');
