import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Switch } from '../../components/ui/switch';
import { Label } from '../../components/ui/label';
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '../../components/ui/tabs';
import { Bell, BellOff, Mail, MessageSquare, Check, X, AlertTriangle, Info } from 'lucide-react';
import { Badge } from '../../components/ui/badge';

type Notification = {
  id: string;
  title: string;
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
  date: string;
  read: boolean;
  action?: {
    label: string;
    onClick: () => void;
  };
};

const Notifications = () => {
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: '1',
      title: 'Payment Received',
      message: 'You have received a payment of $150.00 from <PERSON>',
      type: 'success',
      date: '2023-06-15T14:30:00Z',
      read: false,
      action: {
        label: 'View Transaction',
        onClick: () => console.log('View transaction')
      }
    },
    {
      id: '2',
      title: 'Payout Processing',
      message: 'Your payout of $1,250.00 is being processed',
      type: 'info',
      date: '2023-06-14T09:15:00Z',
      read: false
    },
    {
      id: '3',
      title: 'Failed Payment',
      message: 'A payment of $75.00 from Jane Smith has failed',
      type: 'error',
      date: '2023-06-13T16:45:00Z',
      read: true,
      action: {
        label: 'Retry Payment',
        onClick: () => console.log('Retry payment')
      }
    },
    {
      id: '4',
      title: 'New Feature Available',
      message: 'Check out our new reporting dashboard with enhanced analytics',
      type: 'info',
      date: '2023-06-12T11:20:00Z',
      read: true
    },
    {
      id: '5',
      title: 'Security Alert',
      message: 'A new device has logged into your account from New York, NY',
      type: 'warning',
      date: '2023-06-10T18:30:00Z',
      read: true
    }
  ]);

  const [emailNotifications, setEmailNotifications] = useState({
    paymentReceived: true,
    payoutProcessed: true,
    failedPayments: true,
    securityAlerts: true,
    productUpdates: false,
    marketing: false,
  });

  const [inAppNotifications, setInAppNotifications] = useState({
    paymentReceived: true,
    payoutProcessed: true,
    failedPayments: true,
    securityAlerts: true,
    productUpdates: true,
  });

  const markAsRead = (id: string) => {
    setNotifications(notifications.map(notification => 
      notification.id === id ? { ...notification, read: true } : notification
    ));
  };

  const markAllAsRead = () => {
    setNotifications(notifications.map(notification => ({
      ...notification,
      read: true
    })));
  };

  const deleteNotification = (id: string) => {
    setNotifications(notifications.filter(n => n.id !== id));
  };

  const clearAll = () => {
    setNotifications([]);
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <Check className="h-5 w-5 text-green-500" />;
      case 'error':
        return <X className="h-5 w-5 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      default:
        return <Info className="h-5 w-5 text-blue-500" />;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  const unreadCount = notifications.filter(n => !n.read).length;
  const allNotifications = [...notifications].sort((a, b) => 
    new Date(b.date).getTime() - new Date(a.date).getTime()
  );
  const unreadNotifications = allNotifications.filter(n => !n.read);
  const readNotifications = allNotifications.filter(n => n.read);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Notifications</h2>
          <p className="text-muted-foreground">
            Manage your notification preferences
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={markAllAsRead} disabled={unreadCount === 0}>
            Mark all as read
          </Button>
          <Button variant="outline" size="sm" onClick={clearAll} disabled={notifications.length === 0}>
            Clear all
          </Button>
        </div>
      </div>

      <Tabs defaultValue="all">
        <TabsList>
          <TabsTrigger value="all">
            All
            {unreadCount > 0 && (
              <Badge variant="secondary" className="ml-2">
                {unreadCount}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="unread">Unread</TabsTrigger>
          <TabsTrigger value="read">Read</TabsTrigger>
          <TabsTrigger value="settings">Notification Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4">
          <Card>
            <CardContent className="p-0">
              {allNotifications.length === 0 ? (
                <div className="flex flex-col items-center justify-center p-8 text-center">
                  <BellOff className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium">No notifications</h3>
                  <p className="text-sm text-muted-foreground">
                    You don't have any notifications yet.
                  </p>
                </div>
              ) : (
                <div className="divide-y">
                  {allNotifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={`p-4 hover:bg-muted/50 transition-colors ${
                        !notification.read ? 'bg-muted/30' : ''
                      }`}
                    >
                      <div className="flex items-start">
                        <div className="flex-shrink-0 pt-0.5">
                          {getNotificationIcon(notification.type)}
                        </div>
                        <div className="ml-3 flex-1">
                          <div className="flex items-center justify-between">
                            <h4 className="font-medium">{notification.title}</h4>
                            <div className="flex items-center space-x-2">
                              <span className="text-xs text-muted-foreground">
                                {formatDate(notification.date)}
                              </span>
                              {!notification.read && (
                                <span className="h-2 w-2 rounded-full bg-primary"></span>
                              )}
                            </div>
                          </div>
                          <p className="text-sm text-muted-foreground mt-1">
                            {notification.message}
                          </p>
                          {notification.action && (
                            <Button
                              variant="link"
                              size="sm"
                              className="h-auto p-0 mt-1 text-sm"
                              onClick={notification.action.onClick}
                            >
                              {notification.action.label}
                            </Button>
                          )}
                        </div>
                        <div className="ml-4 flex-shrink-0 flex">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => deleteNotification(notification.id)}
                          >
                            <X className="h-4 w-4" />
                            <span className="sr-only">Delete</span>
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="unread" className="space-y-4">
          <Card>
            <CardContent className="p-0">
              {unreadNotifications.length === 0 ? (
                <div className="flex flex-col items-center justify-center p-8 text-center">
                  <Bell className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium">No unread notifications</h3>
                  <p className="text-sm text-muted-foreground">
                    You're all caught up!
                  </p>
                </div>
              ) : (
                <div className="divide-y">
                  {unreadNotifications.map((notification) => (
                    <div key={notification.id} className="p-4 bg-muted/30 hover:bg-muted/50 transition-colors">
                      <div className="flex items-start">
                        <div className="flex-shrink-0 pt-0.5">
                          {getNotificationIcon(notification.type)}
                        </div>
                        <div className="ml-3 flex-1">
                          <div className="flex items-center justify-between">
                            <h4 className="font-medium">{notification.title}</h4>
                            <span className="text-xs text-muted-foreground">
                              {formatDate(notification.date)}
                            </span>
                          </div>
                          <p className="text-sm text-muted-foreground mt-1">
                            {notification.message}
                          </p>
                          {notification.action && (
                            <Button
                              variant="link"
                              size="sm"
                              className="h-auto p-0 mt-1 text-sm"
                              onClick={notification.action.onClick}
                            >
                              {notification.action.label}
                            </Button>
                          )}
                        </div>
                        <div className="ml-4 flex-shrink-0 flex space-x-1">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => markAsRead(notification.id)}
                          >
                            <Check className="h-4 w-4" />
                            <span className="sr-only">Mark as read</span>
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => deleteNotification(notification.id)}
                          >
                            <X className="h-4 w-4" />
                            <span className="sr-only">Delete</span>
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="read" className="space-y-4">
          <Card>
            <CardContent className="p-0">
              {readNotifications.length === 0 ? (
                <div className="flex flex-col items-center justify-center p-8 text-center">
                  <BellOff className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium">No read notifications</h3>
                  <p className="text-sm text-muted-foreground">
                    You don't have any read notifications.
                  </p>
                </div>
              ) : (
                <div className="divide-y">
                  {readNotifications.map((notification) => (
                    <div key={notification.id} className="p-4 hover:bg-muted/50 transition-colors">
                      <div className="flex items-start">
                        <div className="flex-shrink-0 pt-0.5 opacity-50">
                          {getNotificationIcon(notification.type)}
                        </div>
                        <div className="ml-3 flex-1">
                          <div className="flex items-center justify-between">
                            <h4 className="font-medium text-muted-foreground">{notification.title}</h4>
                            <span className="text-xs text-muted-foreground">
                              {formatDate(notification.date)}
                            </span>
                          </div>
                          <p className="text-sm text-muted-foreground mt-1">
                            {notification.message}
                          </p>
                          {notification.action && (
                            <Button
                              variant="link"
                              size="sm"
                              className="h-auto p-0 mt-1 text-sm"
                              onClick={notification.action.onClick}
                            >
                              {notification.action.label}
                            </Button>
                          )}
                        </div>
                        <div className="ml-4 flex-shrink-0">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => deleteNotification(notification.id)}
                          >
                            <X className="h-4 w-4" />
                            <span className="sr-only">Delete</span>
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Notification Preferences</CardTitle>
              <p className="text-sm text-muted-foreground">
                Choose how you receive notifications
              </p>
            </CardHeader>
            <CardContent>
              <div className="space-y-8">
                <div>
                  <h3 className="text-lg font-medium mb-4 flex items-center">
                    <Mail className="h-5 w-5 mr-2 text-muted-foreground" />
                    Email Notifications
                  </h3>
                  <div className="space-y-4">
                    {Object.entries({
                      paymentReceived: 'Payment received',
                      payoutProcessed: 'Payout processed',
                      failedPayments: 'Failed payments',
                      securityAlerts: 'Security alerts',
                      productUpdates: 'Product updates',
                      marketing: 'Marketing communications'
                    }).map(([key, label]) => (
                      <div key={key} className="flex items-center justify-between">
                        <Label htmlFor={`email-${key}`} className="font-normal">
                          {label}
                        </Label>
                        <Switch
                          id={`email-${key}`}
                          checked={emailNotifications[key as keyof typeof emailNotifications]}
                          onCheckedChange={(checked) =>
                            setEmailNotifications(prev => ({
                              ...prev,
                              [key]: checked
                            }))
                          }
                        />
                      </div>
                    ))}
                  </div>
                </div>

                <div className="border-t pt-6">
                  <h3 className="text-lg font-medium mb-4 flex items-center">
                    <MessageSquare className="h-5 w-5 mr-2 text-muted-foreground" />
                    In-App Notifications
                  </h3>
                  <div className="space-y-4">
                    {Object.entries({
                      paymentReceived: 'Payment received',
                      payoutProcessed: 'Payout processed',
                      failedPayments: 'Failed payments',
                      securityAlerts: 'Security alerts',
                      productUpdates: 'Product updates'
                    }).map(([key, label]) => (
                      <div key={key} className="flex items-center justify-between">
                        <Label htmlFor={`inapp-${key}`} className="font-normal">
                          {label}
                        </Label>
                        <Switch
                          id={`inapp-${key}`}
                          checked={inAppNotifications[key as keyof typeof inAppNotifications]}
                          onCheckedChange={(checked) =>
                            setInAppNotifications(prev => ({
                              ...prev,
                              [key]: checked
                            }))
                          }
                        />
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Notifications;
