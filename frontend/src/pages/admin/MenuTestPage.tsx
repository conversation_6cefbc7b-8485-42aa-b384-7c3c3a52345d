import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';
import DynamicMenu from '../../components/DynamicMenu';
import SimpleDynamicMenu from '../../components/SimpleDynamicMenu';
import { MenuItem } from '../../services/menuService';

const MenuTestPage = () => {
  const [selectedRole, setSelectedRole] = useState<'admin' | 'merchant' | 'trader'>('admin');
  const [useSimpleMenu, setUseSimpleMenu] = useState(false);
  const [lastClickedMenu, setLastClickedMenu] = useState<MenuItem | null>(null);

  const handleMenuClick = (menuItem: MenuItem) => {
    setLastClickedMenu(menuItem);
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col space-y-6">
        <div>
          <h1 className="text-2xl font-bold">Dynamic Menu Testing</h1>
          <p className="text-muted-foreground">Test the dynamic menu system with different roles and configurations</p>
        </div>

        {/* Controls */}
        <Card>
          <CardHeader>
            <CardTitle>Menu Configuration</CardTitle>
            <CardDescription>Configure the menu display options</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-4 items-center">
              <div className="flex-1">
                <label className="text-sm font-medium">User Role:</label>
                <Select value={selectedRole} onValueChange={(value: 'admin' | 'merchant' | 'trader') => setSelectedRole(value)}>
                  <SelectTrigger className="w-full mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="admin">Admin</SelectItem>
                    <SelectItem value="merchant">Merchant</SelectItem>
                    <SelectItem value="trader">Trader</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex-1">
                <label className="text-sm font-medium">Menu Type:</label>
                <div className="mt-1 space-x-2">
                  <Button
                    variant={!useSimpleMenu ? "default" : "outline"}
                    size="sm"
                    onClick={() => setUseSimpleMenu(false)}
                  >
                    Full Menu
                  </Button>
                  <Button
                    variant={useSimpleMenu ? "default" : "outline"}
                    size="sm"
                    onClick={() => setUseSimpleMenu(true)}
                  >
                    Simple Menu
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Menu Display */}
          <Card>
            <CardHeader>
              <CardTitle>
                {useSimpleMenu ? 'Simple' : 'Full'} Dynamic Menu - {selectedRole.charAt(0).toUpperCase() + selectedRole.slice(1)}
              </CardTitle>
              <CardDescription>
                {useSimpleMenu 
                  ? 'Simplified menu without collapsible functionality'
                  : 'Full-featured menu with collapsible submenus'
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="border rounded-lg p-4 bg-gray-50 min-h-[400px]">
                {useSimpleMenu ? (
                  <SimpleDynamicMenu 
                    role={selectedRole} 
                    onMenuClick={handleMenuClick}
                  />
                ) : (
                  <DynamicMenu 
                    role={selectedRole} 
                    onMenuClick={handleMenuClick}
                  />
                )}
              </div>
            </CardContent>
          </Card>

          {/* Menu Information */}
          <Card>
            <CardHeader>
              <CardTitle>Menu Information</CardTitle>
              <CardDescription>Details about the current menu configuration</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-medium">Current Configuration:</h4>
                <ul className="text-sm text-muted-foreground mt-2 space-y-1">
                  <li>• Role: <span className="font-medium">{selectedRole}</span></li>
                  <li>• Menu Type: <span className="font-medium">{useSimpleMenu ? 'Simple' : 'Full'}</span></li>
                  <li>• API Source: <span className="font-medium">Real API with Mock Fallback</span></li>
                </ul>
              </div>

              {lastClickedMenu && (
                <div>
                  <h4 className="font-medium">Last Clicked Menu:</h4>
                  <div className="text-sm text-muted-foreground mt-2 space-y-1">
                    <div>• Label: <span className="font-medium">{lastClickedMenu.label}</span></div>
                    <div>• Path: <span className="font-medium">{lastClickedMenu.to}</span></div>
                    <div>• Icon: <span className="font-medium">{lastClickedMenu.icon}</span></div>
                    <div>• Role: <span className="font-medium">{lastClickedMenu.role}</span></div>
                    <div>• Order: <span className="font-medium">{lastClickedMenu.order}</span></div>
                    {lastClickedMenu.description && (
                      <div>• Description: <span className="font-medium">{lastClickedMenu.description}</span></div>
                    )}
                    {lastClickedMenu.metadata?.category && (
                      <div>• Category: <span className="font-medium">{lastClickedMenu.metadata.category}</span></div>
                    )}
                  </div>
                </div>
              )}

              <div>
                <h4 className="font-medium">Menu Features:</h4>
                <ul className="text-sm text-muted-foreground mt-2 space-y-1">
                  <li>✅ Role-based menu filtering</li>
                  <li>✅ Dynamic API data loading</li>
                  <li>✅ Mock data fallback</li>
                  <li>✅ Icon integration</li>
                  <li>✅ Badge system</li>
                  <li>✅ Active state detection</li>
                  <li>✅ Loading states</li>
                  <li>✅ Error handling</li>
                  {!useSimpleMenu && <li>✅ Collapsible submenus</li>}
                </ul>
              </div>

              <div>
                <h4 className="font-medium">API Endpoints:</h4>
                <ul className="text-sm text-muted-foreground mt-2 space-y-1">
                  <li>• GET /api/menus/role/{selectedRole}</li>
                  <li>• GET /api/menus (all menus)</li>
                  <li>• POST /api/menus (create)</li>
                  <li>• PUT /api/menus/:id (update)</li>
                  <li>• DELETE /api/menus/:id (delete)</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Menu Statistics */}
        <Card>
          <CardHeader>
            <CardTitle>Menu System Statistics</CardTitle>
            <CardDescription>Overview of the menu system data</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold">22</div>
                <div className="text-sm text-muted-foreground">Total Menus</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">11</div>
                <div className="text-sm text-muted-foreground">Admin Menus</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">6</div>
                <div className="text-sm text-muted-foreground">Merchant Menus</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">5</div>
                <div className="text-sm text-muted-foreground">Trader Menus</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default MenuTestPage;
