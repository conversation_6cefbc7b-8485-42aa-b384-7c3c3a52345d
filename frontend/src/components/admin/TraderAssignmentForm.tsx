import { useEffect, useState, ReactNode } from 'react';
import { 
  useForm, 
  useWatch, 
  Controller, 
  useFormContext, 
  SubmitHandler, 
  FieldValues, 
  Control,
  FieldPath,
  FieldPathValue,
  ControllerProps
} from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { format, parseISO } from 'date-fns';
import { z } from 'zod';
import { toast } from '../ui/use-toast';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Checkbox } from '../ui/checkbox';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '../ui/form';
import { useCreateTraderAssignment } from '../../hooks/useTraderAssignments';
import { Merchant, User } from '../../types/trader';

// Define the enums as const arrays for type safety
export const AssignmentTypeValues = ['permanent', 'temporary', 'project_based'] as const;
export const CollectionPeriodValues = ['daily', 'weekly', 'monthly', 'quarterly'] as const;

// Create type from the const arrays
type AssignmentType = typeof AssignmentTypeValues[number];
type CollectionPeriod = typeof CollectionPeriodValues[number];

// Create zod enums from the values using type assertion
const AssignmentTypeZod = z.enum(AssignmentTypeValues as unknown as [string, string, string]);
const CollectionPeriodZod = z.enum(CollectionPeriodValues as unknown as [string, string, string, string]);

const assignmentSchema = z.object({
  traderId: z.string().min(1, 'Trader is required'),
  merchantId: z.string().min(1, 'Merchant is required'),
  assignmentType: AssignmentTypeZod,
  startDate: z.string().optional().transform(str => str ? new Date(str) : null),
  endDate: z.string().optional().transform(str => str ? new Date(str) : null),
  collectionTarget: z.object({
    amount: z.number().min(0, 'Amount must be positive'),
    period: CollectionPeriodZod,
    currency: z.string().default('USD'),
  }).optional().default(undefined as any),
  permissions: z.array(z.string()).min(1, 'At least one permission is required'),
  notes: z.string().optional(),
});

type AssignmentFormData = z.infer<typeof assignmentSchema>;

const PERMISSIONS = [
  { id: 'view_transactions', label: 'View Transactions' },
  { id: 'process_payments', label: 'Process Payments' },
  { id: 'generate_reports', label: 'Generate Reports' },
  { id: 'contact_customers', label: 'Contact Customers' },
];

const getPeriodLabel = (period: CollectionPeriod): string => {
  switch (period) {
    case 'daily': return 'Daily';
    case 'weekly': return 'Weekly';
    case 'monthly': return 'Monthly';
    case 'quarterly': return 'Quarterly';
    default: return String(period);
  }
};

const formatDateForInput = (date: string | Date | null | undefined): string => {
  if (!date) return '';
  try {
    const dateObj = date instanceof Date ? date : new Date(date);
    return isNaN(dateObj.getTime()) ? '' : format(dateObj, 'yyyy-MM-dd');
  } catch (e) {
    return '';
  }
};

export function TraderAssignmentForm({ onSuccess }: { onSuccess?: () => void }) {
  // Define types for trader and merchant
  type User = { id: string; name: string; email: string };
  type Merchant = { id: string; name: string; email: string };

  // Mock data for traders and merchants - replace with actual API calls
  const [traders] = useState<User[]>([
    { id: '1', name: 'Trader 1', email: '<EMAIL>' },
    { id: '2', name: 'Trader 2', email: '<EMAIL>' },
  ]);
  
  const [merchants] = useState<Merchant[]>([
    { id: '1', name: 'Merchant 1', email: '<EMAIL>' },
    { id: '2', name: 'Merchant 2', email: '<EMAIL>' },
  ]);
  
  // Mock toast function - replace with actual toast implementation
  const toast = (options: { title: string; description: string; variant?: 'default' | 'destructive' }) => {
    console.log(options);
  };
  
  // Use the actual hook or mock implementation
  const createAssignment = useCreateTraderAssignment?.() || {
    mutateAsync: async (data: AssignmentFormData) => {
      console.log('Creating assignment:', data);
      return Promise.resolve();
    },
    isLoading: false,
    isError: false,
    error: null
  };

  // Define the form data type
  type FormData = {
    traderId: string;
    merchantId: string;
    assignmentType: AssignmentType;
    startDate?: string;
    endDate?: string;
    collectionTarget?: {
      amount: number;
      period: CollectionPeriod;
      currency: string;
    } | null;
    permissions: string[];
    notes?: string;
  };
  // Default form values with proper typing
  const defaultValues: FormData = {
    traderId: '',
    merchantId: '',
    assignmentType: 'permanent',
    startDate: undefined,
    endDate: undefined,
    collectionTarget: {
      amount: 0,
      period: 'monthly',
      currency: 'USD'
    },
    permissions: [],
    notes: ''
  };

  const methods = useForm<FormData>({
    resolver: zodResolver(assignmentSchema as any),
    defaultValues: {
      traderId: '',
      merchantId: '',
      assignmentType: 'temporary',
      permissions: [],
      collectionTarget: {
        amount: 0,
        period: 'monthly',
        currency: 'USD',
      },
      startDate: '',
      endDate: '',
      notes: '',
    },
  });

  const { 
    control, 
    handleSubmit, 
    reset, 
    watch, 
    setValue, 
    formState: { errors } 
  } = methods;

  // Custom Controller component with proper type handling
  const FormController = <TFieldValues extends FieldValues = FormData, TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>>(
    props: ControllerProps<TFieldValues, TName> & { control: Control<TFieldValues> }
  ) => {
    return <Controller {...props} />;
  };

  useEffect(() => {
    reset(defaultValues);
  }, [reset]);
  
  const onCancel = () => {
    reset(defaultValues);
    onSuccess?.();
  };

  const assignmentType = useWatch({
    control,
    name: 'assignmentType',
    defaultValue: 'permanent' as const
  });
  
  const isTemporary = assignmentType === 'temporary';

  const onSubmit: SubmitHandler<FormData> = async (formData) => {
    try {
      // Prepare collection target data with proper typing
      const collectionTarget = formData.collectionTarget
        ? {
            amount: Number(formData.collectionTarget.amount) || 0,
            period: formData.collectionTarget.period,
            currency: formData.collectionTarget.currency || 'USD'
          }
        : undefined;
      
      // Prepare the final data object with proper typing and type assertions
      const formattedData: AssignmentFormData = {
        traderId: formData.traderId,
        merchantId: formData.merchantId,
        assignmentType: formData.assignmentType as AssignmentType,
        startDate: formData.startDate ? new Date(formData.startDate) : null,
        endDate: formData.endDate ? new Date(formData.endDate) : null,
        collectionTarget: {
          amount: Number(collectionTarget?.amount) || 0,
          period: (collectionTarget?.period as CollectionPeriod) || 'monthly',
          currency: collectionTarget?.currency || 'USD'
        },
        permissions: Array.isArray(formData.permissions) ? formData.permissions : [],
        notes: formData.notes || ''
      };
      
      await createAssignment.mutateAsync(formattedData);
      toast({
        title: 'Success',
        description: 'Assignment created successfully',
      });
      reset(defaultValues);
      onSuccess?.();
    } catch (error) {
      console.error('Error creating assignment:', error);
      toast({
        title: 'Error',
        description: 'Failed to create assignment',
        variant: 'destructive',
      });
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Trader Selection */}
        <div className="space-y-2">
          <Label htmlFor="traderId">Trader *</Label>
          <FormController
            name="traderId"
            control={control}
            render={({ field }) => (
              <Select
                value={field.value}
                onValueChange={(value: string) => field.onChange(value)}
                defaultValue={field.value}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select trader" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {traders.map((trader: User) => (
                    <SelectItem key={trader.id} value={trader.id}>
                      {trader.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
          {errors.traderId && (
            <p className="text-sm text-red-500">{errors.traderId.message}</p>
          )}
        </div>

        {/* Merchant Selection */}
        <div className="space-y-2">
          <Label htmlFor="merchantId">Merchant *</Label>
          <FormController
            name="merchantId"
            control={control}
            render={({ field }) => (
              <Select
                value={field.value}
                onValueChange={(value: string) => field.onChange(value)}
                defaultValue={field.value}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select merchant" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {merchants.map((merchant: Merchant) => (
                    <SelectItem key={merchant.id} value={merchant.id}>
                      {merchant.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
          {errors.merchantId && (
            <p className="text-sm text-red-500">{errors.merchantId.message}</p>
          )}
        </div>

        {/* Assignment Type */}
        <div className="space-y-2">
          <Label htmlFor="assignmentType">Assignment Type *</Label>
          <FormController
            name="assignmentType"
            control={control}
            render={({ field }) => (
              <Select
                value={field.value}
                onValueChange={(value: string) => field.onChange(value)}
                defaultValue={field.value}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select assignment type" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {AssignmentTypeValues.map((type: string) => (
                    <SelectItem key={type} value={type}>
                      {String(type).replace('_', ' ')}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
        </div>

        {/* Start Date */}
        <div className="space-y-2">
          <Label htmlFor="startDate">Start Date *</Label>
          <FormController
            name="startDate"
            control={control}
            render={({ field }) => (
              <Input
                type="date"
                placeholder="Start date"
                value={field.value || ''}
                onChange={(e) => {
                  setValue('startDate', e.target.value, { shouldValidate: true });
                }}
                min={format(new Date(), 'yyyy-MM-dd')}
              />
            )}
          />
          {errors.startDate && (
            <p className="text-sm text-red-500">{errors.startDate.message}</p>
          )}
        </div>

        {/* Collection Target */}
        <div className="space-y-2">
          <Label>Collection Target (Optional)</Label>
          <div className="flex space-x-2">
            <Input
              type="number"
              placeholder="Amount"
              value={watch('collectionTarget.amount') || ''}
              onChange={(e) => {
                const amount = Number(e.target.value);
                const currentTarget = watch('collectionTarget') || { amount: 0, currency: 'USD', period: 'monthly' };
                setValue('collectionTarget', {
                  ...currentTarget,
                  amount: isNaN(amount) ? 0 : amount,
                  currency: currentTarget.currency || 'USD',
                  period: currentTarget.period || 'monthly'
                }, { shouldValidate: true });
              }}
            />
            <Select
              value={watch('collectionTarget.period') || 'monthly'}
              onValueChange={(value) => {
                const currentTarget = watch('collectionTarget') || { amount: 0, currency: 'USD', period: 'monthly' };
                setValue('collectionTarget', {
                  ...currentTarget,
                  period: value as 'daily' | 'weekly' | 'monthly' | 'quarterly',
                  currency: currentTarget.currency || 'USD'
                }, { shouldValidate: true });
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="Period" />
              </SelectTrigger>
              <SelectContent>
                {CollectionPeriodValues.map((period: CollectionPeriod) => (
                  <SelectItem key={period} value={period}>
                    {getPeriodLabel(period)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select
              value={watch('collectionTarget.currency') || 'USD'}
              onValueChange={(value) => {
                const currentTarget = watch('collectionTarget') || { amount: 0, currency: 'USD', period: 'monthly' };
                setValue('collectionTarget', {
                  ...currentTarget,
                  currency: value
                }, { shouldValidate: true });
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="Currency" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="USD">USD</SelectItem>
                <SelectItem value="EUR">EUR</SelectItem>
                <SelectItem value="GBP">GBP</SelectItem>
              </SelectContent>
            </Select>
          </div>
          {errors.collectionTarget?.amount && (
            <p className="text-sm text-red-500">{errors.collectionTarget.amount.message}</p>
          )}
        </div>

        {/* End Date (Conditional) */}
        {isTemporary && (
          <div className="space-y-2">
            <Label htmlFor="endDate">End Date *</Label>
            <FormController
              name="endDate"
              control={control}
              render={({ field }) => (
                <Input
                  type="date"
                  placeholder="End date"
                  value={formatDateForInput(watch('endDate'))}
                  onChange={(e) => {
                    const date = e.target.value ? new Date(e.target.value) : null;
                    setValue('endDate', date ? date.toISOString() : '', { shouldValidate: true });
                  }}
                  min={format(new Date(), 'yyyy-MM-dd')}
                />
              )}
            />
            {errors.endDate && (
              <p className="text-sm text-red-500">{errors.endDate.message}</p>
            )}
          </div>
        )}

        {/* Permissions */}
        <div className="space-y-2">
          <Label>Permissions *</Label>
          <FormController
            name="permissions"
            control={control}
            render={({ field }) => (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {PERMISSIONS.map((permission) => (
                  <div key={permission.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={permission.id}
                      checked={field.value?.includes(permission.id)}
                      onCheckedChange={(checked: boolean) => {
                        const currentPermissions = Array.isArray(field.value) ? field.value : [];
                        const newPermissions = checked
                          ? [...currentPermissions, permission.id]
                          : currentPermissions.filter((p: string) => p !== permission.id);
                        setValue('permissions', newPermissions as string[], { shouldValidate: true });
                      }}
                    />
                    <Label htmlFor={permission.id} className="text-sm font-normal">
                      {permission.label}
                    </Label>
                  </div>
                ))}
              </div>
            )}
          />
          {errors.permissions && (
            <p className="text-sm text-red-500">{errors.permissions.message}</p>
          )}
        </div>
      </div>

      <div className="flex justify-end space-x-4">
        <Button 
          type="button" 
          variant="outline" 
          onClick={onCancel} 
          disabled={createAssignment.isPending}
        >
          Cancel
        </Button>
        <Button 
          type="submit" 
          disabled={createAssignment.isPending}
          className="bg-blue-600 hover:bg-blue-700"
        >
          {createAssignment.isPending ? 'Creating...' : 'Create Assignment'}
        </Button>
      </div>
    </form>
  );
}

// In the formattedData:
const formattedData: AssignmentFormData = {
  traderId: formData.traderId,
  merchantId: formData.merchantId,
  assignmentType: formData.assignmentType as AssignmentType,
  startDate: formData.startDate ? parseISO(formData.startDate) : null,
  endDate: formData.endDate ? parseISO(formData.endDate) : null,
  collectionTarget: formData.collectionTarget ? {
    amount: Number(formData.collectionTarget.amount),
    period: formData.collectionTarget.period as CollectionPeriod,
    currency: formData.collectionTarget.currency
  } : undefined,
  permissions: formData.permissions,
  notes: formData.notes
};
