import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import { MoreHorizontal, Plus } from 'lucide-react';
import { Button } from '../ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/table';
import { Badge } from '../ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button as UIButton } from '../ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '../ui/dropdown-menu';
import { useTraderAssignments, useDeleteTraderAssignment } from '../../hooks/useTraderAssignments';
import { useToast } from '../ui/use-toast';
import { TraderAssignment, User, Merchant } from '../../types/trader';

// Type guard to check if an object is a User
const isUser = (obj: any): obj is User => {
  return obj && typeof obj === 'object' && 'name' in obj && 'email' in obj;
};

// Type guard to check if an object is a Merchant
const isMerchant = (obj: any): obj is Merchant => {
  return obj && typeof obj === 'object' && 'businessName' in obj;
};

type Status = 'active' | 'inactive' | 'suspended' | 'completed';

const statusVariant: Record<Status, 'default' | 'secondary' | 'destructive' | 'outline'> = {
  active: 'default',
  inactive: 'outline',
  suspended: 'destructive',
  completed: 'secondary',
};

export function TraderAssignmentsList() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [showForm, setShowForm] = useState(false);
  const { 
    assignments: assignmentsData = [], 
    isLoading, 
    error 
  } = useTraderAssignments({});
  const deleteAssignment = useDeleteTraderAssignment();
  
  const assignments = Array.isArray(assignmentsData) ? assignmentsData : [];

  const handleDelete = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this assignment?')) {
      try {
        await deleteAssignment.mutateAsync(id);
        toast({
          title: 'Success',
          description: 'Assignment deleted successfully',
        });
      } catch (err) {
        const error = err as Error & { response?: { data?: { message?: string } } };
        toast({
          title: 'Error',
          description: error.response?.data?.message || 'Failed to delete assignment',
          variant: 'destructive',
        });
      }
    }
  };

  if (isLoading) return <div>Loading assignments...</div>;
  
  if (error) {
    const errorMessage = error instanceof Error 
      ? error.message 
      : typeof error === 'string' 
        ? error 
        : 'Unknown error';
    return <div>Error loading assignments: {errorMessage}</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Trader Assignments</h2>
          <p className="text-muted-foreground">Manage trader assignments to merchants</p>
        </div>
        <Button onClick={() => navigate('/admin/trader-assignments/new')}>
          <Plus className="mr-2 h-4 w-4" /> New Assignment
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Current Assignments</CardTitle>
          <CardDescription>View and manage all trader assignments</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Trader</TableHead>
                <TableHead>Merchant</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Start Date</TableHead>
                <TableHead>End Date</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="w-[100px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {assignments.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                    No assignments found
                  </TableCell>
                </TableRow>
              ) : (
                assignments.map((assignment: TraderAssignment) => (
                  <TableRow key={assignment._id}>
                    <TableCell className="font-medium">
                      <div className="space-y-1">
                        <p>{
                          typeof assignment.trader === 'string' 
                            ? assignment.trader 
                            : isUser(assignment.trader) 
                              ? assignment.trader.name 
                              : 'N/A'
                        }</p>
                        <p className="text-sm text-muted-foreground">{
                          typeof assignment.trader === 'string' 
                            ? 'N/A' 
                            : isUser(assignment.trader) 
                              ? assignment.trader.email 
                              : 'N/A'
                        }</p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <p>{
                          typeof assignment.merchant === 'string' 
                            ? assignment.merchant 
                            : isMerchant(assignment.merchant) 
                              ? assignment.merchant.businessName 
                              : 'N/A'
                        }</p>
                        <p className="text-sm text-muted-foreground">{
                          typeof assignment.merchant === 'string' 
                            ? 'N/A' 
                            : isMerchant(assignment.merchant) 
                              ? assignment.merchant.email 
                              : 'N/A'
                        }</p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className="capitalize">
                        {assignment.assignmentType.replace('_', ' ')}
                      </Badge>
                    </TableCell>
                    <TableCell>{format(new Date(assignment.startDate), 'MMM d, yyyy')}</TableCell>
                    <TableCell>
                      {assignment.endDate ? format(new Date(assignment.endDate), 'MMM d, yyyy') : 'N/A'}
                    </TableCell>
                    <TableCell>
                      <Badge variant={statusVariant[assignment.status as Status]} className="capitalize">
                        {assignment.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() => navigate(`/admin/trader-assignments/${assignment._id}`)}
                          >
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => navigate(`/admin/trader-assignments/${assignment._id}/edit`)}
                          >
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="text-red-600"
                            onClick={() => handleDelete(assignment._id)}
                          >
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
