"use strict";(self.webpackChunkpayment_gateway_frontend=self.webpackChunkpayment_gateway_frontend||[]).push([[147],{399:(e,s,a)=>{a.d(s,{A:()=>t});const t=(0,a(3797).A)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},9147:(e,s,a)=>{a.r(s),a.d(s,{default:()=>v});var t=a(5043),i=a(6742),l=a(9772),n=a(1508),c=a(2248),r=a(6736),d=a(1024),m=a(1172),o=a(3797);const x=(0,o.A)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),h=(0,o.A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]),u=(0,o.A)("BellOff",[["path",{d:"M8.7 3A6 6 0 0 1 18 8a21.3 21.3 0 0 0 .6 5",key:"o7mx20"}],["path",{d:"M17 17H3s3-2 3-9a4.67 4.67 0 0 1 .3-1.7",key:"16f1lm"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);var j=a(8283),p=a(9589),f=a(399),y=a(8567),N=a(579);const v=()=>{const[e,s]=(0,t.useState)([{id:"1",title:"Payment Received",message:"You have received a payment of $150.00 from John Doe",type:"success",date:"2023-06-15T14:30:00Z",read:!1,action:{label:"View Transaction",onClick:()=>console.log("View transaction")}},{id:"2",title:"Payout Processing",message:"Your payout of $1,250.00 is being processed",type:"info",date:"2023-06-14T09:15:00Z",read:!1},{id:"3",title:"Failed Payment",message:"A payment of $75.00 from Jane Smith has failed",type:"error",date:"2023-06-13T16:45:00Z",read:!0,action:{label:"Retry Payment",onClick:()=>console.log("Retry payment")}},{id:"4",title:"New Feature Available",message:"Check out our new reporting dashboard with enhanced analytics",type:"info",date:"2023-06-12T11:20:00Z",read:!0},{id:"5",title:"Security Alert",message:"A new device has logged into your account from New York, NY",type:"warning",date:"2023-06-10T18:30:00Z",read:!0}]),[a,o]=(0,t.useState)({paymentReceived:!0,payoutProcessed:!0,failedPayments:!0,securityAlerts:!0,productUpdates:!1,marketing:!1}),[v,g]=(0,t.useState)({paymentReceived:!0,payoutProcessed:!0,failedPayments:!0,securityAlerts:!0,productUpdates:!0}),k=a=>{s(e.filter(e=>e.id!==a))},w=e=>{switch(e){case"success":return(0,N.jsx)(d.A,{className:"h-5 w-5 text-green-500"});case"error":return(0,N.jsx)(m.A,{className:"h-5 w-5 text-red-500"});case"warning":return(0,N.jsx)(x,{className:"h-5 w-5 text-yellow-500"});default:return(0,N.jsx)(h,{className:"h-5 w-5 text-blue-500"})}},b=e=>{const s=new Date(e);return new Intl.DateTimeFormat("en-US",{month:"short",day:"numeric",year:"numeric",hour:"2-digit",minute:"2-digit"}).format(s)},A=e.filter(e=>!e.read).length,C=[...e].sort((e,s)=>new Date(s.date).getTime()-new Date(e.date).getTime()),P=C.filter(e=>!e.read),$=C.filter(e=>e.read);return(0,N.jsxs)("div",{className:"space-y-6",children:[(0,N.jsxs)("div",{className:"flex items-center justify-between",children:[(0,N.jsxs)("div",{children:[(0,N.jsx)("h2",{className:"text-2xl font-bold tracking-tight",children:"Notifications"}),(0,N.jsx)("p",{className:"text-muted-foreground",children:"Manage your notification preferences"})]}),(0,N.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,N.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>{s(e.map(e=>({...e,read:!0})))},disabled:0===A,children:"Mark all as read"}),(0,N.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>{s([])},disabled:0===e.length,children:"Clear all"})]})]}),(0,N.jsxs)(r.tU,{defaultValue:"all",children:[(0,N.jsxs)(r.j7,{children:[(0,N.jsxs)(r.Xi,{value:"all",children:["All",A>0&&(0,N.jsx)(y.E,{variant:"secondary",className:"ml-2",children:A})]}),(0,N.jsx)(r.Xi,{value:"unread",children:"Unread"}),(0,N.jsx)(r.Xi,{value:"read",children:"Read"}),(0,N.jsx)(r.Xi,{value:"settings",children:"Notification Settings"})]}),(0,N.jsx)(r.av,{value:"all",className:"space-y-4",children:(0,N.jsx)(i.Zp,{children:(0,N.jsx)(i.Wu,{className:"p-0",children:0===C.length?(0,N.jsxs)("div",{className:"flex flex-col items-center justify-center p-8 text-center",children:[(0,N.jsx)(u,{className:"h-12 w-12 text-muted-foreground mb-4"}),(0,N.jsx)("h3",{className:"text-lg font-medium",children:"No notifications"}),(0,N.jsx)("p",{className:"text-sm text-muted-foreground",children:"You don't have any notifications yet."})]}):(0,N.jsx)("div",{className:"divide-y",children:C.map(e=>(0,N.jsx)("div",{className:"p-4 hover:bg-muted/50 transition-colors "+(e.read?"":"bg-muted/30"),children:(0,N.jsxs)("div",{className:"flex items-start",children:[(0,N.jsx)("div",{className:"flex-shrink-0 pt-0.5",children:w(e.type)}),(0,N.jsxs)("div",{className:"ml-3 flex-1",children:[(0,N.jsxs)("div",{className:"flex items-center justify-between",children:[(0,N.jsx)("h4",{className:"font-medium",children:e.title}),(0,N.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,N.jsx)("span",{className:"text-xs text-muted-foreground",children:b(e.date)}),!e.read&&(0,N.jsx)("span",{className:"h-2 w-2 rounded-full bg-primary"})]})]}),(0,N.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:e.message}),e.action&&(0,N.jsx)(l.$,{variant:"link",size:"sm",className:"h-auto p-0 mt-1 text-sm",onClick:e.action.onClick,children:e.action.label})]}),(0,N.jsx)("div",{className:"ml-4 flex-shrink-0 flex",children:(0,N.jsxs)(l.$,{variant:"ghost",size:"icon",className:"h-8 w-8",onClick:()=>k(e.id),children:[(0,N.jsx)(m.A,{className:"h-4 w-4"}),(0,N.jsx)("span",{className:"sr-only",children:"Delete"})]})})]})},e.id))})})})}),(0,N.jsx)(r.av,{value:"unread",className:"space-y-4",children:(0,N.jsx)(i.Zp,{children:(0,N.jsx)(i.Wu,{className:"p-0",children:0===P.length?(0,N.jsxs)("div",{className:"flex flex-col items-center justify-center p-8 text-center",children:[(0,N.jsx)(j.A,{className:"h-12 w-12 text-muted-foreground mb-4"}),(0,N.jsx)("h3",{className:"text-lg font-medium",children:"No unread notifications"}),(0,N.jsx)("p",{className:"text-sm text-muted-foreground",children:"You're all caught up!"})]}):(0,N.jsx)("div",{className:"divide-y",children:P.map(a=>(0,N.jsx)("div",{className:"p-4 bg-muted/30 hover:bg-muted/50 transition-colors",children:(0,N.jsxs)("div",{className:"flex items-start",children:[(0,N.jsx)("div",{className:"flex-shrink-0 pt-0.5",children:w(a.type)}),(0,N.jsxs)("div",{className:"ml-3 flex-1",children:[(0,N.jsxs)("div",{className:"flex items-center justify-between",children:[(0,N.jsx)("h4",{className:"font-medium",children:a.title}),(0,N.jsx)("span",{className:"text-xs text-muted-foreground",children:b(a.date)})]}),(0,N.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:a.message}),a.action&&(0,N.jsx)(l.$,{variant:"link",size:"sm",className:"h-auto p-0 mt-1 text-sm",onClick:a.action.onClick,children:a.action.label})]}),(0,N.jsxs)("div",{className:"ml-4 flex-shrink-0 flex space-x-1",children:[(0,N.jsxs)(l.$,{variant:"ghost",size:"icon",className:"h-8 w-8",onClick:()=>{return t=a.id,void s(e.map(e=>e.id===t?{...e,read:!0}:e));var t},children:[(0,N.jsx)(d.A,{className:"h-4 w-4"}),(0,N.jsx)("span",{className:"sr-only",children:"Mark as read"})]}),(0,N.jsxs)(l.$,{variant:"ghost",size:"icon",className:"h-8 w-8",onClick:()=>k(a.id),children:[(0,N.jsx)(m.A,{className:"h-4 w-4"}),(0,N.jsx)("span",{className:"sr-only",children:"Delete"})]})]})]})},a.id))})})})}),(0,N.jsx)(r.av,{value:"read",className:"space-y-4",children:(0,N.jsx)(i.Zp,{children:(0,N.jsx)(i.Wu,{className:"p-0",children:0===$.length?(0,N.jsxs)("div",{className:"flex flex-col items-center justify-center p-8 text-center",children:[(0,N.jsx)(u,{className:"h-12 w-12 text-muted-foreground mb-4"}),(0,N.jsx)("h3",{className:"text-lg font-medium",children:"No read notifications"}),(0,N.jsx)("p",{className:"text-sm text-muted-foreground",children:"You don't have any read notifications."})]}):(0,N.jsx)("div",{className:"divide-y",children:$.map(e=>(0,N.jsx)("div",{className:"p-4 hover:bg-muted/50 transition-colors",children:(0,N.jsxs)("div",{className:"flex items-start",children:[(0,N.jsx)("div",{className:"flex-shrink-0 pt-0.5 opacity-50",children:w(e.type)}),(0,N.jsxs)("div",{className:"ml-3 flex-1",children:[(0,N.jsxs)("div",{className:"flex items-center justify-between",children:[(0,N.jsx)("h4",{className:"font-medium text-muted-foreground",children:e.title}),(0,N.jsx)("span",{className:"text-xs text-muted-foreground",children:b(e.date)})]}),(0,N.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:e.message}),e.action&&(0,N.jsx)(l.$,{variant:"link",size:"sm",className:"h-auto p-0 mt-1 text-sm",onClick:e.action.onClick,children:e.action.label})]}),(0,N.jsx)("div",{className:"ml-4 flex-shrink-0",children:(0,N.jsxs)(l.$,{variant:"ghost",size:"icon",className:"h-8 w-8",onClick:()=>k(e.id),children:[(0,N.jsx)(m.A,{className:"h-4 w-4"}),(0,N.jsx)("span",{className:"sr-only",children:"Delete"})]})})]})},e.id))})})})}),(0,N.jsx)(r.av,{value:"settings",className:"space-y-6",children:(0,N.jsxs)(i.Zp,{children:[(0,N.jsxs)(i.aR,{children:[(0,N.jsx)(i.ZB,{children:"Notification Preferences"}),(0,N.jsx)("p",{className:"text-sm text-muted-foreground",children:"Choose how you receive notifications"})]}),(0,N.jsx)(i.Wu,{children:(0,N.jsxs)("div",{className:"space-y-8",children:[(0,N.jsxs)("div",{children:[(0,N.jsxs)("h3",{className:"text-lg font-medium mb-4 flex items-center",children:[(0,N.jsx)(p.A,{className:"h-5 w-5 mr-2 text-muted-foreground"}),"Email Notifications"]}),(0,N.jsx)("div",{className:"space-y-4",children:Object.entries({paymentReceived:"Payment received",payoutProcessed:"Payout processed",failedPayments:"Failed payments",securityAlerts:"Security alerts",productUpdates:"Product updates",marketing:"Marketing communications"}).map(e=>{let[s,t]=e;return(0,N.jsxs)("div",{className:"flex items-center justify-between",children:[(0,N.jsx)(c.J,{htmlFor:`email-${s}`,className:"font-normal",children:t}),(0,N.jsx)(n.d,{id:`email-${s}`,checked:a[s],onCheckedChange:e=>o(a=>({...a,[s]:e}))})]},s)})})]}),(0,N.jsxs)("div",{className:"border-t pt-6",children:[(0,N.jsxs)("h3",{className:"text-lg font-medium mb-4 flex items-center",children:[(0,N.jsx)(f.A,{className:"h-5 w-5 mr-2 text-muted-foreground"}),"In-App Notifications"]}),(0,N.jsx)("div",{className:"space-y-4",children:Object.entries({paymentReceived:"Payment received",payoutProcessed:"Payout processed",failedPayments:"Failed payments",securityAlerts:"Security alerts",productUpdates:"Product updates"}).map(e=>{let[s,a]=e;return(0,N.jsxs)("div",{className:"flex items-center justify-between",children:[(0,N.jsx)(c.J,{htmlFor:`inapp-${s}`,className:"font-normal",children:a}),(0,N.jsx)(n.d,{id:`inapp-${s}`,checked:v[s],onCheckedChange:e=>g(a=>({...a,[s]:e}))})]},s)})})]})]})})]})})]})]})}},9589:(e,s,a)=>{a.d(s,{A:()=>t});const t=(0,a(3797).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])}}]);
//# sourceMappingURL=147.2553b00e.chunk.js.map