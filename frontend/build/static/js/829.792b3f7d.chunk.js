"use strict";(self.webpackChunkpayment_gateway_frontend=self.webpackChunkpayment_gateway_frontend||[]).push([[829],{656:(e,r,t)=>{t.d(r,{u:()=>i});var a=t(6742),s=t(9772),n=t(3216),o=t(579);function i(e){let{title:r,description:t="This page is under construction and will be available soon."}=e;const i=(0,n.Zp)();return(0,o.jsx)("div",{className:"container mx-auto py-8",children:(0,o.jsxs)(a.Zp,{children:[(0,o.jsx)(a.aR,{children:(0,o.jsx)(a.ZB,{className:"text-2xl font-bold",children:r})}),(0,o.jsxs)(a<PERSON>,{className:"space-y-4",children:[(0,o.jsx)("p",{className:"text-muted-foreground",children:t}),(0,o.jsxs)("div",{className:"flex space-x-4",children:[(0,o.jsx)(s.$,{onClick:()=>i(-1),variant:"outline",children:"Go Back"}),(0,o.jsx)(s.$,{onClick:()=>i("/admin"),children:"Return to Dashboard"})]})]})]})})}},3829:(e,r,t)=>{t.r(r),t.d(r,{SettingsPage:()=>n,default:()=>o});var a=t(656),s=t(579);function n(){return(0,s.jsx)(a.u,{title:"System Settings",description:"Configure system settings, manage integrations, and update platform preferences."})}const o=n},6742:(e,r,t)=>{t.d(r,{BT:()=>c,Wu:()=>l,ZB:()=>d,Zp:()=>o,aR:()=>i,wL:()=>u});var a=t(5043),s=t(3009),n=t(579);const o=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("div",{ref:r,className:(0,s.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...a})});o.displayName="Card";const i=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("div",{ref:r,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",t),...a})});i.displayName="CardHeader";const d=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("h3",{ref:r,className:(0,s.cn)("text-2xl font-semibold leading-none tracking-tight",t),...a})});d.displayName="CardTitle";const c=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("p",{ref:r,className:(0,s.cn)("text-sm text-muted-foreground",t),...a})});c.displayName="CardDescription";const l=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("div",{ref:r,className:(0,s.cn)("p-6 pt-0",t),...a})});l.displayName="CardContent";const u=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("div",{ref:r,className:(0,s.cn)("flex items-center p-6 pt-0",t),...a})});u.displayName="CardFooter"},9772:(e,r,t)=>{t.d(r,{$:()=>c});var a=t(5043),s=t(6851),n=t(917),o=t(3009),i=t(579);const d=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,r)=>{let{className:t,variant:a,size:n,asChild:c=!1,...l}=e;const u=c?s.DX:"button";return(0,i.jsx)(u,{className:(0,o.cn)(d({variant:a,size:n,className:t})),ref:r,...l})});c.displayName="Button"}}]);
//# sourceMappingURL=829.792b3f7d.chunk.js.map