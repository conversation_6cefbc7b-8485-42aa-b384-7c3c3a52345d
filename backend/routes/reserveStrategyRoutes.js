const express = require('express');
const router = express.Router();
const reserveStrategyController = require('../controllers/reserveStrategyController');
const { protect, authorize } = require('../middleware/auth');

// Apply authentication to all routes
router.use(protect);

// Apply authorization based on route requirements
router.use((req, res, next) => {
  // Public routes (no specific role required)
  if (req.path === '/activities' && req.method === 'GET') {
    return next();
  }
  
  // Admin/Finance routes
  if (!req.user.roles.some(role => ['admin', 'finance'].includes(role))) {
    return res.status(403).json({
      success: false,
      message: 'Not authorized to access this route'
    });
  }
  
  next();
});

/**
 * @swagger
 * /api/reserve-strategies:
 *   post:
 *     tags: [Reserve Strategies]
 *     summary: Create a new reserve strategy
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ReserveStrategy'
 *     responses:
 *       201:
 *         description: Reserve strategy created successfully
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - requires admin or finance role
 */
router.post('/', reserveStrategyController.createReserveStrategy);

/**
 * @swagger
 * /api/reserve-strategies:
 *   get:
 *     tags: [Reserve Strategies]
 *     summary: Get all reserve strategies
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of reserve strategies
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/ReserveStrategy'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - requires admin or finance role
 */
router.get('/', reserveStrategyController.getReserveStrategies);

/**
 * @swagger
 * /api/reserve-strategies/{id}:
 *   get:
 *     tags: [Reserve Strategies]
 *     summary: Get a single reserve strategy
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Reserve strategy ID
 *     responses:
 *       200:
 *         description: Reserve strategy data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ReserveStrategy'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - requires admin or finance role
 *       404:
 *         description: Reserve strategy not found
 */
router.get('/:id', reserveStrategyController.getReserveStrategy);

/**
 * @swagger
 * /api/reserve-strategies/{id}:
 *   put:
 *     tags: [Reserve Strategies]
 *     summary: Update a reserve strategy
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Reserve strategy ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ReserveStrategy'
 *     responses:
 *       200:
 *         description: Reserve strategy updated successfully
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - requires admin or finance role
 *       404:
 *         description: Reserve strategy not found
 */
router.put('/:id', reserveStrategyController.updateReserveStrategy);

/**
 * @swagger
 * /api/reserve-strategies/{id}:
 *   delete:
 *     tags: [Reserve Strategies]
 *     summary: Delete a reserve strategy
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Reserve strategy ID
 *     responses:
 *       200:
 *         description: Reserve strategy deleted successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - requires admin or finance role
 *       404:
 *         description: Reserve strategy not found
 */
router.delete('/:id', reserveStrategyController.deleteReserveStrategy);

/**
 * @swagger
 * /api/reserve-strategies/calculate:
 *   post:
 *     tags: [Reserve Strategies]
 *     summary: Calculate reserve strategy
 *     description: Calculate reserve amounts based on the specified strategy and parameters
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - strategyId
 *               - amount
 *             properties:
 *               strategyId:
 *                 type: string
 *                 description: ID of the reserve strategy to use for calculation
 *               amount:
 *                 type: number
 *                 description: Base amount to calculate reserves for
 *               parameters:
 *                 type: object
 *                 description: Additional parameters specific to the strategy
 *     responses:
 *       200:
 *         description: Reserve calculation successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     reserveAmount:
 *                       type: number
 *                       description: Calculated reserve amount
 *                     baseAmount:
 *                       type: number
 *                       description: Original base amount
 *                     strategy:
 *                       type: string
 *                       description: Name of the strategy used
 *       400:
 *         description: Invalid input data
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       403:
 *         $ref: '#/components/responses/Forbidden'
 *       404:
 *         description: Strategy not found
 *       500:
 *         description: Internal server error
 */
router.post('/calculate', reserveStrategyController.calculateReserve);

/**
 * @swagger
 * /api/reserve-strategies/activities:
 *   get:
 *     tags: [Reserve Strategies]
 *     summary: Get activities for reserve strategies
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of reserve strategy activities
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/ReserveStrategyActivity'
 *       401:
 *         description: Unauthorized
 */
router.get('/activities', reserveStrategyController.getActivities);

/**
 * @swagger
 * /api/reserve-strategies/activities/{id}:
 *   get:
 *     tags: [Reserve Strategies]
 *     summary: Get a single activity for a reserve strategy
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Activity ID
 *     responses:
 *       200:
 *         description: Reserve strategy activity data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ReserveStrategyActivity'
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Activity not found
 */
router.get('/activities/:id', reserveStrategyController.getActivity);

/**
 * @swagger
 * /api/reserve-strategies/activities/{id}/review:
 *   post:
 *     tags: [Reserve Strategies]
 *     summary: Review a reserve strategy activity
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Activity ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [status, comments]
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [approved, rejected]
 *                 description: Review status
 *               comments:
 *                 type: string
 *                 description: Review comments
 *     responses:
 *       200:
 *         description: Activity reviewed successfully
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - requires admin or finance role
 *       404:
 *         description: Activity not found
 */
router.post('/activities/:id/review', authorize('admin', 'finance'), reserveStrategyController.reviewActivity);

/**
 * @swagger
 * /api/reserve-strategies/{id}/activities:
 *   get:
 *     tags: [Reserve Strategies]
 *     summary: Get activities for a specific reserve strategy
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Reserve strategy ID
 *     responses:
 *       200:
 *         description: List of activities for the reserve strategy
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/ReserveStrategyActivity'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - requires admin or finance role
 *       404:
 *         description: Reserve strategy not found
 */
router.get('/:id/activities', reserveStrategyController.getStrategyActivities);

/**
 * @swagger
 * /api/reserve-strategies/check-status:
 *   post:
 *     tags: [Reserve Strategies]
 *     summary: Check status of reserve strategies
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [strategyIds]
 *             properties:
 *               strategyIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Array of reserve strategy IDs to check
 *     responses:
 *       200:
 *         description: Status check completed
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                     description: Reserve strategy ID
 *                   status:
 *                     type: string
 *                     description: Current status of the strategy
 *                   lastChecked:
 *                     type: string
 *                     format: date-time
 *                     description: When the status was last checked
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - requires admin or finance role
 */
router.post('/check-status', reserveStrategyController.checkStatus);

module.exports = router;
