import http from './httpService';
import { ApiResponse } from '../types/api';

interface DashboardStats {
  totalRevenue: number;
  activeMerchants: number;
  totalTransactions: number;
  activeTraders: number;
  revenueTrend: number;
  transactionTrend: number;
}

interface RecentTransaction {
  id: string;
  merchantName: string;
  amount: number;
  date: string;
  status: 'completed' | 'pending' | 'failed';
}

interface DashboardStatsParams {
  timeRange?: 'week' | 'month' | 'year';
}

const adminService = {
  // Get dashboard statistics
  async getDashboardStats(params?: DashboardStatsParams): Promise<DashboardStats | undefined> {
    const response = await http.get<ApiResponse<DashboardStats>>('/admin/dashboard/stats', { params });
    return response.data.data;
  },

  // Get recent transactions
  async getRecentTransactions(limit: number = 5): Promise<RecentTransaction[] | undefined> {
    const response = await http.get<ApiResponse<RecentTransaction[]>>('/admin/transactions/recent', {
      params: { limit }
    });
    return response.data.data;
  },

  // Get merchant statistics
  async getMerchantStats(merchantId: string) {
    const response = await http.get<ApiResponse<any>>(`/admin/merchants/${merchantId}/stats`);
    return response.data.data;
  },

  // Get system status
  async getSystemStatus() {
    const response = await http.get<ApiResponse<{
      services: Array<{
        name: string;
        status: 'operational' | 'degraded' | 'outage';
        responseTime: number;
        lastChecked: string;
      }>;
    }>>('/admin/system/status');
    return response.data.data;
  },

  // Process payouts
  async processPayouts(merchantIds: string[]) {
    const response = await http.post<ApiResponse<{ success: boolean; message?: string }>>(
      '/admin/payouts/process',
      { merchantIds }
    );
    return response.data;
  },

  // Generate report
  async generateReport(params: {
    type: 'transactions' | 'merchants' | 'traders';
    startDate: string;
    endDate: string;
    format: 'csv' | 'pdf' | 'xlsx';
  }) {
    const response = await http.get('/admin/reports/generate', {
      params,
      responseType: 'blob',
    });
    return response.data;
  },

  // Get activity log
  async getActivityLog(params: {
    page?: number;
    limit?: number;
    type?: string;
    userId?: string;
    startDate?: string;
    endDate?: string;
  }) {
    const response = await http.get<ApiResponse<{
      data: Array<{
        id: string;
        action: string;
        entityType: string;
        entityId: string;
        userId: string;
        userEmail: string;
        metadata: Record<string, any>;
        createdAt: string;
      }>;
      total: number;
      page: number;
      limit: number;
      totalPages: number;
    }>>('/admin/activity-log', { params });
    return response.data;
  },
};

export default adminService;
