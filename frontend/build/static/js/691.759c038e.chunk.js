"use strict";(self.webpackChunkpayment_gateway_frontend=self.webpackChunkpayment_gateway_frontend||[]).push([[691],{656:(e,r,a)=>{a.d(r,{u:()=>d});var t=a(6742),s=a(9772),n=a(3216),o=a(579);function d(e){let{title:r,description:a="This page is under construction and will be available soon."}=e;const d=(0,n.Zp)();return(0,o.jsx)("div",{className:"container mx-auto py-8",children:(0,o.jsxs)(t.Zp,{children:[(0,o.jsx)(t.aR,{children:(0,o.jsx)(t.ZB,{className:"text-2xl font-bold",children:r})}),(0,o.jsxs)(t.<PERSON>,{className:"space-y-4",children:[(0,o.jsx)("p",{className:"text-muted-foreground",children:a}),(0,o.jsxs)("div",{className:"flex space-x-4",children:[(0,o.jsx)(s.$,{onClick:()=>d(-1),variant:"outline",children:"Go Back"}),(0,o.jsx)(s.$,{onClick:()=>d("/admin"),children:"Return to Dashboard"})]})]})]})})}},6742:(e,r,a)=>{a.d(r,{BT:()=>c,Wu:()=>l,ZB:()=>i,Zp:()=>o,aR:()=>d,wL:()=>u});var t=a(5043),s=a(3009),n=a(579);const o=t.forwardRef((e,r)=>{let{className:a,...t}=e;return(0,n.jsx)("div",{ref:r,className:(0,s.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...t})});o.displayName="Card";const d=t.forwardRef((e,r)=>{let{className:a,...t}=e;return(0,n.jsx)("div",{ref:r,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",a),...t})});d.displayName="CardHeader";const i=t.forwardRef((e,r)=>{let{className:a,...t}=e;return(0,n.jsx)("h3",{ref:r,className:(0,s.cn)("text-2xl font-semibold leading-none tracking-tight",a),...t})});i.displayName="CardTitle";const c=t.forwardRef((e,r)=>{let{className:a,...t}=e;return(0,n.jsx)("p",{ref:r,className:(0,s.cn)("text-sm text-muted-foreground",a),...t})});c.displayName="CardDescription";const l=t.forwardRef((e,r)=>{let{className:a,...t}=e;return(0,n.jsx)("div",{ref:r,className:(0,s.cn)("p-6 pt-0",a),...t})});l.displayName="CardContent";const u=t.forwardRef((e,r)=>{let{className:a,...t}=e;return(0,n.jsx)("div",{ref:r,className:(0,s.cn)("flex items-center p-6 pt-0",a),...t})});u.displayName="CardFooter"},8691:(e,r,a)=>{a.r(r),a.d(r,{ReportsPage:()=>n,default:()=>o});var t=a(656),s=a(579);function n(){return(0,s.jsx)(t.u,{title:"Reports & Analytics",description:"Generate and view detailed reports and analytics for the platform."})}const o=n},9772:(e,r,a)=>{a.d(r,{$:()=>c});var t=a(5043),s=a(6851),n=a(917),o=a(3009),d=a(579);const i=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=t.forwardRef((e,r)=>{let{className:a,variant:t,size:n,asChild:c=!1,...l}=e;const u=c?s.DX:"button";return(0,d.jsx)(u,{className:(0,o.cn)(i({variant:t,size:n,className:a})),ref:r,...l})});c.displayName="Button"}}]);
//# sourceMappingURL=691.759c038e.chunk.js.map