const Settlement = require('../models/Settlement');
const CommissionStructure = require('../models/CommissionStructure');
const Transaction = require('../models/Transaction');
const { NotFoundError, ValidationError, ForbiddenError } = require('../utils/errors');
const logger = require('../utils/logger');

class SettlementService {
  /**
   * Check if a user has access to a specific settlement
   * @param {Object} user - The user object
   * @param {Object} settlement - The settlement object
   * @returns {Promise<boolean>} - Whether the user has access
   */
  static async checkUserAccessToSettlement(user, settlement) {
    // Admin and finance users have full access
    if (user.role === 'admin' || user.role === 'finance') {
      return true;
    }

    // Check if user is either the fromParty or toParty
    const isFromParty = settlement.fromParty && 
      (settlement.fromParty._id.toString() === user.id || 
       (settlement.fromParty.user && settlement.fromParty.user.toString() === user.id));
       
    const isToParty = settlement.toParty && 
      (settlement.toParty._id.toString() === user.id || 
       (settlement.toParty.user && settlement.toParty.user.toString() === user.id));
    
    return isFromParty || isToParty;
  }

  /**
   * Get settlement with minimal data for access checking
   * @param {string} id - Settlement ID
   * @returns {Promise<Object>} - Settlement data
   */
  static async getSettlementForAccessCheck(id) {
    if (!id) {
      throw new ValidationError('Settlement ID is required');
    }

    // Use lean() for better performance since we only need to check access
    return await Settlement.findById(id)
      .select('fromParty toParty status')
      .populate('fromParty', 'user')
      .populate('toParty', 'user')
      .lean();
  }

  /**
   * Get settlement by ID with all details
   * @param {string} id - Settlement ID
   * @returns {Promise<Object>} - Settlement with all details
   */
  static async getSettlementById(id) {
    if (!id) {
      throw new ValidationError('Settlement ID is required');
    }

    const settlement = await Settlement.findById(id)
      .populate('fromParty', 'name email phone')
      .populate('toParty', 'name email phone')
      .populate('transactions')
      .populate('createdBy', 'name email')
      .populate('processedBy', 'name email');

    if (!settlement) {
      throw new NotFoundError('Settlement not found');
    }

    return settlement;
  }

  /**
   * Create a new settlement between parties
   */
  static async createSettlement({
    type,
    fromParty,
    toParty,
    periodStart,
    periodEnd,
    createdBy,
    notes,
    paymentMethod = 'bank_transfer'
  }) {
    // Validate period
    if (periodStart >= periodEnd) {
      throw new ValidationError('Invalid period: periodStart must be before periodEnd');
    }

    // Find all transactions in the period
    const transactions = await Transaction.find({
      $or: [
        { merchantId: fromParty, traderId: toParty },
        { merchantId: toParty, traderId: fromParty }
      ],
      createdAt: { $gte: periodStart, $lte: periodEnd },
      status: { $in: ['completed', 'refunded'] },
      settlementId: { $exists: false }
    });

    if (transactions.length === 0) {
      throw new ValidationError('No transactions found for the specified period and parties');
    }

    // Calculate totals
    const totals = transactions.reduce((acc, tx) => {
      const amount = tx.amount || 0;
      const fees = tx.fees || {};
      
      acc.totalAmount += amount;
      acc.processingFees += fees.processingFee || 0;
      acc.platformFees += fees.platformFee || 0;
      
      if (tx.status === 'refunded') {
        acc.refundedAmount += tx.refundAmount || 0;
      }
      
      return acc;
    }, {
      totalAmount: 0,
      processingFees: 0,
      platformFees: 0,
      refundedAmount: 0
    });

    // Calculate commission (we'll implement this next)
    const commission = await this.calculateCommission({
      fromParty,
      toParty,
      transactionType: type.includes('trader') ? 'payout' : 'internal',
      amount: totals.totalAmount
    });

    // Create settlement
    const settlement = new Settlement({
      type,
      fromParty,
      toParty,
      periodStart,
      periodEnd,
      transactions: transactions.map(tx => tx._id),
      totalAmount: totals.totalAmount,
      fees: {
        processing: totals.processingFees,
        platform: totals.platformFees,
        commission: commission.amount
      },
      settlementAmount: totals.totalAmount - (totals.processingFees + totals.platformFees + commission.amount),
      createdBy,
      notes,
      paymentMethod,
      status: 'pending',
      commissionRates: {
        payin: commission.rate || 0,
        payout: commission.rate || 0,
        internal: 0 // Default, can be overridden later
      }
    });

    await settlement.save();

    // Update transactions with settlement ID
    await Transaction.updateMany(
      { _id: { $in: transactions.map(tx => tx._id) } },
      { $set: { settlementId: settlement._id } }
    );

    return settlement;
  }

  /**
   * Calculate commission for a transaction
   */
  static async calculateCommission({
    fromParty,
    toParty,
    transactionType,
    amount,
    fromType = 'trader',
    toType = 'merchant'
  }) {
    // Find applicable commission structure
    const commissionStructure = await CommissionStructure.findApplicable({
      fromType,
      toType,
      transactionType,
      amount,
      fromParty,
      toParty
    });

    if (!commissionStructure) {
      return { amount: 0, rate: 0, structure: null };
    }

    const commissionAmount = commissionStructure.calculateCommission(amount);
    
    return {
      amount: commissionAmount,
      rate: commissionStructure.percentage || 0,
      structure: commissionStructure._id
    };
  }

  /**
   * Process a pending settlement
   */
  static async processSettlement(settlementId, processedBy) {
    const settlement = await Settlement.findById(settlementId);
    
    if (!settlement) {
      throw new NotFoundError('Settlement not found');
    }

    if (settlement.status !== 'pending') {
      throw new ValidationError(`Settlement is already ${settlement.status}`);
    }

    // Here you would typically integrate with your payment processor
    // to transfer the funds from fromParty to toParty
    // This is a placeholder for that logic
    const paymentSuccessful = await this.executePayment({
      from: settlement.fromParty,
      to: settlement.toParty,
      amount: settlement.settlementAmount,
      reference: settlement.settlementId,
      paymentMethod: settlement.paymentMethod
    });

    if (!paymentSuccessful) {
      throw new Error('Payment processing failed');
    }

    // Update settlement status
    settlement.status = 'completed';
    settlement.processedAt = new Date();
    settlement.processedBy = processedBy;
    
    await settlement.save();

    return settlement;
  }

  /**
   * Execute payment (placeholder for payment gateway integration)
   */
  static async executePayment({ from, to, amount, reference, paymentMethod }) {
    // Implement actual payment processing logic here
    // This could integrate with Stripe, PayPal, bank transfers, etc.
    console.log(`Processing payment of ${amount} from ${from} to ${to} (${reference})`);
    
    // Simulate payment processing
    return new Promise(resolve => {
      setTimeout(() => resolve(true), 1000);
    });
  }

  /**
   * Get settlements with filtering and pagination
   */
  static async getSettlements({
    page = 1,
    limit = 10,
    status,
    type,
    fromParty,
    toParty,
    startDate,
    endDate
  } = {}) {
    const query = {};
    
    if (status) query.status = status;
    if (type) query.type = type;
    if (fromParty) query.fromParty = fromParty;
    if (toParty) query.toParty = toParty;
    
    if (startDate || endDate) {
      query.$and = [];
      if (startDate) query.$and.push({ periodStart: { $gte: new Date(startDate) } });
      if (endDate) query.$and.push({ periodEnd: { $lte: new Date(endDate) } });
    }

    const options = {
      page: parseInt(page, 10),
      limit: parseInt(limit, 10),
      sort: { createdAt: -1 },
      populate: [
        { path: 'fromParty', select: 'name email' },
        { path: 'toParty', select: 'name email' },
        { path: 'transactions', select: 'transactionId amount status' }
      ]
    };

    return await Settlement.paginate(query, options);
  }


}

module.exports = SettlementService;
