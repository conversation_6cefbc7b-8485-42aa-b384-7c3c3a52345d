{"version": 3, "file": "static/js/400.a4415629.chunk.js", "mappings": "mOAKA,MAAMA,EAAOC,EAAAA,GAEKC,EAAAA,WAGhB,CAAAC,EAA0BC,KAAG,IAA5B,UAAEC,KAAcC,GAAOH,EAAA,OACxBI,EAAAA,EAAAA,KAACN,EAAAA,GAAmB,CAClBG,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IAAG,YAAaH,MACvBC,MAGEG,YAAc,YAExB,MAAMC,EAAWR,EAAAA,WAGf,CAAAS,EAA0BP,KAAG,IAA5B,UAAEC,KAAcC,GAAOK,EAAA,OACxBJ,EAAAA,EAAAA,KAAA,OAAKH,IAAKA,EAAKC,WAAWG,EAAAA,EAAAA,IAAG,YAAaH,MAAgBC,MAE5DI,EAASD,YAAc,WAEvB,MAAMG,EAAYV,EAAAA,WAKhB,CAAAW,EAA8CT,KAAG,IAAhD,UAAEC,EAAS,SAAES,EAAQ,SAAEC,KAAaT,GAAOO,EAAA,OAC5CN,EAAAA,EAAAA,KAACN,EAAAA,GAAmB,CAClBG,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IACT,6DACAM,GAAY,sDACZT,MAEEC,EAAKS,SAERA,MAGLH,EAAUH,YAAc,YAExB,MAAMO,EAAcd,EAAAA,WAGlB,CAAAe,EAA0Bb,KAAG,IAA5B,UAAEC,KAAcC,GAAOW,EAAA,OACxBV,EAAAA,EAAAA,KAACN,EAAAA,GAAqB,CACpBG,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IACT,+VACAH,MAEEC,MAGRU,EAAYP,YAAc,cAEFP,EAAAA,WAGtB,CAAAgB,EAA0Bd,KAAG,IAA5B,UAAEC,KAAcC,GAAOY,EAAA,OACxBX,EAAAA,EAAAA,KAAA,KACEH,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IAAG,gCAAiCH,MAC3CC,MAGQG,YAAc,kBAE9B,MAAMU,EAAcjB,EAAAA,WAGlB,CAAAkB,EAAoChB,KAAG,IAAtC,UAAEC,EAAS,SAAEU,KAAaT,GAAOc,EAAA,OAClCb,EAAAA,EAAAA,KAACN,EAAAA,GAAqB,CACpBG,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IAAG,uCAAwCH,MAClDC,EAAKS,SAERA,MAGLI,EAAYV,YAAc,a,gHClF1B,MAAMY,EAAQnB,EAAAA,WAGZ,CAAAC,EAA0BC,KAAG,IAA5B,UAAEC,KAAcC,GAAOH,EAAA,OACxBI,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uBAAsBU,UACnCR,EAAAA,EAAAA,KAAA,SACEH,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IAAG,gCAAiCH,MAC3CC,QAIVe,EAAMZ,YAAc,QAEpB,MAAMa,EAAcpB,EAAAA,WAGlB,CAAAS,EAA0BP,KAAG,IAA5B,UAAEC,KAAcC,GAAOK,EAAA,OACxBJ,EAAAA,EAAAA,KAAA,SAAOH,IAAKA,EAAKC,WAAWG,EAAAA,EAAAA,IAAG,kBAAmBH,MAAgBC,MAEpEgB,EAAYb,YAAc,cAE1B,MAAMc,EAAYrB,EAAAA,WAGhB,CAAAW,EAA0BT,KAAG,IAA5B,UAAEC,KAAcC,GAAOO,EAAA,OACxBN,EAAAA,EAAAA,KAAA,SACEH,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IAAG,6BAA8BH,MACxCC,MAGRiB,EAAUd,YAAc,YAEJP,EAAAA,WAGlB,CAAAe,EAA0Bb,KAAG,IAA5B,UAAEC,KAAcC,GAAOW,EAAA,OACxBV,EAAAA,EAAAA,KAAA,SACEH,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IAAG,iDAAkDH,MAC5DC,MAGIG,YAAc,cAE1B,MAAMe,EAAWtB,EAAAA,WAGf,CAAAgB,EAA0Bd,KAAG,IAA5B,UAAEC,KAAcC,GAAOY,EAAA,OACxBX,EAAAA,EAAAA,KAAA,MACEH,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IACT,8EACAH,MAEEC,MAGRkB,EAASf,YAAc,WAEvB,MAAMgB,EAAYvB,EAAAA,WAGhB,CAAAkB,EAA0BhB,KAAG,IAA5B,UAAEC,KAAcC,GAAOc,EAAA,OACxBb,EAAAA,EAAAA,KAAA,MACEH,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IACT,mGACAH,MAEEC,MAGRmB,EAAUhB,YAAc,YAExB,MAAMiB,EAAYxB,EAAAA,WAGhB,CAAAyB,EAA0BvB,KAAG,IAA5B,UAAEC,KAAcC,GAAOqB,EAAA,OACxBpB,EAAAA,EAAAA,KAAA,MACEH,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IAAG,iDAAkDH,MAC5DC,MAGRoB,EAAUjB,YAAc,YAEHP,EAAAA,WAGnB,CAAA0B,EAA0BxB,KAAG,IAA5B,UAAEC,KAAcC,GAAOsB,EAAA,OACxBrB,EAAAA,EAAAA,KAAA,WACEH,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IAAG,qCAAsCH,MAChDC,MAGKG,YAAc,c,6ICjG3B,MAAMoB,EAASC,EAAAA,GAETC,EAAgBD,EAAAA,GAOhBE,EAAe7B,IAIK,IAJJ,UACpBE,EAAS,SACTU,KACGT,GACeH,EAClB,MAAM,UAAE8B,EAAS,WAAEC,KAAeC,GAAc7B,EAC1C8B,EAAc,CAClBH,YACAC,WAAYA,GAEd,OACE3B,EAAAA,EAAAA,KAACuB,EAAAA,GAAsB,IAAKM,EAAWrB,UACrCR,EAAAA,EAAAA,KAAA,OAAKF,WAAWG,EAAAA,EAAAA,IACd,qEACAH,GACAU,SACCA,OAKTiB,EAAavB,YAAcqB,EAAAA,GAAuBrB,YAElD,MAAM4B,EAAgBnC,EAAAA,WAGpB,CAAAS,EAA0BP,KAAG,IAA5B,UAAEC,KAAcC,GAAOK,EAAA,OACxBJ,EAAAA,EAAAA,KAACuB,EAAAA,GAAuB,CACtB1B,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IACT,qKACAH,MAEEC,MAGR+B,EAAc5B,YAAcqB,EAAAA,GAAwBrB,YAEpD,MAAM6B,EAAgBpC,EAAAA,WAGpB,CAAAW,EAAoCT,KAAG,IAAtC,UAAEC,EAAS,SAAEU,KAAaT,GAAOO,EAAA,OAClC0B,EAAAA,EAAAA,MAACP,EAAY,CAAAjB,SAAA,EACXR,EAAAA,EAAAA,KAAC8B,EAAa,KACdE,EAAAA,EAAAA,MAACT,EAAAA,GAAuB,CACtB1B,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IACT,+NACA,mBACAH,MAEEC,EAAKS,SAAA,CAERA,GACDwB,EAAAA,EAAAA,MAACT,EAAAA,GAAqB,CAACzB,UAAU,mTAAkTU,SAAA,EACjVR,EAAAA,EAAAA,KAACiC,EAAAA,EAAC,CAACnC,UAAU,aACbE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,UAASU,SAAC,qBAKlCuB,EAAc7B,YAAcqB,EAAAA,GAAwBrB,YAEpD,MAAMgC,EAAexB,IAAA,IAAC,UACpBZ,KACGC,GACkCW,EAAA,OACrCV,EAAAA,EAAAA,KAAA,OACEF,WAAWG,EAAAA,EAAAA,IACT,mDACAH,MAEEC,KAGRmC,EAAahC,YAAc,eAE3B,MAAMiC,EAAexB,IAAA,IAAC,UACpBb,KACGC,GACkCY,EAAA,OACrCX,EAAAA,EAAAA,KAAA,OACEF,WAAWG,EAAAA,EAAAA,IACT,gEACAH,MAEEC,KAGRoC,EAAajC,YAAc,eAE3B,MAAMkC,EAAczC,EAAAA,WAGlB,CAAAkB,EAA0BhB,KAAG,IAA5B,UAAEC,KAAcC,GAAOc,EAAA,OACxBb,EAAAA,EAAAA,KAACuB,EAAAA,GAAqB,CACpB1B,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IACT,sCACA,oBACAH,MAEEC,MAGRqC,EAAYlC,YAAcqB,EAAAA,GAAsBrB,YAEhD,MAAMmC,EAAoB1C,EAAAA,WAGxB,CAAAyB,EAA0BvB,KAAG,IAA5B,UAAEC,KAAcC,GAAOqB,EAAA,OACxBpB,EAAAA,EAAAA,KAACuB,EAAAA,GAA2B,CAC1B1B,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IAAG,wBAAyB,qBAAsBH,MACzDC,MAGRsC,EAAkBnC,YAAcqB,EAAAA,GAA4BrB,W,sHC9GrD,MAAMoC,EAAoBA,KAC/B,MAAM,KAAEC,IAASC,EAAAA,EAAAA,KACXC,GAAcC,EAAAA,EAAAA,MAEpB,OAAOC,EAAAA,EAAAA,GAA2C,CAChDC,WAAYC,SAWH,IAAIC,QAASC,IAClBC,WAAW,KACTD,EAAQ,CACNE,WAAY,8CACZC,OAAQ,qBAET,OAGPC,UAAWA,KACTV,EAAYW,kBAAkB,CAAEC,SAAU,CAAC,OAAQ,aAM5CC,EAAqBA,KAChC,MAAMb,GAAcC,EAAAA,EAAAA,MAEpB,OAAOC,EAAAA,EAAAA,GAA8D,CACnEC,WAAYC,UAAqB,IAAd,KAAEU,GAAM3D,EAWzB,GAAa,WAAT2D,EACF,MAAM,IAAIC,MAAM,6BAGlB,OAAO,IAAIV,QAASC,IAClBC,WAAW,KACTD,EAAQ,CACNU,cAAe,CAAC,QAAS,QAAS,QAAS,QAAS,SACpDC,SAAS,KAEV,QAGPP,UAAWA,KACTV,EAAYW,kBAAkB,CAAEC,SAAU,CAAC,OAAQ,aAM5CM,EAAsBA,KACjC,MAAMlB,GAAcC,EAAAA,EAAAA,OACd,KAAEH,IAASC,EAAAA,EAAAA,KAEjB,OAAOG,EAAAA,EAAAA,GAA+D,CACpEC,WAAYC,UAAqB,IAAd,KAAEU,GAAMnD,EAWzB,GAAa,WAATmD,EACF,MAAM,IAAIC,MAAM,6BAGlB,OAAO,IAAIV,QAASC,IAClBC,WAAW,KACTD,EAAQ,CACNW,SAAS,EACTE,QAAS,kDAEV,QAGPT,UAAWA,KACTV,EAAYW,kBAAkB,CAAEC,SAAU,CAAC,OAAQ,a,yCClGlD,SAASQ,EAAajE,GAAoD,IAAnD,iBAAEkE,GAAmB,GAA2BlE,EAC5E,MAAM,MAAEmE,IAAUC,EAAAA,EAAAA,OACXC,EAAcC,IAAmBC,EAAAA,EAAAA,WAAS,IAC1CC,EAAkBC,IAAuBF,EAAAA,EAAAA,UAAS,KAClDlB,EAAYqB,IAAiBH,EAAAA,EAAAA,UAAS,KACtCV,EAAec,IAAoBJ,EAAAA,EAAAA,UAAmB,KACtDK,EAAmBC,IAAwBN,EAAAA,EAAAA,WAAS,IAIzDO,OAAQC,EACRC,UAAWC,EACXC,UAAWC,EACXC,KAAMC,EACNC,QAASC,EACTC,MAAOC,GACL/C,KAGFoC,OAAQY,EACRV,UAAWW,EACXT,UAAWU,EACXR,KAAMS,EACNP,QAASQ,EACTN,MAAOO,GACLrC,KAGFoB,OAAQkB,EACRhB,UAAWiB,EACXf,UAAWgB,EACXZ,QAASa,EACTX,MAAOY,GACLrC,KAGJsC,EAAAA,EAAAA,WAAU,KACJlB,GAAkBE,IACpBX,EAAcW,EAAUhC,YACxBiB,GAAgB,KAEjB,CAACa,EAAgBE,KAGpBgB,EAAAA,EAAAA,WAAU,KACJT,GAAmBC,IACrBlB,EAAiBkB,EAAWhC,eAC5BgB,GAAqB,GACrBV,EAAM,CACJmC,MAAO,UACPC,YAAa,+DAGhB,CAACX,EAAiBC,EAAY1B,KAGjCkC,EAAAA,EAAAA,WAAU,KACJd,GAAgBE,GAClBtB,EAAM,CACJmC,MAAO,QACPC,YAAad,EAAWzB,SAAW,6CACnCwC,QAAS,gBAITV,GAAiBC,GACnB5B,EAAM,CACJmC,MAAO,QACPC,YAAaR,EAAY/B,SAAW,kDACpCwC,QAAS,gBAITL,GAAkBC,GACpBjC,EAAM,CACJmC,MAAO,QACPC,YAAaH,EAAapC,SAAW,8CACrCwC,QAAS,iBAGZ,CAACjB,EAAcE,EAAYK,EAAeC,EAAaI,EAAgBC,EAAcjC,KAGxFkC,EAAAA,EAAAA,WAAU,KACJH,IACF/B,EAAM,CACJmC,MAAO,UACPC,YAAa,8DAEfjC,GAAgB,GAChBG,EAAoB,MAErB,CAACyB,EAAkB/B,IAkGtB,OACE/B,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,YAAWU,SAAA,EACxBwB,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,oCAAmCU,SAAA,EAChDwB,EAAAA,EAAAA,MAAA,OAAAxB,SAAA,EACER,EAAAA,EAAAA,KAAA,MAAIF,UAAU,wBAAuBU,SAAC,+BACtCR,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gCAA+BU,SACzCsD,EACG,wCACA,uDAGPA,GACC9D,EAAAA,EAAAA,KAACqG,EAAAA,EAAM,CACLD,QAAQ,cACRE,QAASA,IAAMpC,GAAgB,GAAM1D,SACtC,iBAIDR,EAAAA,EAAAA,KAACqG,EAAAA,EAAM,CACLC,QApEaC,KACrB5B,OAAe6B,EAAW,CACxBrD,UAAY6B,IACVV,EAAcU,EAAK/B,YACnBiB,GAAgB,IAElBuC,QAAUrB,IACRrB,EAAM,CACJmC,MAAO,QACPC,YAAaf,EAAMxB,SAAW,6CAC9BwC,QAAS,oBA2DPM,SAAU7B,EAAYrE,SAErBqE,GACC7C,EAAAA,EAAAA,MAAA2E,EAAAA,SAAA,CAAAnG,SAAA,EACER,EAAAA,EAAAA,KAAC4G,EAAAA,EAAO,CAAC9G,UAAU,8BAA8B,mBAInD,mBAMRE,EAAAA,EAAAA,KAACsB,EAAAA,GAAM,CAACuF,KAAM5C,EAAc6C,aAAc5C,EAAgB1D,UACxDwB,EAAAA,EAAAA,MAACD,EAAAA,GAAa,CAACjC,UAAU,mBAAkBU,SAAA,EACzCwB,EAAAA,EAAAA,MAACE,EAAAA,GAAY,CAAA1B,SAAA,EACXR,EAAAA,EAAAA,KAACoC,EAAAA,GAAW,CAAA5B,SAAEsD,EAAmB,oCAAsC,sCACvE9D,EAAAA,EAAAA,KAACqC,EAAAA,GAAiB,CAAA7B,SACfsD,EAAmB,qEAAuE,sFAI7FA,GAAoBb,IAAeuB,IACnCxC,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,uCAAsCU,SAAA,EACnDR,EAAAA,EAAAA,KAAA,OAAKF,UAAU,0BAAyBU,UACtCR,EAAAA,EAAAA,KAAA,OAAK+G,IAAK9D,EAAY+D,IAAI,UAAUlH,UAAU,iBAEhDE,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gCAA+BU,SAAC,qDAMhDgE,GACCxC,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,YAAWU,SAAA,EACxBR,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oDAAmDU,UAChEwB,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,OAAMU,SAAA,EACnBR,EAAAA,EAAAA,KAACiH,EAAAA,EAAW,CAACnH,UAAU,uDACvBE,EAAAA,EAAAA,KAAA,KAAGF,UAAU,+CAA8CU,SAAC,wIAMhER,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yBAAwBU,SACpCiD,EAAcyD,IAAI,CAAC3D,EAAM4D,KACxBnH,EAAAA,EAAAA,KAAA,OAAiBF,UAAU,yCAAwCU,SAChE+C,GADO4D,OAMdnF,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,iBAAgBU,SAAA,EAC7BR,EAAAA,EAAAA,KAACqG,EAAAA,EAAM,CAACD,QAAQ,UAAUE,QA7IRc,KAC9BC,UAAUC,UAAUC,UAAU9D,EAAc+D,KAAK,OACjDzD,EAAM,CACJmC,MAAO,SACPC,YAAa,wCAyIwD3F,SAAC,gBAG5DR,EAAAA,EAAAA,KAACqG,EAAAA,EAAM,CAACD,QAAQ,UAAUE,QAxIJmB,KAClC,MAAMC,EAAUC,SAASC,cAAc,KACjCC,EAAO,IAAIC,KAAK,CAACrE,EAAc+D,KAAK,OAAQ,CAAEO,KAAM,eAC1DL,EAAQM,KAAOC,IAAIC,gBAAgBL,GACnCH,EAAQS,SAAW,qBACnBR,SAASS,KAAKC,YAAYX,GAC1BA,EAAQY,QACRX,SAASS,KAAKG,YAAYb,IAiIiDlH,SAAC,iBAKlER,EAAAA,EAAAA,KAACqG,EAAAA,EAAM,CAACvG,UAAU,SAASwG,QAASA,KAClC7B,GAAqB,GACrBP,GAAgB,IAChB1D,SAAC,qCAKLwB,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,YAAWU,SAAA,EACxBwB,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,YAAWU,SAAA,EACxBR,EAAAA,EAAAA,KAACwI,EAAAA,EAAK,CAACC,QAAQ,oBAAmBjI,SAC/BsD,EAAmB,yCAA2C,yDAEjE9D,EAAAA,EAAAA,KAAC0I,EAAAA,EAAK,CACJC,GAAG,oBACHC,MAAOxE,EACPyE,SAAWC,GAAMzE,EAAoByE,EAAEC,OAAOH,OAC9CI,YAAY,SACZlJ,UAAU,gDACVmJ,aAAa,sBAIjBjJ,EAAAA,EAAAA,KAACqG,EAAAA,EAAM,CACLvG,UAAU,SACVwG,QAASxC,EA5MEoF,KAClB9E,EAAiB+E,OAStBvD,EACE,CAAErC,KAAMa,GACR,CACEjB,UAAWA,KACTkB,EAAoB,IACpBC,EAAc,IACdC,EAAiB,IACjBE,GAAqB,IAEvBgC,QAAUrB,IACRrB,EAAM,CACJmC,MAAO,QACPC,YAAaf,EAAMxB,SAAW,8CAC9BwC,QAAS,mBArBfrC,EAAM,CACJmC,MAAO,QACPC,YAAa,mCACbC,QAAS,iBA2DUgD,KAClBhF,EAAiB+E,OAStB7D,EACE,CAAE/B,KAAMa,GACR,CACEjB,UAAY6B,IACVT,EAAiBS,EAAKvB,eACtBgB,GAAqB,GACrBV,EAAM,CACJmC,MAAO,UACPC,YAAa,8DAGjBM,QAAUrB,IACRrB,EAAM,CACJmC,MAAO,QACPC,YAAaf,EAAMxB,SAAW,kDAC9BwC,QAAS,mBAvBfrC,EAAM,CACJmC,MAAO,QACPC,YAAa,mCACbC,QAAS,iBAwIDM,SAAU5C,EAAmB+B,EAAcN,EAAY/E,SAEtDsD,EACC+B,GACE7D,EAAAA,EAAAA,MAAA2E,EAAAA,SAAA,CAAAnG,SAAA,EACER,EAAAA,EAAAA,KAAC4G,EAAAA,EAAO,CAAC9G,UAAU,8BAA8B,kBAInD,cAEAyF,GACFvD,EAAAA,EAAAA,MAAA2E,EAAAA,SAAA,CAAAnG,SAAA,EACER,EAAAA,EAAAA,KAAC4G,EAAAA,EAAO,CAAC9G,UAAU,8BAA8B,kBAInD,oCASlB,C,wICnUO,SAASuJ,IACd,MAAM,MAAEtF,IAAUC,EAAAA,EAAAA,OACXsF,EAAqBC,IAA0BpF,EAAAA,EAAAA,WAAS,IACxDqF,EAAiBC,IAAsBtF,EAAAA,EAAAA,WAAS,IAChDuF,EAAqBC,IAA0BxF,EAAAA,EAAAA,WAAS,IACxDyF,EAAUC,IAAe1F,EAAAA,EAAAA,UAA2B,CACzD2F,gBAAiB,GACjBC,YAAa,GACbC,gBAAiB,MAEZpF,EAAWqF,IAAgB9F,EAAAA,EAAAA,WAAS,IAEnCO,OAAQwF,IAAmBC,EAAAA,EAAAA,IAAkB,CACnDhH,UAAWA,KACTY,EAAM,CACJmC,MAAO,UACPC,YAAa,iDAGf0D,EAAY,CACVC,gBAAiB,GACjBC,YAAa,GACbC,gBAAiB,MAGrBvD,QAAUrB,IACR,MAAMgF,EAAehF,aAAiB5B,MAAQ4B,EAAMxB,QAAU,4BAC9DG,EAAM,CACJmC,MAAO,QACPC,YAAaiE,EACbhE,QAAS,mBAKTiE,EAAgBvB,IACpB,MAAM,KAAEwB,EAAI,MAAE1B,GAAUE,EAAEC,OAC1Bc,EAAYU,IAAI,IACXA,EACH,CAACD,GAAO1B,MA2CZ,OACE5G,EAAAA,EAAAA,MAACvC,EAAAA,GAAI,CAAC+K,SAxCa3H,UACnBiG,EAAE2B,iBAGEb,EAASG,cAAgBH,EAASI,gBASlCJ,EAASG,YAAYW,OAAS,EAChC3G,EAAM,CACJmC,MAAO,QACPC,YAAa,yCACbC,QAAS,gBAKR,yDAAyDuE,KAAKf,EAASG,cAS5EE,GAAa,GACbC,EAAe,CACbJ,gBAAiBF,EAASE,gBAC1BC,YAAaH,EAASG,cAExBE,GAAa,IAbXlG,EAAM,CACJmC,MAAO,QACPC,YAAa,mHACbC,QAAS,gBArBXrC,EAAM,CACJmC,MAAO,QACPC,YAAa,yBACbC,QAAS,iBAgCiBtG,UAAU,YAAWU,SAAA,EACjDwB,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,YAAWU,SAAA,EACxBwB,EAAAA,EAAAA,MAAC7B,EAAAA,GAAQ,CAAAK,SAAA,EACPR,EAAAA,EAAAA,KAACK,EAAAA,GAAS,CAAAG,SAAC,sBACXwB,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,WAAUU,SAAA,EACvBR,EAAAA,EAAAA,KAACS,EAAAA,GAAW,CAAAD,UACVR,EAAAA,EAAAA,KAAC0I,EAAAA,EAAK,CACJX,KAAMuB,EAAsB,OAAS,WACrCgB,KAAK,kBACL1B,MAAOgB,EAASE,gBAChBjB,SAAUwB,EACVrB,YAAY,yBACZzI,UAAQ,OAGZP,EAAAA,EAAAA,KAAA,UACE+H,KAAK,SACLjI,UAAU,8EACVwG,QAASA,IAAMiD,GAAwBD,GAAqB9I,SAE3D8I,GACCtJ,EAAAA,EAAAA,KAAC4K,EAAAA,EAAM,CAAC9K,UAAU,aAElBE,EAAAA,EAAAA,KAAC6K,EAAAA,EAAG,CAAC/K,UAAU,kBAIrBE,EAAAA,EAAAA,KAACY,EAAAA,GAAW,QAGdoB,EAAAA,EAAAA,MAAC7B,EAAAA,GAAQ,CAAAK,SAAA,EACPR,EAAAA,EAAAA,KAACK,EAAAA,GAAS,CAAAG,SAAC,kBACXwB,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,WAAUU,SAAA,EACvBR,EAAAA,EAAAA,KAACS,EAAAA,GAAW,CAAAD,UACVR,EAAAA,EAAAA,KAAC0I,EAAAA,EAAK,CACJX,KAAMyB,EAAkB,OAAS,WACjCc,KAAK,cACL1B,MAAOgB,EAASG,YAChBlB,SAAUwB,EACVrB,YAAY,qBACZzI,UAAQ,OAGZP,EAAAA,EAAAA,KAAA,UACE+H,KAAK,SACLjI,UAAU,8EACVwG,QAASA,IAAMmD,GAAoBD,GAAiBhJ,SAEnDgJ,GACCxJ,EAAAA,EAAAA,KAAC4K,EAAAA,EAAM,CAAC9K,UAAU,aAElBE,EAAAA,EAAAA,KAAC6K,EAAAA,EAAG,CAAC/K,UAAU,kBAIrBE,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qCAAoCU,SAAC,oGAGlDR,EAAAA,EAAAA,KAACY,EAAAA,GAAW,QAGdoB,EAAAA,EAAAA,MAAC7B,EAAAA,GAAQ,CAAAK,SAAA,EACPR,EAAAA,EAAAA,KAACK,EAAAA,GAAS,CAAAG,SAAC,0BACXwB,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,WAAUU,SAAA,EACvBR,EAAAA,EAAAA,KAACS,EAAAA,GAAW,CAAAD,UACVR,EAAAA,EAAAA,KAAC0I,EAAAA,EAAK,CACJX,KAAM2B,EAAsB,OAAS,WACrCY,KAAK,kBACL1B,MAAOgB,EAASI,gBAChBnB,SAAUwB,EACVrB,YAAY,uBACZzI,UAAQ,OAGZP,EAAAA,EAAAA,KAAA,UACE+H,KAAK,SACLjI,UAAU,8EACVwG,QAASA,IAAMqD,GAAwBD,GAAqBlJ,SAE3DkJ,GACC1J,EAAAA,EAAAA,KAAC4K,EAAAA,EAAM,CAAC9K,UAAU,aAElBE,EAAAA,EAAAA,KAAC6K,EAAAA,EAAG,CAAC/K,UAAU,kBAIrBE,EAAAA,EAAAA,KAACY,EAAAA,GAAW,WAIhBoB,EAAAA,EAAAA,MAACqE,EAAAA,EAAM,CAAC0B,KAAK,SAASrB,SAAU9B,EAAUpE,SAAA,CACvCoE,IAAa5E,EAAAA,EAAAA,KAAC4G,EAAAA,EAAO,CAAC9G,UAAU,8BAA+B,uBAKxE,C,4ECpMA,MAAMgL,GAAgBC,EAAAA,EAAAA,GACpB,8FAGIvC,EAAQ7I,EAAAA,WAGZ,CAAAC,EAA0BC,KAAG,IAA5B,UAAEC,KAAcC,GAAOH,EAAA,OACxBI,EAAAA,EAAAA,KAAA,SACEH,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IAAG6K,IAAiBhL,MAC3BC,MAGRyI,EAAMtI,YAAc,O,uICbpB,MAAM8K,EAAeC,EAAAA,GAEfC,EAAsBD,EAAAA,GAEFA,EAAAA,GAECA,EAAAA,GAEHA,EAAAA,GAEOA,EAAAA,GAEAtL,EAAAA,WAM7B,CAAAC,EAA2CC,KAAG,IAA7C,UAAEC,EAAS,MAAEqL,EAAK,SAAE3K,KAAaT,GAAOH,EAAA,OACzCoC,EAAAA,EAAAA,MAACiJ,EAAAA,GAAgC,CAC/BpL,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IACT,uIACAkL,GAAS,OACTrL,MAEEC,EAAKS,SAAA,CAERA,GACDR,EAAAA,EAAAA,KAACoL,EAAAA,EAAY,CAACtL,UAAU,yBAGLI,YACrB+K,EAAAA,GAAiC/K,YAEJP,EAAAA,WAG7B,CAAAS,EAA0BP,KAAG,IAA5B,UAAEC,KAAcC,GAAOK,EAAA,OACxBJ,EAAAA,EAAAA,KAACiL,EAAAA,GAAgC,CAC/BpL,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IACT,wbACAH,MAEEC,MAGeG,YACrB+K,EAAAA,GAAiC/K,YAEnC,MAAMmL,EAAsB1L,EAAAA,WAG1B,CAAAW,EAA0CT,KAAG,IAA5C,UAAEC,EAAS,WAAEwL,EAAa,KAAMvL,GAAOO,EAAA,OACxCN,EAAAA,EAAAA,KAACiL,EAAAA,GAA4B,CAAAzK,UAC3BR,EAAAA,EAAAA,KAACiL,EAAAA,GAA6B,CAC5BpL,IAAKA,EACLyL,WAAYA,EACZxL,WAAWG,EAAAA,EAAAA,IACT,wbACAH,MAEEC,QAIVsL,EAAoBnL,YAAc+K,EAAAA,GAA8B/K,YAEhE,MAAMqL,EAAmB5L,EAAAA,WAMvB,CAAAe,EAAiCb,KAAG,IAAnC,UAAEC,EAAS,MAAEqL,KAAUpL,GAAOW,EAAA,OAC/BV,EAAAA,EAAAA,KAACiL,EAAAA,GAA0B,CACzBpL,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IACT,kOACAkL,GAAS,OACTrL,MAEEC,MAGRwL,EAAiBrL,YAAc+K,EAAAA,GAA2B/K,YAEzBP,EAAAA,WAG/B,CAAAgB,EAA6Cd,KAAG,IAA/C,UAAEC,EAAS,SAAEU,EAAQ,QAAEgL,KAAYzL,GAAOY,EAAA,OAC3CqB,EAAAA,EAAAA,MAACiJ,EAAAA,GAAkC,CACjCpL,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IACT,uOACAH,GAEF0L,QAASA,KACLzL,EAAKS,SAAA,EAETR,EAAAA,EAAAA,KAAA,QAAMF,UAAU,+DAA8DU,UAC5ER,EAAAA,EAAAA,KAACiL,EAAAA,GAAmC,CAAAzK,UAClCR,EAAAA,EAAAA,KAACyL,EAAAA,EAAK,CAAC3L,UAAU,gBAGpBU,OAGoBN,YACvB+K,EAAAA,GAAmC/K,YAEPP,EAAAA,WAG5B,CAAAkB,EAAoChB,KAAG,IAAtC,UAAEC,EAAS,SAAEU,KAAaT,GAAOc,EAAA,OAClCmB,EAAAA,EAAAA,MAACiJ,EAAAA,GAA+B,CAC9BpL,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IACT,uOACAH,MAEEC,EAAKS,SAAA,EAETR,EAAAA,EAAAA,KAAA,QAAMF,UAAU,+DAA8DU,UAC5ER,EAAAA,EAAAA,KAACiL,EAAAA,GAAmC,CAAAzK,UAClCR,EAAAA,EAAAA,KAAC0L,EAAAA,EAAM,CAAC5L,UAAU,6BAGrBU,OAGiBN,YAAc+K,EAAAA,GAAgC/K,YAE1CP,EAAAA,WAKxB,CAAAyB,EAAiCvB,KAAG,IAAnC,UAAEC,EAAS,MAAEqL,KAAUpL,GAAOqB,EAAA,OAC/BpB,EAAAA,EAAAA,KAACiL,EAAAA,GAA2B,CAC1BpL,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IACT,oCACAkL,GAAS,OACTrL,MAEEC,MAGUG,YAAc+K,EAAAA,GAA4B/K,YAE9BP,EAAAA,WAG5B,CAAA0B,EAA0BxB,KAAG,IAA5B,UAAEC,KAAcC,GAAOsB,EAAA,OACxBrB,EAAAA,EAAAA,KAACiL,EAAAA,GAA+B,CAC9BpL,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IAAG,2BAA4BH,MACtCC,MAGcG,YAAc+K,EAAAA,GAAgC/K,W,gOCtIpE,MAAMyL,EAAe9I,UACnB,IACE,MAAM+I,QAAiBC,MAAM,aAC7B,IAAKD,EAASE,GAAI,MAAM,IAAItI,MAAM,4BAClC,aAAaoI,EAASG,MACxB,CAAE,MAAO3G,GAEP,MADA4G,QAAQ5G,MAAM,2BAA4BA,GACpCA,CACR,GAGI6G,EAAepJ,UACnB,IACE,MAAM+I,QAAiBC,MAAM,YAAa,CACxCK,OAAQ,OACRC,QAAS,CAAE,eAAgB,oBAC3B/D,KAAMgE,KAAKC,UAAU,CAAE/B,WAEzB,IAAKsB,EAASE,GAAI,MAAM,IAAItI,MAAM,4BAClC,aAAaoI,EAASG,MACxB,CAAE,MAAO3G,GAEP,MADA4G,QAAQ5G,MAAM,0BAA2BA,GACnCA,CACR,GAGIkH,EAAezJ,UACnB,IAIE,WAHuBgJ,MAAM,aAAalD,IAAM,CAC9CuD,OAAQ,YAEIJ,GAAI,MAAM,IAAItI,MAAM,4BAClC,MAAO,CAAEE,SAAS,EACpB,CAAE,MAAO0B,GAEP,MADA4G,QAAQ5G,MAAM,0BAA2BA,GACnCA,CACR,GAKK,SAASmH,IACd,MAAM,MAAExI,IAAUC,EAAAA,EAAAA,MACZvB,GAAcC,EAAAA,EAAAA,OACb8J,EAAoBC,IAAyBtI,EAAAA,EAAAA,WAAS,IACtDuI,EAAYC,IAAiBxI,EAAAA,EAAAA,UAAS,KACtCyI,EAAQC,IAAa1I,EAAAA,EAAAA,UAAwB,OAC7C2I,EAAaC,IAAkB5I,EAAAA,EAAAA,UAAwB,OAEtDa,KAAMgI,EAAO,UAAEC,IAAcC,EAAAA,EAAAA,GAAmB,CACtD7J,SAAU,CAAC,WACX8J,QAASxB,IAGLyB,GAAoBzK,EAAAA,EAAAA,GAAY,CACpCC,WAAa0H,GAAiB2B,EAAa3B,GAC3CnH,UAAY6B,IACV6H,EAAU7H,GACVvC,EAAYW,kBAAkB,CAAEC,SAAU,CAAC,aAC3CsJ,EAAc,KAEhBlG,QAASA,KACP1C,EAAM,CACJmC,MAAO,QACPC,YAAa,2BACbC,QAAS,mBAKTiH,GAAoB1K,EAAAA,EAAAA,GAAY,CACpCC,WAAa+F,GAAe2D,EAAa3D,GACzCxF,UAAWA,KACTV,EAAYW,kBAAkB,CAAEC,SAAU,CAAC,aAC3CU,EAAM,CACJmC,MAAO,UACPC,YAAa,kCAGjBM,QAASA,KACP1C,EAAM,CACJmC,MAAO,QACPC,YAAa,2BACbC,QAAS,mBAiBTkH,EAAgBA,CAACC,EAAa5E,KAClCtB,UAAUC,UAAUC,UAAUgG,GAC9BR,EAAepE,GACf5E,EAAM,CACJmC,MAAO,SACPC,YAAa,gCAEfnD,WAAW,IAAM+J,EAAe,MAAO,MAGnCS,EAAcC,GACbA,EACE,IAAIC,KAAKD,GAAYE,iBADJ,QAI1B,OAAIV,GACKjN,EAAAA,EAAAA,KAAA,OAAAQ,SAAK,yBAIZwB,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,YAAWU,SAAA,EACxBwB,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,oCAAmCU,SAAA,EAChDwB,EAAAA,EAAAA,MAAA,OAAAxB,SAAA,EACER,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sBAAqBU,SAAC,cACpCR,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gCAA+BU,SAAC,sEAI/CwB,EAAAA,EAAAA,MAACV,EAAAA,GAAM,CAACuF,KAAM2F,EAAoB1F,aAAc2F,EAAsBjM,SAAA,EACpER,EAAAA,EAAAA,KAACwB,EAAAA,GAAa,CAACoM,SAAO,EAAApN,UACpBwB,EAAAA,EAAAA,MAACqE,EAAAA,EAAM,CAAA7F,SAAA,EACLR,EAAAA,EAAAA,KAAC6N,EAAAA,EAAI,CAAC/N,UAAU,iBAAiB,uBAIrCkC,EAAAA,EAAAA,MAACD,EAAAA,GAAa,CAAAvB,SAAA,EACZwB,EAAAA,EAAAA,MAACE,EAAAA,GAAY,CAAA1B,SAAA,EACXR,EAAAA,EAAAA,KAACoC,EAAAA,GAAW,CAAA5B,SAAC,wBACbR,EAAAA,EAAAA,KAACqC,EAAAA,GAAiB,CAAA7B,SAAC,kEAIrBwB,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,YAAWU,SAAA,EACxBwB,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,YAAWU,SAAA,EACxBR,EAAAA,EAAAA,KAACwI,EAAAA,EAAK,CAACC,QAAQ,WAAUjI,SAAC,cAC1BR,EAAAA,EAAAA,KAAC0I,EAAAA,EAAK,CACJC,GAAG,WACHK,YAAY,gCACZJ,MAAO8D,EACP7D,SAAWC,GAAM6D,EAAc7D,EAAEC,OAAOH,UAE1C5I,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gCAA+BU,SAAC,kDAK9CoM,IACC5M,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8BAA6BU,UAC1CwB,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,OAAMU,SAAA,EACnBR,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gBAAeU,UAC5BR,EAAAA,EAAAA,KAACiH,EAAAA,EAAW,CAACnH,UAAU,0BAA0B,cAAY,YAE/DkC,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,OAAMU,SAAA,EACnBR,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sCAAqCU,SAAC,sBAGpDwB,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,+BAA8BU,SAAA,EAC3CR,EAAAA,EAAAA,KAAA,KAAAQ,SAAG,0EAGHwB,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,iCAAgCU,SAAA,EAC7CR,EAAAA,EAAAA,KAAA,OAAKF,UAAU,0DAAyDU,UACtER,EAAAA,EAAAA,KAAA,SACE+H,KAAK,OACLjI,UAAU,6MACV8I,MAAOgE,EAAOW,IACdO,UAAQ,OAGZ9L,EAAAA,EAAAA,MAAA,UACE+F,KAAK,SACLjI,UAAU,iKACVwG,QAASA,IAAMgH,EAAcV,EAAOW,IAAKX,EAAOjE,IAAInI,SAAA,EAEpDR,EAAAA,EAAAA,KAAC+N,EAAAA,EAAI,CAACjO,UAAU,wBAAwB,cAAY,SAAS,4BAU7EkC,EAAAA,EAAAA,MAACG,EAAAA,GAAY,CAAA3B,SAAA,EACXR,EAAAA,EAAAA,KAACqG,EAAAA,EAAM,CACLD,QAAQ,UACRE,QAASA,KACPmG,GAAsB,GACtBI,EAAU,OACVrM,SACH,UAGCoM,EAeE,MAdF5M,EAAAA,EAAAA,KAACqG,EAAAA,EAAM,CACL0B,KAAK,SACLzB,QAvHQ0H,KACjBtB,EAAWvD,OAQhBiE,EAAkB1I,OAAOgI,GAPvB3I,EAAM,CACJmC,MAAO,QACPC,YAAa,sCACbC,QAAS,iBAmHCM,SAAU0G,EAAkBxI,UAAUpE,SAErC4M,EAAkBxI,WACjB5C,EAAAA,EAAAA,MAAA2E,EAAAA,SAAA,CAAAnG,SAAA,EACER,EAAAA,EAAAA,KAAC4G,EAAAA,EAAO,CAAC9G,UAAU,8BAA8B,iBAInD,4BASdE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oBAAmBU,UAChCwB,EAAAA,EAAAA,MAAClB,EAAAA,GAAK,CAAAN,SAAA,EACJR,EAAAA,EAAAA,KAACe,EAAAA,GAAW,CAAAP,UACVwB,EAAAA,EAAAA,MAACf,EAAAA,GAAQ,CAAAT,SAAA,EACPR,EAAAA,EAAAA,KAACkB,EAAAA,GAAS,CAAAV,SAAC,UACXR,EAAAA,EAAAA,KAACkB,EAAAA,GAAS,CAAAV,SAAC,SACXR,EAAAA,EAAAA,KAACkB,EAAAA,GAAS,CAAAV,SAAC,aACXR,EAAAA,EAAAA,KAACkB,EAAAA,GAAS,CAAAV,SAAC,eACXR,EAAAA,EAAAA,KAACkB,EAAAA,GAAS,CAACpB,UAAU,eAGzBE,EAAAA,EAAAA,KAACgB,EAAAA,GAAS,CAAAR,SACA,OAAPwM,QAAO,IAAPA,GAAAA,EAAStC,OACRsC,EAAQ9F,IAAK+G,IACXjM,EAAAA,EAAAA,MAACf,EAAAA,GAAQ,CAAAT,SAAA,EACPR,EAAAA,EAAAA,KAACmB,EAAAA,GAAS,CAACrB,UAAU,cAAaU,SAAEyN,EAAO3D,QAC3CtK,EAAAA,EAAAA,KAACmB,EAAAA,GAAS,CAAAX,UACRwB,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,8BAA6BU,SAAA,EAC1CwB,EAAAA,EAAAA,MAAA,QAAMlC,UAAU,oBAAmBU,SAAA,CAChCyN,EAAOV,IAAIW,UAAU,EAAG,GAAG,MAAID,EAAOV,IAAIW,UAAUD,EAAOV,IAAI7C,OAAS,OAE3E1K,EAAAA,EAAAA,KAAA,UACE+H,KAAK,SACLjI,UAAU,8CACVwG,QAASA,IAAMgH,EAAcW,EAAOV,IAAKU,EAAOtF,IAAInI,SAEnDsM,IAAgBmB,EAAOtF,IACtB3I,EAAAA,EAAAA,KAACmO,EAAAA,EAAY,CAACrO,UAAU,4BAExBE,EAAAA,EAAAA,KAAC+N,EAAAA,EAAI,CAACjO,UAAU,oBAKxBE,EAAAA,EAAAA,KAACmB,EAAAA,GAAS,CAAAX,SAAEgN,EAAWS,EAAOG,cAC9BpO,EAAAA,EAAAA,KAACmB,EAAAA,GAAS,CAAAX,SAAEgN,EAAWS,EAAOI,aAC9BrO,EAAAA,EAAAA,KAACmB,EAAAA,GAAS,CAAAX,UACRwB,EAAAA,EAAAA,MAACgJ,EAAAA,GAAY,CAAAxK,SAAA,EACXR,EAAAA,EAAAA,KAACkL,EAAAA,GAAmB,CAAC0C,SAAO,EAAApN,UAC1BwB,EAAAA,EAAAA,MAACqE,EAAAA,EAAM,CAACD,QAAQ,QAAQkI,KAAK,OAAOxO,UAAU,UAASU,SAAA,EACrDR,EAAAA,EAAAA,KAACuO,EAAAA,EAAY,CAACzO,UAAU,aACxBE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,UAASU,SAAC,kBAG9BR,EAAAA,EAAAA,KAACqL,EAAAA,GAAmB,CAACmD,MAAM,MAAKhO,UAC9BwB,EAAAA,EAAAA,MAACuJ,EAAAA,GAAgB,CACfzL,UAAU,eACVwG,QAASA,IAAM+G,EAAkB3I,OAAOuJ,EAAOtF,IAC/CjC,SAAU2G,EAAkBoB,YAAcR,EAAOtF,GAAGnI,SAAA,CAEnD6M,EAAkBoB,YAAcR,EAAOtF,IACtC3I,EAAAA,EAAAA,KAAC4G,EAAAA,EAAO,CAAC9G,UAAU,+BAEnBE,EAAAA,EAAAA,KAAC0O,EAAAA,EAAM,CAAC5O,UAAU,iBAClB,qBAxCGmO,EAAOtF,MAiDxB3I,EAAAA,EAAAA,KAACiB,EAAAA,GAAQ,CAAAT,UACPR,EAAAA,EAAAA,KAACmB,EAAAA,GAAS,CAACwN,QAAS,EAAG7O,UAAU,mBAAkBU,SAAC,kCAUpE,C,yGCxUA,MAAMoO,EAAOC,EAAAA,GAEPC,EAAWnP,EAAAA,WAGf,CAAAC,EAA0BC,KAAG,IAA5B,UAAEC,KAAcC,GAAOH,EAAA,OACxBI,EAAAA,EAAAA,KAAC6O,EAAAA,GAAkB,CACjBhP,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IACT,6FACAH,MAEEC,MAGR+O,EAAS5O,YAAc2O,EAAAA,GAAmB3O,YAE1C,MAAM6O,EAAcpP,EAAAA,WAGlB,CAAAS,EAA0BP,KAAG,IAA5B,UAAEC,KAAcC,GAAOK,EAAA,OACxBJ,EAAAA,EAAAA,KAAC6O,EAAAA,GAAqB,CACpBhP,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IACT,sYACAH,MAEEC,MAGRgP,EAAY7O,YAAc2O,EAAAA,GAAsB3O,YAEhD,MAAM8O,EAAcrP,EAAAA,WAGlB,CAAAW,EAA0BT,KAAG,IAA5B,UAAEC,KAAcC,GAAOO,EAAA,OACxBN,EAAAA,EAAAA,KAAC6O,EAAAA,GAAqB,CACpBhP,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IACT,kIACAH,MAEEC,MAGRiP,EAAY9O,YAAc2O,EAAAA,GAAsB3O,W,iHC9ChD,MAAM+O,EAAOtP,EAAAA,WAGX,CAAAC,EAA0BC,KAAG,IAA5B,UAAEC,KAAcC,GAAOH,EAAA,OACxBI,EAAAA,EAAAA,KAAA,OACEH,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IACT,2DACAH,MAEEC,MAGRkP,EAAK/O,YAAc,OAEnB,MAAMgP,EAAavP,EAAAA,WAGjB,CAAAS,EAA0BP,KAAG,IAA5B,UAAEC,KAAcC,GAAOK,EAAA,OACxBJ,EAAAA,EAAAA,KAAA,OACEH,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IAAG,gCAAiCH,MAC3CC,MAGRmP,EAAWhP,YAAc,aAEzB,MAAMiP,EAAYxP,EAAAA,WAGhB,CAAAW,EAA0BT,KAAG,IAA5B,UAAEC,KAAcC,GAAOO,EAAA,OACxBN,EAAAA,EAAAA,KAAA,MACEH,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IACT,qDACAH,MAEEC,MAGRoP,EAAUjP,YAAc,YAExB,MAAMkP,EAAkBzP,EAAAA,WAGtB,CAAAe,EAA0Bb,KAAG,IAA5B,UAAEC,KAAcC,GAAOW,EAAA,OACxBV,EAAAA,EAAAA,KAAA,KACEH,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IAAG,gCAAiCH,MAC3CC,MAGRqP,EAAgBlP,YAAc,kBAE9B,MAAMmP,EAAc1P,EAAAA,WAGlB,CAAAgB,EAA0Bd,KAAG,IAA5B,UAAEC,KAAcC,GAAOY,EAAA,OACxBX,EAAAA,EAAAA,KAAA,OAAKH,IAAKA,EAAKC,WAAWG,EAAAA,EAAAA,IAAG,WAAYH,MAAgBC,MAE3DsP,EAAYnP,YAAc,cAE1B,MAAMoP,EAAa3P,EAAAA,WAGjB,CAAAkB,EAA0BhB,KAAG,IAA5B,UAAEC,KAAcC,GAAOc,EAAA,OACxBb,EAAAA,EAAAA,KAAA,OACEH,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IAAG,6BAA8BH,MACxCC,MAGRuP,EAAWpP,YAAc,Y,0DC5DzB,IAAIqP,EAAQ,EA+BZ,MAAMC,EAAgB,IAAIC,IAEpBC,EAAoBC,IACxB,GAAIH,EAAcI,IAAID,GACpB,OAGF,MAAME,EAAU7M,WAAW,KACzBwM,EAAcM,OAAOH,GACrBI,EAAS,CACPhI,KAAM,eACN4H,QAASA,KAEV,KAEHH,EAAcQ,IAAIL,EAASE,IAGhBI,EAAUA,CAACC,EAAcC,KACpC,OAAQA,EAAOpI,MACb,IAAK,YACH,MAAO,IACFmI,EACHE,OAAQ,CAACD,EAAOpM,SAAUmM,EAAME,QAAQC,MAAM,EAAG,IAGrD,IAAK,eACH,MAAO,IACFH,EACHE,OAAQF,EAAME,OAAOlJ,IAAKoJ,GACxBA,EAAE3H,KAAOwH,EAAOpM,MAAM4E,GAAK,IAAK2H,KAAMH,EAAOpM,OAAUuM,IAI7D,IAAK,gBAAiB,CACpB,MAAM,QAAEX,GAAYQ,EAYpB,OARIR,EACFD,EAAiBC,GAEjBO,EAAME,OAAOG,QAASxM,IACpB2L,EAAiB3L,EAAM4E,MAIpB,IACFuH,EACHE,OAAQF,EAAME,OAAOlJ,IAAKoJ,GACxBA,EAAE3H,KAAOgH,QAAuBnJ,IAAZmJ,EAChB,IACKW,EACHzJ,MAAM,GAERyJ,GAGV,CACA,IAAK,eACH,YAAuB9J,IAAnB2J,EAAOR,QACF,IACFO,EACHE,OAAQ,IAGL,IACFF,EACHE,OAAQF,EAAME,OAAOI,OAAQF,GAAMA,EAAE3H,KAAOwH,EAAOR,YAKrDc,EAA2C,GAEjD,IAAIC,EAAqB,CAAEN,OAAQ,IAEnC,SAASL,EAASI,GAChBO,EAAcT,EAAQS,EAAaP,GACnCM,EAAUF,QAASI,IACjBA,EAASD,IAEb,CAIA,SAAS3M,EAAKnE,GAAuB,OAAjBG,GAAcH,EAChC,MAAM+I,GAnHN4G,GAASA,EAAQ,GAAKqB,OAAOC,iBACtBtB,EAAMuB,YAyHPC,EAAUA,IAAMhB,EAAS,CAAEhI,KAAM,gBAAiB4H,QAAShH,IAcjE,OAZAoH,EAAS,CACPhI,KAAM,YACNhE,MAAO,IACFhE,EACH4I,KACA9B,MAAM,EACNC,aAAeD,IACRA,GAAMkK,QAKV,CACLpI,GAAIA,EACJoI,UACAC,OAtBcjR,GACdgQ,EAAS,CACPhI,KAAM,eACNhE,MAAO,IAAKhE,EAAO4I,QAqBzB,CAEA,SAAS3E,IACP,MAAOkM,EAAOe,GAAYtR,EAAAA,SAAsB+Q,GAYhD,OAVA/Q,EAAAA,UAAgB,KACd8Q,EAAUS,KAAKD,GACR,KACL,MAAM9J,EAAQsJ,EAAUU,QAAQF,GAC5B9J,GAAS,GACXsJ,EAAUW,OAAOjK,EAAO,KAG3B,CAAC+I,IAEG,IACFA,EACHnM,QACAgN,QAAUpB,GAAqBI,EAAS,CAAEhI,KAAM,gBAAiB4H,YAErE,C,uIClKA,MAAM0B,EAAgBC,EAAAA,GAAS,CAC7BhH,KAAMgH,EAAAA,KAAWC,IAAI,EAAG,sCACxBC,MAAOF,EAAAA,KAAWE,MAAM,yBACxBC,aAAcH,EAAAA,KAAWI,WACzBC,aAAcL,EAAAA,KAAWI,WACzBE,gBAAiBN,EAAAA,GAAS,CACxBO,OAAQP,EAAAA,KAAWI,WACnBI,KAAMR,EAAAA,KAAWI,WACjBxB,MAAOoB,EAAAA,KAAWI,WAClBK,QAAST,EAAAA,KAAWI,WACpBM,WAAYV,EAAAA,KAAWI,aACtBA,aAWCO,EAAYrS,IAcX,IAdY,KACjB0K,EAAI,MACJ4H,EAAK,OACLC,KACGpS,GAUJH,EACC,MAAM,QAAEwS,IAAYC,EAAAA,EAAAA,MAEpB,OACErS,EAAAA,EAAAA,KAACsS,EAAAA,GAAU,CACThI,KAAMA,EACN8H,QAASA,EACTD,OAAQ/R,IAAA,IAAC,MAAEmS,GAAOnS,EAAA,OAChB4B,EAAAA,EAAAA,MAAC7B,EAAAA,GAAQ,CAAAK,SAAA,EACPR,EAAAA,EAAAA,KAACK,EAAAA,GAAS,CAAAG,SAAE0R,KACZlS,EAAAA,EAAAA,KAACS,EAAAA,GAAW,CAAAD,SACT2R,EAAOI,MAEVvS,EAAAA,EAAAA,KAACY,EAAAA,GAAW,WAOf,SAAS4R,EAAWlS,GAAyC,IAAxC,KAAEiC,EAAI,UAAEY,GAA6B7C,EAC/D,MAAM,MAAEyD,IAAUC,EAAAA,EAAAA,OACVU,OAAQ+N,EAAa,UAAE7N,IAAc8N,EAAAA,EAAAA,IAAiB,CAC5DvP,UAAWA,KACTY,EAAM,CACJmC,MAAO,UACPC,YAAa,gDAEN,OAAThD,QAAS,IAATA,GAAAA,KAEFsD,QAAUrB,IACR,MAAMgF,EAAehF,aAAiB5B,MAAQ4B,EAAMxB,QAAU,2BAC9DG,EAAM,CACJmC,MAAO,QACPC,YAAaiE,EACbhE,QAAS,mBAKTuM,GAAOC,EAAAA,EAAAA,IAA2B,CACtCC,UAAUC,EAAAA,EAAAA,GAAYzB,GACtB0B,cAAe,CACbzI,KAAM/H,EAAK+H,KACXkH,MAAOjP,EAAKiP,MACZC,aAA4B,aAAdlP,EAAKyQ,KAAuBzQ,EAAsBkP,aAAe,GAC/EE,aAAc,GACdC,gBAAiB,CACfC,OAAQ,GACRC,KAAM,GACN5B,MAAO,GACP6B,QAAS,GACTC,WAAY,OASlB,OACEhS,EAAAA,EAAAA,KAACP,EAAAA,GAAI,IAAKkT,EAAInS,UACZwB,EAAAA,EAAAA,MAAA,QAAMwI,SAAUmI,EAAKM,aANPjO,IAChByN,EAAczN,KAKiClF,UAAU,YAAWU,SAAA,EAChEwB,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,wCAAuCU,SAAA,EACpDR,EAAAA,EAAAA,KAACiS,EAAS,CACR3H,KAAK,OACL4H,MAAM,YACNC,OAASI,IACPvS,EAAAA,EAAAA,KAAC0I,EAAAA,EAAK,CAACM,YAAY,cAAeuJ,OAItCvS,EAAAA,EAAAA,KAACiS,EAAS,CACR3H,KAAK,QACL4H,MAAM,QACNC,OAASI,IACPvS,EAAAA,EAAAA,KAAC0I,EAAAA,EAAK,CAACX,KAAK,QAAQiB,YAAY,mBAAmBtC,UAAQ,KAAK6L,MAIrD,aAAdhQ,EAAKyQ,OACJhR,EAAAA,EAAAA,MAAA2E,EAAAA,SAAA,CAAAnG,SAAA,EACER,EAAAA,EAAAA,KAACiS,EAAS,CACR3H,KAAK,eACL4H,MAAM,gBACNC,OAASI,IACPvS,EAAAA,EAAAA,KAAC0I,EAAAA,EAAK,CAACM,YAAY,eAAgBuJ,OAIvCvS,EAAAA,EAAAA,KAACiS,EAAS,CACR3H,KAAK,eACL4H,MAAM,gBACNC,OAASI,IACPvS,EAAAA,EAAAA,KAAC0I,EAAAA,EAAK,CAACM,YAAY,YAAauJ,OAIpCvQ,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,uBAAsBU,SAAA,EACnCR,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sBAAqBU,SAAC,sBACpCwB,EAAAA,EAAAA,MAAA,OAAKlC,UAAU,wCAAuCU,SAAA,EACpDR,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gBAAeU,UAC5BR,EAAAA,EAAAA,KAACiS,EAAS,CACR3H,KAAK,yBACL4H,MAAM,iBACNC,OAASI,IACPvS,EAAAA,EAAAA,KAAC0I,EAAAA,EAAK,CAACM,YAAY,iBAAkBuJ,SAK3CvS,EAAAA,EAAAA,KAACiS,EAAS,CACR3H,KAAK,uBACL4H,MAAM,OACNC,OAASI,IACPvS,EAAAA,EAAAA,KAAC0I,EAAAA,EAAK,CAACM,YAAY,cAAeuJ,OAItCvS,EAAAA,EAAAA,KAACiS,EAAS,CACR3H,KAAK,wBACL4H,MAAM,iBACNC,OAASI,IACPvS,EAAAA,EAAAA,KAAC0I,EAAAA,EAAK,CAACM,YAAY,QAASuJ,OAIhCvS,EAAAA,EAAAA,KAACiS,EAAS,CACR3H,KAAK,6BACL4H,MAAM,cACNC,OAASI,IACPvS,EAAAA,EAAAA,KAAC0I,EAAAA,EAAK,CAACM,YAAY,WAAYuJ,OAInCvS,EAAAA,EAAAA,KAACiS,EAAS,CACR3H,KAAK,0BACL4H,MAAM,UACNC,OAASI,IACPvS,EAAAA,EAAAA,KAAC0I,EAAAA,EAAK,CAACM,YAAY,mBAAoBuJ,mBASrDvS,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mBAAkBU,UAC/BwB,EAAAA,EAAAA,MAACqE,EAAAA,EAAM,CAAC0B,KAAK,SAASrB,SAAU9B,EAAUpE,SAAA,CACvCoE,IAAa5E,EAAAA,EAAAA,KAAC4G,EAAAA,EAAO,CAAC9G,UAAU,8BAA+B,0BAO5E,C,oECzLA,MAAMoT,EAQErQ,eAAgBsQ,GAA0D,IAA7CnO,EAASoO,UAAA1I,OAAA,QAAAlE,IAAA4M,UAAA,GAAAA,UAAA,GAAG,CAAC,EAC9C,MAAMxH,QAAiBC,MAAM,OAAOsH,IAAO,CACzCjH,OAAQ,OACRC,QAAS,CACP,eAAgB,oBAElB/D,KAAMgE,KAAKC,UAAUrH,KAEvB,IAAK4G,EAASE,GACZ,MAAM,IAAItI,MAAM,+BAElB,OAAOoI,EAASG,MAClB,EApBImH,EAqBCrQ,eAAgBsQ,GAA0D,IAA7CnO,EAASoO,UAAA1I,OAAA,QAAAlE,IAAA4M,UAAA,GAAAA,UAAA,GAAG,CAAC,EAC7C,MAAMxH,QAAiBC,MAAM,OAAOsH,IAAO,CACzCjH,OAAQ,MACRC,QAAS,CACP,eAAgB,oBAElB/D,KAAMgE,KAAKC,UAAUrH,KAEvB,IAAK4G,EAASE,GACZ,MAAM,IAAItI,MAAM,+BAElB,OAAOoI,EAASG,MAClB,EAsFW2G,EAAmB,WAAoE,IAAnEW,EAAwDD,UAAA1I,OAAA,QAAAlE,IAAA4M,UAAA,GAAAA,UAAA,GAAG,CAAC,EAC3F,MAAM3Q,GAAcC,EAAAA,EAAAA,MAEpB,OAAOC,EAAAA,EAAAA,GAAsD,CAC3DC,WAAYC,gBACaqQ,EAA4B,YAAaI,IAChDtO,KAElB7B,UAAWA,CAAC6B,EAAMyJ,EAAW8E,KAC3B9Q,EAAY+Q,aAAa,CAAC,eAAgBxO,GACtCqO,EAAQlQ,WACVkQ,EAAQlQ,UAAU6B,EAAMyJ,EAAW8E,IAGvC9M,QAAS4M,EAAQ5M,QACjBgN,UAAWJ,EAAQI,UACnBC,SAAUL,EAAQK,UAEtB,EAEavJ,EAAoB,WAA8E,IAA7EkJ,EAAkED,UAAA1I,OAAA,QAAAlE,IAAA4M,UAAA,GAAAA,UAAA,GAAG,CAAC,EACtG,OAAOzQ,EAAAA,EAAAA,GAAgE,CACrEC,WAAYC,gBACaqQ,EAAsC,wBAAyBS,IACtE3O,KAElB7B,UAAWkQ,EAAQlQ,UACnBsD,QAAS4M,EAAQ5M,QACjBgN,UAAWJ,EAAQI,UACnBC,SAAUL,EAAQK,UAEtB,C,sFC3KA,MAAME,GAAiB7I,EAAAA,EAAAA,GACrB,yRACA,CACE8I,SAAU,CACRzN,QAAS,CACP0N,QAAS,yDACTC,YACE,qEACFC,QACE,iFACFC,UACE,+DACFC,MAAO,+CACPC,KAAM,mDAER7F,KAAM,CACJwF,QAAS,iBACTM,GAAI,sBACJC,GAAI,uBACJC,KAAM,cAGVC,gBAAiB,CACfnO,QAAS,UACTkI,KAAM,aAWNjI,EAAS1G,EAAAA,WACb,CAAAC,EAA0DC,KAAS,IAAlE,UAAEC,EAAS,QAAEsG,EAAO,KAAEkI,EAAI,QAAEV,GAAU,KAAU7N,GAAOH,EACtD,MAAM4U,EAAO5G,EAAU6G,EAAAA,GAAO,SAC9B,OACEzU,EAAAA,EAAAA,KAACwU,EAAI,CACH1U,WAAWG,EAAAA,EAAAA,IAAG2T,EAAe,CAAExN,UAASkI,OAAMxO,eAC9CD,IAAKA,KACDE,MAKZsG,EAAOnG,YAAc,Q,mEC9CrB,MAAMwI,EAAQ/I,EAAAA,WACZ,CAAAC,EAAgCC,KAAS,IAAxC,UAAEC,EAAS,KAAEiI,KAAShI,GAAOH,EAC5B,OACEI,EAAAA,EAAAA,KAAA,SACE+H,KAAMA,EACNjI,WAAWG,EAAAA,EAAAA,IACT,+VACAH,GAEFD,IAAKA,KACDE,MAKZ2I,EAAMxI,YAAc,O", "sources": ["components/ui/form.tsx", "components/ui/table.tsx", "components/ui/dialog.tsx", "hooks/useTwoFactorAuth.ts", "components/profile/TwoFactorAuth.tsx", "components/profile/PasswordChangeForm.tsx", "components/ui/label.tsx", "components/ui/dropdown-menu.tsx", "components/profile/ApiKeys.tsx", "components/ui/tabs.tsx", "components/ui/card.tsx", "components/ui/use-toast.ts", "components/profile/ProfileForm.tsx", "hooks/useUser.ts", "components/ui/button.tsx", "components/ui/input.tsx"], "sourcesContent": ["import * as React from 'react';\nimport * as FormPrimitive from '@radix-ui/react-form';\nimport { Slot } from '@radix-ui/react-slot';\nimport { cn } from '../../lib/utils';\n\nconst Form = FormPrimitive.Root;\n\nconst FormField = React.forwardRef<\n  React.ElementRef<typeof FormPrimitive.Field>,\n  React.ComponentPropsWithoutRef<typeof FormPrimitive.Field>\n>(({ className, ...props }, ref) => (\n  <FormPrimitive.Field\n    ref={ref}\n    className={cn('space-y-2', className)}\n    {...props}\n  />\n));\nFormField.displayName = 'FormField';\n\nconst FormItem = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn('space-y-2', className)} {...props} />\n));\nFormItem.displayName = 'FormItem';\n\nconst FormLabel = React.forwardRef<\n  React.ElementRef<typeof FormPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof FormPrimitive.Label> & {\n    required?: boolean;\n  }\n>(({ className, required, children, ...props }, ref) => (\n  <FormPrimitive.Label\n    ref={ref}\n    className={cn(\n      'block text-sm font-medium text-gray-700 dark:text-gray-300',\n      required && \"after:content-['*'] after:ml-0.5 after:text-red-500\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n  </FormPrimitive.Label>\n));\nFormLabel.displayName = 'FormLabel';\n\nconst FormControl = React.forwardRef<\n  React.ElementRef<typeof FormPrimitive.Control>,\n  React.ComponentPropsWithoutRef<typeof FormPrimitive.Control>\n>(({ className, ...props }, ref) => (\n  <FormPrimitive.Control\n    ref={ref}\n    className={cn(\n      'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',\n      className\n    )}\n    {...props}\n  />\n));\nFormControl.displayName = 'FormControl';\n\nconst FormDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-muted-foreground', className)}\n    {...props}\n  />\n));\nFormDescription.displayName = 'FormDescription';\n\nconst FormMessage = React.forwardRef<\n  React.ElementRef<typeof FormPrimitive.Message>,\n  React.ComponentPropsWithoutRef<typeof FormPrimitive.Message>\n>(({ className, children, ...props }, ref) => (\n  <FormPrimitive.Message\n    ref={ref}\n    className={cn('text-sm font-medium text-destructive', className)}\n    {...props}\n  >\n    {children}\n  </FormPrimitive.Message>\n));\nFormMessage.displayName = 'FormMessage';\n\nexport {\n  Form,\n  FormField,\n  FormItem,\n  FormLabel,\n  FormControl,\n  FormDescription,\n  FormMessage,\n};\n", "import * as React from \"react\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst Table = React.forwardRef<\n  HTMLTableElement,\n  React.HTMLAttributes<HTMLTableElement>\n>(({ className, ...props }, ref) => (\n  <div className=\"w-full overflow-auto\">\n    <table\n      ref={ref}\n      className={cn(\"w-full caption-bottom text-sm\", className)}\n      {...props}\n    />\n  </div>\n))\nTable.displayName = \"Table\"\n\nconst TableHeader = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <thead ref={ref} className={cn(\"[&_tr]:border-b\", className)} {...props} />\n))\nTableHeader.displayName = \"TableHeader\"\n\nconst TableBody = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tbody\n    ref={ref}\n    className={cn(\"[&_tr:last-child]:border-0\", className)}\n    {...props}\n  />\n))\nTableBody.displayName = \"TableBody\"\n\nconst TableFooter = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tfoot\n    ref={ref}\n    className={cn(\"bg-primary font-medium text-primary-foreground\", className)}\n    {...props}\n  />\n))\nTableFooter.displayName = \"TableFooter\"\n\nconst TableRow = React.forwardRef<\n  HTMLTableRowElement,\n  React.HTMLAttributes<HTMLTableRowElement>\n>(({ className, ...props }, ref) => (\n  <tr\n    ref={ref}\n    className={cn(\n      \"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nTableRow.displayName = \"TableRow\"\n\nconst TableHead = React.forwardRef<\n  HTMLTableCellElement,\n  React.ThHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <th\n    ref={ref}\n    className={cn(\n      \"h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0\",\n      className\n    )}\n    {...props}\n  />\n))\nTableHead.displayName = \"TableHead\"\n\nconst TableCell = React.forwardRef<\n  HTMLTableCellElement,\n  React.TdHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <td\n    ref={ref}\n    className={cn(\"p-4 align-middle [&:has([role=checkbox])]:pr-0\", className)}\n    {...props}\n  />\n))\nTableCell.displayName = \"TableCell\"\n\nconst TableCaption = React.forwardRef<\n  HTMLTableCaptionElement,\n  React.HTMLAttributes<HTMLTableCaptionElement>\n>(({ className, ...props }, ref) => (\n  <caption\n    ref={ref}\n    className={cn(\"mt-4 text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nTableCaption.displayName = \"TableCaption\"\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n", "import * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { X } from \"lucide-react\"\nimport { cn } from \"../../lib/utils\"\n\nconst Dialog = DialogPrimitive.Root\n\nconst DialogTrigger = DialogPrimitive.Trigger\n\ninterface DialogPortalProps extends DialogPrimitive.DialogPortalProps {\n  className?: string;\n  children?: React.ReactNode;\n}\n\nconst DialogPortal = ({\n  className,\n  children,\n  ...props\n}: DialogPortalProps) => {\n  const { container, forceMount, ...restProps } = props;\n  const portalProps = { \n    container,\n    forceMount: forceMount as true | undefined \n  };\n  return (\n    <DialogPrimitive.Portal {...portalProps}>\n      <div className={cn(\n        \"fixed inset-0 z-50 flex items-start justify-center sm:items-center\",\n        className\n      )}>\n        {children}\n      </div>\n    </DialogPrimitive.Portal>\n  )\n}\nDialogPortal.displayName = DialogPrimitive.Portal.displayName\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/50 backdrop-blur-sm transition-all duration-100 data-[state=closed]:animate-out data-[state=closed]:fade-out data-[state=open]:fade-in\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed z-50 grid w-full gap-4 rounded-b-lg bg-white p-6 animate-in data-[state=open]:fade-in-90 data-[state=open]:slide-in-from-bottom-10 sm:max-w-lg sm:rounded-lg sm:zoom-in-90 data-[state=open]:sm:slide-in-from-bottom-0\",\n        \"dark:bg-gray-900\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-gray-100 dark:focus:ring-gray-400 dark:focus:ring-offset-gray-900 dark:data-[state=open]:bg-gray-800\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n))\nDialogContent.displayName = DialogPrimitive.Content.displayName\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold text-gray-900\",\n      \"dark:text-gray-50\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = DialogPrimitive.Title.displayName\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-gray-500\", \"dark:text-gray-400\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = DialogPrimitive.Description.displayName\n\nexport {\n  Dialog,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n}\n", "import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';\nimport { useAuth } from '../contexts/AuthContext';\nimport { toast } from '../components/ui/use-toast';\n\n// Types\nexport interface TwoFactorSetupResponse {\n  qrCodeData: string;\n  secret: string;\n}\n\nexport interface TwoFactorVerifyResponse {\n  recoveryCodes: string[];\n  success: boolean;\n}\n\nexport interface TwoFactorDisableResponse {\n  success: boolean;\n  message?: string;\n}\n\n// Hook to set up two-factor authentication\nexport const useSetupTwoFactor = () => {\n  const { user } = useAuth();\n  const queryClient = useQueryClient();\n\n  return useMutation<TwoFactorSetupResponse, Error>({\n    mutationFn: async () => {\n      // In a real app, this would be an API call\n      // const response = await fetch('/api/auth/2fa/setup', {\n      //   method: 'POST',\n      //   headers: { 'Content-Type': 'application/json' },\n      //   body: JSON.stringify({ userId: user?.id })\n      // });\n      // if (!response.ok) throw new Error('Failed to set up two-factor authentication');\n      // return response.json();\n      \n      // Mock implementation\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          resolve({\n            qrCodeData: `data:image/svg+xml;base64,mock-qr-code-data`,\n            secret: 'MOCK_SECRET_KEY',\n          });\n        }, 500);\n      });\n    },\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: ['user', '2fa'] });\n    },\n  });\n};\n\n// Hook to verify two-factor authentication setup\nexport const useVerifyTwoFactor = () => {\n  const queryClient = useQueryClient();\n\n  return useMutation<TwoFactorVerifyResponse, Error, { code: string }>({\n    mutationFn: async ({ code }) => {\n      // In a real app, this would be an API call\n      // const response = await fetch('/api/auth/2fa/verify', {\n      //   method: 'POST',\n      //   headers: { 'Content-Type': 'application/json' },\n      //   body: JSON.stringify({ code })\n      // });\n      // if (!response.ok) throw new Error('Invalid verification code');\n      // return response.json();\n      \n      // Mock implementation\n      if (code !== '123456') {\n        throw new Error('Invalid verification code');\n      }\n      \n      return new Promise((resolve) => {\n        setTimeout(() => {\n          resolve({\n            recoveryCodes: ['mock1', 'mock2', 'mock3', 'mock4', 'mock5'],\n            success: true,\n          });\n        }, 500);\n      });\n    },\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: ['user', '2fa'] });\n    },\n  });\n};\n\n// Hook to disable two-factor authentication\nexport const useDisableTwoFactor = () => {\n  const queryClient = useQueryClient();\n  const { user } = useAuth();\n\n  return useMutation<TwoFactorDisableResponse, Error, { code: string }>({\n    mutationFn: async ({ code }) => {\n      // In a real app, this would be an API call\n      // const response = await fetch('/api/auth/2fa/disable', {\n      //   method: 'POST',\n      //   headers: { 'Content-Type': 'application/json' },\n      //   body: JSON.stringify({ userId: user?.id, code })\n      // });\n      // if (!response.ok) throw new Error('Failed to disable two-factor authentication');\n      // return response.json();\n      \n      // Mock implementation\n      if (code !== '123456') {\n        throw new Error('Invalid verification code');\n      }\n      \n      return new Promise((resolve) => {\n        setTimeout(() => {\n          resolve({\n            success: true,\n            message: 'Two-factor authentication has been disabled.'\n          });\n        }, 500);\n      });\n    },\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: ['user', '2fa'] });\n    },\n  });\n};\n\n// Hook to check if two-factor authentication is enabled\nexport const useIsTwoFactorEnabled = () => {\n  const { user } = useAuth();\n  \n  return useQuery<{ enabled: boolean }, Error>({\n    queryKey: ['user', '2fa', user?.id],\n    queryFn: async () => {\n      // In a real app, this would be an API call\n      // const response = await fetch(`/api/users/${user?.id}/2fa/status`);\n      // if (!response.ok) throw new Error('Failed to fetch 2FA status');\n      // return response.json();\n      \n      // Mock implementation - default to false if not defined\n      return { \n        enabled: user ? (user as any).twoFactorEnabled === true : false \n      };\n    },\n    enabled: !!user?.id,\n  });\n};\n", "import { useState, useEffect } from 'react';\nimport { Button } from '../ui/button';\nimport { Input } from '../ui/input';\nimport { Label } from '../ui/label';\nimport { useToast } from '../ui/use-toast';\nimport { \n  useSetupTwoFactor, \n  useVerifyTwoFactor, \n  useDisableTwoFactor,\n  type TwoFactorSetupResponse,\n  type TwoFactorVerifyResponse,\n  type TwoFactorDisableResponse\n} from '../../hooks/useTwoFactorAuth';\nimport { Loader2, CheckCircle2, AlertCircle, QrCode } from 'lucide-react';\nimport { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '../ui/dialog';\n\ninterface TwoFactorAuthProps {\n  twoFactorEnabled?: boolean;\n}\n\nexport function TwoFactorAuth({ twoFactorEnabled = false }: TwoFactorAuthProps) {\n  const { toast } = useToast();\n  const [isDialogOpen, setIsDialogOpen] = useState(false);\n  const [verificationCode, setVerificationCode] = useState('');\n  const [qrCodeData, setQrCodeData] = useState('');\n  const [recoveryCodes, setRecoveryCodes] = useState<string[]>([]);\n  const [showRecoveryCodes, setShowRecoveryCodes] = useState(false);\n\n  // Setup mutations with proper types\n  const {\n    mutate: setupTwoFactor,\n    isPending: isSettingUp,\n    isSuccess: isSetupSuccess,\n    data: setupData,\n    isError: isSetupError,\n    error: setupError\n  } = useSetupTwoFactor();\n  \n  const {\n    mutate: verifyTwoFactor,\n    isPending: isVerifying,\n    isSuccess: isVerifySuccess,\n    data: verifyData,\n    isError: isVerifyError,\n    error: verifyError\n  } = useVerifyTwoFactor();\n  \n  const {\n    mutate: disableTwoFactor,\n    isPending: isDisabling,\n    isSuccess: isDisableSuccess,\n    isError: isDisableError,\n    error: disableError\n  } = useDisableTwoFactor();\n  \n  // Handle setup success/error with useEffect\n  useEffect(() => {\n    if (isSetupSuccess && setupData) {\n      setQrCodeData(setupData.qrCodeData);\n      setIsDialogOpen(true);\n    }\n  }, [isSetupSuccess, setupData]);\n  \n  // Handle verify success/error with useEffect\n  useEffect(() => {\n    if (isVerifySuccess && verifyData) {\n      setRecoveryCodes(verifyData.recoveryCodes);\n      setShowRecoveryCodes(true);\n      toast({\n        title: 'Success',\n        description: 'Two-factor authentication has been enabled successfully.',\n      });\n    }\n  }, [isVerifySuccess, verifyData, toast]);\n  \n  // Handle errors with useEffect\n  useEffect(() => {\n    if (isSetupError && setupError) {\n      toast({\n        title: 'Error',\n        description: setupError.message || 'Failed to set up two-factor authentication',\n        variant: 'destructive',\n      });\n    }\n    \n    if (isVerifyError && verifyError) {\n      toast({\n        title: 'Error',\n        description: verifyError.message || 'Failed to verify two-factor authentication code',\n        variant: 'destructive',\n      });\n    }\n    \n    if (isDisableError && disableError) {\n      toast({\n        title: 'Error',\n        description: disableError.message || 'Failed to disable two-factor authentication',\n        variant: 'destructive',\n      });\n    }\n  }, [isSetupError, setupError, isVerifyError, verifyError, isDisableError, disableError, toast]);\n  \n  // Handle disable success\n  useEffect(() => {\n    if (isDisableSuccess) {\n      toast({\n        title: 'Success',\n        description: 'Two-factor authentication has been disabled successfully.',\n      });\n      setIsDialogOpen(false);\n      setVerificationCode('');\n    }\n  }, [isDisableSuccess, toast]);\n\n  const handleDisable2FA = () => {\n    if (!verificationCode.trim()) {\n      toast({\n        title: 'Error',\n        description: 'Please enter a verification code',\n        variant: 'destructive',\n      });\n      return;\n    }\n    \n    disableTwoFactor(\n      { code: verificationCode },\n      {\n        onSuccess: () => {\n          setVerificationCode('');\n          setQrCodeData('');\n          setRecoveryCodes([]);\n          setShowRecoveryCodes(false);\n        },\n        onError: (error: Error) => {\n          toast({\n            title: 'Error',\n            description: error.message || 'Failed to disable two-factor authentication',\n            variant: 'destructive',\n          });\n        },\n      }\n    );\n  };\n\n  const handleCopyRecoveryCodes = () => {\n    navigator.clipboard.writeText(recoveryCodes.join('\\n'));\n    toast({\n      title: 'Copied',\n      description: 'Recovery codes copied to clipboard',\n    });\n  };\n\n  const handleDownloadRecoveryCodes = () => {\n    const element = document.createElement('a');\n    const file = new Blob([recoveryCodes.join('\\n')], { type: 'text/plain' });\n    element.href = URL.createObjectURL(file);\n    element.download = 'recovery-codes.txt';\n    document.body.appendChild(element);\n    element.click();\n    document.body.removeChild(element);\n  };\n\n  const handleSetup2FA = () => {\n    setupTwoFactor(undefined, {\n      onSuccess: (data: TwoFactorSetupResponse) => {\n        setQrCodeData(data.qrCodeData);\n        setIsDialogOpen(true);\n      },\n      onError: (error: Error) => {\n        toast({\n          title: 'Error',\n          description: error.message || 'Failed to set up two-factor authentication',\n          variant: 'destructive',\n        });\n      },\n    });\n  };\n  \n  const handleVerifyCode = () => {\n    if (!verificationCode.trim()) {\n      toast({\n        title: 'Error',\n        description: 'Please enter a verification code',\n        variant: 'destructive',\n      });\n      return;\n    }\n    \n    verifyTwoFactor(\n      { code: verificationCode },\n      {\n        onSuccess: (data: TwoFactorVerifyResponse) => {\n          setRecoveryCodes(data.recoveryCodes);\n          setShowRecoveryCodes(true);\n          toast({\n            title: 'Success',\n            description: 'Two-factor authentication has been enabled successfully.',\n          });\n        },\n        onError: (error: Error) => {\n          toast({\n            title: 'Error',\n            description: error.message || 'Failed to verify two-factor authentication code',\n            variant: 'destructive',\n          });\n        },\n      }\n    );\n  };\n\n  return (\n    <div className=\"space-y-4\">\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h3 className=\"text-base font-medium\">Two-Factor Authentication</h3>\n          <p className=\"text-sm text-muted-foreground\">\n            {twoFactorEnabled\n              ? 'Two-factor authentication is enabled.'\n              : 'Add an extra layer of security to your account.'}\n          </p>\n        </div>\n        {twoFactorEnabled ? (\n          <Button\n            variant=\"destructive\"\n            onClick={() => setIsDialogOpen(true)}\n          >\n            Disable 2FA\n          </Button>\n        ) : (\n          <Button\n            onClick={handleSetup2FA}\n            disabled={isSettingUp}\n          >\n            {isSettingUp ? (\n              <>\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                Setting up...\n              </>\n            ) : (\n              'Enable 2FA'\n            )}\n          </Button>\n        )}\n      </div>\n\n      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>\n        <DialogContent className=\"sm:max-w-[425px]\">\n          <DialogHeader>\n            <DialogTitle>{twoFactorEnabled ? 'Disable two-factor authentication' : 'Set up two-factor authentication'}</DialogTitle>\n            <DialogDescription>\n              {twoFactorEnabled ? 'Enter your verification code to disable two-factor authentication.' : 'Scan the QR code with your authenticator app and enter the verification code.'}\n            </DialogDescription>\n          </DialogHeader>\n          \n          {!twoFactorEnabled && qrCodeData && !showRecoveryCodes && (\n            <div className=\"flex flex-col items-center space-y-4\">\n              <div className=\"p-4 bg-white rounded-md\">\n                <img src={qrCodeData} alt=\"QR Code\" className=\"w-48 h-48\" />\n              </div>\n              <p className=\"text-sm text-muted-foreground\">\n                Scan this QR code with your authenticator app\n              </p>\n            </div>\n          )}\n          \n          {showRecoveryCodes ? (\n            <div className=\"space-y-4\">\n              <div className=\"bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-md\">\n                <div className=\"flex\">\n                  <AlertCircle className=\"h-5 w-5 text-yellow-500 mr-2 mt-0.5 flex-shrink-0\" />\n                  <p className=\"text-sm text-yellow-700 dark:text-yellow-300\">\n                    Please save these recovery codes in a safe place. You can use them to recover access to your account if you lose your device.\n                  </p>\n                </div>\n              </div>\n              \n              <div className=\"grid grid-cols-2 gap-2\">\n                {recoveryCodes.map((code, index) => (\n                  <div key={index} className=\"font-mono text-sm p-2 bg-muted rounded\">\n                    {code}\n                  </div>\n                ))}\n              </div>\n              \n              <div className=\"flex space-x-2\">\n                <Button variant=\"outline\" onClick={handleCopyRecoveryCodes}>\n                  Copy Codes\n                </Button>\n                <Button variant=\"outline\" onClick={handleDownloadRecoveryCodes}>\n                  Download\n                </Button>\n              </div>\n              \n              <Button className=\"w-full\" onClick={() => {\n                setShowRecoveryCodes(false);\n                setIsDialogOpen(false);\n              }}>\n                I've saved my recovery codes\n              </Button>\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"verification-code\">\n                  {twoFactorEnabled ? 'Enter verification code to disable 2FA' : 'Enter verification code from your authenticator app'}\n                </Label>\n                <Input\n                  id=\"verification-code\"\n                  value={verificationCode}\n                  onChange={(e) => setVerificationCode(e.target.value)}\n                  placeholder=\"123456\"\n                  className=\"text-center text-xl font-mono tracking-widest\"\n                  autoComplete=\"one-time-code\"\n                />\n              </div>\n              \n              <Button \n                className=\"w-full\" \n                onClick={twoFactorEnabled ? handleDisable2FA : handleVerifyCode}\n                disabled={twoFactorEnabled ? isDisabling : isVerifying}\n              >\n                {twoFactorEnabled ? (\n                  isDisabling ? (\n                    <>\n                      <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                      Disabling...\n                    </>\n                  ) : (\n                    'Disable 2FA'\n                  )\n                ) : isVerifying ? (\n                  <>\n                    <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                    Verifying...\n                  </>\n                ) : (\n                  'Verify and Enable 2FA'\n                )}\n              </Button>\n            </div>\n          )}\n        </DialogContent>\n      </Dialog>\n    </div>\n  );\n}\n", "import { useState } from 'react';\nimport { But<PERSON> } from '../ui/button';\nimport {\n  Form,\n  FormControl,\n  FormField,\n  FormItem,\n  FormLabel,\n  FormMessage,\n} from '../ui/form';\nimport { Input } from '../ui/input';\nimport { useToast } from '../ui/use-toast';\nimport { useChangePassword, type ChangePasswordData } from '../../hooks/useUser';\nimport { Loader2, Eye, EyeOff } from 'lucide-react';\n\n// Validation will be handled by the form component\ninterface PasswordFormData {\n  currentPassword: string;\n  newPassword: string;\n  confirmPassword: string;\n}\n\nexport function PasswordChangeForm() {\n  const { toast } = useToast();\n  const [showCurrentPassword, setShowCurrentPassword] = useState(false);\n  const [showNewPassword, setShowNewPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [formData, setFormData] = useState<PasswordFormData>({\n    currentPassword: '',\n    newPassword: '',\n    confirmPassword: ''\n  });\n  const [isPending, setIsPending] = useState(false);\n  \n  const { mutate: changePassword } = useChangePassword({\n    onSuccess: () => {\n      toast({\n        title: 'Success',\n        description: 'Your password has been updated successfully.',\n      });\n      // Reset form\n      setFormData({\n        currentPassword: '',\n        newPassword: '',\n        confirmPassword: ''\n      });\n    },\n    onError: (error: unknown) => {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to update password';\n      toast({\n        title: 'Error',\n        description: errorMessage,\n        variant: 'destructive',\n      });\n    }\n  });\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    // Basic validation\n    if (formData.newPassword !== formData.confirmPassword) {\n      toast({\n        title: 'Error',\n        description: 'Passwords do not match',\n        variant: 'destructive',\n      });\n      return;\n    }\n\n    if (formData.newPassword.length < 8) {\n      toast({\n        title: 'Error',\n        description: 'Password must be at least 8 characters',\n        variant: 'destructive',\n      });\n      return;\n    }\n\n    if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[^\\da-zA-Z]).{8,}$/.test(formData.newPassword)) {\n      toast({\n        title: 'Error',\n        description: 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',\n        variant: 'destructive',\n      });\n      return;\n    }\n\n    setIsPending(true);\n    changePassword({\n      currentPassword: formData.currentPassword,\n      newPassword: formData.newPassword\n    });\n    setIsPending(false);\n  };\n\n  return (\n    <Form onSubmit={handleSubmit} className=\"space-y-6\">\n      <div className=\"space-y-4\">\n        <FormItem>\n          <FormLabel>Current Password</FormLabel>\n          <div className=\"relative\">\n            <FormControl>\n              <Input\n                type={showCurrentPassword ? 'text' : 'password'}\n                name=\"currentPassword\"\n                value={formData.currentPassword}\n                onChange={handleChange}\n                placeholder=\"Enter current password\"\n                required\n              />\n            </FormControl>\n            <button\n              type=\"button\"\n              className=\"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700\"\n              onClick={() => setShowCurrentPassword(!showCurrentPassword)}\n            >\n              {showCurrentPassword ? (\n                <EyeOff className=\"h-4 w-4\" />\n              ) : (\n                <Eye className=\"h-4 w-4\" />\n              )}\n            </button>\n          </div>\n          <FormMessage />\n        </FormItem>\n\n        <FormItem>\n          <FormLabel>New Password</FormLabel>\n          <div className=\"relative\">\n            <FormControl>\n              <Input\n                type={showNewPassword ? 'text' : 'password'}\n                name=\"newPassword\"\n                value={formData.newPassword}\n                onChange={handleChange}\n                placeholder=\"Enter new password\"\n                required\n              />\n            </FormControl>\n            <button\n              type=\"button\"\n              className=\"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700\"\n              onClick={() => setShowNewPassword(!showNewPassword)}\n            >\n              {showNewPassword ? (\n                <EyeOff className=\"h-4 w-4\" />\n              ) : (\n                <Eye className=\"h-4 w-4\" />\n              )}\n            </button>\n          </div>\n          <p className=\"text-xs text-muted-foreground mt-1\">\n            Must be at least 8 characters and include uppercase, lowercase, number, and special character.\n          </p>\n          <FormMessage />\n        </FormItem>\n\n        <FormItem>\n          <FormLabel>Confirm New Password</FormLabel>\n          <div className=\"relative\">\n            <FormControl>\n              <Input\n                type={showConfirmPassword ? 'text' : 'password'}\n                name=\"confirmPassword\"\n                value={formData.confirmPassword}\n                onChange={handleChange}\n                placeholder=\"Confirm new password\"\n                required\n              />\n            </FormControl>\n            <button\n              type=\"button\"\n              className=\"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700\"\n              onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n            >\n              {showConfirmPassword ? (\n                <EyeOff className=\"h-4 w-4\" />\n              ) : (\n                <Eye className=\"h-4 w-4\" />\n              )}\n            </button>\n          </div>\n          <FormMessage />\n        </FormItem>\n      </div>\n\n      <Button type=\"submit\" disabled={isPending}>\n        {isPending && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n        Update Password\n      </Button>\n    </Form>\n  );\n}\n", "import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  HTMLLabelElement,\n  React.LabelHTMLAttributes<HTMLLabelElement> & VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <label\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = \"Label\"\n\nexport { Label }\n", "import * as React from 'react';\nimport * as DropdownMenuPrimitive from '@radix-ui/react-dropdown-menu';\nimport { Check, ChevronRight, Circle } from 'lucide-react';\n\nimport { cn } from '../../lib/utils';\n\nconst DropdownMenu = DropdownMenuPrimitive.Root;\n\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger;\n\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group;\n\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal;\n\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub;\n\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup;\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\n    inset?: boolean;\n    children?: React.ReactNode;\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      'flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent',\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto h-4 w-4\" />\n  </DropdownMenuPrimitive.SubTrigger>\n));\nDropdownMenuSubTrigger.displayName =\n  DropdownMenuPrimitive.SubTrigger.displayName;\n\nconst DropdownMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\n  Omit<React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>, 'ref'>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      'z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',\n      className\n    )}\n    {...props}\n  />\n));\nDropdownMenuSubContent.displayName =\n  DropdownMenuPrimitive.SubContent.displayName;\n\nconst DropdownMenuContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\n  Omit<React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>, 'ref'>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <DropdownMenuPrimitive.Portal>\n    <DropdownMenuPrimitive.Content\n      ref={ref}\n      sideOffset={sideOffset}\n      className={cn(\n        'z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',\n        className\n      )}\n      {...props}\n    />\n  </DropdownMenuPrimitive.Portal>\n));\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName;\n\nconst DropdownMenuItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\n    inset?: boolean;\n    children?: React.ReactNode;\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      'relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  />\n));\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName;\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <DropdownMenuPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      'relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\n      className\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.CheckboxItem>\n));\nDropdownMenuCheckboxItem.displayName =\n  DropdownMenuPrimitive.CheckboxItem.displayName;\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      'relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Circle className=\"h-2 w-2 fill-current\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.RadioItem>\n));\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName;\n\nconst DropdownMenuLabel = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\n    inset?: boolean;\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      'px-2 py-1.5 text-sm font-semibold',\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  />\n));\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName;\n\nconst DropdownMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.Separator\n    ref={ref}\n    className={cn('-mx-1 my-1 h-px bg-muted', className)}\n    {...props}\n  />\n));\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName;\n\nconst DropdownMenuShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn('ml-auto text-xs tracking-widest opacity-60', className)}\n      {...props}\n    />\n  );\n};\nDropdownMenuShortcut.displayName = 'DropdownMenuShortcut';\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuGroup,\n  DropdownMenuPortal,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuRadioGroup,\n};\n", "import { useState } from 'react';\nimport { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';\nimport { Button } from '../ui/button';\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/table';\nimport { Input } from '../ui/input';\nimport { Label } from '../ui/label';\nimport { useToast } from '../ui/use-toast';\nimport { \n  Dialog, \n  DialogContent, \n  DialogDescription, \n  DialogFooter, \n  DialogHeader, \n  DialogTitle, \n  DialogTrigger \n} from '../ui/dialog';\nimport { \n  DropdownMenu, \n  DropdownMenuContent, \n  DropdownMenuItem, \n  DropdownMenuTrigger \n} from '../ui/dropdown-menu';\nimport { \n  Copy, \n  Loader2, \n  MoreVertical, \n  Plus, \n  Trash2, \n  CheckCircle2, \n  AlertCircle \n} from 'lucide-react';\nimport { ApiKey } from '../../types/trader';\n\n// API service functions\nconst fetchApiKeys = async (): Promise<ApiKey[]> => {\n  try {\n    const response = await fetch('/api/keys');\n    if (!response.ok) throw new Error('Failed to fetch API keys');\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching API keys:', error);\n    throw error;\n  }\n};\n\nconst createApiKey = async (name: string): Promise<ApiKey> => {\n  try {\n    const response = await fetch('/api/keys', {\n      method: 'POST',\n      headers: { 'Content-Type': 'application/json' },\n      body: JSON.stringify({ name }),\n    });\n    if (!response.ok) throw new Error('Failed to create API key');\n    return await response.json();\n  } catch (error) {\n    console.error('Error creating API key:', error);\n    throw error;\n  }\n};\n\nconst deleteApiKey = async (id: string): Promise<{ success: boolean }> => {\n  try {\n    const response = await fetch(`/api/keys/${id}`, {\n      method: 'DELETE',\n    });\n    if (!response.ok) throw new Error('Failed to delete API key');\n    return { success: true };\n  } catch (error) {\n    console.error('Error deleting API key:', error);\n    throw error;\n  }\n};\n\n// Using the imported ApiKey type from '@/types/trader'\n\nexport function ApiKeys() {\n  const { toast } = useToast();\n  const queryClient = useQueryClient();\n  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);\n  const [newKeyName, setNewKeyName] = useState('');\n  const [newKey, setNewKey] = useState<ApiKey | null>(null);\n  const [copiedKeyId, setCopiedKeyId] = useState<string | null>(null);\n\n  const { data: apiKeys, isLoading } = useQuery<ApiKey[]>({\n    queryKey: ['apiKeys'],\n    queryFn: fetchApiKeys,\n  });\n\n  const createKeyMutation = useMutation({\n    mutationFn: (name: string) => createApiKey(name),\n    onSuccess: (data) => {\n      setNewKey(data);\n      queryClient.invalidateQueries({ queryKey: ['apiKeys'] });\n      setNewKeyName('');\n    },\n    onError: () => {\n      toast({\n        title: 'Error',\n        description: 'Failed to create API key',\n        variant: 'destructive',\n      });\n    },\n  });\n\n  const deleteKeyMutation = useMutation({\n    mutationFn: (id: string) => deleteApiKey(id),\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: ['apiKeys'] });\n      toast({\n        title: 'Success',\n        description: 'API key deleted successfully',\n      });\n    },\n    onError: () => {\n      toast({\n        title: 'Error',\n        description: 'Failed to delete API key',\n        variant: 'destructive',\n      });\n    },\n  });\n\n  const handleCreateKey = () => {\n    if (!newKeyName.trim()) {\n      toast({\n        title: 'Error',\n        description: 'Please enter a name for the API key',\n        variant: 'destructive',\n      });\n      return;\n    }\n    createKeyMutation.mutate(newKeyName);\n  };\n\n  const handleCopyKey = (key: string, id: string) => {\n    navigator.clipboard.writeText(key);\n    setCopiedKeyId(id);\n    toast({\n      title: 'Copied',\n      description: 'API key copied to clipboard',\n    });\n    setTimeout(() => setCopiedKeyId(null), 2000);\n  };\n\n  const formatDate = (dateString: string | null) => {\n    if (!dateString) return 'Never';\n    return new Date(dateString).toLocaleString();\n  };\n\n  if (isLoading) {\n    return <div>Loading API keys...</div>;\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h3 className=\"text-lg font-medium\">API Keys</h3>\n          <p className=\"text-sm text-muted-foreground\">\n            Manage your API keys for programmatic access to your account.\n          </p>\n        </div>\n        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>\n          <DialogTrigger asChild>\n            <Button>\n              <Plus className=\"mr-2 h-4 w-4\" />\n              Create New Key\n            </Button>\n          </DialogTrigger>\n          <DialogContent>\n            <DialogHeader>\n              <DialogTitle>Create New API Key</DialogTitle>\n              <DialogDescription>\n                Create a new API key to authenticate requests to our API.\n              </DialogDescription>\n            </DialogHeader>\n            <div className=\"space-y-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"key-name\">Key Name</Label>\n                <Input\n                  id=\"key-name\"\n                  placeholder=\"e.g., Production, Development\"\n                  value={newKeyName}\n                  onChange={(e) => setNewKeyName(e.target.value)}\n                />\n                <p className=\"text-xs text-muted-foreground\">\n                  Give this key a name to identify it later.\n                </p>\n              </div>\n              \n              {newKey && (\n                <div className=\"rounded-md bg-yellow-50 p-4\">\n                  <div className=\"flex\">\n                    <div className=\"flex-shrink-0\">\n                      <AlertCircle className=\"h-5 w-5 text-yellow-400\" aria-hidden=\"true\" />\n                    </div>\n                    <div className=\"ml-3\">\n                      <h3 className=\"text-sm font-medium text-yellow-800\">\n                        Your new API key\n                      </h3>\n                      <div className=\"mt-2 text-sm text-yellow-700\">\n                        <p>\n                          Please copy your new API key now. You won't be able to see it again!\n                        </p>\n                        <div className=\"mt-2 flex rounded-md shadow-sm\">\n                          <div className=\"relative flex flex-grow items-stretch focus-within:z-10\">\n                            <input\n                              type=\"text\"\n                              className=\"block w-full rounded-none rounded-l-md border-0 py-1.5 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6\"\n                              value={newKey.key}\n                              readOnly\n                            />\n                          </div>\n                          <button\n                            type=\"button\"\n                            className=\"relative -ml-px inline-flex items-center gap-x-1.5 rounded-r-md px-3 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50\"\n                            onClick={() => handleCopyKey(newKey.key, newKey.id)}\n                          >\n                            <Copy className=\"h-4 w-4 text-gray-400\" aria-hidden=\"true\" />\n                            Copy\n                          </button>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              )}\n            </div>\n            <DialogFooter>\n              <Button\n                variant=\"outline\"\n                onClick={() => {\n                  setIsCreateDialogOpen(false);\n                  setNewKey(null);\n                }}\n              >\n                Close\n              </Button>\n              {!newKey ? (\n                <Button\n                  type=\"button\"\n                  onClick={handleCreateKey}\n                  disabled={createKeyMutation.isPending}\n                >\n                  {createKeyMutation.isPending ? (\n                    <>\n                      <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                      Creating...\n                    </>\n                  ) : (\n                    'Create Key'\n                  )}\n                </Button>\n              ) : null}\n            </DialogFooter>\n          </DialogContent>\n        </Dialog>\n      </div>\n\n      <div className=\"rounded-md border\">\n        <Table>\n          <TableHeader>\n            <TableRow>\n              <TableHead>Name</TableHead>\n              <TableHead>Key</TableHead>\n              <TableHead>Created</TableHead>\n              <TableHead>Last Used</TableHead>\n              <TableHead className=\"w-10\"></TableHead>\n            </TableRow>\n          </TableHeader>\n          <TableBody>\n            {apiKeys?.length ? (\n              apiKeys.map((apiKey) => (\n                <TableRow key={apiKey.id}>\n                  <TableCell className=\"font-medium\">{apiKey.name}</TableCell>\n                  <TableCell>\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"font-mono text-sm\">\n                        {apiKey.key.substring(0, 8)}...{apiKey.key.substring(apiKey.key.length - 4)}\n                      </span>\n                      <button\n                        type=\"button\"\n                        className=\"text-muted-foreground hover:text-foreground\"\n                        onClick={() => handleCopyKey(apiKey.key, apiKey.id)}\n                      >\n                        {copiedKeyId === apiKey.id ? (\n                          <CheckCircle2 className=\"h-4 w-4 text-green-500\" />\n                        ) : (\n                          <Copy className=\"h-4 w-4\" />\n                        )}\n                      </button>\n                    </div>\n                  </TableCell>\n                  <TableCell>{formatDate(apiKey.createdAt)}</TableCell>\n                  <TableCell>{formatDate(apiKey.lastUsed)}</TableCell>\n                  <TableCell>\n                    <DropdownMenu>\n                      <DropdownMenuTrigger asChild>\n                        <Button variant=\"ghost\" size=\"icon\" className=\"h-8 w-8\">\n                          <MoreVertical className=\"h-4 w-4\" />\n                          <span className=\"sr-only\">Actions</span>\n                        </Button>\n                      </DropdownMenuTrigger>\n                      <DropdownMenuContent align=\"end\">\n                        <DropdownMenuItem\n                          className=\"text-red-600\"\n                          onClick={() => deleteKeyMutation.mutate(apiKey.id)}\n                          disabled={deleteKeyMutation.variables === apiKey.id}\n                        >\n                          {deleteKeyMutation.variables === apiKey.id ? (\n                            <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                          ) : (\n                            <Trash2 className=\"mr-2 h-4 w-4\" />\n                          )}\n                          Delete\n                        </DropdownMenuItem>\n                      </DropdownMenuContent>\n                    </DropdownMenu>\n                  </TableCell>\n                </TableRow>\n              ))\n            ) : (\n              <TableRow>\n                <TableCell colSpan={5} className=\"h-24 text-center\">\n                  No API keys found.\n                </TableCell>\n              </TableRow>\n            )}\n          </TableBody>\n        </Table>\n      </div>\n    </div>\n  );\n}\n", "import * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n", "import * as React from \"react\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardDescription,\n  CardContent,\n}\n", "import * as React from 'react';\nimport { ToastProps, ToastActionElement } from './toast';\n\ntype ToasterToast = ToastProps & {\n  id: string;\n  title?: React.ReactNode;\n  description?: React.ReactNode;\n};\n\nconst actionTypes = {\n  ADD_TOAST: 'ADD_TOAST',\n  UPDATE_TOAST: 'UPDATE_TOAST',\n  DISMISS_TOAST: 'DISMISS_TOAST',\n  REMOVE_TOAST: 'REMOVE_TOAST',\n} as const;\n\nlet count = 0;\n\nfunction genId() {\n  count = (count + 1) % Number.MAX_SAFE_INTEGER;\n  return count.toString();\n}\n\ntype ActionType = typeof actionTypes;\n\ntype Action =\n  | {\n      type: ActionType['ADD_TOAST'];\n      toast: ToasterToast;\n    }\n  | {\n      type: ActionType['UPDATE_TOAST'];\n      toast: Partial<ToasterToast>;\n    }\n  | {\n      type: ActionType['DISMISS_TOAST'];\n      toastId?: ToasterToast['id'];\n    }\n  | {\n      type: ActionType['REMOVE_TOAST'];\n      toastId?: ToasterToast['id'];\n    };\n\ninterface State {\n  toasts: ToasterToast[];\n}\n\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>();\n\nconst addToRemoveQueue = (toastId: string) => {\n  if (toastTimeouts.has(toastId)) {\n    return;\n  }\n\n  const timeout = setTimeout(() => {\n    toastTimeouts.delete(toastId);\n    dispatch({\n      type: 'REMOVE_TOAST',\n      toastId: toastId,\n    });\n  }, 1000);\n\n  toastTimeouts.set(toastId, timeout);\n};\n\nexport const reducer = (state: State, action: Action): State => {\n  switch (action.type) {\n    case 'ADD_TOAST':\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, 5),\n      };\n\n    case 'UPDATE_TOAST':\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\n        ),\n      };\n\n    case 'DISMISS_TOAST': {\n      const { toastId } = action;\n\n      // ! Side effects ! - This could be extracted into a dismissToast() action,\n      // but I'll keep it here for simplicity\n      if (toastId) {\n        addToRemoveQueue(toastId);\n      } else {\n        state.toasts.forEach((toast) => {\n          addToRemoveQueue(toast.id);\n        });\n      }\n\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                open: false,\n              }\n            : t\n        ),\n      };\n    }\n    case 'REMOVE_TOAST':\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        };\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\n      };\n  }\n};\n\nconst listeners: Array<(state: State) => void> = [];\n\nlet memoryState: State = { toasts: [] };\n\nfunction dispatch(action: Action) {\n  memoryState = reducer(memoryState, action);\n  listeners.forEach((listener) => {\n    listener(memoryState);\n  });\n}\n\ntype Toast = Omit<ToasterToast, 'id'>;\n\nfunction toast({ ...props }: Toast) {\n  const id = genId();\n\n  const update = (props: ToasterToast) =>\n    dispatch({\n      type: 'UPDATE_TOAST',\n      toast: { ...props, id },\n    });\n  const dismiss = () => dispatch({ type: 'DISMISS_TOAST', toastId: id });\n\n  dispatch({\n    type: 'ADD_TOAST',\n    toast: {\n      ...props,\n      id,\n      open: true,\n      onOpenChange: (open: boolean) => {\n        if (!open) dismiss();\n      },\n    },\n  });\n\n  return {\n    id: id,\n    dismiss,\n    update,\n  };\n}\n\nfunction useToast() {\n  const [state, setState] = React.useState<State>(memoryState);\n\n  React.useEffect(() => {\n    listeners.push(setState);\n    return () => {\n      const index = listeners.indexOf(setState);\n      if (index > -1) {\n        listeners.splice(index, 1);\n      }\n    };\n  }, [state]);\n\n  return {\n    ...state,\n    toast,\n    dismiss: (toastId?: string) => dispatch({ type: 'DISMISS_TOAST', toastId }),\n  };\n}\n\nexport { useToast, toast };\n", "import { useF<PERSON>, Controller, useFormContext } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport * as z from 'zod';\nimport { Button } from '../ui/button';\nimport {\n  Form,\n  FormControl,\n  FormField as FormFieldPrimitive,\n  FormItem,\n  FormLabel,\n  FormMessage,\n} from '../ui/form';\nimport { Input } from '../ui/input';\nimport { useToast } from '../ui/use-toast';\nimport { useUpdateProfile } from '../../hooks/useUser';\nimport { User, MerchantUser } from '../../contexts/AuthContext';\nimport { Loader2 } from 'lucide-react';\n\nconst profileSchema = z.object({\n  name: z.string().min(2, 'Name must be at least 2 characters'),\n  email: z.string().email('Invalid email address'),\n  businessName: z.string().optional(),\n  businessType: z.string().optional(),\n  businessAddress: z.object({\n    street: z.string().optional(),\n    city: z.string().optional(),\n    state: z.string().optional(),\n    country: z.string().optional(),\n    postalCode: z.string().optional(),\n  }).optional(),\n});\n\ntype ProfileFormValues = z.infer<typeof profileSchema>;\n\ninterface ProfileFormProps {\n  user: User;\n  onSuccess?: () => void;\n}\n\n// Custom FormField component that works with react-hook-form\nconst FormField = ({\n  name,\n  label,\n  render,\n  ...props\n}: {\n  name: string;\n  label: string;\n  render: (field: {\n    onChange: (value: any) => void;\n    onBlur: () => void;\n    value: any;\n    ref: React.Ref<any>;\n  }) => React.ReactNode;\n}) => {\n  const { control } = useFormContext();\n  \n  return (\n    <Controller\n      name={name}\n      control={control}\n      render={({ field }) => (\n        <FormItem>\n          <FormLabel>{label}</FormLabel>\n          <FormControl>\n            {render(field)}\n          </FormControl>\n          <FormMessage />\n        </FormItem>\n      )}\n    />\n  );\n};\n\nexport function ProfileForm({ user, onSuccess }: ProfileFormProps) {\n  const { toast } = useToast();\n  const { mutate: updateProfile, isPending } = useUpdateProfile({\n    onSuccess: () => {\n      toast({\n        title: 'Success',\n        description: 'Your profile has been updated successfully.',\n      });\n      onSuccess?.();\n    },\n    onError: (error: unknown) => {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to update profile';\n      toast({\n        title: 'Error',\n        description: errorMessage,\n        variant: 'destructive',\n      });\n    },\n  });\n\n  const form = useForm<ProfileFormValues>({\n    resolver: zodResolver(profileSchema),\n    defaultValues: {\n      name: user.name,\n      email: user.email,\n      businessName: user.role === 'merchant' ? (user as MerchantUser).businessName : '',\n      businessType: '',\n      businessAddress: {\n        street: '',\n        city: '',\n        state: '',\n        country: '',\n        postalCode: '',\n      },\n    },\n  });\n\n  const onSubmit = (data: ProfileFormValues) => {\n    updateProfile(data);\n  };\n\n  return (\n    <Form {...form}>\n      <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <FormField\n            name=\"name\"\n            label=\"Full Name\"\n            render={(field) => (\n              <Input placeholder=\"John Doe\" {...field} />\n            )}\n          />\n          \n          <FormField\n            name=\"email\"\n            label=\"Email\"\n            render={(field) => (\n              <Input type=\"email\" placeholder=\"<EMAIL>\" disabled {...field} />\n            )}\n          />\n\n          {user.role === 'merchant' && (\n            <>\n              <FormField\n                name=\"businessName\"\n                label=\"Business Name\"\n                render={(field) => (\n                  <Input placeholder=\"Acme Inc.\" {...field} />\n                )}\n              />\n              \n              <FormField\n                name=\"businessType\"\n                label=\"Business Type\"\n                render={(field) => (\n                  <Input placeholder=\"Retail\" {...field} />\n                )}\n              />\n              \n              <div className=\"col-span-2 space-y-4\">\n                <h4 className=\"text-sm font-medium\">Business Address</h4>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div className=\"md:col-span-2\">\n                    <FormField\n                      name=\"businessAddress.street\"\n                      label=\"Street Address\"\n                      render={(field) => (\n                        <Input placeholder=\"123 Main St\" {...field} />\n                      )}\n                    />\n                  </div>\n                  \n                  <FormField\n                    name=\"businessAddress.city\"\n                    label=\"City\"\n                    render={(field) => (\n                      <Input placeholder=\"New York\" {...field} />\n                    )}\n                  />\n                  \n                  <FormField\n                    name=\"businessAddress.state\"\n                    label=\"State/Province\"\n                    render={(field) => (\n                      <Input placeholder=\"NY\" {...field} />\n                    )}\n                  />\n                  \n                  <FormField\n                    name=\"businessAddress.postalCode\"\n                    label=\"Postal Code\"\n                    render={(field) => (\n                      <Input placeholder=\"10001\" {...field} />\n                    )}\n                  />\n                  \n                  <FormField\n                    name=\"businessAddress.country\"\n                    label=\"Country\"\n                    render={(field) => (\n                      <Input placeholder=\"United States\" {...field} />\n                    )}\n                  />\n                </div>\n              </div>\n            </>\n          )}\n        </div>\n        \n        <div className=\"flex justify-end\">\n          <Button type=\"submit\" disabled={isPending}>\n            {isPending && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n            Update Profile\n          </Button>\n        </div>\n      </form>\n    </Form>\n  );\n}\n", "import { \n  useMutation, \n  useQuery, \n  useQueryClient, \n  UseMutationOptions, \n  UseQueryOptions, \n  QueryKey,\n  InvalidateQueryFilters\n} from '@tanstack/react-query';\nimport { useAuth } from '../contexts/AuthContext';\n\n// Define API response types\ninterface ApiResponse<T> {\n  data: T;\n  message?: string;\n  success: boolean;\n}\n\n// Define a type for the API request function\ninterface ApiRequest {\n  get<T = any>(url: string): Promise<ApiResponse<T>>;\n  post<T = any>(url: string, data?: any): Promise<ApiResponse<T>>;\n  put<T = any>(url: string, data?: any): Promise<ApiResponse<T>>;\n  delete<T = any>(url: string): Promise<ApiResponse<T>>;\n}\n\n// Mock API client - replace with your actual API client\nconst apiRequest: ApiRequest = {\n  get: async <T = any>(url: string): Promise<ApiResponse<T>> => {\n    const response = await fetch(`/api${url}`);\n    if (!response.ok) {\n      throw new Error('Network response was not ok');\n    }\n    return response.json();\n  },\n  post: async <T = any>(url: string, data: any = {}): Promise<ApiResponse<T>> => {\n    const response = await fetch(`/api${url}`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    });\n    if (!response.ok) {\n      throw new Error('Network response was not ok');\n    }\n    return response.json();\n  },\n  put: async <T = any>(url: string, data: any = {}): Promise<ApiResponse<T>> => {\n    const response = await fetch(`/api${url}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    });\n    if (!response.ok) {\n      throw new Error('Network response was not ok');\n    }\n    return response.json();\n  },\n  delete: async <T = any>(url: string): Promise<ApiResponse<T>> => {\n    const response = await fetch(`/api${url}`, {\n      method: 'DELETE',\n    });\n    if (!response.ok) {\n      throw new Error('Network response was not ok');\n    }\n    return response.json();\n  },\n};\n\n// Types\ntype ApiError = {\n  message: string;\n  statusCode: number;\n  errors?: Record<string, string[]>;\n};\n\n// Types\ntype UserProfile = {\n  id: string;\n  name: string;\n  email: string;\n  role: string;\n  businessName?: string;\n  businessType?: string;\n  businessAddress?: {\n    street?: string;\n    city?: string;\n    state?: string;\n    country?: string;\n    postalCode?: string;\n  };\n  twoFactorEnabled: boolean;\n  createdAt: string;\n  updatedAt: string;\n};\n\ntype UpdateProfileData = {\n  name: string;\n  businessName?: string;\n  businessType?: string;\n  businessAddress?: {\n    street?: string;\n    city?: string;\n    state?: string;\n    country?: string;\n    postalCode?: string;\n  };\n};\n\nexport type ChangePasswordData = {\n  currentPassword: string;\n  newPassword: string;\n};\n\n// Hooks\nexport const useCurrentUser = () => {\n  // Mock user for now - this will be replaced with actual auth context\n  const user = {\n    id: '1',\n    email: '<EMAIL>',\n    role: 'admin' as const,\n    name: 'Admin User',\n    isVerified: true\n  };\n  \n  // Uncomment when auth is properly set up\n  // const { user } = useAuth();\n  \n  return useQuery<UserProfile, ApiError>({\n    queryKey: ['currentUser'],\n    queryFn: async () => {\n      const response = await apiRequest.get<UserProfile>('/auth/me');\n      return response.data;\n    },\n    enabled: !!user,\n  });\n};\n\ntype MutationOptions<TData = unknown, TVariables = unknown> = Omit<\n  UseMutationOptions<TData, ApiError, TVariables>,\n  'mutationFn'\n>;\n\nexport const useUpdateProfile = (options: MutationOptions<UserProfile, UpdateProfileData> = {}) => {\n  const queryClient = useQueryClient();\n  \n  return useMutation<UserProfile, ApiError, UpdateProfileData>({\n    mutationFn: async (profileData) => {\n      const response = await apiRequest.put<UserProfile>('/users/me', profileData);\n      return response.data;\n    },\n    onSuccess: (data, variables, context) => {\n      queryClient.setQueryData(['currentUser'], data);\n      if (options.onSuccess) {\n        options.onSuccess(data, variables, context);\n      }\n    },\n    onError: options.onError,\n    onSettled: options.onSettled,\n    onMutate: options.onMutate,\n  });\n};\n\nexport const useChangePassword = (options: MutationOptions<{ success: boolean }, ChangePasswordData> = {}) => {\n  return useMutation<{ success: boolean }, ApiError, ChangePasswordData>({\n    mutationFn: async (passwordData) => {\n      const response = await apiRequest.post<{ success: boolean }>('/auth/change-password', passwordData);\n      return response.data;\n    },\n    onSuccess: options.onSuccess,\n    onError: options.onError,\n    onSettled: options.onSettled,\n    onMutate: options.onMutate,\n  });\n};\n\nexport const useSetupTwoFactor = (options: MutationOptions<{ qrCodeData: string; secret: string }> = {}) => {\n  return useMutation<{ qrCodeData: string; secret: string }, ApiError, void>({\n    mutationFn: async () => {\n      const response = await apiRequest.post<{ qrCodeData: string; secret: string }>('/auth/2fa/setup');\n      return response.data;\n    },\n    onSuccess: options.onSuccess,\n    onError: options.onError,\n    onSettled: options.onSettled,\n    onMutate: options.onMutate,\n  });\n};\n\nexport const useVerifyTwoFactor = (options: MutationOptions<{ recoveryCodes: string[] }, { code: string }> = {}) => {\n  return useMutation<{ recoveryCodes: string[] }, ApiError, { code: string }>({\n    mutationFn: async ({ code }) => {\n      const response = await apiRequest.post<{ recoveryCodes: string[] }>('/auth/2fa/verify', { code });\n      return response.data;\n    },\n    onSuccess: options.onSuccess,\n    onError: options.onError,\n    onSettled: options.onSettled,\n    onMutate: options.onMutate,\n  });\n};\n\nexport const useDisableTwoFactor = (options: MutationOptions<{ success: boolean }> = {}) => {\n  const queryClient = useQueryClient();\n  \n  return useMutation<{ success: boolean }, ApiError, void>({\n    mutationFn: async () => {\n      const response = await apiRequest.post<{ success: boolean }>('/auth/2fa/disable');\n      return response.data;\n    },\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries(\n        { queryKey: ['currentUser'] },\n        { cancelRefetch: true }\n      ).catch(console.error);\n      if (options.onSuccess) {\n        options.onSuccess(data, variables, context);\n      }\n    },\n    onError: options.onError,\n    onSettled: options.onSettled,\n    onMutate: options.onMutate,\n  });\n};\n\nexport const useApiKeys = () => {\n  return useQuery<Array<{\n    id: string;\n    name: string;\n    key: string;\n    lastUsed: string | null;\n    createdAt: string;\n  }>, ApiError>({\n    queryKey: ['apiKeys'],\n    queryFn: async () => {\n      const response = await apiRequest.get<Array<{\n        id: string;\n        name: string;\n        key: string;\n        lastUsed: string | null;\n        createdAt: string;\n      }>>('/users/me/api-keys');\n      return response.data;\n    },\n  });\n};\n\ntype ApiKey = {\n  id: string;\n  name: string;\n  key: string;\n  lastUsed: string | null;\n  createdAt: string;\n};\n\nexport const useCreateApiKey = (options: MutationOptions<ApiKey, { name: string }> = {}) => {\n  const queryClient = useQueryClient();\n  \n  return useMutation<ApiKey, ApiError, { name: string }>({\n    mutationFn: async ({ name }) => {\n      const response = await apiRequest.post<ApiKey>('/users/me/api-keys', { name });\n      return response.data;\n    },\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries(\n        { queryKey: ['apiKeys'] },\n        { cancelRefetch: true }\n      ).catch(console.error);\n      if (options.onSuccess) {\n        options.onSuccess(data, variables, context);\n      }\n    },\n    onError: options.onError,\n    onSettled: options.onSettled,\n    onMutate: options.onMutate,\n  });\n};\n\nexport const useDeleteApiKey = (options: MutationOptions<{ success: boolean }, string> = {}) => {\n  const queryClient = useQueryClient();\n  \n  return useMutation<{ success: boolean }, ApiError, string>({\n    mutationFn: async (id) => {\n      const response = await apiRequest.delete<{ success: boolean }>(`/users/me/api-keys/${id}`);\n      return response.data;\n    },\n    onSuccess: (data, variables, context) => {\n      queryClient.invalidateQueries(\n        { queryKey: ['apiKeys'] },\n        { cancelRefetch: true }\n      ).catch(console.error);\n      if (options.onSuccess) {\n        options.onSuccess(data, variables, context);\n      }\n    },\n    onError: options.onError,\n    onSettled: options.onSettled,\n    onMutate: options.onMutate,\n  });\n};\n\nexport const useActivityLogs = () => {\n  return useQuery<Array<{\n    id: string;\n    action: string;\n    description: string;\n    timestamp: string;\n    status: 'success' | 'failed' | 'warning';\n    ip?: string;\n    userAgent?: string;\n  }>, ApiError>({\n    queryKey: ['activityLogs'],\n    queryFn: async () => {\n      const response = await apiRequest.get<Array<{\n        id: string;\n        action: string;\n        description: string;\n        timestamp: string;\n        status: 'success' | 'failed' | 'warning';\n        ip?: string;\n        userAgent?: string;\n      }>>('/users/me/activity-logs');\n      return response.data;\n    },\n  });\n};\n", "import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n", "import * as React from \"react\"\n\nimport { cn } from \"../../lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": ["Form", "FormPrimitive", "React", "_ref", "ref", "className", "props", "_jsx", "cn", "displayName", "FormItem", "_ref2", "FormLabel", "_ref3", "required", "children", "FormControl", "_ref4", "_ref5", "FormMessage", "_ref6", "Table", "TableHeader", "TableBody", "TableRow", "TableHead", "TableCell", "_ref7", "_ref8", "Dialog", "DialogPrimitive", "DialogTrigger", "DialogPortal", "container", "forceMount", "restProps", "portalProps", "DialogOverlay", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_jsxs", "X", "DialogHeader", "<PERSON><PERSON><PERSON><PERSON>er", "DialogTitle", "DialogDescription", "useSetupTwoFactor", "user", "useAuth", "queryClient", "useQueryClient", "useMutation", "mutationFn", "async", "Promise", "resolve", "setTimeout", "qrCodeData", "secret", "onSuccess", "invalidateQueries", "query<PERSON><PERSON>", "useVerifyTwoFactor", "code", "Error", "recoveryCodes", "success", "useDisableTwoFactor", "message", "TwoFactorAuth", "twoFactorEnabled", "toast", "useToast", "isDialogOpen", "setIsDialogOpen", "useState", "verificationCode", "setVerificationCode", "setQrCodeData", "setRecoveryCodes", "showRecoveryCodes", "setShowRecoveryCodes", "mutate", "setupTwoFactor", "isPending", "isSettingUp", "isSuccess", "isSetupSuccess", "data", "setupData", "isError", "isSetupError", "error", "setupError", "verifyTwoFactor", "isVerifying", "isVerifySuccess", "verifyData", "isVerifyError", "verifyError", "disableTwoFactor", "isDisabling", "isDisableSuccess", "isDisableError", "disableError", "useEffect", "title", "description", "variant", "<PERSON><PERSON>", "onClick", "handleSetup2FA", "undefined", "onError", "disabled", "_Fragment", "Loader2", "open", "onOpenChange", "src", "alt", "AlertCircle", "map", "index", "handleCopyRecoveryCodes", "navigator", "clipboard", "writeText", "join", "handleDownloadRecoveryCodes", "element", "document", "createElement", "file", "Blob", "type", "href", "URL", "createObjectURL", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "Label", "htmlFor", "Input", "id", "value", "onChange", "e", "target", "placeholder", "autoComplete", "handleDisable2FA", "trim", "handleVerifyCode", "PasswordChangeForm", "showCurrentPassword", "setShowCurrentPassword", "showNewPassword", "setShowNewPassword", "showConfirmPassword", "setShowConfirmPassword", "formData", "setFormData", "currentPassword", "newPassword", "confirmPassword", "setIsPending", "changePassword", "useChangePassword", "errorMessage", "handleChange", "name", "prev", "onSubmit", "preventDefault", "length", "test", "Eye<PERSON>ff", "Eye", "labelVariants", "cva", "DropdownMenu", "DropdownMenuPrimitive", "DropdownMenuTrigger", "inset", "ChevronRight", "DropdownMenuContent", "sideOffset", "DropdownMenuItem", "checked", "Check", "Circle", "fetchApiKeys", "response", "fetch", "ok", "json", "console", "createApiKey", "method", "headers", "JSON", "stringify", "deleteApiKey", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isCreateDialogOpen", "setIsCreateDialogOpen", "newKeyName", "setNewKeyName", "new<PERSON>ey", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "copiedKeyId", "setCopiedKeyId", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isLoading", "useQuery", "queryFn", "createKeyMutation", "deleteKeyMutation", "handleCopyKey", "key", "formatDate", "dateString", "Date", "toLocaleString", "<PERSON><PERSON><PERSON><PERSON>", "Plus", "readOnly", "Copy", "handleCreateKey", "<PERSON><PERSON><PERSON><PERSON>", "substring", "CheckCircle2", "createdAt", "lastUsed", "size", "MoreVertical", "align", "variables", "Trash2", "colSpan", "Tabs", "TabsPrimitive", "TabsList", "TabsTrigger", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "CardDescription", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>er", "count", "toastTimeouts", "Map", "addToRemoveQueue", "toastId", "has", "timeout", "delete", "dispatch", "set", "reducer", "state", "action", "toasts", "slice", "t", "for<PERSON>ach", "filter", "listeners", "memoryState", "listener", "Number", "MAX_SAFE_INTEGER", "toString", "dismiss", "update", "setState", "push", "indexOf", "splice", "profileSchema", "z", "min", "email", "businessName", "optional", "businessType", "businessAddress", "street", "city", "country", "postalCode", "FormField", "label", "render", "control", "useFormContext", "Controller", "field", "ProfileForm", "updateProfile", "useUpdateProfile", "form", "useForm", "resolver", "zodResolver", "defaultValues", "role", "handleSubmit", "apiRequest", "url", "arguments", "options", "profileData", "context", "setQueryData", "onSettled", "onMutate", "passwordData", "buttonVariants", "variants", "default", "destructive", "outline", "secondary", "ghost", "link", "sm", "lg", "icon", "defaultVariants", "Comp", "Slot"], "sourceRoot": ""}