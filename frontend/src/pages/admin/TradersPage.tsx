import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../../components/ui/dropdown-menu';
import { Badge } from '../../components/ui/badge';
import { MoreHorizontal, Search, Loader2, Plus, Filter, Users, Target, TrendingUp } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '../../components/ui/dialog';
import { toast } from '../../components/ui/use-toast';

interface Trader {
  _id: string;
  name: string;
  email: string;
  phone?: string;
  isActive: boolean;
  isVerified: boolean;
  role: string;
  createdAt: string;
  updatedAt: string;
  // Additional trader-specific fields
  assignedMerchants?: number;
  totalCollections?: number;
  successRate?: number;
}

export default function TradersPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [traders, setTraders] = useState<Trader[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTraders = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Try to fetch from real API
        const response = await fetch('http://localhost:5000/api/users');
        const data = await response.json();
        
        if (data.success) {
          // Filter only traders from users
          const traderUsers = data.data.filter((user: any) => user.role === 'trader');
          // Add mock trader-specific data
          const tradersWithStats = traderUsers.map((trader: any) => ({
            ...trader,
            assignedMerchants: Math.floor(Math.random() * 10) + 1,
            totalCollections: Math.floor(Math.random() * 50) + 10,
            successRate: Math.floor(Math.random() * 30) + 70,
          }));
          setTraders(tradersWithStats);
        } else {
          throw new Error('Failed to fetch traders');
        }
      } catch (err) {
        console.log('API not available, using mock data');
        // Mock data fallback
        setTraders([
          {
            _id: '1',
            name: 'Bob Wilson',
            email: '<EMAIL>',
            phone: '+1234567892',
            isActive: true,
            isVerified: true,
            role: 'trader',
            createdAt: '2025-07-26T19:53:02.226Z',
            updatedAt: '2025-07-26T19:53:02.226Z',
            assignedMerchants: 5,
            totalCollections: 23,
            successRate: 85,
          },
          {
            _id: '2',
            name: 'Alice Johnson',
            email: '<EMAIL>',
            phone: '+1234567893',
            isActive: true,
            isVerified: true,
            role: 'trader',
            createdAt: '2025-07-26T19:53:02.500Z',
            updatedAt: '2025-07-26T19:53:02.500Z',
            assignedMerchants: 8,
            totalCollections: 41,
            successRate: 92,
          },
        ]);
        setError(null);
      } finally {
        setLoading(false);
      }
    };

    fetchTraders();
  }, []);

  const filteredTraders = traders.filter(trader =>
    trader.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    trader.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleStatusToggle = (traderId: string) => {
    setTraders(prev => prev.map(trader => 
      trader._id === traderId 
        ? { ...trader, isActive: !trader.isActive }
        : trader
    ));
  };

  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading traders...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">Traders Management</h1>
            <p className="text-muted-foreground">
              Manage and monitor trader accounts and performance
            </p>
          </div>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Add Trader
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Traders</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{traders.length}</div>
              <p className="text-xs text-muted-foreground">
                Active trader accounts
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Traders</CardTitle>
              <Badge variant="default" className="text-xs">Live</Badge>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {traders.filter(t => t.isActive).length}
              </div>
              <p className="text-xs text-muted-foreground">
                Currently active
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Success Rate</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {traders.length > 0 
                  ? Math.round(traders.reduce((acc, t) => acc + (t.successRate || 0), 0) / traders.length)
                  : 0}%
              </div>
              <p className="text-xs text-muted-foreground">
                Collection success rate
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Collections</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {traders.reduce((acc, t) => acc + (t.totalCollections || 0), 0)}
              </div>
              <p className="text-xs text-muted-foreground">
                All-time collections
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardHeader>
            <CardTitle>Traders List</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2 mb-4">
              <div className="relative flex-1">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search traders..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
              <Button variant="outline">
                <Filter className="mr-2 h-4 w-4" />
                Filter
              </Button>
            </div>

            {/* Table */}
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Assigned Merchants</TableHead>
                    <TableHead>Collections</TableHead>
                    <TableHead>Success Rate</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Verified</TableHead>
                    <TableHead>Joined</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredTraders.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={9} className="text-center py-8">
                        No traders found
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredTraders.map((trader) => (
                      <TableRow key={trader._id}>
                        <TableCell className="font-medium">{trader.name}</TableCell>
                        <TableCell>{trader.email}</TableCell>
                        <TableCell>{trader.assignedMerchants || 0}</TableCell>
                        <TableCell>{trader.totalCollections || 0}</TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <span>{trader.successRate || 0}%</span>
                            <Badge 
                              variant={(trader.successRate || 0) >= 80 ? 'default' : 'secondary'}
                              className="text-xs"
                            >
                              {(trader.successRate || 0) >= 80 ? 'High' : 'Medium'}
                            </Badge>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={trader.isActive ? 'default' : 'outline'}>
                            {trader.isActive ? 'Active' : 'Inactive'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={trader.isVerified ? 'default' : 'secondary'}>
                            {trader.isVerified ? 'Verified' : 'Pending'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {new Date(trader.createdAt).toLocaleDateString()}
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem>View Details</DropdownMenuItem>
                              <DropdownMenuItem>View Assignments</DropdownMenuItem>
                              // Add states for modals
                                const [selectedTrader, setSelectedTrader] = useState<Trader | null>(null);
                                const [isProfileOpen, setIsProfileOpen] = useState(false);
                                const [isEditOpen, setIsEditOpen] = useState(false);
                                const [isDeleteOpen, setIsDeleteOpen] = useState(false);
                              
                                const handleViewProfile = (trader: Trader) => {
                                  setSelectedTrader(trader);
                                  setIsProfileOpen(true);
                                };
                              
                                const handleEdit = (trader: Trader) => {
                                  setSelectedTrader(trader);
                                  setIsEditOpen(true);
                                };
                              
                                const handleDelete = (trader: Trader) => {
                                  setSelectedTrader(trader);
                                  setIsDeleteOpen(true);
                                };
                              
                                const confirmDelete = async () => {
                                  if (!selectedTrader) return;
                                  try {
                                    // Assuming a service like traderAdminService.deleteTrader(selectedTrader._id)
                                    toast({ title: 'Deleted', description: 'Trader deleted successfully.' });
                                    setTraders(prev => prev.filter(t => t._id !== selectedTrader._id));
                                  } catch (error) {
                                    toast({ title: 'Error', description: 'Failed to delete trader.', variant: 'destructive' });
                                  }
                                  setIsDeleteOpen(false);
                                };
                              // Update DropdownMenuItems
                              <DropdownMenuItem onClick={() => handleViewProfile(trader)}>View Profile</DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleEdit(trader)}>Edit</DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleDelete(trader)}>Delete</DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleStatusToggle(trader._id)}>
                                {trader.isActive ? 'Deactivate' : 'Activate'}
                              </DropdownMenuItem>
                              // Add modals
                              <Dialog open={isProfileOpen} onOpenChange={setIsProfileOpen}>
                                <DialogContent>
                                  <DialogHeader>
                                    <DialogTitle>Trader Profile</DialogTitle>
                                  </DialogHeader>
                                  {selectedTrader && (
                                    <div className="space-y-4">
                                      <p><strong>Name:</strong> {selectedTrader.name}</p>
                                      <p><strong>Email:</strong> {selectedTrader.email}</p>
                                      <p><strong>Phone:</strong> {selectedTrader.phone}</p>
                                      // Add more details
                                    </div>
                                  )}
                                </DialogContent>
                              </Dialog>
                              
                              <Dialog open={isEditOpen} onOpenChange={setIsEditOpen}>
                                <DialogContent>
                                  <DialogHeader>
                                    <DialogTitle>Edit Trader</DialogTitle>
                                  </DialogHeader>
                                  // Add edit form
                                </DialogContent>
                              </Dialog>
                              
                              <Dialog open={isDeleteOpen} onOpenChange={setIsDeleteOpen}>
                                <DialogContent>
                                  <DialogHeader>
                                    <DialogTitle>Confirm Deletion</DialogTitle>
                                  </DialogHeader>
                                  <p>Are you sure you want to delete this trader?</p>
                                  <div className="flex justify-end space-x-2">
                                    <Button variant="outline" onClick={() => setIsDeleteOpen(false)}>Cancel</Button>
                                    <Button variant="destructive" onClick={confirmDelete}>Delete</Button>
                                  </div>
                                </DialogContent>
                              </Dialog>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
