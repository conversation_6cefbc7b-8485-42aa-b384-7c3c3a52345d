const webhookService = require('../services/webhooks/webhook.service');
const { WebhookError } = require('../services/webhooks/webhook.errors');

class WebhookController {
  /**
   * Handle incoming webhook
   */
  async handleIncomingWebhook(req, res, next) {
    try {
      const { webhookId } = req.params;
      const signature = req.headers['x-webhook-signature'];
      
      if (!signature) {
        throw new WebhookError('Missing signature header', 401);
      }

      const event = await webhookService.processIncomingEvent(
        webhookId,
        req.body,
        signature
      );

      res.status(202).json({
        success: true,
        eventId: event.eventId,
        status: event.status
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * List webhook events
   */
  async listEvents(req, res, next) {
    try {
      const { merchant } = req;
      const { limit = 50, offset = 0, status, type } = req.query;
      
      const result = await webhookService.listEvents(merchant._id, {
        limit: parseInt(limit, 10),
        offset: parseInt(offset, 10),
        status,
        type
      });

      res.json({
        success: true,
        ...result
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get webhook event details
   */
  async getEventDetails(req, res, next) {
    try {
      const { merchant } = req;
      const { eventId } = req.params;
      
      const event = await webhookService.getEventById(eventId, merchant._id);
      
      if (!event) {
        throw new WebhookError('Event not found', 404);
      }

      res.json({
        success: true,
        data: event
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Retry failed webhook delivery
   */
  async retryEvent(req, res, next) {
    try {
      const { merchant } = req;
      const { eventId } = req.params;
      
      const result = await webhookService.retryEvent(eventId, merchant._id);
      
      res.json({
        success: true,
        ...result
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Error handling middleware
   */
  errorHandler(err, req, res, next) {
    console.error('Webhook error:', err);
    
    if (err instanceof WebhookError) {
      return res.status(err.statusCode || 500).json({
        success: false,
        error: {
          code: err.code || 'WEBHOOK_ERROR',
          message: err.message,
          ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
        }
      });
    }

    // Default error handler
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'An unexpected error occurred',
        ...(process.env.NODE_ENV === 'development' && { 
          message: err.message,
          stack: err.stack 
        })
      }
    });
  }
}

module.exports = new WebhookController();
