import { apiRequest } from '../utils/api';
import { User } from '../contexts/AuthContext';

interface ActivityLog {
  id: string;
  action: string;
  timestamp: string;
  ipAddress: string;
  userAgent: string;
  metadata?: Record<string, unknown>;
}

interface PaginationMeta {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
  code?: number;
}

/**
 * Fetches the current user's profile data
 */
export const getCurrentUser = async (): Promise<User> => {
  const response = await apiRequest.get<ApiResponse<User>>('/users/me');
  return response.data.data;
};

/**
 * Updates the current user's profile
 */
export const updateProfile = async (data: Partial<User>): Promise<User> => {
  const response = await apiRequest.patch<ApiResponse<User>>('/users/me', data);
  return response.data.data;
};

/**
 * Changes the user's password
 */
export const changePassword = async (data: {
  currentPassword: string;
  newPassword: string;
}): Promise<{ success: boolean; message: string }> => {
  const response = await apiRequest.post<ApiResponse<{ success: boolean; message: string }>>(
    '/auth/change-password',
    data
  );
  return response.data.data;
};

/**
 * Enables or disables two-factor authentication
 */
export const toggleTwoFactorAuth = async (enabled: boolean): Promise<{ success: boolean; message: string }> => {
  const response = await apiRequest.post<ApiResponse<{ success: boolean; message: string }>>(
    '/auth/two-factor',
    { enabled }
  );
  return response.data.data;
};

/**
 * Generates a new API key
 */
export const generateApiKey = async (name: string): Promise<{ key: string }> => {
  const response = await apiRequest.post<ApiResponse<{ key: string }>>('/api-keys', { name });
  return response.data.data;
};

/**
 * Deletes an API key
 */
export const deleteApiKey = async (id: string): Promise<{ success: boolean }> => {
  const response = await apiRequest.delete<ApiResponse<{ success: boolean }>>(`/api-keys/${id}`);
  return response.data.data;
};

/**
 * Fetches the user's activity log
 */
export const getActivityLog = async (params: {
  page?: number;
  limit?: number;
} = {}): Promise<{ data: ActivityLog[]; pagination: PaginationMeta }> => {
  const response = await apiRequest.get<ApiResponse<{ data: ActivityLog[]; pagination: PaginationMeta }>>(
    '/users/activity',
    { params }
  );
  return response.data.data;
};
