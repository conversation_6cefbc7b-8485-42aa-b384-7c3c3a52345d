import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { But<PERSON> } from '../ui/button';
import { ScrollArea } from '../ui/scroll-area';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '../ui/dialog';
import { 
  History, 
  User, 
  Calendar, 
  Edit, 
  Plus, 
  Trash2, 
  CheckCircle, 
  XCircle, 
  Pause, 
  Play,
  Eye,
  Loader2
} from 'lucide-react';
import { ChangeLog, merchantAdminService } from '../../services/merchantAdminService';

interface ChangeHistoryViewerProps {
  merchantId: string;
  trigger?: React.ReactNode;
}

const ChangeHistoryViewer: React.FC<ChangeHistoryViewerProps> = ({
  merchantId,
  trigger
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [changeHistory, setChangeHistory] = useState<ChangeLog[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchChangeHistory = async () => {
    try {
      setLoading(true);
      const response = await merchantAdminService.getMerchantChangeHistory(merchantId);
      setChangeHistory(response.data.changeHistory);
    } catch (error) {
      console.error('Error fetching change history:', error);
      // Mock data for demo
      setChangeHistory([
        {
          field: 'merchant_created',
          oldValue: null,
          newValue: 'Created new merchant profile',
          changedBy: {
            _id: '1',
            name: 'Admin User',
            email: '<EMAIL>'
          },
          changedAt: new Date().toISOString(),
          changeType: 'create'
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      fetchChangeHistory();
    }
  }, [isOpen, merchantId]);

  const getChangeIcon = (changeType: string) => {
    switch (changeType) {
      case 'create':
        return <Plus className="h-4 w-4 text-green-600" />;
      case 'update':
        return <Edit className="h-4 w-4 text-blue-600" />;
      case 'delete':
        return <Trash2 className="h-4 w-4 text-red-600" />;
      case 'approve':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'reject':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'suspend':
        return <Pause className="h-4 w-4 text-orange-600" />;
      case 'activate':
        return <Play className="h-4 w-4 text-green-600" />;
      default:
        return <Edit className="h-4 w-4 text-gray-600" />;
    }
  };

  const getChangeBadge = (changeType: string) => {
    const variants: Record<string, any> = {
      create: 'default',
      update: 'secondary',
      delete: 'destructive',
      approve: 'default',
      reject: 'destructive',
      suspend: 'outline',
      activate: 'default'
    };

    return (
      <Badge variant={variants[changeType] || 'secondary'} className="text-xs">
        {changeType.charAt(0).toUpperCase() + changeType.slice(1)}
      </Badge>
    );
  };

  const formatFieldName = (field: string) => {
    return field
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, str => str.toUpperCase())
      .replace('_', ' ');
  };

  const formatValue = (value: any) => {
    if (value === null || value === undefined) {
      return <span className="text-muted-foreground italic">null</span>;
    }
    
    if (typeof value === 'object') {
      return <span className="font-mono text-xs">{JSON.stringify(value, null, 2)}</span>;
    }
    
    if (typeof value === 'boolean') {
      return <span className="font-mono">{value ? 'true' : 'false'}</span>;
    }
    
    return <span className="font-mono">{value.toString()}</span>;
  };

  const defaultTrigger = (
    <Button variant="outline" size="sm">
      <History className="mr-2 h-4 w-4" />
      View History
    </Button>
  );

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            Change History
          </DialogTitle>
          <DialogDescription>
            Complete history of all changes made to this merchant account
          </DialogDescription>
        </DialogHeader>
        
        <ScrollArea className="h-[60vh] pr-4">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              Loading change history...
            </div>
          ) : changeHistory.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No changes recorded yet
            </div>
          ) : (
            <div className="space-y-4">
              {changeHistory.map((change, index) => (
                <Card key={index} className="border-l-4 border-l-blue-500">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-3">
                        {getChangeIcon(change.changeType)}
                        <div>
                          <div className="flex items-center space-x-2">
                            <span className="font-medium">
                              {formatFieldName(change.field)}
                            </span>
                            {getChangeBadge(change.changeType)}
                          </div>
                          <div className="flex items-center space-x-2 text-sm text-muted-foreground mt-1">
                            <User className="h-3 w-3" />
                            <span>{change.changedBy.name}</span>
                            <span>•</span>
                            <Calendar className="h-3 w-3" />
                            <span>{new Date(change.changedAt).toLocaleString()}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="space-y-3">
                      {change.changeType !== 'create' && (
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <div className="text-sm font-medium text-muted-foreground mb-1">
                              Previous Value
                            </div>
                            <div className="p-2 bg-red-50 border border-red-200 rounded text-sm">
                              {formatValue(change.oldValue)}
                            </div>
                          </div>
                          <div>
                            <div className="text-sm font-medium text-muted-foreground mb-1">
                              New Value
                            </div>
                            <div className="p-2 bg-green-50 border border-green-200 rounded text-sm">
                              {formatValue(change.newValue)}
                            </div>
                          </div>
                        </div>
                      )}
                      
                      {change.changeType === 'create' && (
                        <div>
                          <div className="text-sm font-medium text-muted-foreground mb-1">
                            Action
                          </div>
                          <div className="p-2 bg-blue-50 border border-blue-200 rounded text-sm">
                            {formatValue(change.newValue)}
                          </div>
                        </div>
                      )}
                      
                      {change.reason && (
                        <div>
                          <div className="text-sm font-medium text-muted-foreground mb-1">
                            Reason
                          </div>
                          <div className="p-2 bg-gray-50 border border-gray-200 rounded text-sm">
                            {change.reason}
                          </div>
                        </div>
                      )}
                      
                      <div className="text-xs text-muted-foreground">
                        Changed by: {change.changedBy.email}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </ScrollArea>
        
        <div className="flex justify-between items-center pt-4 border-t">
          <div className="text-sm text-muted-foreground">
            {changeHistory.length} change{changeHistory.length !== 1 ? 's' : ''} recorded
          </div>
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ChangeHistoryViewer;
