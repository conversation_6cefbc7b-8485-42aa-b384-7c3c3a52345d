const AWS = require('aws-sdk');
const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

// Configure AWS
const s3 = new AWS.S3({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  region: process.env.AWS_REGION
});

// Upload file to S3
const uploadToS3 = (file, key) => {
  return new Promise((resolve, reject) => {
    const fileStream = fs.createReadStream(file.path);
    
    const params = {
      Bucket: process.env.AWS_S3_BUCKET,
      Key: key + path.extname(file.originalname),
      Body: fileStream,
      ContentType: file.mimetype,
      ACL: 'private' // Make files private by default
    };

    s3.upload(params, (err, data) => {
      if (err) {
        console.error('Error uploading to S3:', err);
        return reject(err);
      }
      resolve(data.Location);
    });
  });
};

// Generate pre-signed URL for private files
const getSignedUrl = (key, expiresIn = 3600) => {
  return new Promise((resolve, reject) => {
    const params = {
      Bucket: process.env.AWS_S3_BUCKET,
      Key: key,
      Expires: expiresIn
    };

    s3.getSignedUrl('getObject', params, (err, url) => {
      if (err) {
        console.error('Error generating signed URL:', err);
        return reject(err);
      }
      resolve(url);
    });
  });
};

module.exports = {
  uploadToS3,
  getSignedUrl
};
