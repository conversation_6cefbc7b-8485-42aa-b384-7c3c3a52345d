const express = require('express');
const router = express.Router();
const TraderAssignment = require('../models/TraderAssignment');
const User = require('../models/User');

// Simple async handler replacement
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

// Get all trader assignments with populated data
router.get('/', asyncHandler(async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    const status = req.query.status;
    const search = req.query.search;
    
    // Build query
    let query = {};
    if (status && status !== 'all') {
      query.status = status;
    }
    
    const assignments = await TraderAssignment.find(query)
      .populate('traderId', 'name email role phone')
      .populate('merchantId', 'name email businessName role phone')
      .populate('assignedBy', 'name email role')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await TraderAssignment.countDocuments(query);

    // Filter by search term if provided
    let filteredAssignments = assignments;
    if (search) {
      filteredAssignments = assignments.filter(assignment => {
        const traderName = assignment.traderId?.name || '';
        const merchantName = assignment.merchantId?.businessName || assignment.merchantId?.name || '';
        return traderName.toLowerCase().includes(search.toLowerCase()) ||
               merchantName.toLowerCase().includes(search.toLowerCase());
      });
    }

    res.json({
      success: true,
      data: filteredAssignments,
      meta: {
        total: total,
        page: page,
        limit: limit,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get trader assignments error:', error);
    res.status(500).json({ 
      success: false,
      message: 'Failed to get trader assignments' 
    });
  }
}));

// Get trader assignment by ID
router.get('/:id', asyncHandler(async (req, res) => {
  try {
    const assignment = await TraderAssignment.findById(req.params.id)
      .populate('traderId', 'name email role phone')
      .populate('merchantId', 'name email businessName role phone')
      .populate('assignedBy', 'name email role');

    if (!assignment) {
      return res.status(404).json({
        success: false,
        message: 'Trader assignment not found'
      });
    }

    res.json({
      success: true,
      data: assignment
    });
  } catch (error) {
    console.error('Get trader assignment error:', error);
    res.status(500).json({ 
      success: false,
      message: 'Failed to get trader assignment' 
    });
  }
}));

// Create new trader assignment
router.post('/', asyncHandler(async (req, res) => {
  try {
    const {
      traderId,
      merchantId,
      assignedBy,
      assignmentType,
      startDate,
      endDate,
      status,
      collectionTarget,
      permissions,
      notes
    } = req.body;

    // Validate required fields
    if (!traderId || !merchantId) {
      return res.status(400).json({
        success: false,
        message: 'Trader ID and Merchant ID are required'
      });
    }

    // Check if trader and merchant exist
    const trader = await User.findById(traderId);
    const merchant = await User.findById(merchantId);

    if (!trader || trader.role !== 'trader') {
      return res.status(400).json({
        success: false,
        message: 'Invalid trader ID'
      });
    }

    if (!merchant || merchant.role !== 'merchant') {
      return res.status(400).json({
        success: false,
        message: 'Invalid merchant ID'
      });
    }

    const assignment = new TraderAssignment({
      traderId,
      merchantId,
      assignedBy: assignedBy || '507f1f77bcf86cd799439011', // Default admin ID
      assignmentType: assignmentType || 'temporary',
      startDate: startDate || new Date(),
      endDate,
      status: status || 'active',
      collectionTarget: collectionTarget || {
        amount: 0,
        period: 'monthly'
      },
      permissions: permissions || [],
      notes
    });

    await assignment.save();

    // Populate the saved assignment
    const populatedAssignment = await TraderAssignment.findById(assignment._id)
      .populate('traderId', 'name email role')
      .populate('merchantId', 'name email businessName role')
      .populate('assignedBy', 'name email role');

    res.status(201).json({
      success: true,
      data: populatedAssignment,
      message: 'Trader assignment created successfully'
    });
  } catch (error) {
    console.error('Create trader assignment error:', error);
    res.status(500).json({ 
      success: false,
      message: 'Failed to create trader assignment' 
    });
  }
}));

// Update trader assignment
router.put('/:id', asyncHandler(async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const assignment = await TraderAssignment.findByIdAndUpdate(
      id,
      { ...updateData, updatedAt: new Date() },
      { new: true, runValidators: true }
    )
    .populate('traderId', 'name email role')
    .populate('merchantId', 'name email businessName role')
    .populate('assignedBy', 'name email role');

    if (!assignment) {
      return res.status(404).json({
        success: false,
        message: 'Trader assignment not found'
      });
    }

    res.json({
      success: true,
      data: assignment,
      message: 'Trader assignment updated successfully'
    });
  } catch (error) {
    console.error('Update trader assignment error:', error);
    res.status(500).json({ 
      success: false,
      message: 'Failed to update trader assignment' 
    });
  }
}));

// Delete trader assignment
router.delete('/:id', asyncHandler(async (req, res) => {
  try {
    const assignment = await TraderAssignment.findByIdAndDelete(req.params.id);

    if (!assignment) {
      return res.status(404).json({
        success: false,
        message: 'Trader assignment not found'
      });
    }

    res.json({
      success: true,
      message: 'Trader assignment deleted successfully'
    });
  } catch (error) {
    console.error('Delete trader assignment error:', error);
    res.status(500).json({ 
      success: false,
      message: 'Failed to delete trader assignment' 
    });
  }
}));

// Get assignments by trader ID
router.get('/trader/:traderId', asyncHandler(async (req, res) => {
  try {
    const assignments = await TraderAssignment.find({ traderId: req.params.traderId })
      .populate('merchantId', 'name email businessName role')
      .populate('assignedBy', 'name email role')
      .sort({ createdAt: -1 });

    res.json({
      success: true,
      data: assignments
    });
  } catch (error) {
    console.error('Get trader assignments error:', error);
    res.status(500).json({ 
      success: false,
      message: 'Failed to get trader assignments' 
    });
  }
}));

// Get assignments by merchant ID
router.get('/merchant/:merchantId', asyncHandler(async (req, res) => {
  try {
    const assignments = await TraderAssignment.find({ merchantId: req.params.merchantId })
      .populate('traderId', 'name email role')
      .populate('assignedBy', 'name email role')
      .sort({ createdAt: -1 });

    res.json({
      success: true,
      data: assignments
    });
  } catch (error) {
    console.error('Get merchant assignments error:', error);
    res.status(500).json({ 
      success: false,
      message: 'Failed to get merchant assignments' 
    });
  }
}));

module.exports = router;
