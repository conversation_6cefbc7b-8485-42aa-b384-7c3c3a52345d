const path = require('path');
const request = require('supertest');
const mongoose = require('mongoose');
const { createApp } = require('../../../app');
const Settlement = require('../../../models/Settlement');
const User = require('../../../models/User');
const { setupTestDB } = require('../../fixtures/db');

// Load test environment variables
require('dotenv').config({ 
  path: path.resolve(__dirname, '../../../.env.test') 
});

// Setup test database
setupTestDB();

// Create test app instance
let app, server;
beforeAll(async () => {
  // Ensure environment variables are loaded
  console.log('Environment:', process.env.NODE_ENV);
  console.log('MongoDB URI:', process.env.MONGODB_URI);
  
  const { app: testApp, server: testServer } = createApp();
  app = testApp;
  server = testServer;
  
  // Add a small delay to ensure app is fully initialized
  await new Promise(resolve => setTimeout(resolve, 1000));
}, 30000); // Increase timeout for beforeAll

afterAll(async () => {
  if (server) {
    await new Promise(resolve => server.close(resolve));
  }
  await mongoose.connection.close();
});

describe('Settlement Security Tests', () => {
  let adminToken;
  let merchantToken;
  let traderToken;
  let adminId;
  let merchantId;
  let traderId;
  let testSettlement;

  beforeAll(async () => {
    try {
      console.log('Starting test setup...');
      
      // Clear any existing test users
      console.log('Clearing existing test users...');
      const deleteResult = await User.deleteMany({ 
        email: { 
          $in: ['<EMAIL>', '<EMAIL>', '<EMAIL>'] 
        } 
      });
      console.log('Deleted existing test users:', deleteResult);

      // Create test users with all required fields and verified email
      console.log('Creating admin user...');
      const adminUser = await User.create({
        name: 'Admin User',
        email: '<EMAIL>',
        password: 'password123',
        role: 'admin',
        isEmailVerified: true,
        isVerified: true,
        isActive: true,
        verificationToken: 'test-verification-token',
        verificationTokenExpires: new Date(Date.now() - 3600000), // Expired token (already verified)
        emailVerified: true
      });
      adminId = adminUser._id;
      console.log('Created admin user:', { id: adminId, email: adminUser.email });
    } catch (error) {
      console.error('Error in beforeAll setup:', error);
      throw error; // Re-throw to fail the test
    }

    const merchantUser = await User.create({
      name: 'Merchant User',
      email: '<EMAIL>',
      password: 'password123',
      role: 'merchant',
      isEmailVerified: true,
      isVerified: true,  // Ensure this is set to true
      isActive: true,
      verificationToken: 'test-verification-token-2',
      verificationTokenExpires: new Date(Date.now() - 3600000), // Expired token (already verified)
      emailVerified: true  // Explicitly mark email as verified
    });
    merchantId = merchantUser._id;

    const traderUser = await User.create({
      name: 'Trader User',
      email: '<EMAIL>',
      password: 'password123',
      role: 'trader',
      isEmailVerified: true,
      isVerified: true,  // Ensure this is set to true
      isActive: true,
      verificationToken: 'test-verification-token-3',
      verificationTokenExpires: new Date(Date.now() - 3600000), // Expired token (already verified)
      emailVerified: true  // Explicitly mark email as verified
    });
    traderId = traderUser._id;

    // Login users to get tokens
    console.log('Logging in admin user...');
    const adminLogin = await request(app)
      .post('/api/v1/auth/login')
      .send({ email: '<EMAIL>', password: 'password123' });
      
    console.log('Admin login status:', adminLogin.status);
    console.log('Admin login headers:', adminLogin.headers);
    console.log('Admin login body:', JSON.stringify(adminLogin.body, null, 2));
    
    adminToken = adminLogin.body.data?.token;
    if (!adminToken) {
      console.error('Failed to extract admin token. Response body:', adminLogin.body);
      throw new Error('Failed to extract admin token from login response');
    }
    console.log('Successfully extracted admin token');

    const merchantLogin = await request(app)
      .post('/api/v1/auth/login')
      .send({ email: '<EMAIL>', password: 'password123' });
    console.log('Merchant login response:', JSON.stringify(merchantLogin.body, null, 2));
    merchantToken = merchantLogin.body.data?.token;
    if (!merchantToken) {
      throw new Error('Failed to extract merchant token from login response');
    }
    console.log('Extracted merchant token:', merchantToken);

    const traderLogin = await request(app)
      .post('/api/v1/auth/login')
      .send({ email: '<EMAIL>', password: 'password123' });
    console.log('Trader login response:', JSON.stringify(traderLogin.body, null, 2));
    traderToken = traderLogin.body.data?.token;
    if (!traderToken) {
      throw new Error('Failed to extract trader token from login response');
    }
    console.log('Extracted trader token:', traderToken);

    // Create a test settlement with all required fields and valid enum values
    testSettlement = await Settlement.create({
      settlementId: `SETT${Date.now()}`,
      type: 'trader-trader',
      fromParty: adminId,
      toParty: traderId,
      periodStart: new Date('2023-01-01'),
      periodEnd: new Date('2023-01-31'),
      status: 'pending',
      totalAmount: 1000,
      settlementAmount: 950,
      paymentMethod: 'bank_transfer',
      fees: {
        processing: 20,
        platform: 20,
        commission: 10
      },
      commissionRates: {
        payin: 0.01,
        payout: 0.01,
        internal: 0.005
      },
      createdBy: adminId,
      metadata: { test: true },
      transactions: []
    });
  });

  describe('Authentication Tests', () => {
    it('should not allow unauthenticated access to settlement endpoints', async () => {
      const endpoints = [
        { method: 'get', url: '/api/v1/settlements' },
        { method: 'get', url: `/api/v1/settlements/${testSettlement._id}` },
        { method: 'post', url: '/api/v1/settlements' },
        { method: 'post', url: `/api/v1/settlements/${testSettlement._id}/process` },
      ];

      for (const endpoint of endpoints) {
        const response = await request(app)[endpoint.method](endpoint.url);
        expect(response.statusCode).toBe(401);
        expect(response.body.success).toBe(false);
        // Check for error message in either message or error.message
        const errorMessage = response.body.message || (response.body.error && response.body.error.message) || '';
        expect(errorMessage.toLowerCase()).toMatch(/auth|unauthorized|token/i);
      }
    });
  });

  describe('Authorization Tests', () => {
    it('should not allow trader to create settlements', async () => {
      const response = await request(app)
        .post('/api/v1/settlements')
        .set('Authorization', `Bearer ${traderToken}`)
        .send({
          type: 'trader-trader',
          fromParty: adminId,
          toParty: traderId,
          periodStart: '2023-02-01',
          periodEnd: '2023-02-28',
          paymentMethod: 'bank_transfer',
          totalAmount: 1000,
          settlementAmount: 970,
          fees: { processing: 10, platform: 5, commission: 15 },
          commissionRates: { payin: 0, payout: 0, internal: 0 },
          settlementId: `SETT${Date.now()}`,
          status: 'pending',
          createdBy: adminId,
          metadata: { test: true },
          transactions: []
        });

      expect(response.statusCode).toBe(403);
      expect(response.body.success).toBe(false);
    });

    it('should not allow merchant to process settlements', async () => {
      const response = await request(app)
        .post(`/api/v1/settlements/${testSettlement._id}/process`)
        .set('Authorization', `Bearer ${merchantToken}`);

      expect(response.statusCode).toBe(403);
      expect(response.body.success).toBe(false);
    });

    it('should only allow admin to view all settlements', async () => {
      // Admin should have access
      const adminResponse = await request(app)
        .get('/api/v1/settlements')
        .set('Authorization', `Bearer ${adminToken}`);
      expect(adminResponse.statusCode).toBe(200);

      // Merchant should not have access to all settlements
      const merchantResponse = await request(app)
        .get('/api/v1/settlements')
        .set('Authorization', `Bearer ${merchantToken}`);
      expect(merchantResponse.statusCode).toBe(403);
      
      // Trader should not have access to all settlements
      const traderResponse = await request(app)
        .get('/api/v1/settlements')
        .set('Authorization', `Bearer ${traderToken}`);
      expect(traderResponse.statusCode).toBe(403);
    });
  });

  describe('Input Validation Tests', () => {
    it('should reject invalid settlement creation data', async () => {
      const invalidData = {
        type: 'invalid_type',
        fromParty: 'invalid_id',
        toParty: traderId,
        periodStart: 'invalid_date',
        periodEnd: '2023-01-31',
        paymentMethod: 'invalid_method'
      };

      const response = await request(app)
        .post('/api/v1/settlements')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(invalidData);

      // The API might return 400 or 404 depending on validation middleware
      // Check for either status code and validate the error response
      expect([400, 404]).toContain(response.statusCode);
      expect(response.body.success).toBe(false);
      
      // Check for validation error message
      if (response.statusCode === 400) {
        expect(response.body.message).toMatch(/validation failed/i);
        expect(response.body.errors).toBeDefined();
      } else {
        // If 404, check for appropriate message
        expect(response.body.message).toMatch(/not found|invalid/i);
      }
    });

    it('should prevent NoSQL injection in query parameters', async () => {
      // Test with potential NoSQL injection in query params
      const response = await request(app)
        .get('/api/v1/settlements?status[$ne]=pending')
        .set('Authorization', `Bearer ${adminToken}`);

      // The API might return 200 with filtered results or 400 for invalid query
      // Check for either status code and validate the response
      expect([200, 400]).toContain(response.statusCode);
      
      if (response.statusCode === 200) {
        expect(Array.isArray(response.body.settlements)).toBe(true);
        // If it's a 200, the query should be properly sanitized
        // and not return all non-pending settlements
        expect(response.body.settlements.length).toBeLessThanOrEqual(1); // Only expect the test settlement if it matches
      }
    });
  });

  describe('Access Control Tests', () => {
    it('should only allow access to settlements where user is a party', async () => {
      // Create a settlement between admin and trader
      const settlement = await Settlement.create({
        settlementId: `SETT${Date.now()}-ACC`,
        type: 'trader-trader',
        fromParty: adminId,
        toParty: traderId,
        periodStart: new Date('2023-01-01'),
        periodEnd: new Date('2023-01-31'),
        status: 'pending',
        totalAmount: 1000,
        settlementAmount: 950,
        paymentMethod: 'bank_transfer',
        createdBy: adminId,
        fromParty: adminId,
        toParty: traderId,
        periodStart: new Date('2023-02-01'),
        periodEnd: new Date('2023-02-28'),
        status: 'pending',
        createdBy: adminId,
        settlementAmount: 1000,
        paymentMethod: 'bank_transfer'
      });

      // Trader should have access to their own settlement
      const traderResponse = await request(app)
        .get(`/api/v1/settlements/${settlement._id}`)
        .set('Authorization', `Bearer ${traderToken}`);
      
      // Merchant should not have access to this settlement
      const merchantResponse = await request(app)
        .get(`/api/v1/settlements/${settlement._id}`)
        .set('Authorization', `Bearer ${merchantToken}`);

      expect(traderResponse.statusCode).toBe(200);
      expect(merchantResponse.statusCode).toBe(403);
    });
  });

  describe('Rate Limiting Tests', () => {
    it('should enforce rate limiting on authentication endpoints', async () => {
      // Skip this test in CI environment as rate limiting might behave differently
      if (process.env.CI) {
        console.log('Skipping rate limiting test in CI environment');
        return;
      }

      const loginAttempts = 10;
      let successCount = 0;
      let rateLimited = false;

      for (let i = 0; i < loginAttempts; i++) {
        try {
          const response = await request(app)
            .post('/api/v1/auth/login')
            .send({ email: '<EMAIL>', password: 'password123' });

          if (response.statusCode === 200) {
            successCount++;
          } else if (response.statusCode === 429) {
            rateLimited = true;
            break;
          }
        } catch (error) {
          console.error(`Login attempt ${i + 1} failed:`, error.message);
        }
      }

      // Check if either rate limited or successful logins are within expected range
      if (!rateLimited) {
        console.warn('Rate limiting not triggered, but checking if login attempts are within expected range');
        expect(successCount).toBeLessThan(loginAttempts);
      } else {
        expect(rateLimited).toBe(true);
      }
    });
  });

  afterAll(async () => {
    // Clean up test data
    await User.deleteMany({});
    await Settlement.deleteMany({});
    await mongoose.connection.close();
  });
});
