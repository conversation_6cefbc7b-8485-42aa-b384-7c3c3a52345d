import React, { useState, useEffect } from 'react';
import { useTheme, styled } from '@mui/material/styles';
import { 
  Box, 
  CssBaseline, 
  Drawer, 
  AppBar, 
  Toolbar, 
  List, 
  Typography, 
  Divider, 
  IconButton, 
  ListItem, 
  ListItemButton, 
  ListItemIcon, 
  ListItemText, 
  useMediaQuery as muiUseMediaQuery,
  Avatar,
  Menu,
  MenuItem,
  Tooltip,
  Badge,
  Theme,
  CircularProgress,
  Skeleton,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  Menu as MenuIcon,
  ChevronLeft as ChevronLeftIcon,
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  Settings as SettingsIcon,
  Notifications as NotificationsIcon,
  Payment as PaymentIcon,
  Receipt as ReceiptIcon,
  AccountBalanceWallet as WalletIcon,
  Help as HelpIcon,
  Logout as LogoutIcon,
  Person as PersonIcon,
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
// Authentication removed

import { MenuItem as ApiMenuItem, menuService } from '../services/menuService';
import { useAuth } from '../contexts/AuthContext';

interface NavigationItem extends ApiMenuItem {
  icon: React.ReactNode;
  path: string;
  roles: string[];
  submenus?: NavigationItem[];
}

// Map of icon names to MUI icons
const iconMap: Record<string, React.ReactNode> = {
  Dashboard: <DashboardIcon />,
  People: <PeopleIcon />,
  Settings: <SettingsIcon />,
  Payment: <PaymentIcon />,
  Receipt: <ReceiptIcon />,
  Wallet: <WalletIcon />,
  Help: <HelpIcon />,
  Person: <PersonIcon />,
  Notifications: <NotificationsIcon />,
  Logout: <LogoutIcon />,
  // Add more icons as needed
  default: <DashboardIcon />
};

const drawerWidth = 240;

interface MainProps {
  open?: boolean;
}

const Main = styled('main', { shouldForwardProp: (prop) => prop !== 'open' })<{ open?: boolean }>(({ theme, open }) => ({
  flexGrow: 1,
  padding: theme.spacing(3),
  transition: theme.transitions.create('margin', {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  marginLeft: 0,
  [theme.breakpoints.up('sm')]: {
    ...(open && {
      marginLeft: `${drawerWidth}px`,
      width: `calc(100% - ${drawerWidth}px)`,
    }),
  },
  [theme.breakpoints.down('sm')]: {
    padding: theme.spacing(2),
  },
}));

// Convert API menu items to navigation items
const mapApiToNavigationItems = (apiItems: ApiMenuItem[]): NavigationItem[] => {
  return apiItems.map(item => ({
    ...item,
    icon: iconMap[item.icon] || iconMap.default,
    path: item.to,
    roles: item.role === 'all' ? ['admin', 'merchant', 'trader'] : [item.role],
    submenus: item.submenus ? mapApiToNavigationItems(item.submenus) : undefined
  }));
};

interface DashboardLayoutProps {
  children: React.ReactNode;
  title?: string;
}

export const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children, title = 'Dashboard' }) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout } = useAuth();
  const isMobile = muiUseMediaQuery(theme.breakpoints.down('md'));
  const [mobileOpen, setMobileOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [navigationItems, setNavigationItems] = useState<NavigationItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sidebarOpen, setSidebarOpen] = useState(() => {
    // Initialize from localStorage if available, default to true
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('sidebarOpen');
      return saved !== null ? JSON.parse(saved) : true;
    }
    return true;
  });
  
  // Fetch menu items from API
  useEffect(() => {
    const fetchMenu = async () => {
      if (!user?.role) return;
      
      try {
        setLoading(true);
        const menuItems = await menuService.getMenuByRole(user.role);
        setNavigationItems(mapApiToNavigationItems(menuItems));
        setError(null);
      } catch (err) {
        console.error('Failed to load menu:', err);
        setError('Failed to load navigation menu. Please try again later.');
        // Fallback to default menu if API fails
        setNavigationItems(getFallbackMenuItems(user.role));
      } finally {
        setLoading(false);
      }
    };
    
    fetchMenu();
  }, [user?.role]);
  
  // Fallback menu items in case API fails
  const getFallbackMenuItems = (role: string): NavigationItem[] => {
    const commonItems: NavigationItem[] = [
      {
        text: 'Dashboard',
        icon: <DashboardIcon />,
        path: '/dashboard',
        roles: ['admin', 'merchant', 'trader'],
      },
    ];

    const merchantItems: NavigationItem[] = [
      {
        text: 'Transactions',
        icon: <ReceiptIcon />,
        path: '/merchant/transactions',
        roles: ['merchant'],
      },
      {
        text: 'Collections',
        icon: <PaymentIcon />,
        path: '/merchant/collections',
        roles: ['merchant'],
      },
      {
        text: 'Billing',
        icon: <WalletIcon />,
        path: '/merchant/billing',
        roles: ['merchant'],
      },
      {
        text: 'Traders',
        icon: <PeopleIcon />,
        path: '/merchant/traders',
        roles: ['merchant'],
      },
      {
        text: 'Settings',
        icon: <SettingsIcon />,
        path: '/merchant/settings',
        roles: ['merchant'],
      },
    ];

    const adminItems: NavigationItem[] = [
      {
        text: 'Merchants',
        icon: <PeopleIcon />,
        path: '/admin/merchants',
        roles: ['admin'],
      },
      {
        text: 'Traders',
        icon: <PeopleIcon />,
        path: '/admin/traders',
        roles: ['admin'],
      },
      {
        text: 'Payments',
        icon: <PaymentIcon />,
        path: '/admin/payments',
        roles: ['admin'],
      },
      {
        text: 'Reports',
        icon: <ReceiptIcon />,
        path: '/admin/reports',
        roles: ['admin'],
      },
      {
        text: 'Settings',
        icon: <SettingsIcon />,
        path: '/admin/settings',
        roles: ['admin'],
      },
    ];

    return [
      ...commonItems,
      ...(role === 'merchant' ? merchantItems : []),
      ...(role === 'admin' ? adminItems : []),
    ];
  };

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const toggleSidebar = () => {
    const newState = !sidebarOpen;
    setSidebarOpen(newState);
    // Persist to localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('sidebarOpen', JSON.stringify(newState));
    }
  };

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const handleNavigation = (path: string) => {
    navigate(path);
    if (isMobile) {
      setMobileOpen(false);
    }
  };

        </Box>
      ) : error ? (
        <Box sx={{ p: 2, color: 'error.main' }}>
          <Typography variant="body2">{error}</Typography>
        </Box>
      ) : (
        <List>
          {renderMenuItems(navigationItems)}
        </List>
      )}
    </div>
  );

  const container = typeof window !== 'undefined' ? () => window.document.body : undefined;

  return (
    <Box sx={{ display: 'flex' }}>
      <CssBaseline />
      <AppBar
        position="fixed"
        sx={{
          width: { sm: `calc(100% - ${sidebarOpen ? drawerWidth : 0}px)` },
          ml: { sm: `${sidebarOpen ? drawerWidth : 0}px` },
          transition: theme.transitions.create(['width', 'margin'], {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.enteringScreen,
          }),
        }}
      >
        <Toolbar>
          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
            <IconButton
              color="inherit"
              aria-label="open drawer"
              edge="start"
              onClick={handleDrawerToggle}
              sx={{ 
                mr: 2, 
                display: { sm: 'none' },
              }}
            >
              <MenuIcon />
            </IconButton>
            <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
              {title}
            </Typography>
            
                <Tooltip title={sidebarOpen ? 'Collapse sidebar' : 'Expand sidebar'}>
                <IconButton
                  color="inherit"
                  onClick={toggleSidebar}
                  sx={{ display: { xs: 'none', sm: 'flex' }, mr: 1 }}
                >
                  {sidebarOpen ? <ChevronLeftIcon /> : <MenuIcon />}
                </IconButton>
              </Tooltip>
          </Box>
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            {title}
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Tooltip title="Notifications">
              <IconButton color="inherit">
                <Badge badgeContent={4} color="secondary">
                  <NotificationsIcon />
                </Badge>
              </IconButton>
            </Tooltip>
            <Tooltip title={user?.name || 'User'}>
              <IconButton
                onClick={(e) => setAnchorEl(e.currentTarget)}
                color="inherit"
                sx={{ ml: 1 }}
              >
                <Avatar 
                  sx={{ 
                    width: 32, 
                    height: 32,
                    bgcolor: theme.palette.primary.main
                  }}
                >
                  {user?.name?.charAt(0) || 'U'}
                </Avatar>
              </IconButton>
            </Tooltip>
          </Box>
        </Toolbar>
      </AppBar>
      <Box
        component="nav"
        sx={{ 
          width: { sm: sidebarOpen ? drawerWidth : 0 },
          flexShrink: { sm: 0 },
          transition: theme.transitions.create('width', {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.leavingScreen,
          }),
        }}
        aria-label="sidebar"
      >
        {/* Mobile Drawer */}
        <Drawer
          container={container}
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true, // Better open performance on mobile.
          }}
          sx={{
            display: { xs: 'block', sm: 'none' },
            '& .MuiDrawer-paper': { 
              boxSizing: 'border-box', 
              width: drawerWidth,
              transition: theme.transitions.create('width', {
                easing: theme.transitions.easing.sharp,
                duration: theme.transitions.duration.enteringScreen,
              }),
            },
          }}
        >
          {drawer}
        </Drawer>

        {/* Desktop Drawer */}
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', sm: 'block' },
            '& .MuiDrawer-paper': { 
              boxSizing: 'border-box',
              width: sidebarOpen ? drawerWidth : 0,
              overflowX: 'hidden',
              transition: theme.transitions.create('width', {
                easing: theme.transitions.easing.sharp,
                duration: theme.transitions.duration.leavingScreen,
              }),
              borderRight: 'none',
              boxShadow: theme.shadows[3],
            },
          }}
          open={sidebarOpen}
        >
          {drawer}
        </Drawer>
      </Box>
      
      <Main open={sidebarOpen}>
        {children}
      </Main>

      {/* Profile Menu */}
      <Menu
        anchorEl={anchorEl}
        id="account-menu"
        open={Boolean(anchorEl)}
        onClose={() => setAnchorEl(null)}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem onClick={() => handleNavigation('/profile')}>
          <ListItemIcon>
            <PersonIcon fontSize="small" />
          </ListItemIcon>
          Profile
        </MenuItem>
        <MenuItem onClick={handleLogout}>
          <ListItemIcon>
            <LogoutIcon fontSize="small" />
          </ListItemIcon>
          Logout
        </MenuItem>
      </Menu>
    </Box>
          <ListItemIcon>
            <PersonIcon fontSize="small" />
          </ListItemIcon>
          Profile
        </MenuItem>
        <Divider />
        <MenuItem onClick={handleLogout}>
          <ListItemIcon>
            <LogoutIcon fontSize="small" />
          </ListItemIcon>
          Logout
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default DashboardLayout;
