import { useQuery, useMutation, useQueryClient, UseQueryOptions } from '@tanstack/react-query';
import { apiClient } from '../../lib/api-client';
import { queryOptions } from '../../lib/react-query';
import { AxiosResponse } from 'axios';

// Helper type to extract the data type from ApiResponse
type ApiResponseData<T> = T extends { data: infer U } ? U : never;

export interface Merchant {
  id: string;
  businessName: string;
  email: string;
  phone: string;
  status: 'active' | 'pending' | 'suspended';
  createdAt: string;
  updatedAt: string;
}

export interface MerchantStats {
  totalRevenue: number;
  totalTransactions: number;
  activeTraders: number;
  pendingPayouts: number;
}

export interface Collection {
  id: string;
  amount: number;
  status: 'pending' | 'completed' | 'failed';
  trader: {
    id: string;
    name: string;
    email: string;
  };
  reference: string;
  createdAt: string;
}

// API Endpoints
const ENDPOINTS = {
  merchant: '/merchants/me',
  stats: '/merchants/me/stats',
  collections: '/merchants/me/collections',
  transactions: '/merchants/me/transactions',
  traders: '/merchants/me/traders',
  settings: '/merchants/me/settings',
};

// Base response type for API calls
interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

// Type for paginated responses
interface PaginatedData<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Merchant Queries
export const useMerchant = (options?: Omit<UseQueryOptions<Merchant, Error, Merchant, string[]>, 'queryKey' | 'queryFn'>) => {
  return useQuery<Merchant, Error, Merchant, string[]>({
    queryKey: ['merchant'],
    queryFn: async (): Promise<Merchant> => {
      const response = await apiClient.get<ApiResponse<Merchant>>(ENDPOINTS.merchant);
      return response.data;
    },
    ...queryOptions.withErrorHandling(),
    ...options,
  });
};

export const useMerchantStats = (options?: Omit<UseQueryOptions<MerchantStats, Error, MerchantStats, string[]>, 'queryKey' | 'queryFn'>) => {
  return useQuery<MerchantStats, Error, MerchantStats, string[]>({
    queryKey: ['merchant', 'stats'],
    queryFn: async (): Promise<MerchantStats> => {
      const response = await apiClient.get<ApiResponse<MerchantStats>>(ENDPOINTS.stats);
      return response.data;
    },
    ...queryOptions.withErrorHandling(),
    ...options,
  });
};

// Collections
export interface GetCollectionsParams {
  status?: 'pending' | 'completed' | 'failed';
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  startDate?: string;
  endDate?: string;
  page?: number;
  limit?: number;
  traderId?: string;
  reference?: string;
  minAmount?: number;
  maxAmount?: number;
}

interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export const useCollections = (params: GetCollectionsParams = {}, options?: Omit<UseQueryOptions<PaginatedData<Collection>, Error, PaginatedData<Collection>, (string | GetCollectionsParams)[]>, 'queryKey' | 'queryFn'>) => {
  return useQuery<PaginatedData<Collection>, Error, PaginatedData<Collection>, (string | GetCollectionsParams)[]>({
    queryKey: ['merchant', 'collections', params],
    queryFn: async (): Promise<PaginatedData<Collection>> => {
      const response = await apiClient.get<ApiResponse<PaginatedData<Collection>>>(ENDPOINTS.collections, { params });
      return response.data;
    },
    ...queryOptions.withErrorHandling(),
    ...options,
  });
};

// Transactions
export interface Transaction {
  id: string;
  amount: number;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  type: string;
  reference: string;
  description: string;
  createdAt: string;
  updatedAt: string;
}

export const useTransactions = (params: Record<string, any> = {}, options?: Omit<UseQueryOptions<PaginatedData<Transaction>, Error, PaginatedData<Transaction>, (string | Record<string, any>)[]>, 'queryKey' | 'queryFn'>) => {
  return useQuery<PaginatedData<Transaction>, Error, PaginatedData<Transaction>, (string | Record<string, any>)[]>({
    queryKey: ['merchant', 'transactions', params],
    queryFn: async (): Promise<PaginatedData<Transaction>> => {
      const response = await apiClient.get<ApiResponse<PaginatedData<Transaction>>>(ENDPOINTS.transactions, { params });
      return response.data;
    },
    ...queryOptions.paginated(),
    ...queryOptions.withErrorHandling(),
    ...options,
  });
};

// Traders
export interface Trader {
  id: string;
  name: string;
  email: string;
  phone: string;
  status: 'active' | 'inactive' | 'suspended';
  lastActive: string;
  createdAt: string;
}

export const useTraders = (options?: Omit<UseQueryOptions<Trader[], Error, Trader[], string[]>, 'queryKey' | 'queryFn'>) => {
  return useQuery<Trader[], Error, Trader[], string[]>({
    queryKey: ['merchant', 'traders'],
    queryFn: async (): Promise<Trader[]> => {
      const response = await apiClient.get<ApiResponse<Trader[]>>(ENDPOINTS.traders);
      return response.data;
    },
    ...queryOptions.withErrorHandling(),
    ...options,
  });
};

// Settings
export interface MerchantSettings {
  id: string;
  businessName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  country: string;
  postalCode: string;
  taxId: string;
  website: string;
  logoUrl: string;
  currency: string;
  timezone: string;
  notificationPreferences: {
    email: boolean;
    sms: boolean;
    push: boolean;
  };
  security: {
    twoFactorEnabled: boolean;
    lastPasswordChange: string;
  };
  createdAt: string;
  updatedAt: string;
}

export const useMerchantSettings = (options?: Omit<UseQueryOptions<MerchantSettings, Error, MerchantSettings, string[]>, 'queryKey' | 'queryFn'>) => {
  return useQuery<MerchantSettings, Error, MerchantSettings, string[]>({
    queryKey: ['merchant', 'settings'],
    queryFn: async (): Promise<MerchantSettings> => {
      const response = await apiClient.get<ApiResponse<MerchantSettings>>(ENDPOINTS.settings);
      return response.data;
    },
    ...queryOptions.withErrorHandling(),
    ...options,
  });
};

export const useUpdateMerchantSettings = () => {
  const queryClient = useQueryClient();
  
  return useMutation<MerchantSettings, Error, Partial<MerchantSettings>>({
    mutationFn: async (data): Promise<MerchantSettings> => {
      const response = await apiClient.put<ApiResponse<MerchantSettings>>(ENDPOINTS.settings, data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['merchant', 'settings'] });
    },
    ...queryOptions.withErrorHandling(),
  });
};

// API Hooks Export
const merchantApi = {
  useMerchant,
  useMerchantStats,
  useCollections,
  useTransactions,
  useTraders,
  useMerchantSettings,
  useUpdateMerchantSettings,
};

export default merchantApi;
