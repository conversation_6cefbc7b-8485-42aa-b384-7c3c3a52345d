# Database Export Summary
- **Database**: payment-gateway
- **Export Time**: 7/25/2025, 9:30:54 PM
- **Total Collections**: 6

## Collections

### adminmenus
- **Documents**: 15
- **Fields**: _id, to, label, __v

#### Sample Document

```json
{
  "_id": "687e37c32ab16b2c1aa10131",
  "to": "/admin/dashboard",
  "label": "Dashboard",
  "__v": 0
}
```

### transactions
- **Documents**: 6
- **Fields**: _id, transactionId, merchantId, traderId, amount, currency, status, paymentMethod, paymentDetails, customerInfo, fees, webhookDelivered, webhookAttempts, refundAmount, disputeStatus, riskScore, settlementDate, notes, createdAt, updatedAt, netAmount, __v

#### Sample Document

```json
{
  "_id": "6881cde138f0c102b1675cf8",
  "transactionId": "txn_30f7e1f5b948ba83758cb169ca6b2a12",
  "merchantId": "6881cde038f0c102b1675cef",
  "traderId": "6881cde138f0c102b1675cf6",
  "amount": 99.99,
  "currency": "USD",
  "status": "completed",
  "paymentMethod": "card",
  "paymentDetails": {
    "cardLast4": "4242",
    "cardBrand": "Visa"
  },
  "customerInfo": {
    "email": "<EMAIL>",
    "name": "Customer One",
    "billingAddress": {
      "street": "123 Main St",
      "city": "New York",
      "state": "NY",
      "zipCode": "10001",
      "country": "US"
    }
  },
  "fees": {
    "processingFee": 3.2,
    "platformFee": 0.5
  },
  "webhookDelivered": false,
  "webhookAttempts": 0,
  "refundAmount": 0,
  "disputeStatus": "none",
  "riskScore": 0,
  "settlementDate": "2025-07-26T06:08:33.952Z",
  "notes": [],
  "createdAt": "2025-07-24T06:08:33.957Z",
  "updatedAt": "2025-07-24T06:08:33.957Z",
  "netAmount": 96.28999999999999,
  "__v": 0
}
```

### users
- **Documents**: 9
- **Fields**: _id, name, email, password, role, isVerified, twoFactorEnabled, security, isActive, loginAttempts, twoFactorRecoveryCodes, sessions, createdAt, updatedAt, __v, lastActive, lastLogin, userId, businessName, businessType, phone, website, apiKey, status, profile

#### Sample Document

```json
{
  "_id": "6881cde038f0c102b1675ce8",
  "name": "Admin User",
  "email": "<EMAIL>",
  "password": "$2a$12$ZY0X0WZlMNNlqvYoAEwfVu1x7vgbcB9WTbXcsb1kG..Kp1SIbgOQu",
  "role": "admin",
  "isVerified": true,
  "twoFactorEnabled": false,
  "security": {
    "passwordHistory": [],
    "loginHistory": [
      {
        "timestamp": "2025-07-25T10:08:09.916Z",
        "success": true,
        "_id": "688357896d5b185909af84e8"
      },
      {
        "timestamp": "2025-07-25T10:11:25.110Z",
        "success": true,
        "_id": "6883584d6d5b185909af84ec"
      },
      {
        "timestamp": "2025-07-25T10:12:43.251Z",
        "success": true,
        "_id": "6883589b6d5b185909af84f1"
      },
      {
        "timestamp": "2025-07-25T10:24:19.013Z",
        "success": true,
        "_id": "68835b536d5b185909af84f7"
      },
      {
        "timestamp": "2025-07-25T10:38:07.589Z",
        "success": true,
        "_id": "68835e8f6501eda695a663c4"
      },
      {
        "timestamp": "2025-07-25T10:38:47.765Z",
        "success": true,
        "_id": "68835eb76501eda695a663cc"
      },
      {
        "timestamp": "2025-07-25T11:09:34.353Z",
        "success": true,
        "_id": "688365ee6501eda695a663db"
      }
    ]
  },
  "isActive": true,
  "loginAttempts": 0,
  "twoFactorRecoveryCodes": [],
  "sessions": [],
  "createdAt": "2025-07-24T06:08:32.200Z",
  "updatedAt": "2025-07-25T11:09:34.364Z",
  "__v": 7,
  "lastActive": "2025-07-25T11:09:34.357Z",
  "lastLogin": "2025-07-25T11:09:34.356Z",
  "userId": "6883a71c0dd872be81f0e6c8"
}
```

### merchants
- **Documents**: 3
- **Fields**: _id, merchantId, userId, businessInfo, bankDetails, kycDocuments, paymentSettings, isActive, createdAt, updatedAt, __v

#### Sample Document

```json
{
  "_id": "686eacedb7192975f7e7e9a4",
  "merchantId": "686eacedb7192975f7e7e9a3",
  "userId": "686eacedb7192975f7e7e9a2",
  "businessInfo": {
    "businessName": "Test Business",
    "businessType": "retail",
    "website": "https://testbusiness.com"
  },
  "bankDetails": {
    "accountNumber": "************",
    "bankName": "HDFC Bank",
    "ifscCode": "HDFC0001234"
  },
  "kycDocuments": [
    {
      "documentType": "business_license",
      "documentUrl": "https://example.com/business_license.jpg",
      "verificationStatus": "verified",
      "_id": "686eacedb7192975f7e7e9a5",
      "uploadedAt": "2025-07-09T17:54:53.678Z"
    },
    {
      "documentType": "tax_certificate",
      "documentUrl": "https://example.com/tax_certificate.jpg",
      "verificationStatus": "verified",
      "_id": "686eacedb7192975f7e7e9a6",
      "uploadedAt": "2025-07-09T17:54:53.679Z"
    }
  ],
  "paymentSettings": {
    "acceptedMethods": [],
    "currencies": [],
    "commissionRate": 2.5,
    "settlementCycle": "weekly"
  },
  "isActive": false,
  "createdAt": "2025-07-09T17:54:53.680Z",
  "updatedAt": "2025-07-09T17:54:53.680Z",
  "__v": 0
}
```

### merchantmenus
- **Documents**: 10
- **Fields**: _id, to, label, __v

#### Sample Document

```json
{
  "_id": "687e37bdd45b9ac1ffd7bcff",
  "to": "/merchant/dashboard",
  "label": "Dashboard",
  "__v": 0
}
```

### tradermenus
- **Documents**: 8
- **Fields**: _id, to, label, __v

#### Sample Document

```json
{
  "_id": "687e37c1dac9deef2b570e16",
  "to": "/trader/dashboard",
  "label": "Dashboard",
  "__v": 0
}
```

