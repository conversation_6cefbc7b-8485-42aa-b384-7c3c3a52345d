const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const logger = require('../../utils/logger');

let mongoServer;

/**
 * Connect to the in-memory database.
 */
const connect = async () => {
  try {
    logger.info('Starting in-memory MongoDB server...');
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();

    const mongooseOpts = {
      // useNewUrlParser and useUnifiedTopology are now default in Mongoose 6+
      // useCreateIndex and useFindAndModify are no longer supported in MongoDB Node.js driver 4.0.0+
      // Instead, set these options at the schema level if needed
      serverSelectionTimeoutMS: 10000, // 10 seconds timeout for initial connection
      socketTimeoutMS: 45000, // 45 seconds timeout for queries
    };

    logger.info(`Connecting to in-memory MongoDB at ${uri}...`);
    await mongoose.connect(uri, mongooseOpts);
    logger.info('Successfully connected to in-memory MongoDB');
  } catch (error) {
    logger.error('Failed to connect to in-memory MongoDB:', error);
    throw error;
  }
};

/**
 * Drop database, close the connection and stop mongod.
 */
const closeDatabase = async () => {
  try {
    if (mongoose.connection.readyState !== 0) { // 0 = disconnected
      logger.info('Dropping test database...');
      await mongoose.connection.dropDatabase();
      logger.info('Closing MongoDB connection...');
      await mongoose.connection.close();
    }
    
    if (mongoServer) {
      logger.info('Stopping in-memory MongoDB server...');
      await mongoServer.stop();
      logger.info('In-memory MongoDB server stopped');
    }
  } catch (error) {
    logger.error('Error while closing database connection:', error);
    throw error;
  }
};

/**
 * Remove all the data for all db collections.
 */
const clearDatabase = async () => {
  try {
    const collections = mongoose.connection.collections;
    logger.info('Clearing test database collections...');

    for (const key in collections) {
      const collection = collections[key];
      logger.debug(`Deleting all documents from ${key} collection`);
      await collection.deleteMany({});
    }
    
    logger.info('Test database cleared');
  } catch (error) {
    logger.error('Error while clearing test database:', error);
    throw error;
  }
};

/**
 * Setup test database before tests run
 */
const setupTestDB = () => {
  // Connect to a new in-memory database before running any tests
  beforeAll(async () => {
    try {
      await connect();
    } catch (error) {
      logger.error('Failed to set up test database:', error);
      throw error;
    }
  }, 30000); // Increase timeout for beforeAll to 30 seconds

  // Clear all test data after each test
  afterEach(async () => {
    try {
      await clearDatabase();
    } catch (error) {
      logger.error('Error during test cleanup:', error);
      throw error;
    }
  });

  // Remove and close the db and server after all tests
  afterAll(async () => {
    try {
      await closeDatabase();
    } catch (error) {
      logger.error('Error during test teardown:', error);
      throw error;
    }
  }, 30000); // Increase timeout for afterAll to 30 seconds
};

module.exports = {
  connect,
  closeDatabase,
  clearDatabase,
  setupTestDB,
};
