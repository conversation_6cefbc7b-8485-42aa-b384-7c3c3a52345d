import { useQuery } from '@tanstack/react-query';
import { User } from '../types/trader';

/**
 * Hook to fetch admin data
 */
export const useAdmin = () => {
  return useQuery({
    queryKey: ['admin'],
    queryFn: async (): Promise<{ users: User[] }> => {
      // In a real app, this would be an API call
      return {
        users: [],
      };
    },
  });
};

/**
 * Hook to fetch traders list
 */
export const useGetTraders = () => {
  return useQuery({
    queryKey: ['traders'],
    queryFn: async (): Promise<User[]> => {
      // In a real app, this would be an API call
      return [];
    },
  });
};
