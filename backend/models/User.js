const mongoose = require("mongoose");
const bcrypt = require("bcryptjs");
const crypto = require('crypto');
const speakeasy = require('speakeasy');

const userSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    email: {
      type: String,
      required: true,
      unique: true,
      lowercase: true,
      trim: true,
      match: [/^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/, 'Please enter a valid email'],
    },
    password: {
      type: String,
      required: true,
      minlength: 8,
      select: false,
    },
    role: {
      type: String,
      enum: ["admin", "merchant", "trader"],
      default: "merchant",
    },
    isVerified: {
      type: Boolean,
      default: false,
    },
    verificationToken: {
      type: String,
      select: false,
    },
    verificationTokenExpires: {
      type: Date,
      select: false,
    },
    resetPasswordToken: {
      type: String,
      select: false,
    },
    resetPasswordExpires: {
      type: Date,
      select: false,
    },
    // 2FA Settings
    twoFactorEnabled: {
      type: Boolean,
      default: false,
    },
    twoFactorSecret: {
      type: String,
      select: false,
    },
    twoFactorRecoveryCodes: [{
      code: String,
      used: {
        type: Boolean,
        default: false
      }
    }],
    // Session Management
    lastActive: Date,
    sessions: [
      {
        _id: mongoose.Schema.Types.ObjectId,
        ip: String,
        userAgent: String,
        lastUsed: {
          type: Date,
          default: Date.now,
        },
        createdAt: {
          type: Date,
          default: Date.now,
        },
      },
    ],
    // Security Settings
    security: {
      lastPasswordChange: Date,
      passwordHistory: [{
        hash: String,
        changedAt: Date
      }],
      loginHistory: [{
        ip: String,
        userAgent: String,
        timestamp: {
          type: Date,
          default: Date.now
        },
        success: Boolean
      }]
    },
    businessName: String,
    businessType: String,
    businessAddress: {
      street: String,
      city: String,
      state: String,
      zipCode: String,
      country: String,
    },
    phone: String,
    website: String,
    webhookUrl: String,
    isActive: {
      type: Boolean,
      default: true,
    },
    lastLogin: Date,
    loginAttempts: {
      type: Number,
      default: 0,
    },
    lockUntil: Date,
  },
  {
    timestamps: true,
  },
)

// Index for performance
userSchema.index({ email: 1 })
userSchema.index({ apiKey: 1 })

// Virtual for account lock
userSchema.virtual("isLocked").get(function () {
  return !!(this.lockUntil && this.lockUntil > Date.now())
})

// Pre-save middleware to hash password
userSchema.pre("save", async function (next) {
  if (!this.isModified("password")) return next()

  try {
    const salt = await bcrypt.genSalt(12)
    this.password = await bcrypt.hash(this.password, salt)
    next()
  } catch (error) {
    next(error)
  }
})

// Method to compare password
userSchema.methods.comparePassword = async function (candidatePassword) {
  const ApiError = require('../utils/ApiError');
  
  // Check if account is locked
  if (this.lockUntil && this.lockUntil > Date.now()) {
    const lockTimeLeft = Math.ceil((this.lockUntil - Date.now()) / (1000 * 60));
    throw new ApiError(403, `Account is temporarily locked. Please try again in ${lockTimeLeft} minutes.`);
  }

  // Compare passwords
  const isMatch = await bcrypt.compare(candidatePassword, this.password);

  if (isMatch) {
    // Reset failed login attempts on successful login
    this.failedLoginAttempts = 0;
    this.loginAttempts = 0;
    this.lockUntil = undefined;
    this.lastLogin = new Date();
    this.lastActive = new Date();
    await this.save();
    return true;
  } else {
    // Increment failed login attempts
    this.failedLoginAttempts = (this.failedLoginAttempts || 0) + 1;
    this.loginAttempts = (this.loginAttempts || 0) + 1;

    // Lock account after 5 failed attempts for 1 hour
    if (this.failedLoginAttempts >= 5) {
      this.lockUntil = new Date(Date.now() + 60 * 60 * 1000);
      await this.save();
      throw new ApiError(403, 'Too many failed login attempts. Account locked for 1 hour.');
    }

    await this.save();
    return false;
  }
}

// Method to create a new session
userSchema.methods.createSession = async function(ip, userAgent) {
  try {
    const session = {
      _id: new mongoose.Types.ObjectId(),
      ip: ip,
      userAgent: userAgent,
      lastUsed: new Date(),
      createdAt: new Date()
    };

    // Initialize sessions array if it doesn't exist
    if (!this.sessions) {
      this.sessions = [];
    }
    
    this.sessions.push(session);
    
    // Limit the number of stored sessions to 10
    if (this.sessions.length > 10) {
      this.sessions = this.sessions.slice(-10);
    }

    await this.save();
    return session._id.toString();
  } catch (error) {
    console.error('Error creating session:', error);
    throw new Error('Error creating user session');
  }
};

// Generate 2FA secret
userSchema.methods.generateTwoFactorSecret = function() {
  const secret = speakeasy.generateSecret({
    name: `PaymentGateway:${this.email}`,
    issuer: 'PaymentGateway'
  });
  
  this.twoFactorSecret = secret.base32;
  return secret;
};

// Verify 2FA token
userSchema.methods.verifyTwoFactorToken = function(token) {
  return speakeasy.totp.verify({
    secret: this.twoFactorSecret,
    encoding: 'base32',
    token: token,
    window: 1 // Allow 1 step (30s) before/after current time
  });
};

// Generate password reset token
userSchema.methods.generatePasswordResetToken = function() {
  const resetToken = crypto.randomBytes(32).toString('hex');
  this.resetPasswordToken = crypto
    .createHash('sha256')
    .update(resetToken)
    .digest('hex');
  this.resetPasswordExpires = Date.now() + 10 * 60 * 1000; // 10 minutes
  return resetToken;
};

// Generate email verification token
userSchema.methods.generateVerificationToken = function() {
  const verificationToken = crypto.randomBytes(32).toString('hex');
  this.verificationToken = crypto
    .createHash('sha256')
    .update(verificationToken)
    .digest('hex');
  this.verificationTokenExpires = Date.now() + 24 * 60 * 60 * 1000; // 24 hours
  return verificationToken;
};

// Check if password was used before
userSchema.methods.isPasswordUsed = async function(newPassword) {
  for (const pwd of this.security.passwordHistory) {
    if (await bcrypt.compare(newPassword, pwd.hash)) {
      return true;
    }
  }
  return false;
};

// Update password with history
userSchema.methods.updatePassword = async function(newPassword) {
  // Add current password to history
  this.security.passwordHistory.unshift({
    hash: this.password,
    changedAt: new Date()
  });
  
  // Keep only last 5 passwords
  if (this.security.passwordHistory.length > 5) {
    this.security.passwordHistory = this.security.passwordHistory.slice(0, 5);
  }
  
  // Update password
  const salt = await bcrypt.genSalt(12);
  this.password = await bcrypt.hash(newPassword, salt);
  this.security.lastPasswordChange = new Date();
  
  // Invalidate all sessions except current one
  this.sessions = this.sessions.filter(session => session._id === this.currentSessionId);
  
  await this.save();
};

// Method to generate API key
userSchema.methods.generateApiKey = function () {
  const crypto = require("crypto")
  this.apiKey = "pk_" + crypto.randomBytes(32).toString("hex")
  return this.apiKey
}

module.exports = mongoose.model("User", userSchema)
