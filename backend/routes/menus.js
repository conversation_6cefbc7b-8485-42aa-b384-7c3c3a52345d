const express = require('express');
const router = express.Router();
const Menu = require('../models/Menu');

// Simple async handler
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

// Get all menus with optional role filtering
router.get('/', asyncHandler(async (req, res) => {
  try {
    const { role, includeInactive } = req.query;
    
    let query = {};
    if (role && role !== 'all') {
      query.$or = [
        { role: role },
        { role: 'all' }
      ];
    }
    
    if (!includeInactive) {
      query.isActive = true;
    }
    
    const menus = await Menu.find(query)
      .sort({ order: 1, label: 1 })
      .populate('submenus');
    
    // Organize menus hierarchically
    const mainMenus = menus.filter(menu => !menu.parentId);
    const subMenus = menus.filter(menu => menu.parentId);
    
    // Attach submenus to their parents
    const organizedMenus = mainMenus.map(menu => {
      const menuObj = menu.toObject();
      menuObj.submenus = subMenus.filter(sub => sub.parentId === menu.menuId);
      return menuObj;
    });
    
    res.json({
      success: true,
      data: organizedMenus,
      meta: {
        total: menus.length,
        mainMenus: mainMenus.length,
        subMenus: subMenus.length
      }
    });
  } catch (error) {
    console.error('Get menus error:', error);
    res.status(500).json({ 
      success: false,
      message: 'Failed to get menus' 
    });
  }
}));

// Get menus by role
router.get('/role/:role', asyncHandler(async (req, res) => {
  try {
    const { role } = req.params;
    
    const menus = await Menu.find({
      $or: [
        { role: role },
        { role: 'all' }
      ],
      isActive: true
    }).sort({ order: 1, label: 1 });
    
    res.json({
      success: true,
      data: menus
    });
  } catch (error) {
    console.error('Get menus by role error:', error);
    res.status(500).json({ 
      success: false,
      message: 'Failed to get menus by role' 
    });
  }
}));

// Get menu by ID
router.get('/:id', asyncHandler(async (req, res) => {
  try {
    const menu = await Menu.findById(req.params.id).populate('submenus');
    
    if (!menu) {
      return res.status(404).json({
        success: false,
        message: 'Menu not found'
      });
    }
    
    res.json({
      success: true,
      data: menu
    });
  } catch (error) {
    console.error('Get menu error:', error);
    res.status(500).json({ 
      success: false,
      message: 'Failed to get menu' 
    });
  }
}));

// Create new menu
router.post('/', asyncHandler(async (req, res) => {
  try {
    const {
      menuId,
      label,
      to,
      icon,
      role,
      parentId,
      order,
      permissions,
      description,
      badge,
      metadata
    } = req.body;
    
    // Check if menuId already exists
    const existingMenu = await Menu.findOne({ menuId });
    if (existingMenu) {
      return res.status(400).json({
        success: false,
        message: 'Menu ID already exists'
      });
    }
    
    const menu = new Menu({
      menuId,
      label,
      to,
      icon: icon || 'Menu',
      role,
      parentId,
      order: order || 0,
      permissions: permissions || [],
      description,
      badge,
      metadata
    });
    
    await menu.save();
    
    res.status(201).json({
      success: true,
      data: menu,
      message: 'Menu created successfully'
    });
  } catch (error) {
    console.error('Create menu error:', error);
    res.status(500).json({ 
      success: false,
      message: 'Failed to create menu' 
    });
  }
}));

// Update menu
router.put('/:id', asyncHandler(async (req, res) => {
  try {
    const menu = await Menu.findByIdAndUpdate(
      req.params.id,
      { ...req.body, updatedAt: new Date() },
      { new: true, runValidators: true }
    );
    
    if (!menu) {
      return res.status(404).json({
        success: false,
        message: 'Menu not found'
      });
    }
    
    res.json({
      success: true,
      data: menu,
      message: 'Menu updated successfully'
    });
  } catch (error) {
    console.error('Update menu error:', error);
    res.status(500).json({ 
      success: false,
      message: 'Failed to update menu' 
    });
  }
}));

// Delete menu
router.delete('/:id', asyncHandler(async (req, res) => {
  try {
    const menu = await Menu.findByIdAndDelete(req.params.id);
    
    if (!menu) {
      return res.status(404).json({
        success: false,
        message: 'Menu not found'
      });
    }
    
    // Also delete any submenus
    await Menu.deleteMany({ parentId: menu.menuId });
    
    res.json({
      success: true,
      message: 'Menu and submenus deleted successfully'
    });
  } catch (error) {
    console.error('Delete menu error:', error);
    res.status(500).json({ 
      success: false,
      message: 'Failed to delete menu' 
    });
  }
}));

// Bulk create menus (for migration)
router.post('/bulk', asyncHandler(async (req, res) => {
  try {
    const { menus } = req.body;
    
    if (!Array.isArray(menus)) {
      return res.status(400).json({
        success: false,
        message: 'Menus must be an array'
      });
    }
    
    const createdMenus = await Menu.insertMany(menus);
    
    res.status(201).json({
      success: true,
      data: createdMenus,
      message: `${createdMenus.length} menus created successfully`
    });
  } catch (error) {
    console.error('Bulk create menus error:', error);
    res.status(500).json({ 
      success: false,
      message: 'Failed to create menus' 
    });
  }
}));

// Reorder menus
router.put('/reorder', asyncHandler(async (req, res) => {
  try {
    const { menuOrders } = req.body; // Array of { id, order }
    
    const updatePromises = menuOrders.map(({ id, order }) =>
      Menu.findByIdAndUpdate(id, { order })
    );
    
    await Promise.all(updatePromises);
    
    res.json({
      success: true,
      message: 'Menu order updated successfully'
    });
  } catch (error) {
    console.error('Reorder menus error:', error);
    res.status(500).json({ 
      success: false,
      message: 'Failed to reorder menus' 
    });
  }
}));

module.exports = router;
