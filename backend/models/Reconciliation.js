const mongoose = require('mongoose');

const reconciliationSchema = new mongoose.Schema({
  reconciliationId: {
    type: String,
    required: true,
    unique: true
  },
  merchantId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  traderId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  transactionIds: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Transaction'
  }],
  reconciliationType: {
    type: String,
    enum: ['daily', 'weekly', 'monthly', 'manual'],
    required: true
  },
  periodStart: {
    type: Date,
    required: true
  },
  periodEnd: {
    type: Date,
    required: true
  },
  summary: {
    totalTransactions: { type: Number, default: 0 },
    totalAmount: { type: Number, default: 0 },
    successfulTransactions: { type: Number, default: 0 },
    failedTransactions: { type: Number, default: 0 },
    pendingTransactions: { type: Number, default: 0 },
    refundedTransactions: { type: Number, default: 0 },
    netAmount: { type: Number, default: 0 },
    fees: { type: Number, default: 0 },
    settlementAmount: { type: Number, default: 0 }
  },
  discrepancies: [{
    transactionId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Transaction'
    },
    discrepancyType: {
      type: String,
      enum: ['amount_mismatch', 'status_mismatch', 'missing_transaction', 'duplicate_transaction']
    },
    expectedValue: mongoose.Schema.Types.Mixed,
    actualValue: mongoose.Schema.Types.Mixed,
    description: String,
    resolved: { type: Boolean, default: false },
    resolvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    resolvedAt: Date,
    resolutionNotes: String
  }],
  status: {
    type: String,
    enum: ['pending', 'in_progress', 'completed', 'failed', 'requires_review'],
    default: 'pending'
  },
  processedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  processedAt: Date,
  auditTrail: [{
    action: String,
    performedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    timestamp: { type: Date, default: Date.now },
    details: mongoose.Schema.Types.Mixed
  }],
  notes: String
}, {
  timestamps: true
});

// Indexes for efficient queries
reconciliationSchema.index({ merchantId: 1, periodStart: 1, periodEnd: 1 });
reconciliationSchema.index({ status: 1, createdAt: 1 });
reconciliationSchema.index({ reconciliationType: 1, periodStart: 1 });

module.exports = mongoose.model('Reconciliation', reconciliationSchema);