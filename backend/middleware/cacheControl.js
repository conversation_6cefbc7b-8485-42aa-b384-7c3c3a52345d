const logger = require('../utils/logger');

// Default cache control settings
const defaultCacheConfig = {
  // Default cache control for public resources
  public: {
    maxAge: 3600, // 1 hour in seconds
    mustRevalidate: true,
    noCache: false,
    noStore: false,
    private: false,
    proxyRevalidate: false,
    sMaxAge: null,
    staleIfError: 0,
    staleWhileRevalidate: 0,
  },
  
  // Cache control for private/user-specific resources
  private: {
    maxAge: 600, // 10 minutes in seconds
    mustRevalidate: true,
    noCache: true,
    noStore: false,
    private: true,
    proxyRevalidate: false,
    sMaxAge: null,
    staleIfError: 0,
    staleWhileRevalidate: 0,
  },
  
  // No cache configuration
  noCache: {
    noCache: true,
    noStore: true,
    mustRevalidate: true,
    private: true,
  },
};

/**
 * Generate Cache-Control header value from options
 * @param {Object} options - Cache control options
 * @returns {string} Cache-Control header value
 */
const generateCacheControl = (options = {}) => {
  const {
    maxAge,
    sMaxAge,
    noCache,
    noStore,
    mustRevalidate,
    proxyRevalidate,
    immutable,
    staleWhileRevalidate,
    staleIfError,
    private: isPrivate,
    public: isPublic,
  } = { ...defaultCacheConfig.public, ...options };
  
  const directives = [];
  
  // Handle no-store (takes precedence over other directives)
  if (noStore) {
    directives.push('no-store');
    return directives.join(', ');
  }
  
  // Handle public/private
  if (isPrivate) {
    directives.push('private');
  } else if (isPublic) {
    directives.push('public');
  }
  
  // Handle max-age and s-maxage
  if (typeof maxAge === 'number') {
    directives.push(`max-age=${maxAge}`);
  }
  
  if (typeof sMaxAge === 'number') {
    directives.push(`s-maxage=${sMaxAge}`);
  }
  
  // Handle other boolean directives
  if (noCache) {
    directives.push('no-cache');
  }
  
  if (mustRevalidate) {
    directives.push('must-revalidate');
  }
  
  if (proxyRevalidate) {
    directives.push('proxy-revalidate');
  }
  
  if (immutable) {
    directives.push('immutable');
  }
  
  if (typeof staleWhileRevalidate === 'number' && staleWhileRevalidate > 0) {
    directives.push(`stale-while-revalidate=${staleWhileRevalidate}`);
  }
  
  if (typeof staleIfError === 'number' && staleIfError > 0) {
    directives.push(`stale-if-error=${staleIfError}`);
  }
  
  return directives.join(', ');
};

/**
 * Middleware to set cache control headers
 * @param {Object} options - Cache control options
 * @returns {Function} Express middleware function
 */
const cacheControl = (options = {}) => {
  return (req, res, next) => {
    // Skip cache control for non-GET/HEAD requests
    if (!['GET', 'HEAD'].includes(req.method)) {
      return next();
    }
    
    // Generate Cache-Control header
    const cacheControlValue = generateCacheControl(options);
    
    // Set Cache-Control header
    res.set('Cache-Control', cacheControlValue);
    
    // Log cache control header for debugging
    logger.debug(`Cache-Control: ${cacheControlValue}`, {
      path: req.path,
      method: req.method,
      cacheControl: cacheControlValue,
    });
    
    next();
  };
};

/**
 * Middleware to set no-cache headers
 * @returns {Function} Express middleware function
 */
const noCache = () => {
  return cacheControl(defaultCacheConfig.noCache);
};

/**
 * Middleware to set private cache headers
 * @param {number} maxAge - Maximum age in seconds (default: 10 minutes)
 * @returns {Function} Express middleware function
 */
const privateCache = (maxAge = 600) => {
  return cacheControl({ ...defaultCacheConfig.private, maxAge });
};

/**
 * Middleware to set public cache headers
 * @param {number} maxAge - Maximum age in seconds (default: 1 hour)
 * @returns {Function} Express middleware function
 */
const publicCache = (maxAge = 3600) => {
  return cacheControl({ ...defaultCacheConfig.public, maxAge });
};

module.exports = {
  cacheControl,
  noCache,
  privateCache,
  publicCache,
  generateCacheControl,
  defaultConfig: defaultCacheConfig,
};
