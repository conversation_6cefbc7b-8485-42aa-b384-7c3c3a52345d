const express = require("express")
const Transaction = require("../models/Transaction")
// Authentication middleware removed

const router = express.Router()

// Get transactions
router.get("/", async (req, res) => {
  try {
    const { page = 1, limit = 20, status, startDate, endDate } = req.query

    const query = {}

    // No role-based filtering - show all transactions

    // Apply filters
    if (status) {
      query.status = status
    }

    if (startDate || endDate) {
      query.createdAt = {}
      if (startDate) query.createdAt.$gte = new Date(startDate)
      if (endDate) query.createdAt.$lte = new Date(endDate)
    }

    const transactions = await Transaction.find(query)
      .populate("merchantId", "businessName email")
      .populate("traderId", "name email")
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit)

    const total = await Transaction.countDocuments(query)

    res.json({
      transactions,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total,
    })
  } catch (error) {
    console.error("Get transactions error:", error)
    res.status(500).json({ message: "Failed to get transactions" })
  }
})

// Get transaction by ID
router.get("/:transactionId", async (req, res) => {
  try {
    const { transactionId } = req.params

    const transaction = await Transaction.findOne({ transactionId })
      .populate("merchantId", "businessName email")
      .populate("traderId", "name email")

    if (!transaction) {
      return res.status(404).json({ message: "Transaction not found" })
    }

    // No permission checks - all transactions accessible

    res.json({ transaction })
  } catch (error) {
    console.error("Get transaction error:", error)
    res.status(500).json({ message: "Failed to get transaction" })
  }
})

// Update transaction status
router.put("/:transactionId/status", async (req, res) => {
  try {

    const { transactionId } = req.params
    const { status, notes } = req.body

    const transaction = await Transaction.findOne({ transactionId })
    if (!transaction) {
      return res.status(404).json({ message: "Transaction not found" })
    }

    transaction.status = status
    if (notes) {
      transaction.notes.push(notes)
    }

    await transaction.save()

    res.json({ message: "Transaction status updated", transaction })
  } catch (error) {
    console.error("Update transaction error:", error)
    res.status(500).json({ message: "Failed to update transaction" })
  }
})

// Get transaction analytics
router.get("/analytics/summary", async (req, res) => {
  try {
    const { period = "30d" } = req.query

    let dateFilter = {}
    const now = new Date()

    switch (period) {
      case "7d":
        dateFilter = { $gte: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) }
        break
      case "30d":
        dateFilter = { $gte: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000) }
        break
      case "90d":
        dateFilter = { $gte: new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000) }
        break
    }

    const matchQuery = { createdAt: dateFilter }

    // Filter by user role
    if (req.user.role === "merchant") {
      matchQuery.merchantId = req.user.userId
    } else if (req.user.role === "trader") {
      matchQuery.traderId = req.user.userId
    }

    const analytics = await Transaction.aggregate([
      { $match: matchQuery },
      {
        $group: {
          _id: null,
          totalTransactions: { $sum: 1 },
          totalAmount: { $sum: "$amount" },
          totalFees: { $sum: { $add: ["$fees.processingFee", "$fees.platformFee"] } },
          successfulTransactions: {
            $sum: { $cond: [{ $eq: ["$status", "completed"] }, 1, 0] },
          },
          failedTransactions: {
            $sum: { $cond: [{ $eq: ["$status", "failed"] }, 1, 0] },
          },
          averageAmount: { $avg: "$amount" },
          averageProcessingTime: { $avg: "$processingTime" },
        },
      },
    ])

    const result = analytics[0] || {
      totalTransactions: 0,
      totalAmount: 0,
      totalFees: 0,
      successfulTransactions: 0,
      failedTransactions: 0,
      averageAmount: 0,
      averageProcessingTime: 0,
    }

    result.successRate =
      result.totalTransactions > 0 ? ((result.successfulTransactions / result.totalTransactions) * 100).toFixed(2) : 0

    res.json({ analytics: result })
  } catch (error) {
    console.error("Analytics error:", error)
    res.status(500).json({ message: "Failed to get analytics" })
  }
})

module.exports = router
