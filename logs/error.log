2025-07-16 14:24:49 [ERROR]: Uncaught Exception: app is not defined
{
  "service": "payment-gateway",
  "stack": "ReferenceError: app is not defined\n    at Object.<anonymous> (/Users/<USER>/Documents/payment-main/payment-gateway (1)/backend/app.js:173:1)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (/Users/<USER>/Documents/payment-main/payment-gateway (1)/backend/server.js:1:34)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)"
}
2025-07-16 14:24:49 [ERROR]: Uncaught Exception: app is not defined
{
  "service": "payment-gateway",
  "stack": "ReferenceError: app is not defined\n    at Object.<anonymous> (/Users/<USER>/Documents/payment-main/payment-gateway (1)/backend/app.js:173:1)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (/Users/<USER>/Documents/payment-main/payment-gateway (1)/backend/server.js:1:34)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)"
}
2025-07-16 14:24:50 [ERROR]: Uncaught Exception: app is not defined
{
  "service": "payment-gateway",
  "stack": "ReferenceError: app is not defined\n    at Object.<anonymous> (/Users/<USER>/Documents/payment-main/payment-gateway (1)/backend/app.js:173:1)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (/Users/<USER>/Documents/payment-main/payment-gateway (1)/backend/server.js:1:34)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)"
}
2025-07-16 14:24:49 [ERROR]: Uncaught Exception: app is not defined
{
  "service": "payment-gateway",
  "stack": "ReferenceError: app is not defined\n    at Object.<anonymous> (/Users/<USER>/Documents/payment-main/payment-gateway (1)/backend/app.js:173:1)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (/Users/<USER>/Documents/payment-main/payment-gateway (1)/backend/server.js:1:34)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)"
}
2025-07-16 14:25:12 [ERROR]: Uncaught Exception: app is not defined
{
  "service": "payment-gateway",
  "stack": "ReferenceError: app is not defined\n    at Object.<anonymous> (/Users/<USER>/Documents/payment-main/payment-gateway (1)/backend/app.js:171:1)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (/Users/<USER>/Documents/payment-main/payment-gateway (1)/backend/server.js:1:34)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)"
}
2025-07-16 14:25:12 [ERROR]: Uncaught Exception: app is not defined
{
  "service": "payment-gateway",
  "stack": "ReferenceError: app is not defined\n    at Object.<anonymous> (/Users/<USER>/Documents/payment-main/payment-gateway (1)/backend/app.js:171:1)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (/Users/<USER>/Documents/payment-main/payment-gateway (1)/backend/server.js:1:34)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)"
}
2025-07-16 14:25:12 [ERROR]: Uncaught Exception: app is not defined
{
  "service": "payment-gateway",
  "stack": "ReferenceError: app is not defined\n    at Object.<anonymous> (/Users/<USER>/Documents/payment-main/payment-gateway (1)/backend/app.js:171:1)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (/Users/<USER>/Documents/payment-main/payment-gateway (1)/backend/server.js:1:34)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)"
}
2025-07-16 14:25:12 [ERROR]: Uncaught Exception: app is not defined
{
  "service": "payment-gateway",
  "stack": "ReferenceError: app is not defined\n    at Object.<anonymous> (/Users/<USER>/Documents/payment-main/payment-gateway (1)/backend/app.js:171:1)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (/Users/<USER>/Documents/payment-main/payment-gateway (1)/backend/server.js:1:34)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)"
}
2025-07-16 14:25:54 [ERROR]: Uncaught Exception: app is not defined
{
  "service": "payment-gateway",
  "stack": "ReferenceError: app is not defined\n    at Object.<anonymous> (/Users/<USER>/Documents/payment-main/payment-gateway (1)/backend/app.js:171:1)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (/Users/<USER>/Documents/payment-main/payment-gateway (1)/backend/server.js:1:34)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)"
}
2025-07-16 14:26:13 [ERROR]: Uncaught Exception: app is not defined
{
  "service": "payment-gateway",
  "stack": "ReferenceError: app is not defined\n    at Object.<anonymous> (/Users/<USER>/Documents/payment-main/payment-gateway (1)/backend/app.js:171:3)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (/Users/<USER>/Documents/payment-main/payment-gateway (1)/backend/server.js:1:34)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)"
}
2025-07-16 14:26:13 [ERROR]: Uncaught Exception: app is not defined
{
  "service": "payment-gateway",
  "stack": "ReferenceError: app is not defined\n    at Object.<anonymous> (/Users/<USER>/Documents/payment-main/payment-gateway (1)/backend/app.js:171:3)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (/Users/<USER>/Documents/payment-main/payment-gateway (1)/backend/server.js:1:34)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)"
}
2025-07-16 14:26:13 [ERROR]: Uncaught Exception: app is not defined
{
  "service": "payment-gateway",
  "stack": "ReferenceError: app is not defined\n    at Object.<anonymous> (/Users/<USER>/Documents/payment-main/payment-gateway (1)/backend/app.js:171:3)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (/Users/<USER>/Documents/payment-main/payment-gateway (1)/backend/server.js:1:34)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)"
}
2025-07-16 14:26:13 [ERROR]: Uncaught Exception: app is not defined
{
  "service": "payment-gateway",
  "stack": "ReferenceError: app is not defined\n    at Object.<anonymous> (/Users/<USER>/Documents/payment-main/payment-gateway (1)/backend/app.js:171:3)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (/Users/<USER>/Documents/payment-main/payment-gateway (1)/backend/server.js:1:34)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)"
}
2025-07-16 14:26:32 [ERROR]: Uncaught Exception: app is not defined
{
  "service": "payment-gateway",
  "stack": "ReferenceError: app is not defined\n    at Object.<anonymous> (/Users/<USER>/Documents/payment-main/payment-gateway (1)/backend/app.js:171:3)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (/Users/<USER>/Documents/payment-main/payment-gateway (1)/backend/server.js:1:34)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)"
}
2025-07-16 14:26:32 [ERROR]: Uncaught Exception: app is not defined
{
  "service": "payment-gateway",
  "stack": "ReferenceError: app is not defined\n    at Object.<anonymous> (/Users/<USER>/Documents/payment-main/payment-gateway (1)/backend/app.js:171:3)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (/Users/<USER>/Documents/payment-main/payment-gateway (1)/backend/server.js:1:34)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)"
}
2025-07-16 14:26:33 [ERROR]: Uncaught Exception: app is not defined
{
  "service": "payment-gateway",
  "stack": "ReferenceError: app is not defined\n    at Object.<anonymous> (/Users/<USER>/Documents/payment-main/payment-gateway (1)/backend/app.js:171:3)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (/Users/<USER>/Documents/payment-main/payment-gateway (1)/backend/server.js:1:34)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)"
}
2025-07-16 14:26:33 [ERROR]: Uncaught Exception: app is not defined
{
  "service": "payment-gateway",
  "stack": "ReferenceError: app is not defined\n    at Object.<anonymous> (/Users/<USER>/Documents/payment-main/payment-gateway (1)/backend/app.js:171:3)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (/Users/<USER>/Documents/payment-main/payment-gateway (1)/backend/server.js:1:34)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)"
}
2025-07-16 14:30:40 [ERROR]: Uncaught Exception: listen EADDRINUSE: address already in use :::5000
{
  "service": "payment-gateway",
  "code": "EADDRINUSE",
  "errno": -48,
  "syscall": "listen",
  "address": "::",
  "port": 5000,
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at startServer (/Users/<USER>/Documents/payment-main/payment-gateway (1)/backend/server.js:16:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"
}
2025-07-16 14:30:40 [ERROR]: Uncaught Exception: listen EADDRINUSE: address already in use :::5000
{
  "service": "payment-gateway",
  "code": "EADDRINUSE",
  "errno": -48,
  "syscall": "listen",
  "address": "::",
  "port": 5000,
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at startServer (/Users/<USER>/Documents/payment-main/payment-gateway (1)/backend/server.js:16:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"
}
2025-07-16 14:30:40 [ERROR]: Uncaught Exception: listen EADDRINUSE: address already in use :::5000
{
  "service": "payment-gateway",
  "code": "EADDRINUSE",
  "errno": -48,
  "syscall": "listen",
  "address": "::",
  "port": 5000,
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at startServer (/Users/<USER>/Documents/payment-main/payment-gateway (1)/backend/server.js:16:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"
}
2025-07-16 14:30:43 [ERROR]: Uncaught Exception: listen EADDRINUSE: address already in use :::5000
{
  "service": "payment-gateway",
  "code": "EADDRINUSE",
  "errno": -48,
  "syscall": "listen",
  "address": "::",
  "port": 5000,
  "stack": "Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at startServer (/Users/<USER>/Documents/payment-main/payment-gateway (1)/backend/server.js:16:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"
}
{"code":"EDNS","command":"CONN","errno":-3008,"hostname":"smtp.example.com","level":"error","message":"Error with mail configuration: getaddrinfo ENOTFOUND smtp.example.com","service":"payment-gateway","stack":"Error: getaddrinfo ENOTFOUND smtp.example.com\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","syscall":"getaddrinfo","timestamp":"2025-07-20 07:56:03.409"}
{"code":"EDNS","command":"CONN","errno":-3008,"hostname":"smtp.example.com","level":"error","message":"Error with mail configuration: getaddrinfo ENOTFOUND smtp.example.com","service":"payment-gateway","stack":"Error: getaddrinfo ENOTFOUND smtp.example.com\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","syscall":"getaddrinfo","timestamp":"2025-07-20 08:30:00.060"}
{"code":"EDNS","command":"CONN","errno":-3008,"hostname":"smtp.example.com","level":"error","message":"Error with mail configuration: getaddrinfo ENOTFOUND smtp.example.com","service":"payment-gateway","stack":"Error: getaddrinfo ENOTFOUND smtp.example.com\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:120:26)","syscall":"getaddrinfo","timestamp":"2025-07-20 08:35:55.064"}
{"level":"error","message":"Failed to start server: setupSwagger is not a function","service":"payment-gateway","timestamp":"2025-07-25 12:30:40.270"}
{"level":"error","message":"Failed to start server: setupSwagger is not a function","service":"payment-gateway","timestamp":"2025-07-25 12:48:11.844"}
{"level":"error","message":"Failed to start server: setupSwagger is not a function","service":"payment-gateway","timestamp":"2025-07-25 12:56:21.902"}
{"address":"::","code":"EADDRINUSE","errno":-48,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::3001","port":3001,"service":"payment-gateway","stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at startServer (/Users/<USER>/Documents/payment-main/backend/server.js:19:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-25 12:58:00.259"}
{"address":"::","code":"EADDRINUSE","errno":-48,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::3001","port":3001,"service":"payment-gateway","stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at startServer (/Users/<USER>/Documents/payment-main/backend/server.js:19:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-25 12:58:00.259"}
{"address":"::","code":"EADDRINUSE","errno":-48,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::3001","port":3001,"service":"payment-gateway","stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at startServer (/Users/<USER>/Documents/payment-main/backend/server.js:19:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-25 13:03:45.526"}
{"address":"::","code":"EADDRINUSE","errno":-48,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::3001","port":3001,"service":"payment-gateway","stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at startServer (/Users/<USER>/Documents/payment-main/backend/server.js:19:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-25 13:04:22.008"}
{"ip":"::1","level":"error","message":"ApiError is not defined","method":"GET","path":"/api/health","service":"payment-gateway","stack":"ReferenceError: ApiError is not defined\n    at /Users/<USER>/Documents/payment-main/backend/app.js:177:14\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:149:13)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:145:7)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:145:7)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:145:7)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:145:7)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:145:7)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:145:7)\n    at Route.dispatch (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:119:3)","statusCode":500,"timestamp":"2025-07-25 13:05:08.347","user":"anonymous"}
{"address":"::","code":"EADDRINUSE","errno":-48,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::3001","port":3001,"service":"payment-gateway","stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at startServer (/Users/<USER>/Documents/payment-main/backend/server.js:19:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-25 13:12:45.860"}
{"address":"::","code":"EADDRINUSE","errno":-48,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::3001","port":3001,"service":"payment-gateway","stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at startServer (/Users/<USER>/Documents/payment-main/backend/server.js:19:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-25 13:12:47.838"}
{"address":"::","code":"EADDRINUSE","errno":-48,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::3001","port":3001,"service":"payment-gateway","stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at startServer (/Users/<USER>/Documents/payment-main/backend/server.js:19:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-25 15:39:33.867"}
{"address":"::","code":"EADDRINUSE","errno":-48,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::3001","port":3001,"service":"payment-gateway","stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at startServer (/Users/<USER>/Documents/payment-main/backend/server.js:19:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-25 15:39:33.938"}
{"address":"::","code":"EADDRINUSE","errno":-48,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::3001","port":3001,"service":"payment-gateway","stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at startServer (/Users/<USER>/Documents/payment-main/backend/server.js:19:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-25 15:39:33.962"}
{"address":"::","code":"EADDRINUSE","errno":-48,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::3001","port":3001,"service":"payment-gateway","stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at startServer (/Users/<USER>/Documents/payment-main/backend/server.js:19:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-25 15:39:46.172"}
{"address":"::","code":"EADDRINUSE","errno":-48,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::3001","port":3001,"service":"payment-gateway","stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at startServer (/Users/<USER>/Documents/payment-main/backend/server.js:19:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-25 15:39:46.217"}
{"address":"::","code":"EADDRINUSE","errno":-48,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::3001","port":3001,"service":"payment-gateway","stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at startServer (/Users/<USER>/Documents/payment-main/backend/server.js:19:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-25 15:39:46.217"}
{"address":"::","code":"EADDRINUSE","errno":-48,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::3001","port":3001,"service":"payment-gateway","stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at startServer (/Users/<USER>/Documents/payment-main/backend/server.js:19:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-25 15:45:17.726"}
{"address":"::","code":"EADDRINUSE","errno":-48,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::3001","port":3001,"service":"payment-gateway","stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at startServer (/Users/<USER>/Documents/payment-main/backend/server.js:19:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-25 15:45:18.089"}
{"address":"::","code":"EADDRINUSE","errno":-48,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::3001","port":3001,"service":"payment-gateway","stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at startServer (/Users/<USER>/Documents/payment-main/backend/server.js:19:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-25 15:45:18.119"}
{"address":"::","code":"EADDRINUSE","errno":-48,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::3001","port":3001,"service":"payment-gateway","stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at startServer (/Users/<USER>/Documents/payment-main/backend/server.js:19:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-25 15:55:33.498"}
{"address":"::","code":"EADDRINUSE","errno":-48,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::3001","port":3001,"service":"payment-gateway","stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at startServer (/Users/<USER>/Documents/payment-main/backend/server.js:19:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-25 15:55:33.610"}
{"address":"::","code":"EADDRINUSE","errno":-48,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::3001","port":3001,"service":"payment-gateway","stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at startServer (/Users/<USER>/Documents/payment-main/backend/server.js:19:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-25 15:55:33.709"}
{"address":"::","code":"EADDRINUSE","errno":-48,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::3001","port":3001,"service":"payment-gateway","stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at startServer (/Users/<USER>/Documents/payment-main/backend/server.js:19:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-25 16:41:41.139"}
{"address":"::","code":"EADDRINUSE","errno":-48,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::3001","port":3001,"service":"payment-gateway","stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at startServer (/Users/<USER>/Documents/payment-main/backend/server.js:19:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-25 16:41:41.182"}
{"address":"::","code":"EADDRINUSE","errno":-48,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::3001","port":3001,"service":"payment-gateway","stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at startServer (/Users/<USER>/Documents/payment-main/backend/server.js:19:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-25 16:41:41.535"}
{"address":"::","code":"EADDRINUSE","errno":-48,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::3001","port":3001,"service":"payment-gateway","stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at startServer (/Users/<USER>/Documents/payment-main/backend/server.js:19:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-25 19:18:31.284"}
{"address":"::","code":"EADDRINUSE","errno":-48,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::3001","port":3001,"service":"payment-gateway","stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at startServer (/Users/<USER>/Documents/payment-main/backend/server.js:19:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-25 19:28:31.437"}
{"address":"::","code":"EADDRINUSE","errno":-48,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::3001","port":3001,"service":"payment-gateway","stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1908:16)\n    at listenInCluster (node:net:1965:12)\n    at Server.listen (node:net:2067:7)\n    at startServer (/Users/<USER>/Documents/payment-main/backend/server.js:19:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-25 19:28:31.472"}
{"level":"error","message":"Port 3001 is already in use","service":"payment-gateway","timestamp":"2025-07-25 20:35:23.477"}
{"level":"error","message":"Port 3001 is already in use","service":"payment-gateway","timestamp":"2025-07-25 20:39:42.644"}
{"level":"error","message":"Port 3001 is already in use","service":"payment-gateway","timestamp":"2025-07-25 20:39:42.654"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongodb","service":"payment-gateway","timestamp":"2025-07-25 20:52:37.578"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongodb","service":"payment-gateway","timestamp":"2025-07-25 20:52:37.593"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongodb","service":"payment-gateway","timestamp":"2025-07-25 20:54:20.411"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongodb","service":"payment-gateway","timestamp":"2025-07-26 00:26:41.408"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongodb","service":"payment-gateway","timestamp":"2025-07-26 00:26:41.463"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongodb","service":"payment-gateway","timestamp":"2025-07-26 00:29:39.229"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongodb","service":"payment-gateway","timestamp":"2025-07-26 00:29:39.348"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 01:49:17.263"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 01:52:53.911"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 01:52:53.917"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 01:52:53.927"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 01:55:44.435"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 01:55:44.483"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 01:55:44.498"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 07:51:58.094"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 07:51:58.095"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 07:51:58.094"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:04:04.713"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:04:04.723"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:04:04.756"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:06:17.629"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:06:17.629"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:06:17.629"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:07:02.116"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:07:02.120"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:07:02.129"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:08:03.483"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:08:03.487"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:08:03.523"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:08:51.697"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:08:51.723"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:08:51.723"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:11:58.744"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:11:58.744"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:11:58.743"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:13:15.910"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:13:15.911"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:13:15.911"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:14:34.646"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:14:34.646"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:14:34.649"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:16:20.048"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:16:20.049"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:16:20.049"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:17:50.330"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:17:50.358"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:17:50.372"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:19:05.330"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:19:05.426"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:19:05.437"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:22:13.693"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:22:13.707"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:22:13.727"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:23:28.146"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:23:28.146"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:23:28.168"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:25:45.691"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:25:45.709"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:25:45.715"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:26:43.298"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:26:43.301"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:26:43.355"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:29:18.796"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:29:18.808"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:29:18.812"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:30:24.997"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:30:25.037"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:30:25.061"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:31:27.522"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:31:27.534"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:31:27.535"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:32:40.370"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:32:40.378"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:32:40.386"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:35:12.880"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:35:12.880"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:35:12.880"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:37:16.325"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:37:16.329"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:37:16.329"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:40:26.244"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:40:26.300"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:40:26.300"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:41:55.349"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:41:55.373"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:41:55.374"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:47:25.620"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:47:25.635"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:47:25.643"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:50:25.591"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:50:25.625"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:50:25.646"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:54:34.268"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:54:34.268"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:54:34.269"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:57:18.936"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:57:18.959"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 08:57:18.966"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 09:14:29.615"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 09:14:29.644"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 09:14:29.699"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 09:18:58.087"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 09:18:58.093"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 09:18:58.127"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 09:24:12.446"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 09:24:12.446"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 09:24:12.483"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 09:27:42.342"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 09:27:42.349"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 09:27:42.406"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 09:30:29.497"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 09:30:29.510"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 09:30:29.517"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 09:34:00.319"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 09:34:00.338"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 09:34:00.341"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 09:36:20.050"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 09:36:20.057"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 09:36:20.059"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 09:38:38.659"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 09:38:38.663"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 09:38:38.751"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 09:43:22.337"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 09:43:22.354"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 09:43:22.357"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 09:47:24.601"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 09:47:24.621"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 09:47:24.622"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 09:48:11.235"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 09:48:11.271"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 09:48:11.296"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 09:57:19.033"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 09:57:19.073"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 09:57:19.089"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 10:05:30.054"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 10:05:30.075"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 10:05:30.077"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 10:06:48.885"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 10:06:48.886"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 10:06:48.899"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 10:17:18.336"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 10:17:18.342"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 10:17:18.349"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 10:20:52.631"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 10:20:52.640"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 10:20:52.694"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 10:23:16.908"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 10:23:16.919"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 10:23:16.931"}
{"level":"error","message":"Missing required environment variables: JWT_REFRESH_SECRET, COOKIE_SECRET, API_VERSION","service":"payment-gateway","timestamp":"2025-07-26 10:50:35.619"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Invalid status code: Too many login attempts, please try again later","method":"POST","path":"/api/v1/auth/login","service":"payment-gateway","stack":"RangeError: Invalid status code: Too many login attempts, please try again later\n    at ServerResponse.writeHead (node:_http_server:351:11)\n    at ServerResponse._implicitHeader (node:_http_server:337:8)\n    at write_ (node:_http_outgoing:945:9)\n    at ServerResponse.end (node:_http_outgoing:1056:5)\n    at ServerResponse.send (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:232:10)\n    at ServerResponse.json (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:278:15)\n    at /Users/<USER>/Documents/payment-main/backend/routes/v1/index.js:56:26\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:71:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:265:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:141:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:147:13)\n    at /Users/<USER>/Documents/payment-main/backend/middleware/rateLimit.js:69:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":500,"timestamp":"2025-07-26 10:50:40.989","user":"anonymous"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Invalid status code: Too many login attempts, please try again later","method":"POST","path":"/api/v1/auth/login","service":"payment-gateway","stack":"RangeError: Invalid status code: Too many login attempts, please try again later\n    at ServerResponse.writeHead (node:_http_server:351:11)\n    at ServerResponse._implicitHeader (node:_http_server:337:8)\n    at write_ (node:_http_outgoing:945:9)\n    at ServerResponse.end (node:_http_outgoing:1056:5)\n    at ServerResponse.send (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:232:10)\n    at ServerResponse.json (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:278:15)\n    at /Users/<USER>/Documents/payment-main/backend/routes/v1/index.js:56:26\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:71:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:265:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:141:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:147:13)\n    at /Users/<USER>/Documents/payment-main/backend/middleware/rateLimit.js:69:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":500,"timestamp":"2025-07-26 10:50:41.024","user":"anonymous"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Invalid status code: Too many login attempts, please try again later","method":"POST","path":"/api/v1/auth/login","service":"payment-gateway","stack":"RangeError: Invalid status code: Too many login attempts, please try again later\n    at ServerResponse.writeHead (node:_http_server:351:11)\n    at ServerResponse._implicitHeader (node:_http_server:337:8)\n    at write_ (node:_http_outgoing:945:9)\n    at ServerResponse.end (node:_http_outgoing:1056:5)\n    at ServerResponse.send (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:232:10)\n    at ServerResponse.json (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:278:15)\n    at /Users/<USER>/Documents/payment-main/backend/routes/v1/index.js:56:26\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:71:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:265:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:141:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:147:13)\n    at /Users/<USER>/Documents/payment-main/backend/middleware/rateLimit.js:69:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":500,"timestamp":"2025-07-26 10:50:41.055","user":"anonymous"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Invalid status code: Too many login attempts, please try again later","method":"POST","path":"/api/v1/auth/login","service":"payment-gateway","stack":"RangeError: Invalid status code: Too many login attempts, please try again later\n    at ServerResponse.writeHead (node:_http_server:351:11)\n    at ServerResponse._implicitHeader (node:_http_server:337:8)\n    at write_ (node:_http_outgoing:945:9)\n    at ServerResponse.end (node:_http_outgoing:1056:5)\n    at ServerResponse.send (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:232:10)\n    at ServerResponse.json (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:278:15)\n    at /Users/<USER>/Documents/payment-main/backend/routes/v1/index.js:56:26\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:71:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:265:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:141:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:147:13)\n    at /Users/<USER>/Documents/payment-main/backend/middleware/rateLimit.js:69:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":500,"timestamp":"2025-07-26 10:50:41.082","user":"anonymous"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Invalid status code: Too many login attempts, please try again later","method":"POST","path":"/api/v1/auth/login","service":"payment-gateway","stack":"RangeError: Invalid status code: Too many login attempts, please try again later\n    at ServerResponse.writeHead (node:_http_server:351:11)\n    at ServerResponse._implicitHeader (node:_http_server:337:8)\n    at write_ (node:_http_outgoing:945:9)\n    at ServerResponse.end (node:_http_outgoing:1056:5)\n    at ServerResponse.send (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:232:10)\n    at ServerResponse.json (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:278:15)\n    at /Users/<USER>/Documents/payment-main/backend/routes/v1/index.js:56:26\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:71:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:265:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:141:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:147:13)\n    at /Users/<USER>/Documents/payment-main/backend/middleware/rateLimit.js:69:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":500,"timestamp":"2025-07-26 10:50:41.114","user":"anonymous"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Invalid status code: Too many login attempts, please try again later","method":"POST","path":"/api/v1/auth/login","service":"payment-gateway","stack":"RangeError: Invalid status code: Too many login attempts, please try again later\n    at ServerResponse.writeHead (node:_http_server:351:11)\n    at ServerResponse._implicitHeader (node:_http_server:337:8)\n    at write_ (node:_http_outgoing:945:9)\n    at ServerResponse.end (node:_http_outgoing:1056:5)\n    at ServerResponse.send (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:232:10)\n    at ServerResponse.json (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:278:15)\n    at /Users/<USER>/Documents/payment-main/backend/routes/v1/index.js:56:26\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:71:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:265:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:141:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:147:13)\n    at /Users/<USER>/Documents/payment-main/backend/middleware/rateLimit.js:69:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":500,"timestamp":"2025-07-26 10:50:41.144","user":"anonymous"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Invalid status code: Too many login attempts, please try again later","method":"POST","path":"/api/v1/auth/login","service":"payment-gateway","stack":"RangeError: Invalid status code: Too many login attempts, please try again later\n    at ServerResponse.writeHead (node:_http_server:351:11)\n    at ServerResponse._implicitHeader (node:_http_server:337:8)\n    at write_ (node:_http_outgoing:945:9)\n    at ServerResponse.end (node:_http_outgoing:1056:5)\n    at ServerResponse.send (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:232:10)\n    at ServerResponse.json (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:278:15)\n    at /Users/<USER>/Documents/payment-main/backend/routes/v1/index.js:56:26\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:71:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:265:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:141:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:147:13)\n    at /Users/<USER>/Documents/payment-main/backend/middleware/rateLimit.js:69:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":500,"timestamp":"2025-07-26 10:50:41.171","user":"anonymous"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Invalid status code: Too many login attempts, please try again later","method":"POST","path":"/api/v1/auth/login","service":"payment-gateway","stack":"RangeError: Invalid status code: Too many login attempts, please try again later\n    at ServerResponse.writeHead (node:_http_server:351:11)\n    at ServerResponse._implicitHeader (node:_http_server:337:8)\n    at write_ (node:_http_outgoing:945:9)\n    at ServerResponse.end (node:_http_outgoing:1056:5)\n    at ServerResponse.send (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:232:10)\n    at ServerResponse.json (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:278:15)\n    at /Users/<USER>/Documents/payment-main/backend/routes/v1/index.js:56:26\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:71:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:265:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:141:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:147:13)\n    at /Users/<USER>/Documents/payment-main/backend/middleware/rateLimit.js:69:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":500,"timestamp":"2025-07-26 10:50:41.197","user":"anonymous"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 10:56:17.327"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 10:56:17.339"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 10:56:17.386"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 10:59:02.110"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 10:59:02.123"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 10:59:02.129"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 11:13:31.037"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 11:13:31.038"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 11:13:31.041"}
{"level":"error","message":"Missing required environment variables: JWT_REFRESH_SECRET, COOKIE_SECRET, API_VERSION","service":"payment-gateway","timestamp":"2025-07-26 11:14:44.725"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Invalid status code: Too many login attempts, please try again later","method":"POST","path":"/api/v1/auth/login","service":"payment-gateway","stack":"RangeError: Invalid status code: Too many login attempts, please try again later\n    at ServerResponse.writeHead (node:_http_server:351:11)\n    at ServerResponse._implicitHeader (node:_http_server:337:8)\n    at write_ (node:_http_outgoing:945:9)\n    at ServerResponse.end (node:_http_outgoing:1056:5)\n    at ServerResponse.send (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:232:10)\n    at ServerResponse.json (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:278:15)\n    at /Users/<USER>/Documents/payment-main/backend/routes/v1/index.js:56:26\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:71:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:265:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:141:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:147:13)\n    at /Users/<USER>/Documents/payment-main/backend/middleware/rateLimit.js:69:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":500,"timestamp":"2025-07-26 11:14:49.236","user":"anonymous"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Invalid status code: Too many login attempts, please try again later","method":"POST","path":"/api/v1/auth/login","service":"payment-gateway","stack":"RangeError: Invalid status code: Too many login attempts, please try again later\n    at ServerResponse.writeHead (node:_http_server:351:11)\n    at ServerResponse._implicitHeader (node:_http_server:337:8)\n    at write_ (node:_http_outgoing:945:9)\n    at ServerResponse.end (node:_http_outgoing:1056:5)\n    at ServerResponse.send (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:232:10)\n    at ServerResponse.json (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:278:15)\n    at /Users/<USER>/Documents/payment-main/backend/routes/v1/index.js:56:26\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:71:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:265:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:141:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:147:13)\n    at /Users/<USER>/Documents/payment-main/backend/middleware/rateLimit.js:69:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":500,"timestamp":"2025-07-26 11:14:49.256","user":"anonymous"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Invalid status code: Too many login attempts, please try again later","method":"POST","path":"/api/v1/auth/login","service":"payment-gateway","stack":"RangeError: Invalid status code: Too many login attempts, please try again later\n    at ServerResponse.writeHead (node:_http_server:351:11)\n    at ServerResponse._implicitHeader (node:_http_server:337:8)\n    at write_ (node:_http_outgoing:945:9)\n    at ServerResponse.end (node:_http_outgoing:1056:5)\n    at ServerResponse.send (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:232:10)\n    at ServerResponse.json (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:278:15)\n    at /Users/<USER>/Documents/payment-main/backend/routes/v1/index.js:56:26\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:71:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:265:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:141:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:147:13)\n    at /Users/<USER>/Documents/payment-main/backend/middleware/rateLimit.js:69:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":500,"timestamp":"2025-07-26 11:14:49.276","user":"anonymous"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Invalid status code: Too many login attempts, please try again later","method":"POST","path":"/api/v1/auth/login","service":"payment-gateway","stack":"RangeError: Invalid status code: Too many login attempts, please try again later\n    at ServerResponse.writeHead (node:_http_server:351:11)\n    at ServerResponse._implicitHeader (node:_http_server:337:8)\n    at write_ (node:_http_outgoing:945:9)\n    at ServerResponse.end (node:_http_outgoing:1056:5)\n    at ServerResponse.send (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:232:10)\n    at ServerResponse.json (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:278:15)\n    at /Users/<USER>/Documents/payment-main/backend/routes/v1/index.js:56:26\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:71:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:265:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:141:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:147:13)\n    at /Users/<USER>/Documents/payment-main/backend/middleware/rateLimit.js:69:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":500,"timestamp":"2025-07-26 11:14:49.294","user":"anonymous"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Invalid status code: Too many login attempts, please try again later","method":"POST","path":"/api/v1/auth/login","service":"payment-gateway","stack":"RangeError: Invalid status code: Too many login attempts, please try again later\n    at ServerResponse.writeHead (node:_http_server:351:11)\n    at ServerResponse._implicitHeader (node:_http_server:337:8)\n    at write_ (node:_http_outgoing:945:9)\n    at ServerResponse.end (node:_http_outgoing:1056:5)\n    at ServerResponse.send (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:232:10)\n    at ServerResponse.json (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:278:15)\n    at /Users/<USER>/Documents/payment-main/backend/routes/v1/index.js:56:26\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:71:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:265:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:141:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:147:13)\n    at /Users/<USER>/Documents/payment-main/backend/middleware/rateLimit.js:69:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":500,"timestamp":"2025-07-26 11:14:49.311","user":"anonymous"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Invalid status code: Too many login attempts, please try again later","method":"POST","path":"/api/v1/auth/login","service":"payment-gateway","stack":"RangeError: Invalid status code: Too many login attempts, please try again later\n    at ServerResponse.writeHead (node:_http_server:351:11)\n    at ServerResponse._implicitHeader (node:_http_server:337:8)\n    at write_ (node:_http_outgoing:945:9)\n    at ServerResponse.end (node:_http_outgoing:1056:5)\n    at ServerResponse.send (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:232:10)\n    at ServerResponse.json (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:278:15)\n    at /Users/<USER>/Documents/payment-main/backend/routes/v1/index.js:56:26\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:71:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:265:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:141:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:147:13)\n    at /Users/<USER>/Documents/payment-main/backend/middleware/rateLimit.js:69:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":500,"timestamp":"2025-07-26 11:14:49.327","user":"anonymous"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Invalid status code: Too many login attempts, please try again later","method":"POST","path":"/api/v1/auth/login","service":"payment-gateway","stack":"RangeError: Invalid status code: Too many login attempts, please try again later\n    at ServerResponse.writeHead (node:_http_server:351:11)\n    at ServerResponse._implicitHeader (node:_http_server:337:8)\n    at write_ (node:_http_outgoing:945:9)\n    at ServerResponse.end (node:_http_outgoing:1056:5)\n    at ServerResponse.send (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:232:10)\n    at ServerResponse.json (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:278:15)\n    at /Users/<USER>/Documents/payment-main/backend/routes/v1/index.js:56:26\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:71:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:265:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:141:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:147:13)\n    at /Users/<USER>/Documents/payment-main/backend/middleware/rateLimit.js:69:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":500,"timestamp":"2025-07-26 11:14:49.343","user":"anonymous"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Invalid status code: Too many login attempts, please try again later","method":"POST","path":"/api/v1/auth/login","service":"payment-gateway","stack":"RangeError: Invalid status code: Too many login attempts, please try again later\n    at ServerResponse.writeHead (node:_http_server:351:11)\n    at ServerResponse._implicitHeader (node:_http_server:337:8)\n    at write_ (node:_http_outgoing:945:9)\n    at ServerResponse.end (node:_http_outgoing:1056:5)\n    at ServerResponse.send (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:232:10)\n    at ServerResponse.json (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:278:15)\n    at /Users/<USER>/Documents/payment-main/backend/routes/v1/index.js:56:26\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:71:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:265:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:141:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:147:13)\n    at /Users/<USER>/Documents/payment-main/backend/middleware/rateLimit.js:69:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":500,"timestamp":"2025-07-26 11:14:49.358","user":"anonymous"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 11:20:17.610"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 13:20:11.721"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 13:20:18.315"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 13:21:11.489"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 13:26:39.724"}
{"code":"EPIPE","errno":-32,"level":"error","message":"Uncaught Exception: write EPIPE","service":"payment-gateway","stack":"Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:161:15)\n    at writeGeneric (node:internal/stream_base_commons:152:3)\n    at Socket._writeGeneric (node:net:958:11)\n    at Socket._write (node:net:970:8)\n    at writeOrBuffer (node:internal/streams/writable:572:12)\n    at _write (node:internal/streams/writable:501:10)\n    at Writable.write (node:internal/streams/writable:510:10)\n    at Console.log (/Users/<USER>/Documents/payment-main/backend/node_modules/winston/lib/winston/transports/console.js:87:23)\n    at Console._write (/Users/<USER>/Documents/payment-main/backend/node_modules/winston-transport/modern.js:103:17)\n    at doWrite (/Users/<USER>/Documents/payment-main/backend/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/Users/<USER>/Documents/payment-main/backend/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/Users/<USER>/Documents/payment-main/backend/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/Users/<USER>/Documents/payment-main/backend/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:536:35)\n    at addChunk (/Users/<USER>/Documents/payment-main/backend/node_modules/readable-stream/lib/_stream_readable.js:279:12)\n    at readableAddChunk (/Users/<USER>/Documents/payment-main/backend/node_modules/readable-stream/lib/_stream_readable.js:262:11)\n    at Readable.push (/Users/<USER>/Documents/payment-main/backend/node_modules/readable-stream/lib/_stream_readable.js:228:10)\n    at Transform.push (/Users/<USER>/Documents/payment-main/backend/node_modules/readable-stream/lib/_stream_transform.js:132:32)\n    at DerivedLogger._transform (/Users/<USER>/Documents/payment-main/backend/node_modules/winston/lib/winston/logger.js:314:12)\n    at Transform._read (/Users/<USER>/Documents/payment-main/backend/node_modules/readable-stream/lib/_stream_transform.js:166:10)\n    at Transform._write (/Users/<USER>/Documents/payment-main/backend/node_modules/readable-stream/lib/_stream_transform.js:155:83)\n    at doWrite (/Users/<USER>/Documents/payment-main/backend/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/Users/<USER>/Documents/payment-main/backend/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/Users/<USER>/Documents/payment-main/backend/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.<computed> [as info] (/Users/<USER>/Documents/payment-main/backend/node_modules/winston/lib/winston/create-logger.js:81:14)\n    at gracefulShutdown (/Users/<USER>/Documents/payment-main/backend/config/db.js:158:10)\n    at process.<anonymous> (/Users/<USER>/Documents/payment-main/backend/config/db.js:171:29)\n    at process.emit (node:events:524:28)","syscall":"write","timestamp":"2025-07-26 17:54:15.682"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 18:22:59.612"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 18:31:25.763"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 18:32:37.175"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 18:34:21.907"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 18:35:03.009"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 18:38:34.795"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 18:40:54.643"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 18:41:29.647"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 18:41:29.647"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 18:42:26.620"}
{"level":"error","message":"MongoDB connection error: getaddrinfo ENOTFOUND mongo","service":"payment-gateway","timestamp":"2025-07-26 18:43:37.134"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-26 18:44:10.324"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-26 18:44:10.321"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-26 18:44:10.325"}
{"ip":"127.0.0.1","level":"error","message":"ApiError is not defined","method":"POST","path":"/api/auth/login","service":"payment-gateway","stack":"ReferenceError: ApiError is not defined\n    at /Users/<USER>/Documents/payment-main/backend/app.js:196:14\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:149:13)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:145:7)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:145:7)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:145:7)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:145:7)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:145:7)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:145:7)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:145:7)","statusCode":500,"timestamp":"2025-07-26 18:44:44.728","user":"anonymous"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-26 18:45:17.066"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-26 18:45:17.071"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-26 18:45:17.084"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-26 18:45:17.221"}
{"ip":"127.0.0.1","level":"error","message":"ApiError is not defined","method":"POST","path":"/api/auth/login","service":"payment-gateway","stack":"ReferenceError: ApiError is not defined\n    at /Users/<USER>/Documents/payment-main/backend/app.js:196:14\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:149:13)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:145:7)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:145:7)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:145:7)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:145:7)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:145:7)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:145:7)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:145:7)","statusCode":500,"timestamp":"2025-07-26 18:46:19.145","user":"anonymous"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-26 18:47:28.976"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-26 18:47:28.982"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-26 18:47:28.985"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-26 18:47:29.007"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-26 18:47:29.043"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-26 18:47:53.586"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-26 18:47:53.593"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-26 18:47:53.608"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-26 18:47:53.614"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-26 18:47:53.668"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-26 18:49:02.449"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-26 18:49:02.449"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-26 18:49:02.452"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-26 18:49:02.477"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-26 18:49:02.482"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-26 18:50:08.302"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-26 18:50:08.315"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-26 18:50:08.329"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-26 18:50:08.365"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-26 18:50:08.451"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-26 18:50:50.382"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-26 18:50:50.382"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-26 18:50:50.395"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-26 18:50:50.442"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-26 18:50:50.452"}
{"date":"Sat Jul 26 2025 20:40:08 GMT+0530 (India Standard Time)","error":{},"level":"error","message":"unhandledRejection: logger is not defined\nReferenceError: logger is not defined\n    at startServer (/Users/<USER>/Documents/payment-main/backend/server.js:160:5)\n    at Object.<anonymous> (/Users/<USER>/Documents/payment-main/backend/server.js:166:1)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:164:12)\n    at node:internal/main/run_main_module:28:49","os":{"loadavg":[4.53564453125,6.89208984375,11.640625],"uptime":9942},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","/Users/<USER>/Documents/payment-main/backend/server.js"],"cwd":"/Users/<USER>/Documents/payment-main/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","gid":20,"memoryUsage":{"arrayBuffers":18263319,"external":20503328,"heapTotal":67969024,"heapUsed":42638472,"rss":110821376},"pid":20086,"uid":501,"version":"v20.19.3"},"rejection":true,"service":"payment-gateway","stack":"ReferenceError: logger is not defined\n    at startServer (/Users/<USER>/Documents/payment-main/backend/server.js:160:5)\n    at Object.<anonymous> (/Users/<USER>/Documents/payment-main/backend/server.js:166:1)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:164:12)\n    at node:internal/main/run_main_module:28:49","timestamp":"2025-07-26 20:40:08.385","trace":[{"column":5,"file":"/Users/<USER>/Documents/payment-main/backend/server.js","function":"startServer","line":160,"method":null,"native":false},{"column":1,"file":"/Users/<USER>/Documents/payment-main/backend/server.js","function":null,"line":166,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":12,"file":"node:internal/modules/run_main","function":"Function.executeUserEntryPoint [as runMain]","line":164,"method":"executeUserEntryPoint [as runMain]","native":false},{"column":49,"file":"node:internal/main/run_main_module","function":null,"line":28,"method":null,"native":false}]}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","service":"payment-gateway","timestamp":"2025-07-26 20:40:08.387"}
{"ip":"127.0.0.1","level":"error","message":"401","method":"POST","path":"/api/auth/login","service":"payment-gateway","stack":"ApiError: 401\n    at exports.login (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:138:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":"Invalid credentials","timestamp":"2025-07-26 21:03:36.490","user":"anonymous"}
{"ip":"127.0.0.1","level":"error","message":"401","method":"POST","path":"/api/auth/login","service":"payment-gateway","stack":"ApiError: 401\n    at exports.login (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:138:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":"Invalid credentials","timestamp":"2025-07-26 21:19:08.241","user":"anonymous"}
{"ip":"127.0.0.1","level":"error","message":"User validation failed: password: Path `password` (`test123`) is shorter than the minimum allowed length (8).","method":"POST","path":"/api/auth/login","service":"payment-gateway","stack":"ValidationError: User validation failed: password: Path `password` (`test123`) is shorter than the minimum allowed length (8).\n    at Document.invalidate (/Users/<USER>/Documents/payment-main/backend/node_modules/mongoose/lib/document.js:3219:32)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/mongoose/lib/document.js:3012:17\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/mongoose/lib/schematype.js:1368:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","statusCode":500,"timestamp":"2025-07-26 21:20:10.261","user":"anonymous"}
{"ip":"127.0.0.1","level":"error","message":"crypto.randomBytes is not a function","method":"POST","path":"/api/auth/register","service":"payment-gateway","stack":"TypeError: crypto.randomBytes is not a function\n    at userSchema.methods.generateVerificationToken (/Users/<USER>/Documents/payment-main/backend/models/User.js:255:36)\n    at exports.register (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:65:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":500,"timestamp":"2025-07-26 21:42:08.563","user":"anonymous"}
{"ip":"127.0.0.1","level":"error","message":"401","method":"POST","path":"/api/auth/login","service":"payment-gateway","stack":"ApiError: 401\n    at exports.login (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:143:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":"Invalid credentials","timestamp":"2025-07-26 21:59:10.741","user":"anonymous"}
{"ip":"127.0.0.1","level":"error","message":"401","method":"POST","path":"/api/auth/login","service":"payment-gateway","stack":"ApiError: 401\n    at exports.login (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:144:21)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":"Invalid credentials","timestamp":"2025-07-26 22:07:41.721","user":"anonymous"}
{"ip":"127.0.0.1","level":"error","message":"401","method":"POST","path":"/api/auth/login","service":"payment-gateway","stack":"ApiError: 401\n    at exports.login (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:144:21)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":"Invalid credentials","timestamp":"2025-07-26 22:46:44.252","user":"anonymous"}
{"ip":"127.0.0.1","level":"error","message":"Invalid credentials","method":"POST","path":"/api/auth/login","service":"payment-gateway","stack":"ApiError: Invalid credentials\n    at exports.login (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:144:21)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":401,"timestamp":"2025-07-26 22:52:11.770","user":"anonymous"}
{"level":"error","message":"Missing required environment variables: MONGODB_URI, COOKIE_SECRET, FRONTEND_URL, API_VERSION","service":"payment-gateway","timestamp":"2025-07-26 23:11:54.047"}
{"level":"error","message":"Missing required environment variables: MONGODB_URI, COOKIE_SECRET, FRONTEND_URL, API_VERSION","service":"payment-gateway","timestamp":"2025-07-26 23:12:07.676"}
{"level":"error","message":"Missing required environment variables: MONGODB_URI, COOKIE_SECRET, FRONTEND_URL, API_VERSION","service":"payment-gateway","timestamp":"2025-07-26 23:16:18.796"}
{"level":"error","message":"Missing required environment variables: MONGODB_URI, COOKIE_SECRET, FRONTEND_URL, API_VERSION","service":"payment-gateway","timestamp":"2025-07-26 23:16:22.479"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Validation failed","method":"POST","path":"/api/auth/register","service":"payment-gateway","stack":"ApiError: Validation failed\n    at Function.validationError (/Users/<USER>/Documents/payment-main/backend/utils/ApiError.js:97:12)\n    at validateRequest (/Users/<USER>/Documents/payment-main/backend/middleware/validation.js:331:28)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/Users/<USER>/Documents/payment-main/backend/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":422,"timestamp":"2025-07-26 23:32:48.100","user":"anonymous"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"this.loginAttempts.push is not a function","method":"POST","path":"/api/auth/login","service":"payment-gateway","stack":"TypeError: this.loginAttempts.push is not a function\n    at model.Object.<anonymous>.userSchema.methods.comparePassword (/Users/<USER>/Documents/payment-main/backend/models/User.js:174:22)\n    at Object.<anonymous>.exports.login (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:142:23)","statusCode":500,"timestamp":"2025-07-26 23:32:49.267","user":"anonymous"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"401","method":"GET","path":"/api/auth/me","service":"payment-gateway","stack":"ApiError: 401\n    at auth (/Users/<USER>/Documents/payment-main/backend/middleware/auth.js:47:19)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at Function.handle (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:47:12)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at Object.<anonymous>.securityHeaders (/Users/<USER>/Documents/payment-main/backend/middleware/security.js:88:5)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Documents/payment-main/backend/middleware/security.js:77:5\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Documents/payment-main/backend/middleware/security.js:71:5\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Documents/payment-main/backend/middleware/security.js:65:5\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at cors (/Users/<USER>/Documents/payment-main/backend/node_modules/cors/lib/index.js:188:7)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/cors/lib/index.js:224:17\n    at originCallback (/Users/<USER>/Documents/payment-main/backend/node_modules/cors/lib/index.js:214:15)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/cors/lib/index.js:219:13\n    at optionsCallback (/Users/<USER>/Documents/payment-main/backend/node_modules/cors/lib/index.js:199:9)\n    at corsMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/cors/lib/index.js:204:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:537:6)\n    at xXssProtectionMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:315:3)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)\n    at xPoweredByMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:308:3)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)\n    at xPermittedCrossDomainPoliciesMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:301:3)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)\n    at xFrameOptionsMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:285:3)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)\n    at xDownloadOptionsMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:265:3)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)\n    at xDnsPrefetchControlMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:258:3)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)\n    at xContentTypeOptionsMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:250:3)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)\n    at strictTransportSecurityMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:243:3)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)\n    at referrerPolicyMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:211:3)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)\n    at originAgentClusterMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:186:3)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)\n    at crossOriginResourcePolicyMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:179:3)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)\n    at crossOriginOpenerPolicyMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:163:3)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)\n    at contentSecurityPolicyMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:128:4)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)\n    at helmetMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:539:6)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express-mongo-sanitize/index.js:122:5\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at Object.<anonymous>.mongoSanitize.onSanitize.req.req (/Users/<USER>/Documents/payment-main/backend/middleware/security.js:173:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at hpp (/Users/<USER>/Documents/payment-main/backend/node_modules/hpp/lib/index.js:146:9)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/xss-clean/src/index.js:13:5)","statusCode":"Authentication required","timestamp":"2025-07-26 23:32:49.295","user":"anonymous"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"this.loginAttempts.push is not a function","method":"POST","path":"/api/auth/login","service":"payment-gateway","stack":"TypeError: this.loginAttempts.push is not a function\n    at model.Object.<anonymous>.userSchema.methods.comparePassword (/Users/<USER>/Documents/payment-main/backend/models/User.js:174:22)\n    at Object.<anonymous>.exports.login (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:142:23)","statusCode":500,"timestamp":"2025-07-26 23:32:50.235","user":"anonymous"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"401","method":"GET","path":"/api/auth/me","service":"payment-gateway","stack":"ApiError: 401\n    at auth (/Users/<USER>/Documents/payment-main/backend/middleware/auth.js:47:19)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at Function.handle (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:47:12)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at Object.<anonymous>.securityHeaders (/Users/<USER>/Documents/payment-main/backend/middleware/security.js:88:5)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Documents/payment-main/backend/middleware/security.js:77:5\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Documents/payment-main/backend/middleware/security.js:71:5\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Documents/payment-main/backend/middleware/security.js:65:5\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at cors (/Users/<USER>/Documents/payment-main/backend/node_modules/cors/lib/index.js:188:7)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/cors/lib/index.js:224:17\n    at originCallback (/Users/<USER>/Documents/payment-main/backend/node_modules/cors/lib/index.js:214:15)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/cors/lib/index.js:219:13\n    at optionsCallback (/Users/<USER>/Documents/payment-main/backend/node_modules/cors/lib/index.js:199:9)\n    at corsMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/cors/lib/index.js:204:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:537:6)\n    at xXssProtectionMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:315:3)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)\n    at xPoweredByMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:308:3)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)\n    at xPermittedCrossDomainPoliciesMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:301:3)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)\n    at xFrameOptionsMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:285:3)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)\n    at xDownloadOptionsMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:265:3)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)\n    at xDnsPrefetchControlMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:258:3)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)\n    at xContentTypeOptionsMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:250:3)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)\n    at strictTransportSecurityMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:243:3)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)\n    at referrerPolicyMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:211:3)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)\n    at originAgentClusterMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:186:3)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)\n    at crossOriginResourcePolicyMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:179:3)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)\n    at crossOriginOpenerPolicyMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:163:3)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)\n    at contentSecurityPolicyMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:128:4)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)\n    at helmetMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:539:6)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express-mongo-sanitize/index.js:122:5\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at Object.<anonymous>.mongoSanitize.onSanitize.req.req (/Users/<USER>/Documents/payment-main/backend/middleware/security.js:173:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at hpp (/Users/<USER>/Documents/payment-main/backend/node_modules/hpp/lib/index.js:146:9)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/xss-clean/src/index.js:13:5)","statusCode":"Authentication required","timestamp":"2025-07-26 23:32:50.259","user":"anonymous"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"this.loginAttempts.push is not a function","method":"POST","path":"/api/auth/login","service":"payment-gateway","stack":"TypeError: this.loginAttempts.push is not a function\n    at model.Object.<anonymous>.userSchema.methods.comparePassword (/Users/<USER>/Documents/payment-main/backend/models/User.js:174:22)\n    at Object.<anonymous>.exports.login (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:142:23)","statusCode":500,"timestamp":"2025-07-26 23:32:51.495","user":"anonymous"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"401","method":"GET","path":"/api/auth/me","service":"payment-gateway","stack":"ApiError: 401\n    at auth (/Users/<USER>/Documents/payment-main/backend/middleware/auth.js:47:19)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at Function.handle (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:47:12)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at Object.<anonymous>.securityHeaders (/Users/<USER>/Documents/payment-main/backend/middleware/security.js:88:5)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Documents/payment-main/backend/middleware/security.js:77:5\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Documents/payment-main/backend/middleware/security.js:71:5\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Documents/payment-main/backend/middleware/security.js:65:5\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at cors (/Users/<USER>/Documents/payment-main/backend/node_modules/cors/lib/index.js:188:7)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/cors/lib/index.js:224:17\n    at originCallback (/Users/<USER>/Documents/payment-main/backend/node_modules/cors/lib/index.js:214:15)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/cors/lib/index.js:219:13\n    at optionsCallback (/Users/<USER>/Documents/payment-main/backend/node_modules/cors/lib/index.js:199:9)\n    at corsMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/cors/lib/index.js:204:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:537:6)\n    at xXssProtectionMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:315:3)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)\n    at xPoweredByMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:308:3)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)\n    at xPermittedCrossDomainPoliciesMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:301:3)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)\n    at xFrameOptionsMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:285:3)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)\n    at xDownloadOptionsMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:265:3)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)\n    at xDnsPrefetchControlMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:258:3)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)\n    at xContentTypeOptionsMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:250:3)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)\n    at strictTransportSecurityMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:243:3)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)\n    at referrerPolicyMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:211:3)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)\n    at originAgentClusterMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:186:3)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)\n    at crossOriginResourcePolicyMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:179:3)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)\n    at crossOriginOpenerPolicyMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:163:3)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)\n    at contentSecurityPolicyMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:128:4)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)\n    at helmetMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:539:6)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express-mongo-sanitize/index.js:122:5\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at Object.<anonymous>.mongoSanitize.onSanitize.req.req (/Users/<USER>/Documents/payment-main/backend/middleware/security.js:173:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at hpp (/Users/<USER>/Documents/payment-main/backend/node_modules/hpp/lib/index.js:146:9)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/xss-clean/src/index.js:13:5)","statusCode":"Authentication required","timestamp":"2025-07-26 23:32:51.515","user":"anonymous"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"this.loginAttempts.push is not a function","method":"POST","path":"/api/auth/login","service":"payment-gateway","stack":"TypeError: this.loginAttempts.push is not a function\n    at model.Object.<anonymous>.userSchema.methods.comparePassword (/Users/<USER>/Documents/payment-main/backend/models/User.js:174:22)\n    at Object.<anonymous>.exports.login (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:142:23)","statusCode":500,"timestamp":"2025-07-26 23:32:52.482","user":"anonymous"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"this.loginAttempts.push is not a function","method":"POST","path":"/api/auth/login","service":"payment-gateway","stack":"TypeError: this.loginAttempts.push is not a function\n    at model.Object.<anonymous>.userSchema.methods.comparePassword (/Users/<USER>/Documents/payment-main/backend/models/User.js:174:22)\n    at Object.<anonymous>.exports.login (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:142:23)","statusCode":500,"timestamp":"2025-07-26 23:32:53.480","user":"anonymous"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Validation failed","method":"POST","path":"/api/auth/register","service":"payment-gateway","stack":"ApiError: Validation failed\n    at Function.validationError (/Users/<USER>/Documents/payment-main/backend/utils/ApiError.js:97:12)\n    at validateRequest (/Users/<USER>/Documents/payment-main/backend/middleware/validation.js:331:28)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/Users/<USER>/Documents/payment-main/backend/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":422,"timestamp":"2025-07-26 23:35:48.365","user":"anonymous"}
{"code":"ERR_No recipient specified","errors":[],"isOperational":true,"level":"error","message":"Error sending email: 400","name":"ApiError","service":"payment-gateway","stack":"ApiError: 400\n    at sendEmail (/Users/<USER>/Documents/payment-main/backend/utils/email.js:44:13)\n    at Object.<anonymous>.exports.register (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:71:13)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","status":"error","statusCode":"No recipient specified","timestamp":"2025-07-26T18:08:07.470Z"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"500","method":"POST","path":"/api/auth/register","service":"payment-gateway","stack":"ApiError: 500\n    at Object.<anonymous>.exports.register (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:84:13)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":"There was an error sending the verification email","timestamp":"2025-07-26 23:38:07.490","user":"anonymous"}
{"code":"ERR_No recipient specified","errors":[],"isOperational":true,"level":"error","message":"Error sending email: 400","name":"ApiError","service":"payment-gateway","stack":"ApiError: 400\n    at sendEmail (/Users/<USER>/Documents/payment-main/backend/utils/email.js:44:13)\n    at Object.<anonymous>.exports.register (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:71:13)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","status":"error","statusCode":"No recipient specified","timestamp":"2025-07-26T18:10:25.342Z"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"500","method":"POST","path":"/api/auth/register","service":"payment-gateway","stack":"ApiError: 500\n    at Object.<anonymous>.exports.register (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:84:13)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":"There was an error sending the verification email","timestamp":"2025-07-26 23:40:25.361","user":"anonymous"}
{"code":"ERR_No recipient specified","errors":[],"isOperational":true,"level":"error","message":"Error sending email: 400","name":"ApiError","service":"payment-gateway","stack":"ApiError: 400\n    at sendEmail (/Users/<USER>/Documents/payment-main/backend/utils/email.js:44:13)\n    at Object.<anonymous>.exports.register (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:71:13)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","status":"error","statusCode":"No recipient specified","timestamp":"2025-07-26T18:15:10.842Z"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"500","method":"POST","path":"/api/auth/register","service":"payment-gateway","stack":"ApiError: 500\n    at Object.<anonymous>.exports.register (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:84:13)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":"There was an error sending the verification email","timestamp":"2025-07-26 23:45:10.858","user":"anonymous"}
{"code":"ERR_No recipient specified","errors":[],"isOperational":true,"level":"error","message":"Error sending email: 400","name":"ApiError","service":"payment-gateway","stack":"ApiError: 400\n    at sendEmail (/Users/<USER>/Documents/payment-main/backend/utils/email.js:44:13)\n    at Object.<anonymous>.exports.register (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:71:13)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","status":"error","statusCode":"No recipient specified","timestamp":"2025-07-26T18:16:46.762Z"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"500","method":"POST","path":"/api/auth/register","service":"payment-gateway","stack":"ApiError: 500\n    at Object.<anonymous>.exports.register (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:84:13)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":"There was an error sending the verification email","timestamp":"2025-07-26 23:46:46.781","user":"anonymous"}
{"code":"ERR_No recipient specified","errors":[],"isOperational":true,"level":"error","message":"Error sending email: 400","name":"ApiError","service":"payment-gateway","stack":"ApiError: 400\n    at sendEmail (/Users/<USER>/Documents/payment-main/backend/utils/email.js:44:13)\n    at Object.<anonymous>.exports.register (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:71:13)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","status":"error","statusCode":"No recipient specified","timestamp":"2025-07-26T18:19:46.982Z"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"500","method":"POST","path":"/api/auth/register","service":"payment-gateway","stack":"ApiError: 500\n    at Object.<anonymous>.exports.register (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:84:13)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":"There was an error sending the verification email","timestamp":"2025-07-26 23:49:47.004","user":"anonymous"}
{"code":"ERR_No recipient specified","errors":[],"isOperational":true,"level":"error","message":"Error sending email: 400","name":"ApiError","service":"payment-gateway","stack":"ApiError: 400\n    at sendEmail (/Users/<USER>/Documents/payment-main/backend/utils/email.js:44:13)\n    at Object.<anonymous>.exports.register (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:71:13)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","status":"error","statusCode":"No recipient specified","timestamp":"2025-07-26T18:30:58.691Z"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"500","method":"POST","path":"/api/auth/register","service":"payment-gateway","stack":"ApiError: 500\n    at Object.<anonymous>.exports.register (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:84:13)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":"There was an error sending the verification email","timestamp":"2025-07-27 00:00:58.701","user":"anonymous"}
{"code":"ERR_No recipient specified","errors":[],"isOperational":true,"level":"error","message":"Error sending email: 400","name":"ApiError","service":"payment-gateway","stack":"ApiError: 400\n    at sendEmail (/Users/<USER>/Documents/payment-main/backend/utils/email.js:44:13)\n    at Object.<anonymous>.exports.register (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:71:13)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","status":"error","statusCode":"No recipient specified","timestamp":"2025-07-26T18:38:19.377Z"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"500","method":"POST","path":"/api/auth/register","service":"payment-gateway","stack":"ApiError: 500\n    at Object.<anonymous>.exports.register (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:84:13)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":"There was an error sending the verification email","timestamp":"2025-07-27 00:08:19.389","user":"anonymous"}
{"code":"ERR_No recipient specified","errors":[],"isOperational":true,"level":"error","message":"Error sending email: 400","name":"ApiError","service":"payment-gateway","stack":"ApiError: 400\n    at sendEmail (/Users/<USER>/Documents/payment-main/backend/utils/email.js:44:13)\n    at Object.<anonymous>.exports.register (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:71:13)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","status":"error","statusCode":"No recipient specified","timestamp":"2025-07-26T18:48:37.765Z"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"500","method":"POST","path":"/api/auth/register","service":"payment-gateway","stack":"ApiError: 500\n    at Object.<anonymous>.exports.register (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:84:13)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":"There was an error sending the verification email","timestamp":"2025-07-27 00:18:37.777","user":"anonymous"}
{"code":"ERR_No recipient specified","errors":[],"isOperational":true,"level":"error","message":"Error sending email: 400","name":"ApiError","service":"payment-gateway","stack":"ApiError: 400\n    at sendEmail (/Users/<USER>/Documents/payment-main/backend/utils/email.js:44:13)\n    at Object.<anonymous>.exports.register (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:71:13)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","status":"error","statusCode":"No recipient specified","timestamp":"2025-07-26T18:50:46.998Z"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"500","method":"POST","path":"/api/auth/register","service":"payment-gateway","stack":"ApiError: 500\n    at Object.<anonymous>.exports.register (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:84:13)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":"There was an error sending the verification email","timestamp":"2025-07-27 00:20:47.021","user":"anonymous"}
{"ip":"127.0.0.1","level":"error","message":"400","method":"POST","path":"/api/auth/register","service":"payment-gateway","stack":"ApiError: 400\n    at exports.register (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:50:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":"User already exists","timestamp":"2025-07-27 00:58:25.271","user":"anonymous"}
{"ip":"127.0.0.1","level":"error","message":"this.loginAttempts.push is not a function","method":"POST","path":"/api/auth/login","service":"payment-gateway","stack":"TypeError: this.loginAttempts.push is not a function\n    at userSchema.methods.comparePassword (/Users/<USER>/Documents/payment-main/backend/models/User.js:174:22)\n    at async exports.login (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:157:23)","statusCode":500,"timestamp":"2025-07-27 00:58:40.914","user":"anonymous"}
{"ip":"127.0.0.1","level":"error","message":"User validation failed: password: Path `password` (`test123`) is shorter than the minimum allowed length (8).","method":"POST","path":"/api/auth/login","service":"payment-gateway","stack":"ValidationError: User validation failed: password: Path `password` (`test123`) is shorter than the minimum allowed length (8).\n    at Document.invalidate (/Users/<USER>/Documents/payment-main/backend/node_modules/mongoose/lib/document.js:3219:32)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/mongoose/lib/document.js:3012:17\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/mongoose/lib/schematype.js:1368:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","statusCode":500,"timestamp":"2025-07-27 00:59:25.858","user":"anonymous"}
{"ip":"127.0.0.1","level":"error","message":"User validation failed: password: Path `password` (`test123`) is shorter than the minimum allowed length (8).","method":"POST","path":"/api/auth/login","service":"payment-gateway","stack":"ValidationError: User validation failed: password: Path `password` (`test123`) is shorter than the minimum allowed length (8).\n    at Document.invalidate (/Users/<USER>/Documents/payment-main/backend/node_modules/mongoose/lib/document.js:3219:32)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/mongoose/lib/document.js:3012:17\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/mongoose/lib/schematype.js:1368:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","statusCode":500,"timestamp":"2025-07-27 00:59:51.249","user":"anonymous"}
{"code":"ERR_No recipient specified","errors":[],"isOperational":true,"level":"error","message":"Error sending email: 400","name":"ApiError","service":"payment-gateway","stack":"ApiError: 400\n    at sendEmail (/Users/<USER>/Documents/payment-main/backend/utils/email.js:44:13)\n    at exports.register (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:84:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","status":"error","statusCode":"No recipient specified","timestamp":"2025-07-26T19:30:53.680Z"}
{"ip":"127.0.0.1","level":"error","message":"500","method":"POST","path":"/api/auth/register","service":"payment-gateway","stack":"ApiError: 500\n    at exports.register (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:99:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":"There was an error sending the verification email","timestamp":"2025-07-27 01:00:53.706","user":"anonymous"}
{"code":"EAUTH","command":"AUTH PLAIN","level":"error","message":"Error connecting to email server: Invalid login: 535 Authentication failed","response":"535 Authentication failed","responseCode":535,"service":"payment-gateway","stack":"Error: Invalid login: 535 Authentication failed\n    at SMTPConnection._formatError (/Users/<USER>/Documents/payment-main/backend/node_modules/nodemailer/lib/smtp-connection/index.js:809:19)\n    at SMTPConnection._actionAUTHComplete (/Users/<USER>/Documents/payment-main/backend/node_modules/nodemailer/lib/smtp-connection/index.js:1588:34)\n    at SMTPConnection.<anonymous> (/Users/<USER>/Documents/payment-main/backend/node_modules/nodemailer/lib/smtp-connection/index.js:556:26)\n    at SMTPConnection._processResponse (/Users/<USER>/Documents/payment-main/backend/node_modules/nodemailer/lib/smtp-connection/index.js:993:20)\n    at SMTPConnection._onData (/Users/<USER>/Documents/payment-main/backend/node_modules/nodemailer/lib/smtp-connection/index.js:774:14)\n    at SMTPConnection._onSocketData (/Users/<USER>/Documents/payment-main/backend/node_modules/nodemailer/lib/smtp-connection/index.js:195:44)\n    at TLSSocket.emit (node:events:524:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-07-27 01:02:21.288"}
{"ip":"127.0.0.1","level":"error","message":"400","method":"POST","path":"/api/auth/register","service":"payment-gateway","stack":"ApiError: 400\n    at exports.register (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:50:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":"User already exists","timestamp":"2025-07-27 01:02:27.502","user":"anonymous"}
{"code":"ERR_No recipient specified","errors":[],"isOperational":true,"level":"error","message":"Error sending email: 400","name":"ApiError","service":"payment-gateway","stack":"ApiError: 400\n    at sendEmail (/Users/<USER>/Documents/payment-main/backend/utils/email.js:44:13)\n    at exports.register (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:84:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","status":"error","statusCode":"No recipient specified","timestamp":"2025-07-26T19:32:32.806Z"}
{"ip":"127.0.0.1","level":"error","message":"500","method":"POST","path":"/api/auth/register","service":"payment-gateway","stack":"ApiError: 500\n    at exports.register (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:99:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":"There was an error sending the verification email","timestamp":"2025-07-27 01:02:32.833","user":"anonymous"}
{"code":"EAUTH","command":"AUTH PLAIN","level":"error","message":"Error connecting to email server: Invalid login: 535 Authentication failed","response":"535 Authentication failed","responseCode":535,"service":"payment-gateway","stack":"Error: Invalid login: 535 Authentication failed\n    at SMTPConnection._formatError (/Users/<USER>/Documents/payment-main/backend/node_modules/nodemailer/lib/smtp-connection/index.js:809:19)\n    at SMTPConnection._actionAUTHComplete (/Users/<USER>/Documents/payment-main/backend/node_modules/nodemailer/lib/smtp-connection/index.js:1588:34)\n    at SMTPConnection.<anonymous> (/Users/<USER>/Documents/payment-main/backend/node_modules/nodemailer/lib/smtp-connection/index.js:556:26)\n    at SMTPConnection._processResponse (/Users/<USER>/Documents/payment-main/backend/node_modules/nodemailer/lib/smtp-connection/index.js:993:20)\n    at SMTPConnection._onData (/Users/<USER>/Documents/payment-main/backend/node_modules/nodemailer/lib/smtp-connection/index.js:774:14)\n    at SMTPConnection._onSocketData (/Users/<USER>/Documents/payment-main/backend/node_modules/nodemailer/lib/smtp-connection/index.js:195:44)\n    at TLSSocket.emit (node:events:524:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-07-27 01:03:42.637"}
{"code":"ERR_No recipient specified","level":"error","message":"400","service":"payment-gateway","stack":"ApiError: 400\n    at sendEmail (/Users/<USER>/Documents/payment-main/backend/utils/email.js:44:13)\n    at exports.register (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:84:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":"No recipient specified","subject":"Verify your email","template":"email-verification","timestamp":"2025-07-27 01:03:46.982"}
{"ip":"127.0.0.1","level":"error","message":"500","method":"POST","path":"/api/auth/register","service":"payment-gateway","stack":"ApiError: 500\n    at exports.register (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:99:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":"There was an error sending the verification email","timestamp":"2025-07-27 01:03:47.010","user":"anonymous"}
{"code":"EAUTH","command":"AUTH PLAIN","level":"error","message":"Invalid login: 535 Authentication failed","response":"535 Authentication failed","service":"payment-gateway","stack":"Error: Invalid login: 535 Authentication failed\n    at SMTPConnection._formatError (/Users/<USER>/Documents/payment-main/backend/node_modules/nodemailer/lib/smtp-connection/index.js:809:19)\n    at SMTPConnection._actionAUTHComplete (/Users/<USER>/Documents/payment-main/backend/node_modules/nodemailer/lib/smtp-connection/index.js:1588:34)\n    at SMTPConnection.<anonymous> (/Users/<USER>/Documents/payment-main/backend/node_modules/nodemailer/lib/smtp-connection/index.js:556:26)\n    at SMTPConnection._processResponse (/Users/<USER>/Documents/payment-main/backend/node_modules/nodemailer/lib/smtp-connection/index.js:993:20)\n    at SMTPConnection._onData (/Users/<USER>/Documents/payment-main/backend/node_modules/nodemailer/lib/smtp-connection/index.js:774:14)\n    at SMTPConnection._onSocketData (/Users/<USER>/Documents/payment-main/backend/node_modules/nodemailer/lib/smtp-connection/index.js:195:44)\n    at TLSSocket.emit (node:events:524:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","subject":"Verify your email","template":"email-verification","timestamp":"2025-07-27 01:04:51.047","to":"<EMAIL>"}
{"0":"5","1":"3","10":"t","11":"i","12":"c","13":"a","14":"t","15":"i","16":"o","17":"n","18":" ","19":"f","2":"5","20":"a","21":"i","22":"l","23":"e","24":"d","3":" ","4":"A","5":"u","6":"t","7":"h","8":"e","9":"n","level":"error","message":"Nodemailer error response:","service":"payment-gateway","timestamp":"2025-07-27 01:04:51.047"}
{"ip":"127.0.0.1","level":"error","message":"500","method":"POST","path":"/api/auth/register","service":"payment-gateway","stack":"ApiError: 500\n    at exports.register (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:99:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":"There was an error sending the verification email","timestamp":"2025-07-27 01:04:51.076","user":"anonymous"}
{"code":"EAUTH","command":"AUTH PLAIN","level":"error","message":"Error connecting to email server: Invalid login: 535 Authentication failed","response":"535 Authentication failed","responseCode":535,"service":"payment-gateway","stack":"Error: Invalid login: 535 Authentication failed\n    at SMTPConnection._formatError (/Users/<USER>/Documents/payment-main/backend/node_modules/nodemailer/lib/smtp-connection/index.js:809:19)\n    at SMTPConnection._actionAUTHComplete (/Users/<USER>/Documents/payment-main/backend/node_modules/nodemailer/lib/smtp-connection/index.js:1588:34)\n    at SMTPConnection.<anonymous> (/Users/<USER>/Documents/payment-main/backend/node_modules/nodemailer/lib/smtp-connection/index.js:556:26)\n    at SMTPConnection._processResponse (/Users/<USER>/Documents/payment-main/backend/node_modules/nodemailer/lib/smtp-connection/index.js:993:20)\n    at SMTPConnection._onData (/Users/<USER>/Documents/payment-main/backend/node_modules/nodemailer/lib/smtp-connection/index.js:774:14)\n    at SMTPConnection._onSocketData (/Users/<USER>/Documents/payment-main/backend/node_modules/nodemailer/lib/smtp-connection/index.js:195:44)\n    at TLSSocket.emit (node:events:524:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-07-27 01:04:51.408"}
{"code":"EAUTH","command":"AUTH PLAIN","level":"error","message":"Error connecting to email server: Invalid login: 535 Authentication failed","response":"535 Authentication failed","responseCode":535,"service":"payment-gateway","stack":"Error: Invalid login: 535 Authentication failed\n    at SMTPConnection._formatError (/Users/<USER>/Documents/payment-main/backend/node_modules/nodemailer/lib/smtp-connection/index.js:809:19)\n    at SMTPConnection._actionAUTHComplete (/Users/<USER>/Documents/payment-main/backend/node_modules/nodemailer/lib/smtp-connection/index.js:1588:34)\n    at SMTPConnection.<anonymous> (/Users/<USER>/Documents/payment-main/backend/node_modules/nodemailer/lib/smtp-connection/index.js:556:26)\n    at SMTPConnection._processResponse (/Users/<USER>/Documents/payment-main/backend/node_modules/nodemailer/lib/smtp-connection/index.js:993:20)\n    at SMTPConnection._onData (/Users/<USER>/Documents/payment-main/backend/node_modules/nodemailer/lib/smtp-connection/index.js:774:14)\n    at SMTPConnection._onSocketData (/Users/<USER>/Documents/payment-main/backend/node_modules/nodemailer/lib/smtp-connection/index.js:195:44)\n    at TLSSocket.emit (node:events:524:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-07-27 01:05:32.413"}
{"code":"EAUTH","command":"AUTH PLAIN","level":"error","message":"Invalid login: 535 Authentication failed","response":"535 Authentication failed","service":"payment-gateway","stack":"Error: Invalid login: 535 Authentication failed\n    at SMTPConnection._formatError (/Users/<USER>/Documents/payment-main/backend/node_modules/nodemailer/lib/smtp-connection/index.js:809:19)\n    at SMTPConnection._actionAUTHComplete (/Users/<USER>/Documents/payment-main/backend/node_modules/nodemailer/lib/smtp-connection/index.js:1588:34)\n    at SMTPConnection.<anonymous> (/Users/<USER>/Documents/payment-main/backend/node_modules/nodemailer/lib/smtp-connection/index.js:556:26)\n    at SMTPConnection._processResponse (/Users/<USER>/Documents/payment-main/backend/node_modules/nodemailer/lib/smtp-connection/index.js:993:20)\n    at SMTPConnection._onData (/Users/<USER>/Documents/payment-main/backend/node_modules/nodemailer/lib/smtp-connection/index.js:774:14)\n    at SMTPConnection._onSocketData (/Users/<USER>/Documents/payment-main/backend/node_modules/nodemailer/lib/smtp-connection/index.js:195:44)\n    at TLSSocket.emit (node:events:524:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","subject":"Verify your email","template":"email-verification","timestamp":"2025-07-27 01:05:45.321","to":"<EMAIL>"}
{"0":"5","1":"3","10":"t","11":"i","12":"c","13":"a","14":"t","15":"i","16":"o","17":"n","18":" ","19":"f","2":"5","20":"a","21":"i","22":"l","23":"e","24":"d","3":" ","4":"A","5":"u","6":"t","7":"h","8":"e","9":"n","level":"error","message":"Nodemailer error response:","service":"payment-gateway","timestamp":"2025-07-27 01:05:45.322"}
{"ip":"127.0.0.1","level":"error","message":"500","method":"POST","path":"/api/auth/register","service":"payment-gateway","stack":"ApiError: 500\n    at exports.register (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:99:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":"There was an error sending the verification email","timestamp":"2025-07-27 01:05:45.352","user":"anonymous"}
{"ip":"127.0.0.1","level":"error","message":"this.loginAttempts.push is not a function","method":"POST","path":"/api/auth/login","service":"payment-gateway","stack":"TypeError: this.loginAttempts.push is not a function\n    at userSchema.methods.comparePassword (/Users/<USER>/Documents/payment-main/backend/models/User.js:174:22)\n    at async exports.login (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:157:23)","statusCode":500,"timestamp":"2025-07-27 01:09:17.054","user":"anonymous"}
{"ip":"127.0.0.1","level":"error","message":"Cannot set properties of undefined (setting 'user')","method":"POST","path":"/api/auth/login","service":"payment-gateway","stack":"TypeError: Cannot set properties of undefined (setting 'user')\n    at exports.login (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:188:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":500,"timestamp":"2025-07-27 01:11:20.961","user":"anonymous"}
{"ip":"127.0.0.1","level":"error","message":"passwordResetLimiter is not defined","method":"POST","path":"/api/auth/forgot-password","service":"payment-gateway","stack":"ReferenceError: passwordResetLimiter is not defined\n    at exports.forgotPassword (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:259:5)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:149:13)\n    at validateRequest (/Users/<USER>/Documents/payment-main/backend/middleware/validation.js:336:5)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/Users/<USER>/Documents/payment-main/backend/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":500,"timestamp":"2025-07-27 01:13:35.296","user":"anonymous"}
{"ip":"127.0.0.1","level":"error","message":"Invalid credentials","method":"POST","path":"/api/auth/login","service":"payment-gateway","stack":"ApiError: Invalid credentials\n    at exports.login (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:152:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":401,"timestamp":"2025-07-27 01:18:20.762","user":"anonymous"}
{"ip":"127.0.0.1","level":"error","message":"Invalid credentials","method":"POST","path":"/api/auth/login","service":"payment-gateway","stack":"ApiError: Invalid credentials\n    at exports.login (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:159:21)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":401,"timestamp":"2025-07-27 01:19:09.265","user":"anonymous"}
{"ip":"127.0.0.1","level":"error","message":"Invalid credentials","method":"POST","path":"/api/auth/login","service":"payment-gateway","stack":"ApiError: Invalid credentials\n    at exports.login (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:159:21)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":401,"timestamp":"2025-07-27 01:20:48.649","user":"anonymous"}
{"ip":"127.0.0.1","level":"error","message":"401","method":"GET","path":"/api/admin/analytics","service":"payment-gateway","stack":"ApiError: 401\n    at auth (/Users/<USER>/Documents/payment-main/backend/middleware/auth.js:47:19)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at Function.handle (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:47:12)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)","statusCode":"Authentication required","timestamp":"2025-07-27 01:28:05.031","user":"anonymous"}
{"ip":"127.0.0.1","level":"error","message":"401","method":"GET","path":"/api/admin/analytics","service":"payment-gateway","stack":"ApiError: 401\n    at auth (/Users/<USER>/Documents/payment-main/backend/middleware/auth.js:47:19)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at Function.handle (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:47:12)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)","statusCode":"Authentication required","timestamp":"2025-07-27 01:33:56.950","user":"anonymous"}
{"date":"Sun Jul 27 2025 01:34:10 GMT+0530 (India Standard Time)","error":{"reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"}},"level":"error","message":"unhandledRejection: getaddrinfo ENOTFOUND mongo\nMongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","os":{"loadavg":[49.5009765625,36.91943359375,24.5048828125],"uptime":27584},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","/Users/<USER>/Documents/payment-main/backend/server.js"],"cwd":"/Users/<USER>/Documents/payment-main/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","gid":20,"memoryUsage":{"arrayBuffers":18271520,"external":20508888,"heapTotal":32264192,"heapUsed":30770816,"rss":74620928},"pid":50430,"uid":501,"version":"v20.19.3"},"rejection":true,"service":"payment-gateway","stack":"MongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-07-27 01:34:10.934","trace":[{"column":38,"file":"/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js","function":"Timeout._onTimeout","line":278,"method":"_onTimeout","native":false},{"column":17,"file":"node:internal/timers","function":"listOnTimeout","line":581,"method":null,"native":false},{"column":7,"file":"node:internal/timers","function":"process.processTimers","line":519,"method":"processTimers","native":false}]}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 01:34:10.937"}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 01:34:10.938"}
{"date":"Sun Jul 27 2025 01:34:12 GMT+0530 (India Standard Time)","error":{"reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"}},"level":"error","message":"unhandledRejection: getaddrinfo ENOTFOUND mongo\nMongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","os":{"loadavg":[49.5009765625,36.91943359375,24.5048828125],"uptime":27586},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","/Users/<USER>/Documents/payment-main/backend/server.js"],"cwd":"/Users/<USER>/Documents/payment-main/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","gid":20,"memoryUsage":{"arrayBuffers":18280362,"external":20517770,"heapTotal":32264192,"heapUsed":31121920,"rss":74985472},"pid":50430,"uid":501,"version":"v20.19.3"},"rejection":true,"service":"payment-gateway","stack":"MongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-07-27 01:34:12.813","trace":[{"column":38,"file":"/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js","function":"Timeout._onTimeout","line":278,"method":"_onTimeout","native":false},{"column":17,"file":"node:internal/timers","function":"listOnTimeout","line":581,"method":null,"native":false},{"column":7,"file":"node:internal/timers","function":"process.processTimers","line":519,"method":"processTimers","native":false}]}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 01:34:12.813"}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 01:34:12.814"}
{"ip":"127.0.0.1","level":"error","message":"Invalid credentials","method":"POST","path":"/api/auth/login","service":"payment-gateway","stack":"ApiError: Invalid credentials\n    at exports.login (/Users/<USER>/Documents/payment-main/backend/controllers/authController.js:152:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":401,"timestamp":"2025-07-27 01:37:29.745","user":"anonymous"}
{"date":"Sun Jul 27 2025 01:37:42 GMT+0530 (India Standard Time)","error":{"reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"}},"level":"error","message":"unhandledRejection: getaddrinfo ENOTFOUND mongo\nMongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","os":{"loadavg":[17.16650390625,27.60498046875,23.2021484375],"uptime":27796},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","/Users/<USER>/Documents/payment-main/backend/server.js"],"cwd":"/Users/<USER>/Documents/payment-main/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","gid":20,"memoryUsage":{"arrayBuffers":18279728,"external":20517860,"heapTotal":33050624,"heapUsed":32023584,"rss":75403264},"pid":51093,"uid":501,"version":"v20.19.3"},"rejection":true,"service":"payment-gateway","stack":"MongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-07-27 01:37:42.069","trace":[{"column":38,"file":"/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js","function":"Timeout._onTimeout","line":278,"method":"_onTimeout","native":false},{"column":17,"file":"node:internal/timers","function":"listOnTimeout","line":581,"method":null,"native":false},{"column":7,"file":"node:internal/timers","function":"process.processTimers","line":519,"method":"processTimers","native":false}]}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 01:37:42.073"}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 01:37:42.073"}
{"date":"Sun Jul 27 2025 01:37:43 GMT+0530 (India Standard Time)","error":{"reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"}},"level":"error","message":"unhandledRejection: getaddrinfo ENOTFOUND mongo\nMongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","os":{"loadavg":[17.16650390625,27.60498046875,23.2021484375],"uptime":27797},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","/Users/<USER>/Documents/payment-main/backend/server.js"],"cwd":"/Users/<USER>/Documents/payment-main/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","gid":20,"memoryUsage":{"arrayBuffers":18288570,"external":20525978,"heapTotal":33312768,"heapUsed":31389424,"rss":74821632},"pid":51093,"uid":501,"version":"v20.19.3"},"rejection":true,"service":"payment-gateway","stack":"MongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-07-27 01:37:43.398","trace":[{"column":38,"file":"/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js","function":"Timeout._onTimeout","line":278,"method":"_onTimeout","native":false},{"column":17,"file":"node:internal/timers","function":"listOnTimeout","line":581,"method":null,"native":false},{"column":7,"file":"node:internal/timers","function":"process.processTimers","line":519,"method":"processTimers","native":false}]}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 01:37:43.399"}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 01:37:43.399"}
{"level":"error","message":"Authentication error: mongoose is not defined","service":"payment-gateway","stack":"ReferenceError: mongoose is not defined\n    at auth (/Users/<USER>/Documents/payment-main/backend/middleware/auth.js:54:27)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at Function.handle (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:47:12)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)","timestamp":"2025-07-27 01:40:14.659"}
{"ip":"127.0.0.1","level":"error","message":"500","method":"GET","path":"/api/admin/analytics","service":"payment-gateway","stack":"ApiError: 500\n    at auth (/Users/<USER>/Documents/payment-main/backend/middleware/auth.js:85:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at Function.handle (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:47:12)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)","statusCode":"Authentication failed","timestamp":"2025-07-27 01:40:14.660","user":"anonymous"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-27 01:54:47.869"}
{"date":"Sun Jul 27 2025 01:55:59 GMT+0530 (India Standard Time)","error":{"reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"}},"level":"error","message":"unhandledRejection: getaddrinfo ENOTFOUND mongo\nMongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","os":{"loadavg":[16.87158203125,14.7060546875,18.24853515625],"uptime":28893},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","/Users/<USER>/Documents/payment-main/backend/server.js"],"cwd":"/Users/<USER>/Documents/payment-main/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","gid":20,"memoryUsage":{"arrayBuffers":18279712,"external":20517706,"heapTotal":32526336,"heapUsed":30802656,"rss":77541376},"pid":53612,"uid":501,"version":"v20.19.3"},"rejection":true,"service":"payment-gateway","stack":"MongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-07-27 01:55:59.763","trace":[{"column":38,"file":"/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js","function":"Timeout._onTimeout","line":278,"method":"_onTimeout","native":false},{"column":17,"file":"node:internal/timers","function":"listOnTimeout","line":581,"method":null,"native":false},{"column":7,"file":"node:internal/timers","function":"process.processTimers","line":519,"method":"processTimers","native":false}]}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 01:55:59.764"}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 01:55:59.764"}
{"date":"Sun Jul 27 2025 01:56:01 GMT+0530 (India Standard Time)","error":{"reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"}},"level":"error","message":"unhandledRejection: getaddrinfo ENOTFOUND mongo\nMongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","os":{"loadavg":[16.87158203125,14.7060546875,18.24853515625],"uptime":28895},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","/Users/<USER>/Documents/payment-main/backend/server.js"],"cwd":"/Users/<USER>/Documents/payment-main/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","gid":20,"memoryUsage":{"arrayBuffers":18288554,"external":20525962,"heapTotal":32526336,"heapUsed":31132064,"rss":77582336},"pid":53612,"uid":501,"version":"v20.19.3"},"rejection":true,"service":"payment-gateway","stack":"MongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-07-27 01:56:01.140","trace":[{"column":38,"file":"/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js","function":"Timeout._onTimeout","line":278,"method":"_onTimeout","native":false},{"column":17,"file":"node:internal/timers","function":"listOnTimeout","line":581,"method":null,"native":false},{"column":7,"file":"node:internal/timers","function":"process.processTimers","line":519,"method":"processTimers","native":false}]}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 01:56:01.142"}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 01:56:01.143"}
{"date":"Sun Jul 27 2025 02:01:16 GMT+0530 (India Standard Time)","error":{"reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"}},"level":"error","message":"unhandledRejection: getaddrinfo ENOTFOUND mongo\nMongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","os":{"loadavg":[62.4794921875,58.2685546875,38.97314453125],"uptime":29210},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","/Users/<USER>/Documents/payment-main/backend/server.js"],"cwd":"/Users/<USER>/Documents/payment-main/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","gid":20,"memoryUsage":{"arrayBuffers":18279696,"external":20517690,"heapTotal":32526336,"heapUsed":31297696,"rss":76992512},"pid":55240,"uid":501,"version":"v20.19.3"},"rejection":true,"service":"payment-gateway","stack":"MongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-07-27 02:01:16.923","trace":[{"column":38,"file":"/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js","function":"Timeout._onTimeout","line":278,"method":"_onTimeout","native":false},{"column":17,"file":"node:internal/timers","function":"listOnTimeout","line":581,"method":null,"native":false},{"column":7,"file":"node:internal/timers","function":"process.processTimers","line":519,"method":"processTimers","native":false}]}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 02:01:16.931"}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 02:01:16.934"}
{"date":"Sun Jul 27 2025 02:01:17 GMT+0530 (India Standard Time)","error":{"reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"}},"level":"error","message":"unhandledRejection: getaddrinfo ENOTFOUND mongo\nMongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","os":{"loadavg":[58.03662109375,57.4169921875,38.78564453125],"uptime":29211},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","/Users/<USER>/Documents/payment-main/backend/server.js"],"cwd":"/Users/<USER>/Documents/payment-main/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","gid":20,"memoryUsage":{"arrayBuffers":18279720,"external":20517128,"heapTotal":32526336,"heapUsed":30600744,"rss":76283904},"pid":55240,"uid":501,"version":"v20.19.3"},"rejection":true,"service":"payment-gateway","stack":"MongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-07-27 02:01:17.289","trace":[{"column":38,"file":"/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js","function":"Timeout._onTimeout","line":278,"method":"_onTimeout","native":false},{"column":17,"file":"node:internal/timers","function":"listOnTimeout","line":581,"method":null,"native":false},{"column":7,"file":"node:internal/timers","function":"process.processTimers","line":519,"method":"processTimers","native":false}]}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 02:01:17.290"}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 02:01:17.290"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-27 02:01:35.247"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-27 02:31:09.690"}
{"date":"Sun Jul 27 2025 09:18:53 GMT+0530 (India Standard Time)","error":{"reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"}},"level":"error","message":"unhandledRejection: getaddrinfo ENOTFOUND mongo\nMongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","os":{"loadavg":[27.5224609375,21.818359375,14.99609375],"uptime":1208},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","/Users/<USER>/Documents/payment-main/backend/server.js"],"cwd":"/Users/<USER>/Documents/payment-main/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","gid":20,"memoryUsage":{"arrayBuffers":18271504,"external":20509498,"heapTotal":32526336,"heapUsed":30730912,"rss":77533184},"pid":9658,"uid":501,"version":"v20.19.3"},"rejection":true,"service":"payment-gateway","stack":"MongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-07-27 09:18:53.116","trace":[{"column":38,"file":"/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js","function":"Timeout._onTimeout","line":278,"method":"_onTimeout","native":false},{"column":17,"file":"node:internal/timers","function":"listOnTimeout","line":581,"method":null,"native":false},{"column":7,"file":"node:internal/timers","function":"process.processTimers","line":519,"method":"processTimers","native":false}]}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 09:18:53.117"}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 09:18:53.117"}
{"date":"Sun Jul 27 2025 09:18:53 GMT+0530 (India Standard Time)","error":{"reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"}},"level":"error","message":"unhandledRejection: getaddrinfo ENOTFOUND mongo\nMongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","os":{"loadavg":[27.5224609375,21.818359375,14.99609375],"uptime":1208},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","/Users/<USER>/Documents/payment-main/backend/server.js"],"cwd":"/Users/<USER>/Documents/payment-main/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","gid":20,"memoryUsage":{"arrayBuffers":18279720,"external":20517128,"heapTotal":32526336,"heapUsed":31172752,"rss":77623296},"pid":9658,"uid":501,"version":"v20.19.3"},"rejection":true,"service":"payment-gateway","stack":"MongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-07-27 09:18:53.362","trace":[{"column":38,"file":"/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js","function":"Timeout._onTimeout","line":278,"method":"_onTimeout","native":false},{"column":17,"file":"node:internal/timers","function":"listOnTimeout","line":581,"method":null,"native":false},{"column":7,"file":"node:internal/timers","function":"process.processTimers","line":519,"method":"processTimers","native":false}]}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 09:18:53.362"}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 09:18:53.363"}
{"date":"Sun Jul 27 2025 09:30:46 GMT+0530 (India Standard Time)","error":{"reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"}},"level":"error","message":"unhandledRejection: getaddrinfo ENOTFOUND mongo\nMongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","os":{"loadavg":[61.435546875,39.0556640625,25.28564453125],"uptime":1921},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","/Users/<USER>/Documents/payment-main/backend/server.js"],"cwd":"/Users/<USER>/Documents/payment-main/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","gid":20,"memoryUsage":{"arrayBuffers":18271504,"external":20508872,"heapTotal":32264192,"heapUsed":30645912,"rss":67567616},"pid":15028,"uid":501,"version":"v20.19.3"},"rejection":true,"service":"payment-gateway","stack":"MongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-07-27 09:30:46.375","trace":[{"column":38,"file":"/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js","function":"Timeout._onTimeout","line":278,"method":"_onTimeout","native":false},{"column":17,"file":"node:internal/timers","function":"listOnTimeout","line":581,"method":null,"native":false},{"column":7,"file":"node:internal/timers","function":"process.processTimers","line":519,"method":"processTimers","native":false}]}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 09:30:46.376"}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 09:30:46.376"}
{"date":"Sun Jul 27 2025 09:30:47 GMT+0530 (India Standard Time)","error":{"reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"}},"level":"error","message":"unhandledRejection: getaddrinfo ENOTFOUND mongo\nMongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","os":{"loadavg":[61.435546875,39.0556640625,25.28564453125],"uptime":1922},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","/Users/<USER>/Documents/payment-main/backend/server.js"],"cwd":"/Users/<USER>/Documents/payment-main/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","gid":20,"memoryUsage":{"arrayBuffers":18272154,"external":20509562,"heapTotal":32264192,"heapUsed":30989008,"rss":67915776},"pid":15028,"uid":501,"version":"v20.19.3"},"rejection":true,"service":"payment-gateway","stack":"MongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-07-27 09:30:47.585","trace":[{"column":38,"file":"/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js","function":"Timeout._onTimeout","line":278,"method":"_onTimeout","native":false},{"column":17,"file":"node:internal/timers","function":"listOnTimeout","line":581,"method":null,"native":false},{"column":7,"file":"node:internal/timers","function":"process.processTimers","line":519,"method":"processTimers","native":false}]}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 09:30:47.586"}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 09:30:47.591"}
{"ip":"127.0.0.1","level":"error","message":"401","method":"GET","path":"/api/api/auth/me","service":"payment-gateway","stack":"ApiError: 401\n    at auth (/Users/<USER>/Documents/payment-main/backend/middleware/auth.js:49:19)\n    at /Users/<USER>/Documents/payment-main/backend/app.js:212:7\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at logger (/Users/<USER>/Documents/payment-main/backend/node_modules/morgan/index.js:144:5)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)","statusCode":"No session ID found","timestamp":"2025-07-27 09:31:35.486","user":"anonymous"}
{"ip":"127.0.0.1","level":"error","message":"401","method":"GET","path":"/favicon.ico","service":"payment-gateway","stack":"ApiError: 401\n    at auth (/Users/<USER>/Documents/payment-main/backend/middleware/auth.js:49:19)\n    at /Users/<USER>/Documents/payment-main/backend/app.js:212:7\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at logger (/Users/<USER>/Documents/payment-main/backend/node_modules/morgan/index.js:144:5)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)","statusCode":"No session ID found","timestamp":"2025-07-27 09:31:36.902","user":"anonymous"}
{"ip":"127.0.0.1","level":"error","message":"401","method":"GET","path":"/api/api/auth/me","service":"payment-gateway","stack":"ApiError: 401\n    at auth (/Users/<USER>/Documents/payment-main/backend/middleware/auth.js:49:19)\n    at /Users/<USER>/Documents/payment-main/backend/app.js:212:7\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at logger (/Users/<USER>/Documents/payment-main/backend/node_modules/morgan/index.js:144:5)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)","statusCode":"No session ID found","timestamp":"2025-07-27 09:31:45.745","user":"anonymous"}
{"date":"Sun Jul 27 2025 09:45:49 GMT+0530 (India Standard Time)","error":{"reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"}},"level":"error","message":"unhandledRejection: getaddrinfo ENOTFOUND mongo\nMongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","os":{"loadavg":[31.33251953125,31.92138671875,30.96630859375],"uptime":2824},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","/Users/<USER>/Documents/payment-main/backend/server.js"],"cwd":"/Users/<USER>/Documents/payment-main/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","gid":20,"memoryUsage":{"arrayBuffers":18271536,"external":20508904,"heapTotal":32002048,"heapUsed":30499440,"rss":63848448},"pid":19629,"uid":501,"version":"v20.19.3"},"rejection":true,"service":"payment-gateway","stack":"MongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-07-27 09:45:49.868","trace":[{"column":38,"file":"/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js","function":"Timeout._onTimeout","line":278,"method":"_onTimeout","native":false},{"column":17,"file":"node:internal/timers","function":"listOnTimeout","line":581,"method":null,"native":false},{"column":7,"file":"node:internal/timers","function":"process.processTimers","line":519,"method":"processTimers","native":false}]}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 09:45:49.869"}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 09:45:49.869"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-27 09:45:50.694"}
{"date":"Sun Jul 27 2025 09:45:50 GMT+0530 (India Standard Time)","error":{"reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"}},"level":"error","message":"unhandledRejection: getaddrinfo ENOTFOUND mongo\nMongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","os":{"loadavg":[31.33251953125,31.92138671875,30.96630859375],"uptime":2825},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","/Users/<USER>/Documents/payment-main/backend/server.js"],"cwd":"/Users/<USER>/Documents/payment-main/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","gid":20,"memoryUsage":{"arrayBuffers":18280378,"external":20517786,"heapTotal":32526336,"heapUsed":31102776,"rss":64212992},"pid":19629,"uid":501,"version":"v20.19.3"},"rejection":true,"service":"payment-gateway","stack":"MongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-07-27 09:45:50.984","trace":[{"column":38,"file":"/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js","function":"Timeout._onTimeout","line":278,"method":"_onTimeout","native":false},{"column":17,"file":"node:internal/timers","function":"listOnTimeout","line":581,"method":null,"native":false},{"column":7,"file":"node:internal/timers","function":"process.processTimers","line":519,"method":"processTimers","native":false}]}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 09:45:50.986"}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 09:45:50.986"}
{"date":"Sun Jul 27 2025 09:47:07 GMT+0530 (India Standard Time)","error":{"reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"}},"level":"error","message":"unhandledRejection: getaddrinfo ENOTFOUND mongo\nMongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","os":{"loadavg":[26.703125,30.6376953125,30.5771484375],"uptime":2902},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","/Users/<USER>/Documents/payment-main/backend/server.js"],"cwd":"/Users/<USER>/Documents/payment-main/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","gid":20,"memoryUsage":{"arrayBuffers":18271536,"external":20509530,"heapTotal":33312768,"heapUsed":30836536,"rss":65916928},"pid":21389,"uid":501,"version":"v20.19.3"},"rejection":true,"service":"payment-gateway","stack":"MongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-07-27 09:47:07.365","trace":[{"column":38,"file":"/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js","function":"Timeout._onTimeout","line":278,"method":"_onTimeout","native":false},{"column":17,"file":"node:internal/timers","function":"listOnTimeout","line":581,"method":null,"native":false},{"column":7,"file":"node:internal/timers","function":"process.processTimers","line":519,"method":"processTimers","native":false}]}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 09:47:07.367"}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 09:47:07.368"}
{"date":"Sun Jul 27 2025 09:47:09 GMT+0530 (India Standard Time)","error":{"reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"}},"level":"error","message":"unhandledRejection: getaddrinfo ENOTFOUND mongo\nMongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","os":{"loadavg":[26.703125,30.6376953125,30.5771484375],"uptime":2904},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","/Users/<USER>/Documents/payment-main/backend/server.js"],"cwd":"/Users/<USER>/Documents/payment-main/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","gid":20,"memoryUsage":{"arrayBuffers":18280378,"external":20517786,"heapTotal":33574912,"heapUsed":31206792,"rss":66334720},"pid":21389,"uid":501,"version":"v20.19.3"},"rejection":true,"service":"payment-gateway","stack":"MongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-07-27 09:47:09.341","trace":[{"column":38,"file":"/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js","function":"Timeout._onTimeout","line":278,"method":"_onTimeout","native":false},{"column":17,"file":"node:internal/timers","function":"listOnTimeout","line":581,"method":null,"native":false},{"column":7,"file":"node:internal/timers","function":"process.processTimers","line":519,"method":"processTimers","native":false}]}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 09:47:09.342"}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 09:47:09.346"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-27 10:02:54.624"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-27 10:04:20.267"}
{"date":"Sun Jul 27 2025 10:05:53 GMT+0530 (India Standard Time)","error":{"reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"}},"level":"error","message":"unhandledRejection: getaddrinfo ENOTFOUND mongo\nMongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","os":{"loadavg":[19.1728515625,78.09130859375,89.58056640625],"uptime":4028},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","/Users/<USER>/Documents/payment-main/backend/server.js"],"cwd":"/Users/<USER>/Documents/payment-main/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","gid":20,"memoryUsage":{"arrayBuffers":18279664,"external":20517658,"heapTotal":32264192,"heapUsed":30673752,"rss":73793536},"pid":29421,"uid":501,"version":"v20.19.3"},"rejection":true,"service":"payment-gateway","stack":"MongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-07-27 10:05:53.510","trace":[{"column":38,"file":"/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js","function":"Timeout._onTimeout","line":278,"method":"_onTimeout","native":false},{"column":17,"file":"node:internal/timers","function":"listOnTimeout","line":581,"method":null,"native":false},{"column":7,"file":"node:internal/timers","function":"process.processTimers","line":519,"method":"processTimers","native":false}]}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 10:05:53.511"}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 10:05:53.511"}
{"date":"Sun Jul 27 2025 10:05:54 GMT+0530 (India Standard Time)","error":{"reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"}},"level":"error","message":"unhandledRejection: getaddrinfo ENOTFOUND mongo\nMongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","os":{"loadavg":[19.1728515625,78.09130859375,89.58056640625],"uptime":4029},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","/Users/<USER>/Documents/payment-main/backend/server.js"],"cwd":"/Users/<USER>/Documents/payment-main/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","gid":20,"memoryUsage":{"arrayBuffers":18280314,"external":20517722,"heapTotal":32264192,"heapUsed":31144752,"rss":73912320},"pid":29421,"uid":501,"version":"v20.19.3"},"rejection":true,"service":"payment-gateway","stack":"MongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-07-27 10:05:54.305","trace":[{"column":38,"file":"/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js","function":"Timeout._onTimeout","line":278,"method":"_onTimeout","native":false},{"column":17,"file":"node:internal/timers","function":"listOnTimeout","line":581,"method":null,"native":false},{"column":7,"file":"node:internal/timers","function":"process.processTimers","line":519,"method":"processTimers","native":false}]}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 10:05:54.306"}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 10:05:54.306"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-27 10:07:42.300"}
{"date":"Sun Jul 27 2025 10:09:23 GMT+0530 (India Standard Time)","error":{"reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"}},"level":"error","message":"unhandledRejection: getaddrinfo ENOTFOUND mongo\nMongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","os":{"loadavg":[57.22900390625,58.36181640625,77.8779296875],"uptime":4238},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","/Users/<USER>/Documents/payment-main/backend/server.js"],"cwd":"/Users/<USER>/Documents/payment-main/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","gid":20,"memoryUsage":{"arrayBuffers":18271696,"external":20533526,"heapTotal":32264192,"heapUsed":30752280,"rss":74940416},"pid":31462,"uid":501,"version":"v20.19.3"},"rejection":true,"service":"payment-gateway","stack":"MongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-07-27 10:09:23.703","trace":[{"column":38,"file":"/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js","function":"Timeout._onTimeout","line":278,"method":"_onTimeout","native":false},{"column":17,"file":"node:internal/timers","function":"listOnTimeout","line":581,"method":null,"native":false},{"column":7,"file":"node:internal/timers","function":"process.processTimers","line":519,"method":"processTimers","native":false}]}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 10:09:23.704"}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 10:09:23.705"}
{"date":"Sun Jul 27 2025 10:09:25 GMT+0530 (India Standard Time)","error":{"reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"}},"level":"error","message":"unhandledRejection: getaddrinfo ENOTFOUND mongo\nMongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","os":{"loadavg":[57.22900390625,58.36181640625,77.8779296875],"uptime":4240},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","/Users/<USER>/Documents/payment-main/backend/server.js"],"cwd":"/Users/<USER>/Documents/payment-main/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","gid":20,"memoryUsage":{"arrayBuffers":18280538,"external":20542408,"heapTotal":32264192,"heapUsed":31093256,"rss":75300864},"pid":31462,"uid":501,"version":"v20.19.3"},"rejection":true,"service":"payment-gateway","stack":"MongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-07-27 10:09:25.704","trace":[{"column":38,"file":"/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js","function":"Timeout._onTimeout","line":278,"method":"_onTimeout","native":false},{"column":17,"file":"node:internal/timers","function":"listOnTimeout","line":581,"method":null,"native":false},{"column":7,"file":"node:internal/timers","function":"process.processTimers","line":519,"method":"processTimers","native":false}]}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 10:09:25.706"}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 10:09:25.710"}
{"ip":"127.0.0.1","level":"error","message":"401","method":"GET","path":"/api/api/auth/me","service":"payment-gateway","stack":"ApiError: 401\n    at auth (/Users/<USER>/Documents/payment-main/backend/middleware/auth.js:49:19)\n    at /Users/<USER>/Documents/payment-main/backend/app.js:212:7\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at logger (/Users/<USER>/Documents/payment-main/backend/node_modules/morgan/index.js:144:5)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)","statusCode":"No session ID found","timestamp":"2025-07-27 10:11:35.269","user":"anonymous"}
{"ip":"127.0.0.1","level":"error","message":"401","method":"GET","path":"/favicon.ico","service":"payment-gateway","stack":"ApiError: 401\n    at auth (/Users/<USER>/Documents/payment-main/backend/middleware/auth.js:49:19)\n    at /Users/<USER>/Documents/payment-main/backend/app.js:212:7\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at logger (/Users/<USER>/Documents/payment-main/backend/node_modules/morgan/index.js:144:5)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)","statusCode":"No session ID found","timestamp":"2025-07-27 10:11:35.464","user":"anonymous"}
{"ip":"127.0.0.1","level":"error","message":"401","method":"GET","path":"/logo192.png","service":"payment-gateway","stack":"ApiError: 401\n    at auth (/Users/<USER>/Documents/payment-main/backend/middleware/auth.js:49:19)\n    at /Users/<USER>/Documents/payment-main/backend/app.js:212:7\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at logger (/Users/<USER>/Documents/payment-main/backend/node_modules/morgan/index.js:144:5)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)","statusCode":"No session ID found","timestamp":"2025-07-27 10:11:35.470","user":"anonymous"}
{"ip":"127.0.0.1","level":"error","message":"401","method":"GET","path":"/favicon.ico","service":"payment-gateway","stack":"ApiError: 401\n    at auth (/Users/<USER>/Documents/payment-main/backend/middleware/auth.js:49:19)\n    at /Users/<USER>/Documents/payment-main/backend/app.js:212:7\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at logger (/Users/<USER>/Documents/payment-main/backend/node_modules/morgan/index.js:144:5)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)","statusCode":"No session ID found","timestamp":"2025-07-27 10:11:41.543","user":"anonymous"}
{"ip":"127.0.0.1","level":"error","message":"401","method":"GET","path":"/favicon.ico","service":"payment-gateway","stack":"ApiError: 401\n    at auth (/Users/<USER>/Documents/payment-main/backend/middleware/auth.js:49:19)\n    at /Users/<USER>/Documents/payment-main/backend/app.js:212:7\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at logger (/Users/<USER>/Documents/payment-main/backend/node_modules/morgan/index.js:144:5)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)","statusCode":"No session ID found","timestamp":"2025-07-27 10:11:55.440","user":"anonymous"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-27 10:12:12.409"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-27 10:14:34.238"}
{"level":"error","message":"Could not close connections in time, forcefully shutting down","service":"payment-gateway","timestamp":"2025-07-27 10:14:38.084"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-27 10:14:43.545"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-27 10:15:00.147"}
{"date":"Sun Jul 27 2025 10:15:02 GMT+0530 (India Standard Time)","error":{"reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"}},"level":"error","message":"unhandledRejection: getaddrinfo ENOTFOUND mongo\nMongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","os":{"loadavg":[104.8134765625,61.64892578125,71.43701171875],"uptime":4577},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","/Users/<USER>/Documents/payment-main/backend/server.js"],"cwd":"/Users/<USER>/Documents/payment-main/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","gid":20,"memoryUsage":{"arrayBuffers":18271744,"external":20533574,"heapTotal":32526336,"heapUsed":30649680,"rss":67526656},"pid":32390,"uid":501,"version":"v20.19.3"},"rejection":true,"service":"payment-gateway","stack":"MongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-07-27 10:15:02.078","trace":[{"column":38,"file":"/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js","function":"Timeout._onTimeout","line":278,"method":"_onTimeout","native":false},{"column":17,"file":"node:internal/timers","function":"listOnTimeout","line":581,"method":null,"native":false},{"column":7,"file":"node:internal/timers","function":"process.processTimers","line":519,"method":"processTimers","native":false}]}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 10:15:02.079"}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 10:15:02.079"}
{"date":"Sun Jul 27 2025 10:15:04 GMT+0530 (India Standard Time)","error":{"reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"}},"level":"error","message":"unhandledRejection: getaddrinfo ENOTFOUND mongo\nMongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","os":{"loadavg":[110.193359375,63.48046875,72.02587890625],"uptime":4579},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","/Users/<USER>/Documents/payment-main/backend/server.js"],"cwd":"/Users/<USER>/Documents/payment-main/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","gid":20,"memoryUsage":{"arrayBuffers":18272394,"external":20534264,"heapTotal":32526336,"heapUsed":31056376,"rss":67948544},"pid":32390,"uid":501,"version":"v20.19.3"},"rejection":true,"service":"payment-gateway","stack":"MongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-07-27 10:15:04.147","trace":[{"column":38,"file":"/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js","function":"Timeout._onTimeout","line":278,"method":"_onTimeout","native":false},{"column":17,"file":"node:internal/timers","function":"listOnTimeout","line":581,"method":null,"native":false},{"column":7,"file":"node:internal/timers","function":"process.processTimers","line":519,"method":"processTimers","native":false}]}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 10:15:04.148"}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 10:15:04.149"}
{"ip":"127.0.0.1","level":"error","message":"401","method":"GET","path":"/api/api/auth/me","service":"payment-gateway","stack":"ApiError: 401\n    at auth (/Users/<USER>/Documents/payment-main/backend/middleware/auth.js:49:19)\n    at /Users/<USER>/Documents/payment-main/backend/app.js:213:7\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at logger (/Users/<USER>/Documents/payment-main/backend/node_modules/morgan/index.js:144:5)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)","statusCode":"No session ID found","timestamp":"2025-07-27 10:17:28.422","user":"anonymous"}
{"ip":"127.0.0.1","level":"error","message":"CORS policy does not allow access from: http://localhost:5000","method":"GET","path":"/@vite/client","service":"payment-gateway","stack":"Error: CORS policy does not allow access from: http://localhost:5000\n    at origin (/Users/<USER>/Documents/payment-main/backend/app.js:88:25)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/cors/lib/index.js:219:13\n    at optionsCallback (/Users/<USER>/Documents/payment-main/backend/node_modules/cors/lib/index.js:199:9)\n    at corsMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/cors/lib/index.js:204:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:537:6)","statusCode":500,"timestamp":"2025-07-27 10:17:28.463","user":"anonymous"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-27 10:27:04.177"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-27 10:27:16.079"}
{"date":"Sun Jul 27 2025 10:28:20 GMT+0530 (India Standard Time)","error":{"reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"}},"level":"error","message":"unhandledRejection: getaddrinfo ENOTFOUND mongo\nMongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","os":{"loadavg":[49.56103515625,88.9677734375,96.162109375],"uptime":5375},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","/Users/<USER>/Documents/payment-main/backend/server.js"],"cwd":"/Users/<USER>/Documents/payment-main/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","gid":20,"memoryUsage":{"arrayBuffers":18272937,"external":20536143,"heapTotal":71847936,"heapUsed":32813624,"rss":112529408},"pid":35835,"uid":501,"version":"v20.19.3"},"rejection":true,"service":"payment-gateway","stack":"MongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-07-27 10:28:20.635","trace":[{"column":38,"file":"/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js","function":"Timeout._onTimeout","line":278,"method":"_onTimeout","native":false},{"column":17,"file":"node:internal/timers","function":"listOnTimeout","line":581,"method":null,"native":false},{"column":7,"file":"node:internal/timers","function":"process.processTimers","line":519,"method":"processTimers","native":false}]}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 10:28:20.639"}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 10:28:20.640"}
{"date":"Sun Jul 27 2025 10:28:22 GMT+0530 (India Standard Time)","error":{"reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"}},"level":"error","message":"unhandledRejection: getaddrinfo ENOTFOUND mongo\nMongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","os":{"loadavg":[49.56103515625,88.9677734375,96.162109375],"uptime":5377},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","/Users/<USER>/Documents/payment-main/backend/server.js"],"cwd":"/Users/<USER>/Documents/payment-main/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","gid":20,"memoryUsage":{"arrayBuffers":18281779,"external":20545025,"heapTotal":71847936,"heapUsed":33151768,"rss":112537600},"pid":35835,"uid":501,"version":"v20.19.3"},"rejection":true,"service":"payment-gateway","stack":"MongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-07-27 10:28:22.089","trace":[{"column":38,"file":"/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js","function":"Timeout._onTimeout","line":278,"method":"_onTimeout","native":false},{"column":17,"file":"node:internal/timers","function":"listOnTimeout","line":581,"method":null,"native":false},{"column":7,"file":"node:internal/timers","function":"process.processTimers","line":519,"method":"processTimers","native":false}]}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 10:28:22.090"}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 10:28:22.090"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-27 10:28:27.298"}
{"level":"error","message":"Port 5000 is already in use","service":"payment-gateway","timestamp":"2025-07-27 10:30:35.544"}
{"date":"Sun Jul 27 2025 10:30:35 GMT+0530 (India Standard Time)","error":{"reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"}},"level":"error","message":"unhandledRejection: getaddrinfo ENOTFOUND mongo\nMongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","os":{"loadavg":[68.83740234375,79.76513671875,91.220703125],"uptime":5510},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","/Users/<USER>/Documents/payment-main/backend/server.js"],"cwd":"/Users/<USER>/Documents/payment-main/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","gid":20,"memoryUsage":{"arrayBuffers":18279696,"external":20517690,"heapTotal":33050624,"heapUsed":30566896,"rss":77115392},"pid":37993,"uid":501,"version":"v20.19.3"},"rejection":true,"service":"payment-gateway","stack":"MongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-07-27 10:30:35.655","trace":[{"column":38,"file":"/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js","function":"Timeout._onTimeout","line":278,"method":"_onTimeout","native":false},{"column":17,"file":"node:internal/timers","function":"listOnTimeout","line":581,"method":null,"native":false},{"column":7,"file":"node:internal/timers","function":"process.processTimers","line":519,"method":"processTimers","native":false}]}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 10:30:35.656"}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 10:30:35.656"}
{"date":"Sun Jul 27 2025 10:30:36 GMT+0530 (India Standard Time)","error":{"reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"}},"level":"error","message":"unhandledRejection: getaddrinfo ENOTFOUND mongo\nMongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","os":{"loadavg":[68.83740234375,79.76513671875,91.220703125],"uptime":5511},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","/Users/<USER>/Documents/payment-main/backend/server.js"],"cwd":"/Users/<USER>/Documents/payment-main/backend","execPath":"/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node","gid":20,"memoryUsage":{"arrayBuffers":18288538,"external":20525946,"heapTotal":33312768,"heapUsed":30920192,"rss":77451264},"pid":37993,"uid":501,"version":"v20.19.3"},"rejection":true,"service":"payment-gateway","stack":"MongoServerSelectionError: getaddrinfo ENOTFOUND mongo\n    at Timeout._onTimeout (/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js:278:38)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-07-27 10:30:36.965","trace":[{"column":38,"file":"/Users/<USER>/Documents/payment-main/backend/node_modules/mongodb/lib/sdam/topology.js","function":"Timeout._onTimeout","line":278,"method":"_onTimeout","native":false},{"column":17,"file":"node:internal/timers","function":"listOnTimeout","line":581,"method":null,"native":false},{"column":7,"file":"node:internal/timers","function":"process.processTimers","line":519,"method":"processTimers","native":false}]}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 10:30:36.965"}
{"0":"r","1":"e","2":"a","3":"s","4":"o","5":"n","6":":","level":"error","message":"Unhandled Rejection at:","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"payment-gateway","timestamp":"2025-07-27 10:30:36.966"}
