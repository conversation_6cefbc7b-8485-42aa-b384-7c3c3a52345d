const mongoose = require('mongoose');

const traderAssignmentSchema = new mongoose.Schema({
  traderId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  merchantId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  assignedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  assignmentType: {
    type: String,
    enum: ['permanent', 'temporary', 'project_based'],
    default: 'permanent'
  },
  startDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  endDate: {
    type: Date
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'suspended', 'completed'],
    default: 'active'
  },
  collectionTarget: {
    amount: Number,
    period: {
      type: String,
      enum: ['daily', 'weekly', 'monthly', 'quarterly']
    }
  },
  permissions: [{
    type: String,
    enum: ['view_transactions', 'process_payments', 'generate_reports', 'contact_customers']
  }],
  performanceMetrics: {
    totalCollected: { type: Number, default: 0 },
    successfulTransactions: { type: Number, default: 0 },
    failedTransactions: { type: Number, default: 0 },
    averageCollectionTime: Number, // in hours
    customerSatisfactionRating: Number
  },
  notes: String,
  isActive: { type: Boolean, default: true }
}, {
  timestamps: true
});

// Index for efficient queries
traderAssignmentSchema.index({ traderId: 1, merchantId: 1 });
traderAssignmentSchema.index({ status: 1, startDate: 1 });

module.exports = mongoose.model('TraderAssignment', traderAssignmentSchema);