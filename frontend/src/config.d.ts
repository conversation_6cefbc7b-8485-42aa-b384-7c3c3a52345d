// Type definitions for config module
declare module '../config' {
  export const API_BASE_URL: string;
  export const DEFAULT_PAGE_SIZE: number;
  export const DEFAULT_PAGE: number;
  export const DATE_FORMAT: string;
  export const DATE_TIME_FORMAT: string;
  
  export const ROLES: {
    ADMIN: string;
    TRADER: string;
    MERCHANT: string;
  };
  
  export interface SelectOption {
    value: string;
    label: string;
  }
  
  export const ASSIGNMENT_TYPES: SelectOption[];
  export const COLLECTION_PERIODS: SelectOption[];
  
  export interface PermissionOption {
    id: string;
    label: string;
  }
  
  export const PERMISSIONS: PermissionOption[];
  export const STATUS_OPTIONS: SelectOption[];
}
