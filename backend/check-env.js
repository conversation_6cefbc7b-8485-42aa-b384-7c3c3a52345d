const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '.env') });

console.log('=== Environment Variables ===');
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('MONGODB_URI:', process.env.MONGODB_URI ? '***' : 'Not set');
console.log('JWT_SECRET:', process.env.JWT_SECRET ? '***' : 'Not set');
console.log('JWT_EXPIRES_IN:', process.env.JWT_EXPIRES_IN);
console.log('COOKIE_SECRET:', process.env.COOKIE_SECRET ? '***' : 'Not set');
console.log('FRONTEND_URL:', process.env.FRONTEND_URL);
console.log('PORT:', process.env.PORT);
