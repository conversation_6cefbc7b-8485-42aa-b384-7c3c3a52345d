const httpStatus = require('http-status');
const accepts = require('accepts');
const { ApiError } = require('../utils/ApiError');
const logger = require('../utils/logger');

// Supported content types
const SUPPORTED_FORMATS = {
  json: 'application/json',
  xml: 'application/xml',
  form: 'application/x-www-form-urlencoded',
  text: 'text/plain',
  html: 'text/html',
  csv: 'text/csv',
  pdf: 'application/pdf',  
  png: 'image/png',
  jpeg: 'image/jpeg',
  jpg: 'image/jpeg',
  gif: 'image/gif',
  webp: 'image/webp',
  svg: 'image/svg+xml',
};

// Default format if none specified
const DEFAULT_FORMAT = 'json';

/**
 * Middleware to handle content negotiation
 * @param {Object} options - Configuration options
 * @returns {Function} Express middleware function
 */
const contentNegotiation = (options = {}) => {
  const {
    // Supported formats (default: json, xml, html, text)
    formats = ['json', 'xml', 'html', 'text'],
    
    // Default format if none specified
    defaultFormat = DEFAULT_FORMAT,
    
    // Whether to fail if requested format is not supported
    failOnUnsupported = true,
    
    // Whether to set Vary header
    vary = true,
  } = options;
  
  // Validate formats
  const supportedFormats = formats.filter(format => SUPPORTED_FORMATS[format]);
  
  if (supportedFormats.length === 0) {
    throw new Error('At least one supported format must be specified');
  }
  
  // Get content type for a format
  const getContentType = (format) => {
    return SUPPORTED_FORMATS[format] || SUPPORTED_FORMATS[defaultFormat];
  };
  
  // Get format from content type
  const getFormatFromContentType = (contentType) => {
    if (!contentType) return defaultFormat;
    
    const [type] = contentType.split(';');
    const format = Object.entries(SUPPORTED_FORMATS).find(
      ([_, value]) => value === type
    );
    
    return format ? format[0] : defaultFormat;
  };
  
  return (req, res, next) => {
    try {
      const accept = accepts(req);
      
      // Get the best matching content type
      const contentType = accept.types(supportedFormats.map(f => SUPPORTED_FORMATS[f]));
      
      // Determine the format
      let format = defaultFormat;
      
      if (contentType) {
        format = getFormatFromContentType(contentType);
      } else if (req.query.format && supportedFormats.includes(req.query.format)) {
        // Allow format to be specified in query parameter
        format = req.query.format;
      } else if (req.accepts(supportedFormats)) {
        format = req.accepts(supportedFormats) || defaultFormat;
      }
      
      // Check if the requested format is supported
      if (!supportedFormats.includes(format)) {
        if (failOnUnsupported) {
          throw new ApiError(
            httpStatus.NOT_ACCEPTABLE,
            `Unsupported content type. Supported types: ${supportedFormats.join(', ')}`
          );
        }
        format = defaultFormat;
      }
      
      // Set response content type
      res.type(getContentType(format));
      
      // Set Vary header to indicate content negotiation is in use
      if (vary) {
        res.vary('Accept');
      }
      
      // Add format to request object for use in controllers
      req.acceptedFormat = format;
      
      // Add response format method
      res.formatResponse = (data, statusCode = httpStatus.OK) => {
        switch (format) {
          case 'xml':
            return res.status(statusCode).send(convertToXml(data));
          case 'html':
            return res.status(statusCode).send(convertToHtml(data));
          case 'text':
            return res.status(statusCode).send(convertToText(data));
          case 'csv':
            res.attachment('data.csv');
            return res.status(statusCode).send(convertToCsv(data));
          case 'json':
          default:
            return res.status(statusCode).json(data);
        }
      };
      
      next();
    } catch (error) {
      next(error);
    }
  };
};

// Helper function to convert data to XML (simplified example)
function convertToXml(data) {
  // This is a simplified example - in a real app, use a proper XML serializer
  if (typeof data === 'string') return data;
  
  const toXml = (obj) => {
    if (Array.isArray(obj)) {
      return obj.map(item => `<item>${toXml(item)}</item>`).join('');
    } else if (typeof obj === 'object' && obj !== null) {
      return Object.entries(obj)
        .map(([key, value]) => `<${key}>${toXml(value)}</${key}>`)
        .join('');
    }
    return String(obj).replace(/[<>&'"]/g, (c) => ({
      '<': '&lt;',
      '>': '&gt;',
      '&': '&amp;',
      "'": '&apos;',
      '"': '&quot;',
    }[c]));
  };
  
  return `<?xml version="1.0" encoding="UTF-8"?>\n<response>${toXml(data)}</response>`;
}

// Helper function to convert data to HTML (simplified example)
function convertToHtml(data) {
  if (typeof data === 'string') return data;
  
  const toHtml = (obj, level = 0) => {
    const indent = '  '.repeat(level);
    
    if (Array.isArray(obj)) {
      const items = obj.map(item => `\n${indent}  <li>${toHtml(item, level + 1)}</li>`).join('');
      return `<ul>${items}\n${indent}</ul>`;
    } else if (typeof obj === 'object' && obj !== null) {
      const items = Object.entries(obj)
        .map(([key, value]) => `\n${indent}  <dt>${key}</dt><dd>${toHtml(value, level + 1)}</dd>`)
        .join('');
      return `<dl>${items}\n${indent}</dl>`;
    }
    
    return String(obj)
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/\n/g, '<br>');
  };
  
  return `
    <!DOCTYPE html>
    <html>
      <head>
        <title>API Response</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; margin: 20px; }
          dl { margin: 0; padding: 0; }
          dt { font-weight: bold; margin-top: 10px; }
          dd { margin: 0 0 10px 20px; }
          ul { margin: 0; padding-left: 20px; }
        </style>
      </head>
      <body>
        ${toHtml(data)}
      </body>
    </html>
  `;
}

// Helper function to convert data to plain text (simplified example)
function convertToText(data) {
  if (typeof data === 'string') return data;
  
  const toText = (obj, level = 0) => {
    const indent = '  '.repeat(level);
    
    if (Array.isArray(obj)) {
      return obj.map((item, i) => {
        const bullet = level === 0 ? `${i + 1}.` : '-';
        return `${indent}${bullet} ${toText(item, level + 1)}`;
      }).join('\n');
    } else if (typeof obj === 'object' && obj !== null) {
      return Object.entries(obj)
        .map(([key, value]) => `${indent}${key}: ${toText(value, level + 1)}`)
        .join('\n');
    }
    
    return String(obj);
  };
  
  return toText(data);
}

// Helper function to convert data to CSV (simplified example)
function convertToCsv(data) {
  if (typeof data === 'string') return data;
  
  const escapeCsv = (value) => {
    if (value === null || value === undefined) return '';
    const str = String(value);
    if (str.includes(',') || str.includes('"') || str.includes('\n')) {
      return `"${str.replace(/"/g, '""')}"`;
    }
    return str;
  };
  
  if (Array.isArray(data)) {
    if (data.length === 0) return '';
    
    // Get all unique keys from all objects
    const allKeys = [...new Set(data.flatMap(Object.keys))];
    
    // Generate CSV header
    const header = allKeys.join(',');
    
    // Generate CSV rows
    const rows = data.map(item => {
      return allKeys
        .map(key => escapeCsv(item[key]))
        .join(',');
    });
    
    return [header, ...rows].join('\n');
  } else if (typeof data === 'object' && data !== null) {
    // Single object
    const keys = Object.keys(data);
    const header = keys.join(',');
    const row = keys.map(key => escapeCsv(data[key])).join(',');
    return [header, row].join('\n');
  }
  
  return String(data);
}

module.exports = {
  contentNegotiation,
  SUPPORTED_FORMATS,
  DEFAULT_FORMAT,
};
