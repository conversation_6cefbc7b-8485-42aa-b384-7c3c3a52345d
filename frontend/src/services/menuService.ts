// Menu service for dynamic navigation system
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

export interface MenuItem {
  _id: string;
  menuId: string;
  label: string;
  to: string;
  icon: string;
  role: 'admin' | 'merchant' | 'trader' | 'all';
  parentId?: string;
  order: number;
  isActive: boolean;
  permissions: string[];
  description?: string;
  badge?: {
    text: string;
    color: 'red' | 'blue' | 'green' | 'yellow' | 'purple' | 'gray';
  };
  metadata?: {
    category: string;
    tags: string[];
    isNew: boolean;
    isBeta: boolean;
  };
  submenus?: MenuItem[];
  createdAt: string;
  updatedAt: string;
}

export interface MenuApiResponse {
  success: boolean;
  data: MenuItem[];
  meta?: {
    total: number;
    mainMenus: number;
    subMenus: number;
  };
  message?: string;
}

class MenuService {
  private async fetchApi<T>(endpoint: string, options?: RequestInit): Promise<T> {
    try {
      const token = localStorage.getItem('token');
      const headers = {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
        ...options?.headers,
      };

      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        headers,
        credentials: 'include',
        ...options,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Menu API request failed:', error);
      throw error;
    }
  }

  // Get all menus
  async getAllMenus(includeInactive = false): Promise<MenuApiResponse> {
    const params = new URLSearchParams();
    if (includeInactive) params.append('includeInactive', 'true');
    
    const queryString = params.toString();
    const endpoint = `/menus${queryString ? `?${queryString}` : ''}`;
    
    return this.fetchApi<MenuApiResponse>(endpoint);
  }

  // Get menus by role
  async getMenuByRole(role: 'admin' | 'merchant' | 'trader' | 'all'): Promise<MenuItem[]> {
    try {
      const data = await this.fetchApi<MenuApiResponse>(`/menu/${role}`);
      if (!data.success) {
        console.error('Failed to fetch menu:', data.message);
        return [];
      }
      return data.data || [];
    } catch (error) {
      console.error('Failed to fetch menu:', error);
      return [];
    }
  }

  // Get menu by ID
  async getMenuById(id: string): Promise<{ success: boolean; data: MenuItem }> {
    return this.fetchApi<{ success: boolean; data: MenuItem }>(`/menus/${id}`);
  }

  // Create new menu
  async createMenu(menuData: Partial<MenuItem>): Promise<{ success: boolean; data: MenuItem; message: string }> {
    return this.fetchApi<{ success: boolean; data: MenuItem; message: string }>('/menus', {
      method: 'POST',
      body: JSON.stringify(menuData),
    });
  }

  // Update menu
  async updateMenu(id: string, menuData: Partial<MenuItem>): Promise<{ success: boolean; data: MenuItem; message: string }> {
    return this.fetchApi<{ success: boolean; data: MenuItem; message: string }>(`/menus/${id}`, {
      method: 'PUT',
      body: JSON.stringify(menuData),
    });
  }

  // Delete menu
  async deleteMenu(id: string): Promise<{ success: boolean; message: string }> {
    return this.fetchApi<{ success: boolean; message: string }>(`/menus/${id}`, {
      method: 'DELETE',
    });
  }

  // Bulk create menus
  async bulkCreateMenus(menus: Partial<MenuItem>[]): Promise<{ success: boolean; data: MenuItem[]; message: string }> {
    return this.fetchApi<{ success: boolean; data: MenuItem[]; message: string }>('/menus/bulk', {
      method: 'POST',
      body: JSON.stringify({ menus }),
    });
  }

  // Reorder menus
  async reorderMenus(menuOrders: { id: string; order: number }[]): Promise<{ success: boolean; message: string }> {
    return this.fetchApi<{ success: boolean; message: string }>('/menus/reorder', {
      method: 'PUT',
      body: JSON.stringify({ menuOrders }),
    });
  }
}

// Create and export a singleton instance
export const menuService = new MenuService();

// Mock menu data for fallback when API is not available
export const mockMenuData: Record<string, MenuItem[]> = {
  admin: [
    {
      _id: 'mock-admin-1',
      menuId: 'admin-dashboard',
      label: 'Dashboard',
      to: '/admin',
      icon: 'LayoutDashboard',
      role: 'admin',
      order: 1,
      isActive: true,
      permissions: [],
      description: 'Admin dashboard with overview and analytics',
      metadata: { category: 'overview', tags: [], isNew: false, isBeta: false },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      _id: 'mock-admin-2',
      menuId: 'admin-users',
      label: 'User Management',
      to: '/admin/users',
      icon: 'Users',
      role: 'admin',
      order: 2,
      isActive: true,
      permissions: [],
      description: 'Manage all system users',
      metadata: { category: 'management', tags: [], isNew: false, isBeta: false },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      _id: 'mock-admin-3',
      menuId: 'admin-trader-assignments',
      label: 'Trader Assignments',
      to: '/admin/trader-assignments',
      icon: 'UserPlus',
      role: 'admin',
      order: 5,
      isActive: true,
      permissions: [],
      description: 'Manage trader-merchant assignments',
      metadata: { category: 'operations', tags: [], isNew: false, isBeta: false },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  ],
  merchant: [
    {
      _id: 'mock-merchant-1',
      menuId: 'merchant-dashboard',
      label: 'Dashboard',
      to: '/merchant',
      icon: 'LayoutDashboard',
      role: 'merchant',
      order: 1,
      isActive: true,
      permissions: [],
      description: 'Merchant dashboard overview',
      metadata: { category: 'overview', tags: [], isNew: false, isBeta: false },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      _id: 'mock-merchant-2',
      menuId: 'merchant-transactions',
      label: 'My Transactions',
      to: '/merchant/transactions',
      icon: 'CreditCard',
      role: 'merchant',
      order: 2,
      isActive: true,
      permissions: [],
      description: 'View your payment transactions',
      metadata: { category: 'financial', tags: [], isNew: false, isBeta: false },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  ],
  trader: [
    {
      _id: 'mock-trader-1',
      menuId: 'trader-dashboard',
      label: 'Dashboard',
      to: '/trader',
      icon: 'LayoutDashboard',
      role: 'trader',
      order: 1,
      isActive: true,
      permissions: [],
      description: 'Trader dashboard overview',
      metadata: { category: 'overview', tags: [], isNew: false, isBeta: false },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      _id: 'mock-trader-2',
      menuId: 'trader-assignments',
      label: 'My Assignments',
      to: '/trader/assignments',
      icon: 'UserCheck',
      role: 'trader',
      order: 2,
      isActive: true,
      permissions: [],
      description: 'View your merchant assignments',
      metadata: { category: 'operations', tags: [], isNew: false, isBeta: false },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  ]
};
