import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '../../contexts/AuthContext';
import { traderService, Assignment, AssignmentStatus, AssignmentType } from '../../services/traderService';
import { format } from 'date-fns';

// UI Components
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Badge } from '../../components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/ui/table';
import { Skeleton } from '../../components/ui/skeleton';
import { useToast } from '../../components/ui/use-toast';
import { Building2, DollarSign, Users, Calendar, CheckCircle2, AlertCircle, Clock } from 'lucide-react';

// Custom Tabs implementation with TypeScript
interface TabContextType {
  activeTab: string;
  setActiveTab: (value: string) => void;
}

const TabContext = React.createContext<TabContextType | undefined>(undefined);

const Tabs = ({ 
  children, 
  value, 
  onValueChange, 
  className = ''
}: { 
  children: React.ReactNode; 
  value: string; 
  onValueChange: (value: string) => void; 
  className?: string;
}) => {
  return (
    <TabContext.Provider value={{ activeTab: value, setActiveTab: onValueChange }}>
      <div className={className}>
        {children}
      </div>
    </TabContext.Provider>
  );
};

const TabsList = ({ 
  children, 
  className = '' 
}: { 
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <div className={`flex border-b ${className}`}>
      {children}
    </div>
  );
};

const TabsTrigger = ({ 
  children, 
  value,
  className = ''
}: { 
  children: React.ReactNode;
  value: string;
  className?: string;
}) => {
  const context = React.useContext(TabContext);
  if (!context) throw new Error('TabsTrigger must be used within a Tabs component');
  
  const { activeTab, setActiveTab } = context;
  const isActive = activeTab === value;
  
  return (
    <button 
      onClick={() => setActiveTab(value)}
      className={`px-4 py-2 font-medium text-sm ${
        isActive 
          ? 'border-b-2 border-blue-500 text-blue-600' 
          : 'text-gray-500 hover:text-gray-700'
      } ${className}`}
    >
      {children}
    </button>
  );
};

const TabsContent = ({ 
  children, 
  value,
  className = '' 
}: { 
  children: React.ReactNode; 
  value: string;
  className?: string;
}) => {
  const context = React.useContext(TabContext);
  if (!context) throw new Error('TabsContent must be used within a Tabs component');
  
  const { activeTab } = context;
  
  return (
    <div className={`${value === activeTab ? 'block' : 'hidden'} ${className}`}>
      {children}
    </div>
  );
};

// Define types for the assignment data
interface AssignmentWithDetails extends Omit<Assignment, 'merchant'> {
  _id: string;
  merchant: {
    _id: string;
    businessName: string;
  };
  collectionTarget?: {
    amount: number;
    period: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
    currency: string;
  };
  startDate: string;
  endDate?: string;
  status: AssignmentStatus;
  assignmentType: AssignmentType;
}

// Type for the API response
type AssignmentsResponse = {
  data: AssignmentWithDetails[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
};

export function TraderDashboard() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');

  // Fetch trader's assignments with proper typing
  const { data: assignments = [], isLoading, error } = useQuery<Assignment[]>({
    queryKey: ['traderAssignments'],
    queryFn: () => traderService.getMyAssignments(),
  });

  // Filter assignments by status with proper typing
  const activeAssignments = assignments.filter((a: Assignment) => a.status === AssignmentStatus.ACTIVE);
  const pendingAssignments = assignments.filter((a: Assignment) => a.status === AssignmentStatus.PENDING);
  const completedAssignments = assignments.filter((a: Assignment) => a.status === AssignmentStatus.COMPLETED);

  // Calculate stats
  const totalCollectionTarget = activeAssignments.reduce((sum: number, assignment: Assignment) => {
    const amount = (assignment as any).collectionTarget?.amount;
    return amount ? sum + Number(amount) : sum;
  }, 0);

  const stats = [
    {
      name: 'Active Assignments',
      value: activeAssignments.length,
      icon: Building2,
      description: 'Merchants you are currently assigned to',
    },
    {
      name: 'Total Collection Target',
      value: `$${totalCollectionTarget.toLocaleString()}`,
      icon: DollarSign,
      description: 'Your current collection target',
    },
    {
      name: 'Pending Approvals',
      value: pendingAssignments.length,
      icon: AlertCircle,
      description: 'Assignments awaiting approval',
    },
    {
      name: 'Completed',
      value: completedAssignments.length,
      icon: CheckCircle2,
      description: 'Successfully completed assignments',
    },
  ];

  const renderStatusBadge = (status: AssignmentStatus) => {
    switch (status) {
      case AssignmentStatus.ACTIVE:
        return <Badge className="bg-green-100 text-green-800">Active</Badge>;
      case AssignmentStatus.PENDING:
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case AssignmentStatus.COMPLETED:
        return <Badge className="bg-blue-100 text-blue-800">Completed</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[1, 2, 3, 4].map((i) => (
            <Skeleton key={i} className="h-[110px] w-full rounded-xl" />
          ))}
        </div>
        <Skeleton className="h-[400px] w-full rounded-xl" />
      </div>
    );
  }

  if (error) {
    return <div>Error loading dashboard data</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Welcome back, {user?.name || 'Trader'}</h1>
          <p className="text-muted-foreground">Here's what's happening with your merchant assignments</p>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{stat.name}</CardTitle>
              <stat.icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-muted-foreground">{stat.description}</p>
            </CardContent>
          </Card>
        ))}
      </div>

      <Tabs 
        value={activeTab}
        onValueChange={setActiveTab}
        className="space-y-4"
      >  
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="merchants">My Merchants ({activeAssignments.length})</TabsTrigger>
          <TabsTrigger value="tasks">Tasks</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Active Assignments</CardTitle>
              <CardDescription>Your current merchant assignments and their status</CardDescription>
            </CardHeader>
            <CardContent>
              {activeAssignments.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Merchant</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Start Date</TableHead>
                      <TableHead>End Date</TableHead>
                      <TableHead>Collection Target</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {activeAssignments.map((assignment) => (
                      <TableRow key={assignment._id}>
                        <TableCell className="font-medium">
                          {assignment.merchant?.businessName || 'N/A'}
                        </TableCell>
                        <TableCell>{assignment.assignmentType}</TableCell>
                        <TableCell>{renderStatusBadge(assignment.status)}</TableCell>
                        <TableCell>{format(new Date(assignment.startDate), 'MMM d, yyyy')}</TableCell>
                        <TableCell>
                          {assignment.endDate ? format(new Date(assignment.endDate), 'MMM d, yyyy') : 'N/A'}
                        </TableCell>
                        <TableCell>
                          {assignment.collectionTarget 
                            ? `$${assignment.collectionTarget.amount} / ${assignment.collectionTarget.period}`
                            : 'Not set'}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <p>No active assignments found</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="merchants" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>My Merchants</CardTitle>
              <CardDescription>All your merchant assignments</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Merchant</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Period</TableHead>
                    <TableHead>Collection Target</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {assignments.map((assignment) => (
                    <TableRow key={assignment._id}>
                      <TableCell className="font-medium">
                        {assignment.merchant?.businessName || 'N/A'}
                      </TableCell>
                      <TableCell>{renderStatusBadge(assignment.status)}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span>
                            {format(new Date(assignment.startDate), 'MMM d')} -{' '}
                            {assignment.endDate
                              ? format(new Date(assignment.endDate), 'MMM d, yyyy')
                              : 'Ongoing'}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {assignment.collectionTarget ? (
                          <div className="flex items-center gap-1">
                            <DollarSign className="h-4 w-4 text-green-500" />
                            <span>
                              ${assignment.collectionTarget.amount} / {assignment.collectionTarget.period}
                            </span>
                          </div>
                        ) : (
                          'Not set'
                        )}
                      </TableCell>
                      <TableCell>
                        <Button variant="outline" size="sm">
                          View Details
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tasks" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Pending Tasks</CardTitle>
              <CardDescription>Your upcoming collection tasks</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {activeAssignments.length > 0 ? (
                  activeAssignments.map((assignment) => (
                    <Card key={assignment._id} className="border-l-4 border-primary">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-medium">
                              Collect payment from {assignment.merchant?.businessName}
                            </h4>
                            <p className="text-sm text-muted-foreground">
                              {assignment.collectionTarget
                                ? `Target: $${assignment.collectionTarget.amount} / ${assignment.collectionTarget.period}`
                                : 'No specific target set'}
                            </p>
                            <div className="mt-2 flex items-center gap-2 text-sm text-muted-foreground">
                              <Clock className="h-4 w-4" />
                              <span>Due in 3 days</span>
                            </div>
                          </div>
                          <Button size="sm">Mark as Complete</Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <p>No pending tasks found</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default TraderDashboard;
