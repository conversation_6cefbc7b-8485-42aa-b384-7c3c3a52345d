#!/bin/bash

# MongoDB Backup Script
# Usage: ./backup-mongodb.sh [output_directory]

# Default output directory
OUTPUT_DIR=${1:-$(pwd)/mongodb_backup_$(date +%Y%m%d_%H%M%S)}

# Create output directory if it doesn't exist
mkdir -p "$OUTPUT_DIR"

# Get current timestamp
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Backup filename
BACKUP_FILE="$OUTPUT_DIR/mongodb_backup_$TIMESTAMP"

# MongoDB connection string (update this with your actual connection string)
# Format: mongodb://[username:password@]host1[:port1][,...hostN[:portN]]][/[database][?options]]
MONGO_URI="mongodb://localhost:27017"  # Update this with your actual MongoDB URI

# Database name (update this with your actual database name)
DB_NAME="payment_gateway"  # Update this with your actual database name

echo "Starting MongoDB backup..."
echo "Output directory: $OUTPUT_DIR"
echo "Database: $DB_NAME"

# Run mongodump
mongodump --uri="$MONGO_URI" --db="$DB_NAME" --out="$BACKUP_FILE" --gzip

# Check if mongodump was successful
if [ $? -eq 0 ]; then
    echo "MongoDB backup completed successfully!"
    echo "Backup saved to: $BACKUP_FILE"
    
    # Create a README file with restore instructions
    cat > "$OUTPUT_DIR/README_RESTORE.md" <<EOL
# MongoDB Restore Instructions

To restore this backup, use the following command:

```bash
mongorestore --uri="YOUR_MONGO_URI" --db="$DB_NAME" --gzip "$BACKUP_FILE/$DB_NAME"
```

Replace `YOUR_MONGO_URI` with your MongoDB connection string.

## Example:
```bash
mongorestore --uri="mongodb://localhost:27017" --db="$DB_NAME" --gzip "$BACKUP_FILE/$DB_NAME"
```
EOL
    
    echo "Restore instructions saved to: $OUTPUT_DIR/README_RESTORE.md"
else
    echo "Error: MongoDB backup failed!"
    exit 1
fi
