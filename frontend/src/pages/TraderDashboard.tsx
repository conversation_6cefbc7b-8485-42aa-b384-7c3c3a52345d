import React, { useState, useEffect } from "react"
import { useAuth } from "../contexts/AuthContext"
import { useNavigate } from "react-router-dom"
import { format } from "date-fns"

// UI Components
import { Button } from "../components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "../components/ui/card"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "../components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "../components/ui/table"
import { Badge } from "../components/ui/badge"
import { Skeleton } from "../components/ui/skeleton"
import { useToast } from "../components/ui/use-toast"

// Icons
import { 
  DollarSign, 
  CreditCard, 
  TrendingUp, 
  RefreshCw, 
  ArrowUpRight, 
  ArrowDownRight,
  Activity,
  Copy,
  BarChart2,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON>hartI<PERSON>,
  <PERSON><PERSON><PERSON>,
  Filter,
  Download,
  Plus,
  ArrowRight,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  ChevronDown,
  ChevronUp,
  ExternalLink
} from "lucide-react"

// Charts
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, Legend } from "recharts"

// Types
interface Trade {
  id: string;
  pair: string;
  type: 'buy' | 'sell';
  price: number;
  amount: number;
  total: number;
  fee: number;
  status: 'completed' | 'pending' | 'canceled';
  date: string;
  time?: string; // Add optional time property for display
}

interface Balance {
  asset: string;
  free: string;
  used: string;
  total: string;
  btcValue: number;
  usdValue: number;
}

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api/v1';

const TraderDashboard: React.FC = () => {
  const { user, logout } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [timeframe, setTimeframe] = useState('24h');
  const [showBalances, setShowBalances] = useState(true);
  
  // Stats interface
  interface StatItem {
    name: string;
    value: string | number;
    change?: number;
    icon: any; // You might want to replace 'any' with the correct icon type
  }

  // Mock data - replace with actual API calls
  // Using const instead of state since we're not updating these values
  const stats = {
    totalVolume: 12345.67,
    totalTrades: 1234,
    activePairs: 24,
    dailyVolume: 1234.56,
    dailyChange: -2.3,
    pnl: 1234.56,
    pnlPercent: 12.5,
    winRate: 67.8,
    avgTradeSize: 456.78,
  };

  // Stats cards data
  const statsCards: StatItem[] = [
    {
      name: 'Total Volume',
      value: `$${stats.totalVolume.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`,
      change: stats.dailyChange,
      icon: DollarSign
    },
    {
      name: 'Total Trades',
      value: stats.totalTrades.toLocaleString(),
      icon: Activity
    },
    {
      name: 'Active Pairs',
      value: stats.activePairs,
      icon: BarChart2
    },
    {
      name: 'Daily P&L',
      value: `$${stats.pnl.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`,
      change: stats.pnlPercent,
      icon: TrendingUp
    }
  ];
  
  const recentTrades: Trade[] = [
    { 
      id: 'TRD123', 
      pair: 'BTC/USD', 
      type: 'buy', 
      price: 42123.45, 
      amount: 0.5, 
      total: 21061.73, 
      fee: 25, 
      status: 'completed', 
      date: new Date().toISOString(),
      time: '10:23:45' 
    },
    { 
      id: 'TRD124', 
      pair: 'ETH/USD', 
      type: 'sell', 
      price: 2345.67, 
      amount: 2.1, 
      total: 4925.91, 
      fee: 12.31, 
      status: 'completed', 
      date: new Date().toISOString(),
      time: '09:45:12' 
    },
    { 
      id: 'TRD125', 
      pair: 'SOL/USD', 
      type: 'buy', 
      price: 123.45, 
      amount: 50, 
      total: 6172.50, 
      fee: 15.43, 
      status: 'pending', 
      date: new Date().toISOString(),
      time: '08:12:33' 
    }
  ];
  
  const balances: Balance[] = [
    { asset: 'BTC', free: '0.5', used: '0.1', total: '0.6', btcValue: 0.6, usdValue: 30000 },
    { asset: 'ETH', free: '5', used: '1.5', total: '6.5', btcValue: 0.5, usdValue: 25000 },
    { asset: 'USDT', free: '10000', used: '2000', total: '12000', btcValue: 0.24, usdValue: 12000 },
  ];
  
  const priceData: {name: string; price: number}[] = [
    { name: '9:00', price: 40000 },
    { name: '10:00', price: 40500 },
    { name: '11:00', price: 39800 },
    { name: '12:00', price: 41000 },
    { name: '13:00', price: 41500 },
    { name: '14:00', price: 41200 },
    { name: '15:00', price: 41800 }
  ];

  // Format currency
  const formatCurrency = (amount: number, currency: string = 'USD'): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string, formatStr: string = 'MMM dd, yyyy HH:mm'): string => {
    return format(new Date(dateString), formatStr);
  };

  // Copy text to clipboard
  const copyToClipboard = (text: string, message: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: 'Copied!',
      description: message,
      variant: 'default',
    });
  };

  // Fetch dashboard data - using mock data for now
  const fetchDashboardData = async () => {
    try {
      // Simulate API call with mock data
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log('Fetched dashboard data');
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    }
  };

  const handleLogout = () => {
    logout()
    navigate('/login')
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8 flex justify-between items-center">
          <h1 className="text-3xl font-bold text-gray-900">Trader Dashboard</h1>
          <div className="flex items-center space-x-4">
            <div className="text-right">
              <p className="text-sm text-gray-500">Connected as</p>
              <p className="font-medium">{user?.email}</p>
            </div>
            <Button onClick={handleLogout} variant="outline">
              Logout
            </Button>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {/* Stats */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-8">
          {statsCards.map((stat) => {
            const Icon = stat.icon;
            return (
              <Card key={stat.name}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    {stat.name}
                  </CardTitle>
                  <Icon className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stat.value}</div>
                  {stat.change !== undefined && (
                    <p className={`text-xs ${stat.change >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                      {stat.change >= 0 ? '+' : ''}{stat.change}% from yesterday
                    </p>
                  )}
                </CardContent>
              </Card>
            );
          })}
        </div>

        <div className="grid gap-4 md:grid-cols-3">
          <div className="md:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>BTC/USD Price</CardTitle>
              </CardHeader>
              <CardContent className="pl-2">
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={priceData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis domain={['auto', 'auto']} />
                      <Tooltip />
                      <Line 
                        type="monotone" 
                        dataKey="price" 
                        stroke="#8884d8" 
                        strokeWidth={2}
                        dot={false}
                        activeDot={{ r: 6 }} 
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
          
          <div>
            <Card>
              <CardHeader>
                <CardTitle>Recent Trades</CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Pair</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Price</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Total</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {recentTrades.map((trade) => (
                      <TableRow key={trade.id}>
                        <TableCell className="font-medium">{trade.pair}</TableCell>
                        <TableCell className={trade.type.toLowerCase() === 'buy' ? 'text-green-500' : 'text-red-500'}>
                          {trade.type.charAt(0).toUpperCase() + trade.type.slice(1)}
                        </TableCell>
                        <TableCell>{trade.price}</TableCell>
                        <TableCell>{trade.amount}</TableCell>
                        <TableCell>{trade.total}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </div>
        </div>

        <div className="mt-8
        ">
          <Tabs defaultValue="open-orders" className="space-y-4">
            <TabsList>
              <TabsTrigger value="open-orders">Open Orders</TabsTrigger>
              <TabsTrigger value="order-history">Order History</TabsTrigger>
              <TabsTrigger value="positions">Positions</TabsTrigger>
            </TabsList>
            
            <TabsContent value="open-orders">
              <Card>
                <CardHeader>
                  <CardTitle>Open Orders</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-500 text-center py-8">No open orders</p>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="order-history">
              <Card>
                <CardHeader>
                  <CardTitle>Order History</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-500 text-center py-8">No order history available</p>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="positions">
              <Card>
                <CardHeader>
                  <CardTitle>Positions</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-500 text-center py-8">No open positions</p>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  )
}

export default TraderDashboard;
