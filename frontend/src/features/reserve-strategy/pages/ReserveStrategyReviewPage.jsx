import React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardHeader,
  Container,
  Divider,
  Grid,
  Paper,
  Typography,
  useTheme,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Gavel as GavelIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Flag as FlagIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import PageHeader from '../../../components/layout/PageHeader';
import ReserveStrategyReviewPanel from '../components/ReserveStrategyReviewPanel';

const ReserveStrategyReviewPage = () => {
  const theme = useTheme();
  const navigate = useNavigate();

  return (
    <Container maxWidth="xl">
      <PageHeader
        title="Reserve Strategy Activity Review"
        subtitle="Review and approve reserve strategy activities"
        breadcrumbs={[
          { label: 'Dashboard', to: '/dashboard' },
          { label: 'Reserve Strategies', to: '/reserve-strategies' },
          { label: 'Activity Review' },
        ]}
        action={
          <Button
            variant="outlined"
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate(-1)}
          >
            Back
          </Button>
        }
      />
      
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={4}>
          <Card>
            <CardHeader 
              title="Review Process" 
              avatar={
                <GavelIcon color="primary" />
              }
            />
            <CardContent>
              <Typography variant="body2" color="textSecondary" paragraph>
                Review and approve or flag reserve strategy activities. This helps maintain the integrity of the reserve management system.
              </Typography>
              
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <CheckCircleIcon color="success" sx={{ mr: 1 }} />
                <Box>
                  <Typography variant="subtitle2">Approve</Typography>
                  <Typography variant="caption" color="textSecondary">
                    Approve standard activities that follow policies
                  </Typography>
                </Box>
              </Box>
              
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <FlagIcon color="warning" sx={{ mr: 1 }} />
                <Box>
                  <Typography variant="subtitle2">Flag for Review</Typography>
                  <Typography variant="caption" color="textSecondary">
                    Flag unusual or suspicious activities
                  </Typography>
                </Box>
              </Box>
              
              <Divider sx={{ my: 2 }} />
              
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <WarningIcon color="warning" sx={{ mr: 1 }} />
                <Typography variant="caption" color="textSecondary">
                  All actions are logged for audit purposes
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={8}>
          <ReserveStrategyReviewPanel />
        </Grid>
      </Grid>
    </Container>
  );
};

export default ReserveStrategyReviewPage;
