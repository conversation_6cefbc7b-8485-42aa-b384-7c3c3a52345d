"use client"

import React, { useState } from "react"
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom"
import { useAuth } from "../contexts/AuthContext"
import { Button } from "../components/ui/button"
import { Input } from "../components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "../components/ui/card"
import { Label } from "../components/ui/label"
import { useToast } from "../hooks/use-toast"
import { Eye, EyeOff, CreditCard, User as UserIcon, Store, Briefcase } from "lucide-react"
import type { User } from "../contexts/AuthContext"

const Login: React.FC = () => {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { login, loading: authLoading } = useAuth()
  const navigate = useNavigate()
  const { toast } = useToast()
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>): Promise<void> => {
    e.preventDefault()
    setIsSubmitting(true)
    setError(null)

    try {
      await login(email, password)
      toast({
        title: "Login successful",
        description: `Welcome back, ${email.split('@')[0]}!`,
      })
      
      // Get user role from the auth context after successful login
      // Note: In a real app, you'd get this from the login response or user context
      // Get user role from login response or determine based on email
      const userRole = email === '<EMAIL>' ? 'admin' :
                      email === '<EMAIL>' ? 'merchant' : 'trader'
      
      // Navigate based on role
      navigate(`/${userRole}`)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred'
      setError(errorMessage)
      toast({
        title: "Login failed",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <CreditCard className="h-12 w-12 text-blue-600" />
          </div>
          <CardTitle className="text-2xl font-bold">PayGateway</CardTitle>
          <CardDescription>Sign in to your account</CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="Enter your email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  placeholder="Enter your password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex flex-col space-y-4">
            <Button 
              type="submit" 
              className="w-full" 
              disabled={isSubmitting || authLoading}
            >
              {isSubmitting || authLoading ? "Signing in..." : "Sign In"}
            </Button>
            <div className="w-full border-t border-gray-200 my-4"></div>
            <div className="w-full space-y-2">
              <p className="text-sm text-center text-gray-600 mb-2">Or sign in with a demo account:</p>
              
              <Button 
                type="button" 
                variant="outline" 
                className="w-full flex items-center justify-start gap-2"
                onClick={() => {
                  setEmail('<EMAIL>')
                  setPassword('password123')
                  setError(null)
                }}
                disabled={isSubmitting || authLoading}
              >
                <UserIcon className="h-4 w-4 text-blue-600" />
                Admin Account
              </Button>
              
              <Button 
                type="button" 
                variant="outline" 
                className="w-full flex items-center justify-start gap-2"
                onClick={() => {
                  setEmail('<EMAIL>')
                  setPassword('password123')
                  setError(null)
                }}
                disabled={isSubmitting || authLoading}
              >
                <Store className="h-4 w-4 text-green-600" />
                Merchant Account
              </Button>
              
              <Button 
                type="button" 
                variant="outline" 
                className="w-full flex items-center justify-start gap-2"
                onClick={() => {
                  setEmail('<EMAIL>')
                  setPassword('password123')
                  setError(null)
                }}
                disabled={isSubmitting || authLoading}
              >
                <Briefcase className="h-4 w-4 text-purple-600" />
                Trader Account
              </Button>
            </div>
            
            <p className="text-sm text-center text-gray-600 mt-4">
              {"Don't have an account? "}
              <Link to="/register" className="text-blue-600 hover:underline">
                Sign up
              </Link>
            </p>
          </CardFooter>
        </form>
      </Card>
    </div>
  )
}

export default Login
