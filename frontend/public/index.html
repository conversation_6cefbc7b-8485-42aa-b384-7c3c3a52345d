<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="Secure Payment Gateway System - Process payments safely and efficiently" />
    <meta name="keywords" content="payment gateway, secure payments, online transactions, merchant services" />
    <meta name="author" content="PayGateway Team" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://paygateway.com/" />
    <meta property="og:title" content="PayGateway - Secure Payment Processing" />
    <meta property="og:description" content="Process payments safely and efficiently with our secure payment gateway system" />
    <meta property="og:image" content="%PUBLIC_URL%/og-image.png" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://paygateway.com/" />
    <meta property="twitter:title" content="PayGateway - Secure Payment Processing" />
    <meta property="twitter:description" content="Process payments safely and efficiently with our secure payment gateway system" />
    <meta property="twitter:image" content="%PUBLIC_URL%/twitter-image.png" />

    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    
    <!-- Preconnect to external domains for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Security headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    <meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin">
    
    <title>PayGateway - Secure Payment Processing</title>
    
    <!-- Loading styles -->
    <style>
      #loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #f8fafc;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #e2e8f0;
        border-top: 4px solid #3b82f6;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .loading-text {
        margin-top: 16px;
        color: #64748b;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
      }
    </style>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    
    <!-- Loading screen -->
    <div id="loading">
      <div>
        <div class="loading-spinner"></div>
        <div class="loading-text">Loading PayGateway...</div>
      </div>
    </div>
    
    <div id="root"></div>
    
    <!-- Remove loading screen when React loads -->
    <script>
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loading = document.getElementById('loading');
          if (loading) {
            loading.style.display = 'none';
          }
        }, 1000);
      });
    </script>
  </body>
</html>
