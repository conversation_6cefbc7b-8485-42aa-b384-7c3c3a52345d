// Load environment variables before running tests
const path = require('path');
const dotenv = require('dotenv');

// Load environment variables from .env.test
const envPath = path.resolve(__dirname, '../.env.test');
dotenv.config({ path: envPath });

// Set default values for required environment variables
process.env.NODE_ENV = process.env.NODE_ENV || 'test';
process.env.MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/payment_gateway_test';
process.env.COOKIE_SECRET = process.env.COOKIE_SECRET || 'test_cookie_secret_123';
process.env.FRONTEND_URL = process.env.FRONTEND_URL || 'http://localhost:3000';
process.env.API_VERSION = process.env.API_VERSION || 'v1';
process.env.SESSION_SECRET = process.env.SESSION_SECRET || 'test_session_secret_123';

// Log environment for debugging
console.log('Test environment variables:');
console.log(`NODE_ENV: ${process.env.NODE_ENV}`);
console.log(`MONGODB_URI: ${process.env.MONGODB_URI}`);
console.log(`COOKIE_SECRET: ${process.env.COOKIE_SECRET ? '***' : 'not set'}`);
console.log(`FRONTEND_URL: ${process.env.FRONTEND_URL}`);
console.log(`API_VERSION: ${process.env.API_VERSION}`);
console.log(`SESSION_SECRET: ${process.env.SESSION_SECRET ? '***' : 'not set'}`);
