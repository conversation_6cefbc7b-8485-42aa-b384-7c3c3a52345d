import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../../components/ui/dropdown-menu';
import { Badge } from '../../components/ui/badge';
import { MoreHorizontal, Search, Loader2, Plus, Filter, Users } from 'lucide-react';

interface Merchant {
  _id: string;
  name: string;
  email: string;
  businessName?: string;
  businessType?: string;
  phone?: string;
  website?: string;
  isActive: boolean;
  isVerified: boolean;
  role: string;
  createdAt: string;
  updatedAt: string;
}

export default function MerchantsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [merchants, setMerchants] = useState<Merchant[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchMerchants = async () => {
      try {
        setLoading(true);

        const response = await fetch('http://localhost:5000/api/users');
        const data = await response.json();

        if (data.success) {
          const merchantUsers = data.data.filter((user: any) => user.role === 'merchant');
          setMerchants(merchantUsers);
        } else {
          throw new Error('Failed to fetch merchants');
        }
      } catch (err) {
        console.log('API not available, using mock data');
        setMerchants([
          {
            _id: '1',
            name: 'John Doe',
            email: '<EMAIL>',
            businessName: 'Tech Solutions Inc',
            businessType: 'Technology',
            phone: '+1234567890',
            website: 'https://techsolutions.com',
            isActive: true,
            isVerified: true,
            role: 'merchant',
            createdAt: '2025-07-26T19:53:01.095Z',
            updatedAt: '2025-07-26T19:53:01.095Z',
          },
          {
            _id: '2',
            name: 'Jane Smith',
            email: '<EMAIL>',
            businessName: 'Fashion Store',
            businessType: 'Retail',
            phone: '+1234567891',
            website: 'https://fashionstore.com',
            isActive: true,
            isVerified: true,
            role: 'merchant',
            createdAt: '2025-07-26T19:53:01.711Z',
            updatedAt: '2025-07-26T19:53:01.711Z',
          },
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchMerchants();
  }, []);

  const filteredMerchants = merchants.filter(merchant =>
    merchant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    merchant.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (merchant.businessName && merchant.businessName.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const handleStatusToggle = (merchantId: string) => {
    setMerchants(prev => prev.map(merchant =>
      merchant._id === merchantId
        ? { ...merchant, isActive: !merchant.isActive }
        : merchant
    ));
  };

  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading merchants...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">Merchants Management</h1>
            <p className="text-muted-foreground">
              Manage and monitor merchant accounts
            </p>
          </div>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Add Merchant
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Merchants</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{merchants.length}</div>
              <p className="text-xs text-muted-foreground">
                Active merchant accounts
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active</CardTitle>
              <Badge variant="default" className="text-xs">Live</Badge>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {merchants.filter(m => m.isActive).length}
              </div>
              <p className="text-xs text-muted-foreground">
                Currently active
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Verified</CardTitle>
              <Badge variant="default" className="text-xs">✓</Badge>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {merchants.filter(m => m.isVerified).length}
              </div>
              <p className="text-xs text-muted-foreground">
                Verified accounts
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">This Month</CardTitle>
              <Badge variant="secondary" className="text-xs">New</Badge>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">2</div>
              <p className="text-xs text-muted-foreground">
                New registrations
              </p>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Merchants List</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2 mb-4">
              <div className="relative flex-1">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search merchants..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
              <Button variant="outline">
                <Filter className="mr-2 h-4 w-4" />
                Filter
              </Button>
            </div>

            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Business</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Verified</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredMerchants.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8">
                        No merchants found
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredMerchants.map((merchant) => (
                      <TableRow key={merchant._id}>
                        <TableCell className="font-medium">{merchant.name}</TableCell>
                        <TableCell>{merchant.email}</TableCell>
                        <TableCell>{merchant.businessName || 'N/A'}</TableCell>
                        <TableCell>{merchant.businessType || 'N/A'}</TableCell>
                        <TableCell>
                          <Badge variant={merchant.isActive ? 'default' : 'outline'}>
                            {merchant.isActive ? 'Active' : 'Inactive'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={merchant.isVerified ? 'default' : 'secondary'}>
                            {merchant.isVerified ? 'Verified' : 'Pending'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {new Date(merchant.createdAt).toLocaleDateString()}
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem>View Details</DropdownMenuItem>
                              <DropdownMenuItem>Edit</DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => handleStatusToggle(merchant._id)}
                              >
                                {merchant.isActive ? 'Deactivate' : 'Activate'}
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}