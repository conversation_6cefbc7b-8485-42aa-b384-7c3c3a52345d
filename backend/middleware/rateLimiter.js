const rateLimit = require('express-rate-limit');

// Rate limiting configuration
const createRateLimiter = (windowMs = 15 * 60 * 1000, // 15 minutes
  max = 100, // limit each IP to 100 requests per windowMs
  message = 'Too many requests from this IP, please try again later') => {
  return rateLimit({
    windowMs,
    max,
    message: { success: false, message },
    standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders: false, // Disable the `X-RateLimit-*` headers
    skip: (req) => {
      // Skip for trusted IPs
      const trustedIPs = ['127.0.0.1', '***********']; // Add your trusted IPs here
      return trustedIPs.includes(req.ip);
      // Or skip for admin users: return req.user && req.user.role === 'admin';
    }
  });
};

// Specific rate limiters
const authLimiter = createRateLimiter(15 * 60 * 1000, 5, 'Too many login attempts, please try again later');
const passwordResetLimiter = createRateLimiter(60 * 60 * 1000, 3, 'Too many password reset attempts, please try again later');
const apiLimiter = createRateLimiter(15 * 60 * 1000, 100);

module.exports = {
  createRateLimiter,
  authLimiter,
  passwordResetLimiter,
  apiLimiter
};
