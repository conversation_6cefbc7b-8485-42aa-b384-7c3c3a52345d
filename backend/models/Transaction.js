const mongoose = require("mongoose")

const transactionSchema = new mongoose.Schema(
  {
    transactionId: {
      type: String,
      required: true,
      unique: true,
    },
    merchantId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    traderId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
    amount: {
      type: Number,
      required: true,
      min: 0.01,
    },
    currency: {
      type: String,
      required: true,
      default: "USD",
    },
    status: {
      type: String,
      enum: ["pending", "processing", "completed", "failed", "cancelled", "refunded"],
      default: "pending",
    },
    paymentMethod: {
      type: String,
      enum: ["card", "bank_transfer", "digital_wallet", "crypto"],
      required: true,
    },
    paymentDetails: {
      cardLast4: String,
      cardBrand: String,
      bankName: String,
      walletType: String,
    },
    customerInfo: {
      email: {
        type: String,
        required: true,
      },
      name: String,
      phone: String,
      billingAddress: {
        street: String,
        city: String,
        state: String,
        zipCode: String,
        country: String,
      },
    },
    fees: {
      processingFee: {
        type: Number,
        default: 0,
      },
      platformFee: {
        type: Number,
        default: 0,
      },
    },
    netAmount: Number,
    description: String,
    metadata: mongoose.Schema.Types.Mixed,
    webhookDelivered: {
      type: Boolean,
      default: false,
    },
    webhookAttempts: {
      type: Number,
      default: 0,
    },
    refundAmount: {
      type: Number,
      default: 0,
    },
    refundReason: String,
    disputeStatus: {
      type: String,
      enum: ["none", "pending", "resolved", "lost"],
      default: "none",
    },
    riskScore: {
      type: Number,
      min: 0,
      max: 100,
    },
    ipAddress: String,
    userAgent: String,
    processingTime: Number, // in milliseconds
    externalTransactionId: String, // ID from payment processor
    settlementDate: Date,
    notes: [String],
  },
  {
    timestamps: true,
  },
)

// Indexes for performance
transactionSchema.index({ merchantId: 1, createdAt: -1 })
transactionSchema.index({ status: 1 })
transactionSchema.index({ transactionId: 1 })
transactionSchema.index({ "customerInfo.email": 1 })

// Pre-save middleware to calculate net amount
transactionSchema.pre("save", function (next) {
  if (this.isModified("amount") || this.isModified("fees")) {
    this.netAmount = this.amount - (this.fees.processingFee + this.fees.platformFee)
  }
  next()
})

// Method to generate unique transaction ID
transactionSchema.statics.generateTransactionId = () => {
  const crypto = require("crypto")
  return "txn_" + crypto.randomBytes(16).toString("hex")
}

// Method to calculate risk score
transactionSchema.methods.calculateRiskScore = function () {
  let score = 0

  // Amount-based risk
  if (this.amount > 1000) score += 20
  if (this.amount > 5000) score += 30

  // New customer risk
  if (!this.traderId) score += 15

  // Payment method risk
  if (this.paymentMethod === "crypto") score += 25

  // Geographic risk (simplified)
  if (this.customerInfo.billingAddress?.country !== "US") score += 10

  this.riskScore = Math.min(score, 100)
  return this.riskScore
}

module.exports = mongoose.model("Transaction", transactionSchema)
