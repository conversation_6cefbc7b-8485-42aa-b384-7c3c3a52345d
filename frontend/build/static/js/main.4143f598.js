/*! For license information please see main.4143f598.js.LICENSE.txt */
(()=>{var e={191:(e,t)=>{"use strict";var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler");Symbol.for("react.provider");var l=Symbol.for("react.consumer"),s=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.suspense_list"),f=Symbol.for("react.memo"),p=Symbol.for("react.lazy"),h=Symbol.for("react.view_transition"),m=Symbol.for("react.client.reference");function g(e){if("object"===typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case o:case i:case a:case c:case d:case h:return e;default:switch(e=e&&e.$$typeof){case s:case u:case p:case f:case l:return e;default:return t}}case r:return t}}}t.vM=u,t.lD=f},219:(e,t,n)=>{"use strict";var r=n(3763),o={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},a={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},i={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},l={};function s(e){return r.isMemo(e)?i:l[e.$$typeof]||o}l[r.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},l[r.Memo]=i;var u=Object.defineProperty,c=Object.getOwnPropertyNames,d=Object.getOwnPropertySymbols,f=Object.getOwnPropertyDescriptor,p=Object.getPrototypeOf,h=Object.prototype;e.exports=function e(t,n,r){if("string"!==typeof n){if(h){var o=p(n);o&&o!==h&&e(t,o,r)}var i=c(n);d&&(i=i.concat(d(n)));for(var l=s(t),m=s(n),g=0;g<i.length;++g){var v=i[g];if(!a[v]&&(!r||!r[v])&&(!m||!m[v])&&(!l||!l[v])){var y=f(n,v);try{u(t,v,y)}catch(b){}}}}return t}},457:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>r.A});var r=n(7868)},503:(e,t,n)=>{"use strict";n.d(t,{N:()=>o});var r=n(5043),o=globalThis?.document?r.useLayoutEffect:()=>{}},579:(e,t,n)=>{"use strict";e.exports=n(1153)},858:(e,t,n)=>{"use strict";function r(e,t){let{checkForDefaultPrevented:n=!0}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}n.d(t,{m:()=>r})},869:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});n(5043);var r=n(3290),o=n(579);function a(e){const{styles:t,defaultTheme:n={}}=e,a="function"===typeof t?e=>{return t(void 0===(r=e)||null===r||0===Object.keys(r).length?n:e);var r}:t;return(0,o.jsx)(r.mL,{styles:a})}},917:(e,t,n)=>{"use strict";n.d(t,{F:()=>i});var r=n(8387);const o=e=>"boolean"===typeof e?`${e}`:0===e?"0":e,a=r.$,i=(e,t)=>n=>{var r;if(null==(null===t||void 0===t?void 0:t.variants))return a(e,null===n||void 0===n?void 0:n.class,null===n||void 0===n?void 0:n.className);const{variants:i,defaultVariants:l}=t,s=Object.keys(i).map(e=>{const t=null===n||void 0===n?void 0:n[e],r=null===l||void 0===l?void 0:l[e];if(null===t)return null;const a=o(t)||o(r);return i[e][a]}),u=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{}),c=null===t||void 0===t||null===(r=t.compoundVariants)||void 0===r?void 0:r.reduce((e,t)=>{let{class:n,className:r,...o}=t;return Object.entries(o).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...l,...u}[t]):{...l,...u}[t]===n})?[...e,n,r]:e},[]);return a(e,s,c,null===n||void 0===n?void 0:n.class,null===n||void 0===n?void 0:n.className)}},918:(e,t,n)=>{"use strict";function r(e){var t=Object.create(null);return function(n){return void 0===t[n]&&(t[n]=e(n)),t[n]}}n.d(t,{A:()=>r})},1153:(e,t,n)=>{"use strict";var r=n(5043),o=Symbol.for("react.element"),a=Symbol.for("react.fragment"),i=Object.prototype.hasOwnProperty,l=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,s={key:!0,ref:!0,__self:!0,__source:!0};function u(e,t,n){var r,a={},u=null,c=null;for(r in void 0!==n&&(u=""+n),void 0!==t.key&&(u=""+t.key),void 0!==t.ref&&(c=t.ref),t)i.call(t,r)&&!s.hasOwnProperty(r)&&(a[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===a[r]&&(a[r]=t[r]);return{$$typeof:o,type:e,key:u,ref:c,props:a,_owner:l.current}}t.Fragment=a,t.jsx=u,t.jsxs=u},1172:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});const r=(0,n(3797).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},1184:(e,t,n)=>{"use strict";n.d(t,{lg:()=>b,qW:()=>h,bL:()=>y});var r=n(5043),o=n(858),a=n(7920),i=n(2814),l=n(7490);var s,u=n(579),c="dismissableLayer.update",d="dismissableLayer.pointerDownOutside",f="dismissableLayer.focusOutside",p=r.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),h=r.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:h,onPointerDownOutside:m,onFocusOutside:y,onInteractOutside:b,onDismiss:w,...x}=e,S=r.useContext(p),[k,E]=r.useState(null),A=k?.ownerDocument??globalThis?.document,[,C]=r.useState({}),P=(0,i.s)(t,e=>E(e)),R=Array.from(S.layers),[O]=[...S.layersWithOutsidePointerEventsDisabled].slice(-1),T=R.indexOf(O),N=k?R.indexOf(k):-1,j=S.layersWithOutsidePointerEventsDisabled.size>0,M=N>=T,_=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:globalThis?.document;const n=(0,l.c)(e),o=r.useRef(!1),a=r.useRef(()=>{});return r.useEffect(()=>{const e=e=>{if(e.target&&!o.current){let r=function(){v(d,n,o,{discrete:!0})};const o={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",a.current),a.current=r,t.addEventListener("click",a.current,{once:!0})):r()}else t.removeEventListener("click",a.current);o.current=!1},r=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(r),t.removeEventListener("pointerdown",e),t.removeEventListener("click",a.current)}},[t,n]),{onPointerDownCapture:()=>o.current=!0}}(e=>{const t=e.target,n=[...S.branches].some(e=>e.contains(t));M&&!n&&(m?.(e),b?.(e),e.defaultPrevented||w?.())},A),L=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:globalThis?.document;const n=(0,l.c)(e),o=r.useRef(!1);return r.useEffect(()=>{const e=e=>{if(e.target&&!o.current){v(f,n,{originalEvent:e},{discrete:!1})}};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{const t=e.target;[...S.branches].some(e=>e.contains(t))||(y?.(e),b?.(e),e.defaultPrevented||w?.())},A);return function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:globalThis?.document;const n=(0,l.c)(e);r.useEffect(()=>{const e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{N===S.layers.size-1&&(h?.(e),!e.defaultPrevented&&w&&(e.preventDefault(),w()))},A),r.useEffect(()=>{if(k)return n&&(0===S.layersWithOutsidePointerEventsDisabled.size&&(s=A.body.style.pointerEvents,A.body.style.pointerEvents="none"),S.layersWithOutsidePointerEventsDisabled.add(k)),S.layers.add(k),g(),()=>{n&&1===S.layersWithOutsidePointerEventsDisabled.size&&(A.body.style.pointerEvents=s)}},[k,A,n,S]),r.useEffect(()=>()=>{k&&(S.layers.delete(k),S.layersWithOutsidePointerEventsDisabled.delete(k),g())},[k,S]),r.useEffect(()=>{const e=()=>C({});return document.addEventListener(c,e),()=>document.removeEventListener(c,e)},[]),(0,u.jsx)(a.sG.div,{...x,ref:P,style:{pointerEvents:j?M?"auto":"none":void 0,...e.style},onFocusCapture:(0,o.m)(e.onFocusCapture,L.onFocusCapture),onBlurCapture:(0,o.m)(e.onBlurCapture,L.onBlurCapture),onPointerDownCapture:(0,o.m)(e.onPointerDownCapture,_.onPointerDownCapture)})});h.displayName="DismissableLayer";var m=r.forwardRef((e,t)=>{const n=r.useContext(p),o=r.useRef(null),l=(0,i.s)(t,o);return r.useEffect(()=>{const e=o.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,u.jsx)(a.sG.div,{...e,ref:l})});function g(){const e=new CustomEvent(c);document.dispatchEvent(e)}function v(e,t,n,r){let{discrete:o}=r;const i=n.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?(0,a.hO)(i,l):i.dispatchEvent(l)}m.displayName="DismissableLayerBranch";var y=h,b=m},1387:(e,t,n)=>{"use strict";function r(){return r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},r.apply(this,arguments)}var o;n.d(t,{AO:()=>d,Gh:()=>_,HS:()=>L,Oi:()=>l,Rr:()=>f,pX:()=>F,pb:()=>T,rc:()=>o,tH:()=>$,ue:()=>m,yD:()=>M,zR:()=>i}),function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"}(o||(o={}));const a="popstate";function i(e){return void 0===e&&(e={}),p(function(e,t){let{pathname:n,search:r,hash:o}=e.location;return c("",{pathname:n,search:r,hash:o},t.state&&t.state.usr||null,t.state&&t.state.key||"default")},function(e,t){return"string"===typeof t?t:d(t)},null,e)}function l(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}function s(e,t){if(!e){"undefined"!==typeof console&&console.warn(t);try{throw new Error(t)}catch(n){}}}function u(e,t){return{usr:e.state,key:e.key,idx:t}}function c(e,t,n,o){return void 0===n&&(n=null),r({pathname:"string"===typeof e?e:e.pathname,search:"",hash:""},"string"===typeof t?f(t):t,{state:n,key:t&&t.key||o||Math.random().toString(36).substr(2,8)})}function d(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(t+="#"===r.charAt(0)?r:"#"+r),t}function f(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function p(e,t,n,i){void 0===i&&(i={});let{window:s=document.defaultView,v5Compat:f=!1}=i,p=s.history,h=o.Pop,m=null,g=v();function v(){return(p.state||{idx:null}).idx}function y(){h=o.Pop;let e=v(),t=null==e?null:e-g;g=e,m&&m({action:h,location:w.location,delta:t})}function b(e){let t="null"!==s.location.origin?s.location.origin:s.location.href,n="string"===typeof e?e:d(e);return n=n.replace(/ $/,"%20"),l(t,"No window.location.(origin|href) available to create URL for href: "+n),new URL(n,t)}null==g&&(g=0,p.replaceState(r({},p.state,{idx:g}),""));let w={get action(){return h},get location(){return e(s,p)},listen(e){if(m)throw new Error("A history only accepts one active listener");return s.addEventListener(a,y),m=e,()=>{s.removeEventListener(a,y),m=null}},createHref:e=>t(s,e),createURL:b,encodeLocation(e){let t=b(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){h=o.Push;let r=c(w.location,e,t);n&&n(r,e),g=v()+1;let a=u(r,g),i=w.createHref(r);try{p.pushState(a,"",i)}catch(l){if(l instanceof DOMException&&"DataCloneError"===l.name)throw l;s.location.assign(i)}f&&m&&m({action:h,location:w.location,delta:1})},replace:function(e,t){h=o.Replace;let r=c(w.location,e,t);n&&n(r,e),g=v();let a=u(r,g),i=w.createHref(r);p.replaceState(a,"",i),f&&m&&m({action:h,location:w.location,delta:0})},go:e=>p.go(e)};return w}var h;!function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"}(h||(h={}));new Set(["lazy","caseSensitive","path","id","index","children"]);function m(e,t,n){return void 0===n&&(n="/"),g(e,t,n,!1)}function g(e,t,n,r){let o=T(("string"===typeof t?f(t):t).pathname||"/",n);if(null==o)return null;let a=v(e);!function(e){e.sort((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every((e,n)=>e===t[n]);return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map(e=>e.childrenIndex),t.routesMeta.map(e=>e.childrenIndex)))}(a);let i=null;for(let l=0;null==i&&l<a.length;++l){let e=O(o);i=P(a[l],e,r)}return i}function v(e,t,n,r){void 0===t&&(t=[]),void 0===n&&(n=[]),void 0===r&&(r="");let o=(e,o,a)=>{let i={relativePath:void 0===a?e.path||"":a,caseSensitive:!0===e.caseSensitive,childrenIndex:o,route:e};i.relativePath.startsWith("/")&&(l(i.relativePath.startsWith(r),'Absolute route path "'+i.relativePath+'" nested under path "'+r+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),i.relativePath=i.relativePath.slice(r.length));let s=L([r,i.relativePath]),u=n.concat(i);e.children&&e.children.length>0&&(l(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+s+'".'),v(e.children,t,u,s)),(null!=e.path||e.index)&&t.push({path:s,score:C(s,e.index),routesMeta:u})};return e.forEach((e,t)=>{var n;if(""!==e.path&&null!=(n=e.path)&&n.includes("?"))for(let r of y(e.path))o(e,t,r);else o(e,t)}),t}function y(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,o=n.endsWith("?"),a=n.replace(/\?$/,"");if(0===r.length)return o?[a,""]:[a];let i=y(r.join("/")),l=[];return l.push(...i.map(e=>""===e?a:[a,e].join("/"))),o&&l.push(...i),l.map(t=>e.startsWith("/")&&""===t?"/":t)}const b=/^:[\w-]+$/,w=3,x=2,S=1,k=10,E=-2,A=e=>"*"===e;function C(e,t){let n=e.split("/"),r=n.length;return n.some(A)&&(r+=E),t&&(r+=x),n.filter(e=>!A(e)).reduce((e,t)=>e+(b.test(t)?w:""===t?S:k),r)}function P(e,t,n){void 0===n&&(n=!1);let{routesMeta:r}=e,o={},a="/",i=[];for(let l=0;l<r.length;++l){let e=r[l],s=l===r.length-1,u="/"===a?t:t.slice(a.length)||"/",c=R({path:e.relativePath,caseSensitive:e.caseSensitive,end:s},u),d=e.route;if(!c&&s&&n&&!r[r.length-1].route.index&&(c=R({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},u)),!c)return null;Object.assign(o,c.params),i.push({params:o,pathname:L([a,c.pathname]),pathnameBase:z(L([a,c.pathnameBase])),route:d}),"/"!==c.pathnameBase&&(a=L([a,c.pathnameBase]))}return i}function R(e,t){"string"===typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=function(e,t,n){void 0===t&&(t=!1);void 0===n&&(n=!0);s("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let r=[],o="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)"));e.endsWith("*")?(r.push({paramName:"*"}),o+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?o+="\\/*$":""!==e&&"/"!==e&&(o+="(?:(?=\\/|$))");let a=new RegExp(o,t?void 0:"i");return[a,r]}(e.path,e.caseSensitive,e.end),o=t.match(n);if(!o)return null;let a=o[0],i=a.replace(/(.)\/+$/,"$1"),l=o.slice(1);return{params:r.reduce((e,t,n)=>{let{paramName:r,isOptional:o}=t;if("*"===r){let e=l[n]||"";i=a.slice(0,a.length-e.length).replace(/(.)\/+$/,"$1")}const s=l[n];return e[r]=o&&!s?void 0:(s||"").replace(/%2F/g,"/"),e},{}),pathname:a,pathnameBase:i,pattern:e}}function O(e){try{return e.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(t){return s(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function T(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function N(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the `to."+n+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function j(e){return e.filter((e,t)=>0===t||e.route.path&&e.route.path.length>0)}function M(e,t){let n=j(e);return t?n.map((e,t)=>t===n.length-1?e.pathname:e.pathnameBase):n.map(e=>e.pathnameBase)}function _(e,t,n,o){let a;void 0===o&&(o=!1),"string"===typeof e?a=f(e):(a=r({},e),l(!a.pathname||!a.pathname.includes("?"),N("?","pathname","search",a)),l(!a.pathname||!a.pathname.includes("#"),N("#","pathname","hash",a)),l(!a.search||!a.search.includes("#"),N("#","search","hash",a)));let i,s=""===e||""===a.pathname,u=s?"/":a.pathname;if(null==u)i=n;else{let e=t.length-1;if(!o&&u.startsWith("..")){let t=u.split("/");for(;".."===t[0];)t.shift(),e-=1;a.pathname=t.join("/")}i=e>=0?t[e]:"/"}let c=function(e,t){void 0===t&&(t="/");let{pathname:n,search:r="",hash:o=""}="string"===typeof e?f(e):e,a=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)}),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:a,search:I(r),hash:D(o)}}(a,i),d=u&&"/"!==u&&u.endsWith("/"),p=(s||"."===u)&&n.endsWith("/");return c.pathname.endsWith("/")||!d&&!p||(c.pathname+="/"),c}const L=e=>e.join("/").replace(/\/\/+/g,"/"),z=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),I=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",D=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";class $ extends Error{}function F(e){return null!=e&&"number"===typeof e.status&&"string"===typeof e.statusText&&"boolean"===typeof e.internal&&"data"in e}const B=["post","put","patch","delete"],U=(new Set(B),["get",...B]);new Set(U),new Set([301,302,303,307,308]),new Set([307,308]);Symbol("deferred")},1704:(e,t,n)=>{"use strict";n.d(t,{dj:()=>f});var r=n(5043);let o=0;const a=new Map,i=e=>{if(a.has(e))return;const t=setTimeout(()=>{a.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);a.set(e,t)},l=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,5)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{const{toastId:n}=t;return n?i(n):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===n||void 0===n?{...e,open:!1}:e)}}case"REMOVE_TOAST":return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},s=[];let u={toasts:[]};function c(e){u=l(u,e),s.forEach(e=>{e(u)})}function d(e){let{...t}=e;const n=(o=(o+1)%Number.MAX_SAFE_INTEGER,o.toString()),r=()=>c({type:"DISMISS_TOAST",toastId:n});return c({type:"ADD_TOAST",toast:{...t,id:n,open:!0,onOpenChange:e=>{e||r()}}}),{id:n,dismiss:r,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:n}})}}function f(){const[e,t]=r.useState(u);return r.useEffect(()=>(s.push(t),()=>{const e=s.indexOf(t);e>-1&&s.splice(e,1)}),[e]),{...e,toast:d,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},1722:(e,t,n)=>{"use strict";n.d(t,{Rk:()=>r,SF:()=>o,sk:()=>a});function r(e,t,n){var r="";return n.split(" ").forEach(function(n){void 0!==e[n]?t.push(e[n]+";"):n&&(r+=n+" ")}),r}var o=function(e,t,n){var r=e.key+"-"+t.name;!1===n&&void 0===e.registered[r]&&(e.registered[r]=t.styles)},a=function(e,t,n){o(e,t,n);var r=e.key+"-"+t.name;if(void 0===e.inserted[t.name]){var a=t;do{e.insert(t===a?"."+r:"",a,e.sheet,!0),a=a.next}while(void 0!==a)}}},1862:(e,t,n)=>{"use strict";n.d(t,{A:()=>i,q:()=>a});var r=n(5043),o=n(579);function a(e,t){const n=r.createContext(t),a=e=>{const{children:t,...a}=e,i=r.useMemo(()=>a,Object.values(a));return(0,o.jsx)(n.Provider,{value:i,children:t})};return a.displayName=e+"Provider",[a,function(o){const a=r.useContext(n);if(a)return a;if(void 0!==t)return t;throw new Error(`\`${o}\` must be used within \`${e}\``)}]}function i(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=[];const a=()=>{const t=n.map(e=>r.createContext(e));return function(n){const o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return a.scopeName=e,[function(t,a){const i=r.createContext(a),l=n.length;n=[...n,a];const s=t=>{const{scope:n,children:a,...s}=t,u=n?.[e]?.[l]||i,c=r.useMemo(()=>s,Object.values(s));return(0,o.jsx)(u.Provider,{value:c,children:a})};return s.displayName=t+"Provider",[s,function(n,o){const s=o?.[e]?.[l]||i,u=r.useContext(s);if(u)return u;if(void 0!==a)return a;throw new Error(`\`${n}\` must be used within \`${t}\``)}]},l(a,...t)]}function l(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];const o=t[0];if(1===t.length)return o;const a=()=>{const e=t.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(t){const n=e.reduce((e,n)=>{let{useScope:r,scopeName:o}=n;return{...e,...r(t)[`__scope${o}`]}},{});return r.useMemo(()=>({[`__scope${o.scopeName}`]:n}),[n])}};return a.scopeName=o.scopeName,a}},2374:(e,t,n)=>{"use strict";t.A=void 0;var r=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var n=a(t);if(n&&n.has(e))return n.get(e);var r={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var l=o?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(r,i,l):r[i]=e[i]}return r.default=e,n&&n.set(e,r),r}(n(5043)),o=n(3174);function a(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(a=function(e){return e?n:t})(e)}t.A=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;const t=r.useContext(o.ThemeContext);return t&&(n=t,0!==Object.keys(n).length)?t:e;var n}},2730:(e,t,n)=>{"use strict";var r=n(5043),o=n(8853);function a(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var i=new Set,l={};function s(e,t){u(e,t),u(e+"Capture",t)}function u(e,t){for(l[e]=t,e=0;e<t.length;e++)i.add(t[e])}var c=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),d=Object.prototype.hasOwnProperty,f=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,p={},h={};function m(e,t,n,r,o,a,i){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=a,this.removeEmptyString=i}var g={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){g[e]=new m(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];g[t]=new m(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){g[e]=new m(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){g[e]=new m(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){g[e]=new m(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){g[e]=new m(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){g[e]=new m(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){g[e]=new m(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){g[e]=new m(e,5,!1,e.toLowerCase(),null,!1,!1)});var v=/[\-:]([a-z])/g;function y(e){return e[1].toUpperCase()}function b(e,t,n,r){var o=g.hasOwnProperty(t)?g[t]:null;(null!==o?0!==o.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null===t||"undefined"===typeof t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,o,r)&&(n=null),r||null===o?function(e){return!!d.call(h,e)||!d.call(p,e)&&(f.test(e)?h[e]=!0:(p[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=null===n?3!==o.type&&"":n:(t=o.attributeName,r=o.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(o=o.type)||4===o&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(v,y);g[t]=new m(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(v,y);g[t]=new m(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(v,y);g[t]=new m(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){g[e]=new m(e,1,!1,e.toLowerCase(),null,!1,!1)}),g.xlinkHref=new m("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){g[e]=new m(e,1,!1,e.toLowerCase(),null,!0,!0)});var w=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,x=Symbol.for("react.element"),S=Symbol.for("react.portal"),k=Symbol.for("react.fragment"),E=Symbol.for("react.strict_mode"),A=Symbol.for("react.profiler"),C=Symbol.for("react.provider"),P=Symbol.for("react.context"),R=Symbol.for("react.forward_ref"),O=Symbol.for("react.suspense"),T=Symbol.for("react.suspense_list"),N=Symbol.for("react.memo"),j=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var M=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var _=Symbol.iterator;function L(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=_&&e[_]||e["@@iterator"])?e:null}var z,I=Object.assign;function D(e){if(void 0===z)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);z=t&&t[1]||""}return"\n"+z+e}var $=!1;function F(e,t){if(!e||$)return"";$=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&"string"===typeof u.stack){for(var o=u.stack.split("\n"),a=r.stack.split("\n"),i=o.length-1,l=a.length-1;1<=i&&0<=l&&o[i]!==a[l];)l--;for(;1<=i&&0<=l;i--,l--)if(o[i]!==a[l]){if(1!==i||1!==l)do{if(i--,0>--l||o[i]!==a[l]){var s="\n"+o[i].replace(" at new "," at ");return e.displayName&&s.includes("<anonymous>")&&(s=s.replace("<anonymous>",e.displayName)),s}}while(1<=i&&0<=l);break}}}finally{$=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?D(e):""}function B(e){switch(e.tag){case 5:return D(e.type);case 16:return D("Lazy");case 13:return D("Suspense");case 19:return D("SuspenseList");case 0:case 2:case 15:return e=F(e.type,!1);case 11:return e=F(e.type.render,!1);case 1:return e=F(e.type,!0);default:return""}}function U(e){if(null==e)return null;if("function"===typeof e)return e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case k:return"Fragment";case S:return"Portal";case A:return"Profiler";case E:return"StrictMode";case O:return"Suspense";case T:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case P:return(e.displayName||"Context")+".Consumer";case C:return(e._context.displayName||"Context")+".Provider";case R:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case N:return null!==(t=e.displayName||null)?t:U(e.type)||"Memo";case j:t=e._payload,e=e._init;try{return U(e(t))}catch(n){}}return null}function W(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return U(t);case 8:return t===E?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"===typeof t)return t.displayName||t.name||null;if("string"===typeof t)return t}return null}function V(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function H(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function q(e){e._valueTracker||(e._valueTracker=function(e){var t=H(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var o=n.get,a=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(e){r=""+e,a.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function K(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=H(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function G(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function X(e,t){var n=t.checked;return I({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function Q(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=V(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function Y(e,t){null!=(t=t.checked)&&b(e,"checked",t,!1)}function J(e,t){Y(e,t);var n=V(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&ee(e,t.type,V(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function Z(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function ee(e,t,n){"number"===t&&G(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var te=Array.isArray;function ne(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+V(n),t=null,o=0;o<e.length;o++){if(e[o].value===n)return e[o].selected=!0,void(r&&(e[o].defaultSelected=!0));null!==t||e[o].disabled||(t=e[o])}null!==t&&(t.selected=!0)}}function re(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(a(91));return I({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function oe(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(a(92));if(te(n)){if(1<n.length)throw Error(a(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:V(n)}}function ae(e,t){var n=V(t.value),r=V(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function ie(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function le(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function se(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?le(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ue,ce=function(e){return"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n)})}:e}(function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ue=ue||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ue.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function de(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var fe={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},pe=["Webkit","ms","Moz","O"];function he(e,t,n){return null==t||"boolean"===typeof t||""===t?"":n||"number"!==typeof t||0===t||fe.hasOwnProperty(e)&&fe[e]?(""+t).trim():t+"px"}function me(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),o=he(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}Object.keys(fe).forEach(function(e){pe.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),fe[t]=fe[e]})});var ge=I({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ve(e,t){if(t){if(ge[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(a(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(a(60));if("object"!==typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(a(61))}if(null!=t.style&&"object"!==typeof t.style)throw Error(a(62))}}function ye(e,t){if(-1===e.indexOf("-"))return"string"===typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var be=null;function we(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var xe=null,Se=null,ke=null;function Ee(e){if(e=go(e)){if("function"!==typeof xe)throw Error(a(280));var t=e.stateNode;t&&(t=yo(t),xe(e.stateNode,e.type,t))}}function Ae(e){Se?ke?ke.push(e):ke=[e]:Se=e}function Ce(){if(Se){var e=Se,t=ke;if(ke=Se=null,Ee(e),t)for(e=0;e<t.length;e++)Ee(t[e])}}function Pe(e,t){return e(t)}function Re(){}var Oe=!1;function Te(e,t,n){if(Oe)return e(t,n);Oe=!0;try{return Pe(e,t,n)}finally{Oe=!1,(null!==Se||null!==ke)&&(Re(),Ce())}}function Ne(e,t){var n=e.stateNode;if(null===n)return null;var r=yo(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(a(231,t,typeof n));return n}var je=!1;if(c)try{var Me={};Object.defineProperty(Me,"passive",{get:function(){je=!0}}),window.addEventListener("test",Me,Me),window.removeEventListener("test",Me,Me)}catch(ec){je=!1}function _e(e,t,n,r,o,a,i,l,s){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var Le=!1,ze=null,Ie=!1,De=null,$e={onError:function(e){Le=!0,ze=e}};function Fe(e,t,n,r,o,a,i,l,s){Le=!1,ze=null,_e.apply($e,arguments)}function Be(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function Ue(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function We(e){if(Be(e)!==e)throw Error(a(188))}function Ve(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=Be(e)))throw Error(a(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(null===o)break;var i=o.alternate;if(null===i){if(null!==(r=o.return)){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return We(o),e;if(i===r)return We(o),t;i=i.sibling}throw Error(a(188))}if(n.return!==r.return)n=o,r=i;else{for(var l=!1,s=o.child;s;){if(s===n){l=!0,n=o,r=i;break}if(s===r){l=!0,r=o,n=i;break}s=s.sibling}if(!l){for(s=i.child;s;){if(s===n){l=!0,n=i,r=o;break}if(s===r){l=!0,r=i,n=o;break}s=s.sibling}if(!l)throw Error(a(189))}}if(n.alternate!==r)throw Error(a(190))}if(3!==n.tag)throw Error(a(188));return n.stateNode.current===n?e:t}(e))?He(e):null}function He(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=He(e);if(null!==t)return t;e=e.sibling}return null}var qe=o.unstable_scheduleCallback,Ke=o.unstable_cancelCallback,Ge=o.unstable_shouldYield,Xe=o.unstable_requestPaint,Qe=o.unstable_now,Ye=o.unstable_getCurrentPriorityLevel,Je=o.unstable_ImmediatePriority,Ze=o.unstable_UserBlockingPriority,et=o.unstable_NormalPriority,tt=o.unstable_LowPriority,nt=o.unstable_IdlePriority,rt=null,ot=null;var at=Math.clz32?Math.clz32:function(e){return 0===(e>>>=0)?32:31-(it(e)/lt|0)|0},it=Math.log,lt=Math.LN2;var st=64,ut=4194304;function ct(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function dt(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,o=e.suspendedLanes,a=e.pingedLanes,i=268435455&n;if(0!==i){var l=i&~o;0!==l?r=ct(l):0!==(a&=i)&&(r=ct(a))}else 0!==(i=n&~o)?r=ct(i):0!==a&&(r=ct(a));if(0===r)return 0;if(0!==t&&t!==r&&0===(t&o)&&((o=r&-r)>=(a=t&-t)||16===o&&0!==(4194240&a)))return t;if(0!==(4&r)&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)o=1<<(n=31-at(t)),r|=e[n],t&=~o;return r}function ft(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function pt(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function ht(){var e=st;return 0===(4194240&(st<<=1))&&(st=64),e}function mt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function gt(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-at(t)]=n}function vt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-at(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var yt=0;function bt(e){return 1<(e&=-e)?4<e?0!==(268435455&e)?16:536870912:4:1}var wt,xt,St,kt,Et,At=!1,Ct=[],Pt=null,Rt=null,Ot=null,Tt=new Map,Nt=new Map,jt=[],Mt="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function _t(e,t){switch(e){case"focusin":case"focusout":Pt=null;break;case"dragenter":case"dragleave":Rt=null;break;case"mouseover":case"mouseout":Ot=null;break;case"pointerover":case"pointerout":Tt.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Nt.delete(t.pointerId)}}function Lt(e,t,n,r,o,a){return null===e||e.nativeEvent!==a?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:a,targetContainers:[o]},null!==t&&(null!==(t=go(t))&&xt(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==o&&-1===t.indexOf(o)&&t.push(o),e)}function zt(e){var t=mo(e.target);if(null!==t){var n=Be(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=Ue(n)))return e.blockedOn=t,void Et(e.priority,function(){St(n)})}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function It(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Gt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=go(n))&&xt(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);be=r,n.target.dispatchEvent(r),be=null,t.shift()}return!0}function Dt(e,t,n){It(e)&&n.delete(t)}function $t(){At=!1,null!==Pt&&It(Pt)&&(Pt=null),null!==Rt&&It(Rt)&&(Rt=null),null!==Ot&&It(Ot)&&(Ot=null),Tt.forEach(Dt),Nt.forEach(Dt)}function Ft(e,t){e.blockedOn===t&&(e.blockedOn=null,At||(At=!0,o.unstable_scheduleCallback(o.unstable_NormalPriority,$t)))}function Bt(e){function t(t){return Ft(t,e)}if(0<Ct.length){Ft(Ct[0],e);for(var n=1;n<Ct.length;n++){var r=Ct[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==Pt&&Ft(Pt,e),null!==Rt&&Ft(Rt,e),null!==Ot&&Ft(Ot,e),Tt.forEach(t),Nt.forEach(t),n=0;n<jt.length;n++)(r=jt[n]).blockedOn===e&&(r.blockedOn=null);for(;0<jt.length&&null===(n=jt[0]).blockedOn;)zt(n),null===n.blockedOn&&jt.shift()}var Ut=w.ReactCurrentBatchConfig,Wt=!0;function Vt(e,t,n,r){var o=yt,a=Ut.transition;Ut.transition=null;try{yt=1,qt(e,t,n,r)}finally{yt=o,Ut.transition=a}}function Ht(e,t,n,r){var o=yt,a=Ut.transition;Ut.transition=null;try{yt=4,qt(e,t,n,r)}finally{yt=o,Ut.transition=a}}function qt(e,t,n,r){if(Wt){var o=Gt(e,t,n,r);if(null===o)Fr(e,t,r,Kt,n),_t(e,r);else if(function(e,t,n,r,o){switch(t){case"focusin":return Pt=Lt(Pt,e,t,n,r,o),!0;case"dragenter":return Rt=Lt(Rt,e,t,n,r,o),!0;case"mouseover":return Ot=Lt(Ot,e,t,n,r,o),!0;case"pointerover":var a=o.pointerId;return Tt.set(a,Lt(Tt.get(a)||null,e,t,n,r,o)),!0;case"gotpointercapture":return a=o.pointerId,Nt.set(a,Lt(Nt.get(a)||null,e,t,n,r,o)),!0}return!1}(o,e,t,n,r))r.stopPropagation();else if(_t(e,r),4&t&&-1<Mt.indexOf(e)){for(;null!==o;){var a=go(o);if(null!==a&&wt(a),null===(a=Gt(e,t,n,r))&&Fr(e,t,r,Kt,n),a===o)break;o=a}null!==o&&r.stopPropagation()}else Fr(e,t,r,null,n)}}var Kt=null;function Gt(e,t,n,r){if(Kt=null,null!==(e=mo(e=we(r))))if(null===(t=Be(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=Ue(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Kt=e,null}function Xt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Ye()){case Je:return 1;case Ze:return 4;case et:case tt:return 16;case nt:return 536870912;default:return 16}default:return 16}}var Qt=null,Yt=null,Jt=null;function Zt(){if(Jt)return Jt;var e,t,n=Yt,r=n.length,o="value"in Qt?Qt.value:Qt.textContent,a=o.length;for(e=0;e<r&&n[e]===o[e];e++);var i=r-e;for(t=1;t<=i&&n[r-t]===o[a-t];t++);return Jt=o.slice(e,1<t?1-t:void 0)}function en(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function tn(){return!0}function nn(){return!1}function rn(e){function t(t,n,r,o,a){for(var i in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=o,this.target=a,this.currentTarget=null,e)e.hasOwnProperty(i)&&(t=e[i],this[i]=t?t(o):o[i]);return this.isDefaultPrevented=(null!=o.defaultPrevented?o.defaultPrevented:!1===o.returnValue)?tn:nn,this.isPropagationStopped=nn,this}return I(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=tn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=tn)},persist:function(){},isPersistent:tn}),t}var on,an,ln,sn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},un=rn(sn),cn=I({},sn,{view:0,detail:0}),dn=rn(cn),fn=I({},cn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:kn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==ln&&(ln&&"mousemove"===e.type?(on=e.screenX-ln.screenX,an=e.screenY-ln.screenY):an=on=0,ln=e),on)},movementY:function(e){return"movementY"in e?e.movementY:an}}),pn=rn(fn),hn=rn(I({},fn,{dataTransfer:0})),mn=rn(I({},cn,{relatedTarget:0})),gn=rn(I({},sn,{animationName:0,elapsedTime:0,pseudoElement:0})),vn=rn(I({},sn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}})),yn=rn(I({},sn,{data:0})),bn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},wn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},xn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Sn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=xn[e])&&!!t[e]}function kn(){return Sn}var En=rn(I({},cn,{key:function(e){if(e.key){var t=bn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=en(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?wn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:kn,charCode:function(e){return"keypress"===e.type?en(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?en(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}})),An=rn(I({},fn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Cn=rn(I({},cn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:kn})),Pn=rn(I({},sn,{propertyName:0,elapsedTime:0,pseudoElement:0})),Rn=rn(I({},fn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0})),On=[9,13,27,32],Tn=c&&"CompositionEvent"in window,Nn=null;c&&"documentMode"in document&&(Nn=document.documentMode);var jn=c&&"TextEvent"in window&&!Nn,Mn=c&&(!Tn||Nn&&8<Nn&&11>=Nn),_n=String.fromCharCode(32),Ln=!1;function zn(e,t){switch(e){case"keyup":return-1!==On.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function In(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var Dn=!1;var $n={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Fn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!$n[e.type]:"textarea"===t}function Bn(e,t,n,r){Ae(r),0<(t=Ur(t,"onChange")).length&&(n=new un("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Un=null,Wn=null;function Vn(e){_r(e,0)}function Hn(e){if(K(vo(e)))return e}function qn(e,t){if("change"===e)return t}var Kn=!1;if(c){var Gn;if(c){var Xn="oninput"in document;if(!Xn){var Qn=document.createElement("div");Qn.setAttribute("oninput","return;"),Xn="function"===typeof Qn.oninput}Gn=Xn}else Gn=!1;Kn=Gn&&(!document.documentMode||9<document.documentMode)}function Yn(){Un&&(Un.detachEvent("onpropertychange",Jn),Wn=Un=null)}function Jn(e){if("value"===e.propertyName&&Hn(Wn)){var t=[];Bn(t,Wn,e,we(e)),Te(Vn,t)}}function Zn(e,t,n){"focusin"===e?(Yn(),Wn=n,(Un=t).attachEvent("onpropertychange",Jn)):"focusout"===e&&Yn()}function er(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Hn(Wn)}function tr(e,t){if("click"===e)return Hn(t)}function nr(e,t){if("input"===e||"change"===e)return Hn(t)}var rr="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function or(e,t){if(rr(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!d.call(t,o)||!rr(e[o],t[o]))return!1}return!0}function ar(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function ir(e,t){var n,r=ar(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=ar(r)}}function lr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?lr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function sr(){for(var e=window,t=G();t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=G((e=t.contentWindow).document)}return t}function ur(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function cr(e){var t=sr(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&lr(n.ownerDocument.documentElement,n)){if(null!==r&&ur(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var o=n.textContent.length,a=Math.min(r.start,o);r=void 0===r.end?a:Math.min(r.end,o),!e.extend&&a>r&&(o=r,r=a,a=o),o=ir(n,a);var i=ir(n,r);o&&i&&(1!==e.rangeCount||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&((t=t.createRange()).setStart(o.node,o.offset),e.removeAllRanges(),a>r?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"===typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var dr=c&&"documentMode"in document&&11>=document.documentMode,fr=null,pr=null,hr=null,mr=!1;function gr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;mr||null==fr||fr!==G(r)||("selectionStart"in(r=fr)&&ur(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},hr&&or(hr,r)||(hr=r,0<(r=Ur(pr,"onSelect")).length&&(t=new un("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=fr)))}function vr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var yr={animationend:vr("Animation","AnimationEnd"),animationiteration:vr("Animation","AnimationIteration"),animationstart:vr("Animation","AnimationStart"),transitionend:vr("Transition","TransitionEnd")},br={},wr={};function xr(e){if(br[e])return br[e];if(!yr[e])return e;var t,n=yr[e];for(t in n)if(n.hasOwnProperty(t)&&t in wr)return br[e]=n[t];return e}c&&(wr=document.createElement("div").style,"AnimationEvent"in window||(delete yr.animationend.animation,delete yr.animationiteration.animation,delete yr.animationstart.animation),"TransitionEvent"in window||delete yr.transitionend.transition);var Sr=xr("animationend"),kr=xr("animationiteration"),Er=xr("animationstart"),Ar=xr("transitionend"),Cr=new Map,Pr="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Rr(e,t){Cr.set(e,t),s(t,[e])}for(var Or=0;Or<Pr.length;Or++){var Tr=Pr[Or];Rr(Tr.toLowerCase(),"on"+(Tr[0].toUpperCase()+Tr.slice(1)))}Rr(Sr,"onAnimationEnd"),Rr(kr,"onAnimationIteration"),Rr(Er,"onAnimationStart"),Rr("dblclick","onDoubleClick"),Rr("focusin","onFocus"),Rr("focusout","onBlur"),Rr(Ar,"onTransitionEnd"),u("onMouseEnter",["mouseout","mouseover"]),u("onMouseLeave",["mouseout","mouseover"]),u("onPointerEnter",["pointerout","pointerover"]),u("onPointerLeave",["pointerout","pointerover"]),s("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),s("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),s("onBeforeInput",["compositionend","keypress","textInput","paste"]),s("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Nr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),jr=new Set("cancel close invalid load scroll toggle".split(" ").concat(Nr));function Mr(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,o,i,l,s,u){if(Fe.apply(this,arguments),Le){if(!Le)throw Error(a(198));var c=ze;Le=!1,ze=null,Ie||(Ie=!0,De=c)}}(r,t,void 0,e),e.currentTarget=null}function _r(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var a=void 0;if(t)for(var i=r.length-1;0<=i;i--){var l=r[i],s=l.instance,u=l.currentTarget;if(l=l.listener,s!==a&&o.isPropagationStopped())break e;Mr(o,l,u),a=s}else for(i=0;i<r.length;i++){if(s=(l=r[i]).instance,u=l.currentTarget,l=l.listener,s!==a&&o.isPropagationStopped())break e;Mr(o,l,u),a=s}}}if(Ie)throw e=De,Ie=!1,De=null,e}function Lr(e,t){var n=t[fo];void 0===n&&(n=t[fo]=new Set);var r=e+"__bubble";n.has(r)||($r(t,e,2,!1),n.add(r))}function zr(e,t,n){var r=0;t&&(r|=4),$r(n,e,r,t)}var Ir="_reactListening"+Math.random().toString(36).slice(2);function Dr(e){if(!e[Ir]){e[Ir]=!0,i.forEach(function(t){"selectionchange"!==t&&(jr.has(t)||zr(t,!1,e),zr(t,!0,e))});var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Ir]||(t[Ir]=!0,zr("selectionchange",!1,t))}}function $r(e,t,n,r){switch(Xt(t)){case 1:var o=Vt;break;case 4:o=Ht;break;default:o=qt}n=o.bind(null,t,n,e),o=void 0,!je||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(o=!0),r?void 0!==o?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):void 0!==o?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Fr(e,t,n,r,o){var a=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var i=r.tag;if(3===i||4===i){var l=r.stateNode.containerInfo;if(l===o||8===l.nodeType&&l.parentNode===o)break;if(4===i)for(i=r.return;null!==i;){var s=i.tag;if((3===s||4===s)&&((s=i.stateNode.containerInfo)===o||8===s.nodeType&&s.parentNode===o))return;i=i.return}for(;null!==l;){if(null===(i=mo(l)))return;if(5===(s=i.tag)||6===s){r=a=i;continue e}l=l.parentNode}}r=r.return}Te(function(){var r=a,o=we(n),i=[];e:{var l=Cr.get(e);if(void 0!==l){var s=un,u=e;switch(e){case"keypress":if(0===en(n))break e;case"keydown":case"keyup":s=En;break;case"focusin":u="focus",s=mn;break;case"focusout":u="blur",s=mn;break;case"beforeblur":case"afterblur":s=mn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":s=pn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":s=hn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":s=Cn;break;case Sr:case kr:case Er:s=gn;break;case Ar:s=Pn;break;case"scroll":s=dn;break;case"wheel":s=Rn;break;case"copy":case"cut":case"paste":s=vn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":s=An}var c=0!==(4&t),d=!c&&"scroll"===e,f=c?null!==l?l+"Capture":null:l;c=[];for(var p,h=r;null!==h;){var m=(p=h).stateNode;if(5===p.tag&&null!==m&&(p=m,null!==f&&(null!=(m=Ne(h,f))&&c.push(Br(h,m,p)))),d)break;h=h.return}0<c.length&&(l=new s(l,u,null,n,o),i.push({event:l,listeners:c}))}}if(0===(7&t)){if(s="mouseout"===e||"pointerout"===e,(!(l="mouseover"===e||"pointerover"===e)||n===be||!(u=n.relatedTarget||n.fromElement)||!mo(u)&&!u[co])&&(s||l)&&(l=o.window===o?o:(l=o.ownerDocument)?l.defaultView||l.parentWindow:window,s?(s=r,null!==(u=(u=n.relatedTarget||n.toElement)?mo(u):null)&&(u!==(d=Be(u))||5!==u.tag&&6!==u.tag)&&(u=null)):(s=null,u=r),s!==u)){if(c=pn,m="onMouseLeave",f="onMouseEnter",h="mouse","pointerout"!==e&&"pointerover"!==e||(c=An,m="onPointerLeave",f="onPointerEnter",h="pointer"),d=null==s?l:vo(s),p=null==u?l:vo(u),(l=new c(m,h+"leave",s,n,o)).target=d,l.relatedTarget=p,m=null,mo(o)===r&&((c=new c(f,h+"enter",u,n,o)).target=p,c.relatedTarget=d,m=c),d=m,s&&u)e:{for(f=u,h=0,p=c=s;p;p=Wr(p))h++;for(p=0,m=f;m;m=Wr(m))p++;for(;0<h-p;)c=Wr(c),h--;for(;0<p-h;)f=Wr(f),p--;for(;h--;){if(c===f||null!==f&&c===f.alternate)break e;c=Wr(c),f=Wr(f)}c=null}else c=null;null!==s&&Vr(i,l,s,c,!1),null!==u&&null!==d&&Vr(i,d,u,c,!0)}if("select"===(s=(l=r?vo(r):window).nodeName&&l.nodeName.toLowerCase())||"input"===s&&"file"===l.type)var g=qn;else if(Fn(l))if(Kn)g=nr;else{g=er;var v=Zn}else(s=l.nodeName)&&"input"===s.toLowerCase()&&("checkbox"===l.type||"radio"===l.type)&&(g=tr);switch(g&&(g=g(e,r))?Bn(i,g,n,o):(v&&v(e,l,r),"focusout"===e&&(v=l._wrapperState)&&v.controlled&&"number"===l.type&&ee(l,"number",l.value)),v=r?vo(r):window,e){case"focusin":(Fn(v)||"true"===v.contentEditable)&&(fr=v,pr=r,hr=null);break;case"focusout":hr=pr=fr=null;break;case"mousedown":mr=!0;break;case"contextmenu":case"mouseup":case"dragend":mr=!1,gr(i,n,o);break;case"selectionchange":if(dr)break;case"keydown":case"keyup":gr(i,n,o)}var y;if(Tn)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else Dn?zn(e,n)&&(b="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(b="onCompositionStart");b&&(Mn&&"ko"!==n.locale&&(Dn||"onCompositionStart"!==b?"onCompositionEnd"===b&&Dn&&(y=Zt()):(Yt="value"in(Qt=o)?Qt.value:Qt.textContent,Dn=!0)),0<(v=Ur(r,b)).length&&(b=new yn(b,e,null,n,o),i.push({event:b,listeners:v}),y?b.data=y:null!==(y=In(n))&&(b.data=y))),(y=jn?function(e,t){switch(e){case"compositionend":return In(t);case"keypress":return 32!==t.which?null:(Ln=!0,_n);case"textInput":return(e=t.data)===_n&&Ln?null:e;default:return null}}(e,n):function(e,t){if(Dn)return"compositionend"===e||!Tn&&zn(e,t)?(e=Zt(),Jt=Yt=Qt=null,Dn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Mn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(r=Ur(r,"onBeforeInput")).length&&(o=new yn("onBeforeInput","beforeinput",null,n,o),i.push({event:o,listeners:r}),o.data=y))}_r(i,t)})}function Br(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Ur(e,t){for(var n=t+"Capture",r=[];null!==e;){var o=e,a=o.stateNode;5===o.tag&&null!==a&&(o=a,null!=(a=Ne(e,n))&&r.unshift(Br(e,a,o)),null!=(a=Ne(e,t))&&r.push(Br(e,a,o))),e=e.return}return r}function Wr(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function Vr(e,t,n,r,o){for(var a=t._reactName,i=[];null!==n&&n!==r;){var l=n,s=l.alternate,u=l.stateNode;if(null!==s&&s===r)break;5===l.tag&&null!==u&&(l=u,o?null!=(s=Ne(n,a))&&i.unshift(Br(n,s,l)):o||null!=(s=Ne(n,a))&&i.push(Br(n,s,l))),n=n.return}0!==i.length&&e.push({event:t,listeners:i})}var Hr=/\r\n?/g,qr=/\u0000|\uFFFD/g;function Kr(e){return("string"===typeof e?e:""+e).replace(Hr,"\n").replace(qr,"")}function Gr(e,t,n){if(t=Kr(t),Kr(e)!==t&&n)throw Error(a(425))}function Xr(){}var Qr=null,Yr=null;function Jr(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var Zr="function"===typeof setTimeout?setTimeout:void 0,eo="function"===typeof clearTimeout?clearTimeout:void 0,to="function"===typeof Promise?Promise:void 0,no="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof to?function(e){return to.resolve(null).then(e).catch(ro)}:Zr;function ro(e){setTimeout(function(){throw e})}function oo(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&8===o.nodeType)if("/$"===(n=o.data)){if(0===r)return e.removeChild(o),void Bt(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=o}while(n);Bt(t)}function ao(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function io(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var lo=Math.random().toString(36).slice(2),so="__reactFiber$"+lo,uo="__reactProps$"+lo,co="__reactContainer$"+lo,fo="__reactEvents$"+lo,po="__reactListeners$"+lo,ho="__reactHandles$"+lo;function mo(e){var t=e[so];if(t)return t;for(var n=e.parentNode;n;){if(t=n[co]||n[so]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=io(e);null!==e;){if(n=e[so])return n;e=io(e)}return t}n=(e=n).parentNode}return null}function go(e){return!(e=e[so]||e[co])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function vo(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(a(33))}function yo(e){return e[uo]||null}var bo=[],wo=-1;function xo(e){return{current:e}}function So(e){0>wo||(e.current=bo[wo],bo[wo]=null,wo--)}function ko(e,t){wo++,bo[wo]=e.current,e.current=t}var Eo={},Ao=xo(Eo),Co=xo(!1),Po=Eo;function Ro(e,t){var n=e.type.contextTypes;if(!n)return Eo;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o,a={};for(o in n)a[o]=t[o];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=a),a}function Oo(e){return null!==(e=e.childContextTypes)&&void 0!==e}function To(){So(Co),So(Ao)}function No(e,t,n){if(Ao.current!==Eo)throw Error(a(168));ko(Ao,t),ko(Co,n)}function jo(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!==typeof r.getChildContext)return n;for(var o in r=r.getChildContext())if(!(o in t))throw Error(a(108,W(e)||"Unknown",o));return I({},n,r)}function Mo(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Eo,Po=Ao.current,ko(Ao,e),ko(Co,Co.current),!0}function _o(e,t,n){var r=e.stateNode;if(!r)throw Error(a(169));n?(e=jo(e,t,Po),r.__reactInternalMemoizedMergedChildContext=e,So(Co),So(Ao),ko(Ao,e)):So(Co),ko(Co,n)}var Lo=null,zo=!1,Io=!1;function Do(e){null===Lo?Lo=[e]:Lo.push(e)}function $o(){if(!Io&&null!==Lo){Io=!0;var e=0,t=yt;try{var n=Lo;for(yt=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}Lo=null,zo=!1}catch(o){throw null!==Lo&&(Lo=Lo.slice(e+1)),qe(Je,$o),o}finally{yt=t,Io=!1}}return null}var Fo=[],Bo=0,Uo=null,Wo=0,Vo=[],Ho=0,qo=null,Ko=1,Go="";function Xo(e,t){Fo[Bo++]=Wo,Fo[Bo++]=Uo,Uo=e,Wo=t}function Qo(e,t,n){Vo[Ho++]=Ko,Vo[Ho++]=Go,Vo[Ho++]=qo,qo=e;var r=Ko;e=Go;var o=32-at(r)-1;r&=~(1<<o),n+=1;var a=32-at(t)+o;if(30<a){var i=o-o%5;a=(r&(1<<i)-1).toString(32),r>>=i,o-=i,Ko=1<<32-at(t)+o|n<<o|r,Go=a+e}else Ko=1<<a|n<<o|r,Go=e}function Yo(e){null!==e.return&&(Xo(e,1),Qo(e,1,0))}function Jo(e){for(;e===Uo;)Uo=Fo[--Bo],Fo[Bo]=null,Wo=Fo[--Bo],Fo[Bo]=null;for(;e===qo;)qo=Vo[--Ho],Vo[Ho]=null,Go=Vo[--Ho],Vo[Ho]=null,Ko=Vo[--Ho],Vo[Ho]=null}var Zo=null,ea=null,ta=!1,na=null;function ra(e,t){var n=Pu(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function oa(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,Zo=e,ea=ao(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,Zo=e,ea=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==qo?{id:Ko,overflow:Go}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Pu(18,null,null,0)).stateNode=t,n.return=e,e.child=n,Zo=e,ea=null,!0);default:return!1}}function aa(e){return 0!==(1&e.mode)&&0===(128&e.flags)}function ia(e){if(ta){var t=ea;if(t){var n=t;if(!oa(e,t)){if(aa(e))throw Error(a(418));t=ao(n.nextSibling);var r=Zo;t&&oa(e,t)?ra(r,n):(e.flags=-4097&e.flags|2,ta=!1,Zo=e)}}else{if(aa(e))throw Error(a(418));e.flags=-4097&e.flags|2,ta=!1,Zo=e}}}function la(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;Zo=e}function sa(e){if(e!==Zo)return!1;if(!ta)return la(e),ta=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!Jr(e.type,e.memoizedProps)),t&&(t=ea)){if(aa(e))throw ua(),Error(a(418));for(;t;)ra(e,t),t=ao(t.nextSibling)}if(la(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(a(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){ea=ao(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}ea=null}}else ea=Zo?ao(e.stateNode.nextSibling):null;return!0}function ua(){for(var e=ea;e;)e=ao(e.nextSibling)}function ca(){ea=Zo=null,ta=!1}function da(e){null===na?na=[e]:na.push(e)}var fa=w.ReactCurrentBatchConfig;function pa(e,t,n){if(null!==(e=n.ref)&&"function"!==typeof e&&"object"!==typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(a(309));var r=n.stateNode}if(!r)throw Error(a(147,e));var o=r,i=""+e;return null!==t&&null!==t.ref&&"function"===typeof t.ref&&t.ref._stringRef===i?t.ref:(t=function(e){var t=o.refs;null===e?delete t[i]:t[i]=e},t._stringRef=i,t)}if("string"!==typeof e)throw Error(a(284));if(!n._owner)throw Error(a(290,e))}return e}function ha(e,t){throw e=Object.prototype.toString.call(t),Error(a(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function ma(e){return(0,e._init)(e._payload)}function ga(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function o(e,t){return(e=Ou(e,t)).index=0,e.sibling=null,e}function i(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function l(t){return e&&null===t.alternate&&(t.flags|=2),t}function s(e,t,n,r){return null===t||6!==t.tag?((t=Mu(n,e.mode,r)).return=e,t):((t=o(t,n)).return=e,t)}function u(e,t,n,r){var a=n.type;return a===k?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===a||"object"===typeof a&&null!==a&&a.$$typeof===j&&ma(a)===t.type)?((r=o(t,n.props)).ref=pa(e,t,n),r.return=e,r):((r=Tu(n.type,n.key,n.props,null,e.mode,r)).ref=pa(e,t,n),r.return=e,r)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=_u(n,e.mode,r)).return=e,t):((t=o(t,n.children||[])).return=e,t)}function d(e,t,n,r,a){return null===t||7!==t.tag?((t=Nu(n,e.mode,r,a)).return=e,t):((t=o(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t)return(t=Mu(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case x:return(n=Tu(t.type,t.key,t.props,null,e.mode,n)).ref=pa(e,null,t),n.return=e,n;case S:return(t=_u(t,e.mode,n)).return=e,t;case j:return f(e,(0,t._init)(t._payload),n)}if(te(t)||L(t))return(t=Nu(t,e.mode,n,null)).return=e,t;ha(e,t)}return null}function p(e,t,n,r){var o=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n)return null!==o?null:s(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case x:return n.key===o?u(e,t,n,r):null;case S:return n.key===o?c(e,t,n,r):null;case j:return p(e,t,(o=n._init)(n._payload),r)}if(te(n)||L(n))return null!==o?null:d(e,t,n,r,null);ha(e,n)}return null}function h(e,t,n,r,o){if("string"===typeof r&&""!==r||"number"===typeof r)return s(t,e=e.get(n)||null,""+r,o);if("object"===typeof r&&null!==r){switch(r.$$typeof){case x:return u(t,e=e.get(null===r.key?n:r.key)||null,r,o);case S:return c(t,e=e.get(null===r.key?n:r.key)||null,r,o);case j:return h(e,t,n,(0,r._init)(r._payload),o)}if(te(r)||L(r))return d(t,e=e.get(n)||null,r,o,null);ha(t,r)}return null}function m(o,a,l,s){for(var u=null,c=null,d=a,m=a=0,g=null;null!==d&&m<l.length;m++){d.index>m?(g=d,d=null):g=d.sibling;var v=p(o,d,l[m],s);if(null===v){null===d&&(d=g);break}e&&d&&null===v.alternate&&t(o,d),a=i(v,a,m),null===c?u=v:c.sibling=v,c=v,d=g}if(m===l.length)return n(o,d),ta&&Xo(o,m),u;if(null===d){for(;m<l.length;m++)null!==(d=f(o,l[m],s))&&(a=i(d,a,m),null===c?u=d:c.sibling=d,c=d);return ta&&Xo(o,m),u}for(d=r(o,d);m<l.length;m++)null!==(g=h(d,o,m,l[m],s))&&(e&&null!==g.alternate&&d.delete(null===g.key?m:g.key),a=i(g,a,m),null===c?u=g:c.sibling=g,c=g);return e&&d.forEach(function(e){return t(o,e)}),ta&&Xo(o,m),u}function g(o,l,s,u){var c=L(s);if("function"!==typeof c)throw Error(a(150));if(null==(s=c.call(s)))throw Error(a(151));for(var d=c=null,m=l,g=l=0,v=null,y=s.next();null!==m&&!y.done;g++,y=s.next()){m.index>g?(v=m,m=null):v=m.sibling;var b=p(o,m,y.value,u);if(null===b){null===m&&(m=v);break}e&&m&&null===b.alternate&&t(o,m),l=i(b,l,g),null===d?c=b:d.sibling=b,d=b,m=v}if(y.done)return n(o,m),ta&&Xo(o,g),c;if(null===m){for(;!y.done;g++,y=s.next())null!==(y=f(o,y.value,u))&&(l=i(y,l,g),null===d?c=y:d.sibling=y,d=y);return ta&&Xo(o,g),c}for(m=r(o,m);!y.done;g++,y=s.next())null!==(y=h(m,o,g,y.value,u))&&(e&&null!==y.alternate&&m.delete(null===y.key?g:y.key),l=i(y,l,g),null===d?c=y:d.sibling=y,d=y);return e&&m.forEach(function(e){return t(o,e)}),ta&&Xo(o,g),c}return function e(r,a,i,s){if("object"===typeof i&&null!==i&&i.type===k&&null===i.key&&(i=i.props.children),"object"===typeof i&&null!==i){switch(i.$$typeof){case x:e:{for(var u=i.key,c=a;null!==c;){if(c.key===u){if((u=i.type)===k){if(7===c.tag){n(r,c.sibling),(a=o(c,i.props.children)).return=r,r=a;break e}}else if(c.elementType===u||"object"===typeof u&&null!==u&&u.$$typeof===j&&ma(u)===c.type){n(r,c.sibling),(a=o(c,i.props)).ref=pa(r,c,i),a.return=r,r=a;break e}n(r,c);break}t(r,c),c=c.sibling}i.type===k?((a=Nu(i.props.children,r.mode,s,i.key)).return=r,r=a):((s=Tu(i.type,i.key,i.props,null,r.mode,s)).ref=pa(r,a,i),s.return=r,r=s)}return l(r);case S:e:{for(c=i.key;null!==a;){if(a.key===c){if(4===a.tag&&a.stateNode.containerInfo===i.containerInfo&&a.stateNode.implementation===i.implementation){n(r,a.sibling),(a=o(a,i.children||[])).return=r,r=a;break e}n(r,a);break}t(r,a),a=a.sibling}(a=_u(i,r.mode,s)).return=r,r=a}return l(r);case j:return e(r,a,(c=i._init)(i._payload),s)}if(te(i))return m(r,a,i,s);if(L(i))return g(r,a,i,s);ha(r,i)}return"string"===typeof i&&""!==i||"number"===typeof i?(i=""+i,null!==a&&6===a.tag?(n(r,a.sibling),(a=o(a,i)).return=r,r=a):(n(r,a),(a=Mu(i,r.mode,s)).return=r,r=a),l(r)):n(r,a)}}var va=ga(!0),ya=ga(!1),ba=xo(null),wa=null,xa=null,Sa=null;function ka(){Sa=xa=wa=null}function Ea(e){var t=ba.current;So(ba),e._currentValue=t}function Aa(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Ca(e,t){wa=e,Sa=xa=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(ml=!0),e.firstContext=null)}function Pa(e){var t=e._currentValue;if(Sa!==e)if(e={context:e,memoizedValue:t,next:null},null===xa){if(null===wa)throw Error(a(308));xa=e,wa.dependencies={lanes:0,firstContext:e}}else xa=xa.next=e;return t}var Ra=null;function Oa(e){null===Ra?Ra=[e]:Ra.push(e)}function Ta(e,t,n,r){var o=t.interleaved;return null===o?(n.next=n,Oa(t)):(n.next=o.next,o.next=n),t.interleaved=n,Na(e,r)}function Na(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var ja=!1;function Ma(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function _a(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function La(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function za(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&Es)){var o=r.pending;return null===o?t.next=t:(t.next=o.next,o.next=t),r.pending=t,Na(e,n)}return null===(o=r.interleaved)?(t.next=t,Oa(r)):(t.next=o.next,o.next=t),r.interleaved=t,Na(e,n)}function Ia(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194240&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,vt(e,n)}}function Da(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var o=null,a=null;if(null!==(n=n.firstBaseUpdate)){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===a?o=a=i:a=a.next=i,n=n.next}while(null!==n);null===a?o=a=t:a=a.next=t}else o=a=t;return n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:a,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function $a(e,t,n,r){var o=e.updateQueue;ja=!1;var a=o.firstBaseUpdate,i=o.lastBaseUpdate,l=o.shared.pending;if(null!==l){o.shared.pending=null;var s=l,u=s.next;s.next=null,null===i?a=u:i.next=u,i=s;var c=e.alternate;null!==c&&((l=(c=c.updateQueue).lastBaseUpdate)!==i&&(null===l?c.firstBaseUpdate=u:l.next=u,c.lastBaseUpdate=s))}if(null!==a){var d=o.baseState;for(i=0,c=u=s=null,l=a;;){var f=l.lane,p=l.eventTime;if((r&f)===f){null!==c&&(c=c.next={eventTime:p,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var h=e,m=l;switch(f=t,p=n,m.tag){case 1:if("function"===typeof(h=m.payload)){d=h.call(p,d,f);break e}d=h;break e;case 3:h.flags=-65537&h.flags|128;case 0:if(null===(f="function"===typeof(h=m.payload)?h.call(p,d,f):h)||void 0===f)break e;d=I({},d,f);break e;case 2:ja=!0}}null!==l.callback&&0!==l.lane&&(e.flags|=64,null===(f=o.effects)?o.effects=[l]:f.push(l))}else p={eventTime:p,lane:f,tag:l.tag,payload:l.payload,callback:l.callback,next:null},null===c?(u=c=p,s=d):c=c.next=p,i|=f;if(null===(l=l.next)){if(null===(l=o.shared.pending))break;l=(f=l).next,f.next=null,o.lastBaseUpdate=f,o.shared.pending=null}}if(null===c&&(s=d),o.baseState=s,o.firstBaseUpdate=u,o.lastBaseUpdate=c,null!==(t=o.shared.interleaved)){o=t;do{i|=o.lane,o=o.next}while(o!==t)}else null===a&&(o.shared.lanes=0);js|=i,e.lanes=i,e.memoizedState=d}}function Fa(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(null!==o){if(r.callback=null,r=n,"function"!==typeof o)throw Error(a(191,o));o.call(r)}}}var Ba={},Ua=xo(Ba),Wa=xo(Ba),Va=xo(Ba);function Ha(e){if(e===Ba)throw Error(a(174));return e}function qa(e,t){switch(ko(Va,t),ko(Wa,e),ko(Ua,Ba),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:se(null,"");break;default:t=se(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}So(Ua),ko(Ua,t)}function Ka(){So(Ua),So(Wa),So(Va)}function Ga(e){Ha(Va.current);var t=Ha(Ua.current),n=se(t,e.type);t!==n&&(ko(Wa,e),ko(Ua,n))}function Xa(e){Wa.current===e&&(So(Ua),So(Wa))}var Qa=xo(0);function Ya(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Ja=[];function Za(){for(var e=0;e<Ja.length;e++)Ja[e]._workInProgressVersionPrimary=null;Ja.length=0}var ei=w.ReactCurrentDispatcher,ti=w.ReactCurrentBatchConfig,ni=0,ri=null,oi=null,ai=null,ii=!1,li=!1,si=0,ui=0;function ci(){throw Error(a(321))}function di(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!rr(e[n],t[n]))return!1;return!0}function fi(e,t,n,r,o,i){if(ni=i,ri=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ei.current=null===e||null===e.memoizedState?Xi:Qi,e=n(r,o),li){i=0;do{if(li=!1,si=0,25<=i)throw Error(a(301));i+=1,ai=oi=null,t.updateQueue=null,ei.current=Yi,e=n(r,o)}while(li)}if(ei.current=Gi,t=null!==oi&&null!==oi.next,ni=0,ai=oi=ri=null,ii=!1,t)throw Error(a(300));return e}function pi(){var e=0!==si;return si=0,e}function hi(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===ai?ri.memoizedState=ai=e:ai=ai.next=e,ai}function mi(){if(null===oi){var e=ri.alternate;e=null!==e?e.memoizedState:null}else e=oi.next;var t=null===ai?ri.memoizedState:ai.next;if(null!==t)ai=t,oi=e;else{if(null===e)throw Error(a(310));e={memoizedState:(oi=e).memoizedState,baseState:oi.baseState,baseQueue:oi.baseQueue,queue:oi.queue,next:null},null===ai?ri.memoizedState=ai=e:ai=ai.next=e}return ai}function gi(e,t){return"function"===typeof t?t(e):t}function vi(e){var t=mi(),n=t.queue;if(null===n)throw Error(a(311));n.lastRenderedReducer=e;var r=oi,o=r.baseQueue,i=n.pending;if(null!==i){if(null!==o){var l=o.next;o.next=i.next,i.next=l}r.baseQueue=o=i,n.pending=null}if(null!==o){i=o.next,r=r.baseState;var s=l=null,u=null,c=i;do{var d=c.lane;if((ni&d)===d)null!==u&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var f={lane:d,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};null===u?(s=u=f,l=r):u=u.next=f,ri.lanes|=d,js|=d}c=c.next}while(null!==c&&c!==i);null===u?l=r:u.next=s,rr(r,t.memoizedState)||(ml=!0),t.memoizedState=r,t.baseState=l,t.baseQueue=u,n.lastRenderedState=r}if(null!==(e=n.interleaved)){o=e;do{i=o.lane,ri.lanes|=i,js|=i,o=o.next}while(o!==e)}else null===o&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function yi(e){var t=mi(),n=t.queue;if(null===n)throw Error(a(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(null!==o){n.pending=null;var l=o=o.next;do{i=e(i,l.action),l=l.next}while(l!==o);rr(i,t.memoizedState)||(ml=!0),t.memoizedState=i,null===t.baseQueue&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function bi(){}function wi(e,t){var n=ri,r=mi(),o=t(),i=!rr(r.memoizedState,o);if(i&&(r.memoizedState=o,ml=!0),r=r.queue,ji(ki.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||null!==ai&&1&ai.memoizedState.tag){if(n.flags|=2048,Pi(9,Si.bind(null,n,r,o,t),void 0,null),null===As)throw Error(a(349));0!==(30&ni)||xi(n,t,o)}return o}function xi(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=ri.updateQueue)?(t={lastEffect:null,stores:null},ri.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Si(e,t,n,r){t.value=n,t.getSnapshot=r,Ei(t)&&Ai(e)}function ki(e,t,n){return n(function(){Ei(t)&&Ai(e)})}function Ei(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!rr(e,n)}catch(r){return!0}}function Ai(e){var t=Na(e,1);null!==t&&Js(t,e,1,-1)}function Ci(e){var t=hi();return"function"===typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:gi,lastRenderedState:e},t.queue=e,e=e.dispatch=Vi.bind(null,ri,e),[t.memoizedState,e]}function Pi(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=ri.updateQueue)?(t={lastEffect:null,stores:null},ri.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function Ri(){return mi().memoizedState}function Oi(e,t,n,r){var o=hi();ri.flags|=e,o.memoizedState=Pi(1|t,n,void 0,void 0===r?null:r)}function Ti(e,t,n,r){var o=mi();r=void 0===r?null:r;var a=void 0;if(null!==oi){var i=oi.memoizedState;if(a=i.destroy,null!==r&&di(r,i.deps))return void(o.memoizedState=Pi(t,n,a,r))}ri.flags|=e,o.memoizedState=Pi(1|t,n,a,r)}function Ni(e,t){return Oi(8390656,8,e,t)}function ji(e,t){return Ti(2048,8,e,t)}function Mi(e,t){return Ti(4,2,e,t)}function _i(e,t){return Ti(4,4,e,t)}function Li(e,t){return"function"===typeof t?(e=e(),t(e),function(){t(null)}):null!==t&&void 0!==t?(e=e(),t.current=e,function(){t.current=null}):void 0}function zi(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Ti(4,4,Li.bind(null,t,e),n)}function Ii(){}function Di(e,t){var n=mi();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&di(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function $i(e,t){var n=mi();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&di(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Fi(e,t,n){return 0===(21&ni)?(e.baseState&&(e.baseState=!1,ml=!0),e.memoizedState=n):(rr(n,t)||(n=ht(),ri.lanes|=n,js|=n,e.baseState=!0),t)}function Bi(e,t){var n=yt;yt=0!==n&&4>n?n:4,e(!0);var r=ti.transition;ti.transition={};try{e(!1),t()}finally{yt=n,ti.transition=r}}function Ui(){return mi().memoizedState}function Wi(e,t,n){var r=Ys(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Hi(e))qi(t,n);else if(null!==(n=Ta(e,t,n,r))){Js(n,e,r,Qs()),Ki(n,t,r)}}function Vi(e,t,n){var r=Ys(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Hi(e))qi(t,o);else{var a=e.alternate;if(0===e.lanes&&(null===a||0===a.lanes)&&null!==(a=t.lastRenderedReducer))try{var i=t.lastRenderedState,l=a(i,n);if(o.hasEagerState=!0,o.eagerState=l,rr(l,i)){var s=t.interleaved;return null===s?(o.next=o,Oa(t)):(o.next=s.next,s.next=o),void(t.interleaved=o)}}catch(u){}null!==(n=Ta(e,t,o,r))&&(Js(n,e,r,o=Qs()),Ki(n,t,r))}}function Hi(e){var t=e.alternate;return e===ri||null!==t&&t===ri}function qi(e,t){li=ii=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Ki(e,t,n){if(0!==(4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,vt(e,n)}}var Gi={readContext:Pa,useCallback:ci,useContext:ci,useEffect:ci,useImperativeHandle:ci,useInsertionEffect:ci,useLayoutEffect:ci,useMemo:ci,useReducer:ci,useRef:ci,useState:ci,useDebugValue:ci,useDeferredValue:ci,useTransition:ci,useMutableSource:ci,useSyncExternalStore:ci,useId:ci,unstable_isNewReconciler:!1},Xi={readContext:Pa,useCallback:function(e,t){return hi().memoizedState=[e,void 0===t?null:t],e},useContext:Pa,useEffect:Ni,useImperativeHandle:function(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Oi(4194308,4,Li.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Oi(4194308,4,e,t)},useInsertionEffect:function(e,t){return Oi(4,2,e,t)},useMemo:function(e,t){var n=hi();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=hi();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Wi.bind(null,ri,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},hi().memoizedState=e},useState:Ci,useDebugValue:Ii,useDeferredValue:function(e){return hi().memoizedState=e},useTransition:function(){var e=Ci(!1),t=e[0];return e=Bi.bind(null,e[1]),hi().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=ri,o=hi();if(ta){if(void 0===n)throw Error(a(407));n=n()}else{if(n=t(),null===As)throw Error(a(349));0!==(30&ni)||xi(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,Ni(ki.bind(null,r,i,e),[e]),r.flags|=2048,Pi(9,Si.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=hi(),t=As.identifierPrefix;if(ta){var n=Go;t=":"+t+"R"+(n=(Ko&~(1<<32-at(Ko)-1)).toString(32)+n),0<(n=si++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=ui++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Qi={readContext:Pa,useCallback:Di,useContext:Pa,useEffect:ji,useImperativeHandle:zi,useInsertionEffect:Mi,useLayoutEffect:_i,useMemo:$i,useReducer:vi,useRef:Ri,useState:function(){return vi(gi)},useDebugValue:Ii,useDeferredValue:function(e){return Fi(mi(),oi.memoizedState,e)},useTransition:function(){return[vi(gi)[0],mi().memoizedState]},useMutableSource:bi,useSyncExternalStore:wi,useId:Ui,unstable_isNewReconciler:!1},Yi={readContext:Pa,useCallback:Di,useContext:Pa,useEffect:ji,useImperativeHandle:zi,useInsertionEffect:Mi,useLayoutEffect:_i,useMemo:$i,useReducer:yi,useRef:Ri,useState:function(){return yi(gi)},useDebugValue:Ii,useDeferredValue:function(e){var t=mi();return null===oi?t.memoizedState=e:Fi(t,oi.memoizedState,e)},useTransition:function(){return[yi(gi)[0],mi().memoizedState]},useMutableSource:bi,useSyncExternalStore:wi,useId:Ui,unstable_isNewReconciler:!1};function Ji(e,t){if(e&&e.defaultProps){for(var n in t=I({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function Zi(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:I({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var el={isMounted:function(e){return!!(e=e._reactInternals)&&Be(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Qs(),o=Ys(e),a=La(r,o);a.payload=t,void 0!==n&&null!==n&&(a.callback=n),null!==(t=za(e,a,o))&&(Js(t,e,o,r),Ia(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Qs(),o=Ys(e),a=La(r,o);a.tag=1,a.payload=t,void 0!==n&&null!==n&&(a.callback=n),null!==(t=za(e,a,o))&&(Js(t,e,o,r),Ia(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Qs(),r=Ys(e),o=La(n,r);o.tag=2,void 0!==t&&null!==t&&(o.callback=t),null!==(t=za(e,o,r))&&(Js(t,e,r,n),Ia(t,e,r))}};function tl(e,t,n,r,o,a,i){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,a,i):!t.prototype||!t.prototype.isPureReactComponent||(!or(n,r)||!or(o,a))}function nl(e,t,n){var r=!1,o=Eo,a=t.contextType;return"object"===typeof a&&null!==a?a=Pa(a):(o=Oo(t)?Po:Ao.current,a=(r=null!==(r=t.contextTypes)&&void 0!==r)?Ro(e,o):Eo),t=new t(n,a),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=el,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=a),t}function rl(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&el.enqueueReplaceState(t,t.state,null)}function ol(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},Ma(e);var a=t.contextType;"object"===typeof a&&null!==a?o.context=Pa(a):(a=Oo(t)?Po:Ao.current,o.context=Ro(e,a)),o.state=e.memoizedState,"function"===typeof(a=t.getDerivedStateFromProps)&&(Zi(e,t,a,n),o.state=e.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof o.getSnapshotBeforeUpdate||"function"!==typeof o.UNSAFE_componentWillMount&&"function"!==typeof o.componentWillMount||(t=o.state,"function"===typeof o.componentWillMount&&o.componentWillMount(),"function"===typeof o.UNSAFE_componentWillMount&&o.UNSAFE_componentWillMount(),t!==o.state&&el.enqueueReplaceState(o,o.state,null),$a(e,n,o,r),o.state=e.memoizedState),"function"===typeof o.componentDidMount&&(e.flags|=4194308)}function al(e,t){try{var n="",r=t;do{n+=B(r),r=r.return}while(r);var o=n}catch(a){o="\nError generating stack: "+a.message+"\n"+a.stack}return{value:e,source:t,stack:o,digest:null}}function il(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function ll(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var sl="function"===typeof WeakMap?WeakMap:Map;function ul(e,t,n){(n=La(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Fs||(Fs=!0,Bs=r),ll(0,t)},n}function cl(e,t,n){(n=La(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"===typeof r){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){ll(0,t)}}var a=e.stateNode;return null!==a&&"function"===typeof a.componentDidCatch&&(n.callback=function(){ll(0,t),"function"!==typeof r&&(null===Us?Us=new Set([this]):Us.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function dl(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new sl;var o=new Set;r.set(t,o)}else void 0===(o=r.get(t))&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=xu.bind(null,e,t,n),t.then(e,e))}function fl(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function pl(e,t,n,r,o){return 0===(1&e.mode)?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=La(-1,1)).tag=2,za(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=o,e)}var hl=w.ReactCurrentOwner,ml=!1;function gl(e,t,n,r){t.child=null===e?ya(t,null,n,r):va(t,e.child,n,r)}function vl(e,t,n,r,o){n=n.render;var a=t.ref;return Ca(t,o),r=fi(e,t,n,r,a,o),n=pi(),null===e||ml?(ta&&n&&Yo(t),t.flags|=1,gl(e,t,r,o),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Fl(e,t,o))}function yl(e,t,n,r,o){if(null===e){var a=n.type;return"function"!==typeof a||Ru(a)||void 0!==a.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Tu(n.type,null,r,t,t.mode,o)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=a,bl(e,t,a,r,o))}if(a=e.child,0===(e.lanes&o)){var i=a.memoizedProps;if((n=null!==(n=n.compare)?n:or)(i,r)&&e.ref===t.ref)return Fl(e,t,o)}return t.flags|=1,(e=Ou(a,r)).ref=t.ref,e.return=t,t.child=e}function bl(e,t,n,r,o){if(null!==e){var a=e.memoizedProps;if(or(a,r)&&e.ref===t.ref){if(ml=!1,t.pendingProps=r=a,0===(e.lanes&o))return t.lanes=e.lanes,Fl(e,t,o);0!==(131072&e.flags)&&(ml=!0)}}return Sl(e,t,n,r,o)}function wl(e,t,n){var r=t.pendingProps,o=r.children,a=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(0===(1&t.mode))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},ko(Os,Rs),Rs|=n;else{if(0===(1073741824&n))return e=null!==a?a.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,ko(Os,Rs),Rs|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==a?a.baseLanes:n,ko(Os,Rs),Rs|=r}else null!==a?(r=a.baseLanes|n,t.memoizedState=null):r=n,ko(Os,Rs),Rs|=r;return gl(e,t,o,n),t.child}function xl(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Sl(e,t,n,r,o){var a=Oo(n)?Po:Ao.current;return a=Ro(t,a),Ca(t,o),n=fi(e,t,n,r,a,o),r=pi(),null===e||ml?(ta&&r&&Yo(t),t.flags|=1,gl(e,t,n,o),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Fl(e,t,o))}function kl(e,t,n,r,o){if(Oo(n)){var a=!0;Mo(t)}else a=!1;if(Ca(t,o),null===t.stateNode)$l(e,t),nl(t,n,r),ol(t,n,r,o),r=!0;else if(null===e){var i=t.stateNode,l=t.memoizedProps;i.props=l;var s=i.context,u=n.contextType;"object"===typeof u&&null!==u?u=Pa(u):u=Ro(t,u=Oo(n)?Po:Ao.current);var c=n.getDerivedStateFromProps,d="function"===typeof c||"function"===typeof i.getSnapshotBeforeUpdate;d||"function"!==typeof i.UNSAFE_componentWillReceiveProps&&"function"!==typeof i.componentWillReceiveProps||(l!==r||s!==u)&&rl(t,i,r,u),ja=!1;var f=t.memoizedState;i.state=f,$a(t,r,i,o),s=t.memoizedState,l!==r||f!==s||Co.current||ja?("function"===typeof c&&(Zi(t,n,c,r),s=t.memoizedState),(l=ja||tl(t,n,l,r,f,s,u))?(d||"function"!==typeof i.UNSAFE_componentWillMount&&"function"!==typeof i.componentWillMount||("function"===typeof i.componentWillMount&&i.componentWillMount(),"function"===typeof i.UNSAFE_componentWillMount&&i.UNSAFE_componentWillMount()),"function"===typeof i.componentDidMount&&(t.flags|=4194308)):("function"===typeof i.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=s),i.props=r,i.state=s,i.context=u,r=l):("function"===typeof i.componentDidMount&&(t.flags|=4194308),r=!1)}else{i=t.stateNode,_a(e,t),l=t.memoizedProps,u=t.type===t.elementType?l:Ji(t.type,l),i.props=u,d=t.pendingProps,f=i.context,"object"===typeof(s=n.contextType)&&null!==s?s=Pa(s):s=Ro(t,s=Oo(n)?Po:Ao.current);var p=n.getDerivedStateFromProps;(c="function"===typeof p||"function"===typeof i.getSnapshotBeforeUpdate)||"function"!==typeof i.UNSAFE_componentWillReceiveProps&&"function"!==typeof i.componentWillReceiveProps||(l!==d||f!==s)&&rl(t,i,r,s),ja=!1,f=t.memoizedState,i.state=f,$a(t,r,i,o);var h=t.memoizedState;l!==d||f!==h||Co.current||ja?("function"===typeof p&&(Zi(t,n,p,r),h=t.memoizedState),(u=ja||tl(t,n,u,r,f,h,s)||!1)?(c||"function"!==typeof i.UNSAFE_componentWillUpdate&&"function"!==typeof i.componentWillUpdate||("function"===typeof i.componentWillUpdate&&i.componentWillUpdate(r,h,s),"function"===typeof i.UNSAFE_componentWillUpdate&&i.UNSAFE_componentWillUpdate(r,h,s)),"function"===typeof i.componentDidUpdate&&(t.flags|=4),"function"===typeof i.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof i.componentDidUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof i.getSnapshotBeforeUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=h),i.props=r,i.state=h,i.context=s,r=u):("function"!==typeof i.componentDidUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof i.getSnapshotBeforeUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return El(e,t,n,r,a,o)}function El(e,t,n,r,o,a){xl(e,t);var i=0!==(128&t.flags);if(!r&&!i)return o&&_o(t,n,!1),Fl(e,t,a);r=t.stateNode,hl.current=t;var l=i&&"function"!==typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&i?(t.child=va(t,e.child,null,a),t.child=va(t,null,l,a)):gl(e,t,l,a),t.memoizedState=r.state,o&&_o(t,n,!0),t.child}function Al(e){var t=e.stateNode;t.pendingContext?No(0,t.pendingContext,t.pendingContext!==t.context):t.context&&No(0,t.context,!1),qa(e,t.containerInfo)}function Cl(e,t,n,r,o){return ca(),da(o),t.flags|=256,gl(e,t,n,r),t.child}var Pl,Rl,Ol,Tl,Nl={dehydrated:null,treeContext:null,retryLane:0};function jl(e){return{baseLanes:e,cachePool:null,transitions:null}}function Ml(e,t,n){var r,o=t.pendingProps,i=Qa.current,l=!1,s=0!==(128&t.flags);if((r=s)||(r=(null===e||null!==e.memoizedState)&&0!==(2&i)),r?(l=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(i|=1),ko(Qa,1&i),null===e)return ia(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(0===(1&t.mode)?t.lanes=1:"$!"===e.data?t.lanes=8:t.lanes=1073741824,null):(s=o.children,e=o.fallback,l?(o=t.mode,l=t.child,s={mode:"hidden",children:s},0===(1&o)&&null!==l?(l.childLanes=0,l.pendingProps=s):l=ju(s,o,0,null),e=Nu(e,o,n,null),l.return=t,e.return=t,l.sibling=e,t.child=l,t.child.memoizedState=jl(n),t.memoizedState=Nl,e):_l(t,s));if(null!==(i=e.memoizedState)&&null!==(r=i.dehydrated))return function(e,t,n,r,o,i,l){if(n)return 256&t.flags?(t.flags&=-257,Ll(e,t,l,r=il(Error(a(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=ju({mode:"visible",children:r.children},o,0,null),(i=Nu(i,o,l,null)).flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,0!==(1&t.mode)&&va(t,e.child,null,l),t.child.memoizedState=jl(l),t.memoizedState=Nl,i);if(0===(1&t.mode))return Ll(e,t,l,null);if("$!"===o.data){if(r=o.nextSibling&&o.nextSibling.dataset)var s=r.dgst;return r=s,Ll(e,t,l,r=il(i=Error(a(419)),r,void 0))}if(s=0!==(l&e.childLanes),ml||s){if(null!==(r=As)){switch(l&-l){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}0!==(o=0!==(o&(r.suspendedLanes|l))?0:o)&&o!==i.retryLane&&(i.retryLane=o,Na(e,o),Js(r,e,o,-1))}return du(),Ll(e,t,l,r=il(Error(a(421))))}return"$?"===o.data?(t.flags|=128,t.child=e.child,t=ku.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,ea=ao(o.nextSibling),Zo=t,ta=!0,na=null,null!==e&&(Vo[Ho++]=Ko,Vo[Ho++]=Go,Vo[Ho++]=qo,Ko=e.id,Go=e.overflow,qo=t),t=_l(t,r.children),t.flags|=4096,t)}(e,t,s,o,r,i,n);if(l){l=o.fallback,s=t.mode,r=(i=e.child).sibling;var u={mode:"hidden",children:o.children};return 0===(1&s)&&t.child!==i?((o=t.child).childLanes=0,o.pendingProps=u,t.deletions=null):(o=Ou(i,u)).subtreeFlags=14680064&i.subtreeFlags,null!==r?l=Ou(r,l):(l=Nu(l,s,n,null)).flags|=2,l.return=t,o.return=t,o.sibling=l,t.child=o,o=l,l=t.child,s=null===(s=e.child.memoizedState)?jl(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},l.memoizedState=s,l.childLanes=e.childLanes&~n,t.memoizedState=Nl,o}return e=(l=e.child).sibling,o=Ou(l,{mode:"visible",children:o.children}),0===(1&t.mode)&&(o.lanes=n),o.return=t,o.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=o,t.memoizedState=null,o}function _l(e,t){return(t=ju({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Ll(e,t,n,r){return null!==r&&da(r),va(t,e.child,null,n),(e=_l(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function zl(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),Aa(e.return,t,n)}function Il(e,t,n,r,o){var a=e.memoizedState;null===a?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(a.isBackwards=t,a.rendering=null,a.renderingStartTime=0,a.last=r,a.tail=n,a.tailMode=o)}function Dl(e,t,n){var r=t.pendingProps,o=r.revealOrder,a=r.tail;if(gl(e,t,r.children,n),0!==(2&(r=Qa.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&zl(e,n,t);else if(19===e.tag)zl(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(ko(Qa,r),0===(1&t.mode))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;null!==n;)null!==(e=n.alternate)&&null===Ya(e)&&(o=n),n=n.sibling;null===(n=o)?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),Il(t,!1,o,n,a);break;case"backwards":for(n=null,o=t.child,t.child=null;null!==o;){if(null!==(e=o.alternate)&&null===Ya(e)){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}Il(t,!0,n,null,a);break;case"together":Il(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function $l(e,t){0===(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Fl(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),js|=t.lanes,0===(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(a(153));if(null!==t.child){for(n=Ou(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Ou(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Bl(e,t){if(!ta)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Ul(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;null!==o;)n|=o.lanes|o.childLanes,r|=14680064&o.subtreeFlags,r|=14680064&o.flags,o.return=e,o=o.sibling;else for(o=e.child;null!==o;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Wl(e,t,n){var r=t.pendingProps;switch(Jo(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ul(t),null;case 1:case 17:return Oo(t.type)&&To(),Ul(t),null;case 3:return r=t.stateNode,Ka(),So(Co),So(Ao),Za(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(sa(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,null!==na&&(nu(na),na=null))),Rl(e,t),Ul(t),null;case 5:Xa(t);var o=Ha(Va.current);if(n=t.type,null!==e&&null!=t.stateNode)Ol(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(a(166));return Ul(t),null}if(e=Ha(Ua.current),sa(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[so]=t,r[uo]=i,e=0!==(1&t.mode),n){case"dialog":Lr("cancel",r),Lr("close",r);break;case"iframe":case"object":case"embed":Lr("load",r);break;case"video":case"audio":for(o=0;o<Nr.length;o++)Lr(Nr[o],r);break;case"source":Lr("error",r);break;case"img":case"image":case"link":Lr("error",r),Lr("load",r);break;case"details":Lr("toggle",r);break;case"input":Q(r,i),Lr("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},Lr("invalid",r);break;case"textarea":oe(r,i),Lr("invalid",r)}for(var s in ve(n,i),o=null,i)if(i.hasOwnProperty(s)){var u=i[s];"children"===s?"string"===typeof u?r.textContent!==u&&(!0!==i.suppressHydrationWarning&&Gr(r.textContent,u,e),o=["children",u]):"number"===typeof u&&r.textContent!==""+u&&(!0!==i.suppressHydrationWarning&&Gr(r.textContent,u,e),o=["children",""+u]):l.hasOwnProperty(s)&&null!=u&&"onScroll"===s&&Lr("scroll",r)}switch(n){case"input":q(r),Z(r,i,!0);break;case"textarea":q(r),ie(r);break;case"select":case"option":break;default:"function"===typeof i.onClick&&(r.onclick=Xr)}r=o,t.updateQueue=r,null!==r&&(t.flags|=4)}else{s=9===o.nodeType?o:o.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=le(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=s.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"===typeof r.is?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),"select"===n&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[so]=t,e[uo]=r,Pl(e,t,!1,!1),t.stateNode=e;e:{switch(s=ye(n,r),n){case"dialog":Lr("cancel",e),Lr("close",e),o=r;break;case"iframe":case"object":case"embed":Lr("load",e),o=r;break;case"video":case"audio":for(o=0;o<Nr.length;o++)Lr(Nr[o],e);o=r;break;case"source":Lr("error",e),o=r;break;case"img":case"image":case"link":Lr("error",e),Lr("load",e),o=r;break;case"details":Lr("toggle",e),o=r;break;case"input":Q(e,r),o=X(e,r),Lr("invalid",e);break;case"option":default:o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=I({},r,{value:void 0}),Lr("invalid",e);break;case"textarea":oe(e,r),o=re(e,r),Lr("invalid",e)}for(i in ve(n,o),u=o)if(u.hasOwnProperty(i)){var c=u[i];"style"===i?me(e,c):"dangerouslySetInnerHTML"===i?null!=(c=c?c.__html:void 0)&&ce(e,c):"children"===i?"string"===typeof c?("textarea"!==n||""!==c)&&de(e,c):"number"===typeof c&&de(e,""+c):"suppressContentEditableWarning"!==i&&"suppressHydrationWarning"!==i&&"autoFocus"!==i&&(l.hasOwnProperty(i)?null!=c&&"onScroll"===i&&Lr("scroll",e):null!=c&&b(e,i,c,s))}switch(n){case"input":q(e),Z(e,r,!1);break;case"textarea":q(e),ie(e);break;case"option":null!=r.value&&e.setAttribute("value",""+V(r.value));break;case"select":e.multiple=!!r.multiple,null!=(i=r.value)?ne(e,!!r.multiple,i,!1):null!=r.defaultValue&&ne(e,!!r.multiple,r.defaultValue,!0);break;default:"function"===typeof o.onClick&&(e.onclick=Xr)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return Ul(t),null;case 6:if(e&&null!=t.stateNode)Tl(e,t,e.memoizedProps,r);else{if("string"!==typeof r&&null===t.stateNode)throw Error(a(166));if(n=Ha(Va.current),Ha(Ua.current),sa(t)){if(r=t.stateNode,n=t.memoizedProps,r[so]=t,(i=r.nodeValue!==n)&&null!==(e=Zo))switch(e.tag){case 3:Gr(r.nodeValue,n,0!==(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Gr(r.nodeValue,n,0!==(1&e.mode))}i&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[so]=t,t.stateNode=r}return Ul(t),null;case 13:if(So(Qa),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(ta&&null!==ea&&0!==(1&t.mode)&&0===(128&t.flags))ua(),ca(),t.flags|=98560,i=!1;else if(i=sa(t),null!==r&&null!==r.dehydrated){if(null===e){if(!i)throw Error(a(318));if(!(i=null!==(i=t.memoizedState)?i.dehydrated:null))throw Error(a(317));i[so]=t}else ca(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;Ul(t),i=!1}else null!==na&&(nu(na),na=null),i=!0;if(!i)return 65536&t.flags?t:null}return 0!==(128&t.flags)?(t.lanes=n,t):((r=null!==r)!==(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,0!==(1&t.mode)&&(null===e||0!==(1&Qa.current)?0===Ts&&(Ts=3):du())),null!==t.updateQueue&&(t.flags|=4),Ul(t),null);case 4:return Ka(),Rl(e,t),null===e&&Dr(t.stateNode.containerInfo),Ul(t),null;case 10:return Ea(t.type._context),Ul(t),null;case 19:if(So(Qa),null===(i=t.memoizedState))return Ul(t),null;if(r=0!==(128&t.flags),null===(s=i.rendering))if(r)Bl(i,!1);else{if(0!==Ts||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(s=Ya(e))){for(t.flags|=128,Bl(i,!1),null!==(r=s.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(i=n).flags&=14680066,null===(s=i.alternate)?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=s.childLanes,i.lanes=s.lanes,i.child=s.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=s.memoizedProps,i.memoizedState=s.memoizedState,i.updateQueue=s.updateQueue,i.type=s.type,e=s.dependencies,i.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return ko(Qa,1&Qa.current|2),t.child}e=e.sibling}null!==i.tail&&Qe()>Ds&&(t.flags|=128,r=!0,Bl(i,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=Ya(s))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),Bl(i,!0),null===i.tail&&"hidden"===i.tailMode&&!s.alternate&&!ta)return Ul(t),null}else 2*Qe()-i.renderingStartTime>Ds&&1073741824!==n&&(t.flags|=128,r=!0,Bl(i,!1),t.lanes=4194304);i.isBackwards?(s.sibling=t.child,t.child=s):(null!==(n=i.last)?n.sibling=s:t.child=s,i.last=s)}return null!==i.tail?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Qe(),t.sibling=null,n=Qa.current,ko(Qa,r?1&n|2:1&n),t):(Ul(t),null);case 22:case 23:return lu(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&0!==(1&t.mode)?0!==(1073741824&Rs)&&(Ul(t),6&t.subtreeFlags&&(t.flags|=8192)):Ul(t),null;case 24:case 25:return null}throw Error(a(156,t.tag))}function Vl(e,t){switch(Jo(t),t.tag){case 1:return Oo(t.type)&&To(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Ka(),So(Co),So(Ao),Za(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 5:return Xa(t),null;case 13:if(So(Qa),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(a(340));ca()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return So(Qa),null;case 4:return Ka(),null;case 10:return Ea(t.type._context),null;case 22:case 23:return lu(),null;default:return null}}Pl=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Rl=function(){},Ol=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,Ha(Ua.current);var a,i=null;switch(n){case"input":o=X(e,o),r=X(e,r),i=[];break;case"select":o=I({},o,{value:void 0}),r=I({},r,{value:void 0}),i=[];break;case"textarea":o=re(e,o),r=re(e,r),i=[];break;default:"function"!==typeof o.onClick&&"function"===typeof r.onClick&&(e.onclick=Xr)}for(c in ve(n,r),n=null,o)if(!r.hasOwnProperty(c)&&o.hasOwnProperty(c)&&null!=o[c])if("style"===c){var s=o[c];for(a in s)s.hasOwnProperty(a)&&(n||(n={}),n[a]="")}else"dangerouslySetInnerHTML"!==c&&"children"!==c&&"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&"autoFocus"!==c&&(l.hasOwnProperty(c)?i||(i=[]):(i=i||[]).push(c,null));for(c in r){var u=r[c];if(s=null!=o?o[c]:void 0,r.hasOwnProperty(c)&&u!==s&&(null!=u||null!=s))if("style"===c)if(s){for(a in s)!s.hasOwnProperty(a)||u&&u.hasOwnProperty(a)||(n||(n={}),n[a]="");for(a in u)u.hasOwnProperty(a)&&s[a]!==u[a]&&(n||(n={}),n[a]=u[a])}else n||(i||(i=[]),i.push(c,n)),n=u;else"dangerouslySetInnerHTML"===c?(u=u?u.__html:void 0,s=s?s.__html:void 0,null!=u&&s!==u&&(i=i||[]).push(c,u)):"children"===c?"string"!==typeof u&&"number"!==typeof u||(i=i||[]).push(c,""+u):"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&(l.hasOwnProperty(c)?(null!=u&&"onScroll"===c&&Lr("scroll",e),i||s===u||(i=[])):(i=i||[]).push(c,u))}n&&(i=i||[]).push("style",n);var c=i;(t.updateQueue=c)&&(t.flags|=4)}},Tl=function(e,t,n,r){n!==r&&(t.flags|=4)};var Hl=!1,ql=!1,Kl="function"===typeof WeakSet?WeakSet:Set,Gl=null;function Xl(e,t){var n=e.ref;if(null!==n)if("function"===typeof n)try{n(null)}catch(r){wu(e,t,r)}else n.current=null}function Ql(e,t,n){try{n()}catch(r){wu(e,t,r)}}var Yl=!1;function Jl(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var o=r=r.next;do{if((o.tag&e)===e){var a=o.destroy;o.destroy=void 0,void 0!==a&&Ql(t,n,a)}o=o.next}while(o!==r)}}function Zl(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function es(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"===typeof t?t(e):t.current=e}}function ts(e){var t=e.alternate;null!==t&&(e.alternate=null,ts(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[so],delete t[uo],delete t[fo],delete t[po],delete t[ho])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function ns(e){return 5===e.tag||3===e.tag||4===e.tag}function rs(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||ns(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function os(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Xr));else if(4!==r&&null!==(e=e.child))for(os(e,t,n),e=e.sibling;null!==e;)os(e,t,n),e=e.sibling}function as(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(as(e,t,n),e=e.sibling;null!==e;)as(e,t,n),e=e.sibling}var is=null,ls=!1;function ss(e,t,n){for(n=n.child;null!==n;)us(e,t,n),n=n.sibling}function us(e,t,n){if(ot&&"function"===typeof ot.onCommitFiberUnmount)try{ot.onCommitFiberUnmount(rt,n)}catch(l){}switch(n.tag){case 5:ql||Xl(n,t);case 6:var r=is,o=ls;is=null,ss(e,t,n),ls=o,null!==(is=r)&&(ls?(e=is,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):is.removeChild(n.stateNode));break;case 18:null!==is&&(ls?(e=is,n=n.stateNode,8===e.nodeType?oo(e.parentNode,n):1===e.nodeType&&oo(e,n),Bt(e)):oo(is,n.stateNode));break;case 4:r=is,o=ls,is=n.stateNode.containerInfo,ls=!0,ss(e,t,n),is=r,ls=o;break;case 0:case 11:case 14:case 15:if(!ql&&(null!==(r=n.updateQueue)&&null!==(r=r.lastEffect))){o=r=r.next;do{var a=o,i=a.destroy;a=a.tag,void 0!==i&&(0!==(2&a)||0!==(4&a))&&Ql(n,t,i),o=o.next}while(o!==r)}ss(e,t,n);break;case 1:if(!ql&&(Xl(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){wu(n,t,l)}ss(e,t,n);break;case 21:ss(e,t,n);break;case 22:1&n.mode?(ql=(r=ql)||null!==n.memoizedState,ss(e,t,n),ql=r):ss(e,t,n);break;default:ss(e,t,n)}}function cs(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Kl),t.forEach(function(t){var r=Eu.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))})}}function ds(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,l=t,s=l;e:for(;null!==s;){switch(s.tag){case 5:is=s.stateNode,ls=!1;break e;case 3:case 4:is=s.stateNode.containerInfo,ls=!0;break e}s=s.return}if(null===is)throw Error(a(160));us(i,l,o),is=null,ls=!1;var u=o.alternate;null!==u&&(u.return=null),o.return=null}catch(c){wu(o,t,c)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)fs(t,e),t=t.sibling}function fs(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(ds(t,e),ps(e),4&r){try{Jl(3,e,e.return),Zl(3,e)}catch(g){wu(e,e.return,g)}try{Jl(5,e,e.return)}catch(g){wu(e,e.return,g)}}break;case 1:ds(t,e),ps(e),512&r&&null!==n&&Xl(n,n.return);break;case 5:if(ds(t,e),ps(e),512&r&&null!==n&&Xl(n,n.return),32&e.flags){var o=e.stateNode;try{de(o,"")}catch(g){wu(e,e.return,g)}}if(4&r&&null!=(o=e.stateNode)){var i=e.memoizedProps,l=null!==n?n.memoizedProps:i,s=e.type,u=e.updateQueue;if(e.updateQueue=null,null!==u)try{"input"===s&&"radio"===i.type&&null!=i.name&&Y(o,i),ye(s,l);var c=ye(s,i);for(l=0;l<u.length;l+=2){var d=u[l],f=u[l+1];"style"===d?me(o,f):"dangerouslySetInnerHTML"===d?ce(o,f):"children"===d?de(o,f):b(o,d,f,c)}switch(s){case"input":J(o,i);break;case"textarea":ae(o,i);break;case"select":var p=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var h=i.value;null!=h?ne(o,!!i.multiple,h,!1):p!==!!i.multiple&&(null!=i.defaultValue?ne(o,!!i.multiple,i.defaultValue,!0):ne(o,!!i.multiple,i.multiple?[]:"",!1))}o[uo]=i}catch(g){wu(e,e.return,g)}}break;case 6:if(ds(t,e),ps(e),4&r){if(null===e.stateNode)throw Error(a(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(g){wu(e,e.return,g)}}break;case 3:if(ds(t,e),ps(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Bt(t.containerInfo)}catch(g){wu(e,e.return,g)}break;case 4:default:ds(t,e),ps(e);break;case 13:ds(t,e),ps(e),8192&(o=e.child).flags&&(i=null!==o.memoizedState,o.stateNode.isHidden=i,!i||null!==o.alternate&&null!==o.alternate.memoizedState||(Is=Qe())),4&r&&cs(e);break;case 22:if(d=null!==n&&null!==n.memoizedState,1&e.mode?(ql=(c=ql)||d,ds(t,e),ql=c):ds(t,e),ps(e),8192&r){if(c=null!==e.memoizedState,(e.stateNode.isHidden=c)&&!d&&0!==(1&e.mode))for(Gl=e,d=e.child;null!==d;){for(f=Gl=d;null!==Gl;){switch(h=(p=Gl).child,p.tag){case 0:case 11:case 14:case 15:Jl(4,p,p.return);break;case 1:Xl(p,p.return);var m=p.stateNode;if("function"===typeof m.componentWillUnmount){r=p,n=p.return;try{t=r,m.props=t.memoizedProps,m.state=t.memoizedState,m.componentWillUnmount()}catch(g){wu(r,n,g)}}break;case 5:Xl(p,p.return);break;case 22:if(null!==p.memoizedState){vs(f);continue}}null!==h?(h.return=p,Gl=h):vs(f)}d=d.sibling}e:for(d=null,f=e;;){if(5===f.tag){if(null===d){d=f;try{o=f.stateNode,c?"function"===typeof(i=o.style).setProperty?i.setProperty("display","none","important"):i.display="none":(s=f.stateNode,l=void 0!==(u=f.memoizedProps.style)&&null!==u&&u.hasOwnProperty("display")?u.display:null,s.style.display=he("display",l))}catch(g){wu(e,e.return,g)}}}else if(6===f.tag){if(null===d)try{f.stateNode.nodeValue=c?"":f.memoizedProps}catch(g){wu(e,e.return,g)}}else if((22!==f.tag&&23!==f.tag||null===f.memoizedState||f===e)&&null!==f.child){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;null===f.sibling;){if(null===f.return||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:ds(t,e),ps(e),4&r&&cs(e);case 21:}}function ps(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(ns(n)){var r=n;break e}n=n.return}throw Error(a(160))}switch(r.tag){case 5:var o=r.stateNode;32&r.flags&&(de(o,""),r.flags&=-33),as(e,rs(e),o);break;case 3:case 4:var i=r.stateNode.containerInfo;os(e,rs(e),i);break;default:throw Error(a(161))}}catch(l){wu(e,e.return,l)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function hs(e,t,n){Gl=e,ms(e,t,n)}function ms(e,t,n){for(var r=0!==(1&e.mode);null!==Gl;){var o=Gl,a=o.child;if(22===o.tag&&r){var i=null!==o.memoizedState||Hl;if(!i){var l=o.alternate,s=null!==l&&null!==l.memoizedState||ql;l=Hl;var u=ql;if(Hl=i,(ql=s)&&!u)for(Gl=o;null!==Gl;)s=(i=Gl).child,22===i.tag&&null!==i.memoizedState?ys(o):null!==s?(s.return=i,Gl=s):ys(o);for(;null!==a;)Gl=a,ms(a,t,n),a=a.sibling;Gl=o,Hl=l,ql=u}gs(e)}else 0!==(8772&o.subtreeFlags)&&null!==a?(a.return=o,Gl=a):gs(e)}}function gs(e){for(;null!==Gl;){var t=Gl;if(0!==(8772&t.flags)){var n=t.alternate;try{if(0!==(8772&t.flags))switch(t.tag){case 0:case 11:case 15:ql||Zl(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!ql)if(null===n)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:Ji(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;null!==i&&Fa(t,i,r);break;case 3:var l=t.updateQueue;if(null!==l){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}Fa(t,l,n)}break;case 5:var s=t.stateNode;if(null===n&&4&t.flags){n=s;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var c=t.alternate;if(null!==c){var d=c.memoizedState;if(null!==d){var f=d.dehydrated;null!==f&&Bt(f)}}}break;default:throw Error(a(163))}ql||512&t.flags&&es(t)}catch(p){wu(t,t.return,p)}}if(t===e){Gl=null;break}if(null!==(n=t.sibling)){n.return=t.return,Gl=n;break}Gl=t.return}}function vs(e){for(;null!==Gl;){var t=Gl;if(t===e){Gl=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Gl=n;break}Gl=t.return}}function ys(e){for(;null!==Gl;){var t=Gl;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Zl(4,t)}catch(s){wu(t,n,s)}break;case 1:var r=t.stateNode;if("function"===typeof r.componentDidMount){var o=t.return;try{r.componentDidMount()}catch(s){wu(t,o,s)}}var a=t.return;try{es(t)}catch(s){wu(t,a,s)}break;case 5:var i=t.return;try{es(t)}catch(s){wu(t,i,s)}}}catch(s){wu(t,t.return,s)}if(t===e){Gl=null;break}var l=t.sibling;if(null!==l){l.return=t.return,Gl=l;break}Gl=t.return}}var bs,ws=Math.ceil,xs=w.ReactCurrentDispatcher,Ss=w.ReactCurrentOwner,ks=w.ReactCurrentBatchConfig,Es=0,As=null,Cs=null,Ps=0,Rs=0,Os=xo(0),Ts=0,Ns=null,js=0,Ms=0,_s=0,Ls=null,zs=null,Is=0,Ds=1/0,$s=null,Fs=!1,Bs=null,Us=null,Ws=!1,Vs=null,Hs=0,qs=0,Ks=null,Gs=-1,Xs=0;function Qs(){return 0!==(6&Es)?Qe():-1!==Gs?Gs:Gs=Qe()}function Ys(e){return 0===(1&e.mode)?1:0!==(2&Es)&&0!==Ps?Ps&-Ps:null!==fa.transition?(0===Xs&&(Xs=ht()),Xs):0!==(e=yt)?e:e=void 0===(e=window.event)?16:Xt(e.type)}function Js(e,t,n,r){if(50<qs)throw qs=0,Ks=null,Error(a(185));gt(e,n,r),0!==(2&Es)&&e===As||(e===As&&(0===(2&Es)&&(Ms|=n),4===Ts&&ru(e,Ps)),Zs(e,r),1===n&&0===Es&&0===(1&t.mode)&&(Ds=Qe()+500,zo&&$o()))}function Zs(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,a=e.pendingLanes;0<a;){var i=31-at(a),l=1<<i,s=o[i];-1===s?0!==(l&n)&&0===(l&r)||(o[i]=ft(l,t)):s<=t&&(e.expiredLanes|=l),a&=~l}}(e,t);var r=dt(e,e===As?Ps:0);if(0===r)null!==n&&Ke(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&Ke(n),1===t)0===e.tag?function(e){zo=!0,Do(e)}(ou.bind(null,e)):Do(ou.bind(null,e)),no(function(){0===(6&Es)&&$o()}),n=null;else{switch(bt(r)){case 1:n=Je;break;case 4:n=Ze;break;case 16:default:n=et;break;case 536870912:n=nt}n=Au(n,eu.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function eu(e,t){if(Gs=-1,Xs=0,0!==(6&Es))throw Error(a(327));var n=e.callbackNode;if(yu()&&e.callbackNode!==n)return null;var r=dt(e,e===As?Ps:0);if(0===r)return null;if(0!==(30&r)||0!==(r&e.expiredLanes)||t)t=fu(e,r);else{t=r;var o=Es;Es|=2;var i=cu();for(As===e&&Ps===t||($s=null,Ds=Qe()+500,su(e,t));;)try{hu();break}catch(s){uu(e,s)}ka(),xs.current=i,Es=o,null!==Cs?t=0:(As=null,Ps=0,t=Ts)}if(0!==t){if(2===t&&(0!==(o=pt(e))&&(r=o,t=tu(e,o))),1===t)throw n=Ns,su(e,0),ru(e,r),Zs(e,Qe()),n;if(6===t)ru(e,r);else{if(o=e.current.alternate,0===(30&r)&&!function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var o=n[r],a=o.getSnapshot;o=o.value;try{if(!rr(a(),o))return!1}catch(l){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(o)&&(2===(t=fu(e,r))&&(0!==(i=pt(e))&&(r=i,t=tu(e,i))),1===t))throw n=Ns,su(e,0),ru(e,r),Zs(e,Qe()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(a(345));case 2:case 5:vu(e,zs,$s);break;case 3:if(ru(e,r),(130023424&r)===r&&10<(t=Is+500-Qe())){if(0!==dt(e,0))break;if(((o=e.suspendedLanes)&r)!==r){Qs(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=Zr(vu.bind(null,e,zs,$s),t);break}vu(e,zs,$s);break;case 4:if(ru(e,r),(4194240&r)===r)break;for(t=e.eventTimes,o=-1;0<r;){var l=31-at(r);i=1<<l,(l=t[l])>o&&(o=l),r&=~i}if(r=o,10<(r=(120>(r=Qe()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*ws(r/1960))-r)){e.timeoutHandle=Zr(vu.bind(null,e,zs,$s),r);break}vu(e,zs,$s);break;default:throw Error(a(329))}}}return Zs(e,Qe()),e.callbackNode===n?eu.bind(null,e):null}function tu(e,t){var n=Ls;return e.current.memoizedState.isDehydrated&&(su(e,t).flags|=256),2!==(e=fu(e,t))&&(t=zs,zs=n,null!==t&&nu(t)),e}function nu(e){null===zs?zs=e:zs.push.apply(zs,e)}function ru(e,t){for(t&=~_s,t&=~Ms,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-at(t),r=1<<n;e[n]=-1,t&=~r}}function ou(e){if(0!==(6&Es))throw Error(a(327));yu();var t=dt(e,0);if(0===(1&t))return Zs(e,Qe()),null;var n=fu(e,t);if(0!==e.tag&&2===n){var r=pt(e);0!==r&&(t=r,n=tu(e,r))}if(1===n)throw n=Ns,su(e,0),ru(e,t),Zs(e,Qe()),n;if(6===n)throw Error(a(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,vu(e,zs,$s),Zs(e,Qe()),null}function au(e,t){var n=Es;Es|=1;try{return e(t)}finally{0===(Es=n)&&(Ds=Qe()+500,zo&&$o())}}function iu(e){null!==Vs&&0===Vs.tag&&0===(6&Es)&&yu();var t=Es;Es|=1;var n=ks.transition,r=yt;try{if(ks.transition=null,yt=1,e)return e()}finally{yt=r,ks.transition=n,0===(6&(Es=t))&&$o()}}function lu(){Rs=Os.current,So(Os)}function su(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,eo(n)),null!==Cs)for(n=Cs.return;null!==n;){var r=n;switch(Jo(r),r.tag){case 1:null!==(r=r.type.childContextTypes)&&void 0!==r&&To();break;case 3:Ka(),So(Co),So(Ao),Za();break;case 5:Xa(r);break;case 4:Ka();break;case 13:case 19:So(Qa);break;case 10:Ea(r.type._context);break;case 22:case 23:lu()}n=n.return}if(As=e,Cs=e=Ou(e.current,null),Ps=Rs=t,Ts=0,Ns=null,_s=Ms=js=0,zs=Ls=null,null!==Ra){for(t=0;t<Ra.length;t++)if(null!==(r=(n=Ra[t]).interleaved)){n.interleaved=null;var o=r.next,a=n.pending;if(null!==a){var i=a.next;a.next=o,r.next=i}n.pending=r}Ra=null}return e}function uu(e,t){for(;;){var n=Cs;try{if(ka(),ei.current=Gi,ii){for(var r=ri.memoizedState;null!==r;){var o=r.queue;null!==o&&(o.pending=null),r=r.next}ii=!1}if(ni=0,ai=oi=ri=null,li=!1,si=0,Ss.current=null,null===n||null===n.return){Ts=1,Ns=t,Cs=null;break}e:{var i=e,l=n.return,s=n,u=t;if(t=Ps,s.flags|=32768,null!==u&&"object"===typeof u&&"function"===typeof u.then){var c=u,d=s,f=d.tag;if(0===(1&d.mode)&&(0===f||11===f||15===f)){var p=d.alternate;p?(d.updateQueue=p.updateQueue,d.memoizedState=p.memoizedState,d.lanes=p.lanes):(d.updateQueue=null,d.memoizedState=null)}var h=fl(l);if(null!==h){h.flags&=-257,pl(h,l,s,0,t),1&h.mode&&dl(i,c,t),u=c;var m=(t=h).updateQueue;if(null===m){var g=new Set;g.add(u),t.updateQueue=g}else m.add(u);break e}if(0===(1&t)){dl(i,c,t),du();break e}u=Error(a(426))}else if(ta&&1&s.mode){var v=fl(l);if(null!==v){0===(65536&v.flags)&&(v.flags|=256),pl(v,l,s,0,t),da(al(u,s));break e}}i=u=al(u,s),4!==Ts&&(Ts=2),null===Ls?Ls=[i]:Ls.push(i),i=l;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t,Da(i,ul(0,u,t));break e;case 1:s=u;var y=i.type,b=i.stateNode;if(0===(128&i.flags)&&("function"===typeof y.getDerivedStateFromError||null!==b&&"function"===typeof b.componentDidCatch&&(null===Us||!Us.has(b)))){i.flags|=65536,t&=-t,i.lanes|=t,Da(i,cl(i,s,t));break e}}i=i.return}while(null!==i)}gu(n)}catch(w){t=w,Cs===n&&null!==n&&(Cs=n=n.return);continue}break}}function cu(){var e=xs.current;return xs.current=Gi,null===e?Gi:e}function du(){0!==Ts&&3!==Ts&&2!==Ts||(Ts=4),null===As||0===(268435455&js)&&0===(268435455&Ms)||ru(As,Ps)}function fu(e,t){var n=Es;Es|=2;var r=cu();for(As===e&&Ps===t||($s=null,su(e,t));;)try{pu();break}catch(o){uu(e,o)}if(ka(),Es=n,xs.current=r,null!==Cs)throw Error(a(261));return As=null,Ps=0,Ts}function pu(){for(;null!==Cs;)mu(Cs)}function hu(){for(;null!==Cs&&!Ge();)mu(Cs)}function mu(e){var t=bs(e.alternate,e,Rs);e.memoizedProps=e.pendingProps,null===t?gu(e):Cs=t,Ss.current=null}function gu(e){var t=e;do{var n=t.alternate;if(e=t.return,0===(32768&t.flags)){if(null!==(n=Wl(n,t,Rs)))return void(Cs=n)}else{if(null!==(n=Vl(n,t)))return n.flags&=32767,void(Cs=n);if(null===e)return Ts=6,void(Cs=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}if(null!==(t=t.sibling))return void(Cs=t);Cs=t=e}while(null!==t);0===Ts&&(Ts=5)}function vu(e,t,n){var r=yt,o=ks.transition;try{ks.transition=null,yt=1,function(e,t,n,r){do{yu()}while(null!==Vs);if(0!==(6&Es))throw Error(a(327));n=e.finishedWork;var o=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(a(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-at(n),a=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~a}}(e,i),e===As&&(Cs=As=null,Ps=0),0===(2064&n.subtreeFlags)&&0===(2064&n.flags)||Ws||(Ws=!0,Au(et,function(){return yu(),null})),i=0!==(15990&n.flags),0!==(15990&n.subtreeFlags)||i){i=ks.transition,ks.transition=null;var l=yt;yt=1;var s=Es;Es|=4,Ss.current=null,function(e,t){if(Qr=Wt,ur(e=sr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch(x){n=null;break e}var l=0,s=-1,u=-1,c=0,d=0,f=e,p=null;t:for(;;){for(var h;f!==n||0!==o&&3!==f.nodeType||(s=l+o),f!==i||0!==r&&3!==f.nodeType||(u=l+r),3===f.nodeType&&(l+=f.nodeValue.length),null!==(h=f.firstChild);)p=f,f=h;for(;;){if(f===e)break t;if(p===n&&++c===o&&(s=l),p===i&&++d===r&&(u=l),null!==(h=f.nextSibling))break;p=(f=p).parentNode}f=h}n=-1===s||-1===u?null:{start:s,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(Yr={focusedElem:e,selectionRange:n},Wt=!1,Gl=t;null!==Gl;)if(e=(t=Gl).child,0!==(1028&t.subtreeFlags)&&null!==e)e.return=t,Gl=e;else for(;null!==Gl;){t=Gl;try{var m=t.alternate;if(0!==(1024&t.flags))switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==m){var g=m.memoizedProps,v=m.memoizedState,y=t.stateNode,b=y.getSnapshotBeforeUpdate(t.elementType===t.type?g:Ji(t.type,g),v);y.__reactInternalSnapshotBeforeUpdate=b}break;case 3:var w=t.stateNode.containerInfo;1===w.nodeType?w.textContent="":9===w.nodeType&&w.documentElement&&w.removeChild(w.documentElement);break;default:throw Error(a(163))}}catch(x){wu(t,t.return,x)}if(null!==(e=t.sibling)){e.return=t.return,Gl=e;break}Gl=t.return}m=Yl,Yl=!1}(e,n),fs(n,e),cr(Yr),Wt=!!Qr,Yr=Qr=null,e.current=n,hs(n,e,o),Xe(),Es=s,yt=l,ks.transition=i}else e.current=n;if(Ws&&(Ws=!1,Vs=e,Hs=o),i=e.pendingLanes,0===i&&(Us=null),function(e){if(ot&&"function"===typeof ot.onCommitFiberRoot)try{ot.onCommitFiberRoot(rt,e,void 0,128===(128&e.current.flags))}catch(t){}}(n.stateNode),Zs(e,Qe()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(Fs)throw Fs=!1,e=Bs,Bs=null,e;0!==(1&Hs)&&0!==e.tag&&yu(),i=e.pendingLanes,0!==(1&i)?e===Ks?qs++:(qs=0,Ks=e):qs=0,$o()}(e,t,n,r)}finally{ks.transition=o,yt=r}return null}function yu(){if(null!==Vs){var e=bt(Hs),t=ks.transition,n=yt;try{if(ks.transition=null,yt=16>e?16:e,null===Vs)var r=!1;else{if(e=Vs,Vs=null,Hs=0,0!==(6&Es))throw Error(a(331));var o=Es;for(Es|=4,Gl=e.current;null!==Gl;){var i=Gl,l=i.child;if(0!==(16&Gl.flags)){var s=i.deletions;if(null!==s){for(var u=0;u<s.length;u++){var c=s[u];for(Gl=c;null!==Gl;){var d=Gl;switch(d.tag){case 0:case 11:case 15:Jl(8,d,i)}var f=d.child;if(null!==f)f.return=d,Gl=f;else for(;null!==Gl;){var p=(d=Gl).sibling,h=d.return;if(ts(d),d===c){Gl=null;break}if(null!==p){p.return=h,Gl=p;break}Gl=h}}}var m=i.alternate;if(null!==m){var g=m.child;if(null!==g){m.child=null;do{var v=g.sibling;g.sibling=null,g=v}while(null!==g)}}Gl=i}}if(0!==(2064&i.subtreeFlags)&&null!==l)l.return=i,Gl=l;else e:for(;null!==Gl;){if(0!==(2048&(i=Gl).flags))switch(i.tag){case 0:case 11:case 15:Jl(9,i,i.return)}var y=i.sibling;if(null!==y){y.return=i.return,Gl=y;break e}Gl=i.return}}var b=e.current;for(Gl=b;null!==Gl;){var w=(l=Gl).child;if(0!==(2064&l.subtreeFlags)&&null!==w)w.return=l,Gl=w;else e:for(l=b;null!==Gl;){if(0!==(2048&(s=Gl).flags))try{switch(s.tag){case 0:case 11:case 15:Zl(9,s)}}catch(S){wu(s,s.return,S)}if(s===l){Gl=null;break e}var x=s.sibling;if(null!==x){x.return=s.return,Gl=x;break e}Gl=s.return}}if(Es=o,$o(),ot&&"function"===typeof ot.onPostCommitFiberRoot)try{ot.onPostCommitFiberRoot(rt,e)}catch(S){}r=!0}return r}finally{yt=n,ks.transition=t}}return!1}function bu(e,t,n){e=za(e,t=ul(0,t=al(n,t),1),1),t=Qs(),null!==e&&(gt(e,1,t),Zs(e,t))}function wu(e,t,n){if(3===e.tag)bu(e,e,n);else for(;null!==t;){if(3===t.tag){bu(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===Us||!Us.has(r))){t=za(t,e=cl(t,e=al(n,e),1),1),e=Qs(),null!==t&&(gt(t,1,e),Zs(t,e));break}}t=t.return}}function xu(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=Qs(),e.pingedLanes|=e.suspendedLanes&n,As===e&&(Ps&n)===n&&(4===Ts||3===Ts&&(130023424&Ps)===Ps&&500>Qe()-Is?su(e,0):_s|=n),Zs(e,t)}function Su(e,t){0===t&&(0===(1&e.mode)?t=1:(t=ut,0===(130023424&(ut<<=1))&&(ut=4194304)));var n=Qs();null!==(e=Na(e,t))&&(gt(e,t,n),Zs(e,n))}function ku(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),Su(e,n)}function Eu(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;null!==o&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(a(314))}null!==r&&r.delete(t),Su(e,n)}function Au(e,t){return qe(e,t)}function Cu(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Pu(e,t,n,r){return new Cu(e,t,n,r)}function Ru(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Ou(e,t){var n=e.alternate;return null===n?((n=Pu(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Tu(e,t,n,r,o,i){var l=2;if(r=e,"function"===typeof e)Ru(e)&&(l=1);else if("string"===typeof e)l=5;else e:switch(e){case k:return Nu(n.children,o,i,t);case E:l=8,o|=8;break;case A:return(e=Pu(12,n,t,2|o)).elementType=A,e.lanes=i,e;case O:return(e=Pu(13,n,t,o)).elementType=O,e.lanes=i,e;case T:return(e=Pu(19,n,t,o)).elementType=T,e.lanes=i,e;case M:return ju(n,o,i,t);default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case C:l=10;break e;case P:l=9;break e;case R:l=11;break e;case N:l=14;break e;case j:l=16,r=null;break e}throw Error(a(130,null==e?e:typeof e,""))}return(t=Pu(l,n,t,o)).elementType=e,t.type=r,t.lanes=i,t}function Nu(e,t,n,r){return(e=Pu(7,e,r,t)).lanes=n,e}function ju(e,t,n,r){return(e=Pu(22,e,r,t)).elementType=M,e.lanes=n,e.stateNode={isHidden:!1},e}function Mu(e,t,n){return(e=Pu(6,e,null,t)).lanes=n,e}function _u(e,t,n){return(t=Pu(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Lu(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=mt(0),this.expirationTimes=mt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=mt(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function zu(e,t,n,r,o,a,i,l,s){return e=new Lu(e,t,n,l,s),1===t?(t=1,!0===a&&(t|=8)):t=0,a=Pu(3,null,null,t),e.current=a,a.stateNode=e,a.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Ma(a),e}function Iu(e){if(!e)return Eo;e:{if(Be(e=e._reactInternals)!==e||1!==e.tag)throw Error(a(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Oo(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(a(171))}if(1===e.tag){var n=e.type;if(Oo(n))return jo(e,n,t)}return t}function Du(e,t,n,r,o,a,i,l,s){return(e=zu(n,r,!0,e,0,a,0,l,s)).context=Iu(null),n=e.current,(a=La(r=Qs(),o=Ys(n))).callback=void 0!==t&&null!==t?t:null,za(n,a,o),e.current.lanes=o,gt(e,o,r),Zs(e,r),e}function $u(e,t,n,r){var o=t.current,a=Qs(),i=Ys(o);return n=Iu(n),null===t.context?t.context=n:t.pendingContext=n,(t=La(a,i)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=za(o,t,i))&&(Js(e,o,i,a),Ia(e,o,i)),i}function Fu(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function Bu(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function Uu(e,t){Bu(e,t),(e=e.alternate)&&Bu(e,t)}bs=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||Co.current)ml=!0;else{if(0===(e.lanes&n)&&0===(128&t.flags))return ml=!1,function(e,t,n){switch(t.tag){case 3:Al(t),ca();break;case 5:Ga(t);break;case 1:Oo(t.type)&&Mo(t);break;case 4:qa(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;ko(ba,r._currentValue),r._currentValue=o;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(ko(Qa,1&Qa.current),t.flags|=128,null):0!==(n&t.child.childLanes)?Ml(e,t,n):(ko(Qa,1&Qa.current),null!==(e=Fl(e,t,n))?e.sibling:null);ko(Qa,1&Qa.current);break;case 19:if(r=0!==(n&t.childLanes),0!==(128&e.flags)){if(r)return Dl(e,t,n);t.flags|=128}if(null!==(o=t.memoizedState)&&(o.rendering=null,o.tail=null,o.lastEffect=null),ko(Qa,Qa.current),r)break;return null;case 22:case 23:return t.lanes=0,wl(e,t,n)}return Fl(e,t,n)}(e,t,n);ml=0!==(131072&e.flags)}else ml=!1,ta&&0!==(1048576&t.flags)&&Qo(t,Wo,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;$l(e,t),e=t.pendingProps;var o=Ro(t,Ao.current);Ca(t,n),o=fi(null,t,r,e,o,n);var i=pi();return t.flags|=1,"object"===typeof o&&null!==o&&"function"===typeof o.render&&void 0===o.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Oo(r)?(i=!0,Mo(t)):i=!1,t.memoizedState=null!==o.state&&void 0!==o.state?o.state:null,Ma(t),o.updater=el,t.stateNode=o,o._reactInternals=t,ol(t,r,e,n),t=El(null,t,r,!0,i,n)):(t.tag=0,ta&&i&&Yo(t),gl(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch($l(e,t),e=t.pendingProps,r=(o=r._init)(r._payload),t.type=r,o=t.tag=function(e){if("function"===typeof e)return Ru(e)?1:0;if(void 0!==e&&null!==e){if((e=e.$$typeof)===R)return 11;if(e===N)return 14}return 2}(r),e=Ji(r,e),o){case 0:t=Sl(null,t,r,e,n);break e;case 1:t=kl(null,t,r,e,n);break e;case 11:t=vl(null,t,r,e,n);break e;case 14:t=yl(null,t,r,Ji(r.type,e),n);break e}throw Error(a(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,Sl(e,t,r,o=t.elementType===r?o:Ji(r,o),n);case 1:return r=t.type,o=t.pendingProps,kl(e,t,r,o=t.elementType===r?o:Ji(r,o),n);case 3:e:{if(Al(t),null===e)throw Error(a(387));r=t.pendingProps,o=(i=t.memoizedState).element,_a(e,t),$a(t,r,null,n);var l=t.memoizedState;if(r=l.element,i.isDehydrated){if(i={element:r,isDehydrated:!1,cache:l.cache,pendingSuspenseBoundaries:l.pendingSuspenseBoundaries,transitions:l.transitions},t.updateQueue.baseState=i,t.memoizedState=i,256&t.flags){t=Cl(e,t,r,n,o=al(Error(a(423)),t));break e}if(r!==o){t=Cl(e,t,r,n,o=al(Error(a(424)),t));break e}for(ea=ao(t.stateNode.containerInfo.firstChild),Zo=t,ta=!0,na=null,n=ya(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(ca(),r===o){t=Fl(e,t,n);break e}gl(e,t,r,n)}t=t.child}return t;case 5:return Ga(t),null===e&&ia(t),r=t.type,o=t.pendingProps,i=null!==e?e.memoizedProps:null,l=o.children,Jr(r,o)?l=null:null!==i&&Jr(r,i)&&(t.flags|=32),xl(e,t),gl(e,t,l,n),t.child;case 6:return null===e&&ia(t),null;case 13:return Ml(e,t,n);case 4:return qa(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=va(t,null,r,n):gl(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,vl(e,t,r,o=t.elementType===r?o:Ji(r,o),n);case 7:return gl(e,t,t.pendingProps,n),t.child;case 8:case 12:return gl(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,l=o.value,ko(ba,r._currentValue),r._currentValue=l,null!==i)if(rr(i.value,l)){if(i.children===o.children&&!Co.current){t=Fl(e,t,n);break e}}else for(null!==(i=t.child)&&(i.return=t);null!==i;){var s=i.dependencies;if(null!==s){l=i.child;for(var u=s.firstContext;null!==u;){if(u.context===r){if(1===i.tag){(u=La(-1,n&-n)).tag=2;var c=i.updateQueue;if(null!==c){var d=(c=c.shared).pending;null===d?u.next=u:(u.next=d.next,d.next=u),c.pending=u}}i.lanes|=n,null!==(u=i.alternate)&&(u.lanes|=n),Aa(i.return,n,t),s.lanes|=n;break}u=u.next}}else if(10===i.tag)l=i.type===t.type?null:i.child;else if(18===i.tag){if(null===(l=i.return))throw Error(a(341));l.lanes|=n,null!==(s=l.alternate)&&(s.lanes|=n),Aa(l,n,t),l=i.sibling}else l=i.child;if(null!==l)l.return=i;else for(l=i;null!==l;){if(l===t){l=null;break}if(null!==(i=l.sibling)){i.return=l.return,l=i;break}l=l.return}i=l}gl(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,Ca(t,n),r=r(o=Pa(o)),t.flags|=1,gl(e,t,r,n),t.child;case 14:return o=Ji(r=t.type,t.pendingProps),yl(e,t,r,o=Ji(r.type,o),n);case 15:return bl(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ji(r,o),$l(e,t),t.tag=1,Oo(r)?(e=!0,Mo(t)):e=!1,Ca(t,n),nl(t,r,o),ol(t,r,o,n),El(null,t,r,!0,e,n);case 19:return Dl(e,t,n);case 22:return wl(e,t,n)}throw Error(a(156,t.tag))};var Wu="function"===typeof reportError?reportError:function(e){console.error(e)};function Vu(e){this._internalRoot=e}function Hu(e){this._internalRoot=e}function qu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Ku(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Gu(){}function Xu(e,t,n,r,o){var a=n._reactRootContainer;if(a){var i=a;if("function"===typeof o){var l=o;o=function(){var e=Fu(i);l.call(e)}}$u(t,i,e,o)}else i=function(e,t,n,r,o){if(o){if("function"===typeof r){var a=r;r=function(){var e=Fu(i);a.call(e)}}var i=Du(t,r,e,0,null,!1,0,"",Gu);return e._reactRootContainer=i,e[co]=i.current,Dr(8===e.nodeType?e.parentNode:e),iu(),i}for(;o=e.lastChild;)e.removeChild(o);if("function"===typeof r){var l=r;r=function(){var e=Fu(s);l.call(e)}}var s=zu(e,0,!1,null,0,!1,0,"",Gu);return e._reactRootContainer=s,e[co]=s.current,Dr(8===e.nodeType?e.parentNode:e),iu(function(){$u(t,s,n,r)}),s}(n,t,e,o,r);return Fu(i)}Hu.prototype.render=Vu.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(a(409));$u(e,t,null,null)},Hu.prototype.unmount=Vu.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;iu(function(){$u(null,e,null,null)}),t[co]=null}},Hu.prototype.unstable_scheduleHydration=function(e){if(e){var t=kt();e={blockedOn:null,target:e,priority:t};for(var n=0;n<jt.length&&0!==t&&t<jt[n].priority;n++);jt.splice(n,0,e),0===n&&zt(e)}},wt=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=ct(t.pendingLanes);0!==n&&(vt(t,1|n),Zs(t,Qe()),0===(6&Es)&&(Ds=Qe()+500,$o()))}break;case 13:iu(function(){var t=Na(e,1);if(null!==t){var n=Qs();Js(t,e,1,n)}}),Uu(e,1)}},xt=function(e){if(13===e.tag){var t=Na(e,134217728);if(null!==t)Js(t,e,134217728,Qs());Uu(e,134217728)}},St=function(e){if(13===e.tag){var t=Ys(e),n=Na(e,t);if(null!==n)Js(n,e,t,Qs());Uu(e,t)}},kt=function(){return yt},Et=function(e,t){var n=yt;try{return yt=e,t()}finally{yt=n}},xe=function(e,t,n){switch(t){case"input":if(J(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=yo(r);if(!o)throw Error(a(90));K(r),J(r,o)}}}break;case"textarea":ae(e,n);break;case"select":null!=(t=n.value)&&ne(e,!!n.multiple,t,!1)}},Pe=au,Re=iu;var Qu={usingClientEntryPoint:!1,Events:[go,vo,yo,Ae,Ce,au]},Yu={findFiberByHostInstance:mo,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Ju={bundleType:Yu.bundleType,version:Yu.version,rendererPackageName:Yu.rendererPackageName,rendererConfig:Yu.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:w.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=Ve(e))?null:e.stateNode},findFiberByHostInstance:Yu.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var Zu=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Zu.isDisabled&&Zu.supportsFiber)try{rt=Zu.inject(Ju),ot=Zu}catch(ec){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Qu,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!qu(t))throw Error(a(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:S,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.createRoot=function(e,t){if(!qu(e))throw Error(a(299));var n=!1,r="",o=Wu;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(o=t.onRecoverableError)),t=zu(e,1,!1,null,0,n,0,r,o),e[co]=t.current,Dr(8===e.nodeType?e.parentNode:e),new Vu(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(a(188));throw e=Object.keys(e).join(","),Error(a(268,e))}return e=null===(e=Ve(t))?null:e.stateNode},t.flushSync=function(e){return iu(e)},t.hydrate=function(e,t,n){if(!Ku(t))throw Error(a(200));return Xu(null,e,t,!0,n)},t.hydrateRoot=function(e,t,n){if(!qu(e))throw Error(a(405));var r=null!=n&&n.hydratedSources||null,o=!1,i="",l=Wu;if(null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(o=!0),void 0!==n.identifierPrefix&&(i=n.identifierPrefix),void 0!==n.onRecoverableError&&(l=n.onRecoverableError)),t=Du(t,null,e,1,null!=n?n:null,o,0,i,l),e[co]=t.current,Dr(e),r)for(e=0;e<r.length;e++)o=(o=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new Hu(t)},t.render=function(e,t,n){if(!Ku(t))throw Error(a(200));return Xu(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!Ku(e))throw Error(a(40));return!!e._reactRootContainer&&(iu(function(){Xu(null,null,e,!1,function(){e._reactRootContainer=null,e[co]=null})}),!0)},t.unstable_batchedUpdates=au,t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Ku(n))throw Error(a(200));if(null==e||void 0===e._reactInternals)throw Error(a(38));return Xu(e,t,n,!1,r)},t.version="18.3.1-next-f1338f8080-20240426"},2814:(e,t,n)=>{"use strict";n.d(t,{s:()=>i,t:()=>a});var r=n(5043);function o(e,t){if("function"===typeof e)return e(t);null!==e&&void 0!==e&&(e.current=t)}function a(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return e=>{let n=!1;const r=t.map(t=>{const r=o(t,e);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let e=0;e<r.length;e++){const n=r[e];"function"==typeof n?n():o(t[e],null)}}}}function i(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return r.useCallback(a(...t),t)}},2894:(e,t,n)=>{"use strict";n.d(t,{C:()=>i});var r=n(5043),o=n(2814),a=n(503);var i=e=>{const{present:t,children:n}=e,i=function(e){const[t,n]=r.useState(),o=r.useRef(null),i=r.useRef(e),s=r.useRef("none"),u=e?"mounted":"unmounted",[c,d]=function(e,t){return r.useReducer((e,n)=>t[e][n]??e,e)}(u,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return r.useEffect(()=>{const e=l(o.current);s.current="mounted"===c?e:"none"},[c]),(0,a.N)(()=>{const t=o.current,n=i.current;if(n!==e){const r=s.current,o=l(t);if(e)d("MOUNT");else if("none"===o||"none"===t?.display)d("UNMOUNT");else{d(n&&r!==o?"ANIMATION_OUT":"UNMOUNT")}i.current=e}},[e,d]),(0,a.N)(()=>{if(t){let e;const n=t.ownerDocument.defaultView??window,r=r=>{const a=l(o.current).includes(r.animationName);if(r.target===t&&a&&(d("ANIMATION_END"),!i.current)){const r=t.style.animationFillMode;t.style.animationFillMode="forwards",e=n.setTimeout(()=>{"forwards"===t.style.animationFillMode&&(t.style.animationFillMode=r)})}},a=e=>{e.target===t&&(s.current=l(o.current))};return t.addEventListener("animationstart",a),t.addEventListener("animationcancel",r),t.addEventListener("animationend",r),()=>{n.clearTimeout(e),t.removeEventListener("animationstart",a),t.removeEventListener("animationcancel",r),t.removeEventListener("animationend",r)}}d("ANIMATION_END")},[t,d]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:r.useCallback(e=>{o.current=e?getComputedStyle(e):null,n(e)},[])}}(t),s="function"===typeof n?n({present:i.isPresent}):r.Children.only(n),u=(0,o.s)(i.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;if(n)return e.ref;if(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n)return e.props.ref;return e.props.ref||e.ref}(s));return"function"===typeof n||i.isPresent?r.cloneElement(s,{ref:u}):null};function l(e){return e?.animationName||"none"}i.displayName="Presence"},3009:(e,t,n)=>{"use strict";n.d(t,{cn:()=>W});var r=n(8387);function o(e){var t=function(e){var t=e.theme,n=e.prefix,r={nextPart:new Map,validators:[]},o=function(e,t){if(!t)return e;return e.map(function(e){return[e[0],e[1].map(function(e){return"string"===typeof e?t+e:"object"===typeof e?Object.fromEntries(Object.entries(e).map(function(e){var n=e[0],r=e[1];return[t+n,r]})):e})]})}(Object.entries(e.classGroups),n);return o.forEach(function(e){var n=e[0];l(e[1],r,n,t)}),r}(e),n=e.conflictingClassGroups,r=e.conflictingClassGroupModifiers,o=void 0===r?{}:r;return{getClassGroupId:function(e){var n=e.split("-");return""===n[0]&&1!==n.length&&n.shift(),a(n,t)||function(e){if(i.test(e)){var t=i.exec(e)[1],n=t?.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}}(e)},getConflictingClassGroupIds:function(e,t){var r=n[e]||[];return t&&o[e]?[].concat(r,o[e]):r}}}function a(e,t){if(0===e.length)return t.classGroupId;var n=e[0],r=t.nextPart.get(n),o=r?a(e.slice(1),r):void 0;if(o)return o;if(0!==t.validators.length){var i=e.join("-");return t.validators.find(function(e){return(0,e.validator)(i)})?.classGroupId}}var i=/^\[(.+)\]$/;function l(e,t,n,r){e.forEach(function(e){if("string"!==typeof e){if("function"===typeof e)return e.isThemeGetter?void l(e(r),t,n,r):void t.validators.push({validator:e,classGroupId:n});Object.entries(e).forEach(function(e){var o=e[0];l(e[1],s(t,o),n,r)})}else{(""===e?t:s(t,e)).classGroupId=n}})}function s(e,t){var n=e;return t.split("-").forEach(function(e){n.nextPart.has(e)||n.nextPart.set(e,{nextPart:new Map,validators:[]}),n=n.nextPart.get(e)}),n}function u(e){if(e<1)return{get:function(){},set:function(){}};var t=0,n=new Map,r=new Map;function o(o,a){n.set(o,a),++t>e&&(t=0,r=n,n=new Map)}return{get:function(e){var t=n.get(e);return void 0!==t?t:void 0!==(t=r.get(e))?(o(e,t),t):void 0},set:function(e,t){n.has(e)?n.set(e,t):o(e,t)}}}function c(e){var t=e.separator||":",n=1===t.length,r=t[0],o=t.length;return function(e){for(var a,i=[],l=0,s=0,u=0;u<e.length;u++){var c=e[u];if(0===l){if(c===r&&(n||e.slice(u,u+o)===t)){i.push(e.slice(s,u)),s=u+o;continue}if("/"===c){a=u;continue}}"["===c?l++:"]"===c&&l--}var d=0===i.length?e:e.substring(s),f=d.startsWith("!");return{modifiers:i,hasImportantModifier:f,baseClassName:f?d.substring(1):d,maybePostfixModifierPosition:a&&a>s?a-s:void 0}}}var d=/\s+/;function f(){for(var e,t,n=0,r="";n<arguments.length;)(e=arguments[n++])&&(t=p(e))&&(r&&(r+=" "),r+=t);return r}function p(e){if("string"===typeof e)return e;for(var t,n="",r=0;r<e.length;r++)e[r]&&(t=p(e[r]))&&(n&&(n+=" "),n+=t);return n}function h(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r,a,i,l=function(e){var n=t[0],d=t.slice(1).reduce(function(e,t){return t(e)},n());return r=function(e){return{cache:u(e.cacheSize),splitModifiers:c(e),...o(e)}}(d),a=r.cache.get,i=r.cache.set,l=s,s(e)};function s(e){var t=a(e);if(t)return t;var n=function(e,t){var n=t.splitModifiers,r=t.getClassGroupId,o=t.getConflictingClassGroupIds,a=new Set;return e.trim().split(d).map(function(e){var t=n(e),o=t.modifiers,a=t.hasImportantModifier,i=t.baseClassName,l=t.maybePostfixModifierPosition,s=r(l?i.substring(0,l):i),u=Boolean(l);if(!s){if(!l)return{isTailwindClass:!1,originalClassName:e};if(!(s=r(i)))return{isTailwindClass:!1,originalClassName:e};u=!1}var c=function(e){if(e.length<=1)return e;var t=[],n=[];return e.forEach(function(e){"["===e[0]?(t.push.apply(t,n.sort().concat([e])),n=[]):n.push(e)}),t.push.apply(t,n.sort()),t}(o).join(":");return{isTailwindClass:!0,modifierId:a?c+"!":c,classGroupId:s,originalClassName:e,hasPostfixModifier:u}}).reverse().filter(function(e){if(!e.isTailwindClass)return!0;var t=e.modifierId,n=e.classGroupId,r=e.hasPostfixModifier,i=t+n;return!a.has(i)&&(a.add(i),o(n,r).forEach(function(e){return a.add(t+e)}),!0)}).reverse().map(function(e){return e.originalClassName}).join(" ")}(e,r);return i(e,n),n}return function(){return l(f.apply(null,arguments))}}function m(e){var t=function(t){return t[e]||[]};return t.isThemeGetter=!0,t}var g=/^\[(?:([a-z-]+):)?(.+)\]$/i,v=/^\d+\/\d+$/,y=new Set(["px","full","screen"]),b=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,w=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,x=/^-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/;function S(e){return R(e)||y.has(e)||v.test(e)||k(e)}function k(e){return L(e,"length",z)}function E(e){return L(e,"size",I)}function A(e){return L(e,"position",I)}function C(e){return L(e,"url",D)}function P(e){return L(e,"number",R)}function R(e){return!Number.isNaN(Number(e))}function O(e){return e.endsWith("%")&&R(e.slice(0,-1))}function T(e){return $(e)||L(e,"number",$)}function N(e){return g.test(e)}function j(){return!0}function M(e){return b.test(e)}function _(e){return L(e,"",F)}function L(e,t,n){var r=g.exec(e);return!!r&&(r[1]?r[1]===t:n(r[2]))}function z(e){return w.test(e)}function I(){return!1}function D(e){return e.startsWith("url(")}function $(e){return Number.isInteger(Number(e))}function F(e){return x.test(e)}function B(){var e=m("colors"),t=m("spacing"),n=m("blur"),r=m("brightness"),o=m("borderColor"),a=m("borderRadius"),i=m("borderSpacing"),l=m("borderWidth"),s=m("contrast"),u=m("grayscale"),c=m("hueRotate"),d=m("invert"),f=m("gap"),p=m("gradientColorStops"),h=m("gradientColorStopPositions"),g=m("inset"),v=m("margin"),y=m("opacity"),b=m("padding"),w=m("saturate"),x=m("scale"),L=m("sepia"),z=m("skew"),I=m("space"),D=m("translate"),$=function(){return["auto",N,t]},F=function(){return[N,t]},B=function(){return["",S]},U=function(){return["auto",R,N]},W=function(){return["","0",N]},V=function(){return[R,P]},H=function(){return[R,N]};return{cacheSize:500,theme:{colors:[j],spacing:[S],blur:["none","",M,N],brightness:V(),borderColor:[e],borderRadius:["none","","full",M,N],borderSpacing:F(),borderWidth:B(),contrast:V(),grayscale:W(),hueRotate:H(),invert:W(),gap:F(),gradientColorStops:[e],gradientColorStopPositions:[O,k],inset:$(),margin:$(),opacity:V(),padding:F(),saturate:V(),scale:V(),sepia:W(),skew:H(),space:F(),translate:F()},classGroups:{aspect:[{aspect:["auto","square","video",N]}],container:["container"],columns:[{columns:[M]}],"break-after":[{"break-after":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-before":[{"break-before":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none"]}],clear:[{clear:["left","right","both","none"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[].concat(["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],[N])}],overflow:[{overflow:["auto","hidden","clip","visible","scroll"]}],"overflow-x":[{"overflow-x":["auto","hidden","clip","visible","scroll"]}],"overflow-y":[{"overflow-y":["auto","hidden","clip","visible","scroll"]}],overscroll:[{overscroll:["auto","contain","none"]}],"overscroll-x":[{"overscroll-x":["auto","contain","none"]}],"overscroll-y":[{"overscroll-y":["auto","contain","none"]}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[g]}],"inset-x":[{"inset-x":[g]}],"inset-y":[{"inset-y":[g]}],start:[{start:[g]}],end:[{end:[g]}],top:[{top:[g]}],right:[{right:[g]}],bottom:[{bottom:[g]}],left:[{left:[g]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",T]}],basis:[{basis:$()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",N]}],grow:[{grow:W()}],shrink:[{shrink:W()}],order:[{order:["first","last","none",T]}],"grid-cols":[{"grid-cols":[j]}],"col-start-end":[{col:["auto",{span:["full",T]},N]}],"col-start":[{"col-start":U()}],"col-end":[{"col-end":U()}],"grid-rows":[{"grid-rows":[j]}],"row-start-end":[{row:["auto",{span:[T]},N]}],"row-start":[{"row-start":U()}],"row-end":[{"row-end":U()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",N]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",N]}],gap:[{gap:[f]}],"gap-x":[{"gap-x":[f]}],"gap-y":[{"gap-y":[f]}],"justify-content":[{justify:["normal"].concat(["start","end","center","between","around","evenly","stretch"])}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal"].concat(["start","end","center","between","around","evenly","stretch"],["baseline"])}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[].concat(["start","end","center","between","around","evenly","stretch"],["baseline"])}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[b]}],px:[{px:[b]}],py:[{py:[b]}],ps:[{ps:[b]}],pe:[{pe:[b]}],pt:[{pt:[b]}],pr:[{pr:[b]}],pb:[{pb:[b]}],pl:[{pl:[b]}],m:[{m:[v]}],mx:[{mx:[v]}],my:[{my:[v]}],ms:[{ms:[v]}],me:[{me:[v]}],mt:[{mt:[v]}],mr:[{mr:[v]}],mb:[{mb:[v]}],ml:[{ml:[v]}],"space-x":[{"space-x":[I]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[I]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit",N,t]}],"min-w":[{"min-w":["min","max","fit",N,S]}],"max-w":[{"max-w":["0","none","full","min","max","fit","prose",{screen:[M]},M,N]}],h:[{h:[N,t,"auto","min","max","fit"]}],"min-h":[{"min-h":["min","max","fit",N,S]}],"max-h":[{"max-h":[N,t,"min","max","fit"]}],"font-size":[{text:["base",M,k]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",P]}],"font-family":[{font:[j]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",N]}],"line-clamp":[{"line-clamp":["none",R,P]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",N,S]}],"list-image":[{"list-image":["none",N]}],"list-style-type":[{list:["none","disc","decimal",N]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[y]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[y]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[].concat(["solid","dashed","dotted","double","none"],["wavy"])}],"text-decoration-thickness":[{decoration:["auto","from-font",S]}],"underline-offset":[{"underline-offset":["auto",N,S]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],indent:[{indent:F()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",N]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",N]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[y]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[].concat(["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],[A])}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",E]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},C]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[h]}],"gradient-via-pos":[{via:[h]}],"gradient-to-pos":[{to:[h]}],"gradient-from":[{from:[p]}],"gradient-via":[{via:[p]}],"gradient-to":[{to:[p]}],rounded:[{rounded:[a]}],"rounded-s":[{"rounded-s":[a]}],"rounded-e":[{"rounded-e":[a]}],"rounded-t":[{"rounded-t":[a]}],"rounded-r":[{"rounded-r":[a]}],"rounded-b":[{"rounded-b":[a]}],"rounded-l":[{"rounded-l":[a]}],"rounded-ss":[{"rounded-ss":[a]}],"rounded-se":[{"rounded-se":[a]}],"rounded-ee":[{"rounded-ee":[a]}],"rounded-es":[{"rounded-es":[a]}],"rounded-tl":[{"rounded-tl":[a]}],"rounded-tr":[{"rounded-tr":[a]}],"rounded-br":[{"rounded-br":[a]}],"rounded-bl":[{"rounded-bl":[a]}],"border-w":[{border:[l]}],"border-w-x":[{"border-x":[l]}],"border-w-y":[{"border-y":[l]}],"border-w-s":[{"border-s":[l]}],"border-w-e":[{"border-e":[l]}],"border-w-t":[{"border-t":[l]}],"border-w-r":[{"border-r":[l]}],"border-w-b":[{"border-b":[l]}],"border-w-l":[{"border-l":[l]}],"border-opacity":[{"border-opacity":[y]}],"border-style":[{border:[].concat(["solid","dashed","dotted","double","none"],["hidden"])}],"divide-x":[{"divide-x":[l]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[l]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[y]}],"divide-style":[{divide:["solid","dashed","dotted","double","none"]}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:[""].concat(["solid","dashed","dotted","double","none"])}],"outline-offset":[{"outline-offset":[N,S]}],"outline-w":[{outline:[S]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:B()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[y]}],"ring-offset-w":[{"ring-offset":[S]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",M,_]}],"shadow-color":[{shadow:[j]}],opacity:[{opacity:[y]}],"mix-blend":[{"mix-blend":["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-lighter"]}],"bg-blend":[{"bg-blend":["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-lighter"]}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[s]}],"drop-shadow":[{"drop-shadow":["","none",M,N]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[c]}],invert:[{invert:[d]}],saturate:[{saturate:[w]}],sepia:[{sepia:[L]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[s]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[c]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[y]}],"backdrop-saturate":[{"backdrop-saturate":[w]}],"backdrop-sepia":[{"backdrop-sepia":[L]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[i]}],"border-spacing-x":[{"border-spacing-x":[i]}],"border-spacing-y":[{"border-spacing-y":[i]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",N]}],duration:[{duration:H()}],ease:[{ease:["linear","in","out","in-out",N]}],delay:[{delay:H()}],animate:[{animate:["none","spin","ping","pulse","bounce",N]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[x]}],"scale-x":[{"scale-x":[x]}],"scale-y":[{"scale-y":[x]}],rotate:[{rotate:[T,N]}],"translate-x":[{"translate-x":[D]}],"translate-y":[{"translate-y":[D]}],"skew-x":[{"skew-x":[z]}],"skew-y":[{"skew-y":[z]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",N]}],accent:[{accent:["auto",e]}],appearance:["appearance-none"],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",N]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":F()}],"scroll-mx":[{"scroll-mx":F()}],"scroll-my":[{"scroll-my":F()}],"scroll-ms":[{"scroll-ms":F()}],"scroll-me":[{"scroll-me":F()}],"scroll-mt":[{"scroll-mt":F()}],"scroll-mr":[{"scroll-mr":F()}],"scroll-mb":[{"scroll-mb":F()}],"scroll-ml":[{"scroll-ml":F()}],"scroll-p":[{"scroll-p":F()}],"scroll-px":[{"scroll-px":F()}],"scroll-py":[{"scroll-py":F()}],"scroll-ps":[{"scroll-ps":F()}],"scroll-pe":[{"scroll-pe":F()}],"scroll-pt":[{"scroll-pt":F()}],"scroll-pr":[{"scroll-pr":F()}],"scroll-pb":[{"scroll-pb":F()}],"scroll-pl":[{"scroll-pl":F()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","pinch-zoom","manipulation",{pan:["x","left","right","y","up","down"]}]}],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",N]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[S,P]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}}var U=h(B);function W(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return U((0,r.$)(t))}},3174:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalStyles:()=>S.A,StyledEngineProvider:()=>x,ThemeContext:()=>o.T,css:()=>v.AH,default:()=>k,internal_processStyles:()=>E,internal_serializeStyles:()=>C,keyframes:()=>v.i7});var r=n(8168),o=n(9369),a=n(6598),i=n(9436),l=n(1722),s=n(5043),u=n(918),c=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|disableRemotePlayback|download|draggable|encType|enterKeyHint|fetchpriority|fetchPriority|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,d=(0,u.A)(function(e){return c.test(e)||111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&e.charCodeAt(2)<91}),f=function(e){return"theme"!==e},p=function(e){return"string"===typeof e&&e.charCodeAt(0)>96?d:f},h=function(e,t,n){var r;if(t){var o=t.shouldForwardProp;r=e.__emotion_forwardProp&&o?function(t){return e.__emotion_forwardProp(t)&&o(t)}:o}return"function"!==typeof r&&n&&(r=e.__emotion_forwardProp),r},m=function(e){var t=e.cache,n=e.serialized,r=e.isStringTag;return(0,l.SF)(t,n,r),(0,i.s)(function(){return(0,l.sk)(t,n,r)}),null},g=function e(t,n){var i,u,c=t.__emotion_real===t,d=c&&t.__emotion_base||t;void 0!==n&&(i=n.label,u=n.target);var f=h(t,n,c),g=f||p(d),v=!g("as");return function(){var y=arguments,b=c&&void 0!==t.__emotion_styles?t.__emotion_styles.slice(0):[];if(void 0!==i&&b.push("label:"+i+";"),null==y[0]||void 0===y[0].raw)b.push.apply(b,y);else{var w=y[0];b.push(w[0]);for(var x=y.length,S=1;S<x;S++)b.push(y[S],w[S])}var k=(0,o.w)(function(e,t,n){var r=v&&e.as||d,i="",c=[],h=e;if(null==e.theme){for(var y in h={},e)h[y]=e[y];h.theme=s.useContext(o.T)}"string"===typeof e.className?i=(0,l.Rk)(t.registered,c,e.className):null!=e.className&&(i=e.className+" ");var w=(0,a.J)(b.concat(c),t.registered,h);i+=t.key+"-"+w.name,void 0!==u&&(i+=" "+u);var x=v&&void 0===f?p(r):g,S={};for(var k in e)v&&"as"===k||x(k)&&(S[k]=e[k]);return S.className=i,n&&(S.ref=n),s.createElement(s.Fragment,null,s.createElement(m,{cache:t,serialized:w,isStringTag:"string"===typeof r}),s.createElement(r,S))});return k.displayName=void 0!==i?i:"Styled("+("string"===typeof d?d:d.displayName||d.name||"Component")+")",k.defaultProps=t.defaultProps,k.__emotion_real=k,k.__emotion_base=d,k.__emotion_styles=b,k.__emotion_forwardProp=f,Object.defineProperty(k,"toString",{value:function(){return"."+u}}),k.withComponent=function(t,o){return e(t,(0,r.A)({},n,o,{shouldForwardProp:h(k,o,!0)})).apply(void 0,b)},k}}.bind(null);["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","marquee","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"].forEach(function(e){g[e]=g(e)});var v=n(3290),y=n(3803),b=n(579);const w=new Map;function x(e){const{injectFirst:t,enableCssLayer:n,children:r}=e,a=s.useMemo(()=>{const e=`${t}-${n}`;if("object"===typeof document&&w.has(e))return w.get(e);const r=function(e,t){const n=(0,y.A)({key:"css",prepend:e});if(t){const e=n.insert;n.insert=function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return n[1].styles.match(/^@layer\s+[^{]*$/)||(n[1].styles=`@layer mui {${n[1].styles}}`),e(...n)}}return n}(t,n);return w.set(e,r),r},[t,n]);return t||n?(0,b.jsx)(o.C,{value:a,children:r}):r}var S=n(869);function k(e,t){return g(e,t)}const E=(e,t)=>{Array.isArray(e.__emotion_styles)&&(e.__emotion_styles=t(e.__emotion_styles))},A=[];function C(e){return A[0]=e,(0,a.J)(A)}},3216:(e,t,n)=>{"use strict";var r;n.d(t,{$P:()=>p,C5:()=>L,Ix:()=>z,V8:()=>_,Ye:()=>w,Zp:()=>v,g:()=>y,jb:()=>u,x$:()=>b,zy:()=>m});var o=n(5043),a=n(1387);function i(){return i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},i.apply(this,arguments)}const l=o.createContext(null);const s=o.createContext(null);const u=o.createContext(null);const c=o.createContext(null);const d=o.createContext({outlet:null,matches:[],isDataRoute:!1});const f=o.createContext(null);function p(e,t){let{relative:n}=void 0===t?{}:t;h()||(0,a.Oi)(!1);let{basename:r,navigator:i}=o.useContext(u),{hash:l,pathname:s,search:c}=b(e,{relative:n}),d=s;return"/"!==r&&(d="/"===s?r:(0,a.HS)([r,s])),i.createHref({pathname:d,search:c,hash:l})}function h(){return null!=o.useContext(c)}function m(){return h()||(0,a.Oi)(!1),o.useContext(c).location}function g(e){o.useContext(u).static||o.useLayoutEffect(e)}function v(){let{isDataRoute:e}=o.useContext(d);return e?function(){let{router:e}=O(P.UseNavigateStable),t=N(R.UseNavigateStable),n=o.useRef(!1);return g(()=>{n.current=!0}),o.useCallback(function(r,o){void 0===o&&(o={}),n.current&&("number"===typeof r?e.navigate(r):e.navigate(r,i({fromRouteId:t},o)))},[e,t])}():function(){h()||(0,a.Oi)(!1);let e=o.useContext(l),{basename:t,future:n,navigator:r}=o.useContext(u),{matches:i}=o.useContext(d),{pathname:s}=m(),c=JSON.stringify((0,a.yD)(i,n.v7_relativeSplatPath)),f=o.useRef(!1);return g(()=>{f.current=!0}),o.useCallback(function(n,o){if(void 0===o&&(o={}),!f.current)return;if("number"===typeof n)return void r.go(n);let i=(0,a.Gh)(n,JSON.parse(c),s,"path"===o.relative);null==e&&"/"!==t&&(i.pathname="/"===i.pathname?t:(0,a.HS)([t,i.pathname])),(o.replace?r.replace:r.push)(i,o.state,o)},[t,r,c,s,e])}()}function y(){let{matches:e}=o.useContext(d),t=e[e.length-1];return t?t.params:{}}function b(e,t){let{relative:n}=void 0===t?{}:t,{future:r}=o.useContext(u),{matches:i}=o.useContext(d),{pathname:l}=m(),s=JSON.stringify((0,a.yD)(i,r.v7_relativeSplatPath));return o.useMemo(()=>(0,a.Gh)(e,JSON.parse(s),l,"path"===n),[e,s,l,n])}function w(e,t){return x(e,t)}function x(e,t,n,r){h()||(0,a.Oi)(!1);let{navigator:l}=o.useContext(u),{matches:s}=o.useContext(d),f=s[s.length-1],p=f?f.params:{},g=(f&&f.pathname,f?f.pathnameBase:"/");f&&f.route;let v,y=m();if(t){var b;let e="string"===typeof t?(0,a.Rr)(t):t;"/"===g||(null==(b=e.pathname)?void 0:b.startsWith(g))||(0,a.Oi)(!1),v=e}else v=y;let w=v.pathname||"/",x=w;if("/"!==g){let e=g.replace(/^\//,"").split("/");x="/"+w.replace(/^\//,"").split("/").slice(e.length).join("/")}let S=(0,a.ue)(e,{pathname:x});let k=C(S&&S.map(e=>Object.assign({},e,{params:Object.assign({},p,e.params),pathname:(0,a.HS)([g,l.encodeLocation?l.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?g:(0,a.HS)([g,l.encodeLocation?l.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])})),s,n,r);return t&&k?o.createElement(c.Provider,{value:{location:i({pathname:"/",search:"",hash:"",state:null,key:"default"},v),navigationType:a.rc.Pop}},k):k}function S(){let e=function(){var e;let t=o.useContext(f),n=T(R.UseRouteError),r=N(R.UseRouteError);if(void 0!==t)return t;return null==(e=n.errors)?void 0:e[r]}(),t=(0,a.pX)(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,r="rgba(200,200,200, 0.5)",i={padding:"0.5rem",backgroundColor:r};return o.createElement(o.Fragment,null,o.createElement("h2",null,"Unexpected Application Error!"),o.createElement("h3",{style:{fontStyle:"italic"}},t),n?o.createElement("pre",{style:i},n):null,null)}const k=o.createElement(S,null);class E extends o.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?o.createElement(d.Provider,{value:this.props.routeContext},o.createElement(f.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function A(e){let{routeContext:t,match:n,children:r}=e,a=o.useContext(l);return a&&a.static&&a.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=n.route.id),o.createElement(d.Provider,{value:t},r)}function C(e,t,n,r){var i;if(void 0===t&&(t=[]),void 0===n&&(n=null),void 0===r&&(r=null),null==e){var l;if(!n)return null;if(n.errors)e=n.matches;else{if(!(null!=(l=r)&&l.v7_partialHydration&&0===t.length&&!n.initialized&&n.matches.length>0))return null;e=n.matches}}let s=e,u=null==(i=n)?void 0:i.errors;if(null!=u){let e=s.findIndex(e=>e.route.id&&void 0!==(null==u?void 0:u[e.route.id]));e>=0||(0,a.Oi)(!1),s=s.slice(0,Math.min(s.length,e+1))}let c=!1,d=-1;if(n&&r&&r.v7_partialHydration)for(let o=0;o<s.length;o++){let e=s[o];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(d=o),e.route.id){let{loaderData:t,errors:r}=n,o=e.route.loader&&void 0===t[e.route.id]&&(!r||void 0===r[e.route.id]);if(e.route.lazy||o){c=!0,s=d>=0?s.slice(0,d+1):[s[0]];break}}}return s.reduceRight((e,r,a)=>{let i,l=!1,f=null,p=null;var h;n&&(i=u&&r.route.id?u[r.route.id]:void 0,f=r.route.errorElement||k,c&&(d<0&&0===a?(h="route-fallback",!1||j[h]||(j[h]=!0),l=!0,p=null):d===a&&(l=!0,p=r.route.hydrateFallbackElement||null)));let m=t.concat(s.slice(0,a+1)),g=()=>{let t;return t=i?f:l?p:r.route.Component?o.createElement(r.route.Component,null):r.route.element?r.route.element:e,o.createElement(A,{match:r,routeContext:{outlet:e,matches:m,isDataRoute:null!=n},children:t})};return n&&(r.route.ErrorBoundary||r.route.errorElement||0===a)?o.createElement(E,{location:n.location,revalidation:n.revalidation,component:f,error:i,children:g(),routeContext:{outlet:null,matches:m,isDataRoute:!0}}):g()},null)}var P=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(P||{}),R=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(R||{});function O(e){let t=o.useContext(l);return t||(0,a.Oi)(!1),t}function T(e){let t=o.useContext(s);return t||(0,a.Oi)(!1),t}function N(e){let t=function(){let e=o.useContext(d);return e||(0,a.Oi)(!1),e}(),n=t.matches[t.matches.length-1];return n.route.id||(0,a.Oi)(!1),n.route.id}const j={};const M=(e,t,n)=>{};function _(e,t){void 0===(null==e?void 0:e.v7_startTransition)&&M("v7_startTransition","React Router will begin wrapping state updates in `React.startTransition` in v7","https://reactrouter.com/v6/upgrading/future#v7_starttransition"),void 0!==(null==e?void 0:e.v7_relativeSplatPath)||t&&void 0!==t.v7_relativeSplatPath||M("v7_relativeSplatPath","Relative route resolution within Splat routes is changing in v7","https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath"),t&&(void 0===t.v7_fetcherPersist&&M("v7_fetcherPersist","The persistence behavior of fetchers is changing in v7","https://reactrouter.com/v6/upgrading/future#v7_fetcherpersist"),void 0===t.v7_normalizeFormMethod&&M("v7_normalizeFormMethod","Casing of `formMethod` fields is being normalized to uppercase in v7","https://reactrouter.com/v6/upgrading/future#v7_normalizeformmethod"),void 0===t.v7_partialHydration&&M("v7_partialHydration","`RouterProvider` hydration behavior is changing in v7","https://reactrouter.com/v6/upgrading/future#v7_partialhydration"),void 0===t.v7_skipActionErrorRevalidation&&M("v7_skipActionErrorRevalidation","The revalidation behavior after 4xx/5xx `action` responses is changing in v7","https://reactrouter.com/v6/upgrading/future#v7_skipactionerrorrevalidation"))}(r||(r=n.t(o,2))).startTransition;function L(e){let{to:t,replace:n,state:r,relative:i}=e;h()||(0,a.Oi)(!1);let{future:l,static:s}=o.useContext(u),{matches:c}=o.useContext(d),{pathname:f}=m(),p=v(),g=(0,a.Gh)(t,(0,a.yD)(c,l.v7_relativeSplatPath),f,"path"===i),y=JSON.stringify(g);return o.useEffect(()=>p(JSON.parse(y),{replace:n,state:r,relative:i}),[p,y,i,n,r]),null}function z(e){let{basename:t="/",children:n=null,location:r,navigationType:l=a.rc.Pop,navigator:s,static:d=!1,future:f}=e;h()&&(0,a.Oi)(!1);let p=t.replace(/^\/*/,"/"),m=o.useMemo(()=>({basename:p,navigator:s,static:d,future:i({v7_relativeSplatPath:!1},f)}),[p,f,s,d]);"string"===typeof r&&(r=(0,a.Rr)(r));let{pathname:g="/",search:v="",hash:y="",state:b=null,key:w="default"}=r,x=o.useMemo(()=>{let e=(0,a.pb)(g,p);return null==e?null:{location:{pathname:e,search:v,hash:y,state:b,key:w},navigationType:l}},[p,g,v,y,b,w,l]);return null==x?null:o.createElement(u.Provider,{value:m},o.createElement(c.Provider,{children:n,value:x}))}new Promise(()=>{});o.Component},3234:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>r.A,extendSxProp:()=>o.A,unstable_createStyleFunctionSx:()=>r.k,unstable_defaultSxConfig:()=>a.A});var r=n(8812),o=n(8698),a=n(7758)},3290:(e,t,n)=>{"use strict";n.d(t,{AH:()=>c,i7:()=>d,mL:()=>u});var r=n(9369),o=n(5043),a=n(1722),i=n(9436),l=n(6598),s=(n(3803),n(219),function(e,t){var n=arguments;if(null==t||!r.h.call(t,"css"))return o.createElement.apply(void 0,n);var a=n.length,i=new Array(a);i[0]=r.E,i[1]=(0,r.c)(e,t);for(var l=2;l<a;l++)i[l]=n[l];return o.createElement.apply(null,i)});!function(e){var t;t||(t=e.JSX||(e.JSX={}))}(s||(s={}));var u=(0,r.w)(function(e,t){var n=e.styles,s=(0,l.J)([n],void 0,o.useContext(r.T)),u=o.useRef();return(0,i.i)(function(){var e=t.key+"-global",n=new t.sheet.constructor({key:e,nonce:t.sheet.nonce,container:t.sheet.container,speedy:t.sheet.isSpeedy}),r=!1,o=document.querySelector('style[data-emotion="'+e+" "+s.name+'"]');return t.sheet.tags.length&&(n.before=t.sheet.tags[0]),null!==o&&(r=!0,o.setAttribute("data-emotion",e),n.hydrate([o])),u.current=[n,r],function(){n.flush()}},[t]),(0,i.i)(function(){var e=u.current,n=e[0];if(e[1])e[1]=!1;else{if(void 0!==s.next&&(0,a.sk)(t,s.next,!0),n.tags.length){var r=n.tags[n.tags.length-1].nextElementSibling;n.before=r,n.flush()}t.insert("",s,n,!1)}},[t,s.name]),null});function c(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,l.J)(t)}function d(){var e=c.apply(void 0,arguments),t="animation-"+e.name;return{name:t,styles:"@keyframes "+t+"{"+e.styles+"}",anim:1,toString:function(){return"_EMO_"+this.name+"_"+this.styles+"_EMO_"}}}},3321:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});var r=n(5043),o=n(7950),a=n(7920),i=n(503),l=n(579),s=r.forwardRef((e,t)=>{const{container:n,...s}=e,[u,c]=r.useState(!1);(0,i.N)(()=>c(!0),[]);const d=n||u&&globalThis?.document?.body;return d?o.createPortal((0,l.jsx)(a.sG.div,{...s,ref:t}),d):null});s.displayName="Portal"},3382:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>s,getFunctionName:()=>a});var r=n(191);const o=/^\s*function(?:\s|\s*\/\*.*\*\/\s*)+([^(\s/]*)\s*/;function a(e){const t=`${e}`.match(o);return t&&t[1]||""}function i(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return e.displayName||e.name||a(e)||t}function l(e,t,n){const r=i(t);return e.displayName||(""!==r?`${n}(${r})`:n)}function s(e){if(null!=e){if("string"===typeof e)return e;if("function"===typeof e)return i(e,"Component");if("object"===typeof e)switch(e.$$typeof){case r.vM:return l(e,e.render,"ForwardRef");case r.lD:return l(e,e.type,"memo");default:return}}}},3642:(e,t,n)=>{"use strict";var r;n.d(t,{i:()=>l});var o=n(5043),a=n(503),i=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||a.N;function l(e){let{prop:t,defaultProp:n,onChange:r=()=>{},caller:a}=e;const[l,s,u]=function(e){let{defaultProp:t,onChange:n}=e;const[r,a]=o.useState(t),l=o.useRef(r),s=o.useRef(n);return i(()=>{s.current=n},[n]),o.useEffect(()=>{l.current!==r&&(s.current?.(r),l.current=r)},[r,l]),[r,a,s]}({defaultProp:n,onChange:r}),c=void 0!==t,d=c?t:l;{const e=o.useRef(void 0!==t);o.useEffect(()=>{const t=e.current;if(t!==c){const e=t?"controlled":"uncontrolled",n=c?"controlled":"uncontrolled";console.warn(`${a} is changing from ${e} to ${n}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}e.current=c},[c,a])}const f=o.useCallback(e=>{if(c){const n=function(e){return"function"===typeof e}(e)?e(t):e;n!==t&&u.current?.(n)}else s(e)},[c,t,s,u]);return[d,f]}Symbol("RADIX:SYNC_STATE")},3763:(e,t,n)=>{"use strict";e.exports=n(4983)},3797:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var r=n(5043),o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};var a=(e,t)=>{const n=(0,r.forwardRef)((n,a)=>{let{color:i="currentColor",size:l=24,strokeWidth:s=2,absoluteStrokeWidth:u,children:c,...d}=n;return(0,r.createElement)("svg",{ref:a,...o,width:l,height:l,stroke:i,strokeWidth:u?24*Number(s)/Number(l):s,className:`lucide lucide-${f=e,f.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,...d},[...t.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...(Array.isArray(c)?c:[c])||[]]);var f});return n.displayName=`${e}`,n}},3803:(e,t,n)=>{"use strict";n.d(t,{A:()=>oe});var r=function(){function e(e){var t=this;this._insertTag=function(e){var n;n=0===t.tags.length?t.insertionPoint?t.insertionPoint.nextSibling:t.prepend?t.container.firstChild:t.before:t.tags[t.tags.length-1].nextSibling,t.container.insertBefore(e,n),t.tags.push(e)},this.isSpeedy=void 0===e.speedy||e.speedy,this.tags=[],this.ctr=0,this.nonce=e.nonce,this.key=e.key,this.container=e.container,this.prepend=e.prepend,this.insertionPoint=e.insertionPoint,this.before=null}var t=e.prototype;return t.hydrate=function(e){e.forEach(this._insertTag)},t.insert=function(e){this.ctr%(this.isSpeedy?65e3:1)===0&&this._insertTag(function(e){var t=document.createElement("style");return t.setAttribute("data-emotion",e.key),void 0!==e.nonce&&t.setAttribute("nonce",e.nonce),t.appendChild(document.createTextNode("")),t.setAttribute("data-s",""),t}(this));var t=this.tags[this.tags.length-1];if(this.isSpeedy){var n=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]}(t);try{n.insertRule(e,n.cssRules.length)}catch(r){}}else t.appendChild(document.createTextNode(e));this.ctr++},t.flush=function(){this.tags.forEach(function(e){var t;return null==(t=e.parentNode)?void 0:t.removeChild(e)}),this.tags=[],this.ctr=0},e}(),o=Math.abs,a=String.fromCharCode,i=Object.assign;function l(e){return e.trim()}function s(e,t,n){return e.replace(t,n)}function u(e,t){return e.indexOf(t)}function c(e,t){return 0|e.charCodeAt(t)}function d(e,t,n){return e.slice(t,n)}function f(e){return e.length}function p(e){return e.length}function h(e,t){return t.push(e),e}var m=1,g=1,v=0,y=0,b=0,w="";function x(e,t,n,r,o,a,i){return{value:e,root:t,parent:n,type:r,props:o,children:a,line:m,column:g,length:i,return:""}}function S(e,t){return i(x("",null,null,"",null,null,0),e,{length:-e.length},t)}function k(){return b=y>0?c(w,--y):0,g--,10===b&&(g=1,m--),b}function E(){return b=y<v?c(w,y++):0,g++,10===b&&(g=1,m++),b}function A(){return c(w,y)}function C(){return y}function P(e,t){return d(w,e,t)}function R(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function O(e){return m=g=1,v=f(w=e),y=0,[]}function T(e){return w="",e}function N(e){return l(P(y-1,_(91===e?e+2:40===e?e+1:e)))}function j(e){for(;(b=A())&&b<33;)E();return R(e)>2||R(b)>3?"":" "}function M(e,t){for(;--t&&E()&&!(b<48||b>102||b>57&&b<65||b>70&&b<97););return P(e,C()+(t<6&&32==A()&&32==E()))}function _(e){for(;E();)switch(b){case e:return y;case 34:case 39:34!==e&&39!==e&&_(b);break;case 40:41===e&&_(e);break;case 92:E()}return y}function L(e,t){for(;E()&&e+b!==57&&(e+b!==84||47!==A()););return"/*"+P(t,y-1)+"*"+a(47===e?e:E())}function z(e){for(;!R(A());)E();return P(e,y)}var I="-ms-",D="-moz-",$="-webkit-",F="comm",B="rule",U="decl",W="@keyframes";function V(e,t){for(var n="",r=p(e),o=0;o<r;o++)n+=t(e[o],o,e,t)||"";return n}function H(e,t,n,r){switch(e.type){case"@layer":if(e.children.length)break;case"@import":case U:return e.return=e.return||e.value;case F:return"";case W:return e.return=e.value+"{"+V(e.children,r)+"}";case B:e.value=e.props.join(",")}return f(n=V(e.children,r))?e.return=e.value+"{"+n+"}":""}function q(e){return T(K("",null,null,null,[""],e=O(e),0,[0],e))}function K(e,t,n,r,o,i,l,d,p){for(var m=0,g=0,v=l,y=0,b=0,w=0,x=1,S=1,P=1,R=0,O="",T=o,_=i,I=r,D=O;S;)switch(w=R,R=E()){case 40:if(108!=w&&58==c(D,v-1)){-1!=u(D+=s(N(R),"&","&\f"),"&\f")&&(P=-1);break}case 34:case 39:case 91:D+=N(R);break;case 9:case 10:case 13:case 32:D+=j(w);break;case 92:D+=M(C()-1,7);continue;case 47:switch(A()){case 42:case 47:h(X(L(E(),C()),t,n),p);break;default:D+="/"}break;case 123*x:d[m++]=f(D)*P;case 125*x:case 59:case 0:switch(R){case 0:case 125:S=0;case 59+g:-1==P&&(D=s(D,/\f/g,"")),b>0&&f(D)-v&&h(b>32?Q(D+";",r,n,v-1):Q(s(D," ","")+";",r,n,v-2),p);break;case 59:D+=";";default:if(h(I=G(D,t,n,m,g,o,d,O,T=[],_=[],v),i),123===R)if(0===g)K(D,t,I,I,T,i,v,d,_);else switch(99===y&&110===c(D,3)?100:y){case 100:case 108:case 109:case 115:K(e,I,I,r&&h(G(e,I,I,0,0,o,d,O,o,T=[],v),_),o,_,v,d,r?T:_);break;default:K(D,I,I,I,[""],_,0,d,_)}}m=g=b=0,x=P=1,O=D="",v=l;break;case 58:v=1+f(D),b=w;default:if(x<1)if(123==R)--x;else if(125==R&&0==x++&&125==k())continue;switch(D+=a(R),R*x){case 38:P=g>0?1:(D+="\f",-1);break;case 44:d[m++]=(f(D)-1)*P,P=1;break;case 64:45===A()&&(D+=N(E())),y=A(),g=v=f(O=D+=z(C())),R++;break;case 45:45===w&&2==f(D)&&(x=0)}}return i}function G(e,t,n,r,a,i,u,c,f,h,m){for(var g=a-1,v=0===a?i:[""],y=p(v),b=0,w=0,S=0;b<r;++b)for(var k=0,E=d(e,g+1,g=o(w=u[b])),A=e;k<y;++k)(A=l(w>0?v[k]+" "+E:s(E,/&\f/g,v[k])))&&(f[S++]=A);return x(e,t,n,0===a?B:c,f,h,m)}function X(e,t,n){return x(e,t,n,F,a(b),d(e,2,-2),0)}function Q(e,t,n,r){return x(e,t,n,U,d(e,0,r),d(e,r+1,-1),r)}var Y=function(e,t,n){for(var r=0,o=0;r=o,o=A(),38===r&&12===o&&(t[n]=1),!R(o);)E();return P(e,y)},J=function(e,t){return T(function(e,t){var n=-1,r=44;do{switch(R(r)){case 0:38===r&&12===A()&&(t[n]=1),e[n]+=Y(y-1,t,n);break;case 2:e[n]+=N(r);break;case 4:if(44===r){e[++n]=58===A()?"&\f":"",t[n]=e[n].length;break}default:e[n]+=a(r)}}while(r=E());return e}(O(e),t))},Z=new WeakMap,ee=function(e){if("rule"===e.type&&e.parent&&!(e.length<1)){for(var t=e.value,n=e.parent,r=e.column===n.column&&e.line===n.line;"rule"!==n.type;)if(!(n=n.parent))return;if((1!==e.props.length||58===t.charCodeAt(0)||Z.get(n))&&!r){Z.set(e,!0);for(var o=[],a=J(t,o),i=n.props,l=0,s=0;l<a.length;l++)for(var u=0;u<i.length;u++,s++)e.props[s]=o[l]?a[l].replace(/&\f/g,i[u]):i[u]+" "+a[l]}}},te=function(e){if("decl"===e.type){var t=e.value;108===t.charCodeAt(0)&&98===t.charCodeAt(2)&&(e.return="",e.value="")}};function ne(e,t){switch(function(e,t){return 45^c(e,0)?(((t<<2^c(e,0))<<2^c(e,1))<<2^c(e,2))<<2^c(e,3):0}(e,t)){case 5103:return $+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return $+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return $+e+D+e+I+e+e;case 6828:case 4268:return $+e+I+e+e;case 6165:return $+e+I+"flex-"+e+e;case 5187:return $+e+s(e,/(\w+).+(:[^]+)/,$+"box-$1$2"+I+"flex-$1$2")+e;case 5443:return $+e+I+"flex-item-"+s(e,/flex-|-self/,"")+e;case 4675:return $+e+I+"flex-line-pack"+s(e,/align-content|flex-|-self/,"")+e;case 5548:return $+e+I+s(e,"shrink","negative")+e;case 5292:return $+e+I+s(e,"basis","preferred-size")+e;case 6060:return $+"box-"+s(e,"-grow","")+$+e+I+s(e,"grow","positive")+e;case 4554:return $+s(e,/([^-])(transform)/g,"$1"+$+"$2")+e;case 6187:return s(s(s(e,/(zoom-|grab)/,$+"$1"),/(image-set)/,$+"$1"),e,"")+e;case 5495:case 3959:return s(e,/(image-set\([^]*)/,$+"$1$`$1");case 4968:return s(s(e,/(.+:)(flex-)?(.*)/,$+"box-pack:$3"+I+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+$+e+e;case 4095:case 3583:case 4068:case 2532:return s(e,/(.+)-inline(.+)/,$+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(f(e)-1-t>6)switch(c(e,t+1)){case 109:if(45!==c(e,t+4))break;case 102:return s(e,/(.+:)(.+)-([^]+)/,"$1"+$+"$2-$3$1"+D+(108==c(e,t+3)?"$3":"$2-$3"))+e;case 115:return~u(e,"stretch")?ne(s(e,"stretch","fill-available"),t)+e:e}break;case 4949:if(115!==c(e,t+1))break;case 6444:switch(c(e,f(e)-3-(~u(e,"!important")&&10))){case 107:return s(e,":",":"+$)+e;case 101:return s(e,/(.+:)([^;!]+)(;|!.+)?/,"$1"+$+(45===c(e,14)?"inline-":"")+"box$3$1"+$+"$2$3$1"+I+"$2box$3")+e}break;case 5936:switch(c(e,t+11)){case 114:return $+e+I+s(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return $+e+I+s(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return $+e+I+s(e,/[svh]\w+-[tblr]{2}/,"lr")+e}return $+e+I+e+e}return e}var re=[function(e,t,n,r){if(e.length>-1&&!e.return)switch(e.type){case U:e.return=ne(e.value,e.length);break;case W:return V([S(e,{value:s(e.value,"@","@"+$)})],r);case B:if(e.length)return function(e,t){return e.map(t).join("")}(e.props,function(t){switch(function(e,t){return(e=t.exec(e))?e[0]:e}(t,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return V([S(e,{props:[s(t,/:(read-\w+)/,":-moz-$1")]})],r);case"::placeholder":return V([S(e,{props:[s(t,/:(plac\w+)/,":"+$+"input-$1")]}),S(e,{props:[s(t,/:(plac\w+)/,":-moz-$1")]}),S(e,{props:[s(t,/:(plac\w+)/,I+"input-$1")]})],r)}return""})}}],oe=function(e){var t=e.key;if("css"===t){var n=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(n,function(e){-1!==e.getAttribute("data-emotion").indexOf(" ")&&(document.head.appendChild(e),e.setAttribute("data-s",""))})}var o,a,i=e.stylisPlugins||re,l={},s=[];o=e.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+t+' "]'),function(e){for(var t=e.getAttribute("data-emotion").split(" "),n=1;n<t.length;n++)l[t[n]]=!0;s.push(e)});var u,c,d=[H,(c=function(e){u.insert(e)},function(e){e.root||(e=e.return)&&c(e)})],f=function(e){var t=p(e);return function(n,r,o,a){for(var i="",l=0;l<t;l++)i+=e[l](n,r,o,a)||"";return i}}([ee,te].concat(i,d));a=function(e,t,n,r){u=n,V(q(e?e+"{"+t.styles+"}":t.styles),f),r&&(h.inserted[t.name]=!0)};var h={key:t,sheet:new r({key:t,container:o,nonce:e.nonce,speedy:e.speedy,prepend:e.prepend,insertionPoint:e.insertionPoint}),nonce:e.nonce,inserted:l,registered:{},insert:a};return h.sheet.hydrate(s),h}},3815:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r=n(9172);const o=function(e,t){return t?(0,r.A)(e,t,{clone:!1}):e}},4202:(e,t)=>{"use strict";var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),l=Symbol.for("react.provider"),s=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.iterator;var h={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,g={};function v(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}function y(){}function b(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}v.prototype.isReactComponent={},v.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},v.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},y.prototype=v.prototype;var w=b.prototype=new y;w.constructor=b,m(w,v.prototype),w.isPureReactComponent=!0;var x=Array.isArray,S=Object.prototype.hasOwnProperty,k={current:null},E={key:!0,ref:!0,__self:!0,__source:!0};function A(e,t,r){var o,a={},i=null,l=null;if(null!=t)for(o in void 0!==t.ref&&(l=t.ref),void 0!==t.key&&(i=""+t.key),t)S.call(t,o)&&!E.hasOwnProperty(o)&&(a[o]=t[o]);var s=arguments.length-2;if(1===s)a.children=r;else if(1<s){for(var u=Array(s),c=0;c<s;c++)u[c]=arguments[c+2];a.children=u}if(e&&e.defaultProps)for(o in s=e.defaultProps)void 0===a[o]&&(a[o]=s[o]);return{$$typeof:n,type:e,key:i,ref:l,props:a,_owner:k.current}}function C(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var P=/\/+/g;function R(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(e){return t[e]})}(""+e.key):t.toString(36)}function O(e,t,o,a,i){var l=typeof e;"undefined"!==l&&"boolean"!==l||(e=null);var s=!1;if(null===e)s=!0;else switch(l){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case n:case r:s=!0}}if(s)return i=i(s=e),e=""===a?"."+R(s,0):a,x(i)?(o="",null!=e&&(o=e.replace(P,"$&/")+"/"),O(i,t,o,"",function(e){return e})):null!=i&&(C(i)&&(i=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(i,o+(!i.key||s&&s.key===i.key?"":(""+i.key).replace(P,"$&/")+"/")+e)),t.push(i)),1;if(s=0,a=""===a?".":a+":",x(e))for(var u=0;u<e.length;u++){var c=a+R(l=e[u],u);s+=O(l,t,o,c,i)}else if(c=function(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=p&&e[p]||e["@@iterator"])?e:null}(e),"function"===typeof c)for(e=c.call(e),u=0;!(l=e.next()).done;)s+=O(l=l.value,t,o,c=a+R(l,u++),i);else if("object"===l)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function T(e,t,n){if(null==e)return e;var r=[],o=0;return O(e,r,"","",function(e){return t.call(n,e,o++)}),r}function N(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)},function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var j={current:null},M={transition:null},_={ReactCurrentDispatcher:j,ReactCurrentBatchConfig:M,ReactCurrentOwner:k};function L(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:T,forEach:function(e,t,n){T(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return T(e,function(){t++}),t},toArray:function(e){return T(e,function(e){return e})||[]},only:function(e){if(!C(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=v,t.Fragment=o,t.Profiler=i,t.PureComponent=b,t.StrictMode=a,t.Suspense=c,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=_,t.act=L,t.cloneElement=function(e,t,r){if(null===e||void 0===e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var o=m({},e.props),a=e.key,i=e.ref,l=e._owner;if(null!=t){if(void 0!==t.ref&&(i=t.ref,l=k.current),void 0!==t.key&&(a=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(u in t)S.call(t,u)&&!E.hasOwnProperty(u)&&(o[u]=void 0===t[u]&&void 0!==s?s[u]:t[u])}var u=arguments.length-2;if(1===u)o.children=r;else if(1<u){s=Array(u);for(var c=0;c<u;c++)s[c]=arguments[c+2];o.children=s}return{$$typeof:n,type:e.type,key:a,ref:i,props:o,_owner:l}},t.createContext=function(e){return(e={$$typeof:s,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:l,_context:e},e.Consumer=e},t.createElement=A,t.createFactory=function(e){var t=A.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=C,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:N}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=M.transition;M.transition={};try{e()}finally{M.transition=t}},t.unstable_act=L,t.useCallback=function(e,t){return j.current.useCallback(e,t)},t.useContext=function(e){return j.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return j.current.useDeferredValue(e)},t.useEffect=function(e,t){return j.current.useEffect(e,t)},t.useId=function(){return j.current.useId()},t.useImperativeHandle=function(e,t,n){return j.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return j.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return j.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return j.current.useMemo(e,t)},t.useReducer=function(e,t,n){return j.current.useReducer(e,t,n)},t.useRef=function(e){return j.current.useRef(e)},t.useState=function(e){return j.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return j.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return j.current.useTransition()},t.version="18.3.1"},4391:(e,t,n)=>{"use strict";var r=n(7950);t.createRoot=r.createRoot,t.hydrateRoot=r.hydrateRoot},4558:(e,t,n)=>{"use strict";n.d(t,{JR:()=>r});const r="http://localhost:5001/api"},4634:e=>{function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},4853:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var r=n(8587),o=n(8168);const a=["values","unit","step"],i=e=>{const t=Object.keys(e).map(t=>({key:t,val:e[t]}))||[];return t.sort((e,t)=>e.val-t.val),t.reduce((e,t)=>(0,o.A)({},e,{[t.key]:t.val}),{})};function l(e){const{values:t={xs:0,sm:600,md:900,lg:1200,xl:1536},unit:n="px",step:l=5}=e,s=(0,r.A)(e,a),u=i(t),c=Object.keys(u);function d(e){return`@media (min-width:${"number"===typeof t[e]?t[e]:e}${n})`}function f(e){return`@media (max-width:${("number"===typeof t[e]?t[e]:e)-l/100}${n})`}function p(e,r){const o=c.indexOf(r);return`@media (min-width:${"number"===typeof t[e]?t[e]:e}${n}) and (max-width:${(-1!==o&&"number"===typeof t[c[o]]?t[c[o]]:r)-l/100}${n})`}return(0,o.A)({keys:c,values:u,up:d,down:f,between:p,only:function(e){return c.indexOf(e)+1<c.length?p(e,c[c.indexOf(e)+1]):d(e)},not:function(e){const t=c.indexOf(e);return 0===t?d(c[1]):t===c.length-1?f(c[t]):p(e,c[c.indexOf(e)+1]).replace("@media","@media not all and")},unit:n},s)}},4893:e=>{e.exports=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n},e.exports.__esModule=!0,e.exports.default=e.exports},4983:(e,t)=>{"use strict";var n="function"===typeof Symbol&&Symbol.for,r=n?Symbol.for("react.element"):60103,o=n?Symbol.for("react.portal"):60106,a=n?Symbol.for("react.fragment"):60107,i=n?Symbol.for("react.strict_mode"):60108,l=n?Symbol.for("react.profiler"):60114,s=n?Symbol.for("react.provider"):60109,u=n?Symbol.for("react.context"):60110,c=n?Symbol.for("react.async_mode"):60111,d=n?Symbol.for("react.concurrent_mode"):60111,f=n?Symbol.for("react.forward_ref"):60112,p=n?Symbol.for("react.suspense"):60113,h=n?Symbol.for("react.suspense_list"):60120,m=n?Symbol.for("react.memo"):60115,g=n?Symbol.for("react.lazy"):60116,v=n?Symbol.for("react.block"):60121,y=n?Symbol.for("react.fundamental"):60117,b=n?Symbol.for("react.responder"):60118,w=n?Symbol.for("react.scope"):60119;function x(e){if("object"===typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case c:case d:case a:case l:case i:case p:return e;default:switch(e=e&&e.$$typeof){case u:case f:case g:case m:case s:return e;default:return t}}case o:return t}}}function S(e){return x(e)===d}t.AsyncMode=c,t.ConcurrentMode=d,t.ContextConsumer=u,t.ContextProvider=s,t.Element=r,t.ForwardRef=f,t.Fragment=a,t.Lazy=g,t.Memo=m,t.Portal=o,t.Profiler=l,t.StrictMode=i,t.Suspense=p,t.isAsyncMode=function(e){return S(e)||x(e)===c},t.isConcurrentMode=S,t.isContextConsumer=function(e){return x(e)===u},t.isContextProvider=function(e){return x(e)===s},t.isElement=function(e){return"object"===typeof e&&null!==e&&e.$$typeof===r},t.isForwardRef=function(e){return x(e)===f},t.isFragment=function(e){return x(e)===a},t.isLazy=function(e){return x(e)===g},t.isMemo=function(e){return x(e)===m},t.isPortal=function(e){return x(e)===o},t.isProfiler=function(e){return x(e)===l},t.isStrictMode=function(e){return x(e)===i},t.isSuspense=function(e){return x(e)===p},t.isValidElementType=function(e){return"string"===typeof e||"function"===typeof e||e===a||e===d||e===l||e===i||e===p||e===h||"object"===typeof e&&null!==e&&(e.$$typeof===g||e.$$typeof===m||e.$$typeof===s||e.$$typeof===u||e.$$typeof===f||e.$$typeof===y||e.$$typeof===b||e.$$typeof===w||e.$$typeof===v)},t.typeOf=x},4989:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>r.A,private_createBreakpoints:()=>o.A,unstable_applyStyles:()=>a.A});var r=n(8280),o=n(4853),a=n(9703)},4994:e=>{e.exports=function(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},5043:(e,t,n)=>{"use strict";e.exports=n(4202)},5463:(e,t,n)=>{"use strict";n.d(t,{N:()=>s});var r=n(5043),o=n(1862),a=n(2814),i=n(6851),l=n(579);function s(e){const t=e+"CollectionProvider",[n,s]=(0,o.A)(t),[u,c]=n(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{const{scope:t,children:n}=e,o=r.useRef(null),a=r.useRef(new Map).current;return(0,l.jsx)(u,{scope:t,itemMap:a,collectionRef:o,children:n})};d.displayName=t;const f=e+"CollectionSlot",p=(0,i.TL)(f),h=r.forwardRef((e,t)=>{const{scope:n,children:r}=e,o=c(f,n),i=(0,a.s)(t,o.collectionRef);return(0,l.jsx)(p,{ref:i,children:r})});h.displayName=f;const m=e+"CollectionItemSlot",g="data-radix-collection-item",v=(0,i.TL)(m),y=r.forwardRef((e,t)=>{const{scope:n,children:o,...i}=e,s=r.useRef(null),u=(0,a.s)(t,s),d=c(m,n);return r.useEffect(()=>(d.itemMap.set(s,{ref:s,...i}),()=>{d.itemMap.delete(s)})),(0,l.jsx)(v,{[g]:"",ref:u,children:o})});return y.displayName=m,[{Provider:d,Slot:h,ItemSlot:y},function(t){const n=c(e+"CollectionConsumer",t);return r.useCallback(()=>{const e=n.collectionRef.current;if(!e)return[];const t=Array.from(e.querySelectorAll(`[${g}]`));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},s]}var u=new WeakMap;Map;function c(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);const n=function(e,t){const n=e.length,r=d(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return-1===n?void 0:e[n]}function d(e){return e!==e||0===e?0:Math.trunc(e)}},5475:(e,t,n)=>{"use strict";var r,o;n.d(t,{Kd:()=>p,N_:()=>g});var a=n(5043),i=n(7950),l=n(3216),s=n(1387);function u(){return u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},u.apply(this,arguments)}function c(e,t){if(null==e)return{};var n,r,o={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);const d=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"];try{window.__reactRouterVersion="6"}catch(b){}new Map;const f=(r||(r=n.t(a,2))).startTransition;(o||(o=n.t(i,2))).flushSync,(r||(r=n.t(a,2))).useId;function p(e){let{basename:t,children:n,future:r,window:o}=e,i=a.useRef();null==i.current&&(i.current=(0,s.zR)({window:o,v5Compat:!0}));let u=i.current,[c,d]=a.useState({action:u.action,location:u.location}),{v7_startTransition:p}=r||{},h=a.useCallback(e=>{p&&f?f(()=>d(e)):d(e)},[d,p]);return a.useLayoutEffect(()=>u.listen(h),[u,h]),a.useEffect(()=>(0,l.V8)(r),[r]),a.createElement(l.Ix,{basename:t,children:n,location:c.location,navigationType:c.action,navigator:u,future:r})}const h="undefined"!==typeof window&&"undefined"!==typeof window.document&&"undefined"!==typeof window.document.createElement,m=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,g=a.forwardRef(function(e,t){let n,{onClick:r,relative:o,reloadDocument:i,replace:f,state:p,target:g,to:v,preventScrollReset:y,viewTransition:w}=e,x=c(e,d),{basename:S}=a.useContext(l.jb),k=!1;if("string"===typeof v&&m.test(v)&&(n=v,h))try{let e=new URL(window.location.href),t=v.startsWith("//")?new URL(e.protocol+v):new URL(v),n=(0,s.pb)(t.pathname,S);t.origin===e.origin&&null!=n?v=n+t.search+t.hash:k=!0}catch(b){}let E=(0,l.$P)(v,{relative:o}),A=function(e,t){let{target:n,replace:r,state:o,preventScrollReset:i,relative:u,viewTransition:c}=void 0===t?{}:t,d=(0,l.Zp)(),f=(0,l.zy)(),p=(0,l.x$)(e,{relative:u});return a.useCallback(t=>{if(function(e,t){return 0===e.button&&(!t||"_self"===t)&&!function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e)}(t,n)){t.preventDefault();let n=void 0!==r?r:(0,s.AO)(f)===(0,s.AO)(p);d(e,{replace:n,state:o,preventScrollReset:i,relative:u,viewTransition:c})}},[f,d,p,r,o,n,e,i,u,c])}(v,{replace:f,state:p,target:g,preventScrollReset:y,relative:o,viewTransition:w});return a.createElement("a",u({},x,{href:n||E,onClick:k||i?r:function(e){r&&r(e),e.defaultPrevented||A(e)},ref:t,target:g}))});var v,y;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(v||(v={})),function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"}(y||(y={}))},6213:(e,t,n)=>{"use strict";n.d(t,{A:()=>bt});var r={};function o(e,t){return function(){return e.apply(t,arguments)}}n.r(r),n.d(r,{hasBrowserEnv:()=>se,hasStandardBrowserEnv:()=>ce,hasStandardBrowserWebWorkerEnv:()=>de,navigator:()=>ue,origin:()=>fe});const{toString:a}=Object.prototype,{getPrototypeOf:i}=Object,{iterator:l,toStringTag:s}=Symbol,u=(c=Object.create(null),e=>{const t=a.call(e);return c[t]||(c[t]=t.slice(8,-1).toLowerCase())});var c;const d=e=>(e=e.toLowerCase(),t=>u(t)===e),f=e=>t=>typeof t===e,{isArray:p}=Array,h=f("undefined");const m=d("ArrayBuffer");const g=f("string"),v=f("function"),y=f("number"),b=e=>null!==e&&"object"===typeof e,w=e=>{if("object"!==u(e))return!1;const t=i(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(s in e)&&!(l in e)},x=d("Date"),S=d("File"),k=d("Blob"),E=d("FileList"),A=d("URLSearchParams"),[C,P,R,O]=["ReadableStream","Request","Response","Headers"].map(d);function T(e,t){let n,r,{allOwnKeys:o=!1}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(null!==e&&"undefined"!==typeof e)if("object"!==typeof e&&(e=[e]),p(e))for(n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else{const r=o?Object.getOwnPropertyNames(e):Object.keys(e),a=r.length;let i;for(n=0;n<a;n++)i=r[n],t.call(null,e[i],i,e)}}function N(e,t){t=t.toLowerCase();const n=Object.keys(e);let r,o=n.length;for(;o-- >0;)if(r=n[o],t===r.toLowerCase())return r;return null}const j="undefined"!==typeof globalThis?globalThis:"undefined"!==typeof self?self:"undefined"!==typeof window?window:global,M=e=>!h(e)&&e!==j;const _=(L="undefined"!==typeof Uint8Array&&i(Uint8Array),e=>L&&e instanceof L);var L;const z=d("HTMLFormElement"),I=(e=>{let{hasOwnProperty:t}=e;return(e,n)=>t.call(e,n)})(Object.prototype),D=d("RegExp"),$=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};T(n,(n,o)=>{let a;!1!==(a=t(n,o,e))&&(r[o]=a||n)}),Object.defineProperties(e,r)};const F=d("AsyncFunction"),B=((e,t)=>{return e?setImmediate:t?(n=`axios@${Math.random()}`,r=[],j.addEventListener("message",e=>{let{source:t,data:o}=e;t===j&&o===n&&r.length&&r.shift()()},!1),e=>{r.push(e),j.postMessage(n,"*")}):e=>setTimeout(e);var n,r})("function"===typeof setImmediate,v(j.postMessage)),U="undefined"!==typeof queueMicrotask?queueMicrotask.bind(j):"undefined"!==typeof process&&process.nextTick||B,W={isArray:p,isArrayBuffer:m,isBuffer:function(e){return null!==e&&!h(e)&&null!==e.constructor&&!h(e.constructor)&&v(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"===typeof FormData&&e instanceof FormData||v(e.append)&&("formdata"===(t=u(e))||"object"===t&&v(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return t="undefined"!==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&m(e.buffer),t},isString:g,isNumber:y,isBoolean:e=>!0===e||!1===e,isObject:b,isPlainObject:w,isReadableStream:C,isRequest:P,isResponse:R,isHeaders:O,isUndefined:h,isDate:x,isFile:S,isBlob:k,isRegExp:D,isFunction:v,isStream:e=>b(e)&&v(e.pipe),isURLSearchParams:A,isTypedArray:_,isFileList:E,forEach:T,merge:function e(){const{caseless:t}=M(this)&&this||{},n={},r=(r,o)=>{const a=t&&N(n,o)||o;w(n[a])&&w(r)?n[a]=e(n[a],r):w(r)?n[a]=e({},r):p(r)?n[a]=r.slice():n[a]=r};for(let o=0,a=arguments.length;o<a;o++)arguments[o]&&T(arguments[o],r);return n},extend:function(e,t,n){let{allOwnKeys:r}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return T(t,(t,r)=>{n&&v(t)?e[r]=o(t,n):e[r]=t},{allOwnKeys:r}),e},trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,t,n,r)=>{let o,a,l;const s={};if(t=t||{},null==e)return t;do{for(o=Object.getOwnPropertyNames(e),a=o.length;a-- >0;)l=o[a],r&&!r(l,e,t)||s[l]||(t[l]=e[l],s[l]=!0);e=!1!==n&&i(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:u,kindOfTest:d,endsWith:(e,t,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return-1!==r&&r===n},toArray:e=>{if(!e)return null;if(p(e))return e;let t=e.length;if(!y(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},forEachEntry:(e,t)=>{const n=(e&&e[l]).call(e);let r;for(;(r=n.next())&&!r.done;){const n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let n;const r=[];for(;null!==(n=e.exec(t));)r.push(n);return r},isHTMLForm:z,hasOwnProperty:I,hasOwnProp:I,reduceDescriptors:$,freezeMethods:e=>{$(e,(t,n)=>{if(v(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=e[n];v(r)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))})},toObjectSet:(e,t)=>{const n={},r=e=>{e.forEach(e=>{n[e]=!0})};return p(e)?r(e):r(String(e).split(t)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(e,t,n){return t.toUpperCase()+n}),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e=+e)?e:t,findKey:N,global:j,isContextDefined:M,isSpecCompliantForm:function(e){return!!(e&&v(e.append)&&"FormData"===e[s]&&e[l])},toJSONObject:e=>{const t=new Array(10),n=(e,r)=>{if(b(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[r]=e;const o=p(e)?[]:{};return T(e,(e,t)=>{const a=n(e,r+1);!h(a)&&(o[t]=a)}),t[r]=void 0,o}}return e};return n(e,0)},isAsyncFn:F,isThenable:e=>e&&(b(e)||v(e))&&v(e.then)&&v(e.catch),setImmediate:B,asap:U,isIterable:e=>null!=e&&v(e[l])};function V(e,t,n,r,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),o&&(this.response=o,this.status=o.status?o.status:null)}W.inherits(V,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:W.toJSONObject(this.config),code:this.code,status:this.status}}});const H=V.prototype,q={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{q[e]={value:e}}),Object.defineProperties(V,q),Object.defineProperty(H,"isAxiosError",{value:!0}),V.from=(e,t,n,r,o,a)=>{const i=Object.create(H);return W.toFlatObject(e,i,function(e){return e!==Error.prototype},e=>"isAxiosError"!==e),V.call(i,e.message,t,n,r,o),i.cause=e,i.name=e.name,a&&Object.assign(i,a),i};const K=V;function G(e){return W.isPlainObject(e)||W.isArray(e)}function X(e){return W.endsWith(e,"[]")?e.slice(0,-2):e}function Q(e,t,n){return e?e.concat(t).map(function(e,t){return e=X(e),!n&&t?"["+e+"]":e}).join(n?".":""):t}const Y=W.toFlatObject(W,{},null,function(e){return/^is[A-Z]/.test(e)});const J=function(e,t,n){if(!W.isObject(e))throw new TypeError("target must be an object");t=t||new FormData;const r=(n=W.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(e,t){return!W.isUndefined(t[e])})).metaTokens,o=n.visitor||u,a=n.dots,i=n.indexes,l=(n.Blob||"undefined"!==typeof Blob&&Blob)&&W.isSpecCompliantForm(t);if(!W.isFunction(o))throw new TypeError("visitor must be a function");function s(e){if(null===e)return"";if(W.isDate(e))return e.toISOString();if(W.isBoolean(e))return e.toString();if(!l&&W.isBlob(e))throw new K("Blob is not supported. Use a Buffer instead.");return W.isArrayBuffer(e)||W.isTypedArray(e)?l&&"function"===typeof Blob?new Blob([e]):Buffer.from(e):e}function u(e,n,o){let l=e;if(e&&!o&&"object"===typeof e)if(W.endsWith(n,"{}"))n=r?n:n.slice(0,-2),e=JSON.stringify(e);else if(W.isArray(e)&&function(e){return W.isArray(e)&&!e.some(G)}(e)||(W.isFileList(e)||W.endsWith(n,"[]"))&&(l=W.toArray(e)))return n=X(n),l.forEach(function(e,r){!W.isUndefined(e)&&null!==e&&t.append(!0===i?Q([n],r,a):null===i?n:n+"[]",s(e))}),!1;return!!G(e)||(t.append(Q(o,n,a),s(e)),!1)}const c=[],d=Object.assign(Y,{defaultVisitor:u,convertValue:s,isVisitable:G});if(!W.isObject(e))throw new TypeError("data must be an object");return function e(n,r){if(!W.isUndefined(n)){if(-1!==c.indexOf(n))throw Error("Circular reference detected in "+r.join("."));c.push(n),W.forEach(n,function(n,a){!0===(!(W.isUndefined(n)||null===n)&&o.call(t,n,W.isString(a)?a.trim():a,r,d))&&e(n,r?r.concat(a):[a])}),c.pop()}}(e),t};function Z(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(e){return t[e]})}function ee(e,t){this._pairs=[],e&&J(e,this,t)}const te=ee.prototype;te.append=function(e,t){this._pairs.push([e,t])},te.toString=function(e){const t=e?function(t){return e.call(this,t,Z)}:Z;return this._pairs.map(function(e){return t(e[0])+"="+t(e[1])},"").join("&")};const ne=ee;function re(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function oe(e,t,n){if(!t)return e;const r=n&&n.encode||re;W.isFunction(n)&&(n={serialize:n});const o=n&&n.serialize;let a;if(a=o?o(t,n):W.isURLSearchParams(t)?t.toString():new ne(t,n).toString(r),a){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+a}return e}const ae=class{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){W.forEach(this.handlers,function(t){null!==t&&e(t)})}},ie={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},le={isBrowser:!0,classes:{URLSearchParams:"undefined"!==typeof URLSearchParams?URLSearchParams:ne,FormData:"undefined"!==typeof FormData?FormData:null,Blob:"undefined"!==typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},se="undefined"!==typeof window&&"undefined"!==typeof document,ue="object"===typeof navigator&&navigator||void 0,ce=se&&(!ue||["ReactNative","NativeScript","NS"].indexOf(ue.product)<0),de="undefined"!==typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"===typeof self.importScripts,fe=se&&window.location.href||"http://localhost",pe={...r,...le};const he=function(e){function t(e,n,r,o){let a=e[o++];if("__proto__"===a)return!0;const i=Number.isFinite(+a),l=o>=e.length;if(a=!a&&W.isArray(r)?r.length:a,l)return W.hasOwnProp(r,a)?r[a]=[r[a],n]:r[a]=n,!i;r[a]&&W.isObject(r[a])||(r[a]=[]);return t(e,n,r[a],o)&&W.isArray(r[a])&&(r[a]=function(e){const t={},n=Object.keys(e);let r;const o=n.length;let a;for(r=0;r<o;r++)a=n[r],t[a]=e[a];return t}(r[a])),!i}if(W.isFormData(e)&&W.isFunction(e.entries)){const n={};return W.forEachEntry(e,(e,r)=>{t(function(e){return W.matchAll(/\w+|\[(\w*)]/g,e).map(e=>"[]"===e[0]?"":e[1]||e[0])}(e),r,n,0)}),n}return null};const me={transitional:ie,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const n=t.getContentType()||"",r=n.indexOf("application/json")>-1,o=W.isObject(e);o&&W.isHTMLForm(e)&&(e=new FormData(e));if(W.isFormData(e))return r?JSON.stringify(he(e)):e;if(W.isArrayBuffer(e)||W.isBuffer(e)||W.isStream(e)||W.isFile(e)||W.isBlob(e)||W.isReadableStream(e))return e;if(W.isArrayBufferView(e))return e.buffer;if(W.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let a;if(o){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(e,t){return J(e,new pe.classes.URLSearchParams,Object.assign({visitor:function(e,t,n,r){return pe.isNode&&W.isBuffer(e)?(this.append(t,e.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},t))}(e,this.formSerializer).toString();if((a=W.isFileList(e))||n.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return J(a?{"files[]":e}:e,t&&new t,this.formSerializer)}}return o||r?(t.setContentType("application/json",!1),function(e,t,n){if(W.isString(e))try{return(t||JSON.parse)(e),W.trim(e)}catch(r){if("SyntaxError"!==r.name)throw r}return(n||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const t=this.transitional||me.transitional,n=t&&t.forcedJSONParsing,r="json"===this.responseType;if(W.isResponse(e)||W.isReadableStream(e))return e;if(e&&W.isString(e)&&(n&&!this.responseType||r)){const n=!(t&&t.silentJSONParsing)&&r;try{return JSON.parse(e)}catch(o){if(n){if("SyntaxError"===o.name)throw K.from(o,K.ERR_BAD_RESPONSE,this,null,this.response);throw o}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:pe.classes.FormData,Blob:pe.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};W.forEach(["delete","get","head","post","put","patch"],e=>{me.headers[e]={}});const ge=me,ve=W.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),ye=Symbol("internals");function be(e){return e&&String(e).trim().toLowerCase()}function we(e){return!1===e||null==e?e:W.isArray(e)?e.map(we):String(e)}function xe(e,t,n,r,o){return W.isFunction(r)?r.call(this,t,n):(o&&(t=n),W.isString(t)?W.isString(r)?-1!==t.indexOf(r):W.isRegExp(r)?r.test(t):void 0:void 0)}class Se{constructor(e){e&&this.set(e)}set(e,t,n){const r=this;function o(e,t,n){const o=be(t);if(!o)throw new Error("header name must be a non-empty string");const a=W.findKey(r,o);(!a||void 0===r[a]||!0===n||void 0===n&&!1!==r[a])&&(r[a||t]=we(e))}const a=(e,t)=>W.forEach(e,(e,n)=>o(e,n,t));if(W.isPlainObject(e)||e instanceof this.constructor)a(e,t);else if(W.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim()))a((e=>{const t={};let n,r,o;return e&&e.split("\n").forEach(function(e){o=e.indexOf(":"),n=e.substring(0,o).trim().toLowerCase(),r=e.substring(o+1).trim(),!n||t[n]&&ve[n]||("set-cookie"===n?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t})(e),t);else if(W.isObject(e)&&W.isIterable(e)){let n,r,o={};for(const t of e){if(!W.isArray(t))throw TypeError("Object iterator must return a key-value pair");o[r=t[0]]=(n=o[r])?W.isArray(n)?[...n,t[1]]:[n,t[1]]:t[1]}a(o,t)}else null!=e&&o(t,e,n);return this}get(e,t){if(e=be(e)){const n=W.findKey(this,e);if(n){const e=this[n];if(!t)return e;if(!0===t)return function(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}(e);if(W.isFunction(t))return t.call(this,e,n);if(W.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=be(e)){const n=W.findKey(this,e);return!(!n||void 0===this[n]||t&&!xe(0,this[n],n,t))}return!1}delete(e,t){const n=this;let r=!1;function o(e){if(e=be(e)){const o=W.findKey(n,e);!o||t&&!xe(0,n[o],o,t)||(delete n[o],r=!0)}}return W.isArray(e)?e.forEach(o):o(e),r}clear(e){const t=Object.keys(this);let n=t.length,r=!1;for(;n--;){const o=t[n];e&&!xe(0,this[o],o,e,!0)||(delete this[o],r=!0)}return r}normalize(e){const t=this,n={};return W.forEach(this,(r,o)=>{const a=W.findKey(n,o);if(a)return t[a]=we(r),void delete t[o];const i=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,t,n)=>t.toUpperCase()+n)}(o):String(o).trim();i!==o&&delete t[o],t[i]=we(r),n[i]=!0}),this}concat(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.constructor.concat(this,...t)}toJSON(e){const t=Object.create(null);return W.forEach(this,(n,r)=>{null!=n&&!1!==n&&(t[r]=e&&W.isArray(n)?n.join(", "):n)}),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(e=>{let[t,n]=e;return t+": "+n}).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e){const t=new this(e);for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return r.forEach(e=>t.set(e)),t}static accessor(e){const t=(this[ye]=this[ye]={accessors:{}}).accessors,n=this.prototype;function r(e){const r=be(e);t[r]||(!function(e,t){const n=W.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(e,n,o){return this[r].call(this,t,e,n,o)},configurable:!0})})}(n,e),t[r]=!0)}return W.isArray(e)?e.forEach(r):r(e),this}}Se.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),W.reduceDescriptors(Se.prototype,(e,t)=>{let{value:n}=e,r=t[0].toUpperCase()+t.slice(1);return{get:()=>n,set(e){this[r]=e}}}),W.freezeMethods(Se);const ke=Se;function Ee(e,t){const n=this||ge,r=t||n,o=ke.from(r.headers);let a=r.data;return W.forEach(e,function(e){a=e.call(n,a,o.normalize(),t?t.status:void 0)}),o.normalize(),a}function Ae(e){return!(!e||!e.__CANCEL__)}function Ce(e,t,n){K.call(this,null==e?"canceled":e,K.ERR_CANCELED,t,n),this.name="CanceledError"}W.inherits(Ce,K,{__CANCEL__:!0});const Pe=Ce;function Re(e,t,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(new K("Request failed with status code "+n.status,[K.ERR_BAD_REQUEST,K.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}const Oe=function(e,t){e=e||10;const n=new Array(e),r=new Array(e);let o,a=0,i=0;return t=void 0!==t?t:1e3,function(l){const s=Date.now(),u=r[i];o||(o=s),n[a]=l,r[a]=s;let c=i,d=0;for(;c!==a;)d+=n[c++],c%=e;if(a=(a+1)%e,a===i&&(i=(i+1)%e),s-o<t)return;const f=u&&s-u;return f?Math.round(1e3*d/f):void 0}};const Te=function(e,t){let n,r,o=0,a=1e3/t;const i=function(t){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Date.now();o=a,n=null,r&&(clearTimeout(r),r=null),e.apply(null,t)};return[function(){const e=Date.now(),t=e-o;for(var l=arguments.length,s=new Array(l),u=0;u<l;u++)s[u]=arguments[u];t>=a?i(s,e):(n=s,r||(r=setTimeout(()=>{r=null,i(n)},a-t)))},()=>n&&i(n)]},Ne=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3,r=0;const o=Oe(50,250);return Te(n=>{const a=n.loaded,i=n.lengthComputable?n.total:void 0,l=a-r,s=o(l);r=a;e({loaded:a,total:i,progress:i?a/i:void 0,bytes:l,rate:s||void 0,estimated:s&&i&&a<=i?(i-a)/s:void 0,event:n,lengthComputable:null!=i,[t?"download":"upload"]:!0})},n)},je=(e,t)=>{const n=null!=e;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},Me=e=>function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return W.asap(()=>e(...n))},_e=pe.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,pe.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(pe.origin),pe.navigator&&/(msie|trident)/i.test(pe.navigator.userAgent)):()=>!0,Le=pe.hasStandardBrowserEnv?{write(e,t,n,r,o,a){const i=[e+"="+encodeURIComponent(t)];W.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),W.isString(r)&&i.push("path="+r),W.isString(o)&&i.push("domain="+o),!0===a&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function ze(e,t,n){let r=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t);return e&&(r||0==n)?function(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t}const Ie=e=>e instanceof ke?{...e}:e;function De(e,t){t=t||{};const n={};function r(e,t,n,r){return W.isPlainObject(e)&&W.isPlainObject(t)?W.merge.call({caseless:r},e,t):W.isPlainObject(t)?W.merge({},t):W.isArray(t)?t.slice():t}function o(e,t,n,o){return W.isUndefined(t)?W.isUndefined(e)?void 0:r(void 0,e,0,o):r(e,t,0,o)}function a(e,t){if(!W.isUndefined(t))return r(void 0,t)}function i(e,t){return W.isUndefined(t)?W.isUndefined(e)?void 0:r(void 0,e):r(void 0,t)}function l(n,o,a){return a in t?r(n,o):a in e?r(void 0,n):void 0}const s={url:a,method:a,data:a,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:l,headers:(e,t,n)=>o(Ie(e),Ie(t),0,!0)};return W.forEach(Object.keys(Object.assign({},e,t)),function(r){const a=s[r]||o,i=a(e[r],t[r],r);W.isUndefined(i)&&a!==l||(n[r]=i)}),n}const $e=e=>{const t=De({},e);let n,{data:r,withXSRFToken:o,xsrfHeaderName:a,xsrfCookieName:i,headers:l,auth:s}=t;if(t.headers=l=ke.from(l),t.url=oe(ze(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),s&&l.set("Authorization","Basic "+btoa((s.username||"")+":"+(s.password?unescape(encodeURIComponent(s.password)):""))),W.isFormData(r))if(pe.hasStandardBrowserEnv||pe.hasStandardBrowserWebWorkerEnv)l.setContentType(void 0);else if(!1!==(n=l.getContentType())){const[e,...t]=n?n.split(";").map(e=>e.trim()).filter(Boolean):[];l.setContentType([e||"multipart/form-data",...t].join("; "))}if(pe.hasStandardBrowserEnv&&(o&&W.isFunction(o)&&(o=o(t)),o||!1!==o&&_e(t.url))){const e=a&&i&&Le.read(i);e&&l.set(a,e)}return t},Fe="undefined"!==typeof XMLHttpRequest&&function(e){return new Promise(function(t,n){const r=$e(e);let o=r.data;const a=ke.from(r.headers).normalize();let i,l,s,u,c,{responseType:d,onUploadProgress:f,onDownloadProgress:p}=r;function h(){u&&u(),c&&c(),r.cancelToken&&r.cancelToken.unsubscribe(i),r.signal&&r.signal.removeEventListener("abort",i)}let m=new XMLHttpRequest;function g(){if(!m)return;const r=ke.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders());Re(function(e){t(e),h()},function(e){n(e),h()},{data:d&&"text"!==d&&"json"!==d?m.response:m.responseText,status:m.status,statusText:m.statusText,headers:r,config:e,request:m}),m=null}m.open(r.method.toUpperCase(),r.url,!0),m.timeout=r.timeout,"onloadend"in m?m.onloadend=g:m.onreadystatechange=function(){m&&4===m.readyState&&(0!==m.status||m.responseURL&&0===m.responseURL.indexOf("file:"))&&setTimeout(g)},m.onabort=function(){m&&(n(new K("Request aborted",K.ECONNABORTED,e,m)),m=null)},m.onerror=function(){n(new K("Network Error",K.ERR_NETWORK,e,m)),m=null},m.ontimeout=function(){let t=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const o=r.transitional||ie;r.timeoutErrorMessage&&(t=r.timeoutErrorMessage),n(new K(t,o.clarifyTimeoutError?K.ETIMEDOUT:K.ECONNABORTED,e,m)),m=null},void 0===o&&a.setContentType(null),"setRequestHeader"in m&&W.forEach(a.toJSON(),function(e,t){m.setRequestHeader(t,e)}),W.isUndefined(r.withCredentials)||(m.withCredentials=!!r.withCredentials),d&&"json"!==d&&(m.responseType=r.responseType),p&&([s,c]=Ne(p,!0),m.addEventListener("progress",s)),f&&m.upload&&([l,u]=Ne(f),m.upload.addEventListener("progress",l),m.upload.addEventListener("loadend",u)),(r.cancelToken||r.signal)&&(i=t=>{m&&(n(!t||t.type?new Pe(null,e,m):t),m.abort(),m=null)},r.cancelToken&&r.cancelToken.subscribe(i),r.signal&&(r.signal.aborted?i():r.signal.addEventListener("abort",i)));const v=function(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(r.url);v&&-1===pe.protocols.indexOf(v)?n(new K("Unsupported protocol "+v+":",K.ERR_BAD_REQUEST,e)):m.send(o||null)})},Be=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let n,r=new AbortController;const o=function(e){if(!n){n=!0,i();const t=e instanceof Error?e:this.reason;r.abort(t instanceof K?t:new Pe(t instanceof Error?t.message:t))}};let a=t&&setTimeout(()=>{a=null,o(new K(`timeout ${t} of ms exceeded`,K.ETIMEDOUT))},t);const i=()=>{e&&(a&&clearTimeout(a),a=null,e.forEach(e=>{e.unsubscribe?e.unsubscribe(o):e.removeEventListener("abort",o)}),e=null)};e.forEach(e=>e.addEventListener("abort",o));const{signal:l}=r;return l.unsubscribe=()=>W.asap(i),l}},Ue=function*(e,t){let n=e.byteLength;if(!t||n<t)return void(yield e);let r,o=0;for(;o<n;)r=o+t,yield e.slice(o,r),o=r},We=async function*(e){if(e[Symbol.asyncIterator])return void(yield*e);const t=e.getReader();try{for(;;){const{done:e,value:n}=await t.read();if(e)break;yield n}}finally{await t.cancel()}},Ve=(e,t,n,r)=>{const o=async function*(e,t){for await(const n of We(e))yield*Ue(n,t)}(e,t);let a,i=0,l=e=>{a||(a=!0,r&&r(e))};return new ReadableStream({async pull(e){try{const{done:t,value:r}=await o.next();if(t)return l(),void e.close();let a=r.byteLength;if(n){let e=i+=a;n(e)}e.enqueue(new Uint8Array(r))}catch(t){throw l(t),t}},cancel:e=>(l(e),o.return())},{highWaterMark:2})},He="function"===typeof fetch&&"function"===typeof Request&&"function"===typeof Response,qe=He&&"function"===typeof ReadableStream,Ke=He&&("function"===typeof TextEncoder?(Ge=new TextEncoder,e=>Ge.encode(e)):async e=>new Uint8Array(await new Response(e).arrayBuffer()));var Ge;const Xe=function(e){try{for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return!!e(...n)}catch(o){return!1}},Qe=qe&&Xe(()=>{let e=!1;const t=new Request(pe.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Ye=qe&&Xe(()=>W.isReadableStream(new Response("").body)),Je={stream:Ye&&(e=>e.body)};var Ze;He&&(Ze=new Response,["text","arrayBuffer","blob","formData","stream"].forEach(e=>{!Je[e]&&(Je[e]=W.isFunction(Ze[e])?t=>t[e]():(t,n)=>{throw new K(`Response type '${e}' is not supported`,K.ERR_NOT_SUPPORT,n)})}));const et=async(e,t)=>{const n=W.toFiniteNumber(e.getContentLength());return null==n?(async e=>{if(null==e)return 0;if(W.isBlob(e))return e.size;if(W.isSpecCompliantForm(e)){const t=new Request(pe.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return W.isArrayBufferView(e)||W.isArrayBuffer(e)?e.byteLength:(W.isURLSearchParams(e)&&(e+=""),W.isString(e)?(await Ke(e)).byteLength:void 0)})(t):n},tt={http:null,xhr:Fe,fetch:He&&(async e=>{let{url:t,method:n,data:r,signal:o,cancelToken:a,timeout:i,onDownloadProgress:l,onUploadProgress:s,responseType:u,headers:c,withCredentials:d="same-origin",fetchOptions:f}=$e(e);u=u?(u+"").toLowerCase():"text";let p,h=Be([o,a&&a.toAbortSignal()],i);const m=h&&h.unsubscribe&&(()=>{h.unsubscribe()});let g;try{if(s&&Qe&&"get"!==n&&"head"!==n&&0!==(g=await et(c,r))){let e,n=new Request(t,{method:"POST",body:r,duplex:"half"});if(W.isFormData(r)&&(e=n.headers.get("content-type"))&&c.setContentType(e),n.body){const[e,t]=je(g,Ne(Me(s)));r=Ve(n.body,65536,e,t)}}W.isString(d)||(d=d?"include":"omit");const o="credentials"in Request.prototype;p=new Request(t,{...f,signal:h,method:n.toUpperCase(),headers:c.normalize().toJSON(),body:r,duplex:"half",credentials:o?d:void 0});let a=await fetch(p,f);const i=Ye&&("stream"===u||"response"===u);if(Ye&&(l||i&&m)){const e={};["status","statusText","headers"].forEach(t=>{e[t]=a[t]});const t=W.toFiniteNumber(a.headers.get("content-length")),[n,r]=l&&je(t,Ne(Me(l),!0))||[];a=new Response(Ve(a.body,65536,n,()=>{r&&r(),m&&m()}),e)}u=u||"text";let v=await Je[W.findKey(Je,u)||"text"](a,e);return!i&&m&&m(),await new Promise((t,n)=>{Re(t,n,{data:v,headers:ke.from(a.headers),status:a.status,statusText:a.statusText,config:e,request:p})})}catch(v){if(m&&m(),v&&"TypeError"===v.name&&/Load failed|fetch/i.test(v.message))throw Object.assign(new K("Network Error",K.ERR_NETWORK,e,p),{cause:v.cause||v});throw K.from(v,v&&v.code,e,p)}})};W.forEach(tt,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(n){}Object.defineProperty(e,"adapterName",{value:t})}});const nt=e=>`- ${e}`,rt=e=>W.isFunction(e)||null===e||!1===e,ot=e=>{e=W.isArray(e)?e:[e];const{length:t}=e;let n,r;const o={};for(let a=0;a<t;a++){let t;if(n=e[a],r=n,!rt(n)&&(r=tt[(t=String(n)).toLowerCase()],void 0===r))throw new K(`Unknown adapter '${t}'`);if(r)break;o[t||"#"+a]=r}if(!r){const e=Object.entries(o).map(e=>{let[t,n]=e;return`adapter ${t} `+(!1===n?"is not supported by the environment":"is not available in the build")});let n=t?e.length>1?"since :\n"+e.map(nt).join("\n"):" "+nt(e[0]):"as no adapter specified";throw new K("There is no suitable adapter to dispatch the request "+n,"ERR_NOT_SUPPORT")}return r};function at(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Pe(null,e)}function it(e){at(e),e.headers=ke.from(e.headers),e.data=Ee.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1);return ot(e.adapter||ge.adapter)(e).then(function(t){return at(e),t.data=Ee.call(e,e.transformResponse,t),t.headers=ke.from(t.headers),t},function(t){return Ae(t)||(at(e),t&&t.response&&(t.response.data=Ee.call(e,e.transformResponse,t.response),t.response.headers=ke.from(t.response.headers))),Promise.reject(t)})}const lt="1.10.0",st={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{st[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}});const ut={};st.transitional=function(e,t,n){function r(e,t){return"[Axios v"+lt+"] Transitional option '"+e+"'"+t+(n?". "+n:"")}return(n,o,a)=>{if(!1===e)throw new K(r(o," has been removed"+(t?" in "+t:"")),K.ERR_DEPRECATED);return t&&!ut[o]&&(ut[o]=!0,console.warn(r(o," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,o,a)}},st.spelling=function(e){return(t,n)=>(console.warn(`${n} is likely a misspelling of ${e}`),!0)};const ct={assertOptions:function(e,t,n){if("object"!==typeof e)throw new K("options must be an object",K.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let o=r.length;for(;o-- >0;){const a=r[o],i=t[a];if(i){const t=e[a],n=void 0===t||i(t,a,e);if(!0!==n)throw new K("option "+a+" must be "+n,K.ERR_BAD_OPTION_VALUE);continue}if(!0!==n)throw new K("Unknown option "+a,K.ERR_BAD_OPTION)}},validators:st},dt=ct.validators;class ft{constructor(e){this.defaults=e||{},this.interceptors={request:new ae,response:new ae}}async request(e,t){try{return await this._request(e,t)}catch(n){if(n instanceof Error){let e={};Error.captureStackTrace?Error.captureStackTrace(e):e=new Error;const t=e.stack?e.stack.replace(/^.+\n/,""):"";try{n.stack?t&&!String(n.stack).endsWith(t.replace(/^.+\n.+\n/,""))&&(n.stack+="\n"+t):n.stack=t}catch(r){}}throw n}}_request(e,t){"string"===typeof e?(t=t||{}).url=e:t=e||{},t=De(this.defaults,t);const{transitional:n,paramsSerializer:r,headers:o}=t;void 0!==n&&ct.assertOptions(n,{silentJSONParsing:dt.transitional(dt.boolean),forcedJSONParsing:dt.transitional(dt.boolean),clarifyTimeoutError:dt.transitional(dt.boolean)},!1),null!=r&&(W.isFunction(r)?t.paramsSerializer={serialize:r}:ct.assertOptions(r,{encode:dt.function,serialize:dt.function},!0)),void 0!==t.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),ct.assertOptions(t,{baseUrl:dt.spelling("baseURL"),withXsrfToken:dt.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let a=o&&W.merge(o.common,o[t.method]);o&&W.forEach(["delete","get","head","post","put","patch","common"],e=>{delete o[e]}),t.headers=ke.concat(a,o);const i=[];let l=!0;this.interceptors.request.forEach(function(e){"function"===typeof e.runWhen&&!1===e.runWhen(t)||(l=l&&e.synchronous,i.unshift(e.fulfilled,e.rejected))});const s=[];let u;this.interceptors.response.forEach(function(e){s.push(e.fulfilled,e.rejected)});let c,d=0;if(!l){const e=[it.bind(this),void 0];for(e.unshift.apply(e,i),e.push.apply(e,s),c=e.length,u=Promise.resolve(t);d<c;)u=u.then(e[d++],e[d++]);return u}c=i.length;let f=t;for(d=0;d<c;){const e=i[d++],t=i[d++];try{f=e(f)}catch(p){t.call(this,p);break}}try{u=it.call(this,f)}catch(p){return Promise.reject(p)}for(d=0,c=s.length;d<c;)u=u.then(s[d++],s[d++]);return u}getUri(e){return oe(ze((e=De(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}}W.forEach(["delete","get","head","options"],function(e){ft.prototype[e]=function(t,n){return this.request(De(n||{},{method:e,url:t,data:(n||{}).data}))}}),W.forEach(["post","put","patch"],function(e){function t(t){return function(n,r,o){return this.request(De(o||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}ft.prototype[e]=t(),ft.prototype[e+"Form"]=t(!0)});const pt=ft;class ht{constructor(e){if("function"!==typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise(function(e){t=e});const n=this;this.promise.then(e=>{if(!n._listeners)return;let t=n._listeners.length;for(;t-- >0;)n._listeners[t](e);n._listeners=null}),this.promise.then=e=>{let t;const r=new Promise(e=>{n.subscribe(e),t=e}).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e(function(e,r,o){n.reason||(n.reason=new Pe(e,r,o),t(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let e;return{token:new ht(function(t){e=t}),cancel:e}}}const mt=ht;const gt={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(gt).forEach(e=>{let[t,n]=e;gt[n]=t});const vt=gt;const yt=function e(t){const n=new pt(t),r=o(pt.prototype.request,n);return W.extend(r,pt.prototype,n,{allOwnKeys:!0}),W.extend(r,n,null,{allOwnKeys:!0}),r.create=function(n){return e(De(t,n))},r}(ge);yt.Axios=pt,yt.CanceledError=Pe,yt.CancelToken=mt,yt.isCancel=Ae,yt.VERSION=lt,yt.toFormData=J,yt.AxiosError=K,yt.Cancel=yt.CanceledError,yt.all=function(e){return Promise.all(e)},yt.spread=function(e){return function(t){return e.apply(null,t)}},yt.isAxiosError=function(e){return W.isObject(e)&&!0===e.isAxiosError},yt.mergeConfig=De,yt.AxiosHeaders=ke,yt.formToJSON=e=>he(W.isHTMLForm(e)?new FormData(e):e),yt.getAdapter=ot,yt.HttpStatusCode=vt,yt.default=yt;const bt=yt},6598:(e,t,n)=>{"use strict";n.d(t,{J:()=>g});var r={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},o=n(918),a=!1,i=/[A-Z]|^ms/g,l=/_EMO_([^_]+?)_([^]*?)_EMO_/g,s=function(e){return 45===e.charCodeAt(1)},u=function(e){return null!=e&&"boolean"!==typeof e},c=(0,o.A)(function(e){return s(e)?e:e.replace(i,"-$&").toLowerCase()}),d=function(e,t){switch(e){case"animation":case"animationName":if("string"===typeof t)return t.replace(l,function(e,t,n){return h={name:t,styles:n,next:h},t})}return 1===r[e]||s(e)||"number"!==typeof t||0===t?t:t+"px"},f="Component selectors can only be used in conjunction with @emotion/babel-plugin, the swc Emotion plugin, or another Emotion-aware compiler transform.";function p(e,t,n){if(null==n)return"";var r=n;if(void 0!==r.__emotion_styles)return r;switch(typeof n){case"boolean":return"";case"object":var o=n;if(1===o.anim)return h={name:o.name,styles:o.styles,next:h},o.name;var i=n;if(void 0!==i.styles){var l=i.next;if(void 0!==l)for(;void 0!==l;)h={name:l.name,styles:l.styles,next:h},l=l.next;return i.styles+";"}return function(e,t,n){var r="";if(Array.isArray(n))for(var o=0;o<n.length;o++)r+=p(e,t,n[o])+";";else for(var i in n){var l=n[i];if("object"!==typeof l){var s=l;null!=t&&void 0!==t[s]?r+=i+"{"+t[s]+"}":u(s)&&(r+=c(i)+":"+d(i,s)+";")}else{if("NO_COMPONENT_SELECTOR"===i&&a)throw new Error(f);if(!Array.isArray(l)||"string"!==typeof l[0]||null!=t&&void 0!==t[l[0]]){var h=p(e,t,l);switch(i){case"animation":case"animationName":r+=c(i)+":"+h+";";break;default:r+=i+"{"+h+"}"}}else for(var m=0;m<l.length;m++)u(l[m])&&(r+=c(i)+":"+d(i,l[m])+";")}}return r}(e,t,n);case"function":if(void 0!==e){var s=h,m=n(e);return h=s,p(e,t,m)}}var g=n;if(null==t)return g;var v=t[g];return void 0!==v?v:g}var h,m=/label:\s*([^\s;{]+)\s*(;|$)/g;function g(e,t,n){if(1===e.length&&"object"===typeof e[0]&&null!==e[0]&&void 0!==e[0].styles)return e[0];var r=!0,o="";h=void 0;var a=e[0];null==a||void 0===a.raw?(r=!1,o+=p(n,t,a)):o+=a[0];for(var i=1;i<e.length;i++){if(o+=p(n,t,e[i]),r)o+=a[i]}m.lastIndex=0;for(var l,s="";null!==(l=m.exec(o));)s+="-"+l[1];var u=function(e){for(var t,n=0,r=0,o=e.length;o>=4;++r,o-=4)t=1540483477*(65535&(t=255&e.charCodeAt(r)|(255&e.charCodeAt(++r))<<8|(255&e.charCodeAt(++r))<<16|(255&e.charCodeAt(++r))<<24))+(59797*(t>>>16)<<16),n=1540483477*(65535&(t^=t>>>24))+(59797*(t>>>16)<<16)^1540483477*(65535&n)+(59797*(n>>>16)<<16);switch(o){case 3:n^=(255&e.charCodeAt(r+2))<<16;case 2:n^=(255&e.charCodeAt(r+1))<<8;case 1:n=1540483477*(65535&(n^=255&e.charCodeAt(r)))+(59797*(n>>>16)<<16)}return(((n=1540483477*(65535&(n^=n>>>13))+(59797*(n>>>16)<<16))^n>>>15)>>>0).toString(36)}(o)+s;return{name:u,styles:o,next:h}}},6851:(e,t,n)=>{"use strict";n.d(t,{DX:()=>l,TL:()=>i});var r=n(5043),o=n(2814),a=n(579);function i(e){const t=s(e),n=r.forwardRef((e,n)=>{const{children:o,...i}=e,l=r.Children.toArray(o),s=l.find(c);if(s){const e=s.props.children,o=l.map(t=>t===s?r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null:t);return(0,a.jsx)(t,{...i,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,o):null})}return(0,a.jsx)(t,{...i,ref:n,children:o})});return n.displayName=`${e}.Slot`,n}var l=i("Slot");function s(e){const t=r.forwardRef((e,t)=>{const{children:n,...a}=e;if(r.isValidElement(n)){const e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;if(n)return e.ref;if(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n)return e.props.ref;return e.props.ref||e.ref}(n),i=function(e,t){const n={...t};for(const r in t){const o=e[r],a=t[r];/^on[A-Z]/.test(r)?o&&a?n[r]=function(){const e=a(...arguments);return o(...arguments),e}:o&&(n[r]=o):"style"===r?n[r]={...o,...a}:"className"===r&&(n[r]=[o,a].filter(Boolean).join(" "))}return{...e,...n}}(a,n.props);return n.type!==r.Fragment&&(i.ref=t?(0,o.t)(t,e):e),r.cloneElement(n,i)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var u=Symbol("radix.slottable");function c(e){return r.isValidElement(e)&&"function"===typeof e.type&&"__radixId"in e.type&&e.type.__radixId===u}},7162:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>l,BO:()=>i,Yn:()=>a});var r=n(7598),o=n(9751);function a(e,t){let n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!t||"string"!==typeof t)return null;if(e&&e.vars&&n){const n=`vars.${t}`.split(".").reduce((e,t)=>e&&e[t]?e[t]:null,e);if(null!=n)return n}return t.split(".").reduce((e,t)=>e&&null!=e[t]?e[t]:null,e)}function i(e,t,n){let r,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:n;return r="function"===typeof e?e(n):Array.isArray(e)?e[n]||o:a(e,n)||o,t&&(r=t(r,o,e)),r}const l=function(e){const{prop:t,cssProperty:n=e.prop,themeKey:l,transform:s}=e,u=e=>{if(null==e[t])return null;const u=e[t],c=a(e.theme,l)||{};return(0,o.NI)(e,u,e=>{let o=i(c,s,e);return e===o&&"string"===typeof e&&(o=i(c,s,`${t}${"default"===e?"":(0,r.A)(e)}`,e)),!1===n?o:{[n]:o}})};return u.propTypes={},u.filterProps=[t],u}},7234:(e,t)=>{"use strict";function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,o=e[r];if(!(0<a(o,t)))break e;e[r]=t,e[n]=o,n=r}}function r(e){return 0===e.length?null:e[0]}function o(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,o=e.length,i=o>>>1;r<i;){var l=2*(r+1)-1,s=e[l],u=l+1,c=e[u];if(0>a(s,n))u<o&&0>a(c,s)?(e[r]=c,e[u]=n,r=u):(e[r]=s,e[l]=n,r=l);else{if(!(u<o&&0>a(c,n)))break e;e[r]=c,e[u]=n,r=u}}}return t}function a(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"===typeof performance&&"function"===typeof performance.now){var i=performance;t.unstable_now=function(){return i.now()}}else{var l=Date,s=l.now();t.unstable_now=function(){return l.now()-s}}var u=[],c=[],d=1,f=null,p=3,h=!1,m=!1,g=!1,v="function"===typeof setTimeout?setTimeout:null,y="function"===typeof clearTimeout?clearTimeout:null,b="undefined"!==typeof setImmediate?setImmediate:null;function w(e){for(var t=r(c);null!==t;){if(null===t.callback)o(c);else{if(!(t.startTime<=e))break;o(c),t.sortIndex=t.expirationTime,n(u,t)}t=r(c)}}function x(e){if(g=!1,w(e),!m)if(null!==r(u))m=!0,M(S);else{var t=r(c);null!==t&&_(x,t.startTime-e)}}function S(e,n){m=!1,g&&(g=!1,y(C),C=-1),h=!0;var a=p;try{for(w(n),f=r(u);null!==f&&(!(f.expirationTime>n)||e&&!O());){var i=f.callback;if("function"===typeof i){f.callback=null,p=f.priorityLevel;var l=i(f.expirationTime<=n);n=t.unstable_now(),"function"===typeof l?f.callback=l:f===r(u)&&o(u),w(n)}else o(u);f=r(u)}if(null!==f)var s=!0;else{var d=r(c);null!==d&&_(x,d.startTime-n),s=!1}return s}finally{f=null,p=a,h=!1}}"undefined"!==typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var k,E=!1,A=null,C=-1,P=5,R=-1;function O(){return!(t.unstable_now()-R<P)}function T(){if(null!==A){var e=t.unstable_now();R=e;var n=!0;try{n=A(!0,e)}finally{n?k():(E=!1,A=null)}}else E=!1}if("function"===typeof b)k=function(){b(T)};else if("undefined"!==typeof MessageChannel){var N=new MessageChannel,j=N.port2;N.port1.onmessage=T,k=function(){j.postMessage(null)}}else k=function(){v(T,0)};function M(e){A=e,E||(E=!0,k())}function _(e,n){C=v(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){m||h||(m=!0,M(S))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):P=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return p},t.unstable_getFirstCallbackNode=function(){return r(u)},t.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var n=p;p=t;try{return e()}finally{p=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=p;p=e;try{return t()}finally{p=n}},t.unstable_scheduleCallback=function(e,o,a){var i=t.unstable_now();switch("object"===typeof a&&null!==a?a="number"===typeof(a=a.delay)&&0<a?i+a:i:a=i,e){case 1:var l=-1;break;case 2:l=250;break;case 5:l=1073741823;break;case 4:l=1e4;break;default:l=5e3}return e={id:d++,callback:o,priorityLevel:e,startTime:a,expirationTime:l=a+l,sortIndex:-1},a>i?(e.sortIndex=a,n(c,e),null===r(u)&&e===r(c)&&(g?(y(C),C=-1):g=!0,_(x,a-i))):(e.sortIndex=l,n(u,e),m||h||(m=!0,M(S))),e},t.unstable_shouldYield=O,t.unstable_wrapCallback=function(e){var t=p;return function(){var n=p;p=t;try{return e.apply(this,arguments)}finally{p=n}}}},7266:(e,t,n)=>{"use strict";var r=n(4994);t.X4=p,t.e$=h,t.eM=function(e,t){const n=f(e),r=f(t);return(Math.max(n,r)+.05)/(Math.min(n,r)+.05)},t.a=m;var o=r(n(457)),a=r(n(9214));function i(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;return(0,a.default)(e,t,n)}function l(e){e=e.slice(1);const t=new RegExp(`.{1,${e.length>=6?2:1}}`,"g");let n=e.match(t);return n&&1===n[0].length&&(n=n.map(e=>e+e)),n?`rgb${4===n.length?"a":""}(${n.map((e,t)=>t<3?parseInt(e,16):Math.round(parseInt(e,16)/255*1e3)/1e3).join(", ")})`:""}function s(e){if(e.type)return e;if("#"===e.charAt(0))return s(l(e));const t=e.indexOf("("),n=e.substring(0,t);if(-1===["rgb","rgba","hsl","hsla","color"].indexOf(n))throw new Error((0,o.default)(9,e));let r,a=e.substring(t+1,e.length-1);if("color"===n){if(a=a.split(" "),r=a.shift(),4===a.length&&"/"===a[3].charAt(0)&&(a[3]=a[3].slice(1)),-1===["srgb","display-p3","a98-rgb","prophoto-rgb","rec-2020"].indexOf(r))throw new Error((0,o.default)(10,r))}else a=a.split(",");return a=a.map(e=>parseFloat(e)),{type:n,values:a,colorSpace:r}}const u=e=>{const t=s(e);return t.values.slice(0,3).map((e,n)=>-1!==t.type.indexOf("hsl")&&0!==n?`${e}%`:e).join(" ")};function c(e){const{type:t,colorSpace:n}=e;let{values:r}=e;return-1!==t.indexOf("rgb")?r=r.map((e,t)=>t<3?parseInt(e,10):e):-1!==t.indexOf("hsl")&&(r[1]=`${r[1]}%`,r[2]=`${r[2]}%`),r=-1!==t.indexOf("color")?`${n} ${r.join(" ")}`:`${r.join(", ")}`,`${t}(${r})`}function d(e){e=s(e);const{values:t}=e,n=t[0],r=t[1]/100,o=t[2]/100,a=r*Math.min(o,1-o),i=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:(e+n/30)%12;return o-a*Math.max(Math.min(t-3,9-t,1),-1)};let l="rgb";const u=[Math.round(255*i(0)),Math.round(255*i(8)),Math.round(255*i(4))];return"hsla"===e.type&&(l+="a",u.push(t[3])),c({type:l,values:u})}function f(e){let t="hsl"===(e=s(e)).type||"hsla"===e.type?s(d(e)).values:e.values;return t=t.map(t=>("color"!==e.type&&(t/=255),t<=.03928?t/12.92:((t+.055)/1.055)**2.4)),Number((.2126*t[0]+.7152*t[1]+.0722*t[2]).toFixed(3))}function p(e,t){return e=s(e),t=i(t),"rgb"!==e.type&&"hsl"!==e.type||(e.type+="a"),"color"===e.type?e.values[3]=`/${t}`:e.values[3]=t,c(e)}function h(e,t){if(e=s(e),t=i(t),-1!==e.type.indexOf("hsl"))e.values[2]*=1-t;else if(-1!==e.type.indexOf("rgb")||-1!==e.type.indexOf("color"))for(let n=0;n<3;n+=1)e.values[n]*=1-t;return c(e)}function m(e,t){if(e=s(e),t=i(t),-1!==e.type.indexOf("hsl"))e.values[2]+=(100-e.values[2])*t;else if(-1!==e.type.indexOf("rgb"))for(let n=0;n<3;n+=1)e.values[n]+=(255-e.values[n])*t;else if(-1!==e.type.indexOf("color"))for(let n=0;n<3;n+=1)e.values[n]+=(1-e.values[n])*t;return c(e)}function g(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:.15;return f(e)>.5?h(e,t):m(e,t)}},7490:(e,t,n)=>{"use strict";n.d(t,{c:()=>o});var r=n(5043);function o(e){const t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return t.current?.(...n)},[])}},7598:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r=n(7868);function o(e){if("string"!==typeof e)throw new Error((0,r.A)(7));return e.charAt(0).toUpperCase()+e.slice(1)}},7758:(e,t,n)=>{"use strict";n.d(t,{A:()=>_});var r=n(8604),o=n(7162),a=n(3815);const i=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];const r=t.reduce((e,t)=>(t.filterProps.forEach(n=>{e[n]=t}),e),{}),o=e=>Object.keys(e).reduce((t,n)=>r[n]?(0,a.A)(t,r[n](e)):t,{});return o.propTypes={},o.filterProps=t.reduce((e,t)=>e.concat(t.filterProps),[]),o};var l=n(9751);function s(e){return"number"!==typeof e?e:`${e}px solid`}function u(e,t){return(0,o.Ay)({prop:e,themeKey:"borders",transform:t})}const c=u("border",s),d=u("borderTop",s),f=u("borderRight",s),p=u("borderBottom",s),h=u("borderLeft",s),m=u("borderColor"),g=u("borderTopColor"),v=u("borderRightColor"),y=u("borderBottomColor"),b=u("borderLeftColor"),w=u("outline",s),x=u("outlineColor"),S=e=>{if(void 0!==e.borderRadius&&null!==e.borderRadius){const t=(0,r.MA)(e.theme,"shape.borderRadius",4,"borderRadius"),n=e=>({borderRadius:(0,r._W)(t,e)});return(0,l.NI)(e,e.borderRadius,n)}return null};S.propTypes={},S.filterProps=["borderRadius"];i(c,d,f,p,h,m,g,v,y,b,S,w,x);const k=e=>{if(void 0!==e.gap&&null!==e.gap){const t=(0,r.MA)(e.theme,"spacing",8,"gap"),n=e=>({gap:(0,r._W)(t,e)});return(0,l.NI)(e,e.gap,n)}return null};k.propTypes={},k.filterProps=["gap"];const E=e=>{if(void 0!==e.columnGap&&null!==e.columnGap){const t=(0,r.MA)(e.theme,"spacing",8,"columnGap"),n=e=>({columnGap:(0,r._W)(t,e)});return(0,l.NI)(e,e.columnGap,n)}return null};E.propTypes={},E.filterProps=["columnGap"];const A=e=>{if(void 0!==e.rowGap&&null!==e.rowGap){const t=(0,r.MA)(e.theme,"spacing",8,"rowGap"),n=e=>({rowGap:(0,r._W)(t,e)});return(0,l.NI)(e,e.rowGap,n)}return null};A.propTypes={},A.filterProps=["rowGap"];i(k,E,A,(0,o.Ay)({prop:"gridColumn"}),(0,o.Ay)({prop:"gridRow"}),(0,o.Ay)({prop:"gridAutoFlow"}),(0,o.Ay)({prop:"gridAutoColumns"}),(0,o.Ay)({prop:"gridAutoRows"}),(0,o.Ay)({prop:"gridTemplateColumns"}),(0,o.Ay)({prop:"gridTemplateRows"}),(0,o.Ay)({prop:"gridTemplateAreas"}),(0,o.Ay)({prop:"gridArea"}));function C(e,t){return"grey"===t?t:e}i((0,o.Ay)({prop:"color",themeKey:"palette",transform:C}),(0,o.Ay)({prop:"bgcolor",cssProperty:"backgroundColor",themeKey:"palette",transform:C}),(0,o.Ay)({prop:"backgroundColor",themeKey:"palette",transform:C}));function P(e){return e<=1&&0!==e?100*e+"%":e}const R=(0,o.Ay)({prop:"width",transform:P}),O=e=>{if(void 0!==e.maxWidth&&null!==e.maxWidth){const t=t=>{var n,r;const o=(null==(n=e.theme)||null==(n=n.breakpoints)||null==(n=n.values)?void 0:n[t])||l.zu[t];return o?"px"!==(null==(r=e.theme)||null==(r=r.breakpoints)?void 0:r.unit)?{maxWidth:`${o}${e.theme.breakpoints.unit}`}:{maxWidth:o}:{maxWidth:P(t)}};return(0,l.NI)(e,e.maxWidth,t)}return null};O.filterProps=["maxWidth"];const T=(0,o.Ay)({prop:"minWidth",transform:P}),N=(0,o.Ay)({prop:"height",transform:P}),j=(0,o.Ay)({prop:"maxHeight",transform:P}),M=(0,o.Ay)({prop:"minHeight",transform:P}),_=((0,o.Ay)({prop:"size",cssProperty:"width",transform:P}),(0,o.Ay)({prop:"size",cssProperty:"height",transform:P}),i(R,O,T,N,j,M,(0,o.Ay)({prop:"boxSizing"})),{border:{themeKey:"borders",transform:s},borderTop:{themeKey:"borders",transform:s},borderRight:{themeKey:"borders",transform:s},borderBottom:{themeKey:"borders",transform:s},borderLeft:{themeKey:"borders",transform:s},borderColor:{themeKey:"palette"},borderTopColor:{themeKey:"palette"},borderRightColor:{themeKey:"palette"},borderBottomColor:{themeKey:"palette"},borderLeftColor:{themeKey:"palette"},outline:{themeKey:"borders",transform:s},outlineColor:{themeKey:"palette"},borderRadius:{themeKey:"shape.borderRadius",style:S},color:{themeKey:"palette",transform:C},bgcolor:{themeKey:"palette",cssProperty:"backgroundColor",transform:C},backgroundColor:{themeKey:"palette",transform:C},p:{style:r.Ms},pt:{style:r.Ms},pr:{style:r.Ms},pb:{style:r.Ms},pl:{style:r.Ms},px:{style:r.Ms},py:{style:r.Ms},padding:{style:r.Ms},paddingTop:{style:r.Ms},paddingRight:{style:r.Ms},paddingBottom:{style:r.Ms},paddingLeft:{style:r.Ms},paddingX:{style:r.Ms},paddingY:{style:r.Ms},paddingInline:{style:r.Ms},paddingInlineStart:{style:r.Ms},paddingInlineEnd:{style:r.Ms},paddingBlock:{style:r.Ms},paddingBlockStart:{style:r.Ms},paddingBlockEnd:{style:r.Ms},m:{style:r.Lc},mt:{style:r.Lc},mr:{style:r.Lc},mb:{style:r.Lc},ml:{style:r.Lc},mx:{style:r.Lc},my:{style:r.Lc},margin:{style:r.Lc},marginTop:{style:r.Lc},marginRight:{style:r.Lc},marginBottom:{style:r.Lc},marginLeft:{style:r.Lc},marginX:{style:r.Lc},marginY:{style:r.Lc},marginInline:{style:r.Lc},marginInlineStart:{style:r.Lc},marginInlineEnd:{style:r.Lc},marginBlock:{style:r.Lc},marginBlockStart:{style:r.Lc},marginBlockEnd:{style:r.Lc},displayPrint:{cssProperty:!1,transform:e=>({"@media print":{display:e}})},display:{},overflow:{},textOverflow:{},visibility:{},whiteSpace:{},flexBasis:{},flexDirection:{},flexWrap:{},justifyContent:{},alignItems:{},alignContent:{},order:{},flex:{},flexGrow:{},flexShrink:{},alignSelf:{},justifyItems:{},justifySelf:{},gap:{style:k},rowGap:{style:A},columnGap:{style:E},gridColumn:{},gridRow:{},gridAutoFlow:{},gridAutoColumns:{},gridAutoRows:{},gridTemplateColumns:{},gridTemplateRows:{},gridTemplateAreas:{},gridArea:{},position:{},zIndex:{themeKey:"zIndex"},top:{},right:{},bottom:{},left:{},boxShadow:{themeKey:"shadows"},width:{transform:P},maxWidth:{style:O},minWidth:{transform:P},height:{transform:P},maxHeight:{transform:P},minHeight:{transform:P},boxSizing:{},fontFamily:{themeKey:"typography"},fontSize:{themeKey:"typography"},fontStyle:{themeKey:"typography"},fontWeight:{themeKey:"typography"},letterSpacing:{},textTransform:{},lineHeight:{},textAlign:{},typography:{cssProperty:!1,themeKey:"typography"}})},7826:(e,t,n)=>{"use strict";n.d(t,{C:()=>s,u:()=>l});var r=n(5043),o=n(6213),a=n(579);const i=(0,r.createContext)(void 0),l=()=>{const e=(0,r.useContext)(i);if(void 0===e)throw new Error("usePayment must be used within a PaymentProvider");return e},s=e=>{let{children:t}=e;const[n,l]=(0,r.useState)([]),[s,u]=(0,r.useState)(!1),c=async()=>{try{const e=await o.A.get("/api/transactions");l(e.data.transactions)}catch(e){console.error("Failed to fetch transactions:",e)}},d={transactions:n,processPayment:async e=>{u(!0);try{const t=await o.A.post("/api/payments/process",e);return await c(),t.data}finally{u(!1)}},getTransactions:c,refundTransaction:async e=>{u(!0);try{await o.A.post(`/api/payments/refund/${e}`),await c()}finally{u(!1)}},loading:s};return(0,a.jsx)(i.Provider,{value:d,children:t})}},7868:(e,t,n)=>{"use strict";function r(e){let t="https://mui.com/production-error/?code="+e;for(let n=1;n<arguments.length;n+=1)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified MUI error #"+e+"; visit "+t+" for the full message."}n.d(t,{A:()=>r})},7918:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>r.A});var r=n(7598)},7920:(e,t,n)=>{"use strict";n.d(t,{hO:()=>s,sG:()=>l});var r=n(5043),o=n(7950),a=n(6851),i=n(579),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{const n=(0,a.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{const{asChild:o,...a}=e,l=o?n:t;return"undefined"!==typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(l,{...a,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function s(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},7950:(e,t,n)=>{"use strict";!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(2730)},8052:(e,t,n)=>{"use strict";var r=n(4994);t.Ay=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{themeId:t,defaultTheme:n=g,rootShouldForwardProp:r=h,slotShouldForwardProp:s=h}=e,c=e=>(0,u.default)((0,o.default)({},e,{theme:y((0,o.default)({},e,{defaultTheme:n,themeId:t}))}));return c.__mui_systemSx=!0,function(e){let u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};(0,i.internal_processStyles)(e,e=>e.filter(e=>!(null!=e&&e.__mui_systemSx)));const{name:d,slot:p,skipVariantsResolver:m,skipSx:g,overridesResolver:x=b(v(p))}=u,S=(0,a.default)(u,f),k=d&&d.startsWith("Mui")||p?"components":"custom",E=void 0!==m?m:p&&"Root"!==p&&"root"!==p||!1,A=g||!1;let C=h;"Root"===p||"root"===p?C=r:p?C=s:function(e){return"string"===typeof e&&e.charCodeAt(0)>96}(e)&&(C=void 0);const P=(0,i.default)(e,(0,o.default)({shouldForwardProp:C,label:undefined},S)),R=e=>"function"===typeof e&&e.__emotion_real!==e||(0,l.isPlainObject)(e)?r=>{const a=y({theme:r.theme,defaultTheme:n,themeId:t});return w(e,(0,o.default)({},r,{theme:a}),a.modularCssLayers?k:void 0)}:e,O=function(r){let a=R(r);for(var i=arguments.length,l=new Array(i>1?i-1:0),s=1;s<i;s++)l[s-1]=arguments[s];const u=l?l.map(R):[];d&&x&&u.push(e=>{const r=y((0,o.default)({},e,{defaultTheme:n,themeId:t}));if(!r.components||!r.components[d]||!r.components[d].styleOverrides)return null;const a=r.components[d].styleOverrides,i={};return Object.entries(a).forEach(t=>{let[n,a]=t;i[n]=w(a,(0,o.default)({},e,{theme:r}),r.modularCssLayers?"theme":void 0)}),x(e,i)}),d&&!E&&u.push(e=>{var r;const a=y((0,o.default)({},e,{defaultTheme:n,themeId:t}));return w({variants:null==a||null==(r=a.components)||null==(r=r[d])?void 0:r.variants},(0,o.default)({},e,{theme:a}),a.modularCssLayers?"theme":void 0)}),A||u.push(c);const f=u.length-l.length;if(Array.isArray(r)&&f>0){const e=new Array(f).fill("");a=[...r,...e],a.raw=[...r.raw,...e]}const p=P(a,...u);return e.muiName&&(p.muiName=e.muiName),p};return P.withConfig&&(O.withConfig=P.withConfig),O}};var o=r(n(4634)),a=r(n(4893)),i=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var n=p(t);if(n&&n.has(e))return n.get(e);var r={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var i=o?Object.getOwnPropertyDescriptor(e,a):null;i&&(i.get||i.set)?Object.defineProperty(r,a,i):r[a]=e[a]}return r.default=e,n&&n.set(e,r),r}(n(3174)),l=n(9482),s=(r(n(7918)),r(n(3382)),r(n(4989))),u=r(n(3234));const c=["ownerState"],d=["variants"],f=["name","slot","skipVariantsResolver","skipSx","overridesResolver"];function p(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(p=function(e){return e?n:t})(e)}function h(e){return"ownerState"!==e&&"theme"!==e&&"sx"!==e&&"as"!==e}function m(e,t){return t&&e&&"object"===typeof e&&e.styles&&!e.styles.startsWith("@layer")&&(e.styles=`@layer ${t}{${String(e.styles)}}`),e}const g=(0,s.default)(),v=e=>e?e.charAt(0).toLowerCase()+e.slice(1):e;function y(e){let{defaultTheme:t,theme:n,themeId:r}=e;return o=n,0===Object.keys(o).length?t:n[r]||n;var o}function b(e){return e?(t,n)=>n[e]:null}function w(e,t,n){let{ownerState:r}=t,l=(0,a.default)(t,c);const s="function"===typeof e?e((0,o.default)({ownerState:r},l)):e;if(Array.isArray(s))return s.flatMap(e=>w(e,(0,o.default)({ownerState:r},l),n));if(s&&"object"===typeof s&&Array.isArray(s.variants)){const{variants:e=[]}=s;let t=(0,a.default)(s,d);return e.forEach(e=>{let a=!0;if("function"===typeof e.props?a=e.props((0,o.default)({ownerState:r},l,r)):Object.keys(e.props).forEach(t=>{(null==r?void 0:r[t])!==e.props[t]&&l[t]!==e.props[t]&&(a=!1)}),a){Array.isArray(t)||(t=[t]);const a="function"===typeof e.style?e.style((0,o.default)({ownerState:r},l,r)):e.style;t.push(n?m((0,i.internal_serializeStyles)(a),n):a)}}),t}return n?m((0,i.internal_serializeStyles)(s),n):s}},8168:(e,t,n)=>{"use strict";function r(){return r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},r.apply(null,arguments)}n.d(t,{A:()=>r})},8280:(e,t,n)=>{"use strict";n.d(t,{A:()=>p});var r=n(8168),o=n(8587),a=n(9172),i=n(4853);const l={borderRadius:4};var s=n(8604);var u=n(8812),c=n(7758),d=n(9703);const f=["breakpoints","palette","spacing","shape"];const p=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{breakpoints:t={},palette:n={},spacing:p,shape:h={}}=e,m=(0,o.A)(e,f),g=(0,i.A)(t),v=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:8;if(e.mui)return e;const t=(0,s.LX)({spacing:e}),n=function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return(0===n.length?[1]:n).map(e=>{const n=t(e);return"number"===typeof n?`${n}px`:n}).join(" ")};return n.mui=!0,n}(p);let y=(0,a.A)({breakpoints:g,direction:"ltr",components:{},palette:(0,r.A)({mode:"light"},n),spacing:v,shape:(0,r.A)({},l,h)},m);y.applyStyles=d.A;for(var b=arguments.length,w=new Array(b>1?b-1:0),x=1;x<b;x++)w[x-1]=arguments[x];return y=w.reduce((e,t)=>(0,a.A)(e,t),y),y.unstable_sxConfig=(0,r.A)({},c.A,null==m?void 0:m.unstable_sxConfig),y.unstable_sx=function(e){return(0,u.A)({sx:e,theme:this})},y}},8387:(e,t,n)=>{"use strict";function r(e){var t,n,o="";if("string"==typeof e||"number"==typeof e)o+=e;else if("object"==typeof e)if(Array.isArray(e)){var a=e.length;for(t=0;t<a;t++)e[t]&&(n=r(e[t]))&&(o&&(o+=" "),o+=n)}else for(n in e)e[n]&&(o&&(o+=" "),o+=n);return o}function o(){for(var e,t,n=0,o="",a=arguments.length;n<a;n++)(e=arguments[n])&&(t=r(e))&&(o&&(o+=" "),o+=t);return o}n.d(t,{$:()=>o,A:()=>a});const a=o},8587:(e,t,n)=>{"use strict";function r(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}n.d(t,{A:()=>r})},8604:(e,t,n)=>{"use strict";n.d(t,{LX:()=>h,MA:()=>p,_W:()=>m,Lc:()=>y,Ms:()=>b});var r=n(9751),o=n(7162),a=n(3815);const i={m:"margin",p:"padding"},l={t:"Top",r:"Right",b:"Bottom",l:"Left",x:["Left","Right"],y:["Top","Bottom"]},s={marginX:"mx",marginY:"my",paddingX:"px",paddingY:"py"},u=function(e){const t={};return n=>(void 0===t[n]&&(t[n]=e(n)),t[n])}(e=>{if(e.length>2){if(!s[e])return[e];e=s[e]}const[t,n]=e.split(""),r=i[t],o=l[n]||"";return Array.isArray(o)?o.map(e=>r+e):[r+o]}),c=["m","mt","mr","mb","ml","mx","my","margin","marginTop","marginRight","marginBottom","marginLeft","marginX","marginY","marginInline","marginInlineStart","marginInlineEnd","marginBlock","marginBlockStart","marginBlockEnd"],d=["p","pt","pr","pb","pl","px","py","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","paddingX","paddingY","paddingInline","paddingInlineStart","paddingInlineEnd","paddingBlock","paddingBlockStart","paddingBlockEnd"],f=[...c,...d];function p(e,t,n,r){var a;const i=null!=(a=(0,o.Yn)(e,t,!1))?a:n;return"number"===typeof i?e=>"string"===typeof e?e:i*e:Array.isArray(i)?e=>"string"===typeof e?e:i[e]:"function"===typeof i?i:()=>{}}function h(e){return p(e,"spacing",8)}function m(e,t){if("string"===typeof t||null==t)return t;const n=e(Math.abs(t));return t>=0?n:"number"===typeof n?-n:`-${n}`}function g(e,t,n,o){if(-1===t.indexOf(n))return null;const a=function(e,t){return n=>e.reduce((e,r)=>(e[r]=m(t,n),e),{})}(u(n),o),i=e[n];return(0,r.NI)(e,i,a)}function v(e,t){const n=h(e.theme);return Object.keys(e).map(r=>g(e,t,r,n)).reduce(a.A,{})}function y(e){return v(e,c)}function b(e){return v(e,d)}function w(e){return v(e,f)}y.propTypes={},y.filterProps=c,b.propTypes={},b.filterProps=d,w.propTypes={},w.filterProps=f},8698:(e,t,n)=>{"use strict";n.d(t,{A:()=>u});var r=n(8168),o=n(8587),a=n(9172),i=n(7758);const l=["sx"],s=e=>{var t,n;const r={systemProps:{},otherProps:{}},o=null!=(t=null==e||null==(n=e.theme)?void 0:n.unstable_sxConfig)?t:i.A;return Object.keys(e).forEach(t=>{o[t]?r.systemProps[t]=e[t]:r.otherProps[t]=e[t]}),r};function u(e){const{sx:t}=e,n=(0,o.A)(e,l),{systemProps:i,otherProps:u}=s(n);let c;return c=Array.isArray(t)?[i,...t]:"function"===typeof t?function(){const e=t(...arguments);return(0,a.Q)(e)?(0,r.A)({},i,e):i}:(0,r.A)({},i,t),(0,r.A)({},u,{sx:c})}},8812:(e,t,n)=>{"use strict";n.d(t,{A:()=>c,k:()=>s});var r=n(7598),o=n(3815),a=n(7162),i=n(9751),l=n(7758);function s(){function e(e,t,n,o){const l={[e]:t,theme:n},s=o[e];if(!s)return{[e]:t};const{cssProperty:u=e,themeKey:c,transform:d,style:f}=s;if(null==t)return null;if("typography"===c&&"inherit"===t)return{[e]:t};const p=(0,a.Yn)(n,c)||{};if(f)return f(l);return(0,i.NI)(l,t,t=>{let n=(0,a.BO)(p,d,t);return t===n&&"string"===typeof t&&(n=(0,a.BO)(p,d,`${e}${"default"===t?"":(0,r.A)(t)}`,t)),!1===u?n:{[u]:n}})}return function t(n){var r;const{sx:a,theme:s={},nested:u}=n||{};if(!a)return null;const c=null!=(r=s.unstable_sxConfig)?r:l.A;function d(n){let r=n;if("function"===typeof n)r=n(s);else if("object"!==typeof n)return n;if(!r)return null;const a=(0,i.EU)(s.breakpoints),l=Object.keys(a);let d=a;return Object.keys(r).forEach(n=>{const a=(l=r[n],u=s,"function"===typeof l?l(u):l);var l,u;if(null!==a&&void 0!==a)if("object"===typeof a)if(c[n])d=(0,o.A)(d,e(n,a,s,c));else{const e=(0,i.NI)({theme:s},a,e=>({[n]:e}));!function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];const r=t.reduce((e,t)=>e.concat(Object.keys(t)),[]),o=new Set(r);return t.every(e=>o.size===Object.keys(e).length)}(e,a)?d=(0,o.A)(d,e):d[n]=t({sx:a,theme:s,nested:!0})}else d=(0,o.A)(d,e(n,a,s,c))}),!u&&s.modularCssLayers?{"@layer sx":(0,i.vf)(l,d)}:(0,i.vf)(l,d)}return Array.isArray(a)?a.map(d):d(a)}}const u=s();u.filterProps=["sx"];const c=u},8853:(e,t,n)=>{"use strict";e.exports=n(7234)},9066:(e,t,n)=>{"use strict";n.d(t,{A:()=>u,O:()=>c});var r=n(5043),o=n(6213),a=n(1704),i=n(4558),l=n(579);const s=(0,r.createContext)(void 0),u=()=>{const e=(0,r.useContext)(s);if(void 0===e)throw new Error("useAuth must be used within an AuthProvider");return e},c=e=>{let{children:t}=e;const[n,u]=(0,r.useState)(null),[c,d]=(0,r.useState)(!0),[f,p]=(0,r.useState)(null),{toast:h}=(0,a.dj)();(0,r.useEffect)(()=>{(async()=>{try{const e=await o.A.get(`${i.JR}/auth/me`,{withCredentials:!0,headers:{Accept:"application/json","Content-Type":"application/json"}});e.data&&e.data.user&&u(e.data.user)}catch(f){console.error("Session check failed:",f),delete o.A.defaults.headers.common.Authorization,u(null)}finally{d(!1)}})()},[]);(new Date).toISOString(),(new Date).toISOString(),(new Date).toISOString();const m={user:n,login:async(e,t)=>{d(!0),p(null);try{var n;const r=`Basic ${btoa(`${e}:${t}`)}`,a=await o.A.post("/api/auth/login",{email:e,password:t},{headers:{Authorization:r},withCredentials:!0});if(a.data.requires2FA)return h({title:"2FA Required",description:"Please complete two-factor authentication."}),{requires2FA:!0,userId:a.data.userId};o.A.defaults.headers.common.Authorization=r;const i=await o.A.get("/api/auth/me");if(null!==(n=i.data)&&void 0!==n&&n.user){const e={...i.data.user,lastLogin:(new Date).toISOString()};return u(e),h({title:"Login successful",description:`Welcome back, ${e.name||"User"}!`}),{success:!0}}throw new Error("Invalid email or password")}catch(f){const t=f instanceof Error?f.message:"Login failed. Please try again.";throw p(t),null===h||void 0===h||h({title:"Login Failed",description:t,variant:"destructive"}),f}finally{d(!1)}},register:async e=>{d(!0),p(null);try{if(!e.email||!e.password||!e.name)throw new Error("All fields are required");if(e.password.length<6)throw new Error("Password must be at least 6 characters long");await new Promise(e=>setTimeout(e,500));const t={id:`user_${Date.now()}`,email:e.email,name:e.name,role:"trader",isVerified:!1,twoFactorEnabled:!1,traderId:`trader_${Date.now()}`,level:1,totalTrades:0,permissions:[],merchantId:"",lastLogin:(new Date).toISOString()},n=`mock-jwt-token-${t.id}`;sessionStorage.setItem("token",n),sessionStorage.setItem("user",JSON.stringify(t)),o.A.defaults.headers.common.Authorization=`Bearer ${n}`,u(t),p(null),null===h||void 0===h||h({title:"Registration Successful",description:"Your account has been created!"})}catch(f){const t=f instanceof Error?f.message:"Registration failed. Please try again.";throw p(t),null===h||void 0===h||h({title:"Registration Failed",description:t,variant:"destructive"}),f}finally{d(!1)}},logout:()=>{localStorage.removeItem("token"),localStorage.removeItem("user"),delete o.A.defaults.headers.common.Authorization,u(null),h&&h({title:"Logged out",description:"You have been successfully logged out."})},loading:c,error:f,clearError:()=>p(null)};return(0,l.jsx)(s.Provider,{value:m,children:t})}},9172:(e,t,n)=>{"use strict";n.d(t,{A:()=>l,Q:()=>a});var r=n(8168),o=n(5043);function a(e){if("object"!==typeof e||null===e)return!1;const t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}function i(e){if(o.isValidElement(e)||!a(e))return e;const t={};return Object.keys(e).forEach(n=>{t[n]=i(e[n])}),t}function l(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{clone:!0};const s=n.clone?(0,r.A)({},e):e;return a(e)&&a(t)&&Object.keys(t).forEach(r=>{o.isValidElement(t[r])?s[r]=t[r]:a(t[r])&&Object.prototype.hasOwnProperty.call(e,r)&&a(e[r])?s[r]=l(e[r],t[r],n):n.clone?s[r]=a(t[r])?i(t[r]):t[r]:s[r]=t[r]}),s}},9214:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>r});const r=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.MIN_SAFE_INTEGER,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Number.MAX_SAFE_INTEGER;return Math.max(t,Math.min(e,n))}},9369:(e,t,n)=>{"use strict";n.d(t,{C:()=>u,E:()=>g,T:()=>d,c:()=>h,h:()=>f,w:()=>c});var r=n(5043),o=n(3803),a=n(1722),i=n(6598),l=n(9436),s=r.createContext("undefined"!==typeof HTMLElement?(0,o.A)({key:"css"}):null),u=s.Provider,c=function(e){return(0,r.forwardRef)(function(t,n){var o=(0,r.useContext)(s);return e(t,o,n)})},d=r.createContext({});var f={}.hasOwnProperty,p="__EMOTION_TYPE_PLEASE_DO_NOT_USE__",h=function(e,t){var n={};for(var r in t)f.call(t,r)&&(n[r]=t[r]);return n[p]=e,n},m=function(e){var t=e.cache,n=e.serialized,r=e.isStringTag;return(0,a.SF)(t,n,r),(0,l.s)(function(){return(0,a.sk)(t,n,r)}),null},g=c(function(e,t,n){var o=e.css;"string"===typeof o&&void 0!==t.registered[o]&&(o=t.registered[o]);var l=e[p],s=[o],u="";"string"===typeof e.className?u=(0,a.Rk)(t.registered,s,e.className):null!=e.className&&(u=e.className+" ");var c=(0,i.J)(s,void 0,r.useContext(d));u+=t.key+"-"+c.name;var h={};for(var g in e)f.call(e,g)&&"css"!==g&&g!==p&&(h[g]=e[g]);return h.className=u,n&&(h.ref=n),r.createElement(r.Fragment,null,r.createElement(m,{cache:t,serialized:c,isStringTag:"string"===typeof l}),r.createElement(l,h))})},9436:(e,t,n)=>{"use strict";var r;n.d(t,{i:()=>l,s:()=>i});var o=n(5043),a=!!(r||(r=n.t(o,2))).useInsertionEffect&&(r||(r=n.t(o,2))).useInsertionEffect,i=a||function(e){return e()},l=a||o.useLayoutEffect},9482:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>r.A,isPlainObject:()=>r.Q});var r=n(9172)},9703:(e,t,n)=>{"use strict";function r(e,t){const n=this;if(n.vars&&"function"===typeof n.getColorSchemeSelector){const r=n.getColorSchemeSelector(e).replace(/(\[[^\]]+\])/,"*:where($1)");return{[r]:t}}return n.palette.mode===e?t:{}}n.d(t,{A:()=>r})},9751:(e,t,n)=>{"use strict";n.d(t,{EU:()=>i,NI:()=>a,vf:()=>l,zu:()=>r});const r={xs:0,sm:600,md:900,lg:1200,xl:1536},o={keys:["xs","sm","md","lg","xl"],up:e=>`@media (min-width:${r[e]}px)`};function a(e,t,n){const a=e.theme||{};if(Array.isArray(t)){const e=a.breakpoints||o;return t.reduce((r,o,a)=>(r[e.up(e.keys[a])]=n(t[a]),r),{})}if("object"===typeof t){const e=a.breakpoints||o;return Object.keys(t).reduce((o,a)=>{if(-1!==Object.keys(e.values||r).indexOf(a)){o[e.up(a)]=n(t[a],a)}else{const e=a;o[e]=t[e]}return o},{})}return n(t)}function i(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};var t;return(null==(t=e.keys)?void 0:t.reduce((t,n)=>(t[e.up(n)]={},t),{}))||{}}function l(e,t){return e.reduce((e,t)=>{const n=e[t];return(!n||0===Object.keys(n).length)&&delete e[t],e},t)}}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var a=t[r]={id:r,loaded:!1,exports:{}};return e[r].call(a.exports,a,a.exports,n),a.loaded=!0,a.exports}n.m=e,n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},(()=>{var e,t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__;n.t=function(r,o){if(1&o&&(r=this(r)),8&o)return r;if("object"===typeof r&&r){if(4&o&&r.__esModule)return r;if(16&o&&"function"===typeof r.then)return r}var a=Object.create(null);n.r(a);var i={};e=e||[null,t({}),t([]),t(t)];for(var l=2&o&&r;("object"==typeof l||"function"==typeof l)&&!~e.indexOf(l);l=t(l))Object.getOwnPropertyNames(l).forEach(e=>i[e]=()=>r[e]);return i.default=()=>r,n.d(a,i),a}})(),n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.f={},n.e=e=>Promise.all(Object.keys(n.f).reduce((t,r)=>(n.f[r](e,t),t),[])),n.u=e=>"static/js/"+e+"."+{15:"34860cc8",25:"b14e392c",60:"6272e01f",72:"4d3e6b52",73:"20135988",102:"048c77ed",141:"549c3364",147:"2553b00e",168:"d27113db",197:"716a2cd4",236:"16d2aa83",263:"ef814039",280:"5fd627be",284:"95386dc8",293:"39f79040",337:"bf71db34",396:"d0bd5c17",400:"a4415629",466:"3e7a20d7",508:"119a30c0",517:"fd3f6aaf",523:"932b8bdb",585:"35b9ad52",604:"14d0e974",620:"cbf32622",691:"759c038e",693:"fe43241e",720:"f21c368c",790:"897734ef",825:"7e4f6110",829:"792b3f7d",836:"a71a4994",839:"a4ee6f1c",842:"a89be24e",975:"47a9de52"}[e]+".chunk.js",n.miniCssF=e=>{},n.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={},t="payment-gateway-frontend:";n.l=(r,o,a,i)=>{if(e[r])e[r].push(o);else{var l,s;if(void 0!==a)for(var u=document.getElementsByTagName("script"),c=0;c<u.length;c++){var d=u[c];if(d.getAttribute("src")==r||d.getAttribute("data-webpack")==t+a){l=d;break}}l||(s=!0,(l=document.createElement("script")).charset="utf-8",l.timeout=120,n.nc&&l.setAttribute("nonce",n.nc),l.setAttribute("data-webpack",t+a),l.src=r),e[r]=[o];var f=(t,n)=>{l.onerror=l.onload=null,clearTimeout(p);var o=e[r];if(delete e[r],l.parentNode&&l.parentNode.removeChild(l),o&&o.forEach(e=>e(n)),t)return t(n)},p=setTimeout(f.bind(null,void 0,{type:"timeout",target:l}),12e4);l.onerror=f.bind(null,l.onerror),l.onload=f.bind(null,l.onload),s&&document.head.appendChild(l)}}})(),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),n.p="/",(()=>{var e={792:0};n.f.j=(t,r)=>{var o=n.o(e,t)?e[t]:void 0;if(0!==o)if(o)r.push(o[2]);else{var a=new Promise((n,r)=>o=e[t]=[n,r]);r.push(o[2]=a);var i=n.p+n.u(t),l=new Error;n.l(i,r=>{if(n.o(e,t)&&(0!==(o=e[t])&&(e[t]=void 0),o)){var a=r&&("load"===r.type?"missing":r.type),i=r&&r.target&&r.target.src;l.message="Loading chunk "+t+" failed.\n("+a+": "+i+")",l.name="ChunkLoadError",l.type=a,l.request=i,o[1](l)}},"chunk-"+t,t)}};var t=(t,r)=>{var o,a,i=r[0],l=r[1],s=r[2],u=0;if(i.some(t=>0!==e[t])){for(o in l)n.o(l,o)&&(n.m[o]=l[o]);if(s)s(n)}for(t&&t(r);u<i.length;u++)a=i[u],n.o(e,a)&&e[a]&&e[a][0](),e[a]=0},r=self.webpackChunkpayment_gateway_frontend=self.webpackChunkpayment_gateway_frontend||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})(),n.nc=void 0,(()=>{"use strict";var e=n(5043),t=n.t(e,2),r=n(4391),o=n(3216),a=n(5475),i=n(9066),l=n(7826);const s=(0,e.createContext)(void 0),u={setTheme:e=>{},themes:[]};var c=n(7950),d=n(858),f=n(2814),p=n(5463),h=n(1862),m=n(1184),g=n(3321),v=n(2894),y=n(7920),b=n(7490),w=n(3642),x=n(503),S=n(579),k=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),E=e.forwardRef((e,t)=>(0,S.jsx)(y.sG.span,{...e,ref:t,style:{...k,...e.style}}));E.displayName="VisuallyHidden";var A="ToastProvider",[C,P,R]=(0,p.N)("Toast"),[O,T]=(0,h.A)("Toast",[R]),[N,j]=O(A),M=t=>{const{__scopeToast:n,label:r="Notification",duration:o=5e3,swipeDirection:a="right",swipeThreshold:i=50,children:l}=t,[s,u]=e.useState(null),[c,d]=e.useState(0),f=e.useRef(!1),p=e.useRef(!1);return r.trim()||console.error(`Invalid prop \`label\` supplied to \`${A}\`. Expected non-empty \`string\`.`),(0,S.jsx)(C.Provider,{scope:n,children:(0,S.jsx)(N,{scope:n,label:r,duration:o,swipeDirection:a,swipeThreshold:i,toastCount:c,viewport:s,onViewportChange:u,onToastAdd:e.useCallback(()=>d(e=>e+1),[]),onToastRemove:e.useCallback(()=>d(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:f,isClosePausedRef:p,children:l})})};M.displayName=A;var _="ToastViewport",L=["F8"],z="toast.viewportPause",I="toast.viewportResume",D=e.forwardRef((t,n)=>{const{__scopeToast:r,hotkey:o=L,label:a="Notifications ({hotkey})",...i}=t,l=j(_,r),s=P(r),u=e.useRef(null),c=e.useRef(null),d=e.useRef(null),p=e.useRef(null),h=(0,f.s)(n,p,l.onViewportChange),g=o.join("+").replace(/Key/g,"").replace(/Digit/g,""),v=l.toastCount>0;e.useEffect(()=>{const e=e=>{0!==o.length&&o.every(t=>e[t]||e.code===t)&&p.current?.focus()};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[o]),e.useEffect(()=>{const e=u.current,t=p.current;if(v&&e&&t){const n=()=>{if(!l.isClosePausedRef.current){const e=new CustomEvent(z);t.dispatchEvent(e),l.isClosePausedRef.current=!0}},r=()=>{if(l.isClosePausedRef.current){const e=new CustomEvent(I);t.dispatchEvent(e),l.isClosePausedRef.current=!1}},o=t=>{!e.contains(t.relatedTarget)&&r()},a=()=>{e.contains(document.activeElement)||r()};return e.addEventListener("focusin",n),e.addEventListener("focusout",o),e.addEventListener("pointermove",n),e.addEventListener("pointerleave",a),window.addEventListener("blur",n),window.addEventListener("focus",r),()=>{e.removeEventListener("focusin",n),e.removeEventListener("focusout",o),e.removeEventListener("pointermove",n),e.removeEventListener("pointerleave",a),window.removeEventListener("blur",n),window.removeEventListener("focus",r)}}},[v,l.isClosePausedRef]);const b=e.useCallback(e=>{let{tabbingDirection:t}=e;const n=s().map(e=>{const n=e.ref.current,r=[n,...re(n)];return"forwards"===t?r:r.reverse()});return("forwards"===t?n.reverse():n).flat()},[s]);return e.useEffect(()=>{const e=p.current;if(e){const t=t=>{const n=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!n){const n=document.activeElement,r=t.shiftKey;if(t.target===e&&r)return void c.current?.focus();const o=b({tabbingDirection:r?"backwards":"forwards"}),a=o.findIndex(e=>e===n);oe(o.slice(a+1))?t.preventDefault():r?c.current?.focus():d.current?.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}},[s,b]),(0,S.jsxs)(m.lg,{ref:u,role:"region","aria-label":a.replace("{hotkey}",g),tabIndex:-1,style:{pointerEvents:v?void 0:"none"},children:[v&&(0,S.jsx)(F,{ref:c,onFocusFromOutsideViewport:()=>{oe(b({tabbingDirection:"forwards"}))}}),(0,S.jsx)(C.Slot,{scope:r,children:(0,S.jsx)(y.sG.ol,{tabIndex:-1,...i,ref:h})}),v&&(0,S.jsx)(F,{ref:d,onFocusFromOutsideViewport:()=>{oe(b({tabbingDirection:"backwards"}))}})]})});D.displayName=_;var $="ToastFocusProxy",F=e.forwardRef((e,t)=>{const{__scopeToast:n,onFocusFromOutsideViewport:r,...o}=e,a=j($,n);return(0,S.jsx)(E,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:e=>{const t=e.relatedTarget;!a.viewport?.contains(t)&&r()}})});F.displayName=$;var B="Toast",U=e.forwardRef((e,t)=>{const{forceMount:n,open:r,defaultOpen:o,onOpenChange:a,...i}=e,[l,s]=(0,w.i)({prop:r,defaultProp:o??!0,onChange:a,caller:B});return(0,S.jsx)(v.C,{present:n||l,children:(0,S.jsx)(H,{open:l,...i,ref:t,onClose:()=>s(!1),onPause:(0,b.c)(e.onPause),onResume:(0,b.c)(e.onResume),onSwipeStart:(0,d.m)(e.onSwipeStart,e=>{e.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:(0,d.m)(e.onSwipeMove,e=>{const{x:t,y:n}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${t}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${n}px`)}),onSwipeCancel:(0,d.m)(e.onSwipeCancel,e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:(0,d.m)(e.onSwipeEnd,e=>{const{x:t,y:n}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${t}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${n}px`),s(!1)})})})});U.displayName=B;var[W,V]=O(B,{onClose(){}}),H=e.forwardRef((t,n)=>{const{__scopeToast:r,type:o="foreground",duration:a,open:i,onClose:l,onEscapeKeyDown:s,onPause:u,onResume:p,onSwipeStart:h,onSwipeMove:g,onSwipeCancel:v,onSwipeEnd:w,...x}=t,k=j(B,r),[E,A]=e.useState(null),P=(0,f.s)(n,e=>A(e)),R=e.useRef(null),O=e.useRef(null),T=a||k.duration,N=e.useRef(0),M=e.useRef(T),_=e.useRef(0),{onToastAdd:L,onToastRemove:D}=k,$=(0,b.c)(()=>{const e=E?.contains(document.activeElement);e&&k.viewport?.focus(),l()}),F=e.useCallback(e=>{e&&e!==1/0&&(window.clearTimeout(_.current),N.current=(new Date).getTime(),_.current=window.setTimeout($,e))},[$]);e.useEffect(()=>{const e=k.viewport;if(e){const t=()=>{F(M.current),p?.()},n=()=>{const e=(new Date).getTime()-N.current;M.current=M.current-e,window.clearTimeout(_.current),u?.()};return e.addEventListener(z,n),e.addEventListener(I,t),()=>{e.removeEventListener(z,n),e.removeEventListener(I,t)}}},[k.viewport,T,u,p,F]),e.useEffect(()=>{i&&!k.isClosePausedRef.current&&F(T)},[i,T,k.isClosePausedRef,F]),e.useEffect(()=>(L(),()=>D()),[L,D]);const U=e.useMemo(()=>E?ee(E):null,[E]);return k.viewport?(0,S.jsxs)(S.Fragment,{children:[U&&(0,S.jsx)(q,{__scopeToast:r,role:"status","aria-live":"foreground"===o?"assertive":"polite","aria-atomic":!0,children:U}),(0,S.jsx)(W,{scope:r,onClose:$,children:c.createPortal((0,S.jsx)(C.ItemSlot,{scope:r,children:(0,S.jsx)(m.bL,{asChild:!0,onEscapeKeyDown:(0,d.m)(s,()=>{k.isFocusedToastEscapeKeyDownRef.current||$(),k.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,S.jsx)(y.sG.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":i?"open":"closed","data-swipe-direction":k.swipeDirection,...x,ref:P,style:{userSelect:"none",touchAction:"none",...t.style},onKeyDown:(0,d.m)(t.onKeyDown,e=>{"Escape"===e.key&&(s?.(e.nativeEvent),e.nativeEvent.defaultPrevented||(k.isFocusedToastEscapeKeyDownRef.current=!0,$()))}),onPointerDown:(0,d.m)(t.onPointerDown,e=>{0===e.button&&(R.current={x:e.clientX,y:e.clientY})}),onPointerMove:(0,d.m)(t.onPointerMove,e=>{if(!R.current)return;const t=e.clientX-R.current.x,n=e.clientY-R.current.y,r=Boolean(O.current),o=["left","right"].includes(k.swipeDirection),a=["left","up"].includes(k.swipeDirection)?Math.min:Math.max,i=o?a(0,t):0,l=o?0:a(0,n),s="touch"===e.pointerType?10:2,u={x:i,y:l},c={originalEvent:e,delta:u};r?(O.current=u,te("toast.swipeMove",g,c,{discrete:!1})):ne(u,k.swipeDirection,s)?(O.current=u,te("toast.swipeStart",h,c,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>s||Math.abs(n)>s)&&(R.current=null)}),onPointerUp:(0,d.m)(t.onPointerUp,e=>{const t=O.current,n=e.target;if(n.hasPointerCapture(e.pointerId)&&n.releasePointerCapture(e.pointerId),O.current=null,R.current=null,t){const n=e.currentTarget,r={originalEvent:e,delta:t};ne(t,k.swipeDirection,k.swipeThreshold)?te("toast.swipeEnd",w,r,{discrete:!0}):te("toast.swipeCancel",v,r,{discrete:!0}),n.addEventListener("click",e=>e.preventDefault(),{once:!0})}})})})}),k.viewport)})]}):null}),q=t=>{const{__scopeToast:n,children:r,...o}=t,a=j(B,n),[i,l]=e.useState(!1),[s,u]=e.useState(!1);return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:()=>{};const t=(0,b.c)(e);(0,x.N)(()=>{let e=0,n=0;return e=window.requestAnimationFrame(()=>n=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(n)}},[t])}(()=>l(!0)),e.useEffect(()=>{const e=window.setTimeout(()=>u(!0),1e3);return()=>window.clearTimeout(e)},[]),s?null:(0,S.jsx)(g.Z,{asChild:!0,children:(0,S.jsx)(E,{...o,children:i&&(0,S.jsxs)(S.Fragment,{children:[a.label," ",r]})})})},K=e.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return(0,S.jsx)(y.sG.div,{...r,ref:t})});K.displayName="ToastTitle";var G=e.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return(0,S.jsx)(y.sG.div,{...r,ref:t})});G.displayName="ToastDescription";var X="ToastAction",Q=e.forwardRef((e,t)=>{const{altText:n,...r}=e;return n.trim()?(0,S.jsx)(Z,{altText:n,asChild:!0,children:(0,S.jsx)(J,{...r,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${X}\`. Expected non-empty \`string\`.`),null)});Q.displayName=X;var Y="ToastClose",J=e.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e,o=V(Y,n);return(0,S.jsx)(Z,{asChild:!0,children:(0,S.jsx)(y.sG.button,{type:"button",...r,ref:t,onClick:(0,d.m)(e.onClick,o.onClose)})})});J.displayName=Y;var Z=e.forwardRef((e,t)=>{const{__scopeToast:n,altText:r,...o}=e;return(0,S.jsx)(y.sG.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...o,ref:t})});function ee(e){const t=[];return Array.from(e.childNodes).forEach(e=>{if(e.nodeType===e.TEXT_NODE&&e.textContent&&t.push(e.textContent),function(e){return e.nodeType===e.ELEMENT_NODE}(e)){const n=e.ariaHidden||e.hidden||"none"===e.style.display,r=""===e.dataset.radixToastAnnounceExclude;if(!n)if(r){const n=e.dataset.radixToastAnnounceAlt;n&&t.push(n)}else t.push(...ee(e))}}),t}function te(e,t,n,r){let{discrete:o}=r;const a=n.originalEvent.currentTarget,i=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&a.addEventListener(e,t,{once:!0}),o?(0,y.hO)(a,i):a.dispatchEvent(i)}var ne=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;const r=Math.abs(e.x),o=Math.abs(e.y),a=r>o;return"left"===t||"right"===t?a&&r>n:!a&&o>n};function re(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{const t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function oe(e){const t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}var ae=M,ie=D,le=U,se=K,ue=G,ce=Q,de=J,fe=n(917),pe=n(1172),he=n(3009);const me=ae,ge=e.forwardRef((e,t)=>{let{className:n,...r}=e;return(0,S.jsx)(ie,{ref:t,className:(0,he.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",n),...r})});ge.displayName="ToastViewport";const ve=(0,fe.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground",success:"group border-green-500 bg-green-50 text-green-700 dark:border-green-800 dark:bg-green-900/30 dark:text-green-400",warning:"group border-yellow-500 bg-yellow-50 text-yellow-700 dark:border-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400",info:"group border-blue-500 bg-blue-50 text-blue-700 dark:border-blue-800 dark:bg-blue-900/30 dark:text-blue-400"}},defaultVariants:{variant:"default"}}),ye=e.forwardRef((e,t)=>{let{className:n,variant:r,...o}=e;return(0,S.jsx)(le,{ref:t,className:(0,he.cn)(ve({variant:r}),n),...o})});ye.displayName="Toast";e.forwardRef((e,t)=>{let{className:n,altText:r,...o}=e;return(0,S.jsx)(ce,{ref:t,className:(0,he.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",n),altText:r,...o})}).displayName="ToastAction";e.forwardRef((e,t)=>{let{className:n,...r}=e;return(0,S.jsx)(de,{ref:t,className:(0,he.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",n),"toast-close":"",...r,children:(0,S.jsx)(pe.A,{className:"h-4 w-4"})})}).displayName="ToastClose";e.forwardRef((e,t)=>{let{className:n,...r}=e;return(0,S.jsx)(se,{ref:t,className:(0,he.cn)("text-sm font-semibold",n),...r})}).displayName="ToastTitle";e.forwardRef((e,t)=>{let{className:n,...r}=e;return(0,S.jsx)(ue,{ref:t,className:(0,he.cn)("text-sm opacity-90",n),...r})}).displayName="ToastDescription";var be=n(1704);function we(){const{toasts:t}=(0,be.dj)(),{theme:n}=(()=>{var t;return null!==(t=(0,e.useContext)(s))&&void 0!==t?t:u})();return(0,S.jsxs)(me,{children:[t.map(function(e){let{id:t,title:n,description:r,action:o,...a}=e;return(0,S.jsxs)(ye,{...a,children:[(0,S.jsxs)("div",{className:"grid gap-1",children:[n&&(0,S.jsx)("div",{className:"font-medium",children:n}),r&&(0,S.jsx)("div",{className:"text-sm opacity-90",children:r})]}),o]},t)}),(0,S.jsx)(ge,{})]})}function xe(e){let{children:t}=e;return(0,S.jsx)(i.O,{children:(0,S.jsxs)(l.C,{children:[t,(0,S.jsx)(we,{})]})})}class Se extends e.Component{constructor(e){super(e),this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){console.error("Error caught by ErrorBoundary:",e,t),this.props.onError&&this.props.onError(e,t)}render(){return this.state.hasError?this.props.fallback||(0,S.jsx)("div",{className:"p-4 text-red-600 bg-red-100 rounded-md",children:"Something went wrong. Please refresh the page or try again later."}):this.props.children}}const ke=e=>{let{children:t,role:n}=e;const{user:r,loading:a}=(0,i.A)();return a?(0,S.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,S.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})}):r?n&&r.role!==n?(0,S.jsx)(o.C5,{to:"/login",replace:!0}):(0,S.jsx)(S.Fragment,{children:t}):(0,S.jsx)(o.C5,{to:"/login",replace:!0})};function Ee(e){let{size:t="md",className:n=""}=e;return(0,S.jsx)("div",{className:`flex items-center justify-center ${n}`,children:(0,S.jsx)("div",{className:`animate-spin rounded-full border-t-2 border-b-2 border-primary ${{sm:"h-4 w-4 border-2",md:"h-8 w-8 border-2",lg:"h-12 w-12 border-4"}[t]}`})})}var Ae=n(8052),Ce=n(8168),Pe=n(8587),Re=n(7868),Oe=n(9172),Te=n(7758),Ne=n(8812),je=n(8280);var Me=n(7266);const _e={black:"#000",white:"#fff"},Le={50:"#fafafa",100:"#f5f5f5",200:"#eeeeee",300:"#e0e0e0",400:"#bdbdbd",500:"#9e9e9e",600:"#757575",700:"#616161",800:"#424242",900:"#212121",A100:"#f5f5f5",A200:"#eeeeee",A400:"#bdbdbd",A700:"#616161"},ze={50:"#f3e5f5",100:"#e1bee7",200:"#ce93d8",300:"#ba68c8",400:"#ab47bc",500:"#9c27b0",600:"#8e24aa",700:"#7b1fa2",800:"#6a1b9a",900:"#4a148c",A100:"#ea80fc",A200:"#e040fb",A400:"#d500f9",A700:"#aa00ff"},Ie={50:"#ffebee",100:"#ffcdd2",200:"#ef9a9a",300:"#e57373",400:"#ef5350",500:"#f44336",600:"#e53935",700:"#d32f2f",800:"#c62828",900:"#b71c1c",A100:"#ff8a80",A200:"#ff5252",A400:"#ff1744",A700:"#d50000"},De={50:"#fff3e0",100:"#ffe0b2",200:"#ffcc80",300:"#ffb74d",400:"#ffa726",500:"#ff9800",600:"#fb8c00",700:"#f57c00",800:"#ef6c00",900:"#e65100",A100:"#ffd180",A200:"#ffab40",A400:"#ff9100",A700:"#ff6d00"},$e={50:"#e3f2fd",100:"#bbdefb",200:"#90caf9",300:"#64b5f6",400:"#42a5f5",500:"#2196f3",600:"#1e88e5",700:"#1976d2",800:"#1565c0",900:"#0d47a1",A100:"#82b1ff",A200:"#448aff",A400:"#2979ff",A700:"#2962ff"},Fe={50:"#e1f5fe",100:"#b3e5fc",200:"#81d4fa",300:"#4fc3f7",400:"#29b6f6",500:"#03a9f4",600:"#039be5",700:"#0288d1",800:"#0277bd",900:"#01579b",A100:"#80d8ff",A200:"#40c4ff",A400:"#00b0ff",A700:"#0091ea"},Be={50:"#e8f5e9",100:"#c8e6c9",200:"#a5d6a7",300:"#81c784",400:"#66bb6a",500:"#4caf50",600:"#43a047",700:"#388e3c",800:"#2e7d32",900:"#1b5e20",A100:"#b9f6ca",A200:"#69f0ae",A400:"#00e676",A700:"#00c853"},Ue=["mode","contrastThreshold","tonalOffset"],We={text:{primary:"rgba(0, 0, 0, 0.87)",secondary:"rgba(0, 0, 0, 0.6)",disabled:"rgba(0, 0, 0, 0.38)"},divider:"rgba(0, 0, 0, 0.12)",background:{paper:_e.white,default:_e.white},action:{active:"rgba(0, 0, 0, 0.54)",hover:"rgba(0, 0, 0, 0.04)",hoverOpacity:.04,selected:"rgba(0, 0, 0, 0.08)",selectedOpacity:.08,disabled:"rgba(0, 0, 0, 0.26)",disabledBackground:"rgba(0, 0, 0, 0.12)",disabledOpacity:.38,focus:"rgba(0, 0, 0, 0.12)",focusOpacity:.12,activatedOpacity:.12}},Ve={text:{primary:_e.white,secondary:"rgba(255, 255, 255, 0.7)",disabled:"rgba(255, 255, 255, 0.5)",icon:"rgba(255, 255, 255, 0.5)"},divider:"rgba(255, 255, 255, 0.12)",background:{paper:"#121212",default:"#121212"},action:{active:_e.white,hover:"rgba(255, 255, 255, 0.08)",hoverOpacity:.08,selected:"rgba(255, 255, 255, 0.16)",selectedOpacity:.16,disabled:"rgba(255, 255, 255, 0.3)",disabledBackground:"rgba(255, 255, 255, 0.12)",disabledOpacity:.38,focus:"rgba(255, 255, 255, 0.12)",focusOpacity:.12,activatedOpacity:.24}};function He(e,t,n,r){const o=r.light||r,a=r.dark||1.5*r;e[t]||(e.hasOwnProperty(n)?e[t]=e[n]:"light"===t?e.light=(0,Me.a)(e.main,o):"dark"===t&&(e.dark=(0,Me.e$)(e.main,a)))}function qe(e){const{mode:t="light",contrastThreshold:n=3,tonalOffset:r=.2}=e,o=(0,Pe.A)(e,Ue),a=e.primary||function(){return"dark"===(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light")?{main:$e[200],light:$e[50],dark:$e[400]}:{main:$e[700],light:$e[400],dark:$e[800]}}(t),i=e.secondary||function(){return"dark"===(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light")?{main:ze[200],light:ze[50],dark:ze[400]}:{main:ze[500],light:ze[300],dark:ze[700]}}(t),l=e.error||function(){return"dark"===(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light")?{main:Ie[500],light:Ie[300],dark:Ie[700]}:{main:Ie[700],light:Ie[400],dark:Ie[800]}}(t),s=e.info||function(){return"dark"===(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light")?{main:Fe[400],light:Fe[300],dark:Fe[700]}:{main:Fe[700],light:Fe[500],dark:Fe[900]}}(t),u=e.success||function(){return"dark"===(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light")?{main:Be[400],light:Be[300],dark:Be[700]}:{main:Be[800],light:Be[500],dark:Be[900]}}(t),c=e.warning||function(){return"dark"===(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light")?{main:De[400],light:De[300],dark:De[700]}:{main:"#ed6c02",light:De[500],dark:De[900]}}(t);function d(e){return(0,Me.eM)(e,Ve.text.primary)>=n?Ve.text.primary:We.text.primary}const f=e=>{let{color:t,name:n,mainShade:o=500,lightShade:a=300,darkShade:i=700}=e;if(t=(0,Ce.A)({},t),!t.main&&t[o]&&(t.main=t[o]),!t.hasOwnProperty("main"))throw new Error((0,Re.A)(11,n?` (${n})`:"",o));if("string"!==typeof t.main)throw new Error((0,Re.A)(12,n?` (${n})`:"",JSON.stringify(t.main)));return He(t,"light",a,r),He(t,"dark",i,r),t.contrastText||(t.contrastText=d(t.main)),t},p={dark:Ve,light:We};return(0,Oe.A)((0,Ce.A)({common:(0,Ce.A)({},_e),mode:t,primary:f({color:a,name:"primary"}),secondary:f({color:i,name:"secondary",mainShade:"A400",lightShade:"A200",darkShade:"A700"}),error:f({color:l,name:"error"}),warning:f({color:c,name:"warning"}),info:f({color:s,name:"info"}),success:f({color:u,name:"success"}),grey:Le,contrastThreshold:n,getContrastText:d,augmentColor:f,tonalOffset:r},p[t]),o)}const Ke=["fontFamily","fontSize","fontWeightLight","fontWeightRegular","fontWeightMedium","fontWeightBold","htmlFontSize","allVariants","pxToRem"];const Ge={textTransform:"uppercase"},Xe='"Roboto", "Helvetica", "Arial", sans-serif';function Qe(e,t){const n="function"===typeof t?t(e):t,{fontFamily:r=Xe,fontSize:o=14,fontWeightLight:a=300,fontWeightRegular:i=400,fontWeightMedium:l=500,fontWeightBold:s=700,htmlFontSize:u=16,allVariants:c,pxToRem:d}=n,f=(0,Pe.A)(n,Ke);const p=o/14,h=d||(e=>e/u*p+"rem"),m=(e,t,n,o,a)=>{return(0,Ce.A)({fontFamily:r,fontWeight:e,fontSize:h(t),lineHeight:n},r===Xe?{letterSpacing:(i=o/t,Math.round(1e5*i)/1e5)+"em"}:{},a,c);var i},g={h1:m(a,96,1.167,-1.5),h2:m(a,60,1.2,-.5),h3:m(i,48,1.167,0),h4:m(i,34,1.235,.25),h5:m(i,24,1.334,0),h6:m(l,20,1.6,.15),subtitle1:m(i,16,1.75,.15),subtitle2:m(l,14,1.57,.1),body1:m(i,16,1.5,.15),body2:m(i,14,1.43,.15),button:m(l,14,1.75,.4,Ge),caption:m(i,12,1.66,.4),overline:m(i,12,2.66,1,Ge),inherit:{fontFamily:"inherit",fontWeight:"inherit",fontSize:"inherit",lineHeight:"inherit",letterSpacing:"inherit"}};return(0,Oe.A)((0,Ce.A)({htmlFontSize:u,pxToRem:h,fontFamily:r,fontSize:o,fontWeightLight:a,fontWeightRegular:i,fontWeightMedium:l,fontWeightBold:s},g),f,{clone:!1})}function Ye(){return[`${arguments.length<=0?void 0:arguments[0]}px ${arguments.length<=1?void 0:arguments[1]}px ${arguments.length<=2?void 0:arguments[2]}px ${arguments.length<=3?void 0:arguments[3]}px rgba(0,0,0,0.2)`,`${arguments.length<=4?void 0:arguments[4]}px ${arguments.length<=5?void 0:arguments[5]}px ${arguments.length<=6?void 0:arguments[6]}px ${arguments.length<=7?void 0:arguments[7]}px rgba(0,0,0,0.14)`,`${arguments.length<=8?void 0:arguments[8]}px ${arguments.length<=9?void 0:arguments[9]}px ${arguments.length<=10?void 0:arguments[10]}px ${arguments.length<=11?void 0:arguments[11]}px rgba(0,0,0,0.12)`].join(",")}const Je=["none",Ye(0,2,1,-1,0,1,1,0,0,1,3,0),Ye(0,3,1,-2,0,2,2,0,0,1,5,0),Ye(0,3,3,-2,0,3,4,0,0,1,8,0),Ye(0,2,4,-1,0,4,5,0,0,1,10,0),Ye(0,3,5,-1,0,5,8,0,0,1,14,0),Ye(0,3,5,-1,0,6,10,0,0,1,18,0),Ye(0,4,5,-2,0,7,10,1,0,2,16,1),Ye(0,5,5,-3,0,8,10,1,0,3,14,2),Ye(0,5,6,-3,0,9,12,1,0,3,16,2),Ye(0,6,6,-3,0,10,14,1,0,4,18,3),Ye(0,6,7,-4,0,11,15,1,0,4,20,3),Ye(0,7,8,-4,0,12,17,2,0,5,22,4),Ye(0,7,8,-4,0,13,19,2,0,5,24,4),Ye(0,7,9,-4,0,14,21,2,0,5,26,4),Ye(0,8,9,-5,0,15,22,2,0,6,28,5),Ye(0,8,10,-5,0,16,24,2,0,6,30,5),Ye(0,8,11,-5,0,17,26,2,0,6,32,5),Ye(0,9,11,-5,0,18,28,2,0,7,34,6),Ye(0,9,12,-6,0,19,29,2,0,7,36,6),Ye(0,10,13,-6,0,20,31,3,0,8,38,7),Ye(0,10,13,-6,0,21,33,3,0,8,40,7),Ye(0,10,14,-6,0,22,35,3,0,8,42,7),Ye(0,11,14,-7,0,23,36,3,0,9,44,8),Ye(0,11,15,-7,0,24,38,3,0,9,46,8)],Ze=["duration","easing","delay"],et={easeInOut:"cubic-bezier(0.4, 0, 0.2, 1)",easeOut:"cubic-bezier(0.0, 0, 0.2, 1)",easeIn:"cubic-bezier(0.4, 0, 1, 1)",sharp:"cubic-bezier(0.4, 0, 0.6, 1)"},tt={shortest:150,shorter:200,short:250,standard:300,complex:375,enteringScreen:225,leavingScreen:195};function nt(e){return`${Math.round(e)}ms`}function rt(e){if(!e)return 0;const t=e/36;return Math.round(10*(4+15*t**.25+t/5))}function ot(e){const t=(0,Ce.A)({},et,e.easing),n=(0,Ce.A)({},tt,e.duration);return(0,Ce.A)({getAutoHeightDuration:rt,create:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:["all"],r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{duration:o=n.standard,easing:a=t.easeInOut,delay:i=0}=r;(0,Pe.A)(r,Ze);return(Array.isArray(e)?e:[e]).map(e=>`${e} ${"string"===typeof o?o:nt(o)} ${a} ${"string"===typeof i?i:nt(i)}`).join(",")}},e,{easing:t,duration:n})}const at={mobileStepper:1e3,fab:1050,speedDial:1050,appBar:1100,drawer:1200,modal:1300,snackbar:1400,tooltip:1500},it=["breakpoints","mixins","spacing","palette","transitions","typography","shape"];function lt(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{mixins:t={},palette:n={},transitions:r={},typography:o={}}=e,a=(0,Pe.A)(e,it);if(e.vars&&void 0===e.generateCssVars)throw new Error((0,Re.A)(18));const i=qe(n),l=(0,je.A)(e);let s=(0,Oe.A)(l,{mixins:(u=l.breakpoints,c=t,(0,Ce.A)({toolbar:{minHeight:56,[u.up("xs")]:{"@media (orientation: landscape)":{minHeight:48}},[u.up("sm")]:{minHeight:64}}},c)),palette:i,shadows:Je.slice(),typography:Qe(i,o),transitions:ot(r),zIndex:(0,Ce.A)({},at)});var u,c;s=(0,Oe.A)(s,a);for(var d=arguments.length,f=new Array(d>1?d-1:0),p=1;p<d;p++)f[p-1]=arguments[p];return s=f.reduce((e,t)=>(0,Oe.A)(e,t),s),s.unstable_sxConfig=(0,Ce.A)({},Te.A,null==a?void 0:a.unstable_sxConfig),s.unstable_sx=function(e){return(0,Ne.A)({sx:e,theme:this})},s}const st=lt,ut=st(),ct="$$material";const dt=function(e){return"ownerState"!==e&&"theme"!==e&&"sx"!==e&&"as"!==e},ft=e=>dt(e)&&"classes"!==e,pt=(0,Ae.Ay)({themeId:ct,defaultTheme:ut,rootShouldForwardProp:ft});var ht=n(9369);const mt=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;const n=e.useContext(ht.T);return n&&(r=n,0!==Object.keys(r).length)?n:t;var r},gt=(0,je.A)();const vt=function(){return mt(arguments.length>0&&void 0!==arguments[0]?arguments[0]:gt)};function yt(){const e=vt(ut);return e[ct]||e}const bt="undefined"!==typeof window?e.useLayoutEffect:e.useEffect;function wt(e,t){const n=(0,Ce.A)({},t);return Object.keys(e).forEach(r=>{if(r.toString().match(/^(components|slots)$/))n[r]=(0,Ce.A)({},e[r],n[r]);else if(r.toString().match(/^(componentsProps|slotProps)$/)){const o=e[r]||{},a=t[r];n[r]={},a&&Object.keys(a)?o&&Object.keys(o)?(n[r]=(0,Ce.A)({},a),Object.keys(o).forEach(e=>{n[r][e]=wt(o[e],a[e])})):n[r]=a:n[r]=o}else void 0===n[r]&&(n[r]=e[r])}),n}function xt(t,n,r,o,a){const[i,l]=e.useState(()=>a&&r?r(t).matches:o?o(t).matches:n);return bt(()=>{let e=!0;if(!r)return;const n=r(t),o=()=>{e&&l(n.matches)};return o(),n.addListener(o),()=>{e=!1,n.removeListener(o)}},[t,r]),i}const St=t.useSyncExternalStore;function kt(t,n,r,o,a){const i=e.useCallback(()=>n,[n]),l=e.useMemo(()=>{if(a&&r)return()=>r(t).matches;if(null!==o){const{matches:e}=o(t);return()=>e}return i},[i,t,o,a,r]),[s,u]=e.useMemo(()=>{if(null===r)return[i,()=>()=>{}];const e=r(t);return[()=>e.matches,t=>(e.addListener(t),()=>{e.removeListener(t)})]},[i,r,t]);return St(u,s,l)}function Et(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n=mt(),r="undefined"!==typeof window&&"undefined"!==typeof window.matchMedia,{defaultMatches:o=!1,matchMedia:a=(r?window.matchMedia:null),ssrMatchMedia:i=null,noSsr:l=!1}=function(e){const{theme:t,name:n,props:r}=e;return t&&t.components&&t.components[n]&&t.components[n].defaultProps?wt(t.components[n].defaultProps,r):r}({name:"MuiUseMediaQuery",props:t,theme:n});let s="function"===typeof e?e(n):e;s=s.replace(/^@media( ?)/m,"");return(void 0!==St?kt:xt)(s,o,a,i,l)}var At=n(8387);function Ct(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;const r={};return Object.keys(e).forEach(o=>{r[o]=e[o].reduce((e,r)=>{if(r){const o=t(r);""!==o&&e.push(o),n&&n[r]&&e.push(n[r])}return e},[]).join(" ")}),r}const Pt=e.createContext(void 0);function Rt(t){let{props:n,name:r}=t;return function(e){const{theme:t,name:n,props:r}=e;if(!t||!t.components||!t.components[n])return r;const o=t.components[n];return o.defaultProps?wt(o.defaultProps,r):o.styleOverrides||o.variants?r:wt(o,r)}({props:n,name:r,theme:{components:e.useContext(Pt)}})}function Ot(e){return Rt(e)}const Tt=e=>e,Nt=(()=>{let e=Tt;return{configure(t){e=t},generate:t=>e(t),reset(){e=Tt}}})(),jt={active:"active",checked:"checked",completed:"completed",disabled:"disabled",error:"error",expanded:"expanded",focused:"focused",focusVisible:"focusVisible",open:"open",readOnly:"readOnly",required:"required",selected:"selected"};function Mt(e,t){const n=jt[t];return n?`${arguments.length>2&&void 0!==arguments[2]?arguments[2]:"Mui"}-${n}`:`${Nt.generate(e)}-${t}`}function _t(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"Mui";const r={};return t.forEach(t=>{r[t]=Mt(e,t,n)}),r}function Lt(e){return Mt("MuiToolbar",e)}_t("MuiToolbar",["root","gutters","regular","dense"]);const zt=["className","component","disableGutters","variant"],It=pt("div",{name:"MuiToolbar",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,!n.disableGutters&&t.gutters,t[n.variant]]}})(e=>{let{theme:t,ownerState:n}=e;return(0,Ce.A)({position:"relative",display:"flex",alignItems:"center"},!n.disableGutters&&{paddingLeft:t.spacing(2),paddingRight:t.spacing(2),[t.breakpoints.up("sm")]:{paddingLeft:t.spacing(3),paddingRight:t.spacing(3)}},"dense"===n.variant&&{minHeight:48})},e=>{let{theme:t,ownerState:n}=e;return"regular"===n.variant&&t.mixins.toolbar}),Dt=e.forwardRef(function(e,t){const n=Ot({props:e,name:"MuiToolbar"}),{className:r,component:o="div",disableGutters:a=!1,variant:i="regular"}=n,l=(0,Pe.A)(n,zt),s=(0,Ce.A)({},n,{component:o,disableGutters:a,variant:i}),u=(e=>{const{classes:t,disableGutters:n,variant:r}=e;return Ct({root:["root",!n&&"gutters",r]},Lt,t)})(s);return(0,S.jsx)(It,(0,Ce.A)({as:o,className:(0,At.A)(u.root,r),ref:t,ownerState:s},l))});var $t=n(8698);const Ft=n(7598).A;function Bt(e){return Mt("MuiTypography",e)}_t("MuiTypography",["root","h1","h2","h3","h4","h5","h6","subtitle1","subtitle2","body1","body2","inherit","button","caption","overline","alignLeft","alignRight","alignCenter","alignJustify","noWrap","gutterBottom","paragraph"]);const Ut=["align","className","component","gutterBottom","noWrap","paragraph","variant","variantMapping"],Wt=pt("span",{name:"MuiTypography",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,n.variant&&t[n.variant],"inherit"!==n.align&&t[`align${Ft(n.align)}`],n.noWrap&&t.noWrap,n.gutterBottom&&t.gutterBottom,n.paragraph&&t.paragraph]}})(e=>{let{theme:t,ownerState:n}=e;return(0,Ce.A)({margin:0},"inherit"===n.variant&&{font:"inherit"},"inherit"!==n.variant&&t.typography[n.variant],"inherit"!==n.align&&{textAlign:n.align},n.noWrap&&{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},n.gutterBottom&&{marginBottom:"0.35em"},n.paragraph&&{marginBottom:16})}),Vt={h1:"h1",h2:"h2",h3:"h3",h4:"h4",h5:"h5",h6:"h6",subtitle1:"h6",subtitle2:"h6",body1:"p",body2:"p",inherit:"p"},Ht={primary:"primary.main",textPrimary:"text.primary",secondary:"secondary.main",textSecondary:"text.secondary",error:"error.main"},qt=e.forwardRef(function(e,t){const n=Ot({props:e,name:"MuiTypography"}),r=(e=>Ht[e]||e)(n.color),o=(0,$t.A)((0,Ce.A)({},n,{color:r})),{align:a="inherit",className:i,component:l,gutterBottom:s=!1,noWrap:u=!1,paragraph:c=!1,variant:d="body1",variantMapping:f=Vt}=o,p=(0,Pe.A)(o,Ut),h=(0,Ce.A)({},o,{align:a,color:r,className:i,component:l,gutterBottom:s,noWrap:u,paragraph:c,variant:d,variantMapping:f}),m=l||(c?"p":f[d]||Vt[d])||"span",g=(e=>{const{align:t,gutterBottom:n,noWrap:r,paragraph:o,variant:a,classes:i}=e;return Ct({root:["root",a,"inherit"!==e.align&&`align${Ft(t)}`,n&&"gutterBottom",r&&"noWrap",o&&"paragraph"]},Bt,i)})(h);return(0,S.jsx)(Wt,(0,Ce.A)({as:m,ref:t,ownerState:h,className:(0,At.A)(g.root,i)},p))});function Kt(e){return Mt("MuiDivider",e)}const Gt=_t("MuiDivider",["root","absolute","fullWidth","inset","middle","flexItem","light","vertical","withChildren","withChildrenVertical","textAlignRight","textAlignLeft","wrapper","wrapperVertical"]),Xt=["absolute","children","className","component","flexItem","light","orientation","role","textAlign","variant"],Qt=pt("div",{name:"MuiDivider",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,n.absolute&&t.absolute,t[n.variant],n.light&&t.light,"vertical"===n.orientation&&t.vertical,n.flexItem&&t.flexItem,n.children&&t.withChildren,n.children&&"vertical"===n.orientation&&t.withChildrenVertical,"right"===n.textAlign&&"vertical"!==n.orientation&&t.textAlignRight,"left"===n.textAlign&&"vertical"!==n.orientation&&t.textAlignLeft]}})(e=>{let{theme:t,ownerState:n}=e;return(0,Ce.A)({margin:0,flexShrink:0,borderWidth:0,borderStyle:"solid",borderColor:(t.vars||t).palette.divider,borderBottomWidth:"thin"},n.absolute&&{position:"absolute",bottom:0,left:0,width:"100%"},n.light&&{borderColor:t.vars?`rgba(${t.vars.palette.dividerChannel} / 0.08)`:(0,Me.X4)(t.palette.divider,.08)},"inset"===n.variant&&{marginLeft:72},"middle"===n.variant&&"horizontal"===n.orientation&&{marginLeft:t.spacing(2),marginRight:t.spacing(2)},"middle"===n.variant&&"vertical"===n.orientation&&{marginTop:t.spacing(1),marginBottom:t.spacing(1)},"vertical"===n.orientation&&{height:"100%",borderBottomWidth:0,borderRightWidth:"thin"},n.flexItem&&{alignSelf:"stretch",height:"auto"})},e=>{let{ownerState:t}=e;return(0,Ce.A)({},t.children&&{display:"flex",whiteSpace:"nowrap",textAlign:"center",border:0,borderTopStyle:"solid",borderLeftStyle:"solid","&::before, &::after":{content:'""',alignSelf:"center"}})},e=>{let{theme:t,ownerState:n}=e;return(0,Ce.A)({},n.children&&"vertical"!==n.orientation&&{"&::before, &::after":{width:"100%",borderTop:`thin solid ${(t.vars||t).palette.divider}`,borderTopStyle:"inherit"}})},e=>{let{theme:t,ownerState:n}=e;return(0,Ce.A)({},n.children&&"vertical"===n.orientation&&{flexDirection:"column","&::before, &::after":{height:"100%",borderLeft:`thin solid ${(t.vars||t).palette.divider}`,borderLeftStyle:"inherit"}})},e=>{let{ownerState:t}=e;return(0,Ce.A)({},"right"===t.textAlign&&"vertical"!==t.orientation&&{"&::before":{width:"90%"},"&::after":{width:"10%"}},"left"===t.textAlign&&"vertical"!==t.orientation&&{"&::before":{width:"10%"},"&::after":{width:"90%"}})}),Yt=pt("span",{name:"MuiDivider",slot:"Wrapper",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.wrapper,"vertical"===n.orientation&&t.wrapperVertical]}})(e=>{let{theme:t,ownerState:n}=e;return(0,Ce.A)({display:"inline-block",paddingLeft:`calc(${t.spacing(1)} * 1.2)`,paddingRight:`calc(${t.spacing(1)} * 1.2)`},"vertical"===n.orientation&&{paddingTop:`calc(${t.spacing(1)} * 1.2)`,paddingBottom:`calc(${t.spacing(1)} * 1.2)`})}),Jt=e.forwardRef(function(e,t){const n=Ot({props:e,name:"MuiDivider"}),{absolute:r=!1,children:o,className:a,component:i=(o?"div":"hr"),flexItem:l=!1,light:s=!1,orientation:u="horizontal",role:c=("hr"!==i?"separator":void 0),textAlign:d="center",variant:f="fullWidth"}=n,p=(0,Pe.A)(n,Xt),h=(0,Ce.A)({},n,{absolute:r,component:i,flexItem:l,light:s,orientation:u,role:c,textAlign:d,variant:f}),m=(e=>{const{absolute:t,children:n,classes:r,flexItem:o,light:a,orientation:i,textAlign:l,variant:s}=e;return Ct({root:["root",t&&"absolute",s,a&&"light","vertical"===i&&"vertical",o&&"flexItem",n&&"withChildren",n&&"vertical"===i&&"withChildrenVertical","right"===l&&"vertical"!==i&&"textAlignRight","left"===l&&"vertical"!==i&&"textAlignLeft"],wrapper:["wrapper","vertical"===i&&"wrapperVertical"]},Kt,r)})(h);return(0,S.jsx)(Qt,(0,Ce.A)({as:i,className:(0,At.A)(m.root,a),role:c,ref:t,ownerState:h},p,{children:o?(0,S.jsx)(Yt,{className:m.wrapper,ownerState:h,children:o}):null}))});Jt.muiSkipListHighlight=!0;const Zt=Jt;const en=e.createContext({});function tn(e){return Mt("MuiList",e)}_t("MuiList",["root","padding","dense","subheader"]);const nn=["children","className","component","dense","disablePadding","subheader"],rn=pt("ul",{name:"MuiList",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,!n.disablePadding&&t.padding,n.dense&&t.dense,n.subheader&&t.subheader]}})(e=>{let{ownerState:t}=e;return(0,Ce.A)({listStyle:"none",margin:0,padding:0,position:"relative"},!t.disablePadding&&{paddingTop:8,paddingBottom:8},t.subheader&&{paddingTop:0})}),on=e.forwardRef(function(t,n){const r=Ot({props:t,name:"MuiList"}),{children:o,className:a,component:i="ul",dense:l=!1,disablePadding:s=!1,subheader:u}=r,c=(0,Pe.A)(r,nn),d=e.useMemo(()=>({dense:l}),[l]),f=(0,Ce.A)({},r,{component:i,dense:l,disablePadding:s}),p=(e=>{const{classes:t,disablePadding:n,dense:r,subheader:o}=e;return Ct({root:["root",!n&&"padding",r&&"dense",o&&"subheader"]},tn,t)})(f);return(0,S.jsx)(en.Provider,{value:d,children:(0,S.jsxs)(rn,(0,Ce.A)({as:i,className:(0,At.A)(p.root,a),ref:n,ownerState:f},c,{children:[u,o]}))})});const an=function(e){return"string"===typeof e};function ln(e,t){"function"===typeof e?e(t):e&&(e.current=t)}function sn(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return e.useMemo(()=>n.every(e=>null==e)?null:e=>{n.forEach(t=>{ln(t,e)})},n)}const un=sn;const cn=function(t){const n=e.useRef(t);return bt(()=>{n.current=t}),e.useRef(function(){return(0,n.current)(...arguments)}).current},dn=cn,fn={};const pn=[];class hn{constructor(){this.currentId=null,this.clear=()=>{null!==this.currentId&&(clearTimeout(this.currentId),this.currentId=null)},this.disposeEffect=()=>this.clear}static create(){return new hn}start(e,t){this.clear(),this.currentId=setTimeout(()=>{this.currentId=null,t()},e)}}function mn(){const t=function(t,n){const r=e.useRef(fn);return r.current===fn&&(r.current=t(n)),r}(hn.create).current;var n;return n=t.disposeEffect,e.useEffect(n,pn),t}let gn=!0,vn=!1;const yn=new hn,bn={text:!0,search:!0,url:!0,tel:!0,email:!0,password:!0,number:!0,date:!0,month:!0,week:!0,time:!0,datetime:!0,"datetime-local":!0};function wn(e){e.metaKey||e.altKey||e.ctrlKey||(gn=!0)}function xn(){gn=!1}function Sn(){"hidden"===this.visibilityState&&vn&&(gn=!0)}function kn(e){const{target:t}=e;try{return t.matches(":focus-visible")}catch(n){}return gn||function(e){const{type:t,tagName:n}=e;return!("INPUT"!==n||!bn[t]||e.readOnly)||"TEXTAREA"===n&&!e.readOnly||!!e.isContentEditable}(t)}const En=function(){const t=e.useCallback(e=>{var t;null!=e&&((t=e.ownerDocument).addEventListener("keydown",wn,!0),t.addEventListener("mousedown",xn,!0),t.addEventListener("pointerdown",xn,!0),t.addEventListener("touchstart",xn,!0),t.addEventListener("visibilitychange",Sn,!0))},[]),n=e.useRef(!1);return{isFocusVisibleRef:n,onFocus:function(e){return!!kn(e)&&(n.current=!0,!0)},onBlur:function(){return!!n.current&&(vn=!0,yn.start(100,()=>{vn=!1}),n.current=!1,!0)},ref:t}};function An(e,t){return An=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},An(e,t)}function Cn(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,An(e,t)}const Pn=e.createContext(null);function Rn(t,n){var r=Object.create(null);return t&&e.Children.map(t,function(e){return e}).forEach(function(t){r[t.key]=function(t){return n&&(0,e.isValidElement)(t)?n(t):t}(t)}),r}function On(e,t,n){return null!=n[t]?n[t]:e.props[t]}function Tn(t,n,r){var o=Rn(t.children),a=function(e,t){function n(n){return n in t?t[n]:e[n]}e=e||{},t=t||{};var r,o=Object.create(null),a=[];for(var i in e)i in t?a.length&&(o[i]=a,a=[]):a.push(i);var l={};for(var s in t){if(o[s])for(r=0;r<o[s].length;r++){var u=o[s][r];l[o[s][r]]=n(u)}l[s]=n(s)}for(r=0;r<a.length;r++)l[a[r]]=n(a[r]);return l}(n,o);return Object.keys(a).forEach(function(i){var l=a[i];if((0,e.isValidElement)(l)){var s=i in n,u=i in o,c=n[i],d=(0,e.isValidElement)(c)&&!c.props.in;!u||s&&!d?u||!s||d?u&&s&&(0,e.isValidElement)(c)&&(a[i]=(0,e.cloneElement)(l,{onExited:r.bind(null,l),in:c.props.in,exit:On(l,"exit",t),enter:On(l,"enter",t)})):a[i]=(0,e.cloneElement)(l,{in:!1}):a[i]=(0,e.cloneElement)(l,{onExited:r.bind(null,l),in:!0,exit:On(l,"exit",t),enter:On(l,"enter",t)})}}),a}var Nn=Object.values||function(e){return Object.keys(e).map(function(t){return e[t]})},jn=function(t){function n(e,n){var r,o=(r=t.call(this,e,n)||this).handleExited.bind(function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(r));return r.state={contextValue:{isMounting:!0},handleExited:o,firstRender:!0},r}Cn(n,t);var r=n.prototype;return r.componentDidMount=function(){this.mounted=!0,this.setState({contextValue:{isMounting:!1}})},r.componentWillUnmount=function(){this.mounted=!1},n.getDerivedStateFromProps=function(t,n){var r,o,a=n.children,i=n.handleExited;return{children:n.firstRender?(r=t,o=i,Rn(r.children,function(t){return(0,e.cloneElement)(t,{onExited:o.bind(null,t),in:!0,appear:On(t,"appear",r),enter:On(t,"enter",r),exit:On(t,"exit",r)})})):Tn(t,a,i),firstRender:!1}},r.handleExited=function(e,t){var n=Rn(this.props.children);e.key in n||(e.props.onExited&&e.props.onExited(t),this.mounted&&this.setState(function(t){var n=(0,Ce.A)({},t.children);return delete n[e.key],{children:n}}))},r.render=function(){var t=this.props,n=t.component,r=t.childFactory,o=(0,Pe.A)(t,["component","childFactory"]),a=this.state.contextValue,i=Nn(this.state.children).map(r);return delete o.appear,delete o.enter,delete o.exit,null===n?e.createElement(Pn.Provider,{value:a},i):e.createElement(Pn.Provider,{value:a},e.createElement(n,o,i))},n}(e.Component);jn.propTypes={},jn.defaultProps={component:"div",childFactory:function(e){return e}};const Mn=jn;var _n=n(3290);const Ln=function(t){const{className:n,classes:r,pulsate:o=!1,rippleX:a,rippleY:i,rippleSize:l,in:s,onExited:u,timeout:c}=t,[d,f]=e.useState(!1),p=(0,At.A)(n,r.ripple,r.rippleVisible,o&&r.ripplePulsate),h={width:l,height:l,top:-l/2+i,left:-l/2+a},m=(0,At.A)(r.child,d&&r.childLeaving,o&&r.childPulsate);return s||d||f(!0),e.useEffect(()=>{if(!s&&null!=u){const e=setTimeout(u,c);return()=>{clearTimeout(e)}}},[u,s,c]),(0,S.jsx)("span",{className:p,style:h,children:(0,S.jsx)("span",{className:m})})};const zn=_t("MuiTouchRipple",["root","ripple","rippleVisible","ripplePulsate","child","childLeaving","childPulsate"]),In=["center","classes","className"];let Dn,$n,Fn,Bn,Un=e=>e;const Wn=(0,_n.i7)(Dn||(Dn=Un`
  0% {
    transform: scale(0);
    opacity: 0.1;
  }

  100% {
    transform: scale(1);
    opacity: 0.3;
  }
`)),Vn=(0,_n.i7)($n||($n=Un`
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
`)),Hn=(0,_n.i7)(Fn||(Fn=Un`
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(0.92);
  }

  100% {
    transform: scale(1);
  }
`)),qn=pt("span",{name:"MuiTouchRipple",slot:"Root"})({overflow:"hidden",pointerEvents:"none",position:"absolute",zIndex:0,top:0,right:0,bottom:0,left:0,borderRadius:"inherit"}),Kn=pt(Ln,{name:"MuiTouchRipple",slot:"Ripple"})(Bn||(Bn=Un`
  opacity: 0;
  position: absolute;

  &.${0} {
    opacity: 0.3;
    transform: scale(1);
    animation-name: ${0};
    animation-duration: ${0}ms;
    animation-timing-function: ${0};
  }

  &.${0} {
    animation-duration: ${0}ms;
  }

  & .${0} {
    opacity: 1;
    display: block;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: currentColor;
  }

  & .${0} {
    opacity: 0;
    animation-name: ${0};
    animation-duration: ${0}ms;
    animation-timing-function: ${0};
  }

  & .${0} {
    position: absolute;
    /* @noflip */
    left: 0px;
    top: 0;
    animation-name: ${0};
    animation-duration: 2500ms;
    animation-timing-function: ${0};
    animation-iteration-count: infinite;
    animation-delay: 200ms;
  }
`),zn.rippleVisible,Wn,550,e=>{let{theme:t}=e;return t.transitions.easing.easeInOut},zn.ripplePulsate,e=>{let{theme:t}=e;return t.transitions.duration.shorter},zn.child,zn.childLeaving,Vn,550,e=>{let{theme:t}=e;return t.transitions.easing.easeInOut},zn.childPulsate,Hn,e=>{let{theme:t}=e;return t.transitions.easing.easeInOut}),Gn=e.forwardRef(function(t,n){const r=Ot({props:t,name:"MuiTouchRipple"}),{center:o=!1,classes:a={},className:i}=r,l=(0,Pe.A)(r,In),[s,u]=e.useState([]),c=e.useRef(0),d=e.useRef(null);e.useEffect(()=>{d.current&&(d.current(),d.current=null)},[s]);const f=e.useRef(!1),p=mn(),h=e.useRef(null),m=e.useRef(null),g=e.useCallback(e=>{const{pulsate:t,rippleX:n,rippleY:r,rippleSize:o,cb:i}=e;u(e=>[...e,(0,S.jsx)(Kn,{classes:{ripple:(0,At.A)(a.ripple,zn.ripple),rippleVisible:(0,At.A)(a.rippleVisible,zn.rippleVisible),ripplePulsate:(0,At.A)(a.ripplePulsate,zn.ripplePulsate),child:(0,At.A)(a.child,zn.child),childLeaving:(0,At.A)(a.childLeaving,zn.childLeaving),childPulsate:(0,At.A)(a.childPulsate,zn.childPulsate)},timeout:550,pulsate:t,rippleX:n,rippleY:r,rippleSize:o},c.current)]),c.current+=1,d.current=i},[a]),v=e.useCallback(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:()=>{};const{pulsate:r=!1,center:a=o||t.pulsate,fakeElement:i=!1}=t;if("mousedown"===(null==e?void 0:e.type)&&f.current)return void(f.current=!1);"touchstart"===(null==e?void 0:e.type)&&(f.current=!0);const l=i?null:m.current,s=l?l.getBoundingClientRect():{width:0,height:0,left:0,top:0};let u,c,d;if(a||void 0===e||0===e.clientX&&0===e.clientY||!e.clientX&&!e.touches)u=Math.round(s.width/2),c=Math.round(s.height/2);else{const{clientX:t,clientY:n}=e.touches&&e.touches.length>0?e.touches[0]:e;u=Math.round(t-s.left),c=Math.round(n-s.top)}if(a)d=Math.sqrt((2*s.width**2+s.height**2)/3),d%2===0&&(d+=1);else{const e=2*Math.max(Math.abs((l?l.clientWidth:0)-u),u)+2,t=2*Math.max(Math.abs((l?l.clientHeight:0)-c),c)+2;d=Math.sqrt(e**2+t**2)}null!=e&&e.touches?null===h.current&&(h.current=()=>{g({pulsate:r,rippleX:u,rippleY:c,rippleSize:d,cb:n})},p.start(80,()=>{h.current&&(h.current(),h.current=null)})):g({pulsate:r,rippleX:u,rippleY:c,rippleSize:d,cb:n})},[o,g,p]),y=e.useCallback(()=>{v({},{pulsate:!0})},[v]),b=e.useCallback((e,t)=>{if(p.clear(),"touchend"===(null==e?void 0:e.type)&&h.current)return h.current(),h.current=null,void p.start(0,()=>{b(e,t)});h.current=null,u(e=>e.length>0?e.slice(1):e),d.current=t},[p]);return e.useImperativeHandle(n,()=>({pulsate:y,start:v,stop:b}),[y,v,b]),(0,S.jsx)(qn,(0,Ce.A)({className:(0,At.A)(zn.root,a.root,i),ref:m},l,{children:(0,S.jsx)(Mn,{component:null,exit:!0,children:s})}))}),Xn=Gn;function Qn(e){return Mt("MuiButtonBase",e)}const Yn=_t("MuiButtonBase",["root","disabled","focusVisible"]),Jn=["action","centerRipple","children","className","component","disabled","disableRipple","disableTouchRipple","focusRipple","focusVisibleClassName","LinkComponent","onBlur","onClick","onContextMenu","onDragLeave","onFocus","onFocusVisible","onKeyDown","onKeyUp","onMouseDown","onMouseLeave","onMouseUp","onTouchEnd","onTouchMove","onTouchStart","tabIndex","TouchRippleProps","touchRippleRef","type"],Zn=pt("button",{name:"MuiButtonBase",slot:"Root",overridesResolver:(e,t)=>t.root})({display:"inline-flex",alignItems:"center",justifyContent:"center",position:"relative",boxSizing:"border-box",WebkitTapHighlightColor:"transparent",backgroundColor:"transparent",outline:0,border:0,margin:0,borderRadius:0,padding:0,cursor:"pointer",userSelect:"none",verticalAlign:"middle",MozAppearance:"none",WebkitAppearance:"none",textDecoration:"none",color:"inherit","&::-moz-focus-inner":{borderStyle:"none"},[`&.${Yn.disabled}`]:{pointerEvents:"none",cursor:"default"},"@media print":{colorAdjust:"exact"}}),er=e.forwardRef(function(t,n){const r=Ot({props:t,name:"MuiButtonBase"}),{action:o,centerRipple:a=!1,children:i,className:l,component:s="button",disabled:u=!1,disableRipple:c=!1,disableTouchRipple:d=!1,focusRipple:f=!1,LinkComponent:p="a",onBlur:h,onClick:m,onContextMenu:g,onDragLeave:v,onFocus:y,onFocusVisible:b,onKeyDown:w,onKeyUp:x,onMouseDown:k,onMouseLeave:E,onMouseUp:A,onTouchEnd:C,onTouchMove:P,onTouchStart:R,tabIndex:O=0,TouchRippleProps:T,touchRippleRef:N,type:j}=r,M=(0,Pe.A)(r,Jn),_=e.useRef(null),L=e.useRef(null),z=un(L,N),{isFocusVisibleRef:I,onFocus:D,onBlur:$,ref:F}=En(),[B,U]=e.useState(!1);u&&B&&U(!1),e.useImperativeHandle(o,()=>({focusVisible:()=>{U(!0),_.current.focus()}}),[]);const[W,V]=e.useState(!1);e.useEffect(()=>{V(!0)},[]);const H=W&&!c&&!u;function q(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:d;return dn(r=>{t&&t(r);return!n&&L.current&&L.current[e](r),!0})}e.useEffect(()=>{B&&f&&!c&&W&&L.current.pulsate()},[c,f,B,W]);const K=q("start",k),G=q("stop",g),X=q("stop",v),Q=q("stop",A),Y=q("stop",e=>{B&&e.preventDefault(),E&&E(e)}),J=q("start",R),Z=q("stop",C),ee=q("stop",P),te=q("stop",e=>{$(e),!1===I.current&&U(!1),h&&h(e)},!1),ne=dn(e=>{_.current||(_.current=e.currentTarget),D(e),!0===I.current&&(U(!0),b&&b(e)),y&&y(e)}),re=()=>{const e=_.current;return s&&"button"!==s&&!("A"===e.tagName&&e.href)},oe=e.useRef(!1),ae=dn(e=>{f&&!oe.current&&B&&L.current&&" "===e.key&&(oe.current=!0,L.current.stop(e,()=>{L.current.start(e)})),e.target===e.currentTarget&&re()&&" "===e.key&&e.preventDefault(),w&&w(e),e.target===e.currentTarget&&re()&&"Enter"===e.key&&!u&&(e.preventDefault(),m&&m(e))}),ie=dn(e=>{f&&" "===e.key&&L.current&&B&&!e.defaultPrevented&&(oe.current=!1,L.current.stop(e,()=>{L.current.pulsate(e)})),x&&x(e),m&&e.target===e.currentTarget&&re()&&" "===e.key&&!e.defaultPrevented&&m(e)});let le=s;"button"===le&&(M.href||M.to)&&(le=p);const se={};"button"===le?(se.type=void 0===j?"button":j,se.disabled=u):(M.href||M.to||(se.role="button"),u&&(se["aria-disabled"]=u));const ue=un(n,F,_);const ce=(0,Ce.A)({},r,{centerRipple:a,component:s,disabled:u,disableRipple:c,disableTouchRipple:d,focusRipple:f,tabIndex:O,focusVisible:B}),de=(e=>{const{disabled:t,focusVisible:n,focusVisibleClassName:r,classes:o}=e,a=Ct({root:["root",t&&"disabled",n&&"focusVisible"]},Qn,o);return n&&r&&(a.root+=` ${r}`),a})(ce);return(0,S.jsxs)(Zn,(0,Ce.A)({as:le,className:(0,At.A)(de.root,l),ownerState:ce,onBlur:te,onClick:m,onContextMenu:G,onFocus:ne,onKeyDown:ae,onKeyUp:ie,onMouseDown:K,onMouseLeave:Y,onMouseUp:Q,onDragLeave:X,onTouchEnd:Z,onTouchMove:ee,onTouchStart:J,ref:ue,tabIndex:u?-1:O,type:j},se,M,{children:[i,H?(0,S.jsx)(Xn,(0,Ce.A)({ref:z,center:a},T)):null]}))}),tr=er;const nr=function(t,n){var r,o;return e.isValidElement(t)&&-1!==n.indexOf(null!=(r=t.type.muiName)?r:null==(o=t.type)||null==(o=o._payload)||null==(o=o.value)?void 0:o.muiName)},rr=bt;function or(e){return Mt("MuiListItem",e)}const ar=_t("MuiListItem",["root","container","focusVisible","dense","alignItemsFlexStart","disabled","divider","gutters","padding","button","secondaryAction","selected"]);function ir(e){return Mt("MuiListItemButton",e)}const lr=_t("MuiListItemButton",["root","focusVisible","dense","alignItemsFlexStart","disabled","divider","gutters","selected"]);function sr(e){return Mt("MuiListItemSecondaryAction",e)}_t("MuiListItemSecondaryAction",["root","disableGutters"]);const ur=["className"],cr=pt("div",{name:"MuiListItemSecondaryAction",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,n.disableGutters&&t.disableGutters]}})(e=>{let{ownerState:t}=e;return(0,Ce.A)({position:"absolute",right:16,top:"50%",transform:"translateY(-50%)"},t.disableGutters&&{right:0})}),dr=e.forwardRef(function(t,n){const r=Ot({props:t,name:"MuiListItemSecondaryAction"}),{className:o}=r,a=(0,Pe.A)(r,ur),i=e.useContext(en),l=(0,Ce.A)({},r,{disableGutters:i.disableGutters}),s=(e=>{const{disableGutters:t,classes:n}=e;return Ct({root:["root",t&&"disableGutters"]},sr,n)})(l);return(0,S.jsx)(cr,(0,Ce.A)({className:(0,At.A)(s.root,o),ownerState:l,ref:n},a))});dr.muiName="ListItemSecondaryAction";const fr=dr,pr=["className"],hr=["alignItems","autoFocus","button","children","className","component","components","componentsProps","ContainerComponent","ContainerProps","dense","disabled","disableGutters","disablePadding","divider","focusVisibleClassName","secondaryAction","selected","slotProps","slots"],mr=pt("div",{name:"MuiListItem",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,n.dense&&t.dense,"flex-start"===n.alignItems&&t.alignItemsFlexStart,n.divider&&t.divider,!n.disableGutters&&t.gutters,!n.disablePadding&&t.padding,n.button&&t.button,n.hasSecondaryAction&&t.secondaryAction]}})(e=>{let{theme:t,ownerState:n}=e;return(0,Ce.A)({display:"flex",justifyContent:"flex-start",alignItems:"center",position:"relative",textDecoration:"none",width:"100%",boxSizing:"border-box",textAlign:"left"},!n.disablePadding&&(0,Ce.A)({paddingTop:8,paddingBottom:8},n.dense&&{paddingTop:4,paddingBottom:4},!n.disableGutters&&{paddingLeft:16,paddingRight:16},!!n.secondaryAction&&{paddingRight:48}),!!n.secondaryAction&&{[`& > .${lr.root}`]:{paddingRight:48}},{[`&.${ar.focusVisible}`]:{backgroundColor:(t.vars||t).palette.action.focus},[`&.${ar.selected}`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / ${t.vars.palette.action.selectedOpacity})`:(0,Me.X4)(t.palette.primary.main,t.palette.action.selectedOpacity),[`&.${ar.focusVisible}`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / calc(${t.vars.palette.action.selectedOpacity} + ${t.vars.palette.action.focusOpacity}))`:(0,Me.X4)(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.focusOpacity)}},[`&.${ar.disabled}`]:{opacity:(t.vars||t).palette.action.disabledOpacity}},"flex-start"===n.alignItems&&{alignItems:"flex-start"},n.divider&&{borderBottom:`1px solid ${(t.vars||t).palette.divider}`,backgroundClip:"padding-box"},n.button&&{transition:t.transitions.create("background-color",{duration:t.transitions.duration.shortest}),"&:hover":{textDecoration:"none",backgroundColor:(t.vars||t).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${ar.selected}:hover`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / calc(${t.vars.palette.action.selectedOpacity} + ${t.vars.palette.action.hoverOpacity}))`:(0,Me.X4)(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / ${t.vars.palette.action.selectedOpacity})`:(0,Me.X4)(t.palette.primary.main,t.palette.action.selectedOpacity)}}},n.hasSecondaryAction&&{paddingRight:48})}),gr=pt("li",{name:"MuiListItem",slot:"Container",overridesResolver:(e,t)=>t.container})({position:"relative"}),vr=e.forwardRef(function(t,n){const r=Ot({props:t,name:"MuiListItem"}),{alignItems:o="center",autoFocus:a=!1,button:i=!1,children:l,className:s,component:u,components:c={},componentsProps:d={},ContainerComponent:f="li",ContainerProps:{className:p}={},dense:h=!1,disabled:m=!1,disableGutters:g=!1,disablePadding:v=!1,divider:y=!1,focusVisibleClassName:b,secondaryAction:w,selected:x=!1,slotProps:k={},slots:E={}}=r,A=(0,Pe.A)(r.ContainerProps,pr),C=(0,Pe.A)(r,hr),P=e.useContext(en),R=e.useMemo(()=>({dense:h||P.dense||!1,alignItems:o,disableGutters:g}),[o,P.dense,h,g]),O=e.useRef(null);rr(()=>{a&&O.current&&O.current.focus()},[a]);const T=e.Children.toArray(l),N=T.length&&nr(T[T.length-1],["ListItemSecondaryAction"]),j=(0,Ce.A)({},r,{alignItems:o,autoFocus:a,button:i,dense:R.dense,disabled:m,disableGutters:g,disablePadding:v,divider:y,hasSecondaryAction:N,selected:x}),M=(e=>{const{alignItems:t,button:n,classes:r,dense:o,disabled:a,disableGutters:i,disablePadding:l,divider:s,hasSecondaryAction:u,selected:c}=e;return Ct({root:["root",o&&"dense",!i&&"gutters",!l&&"padding",s&&"divider",a&&"disabled",n&&"button","flex-start"===t&&"alignItemsFlexStart",u&&"secondaryAction",c&&"selected"],container:["container"]},or,r)})(j),_=un(O,n),L=E.root||c.Root||mr,z=k.root||d.root||{},I=(0,Ce.A)({className:(0,At.A)(M.root,z.className,s),disabled:m},C);let D=u||"li";return i&&(I.component=u||"div",I.focusVisibleClassName=(0,At.A)(ar.focusVisible,b),D=tr),N?(D=I.component||u?D:"div","li"===f&&("li"===D?D="div":"li"===I.component&&(I.component="div")),(0,S.jsx)(en.Provider,{value:R,children:(0,S.jsxs)(gr,(0,Ce.A)({as:f,className:(0,At.A)(M.container,p),ref:_,ownerState:j},A,{children:[(0,S.jsx)(L,(0,Ce.A)({},z,!an(L)&&{as:D,ownerState:(0,Ce.A)({},j,z.ownerState)},I,{children:T})),T.pop()]}))})):(0,S.jsx)(en.Provider,{value:R,children:(0,S.jsxs)(L,(0,Ce.A)({},z,{as:D,ref:_},!an(L)&&{ownerState:(0,Ce.A)({},j,z.ownerState)},I,{children:[T,w&&(0,S.jsx)(fr,{children:w})]}))})}),yr=vr,br=["alignItems","autoFocus","component","children","dense","disableGutters","divider","focusVisibleClassName","selected","className"],wr=pt(tr,{shouldForwardProp:e=>ft(e)||"classes"===e,name:"MuiListItemButton",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,n.dense&&t.dense,"flex-start"===n.alignItems&&t.alignItemsFlexStart,n.divider&&t.divider,!n.disableGutters&&t.gutters]}})(e=>{let{theme:t,ownerState:n}=e;return(0,Ce.A)({display:"flex",flexGrow:1,justifyContent:"flex-start",alignItems:"center",position:"relative",textDecoration:"none",minWidth:0,boxSizing:"border-box",textAlign:"left",paddingTop:8,paddingBottom:8,transition:t.transitions.create("background-color",{duration:t.transitions.duration.shortest}),"&:hover":{textDecoration:"none",backgroundColor:(t.vars||t).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${lr.selected}`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / ${t.vars.palette.action.selectedOpacity})`:(0,Me.X4)(t.palette.primary.main,t.palette.action.selectedOpacity),[`&.${lr.focusVisible}`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / calc(${t.vars.palette.action.selectedOpacity} + ${t.vars.palette.action.focusOpacity}))`:(0,Me.X4)(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.focusOpacity)}},[`&.${lr.selected}:hover`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / calc(${t.vars.palette.action.selectedOpacity} + ${t.vars.palette.action.hoverOpacity}))`:(0,Me.X4)(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / ${t.vars.palette.action.selectedOpacity})`:(0,Me.X4)(t.palette.primary.main,t.palette.action.selectedOpacity)}},[`&.${lr.focusVisible}`]:{backgroundColor:(t.vars||t).palette.action.focus},[`&.${lr.disabled}`]:{opacity:(t.vars||t).palette.action.disabledOpacity}},n.divider&&{borderBottom:`1px solid ${(t.vars||t).palette.divider}`,backgroundClip:"padding-box"},"flex-start"===n.alignItems&&{alignItems:"flex-start"},!n.disableGutters&&{paddingLeft:16,paddingRight:16},n.dense&&{paddingTop:4,paddingBottom:4})}),xr=e.forwardRef(function(t,n){const r=Ot({props:t,name:"MuiListItemButton"}),{alignItems:o="center",autoFocus:a=!1,component:i="div",children:l,dense:s=!1,disableGutters:u=!1,divider:c=!1,focusVisibleClassName:d,selected:f=!1,className:p}=r,h=(0,Pe.A)(r,br),m=e.useContext(en),g=e.useMemo(()=>({dense:s||m.dense||!1,alignItems:o,disableGutters:u}),[o,m.dense,s,u]),v=e.useRef(null);rr(()=>{a&&v.current&&v.current.focus()},[a]);const y=(0,Ce.A)({},r,{alignItems:o,dense:g.dense,disableGutters:u,divider:c,selected:f}),b=(e=>{const{alignItems:t,classes:n,dense:r,disabled:o,disableGutters:a,divider:i,selected:l}=e,s=Ct({root:["root",r&&"dense",!a&&"gutters",i&&"divider",o&&"disabled","flex-start"===t&&"alignItemsFlexStart",l&&"selected"]},ir,n);return(0,Ce.A)({},n,s)})(y),w=un(v,n);return(0,S.jsx)(en.Provider,{value:g,children:(0,S.jsx)(wr,(0,Ce.A)({ref:w,href:h.href||h.to,component:(h.href||h.to)&&"div"===i?"button":i,focusVisibleClassName:(0,At.A)(b.focusVisible,d),ownerState:y,className:(0,At.A)(b.root,p)},h,{classes:b,children:l}))})});function Sr(e){return Mt("MuiListItemIcon",e)}const kr=_t("MuiListItemIcon",["root","alignItemsFlexStart"]),Er=["className"],Ar=pt("div",{name:"MuiListItemIcon",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,"flex-start"===n.alignItems&&t.alignItemsFlexStart]}})(e=>{let{theme:t,ownerState:n}=e;return(0,Ce.A)({minWidth:56,color:(t.vars||t).palette.action.active,flexShrink:0,display:"inline-flex"},"flex-start"===n.alignItems&&{marginTop:8})}),Cr=e.forwardRef(function(t,n){const r=Ot({props:t,name:"MuiListItemIcon"}),{className:o}=r,a=(0,Pe.A)(r,Er),i=e.useContext(en),l=(0,Ce.A)({},r,{alignItems:i.alignItems}),s=(e=>{const{alignItems:t,classes:n}=e;return Ct({root:["root","flex-start"===t&&"alignItemsFlexStart"]},Sr,n)})(l);return(0,S.jsx)(Ar,(0,Ce.A)({className:(0,At.A)(s.root,o),ownerState:l,ref:n},a))});function Pr(e){return Mt("MuiListItemText",e)}const Rr=_t("MuiListItemText",["root","multiline","dense","inset","primary","secondary"]),Or=["children","className","disableTypography","inset","primary","primaryTypographyProps","secondary","secondaryTypographyProps"],Tr=pt("div",{name:"MuiListItemText",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[{[`& .${Rr.primary}`]:t.primary},{[`& .${Rr.secondary}`]:t.secondary},t.root,n.inset&&t.inset,n.primary&&n.secondary&&t.multiline,n.dense&&t.dense]}})(e=>{let{ownerState:t}=e;return(0,Ce.A)({flex:"1 1 auto",minWidth:0,marginTop:4,marginBottom:4},t.primary&&t.secondary&&{marginTop:6,marginBottom:6},t.inset&&{paddingLeft:56})}),Nr=e.forwardRef(function(t,n){const r=Ot({props:t,name:"MuiListItemText"}),{children:o,className:a,disableTypography:i=!1,inset:l=!1,primary:s,primaryTypographyProps:u,secondary:c,secondaryTypographyProps:d}=r,f=(0,Pe.A)(r,Or),{dense:p}=e.useContext(en);let h=null!=s?s:o,m=c;const g=(0,Ce.A)({},r,{disableTypography:i,inset:l,primary:!!h,secondary:!!m,dense:p}),v=(e=>{const{classes:t,inset:n,primary:r,secondary:o,dense:a}=e;return Ct({root:["root",n&&"inset",a&&"dense",r&&o&&"multiline"],primary:["primary"],secondary:["secondary"]},Pr,t)})(g);return null==h||h.type===qt||i||(h=(0,S.jsx)(qt,(0,Ce.A)({variant:p?"body2":"body1",className:v.primary,component:null!=u&&u.variant?void 0:"span",display:"block"},u,{children:h}))),null==m||m.type===qt||i||(m=(0,S.jsx)(qt,(0,Ce.A)({variant:"body2",className:v.secondary,color:"text.secondary",display:"block"},d,{children:m}))),(0,S.jsxs)(Tr,(0,Ce.A)({className:(0,At.A)(v.root,a),ownerState:g,ref:n},f,{children:[h,m]}))});var jr=n(3174);const Mr=["className","component"];const _r=_t("MuiBox",["root"]),Lr=st(),zr=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{themeId:n,defaultTheme:r,defaultClassName:o="MuiBox-root",generateClassName:a}=t,i=(0,jr.default)("div",{shouldForwardProp:e=>"theme"!==e&&"sx"!==e&&"as"!==e})(Ne.A);return e.forwardRef(function(e,t){const l=vt(r),s=(0,$t.A)(e),{className:u,component:c="div"}=s,d=(0,Pe.A)(s,Mr);return(0,S.jsx)(i,(0,Ce.A)({as:c,ref:t,className:(0,At.A)(u,a?a(o):o),theme:n&&l[n]||l},d))})}({themeId:ct,defaultTheme:Lr,defaultClassName:_r.root,generateClassName:Nt.generate}),Ir=zr;var Dr=n(869);function $r(e){const t=(0,jr.internal_serializeStyles)(e);return e!==t&&t.styles?(t.styles.match(/^@layer\s+[^{]*$/)||(t.styles=`@layer global{${t.styles}}`),t):e}const Fr=function(e){let{styles:t,themeId:n,defaultTheme:r={}}=e;const o=vt(r),a=n&&o[n]||o;let i="function"===typeof t?t(a):t;return a.modularCssLayers&&(i=Array.isArray(i)?i.map(e=>$r("function"===typeof e?e(a):e)):$r(i)),(0,S.jsx)(Dr.A,{styles:i})};const Br=function(e){return(0,S.jsx)(Fr,(0,Ce.A)({},e,{defaultTheme:ut,themeId:ct}))},Ur=(e,t)=>(0,Ce.A)({WebkitFontSmoothing:"antialiased",MozOsxFontSmoothing:"grayscale",boxSizing:"border-box",WebkitTextSizeAdjust:"100%"},t&&!e.vars&&{colorScheme:e.palette.mode}),Wr=e=>(0,Ce.A)({color:(e.vars||e).palette.text.primary},e.typography.body1,{backgroundColor:(e.vars||e).palette.background.default,"@media print":{backgroundColor:(e.vars||e).palette.common.white}});const Vr=function(t){const n=Ot({props:t,name:"MuiCssBaseline"}),{children:r,enableColorScheme:o=!1}=n;return(0,S.jsxs)(e.Fragment,{children:[(0,S.jsx)(Br,{styles:e=>function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];var n;const r={};t&&e.colorSchemes&&Object.entries(e.colorSchemes).forEach(t=>{let[n,o]=t;var a;r[e.getColorSchemeSelector(n).replace(/\s*&/,"")]={colorScheme:null==(a=o.palette)?void 0:a.mode}});let o=(0,Ce.A)({html:Ur(e,t),"*, *::before, *::after":{boxSizing:"inherit"},"strong, b":{fontWeight:e.typography.fontWeightBold},body:(0,Ce.A)({margin:0},Wr(e),{"&::backdrop":{backgroundColor:(e.vars||e).palette.background.default}})},r);const a=null==(n=e.components)||null==(n=n.MuiCssBaseline)?void 0:n.styleOverrides;return a&&(o=[o,a]),o}(e,o)}),r]})},Hr=e=>{let t;return t=e<1?5.11916*e**2:4.5*Math.log(e+1)+2,(t/100).toFixed(2)};function qr(e){return Mt("MuiPaper",e)}_t("MuiPaper",["root","rounded","outlined","elevation","elevation0","elevation1","elevation2","elevation3","elevation4","elevation5","elevation6","elevation7","elevation8","elevation9","elevation10","elevation11","elevation12","elevation13","elevation14","elevation15","elevation16","elevation17","elevation18","elevation19","elevation20","elevation21","elevation22","elevation23","elevation24"]);const Kr=["className","component","elevation","square","variant"],Gr=pt("div",{name:"MuiPaper",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,t[n.variant],!n.square&&t.rounded,"elevation"===n.variant&&t[`elevation${n.elevation}`]]}})(e=>{let{theme:t,ownerState:n}=e;var r;return(0,Ce.A)({backgroundColor:(t.vars||t).palette.background.paper,color:(t.vars||t).palette.text.primary,transition:t.transitions.create("box-shadow")},!n.square&&{borderRadius:t.shape.borderRadius},"outlined"===n.variant&&{border:`1px solid ${(t.vars||t).palette.divider}`},"elevation"===n.variant&&(0,Ce.A)({boxShadow:(t.vars||t).shadows[n.elevation]},!t.vars&&"dark"===t.palette.mode&&{backgroundImage:`linear-gradient(${(0,Me.X4)("#fff",Hr(n.elevation))}, ${(0,Me.X4)("#fff",Hr(n.elevation))})`},t.vars&&{backgroundImage:null==(r=t.vars.overlays)?void 0:r[n.elevation]}))}),Xr=e.forwardRef(function(e,t){const n=Ot({props:e,name:"MuiPaper"}),{className:r,component:o="div",elevation:a=1,square:i=!1,variant:l="elevation"}=n,s=(0,Pe.A)(n,Kr),u=(0,Ce.A)({},n,{component:o,elevation:a,square:i,variant:l}),c=(e=>{const{square:t,elevation:n,variant:r,classes:o}=e;return Ct({root:["root",r,!t&&"rounded","elevation"===r&&`elevation${n}`]},qr,o)})(u);return(0,S.jsx)(Gr,(0,Ce.A)({as:o,ownerState:u,className:(0,At.A)(c.root,r),ref:t},s))});function Qr(e){return Mt("MuiAppBar",e)}_t("MuiAppBar",["root","positionFixed","positionAbsolute","positionSticky","positionStatic","positionRelative","colorDefault","colorPrimary","colorSecondary","colorInherit","colorTransparent","colorError","colorInfo","colorSuccess","colorWarning"]);const Yr=["className","color","enableColorOnDark","position"],Jr=(e,t)=>e?`${null==e?void 0:e.replace(")","")}, ${t})`:t,Zr=pt(Xr,{name:"MuiAppBar",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,t[`position${Ft(n.position)}`],t[`color${Ft(n.color)}`]]}})(e=>{let{theme:t,ownerState:n}=e;const r="light"===t.palette.mode?t.palette.grey[100]:t.palette.grey[900];return(0,Ce.A)({display:"flex",flexDirection:"column",width:"100%",boxSizing:"border-box",flexShrink:0},"fixed"===n.position&&{position:"fixed",zIndex:(t.vars||t).zIndex.appBar,top:0,left:"auto",right:0,"@media print":{position:"absolute"}},"absolute"===n.position&&{position:"absolute",zIndex:(t.vars||t).zIndex.appBar,top:0,left:"auto",right:0},"sticky"===n.position&&{position:"sticky",zIndex:(t.vars||t).zIndex.appBar,top:0,left:"auto",right:0},"static"===n.position&&{position:"static"},"relative"===n.position&&{position:"relative"},!t.vars&&(0,Ce.A)({},"default"===n.color&&{backgroundColor:r,color:t.palette.getContrastText(r)},n.color&&"default"!==n.color&&"inherit"!==n.color&&"transparent"!==n.color&&{backgroundColor:t.palette[n.color].main,color:t.palette[n.color].contrastText},"inherit"===n.color&&{color:"inherit"},"dark"===t.palette.mode&&!n.enableColorOnDark&&{backgroundColor:null,color:null},"transparent"===n.color&&(0,Ce.A)({backgroundColor:"transparent",color:"inherit"},"dark"===t.palette.mode&&{backgroundImage:"none"})),t.vars&&(0,Ce.A)({},"default"===n.color&&{"--AppBar-background":n.enableColorOnDark?t.vars.palette.AppBar.defaultBg:Jr(t.vars.palette.AppBar.darkBg,t.vars.palette.AppBar.defaultBg),"--AppBar-color":n.enableColorOnDark?t.vars.palette.text.primary:Jr(t.vars.palette.AppBar.darkColor,t.vars.palette.text.primary)},n.color&&!n.color.match(/^(default|inherit|transparent)$/)&&{"--AppBar-background":n.enableColorOnDark?t.vars.palette[n.color].main:Jr(t.vars.palette.AppBar.darkBg,t.vars.palette[n.color].main),"--AppBar-color":n.enableColorOnDark?t.vars.palette[n.color].contrastText:Jr(t.vars.palette.AppBar.darkColor,t.vars.palette[n.color].contrastText)},!["inherit","transparent"].includes(n.color)&&{backgroundColor:"var(--AppBar-background)"},{color:"inherit"===n.color?"inherit":"var(--AppBar-color)"},"transparent"===n.color&&{backgroundImage:"none",backgroundColor:"transparent",color:"inherit"}))}),eo=e.forwardRef(function(e,t){const n=Ot({props:e,name:"MuiAppBar"}),{className:r,color:o="primary",enableColorOnDark:a=!1,position:i="fixed"}=n,l=(0,Pe.A)(n,Yr),s=(0,Ce.A)({},n,{color:o,position:i,enableColorOnDark:a}),u=(e=>{const{color:t,position:n,classes:r}=e;return Ct({root:["root",`color${Ft(t)}`,`position${Ft(n)}`]},Qr,r)})(s);return(0,S.jsx)(Zr,(0,Ce.A)({square:!0,component:"header",ownerState:s,elevation:4,className:(0,At.A)(u.root,r,"fixed"===i&&"mui-fixed"),ref:t},l))});function to(e){return Mt("MuiIconButton",e)}const no=_t("MuiIconButton",["root","disabled","colorInherit","colorPrimary","colorSecondary","colorError","colorInfo","colorSuccess","colorWarning","edgeStart","edgeEnd","sizeSmall","sizeMedium","sizeLarge"]),ro=["edge","children","className","color","disabled","disableFocusRipple","size"],oo=pt(tr,{name:"MuiIconButton",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,"default"!==n.color&&t[`color${Ft(n.color)}`],n.edge&&t[`edge${Ft(n.edge)}`],t[`size${Ft(n.size)}`]]}})(e=>{let{theme:t,ownerState:n}=e;return(0,Ce.A)({textAlign:"center",flex:"0 0 auto",fontSize:t.typography.pxToRem(24),padding:8,borderRadius:"50%",overflow:"visible",color:(t.vars||t).palette.action.active,transition:t.transitions.create("background-color",{duration:t.transitions.duration.shortest})},!n.disableRipple&&{"&:hover":{backgroundColor:t.vars?`rgba(${t.vars.palette.action.activeChannel} / ${t.vars.palette.action.hoverOpacity})`:(0,Me.X4)(t.palette.action.active,t.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}}},"start"===n.edge&&{marginLeft:"small"===n.size?-3:-12},"end"===n.edge&&{marginRight:"small"===n.size?-3:-12})},e=>{let{theme:t,ownerState:n}=e;var r;const o=null==(r=(t.vars||t).palette)?void 0:r[n.color];return(0,Ce.A)({},"inherit"===n.color&&{color:"inherit"},"inherit"!==n.color&&"default"!==n.color&&(0,Ce.A)({color:null==o?void 0:o.main},!n.disableRipple&&{"&:hover":(0,Ce.A)({},o&&{backgroundColor:t.vars?`rgba(${o.mainChannel} / ${t.vars.palette.action.hoverOpacity})`:(0,Me.X4)(o.main,t.palette.action.hoverOpacity)},{"@media (hover: none)":{backgroundColor:"transparent"}})}),"small"===n.size&&{padding:5,fontSize:t.typography.pxToRem(18)},"large"===n.size&&{padding:12,fontSize:t.typography.pxToRem(28)},{[`&.${no.disabled}`]:{backgroundColor:"transparent",color:(t.vars||t).palette.action.disabled}})}),ao=e.forwardRef(function(e,t){const n=Ot({props:e,name:"MuiIconButton"}),{edge:r=!1,children:o,className:a,color:i="default",disabled:l=!1,disableFocusRipple:s=!1,size:u="medium"}=n,c=(0,Pe.A)(n,ro),d=(0,Ce.A)({},n,{edge:r,color:i,disabled:l,disableFocusRipple:s,size:u}),f=(e=>{const{classes:t,disabled:n,color:r,edge:o,size:a}=e;return Ct({root:["root",n&&"disabled","default"!==r&&`color${Ft(r)}`,o&&`edge${Ft(o)}`,`size${Ft(a)}`]},to,t)})(d);return(0,S.jsx)(oo,(0,Ce.A)({className:(0,At.A)(f.root,a),centerRipple:!0,focusRipple:!s,disabled:l,ref:t},c,{ownerState:d,children:o}))}),io=e.createContext();const lo=()=>{const t=e.useContext(io);return null!=t&&t};const so=function(e,t,n){return void 0===e||an(e)?t:(0,Ce.A)({},t,{ownerState:(0,Ce.A)({},t.ownerState,n)})};function uo(t){var n;return parseInt(e.version,10)>=19?(null==t||null==(n=t.props)?void 0:n.ref)||null:(null==t?void 0:t.ref)||null}const co=!1;var fo="unmounted",po="exited",ho="entering",mo="entered",go="exiting",vo=function(t){function n(e,n){var r;r=t.call(this,e,n)||this;var o,a=n&&!n.isMounting?e.enter:e.appear;return r.appearStatus=null,e.in?a?(o=po,r.appearStatus=ho):o=mo:o=e.unmountOnExit||e.mountOnEnter?fo:po,r.state={status:o},r.nextCallback=null,r}Cn(n,t),n.getDerivedStateFromProps=function(e,t){return e.in&&t.status===fo?{status:po}:null};var r=n.prototype;return r.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},r.componentDidUpdate=function(e){var t=null;if(e!==this.props){var n=this.state.status;this.props.in?n!==ho&&n!==mo&&(t=ho):n!==ho&&n!==mo||(t=go)}this.updateStatus(!1,t)},r.componentWillUnmount=function(){this.cancelNextCallback()},r.getTimeouts=function(){var e,t,n,r=this.props.timeout;return e=t=n=r,null!=r&&"number"!==typeof r&&(e=r.exit,t=r.enter,n=void 0!==r.appear?r.appear:t),{exit:e,enter:t,appear:n}},r.updateStatus=function(e,t){if(void 0===e&&(e=!1),null!==t)if(this.cancelNextCallback(),t===ho){if(this.props.unmountOnExit||this.props.mountOnEnter){var n=this.props.nodeRef?this.props.nodeRef.current:c.findDOMNode(this);n&&function(e){e.scrollTop}(n)}this.performEnter(e)}else this.performExit();else this.props.unmountOnExit&&this.state.status===po&&this.setState({status:fo})},r.performEnter=function(e){var t=this,n=this.props.enter,r=this.context?this.context.isMounting:e,o=this.props.nodeRef?[r]:[c.findDOMNode(this),r],a=o[0],i=o[1],l=this.getTimeouts(),s=r?l.appear:l.enter;!e&&!n||co?this.safeSetState({status:mo},function(){t.props.onEntered(a)}):(this.props.onEnter(a,i),this.safeSetState({status:ho},function(){t.props.onEntering(a,i),t.onTransitionEnd(s,function(){t.safeSetState({status:mo},function(){t.props.onEntered(a,i)})})}))},r.performExit=function(){var e=this,t=this.props.exit,n=this.getTimeouts(),r=this.props.nodeRef?void 0:c.findDOMNode(this);t&&!co?(this.props.onExit(r),this.safeSetState({status:go},function(){e.props.onExiting(r),e.onTransitionEnd(n.exit,function(){e.safeSetState({status:po},function(){e.props.onExited(r)})})})):this.safeSetState({status:po},function(){e.props.onExited(r)})},r.cancelNextCallback=function(){null!==this.nextCallback&&(this.nextCallback.cancel(),this.nextCallback=null)},r.safeSetState=function(e,t){t=this.setNextCallback(t),this.setState(e,t)},r.setNextCallback=function(e){var t=this,n=!0;return this.nextCallback=function(r){n&&(n=!1,t.nextCallback=null,e(r))},this.nextCallback.cancel=function(){n=!1},this.nextCallback},r.onTransitionEnd=function(e,t){this.setNextCallback(t);var n=this.props.nodeRef?this.props.nodeRef.current:c.findDOMNode(this),r=null==e&&!this.props.addEndListener;if(n&&!r){if(this.props.addEndListener){var o=this.props.nodeRef?[this.nextCallback]:[n,this.nextCallback],a=o[0],i=o[1];this.props.addEndListener(a,i)}null!=e&&setTimeout(this.nextCallback,e)}else setTimeout(this.nextCallback,0)},r.render=function(){var t=this.state.status;if(t===fo)return null;var n=this.props,r=n.children,o=(n.in,n.mountOnEnter,n.unmountOnExit,n.appear,n.enter,n.exit,n.timeout,n.addEndListener,n.onEnter,n.onEntering,n.onEntered,n.onExit,n.onExiting,n.onExited,n.nodeRef,(0,Pe.A)(n,["children","in","mountOnEnter","unmountOnExit","appear","enter","exit","timeout","addEndListener","onEnter","onEntering","onEntered","onExit","onExiting","onExited","nodeRef"]));return e.createElement(Pn.Provider,{value:null},"function"===typeof r?r(t,o):e.cloneElement(e.Children.only(r),o))},n}(e.Component);function yo(){}vo.contextType=Pn,vo.propTypes={},vo.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:yo,onEntering:yo,onEntered:yo,onExit:yo,onExiting:yo,onExited:yo},vo.UNMOUNTED=fo,vo.EXITED=po,vo.ENTERING=ho,vo.ENTERED=mo,vo.EXITING=go;const bo=vo,wo=e=>e.scrollTop;function xo(e,t){var n,r;const{timeout:o,easing:a,style:i={}}=e;return{duration:null!=(n=i.transitionDuration)?n:"number"===typeof o?o:o[t.mode]||0,easing:null!=(r=i.transitionTimingFunction)?r:"object"===typeof a?a[t.mode]:a,delay:i.transitionDelay}}const So=["addEndListener","appear","children","easing","in","onEnter","onEntered","onEntering","onExit","onExited","onExiting","style","timeout","TransitionComponent"];function ko(e){return`scale(${e}, ${e**2})`}const Eo={entering:{opacity:1,transform:ko(1)},entered:{opacity:1,transform:"none"}},Ao="undefined"!==typeof navigator&&/^((?!chrome|android).)*(safari|mobile)/i.test(navigator.userAgent)&&/(os |version\/)15(.|_)4/i.test(navigator.userAgent),Co=e.forwardRef(function(t,n){const{addEndListener:r,appear:o=!0,children:a,easing:i,in:l,onEnter:s,onEntered:u,onEntering:c,onExit:d,onExited:f,onExiting:p,style:h,timeout:m="auto",TransitionComponent:g=bo}=t,v=(0,Pe.A)(t,So),y=mn(),b=e.useRef(),w=yt(),x=e.useRef(null),k=un(x,uo(a),n),E=e=>t=>{if(e){const n=x.current;void 0===t?e(n):e(n,t)}},A=E(c),C=E((e,t)=>{wo(e);const{duration:n,delay:r,easing:o}=xo({style:h,timeout:m,easing:i},{mode:"enter"});let a;"auto"===m?(a=w.transitions.getAutoHeightDuration(e.clientHeight),b.current=a):a=n,e.style.transition=[w.transitions.create("opacity",{duration:a,delay:r}),w.transitions.create("transform",{duration:Ao?a:.666*a,delay:r,easing:o})].join(","),s&&s(e,t)}),P=E(u),R=E(p),O=E(e=>{const{duration:t,delay:n,easing:r}=xo({style:h,timeout:m,easing:i},{mode:"exit"});let o;"auto"===m?(o=w.transitions.getAutoHeightDuration(e.clientHeight),b.current=o):o=t,e.style.transition=[w.transitions.create("opacity",{duration:o,delay:n}),w.transitions.create("transform",{duration:Ao?o:.666*o,delay:Ao?n:n||.333*o,easing:r})].join(","),e.style.opacity=0,e.style.transform=ko(.75),d&&d(e)}),T=E(f);return(0,S.jsx)(g,(0,Ce.A)({appear:o,in:l,nodeRef:x,onEnter:C,onEntered:P,onEntering:A,onExit:O,onExited:T,onExiting:R,addEndListener:e=>{"auto"===m&&y.start(b.current||0,e),r&&r(x.current,e)},timeout:"auto"===m?null:m},v,{children:(t,n)=>e.cloneElement(a,(0,Ce.A)({style:(0,Ce.A)({opacity:0,transform:ko(.75),visibility:"exited"!==t||l?void 0:"hidden"},Eo[t],h,a.props.style),ref:k},n))}))});Co.muiSupportAuto=!0;const Po=Co;var Ro=n(2374);function Oo(e){return e&&e.ownerDocument||document}function To(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function No(e){return e instanceof To(e).Element||e instanceof Element}function jo(e){return e instanceof To(e).HTMLElement||e instanceof HTMLElement}function Mo(e){return"undefined"!==typeof ShadowRoot&&(e instanceof To(e).ShadowRoot||e instanceof ShadowRoot)}var _o=Math.max,Lo=Math.min,zo=Math.round;function Io(){var e=navigator.userAgentData;return null!=e&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(e){return e.brand+"/"+e.version}).join(" "):navigator.userAgent}function Do(){return!/^((?!chrome|android).)*safari/i.test(Io())}function $o(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);var r=e.getBoundingClientRect(),o=1,a=1;t&&jo(e)&&(o=e.offsetWidth>0&&zo(r.width)/e.offsetWidth||1,a=e.offsetHeight>0&&zo(r.height)/e.offsetHeight||1);var i=(No(e)?To(e):window).visualViewport,l=!Do()&&n,s=(r.left+(l&&i?i.offsetLeft:0))/o,u=(r.top+(l&&i?i.offsetTop:0))/a,c=r.width/o,d=r.height/a;return{width:c,height:d,top:u,right:s+c,bottom:u+d,left:s,x:s,y:u}}function Fo(e){var t=To(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function Bo(e){return e?(e.nodeName||"").toLowerCase():null}function Uo(e){return((No(e)?e.ownerDocument:e.document)||window.document).documentElement}function Wo(e){return $o(Uo(e)).left+Fo(e).scrollLeft}function Vo(e){return To(e).getComputedStyle(e)}function Ho(e){var t=Vo(e),n=t.overflow,r=t.overflowX,o=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+o+r)}function qo(e,t,n){void 0===n&&(n=!1);var r=jo(t),o=jo(t)&&function(e){var t=e.getBoundingClientRect(),n=zo(t.width)/e.offsetWidth||1,r=zo(t.height)/e.offsetHeight||1;return 1!==n||1!==r}(t),a=Uo(t),i=$o(e,o,n),l={scrollLeft:0,scrollTop:0},s={x:0,y:0};return(r||!r&&!n)&&(("body"!==Bo(t)||Ho(a))&&(l=function(e){return e!==To(e)&&jo(e)?{scrollLeft:(t=e).scrollLeft,scrollTop:t.scrollTop}:Fo(e);var t}(t)),jo(t)?((s=$o(t,!0)).x+=t.clientLeft,s.y+=t.clientTop):a&&(s.x=Wo(a))),{x:i.left+l.scrollLeft-s.x,y:i.top+l.scrollTop-s.y,width:i.width,height:i.height}}function Ko(e){var t=$o(e),n=e.offsetWidth,r=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-r)<=1&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:r}}function Go(e){return"html"===Bo(e)?e:e.assignedSlot||e.parentNode||(Mo(e)?e.host:null)||Uo(e)}function Xo(e){return["html","body","#document"].indexOf(Bo(e))>=0?e.ownerDocument.body:jo(e)&&Ho(e)?e:Xo(Go(e))}function Qo(e,t){var n;void 0===t&&(t=[]);var r=Xo(e),o=r===(null==(n=e.ownerDocument)?void 0:n.body),a=To(r),i=o?[a].concat(a.visualViewport||[],Ho(r)?r:[]):r,l=t.concat(i);return o?l:l.concat(Qo(Go(i)))}function Yo(e){return["table","td","th"].indexOf(Bo(e))>=0}function Jo(e){return jo(e)&&"fixed"!==Vo(e).position?e.offsetParent:null}function Zo(e){for(var t=To(e),n=Jo(e);n&&Yo(n)&&"static"===Vo(n).position;)n=Jo(n);return n&&("html"===Bo(n)||"body"===Bo(n)&&"static"===Vo(n).position)?t:n||function(e){var t=/firefox/i.test(Io());if(/Trident/i.test(Io())&&jo(e)&&"fixed"===Vo(e).position)return null;var n=Go(e);for(Mo(n)&&(n=n.host);jo(n)&&["html","body"].indexOf(Bo(n))<0;){var r=Vo(n);if("none"!==r.transform||"none"!==r.perspective||"paint"===r.contain||-1!==["transform","perspective"].indexOf(r.willChange)||t&&"filter"===r.willChange||t&&r.filter&&"none"!==r.filter)return n;n=n.parentNode}return null}(e)||t}var ea="top",ta="bottom",na="right",ra="left",oa="auto",aa=[ea,ta,na,ra],ia="start",la="end",sa="viewport",ua="popper",ca=aa.reduce(function(e,t){return e.concat([t+"-"+ia,t+"-"+la])},[]),da=[].concat(aa,[oa]).reduce(function(e,t){return e.concat([t,t+"-"+ia,t+"-"+la])},[]),fa=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];function pa(e){var t=new Map,n=new Set,r=[];function o(e){n.add(e.name),[].concat(e.requires||[],e.requiresIfExists||[]).forEach(function(e){if(!n.has(e)){var r=t.get(e);r&&o(r)}}),r.push(e)}return e.forEach(function(e){t.set(e.name,e)}),e.forEach(function(e){n.has(e.name)||o(e)}),r}function ha(e){var t;return function(){return t||(t=new Promise(function(n){Promise.resolve().then(function(){t=void 0,n(e())})})),t}}var ma={placement:"bottom",modifiers:[],strategy:"absolute"};function ga(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some(function(e){return!(e&&"function"===typeof e.getBoundingClientRect)})}function va(e){void 0===e&&(e={});var t=e,n=t.defaultModifiers,r=void 0===n?[]:n,o=t.defaultOptions,a=void 0===o?ma:o;return function(e,t,n){void 0===n&&(n=a);var o={placement:"bottom",orderedModifiers:[],options:Object.assign({},ma,a),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},i=[],l=!1,s={state:o,setOptions:function(n){var l="function"===typeof n?n(o.options):n;u(),o.options=Object.assign({},a,o.options,l),o.scrollParents={reference:No(e)?Qo(e):e.contextElement?Qo(e.contextElement):[],popper:Qo(t)};var c=function(e){var t=pa(e);return fa.reduce(function(e,n){return e.concat(t.filter(function(e){return e.phase===n}))},[])}(function(e){var t=e.reduce(function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e},{});return Object.keys(t).map(function(e){return t[e]})}([].concat(r,o.options.modifiers)));return o.orderedModifiers=c.filter(function(e){return e.enabled}),o.orderedModifiers.forEach(function(e){var t=e.name,n=e.options,r=void 0===n?{}:n,a=e.effect;if("function"===typeof a){var l=a({state:o,name:t,instance:s,options:r}),u=function(){};i.push(l||u)}}),s.update()},forceUpdate:function(){if(!l){var e=o.elements,t=e.reference,n=e.popper;if(ga(t,n)){o.rects={reference:qo(t,Zo(n),"fixed"===o.options.strategy),popper:Ko(n)},o.reset=!1,o.placement=o.options.placement,o.orderedModifiers.forEach(function(e){return o.modifiersData[e.name]=Object.assign({},e.data)});for(var r=0;r<o.orderedModifiers.length;r++)if(!0!==o.reset){var a=o.orderedModifiers[r],i=a.fn,u=a.options,c=void 0===u?{}:u,d=a.name;"function"===typeof i&&(o=i({state:o,options:c,name:d,instance:s})||o)}else o.reset=!1,r=-1}}},update:ha(function(){return new Promise(function(e){s.forceUpdate(),e(o)})}),destroy:function(){u(),l=!0}};if(!ga(e,t))return s;function u(){i.forEach(function(e){return e()}),i=[]}return s.setOptions(n).then(function(e){!l&&n.onFirstUpdate&&n.onFirstUpdate(e)}),s}}var ya={passive:!0};function ba(e){return e.split("-")[0]}function wa(e){return e.split("-")[1]}function xa(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Sa(e){var t,n=e.reference,r=e.element,o=e.placement,a=o?ba(o):null,i=o?wa(o):null,l=n.x+n.width/2-r.width/2,s=n.y+n.height/2-r.height/2;switch(a){case ea:t={x:l,y:n.y-r.height};break;case ta:t={x:l,y:n.y+n.height};break;case na:t={x:n.x+n.width,y:s};break;case ra:t={x:n.x-r.width,y:s};break;default:t={x:n.x,y:n.y}}var u=a?xa(a):null;if(null!=u){var c="y"===u?"height":"width";switch(i){case ia:t[u]=t[u]-(n[c]/2-r[c]/2);break;case la:t[u]=t[u]+(n[c]/2-r[c]/2)}}return t}var ka={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Ea(e){var t,n=e.popper,r=e.popperRect,o=e.placement,a=e.variation,i=e.offsets,l=e.position,s=e.gpuAcceleration,u=e.adaptive,c=e.roundOffsets,d=e.isFixed,f=i.x,p=void 0===f?0:f,h=i.y,m=void 0===h?0:h,g="function"===typeof c?c({x:p,y:m}):{x:p,y:m};p=g.x,m=g.y;var v=i.hasOwnProperty("x"),y=i.hasOwnProperty("y"),b=ra,w=ea,x=window;if(u){var S=Zo(n),k="clientHeight",E="clientWidth";if(S===To(n)&&"static"!==Vo(S=Uo(n)).position&&"absolute"===l&&(k="scrollHeight",E="scrollWidth"),o===ea||(o===ra||o===na)&&a===la)w=ta,m-=(d&&S===x&&x.visualViewport?x.visualViewport.height:S[k])-r.height,m*=s?1:-1;if(o===ra||(o===ea||o===ta)&&a===la)b=na,p-=(d&&S===x&&x.visualViewport?x.visualViewport.width:S[E])-r.width,p*=s?1:-1}var A,C=Object.assign({position:l},u&&ka),P=!0===c?function(e,t){var n=e.x,r=e.y,o=t.devicePixelRatio||1;return{x:zo(n*o)/o||0,y:zo(r*o)/o||0}}({x:p,y:m},To(n)):{x:p,y:m};return p=P.x,m=P.y,s?Object.assign({},C,((A={})[w]=y?"0":"",A[b]=v?"0":"",A.transform=(x.devicePixelRatio||1)<=1?"translate("+p+"px, "+m+"px)":"translate3d("+p+"px, "+m+"px, 0)",A)):Object.assign({},C,((t={})[w]=y?m+"px":"",t[b]=v?p+"px":"",t.transform="",t))}const Aa={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,r=e.name,o=n.offset,a=void 0===o?[0,0]:o,i=da.reduce(function(e,n){return e[n]=function(e,t,n){var r=ba(e),o=[ra,ea].indexOf(r)>=0?-1:1,a="function"===typeof n?n(Object.assign({},t,{placement:e})):n,i=a[0],l=a[1];return i=i||0,l=(l||0)*o,[ra,na].indexOf(r)>=0?{x:l,y:i}:{x:i,y:l}}(n,t.rects,a),e},{}),l=i[t.placement],s=l.x,u=l.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=s,t.modifiersData.popperOffsets.y+=u),t.modifiersData[r]=i}};var Ca={left:"right",right:"left",bottom:"top",top:"bottom"};function Pa(e){return e.replace(/left|right|bottom|top/g,function(e){return Ca[e]})}var Ra={start:"end",end:"start"};function Oa(e){return e.replace(/start|end/g,function(e){return Ra[e]})}function Ta(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&Mo(n)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function Na(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function ja(e,t,n){return t===sa?Na(function(e,t){var n=To(e),r=Uo(e),o=n.visualViewport,a=r.clientWidth,i=r.clientHeight,l=0,s=0;if(o){a=o.width,i=o.height;var u=Do();(u||!u&&"fixed"===t)&&(l=o.offsetLeft,s=o.offsetTop)}return{width:a,height:i,x:l+Wo(e),y:s}}(e,n)):No(t)?function(e,t){var n=$o(e,!1,"fixed"===t);return n.top=n.top+e.clientTop,n.left=n.left+e.clientLeft,n.bottom=n.top+e.clientHeight,n.right=n.left+e.clientWidth,n.width=e.clientWidth,n.height=e.clientHeight,n.x=n.left,n.y=n.top,n}(t,n):Na(function(e){var t,n=Uo(e),r=Fo(e),o=null==(t=e.ownerDocument)?void 0:t.body,a=_o(n.scrollWidth,n.clientWidth,o?o.scrollWidth:0,o?o.clientWidth:0),i=_o(n.scrollHeight,n.clientHeight,o?o.scrollHeight:0,o?o.clientHeight:0),l=-r.scrollLeft+Wo(e),s=-r.scrollTop;return"rtl"===Vo(o||n).direction&&(l+=_o(n.clientWidth,o?o.clientWidth:0)-a),{width:a,height:i,x:l,y:s}}(Uo(e)))}function Ma(e,t,n,r){var o="clippingParents"===t?function(e){var t=Qo(Go(e)),n=["absolute","fixed"].indexOf(Vo(e).position)>=0&&jo(e)?Zo(e):e;return No(n)?t.filter(function(e){return No(e)&&Ta(e,n)&&"body"!==Bo(e)}):[]}(e):[].concat(t),a=[].concat(o,[n]),i=a[0],l=a.reduce(function(t,n){var o=ja(e,n,r);return t.top=_o(o.top,t.top),t.right=Lo(o.right,t.right),t.bottom=Lo(o.bottom,t.bottom),t.left=_o(o.left,t.left),t},ja(e,i,r));return l.width=l.right-l.left,l.height=l.bottom-l.top,l.x=l.left,l.y=l.top,l}function _a(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}function La(e,t){return t.reduce(function(t,n){return t[n]=e,t},{})}function za(e,t){void 0===t&&(t={});var n=t,r=n.placement,o=void 0===r?e.placement:r,a=n.strategy,i=void 0===a?e.strategy:a,l=n.boundary,s=void 0===l?"clippingParents":l,u=n.rootBoundary,c=void 0===u?sa:u,d=n.elementContext,f=void 0===d?ua:d,p=n.altBoundary,h=void 0!==p&&p,m=n.padding,g=void 0===m?0:m,v=_a("number"!==typeof g?g:La(g,aa)),y=f===ua?"reference":ua,b=e.rects.popper,w=e.elements[h?y:f],x=Ma(No(w)?w:w.contextElement||Uo(e.elements.popper),s,c,i),S=$o(e.elements.reference),k=Sa({reference:S,element:b,strategy:"absolute",placement:o}),E=Na(Object.assign({},b,k)),A=f===ua?E:S,C={top:x.top-A.top+v.top,bottom:A.bottom-x.bottom+v.bottom,left:x.left-A.left+v.left,right:A.right-x.right+v.right},P=e.modifiersData.offset;if(f===ua&&P){var R=P[o];Object.keys(C).forEach(function(e){var t=[na,ta].indexOf(e)>=0?1:-1,n=[ea,ta].indexOf(e)>=0?"y":"x";C[e]+=R[n]*t})}return C}const Ia={name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name;if(!t.modifiersData[r]._skip){for(var o=n.mainAxis,a=void 0===o||o,i=n.altAxis,l=void 0===i||i,s=n.fallbackPlacements,u=n.padding,c=n.boundary,d=n.rootBoundary,f=n.altBoundary,p=n.flipVariations,h=void 0===p||p,m=n.allowedAutoPlacements,g=t.options.placement,v=ba(g),y=s||(v===g||!h?[Pa(g)]:function(e){if(ba(e)===oa)return[];var t=Pa(e);return[Oa(e),t,Oa(t)]}(g)),b=[g].concat(y).reduce(function(e,n){return e.concat(ba(n)===oa?function(e,t){void 0===t&&(t={});var n=t,r=n.placement,o=n.boundary,a=n.rootBoundary,i=n.padding,l=n.flipVariations,s=n.allowedAutoPlacements,u=void 0===s?da:s,c=wa(r),d=c?l?ca:ca.filter(function(e){return wa(e)===c}):aa,f=d.filter(function(e){return u.indexOf(e)>=0});0===f.length&&(f=d);var p=f.reduce(function(t,n){return t[n]=za(e,{placement:n,boundary:o,rootBoundary:a,padding:i})[ba(n)],t},{});return Object.keys(p).sort(function(e,t){return p[e]-p[t]})}(t,{placement:n,boundary:c,rootBoundary:d,padding:u,flipVariations:h,allowedAutoPlacements:m}):n)},[]),w=t.rects.reference,x=t.rects.popper,S=new Map,k=!0,E=b[0],A=0;A<b.length;A++){var C=b[A],P=ba(C),R=wa(C)===ia,O=[ea,ta].indexOf(P)>=0,T=O?"width":"height",N=za(t,{placement:C,boundary:c,rootBoundary:d,altBoundary:f,padding:u}),j=O?R?na:ra:R?ta:ea;w[T]>x[T]&&(j=Pa(j));var M=Pa(j),_=[];if(a&&_.push(N[P]<=0),l&&_.push(N[j]<=0,N[M]<=0),_.every(function(e){return e})){E=C,k=!1;break}S.set(C,_)}if(k)for(var L=function(e){var t=b.find(function(t){var n=S.get(t);if(n)return n.slice(0,e).every(function(e){return e})});if(t)return E=t,"break"},z=h?3:1;z>0;z--){if("break"===L(z))break}t.placement!==E&&(t.modifiersData[r]._skip=!0,t.placement=E,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function Da(e,t,n){return _o(e,Lo(t,n))}const $a={name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name,o=n.mainAxis,a=void 0===o||o,i=n.altAxis,l=void 0!==i&&i,s=n.boundary,u=n.rootBoundary,c=n.altBoundary,d=n.padding,f=n.tether,p=void 0===f||f,h=n.tetherOffset,m=void 0===h?0:h,g=za(t,{boundary:s,rootBoundary:u,padding:d,altBoundary:c}),v=ba(t.placement),y=wa(t.placement),b=!y,w=xa(v),x="x"===w?"y":"x",S=t.modifiersData.popperOffsets,k=t.rects.reference,E=t.rects.popper,A="function"===typeof m?m(Object.assign({},t.rects,{placement:t.placement})):m,C="number"===typeof A?{mainAxis:A,altAxis:A}:Object.assign({mainAxis:0,altAxis:0},A),P=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,R={x:0,y:0};if(S){if(a){var O,T="y"===w?ea:ra,N="y"===w?ta:na,j="y"===w?"height":"width",M=S[w],_=M+g[T],L=M-g[N],z=p?-E[j]/2:0,I=y===ia?k[j]:E[j],D=y===ia?-E[j]:-k[j],$=t.elements.arrow,F=p&&$?Ko($):{width:0,height:0},B=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},U=B[T],W=B[N],V=Da(0,k[j],F[j]),H=b?k[j]/2-z-V-U-C.mainAxis:I-V-U-C.mainAxis,q=b?-k[j]/2+z+V+W+C.mainAxis:D+V+W+C.mainAxis,K=t.elements.arrow&&Zo(t.elements.arrow),G=K?"y"===w?K.clientTop||0:K.clientLeft||0:0,X=null!=(O=null==P?void 0:P[w])?O:0,Q=M+q-X,Y=Da(p?Lo(_,M+H-X-G):_,M,p?_o(L,Q):L);S[w]=Y,R[w]=Y-M}if(l){var J,Z="x"===w?ea:ra,ee="x"===w?ta:na,te=S[x],ne="y"===x?"height":"width",re=te+g[Z],oe=te-g[ee],ae=-1!==[ea,ra].indexOf(v),ie=null!=(J=null==P?void 0:P[x])?J:0,le=ae?re:te-k[ne]-E[ne]-ie+C.altAxis,se=ae?te+k[ne]+E[ne]-ie-C.altAxis:oe,ue=p&&ae?function(e,t,n){var r=Da(e,t,n);return r>n?n:r}(le,te,se):Da(p?le:re,te,p?se:oe);S[x]=ue,R[x]=ue-te}t.modifiersData[r]=R}},requiresIfExists:["offset"]};const Fa={name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state,r=e.name,o=e.options,a=n.elements.arrow,i=n.modifiersData.popperOffsets,l=ba(n.placement),s=xa(l),u=[ra,na].indexOf(l)>=0?"height":"width";if(a&&i){var c=function(e,t){return _a("number"!==typeof(e="function"===typeof e?e(Object.assign({},t.rects,{placement:t.placement})):e)?e:La(e,aa))}(o.padding,n),d=Ko(a),f="y"===s?ea:ra,p="y"===s?ta:na,h=n.rects.reference[u]+n.rects.reference[s]-i[s]-n.rects.popper[u],m=i[s]-n.rects.reference[s],g=Zo(a),v=g?"y"===s?g.clientHeight||0:g.clientWidth||0:0,y=h/2-m/2,b=c[f],w=v-d[u]-c[p],x=v/2-d[u]/2+y,S=Da(b,x,w),k=s;n.modifiersData[r]=((t={})[k]=S,t.centerOffset=S-x,t)}},effect:function(e){var t=e.state,n=e.options.element,r=void 0===n?"[data-popper-arrow]":n;null!=r&&("string"!==typeof r||(r=t.elements.popper.querySelector(r)))&&Ta(t.elements.popper,r)&&(t.elements.arrow=r)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Ba(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function Ua(e){return[ea,na,ta,ra].some(function(t){return e[t]>=0})}var Wa=va({defaultModifiers:[{name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,r=e.options,o=r.scroll,a=void 0===o||o,i=r.resize,l=void 0===i||i,s=To(t.elements.popper),u=[].concat(t.scrollParents.reference,t.scrollParents.popper);return a&&u.forEach(function(e){e.addEventListener("scroll",n.update,ya)}),l&&s.addEventListener("resize",n.update,ya),function(){a&&u.forEach(function(e){e.removeEventListener("scroll",n.update,ya)}),l&&s.removeEventListener("resize",n.update,ya)}},data:{}},{name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=Sa({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}},{name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,r=n.gpuAcceleration,o=void 0===r||r,a=n.adaptive,i=void 0===a||a,l=n.roundOffsets,s=void 0===l||l,u={placement:ba(t.placement),variation:wa(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:o,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,Ea(Object.assign({},u,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:i,roundOffsets:s})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,Ea(Object.assign({},u,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:s})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}},{name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach(function(e){var n=t.styles[e]||{},r=t.attributes[e]||{},o=t.elements[e];jo(o)&&Bo(o)&&(Object.assign(o.style,n),Object.keys(r).forEach(function(e){var t=r[e];!1===t?o.removeAttribute(e):o.setAttribute(e,!0===t?"":t)}))})},effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach(function(e){var r=t.elements[e],o=t.attributes[e]||{},a=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:n[e]).reduce(function(e,t){return e[t]="",e},{});jo(r)&&Bo(r)&&(Object.assign(r.style,a),Object.keys(o).forEach(function(e){r.removeAttribute(e)}))})}},requires:["computeStyles"]},Aa,Ia,$a,Fa,{name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,r=t.rects.reference,o=t.rects.popper,a=t.modifiersData.preventOverflow,i=za(t,{elementContext:"reference"}),l=za(t,{altBoundary:!0}),s=Ba(i,r),u=Ba(l,o,a),c=Ua(s),d=Ua(u);t.modifiersData[n]={referenceClippingOffsets:s,popperEscapeOffsets:u,isReferenceHidden:c,hasPopperEscaped:d},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":c,"data-popper-escaped":d})}}]});const Va=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];if(void 0===e)return{};const n={};return Object.keys(e).filter(n=>n.match(/^on[A-Z]/)&&"function"===typeof e[n]&&!t.includes(n)).forEach(t=>{n[t]=e[t]}),n};const Ha=function(e){if(void 0===e)return{};const t={};return Object.keys(e).filter(t=>!(t.match(/^on[A-Z]/)&&"function"===typeof e[t])).forEach(n=>{t[n]=e[n]}),t};const qa=function(e){const{getSlotProps:t,additionalProps:n,externalSlotProps:r,externalForwardedProps:o,className:a}=e;if(!t){const e=(0,At.A)(null==n?void 0:n.className,a,null==o?void 0:o.className,null==r?void 0:r.className),t=(0,Ce.A)({},null==n?void 0:n.style,null==o?void 0:o.style,null==r?void 0:r.style),i=(0,Ce.A)({},n,o,r);return e.length>0&&(i.className=e),Object.keys(t).length>0&&(i.style=t),{props:i,internalRef:void 0}}const i=Va((0,Ce.A)({},o,r)),l=Ha(r),s=Ha(o),u=t(i),c=(0,At.A)(null==u?void 0:u.className,null==n?void 0:n.className,a,null==o?void 0:o.className,null==r?void 0:r.className),d=(0,Ce.A)({},null==u?void 0:u.style,null==n?void 0:n.style,null==o?void 0:o.style,null==r?void 0:r.style),f=(0,Ce.A)({},u,n,s,l);return c.length>0&&(f.className=c),Object.keys(d).length>0&&(f.style=d),{props:f,internalRef:u.ref}};const Ka=function(e,t,n){return"function"===typeof e?e(t,n):e},Ga=["elementType","externalSlotProps","ownerState","skipResolvingSlotProps"];const Xa=function(e){var t;const{elementType:n,externalSlotProps:r,ownerState:o,skipResolvingSlotProps:a=!1}=e,i=(0,Pe.A)(e,Ga),l=a?{}:Ka(r,o),{props:s,internalRef:u}=qa((0,Ce.A)({},i,{externalSlotProps:l})),c=sn(u,null==l?void 0:l.ref,null==(t=e.additionalProps)?void 0:t.ref);return so(n,(0,Ce.A)({},s,{ref:c}),o)};const Qa=e.forwardRef(function(t,n){const{children:r,container:o,disablePortal:a=!1}=t,[i,l]=e.useState(null),s=sn(e.isValidElement(r)?uo(r):null,n);if(bt(()=>{a||l(function(e){return"function"===typeof e?e():e}(o)||document.body)},[o,a]),bt(()=>{if(i&&!a)return ln(n,i),()=>{ln(n,null)}},[n,i,a]),a){if(e.isValidElement(r)){const t={ref:s};return e.cloneElement(r,t)}return(0,S.jsx)(e.Fragment,{children:r})}return(0,S.jsx)(e.Fragment,{children:i?c.createPortal(r,i):i})});function Ya(e){return Mt("MuiPopper",e)}_t("MuiPopper",["root"]);const Ja=["anchorEl","children","direction","disablePortal","modifiers","open","placement","popperOptions","popperRef","slotProps","slots","TransitionProps","ownerState"],Za=["anchorEl","children","container","direction","disablePortal","keepMounted","modifiers","open","placement","popperOptions","popperRef","style","transition","slotProps","slots"];function ei(e){return"function"===typeof e?e():e}function ti(e){return void 0!==e.nodeType}const ni={},ri=e.forwardRef(function(t,n){var r;const{anchorEl:o,children:a,direction:i,disablePortal:l,modifiers:s,open:u,placement:c,popperOptions:d,popperRef:f,slotProps:p={},slots:h={},TransitionProps:m}=t,g=(0,Pe.A)(t,Ja),v=e.useRef(null),y=sn(v,n),b=e.useRef(null),w=sn(b,f),x=e.useRef(w);bt(()=>{x.current=w},[w]),e.useImperativeHandle(f,()=>b.current,[]);const k=function(e,t){if("ltr"===t)return e;switch(e){case"bottom-end":return"bottom-start";case"bottom-start":return"bottom-end";case"top-end":return"top-start";case"top-start":return"top-end";default:return e}}(c,i),[E,A]=e.useState(k),[C,P]=e.useState(ei(o));e.useEffect(()=>{b.current&&b.current.forceUpdate()}),e.useEffect(()=>{o&&P(ei(o))},[o]),bt(()=>{if(!C||!u)return;let e=[{name:"preventOverflow",options:{altBoundary:l}},{name:"flip",options:{altBoundary:l}},{name:"onUpdate",enabled:!0,phase:"afterWrite",fn:e=>{let{state:t}=e;A(t.placement)}}];null!=s&&(e=e.concat(s)),d&&null!=d.modifiers&&(e=e.concat(d.modifiers));const t=Wa(C,v.current,(0,Ce.A)({placement:k},d,{modifiers:e}));return x.current(t),()=>{t.destroy(),x.current(null)}},[C,l,s,u,d,k]);const R={placement:E};null!==m&&(R.TransitionProps=m);const O=(e=>{const{classes:t}=e;return Ct({root:["root"]},Ya,t)})(t),T=null!=(r=h.root)?r:"div",N=Xa({elementType:T,externalSlotProps:p.root,externalForwardedProps:g,additionalProps:{role:"tooltip",ref:y},ownerState:t,className:O.root});return(0,S.jsx)(T,(0,Ce.A)({},N,{children:"function"===typeof a?a(R):a}))}),oi=["anchorEl","component","components","componentsProps","container","disablePortal","keepMounted","modifiers","open","placement","popperOptions","popperRef","transition","slots","slotProps"],ai=pt(e.forwardRef(function(t,n){const{anchorEl:r,children:o,container:a,direction:i="ltr",disablePortal:l=!1,keepMounted:s=!1,modifiers:u,open:c,placement:d="bottom",popperOptions:f=ni,popperRef:p,style:h,transition:m=!1,slotProps:g={},slots:v={}}=t,y=(0,Pe.A)(t,Za),[b,w]=e.useState(!0);if(!s&&!c&&(!m||b))return null;let x;if(a)x=a;else if(r){const e=ei(r);x=e&&ti(e)?Oo(e).body:Oo(null).body}const k=c||!s||m&&!b?void 0:"none",E=m?{in:c,onEnter:()=>{w(!1)},onExited:()=>{w(!0)}}:void 0;return(0,S.jsx)(Qa,{disablePortal:l,container:x,children:(0,S.jsx)(ri,(0,Ce.A)({anchorEl:r,direction:i,disablePortal:l,modifiers:u,ref:n,open:m?!b:c,placement:d,popperOptions:f,popperRef:p,slotProps:g,slots:v},y,{style:(0,Ce.A)({position:"fixed",top:0,left:0,display:k},h),TransitionProps:E,children:o}))})}),{name:"MuiPopper",slot:"Root",overridesResolver:(e,t)=>t.root})({}),ii=e.forwardRef(function(e,t){var n;const r=(0,Ro.A)(),o=Ot({props:e,name:"MuiPopper"}),{anchorEl:a,component:i,components:l,componentsProps:s,container:u,disablePortal:c,keepMounted:d,modifiers:f,open:p,placement:h,popperOptions:m,popperRef:g,transition:v,slots:y,slotProps:b}=o,w=(0,Pe.A)(o,oi),x=null!=(n=null==y?void 0:y.root)?n:null==l?void 0:l.Root,k=(0,Ce.A)({anchorEl:a,container:u,disablePortal:c,keepMounted:d,modifiers:f,open:p,placement:h,popperOptions:m,popperRef:g,transition:v},w);return(0,S.jsx)(ai,(0,Ce.A)({as:i,direction:null==r?void 0:r.direction,slots:{root:x},slotProps:null!=b?b:s},k,{ref:t}))});let li=0;const si=t["useId".toString()];const ui=function(t){if(void 0!==si){const e=si();return null!=t?t:e}return function(t){const[n,r]=e.useState(t),o=t||n;return e.useEffect(()=>{null==n&&(li+=1,r(`mui-${li}`))},[n]),o}(t)};const ci=function(t){let{controlled:n,default:r,name:o,state:a="value"}=t;const{current:i}=e.useRef(void 0!==n),[l,s]=e.useState(r);return[i?n:l,e.useCallback(e=>{i||s(e)},[])]};function di(e){return Mt("MuiTooltip",e)}const fi=_t("MuiTooltip",["popper","popperInteractive","popperArrow","popperClose","tooltip","tooltipArrow","touch","tooltipPlacementLeft","tooltipPlacementRight","tooltipPlacementTop","tooltipPlacementBottom","arrow"]),pi=["arrow","children","classes","components","componentsProps","describeChild","disableFocusListener","disableHoverListener","disableInteractive","disableTouchListener","enterDelay","enterNextDelay","enterTouchDelay","followCursor","id","leaveDelay","leaveTouchDelay","onClose","onOpen","open","placement","PopperComponent","PopperProps","slotProps","slots","title","TransitionComponent","TransitionProps"];const hi=pt(ii,{name:"MuiTooltip",slot:"Popper",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.popper,!n.disableInteractive&&t.popperInteractive,n.arrow&&t.popperArrow,!n.open&&t.popperClose]}})(e=>{let{theme:t,ownerState:n,open:r}=e;return(0,Ce.A)({zIndex:(t.vars||t).zIndex.tooltip,pointerEvents:"none"},!n.disableInteractive&&{pointerEvents:"auto"},!r&&{pointerEvents:"none"},n.arrow&&{[`&[data-popper-placement*="bottom"] .${fi.arrow}`]:{top:0,marginTop:"-0.71em","&::before":{transformOrigin:"0 100%"}},[`&[data-popper-placement*="top"] .${fi.arrow}`]:{bottom:0,marginBottom:"-0.71em","&::before":{transformOrigin:"100% 0"}},[`&[data-popper-placement*="right"] .${fi.arrow}`]:(0,Ce.A)({},n.isRtl?{right:0,marginRight:"-0.71em"}:{left:0,marginLeft:"-0.71em"},{height:"1em",width:"0.71em","&::before":{transformOrigin:"100% 100%"}}),[`&[data-popper-placement*="left"] .${fi.arrow}`]:(0,Ce.A)({},n.isRtl?{left:0,marginLeft:"-0.71em"}:{right:0,marginRight:"-0.71em"},{height:"1em",width:"0.71em","&::before":{transformOrigin:"0 0"}})})}),mi=pt("div",{name:"MuiTooltip",slot:"Tooltip",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.tooltip,n.touch&&t.touch,n.arrow&&t.tooltipArrow,t[`tooltipPlacement${Ft(n.placement.split("-")[0])}`]]}})(e=>{let{theme:t,ownerState:n}=e;return(0,Ce.A)({backgroundColor:t.vars?t.vars.palette.Tooltip.bg:(0,Me.X4)(t.palette.grey[700],.92),borderRadius:(t.vars||t).shape.borderRadius,color:(t.vars||t).palette.common.white,fontFamily:t.typography.fontFamily,padding:"4px 8px",fontSize:t.typography.pxToRem(11),maxWidth:300,margin:2,wordWrap:"break-word",fontWeight:t.typography.fontWeightMedium},n.arrow&&{position:"relative",margin:0},n.touch&&{padding:"8px 16px",fontSize:t.typography.pxToRem(14),lineHeight:(r=16/14,Math.round(1e5*r)/1e5)+"em",fontWeight:t.typography.fontWeightRegular},{[`.${fi.popper}[data-popper-placement*="left"] &`]:(0,Ce.A)({transformOrigin:"right center"},n.isRtl?(0,Ce.A)({marginLeft:"14px"},n.touch&&{marginLeft:"24px"}):(0,Ce.A)({marginRight:"14px"},n.touch&&{marginRight:"24px"})),[`.${fi.popper}[data-popper-placement*="right"] &`]:(0,Ce.A)({transformOrigin:"left center"},n.isRtl?(0,Ce.A)({marginRight:"14px"},n.touch&&{marginRight:"24px"}):(0,Ce.A)({marginLeft:"14px"},n.touch&&{marginLeft:"24px"})),[`.${fi.popper}[data-popper-placement*="top"] &`]:(0,Ce.A)({transformOrigin:"center bottom",marginBottom:"14px"},n.touch&&{marginBottom:"24px"}),[`.${fi.popper}[data-popper-placement*="bottom"] &`]:(0,Ce.A)({transformOrigin:"center top",marginTop:"14px"},n.touch&&{marginTop:"24px"})});var r}),gi=pt("span",{name:"MuiTooltip",slot:"Arrow",overridesResolver:(e,t)=>t.arrow})(e=>{let{theme:t}=e;return{overflow:"hidden",position:"absolute",width:"1em",height:"0.71em",boxSizing:"border-box",color:t.vars?t.vars.palette.Tooltip.bg:(0,Me.X4)(t.palette.grey[700],.9),"&::before":{content:'""',margin:"auto",display:"block",width:"100%",height:"100%",backgroundColor:"currentColor",transform:"rotate(45deg)"}}});let vi=!1;const yi=new hn;let bi={x:0,y:0};function wi(e,t){return function(n){for(var r=arguments.length,o=new Array(r>1?r-1:0),a=1;a<r;a++)o[a-1]=arguments[a];t&&t(n,...o),e(n,...o)}}const xi=e.forwardRef(function(t,n){var r,o,a,i,l,s,u,c,d,f,p,h,m,g,v,y,b,w,x;const k=Ot({props:t,name:"MuiTooltip"}),{arrow:E=!1,children:A,components:C={},componentsProps:P={},describeChild:R=!1,disableFocusListener:O=!1,disableHoverListener:T=!1,disableInteractive:N=!1,disableTouchListener:j=!1,enterDelay:M=100,enterNextDelay:_=0,enterTouchDelay:L=700,followCursor:z=!1,id:I,leaveDelay:D=0,leaveTouchDelay:$=1500,onClose:F,onOpen:B,open:U,placement:W="bottom",PopperComponent:V,PopperProps:H={},slotProps:q={},slots:K={},title:G,TransitionComponent:X=Po,TransitionProps:Q}=k,Y=(0,Pe.A)(k,pi),J=e.isValidElement(A)?A:(0,S.jsx)("span",{children:A}),Z=yt(),ee=lo(),[te,ne]=e.useState(),[re,oe]=e.useState(null),ae=e.useRef(!1),ie=N||z,le=mn(),se=mn(),ue=mn(),ce=mn(),[de,fe]=ci({controlled:U,default:!1,name:"Tooltip",state:"open"});let pe=de;const he=ui(I),me=e.useRef(),ge=dn(()=>{void 0!==me.current&&(document.body.style.WebkitUserSelect=me.current,me.current=void 0),ce.clear()});e.useEffect(()=>ge,[ge]);const ve=e=>{yi.clear(),vi=!0,fe(!0),B&&!pe&&B(e)},ye=dn(e=>{yi.start(800+D,()=>{vi=!1}),fe(!1),F&&pe&&F(e),le.start(Z.transitions.duration.shortest,()=>{ae.current=!1})}),be=e=>{ae.current&&"touchstart"!==e.type||(te&&te.removeAttribute("title"),se.clear(),ue.clear(),M||vi&&_?se.start(vi?_:M,()=>{ve(e)}):ve(e))},we=e=>{se.clear(),ue.start(D,()=>{ye(e)})},{isFocusVisibleRef:xe,onBlur:Se,onFocus:ke,ref:Ee}=En(),[,Ae]=e.useState(!1),Re=e=>{Se(e),!1===xe.current&&(Ae(!1),we(e))},Oe=e=>{te||ne(e.currentTarget),ke(e),!0===xe.current&&(Ae(!0),be(e))},Te=e=>{ae.current=!0;const t=J.props;t.onTouchStart&&t.onTouchStart(e)},Ne=e=>{Te(e),ue.clear(),le.clear(),ge(),me.current=document.body.style.WebkitUserSelect,document.body.style.WebkitUserSelect="none",ce.start(L,()=>{document.body.style.WebkitUserSelect=me.current,be(e)})},je=e=>{J.props.onTouchEnd&&J.props.onTouchEnd(e),ge(),ue.start($,()=>{ye(e)})};e.useEffect(()=>{if(pe)return document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)};function e(e){"Escape"!==e.key&&"Esc"!==e.key||ye(e)}},[ye,pe]);const Me=un(uo(J),Ee,ne,n);G||0===G||(pe=!1);const _e=e.useRef(),Le={},ze="string"===typeof G;R?(Le.title=pe||!ze||T?null:G,Le["aria-describedby"]=pe?he:null):(Le["aria-label"]=ze?G:null,Le["aria-labelledby"]=pe&&!ze?he:null);const Ie=(0,Ce.A)({},Le,Y,J.props,{className:(0,At.A)(Y.className,J.props.className),onTouchStart:Te,ref:Me},z?{onMouseMove:e=>{const t=J.props;t.onMouseMove&&t.onMouseMove(e),bi={x:e.clientX,y:e.clientY},_e.current&&_e.current.update()}}:{});const De={};j||(Ie.onTouchStart=Ne,Ie.onTouchEnd=je),T||(Ie.onMouseOver=wi(be,Ie.onMouseOver),Ie.onMouseLeave=wi(we,Ie.onMouseLeave),ie||(De.onMouseOver=be,De.onMouseLeave=we)),O||(Ie.onFocus=wi(Oe,Ie.onFocus),Ie.onBlur=wi(Re,Ie.onBlur),ie||(De.onFocus=Oe,De.onBlur=Re));const $e=e.useMemo(()=>{var e;let t=[{name:"arrow",enabled:Boolean(re),options:{element:re,padding:4}}];return null!=(e=H.popperOptions)&&e.modifiers&&(t=t.concat(H.popperOptions.modifiers)),(0,Ce.A)({},H.popperOptions,{modifiers:t})},[re,H]),Fe=(0,Ce.A)({},k,{isRtl:ee,arrow:E,disableInteractive:ie,placement:W,PopperComponentProp:V,touch:ae.current}),Be=(e=>{const{classes:t,disableInteractive:n,arrow:r,touch:o,placement:a}=e;return Ct({popper:["popper",!n&&"popperInteractive",r&&"popperArrow"],tooltip:["tooltip",r&&"tooltipArrow",o&&"touch",`tooltipPlacement${Ft(a.split("-")[0])}`],arrow:["arrow"]},di,t)})(Fe),Ue=null!=(r=null!=(o=K.popper)?o:C.Popper)?r:hi,We=null!=(a=null!=(i=null!=(l=K.transition)?l:C.Transition)?i:X)?a:Po,Ve=null!=(s=null!=(u=K.tooltip)?u:C.Tooltip)?s:mi,He=null!=(c=null!=(d=K.arrow)?d:C.Arrow)?c:gi,qe=so(Ue,(0,Ce.A)({},H,null!=(f=q.popper)?f:P.popper,{className:(0,At.A)(Be.popper,null==H?void 0:H.className,null==(p=null!=(h=q.popper)?h:P.popper)?void 0:p.className)}),Fe),Ke=so(We,(0,Ce.A)({},Q,null!=(m=q.transition)?m:P.transition),Fe),Ge=so(Ve,(0,Ce.A)({},null!=(g=q.tooltip)?g:P.tooltip,{className:(0,At.A)(Be.tooltip,null==(v=null!=(y=q.tooltip)?y:P.tooltip)?void 0:v.className)}),Fe),Xe=so(He,(0,Ce.A)({},null!=(b=q.arrow)?b:P.arrow,{className:(0,At.A)(Be.arrow,null==(w=null!=(x=q.arrow)?x:P.arrow)?void 0:w.className)}),Fe);return(0,S.jsxs)(e.Fragment,{children:[e.cloneElement(J,Ie),(0,S.jsx)(Ue,(0,Ce.A)({as:null!=V?V:ii,placement:W,anchorEl:z?{getBoundingClientRect:()=>({top:bi.y,left:bi.x,right:bi.x,bottom:bi.y,width:0,height:0})}:te,popperRef:_e,open:!!te&&pe,id:he,transition:!0},De,qe,{popperOptions:$e,children:e=>{let{TransitionProps:t}=e;return(0,S.jsx)(We,(0,Ce.A)({timeout:Z.transitions.duration.shorter},t,Ke,{children:(0,S.jsxs)(Ve,(0,Ce.A)({},Ge,{children:[G,E?(0,S.jsx)(He,(0,Ce.A)({},Xe,{ref:oe})):null]}))}))}}))]})}),Si=xi,ki=t=>{const n=e.useRef({});return e.useEffect(()=>{n.current=t}),n.current};const Ei=function(e){const{badgeContent:t,invisible:n=!1,max:r=99,showZero:o=!1}=e,a=ki({badgeContent:t,max:r});let i=n;!1!==n||0!==t||o||(i=!0);const{badgeContent:l,max:s=r}=i?a:e;return{badgeContent:l,invisible:i,max:s,displayValue:l&&Number(l)>s?`${s}+`:l}};function Ai(e){return Mt("MuiBadge",e)}const Ci=_t("MuiBadge",["root","badge","dot","standard","anchorOriginTopRight","anchorOriginBottomRight","anchorOriginTopLeft","anchorOriginBottomLeft","invisible","colorError","colorInfo","colorPrimary","colorSecondary","colorSuccess","colorWarning","overlapRectangular","overlapCircular","anchorOriginTopLeftCircular","anchorOriginTopLeftRectangular","anchorOriginTopRightCircular","anchorOriginTopRightRectangular","anchorOriginBottomLeftCircular","anchorOriginBottomLeftRectangular","anchorOriginBottomRightCircular","anchorOriginBottomRightRectangular"]),Pi=["anchorOrigin","className","classes","component","components","componentsProps","children","overlap","color","invisible","max","badgeContent","slots","slotProps","showZero","variant"],Ri=pt("span",{name:"MuiBadge",slot:"Root",overridesResolver:(e,t)=>t.root})({position:"relative",display:"inline-flex",verticalAlign:"middle",flexShrink:0}),Oi=pt("span",{name:"MuiBadge",slot:"Badge",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.badge,t[n.variant],t[`anchorOrigin${Ft(n.anchorOrigin.vertical)}${Ft(n.anchorOrigin.horizontal)}${Ft(n.overlap)}`],"default"!==n.color&&t[`color${Ft(n.color)}`],n.invisible&&t.invisible]}})(e=>{let{theme:t}=e;var n;return{display:"flex",flexDirection:"row",flexWrap:"wrap",justifyContent:"center",alignContent:"center",alignItems:"center",position:"absolute",boxSizing:"border-box",fontFamily:t.typography.fontFamily,fontWeight:t.typography.fontWeightMedium,fontSize:t.typography.pxToRem(12),minWidth:20,lineHeight:1,padding:"0 6px",height:20,borderRadius:10,zIndex:1,transition:t.transitions.create("transform",{easing:t.transitions.easing.easeInOut,duration:t.transitions.duration.enteringScreen}),variants:[...Object.keys((null!=(n=t.vars)?n:t).palette).filter(e=>{var n,r;return(null!=(n=t.vars)?n:t).palette[e].main&&(null!=(r=t.vars)?r:t).palette[e].contrastText}).map(e=>({props:{color:e},style:{backgroundColor:(t.vars||t).palette[e].main,color:(t.vars||t).palette[e].contrastText}})),{props:{variant:"dot"},style:{borderRadius:4,height:8,minWidth:8,padding:0}},{props:e=>{let{ownerState:t}=e;return"top"===t.anchorOrigin.vertical&&"right"===t.anchorOrigin.horizontal&&"rectangular"===t.overlap},style:{top:0,right:0,transform:"scale(1) translate(50%, -50%)",transformOrigin:"100% 0%",[`&.${Ci.invisible}`]:{transform:"scale(0) translate(50%, -50%)"}}},{props:e=>{let{ownerState:t}=e;return"bottom"===t.anchorOrigin.vertical&&"right"===t.anchorOrigin.horizontal&&"rectangular"===t.overlap},style:{bottom:0,right:0,transform:"scale(1) translate(50%, 50%)",transformOrigin:"100% 100%",[`&.${Ci.invisible}`]:{transform:"scale(0) translate(50%, 50%)"}}},{props:e=>{let{ownerState:t}=e;return"top"===t.anchorOrigin.vertical&&"left"===t.anchorOrigin.horizontal&&"rectangular"===t.overlap},style:{top:0,left:0,transform:"scale(1) translate(-50%, -50%)",transformOrigin:"0% 0%",[`&.${Ci.invisible}`]:{transform:"scale(0) translate(-50%, -50%)"}}},{props:e=>{let{ownerState:t}=e;return"bottom"===t.anchorOrigin.vertical&&"left"===t.anchorOrigin.horizontal&&"rectangular"===t.overlap},style:{bottom:0,left:0,transform:"scale(1) translate(-50%, 50%)",transformOrigin:"0% 100%",[`&.${Ci.invisible}`]:{transform:"scale(0) translate(-50%, 50%)"}}},{props:e=>{let{ownerState:t}=e;return"top"===t.anchorOrigin.vertical&&"right"===t.anchorOrigin.horizontal&&"circular"===t.overlap},style:{top:"14%",right:"14%",transform:"scale(1) translate(50%, -50%)",transformOrigin:"100% 0%",[`&.${Ci.invisible}`]:{transform:"scale(0) translate(50%, -50%)"}}},{props:e=>{let{ownerState:t}=e;return"bottom"===t.anchorOrigin.vertical&&"right"===t.anchorOrigin.horizontal&&"circular"===t.overlap},style:{bottom:"14%",right:"14%",transform:"scale(1) translate(50%, 50%)",transformOrigin:"100% 100%",[`&.${Ci.invisible}`]:{transform:"scale(0) translate(50%, 50%)"}}},{props:e=>{let{ownerState:t}=e;return"top"===t.anchorOrigin.vertical&&"left"===t.anchorOrigin.horizontal&&"circular"===t.overlap},style:{top:"14%",left:"14%",transform:"scale(1) translate(-50%, -50%)",transformOrigin:"0% 0%",[`&.${Ci.invisible}`]:{transform:"scale(0) translate(-50%, -50%)"}}},{props:e=>{let{ownerState:t}=e;return"bottom"===t.anchorOrigin.vertical&&"left"===t.anchorOrigin.horizontal&&"circular"===t.overlap},style:{bottom:"14%",left:"14%",transform:"scale(1) translate(-50%, 50%)",transformOrigin:"0% 100%",[`&.${Ci.invisible}`]:{transform:"scale(0) translate(-50%, 50%)"}}},{props:{invisible:!0},style:{transition:t.transitions.create("transform",{easing:t.transitions.easing.easeInOut,duration:t.transitions.duration.leavingScreen})}}]}}),Ti=e.forwardRef(function(e,t){var n,r,o,a,i,l;const s=Ot({props:e,name:"MuiBadge"}),{anchorOrigin:u={vertical:"top",horizontal:"right"},className:c,component:d,components:f={},componentsProps:p={},children:h,overlap:m="rectangular",color:g="default",invisible:v=!1,max:y=99,badgeContent:b,slots:w,slotProps:x,showZero:k=!1,variant:E="standard"}=s,A=(0,Pe.A)(s,Pi),{badgeContent:C,invisible:P,max:R,displayValue:O}=Ei({max:y,invisible:v,badgeContent:b,showZero:k}),T=ki({anchorOrigin:u,color:g,overlap:m,variant:E,badgeContent:b}),N=P||null==C&&"dot"!==E,{color:j=g,overlap:M=m,anchorOrigin:_=u,variant:L=E}=N?T:s,z="dot"!==L?O:void 0,I=(0,Ce.A)({},s,{badgeContent:C,invisible:N,max:R,displayValue:z,showZero:k,anchorOrigin:_,color:j,overlap:M,variant:L}),D=(e=>{const{color:t,anchorOrigin:n,invisible:r,overlap:o,variant:a,classes:i={}}=e;return Ct({root:["root"],badge:["badge",a,r&&"invisible",`anchorOrigin${Ft(n.vertical)}${Ft(n.horizontal)}`,`anchorOrigin${Ft(n.vertical)}${Ft(n.horizontal)}${Ft(o)}`,`overlap${Ft(o)}`,"default"!==t&&`color${Ft(t)}`]},Ai,i)})(I),$=null!=(n=null!=(r=null==w?void 0:w.root)?r:f.Root)?n:Ri,F=null!=(o=null!=(a=null==w?void 0:w.badge)?a:f.Badge)?o:Oi,B=null!=(i=null==x?void 0:x.root)?i:p.root,U=null!=(l=null==x?void 0:x.badge)?l:p.badge,W=Xa({elementType:$,externalSlotProps:B,externalForwardedProps:A,additionalProps:{ref:t,as:d},ownerState:I,className:(0,At.A)(null==B?void 0:B.className,D.root,c)}),V=Xa({elementType:F,externalSlotProps:U,ownerState:I,className:(0,At.A)(D.badge,null==U?void 0:U.className)});return(0,S.jsxs)($,(0,Ce.A)({},W,{children:[h,(0,S.jsx)(F,(0,Ce.A)({},V,{children:z}))]}))});function Ni(e){return Mt("MuiSvgIcon",e)}_t("MuiSvgIcon",["root","colorPrimary","colorSecondary","colorAction","colorError","colorDisabled","fontSizeInherit","fontSizeSmall","fontSizeMedium","fontSizeLarge"]);const ji=["children","className","color","component","fontSize","htmlColor","inheritViewBox","titleAccess","viewBox"],Mi=pt("svg",{name:"MuiSvgIcon",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,"inherit"!==n.color&&t[`color${Ft(n.color)}`],t[`fontSize${Ft(n.fontSize)}`]]}})(e=>{let{theme:t,ownerState:n}=e;var r,o,a,i,l,s,u,c,d,f,p,h,m;return{userSelect:"none",width:"1em",height:"1em",display:"inline-block",fill:n.hasSvgAsChild?void 0:"currentColor",flexShrink:0,transition:null==(r=t.transitions)||null==(o=r.create)?void 0:o.call(r,"fill",{duration:null==(a=t.transitions)||null==(a=a.duration)?void 0:a.shorter}),fontSize:{inherit:"inherit",small:(null==(i=t.typography)||null==(l=i.pxToRem)?void 0:l.call(i,20))||"1.25rem",medium:(null==(s=t.typography)||null==(u=s.pxToRem)?void 0:u.call(s,24))||"1.5rem",large:(null==(c=t.typography)||null==(d=c.pxToRem)?void 0:d.call(c,35))||"2.1875rem"}[n.fontSize],color:null!=(f=null==(p=(t.vars||t).palette)||null==(p=p[n.color])?void 0:p.main)?f:{action:null==(h=(t.vars||t).palette)||null==(h=h.action)?void 0:h.active,disabled:null==(m=(t.vars||t).palette)||null==(m=m.action)?void 0:m.disabled,inherit:void 0}[n.color]}}),_i=e.forwardRef(function(t,n){const r=Ot({props:t,name:"MuiSvgIcon"}),{children:o,className:a,color:i="inherit",component:l="svg",fontSize:s="medium",htmlColor:u,inheritViewBox:c=!1,titleAccess:d,viewBox:f="0 0 24 24"}=r,p=(0,Pe.A)(r,ji),h=e.isValidElement(o)&&"svg"===o.type,m=(0,Ce.A)({},r,{color:i,component:l,fontSize:s,instanceFontSize:t.fontSize,inheritViewBox:c,viewBox:f,hasSvgAsChild:h}),g={};c||(g.viewBox=f);const v=(e=>{const{color:t,fontSize:n,classes:r}=e;return Ct({root:["root","inherit"!==t&&`color${Ft(t)}`,`fontSize${Ft(n)}`]},Ni,r)})(m);return(0,S.jsxs)(Mi,(0,Ce.A)({as:l,className:(0,At.A)(v.root,a),focusable:"false",color:u,"aria-hidden":!d||void 0,role:d?"img":void 0,ref:n},g,p,h&&o.props,{ownerState:m,children:[h?o.props.children:o,d?(0,S.jsx)("title",{children:d}):null]}))});_i.muiName="SvgIcon";const Li=_i;function zi(t,n){function r(e,r){return(0,S.jsx)(Li,(0,Ce.A)({"data-testid":`${n}Icon`,ref:r},e,{children:t}))}return r.muiName=Li.muiName,e.memo(e.forwardRef(r))}const Ii=zi((0,S.jsx)("path",{d:"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"}),"Person");function Di(e){return Mt("MuiAvatar",e)}_t("MuiAvatar",["root","colorDefault","circular","rounded","square","img","fallback"]);const $i=["className","elementType","ownerState","externalForwardedProps","getSlotOwnerState","internalForwardedProps"],Fi=["component","slots","slotProps"],Bi=["component"];const Ui=["alt","children","className","component","slots","slotProps","imgProps","sizes","src","srcSet","variant"],Wi=pt("div",{name:"MuiAvatar",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,t[n.variant],n.colorDefault&&t.colorDefault]}})(e=>{let{theme:t}=e;return{position:"relative",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,width:40,height:40,fontFamily:t.typography.fontFamily,fontSize:t.typography.pxToRem(20),lineHeight:1,borderRadius:"50%",overflow:"hidden",userSelect:"none",variants:[{props:{variant:"rounded"},style:{borderRadius:(t.vars||t).shape.borderRadius}},{props:{variant:"square"},style:{borderRadius:0}},{props:{colorDefault:!0},style:(0,Ce.A)({color:(t.vars||t).palette.background.default},t.vars?{backgroundColor:t.vars.palette.Avatar.defaultBg}:(0,Ce.A)({backgroundColor:t.palette.grey[400]},t.applyStyles("dark",{backgroundColor:t.palette.grey[600]})))}]}}),Vi=pt("img",{name:"MuiAvatar",slot:"Img",overridesResolver:(e,t)=>t.img})({width:"100%",height:"100%",textAlign:"center",objectFit:"cover",color:"transparent",textIndent:1e4}),Hi=pt(Ii,{name:"MuiAvatar",slot:"Fallback",overridesResolver:(e,t)=>t.fallback})({width:"75%",height:"75%"});const qi=e.forwardRef(function(t,n){const r=Ot({props:t,name:"MuiAvatar"}),{alt:o,children:a,className:i,component:l="div",slots:s={},slotProps:u={},imgProps:c,sizes:d,src:f,srcSet:p,variant:h="circular"}=r,m=(0,Pe.A)(r,Ui);let g=null;const v=function(t){let{crossOrigin:n,referrerPolicy:r,src:o,srcSet:a}=t;const[i,l]=e.useState(!1);return e.useEffect(()=>{if(!o&&!a)return;l(!1);let e=!0;const t=new Image;return t.onload=()=>{e&&l("loaded")},t.onerror=()=>{e&&l("error")},t.crossOrigin=n,t.referrerPolicy=r,t.src=o,a&&(t.srcset=a),()=>{e=!1}},[n,r,o,a]),i}((0,Ce.A)({},c,{src:f,srcSet:p})),y=f||p,b=y&&"error"!==v,w=(0,Ce.A)({},r,{colorDefault:!b,component:l,variant:h}),x=(e=>{const{classes:t,variant:n,colorDefault:r}=e;return Ct({root:["root",n,r&&"colorDefault"],img:["img"],fallback:["fallback"]},Di,t)})(w),[k,E]=function(e,t){const{className:n,elementType:r,ownerState:o,externalForwardedProps:a,getSlotOwnerState:i,internalForwardedProps:l}=t,s=(0,Pe.A)(t,$i),{component:u,slots:c={[e]:void 0},slotProps:d={[e]:void 0}}=a,f=(0,Pe.A)(a,Fi),p=c[e]||r,h=Ka(d[e],o),m=qa((0,Ce.A)({className:n},s,{externalForwardedProps:"root"===e?f:void 0,externalSlotProps:h})),{props:{component:g},internalRef:v}=m,y=(0,Pe.A)(m.props,Bi),b=sn(v,null==h?void 0:h.ref,t.ref),w=i?i(y):{},x=(0,Ce.A)({},o,w),S="root"===e?g||u:g,k=so(p,(0,Ce.A)({},"root"===e&&!u&&!c[e]&&l,"root"!==e&&!c[e]&&l,y,S&&{as:S},{ref:b}),x);return Object.keys(w).forEach(e=>{delete k[e]}),[p,k]}("img",{className:x.img,elementType:Vi,externalForwardedProps:{slots:s,slotProps:{img:(0,Ce.A)({},c,u.img)}},additionalProps:{alt:o,src:f,srcSet:p,sizes:d},ownerState:w});return g=b?(0,S.jsx)(k,(0,Ce.A)({},E)):a||0===a?a:y&&o?o[0]:(0,S.jsx)(Hi,{ownerState:w,className:x.fallback}),(0,S.jsx)(Wi,(0,Ce.A)({as:l,ownerState:w,className:(0,At.A)(x.root,i),ref:n},m,{children:g}))}),Ki=qi,Gi=["input","select","textarea","a[href]","button","[tabindex]","audio[controls]","video[controls]",'[contenteditable]:not([contenteditable="false"])'].join(",");function Xi(e){const t=[],n=[];return Array.from(e.querySelectorAll(Gi)).forEach((e,r)=>{const o=function(e){const t=parseInt(e.getAttribute("tabindex")||"",10);return Number.isNaN(t)?"true"===e.contentEditable||("AUDIO"===e.nodeName||"VIDEO"===e.nodeName||"DETAILS"===e.nodeName)&&null===e.getAttribute("tabindex")?0:e.tabIndex:t}(e);-1!==o&&function(e){return!(e.disabled||"INPUT"===e.tagName&&"hidden"===e.type||function(e){if("INPUT"!==e.tagName||"radio"!==e.type)return!1;if(!e.name)return!1;const t=t=>e.ownerDocument.querySelector(`input[type="radio"]${t}`);let n=t(`[name="${e.name}"]:checked`);return n||(n=t(`[name="${e.name}"]`)),n!==e}(e))}(e)&&(0===o?t.push(e):n.push({documentOrder:r,tabIndex:o,node:e}))}),n.sort((e,t)=>e.tabIndex===t.tabIndex?e.documentOrder-t.documentOrder:e.tabIndex-t.tabIndex).map(e=>e.node).concat(t)}function Qi(){return!0}const Yi=function(t){const{children:n,disableAutoFocus:r=!1,disableEnforceFocus:o=!1,disableRestoreFocus:a=!1,getTabbable:i=Xi,isEnabled:l=Qi,open:s}=t,u=e.useRef(!1),c=e.useRef(null),d=e.useRef(null),f=e.useRef(null),p=e.useRef(null),h=e.useRef(!1),m=e.useRef(null),g=sn(uo(n),m),v=e.useRef(null);e.useEffect(()=>{s&&m.current&&(h.current=!r)},[r,s]),e.useEffect(()=>{if(!s||!m.current)return;const e=Oo(m.current);return m.current.contains(e.activeElement)||(m.current.hasAttribute("tabIndex")||m.current.setAttribute("tabIndex","-1"),h.current&&m.current.focus()),()=>{a||(f.current&&f.current.focus&&(u.current=!0,f.current.focus()),f.current=null)}},[s]),e.useEffect(()=>{if(!s||!m.current)return;const e=Oo(m.current),t=t=>{v.current=t,!o&&l()&&"Tab"===t.key&&e.activeElement===m.current&&t.shiftKey&&(u.current=!0,d.current&&d.current.focus())},n=()=>{const t=m.current;if(null===t)return;if(!e.hasFocus()||!l()||u.current)return void(u.current=!1);if(t.contains(e.activeElement))return;if(o&&e.activeElement!==c.current&&e.activeElement!==d.current)return;if(e.activeElement!==p.current)p.current=null;else if(null!==p.current)return;if(!h.current)return;let n=[];if(e.activeElement!==c.current&&e.activeElement!==d.current||(n=i(m.current)),n.length>0){var r,a;const e=Boolean((null==(r=v.current)?void 0:r.shiftKey)&&"Tab"===(null==(a=v.current)?void 0:a.key)),t=n[0],o=n[n.length-1];"string"!==typeof t&&"string"!==typeof o&&(e?o.focus():t.focus())}else t.focus()};e.addEventListener("focusin",n),e.addEventListener("keydown",t,!0);const r=setInterval(()=>{e.activeElement&&"BODY"===e.activeElement.tagName&&n()},50);return()=>{clearInterval(r),e.removeEventListener("focusin",n),e.removeEventListener("keydown",t,!0)}},[r,o,a,l,s,i]);const y=e=>{null===f.current&&(f.current=e.relatedTarget),h.current=!0};return(0,S.jsxs)(e.Fragment,{children:[(0,S.jsx)("div",{tabIndex:s?0:-1,onFocus:y,ref:c,"data-testid":"sentinelStart"}),e.cloneElement(n,{ref:g,onFocus:e=>{null===f.current&&(f.current=e.relatedTarget),h.current=!0,p.current=e.target;const t=n.props.onFocus;t&&t(e)}}),(0,S.jsx)("div",{tabIndex:s?0:-1,onFocus:y,ref:d,"data-testid":"sentinelEnd"})]})},Ji=["addEndListener","appear","children","easing","in","onEnter","onEntered","onEntering","onExit","onExited","onExiting","style","timeout","TransitionComponent"],Zi={entering:{opacity:1},entered:{opacity:1}},el=e.forwardRef(function(t,n){const r=yt(),o={enter:r.transitions.duration.enteringScreen,exit:r.transitions.duration.leavingScreen},{addEndListener:a,appear:i=!0,children:l,easing:s,in:u,onEnter:c,onEntered:d,onEntering:f,onExit:p,onExited:h,onExiting:m,style:g,timeout:v=o,TransitionComponent:y=bo}=t,b=(0,Pe.A)(t,Ji),w=e.useRef(null),x=un(w,uo(l),n),k=e=>t=>{if(e){const n=w.current;void 0===t?e(n):e(n,t)}},E=k(f),A=k((e,t)=>{wo(e);const n=xo({style:g,timeout:v,easing:s},{mode:"enter"});e.style.webkitTransition=r.transitions.create("opacity",n),e.style.transition=r.transitions.create("opacity",n),c&&c(e,t)}),C=k(d),P=k(m),R=k(e=>{const t=xo({style:g,timeout:v,easing:s},{mode:"exit"});e.style.webkitTransition=r.transitions.create("opacity",t),e.style.transition=r.transitions.create("opacity",t),p&&p(e)}),O=k(h);return(0,S.jsx)(y,(0,Ce.A)({appear:i,in:u,nodeRef:w,onEnter:A,onEntered:C,onEntering:E,onExit:R,onExited:O,onExiting:P,addEndListener:e=>{a&&a(w.current,e)},timeout:v},b,{children:(t,n)=>e.cloneElement(l,(0,Ce.A)({style:(0,Ce.A)({opacity:0,visibility:"exited"!==t||u?void 0:"hidden"},Zi[t],g,l.props.style),ref:x},n))}))}),tl=el;function nl(e){return Mt("MuiBackdrop",e)}_t("MuiBackdrop",["root","invisible"]);const rl=["children","className","component","components","componentsProps","invisible","open","slotProps","slots","TransitionComponent","transitionDuration"],ol=pt("div",{name:"MuiBackdrop",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,n.invisible&&t.invisible]}})(e=>{let{ownerState:t}=e;return(0,Ce.A)({position:"fixed",display:"flex",alignItems:"center",justifyContent:"center",right:0,bottom:0,top:0,left:0,backgroundColor:"rgba(0, 0, 0, 0.5)",WebkitTapHighlightColor:"transparent"},t.invisible&&{backgroundColor:"transparent"})}),al=e.forwardRef(function(e,t){var n,r,o;const a=Ot({props:e,name:"MuiBackdrop"}),{children:i,className:l,component:s="div",components:u={},componentsProps:c={},invisible:d=!1,open:f,slotProps:p={},slots:h={},TransitionComponent:m=tl,transitionDuration:g}=a,v=(0,Pe.A)(a,rl),y=(0,Ce.A)({},a,{component:s,invisible:d}),b=(e=>{const{classes:t,invisible:n}=e;return Ct({root:["root",n&&"invisible"]},nl,t)})(y),w=null!=(n=p.root)?n:c.root;return(0,S.jsx)(m,(0,Ce.A)({in:f,timeout:g},v,{children:(0,S.jsx)(ol,(0,Ce.A)({"aria-hidden":!0},w,{as:null!=(r=null!=(o=h.root)?o:u.Root)?r:s,className:(0,At.A)(b.root,l,null==w?void 0:w.className),ownerState:(0,Ce.A)({},y,null==w?void 0:w.ownerState),classes:b,ref:t,children:i}))}))});function il(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.reduce((e,t)=>null==t?e:function(){for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];e.apply(this,r),t.apply(this,r)},()=>{})}function ll(e){return Oo(e).defaultView||window}function sl(e){const t=e.documentElement.clientWidth;return Math.abs(window.innerWidth-t)}function ul(e,t){t?e.setAttribute("aria-hidden","true"):e.removeAttribute("aria-hidden")}function cl(e){return parseInt(ll(e).getComputedStyle(e).paddingRight,10)||0}function dl(e,t,n,r,o){const a=[t,n,...r];[].forEach.call(e.children,e=>{const t=-1===a.indexOf(e),n=!function(e){const t=-1!==["TEMPLATE","SCRIPT","STYLE","LINK","MAP","META","NOSCRIPT","PICTURE","COL","COLGROUP","PARAM","SLOT","SOURCE","TRACK"].indexOf(e.tagName),n="INPUT"===e.tagName&&"hidden"===e.getAttribute("type");return t||n}(e);t&&n&&ul(e,o)})}function fl(e,t){let n=-1;return e.some((e,r)=>!!t(e)&&(n=r,!0)),n}function pl(e,t){const n=[],r=e.container;if(!t.disableScrollLock){if(function(e){const t=Oo(e);return t.body===e?ll(e).innerWidth>t.documentElement.clientWidth:e.scrollHeight>e.clientHeight}(r)){const e=sl(Oo(r));n.push({value:r.style.paddingRight,property:"padding-right",el:r}),r.style.paddingRight=`${cl(r)+e}px`;const t=Oo(r).querySelectorAll(".mui-fixed");[].forEach.call(t,t=>{n.push({value:t.style.paddingRight,property:"padding-right",el:t}),t.style.paddingRight=`${cl(t)+e}px`})}let e;if(r.parentNode instanceof DocumentFragment)e=Oo(r).body;else{const t=r.parentElement,n=ll(r);e="HTML"===(null==t?void 0:t.nodeName)&&"scroll"===n.getComputedStyle(t).overflowY?t:r}n.push({value:e.style.overflow,property:"overflow",el:e},{value:e.style.overflowX,property:"overflow-x",el:e},{value:e.style.overflowY,property:"overflow-y",el:e}),e.style.overflow="hidden"}return()=>{n.forEach(e=>{let{value:t,el:n,property:r}=e;t?n.style.setProperty(r,t):n.style.removeProperty(r)})}}const hl=new class{constructor(){this.containers=void 0,this.modals=void 0,this.modals=[],this.containers=[]}add(e,t){let n=this.modals.indexOf(e);if(-1!==n)return n;n=this.modals.length,this.modals.push(e),e.modalRef&&ul(e.modalRef,!1);const r=function(e){const t=[];return[].forEach.call(e.children,e=>{"true"===e.getAttribute("aria-hidden")&&t.push(e)}),t}(t);dl(t,e.mount,e.modalRef,r,!0);const o=fl(this.containers,e=>e.container===t);return-1!==o?(this.containers[o].modals.push(e),n):(this.containers.push({modals:[e],container:t,restore:null,hiddenSiblings:r}),n)}mount(e,t){const n=fl(this.containers,t=>-1!==t.modals.indexOf(e)),r=this.containers[n];r.restore||(r.restore=pl(r,t))}remove(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];const n=this.modals.indexOf(e);if(-1===n)return n;const r=fl(this.containers,t=>-1!==t.modals.indexOf(e)),o=this.containers[r];if(o.modals.splice(o.modals.indexOf(e),1),this.modals.splice(n,1),0===o.modals.length)o.restore&&o.restore(),e.modalRef&&ul(e.modalRef,t),dl(o.container,e.mount,e.modalRef,o.hiddenSiblings,!1),this.containers.splice(r,1);else{const e=o.modals[o.modals.length-1];e.modalRef&&ul(e.modalRef,!1)}return n}isTopModal(e){return this.modals.length>0&&this.modals[this.modals.length-1]===e}};const ml=function(t){const{container:n,disableEscapeKeyDown:r=!1,disableScrollLock:o=!1,manager:a=hl,closeAfterTransition:i=!1,onTransitionEnter:l,onTransitionExited:s,children:u,onClose:c,open:d,rootRef:f}=t,p=e.useRef({}),h=e.useRef(null),m=e.useRef(null),g=sn(m,f),[v,y]=e.useState(!d),b=function(e){return!!e&&e.props.hasOwnProperty("in")}(u);let w=!0;"false"!==t["aria-hidden"]&&!1!==t["aria-hidden"]||(w=!1);const x=()=>(p.current.modalRef=m.current,p.current.mount=h.current,p.current),S=()=>{a.mount(x(),{disableScrollLock:o}),m.current&&(m.current.scrollTop=0)},k=cn(()=>{const e=function(e){return"function"===typeof e?e():e}(n)||Oo(h.current).body;a.add(x(),e),m.current&&S()}),E=e.useCallback(()=>a.isTopModal(x()),[a]),A=cn(e=>{h.current=e,e&&(d&&E()?S():m.current&&ul(m.current,w))}),C=e.useCallback(()=>{a.remove(x(),w)},[w,a]);e.useEffect(()=>()=>{C()},[C]),e.useEffect(()=>{d?k():b&&i||C()},[d,C,b,i,k]);const P=e=>t=>{var n;null==(n=e.onKeyDown)||n.call(e,t),"Escape"===t.key&&229!==t.which&&E()&&(r||(t.stopPropagation(),c&&c(t,"escapeKeyDown")))},R=e=>t=>{var n;null==(n=e.onClick)||n.call(e,t),t.target===t.currentTarget&&c&&c(t,"backdropClick")};return{getRootProps:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const n=Va(t);delete n.onTransitionEnter,delete n.onTransitionExited;const r=(0,Ce.A)({},n,e);return(0,Ce.A)({role:"presentation"},r,{onKeyDown:P(r),ref:g})},getBackdropProps:function(){const e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return(0,Ce.A)({"aria-hidden":!0},e,{onClick:R(e),open:d})},getTransitionProps:()=>({onEnter:il(()=>{y(!1),l&&l()},null==u?void 0:u.props.onEnter),onExited:il(()=>{y(!0),s&&s(),i&&C()},null==u?void 0:u.props.onExited)}),rootRef:g,portalRef:A,isTopModal:E,exited:v,hasTransition:b}};function gl(e){return Mt("MuiModal",e)}_t("MuiModal",["root","hidden","backdrop"]);const vl=["BackdropComponent","BackdropProps","classes","className","closeAfterTransition","children","container","component","components","componentsProps","disableAutoFocus","disableEnforceFocus","disableEscapeKeyDown","disablePortal","disableRestoreFocus","disableScrollLock","hideBackdrop","keepMounted","onBackdropClick","onClose","onTransitionEnter","onTransitionExited","open","slotProps","slots","theme"],yl=pt("div",{name:"MuiModal",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,!n.open&&n.exited&&t.hidden]}})(e=>{let{theme:t,ownerState:n}=e;return(0,Ce.A)({position:"fixed",zIndex:(t.vars||t).zIndex.modal,right:0,bottom:0,top:0,left:0},!n.open&&n.exited&&{visibility:"hidden"})}),bl=pt(al,{name:"MuiModal",slot:"Backdrop",overridesResolver:(e,t)=>t.backdrop})({zIndex:-1}),wl=e.forwardRef(function(t,n){var r,o,a,i,l,s;const u=Ot({name:"MuiModal",props:t}),{BackdropComponent:c=bl,BackdropProps:d,className:f,closeAfterTransition:p=!1,children:h,container:m,component:g,components:v={},componentsProps:y={},disableAutoFocus:b=!1,disableEnforceFocus:w=!1,disableEscapeKeyDown:x=!1,disablePortal:k=!1,disableRestoreFocus:E=!1,disableScrollLock:A=!1,hideBackdrop:C=!1,keepMounted:P=!1,onBackdropClick:R,open:O,slotProps:T,slots:N}=u,j=(0,Pe.A)(u,vl),M=(0,Ce.A)({},u,{closeAfterTransition:p,disableAutoFocus:b,disableEnforceFocus:w,disableEscapeKeyDown:x,disablePortal:k,disableRestoreFocus:E,disableScrollLock:A,hideBackdrop:C,keepMounted:P}),{getRootProps:_,getBackdropProps:L,getTransitionProps:z,portalRef:I,isTopModal:D,exited:$,hasTransition:F}=ml((0,Ce.A)({},M,{rootRef:n})),B=(0,Ce.A)({},M,{exited:$}),U=(e=>{const{open:t,exited:n,classes:r}=e;return Ct({root:["root",!t&&n&&"hidden"],backdrop:["backdrop"]},gl,r)})(B),W={};if(void 0===h.props.tabIndex&&(W.tabIndex="-1"),F){const{onEnter:e,onExited:t}=z();W.onEnter=e,W.onExited=t}const V=null!=(r=null!=(o=null==N?void 0:N.root)?o:v.Root)?r:yl,H=null!=(a=null!=(i=null==N?void 0:N.backdrop)?i:v.Backdrop)?a:c,q=null!=(l=null==T?void 0:T.root)?l:y.root,K=null!=(s=null==T?void 0:T.backdrop)?s:y.backdrop,G=Xa({elementType:V,externalSlotProps:q,externalForwardedProps:j,getSlotProps:_,additionalProps:{ref:n,as:g},ownerState:B,className:(0,At.A)(f,null==q?void 0:q.className,null==U?void 0:U.root,!B.open&&B.exited&&(null==U?void 0:U.hidden))}),X=Xa({elementType:H,externalSlotProps:K,additionalProps:d,getSlotProps:e=>L((0,Ce.A)({},e,{onClick:t=>{R&&R(t),null!=e&&e.onClick&&e.onClick(t)}})),className:(0,At.A)(null==K?void 0:K.className,null==d?void 0:d.className,null==U?void 0:U.backdrop),ownerState:B});return P||O||F&&!$?(0,S.jsx)(Qa,{ref:I,container:m,disablePortal:k,children:(0,S.jsxs)(V,(0,Ce.A)({},G,{children:[!C&&c?(0,S.jsx)(H,(0,Ce.A)({},X)):null,(0,S.jsx)(Yi,{disableEnforceFocus:w,disableAutoFocus:b,disableRestoreFocus:E,isEnabled:D,open:O,children:e.cloneElement(h,W)})]}))}):null}),xl=wl;const Sl=function(e){let t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:166;function r(){for(var r=arguments.length,o=new Array(r),a=0;a<r;a++)o[a]=arguments[a];clearTimeout(t),t=setTimeout(()=>{e.apply(this,o)},n)}return r.clear=()=>{clearTimeout(t)},r},kl=ll,El=["addEndListener","appear","children","container","direction","easing","in","onEnter","onEntered","onEntering","onExit","onExited","onExiting","style","timeout","TransitionComponent"];function Al(e,t,n){var r;const o=function(e,t,n){const r=t.getBoundingClientRect(),o=n&&n.getBoundingClientRect(),a=kl(t);let i;if(t.fakeTransform)i=t.fakeTransform;else{const e=a.getComputedStyle(t);i=e.getPropertyValue("-webkit-transform")||e.getPropertyValue("transform")}let l=0,s=0;if(i&&"none"!==i&&"string"===typeof i){const e=i.split("(")[1].split(")")[0].split(",");l=parseInt(e[4],10),s=parseInt(e[5],10)}return"left"===e?o?`translateX(${o.right+l-r.left}px)`:`translateX(${a.innerWidth+l-r.left}px)`:"right"===e?o?`translateX(-${r.right-o.left-l}px)`:`translateX(-${r.left+r.width-l}px)`:"up"===e?o?`translateY(${o.bottom+s-r.top}px)`:`translateY(${a.innerHeight+s-r.top}px)`:o?`translateY(-${r.top-o.top+r.height-s}px)`:`translateY(-${r.top+r.height-s}px)`}(e,t,"function"===typeof(r=n)?r():r);o&&(t.style.webkitTransform=o,t.style.transform=o)}const Cl=e.forwardRef(function(t,n){const r=yt(),o={enter:r.transitions.easing.easeOut,exit:r.transitions.easing.sharp},a={enter:r.transitions.duration.enteringScreen,exit:r.transitions.duration.leavingScreen},{addEndListener:i,appear:l=!0,children:s,container:u,direction:c="down",easing:d=o,in:f,onEnter:p,onEntered:h,onEntering:m,onExit:g,onExited:v,onExiting:y,style:b,timeout:w=a,TransitionComponent:x=bo}=t,k=(0,Pe.A)(t,El),E=e.useRef(null),A=un(uo(s),E,n),C=e=>t=>{e&&(void 0===t?e(E.current):e(E.current,t))},P=C((e,t)=>{Al(c,e,u),wo(e),p&&p(e,t)}),R=C((e,t)=>{const n=xo({timeout:w,style:b,easing:d},{mode:"enter"});e.style.webkitTransition=r.transitions.create("-webkit-transform",(0,Ce.A)({},n)),e.style.transition=r.transitions.create("transform",(0,Ce.A)({},n)),e.style.webkitTransform="none",e.style.transform="none",m&&m(e,t)}),O=C(h),T=C(y),N=C(e=>{const t=xo({timeout:w,style:b,easing:d},{mode:"exit"});e.style.webkitTransition=r.transitions.create("-webkit-transform",t),e.style.transition=r.transitions.create("transform",t),Al(c,e,u),g&&g(e)}),j=C(e=>{e.style.webkitTransition="",e.style.transition="",v&&v(e)}),M=e.useCallback(()=>{E.current&&Al(c,E.current,u)},[c,u]);return e.useEffect(()=>{if(f||"down"===c||"right"===c)return;const e=Sl(()=>{E.current&&Al(c,E.current,u)}),t=kl(E.current);return t.addEventListener("resize",e),()=>{e.clear(),t.removeEventListener("resize",e)}},[c,f,u]),e.useEffect(()=>{f||M()},[f,M]),(0,S.jsx)(x,(0,Ce.A)({nodeRef:E,onEnter:P,onEntered:O,onEntering:R,onExit:N,onExited:j,onExiting:T,addEndListener:e=>{i&&i(E.current,e)},appear:l,in:f,timeout:w},k,{children:(t,n)=>e.cloneElement(s,(0,Ce.A)({ref:A,style:(0,Ce.A)({visibility:"exited"!==t||f?void 0:"hidden"},b,s.props.style)},n))}))});function Pl(e){return Mt("MuiDrawer",e)}_t("MuiDrawer",["root","docked","paper","paperAnchorLeft","paperAnchorRight","paperAnchorTop","paperAnchorBottom","paperAnchorDockedLeft","paperAnchorDockedRight","paperAnchorDockedTop","paperAnchorDockedBottom","modal"]);const Rl=["BackdropProps"],Ol=["anchor","BackdropProps","children","className","elevation","hideBackdrop","ModalProps","onClose","open","PaperProps","SlideProps","TransitionComponent","transitionDuration","variant"],Tl=(e,t)=>{const{ownerState:n}=e;return[t.root,("permanent"===n.variant||"persistent"===n.variant)&&t.docked,t.modal]},Nl=pt(xl,{name:"MuiDrawer",slot:"Root",overridesResolver:Tl})(e=>{let{theme:t}=e;return{zIndex:(t.vars||t).zIndex.drawer}}),jl=pt("div",{shouldForwardProp:ft,name:"MuiDrawer",slot:"Docked",skipVariantsResolver:!1,overridesResolver:Tl})({flex:"0 0 auto"}),Ml=pt(Xr,{name:"MuiDrawer",slot:"Paper",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.paper,t[`paperAnchor${Ft(n.anchor)}`],"temporary"!==n.variant&&t[`paperAnchorDocked${Ft(n.anchor)}`]]}})(e=>{let{theme:t,ownerState:n}=e;return(0,Ce.A)({overflowY:"auto",display:"flex",flexDirection:"column",height:"100%",flex:"1 0 auto",zIndex:(t.vars||t).zIndex.drawer,WebkitOverflowScrolling:"touch",position:"fixed",top:0,outline:0},"left"===n.anchor&&{left:0},"top"===n.anchor&&{top:0,left:0,right:0,height:"auto",maxHeight:"100%"},"right"===n.anchor&&{right:0},"bottom"===n.anchor&&{top:"auto",left:0,bottom:0,right:0,height:"auto",maxHeight:"100%"},"left"===n.anchor&&"temporary"!==n.variant&&{borderRight:`1px solid ${(t.vars||t).palette.divider}`},"top"===n.anchor&&"temporary"!==n.variant&&{borderBottom:`1px solid ${(t.vars||t).palette.divider}`},"right"===n.anchor&&"temporary"!==n.variant&&{borderLeft:`1px solid ${(t.vars||t).palette.divider}`},"bottom"===n.anchor&&"temporary"!==n.variant&&{borderTop:`1px solid ${(t.vars||t).palette.divider}`})}),_l={left:"right",right:"left",top:"down",bottom:"up"};const Ll=e.forwardRef(function(t,n){const r=Ot({props:t,name:"MuiDrawer"}),o=yt(),a=lo(),i={enter:o.transitions.duration.enteringScreen,exit:o.transitions.duration.leavingScreen},{anchor:l="left",BackdropProps:s,children:u,className:c,elevation:d=16,hideBackdrop:f=!1,ModalProps:{BackdropProps:p}={},onClose:h,open:m=!1,PaperProps:g={},SlideProps:v,TransitionComponent:y=Cl,transitionDuration:b=i,variant:w="temporary"}=r,x=(0,Pe.A)(r.ModalProps,Rl),k=(0,Pe.A)(r,Ol),E=e.useRef(!1);e.useEffect(()=>{E.current=!0},[]);const A=function(e,t){let{direction:n}=e;return"rtl"===n&&function(e){return-1!==["left","right"].indexOf(e)}(t)?_l[t]:t}({direction:a?"rtl":"ltr"},l),C=l,P=(0,Ce.A)({},r,{anchor:C,elevation:d,open:m,variant:w},k),R=(e=>{const{classes:t,anchor:n,variant:r}=e;return Ct({root:["root"],docked:[("permanent"===r||"persistent"===r)&&"docked"],modal:["modal"],paper:["paper",`paperAnchor${Ft(n)}`,"temporary"!==r&&`paperAnchorDocked${Ft(n)}`]},Pl,t)})(P),O=(0,S.jsx)(Ml,(0,Ce.A)({elevation:"temporary"===w?d:0,square:!0},g,{className:(0,At.A)(R.paper,g.className),ownerState:P,children:u}));if("permanent"===w)return(0,S.jsx)(jl,(0,Ce.A)({className:(0,At.A)(R.root,R.docked,c),ownerState:P,ref:n},k,{children:O}));const T=(0,S.jsx)(y,(0,Ce.A)({in:m,direction:_l[A],timeout:b,appear:E.current},v,{children:O}));return"persistent"===w?(0,S.jsx)(jl,(0,Ce.A)({className:(0,At.A)(R.root,R.docked,c),ownerState:P,ref:n},k,{children:T})):(0,S.jsx)(Nl,(0,Ce.A)({BackdropProps:(0,Ce.A)({},s,p,{transitionDuration:b}),className:(0,At.A)(R.root,R.modal,c),open:m,ownerState:P,onClose:h,hideBackdrop:f,ref:n},k,x,{children:T}))}),zl=Oo,Il=sl,Dl=["actions","autoFocus","autoFocusItem","children","className","disabledItemsFocusable","disableListWrap","onKeyDown","variant"];function $l(e,t,n){return e===t?e.firstChild:t&&t.nextElementSibling?t.nextElementSibling:n?null:e.firstChild}function Fl(e,t,n){return e===t?n?e.firstChild:e.lastChild:t&&t.previousElementSibling?t.previousElementSibling:n?null:e.lastChild}function Bl(e,t){if(void 0===t)return!0;let n=e.innerText;return void 0===n&&(n=e.textContent),n=n.trim().toLowerCase(),0!==n.length&&(t.repeating?n[0]===t.keys[0]:0===n.indexOf(t.keys.join("")))}function Ul(e,t,n,r,o,a){let i=!1,l=o(e,t,!!t&&n);for(;l;){if(l===e.firstChild){if(i)return!1;i=!0}const t=!r&&(l.disabled||"true"===l.getAttribute("aria-disabled"));if(l.hasAttribute("tabindex")&&Bl(l,a)&&!t)return l.focus(),!0;l=o(e,l,n)}return!1}const Wl=e.forwardRef(function(t,n){const{actions:r,autoFocus:o=!1,autoFocusItem:a=!1,children:i,className:l,disabledItemsFocusable:s=!1,disableListWrap:u=!1,onKeyDown:c,variant:d="selectedMenu"}=t,f=(0,Pe.A)(t,Dl),p=e.useRef(null),h=e.useRef({keys:[],repeating:!0,previousKeyMatched:!0,lastTime:null});rr(()=>{o&&p.current.focus()},[o]),e.useImperativeHandle(r,()=>({adjustStyleForScrollbar:(e,t)=>{let{direction:n}=t;const r=!p.current.style.width;if(e.clientHeight<p.current.clientHeight&&r){const t=`${Il(zl(e))}px`;p.current.style["rtl"===n?"paddingLeft":"paddingRight"]=t,p.current.style.width=`calc(100% + ${t})`}return p.current}}),[]);const m=un(p,n);let g=-1;e.Children.forEach(i,(t,n)=>{e.isValidElement(t)?(t.props.disabled||("selectedMenu"===d&&t.props.selected||-1===g)&&(g=n),g===n&&(t.props.disabled||t.props.muiSkipListHighlight||t.type.muiSkipListHighlight)&&(g+=1,g>=i.length&&(g=-1))):g===n&&(g+=1,g>=i.length&&(g=-1))});const v=e.Children.map(i,(t,n)=>{if(n===g){const n={};return a&&(n.autoFocus=!0),void 0===t.props.tabIndex&&"selectedMenu"===d&&(n.tabIndex=0),e.cloneElement(t,n)}return t});return(0,S.jsx)(on,(0,Ce.A)({role:"menu",ref:m,className:l,onKeyDown:e=>{const t=p.current,n=e.key,r=zl(t).activeElement;if("ArrowDown"===n)e.preventDefault(),Ul(t,r,u,s,$l);else if("ArrowUp"===n)e.preventDefault(),Ul(t,r,u,s,Fl);else if("Home"===n)e.preventDefault(),Ul(t,null,u,s,$l);else if("End"===n)e.preventDefault(),Ul(t,null,u,s,Fl);else if(1===n.length){const o=h.current,a=n.toLowerCase(),i=performance.now();o.keys.length>0&&(i-o.lastTime>500?(o.keys=[],o.repeating=!0,o.previousKeyMatched=!0):o.repeating&&a!==o.keys[0]&&(o.repeating=!1)),o.lastTime=i,o.keys.push(a);const l=r&&!o.repeating&&Bl(r,o);o.previousKeyMatched&&(l||Ul(t,r,!1,s,$l,o))?e.preventDefault():o.previousKeyMatched=!1}c&&c(e)},tabIndex:o?0:-1},f,{children:v}))});function Vl(e){return Mt("MuiPopover",e)}_t("MuiPopover",["root","paper"]);const Hl=["onEntering"],ql=["action","anchorEl","anchorOrigin","anchorPosition","anchorReference","children","className","container","elevation","marginThreshold","open","PaperProps","slots","slotProps","transformOrigin","TransitionComponent","transitionDuration","TransitionProps","disableScrollLock"],Kl=["slotProps"];function Gl(e,t){let n=0;return"number"===typeof t?n=t:"center"===t?n=e.height/2:"bottom"===t&&(n=e.height),n}function Xl(e,t){let n=0;return"number"===typeof t?n=t:"center"===t?n=e.width/2:"right"===t&&(n=e.width),n}function Ql(e){return[e.horizontal,e.vertical].map(e=>"number"===typeof e?`${e}px`:e).join(" ")}function Yl(e){return"function"===typeof e?e():e}const Jl=pt(xl,{name:"MuiPopover",slot:"Root",overridesResolver:(e,t)=>t.root})({}),Zl=pt(Xr,{name:"MuiPopover",slot:"Paper",overridesResolver:(e,t)=>t.paper})({position:"absolute",overflowY:"auto",overflowX:"hidden",minWidth:16,minHeight:16,maxWidth:"calc(100% - 32px)",maxHeight:"calc(100% - 32px)",outline:0}),es=e.forwardRef(function(t,n){var r,o,a;const i=Ot({props:t,name:"MuiPopover"}),{action:l,anchorEl:s,anchorOrigin:u={vertical:"top",horizontal:"left"},anchorPosition:c,anchorReference:d="anchorEl",children:f,className:p,container:h,elevation:m=8,marginThreshold:g=16,open:v,PaperProps:y={},slots:b,slotProps:w,transformOrigin:x={vertical:"top",horizontal:"left"},TransitionComponent:k=Po,transitionDuration:E="auto",TransitionProps:{onEntering:A}={},disableScrollLock:C=!1}=i,P=(0,Pe.A)(i.TransitionProps,Hl),R=(0,Pe.A)(i,ql),O=null!=(r=null==w?void 0:w.paper)?r:y,T=e.useRef(),N=un(T,O.ref),j=(0,Ce.A)({},i,{anchorOrigin:u,anchorReference:d,elevation:m,marginThreshold:g,externalPaperSlotProps:O,transformOrigin:x,TransitionComponent:k,transitionDuration:E,TransitionProps:P}),M=(e=>{const{classes:t}=e;return Ct({root:["root"],paper:["paper"]},Vl,t)})(j),_=e.useCallback(()=>{if("anchorPosition"===d)return c;const e=Yl(s),t=(e&&1===e.nodeType?e:zl(T.current).body).getBoundingClientRect();return{top:t.top+Gl(t,u.vertical),left:t.left+Xl(t,u.horizontal)}},[s,u.horizontal,u.vertical,c,d]),L=e.useCallback(e=>({vertical:Gl(e,x.vertical),horizontal:Xl(e,x.horizontal)}),[x.horizontal,x.vertical]),z=e.useCallback(e=>{const t={width:e.offsetWidth,height:e.offsetHeight},n=L(t);if("none"===d)return{top:null,left:null,transformOrigin:Ql(n)};const r=_();let o=r.top-n.vertical,a=r.left-n.horizontal;const i=o+t.height,l=a+t.width,u=kl(Yl(s)),c=u.innerHeight-g,f=u.innerWidth-g;if(null!==g&&o<g){const e=o-g;o-=e,n.vertical+=e}else if(null!==g&&i>c){const e=i-c;o-=e,n.vertical+=e}if(null!==g&&a<g){const e=a-g;a-=e,n.horizontal+=e}else if(l>f){const e=l-f;a-=e,n.horizontal+=e}return{top:`${Math.round(o)}px`,left:`${Math.round(a)}px`,transformOrigin:Ql(n)}},[s,d,_,L,g]),[I,D]=e.useState(v),$=e.useCallback(()=>{const e=T.current;if(!e)return;const t=z(e);null!==t.top&&(e.style.top=t.top),null!==t.left&&(e.style.left=t.left),e.style.transformOrigin=t.transformOrigin,D(!0)},[z]);e.useEffect(()=>(C&&window.addEventListener("scroll",$),()=>window.removeEventListener("scroll",$)),[s,C,$]);e.useEffect(()=>{v&&$()}),e.useImperativeHandle(l,()=>v?{updatePosition:()=>{$()}}:null,[v,$]),e.useEffect(()=>{if(!v)return;const e=Sl(()=>{$()}),t=kl(s);return t.addEventListener("resize",e),()=>{e.clear(),t.removeEventListener("resize",e)}},[s,v,$]);let F=E;"auto"!==E||k.muiSupportAuto||(F=void 0);const B=h||(s?zl(Yl(s)).body:void 0),U=null!=(o=null==b?void 0:b.root)?o:Jl,W=null!=(a=null==b?void 0:b.paper)?a:Zl,V=Xa({elementType:W,externalSlotProps:(0,Ce.A)({},O,{style:I?O.style:(0,Ce.A)({},O.style,{opacity:0})}),additionalProps:{elevation:m,ref:N},ownerState:j,className:(0,At.A)(M.paper,null==O?void 0:O.className)}),H=Xa({elementType:U,externalSlotProps:(null==w?void 0:w.root)||{},externalForwardedProps:R,additionalProps:{ref:n,slotProps:{backdrop:{invisible:!0}},container:B,open:v},ownerState:j,className:(0,At.A)(M.root,p)}),{slotProps:q}=H,K=(0,Pe.A)(H,Kl);return(0,S.jsx)(U,(0,Ce.A)({},K,!an(U)&&{slotProps:q,disableScrollLock:C},{children:(0,S.jsx)(k,(0,Ce.A)({appear:!0,in:v,onEntering:(e,t)=>{A&&A(e,t),$()},onExited:()=>{D(!1)},timeout:F},P,{children:(0,S.jsx)(W,(0,Ce.A)({},V,{children:f}))}))}))}),ts=es;function ns(e){return Mt("MuiMenu",e)}_t("MuiMenu",["root","paper","list"]);const rs=["onEntering"],os=["autoFocus","children","className","disableAutoFocusItem","MenuListProps","onClose","open","PaperProps","PopoverClasses","transitionDuration","TransitionProps","variant","slots","slotProps"],as={vertical:"top",horizontal:"right"},is={vertical:"top",horizontal:"left"},ls=pt(ts,{shouldForwardProp:e=>ft(e)||"classes"===e,name:"MuiMenu",slot:"Root",overridesResolver:(e,t)=>t.root})({}),ss=pt(Zl,{name:"MuiMenu",slot:"Paper",overridesResolver:(e,t)=>t.paper})({maxHeight:"calc(100% - 96px)",WebkitOverflowScrolling:"touch"}),us=pt(Wl,{name:"MuiMenu",slot:"List",overridesResolver:(e,t)=>t.list})({outline:0}),cs=e.forwardRef(function(t,n){var r,o;const a=Ot({props:t,name:"MuiMenu"}),{autoFocus:i=!0,children:l,className:s,disableAutoFocusItem:u=!1,MenuListProps:c={},onClose:d,open:f,PaperProps:p={},PopoverClasses:h,transitionDuration:m="auto",TransitionProps:{onEntering:g}={},variant:v="selectedMenu",slots:y={},slotProps:b={}}=a,w=(0,Pe.A)(a.TransitionProps,rs),x=(0,Pe.A)(a,os),k=lo(),E=(0,Ce.A)({},a,{autoFocus:i,disableAutoFocusItem:u,MenuListProps:c,onEntering:g,PaperProps:p,transitionDuration:m,TransitionProps:w,variant:v}),A=(e=>{const{classes:t}=e;return Ct({root:["root"],paper:["paper"],list:["list"]},ns,t)})(E),C=i&&!u&&f,P=e.useRef(null);let R=-1;e.Children.map(l,(t,n)=>{e.isValidElement(t)&&(t.props.disabled||("selectedMenu"===v&&t.props.selected||-1===R)&&(R=n))});const O=null!=(r=y.paper)?r:ss,T=null!=(o=b.paper)?o:p,N=Xa({elementType:y.root,externalSlotProps:b.root,ownerState:E,className:[A.root,s]}),j=Xa({elementType:O,externalSlotProps:T,ownerState:E,className:A.paper});return(0,S.jsx)(ls,(0,Ce.A)({onClose:d,anchorOrigin:{vertical:"bottom",horizontal:k?"right":"left"},transformOrigin:k?as:is,slots:{paper:O,root:y.root},slotProps:{root:N,paper:j},open:f,ref:n,transitionDuration:m,TransitionProps:(0,Ce.A)({onEntering:(e,t)=>{P.current&&P.current.adjustStyleForScrollbar(e,{direction:k?"rtl":"ltr"}),g&&g(e,t)}},w),ownerState:E},x,{classes:h,children:(0,S.jsx)(us,(0,Ce.A)({onKeyDown:e=>{"Tab"===e.key&&(e.preventDefault(),d&&d(e,"tabKeyDown"))},actions:P,autoFocus:i&&(-1===R||u),autoFocusItem:C,variant:v},c,{className:(0,At.A)(A.list,c.className),children:l}))}))});function ds(e){return Mt("MuiMenuItem",e)}const fs=_t("MuiMenuItem",["root","focusVisible","dense","disabled","divider","gutters","selected"]),ps=["autoFocus","component","dense","divider","disableGutters","focusVisibleClassName","role","tabIndex","className"],hs=pt(tr,{shouldForwardProp:e=>ft(e)||"classes"===e,name:"MuiMenuItem",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,n.dense&&t.dense,n.divider&&t.divider,!n.disableGutters&&t.gutters]}})(e=>{let{theme:t,ownerState:n}=e;return(0,Ce.A)({},t.typography.body1,{display:"flex",justifyContent:"flex-start",alignItems:"center",position:"relative",textDecoration:"none",minHeight:48,paddingTop:6,paddingBottom:6,boxSizing:"border-box",whiteSpace:"nowrap"},!n.disableGutters&&{paddingLeft:16,paddingRight:16},n.divider&&{borderBottom:`1px solid ${(t.vars||t).palette.divider}`,backgroundClip:"padding-box"},{"&:hover":{textDecoration:"none",backgroundColor:(t.vars||t).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${fs.selected}`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / ${t.vars.palette.action.selectedOpacity})`:(0,Me.X4)(t.palette.primary.main,t.palette.action.selectedOpacity),[`&.${fs.focusVisible}`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / calc(${t.vars.palette.action.selectedOpacity} + ${t.vars.palette.action.focusOpacity}))`:(0,Me.X4)(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.focusOpacity)}},[`&.${fs.selected}:hover`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / calc(${t.vars.palette.action.selectedOpacity} + ${t.vars.palette.action.hoverOpacity}))`:(0,Me.X4)(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / ${t.vars.palette.action.selectedOpacity})`:(0,Me.X4)(t.palette.primary.main,t.palette.action.selectedOpacity)}},[`&.${fs.focusVisible}`]:{backgroundColor:(t.vars||t).palette.action.focus},[`&.${fs.disabled}`]:{opacity:(t.vars||t).palette.action.disabledOpacity},[`& + .${Gt.root}`]:{marginTop:t.spacing(1),marginBottom:t.spacing(1)},[`& + .${Gt.inset}`]:{marginLeft:52},[`& .${Rr.root}`]:{marginTop:0,marginBottom:0},[`& .${Rr.inset}`]:{paddingLeft:36},[`& .${kr.root}`]:{minWidth:36}},!n.dense&&{[t.breakpoints.up("sm")]:{minHeight:"auto"}},n.dense&&(0,Ce.A)({minHeight:32,paddingTop:4,paddingBottom:4},t.typography.body2,{[`& .${kr.root} svg`]:{fontSize:"1.25rem"}}))}),ms=e.forwardRef(function(t,n){const r=Ot({props:t,name:"MuiMenuItem"}),{autoFocus:o=!1,component:a="li",dense:i=!1,divider:l=!1,disableGutters:s=!1,focusVisibleClassName:u,role:c="menuitem",tabIndex:d,className:f}=r,p=(0,Pe.A)(r,ps),h=e.useContext(en),m=e.useMemo(()=>({dense:i||h.dense||!1,disableGutters:s}),[h.dense,i,s]),g=e.useRef(null);rr(()=>{o&&g.current&&g.current.focus()},[o]);const v=(0,Ce.A)({},r,{dense:m.dense,divider:l,disableGutters:s}),y=(e=>{const{disabled:t,dense:n,divider:r,disableGutters:o,selected:a,classes:i}=e,l=Ct({root:["root",n&&"dense",t&&"disabled",!o&&"gutters",r&&"divider",a&&"selected"]},ds,i);return(0,Ce.A)({},i,l)})(r),b=un(g,n);let w;return r.disabled||(w=void 0!==d?d:-1),(0,S.jsx)(en.Provider,{value:m,children:(0,S.jsx)(hs,(0,Ce.A)({ref:b,role:c,tabIndex:w,component:a,focusVisibleClassName:(0,At.A)(y.focusVisible,u),className:(0,At.A)(y.root,f)},p,{ownerState:v,classes:y}))})}),gs=zi((0,S.jsx)("path",{d:"M3 13h8V3H3zm0 8h8v-6H3zm10 0h8V11h-8zm0-18v6h8V3z"}),"Dashboard"),vs=zi((0,S.jsx)("path",{d:"M18 17H6v-2h12zm0-4H6v-2h12zm0-4H6V7h12zM3 22l1.5-1.5L6 22l1.5-1.5L9 22l1.5-1.5L12 22l1.5-1.5L15 22l1.5-1.5L18 22l1.5-1.5L21 22V2l-1.5 1.5L18 2l-1.5 1.5L15 2l-1.5 1.5L12 2l-1.5 1.5L9 2 7.5 3.5 6 2 4.5 3.5 3 2z"}),"Receipt"),ys=zi((0,S.jsx)("path",{d:"M20 4H4c-1.11 0-1.99.89-1.99 2L2 18c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2m0 14H4v-6h16zm0-10H4V6h16z"}),"Payment"),bs=zi((0,S.jsx)("path",{d:"M21 18v1c0 1.1-.9 2-2 2H5c-1.11 0-2-.9-2-2V5c0-1.1.89-2 2-2h14c1.1 0 2 .9 2 2v1h-9c-1.11 0-2 .9-2 2v8c0 1.1.89 2 2 2zm-9-2h10V8H12zm4-2.5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5"}),"AccountBalanceWallet"),ws=zi((0,S.jsx)("path",{d:"M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3m-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3m0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5m8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5"}),"People"),xs=zi((0,S.jsx)("path",{d:"M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.12.22.37.29.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.05.24.24.41.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6"}),"Settings"),Ss=zi((0,S.jsx)("path",{d:"M3 18h18v-2H3zm0-5h18v-2H3zm0-7v2h18V6z"}),"Menu"),ks=zi((0,S.jsx)("path",{d:"M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2m6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1z"}),"Notifications"),Es=zi((0,S.jsx)("path",{d:"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4m0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4"}),"Person"),As=zi((0,S.jsx)("path",{d:"m17 7-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.58L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4z"}),"Logout"),Cs=240,Ps=pt("main",{shouldForwardProp:e=>"open"!==e})(e=>{let{theme:t,open:n}=e;return{flexGrow:1,padding:t.spacing(3),transition:t.transitions.create("margin",{easing:t.transitions.easing.sharp,duration:t.transitions.duration.leavingScreen}),marginLeft:"-240px",...n&&{transition:t.transitions.create("margin",{easing:t.transitions.easing.easeOut,duration:t.transitions.duration.enteringScreen}),marginLeft:0}}}),Rs=t=>{var n;let{children:r,title:a="Dashboard"}=t;const l=yt(),s=(0,o.Zp)(),u=(0,o.zy)(),{user:c,logout:d}=(0,i.A)(),f=Et(l.breakpoints.down("md")),[p,h]=(0,e.useState)(!1),[m,g]=(0,e.useState)(null),v=Boolean(m),y=()=>{h(!p)},b=()=>{g(null)},w=e=>{s(e),f&&h(!1)};var x;const k=(x=(null===c||void 0===c?void 0:c.role)||"",[{text:"Dashboard",icon:(0,S.jsx)(gs,{}),path:"/dashboard",roles:["merchant","admin","trader"]},..."merchant"===x?[{text:"Transactions",icon:(0,S.jsx)(vs,{}),path:"/merchant/transactions",roles:["merchant"]},{text:"Collections",icon:(0,S.jsx)(ys,{}),path:"/merchant/collections",roles:["merchant"]},{text:"Billing",icon:(0,S.jsx)(bs,{}),path:"/merchant/billing",roles:["merchant"]},{text:"Traders",icon:(0,S.jsx)(ws,{}),path:"/merchant/traders",roles:["merchant"]},{text:"Settings",icon:(0,S.jsx)(xs,{}),path:"/merchant/settings",roles:["merchant"]}]:[],..."admin"===x?[{text:"Merchants",icon:(0,S.jsx)(ws,{}),path:"/admin/merchants",roles:["admin"]},{text:"Traders",icon:(0,S.jsx)(ws,{}),path:"/admin/traders",roles:["admin"]},{text:"Payments",icon:(0,S.jsx)(ys,{}),path:"/admin/payments",roles:["admin"]},{text:"Reports",icon:(0,S.jsx)(vs,{}),path:"/admin/reports",roles:["admin"]},{text:"Settings",icon:(0,S.jsx)(xs,{}),path:"/admin/settings",roles:["admin"]}]:[]]).filter(e=>e.roles.includes((null===c||void 0===c?void 0:c.role)||"")),E=(0,S.jsxs)("div",{children:[(0,S.jsx)(Dt,{children:(0,S.jsx)(qt,{variant:"h6",noWrap:!0,component:"div",children:"Payment Gateway"})}),(0,S.jsx)(Zt,{}),(0,S.jsx)(on,{children:k.map(e=>(0,S.jsx)(yr,{disablePadding:!0,children:(0,S.jsxs)(xr,{selected:u.pathname===e.path,onClick:()=>w(e.path),children:[(0,S.jsx)(Cr,{children:e.icon}),(0,S.jsx)(Nr,{primary:e.text})]})},e.path))})]});return(0,S.jsxs)(Ir,{sx:{display:"flex"},children:[(0,S.jsx)(Vr,{}),(0,S.jsx)(eo,{position:"fixed",sx:{width:{sm:"calc(100% - 240px)"},ml:{sm:"240px"}},children:(0,S.jsxs)(Dt,{children:[(0,S.jsx)(ao,{color:"inherit","aria-label":"open drawer",edge:"start",onClick:y,sx:{mr:2,display:{sm:"none"}},children:(0,S.jsx)(Ss,{})}),(0,S.jsx)(qt,{variant:"h6",noWrap:!0,component:"div",sx:{flexGrow:1},children:a}),(0,S.jsxs)(Ir,{sx:{display:"flex",alignItems:"center"},children:[(0,S.jsx)(Si,{title:"Notifications",children:(0,S.jsx)(ao,{color:"inherit",children:(0,S.jsx)(Ti,{badgeContent:4,color:"secondary",children:(0,S.jsx)(ks,{})})})}),(0,S.jsx)(Si,{title:"Account settings",children:(0,S.jsx)(ao,{onClick:e=>{g(e.currentTarget)},size:"small",sx:{ml:2},"aria-controls":v?"account-menu":void 0,"aria-haspopup":"true","aria-expanded":v?"true":void 0,children:(0,S.jsx)(Ki,{sx:{width:32,height:32},children:(null===c||void 0===c||null===(n=c.name)||void 0===n?void 0:n.charAt(0))||(0,S.jsx)(Es,{})})})})]})]})}),(0,S.jsx)(Ir,{component:"nav",sx:{width:{sm:Cs},flexShrink:{sm:0}},"aria-label":"mailbox folders",children:(0,S.jsx)(Ll,{variant:f?"temporary":"permanent",open:p,onClose:y,ModalProps:{keepMounted:!0},sx:{display:{xs:"block",sm:"block"},"& .MuiDrawer-paper":{boxSizing:"border-box",width:Cs}},children:E})}),(0,S.jsx)(Ps,{sx:{pt:8,px:3,width:{sm:"calc(100% - 240px)"}},children:r}),(0,S.jsxs)(cs,{anchorEl:m,id:"account-menu",open:v,onClose:b,onClick:b,transformOrigin:{horizontal:"right",vertical:"top"},anchorOrigin:{horizontal:"right",vertical:"bottom"},children:[(0,S.jsxs)(ms,{onClick:()=>w("/profile"),children:[(0,S.jsx)(Cr,{children:(0,S.jsx)(Es,{fontSize:"small"})}),"Profile"]}),(0,S.jsx)(Zt,{}),(0,S.jsxs)(ms,{onClick:()=>{b(),d(),s("/login")},children:[(0,S.jsx)(Cr,{children:(0,S.jsx)(As,{fontSize:"small"})}),"Logout"]})]})]})},Os=(0,e.lazy)(()=>Promise.all([n.e(604),n.e(73),n.e(523)]).then(n.bind(n,6523))),Ts=(0,e.lazy)(()=>Promise.all([n.e(604),n.e(73),n.e(790)]).then(n.bind(n,9790))),Ns=(0,e.lazy)(()=>Promise.all([n.e(604),n.e(73),n.e(60)]).then(n.bind(n,9060))),js=(0,e.lazy)(()=>n.e(396).then(n.bind(n,8396))),Ms=(0,e.lazy)(()=>n.e(466).then(n.bind(n,7466))),_s=(0,e.lazy)(()=>Promise.all([n.e(517),n.e(25)]).then(n.bind(n,2025))),Ls=(0,e.lazy)(()=>Promise.all([n.e(141),n.e(975),n.e(604),n.e(693),n.e(720),n.e(620),n.e(168),n.e(400),n.e(293)]).then(n.bind(n,3293))),zs=(0,e.lazy)(()=>Promise.all([n.e(141),n.e(975),n.e(825),n.e(585)]).then(n.bind(n,5585))),Is=(0,e.lazy)(()=>Promise.all([n.e(517),n.e(147)]).then(n.bind(n,9147))),Ds=[{path:"/merchant",element:(0,S.jsx)(ke,{role:"merchant",children:(0,S.jsx)(Rs,{title:"Merchant Dashboard",children:(0,S.jsx)(Os,{})})})},{path:"/merchant/dashboard",element:(0,S.jsx)(ke,{role:"merchant",children:(0,S.jsx)(Rs,{title:"Dashboard",children:(0,S.jsx)(Os,{})})})},{path:"/merchant/transactions",element:(0,S.jsx)(ke,{role:"merchant",children:(0,S.jsx)(Rs,{title:"Transactions",children:(0,S.jsx)(Ts,{})})})},{path:"/merchant/collections",element:(0,S.jsx)(ke,{role:"merchant",children:(0,S.jsx)(Rs,{title:"Collections",children:(0,S.jsx)(Ns,{})})})},{path:"/merchant/billing",element:(0,S.jsx)(ke,{role:"merchant",children:(0,S.jsx)(Rs,{title:"Billing",children:(0,S.jsx)(js,{})})})},{path:"/merchant/traders",element:(0,S.jsx)(ke,{role:"merchant",children:(0,S.jsx)(Rs,{title:"Traders",children:(0,S.jsx)(Ms,{})})})},{path:"/merchant/settings",element:(0,S.jsx)(ke,{role:"merchant",children:(0,S.jsx)(Rs,{title:"Settings",children:(0,S.jsx)(_s,{})})})},{path:"/merchant/profile",element:(0,S.jsx)(ke,{role:"merchant",children:(0,S.jsx)(Rs,{title:"Profile",children:(0,S.jsx)(Ls,{})})})},{path:"/merchant/support",element:(0,S.jsx)(ke,{role:"merchant",children:(0,S.jsx)(Rs,{title:"Support",children:(0,S.jsx)(zs,{})})})},{path:"/merchant/notifications",element:(0,S.jsx)(ke,{role:"merchant",children:(0,S.jsx)(Rs,{title:"Notifications",children:(0,S.jsx)(Is,{})})})}],$s=(0,e.lazy)(()=>n.e(102).then(n.bind(n,8102))),Fs=(0,e.lazy)(()=>n.e(197).then(n.bind(n,1197))),Bs=(0,e.lazy)(()=>Promise.all([n.e(141),n.e(975),n.e(693),n.e(842),n.e(825),n.e(280),n.e(337)]).then(n.bind(n,3337))),Us=(0,e.lazy)(()=>Promise.all([n.e(842),n.e(280),n.e(72)]).then(n.bind(n,5595))),Ws=(0,e.lazy)(()=>Promise.all([n.e(141),n.e(975),n.e(825),n.e(284)]).then(n.bind(n,284))),Vs=(0,e.lazy)(()=>Promise.all([n.e(141),n.e(975),n.e(604),n.e(693),n.e(842),n.e(620),n.e(836)]).then(n.bind(n,6836))),Hs=(0,e.lazy)(()=>Promise.all([n.e(141),n.e(604),n.e(693),n.e(842),n.e(720),n.e(236)]).then(n.bind(n,5236))),qs=(0,e.lazy)(()=>n.e(15).then(n.bind(n,3015))),Ks=(0,e.lazy)(()=>n.e(691).then(n.bind(n,8691))),Gs=(0,e.lazy)(()=>n.e(829).then(n.bind(n,3829))),Xs=()=>(0,S.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,S.jsx)(Ee,{size:"lg"})}),Qs=e=>{let{children:t}=e;return(0,S.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,S.jsxs)("div",{className:"text-center",children:[(0,S.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"Something went wrong"}),(0,S.jsx)("button",{className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",onClick:()=>window.location.reload(),children:"Reload Page"})]})})},Ys=[{path:"/",element:(0,S.jsx)(o.C5,{to:"/login",replace:!0})},{path:"/login",element:(0,S.jsx)(e.Suspense,{fallback:(0,S.jsx)(Xs,{}),children:(0,S.jsx)(Qs,{children:(0,S.jsx)($s,{})})})},{path:"/register",element:(0,S.jsx)(e.Suspense,{fallback:(0,S.jsx)(Xs,{}),children:(0,S.jsx)(Qs,{children:(0,S.jsx)(Fs,{})})})},{path:"/payment/:merchantId",element:(0,S.jsx)(e.Suspense,{fallback:(0,S.jsx)(Xs,{}),children:(0,S.jsx)(Qs,{children:(0,S.jsx)(Ws,{})})})},...Ds,{path:"/admin",element:(0,S.jsx)(ke,{role:"admin",children:(0,S.jsx)(Rs,{title:"Admin Dashboard",children:(0,S.jsx)(e.Suspense,{fallback:(0,S.jsx)(Xs,{}),children:(0,S.jsx)(Qs,{children:(0,S.jsx)(Bs,{})})})})})},{path:"/admin/merchants",element:(0,S.jsx)(ke,{role:"admin",children:(0,S.jsx)(Rs,{title:"Merchants Management",children:(0,S.jsx)(e.Suspense,{fallback:(0,S.jsx)(Xs,{}),children:(0,S.jsx)(Qs,{children:(0,S.jsx)(Vs,{})})})})})},{path:"/admin/traders",element:(0,S.jsx)(ke,{role:"admin",children:(0,S.jsx)(Rs,{title:"Traders Management",children:(0,S.jsx)(e.Suspense,{fallback:(0,S.jsx)(Xs,{}),children:(0,S.jsx)(Qs,{children:(0,S.jsx)(Hs,{})})})})})},{path:"/admin/payments",element:(0,S.jsx)(ke,{role:"admin",children:(0,S.jsx)(Rs,{title:"Payments Overview",children:(0,S.jsx)(e.Suspense,{fallback:(0,S.jsx)(Xs,{}),children:(0,S.jsx)(Qs,{children:(0,S.jsx)(qs,{})})})})})},{path:"/admin/reports",element:(0,S.jsx)(ke,{role:"admin",children:(0,S.jsx)(Rs,{title:"Reports & Analytics",children:(0,S.jsx)(e.Suspense,{fallback:(0,S.jsx)(Xs,{}),children:(0,S.jsx)(Qs,{children:(0,S.jsx)(Ks,{})})})})})},{path:"/admin/settings",element:(0,S.jsx)(ke,{role:"admin",children:(0,S.jsx)(Rs,{title:"System Settings",children:(0,S.jsx)(e.Suspense,{fallback:(0,S.jsx)(Xs,{}),children:(0,S.jsx)(Qs,{children:(0,S.jsx)(Gs,{})})})})})},{path:"/trader",element:(0,S.jsx)(ke,{role:"trader",children:(0,S.jsx)(Rs,{title:"Trader Dashboard",children:(0,S.jsx)(e.Suspense,{fallback:(0,S.jsx)(Xs,{}),children:(0,S.jsx)(Qs,{children:(0,S.jsx)(Us,{})})})})})},{path:"*",element:(0,S.jsx)(o.C5,{to:"/login",replace:!0})}],Js=()=>(0,o.Ye)(Ys),Zs=e=>{let{children:t}=e;return(0,S.jsx)(Se,{fallback:(0,S.jsx)("div",{className:"flex items-center justify-center min-h-screen bg-gray-100",children:(0,S.jsxs)("div",{className:"p-8 bg-white rounded-lg shadow-md",children:[(0,S.jsx)("h2",{className:"text-2xl font-bold text-red-600 mb-4",children:"Something went wrong"}),(0,S.jsx)("p",{className:"text-gray-700",children:"Please refresh the page or try again later."})]})}),children:t})};const eu=function(){return(0,S.jsx)(e.StrictMode,{children:(0,S.jsx)(Zs,{children:(0,S.jsx)(a.Kd,{children:(0,S.jsx)(xe,{children:(0,S.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,S.jsx)(Zs,{children:(0,S.jsx)(Js,{})})})})})})})};var tu=n(6213);tu.A.defaults.baseURL="http://localhost:5001/api",tu.A.defaults.timeout=1e4,tu.A.interceptors.request.use(e=>{const t=localStorage.getItem("token");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e)),tu.A.interceptors.response.use(e=>e,e=>{var t;return 401===(null===(t=e.response)||void 0===t?void 0:t.status)&&(localStorage.removeItem("token"),window.location.href="/login"),Promise.reject(e)});r.createRoot(document.getElementById("root")).render((0,S.jsx)(e.StrictMode,{children:(0,S.jsx)(eu,{})}))})()})();
//# sourceMappingURL=main.4143f598.js.map