import { useState } from 'react';
import { useQuery, UseQueryResult } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../../components/ui/dropdown-menu';
import { Badge } from '../../components/ui/badge';
import { MoreHorizontal, Search, Loader2 } from 'lucide-react';
import { merchantAdminService } from '../../services/merchantService';
import { MerchantListResponse, MerchantListItem, MerchantStatusUpdate } from '../../types/merchant';
import { toast } from '../../components/ui/use-toast';
import { format } from 'date-fns';

const statusVariant: Record<string, 'default' | 'secondary' | 'destructive' | 'outline'> = {
  verified: 'default',
  pending: 'secondary',
  rejected: 'destructive',
  suspended: 'outline',
  draft: 'outline',
};

export function MerchantsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(1);
  const limit = 10;

  const { 
    data: merchantsData, 
    isLoading, 
    error, 
    refetch 
  } = useQuery<MerchantListResponse>({
    queryKey: ['merchants', { page, limit, search: searchTerm }],
    queryFn: () => merchantAdminService.getAllMerchants({ page, limit, search: searchTerm })
  });

  const handleStatusUpdate = async (id: string, status: MerchantStatusUpdate) => {
    try {
      await merchantAdminService.updateMerchantStatus(id, status.status, status.reason);
      toast({
        title: 'Status updated',
        description: 'Merchant status has been updated successfully.'
      });
      refetch();
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update merchant status. Please try again.',
        variant: 'destructive',
      });
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="w-8 h-8 animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-10">
        <h3 className="text-lg font-medium mb-2">Failed to load merchants</h3>
        <p className="text-muted-foreground mb-4">Please try again later.</p>
        <Button onClick={() => refetch()}>Retry</Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Merchants Management</h2>
      </div>
      
      <Card>
        <CardHeader className="pb-0">
          <div className="flex items-center justify-between">
            <CardTitle>All Merchants</CardTitle>
            <div className="relative w-64">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search merchants..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Business Name</TableHead>
                <TableHead>Contact Email</TableHead>
                <TableHead>Phone</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Created At</TableHead>
                <TableHead className="w-[100px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {merchantsData?.data.map((merchant: MerchantListItem) => (
                <TableRow key={merchant.id}>
                  <TableCell className="font-medium">{merchant.businessName}</TableCell>
                  <TableCell>{merchant.contactEmail}</TableCell>
                  <TableCell>{merchant.contactPhone}</TableCell>
                  <TableCell>
                    <Badge variant={statusVariant[merchant.status]}>
                      {merchant.status.charAt(0).toUpperCase() + merchant.status.slice(1)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {format(new Date(merchant.createdAt), 'MMM d, yyyy')}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() => handleStatusUpdate(merchant.id, { status: 'verified' })}
                          disabled={merchant.status === 'verified'}
                        >
                          Mark as Verified
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleStatusUpdate(merchant.id, { status: 'suspended' })}
                          disabled={merchant.status === 'suspended'}
                        >
                          Suspend Account
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleStatusUpdate(merchant.id, { status: 'rejected', reason: 'Business verification failed' })}
                          disabled={merchant.status === 'rejected'}
                          className="text-destructive"
                        >
                          Reject
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          
          {merchantsData?.data.length === 0 && (
            <div className="py-8 text-center text-muted-foreground">
              No merchants found
            </div>
          )}
          
          <div className="flex items-center justify-end space-x-2 p-4 border-t">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(p => Math.max(1, p - 1))}
              disabled={page === 1}
            >
              Previous
            </Button>
            <span className="text-sm text-muted-foreground">
              Page {page} of {merchantsData?.totalPages || 1}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(p => p + 1)}
              disabled={page >= (merchantsData?.totalPages || 1)}
            >
              Next
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default MerchantsPage;
