const crypto = require('crypto');
const axios = require('axios');
const { WebhookError } = require('./webhook.errors');
const { getDatabase } = require('../../config/database');

class WebhookService {
  constructor() {
    this.retryDelays = [5000, 15000, 30000]; // 5s, 15s, 30s
    this.timeout = 10000; // 10 seconds
  }

  /**
   * Verify webhook signature
   */
  verifySignature(signature, payload, secret) {
    if (!signature || !secret) {
      throw new WebhookError('Missing signature or secret', 401);
    }

    const [timestamp, signatureHash] = signature.split(',').map(part => part.split('=')[1]);
    
    // Verify timestamp (prevent replay attacks)
    const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
    if (parseInt(timestamp) < fiveMinutesAgo) {
      throw new WebhookError('Signature expired', 401);
    }

    // Create signature
    const expectedSignature = crypto
      .createHmac('sha256', secret)
      .update(`${timestamp}.${JSON.stringify(payload)}`)
      .digest('hex');

    // Compare signatures
    if (!crypto.timingSafeEqual(
      Buffer.from(signatureHash, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    )) {
      throw new WebhookError('Invalid signature', 401);
    }

    return true;
  }

  /**
   * Find webhook configuration by ID
   */
  async findWebhookConfig(webhookId) {
    const db = await getDatabase();
    return await db.collection('webhooks').findOne({ webhookId });
  }

  /**
   * Process incoming webhook event
   */
  async processIncomingEvent(webhookId, event, signature) {
    const db = await getDatabase();
    const webhook = await this.findWebhookConfig(webhookId);
    
    if (!webhook) {
      throw new WebhookError('Webhook not found', 404);
    }

    // Verify signature
    this.verifySignature(signature, event, webhook.secret);

    // Store the incoming event
    const eventDoc = {
      eventId: `evt_${crypto.randomBytes(8).toString('hex')}`,
      webhookId: webhook.webhookId,
      merchantId: webhook.merchantId,
      type: event.type || 'unknown',
      payload: event,
      status: 'received',
      receivedAt: new Date(),
      metadata: {
        ip: event.metadata?.ip || null,
        userAgent: event.metadata?.userAgent || null,
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const result = await db.collection('webhook_events').insertOne(eventDoc);
    
    // Process the event asynchronously
    this.processEventAsync(webhook, eventDoc);

    return { ...eventDoc, _id: result.insertedId };
  }

  /**
   * Process webhook event asynchronously
   */
  async processEventAsync(webhook, eventDoc) {
    const db = await getDatabase();
    
    try {
      // Update event status to processing
      await db.collection('webhook_events').updateOne(
        { _id: eventDoc._id },
        { $set: { status: 'processing', updatedAt: new Date() } }
      );

      // Deliver webhook with retry logic
      await this.deliverWithRetry(webhook, eventDoc);

      // Update event status to delivered
      await db.collection('webhook_events').updateOne(
        { _id: eventDoc._id },
        { 
          $set: { 
            status: 'delivered',
            deliveredAt: new Date(),
            updatedAt: new Date() 
          } 
        }
      );
    } catch (error) {
      console.error('Error processing webhook event:', error);
      
      // Update event status to failed
      await db.collection('webhook_events').updateOne(
        { _id: eventDoc._id },
        { 
          $set: { 
            status: 'failed',
            error: error.message,
            updatedAt: new Date() 
          } 
        }
      );
    }
  }

  /**
   * Deliver webhook with retry logic
   */
  async deliverWithRetry(webhook, eventDoc, attempt = 0) {
    const db = await getDatabase();
    
    try {
      const response = await axios({
        method: 'POST',
        url: webhook.url,
        data: {
          id: eventDoc.eventId,
          type: eventDoc.type,
          created: Math.floor(Date.now() / 1000),
          data: eventDoc.payload,
          previous_attributes: eventDoc.previousAttributes
        },
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'PaymentGateway/1.0',
          'X-Webhook-Signature': this.generateSignature(webhook.secret, eventDoc)
        },
        timeout: this.timeout
      });

      // Log the delivery
      await this.logDelivery(webhook, eventDoc, {
        status: 'delivered',
        statusCode: response.status,
        response: response.data,
        attempt: attempt + 1
      });

      return response.data;
    } catch (error) {
      const statusCode = error.response?.status || 0;
      const responseData = error.response?.data || error.message;
      
      // Log the failed delivery
      await this.logDelivery(webhook, eventDoc, {
        status: 'failed',
        statusCode,
        response: responseData,
        error: error.message,
        attempt: attempt + 1
      });

      // Check if we should retry
      if (attempt < this.retryDelays.length) {
        const delay = this.retryDelays[attempt];
        console.log(`Retrying webhook in ${delay}ms (attempt ${attempt + 1})`);
        
        // Schedule retry
        await new Promise(resolve => setTimeout(resolve, delay));
        return this.deliverWithRetry(webhook, eventDoc, attempt + 1);
      }

      throw new WebhookError(`Failed to deliver webhook after ${attempt + 1} attempts: ${error.message}`);
    }
  }

  /**
   * Generate webhook signature
   */
  generateSignature(secret, event) {
    const timestamp = Math.floor(Date.now() / 1000);
    const payload = `${timestamp}.${JSON.stringify(event)}`;
    const signature = crypto
      .createHmac('sha256', secret)
      .update(payload)
      .digest('hex');
    
    return `t=${timestamp},v1=${signature}`;
  }

  /**
   * Log webhook delivery attempt
   */
  async logDelivery(webhook, eventDoc, { status, statusCode, response, error, attempt }) {
    const db = await getDatabase();
    
    const log = {
      webhookLogId: `whlog_${crypto.randomBytes(8).toString('hex')}`,
      webhookId: webhook.webhookId,
      eventId: eventDoc.eventId,
      merchantId: webhook.merchantId,
      status,
      statusCode,
      attempt,
      url: webhook.url,
      eventType: eventDoc.type,
      request: {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'PaymentGateway/1.0'
        },
        body: JSON.stringify(eventDoc.payload, null, 2)
      },
      response: {
        statusCode,
        body: response ? JSON.stringify(response, null, 2) : null,
        error: error || null
      },
      processingTime: Date.now() - eventDoc.createdAt,
      metadata: {
        ip: eventDoc.metadata?.ip || null,
        userAgent: eventDoc.metadata?.userAgent || null
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await db.collection('webhooklogs').insertOne(log);
    return log;
  }

  /**
   * List webhook events with pagination
   */
  async listEvents(merchantId, { limit = 50, offset = 0, status, type } = {}) {
    const db = await getDatabase();
    
    const query = { merchantId };
    if (status) query.status = status;
    if (type) query.type = type;
    
    const [events, total] = await Promise.all([
      db.collection('webhook_events')
        .find(query)
        .sort({ createdAt: -1 })
        .skip(offset)
        .limit(limit)
        .toArray(),
      db.collection('webhook_events').countDocuments(query)
    ]);

    return {
      data: events,
      pagination: {
        total,
        limit,
        offset,
        hasMore: offset + events.length < total
      }
    };
  }

  /**
   * Get webhook event by ID
   */
  async getEventById(eventId, merchantId) {
    const db = await getDatabase();
    return await db.collection('webhook_events').findOne({
      eventId,
      merchantId
    });
  }

  /**
   * Retry failed webhook delivery
   */
  async retryEvent(eventId, merchantId) {
    const db = await getDatabase();
    const event = await db.collection('webhook_events').findOne({
      eventId,
      merchantId,
      status: 'failed'
    });

    if (!event) {
      throw new WebhookError('Event not found or not eligible for retry', 404);
    }

    const webhook = await this.findWebhookConfig(event.webhookId);
    if (!webhook) {
      throw new WebhookError('Webhook configuration not found', 404);
    }

    // Reset event status and retry
    await db.collection('webhook_events').updateOne(
      { _id: event._id },
      { $set: { status: 'pending', updatedAt: new Date() } }
    );

    // Process the event again
    this.processEventAsync(webhook, event);

    return { success: true, message: 'Webhook event queued for retry' };
  }
}

module.exports = new WebhookService();
