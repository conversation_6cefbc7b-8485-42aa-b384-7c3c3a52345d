"use strict";(self.webpackChunkpayment_gateway_frontend=self.webpackChunkpayment_gateway_frontend||[]).push([[337,508],{382:(e,t,a)=>{a.d(t,{A0:()=>o,BF:()=>l,Hj:()=>d,XI:()=>i,nA:()=>u,nd:()=>c});var s=a(5043),r=a(3009),n=a(579);const i=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("div",{className:"w-full overflow-auto",children:(0,n.jsx)("table",{ref:t,className:(0,r.cn)("w-full caption-bottom text-sm",a),...s})})});i.displayName="Table";const o=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("thead",{ref:t,className:(0,r.cn)("[&_tr]:border-b",a),...s})});o.displayName="TableHeader";const l=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("tbody",{ref:t,className:(0,r.cn)("[&_tr:last-child]:border-0",a),...s})});l.displayName="TableBody";s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("tfoot",{ref:t,className:(0,r.cn)("bg-primary font-medium text-primary-foreground",a),...s})}).displayName="TableFooter";const d=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("tr",{ref:t,className:(0,r.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",a),...s})});d.displayName="TableRow";const c=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("th",{ref:t,className:(0,r.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",a),...s})});c.displayName="TableHead";const u=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("td",{ref:t,className:(0,r.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",a),...s})});u.displayName="TableCell";s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("caption",{ref:t,className:(0,r.cn)("mt-4 text-sm text-muted-foreground",a),...s})}).displayName="TableCaption"},492:(e,t,a)=>{a.d(t,{Cf:()=>m,Es:()=>p,L3:()=>x,c7:()=>f,lG:()=>l,rr:()=>h,zM:()=>d});var s=a(5043),r=a(5179),n=a(1172),i=a(3009),o=a(579);const l=r.bL,d=r.l9,c=e=>{let{className:t,children:a,...s}=e;const{container:n,forceMount:l,...d}=s,c={container:n,forceMount:l};return(0,o.jsx)(r.ZL,{...c,children:(0,o.jsx)("div",{className:(0,i.cn)("fixed inset-0 z-50 flex items-start justify-center sm:items-center",t),children:a})})};c.displayName=r.ZL.displayName;const u=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,o.jsx)(r.hJ,{ref:t,className:(0,i.cn)("fixed inset-0 z-50 bg-black/50 backdrop-blur-sm transition-all duration-100 data-[state=closed]:animate-out data-[state=closed]:fade-out data-[state=open]:fade-in",a),...s})});u.displayName=r.hJ.displayName;const m=s.forwardRef((e,t)=>{let{className:a,children:s,...l}=e;return(0,o.jsxs)(c,{children:[(0,o.jsx)(u,{}),(0,o.jsxs)(r.UC,{ref:t,className:(0,i.cn)("fixed z-50 grid w-full gap-4 rounded-b-lg bg-white p-6 animate-in data-[state=open]:fade-in-90 data-[state=open]:slide-in-from-bottom-10 sm:max-w-lg sm:rounded-lg sm:zoom-in-90 data-[state=open]:sm:slide-in-from-bottom-0","dark:bg-gray-900",a),...l,children:[s,(0,o.jsxs)(r.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-gray-100 dark:focus:ring-gray-400 dark:focus:ring-offset-gray-900 dark:data-[state=open]:bg-gray-800",children:[(0,o.jsx)(n.A,{className:"h-4 w-4"}),(0,o.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});m.displayName=r.UC.displayName;const f=e=>{let{className:t,...a}=e;return(0,o.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-2 text-center sm:text-left",t),...a})};f.displayName="DialogHeader";const p=e=>{let{className:t,...a}=e;return(0,o.jsx)("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...a})};p.displayName="DialogFooter";const x=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,o.jsx)(r.hE,{ref:t,className:(0,i.cn)("text-lg font-semibold text-gray-900","dark:text-gray-50",a),...s})});x.displayName=r.hE.displayName;const h=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,o.jsx)(r.VY,{ref:t,className:(0,i.cn)("text-sm text-gray-500","dark:text-gray-400",a),...s})});h.displayName=r.VY.displayName},2248:(e,t,a)=>{a.d(t,{J:()=>l});var s=a(5043),r=a(917),n=a(3009),i=a(579);const o=(0,r.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,i.jsx)("label",{ref:t,className:(0,n.cn)(o(),a),...s})});l.displayName="Label"},2417:(e,t,a)=>{a.d(t,{E:()=>n});a(5043);var s=a(3009),r=a(579);function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{className:(0,s.cn)("animate-pulse rounded-md bg-gray-100 dark:bg-gray-800",t),...a})}},2699:(e,t,a)=>{a.d(t,{A:()=>s});const s=(0,a(3797).A)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z",key:"3xmgem"}]])},3337:(e,t,a)=>{a.r(t),a.d(t,{default:()=>I});var s=a(5043),r=a(9066),n=a(3216),i=a(9772),o=a(6742),l=a(382),d=a(6736),c=a(2417),u=a(108),m=a(8420),f=a(8763),p=a(2185),x=a(6026),h=a(3831),g=(0,m.gu)({chartName:"BarChart",GraphicalChild:f.y,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:p.W},{axisType:"yAxis",AxisComp:x.h}],formatAxisMap:h.pr}),y=a(7734),j=a(6150),v=a(1327),N=a(8112),b=a(5722),w=a(9120);const A=(0,a(3797).A)("UserCheck",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]]);var D=a(4068),T=a(9445),R=a(2699),C=a(9508),S=a(6879),k=a(579);const M=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD";return new Intl.NumberFormat("en-US",{style:"currency",currency:t,minimumFractionDigits:2,maximumFractionDigits:2}).format(e)},I=()=>{const{user:e,logout:t}=(0,r.A)(),a=(0,n.Zp)(),{toast:m}=(0,S.dj)(),[h,I]=(0,s.useState)(!0),[E,_]=(0,s.useState)({totalUsers:0,totalTransactions:0,totalRevenue:0,activeMerchants:0}),[F,O]=(0,s.useState)([]),[U,L]=(0,s.useState)([]),$=[{name:"Total Users",value:E.totalUsers.toLocaleString(),icon:N.A,description:"Total registered users",change:"+12.5%",trend:"up"},{name:"Total Transactions",value:E.totalTransactions.toLocaleString(),icon:b.A,description:"All-time transactions",change:"+8.2%",trend:"up"},{name:"Total Revenue",value:M(E.totalRevenue),icon:w.A,description:"Total revenue generated",change:"+15.3%",trend:"up"},{name:"Active Merchants",value:E.activeMerchants.toString(),icon:A,description:"Currently active merchants",change:"+4.1%",trend:"up"}],B=async()=>{try{I(!0),await new Promise(e=>setTimeout(e,1e3)),_({totalUsers:1245,totalTransactions:12478,totalRevenue:124560.78,activeMerchants:92}),O([{id:"TXN"+Math.floor(1e6*Math.random()),date:(new Date).toISOString(),merchant:"Test Merchant",amount:100.5,status:"completed",currency:"USD"},{id:"TXN"+Math.floor(1e6*Math.random()),date:new Date(Date.now()-864e5).toISOString(),merchant:"Another Business",amount:75.25,status:"pending",currency:"USD"},{id:"TXN"+Math.floor(1e6*Math.random()),date:new Date(Date.now()-1728e5).toISOString(),merchant:"Online Store",amount:200,status:"completed",currency:"USD"}]),L([{name:"Jan",revenue:4e3},{name:"Feb",revenue:3e3},{name:"Mar",revenue:5e3},{name:"Apr",revenue:4780},{name:"May",revenue:3890},{name:"Jun",revenue:6390}])}catch(e){console.error("Error fetching dashboard data:",e),m({title:"Error",description:"Failed to load dashboard data. Please try again.",variant:"destructive"})}finally{I(!1)}};(0,s.useEffect)(()=>{B();const e=setInterval(B,3e5);return()=>clearInterval(e)},[]);return(0,k.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,k.jsx)("header",{className:"bg-white shadow",children:(0,k.jsxs)("div",{className:"max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8 flex justify-between items-center",children:[(0,k.jsxs)("div",{children:[(0,k.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Admin Dashboard"}),(0,k.jsxs)("p",{className:"text-sm text-gray-500",children:["Welcome back, ",(null===e||void 0===e?void 0:e.name)||"Admin"]})]}),(0,k.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,k.jsxs)(i.$,{variant:"outline",size:"sm",onClick:B,disabled:h,children:[(0,k.jsx)(D.A,{className:"h-4 w-4 mr-2 "+(h?"animate-spin":"")}),"Refresh"]}),(0,k.jsxs)(i.$,{variant:"outline",size:"sm",onClick:async()=>{try{await t(),a("/login")}catch(e){console.error("Logout error:",e),m({title:"Error",description:"Failed to log out. Please try again.",variant:"destructive"})}},children:[(0,k.jsx)(T.A,{className:"h-4 w-4 mr-2"}),"Logout"]})]})]})}),(0,k.jsxs)("main",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:[(0,k.jsx)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-8",children:h?Array(4).fill(0).map((e,t)=>(0,k.jsxs)(o.Zp,{children:[(0,k.jsxs)(o.aR,{className:"space-y-2",children:[(0,k.jsx)(c.E,{className:"h-4 w-24"}),(0,k.jsx)(c.E,{className:"h-6 w-16"})]}),(0,k.jsx)(o.Wu,{children:(0,k.jsx)(c.E,{className:"h-4 w-32"})})]},`stat-skeleton-${t}`)):$.map(e=>(0,k.jsxs)(o.Zp,{className:"hover:shadow-md transition-shadow",children:[(0,k.jsx)(o.aR,{className:"pb-2",children:(0,k.jsxs)("div",{className:"flex justify-between items-center",children:[(0,k.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:e.name}),(0,k.jsx)(e.icon,{className:"h-4 w-4 text-muted-foreground"})]})}),(0,k.jsxs)(o.Wu,{children:[(0,k.jsx)("div",{className:"text-2xl font-bold",children:e.value}),(0,k.jsxs)("p",{className:"text-xs text-muted-foreground mt-1",children:[(0,k.jsx)("span",{className:"up"===e.trend?"text-green-500":"text-red-500",children:e.change})," ",e.description]})]})]},e.name))}),(0,k.jsxs)(d.tU,{defaultValue:"overview",className:"space-y-4",children:[(0,k.jsxs)("div",{className:"flex justify-between items-center",children:[(0,k.jsxs)(d.j7,{children:[(0,k.jsx)(d.Xi,{value:"overview",children:"Overview"}),(0,k.jsx)(d.Xi,{value:"transactions",children:"Transactions"}),(0,k.jsx)(d.Xi,{value:"traders",children:"Traders"}),(0,k.jsxs)(d.Xi,{value:"security",children:[(0,k.jsx)(R.A,{className:"h-4 w-4 mr-2"}),"Security"]})]}),(0,k.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Last updated: ",(new Date).toLocaleTimeString()]})]}),(0,k.jsx)(d.av,{value:"overview",className:"space-y-4",children:(0,k.jsxs)(o.Zp,{children:[(0,k.jsxs)(o.aR,{children:[(0,k.jsx)(o.ZB,{children:"Revenue Overview"}),(0,k.jsx)(o.BT,{children:"Monthly revenue trends and analytics"})]}),(0,k.jsx)(o.Wu,{className:"pl-2",children:h?(0,k.jsx)("div",{className:"h-[300px] flex items-center justify-center",children:(0,k.jsx)(D.A,{className:"h-8 w-8 animate-spin text-muted-foreground"})}):(0,k.jsx)("div",{className:"h-[300px]",children:(0,k.jsx)(u.u,{width:"100%",height:"100%",children:(0,k.jsxs)(g,{data:U,children:[(0,k.jsx)(y.d,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),(0,k.jsx)(p.W,{dataKey:"name",tick:{fill:"#6b7280"},axisLine:!1}),(0,k.jsx)(x.h,{tick:{fill:"#6b7280"},axisLine:!1,tickFormatter:e=>`$${e.toLocaleString()}`}),(0,k.jsx)(j.m,{formatter:e=>[`$${e.toLocaleString()}`,"Revenue"],labelFormatter:e=>`Month: ${e}`}),(0,k.jsx)(v.s,{}),(0,k.jsx)(f.y,{dataKey:"revenue",name:"Monthly Revenue",fill:"#6366f1",radius:[4,4,0,0]})]})})})})]})}),(0,k.jsx)(d.av,{value:"transactions",children:(0,k.jsxs)(o.Zp,{children:[(0,k.jsx)(o.aR,{children:(0,k.jsxs)("div",{className:"flex justify-between items-center",children:[(0,k.jsxs)("div",{children:[(0,k.jsx)(o.ZB,{children:"Recent Transactions"}),(0,k.jsx)(o.BT,{children:"Latest transactions across all merchants"})]}),(0,k.jsxs)(i.$,{variant:"outline",size:"sm",onClick:B,children:[(0,k.jsx)(D.A,{className:"h-4 w-4 mr-2 "+(h?"animate-spin":"")}),"Refresh"]})]})}),(0,k.jsx)(o.Wu,{children:h?(0,k.jsx)("div",{className:"space-y-4",children:Array(5).fill(0).map((e,t)=>(0,k.jsx)(c.E,{className:"h-12 w-full"},`txn-skeleton-${t}`))}):(0,k.jsx)("div",{className:"rounded-md border",children:(0,k.jsxs)(l.XI,{children:[(0,k.jsx)(l.A0,{children:(0,k.jsxs)(l.Hj,{children:[(0,k.jsx)(l.nd,{children:"ID"}),(0,k.jsx)(l.nd,{children:"Date & Time"}),(0,k.jsx)(l.nd,{children:"Merchant"}),(0,k.jsx)(l.nd,{className:"text-right",children:"Amount"}),(0,k.jsx)(l.nd,{children:"Status"})]})}),(0,k.jsx)(l.BF,{children:F.length>0?F.map(e=>{return(0,k.jsxs)(l.Hj,{className:"hover:bg-gray-50",children:[(0,k.jsx)(l.nA,{className:"font-medium",children:(0,k.jsx)("span",{className:"font-mono text-xs",children:e.id})}),(0,k.jsx)(l.nA,{children:(0,k.jsx)("div",{className:"text-sm",children:(t=e.date,new Date(t).toLocaleString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}))})}),(0,k.jsx)(l.nA,{children:(0,k.jsx)("div",{className:"font-medium",children:e.merchant})}),(0,k.jsx)(l.nA,{className:"text-right font-medium",children:M(e.amount,e.currency)}),(0,k.jsx)(l.nA,{children:(0,k.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium "+("completed"===e.status?"bg-green-100 text-green-800":"pending"===e.status?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"),children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})})]},e.id);var t}):(0,k.jsx)(l.Hj,{children:(0,k.jsx)(l.nA,{colSpan:5,className:"text-center py-8 text-muted-foreground",children:"No transactions found"})})})]})})})]})}),(0,k.jsx)(d.av,{value:"traders",children:(0,k.jsxs)(o.Zp,{children:[(0,k.jsxs)(o.aR,{children:[(0,k.jsx)(o.ZB,{children:"Trader Management"}),(0,k.jsx)(o.BT,{children:"Manage traders and their assignments"})]}),(0,k.jsx)(o.Wu,{children:(0,k.jsx)(C.TraderManagement,{})})]})}),(0,k.jsx)(d.av,{value:"security",children:(0,k.jsxs)(o.Zp,{children:[(0,k.jsxs)(o.aR,{children:[(0,k.jsx)(o.ZB,{children:"Security Settings"}),(0,k.jsx)(o.BT,{children:"Manage security settings and access controls"})]}),(0,k.jsxs)(o.Wu,{className:"space-y-4",children:[(0,k.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,k.jsx)("h3",{className:"font-medium mb-2",children:"Two-Factor Authentication"}),(0,k.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"Add an extra layer of security to your admin account"}),(0,k.jsx)(i.$,{variant:"outline",size:"sm",children:"Enable 2FA"})]}),(0,k.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,k.jsx)("h3",{className:"font-medium mb-2",children:"Session Management"}),(0,k.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"View and manage active sessions"}),(0,k.jsx)(i.$,{variant:"outline",size:"sm",children:"View Active Sessions"})]})]})]})})]})]})]})}},4068:(e,t,a)=>{a.d(t,{A:()=>s});const s=(0,a(3797).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},5179:(e,t,a)=>{a.d(t,{UC:()=>ae,VY:()=>re,ZL:()=>ee,bL:()=>K,bm:()=>ne,hE:()=>se,hJ:()=>te,l9:()=>Q});var s=a(5043),r=a(858),n=a(2814),i=a(1862),o=a(4490),l=a(3642),d=a(1184),c=a(276),u=a(3321),m=a(2894),f=a(7920),p=a(6590),x=a(4064),h=a(5754),g=a(6851),y=a(579),j="Dialog",[v,N]=(0,i.A)(j),[b,w]=v(j),A=e=>{const{__scopeDialog:t,children:a,open:r,defaultOpen:n,onOpenChange:i,modal:d=!0}=e,c=s.useRef(null),u=s.useRef(null),[m,f]=(0,l.i)({prop:r,defaultProp:n??!1,onChange:i,caller:j});return(0,y.jsx)(b,{scope:t,triggerRef:c,contentRef:u,contentId:(0,o.B)(),titleId:(0,o.B)(),descriptionId:(0,o.B)(),open:m,onOpenChange:f,onOpenToggle:s.useCallback(()=>f(e=>!e),[f]),modal:d,children:a})};A.displayName=j;var D="DialogTrigger",T=s.forwardRef((e,t)=>{const{__scopeDialog:a,...s}=e,i=w(D,a),o=(0,n.s)(t,i.triggerRef);return(0,y.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":V(i.open),...s,ref:o,onClick:(0,r.m)(e.onClick,i.onOpenToggle)})});T.displayName=D;var R="DialogPortal",[C,S]=v(R,{forceMount:void 0}),k=e=>{const{__scopeDialog:t,forceMount:a,children:r,container:n}=e,i=w(R,t);return(0,y.jsx)(C,{scope:t,forceMount:a,children:s.Children.map(r,e=>(0,y.jsx)(m.C,{present:a||i.open,children:(0,y.jsx)(u.Z,{asChild:!0,container:n,children:e})}))})};k.displayName=R;var M="DialogOverlay",I=s.forwardRef((e,t)=>{const a=S(M,e.__scopeDialog),{forceMount:s=a.forceMount,...r}=e,n=w(M,e.__scopeDialog);return n.modal?(0,y.jsx)(m.C,{present:s||n.open,children:(0,y.jsx)(_,{...r,ref:t})}):null});I.displayName=M;var E=(0,g.TL)("DialogOverlay.RemoveScroll"),_=s.forwardRef((e,t)=>{const{__scopeDialog:a,...s}=e,r=w(M,a);return(0,y.jsx)(x.A,{as:E,allowPinchZoom:!0,shards:[r.contentRef],children:(0,y.jsx)(f.sG.div,{"data-state":V(r.open),...s,ref:t,style:{pointerEvents:"auto",...s.style}})})}),F="DialogContent",O=s.forwardRef((e,t)=>{const a=S(F,e.__scopeDialog),{forceMount:s=a.forceMount,...r}=e,n=w(F,e.__scopeDialog);return(0,y.jsx)(m.C,{present:s||n.open,children:n.modal?(0,y.jsx)(U,{...r,ref:t}):(0,y.jsx)(L,{...r,ref:t})})});O.displayName=F;var U=s.forwardRef((e,t)=>{const a=w(F,e.__scopeDialog),i=s.useRef(null),o=(0,n.s)(t,a.contentRef,i);return s.useEffect(()=>{const e=i.current;if(e)return(0,h.Eq)(e)},[]),(0,y.jsx)($,{...e,ref:o,trapFocus:a.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,r.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),a.triggerRef.current?.focus()}),onPointerDownOutside:(0,r.m)(e.onPointerDownOutside,e=>{const t=e.detail.originalEvent,a=0===t.button&&!0===t.ctrlKey;(2===t.button||a)&&e.preventDefault()}),onFocusOutside:(0,r.m)(e.onFocusOutside,e=>e.preventDefault())})}),L=s.forwardRef((e,t)=>{const a=w(F,e.__scopeDialog),r=s.useRef(!1),n=s.useRef(!1);return(0,y.jsx)($,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(r.current||a.triggerRef.current?.focus(),t.preventDefault()),r.current=!1,n.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(r.current=!0,"pointerdown"===t.detail.originalEvent.type&&(n.current=!0));const s=t.target,i=a.triggerRef.current?.contains(s);i&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&n.current&&t.preventDefault()}})}),$=s.forwardRef((e,t)=>{const{__scopeDialog:a,trapFocus:r,onOpenAutoFocus:i,onCloseAutoFocus:o,...l}=e,u=w(F,a),m=s.useRef(null),f=(0,n.s)(t,m);return(0,p.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(c.n,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:i,onUnmountAutoFocus:o,children:(0,y.jsx)(d.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":V(u.open),...l,ref:f,onDismiss:()=>u.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(G,{titleId:u.titleId}),(0,y.jsx)(Y,{contentRef:m,descriptionId:u.descriptionId})]})]})}),B="DialogTitle",z=s.forwardRef((e,t)=>{const{__scopeDialog:a,...s}=e,r=w(B,a);return(0,y.jsx)(f.sG.h2,{id:r.titleId,...s,ref:t})});z.displayName=B;var Z="DialogDescription",H=s.forwardRef((e,t)=>{const{__scopeDialog:a,...s}=e,r=w(Z,a);return(0,y.jsx)(f.sG.p,{id:r.descriptionId,...s,ref:t})});H.displayName=Z;var P="DialogClose",q=s.forwardRef((e,t)=>{const{__scopeDialog:a,...s}=e,n=w(P,a);return(0,y.jsx)(f.sG.button,{type:"button",...s,ref:t,onClick:(0,r.m)(e.onClick,()=>n.onOpenChange(!1))})});function V(e){return e?"open":"closed"}q.displayName=P;var W="DialogTitleWarning",[J,X]=(0,i.q)(W,{contentName:F,titleName:B,docsSlug:"dialog"}),G=e=>{let{titleId:t}=e;const a=X(W),r=`\`${a.contentName}\` requires a \`${a.titleName}\` for the component to be accessible for screen reader users.\n\nIf you want to hide the \`${a.titleName}\`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/${a.docsSlug}`;return s.useEffect(()=>{if(t){document.getElementById(t)||console.error(r)}},[r,t]),null},Y=e=>{let{contentRef:t,descriptionId:a}=e;const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${X("DialogDescriptionWarning").contentName}}.`;return s.useEffect(()=>{const e=t.current?.getAttribute("aria-describedby");if(a&&e){document.getElementById(a)||console.warn(r)}},[r,t,a]),null},K=A,Q=T,ee=k,te=I,ae=O,se=z,re=H,ne=q},5320:(e,t,a)=>{a.d(t,{bq:()=>u,eb:()=>f,gC:()=>m,l6:()=>d,yv:()=>c});var s=a(5043),r=a(2500),n=a(1024),i=a(1285),o=a(3009),l=a(579);const d=r.bL,c=(r.YJ,r.WT),u=s.forwardRef((e,t)=>{let{className:a,children:s,...n}=e;return(0,l.jsxs)(r.l9,{ref:t,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),...n,children:[s,(0,l.jsx)(r.In,{asChild:!0,children:(0,l.jsx)(i.A,{className:"h-4 w-4 opacity-50"})})]})});u.displayName=r.l9.displayName;const m=s.forwardRef((e,t)=>{let{className:a,children:s,position:n="popper",...i}=e;return(0,l.jsx)(r.ZL,{children:(0,l.jsx)(r.UC,{ref:t,className:(0,o.cn)("relative z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:n,...i,children:(0,l.jsx)(r.LM,{className:(0,o.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s})})})});m.displayName=r.UC.displayName;s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,l.jsx)(r.JU,{ref:t,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...s})}).displayName=r.JU.displayName;const f=s.forwardRef((e,t)=>{let{className:a,children:s,...i}=e;return(0,l.jsxs)(r.q7,{ref:t,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...i,children:[(0,l.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,l.jsx)(r.VF,{children:(0,l.jsx)(n.A,{className:"h-4 w-4"})})}),(0,l.jsx)(r.p4,{children:s})]})});f.displayName=r.q7.displayName;s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,l.jsx)(r.wv,{ref:t,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",a),...s})}).displayName=r.wv.displayName},5722:(e,t,a)=>{a.d(t,{A:()=>s});const s=(0,a(3797).A)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},6736:(e,t,a)=>{a.d(t,{Xi:()=>d,av:()=>c,j7:()=>l,tU:()=>o});var s=a(5043),r=a(7127),n=a(3009),i=a(579);const o=r.bL,l=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,i.jsx)(r.B8,{ref:t,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",a),...s})});l.displayName=r.B8.displayName;const d=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,i.jsx)(r.l9,{ref:t,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",a),...s})});d.displayName=r.l9.displayName;const c=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,i.jsx)(r.UC,{ref:t,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",a),...s})});c.displayName=r.UC.displayName},6742:(e,t,a)=>{a.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>i,aR:()=>o,wL:()=>u});var s=a(5043),r=a(3009),n=a(579);const i=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,r.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...s})});i.displayName="Card";const o=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,r.cn)("flex flex-col space-y-1.5 p-6",a),...s})});o.displayName="CardHeader";const l=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("h3",{ref:t,className:(0,r.cn)("text-2xl font-semibold leading-none tracking-tight",a),...s})});l.displayName="CardTitle";const d=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("p",{ref:t,className:(0,r.cn)("text-sm text-muted-foreground",a),...s})});d.displayName="CardDescription";const c=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,r.cn)("p-6 pt-0",a),...s})});c.displayName="CardContent";const u=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,r.cn)("flex items-center p-6 pt-0",a),...s})});u.displayName="CardFooter"},6879:(e,t,a)=>{a.d(t,{dj:()=>m,oR:()=>u});var s=a(5043);let r=0;const n=new Map,i=e=>{if(n.has(e))return;const t=setTimeout(()=>{n.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e3);n.set(e,t)},o=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,5)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{const{toastId:a}=t;return a?i(a):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,open:!1}:e)}}case"REMOVE_TOAST":return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[];let d={toasts:[]};function c(e){d=o(d,e),l.forEach(e=>{e(d)})}function u(e){let{...t}=e;const a=(r=(r+1)%Number.MAX_SAFE_INTEGER,r.toString()),s=()=>c({type:"DISMISS_TOAST",toastId:a});return c({type:"ADD_TOAST",toast:{...t,id:a,open:!0,onOpenChange:e=>{e||s()}}}),{id:a,dismiss:s,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:a}})}}function m(){const[e,t]=s.useState(d);return s.useEffect(()=>(l.push(t),()=>{const e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},8112:(e,t,a)=>{a.d(t,{A:()=>s});const s=(0,a(3797).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},9445:(e,t,a)=>{a.d(t,{A:()=>s});const s=(0,a(3797).A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},9508:(e,t,a)=>{a.r(t),a.d(t,{TraderManagement:()=>h,default:()=>g});var s=a(5043),r=a(9066),n=a(6213),i=a(7842),o=a(382),l=a(9772),d=a(9954),c=a(2248),u=a(5320),m=a(492),f=a(6879),p=a(579);const x="http://localhost:5001/api",h=()=>{const{user:e,logout:t}=(0,r.A)(),a=localStorage.getItem("token"),{toast:h}=(0,f.dj)(),[g,y]=(0,s.useState)([]),[j,v]=(0,s.useState)([]),[N,b]=(0,s.useState)([]),[w,A]=(0,s.useState)(!0),[D,T]=(0,s.useState)(!1),[R,C]=(0,s.useState)({merchantId:"",traderId:"",startDate:(0,i.A)(new Date,"yyyy-MM-dd"),endDate:"",notes:""}),S={"Content-Type":"application/json",...a?{Authorization:`Bearer ${a}`}:{}};(0,s.useEffect)(()=>{k(),M()},[]);const k=async()=>{try{const[e]=await Promise.all([n.A.get(`${x}/trader/assignments`,{headers:S})]);y(e.data.data)}catch(e){console.error("Error fetching data:",e),h({title:"Error",description:"Failed to fetch data",variant:"destructive"})}finally{A(!1)}},M=async()=>{try{const[e,t]=await Promise.all([n.A.get(`${x}/admin/users?role=trader`,{headers:S}),n.A.get(`${x}/admin/users?role=merchant`,{headers:S})]);v(e.data.data),b(t.data.data)}catch(e){console.error("Error fetching users:",e),h({title:"Error",description:"Failed to fetch users",variant:"destructive"})}},I=e=>e?"string"===typeof e?"Loading...":e.name||e.email||"Unknown":"Unknown",E=e=>{if(!e)return"Unknown";if("string"===typeof e)return"Loading...";const t=e;return t.businessName||t.name||t.email||"Unknown"};return w?(0,p.jsx)("div",{children:"Loading..."}):(0,p.jsxs)("div",{className:"container mx-auto p-6",children:[(0,p.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,p.jsx)("h1",{className:"text-2xl font-bold",children:"Trader Management"}),(0,p.jsxs)(m.lG,{open:D,onOpenChange:T,children:[(0,p.jsx)(m.zM,{asChild:!0,children:(0,p.jsx)(l.$,{children:"Assign Trader"})}),(0,p.jsxs)(m.Cf,{children:[(0,p.jsx)(m.c7,{children:(0,p.jsx)(m.L3,{children:"Assign Trader to Merchant"})}),(0,p.jsxs)("form",{onSubmit:async e=>{e.preventDefault();try{await n.A.post(`${x}/trader/assignments`,{...R,startDate:new Date(R.startDate).toISOString(),endDate:R.endDate?new Date(R.endDate).toISOString():void 0},{headers:S}),h({title:"Success",description:"Trader assigned successfully"}),T(!1),C({merchantId:"",traderId:"",startDate:(0,i.A)(new Date,"yyyy-MM-dd"),endDate:"",notes:""}),k()}catch(t){console.error("Error creating assignment:",t),h({title:"Error",description:"Failed to assign trader",variant:"destructive"})}},className:"space-y-4",children:[(0,p.jsxs)("div",{className:"space-y-2",children:[(0,p.jsx)(c.J,{htmlFor:"merchantId",children:"Merchant"}),(0,p.jsxs)(u.l6,{value:R.merchantId,onValueChange:e=>C({...R,merchantId:e}),required:!0,children:[(0,p.jsx)(u.bq,{children:(0,p.jsx)(u.yv,{placeholder:"Select a merchant"})}),(0,p.jsx)(u.gC,{children:N.map(e=>(0,p.jsx)(u.eb,{value:e._id||"",children:E(e)},e._id))})]})]}),(0,p.jsxs)("div",{className:"space-y-2",children:[(0,p.jsx)(c.J,{htmlFor:"traderId",children:"Trader"}),(0,p.jsxs)(u.l6,{value:R.traderId,onValueChange:e=>C({...R,traderId:e}),required:!0,children:[(0,p.jsx)(u.bq,{children:(0,p.jsx)(u.yv,{placeholder:"Select a trader"})}),(0,p.jsx)(u.gC,{children:j.map(e=>(0,p.jsx)(u.eb,{value:e._id||"",children:e.name||e.email||"Unknown Trader"},e._id))})]})]}),(0,p.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,p.jsxs)("div",{className:"space-y-2",children:[(0,p.jsx)(c.J,{htmlFor:"startDate",children:"Start Date"}),(0,p.jsx)(d.p,{type:"date",id:"startDate",value:R.startDate,onChange:e=>C({...R,startDate:e.target.value}),required:!0})]}),(0,p.jsxs)("div",{className:"space-y-2",children:[(0,p.jsx)(c.J,{htmlFor:"endDate",children:"End Date (Optional)"}),(0,p.jsx)(d.p,{type:"date",id:"endDate",value:R.endDate,onChange:e=>C({...R,endDate:e.target.value}),min:R.startDate})]})]}),(0,p.jsxs)("div",{className:"space-y-2",children:[(0,p.jsx)(c.J,{htmlFor:"notes",children:"Notes (Optional)"}),(0,p.jsx)(d.p,{id:"notes",value:R.notes,onChange:e=>C({...R,notes:e.target.value}),placeholder:"Additional notes about this assignment"})]}),(0,p.jsxs)("div",{className:"flex justify-end space-x-2 pt-4",children:[(0,p.jsx)(l.$,{type:"button",variant:"outline",onClick:()=>T(!1),children:"Cancel"}),(0,p.jsx)(l.$,{type:"submit",children:"Assign Trader"})]})]})]})]})]}),(0,p.jsx)("div",{className:"rounded-md border",children:(0,p.jsxs)(o.XI,{children:[(0,p.jsx)(o.A0,{children:(0,p.jsxs)(o.Hj,{children:[(0,p.jsx)(o.nd,{children:"Merchant"}),(0,p.jsx)(o.nd,{children:"Trader"}),(0,p.jsx)(o.nd,{children:"Start Date"}),(0,p.jsx)(o.nd,{children:"End Date"}),(0,p.jsx)(o.nd,{children:"Status"}),(0,p.jsx)(o.nd,{children:"Actions"})]})}),(0,p.jsx)(o.BF,{children:0===g.length?(0,p.jsx)(o.Hj,{children:(0,p.jsx)(o.nA,{colSpan:6,className:"text-center py-4",children:"No assignments found"})}):g.map(e=>(0,p.jsxs)(o.Hj,{children:[(0,p.jsx)(o.nA,{children:I(e.merchant)}),(0,p.jsx)(o.nA,{children:I(e.trader)}),(0,p.jsx)(o.nA,{children:(0,i.A)(new Date(e.startDate),"MMM d, yyyy")}),(0,p.jsx)(o.nA,{children:e.endDate?(0,i.A)(new Date(e.endDate),"MMM d, yyyy"):"N/A"}),(0,p.jsx)(o.nA,{children:(0,p.jsx)("span",{className:"px-2 py-1 rounded-full text-xs "+("active"===e.status?"bg-green-100 text-green-800":"completed"===e.status?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-800"),children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})}),(0,p.jsx)(o.nA,{children:"active"===e.status&&(0,p.jsx)(l.$,{variant:"outline",size:"sm",className:"mr-2",onClick:()=>(async(e,t)=>{try{await n.A.put(`${x}/trader/assignments/${e}`,{status:t},{headers:S}),h({title:"Success",description:"Assignment updated successfully"}),k()}catch(a){console.error("Error updating assignment:",a),h({title:"Error",description:"Failed to update assignment",variant:"destructive"})}})(e._id,"completed"),children:"Mark Complete"})})]},e._id))})]})})]})},g=h},9772:(e,t,a)=>{a.d(t,{$:()=>d});var s=a(5043),r=a(6851),n=a(917),i=a(3009),o=a(579);const l=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=s.forwardRef((e,t)=>{let{className:a,variant:s,size:n,asChild:d=!1,...c}=e;const u=d?r.DX:"button";return(0,o.jsx)(u,{className:(0,i.cn)(l({variant:s,size:n,className:a})),ref:t,...c})});d.displayName="Button"},9954:(e,t,a)=>{a.d(t,{p:()=>i});var s=a(5043),r=a(3009),n=a(579);const i=s.forwardRef((e,t)=>{let{className:a,type:s,...i}=e;return(0,n.jsx)("input",{type:s,className:(0,r.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:t,...i})});i.displayName="Input"}}]);
//# sourceMappingURL=337.bf71db34.chunk.js.map