import React, { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { Plus, Search, Filter, Edit, Trash2, RefreshCw } from 'lucide-react';

// UI Components
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Badge } from '../../components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';
import { useToast } from '../../components/ui/use-toast';

// Services
import { 
  realTraderAssignmentService, 
  RealTraderAssignment, 
  RealUser,
  mockTraderAssignments,
  mockUsers
} from '../../services/realTraderAssignmentService';

const RealTraderAssignments = () => {
  const { toast } = useToast();
  const [assignments, setAssignments] = useState<RealTraderAssignment[]>([]);
  const [traders, setTraders] = useState<RealUser[]>([]);
  const [merchants, setMerchants] = useState<RealUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Filters
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [page, setPage] = useState(1);
  const [limit] = useState(10);

  // Fetch data
  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Try to fetch real data from API
      try {
        const [assignmentsResponse, tradersResponse, merchantsResponse] = await Promise.all([
          realTraderAssignmentService.getTraderAssignments({
            page,
            limit,
            status: statusFilter,
            search: searchTerm
          }),
          realTraderAssignmentService.getTraders(),
          realTraderAssignmentService.getMerchants()
        ]);

        if (assignmentsResponse.success) {
          setAssignments(assignmentsResponse.data);
        }
        if (tradersResponse.success) {
          setTraders(tradersResponse.data);
        }
        if (merchantsResponse.success) {
          setMerchants(merchantsResponse.data);
        }
      } catch (apiError) {
        console.log('API not available, using mock data');
        // Fallback to mock data
        setAssignments(mockTraderAssignments);
        setTraders(mockUsers.filter(u => u.role === 'trader'));
        setMerchants(mockUsers.filter(u => u.role === 'merchant'));
      }
    } catch (err) {
      setError('Failed to load data');
      console.error('Fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [page, statusFilter, searchTerm]);

  // Handle delete assignment
  const handleDeleteAssignment = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this assignment?')) {
      return;
    }

    try {
      await realTraderAssignmentService.deleteTraderAssignment(id);
      toast({
        title: 'Success',
        description: 'Assignment deleted successfully',
      });
      fetchData(); // Refresh data
    } catch (error) {
      console.error('Error deleting assignment:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete assignment. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Render status badge
  const renderStatusBadge = (status: string) => {
    const variants: Record<string, "default" | "secondary" | "destructive" | "outline"> = {
      active: "default",
      inactive: "secondary",
      suspended: "destructive",
      completed: "outline"
    };
    
    return (
      <Badge variant={variants[status] || "outline"}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  // Filter assignments based on search and status
  const filteredAssignments = assignments.filter(assignment => {
    const matchesSearch = searchTerm 
      ? assignment.traderId.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        assignment.merchantId.businessName.toLowerCase().includes(searchTerm.toLowerCase())
      : true;
    
    const matchesStatus = statusFilter !== 'all' 
      ? assignment.status === statusFilter 
      : true;
    
    return matchesSearch && matchesStatus;
  });

  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading trader assignments...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold">Trader Assignments</h1>
            <p className="text-muted-foreground">Manage trader assignments from payment-gateway database</p>
          </div>
          <div className="flex gap-2">
            <Button onClick={fetchData} variant="outline" size="sm">
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </Button>
            <Button>
              <Plus className="mr-2 h-4 w-4" /> New Assignment
            </Button>
          </div>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
        )}

        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search by trader or merchant name..."
              className="w-full pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <Select
            value={statusFilter}
            onValueChange={setStatusFilter}
          >
            <SelectTrigger className="w-[180px]">
              <Filter className="mr-2 h-4 w-4" />
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
              <SelectItem value="suspended">Suspended</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Assignments ({filteredAssignments.length})</CardTitle>
            <CardDescription>
              Real-time data from payment-gateway database
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Trader</TableHead>
                  <TableHead>Merchant</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Collection Target</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Start Date</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredAssignments.length > 0 ? (
                  filteredAssignments.map((assignment) => (
                    <TableRow key={assignment._id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{assignment.traderId.name}</div>
                          <div className="text-sm text-muted-foreground">{assignment.traderId.email}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{assignment.merchantId.businessName}</div>
                          <div className="text-sm text-muted-foreground">{assignment.merchantId.email}</div>
                        </div>
                      </TableCell>
                      <TableCell className="capitalize">
                        {assignment.assignmentType.replace('_', ' ')}
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">
                            ${assignment.collectionTarget.amount.toLocaleString()}
                          </div>
                          <div className="text-sm text-muted-foreground capitalize">
                            {assignment.collectionTarget.period}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{renderStatusBadge(assignment.status)}</TableCell>
                      <TableCell>
                        {format(new Date(assignment.startDate), 'MMM d, yyyy')}
                      </TableCell>
                      <TableCell className="text-right space-x-2">
                        <Button variant="ghost" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteAssignment(assignment._id)}
                        >
                          <Trash2 className="h-4 w-4 text-destructive" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                      No assignments found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Assignments</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{assignments.length}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Active Assignments</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {assignments.filter(a => a.status === 'active').length}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Available Traders</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{traders.length}</div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default RealTraderAssignments;
