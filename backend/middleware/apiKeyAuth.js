const { ApiError } = require('../utils/ApiError');
const logger = require('../utils/logger');
const { ApiKey } = require('../models');

/**
 * Middleware to authenticate API requests using API keys
 * @param {Object} options - Configuration options
 * @param {boolean} options.required - Whether the API key is required (default: true)
 * @param {Array} options.roles - Allowed roles for the API key (default: ['admin', 'merchant'])
 * @param {Array} options.permissions - Required permissions for the API key (optional)
 * @returns {Function} Express middleware function
 */
const apiKeyAuth = (options = {}) => {
  const {
    required = true,
    roles = ['admin', 'merchant'],
    permissions = []
  } = options;

  return async (req, res, next) => {
    try {
      // Get API key from header, query parameter, or cookie
      const apiKey = 
        req.headers['x-api-key'] || 
        req.query.apiKey ||
        req.cookies?.apiKey;

      // If API key is required but not provided
      if (required && !apiKey) {
        logger.warn('API key is required but not provided', {
          ip: req.ip,
          path: req.path,
          method: req.method
        });
        throw new ApiError(401, 'API key is required');
      }

      // If API key is not required and not provided, skip validation
      if (!required && !apiKey) {
        return next();
      }

      // Find the API key in the database
      const apiKeyDoc = await ApiKey.findOne({
        where: { key: apiKey, isActive: true },
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'email', 'role', 'isActive']
          }
        ]
      });

      // Check if API key exists and is active
      if (!apiKeyDoc) {
        logger.warn('Invalid or inactive API key', {
          ip: req.ip,
          path: req.path,
          method: req.method,
          apiKey: apiKey ? '***' + apiKey.slice(-4) : 'none'
        });
        throw new ApiError(401, 'Invalid or inactive API key');
      }

      // Check if the user exists and is active
      if (!apiKeyDoc.user || !apiKeyDoc.user.isActive) {
        logger.warn('API key user is inactive or does not exist', {
          apiKeyId: apiKeyDoc.id,
          userId: apiKeyDoc.user?.id
        });
        throw new ApiError(401, 'Invalid API key user');
      }

      // Check if the user role is allowed
      if (roles.length > 0 && !roles.includes(apiKeyDoc.user.role)) {
        logger.warn('User role not allowed', {
          userId: apiKeyDoc.user.id,
          role: apiKeyDoc.user.role,
          allowedRoles: roles
        });
        throw new ApiError(403, 'Insufficient permissions');
      }

      // Check if the API key has the required permissions
      if (permissions.length > 0) {
        const hasPermission = permissions.every(permission => 
          apiKeyDoc.permissions.includes(permission)
        );

        if (!hasPermission) {
          logger.warn('Insufficient API key permissions', {
            apiKeyId: apiKeyDoc.id,
            userId: apiKeyDoc.user.id,
            requiredPermissions: permissions,
            userPermissions: apiKeyDoc.permissions
          });
          throw new ApiError(403, 'Insufficient permissions');
        }
      }

      // Update last used timestamp
      await apiKeyDoc.update({ lastUsedAt: new Date() });

      // Attach API key and user to the request object
      req.apiKey = apiKeyDoc;
      req.user = apiKeyDoc.user;

      // Log successful authentication
      logger.info('API key authentication successful', {
        apiKeyId: apiKeyDoc.id,
        userId: apiKeyDoc.user.id,
        path: req.path,
        method: req.method
      });

      next();
    } catch (error) {
      next(error);
    }
  };
};

module.exports = apiKeyAuth;
