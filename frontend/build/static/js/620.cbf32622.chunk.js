"use strict";(self.webpackChunkpayment_gateway_frontend=self.webpackChunkpayment_gateway_frontend||[]).push([[620],{1624:(e,n,r)=>{r.d(n,{H_:()=>Zn,UC:()=>Wn,YJ:()=>Xn,q7:()=>qn,VF:()=>er,JU:()=>Yn,ZL:()=>$n,z6:()=>Jn,hN:()=>Qn,bL:()=>zn,wv:()=>nr,Pb:()=>rr,G5:()=>or,ZP:()=>tr,l9:()=>Vn});var t=r(5043),o=r(858),a=r(2814),i=r(1862),s=r(3642),c=r(7920),u=r(5463),d=r(4204),l=r(1184),p=r(6590),f=r(276),h=r(4490),m=r(6178),g=r(6667),v=r(579),w=t.forwardRef((e,n)=>{const{children:r,width:t=10,height:o=5,...a}=e;return(0,v.jsx)(c.sG.svg,{...a,ref:n,width:t,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,v.jsx)("polygon",{points:"0,0 30,0 15,10"})})});w.displayName="Arrow";var x=w,y=r(7490),b=r(503);var _="Popper",[C,M]=(0,i.A)(_),[D,R]=C(_),j=e=>{const{__scopePopper:n,children:r}=e,[o,a]=t.useState(null);return(0,v.jsx)(D,{scope:n,anchor:o,onAnchorChange:a,children:r})};j.displayName=_;var P="PopperAnchor",S=t.forwardRef((e,n)=>{const{__scopePopper:r,virtualRef:o,...i}=e,s=R(P,r),u=t.useRef(null),d=(0,a.s)(n,u);return t.useEffect(()=>{s.onAnchorChange(o?.current||u.current)}),o?null:(0,v.jsx)(c.sG.div,{...i,ref:d})});S.displayName=P;var A="PopperContent",[k,I]=C(A),O=t.forwardRef((e,n)=>{const{__scopePopper:r,side:o="bottom",sideOffset:i=0,align:s="center",alignOffset:u=0,arrowPadding:d=0,avoidCollisions:l=!0,collisionBoundary:p=[],collisionPadding:f=0,sticky:h="partial",hideWhenDetached:w=!1,updatePositionStrategy:x="optimized",onPlaced:_,...C}=e,M=R(A,r),[D,j]=t.useState(null),P=(0,a.s)(n,e=>j(e)),[S,I]=t.useState(null),O=function(e){const[n,r]=t.useState(void 0);return(0,b.N)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});const n=new ResizeObserver(n=>{if(!Array.isArray(n))return;if(!n.length)return;const t=n[0];let o,a;if("borderBoxSize"in t){const e=t.borderBoxSize,n=Array.isArray(e)?e[0]:e;o=n.inlineSize,a=n.blockSize}else o=e.offsetWidth,a=e.offsetHeight;r({width:o,height:a})});return n.observe(e,{box:"border-box"}),()=>n.unobserve(e)}r(void 0)},[e]),n}(S),E=O?.width??0,F=O?.height??0,T=o+("center"!==s?"-"+s:""),K="number"===typeof f?f:{top:0,right:0,bottom:0,left:0,...f},B=Array.isArray(p)?p:[p],U=B.length>0,H={padding:K,boundary:B.filter(N),altBoundary:U},{refs:z,floatingStyles:V,placement:$,isPositioned:W,middlewareData:X}=(0,m.we)({strategy:"fixed",placement:T,whileElementsMounted:function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return(0,g.ll)(...n,{animationFrame:"always"===x})},elements:{reference:M.anchor},middleware:[(0,m.cY)({mainAxis:i+F,alignmentAxis:u}),l&&(0,m.BN)({mainAxis:!0,crossAxis:!1,limiter:"partial"===h?(0,m.ER)():void 0,...H}),l&&(0,m.UU)({...H}),(0,m.Ej)({...H,apply:e=>{let{elements:n,rects:r,availableWidth:t,availableHeight:o}=e;const{width:a,height:i}=r.reference,s=n.floating.style;s.setProperty("--radix-popper-available-width",`${t}px`),s.setProperty("--radix-popper-available-height",`${o}px`),s.setProperty("--radix-popper-anchor-width",`${a}px`),s.setProperty("--radix-popper-anchor-height",`${i}px`)}}),S&&(0,m.UE)({element:S,padding:d}),G({arrowWidth:E,arrowHeight:F}),w&&(0,m.jD)({strategy:"referenceHidden",...H})]}),[Y,q]=L($),Z=(0,y.c)(_);(0,b.N)(()=>{W&&Z?.()},[W,Z]);const J=X.arrow?.x,Q=X.arrow?.y,ee=0!==X.arrow?.centerOffset,[ne,re]=t.useState();return(0,b.N)(()=>{D&&re(window.getComputedStyle(D).zIndex)},[D]),(0,v.jsx)("div",{ref:z.setFloating,"data-radix-popper-content-wrapper":"",style:{...V,transform:W?V.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ne,"--radix-popper-transform-origin":[X.transformOrigin?.x,X.transformOrigin?.y].join(" "),...X.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,v.jsx)(k,{scope:r,placedSide:Y,onArrowChange:I,arrowX:J,arrowY:Q,shouldHideArrow:ee,children:(0,v.jsx)(c.sG.div,{"data-side":Y,"data-align":q,...C,ref:P,style:{...C.style,animation:W?void 0:"none"}})})})});O.displayName=A;var E="PopperArrow",F={top:"bottom",right:"left",bottom:"top",left:"right"},T=t.forwardRef(function(e,n){const{__scopePopper:r,...t}=e,o=I(E,r),a=F[o.placedSide];return(0,v.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[a]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,v.jsx)(x,{...t,ref:n,style:{...t.style,display:"block"}})})});function N(e){return null!==e}T.displayName=E;var G=e=>({name:"transformOrigin",options:e,fn(n){const{placement:r,rects:t,middlewareData:o}=n,a=0!==o.arrow?.centerOffset,i=a?0:e.arrowWidth,s=a?0:e.arrowHeight,[c,u]=L(r),d={start:"0%",center:"50%",end:"100%"}[u],l=(o.arrow?.x??0)+i/2,p=(o.arrow?.y??0)+s/2;let f="",h="";return"bottom"===c?(f=a?d:`${l}px`,h=-s+"px"):"top"===c?(f=a?d:`${l}px`,h=`${t.floating.height+s}px`):"right"===c?(f=-s+"px",h=a?d:`${p}px`):"left"===c&&(f=`${t.floating.width+s}px`,h=a?d:`${p}px`),{data:{x:f,y:h}}}});function L(e){const[n,r="center"]=e.split("-");return[n,r]}var K=j,B=S,U=O,H=T,z=r(3321),V=r(2894),$=r(6711),W=r(6851),X=r(5754),Y=r(4064),q=["Enter"," "],Z=["ArrowUp","PageDown","End"],J=["ArrowDown","PageUp","Home",...Z],Q={ltr:[...q,"ArrowRight"],rtl:[...q,"ArrowLeft"]},ee={ltr:["ArrowLeft"],rtl:["ArrowRight"]},ne="Menu",[re,te,oe]=(0,u.N)(ne),[ae,ie]=(0,i.A)(ne,[oe,M,$.RG]),se=M(),ce=(0,$.RG)(),[ue,de]=ae(ne),[le,pe]=ae(ne),fe=e=>{const{__scopeMenu:n,open:r=!1,children:o,dir:a,onOpenChange:i,modal:s=!0}=e,c=se(n),[u,l]=t.useState(null),p=t.useRef(!1),f=(0,y.c)(i),h=(0,d.jH)(a);return t.useEffect(()=>{const e=()=>{p.current=!0,document.addEventListener("pointerdown",n,{capture:!0,once:!0}),document.addEventListener("pointermove",n,{capture:!0,once:!0})},n=()=>p.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",n,{capture:!0}),document.removeEventListener("pointermove",n,{capture:!0})}},[]),(0,v.jsx)(K,{...c,children:(0,v.jsx)(ue,{scope:n,open:r,onOpenChange:f,content:u,onContentChange:l,children:(0,v.jsx)(le,{scope:n,onClose:t.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:p,dir:h,modal:s,children:o})})})};fe.displayName=ne;var he=t.forwardRef((e,n)=>{const{__scopeMenu:r,...t}=e,o=se(r);return(0,v.jsx)(B,{...o,...t,ref:n})});he.displayName="MenuAnchor";var me="MenuPortal",[ge,ve]=ae(me,{forceMount:void 0}),we=e=>{const{__scopeMenu:n,forceMount:r,children:t,container:o}=e,a=de(me,n);return(0,v.jsx)(ge,{scope:n,forceMount:r,children:(0,v.jsx)(V.C,{present:r||a.open,children:(0,v.jsx)(z.Z,{asChild:!0,container:o,children:t})})})};we.displayName=me;var xe="MenuContent",[ye,be]=ae(xe),_e=t.forwardRef((e,n)=>{const r=ve(xe,e.__scopeMenu),{forceMount:t=r.forceMount,...o}=e,a=de(xe,e.__scopeMenu),i=pe(xe,e.__scopeMenu);return(0,v.jsx)(re.Provider,{scope:e.__scopeMenu,children:(0,v.jsx)(V.C,{present:t||a.open,children:(0,v.jsx)(re.Slot,{scope:e.__scopeMenu,children:i.modal?(0,v.jsx)(Ce,{...o,ref:n}):(0,v.jsx)(Me,{...o,ref:n})})})})}),Ce=t.forwardRef((e,n)=>{const r=de(xe,e.__scopeMenu),i=t.useRef(null),s=(0,a.s)(n,i);return t.useEffect(()=>{const e=i.current;if(e)return(0,X.Eq)(e)},[]),(0,v.jsx)(Re,{...e,ref:s,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),Me=t.forwardRef((e,n)=>{const r=de(xe,e.__scopeMenu);return(0,v.jsx)(Re,{...e,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),De=(0,W.TL)("MenuContent.ScrollLock"),Re=t.forwardRef((e,n)=>{const{__scopeMenu:r,loop:i=!1,trapFocus:s,onOpenAutoFocus:c,onCloseAutoFocus:u,disableOutsidePointerEvents:d,onEntryFocus:h,onEscapeKeyDown:m,onPointerDownOutside:g,onFocusOutside:w,onInteractOutside:x,onDismiss:y,disableOutsideScroll:b,..._}=e,C=de(xe,r),M=pe(xe,r),D=se(r),R=ce(r),j=te(r),[P,S]=t.useState(null),A=t.useRef(null),k=(0,a.s)(n,A,C.onContentChange),I=t.useRef(0),O=t.useRef(""),E=t.useRef(0),F=t.useRef(null),T=t.useRef("right"),N=t.useRef(0),G=b?Y.A:t.Fragment,L=b?{as:De,allowPinchZoom:!0}:void 0,K=e=>{const n=O.current+e,r=j().filter(e=>!e.disabled),t=document.activeElement,o=r.find(e=>e.ref.current===t)?.textValue,a=function(e,n,r){const t=n.length>1&&Array.from(n).every(e=>e===n[0]),o=t?n[0]:n,a=r?e.indexOf(r):-1;let i=(s=e,c=Math.max(a,0),s.map((e,n)=>s[(c+n)%s.length]));var s,c;1===o.length&&(i=i.filter(e=>e!==r));const u=i.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return u!==r?u:void 0}(r.map(e=>e.textValue),n,o),i=r.find(e=>e.textValue===a)?.ref.current;!function e(n){O.current=n,window.clearTimeout(I.current),""!==n&&(I.current=window.setTimeout(()=>e(""),1e3))}(n),i&&setTimeout(()=>i.focus())};t.useEffect(()=>()=>window.clearTimeout(I.current),[]),(0,p.Oh)();const B=t.useCallback(e=>T.current===F.current?.side&&function(e,n){if(!n)return!1;const r={x:e.clientX,y:e.clientY};return function(e,n){const{x:r,y:t}=e;let o=!1;for(let a=0,i=n.length-1;a<n.length;i=a++){const e=n[a],s=n[i],c=e.x,u=e.y,d=s.x,l=s.y;u>t!==l>t&&r<(d-c)*(t-u)/(l-u)+c&&(o=!o)}return o}(r,n)}(e,F.current?.area),[]);return(0,v.jsx)(ye,{scope:r,searchRef:O,onItemEnter:t.useCallback(e=>{B(e)&&e.preventDefault()},[B]),onItemLeave:t.useCallback(e=>{B(e)||(A.current?.focus(),S(null))},[B]),onTriggerLeave:t.useCallback(e=>{B(e)&&e.preventDefault()},[B]),pointerGraceTimerRef:E,onPointerGraceIntentChange:t.useCallback(e=>{F.current=e},[]),children:(0,v.jsx)(G,{...L,children:(0,v.jsx)(f.n,{asChild:!0,trapped:s,onMountAutoFocus:(0,o.m)(c,e=>{e.preventDefault(),A.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:u,children:(0,v.jsx)(l.qW,{asChild:!0,disableOutsidePointerEvents:d,onEscapeKeyDown:m,onPointerDownOutside:g,onFocusOutside:w,onInteractOutside:x,onDismiss:y,children:(0,v.jsx)($.bL,{asChild:!0,...R,dir:M.dir,orientation:"vertical",loop:i,currentTabStopId:P,onCurrentTabStopIdChange:S,onEntryFocus:(0,o.m)(h,e=>{M.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,v.jsx)(U,{role:"menu","aria-orientation":"vertical","data-state":en(C.open),"data-radix-menu-content":"",dir:M.dir,...D,..._,ref:k,style:{outline:"none",..._.style},onKeyDown:(0,o.m)(_.onKeyDown,e=>{const n=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,t=1===e.key.length;n&&("Tab"===e.key&&e.preventDefault(),!r&&t&&K(e.key));const o=A.current;if(e.target!==o)return;if(!J.includes(e.key))return;e.preventDefault();const a=j().filter(e=>!e.disabled).map(e=>e.ref.current);Z.includes(e.key)&&a.reverse(),function(e){const n=document.activeElement;for(const r of e){if(r===n)return;if(r.focus(),document.activeElement!==n)return}}(a)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(I.current),O.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,tn(e=>{const n=e.target,r=N.current!==e.clientX;if(e.currentTarget.contains(n)&&r){const n=e.clientX>N.current?"right":"left";T.current=n,N.current=e.clientX}}))})})})})})})});_e.displayName=xe;var je=t.forwardRef((e,n)=>{const{__scopeMenu:r,...t}=e;return(0,v.jsx)(c.sG.div,{role:"group",...t,ref:n})});je.displayName="MenuGroup";var Pe=t.forwardRef((e,n)=>{const{__scopeMenu:r,...t}=e;return(0,v.jsx)(c.sG.div,{...t,ref:n})});Pe.displayName="MenuLabel";var Se="MenuItem",Ae="menu.itemSelect",ke=t.forwardRef((e,n)=>{const{disabled:r=!1,onSelect:i,...s}=e,u=t.useRef(null),d=pe(Se,e.__scopeMenu),l=be(Se,e.__scopeMenu),p=(0,a.s)(n,u),f=t.useRef(!1);return(0,v.jsx)(Ie,{...s,ref:p,disabled:r,onClick:(0,o.m)(e.onClick,()=>{const e=u.current;if(!r&&e){const n=new CustomEvent(Ae,{bubbles:!0,cancelable:!0});e.addEventListener(Ae,e=>i?.(e),{once:!0}),(0,c.hO)(e,n),n.defaultPrevented?f.current=!1:d.onClose()}}),onPointerDown:n=>{e.onPointerDown?.(n),f.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{f.current||e.currentTarget?.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{const n=""!==l.searchRef.current;r||n&&" "===e.key||q.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ke.displayName=Se;var Ie=t.forwardRef((e,n)=>{const{__scopeMenu:r,disabled:i=!1,textValue:s,...u}=e,d=be(Se,r),l=ce(r),p=t.useRef(null),f=(0,a.s)(n,p),[h,m]=t.useState(!1),[g,w]=t.useState("");return t.useEffect(()=>{const e=p.current;e&&w((e.textContent??"").trim())},[u.children]),(0,v.jsx)(re.ItemSlot,{scope:r,disabled:i,textValue:s??g,children:(0,v.jsx)($.q7,{asChild:!0,...l,focusable:!i,children:(0,v.jsx)(c.sG.div,{role:"menuitem","data-highlighted":h?"":void 0,"aria-disabled":i||void 0,"data-disabled":i?"":void 0,...u,ref:f,onPointerMove:(0,o.m)(e.onPointerMove,tn(e=>{if(i)d.onItemLeave(e);else if(d.onItemEnter(e),!e.defaultPrevented){e.currentTarget.focus({preventScroll:!0})}})),onPointerLeave:(0,o.m)(e.onPointerLeave,tn(e=>d.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>m(!0)),onBlur:(0,o.m)(e.onBlur,()=>m(!1))})})})}),Oe=t.forwardRef((e,n)=>{const{checked:r=!1,onCheckedChange:t,...a}=e;return(0,v.jsx)(Be,{scope:e.__scopeMenu,checked:r,children:(0,v.jsx)(ke,{role:"menuitemcheckbox","aria-checked":nn(r)?"mixed":r,...a,ref:n,"data-state":rn(r),onSelect:(0,o.m)(a.onSelect,()=>t?.(!!nn(r)||!r),{checkForDefaultPrevented:!1})})})});Oe.displayName="MenuCheckboxItem";var Ee="MenuRadioGroup",[Fe,Te]=ae(Ee,{value:void 0,onValueChange:()=>{}}),Ne=t.forwardRef((e,n)=>{const{value:r,onValueChange:t,...o}=e,a=(0,y.c)(t);return(0,v.jsx)(Fe,{scope:e.__scopeMenu,value:r,onValueChange:a,children:(0,v.jsx)(je,{...o,ref:n})})});Ne.displayName=Ee;var Ge="MenuRadioItem",Le=t.forwardRef((e,n)=>{const{value:r,...t}=e,a=Te(Ge,e.__scopeMenu),i=r===a.value;return(0,v.jsx)(Be,{scope:e.__scopeMenu,checked:i,children:(0,v.jsx)(ke,{role:"menuitemradio","aria-checked":i,...t,ref:n,"data-state":rn(i),onSelect:(0,o.m)(t.onSelect,()=>a.onValueChange?.(r),{checkForDefaultPrevented:!1})})})});Le.displayName=Ge;var Ke="MenuItemIndicator",[Be,Ue]=ae(Ke,{checked:!1}),He=t.forwardRef((e,n)=>{const{__scopeMenu:r,forceMount:t,...o}=e,a=Ue(Ke,r);return(0,v.jsx)(V.C,{present:t||nn(a.checked)||!0===a.checked,children:(0,v.jsx)(c.sG.span,{...o,ref:n,"data-state":rn(a.checked)})})});He.displayName=Ke;var ze=t.forwardRef((e,n)=>{const{__scopeMenu:r,...t}=e;return(0,v.jsx)(c.sG.div,{role:"separator","aria-orientation":"horizontal",...t,ref:n})});ze.displayName="MenuSeparator";var Ve=t.forwardRef((e,n)=>{const{__scopeMenu:r,...t}=e,o=se(r);return(0,v.jsx)(H,{...o,...t,ref:n})});Ve.displayName="MenuArrow";var $e="MenuSub",[We,Xe]=ae($e),Ye=e=>{const{__scopeMenu:n,children:r,open:o=!1,onOpenChange:a}=e,i=de($e,n),s=se(n),[c,u]=t.useState(null),[d,l]=t.useState(null),p=(0,y.c)(a);return t.useEffect(()=>(!1===i.open&&p(!1),()=>p(!1)),[i.open,p]),(0,v.jsx)(K,{...s,children:(0,v.jsx)(ue,{scope:n,open:o,onOpenChange:p,content:d,onContentChange:l,children:(0,v.jsx)(We,{scope:n,contentId:(0,h.B)(),triggerId:(0,h.B)(),trigger:c,onTriggerChange:u,children:r})})})};Ye.displayName=$e;var qe="MenuSubTrigger",Ze=t.forwardRef((e,n)=>{const r=de(qe,e.__scopeMenu),i=pe(qe,e.__scopeMenu),s=Xe(qe,e.__scopeMenu),c=be(qe,e.__scopeMenu),u=t.useRef(null),{pointerGraceTimerRef:d,onPointerGraceIntentChange:l}=c,p={__scopeMenu:e.__scopeMenu},f=t.useCallback(()=>{u.current&&window.clearTimeout(u.current),u.current=null},[]);return t.useEffect(()=>f,[f]),t.useEffect(()=>{const e=d.current;return()=>{window.clearTimeout(e),l(null)}},[d,l]),(0,v.jsx)(he,{asChild:!0,...p,children:(0,v.jsx)(Ie,{id:s.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":s.contentId,"data-state":en(r.open),...e,ref:(0,a.t)(n,s.onTriggerChange),onClick:n=>{e.onClick?.(n),e.disabled||n.defaultPrevented||(n.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,tn(n=>{c.onItemEnter(n),n.defaultPrevented||e.disabled||r.open||u.current||(c.onPointerGraceIntentChange(null),u.current=window.setTimeout(()=>{r.onOpenChange(!0),f()},100))})),onPointerLeave:(0,o.m)(e.onPointerLeave,tn(e=>{f();const n=r.content?.getBoundingClientRect();if(n){const t=r.content?.dataset.side,o="right"===t,a=o?-5:5,i=n[o?"left":"right"],s=n[o?"right":"left"];c.onPointerGraceIntentChange({area:[{x:e.clientX+a,y:e.clientY},{x:i,y:n.top},{x:s,y:n.top},{x:s,y:n.bottom},{x:i,y:n.bottom}],side:t}),window.clearTimeout(d.current),d.current=window.setTimeout(()=>c.onPointerGraceIntentChange(null),300)}else{if(c.onTriggerLeave(e),e.defaultPrevented)return;c.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,n=>{const t=""!==c.searchRef.current;e.disabled||t&&" "===n.key||Q[i.dir].includes(n.key)&&(r.onOpenChange(!0),r.content?.focus(),n.preventDefault())})})})});Ze.displayName=qe;var Je="MenuSubContent",Qe=t.forwardRef((e,n)=>{const r=ve(xe,e.__scopeMenu),{forceMount:i=r.forceMount,...s}=e,c=de(xe,e.__scopeMenu),u=pe(xe,e.__scopeMenu),d=Xe(Je,e.__scopeMenu),l=t.useRef(null),p=(0,a.s)(n,l);return(0,v.jsx)(re.Provider,{scope:e.__scopeMenu,children:(0,v.jsx)(V.C,{present:i||c.open,children:(0,v.jsx)(re.Slot,{scope:e.__scopeMenu,children:(0,v.jsx)(Re,{id:d.contentId,"aria-labelledby":d.triggerId,...s,ref:p,align:"start",side:"rtl"===u.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{u.isUsingKeyboardRef.current&&l.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==d.trigger&&c.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{u.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{const n=e.currentTarget.contains(e.target),r=ee[u.dir].includes(e.key);n&&r&&(c.onOpenChange(!1),d.trigger?.focus(),e.preventDefault())})})})})})});function en(e){return e?"open":"closed"}function nn(e){return"indeterminate"===e}function rn(e){return nn(e)?"indeterminate":e?"checked":"unchecked"}function tn(e){return n=>"mouse"===n.pointerType?e(n):void 0}Qe.displayName=Je;var on=fe,an=he,sn=we,cn=_e,un=je,dn=Pe,ln=ke,pn=Oe,fn=Ne,hn=Le,mn=He,gn=ze,vn=Ve,wn=Ye,xn=Ze,yn=Qe,bn="DropdownMenu",[_n,Cn]=(0,i.A)(bn,[ie]),Mn=ie(),[Dn,Rn]=_n(bn),jn=e=>{const{__scopeDropdownMenu:n,children:r,dir:o,open:a,defaultOpen:i,onOpenChange:c,modal:u=!0}=e,d=Mn(n),l=t.useRef(null),[p,f]=(0,s.i)({prop:a,defaultProp:i??!1,onChange:c,caller:bn});return(0,v.jsx)(Dn,{scope:n,triggerId:(0,h.B)(),triggerRef:l,contentId:(0,h.B)(),open:p,onOpenChange:f,onOpenToggle:t.useCallback(()=>f(e=>!e),[f]),modal:u,children:(0,v.jsx)(on,{...d,open:p,onOpenChange:f,dir:o,modal:u,children:r})})};jn.displayName=bn;var Pn="DropdownMenuTrigger",Sn=t.forwardRef((e,n)=>{const{__scopeDropdownMenu:r,disabled:t=!1,...i}=e,s=Rn(Pn,r),u=Mn(r);return(0,v.jsx)(an,{asChild:!0,...u,children:(0,v.jsx)(c.sG.button,{type:"button",id:s.triggerId,"aria-haspopup":"menu","aria-expanded":s.open,"aria-controls":s.open?s.contentId:void 0,"data-state":s.open?"open":"closed","data-disabled":t?"":void 0,disabled:t,...i,ref:(0,a.t)(n,s.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{t||0!==e.button||!1!==e.ctrlKey||(s.onOpenToggle(),s.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{t||(["Enter"," "].includes(e.key)&&s.onOpenToggle(),"ArrowDown"===e.key&&s.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});Sn.displayName=Pn;var An=e=>{const{__scopeDropdownMenu:n,...r}=e,t=Mn(n);return(0,v.jsx)(sn,{...t,...r})};An.displayName="DropdownMenuPortal";var kn="DropdownMenuContent",In=t.forwardRef((e,n)=>{const{__scopeDropdownMenu:r,...a}=e,i=Rn(kn,r),s=Mn(r),c=t.useRef(!1);return(0,v.jsx)(cn,{id:i.contentId,"aria-labelledby":i.triggerId,...s,...a,ref:n,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{c.current||i.triggerRef.current?.focus(),c.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{const n=e.detail.originalEvent,r=0===n.button&&!0===n.ctrlKey,t=2===n.button||r;i.modal&&!t||(c.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});In.displayName=kn;var On=t.forwardRef((e,n)=>{const{__scopeDropdownMenu:r,...t}=e,o=Mn(r);return(0,v.jsx)(un,{...o,...t,ref:n})});On.displayName="DropdownMenuGroup";var En=t.forwardRef((e,n)=>{const{__scopeDropdownMenu:r,...t}=e,o=Mn(r);return(0,v.jsx)(dn,{...o,...t,ref:n})});En.displayName="DropdownMenuLabel";var Fn=t.forwardRef((e,n)=>{const{__scopeDropdownMenu:r,...t}=e,o=Mn(r);return(0,v.jsx)(ln,{...o,...t,ref:n})});Fn.displayName="DropdownMenuItem";var Tn=t.forwardRef((e,n)=>{const{__scopeDropdownMenu:r,...t}=e,o=Mn(r);return(0,v.jsx)(pn,{...o,...t,ref:n})});Tn.displayName="DropdownMenuCheckboxItem";var Nn=t.forwardRef((e,n)=>{const{__scopeDropdownMenu:r,...t}=e,o=Mn(r);return(0,v.jsx)(fn,{...o,...t,ref:n})});Nn.displayName="DropdownMenuRadioGroup";var Gn=t.forwardRef((e,n)=>{const{__scopeDropdownMenu:r,...t}=e,o=Mn(r);return(0,v.jsx)(hn,{...o,...t,ref:n})});Gn.displayName="DropdownMenuRadioItem";var Ln=t.forwardRef((e,n)=>{const{__scopeDropdownMenu:r,...t}=e,o=Mn(r);return(0,v.jsx)(mn,{...o,...t,ref:n})});Ln.displayName="DropdownMenuItemIndicator";var Kn=t.forwardRef((e,n)=>{const{__scopeDropdownMenu:r,...t}=e,o=Mn(r);return(0,v.jsx)(gn,{...o,...t,ref:n})});Kn.displayName="DropdownMenuSeparator";var Bn=t.forwardRef((e,n)=>{const{__scopeDropdownMenu:r,...t}=e,o=Mn(r);return(0,v.jsx)(vn,{...o,...t,ref:n})});Bn.displayName="DropdownMenuArrow";var Un=t.forwardRef((e,n)=>{const{__scopeDropdownMenu:r,...t}=e,o=Mn(r);return(0,v.jsx)(xn,{...o,...t,ref:n})});Un.displayName="DropdownMenuSubTrigger";var Hn=t.forwardRef((e,n)=>{const{__scopeDropdownMenu:r,...t}=e,o=Mn(r);return(0,v.jsx)(yn,{...o,...t,ref:n,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});Hn.displayName="DropdownMenuSubContent";var zn=jn,Vn=Sn,$n=An,Wn=In,Xn=On,Yn=En,qn=Fn,Zn=Tn,Jn=Nn,Qn=Gn,er=Ln,nr=Kn,rr=e=>{const{__scopeDropdownMenu:n,children:r,open:t,onOpenChange:o,defaultOpen:a}=e,i=Mn(n),[c,u]=(0,s.i)({prop:t,defaultProp:a??!1,onChange:o,caller:"DropdownMenuSub"});return(0,v.jsx)(wn,{...i,open:c,onOpenChange:u,children:r})},tr=Un,or=Hn},3992:(e,n,r)=>{r.d(n,{A:()=>t});const t=(0,r(3797).A)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},4204:(e,n,r)=>{r.d(n,{jH:()=>a});var t=r(5043),o=(r(579),t.createContext(void 0));function a(e){const n=t.useContext(o);return e||n||"ltr"}},6711:(e,n,r)=>{r.d(n,{RG:()=>b,bL:()=>A,q7:()=>k});var t=r(5043),o=r(858),a=r(5463),i=r(2814),s=r(1862),c=r(4490),u=r(7920),d=r(7490),l=r(3642),p=r(4204),f=r(579),h="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},g="RovingFocusGroup",[v,w,x]=(0,a.N)(g),[y,b]=(0,s.A)(g,[x]),[_,C]=y(g),M=t.forwardRef((e,n)=>(0,f.jsx)(v.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(v.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(D,{...e,ref:n})})}));M.displayName=g;var D=t.forwardRef((e,n)=>{const{__scopeRovingFocusGroup:r,orientation:a,loop:s=!1,dir:c,currentTabStopId:v,defaultCurrentTabStopId:x,onCurrentTabStopIdChange:y,onEntryFocus:b,preventScrollOnEntryFocus:C=!1,...M}=e,D=t.useRef(null),R=(0,i.s)(n,D),j=(0,p.jH)(c),[P,A]=(0,l.i)({prop:v,defaultProp:x??null,onChange:y,caller:g}),[k,I]=t.useState(!1),O=(0,d.c)(b),E=w(r),F=t.useRef(!1),[T,N]=t.useState(0);return t.useEffect(()=>{const e=D.current;if(e)return e.addEventListener(h,O),()=>e.removeEventListener(h,O)},[O]),(0,f.jsx)(_,{scope:r,orientation:a,dir:j,loop:s,currentTabStopId:P,onItemFocus:t.useCallback(e=>A(e),[A]),onItemShiftTab:t.useCallback(()=>I(!0),[]),onFocusableItemAdd:t.useCallback(()=>N(e=>e+1),[]),onFocusableItemRemove:t.useCallback(()=>N(e=>e-1),[]),children:(0,f.jsx)(u.sG.div,{tabIndex:k||0===T?-1:0,"data-orientation":a,...M,ref:R,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{F.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{const n=!F.current;if(e.target===e.currentTarget&&n&&!k){const n=new CustomEvent(h,m);if(e.currentTarget.dispatchEvent(n),!n.defaultPrevented){const e=E().filter(e=>e.focusable);S([e.find(e=>e.active),e.find(e=>e.id===P),...e].filter(Boolean).map(e=>e.ref.current),C)}}F.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>I(!1))})})}),R="RovingFocusGroupItem",j=t.forwardRef((e,n)=>{const{__scopeRovingFocusGroup:r,focusable:a=!0,active:i=!1,tabStopId:s,children:d,...l}=e,p=(0,c.B)(),h=s||p,m=C(R,r),g=m.currentTabStopId===h,x=w(r),{onFocusableItemAdd:y,onFocusableItemRemove:b,currentTabStopId:_}=m;return t.useEffect(()=>{if(a)return y(),()=>b()},[a,y,b]),(0,f.jsx)(v.ItemSlot,{scope:r,id:h,focusable:a,active:i,children:(0,f.jsx)(u.sG.span,{tabIndex:g?0:-1,"data-orientation":m.orientation,...l,ref:n,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a?m.onItemFocus(h):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>m.onItemFocus(h)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void m.onItemShiftTab();if(e.target!==e.currentTarget)return;const n=function(e,n,r){const t=function(e,n){return"rtl"!==n?e:"ArrowLeft"===e?"ArrowRight":"ArrowRight"===e?"ArrowLeft":e}(e.key,r);return"vertical"===n&&["ArrowLeft","ArrowRight"].includes(t)||"horizontal"===n&&["ArrowUp","ArrowDown"].includes(t)?void 0:P[t]}(e,m.orientation,m.dir);if(void 0!==n){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let o=x().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===n)o.reverse();else if("prev"===n||"next"===n){"prev"===n&&o.reverse();const a=o.indexOf(e.currentTarget);o=m.loop?(t=a+1,(r=o).map((e,n)=>r[(t+n)%r.length])):o.slice(a+1)}setTimeout(()=>S(o))}var r,t}),children:"function"===typeof d?d({isCurrentTabStop:g,hasTabStop:null!=_}):d})})});j.displayName=R;var P={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function S(e){let n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const r=document.activeElement;for(const t of e){if(t===r)return;if(t.focus({preventScroll:n}),document.activeElement!==r)return}}var A=M,k=j},8432:(e,n,r)=>{r.d(n,{A:()=>t});const t=(0,r(3797).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])}}]);
//# sourceMappingURL=620.cbf32622.chunk.js.map