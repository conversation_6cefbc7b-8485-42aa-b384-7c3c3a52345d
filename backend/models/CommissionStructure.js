const mongoose = require('mongoose');

const commissionTierSchema = new mongoose.Schema({
  minAmount: { 
    type: Number, 
    required: true 
  },
  maxAmount: { 
    type: Number 
  },
  percentage: { 
    type: Number,
    min: 0,
    max: 100
  },
  fixedAmount: { 
    type: Number,
    min: 0
  }
}, { _id: false });

const commissionStructureSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  fromType: { 
    type: String, 
    required: true, 
    enum: ['trader', 'merchant', 'agent'] 
  },
  toType: { 
    type: String, 
    required: true, 
    enum: ['trader', 'merchant', 'agent'] 
  },
  transactionType: { 
    type: String, 
    required: true, 
    enum: ['payin', 'payout', 'internal'] 
  },
  // Flat rate or tiered
  isTiered: {
    type: Boolean,
    default: false
  },
  // For flat rate
  percentage: { 
    type: Number, 
    min: 0, 
    max: 100 
  },
  fixedAmount: { 
    type: Number, 
    min: 0 
  },
  // For tiered structure
  tiers: [commissionTierSchema],
  // Effective date range
  effectiveFrom: { 
    type: Date, 
    default: Date.now 
  },
  effectiveTo: { 
    type: Date 
  },
  // Specific parties (if null, applies to all)
  fromParty: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User' 
  },
  toParty: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User' 
  },
  // Additional conditions
  minTransactionAmount: { 
    type: Number, 
    min: 0 
  },
  maxTransactionAmount: { 
    type: Number, 
    min: 0 
  },
  // Metadata
  createdBy: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User' 
  },
  updatedBy: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User' 
  },
  isActive: { 
    type: Boolean, 
    default: true 
  },
  // For versioning
  version: { 
    type: Number, 
    default: 1 
  },
  // Additional metadata
  tags: [{
    type: String,
    trim: true
  }],
  notes: String
}, { 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
commissionStructureSchema.index({ fromType: 1, toType: 1 });
commissionStructureSchema.index({ transactionType: 1 });
commissionStructureSchema.index({ isActive: 1 });
commissionStructureSchema.index({ effectiveFrom: 1, effectiveTo: 1 });

// Method to calculate commission for a given amount
commissionStructureSchema.methods.calculateCommission = function(amount) {
  if (this.isTiered && this.tiers && this.tiers.length > 0) {
    // Find the applicable tier
    const applicableTier = this.tiers.find(tier => {
      return amount >= tier.minAmount && 
             (!tier.maxAmount || amount <= tier.maxAmount);
    });
    
    if (applicableTier) {
      if (applicableTier.percentage !== undefined) {
        return (amount * applicableTier.percentage) / 100;
      } else if (applicableTier.fixedAmount !== undefined) {
        return applicableTier.fixedAmount;
      }
    }
    return 0;
  } else {
    // Flat rate calculation
    if (this.percentage !== undefined) {
      return (amount * this.percentage) / 100;
    } else if (this.fixedAmount !== undefined) {
      return this.fixedAmount;
    }
    return 0;
  }
};

// Static method to find applicable commission structure
commissionStructureSchema.statics.findApplicable = async function(params) {
  const { fromType, toType, transactionType, amount, fromParty, toParty } = params;
  
  const query = {
    fromType,
    toType,
    transactionType,
    isActive: true,
    $or: [
      { effectiveFrom: { $lte: new Date() } },
      { effectiveFrom: { $exists: false } }
    ],
    $or: [
      { effectiveTo: { $gte: new Date() } },
      { effectiveTo: { $exists: false } }
    ],
    $and: [
      {
        $or: [
          { fromParty: fromParty },
          { fromParty: { $exists: false } }
        ]
      },
      {
        $or: [
          { toParty: toParty },
          { toParty: { $exists: false } }
        ]
      }
    ]
  };

  if (amount !== undefined) {
    query.$and.push({
      $or: [
        { minTransactionAmount: { $exists: false } },
        { minTransactionAmount: { $lte: amount } }
      ]
    });
    
    query.$and.push({
      $or: [
        { maxTransactionAmount: { $exists: false } },
        { maxTransactionAmount: { $gte: amount } }
      ]
    });
  }

  // Find all matching commission structures
  const structures = await this.find(query)
    .sort({
      fromParty: -1,  // More specific (with fromParty) comes first
      toParty: -1,    // More specific (with toParty) comes first
      version: -1     // Newer versions first
    })
    .limit(1);

  return structures[0] || null;
};

// Pre-save hook to handle versioning
commissionStructureSchema.pre('save', function(next) {
  if (this.isModified() && !this.isNew) {
    // When updating, we create a new version instead of updating the existing one
    this.isNew = true;
    this.version += 1;
  }
  next();
});

module.exports = mongoose.model('CommissionStructure', commissionStructureSchema);
