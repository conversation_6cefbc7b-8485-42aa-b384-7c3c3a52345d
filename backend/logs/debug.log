winston:create-logger: Define prototype method for "error"
winston:create-logger: Define prototype method for "warn"
winston:create-logger: Define prototype method for "info"
winston:create-logger: Define prototype method for "http"
winston:create-logger: Define prototype method for "verbose"
winston:create-logger: Define prototype method for "debug"
winston:create-logger: Define prototype method for "silly"
winston:create-logger: Define prototype method for "error"
winston:create-logger: Define prototype method for "warn"
winston:create-logger: Define prototype method for "info"
winston:create-logger: Define prototype method for "http"
winston:create-logger: Define prototype method for "verbose"
winston:create-logger: Define prototype method for "debug"
winston:create-logger: Define prototype method for "silly"
Thu, 24 Jul 2025 07:58:29 GMT express:router:route new '/register'
Thu, 24 Jul 2025 07:58:29 GMT express:router:layer new '/register'
Thu, 24 Jul 2025 07:58:29 GMT express:router:route post '/register'
Thu, 24 Jul 2025 07:58:29 GMT express:router:layer new '/'
Thu, 24 Jul 2025 07:58:29 GMT express:router:route post '/register'
Thu, 24 Jul 2025 07:58:29 GMT express:router:layer new '/'
Thu, 24 Jul 2025 07:58:29 GMT express:router:route post '/register'
Thu, 24 Jul 2025 07:58:29 GMT express:router:layer new '/'
Thu, 24 Jul 2025 07:58:29 GMT express:router:route post '/register'
Thu, 24 Jul 2025 07:58:29 GMT express:router:layer new '/'
Thu, 24 Jul 2025 07:58:29 GMT express:router:route post '/register'
Thu, 24 Jul 2025 07:58:29 GMT express:router:layer new '/'
Thu, 24 Jul 2025 07:58:29 GMT express:router:route post '/register'
Thu, 24 Jul 2025 07:58:29 GMT express:router:layer new '/'
Thu, 24 Jul 2025 07:58:29 GMT express:router:route post '/register'
Thu, 24 Jul 2025 07:58:29 GMT express:router:layer new '/'
Thu, 24 Jul 2025 07:58:29 GMT express:router:route new '/login'
Thu, 24 Jul 2025 07:58:29 GMT express:router:layer new '/login'
Thu, 24 Jul 2025 07:58:29 GMT express:router:route post '/login'
Thu, 24 Jul 2025 07:58:29 GMT express:router:layer new '/'
Thu, 24 Jul 2025 07:58:29 GMT express:router:route post '/login'
Thu, 24 Jul 2025 07:58:29 GMT express:router:layer new '/'
Thu, 24 Jul 2025 07:58:29 GMT express:router:route post '/login'
Thu, 24 Jul 2025 07:58:29 GMT express:router:layer new '/'
[2025-07-24 13:28:29.146] [31merror[39m: 	[31muncaughtException: Route.post() requires a callback function but got a [object Undefined][39m
[31mError: Route.post() requires a callback function but got a [object Undefined][39m
[31m    at Route.<computed> [as post] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:216:15)[39m
[31m    at proto.<computed> [as post] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:521:19)[39m
[31m    at Object.<anonymous> (/Users/<USER>/Documents/payment-main/backend/routes/auth.js:40:8)[39m
[31m    at Module._compile (node:internal/modules/cjs/loader:1529:14)[39m
[31m    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)[39m
[31m    at Module.load (node:internal/modules/cjs/loader:1275:32)[39m
[31m    at Module._load (node:internal/modules/cjs/loader:1096:12)[39m
[31m    at Module.require (node:internal/modules/cjs/loader:1298:19)[39m
[31m    at require (node:internal/modules/helpers:182:18)[39m
[31m    at Object.<anonymous> (/Users/<USER>/Documents/payment-main/backend/app.js:31:20)[39m
{
  "error": {},
  "stack": "Error: Route.post() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as post] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:216:15)\n    at proto.<computed> [as post] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:521:19)\n    at Object.<anonymous> (/Users/<USER>/Documents/payment-main/backend/routes/auth.js:40:8)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (/Users/<USER>/Documents/payment-main/backend/app.js:31:20)",
  "exception": true,
  "date": "Thu Jul 24 2025 13:28:29 GMT+0530 (India Standard Time)",
  "process": {
    "pid": 91529,
    "uid": 501,
    "gid": 20,
    "cwd": "/Users/<USER>/Documents/payment-main/backend",
    "execPath": "/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node",
    "version": "v20.19.3",
    "argv": [
      "/Users/<USER>/.nvm/versions/node/v20.19.3/bin/node",
      "/Users/<USER>/Documents/payment-main/backend/server.js"
    ],
    "memoryUsage": {
      "rss": 89743360,
      "heapTotal": 60682240,
      "heapUsed": 38763248,
      "external": 20504423,
      "arrayBuffers": 18263320
    }
  },
  "os": {
    "loadavg": [
      29.068359375,
      15.6826171875,
      13.1044921875
    ],
    "uptime": 18827
  },
  "trace": [
    {
      "column": 15,
      "file": "/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js",
      "function": "Route.<computed> [as post]",
      "line": 216,
      "method": "<computed> [as post]",
      "native": false
    },
    {
      "column": 19,
      "file": "/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js",
      "function": "proto.<computed> [as post]",
      "line": 521,
      "method": "<computed> [as post]",
      "native": false
    },
    {
      "column": 8,
      "file": "/Users/<USER>/Documents/payment-main/backend/routes/auth.js",
      "function": null,
      "line": 40,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1529,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1613,
      "method": ".js",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1275,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1096,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1298,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 182,
      "method": null,
      "native": false
    },
    {
      "column": 20,
      "file": "/Users/<USER>/Documents/payment-main/backend/app.js",
      "function": null,
      "line": 31,
      "method": null,
      "native": false
    }
  ],
  "service": "payment-gateway"
}
winston:file: stat done: error.log { size: 18962 }
winston:file: create stream start /Users/<USER>/Documents/payment-main/logs/error.log { flags: 'a' }
winston:file: create stream ok /Users/<USER>/Documents/payment-main/logs/error.log
winston:file: stat done: combined.log { size: 32125 }
winston:file: create stream start /Users/<USER>/Documents/payment-main/logs/combined.log { flags: 'a' }
winston:file: create stream ok /Users/<USER>/Documents/payment-main/logs/combined.log
winston:file: file open ok /Users/<USER>/Documents/payment-main/logs/error.log
winston:file: file open ok /Users/<USER>/Documents/payment-main/logs/combined.log
[2025-07-24 13:28:30.907] [32minfo[39m: 	[32mEmail server connection established[39m
{
  "service": "payment-gateway"
}
winston:file: written true false
winston:file: logged 32256 {"level":"info","message":"Email server connection established","service":"payment-gateway","timestamp":"2025-07-24 13:28:30.910"}

