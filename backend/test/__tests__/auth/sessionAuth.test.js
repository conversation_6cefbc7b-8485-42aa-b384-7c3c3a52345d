const request = require('supertest');
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const { app, server } = require('../../../app');
const User = require('../../../models/User');
const { createAndLoginUser, loginAsAdmin } = require('../../helpers/testAuth');
const emailService = require('../../../utils/email');

// Mock the email module to prevent actual email sending
jest.mock('../../../utils/email', () => ({
  sendEmail: jest.fn().mockResolvedValue({ messageId: 'mocked-email-id' }),
  sendVerificationEmail: jest.fn().mockResolvedValue({ messageId: 'mocked-verification-email' }),
  sendPasswordResetEmail: jest.fn().mockResolvedValue({ messageId: 'mocked-password-reset-email' }),
  sendPasswordChangedEmail: jest.fn().mockResolvedValue({ messageId: 'mocked-password-changed-email' })
}));

// Mock the email template rendering
jest.mock('ejs', () => ({
  render: jest.fn().mockImplementation((template, data, callback) => {
    callback(null, '<html>Mocked Email Template</html>');
  })
}));

describe('Session-based Authentication', () => {
  // Clean up test database
  beforeAll(async () => {
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
  });

  afterEach(async () => {
    // Clean up test data after each test
    await User.deleteMany({});
  });

  afterEach(async () => {
    // Clear all mocks after each test
    jest.clearAllMocks();
    
    // Clean up the test database
    await User.deleteMany({});
  });

  afterAll(async () => {
    // Close database connection and server after all tests
    await mongoose.connection.close();
    if (server && server.close) {
      await new Promise(resolve => server.close(resolve));
    }
  });

  describe('User Authentication Flow', () => {
    let sendEmailMock;
    
    beforeEach(() => {
      // Log the current working directory and module paths for debugging
      console.log('Current working directory:', process.cwd());
      console.log('Module paths:', module.paths);
      
      // Mock the sendEmail function to resolve successfully
      const emailModule = require('../../../utils/email');
      console.log('Email module loaded:', !!emailModule);
      console.log('sendEmail function exists:', typeof emailModule.sendEmail === 'function');
      
      sendEmailMock = jest.spyOn(emailModule, 'sendEmail')
        .mockImplementation((...args) => {
          console.log('sendEmail mock called with:', args);
          return Promise.resolve({ message: 'Email sent' });
        });
      
      console.log('sendEmail mock set up:', !!sendEmailMock);
    });
    
    afterEach(() => {
      // Restore all mocks after each test
      jest.restoreAllMocks();
    });

    it('should register a new user', async () => {
      // Log the current state of the mock
      console.log('Test starting, sendEmailMock:', {
        isMock: jest.isMockFunction(sendEmailMock),
        calls: sendEmailMock.mock.calls
      });
      
      // Ensure our mock is properly set up
      expect(jest.isMockFunction(sendEmailMock)).toBe(true);
      // Create a test user
      const userData = {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'Test1234!',
        businessName: 'Test Business',
        phone: '1234567890'
      };

      // First, verify the user doesn't exist
      let user = await User.findOne({ email: userData.email });
      expect(user).toBeNull();

      // Make the registration request to the actual endpoint
      const res = await request(app)
        .post('/api/auth/register')
        .send(userData);

      // Check if the user was created in the database
      user = await User.findOne({ email: userData.email });
      
      // Verify user was created with correct data
      expect(user).not.toBeNull();
      expect(user.name).toBe(userData.name);
      expect(user.email).toBe(userData.email);
      expect(user.businessName).toBe(userData.businessName);
      expect(user.phone).toBe(userData.phone);
      expect(user.role).toBe('merchant');
      
      // Verify email was sent
      expect(sendEmailMock).toHaveBeenCalledWith({
        email: userData.email,
        subject: 'Verify your email',
        template: 'email-verification',
        context: expect.objectContaining({
          name: userData.name,
          verificationUrl: expect.stringContaining('/api/auth/verify-email/')
        })
      });

      // Check the response
      expect(res.statusCode).toEqual(201);
      expect(res.body).toHaveProperty('success', true);
      expect(res.body).toHaveProperty('user');
      expect(res.body.user.email).toBe(userData.email);
      expect(res.body.user.name).toBe(userData.name);
    });

    it('should login with correct credentials', async () => {
      const email = '<EMAIL>';
      const password = 'test1234';
      
      // Create a test user
      await User.create({
        name: 'Login Test',
        email,
        password: await bcrypt.hash(password, 10),
        isVerified: true
      });

      const res = await request(app)
        .post('/api/auth/login')
        .send({ email, password });

      expect(res.statusCode).toEqual(200);
      expect(res.body).toHaveProperty('success', true);
      expect(res.body).toHaveProperty('user');
      expect(res.body.user.email).toBe(email);
      
      // Check if session cookie is set
      expect(res.headers['set-cookie']).toBeDefined();
      const sessionCookie = res.headers['set-cookie'].find(c => c.startsWith('connect.sid'));
      expect(sessionCookie).toBeDefined();
    });

    it('should not allow access to protected routes without authentication', async () => {
      const res = await request(app)
        .get('/api/auth/me')
        .expect(401);

      expect(res.body).toHaveProperty('success', false);
      expect(res.body).toHaveProperty('message', 'Authentication required');
    });

    it('should allow access to protected routes with valid session', async () => {
      const { user, agent } = await createAndLoginUser(app);
      
      const res = await agent
        .get('/api/auth/me')
        .expect(200);

      expect(res.body).toHaveProperty('success', true);
      expect(res.body).toHaveProperty('user');
      expect(res.body.user.email).toBe(user.email);
    });

    it('should logout and clear session', async () => {
      const { agent } = await createAndLoginUser(app);
      
      // First verify we have an active session
      await agent
        .get('/api/auth/me')
        .expect(200);
      
      // Logout
      const res = await agent
        .post('/api/auth/logout')
        .expect(200);
      
      expect(res.body).toHaveProperty('success', true);
      
      // Verify session is cleared
      await agent
        .get('/api/auth/me')
        .expect(401);
    });
  });

  describe('Role-based Access Control', () => {
    it('should allow admin to access admin-only routes', async () => {
      const { agent } = await loginAsAdmin(app);
      
      // Assuming /api/admin is an admin-only route
      const res = await agent
        .get('/api/admin')
        .expect(200);
      
      expect(res.body).toHaveProperty('success', true);
    });

    it('should not allow non-admin to access admin-only routes', async () => {
      const { agent } = await createAndLoginUser(app, { role: 'merchant' });
      
      // Assuming /api/admin is an admin-only route
      const res = await agent
        .get('/api/admin')
        .expect(403);
      
      expect(res.body).toHaveProperty('success', false);
      expect(res.body).toHaveProperty('message');
    });
  });
});
