"use strict";(self.webpackChunkpayment_gateway_frontend=self.webpackChunkpayment_gateway_frontend||[]).push([[197],{1197:(e,t,n)=>{n.r(t),n.d(t,{default:()=>$});var a=n(5043),s=n(3216),r=n(5475),o=n(6213);const i=e=>"number"==typeof e&&!isNaN(e),l=e=>"string"==typeof e,d=e=>"function"==typeof e,c=e=>(0,a.isValidElement)(e)||l(e)||d(e)||i(e);function u(e){let{enter:t,exit:n,appendPosition:s=!1,collapse:r=!0,collapseDuration:o=300}=e;return function(e){let{children:i,position:l,preventExitTransition:d,done:c,nodeRef:u,isIn:f,playToast:m}=e;const p=s?`${t}--${l}`:t,g=s?`${n}--${l}`:n,h=(0,a.useRef)(0);return(0,a.useLayoutEffect)(()=>{const e=u.current,t=p.split(" "),n=a=>{a.target===u.current&&(m(),e.removeEventListener("animationend",n),e.removeEventListener("animationcancel",n),0===h.current&&"animationcancel"!==a.type&&e.classList.remove(...t))};e.classList.add(...t),e.addEventListener("animationend",n),e.addEventListener("animationcancel",n)},[]),(0,a.useEffect)(()=>{const e=u.current,t=()=>{e.removeEventListener("animationend",t),r?function(e,t,n){void 0===n&&(n=300);const{scrollHeight:a,style:s}=e;requestAnimationFrame(()=>{s.minHeight="initial",s.height=a+"px",s.transition=`all ${n}ms`,requestAnimationFrame(()=>{s.height="0",s.padding="0",s.margin="0",setTimeout(t,n)})})}(e,c,o):c()};f||(d?t():(h.current=1,e.className+=` ${g}`,e.addEventListener("animationend",t)))},[f]),a.createElement(a.Fragment,null,i)}}const f=new Map;let m=[];const p=new Set,g=()=>f.size>0;function h(e,t){var n;if(t)return!(null==(n=f.get(t))||!n.isToastActive(e));let a=!1;return f.forEach(t=>{t.isToastActive(e)&&(a=!0)}),a}function v(e,t){c(e)&&(g()||m.push({content:e,options:t}),f.forEach(n=>{n.buildToast(e,t)}))}function x(e,t){f.forEach(n=>{null!=t&&null!=t&&t.containerId?(null==t?void 0:t.containerId)===n.id&&n.toggle(e,null==t?void 0:t.id):n.toggle(e,null==t?void 0:t.id)})}let y=1;const w=()=>""+y++;function b(e){return e&&(l(e.toastId)||i(e.toastId))?e.toastId:w()}function N(e,t){return v(e,t),t.toastId}function j(e,t){return{...t,type:t&&t.type||e,toastId:b(t)}}function C(e){return(t,n)=>N(t,j(e,n))}function E(e,t){return N(e,j("default",t))}E.loading=(e,t)=>N(e,j("default",{isLoading:!0,autoClose:!1,closeOnClick:!1,closeButton:!1,draggable:!1,...t})),E.promise=function(e,t,n){let a,{pending:s,error:r,success:o}=t;s&&(a=l(s)?E.loading(s,n):E.loading(s.render,{...n,...s}));const i={isLoading:null,autoClose:null,closeOnClick:null,closeButton:null,draggable:null},c=(e,t,s)=>{if(null==t)return void E.dismiss(a);const r={type:e,...i,...n,data:s},o=l(t)?{render:t}:t;return a?E.update(a,{...r,...o}):E(o.render,{...r,...o}),s},u=d(e)?e():e;return u.then(e=>c("success",o,e)).catch(e=>c("error",r,e)),u},E.success=C("success"),E.info=C("info"),E.error=C("error"),E.warning=C("warning"),E.warn=E.warning,E.dark=(e,t)=>N(e,j("default",{theme:"dark",...t})),E.dismiss=function(e){!function(e){var t;if(g()){if(null==e||l(t=e)||i(t))f.forEach(t=>{t.removeToast(e)});else if(e&&("containerId"in e||"id"in e)){const t=f.get(e.containerId);t?t.removeToast(e.id):f.forEach(t=>{t.removeToast(e.id)})}}else m=m.filter(t=>null!=e&&t.options.toastId!==e)}(e)},E.clearWaitingQueue=function(e){void 0===e&&(e={}),f.forEach(t=>{!t.props.limit||e.containerId&&t.id!==e.containerId||t.clearQueue()})},E.isActive=h,E.update=function(e,t){void 0===t&&(t={});const n=((e,t)=>{var n;let{containerId:a}=t;return null==(n=f.get(a||1))?void 0:n.toasts.get(e)})(e,t);if(n){const{props:a,content:s}=n,r={delay:100,...a,...t,toastId:t.toastId||e,updateId:w()};r.toastId!==e&&(r.staleId=e);const o=r.render||s;delete r.render,N(o,r)}},E.done=e=>{E.update(e,{progress:1})},E.onChange=function(e){return p.add(e),()=>{p.delete(e)}},E.play=e=>x(!0,e),E.pause=e=>x(!1,e);"undefined"!=typeof window?a.useLayoutEffect:a.useEffect;const I=function(e,t){return void 0===t&&(t=!1),{enter:`Toastify--animate Toastify__${e}-enter`,exit:`Toastify--animate Toastify__${e}-exit`,appendPosition:t}};u(I("bounce",!0)),u(I("slide",!0)),u(I("zoom")),u(I("flip"));var k=n(9772),L=n(9954),T=n(2248),R=n(6742),P=n(579);const F=e=>(0,P.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",...e,children:(0,P.jsx)("path",{d:"M21 12a9 9 0 1 1-6.219-8.56"})}),$=()=>{const e=(0,s.Zp)(),[t,n]=(0,a.useState)(!1),[i,l]=(0,a.useState)({name:"",email:"",password:"",confirmPassword:""}),d=e=>{const{name:t,value:n}=e.target;l(e=>({...e,[t]:n}))};return(0,P.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,P.jsxs)(R.Zp,{className:"w-full max-w-md",children:[(0,P.jsxs)(R.aR,{className:"space-y-1",children:[(0,P.jsx)(R.ZB,{className:"text-2xl font-bold text-center",children:"Create an account"}),(0,P.jsx)(R.BT,{className:"text-center",children:"Enter your information to create an account"})]}),(0,P.jsxs)("form",{onSubmit:async t=>{if(t.preventDefault(),i.password===i.confirmPassword)if(i.password.length<6)E.error("Password must be at least 6 characters long");else try{n(!0);await o.A.post("http://localhost:5001/api/api/v1/auth/register",{name:i.name,email:i.email,password:i.password});E.success("Registration successful! Please log in."),e("/login")}catch(r){var a,s;const e=(null===(a=r.response)||void 0===a||null===(s=a.data)||void 0===s?void 0:s.message)||"Registration failed. Please try again.";E.error(e)}finally{n(!1)}else E.error("Passwords do not match")},children:[(0,P.jsxs)(R.Wu,{className:"space-y-4",children:[(0,P.jsxs)("div",{className:"space-y-2",children:[(0,P.jsx)(T.J,{htmlFor:"name",children:"Full Name"}),(0,P.jsx)(L.p,{id:"name",name:"name",type:"text",required:!0,value:i.name,onChange:d,disabled:t,placeholder:"John Doe"})]}),(0,P.jsxs)("div",{className:"space-y-2",children:[(0,P.jsx)(T.J,{htmlFor:"email",children:"Email"}),(0,P.jsx)(L.p,{id:"email",name:"email",type:"email",required:!0,value:i.email,onChange:d,disabled:t,placeholder:"<EMAIL>"})]}),(0,P.jsxs)("div",{className:"space-y-2",children:[(0,P.jsx)(T.J,{htmlFor:"password",children:"Password"}),(0,P.jsx)(L.p,{id:"password",name:"password",type:"password",required:!0,value:i.password,onChange:d,disabled:t,placeholder:"\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022"})]}),(0,P.jsxs)("div",{className:"space-y-2",children:[(0,P.jsx)(T.J,{htmlFor:"confirmPassword",children:"Confirm Password"}),(0,P.jsx)(L.p,{id:"confirmPassword",name:"confirmPassword",type:"password",required:!0,value:i.confirmPassword,onChange:d,disabled:t,placeholder:"\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022"})]})]}),(0,P.jsxs)(R.wL,{className:"flex flex-col space-y-4",children:[(0,P.jsxs)(k.$,{type:"submit",className:"w-full",disabled:t,children:[t&&(0,P.jsx)(F,{className:"mr-2 h-4 w-4 animate-spin"}),"Create Account"]}),(0,P.jsxs)("div",{className:"text-sm text-center text-muted-foreground",children:["Already have an account?"," ",(0,P.jsx)(r.N_,{to:"/login",className:"text-primary hover:underline",children:"Sign in"})]})]})]})]})})}},2248:(e,t,n)=>{n.d(t,{J:()=>l});var a=n(5043),s=n(917),r=n(3009),o=n(579);const i=(0,s.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,o.jsx)("label",{ref:t,className:(0,r.cn)(i(),n),...a})});l.displayName="Label"},6742:(e,t,n)=>{n.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>o,aR:()=>i,wL:()=>u});var a=n(5043),s=n(3009),r=n(579);const o=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,s.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",n),...a})});o.displayName="Card";const i=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",n),...a})});i.displayName="CardHeader";const l=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("h3",{ref:t,className:(0,s.cn)("text-2xl font-semibold leading-none tracking-tight",n),...a})});l.displayName="CardTitle";const d=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("p",{ref:t,className:(0,s.cn)("text-sm text-muted-foreground",n),...a})});d.displayName="CardDescription";const c=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,s.cn)("p-6 pt-0",n),...a})});c.displayName="CardContent";const u=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,s.cn)("flex items-center p-6 pt-0",n),...a})});u.displayName="CardFooter"},9772:(e,t,n)=>{n.d(t,{$:()=>d});var a=n(5043),s=n(6851),r=n(917),o=n(3009),i=n(579);const l=(0,r.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,t)=>{let{className:n,variant:a,size:r,asChild:d=!1,...c}=e;const u=d?s.DX:"button";return(0,i.jsx)(u,{className:(0,o.cn)(l({variant:a,size:r,className:n})),ref:t,...c})});d.displayName="Button"},9954:(e,t,n)=>{n.d(t,{p:()=>o});var a=n(5043),s=n(3009),r=n(579);const o=a.forwardRef((e,t)=>{let{className:n,type:a,...o}=e;return(0,r.jsx)("input",{type:a,className:(0,s.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",n),ref:t,...o})});o.displayName="Input"}}]);
//# sourceMappingURL=197.716a2cd4.chunk.js.map