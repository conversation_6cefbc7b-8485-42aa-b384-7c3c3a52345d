const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const User = require('./models/User');
const TraderAssignment = require('./models/TraderAssignment');
const Transaction = require('./models/Transaction');

async function checkDatabase() {
  try {
    console.log('🔍 Connecting to payment-gateway database...');
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/payment-gateway');
    console.log('✅ Connected successfully!');
    
    // Get all collections
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log('\n📊 COLLECTIONS IN payment-gateway DATABASE:');
    console.log('='.repeat(60));
    
    for (const collection of collections) {
      const count = await mongoose.connection.db.collection(collection.name).countDocuments();
      console.log(`${collection.name.padEnd(25)} | ${count.toString().padStart(5)} documents`);
      
      if (count > 0 && count <= 3) {
        // Show sample documents for small collections
        const samples = await mongoose.connection.db.collection(collection.name).find({}).limit(1).toArray();
        if (samples.length > 0) {
          const fields = Object.keys(samples[0]).filter(key => !key.startsWith('_')).slice(0, 5);
          console.log(`${' '.repeat(25)} | Fields: ${fields.join(', ')}`);
        }
      }
    }
    
    // Check specific collections we need
    console.log('\n🔍 CHECKING KEY COLLECTIONS:');
    console.log('='.repeat(60));
    
    // Check Users
    const userCount = await User.countDocuments();
    console.log(`Users: ${userCount} total`);
    if (userCount > 0) {
      const usersByRole = await User.aggregate([
        { $group: { _id: '$role', count: { $sum: 1 } } }
      ]);
      usersByRole.forEach(role => {
        console.log(`  - ${role._id}: ${role.count}`);
      });
    }
    
    // Check Trader Assignments
    const assignmentCount = await TraderAssignment.countDocuments();
    console.log(`\nTrader Assignments: ${assignmentCount} total`);
    if (assignmentCount > 0) {
      const assignmentsByStatus = await TraderAssignment.aggregate([
        { $group: { _id: '$status', count: { $sum: 1 } } }
      ]);
      assignmentsByStatus.forEach(status => {
        console.log(`  - ${status._id}: ${status.count}`);
      });
    }
    
    // Check Transactions
    const transactionCount = await Transaction.countDocuments();
    console.log(`\nTransactions: ${transactionCount} total`);
    if (transactionCount > 0) {
      const transactionsByStatus = await Transaction.aggregate([
        { $group: { _id: '$status', count: { $sum: 1 } } }
      ]);
      transactionsByStatus.forEach(status => {
        console.log(`  - ${status._id}: ${status.count}`);
      });
    }
    
    // Create sample data if collections are empty
    if (userCount === 0) {
      console.log('\n🔧 Creating sample users...');
      await createSampleUsers();
    }
    
    if (assignmentCount === 0 && userCount > 0) {
      console.log('\n🔧 Creating sample trader assignments...');
      await createSampleAssignments();
    }
    
    console.log('\n✅ Database check completed!');
    
  } catch (error) {
    console.error('❌ Database check failed:', error.message);
  } finally {
    await mongoose.connection.close();
  }
}

async function createSampleUsers() {
  try {
    const users = [
      {
        name: 'Admin User',
        email: '<EMAIL>',
        role: 'admin',
        isActive: true
      },
      {
        name: 'John Trader',
        email: '<EMAIL>',
        role: 'trader',
        phone: '+1234567890',
        isActive: true
      },
      {
        name: 'Alice Merchant',
        email: '<EMAIL>',
        role: 'merchant',
        businessName: 'Tech Store Inc',
        phone: '+1234567891',
        isActive: true
      },
      {
        name: 'Bob Trader',
        email: '<EMAIL>',
        role: 'trader',
        phone: '+1234567892',
        isActive: true
      },
      {
        name: 'Sarah Merchant',
        email: '<EMAIL>',
        role: 'merchant',
        businessName: 'Fashion Boutique LLC',
        phone: '+1234567893',
        isActive: true
      }
    ];
    
    await User.insertMany(users);
    console.log(`✅ Created ${users.length} sample users`);
  } catch (error) {
    console.error('❌ Failed to create sample users:', error.message);
  }
}

async function createSampleAssignments() {
  try {
    // Get traders and merchants
    const traders = await User.find({ role: 'trader' });
    const merchants = await User.find({ role: 'merchant' });
    const admin = await User.findOne({ role: 'admin' });
    
    if (traders.length === 0 || merchants.length === 0) {
      console.log('⚠️ No traders or merchants found, skipping assignment creation');
      return;
    }
    
    const assignments = [
      {
        traderId: traders[0]._id,
        merchantId: merchants[0]._id,
        assignedBy: admin?._id || traders[0]._id,
        assignmentType: 'permanent',
        startDate: new Date(),
        status: 'active',
        collectionTarget: {
          amount: 10000,
          period: 'monthly'
        },
        permissions: ['view_transactions', 'process_payments'],
        notes: 'Primary trader for tech store operations'
      }
    ];
    
    if (traders.length > 1 && merchants.length > 1) {
      assignments.push({
        traderId: traders[1]._id,
        merchantId: merchants[1]._id,
        assignedBy: admin?._id || traders[1]._id,
        assignmentType: 'temporary',
        startDate: new Date(),
        endDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days from now
        status: 'active',
        collectionTarget: {
          amount: 5000,
          period: 'monthly'
        },
        permissions: ['view_transactions'],
        notes: 'Temporary assignment for fashion boutique'
      });
    }
    
    await TraderAssignment.insertMany(assignments);
    console.log(`✅ Created ${assignments.length} sample trader assignments`);
  } catch (error) {
    console.error('❌ Failed to create sample assignments:', error.message);
  }
}

// Run the check
checkDatabase();
