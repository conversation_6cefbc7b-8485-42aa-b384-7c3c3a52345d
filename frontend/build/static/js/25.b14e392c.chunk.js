"use strict";(self.webpackChunkpayment_gateway_frontend=self.webpackChunkpayment_gateway_frontend||[]).push([[25],{2025:(e,s,a)=>{a.r(s),a.d(s,{default:()=>N});var t=a(5043),i=a(6742),l=a(9772),c=a(9954),r=a(2248),n=a(6736),d=a(1508),o=a(3797);const h=(0,o.A)("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]]);var m=a(8283),x=a(6713),u=a(1024),p=a(5979),j=a(4068);const y=(0,o.A)("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]]),v=(0,o.A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var f=a(8567),g=a(579);const N=()=>{const[e,s]=(0,t.useState)("sk_test_51N1lZ2SJv8rX9vX7..."),[a,o]=(0,t.useState)(!1),[N,k]=(0,t.useState)("https://your-merchant-url.com/api/webhooks/payment"),[b,w]=(0,t.useState)(!1),[A,C]=(0,t.useState)(!1),I=e=>{navigator.clipboard.writeText(e),w(!0),setTimeout(()=>w(!1),2e3)};return(0,g.jsxs)("div",{className:"space-y-6",children:[(0,g.jsxs)("div",{children:[(0,g.jsx)("h2",{className:"text-2xl font-bold tracking-tight",children:"Integration Settings"}),(0,g.jsx)("p",{className:"text-muted-foreground",children:"Configure how your application connects with our payment gateway"})]}),(0,g.jsxs)(n.tU,{defaultValue:"api",className:"space-y-4",children:[(0,g.jsxs)(n.j7,{children:[(0,g.jsxs)(n.Xi,{value:"api",children:[(0,g.jsx)(h,{className:"mr-2 h-4 w-4"}),"API Keys"]}),(0,g.jsxs)(n.Xi,{value:"webhooks",children:[(0,g.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"Webhooks"]}),(0,g.jsxs)(n.Xi,{value:"security",children:[(0,g.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Security"]})]}),(0,g.jsx)(n.av,{value:"api",className:"space-y-4",children:(0,g.jsxs)(i.Zp,{children:[(0,g.jsxs)(i.aR,{children:[(0,g.jsx)(i.ZB,{children:"API Keys"}),(0,g.jsx)(i.BT,{children:"Your API keys are used to authenticate requests to our API. Keep them secret and never share them."})]}),(0,g.jsxs)(i.Wu,{className:"space-y-6",children:[(0,g.jsxs)("div",{className:"flex items-center justify-between space-x-4",children:[(0,g.jsxs)("div",{className:"flex-1",children:[(0,g.jsx)(r.J,{htmlFor:"api-key",children:"Secret API Key"}),(0,g.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,g.jsx)(c.p,{id:"api-key",value:e,readOnly:!0,className:"font-mono"}),(0,g.jsx)(l.$,{variant:"outline",size:"icon",onClick:()=>I(e),disabled:b,children:b?(0,g.jsx)(u.A,{className:"h-4 w-4"}):(0,g.jsx)(p.A,{className:"h-4 w-4"})})]})]}),(0,g.jsx)(l.$,{variant:"outline",onClick:()=>{C(!0),setTimeout(()=>{s(`sk_${Math.random().toString(36).substring(2,42)}`),C(!1)},1e3)},disabled:A,className:"mt-6",children:A?(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(j.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Regenerating..."]}):"Regenerate Key"})]}),(0,g.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,g.jsx)(d.d,{id:"live-mode",checked:a,onCheckedChange:o}),(0,g.jsx)(r.J,{htmlFor:"live-mode",children:a?"Live Mode":"Test Mode"}),(0,g.jsx)(f.E,{variant:a?"destructive":"secondary",className:"ml-2",children:a?"Charges will be processed":"No real charges will be processed"})]}),(0,g.jsxs)("div",{className:"rounded-md bg-muted p-4",children:[(0,g.jsx)("h4",{className:"font-medium mb-2",children:"API Documentation"}),(0,g.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"Check out our API documentation for detailed integration guides and examples."}),(0,g.jsxs)(l.$,{variant:"outline",size:"sm",children:[(0,g.jsx)(y,{className:"mr-2 h-4 w-4"}),"View API Documentation"]})]})]})]})}),(0,g.jsx)(n.av,{value:"webhooks",className:"space-y-4",children:(0,g.jsxs)(i.Zp,{children:[(0,g.jsxs)(i.aR,{children:[(0,g.jsx)(i.ZB,{children:"Webhook Configuration"}),(0,g.jsx)(i.BT,{children:"Receive real-time updates about payment events directly to your server."})]}),(0,g.jsx)(i.Wu,{children:(0,g.jsxs)("div",{className:"space-y-4",children:[(0,g.jsxs)("div",{children:[(0,g.jsx)(r.J,{htmlFor:"webhook-url",children:"Webhook URL"}),(0,g.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,g.jsx)(c.p,{id:"webhook-url",value:N,onChange:e=>k(e.target.value),placeholder:"https://your-merchant-url.com/api/webhooks"}),(0,g.jsxs)(l.$,{variant:"outline",onClick:()=>I(N),children:[(0,g.jsx)(p.A,{className:"mr-2 h-4 w-4"}),b?"Copied!":"Copy"]})]})]}),(0,g.jsxs)("div",{className:"rounded-md bg-muted p-4",children:[(0,g.jsx)("h4",{className:"font-medium mb-2",children:"Webhook Events"}),(0,g.jsx)("p",{className:"text-sm text-muted-foreground mb-2",children:"We'll send POST requests to your webhook URL for the following events:"}),(0,g.jsxs)("ul",{className:"list-disc pl-5 space-y-1 text-sm text-muted-foreground",children:[(0,g.jsxs)("li",{children:[(0,g.jsx)("code",{children:"payment.succeeded"})," - When a payment is successfully completed"]}),(0,g.jsxs)("li",{children:[(0,g.jsx)("code",{children:"payment.failed"})," - When a payment fails"]}),(0,g.jsxs)("li",{children:[(0,g.jsx)("code",{children:"payment.refunded"})," - When a payment is refunded"]}),(0,g.jsxs)("li",{children:[(0,g.jsx)("code",{children:"charge.dispute.created"})," - When a dispute is created"]})]})]})]})})]})}),(0,g.jsx)(n.av,{value:"security",className:"space-y-4",children:(0,g.jsxs)(i.Zp,{children:[(0,g.jsxs)(i.aR,{children:[(0,g.jsx)(i.ZB,{children:"Security Settings"}),(0,g.jsx)(i.BT,{children:"Manage your account security and access controls."})]}),(0,g.jsxs)(i.Wu,{className:"space-y-6",children:[(0,g.jsxs)("div",{className:"flex items-center justify-between",children:[(0,g.jsxs)("div",{children:[(0,g.jsx)("h4",{className:"font-medium",children:"Two-Factor Authentication"}),(0,g.jsx)("p",{className:"text-sm text-muted-foreground",children:"Add an extra layer of security to your account"})]}),(0,g.jsxs)(l.$,{variant:"outline",children:[(0,g.jsx)(v,{className:"mr-2 h-4 w-4"}),"Configure 2FA"]})]}),(0,g.jsxs)("div",{className:"border-t pt-4",children:[(0,g.jsx)("h4",{className:"font-medium mb-2",children:"IP Whitelisting"}),(0,g.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"Restrict API access to specific IP addresses for enhanced security."}),(0,g.jsxs)("div",{className:"space-y-2",children:[(0,g.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,g.jsx)(c.p,{placeholder:"***********",className:"max-w-xs"}),(0,g.jsx)(l.$,{variant:"outline",children:"Add IP"})]}),(0,g.jsx)("p",{className:"text-xs text-muted-foreground",children:"Leave empty to allow access from any IP address (not recommended for production)"})]})]})]})]})})]})]})}},4068:(e,s,a)=>{a.d(s,{A:()=>t});const t=(0,a(3797).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},5979:(e,s,a)=>{a.d(s,{A:()=>t});const t=(0,a(3797).A)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},6713:(e,s,a)=>{a.d(s,{A:()=>t});const t=(0,a(3797).A)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},9954:(e,s,a)=>{a.d(s,{p:()=>c});var t=a(5043),i=a(3009),l=a(579);const c=t.forwardRef((e,s)=>{let{className:a,type:t,...c}=e;return(0,l.jsx)("input",{type:t,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:s,...c})});c.displayName="Input"}}]);
//# sourceMappingURL=25.b14e392c.chunk.js.map