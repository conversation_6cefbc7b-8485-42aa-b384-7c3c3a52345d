import axios, { AxiosError, Axios<PERSON>nstance, AxiosRequestConfig, AxiosResponse } from 'axios';
// Authentication removed - no token management needed
import { toast } from '../components/ui/use-toast';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

class ApiClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      headers: {
        'Content-Type': 'application/json',
      },
      withCredentials: true,
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor (no authentication)
    this.client.interceptors.request.use(
      (config) => {
        // No token needed - authentication removed
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response: AxiosResponse) => response,
      (error: AxiosError) => {
        const status = error.response?.status;
        const data = error.response?.data as { message?: string };
        
        // Handle 401 Unauthorized (no auth redirect needed)
        if (status === 401) {
          console.log('Unauthorized request - authentication removed');
        }

        // Show error toast
        if (data?.message) {
          toast({
            title: 'Error',
            description: data.message,
            variant: 'destructive',
          });
        } else if (error.message) {
          toast({
            title: 'Error',
            description: error.message,
            variant: 'destructive',
          });
        }

        return Promise.reject(error);
      }
    );
  }

  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.get<T>(url, config);
    return response.data;
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.post<T>(url, data, config);
    return response.data;
  }

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.put<T>(url, data, config);
    return response.data;
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.delete<T>(url, config);
    return response.data;
  }

  // Add other HTTP methods as needed
}

export const apiClient = new ApiClient();
