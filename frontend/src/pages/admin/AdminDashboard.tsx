import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Grid, 
  Paper, 
  Typography, 
  CircularProgress,
  Card,
  CardContent,
  CardHeader,
  Divider,
  useTheme,
  IconButton,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import {
  People as PeopleIcon,
  Payment as PaymentIcon,
  Store as StoreIcon,
  Receipt as ReceiptIcon,
  MoreVert as MoreVertIcon,
  ArrowUpward as ArrowUpwardIcon,
  ArrowDownward as ArrowDownwardIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon
} from '@mui/icons-material';
import { useQuery } from '@tanstack/react-query';
import { adminService } from '../../services/adminService';
import { formatCurrency } from '../../utils/formatters';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';

const StatCard = ({ title, value, icon, trend, trendValue, trendLabel, color = 'primary' }) => {
  const TrendIcon = trend === 'up' ? ArrowUpwardIcon : ArrowDownwardIcon;
  const trendColor = trend === 'up' ? 'success.main' : 'error.main';
  
  return (
    <Card>
      <CardHeader
        title={title}
        action={icon}
        titleTypographyProps={{ variant: 'subtitle2' }}
      />
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Typography variant="h4">{value}</Typography>
          {trend && (
            <Box display="flex" alignItems="center" color={trendColor}>
              <TrendIcon fontSize="small" />
              <Typography variant="body2" ml={0.5}>
                {trendValue}%
              </Typography>
            </Box>
          )}
        </Box>
        {trendLabel && (
          <Typography variant="caption" color="textSecondary">
            {trendLabel}
          </Typography>
        )}
      </CardContent>
    </Card>
  );
};\n
const AdminDashboard = () => {
  const theme = useTheme();
  const [anchorEl, setAnchorEl] = useState(null);
  const [timeRange, setTimeRange] = useState('month');
  
  const { data: stats, isLoading } = useQuery(['admin', 'dashboard', 'stats'], 
    () => adminService.getDashboardStats({ timeRange }),
    { keepPreviousData: true }
  );
  
  const { data: recentTransactions = [] } = useQuery(
    ['admin', 'dashboard', 'recent-transactions'],
    () => adminService.getRecentTransactions(),
    { enabled: !isLoading }
  );
  
  const handleMenuClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  
  const handleMenuClose = () => {
    setAnchorEl(null);
  };
  
  const handleTimeRangeChange = (range) => {
    setTimeRange(range);
    handleMenuClose();
  };
  
  // Mock data for the chart - replace with actual data from your API
  const chartData = [
    { name: 'Jan', revenue: 4000, transactions: 2400 },
    { name: 'Feb', revenue: 3000, transactions: 1398 },
    { name: 'Mar', revenue: 2000, transactions: 9800 },
    { name: 'Apr', revenue: 2780, transactions: 3908 },
    { name: 'May', revenue: 1890, transactions: 4800 },
    { name: 'Jun', revenue: 2390, transactions: 3800 },
  ];

  return (
    <Box p={3}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">Dashboard Overview</Typography>
        <Box>
          <IconButton
            aria-label="time range"
            aria-controls="time-range-menu"
            aria-haspopup="true"
            onClick={handleMenuClick}
            color="inherit"
          >
            <MoreVertIcon />
          </IconButton>
          <Menu
            id="time-range-menu"
            anchorEl={anchorEl}
            keepMounted
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
          >
            <MenuItem onClick={() => handleTimeRangeChange('week')}>This Week</MenuItem>
            <MenuItem onClick={() => handleTimeRangeChange('month')}>This Month</MenuItem>
            <MenuItem onClick={() => handleTimeRangeChange('year')}>This Year</MenuItem>
          </Menu>
        </Box>
      </Box>
      
      {isLoading ? (
        <Box display="flex" justifyContent="center" p={4}>
          <CircularProgress />
        </Box>
      ) : (
        <>
          <Grid container spacing={3} mb={3}>
            <Grid item xs={12} sm={6} md={3}>
              <StatCard
                title="Total Revenue"
                value={formatCurrency(stats?.totalRevenue || 0)}
                icon={<PaymentIcon color="primary" />}
                trend="up"
                trendValue="12.5"
                trendLabel="vs last month"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <StatCard
                title="Active Merchants"
                value={stats?.activeMerchants || 0}
                icon={<StoreIcon color="secondary" />}
                trend="up"
                trendValue="5.2"
                trendLabel="vs last month"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <StatCard
                title="Total Transactions"
                value={stats?.totalTransactions || 0}
                icon={<ReceiptIcon color="success" />}
                trend="up"
                trendValue="8.7"
                trendLabel="vs last month"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <StatCard
                title="Active Traders"
                value={stats?.activeTraders || 0}
                icon={<PeopleIcon color="warning" />}
                trend="down"
                trendValue="2.1"
                trendLabel="vs last month"
              />
            </Grid>
          </Grid>
          
          <Grid container spacing={3} mb={3}>
            <Grid item xs={12} md={8}>
              <Card>
                <CardHeader 
                  title="Revenue & Transactions" 
                  subheader={`Last ${timeRange === 'week' ? '7' : timeRange === 'month' ? '30' : '12'} days`}
                />
                <Divider />
                <Box p={3} height={350}>
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={chartData}
                      margin={{
                        top: 5,
                        right: 30,
                        left: 20,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis yAxisId="left" />
                      <YAxis yAxisId="right" orientation="right" />
                      <Tooltip />
                      <Legend />
                      <Line 
                        yAxisId="left"
                        type="monotone" 
                        dataKey="revenue" 
                        name="Revenue" 
                        stroke={theme.palette.primary.main} 
                        activeDot={{ r: 8 }} 
                      />
                      <Line 
                        yAxisId="right" 
                        type="monotone" 
                        dataKey="transactions" 
                        name="Transactions" 
                        stroke={theme.palette.secondary.main} 
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </Box>
              </Card>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <Card sx={{ height: '100%' }}>
                <CardHeader title="Recent Transactions" />
                <Divider />
                <Box p={2}>
                  {recentTransactions.length > 0 ? (
                    recentTransactions.map((tx) => (
                      <Box key={tx.id} mb={2} p={1} sx={{ '&:hover': { bgcolor: 'action.hover' } }}>
                        <Box display="flex" justifyContent="space-between" alignItems="center">
                          <Typography variant="subtitle2">{tx.merchantName}</Typography>
                          <Typography variant="body2" color={tx.amount >= 0 ? 'success.main' : 'error.main'}>
                            {formatCurrency(tx.amount)}
                          </Typography>
                        </Box>
                        <Box display="flex" justifyContent="space-between" alignItems="center">
                          <Typography variant="caption" color="textSecondary">
                            {new Date(tx.date).toLocaleDateString()}
                          </Typography>
                          <Typography variant="caption" color="textSecondary">
                            {tx.status}
                          </Typography>
                        </Box>
                      </Box>
                    ))
                  ) : (
                    <Box p={2} textAlign="center">
                      <Typography variant="body2" color="textSecondary">
                        No recent transactions
                      </Typography>
                    </Box>
                  )}
                </Box>
              </Card>
            </Grid>
          </Grid>
          
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader title="Quick Actions" />
                <Divider />
                <Box p={2}>
                  <Grid container spacing={2}>
                    {[
                      { icon: <PaymentIcon />, label: 'Process Payouts' },
                      { icon: <PeopleIcon />, label: 'Add New Merchant' },
                      { icon: <StoreIcon />, label: 'View All Merchants' },
                      { icon: <ReceiptIcon />, label: 'Generate Report' },
                    ].map((action, index) => (
                      <Grid item xs={6} key={index}>
                        <Card variant="outlined" sx={{ height: '100%' }}>
                          <Box p={2} textAlign="center">
                            <IconButton color="primary">
                              {action.icon}
                            </IconButton>
                            <Typography variant="body2">{action.label}</Typography>
                          </Box>
                        </Card>
                      </Grid>
                    ))}
                  </Grid>
                </Box>
              </Card>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader title="System Status" />>
                <Divider />
                <Box p={2}>
                  {[
                    { label: 'API Service', status: 'operational', lastChecked: '2 min ago' },
                    { label: 'Database', status: 'operational', lastChecked: '1 min ago' },
                    { label: 'Payment Processor', status: 'degraded', lastChecked: '5 min ago' },
                    { label: 'Email Service', status: 'operational', lastChecked: '10 min ago' },
                  ].map((service, index) => (
                    <Box key={index} display="flex" justifyContent="space-between" alignItems="center" mb={1} p={1}>
                      <Box display="flex" alignItems="center">
                        <Box
                          width={10}
                          height={10}
                          borderRadius="50%"
                          bgcolor={service.status === 'operational' ? 'success.main' : 'warning.main'}
                          mr={1}
                        />
                        <Typography variant="body2">{service.label}</Typography>
                      </Box>
                      <Typography variant="caption" color="textSecondary">
                        {service.lastChecked}
                      </Typography>
                    </Box>
                  ))}
                </Box>
              </Card>
            </Grid>
          </Grid>
        </>
      )}
    </Box>
  );
};

export default AdminDashboard;
