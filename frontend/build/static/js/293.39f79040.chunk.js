"use strict";(self.webpackChunkpayment_gateway_frontend=self.webpackChunkpayment_gateway_frontend||[]).push([[293],{3293:(e,s,r)=>{r.r(s),r.d(s,{default:()=>j});r(5043);var a=r(6742),i=r(6736),l=r(7298),n=r(2246),c=r(2036),d=r(4199),x=r(9066),h=r(579);function j(){const{user:e}=(0,x.A)();return e?(0,h.jsxs)("div",{className:"space-y-6",children:[(0,h.jsxs)("div",{children:[(0,h.jsx)("h1",{className:"text-3xl font-bold",children:"Profile Settings"}),(0,h.jsx)("p",{className:"text-muted-foreground",children:"Manage your account settings and preferences"})]}),(0,h.jsxs)(i.tU,{defaultValue:"profile",className:"space-y-6",children:[(0,h.jsxs)(i.j7,{children:[(0,h.jsx)(i.Xi,{value:"profile",children:"Profile"}),(0,h.jsx)(i.Xi,{value:"security",children:"Security"}),(0,h.jsx)(i.Xi,{value:"api-keys",children:"API Keys"}),(0,h.jsx)(i.Xi,{value:"2fa",children:"Two-Factor Auth"})]}),(0,h.jsx)(i.av,{value:"profile",className:"space-y-6",children:(0,h.jsxs)(a.Zp,{children:[(0,h.jsx)(a.aR,{children:(0,h.jsx)(a.ZB,{children:"Profile Information"})}),(0,h.jsx)(a.Wu,{children:(0,h.jsx)(l.g,{user:e})})]})}),(0,h.jsx)(i.av,{value:"security",children:(0,h.jsxs)(a.Zp,{children:[(0,h.jsx)(a.aR,{children:(0,h.jsx)(a.ZB,{children:"Change Password"})}),(0,h.jsx)(a.Wu,{children:(0,h.jsx)(n.G,{})})]})}),(0,h.jsx)(i.av,{value:"api-keys",children:(0,h.jsxs)(a.Zp,{children:[(0,h.jsx)(a.aR,{children:(0,h.jsx)(a.ZB,{children:"API Keys"})}),(0,h.jsx)(a.Wu,{children:(0,h.jsx)(d.x,{})})]})}),(0,h.jsx)(i.av,{value:"2fa",children:(0,h.jsxs)(a.Zp,{children:[(0,h.jsx)(a.aR,{children:(0,h.jsx)(a.ZB,{children:"Two-Factor Authentication"})}),(0,h.jsx)(a.Wu,{children:(0,h.jsx)(c.k,{})})]})})]})]}):(0,h.jsx)("div",{children:"Loading user data..."})}}}]);
//# sourceMappingURL=293.39f79040.chunk.js.map