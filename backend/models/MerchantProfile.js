const mongoose = require('mongoose');

const merchantProfileSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  businessName: {
    type: String,
    required: true,
    trim: true
  },
  businessType: {
    type: String,
    required: true,
    enum: ['sole_proprietorship', 'partnership', 'corporation', 'llc', 'other']
  },
  businessRegistrationNumber: {
    type: String,
    required: true,
    unique: true
  },
  taxId: {
    type: String,
    required: true
  },
  businessAddress: {
    street: { type: String, required: true },
    city: { type: String, required: true },
    state: { type: String, required: true },
    zipCode: { type: String, required: true },
    country: { type: String, required: true, default: 'US' }
  },
  contactInfo: {
    phone: { type: String, required: true },
    email: { type: String, required: true },
    website: String
  },
  kycDocuments: [{
    documentType: {
      type: String,
      required: true,
      enum: ['business_license', 'tax_certificate', 'bank_statement', 'identity_proof', 'address_proof']
    },
    documentUrl: { type: String, required: true },
    uploadedAt: { type: Date, default: Date.now },
    verificationStatus: {
      type: String,
      enum: ['pending', 'approved', 'rejected'],
      default: 'pending'
    },
    rejectionReason: String
  }],
  verificationStatus: {
    type: String,
    enum: ['pending', 'under_review', 'approved', 'rejected', 'suspended'],
    default: 'pending'
  },
  verificationNotes: String,
  approvedAt: Date,
  approvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  bankDetails: {
    accountHolderName: String,
    accountNumber: String,
    routingNumber: String,
    bankName: String,
    accountType: {
      type: String,
      enum: ['checking', 'savings', 'business']
    }
  },
  paymentSettings: {
    processingFee: { type: Number, default: 2.9 },
    settlementPeriod: { type: Number, default: 2 }, // days
    minimumPayout: { type: Number, default: 10 }
  },
  isActive: { type: Boolean, default: true }
}, {
  timestamps: true
});

module.exports = mongoose.model('MerchantProfile', merchantProfileSchema);