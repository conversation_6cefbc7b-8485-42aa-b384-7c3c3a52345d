import { useState, useCallback } from 'react';
import { useQuery } from '@tanstack/react-query';
import { format } from 'date-fns';
import { toast } from '../../components/ui/use-toast';
import { Button } from '../../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Badge } from '../../components/ui/badge';
import { MoreHorizontal, Plus, Users } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '../../components/ui/dropdown-menu';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '../../components/ui/dialog';
import { Input } from '../../components/ui/input';
import { Label } from '../../components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';
import type { AxiosError } from 'axios';

// Import services
import { merchantAdminService } from '../../services/merchantService';
import { DataTable } from '../../components/admin/DataTable';

// Types
type MerchantStatusUpdate = {
  status: 'active' | 'inactive' | 'suspended' | 'pending' | 'rejected';
  reason?: string;
};

type MerchantListResponse = {
  data: MerchantListItem[];
  total: number;
  page: number;
  limit: number;
};

type MerchantListItem = {
  id: string;
  _id: string;
  name: string;
  email: string;
  businessName?: string;
  businessType?: string;
  phone?: string;
  website?: string;
  isActive: boolean;
  isVerified: boolean;
  status: 'active' | 'inactive' | 'suspended' | 'pending' | 'rejected';
  role: string;
  createdAt: string;
  updatedAt: string;
};

const statusVariant: Record<string, 'default' | 'secondary' | 'destructive' | 'outline'> = {
  active: 'default',
  inactive: 'outline',
  verified: 'default',
  pending: 'secondary',
  rejected: 'destructive',
  suspended: 'outline',
};

const columns = [
  {
    header: 'Name',
    accessor: 'name',
  },
  {
    header: 'Email',
    accessor: 'email',
  },
  {
    header: 'Business',
    accessor: (row: MerchantListItem) => row.businessName || 'N/A',
  },
  {
    header: 'Type',
    accessor: (row: MerchantListItem) => row.businessType || 'N/A',
  },
  {
    header: 'Status',
    accessor: (row: MerchantListItem) => (
      <Badge variant={statusVariant[row.status] || 'outline'}>
        {row.status.charAt(0).toUpperCase() + row.status.slice(1)}
      </Badge>
    ),
  },
  {
    header: 'Created',
    accessor: (row: MerchantListItem) => format(new Date(row.createdAt), 'MMM d, yyyy'),
  },
];

export default function MerchantsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);

  const { 
    data: merchantsData, 
    isLoading, 
    error: queryError, 
    refetch 
  } = useQuery<MerchantListResponse, AxiosError>({
    queryKey: ['merchants', { page, limit, search: searchTerm }],
    queryFn: () => merchantAdminService.getAllMerchants({ 
      page, 
      limit, 
      search: searchTerm || undefined,
      status: 'active',
    }),
    keepPreviousData: true
  });

  const handleStatusUpdate = useCallback(async (id: string, status: MerchantStatusUpdate) => {
    try {
      await merchantAdminService.updateMerchantStatus(id, status.status, status.reason);
      toast({
        title: 'Status updated',
        description: 'Merchant status has been updated successfully.'
      });
      await refetch();
    } catch (error) {
      console.error('Error updating merchant status:', error);
      toast({
        title: 'Error',
        description: 'Failed to update merchant status. Please try again.',
        variant: 'destructive',
      });
    }
  }, [refetch]);

  const handleSearch = useCallback((query: string) => {
    setSearchTerm(query);
    setPage(1); // Reset to first page on new search
  }, []);

  const handlePageChange = useCallback((newPage: number) => {
    setPage(newPage);
  }, []);

  const handleLimitChange = useCallback((newLimit: number) => {
    setLimit(newLimit);
    setPage(1); // Reset to first page when changing page size
  }, []);

  // Add states for modals
  const [selectedMerchant, setSelectedMerchant] = useState<MerchantListItem | null>(null);
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [isEditOpen, setIsEditOpen] = useState(false);
  const [isDeleteOpen, setIsDeleteOpen] = useState(false);

  const handleViewProfile = useCallback((merchant: MerchantListItem) => {
    setSelectedMerchant(merchant);
    setIsProfileOpen(true);
  }, []);

  const handleEdit = useCallback((merchant: MerchantListItem) => {
    setSelectedMerchant(merchant);
    setIsEditOpen(true);
  }, []);

  const handleDelete = useCallback((merchant: MerchantListItem) => {
    setSelectedMerchant(merchant);
    setIsDeleteOpen(true);
  }, []);

  const confirmDelete = useCallback(async () => {
    if (!selectedMerchant) return;
    try {
      await merchantAdminService.deleteMerchant(selectedMerchant.id);
      toast({ 
        title: 'Deleted', 
        description: 'Merchant deleted successfully.' 
      });
      await refetch();
    } catch (error) {
      console.error('Error deleting merchant:', error);
      toast({ 
        title: 'Error', 
        description: 'Failed to delete merchant. Please try again.', 
        variant: 'destructive' 
      });
    } finally {
      setIsDeleteOpen(false);
    }
  }, [selectedMerchant, refetch]);

  const handleEditSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedMerchant) return;
    
    try {
      // Implement edit logic using merchantAdminService.updateMerchant
      // await merchantAdminService.updateMerchant(selectedMerchant.id, formData);
      toast({
        title: 'Success',
        description: 'Merchant updated successfully.',
      });
      setIsEditOpen(false);
      await refetch();
    } catch (error) {
      console.error('Error updating merchant:', error);
      toast({
        title: 'Error',
        description: 'Failed to update merchant. Please try again.',
        variant: 'destructive',
      });
    }
  }, [selectedMerchant, refetch]);

  const renderActions = useCallback((row: MerchantListItem) => (
    <div className="flex justify-end">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button 
            variant="ghost" 
            className="h-8 w-8 p-0"
            aria-label="Merchant actions"
          >
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem 
            onSelect={(e) => {
              e.preventDefault();
              handleViewProfile(row);
            }}
          >
            View Profile
          </DropdownMenuItem>
          <DropdownMenuItem 
            onSelect={(e) => {
              e.preventDefault();
              handleEdit(row);
            }}
          >
            Edit
          </DropdownMenuItem>
          <DropdownMenuItem 
            className="text-destructive focus:text-destructive"
            onSelect={(e) => {
              e.preventDefault();
              handleDelete(row);
            }}
          >
            Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  ), [handleViewProfile, handleEdit, handleDelete]);

  // Move modals inside the component
  const renderModals = () => (
    <>
      <Dialog open={isProfileOpen} onOpenChange={setIsProfileOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Merchant Profile</DialogTitle>
          </DialogHeader>
          {selectedMerchant && (
            <div className="space-y-4">
              <p><strong>Name:</strong> {selectedMerchant.name}</p>
              <p><strong>Email:</strong> {selectedMerchant.email}</p>
              <p><strong>Business:</strong> {selectedMerchant.businessName || 'N/A'}</p>
              <p><strong>Status:</strong> 
                <Badge variant={statusVariant[selectedMerchant.status] || 'outline'} className="ml-2">
                  {selectedMerchant.status.charAt(0).toUpperCase() + selectedMerchant.status.slice(1)}
                </Badge>
              </p>
              <p><strong>Created:</strong> {format(new Date(selectedMerchant.createdAt), 'MMM d, yyyy')}</p>
            </div>
          )}
        </DialogContent>
      </Dialog>

      <Dialog open={isEditOpen} onOpenChange={setIsEditOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Merchant</DialogTitle>
          </DialogHeader>
          {selectedMerchant && (
            <div className="space-y-4">
              <p>Edit form would go here</p>
            </div>
          )}
        </DialogContent>
      </Dialog>

      <Dialog open={isDeleteOpen} onOpenChange={setIsDeleteOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
          </DialogHeader>
          <p>Are you sure you want to delete this merchant?</p>
          <div className="flex justify-end space-x-2 mt-4">
            <Button variant="outline" onClick={() => setIsDeleteOpen(false)}>Cancel</Button>
            <Button variant="destructive" onClick={confirmDelete}>Delete</Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );

  return (
    <div className="container mx-auto py-6">
      {renderModals()}
      <div className="flex flex-col space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">Merchants Management</h1>
            <p className="text-muted-foreground">
              Manage and monitor merchant accounts
            </p>
          </div>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Add Merchant
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Merchants</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{merchantsData?.total || 0}</div>
              <p className="text-xs text-muted-foreground">
                Total merchant accounts
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active</CardTitle>
              <Badge variant="default" className="text-xs">Live</Badge>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {merchantsData?.data?.filter((m) => (m as MerchantListItem).status === 'active').length || 0}
              </div>
              <p className="text-xs text-muted-foreground">
                Currently active
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Verified</CardTitle>
              <Badge variant="default" className="text-xs">✓</Badge>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {merchantsData?.data?.filter((m) => (m as MerchantListItem).isVerified).length || 0}
              </div>
              <p className="text-xs text-muted-foreground">
                Verified accounts
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">This Month</CardTitle>
              <Badge variant="secondary" className="text-xs">New</Badge>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">2</div>
              <p className="text-xs text-muted-foreground">
                New registrations
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardHeader>
            <CardTitle>Merchants List</CardTitle>
          </CardHeader>
          <CardContent>
            <DataTable<MerchantListItem>
              columns={columns}
              data={merchantsData?.data || []}
              loading={isLoading}
              error={queryError || null}
              searchPlaceholder="Search merchants..."
              onSearch={handleSearch}
              searchValue={searchTerm}
              page={page}
              totalPages={merchantsData ? Math.ceil(merchantsData.total / limit) : 1}
              onPageChange={handlePageChange}
              limit={limit}
              onLimitChange={handleLimitChange}
              totalItems={merchantsData?.total || 0}
              actions={renderActions}
              emptyMessage="No merchants found"
            />

          </CardContent>
        </Card>
      </div>
    </div>
  );
}
