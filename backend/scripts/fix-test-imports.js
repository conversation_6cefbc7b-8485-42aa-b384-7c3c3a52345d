const fs = require('fs');
const path = require('path');

// Define the test directory
const testDir = path.join(__dirname, '..', 'test', '__tests__');

// Function to update import paths in a file
function updateImportPaths(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Fix relative paths for test utilities and fixtures
    content = content.replace(
      /require\('(\..*?\/)?(testUtils|fixtures\/db)'\)/g,
      (match, p1, p2) => {
        // Calculate the relative path from the current file to the target file
        const relativePath = path.relative(path.dirname(filePath), path.join(testDir, p2));
        return `require('${relativePath.startsWith('.') ? relativePath : './' + relativePath}')`;
      }
    );

    // Fix relative paths for middleware and models
    content = content.replace(
      /require\('(\.{2,}\/)+(middleware|models)\/[^']+'\)/g,
      (match) => {
        // Calculate the relative path from the test file to the src directory
        const relativePath = path.relative(
          path.dirname(filePath),
          path.join(__dirname, '..', match.match(/require\('(\.{2,}\/)+(middleware|models)\/[^']+'\)/)[0].match(/['"]([^'"]+)['"]/)[1])
        );
        return `require('${relativePath}')`;
      }
    );

    // Write the updated content back to the file
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Updated imports in ${path.relative(process.cwd(), filePath)}`);
  } catch (error) {
    console.error(`❌ Error updating imports in ${filePath}:`, error.message);
  }
}

// Recursively find all test files
function processDirectory(directory) {
  const files = fs.readdirSync(directory);
  
  files.forEach(file => {
    const fullPath = path.join(directory, file);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      processDirectory(fullPath);
    } else if (file.endsWith('.test.js')) {
      updateImportPaths(fullPath);
    }
  });
}

// Start processing from the test directory
console.log('🔍 Updating import paths in test files...');
processDirectory(testDir);
console.log('✨ Finished updating import paths!');
