const mongoose = require('mongoose');
const User = require('../models/User');
const MerchantProfile = require('../models/MerchantProfile');
const { v4: uuidv4 } = require('uuid');

async function seedData() {
  try {
    await mongoose.connect('mongodb://localhost:27017/payment_gateway', {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });

    // Clear existing data
    await User.deleteMany({ role: { $in: ['merchant', 'trader'] } });
    await MerchantProfile.deleteMany({});

    // Seed merchants
    const merchants = [];
    for (let i = 1; i <= 5; i++) {
      const user = new User({
        name: `Merchant ${i}`,
        email: `merchant${i}@example.com`,
        password: 'hashedpassword',
        role: 'merchant',
        status: 'active'
      });
      await user.save();

      const profile = new MerchantProfile({
        userId: user._id,
        businessName: `Business ${i}`,
        businessType: 'corporation',
        businessRegistrationNumber: `REG${i}000${i}`,
        taxId: `TAX${i}00${i}`,
        businessAddress: {
          street: `${i} Main St`,
          city: 'New York',
          state: 'NY',
          zipCode: '1000' + i,
          country: 'US'
        },
        contactInfo: {
          phone: `+*********${i}`,
          email: `contact${i}@business.com`,
          website: `https://business${i}.com`
        },
        verificationStatus: 'approved'
      });
      await profile.save();
      merchants.push(profile);
    }

    // Seed traders
    const traders = [];
    for (let i = 1; i <= 5; i++) {
      const trader = new User({
        name: `Trader ${i}`,
        email: `trader${i}@example.com`,
        password: 'hashedpassword',
        role: 'trader',
        status: 'active',
        traderId: uuidv4()
      });
      await trader.save();
      traders.push(trader);
    }

    console.log('Data seeded successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error seeding data:', error);
    process.exit(1);
  }
}

seedData();
