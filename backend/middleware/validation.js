const { validationResult, body, param, query, matchedData } = require('express-validator');
const { isEmail, isURL, isMobilePhone, isStrongPassword: isStrongPasswordValidator } = require('validator');
const logger = require('../utils/logger');
const ApiError = require('../utils/ApiError');

// Custom validation functions
const isStrongPassword = (value) => {
  if (!value) return false;
  // At least 8 chars, 1 uppercase, 1 lowercase, 1 number, 1 special char
  const strongRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
  return strongRegex.test(value);
};

const isPhoneNumber = (value) => {
  if (!value) return true; // Optional field
  // Supports international phone numbers with optional + prefix
  return isMobilePhone(value.replace(/\s+/g, ''), 'any', { strictMode: true });
};

const isURLOrRelativePath = (value) => {
  if (!value) return true; // Optional fields should pass
  try {
    // Check if it's a valid URL
    return isURL(value, {
      protocols: ['http', 'https'],
      require_tld: true,
      require_protocol: false,
      require_host: true,
      require_valid_protocol: true,
      allow_underscores: true,
      host_whitelist: false,
      host_blacklist: false,
      allow_trailing_dot: false,
      allow_protocol_relative_urls: false,
    });
  } catch (e) {
    return false;
  }
};

// Common validation rules
const commonRules = {
  // Authentication
  email: body('email')
    .trim()
    .notEmpty()
    .withMessage('Email is required')
    .isEmail()
    .withMessage('Please provide a valid email address')
    .normalizeEmail()
    .customSanitizer((email) => email.toLowerCase()),

  password: body('password')
    .notEmpty()
    .withMessage('Password is required')
    .isLength({ min: 8, max: 100 })
    .withMessage('Password must be between 8 and 100 characters')
    .custom(isStrongPassword)
    .withMessage(
      'Password must contain at least 1 uppercase letter, 1 lowercase letter, 1 number, and 1 special character',
    ),

  // User profile
  name: body('name')
    .trim()
    .notEmpty()
    .withMessage('Name is required')
    .isLength({ min: 2, max: 100 })
    .withMessage('Name must be between 2 and 100 characters')
    .matches(/^[a-zA-Z\s'-]+$/)
    .withMessage('Name can only contain letters, spaces, hyphens, and apostrophes'),

  firstName: body('firstName')
    .optional({ checkFalsy: true })
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('First name must be between 1 and 50 characters')
    .matches(/^[a-zA-Z\s'-]+$/)
    .withMessage('First name can only contain letters, spaces, hyphens, and apostrophes'),

  lastName: body('lastName')
    .optional({ checkFalsy: true })
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Last name must be between 1 and 50 characters')
    .matches(/^[a-zA-Z\s'-]+$/)
    .withMessage('Last name can only contain letters, spaces, hyphens, and apostrophes'),

  phone: body('phone')
    .optional({ checkFalsy: true })
    .trim()
    .custom(isPhoneNumber)
    .withMessage('Please provide a valid international phone number')
    .customSanitizer((phone) => phone.replace(/\s+/g, '')), // Remove all whitespace

  // Address
  address: body('address')
    .optional({ checkFalsy: true })
    .isObject()
    .withMessage('Address must be an object'),

  'address.street': body('address.street')
    .optional({ checkFalsy: true })
    .trim()
    .isLength({ max: 255 })
    .withMessage('Street address cannot exceed 255 characters'),

  'address.city': body('address.city')
    .optional({ checkFalsy: true })
    .trim()
    .isLength({ max: 100 })
    .withMessage('City cannot exceed 100 characters'),

  'address.state': body('address.state')
    .optional({ checkFalsy: true })
    .trim()
    .isLength({ max: 100 })
    .withMessage('State cannot exceed 100 characters'),

  'address.postalCode': body('address.postalCode')
    .optional({ checkFalsy: true })
    .trim()
    .isPostalCode('any')
    .withMessage('Please provide a valid postal code'),

  'address.country': body('address.country')
    .optional({ checkFalsy: true })
    .trim()
    .isLength({ max: 100 })
    .withMessage('Country cannot exceed 100 characters'),

  // Business
  businessName: body('businessName')
    .optional({ checkFalsy: true })
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Business name must be between 2 and 100 characters'),

  businessWebsite: body('businessWebsite')
    .optional({ checkFalsy: true })
    .trim()
    .isURL({
      protocols: ['http', 'https'],
      require_protocol: true,
      require_valid_protocol: true,
    })
    .withMessage('Please provide a valid business website URL'),

  // Payment methods
  cardNumber: body('cardNumber')
    .optional({ checkFalsy: true })
    .trim()
    .isCreditCard()
    .withMessage('Please provide a valid credit card number'),

  cardExpiry: body('cardExpiry')
    .optional({ checkFalsy: true })
    .trim()
    .matches(/^(0[1-9]|1[0-2])\/([0-9]{4}|[0-9]{2})$/)
    .withMessage('Please provide a valid expiration date (MM/YY or MM/YYYY)'),

  cardCvc: body('cardCvc')
    .optional({ checkFalsy: true })
    .trim()
    .isLength({ min: 3, max: 4 })
    .withMessage('CVC must be 3 or 4 digits')
    .isNumeric()
    .withMessage('CVC must contain only numbers'),

  // Common fields
  url: (field) =>
    body(field)
      .optional({ checkFalsy: true })
      .trim()
      .isURL({
        protocols: ['http', 'https'],
        require_protocol: true,
        require_valid_protocol: true,
      })
      .withMessage(`Please provide a valid URL for ${field}`),

  boolean: (field) =>
    body(field)
      .optional()
      .isBoolean()
      .withMessage(`${field} must be a boolean value`)
      .toBoolean(),

  number: (field, options = {}) => {
    const { min, max, isInt = false, isFloat = false } = options;
    let validator = body(field).optional();

    if (isInt) {
      validator = validator.isInt();
    } else if (isFloat) {
      validator = validator.isFloat();
    } else {
      validator = validator.isNumeric();
    }

    if (min !== undefined) {
      validator = validator.custom((value) => {
        const num = isInt ? parseInt(value, 10) : parseFloat(value);
        return num >= min;
      });
    }

    if (max !== undefined) {
      validator = validator.custom((value) => {
        const num = isInt ? parseInt(value, 10) : parseFloat(value);
        return num <= max;
      });
    }

    return validator.withMessage(
      `${field} must be a valid ${isInt ? 'integer' : 'number'}${
        min !== undefined ? ` ≥ ${min}` : ''
      }${max !== undefined ? ` and ≤ ${max}` : ''}`,
    );
  },

  array: (field, options = {}) => {
    const { min, max, itemType, unique = false } = options;
    let validator = body(field).isArray();

    if (min !== undefined) {
      validator = validator.isArray({ min });
    }

    if (max !== undefined) {
      validator = validator.isArray({ max });
    }

    if (unique) {
      validator = validator.custom((array) => {
        return new Set(array).size === array.length;
      }).withMessage(`${field} must contain unique values`);
    }

    if (itemType) {
      validator = validator.custom((array) => {
        return array.every((item) => {
          if (itemType === 'string') return typeof item === 'string';
          if (itemType === 'number') return !isNaN(parseFloat(item));
          if (itemType === 'boolean') return typeof item === 'boolean';
          if (itemType === 'object') return typeof item === 'object' && item !== null;
          return true;
        });
      }).withMessage(`All items in ${field} must be of type ${itemType}`);
    }

    return validator.withMessage(
      `${field} must be an array${min !== undefined ? ` with at least ${min} items` : ''}${
        max !== undefined ? ` and at most ${max} items` : ''
      }${itemType ? ` of type ${itemType}` : ''}${unique ? ' with unique values' : ''}`,
    );
  },

  enum: (field, values, message) =>
    body(field)
      .optional()
      .isIn(values)
      .withMessage(
        message ||
          `${field} must be one of: ${values.map((v) => `'${v}'`).join(', ')}`,
      ),

  // Token validation (simplified for session-based auth)
  token: (field) =>
    body(field)
      .notEmpty()
      .withMessage(`${field} is required`)
      .isString()
      .withMessage('Invalid token format'),

  // File validation
  file: (field, options = {}) => {
    const { allowedTypes = [], maxSize = 5 * 1024 * 1024 } = options; // Default 5MB
    return body(field)
      .custom((_, { req }) => {
        if (!req.files || !req.files[field]) {
          return true; // Optional by default
        }
        return true;
      })
      .withMessage('No file uploaded')
      .custom((_, { req }) => {
        if (!req.files || !req.files[field]) return true;
        const file = req.files[field];
        return file.size <= maxSize;
      })
      .withMessage(`File size must be less than ${maxSize / (1024 * 1024)}MB`)
      .custom((_, { req }) => {
        if (!req.files || !req.files[field] || allowedTypes.length === 0)
          return true;
        const file = req.files[field];
        const mimeType = file.mimetype;
        return allowedTypes.includes(mimeType);
      })
      .withMessage(
        `File type not allowed. Allowed types: ${allowedTypes.join(', ')}`,
      );
  },
};

// Validation middleware with error handling

/**
 * Middleware to validate request using express-validator
 * @returns {Function} Express middleware function
 */
const validateRequest = (req, res, next) => {
  try {
    const errors = validationResult(req);
    
    if (!errors.isEmpty()) {
      // Format errors
      const formattedErrors = errors.array().map(error => ({
        field: error.param,
        message: error.msg,
        value: error.value,
        location: error.location,
      }));
      
      logger.warn('Validation failed', { 
        path: req.path, 
        method: req.method, 
        errors: formattedErrors 
      });
      
      return next(ApiError.validationError('Validation failed', formattedErrors));
    }
    
    // Store only the validated data
    req.validatedData = matchedData(req, { locations: ['body', 'params', 'query'] });
    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Middleware to sanitize request data
 * @returns {Function} Express middleware function
 */
const sanitizeRequest = (req, res, next) => {
  try {
    // Sanitize request body
    if (req.body) {
      Object.keys(req.body).forEach(key => {
        if (typeof req.body[key] === 'string') {
          // Trim strings
          req.body[key] = req.body[key].trim();
          
          // Convert empty strings to null
          if (req.body[key] === '') {
            req.body[key] = null;
          }
        }
      });
    }
    
    // Sanitize query parameters
    if (req.query) {
      Object.keys(req.query).forEach(key => {
        if (typeof req.query[key] === 'string') {
          req.query[key] = req.query[key].trim();
          
          // Convert empty strings to null for query params
          if (req.query[key] === '') {
            req.query[key] = null;
          }
        }
      });
    }
    
    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Middleware to validate file uploads
 * @param {Object} options - Options for file validation
 * @param {Array} options.allowedTypes - Allowed MIME types
 * @param {number} options.maxSize - Maximum file size in bytes
 * @returns {Function} Express middleware function
 */
const validateFileUpload = (options = {}) => {
  const { allowedTypes = [], maxSize = 5 * 1024 * 1024 } = options;
  
  return (req, res, next) => {
    try {
      if (!req.files || Object.keys(req.files).length === 0) {
        return next(ApiError.badRequest('No files were uploaded'));
      }
      
      // Handle single file
      if (req.file) {
        if (allowedTypes.length > 0 && !allowedTypes.includes(req.file.mimetype)) {
          return next(ApiError.badRequest(`Invalid file type. Allowed types: ${allowedTypes.join(', ')}`));
        }
        
        if (req.file.size > maxSize) {
          return next(ApiError.badRequest(`File too large. Maximum size: ${maxSize / (1024 * 1024)}MB`));
        }
      }
      
      // Handle multiple files
      if (req.files) {
        const files = Array.isArray(req.files) ? req.files : Object.values(req.files).flat();
        
        for (const file of files) {
          if (allowedTypes.length > 0 && !allowedTypes.includes(file.mimetype)) {
            return next(ApiError.badRequest(`Invalid file type for ${file.originalname}. Allowed types: ${allowedTypes.join(', ')}`));
          }
          
          if (file.size > maxSize) {
            return next(ApiError.badRequest(`File too large: ${file.originalname}. Maximum size: ${maxSize / (1024 * 1024)}MB`));
          }
        }
      }
      
      next();
    } catch (error) {
      next(error);
    }
  };
};

// Custom validators
const customValidators = {
  isStrongPassword: (value) => {
    if (!value) return false;
    return isStrongPassword(value);
  },
  
  isPhoneNumber: (value) => {
    if (!value) return true; // Optional field
    return isPhoneNumber(value);
  },
  
  isEmail: (value) => {
    if (!value) return false;
    return isEmail(value);
  },
  
  isURLOrRelativePath: (value) => {
    if (!value) return true; // Optional field
    return isURLOrRelativePath(value);
  },
  
  // Custom validator for checking if a date is in the future
  isFutureDate: (value) => {
    if (!value) return false;
    const date = new Date(value);
    return date > new Date();
  },
  
  // Custom validator for checking if a date is in the past
  isPastDate: (value) => {
    if (!value) return false;
    const date = new Date(value);
    return date < new Date();
  },
  
  // Custom validator for checking if a string is a valid currency amount
  isCurrency: (value) => {
    if (!value) return false;
    return /^\d+(\.\d{1,2})?$/.test(value);
  },
};

// Export common rules and middleware
module.exports = {
  commonRules,
  validateRequest,
  sanitizeRequest,
  customValidators,
  isStrongPassword,
  isPhoneNumber,
  isURLOrRelativePath,
  body,
  param,
  query,
};
