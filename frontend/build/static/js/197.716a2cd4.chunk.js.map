{"version": 3, "file": "static/js/197.716a2cd4.chunk.js", "mappings": "4/HAQO,MAAMA,EACDC,IACRC,EAAAA,EAAAA,KAAA,OACEC,MAAM,6BACNC,MAAM,KACNC,OAAO,KACPC,QAAQ,YACRC,KAAK,OACLC,OAAO,eACPC,YAAY,IACZC,cAAc,QACdC,eAAe,WACXV,EAAKW,UAETV,EAAAA,EAAAA,KAAA,QAAMW,EAAE,kCC6Hd,EAvIiBC,KACf,MAAMC,GAAWC,EAAAA,EAAAA,OACVC,EAAWC,IAAgBC,EAAAA,EAAAA,WAAS,IACpCC,EAAUC,IAAeF,EAAAA,EAAAA,UAAS,CACvCG,KAAM,GACNC,MAAO,GACPC,SAAU,GACVC,gBAAiB,KAGbC,EAAgBC,IACpB,MAAM,KAAEL,EAAI,MAAEM,GAAUD,EAAEE,OAC1BR,EAAYS,IAAI,IACXA,EACH,CAACR,GAAOM,MAqCZ,OACE1B,EAAAA,EAAAA,KAAA,OAAK6B,UAAU,sFAAqFnB,UAClGoB,EAAAA,EAAAA,MAACC,EAAAA,GAAI,CAACF,UAAU,kBAAiBnB,SAAA,EAC/BoB,EAAAA,EAAAA,MAACE,EAAAA,GAAU,CAACH,UAAU,YAAWnB,SAAA,EAC/BV,EAAAA,EAAAA,KAACiC,EAAAA,GAAS,CAACJ,UAAU,iCAAgCnB,SAAC,uBACtDV,EAAAA,EAAAA,KAACkC,EAAAA,GAAe,CAACL,UAAU,cAAanB,SAAC,oDAI3CoB,EAAAA,EAAAA,MAAA,QAAMK,SA1CSC,UAInB,GAHAX,EAAEY,iBAGEnB,EAASI,WAAaJ,EAASK,gBAKnC,GAAIL,EAASI,SAASgB,OAAS,EAC7BC,EAAMC,MAAM,oDAId,IACExB,GAAa,SAEUyB,EAAAA,EAAMC,KAAK,iDAAoF,CACpHtB,KAAMF,EAASE,KACfC,MAAOH,EAASG,MAChBC,SAAUJ,EAASI,WAGrBiB,EAAMI,QAAQ,2CACd9B,EAAS,SACX,CAAE,MAAO2B,GAAa,IAADI,EAAAC,EACnB,MAAMC,GAA6B,QAAdF,EAAAJ,EAAMO,gBAAQ,IAAAH,GAAM,QAANC,EAAdD,EAAgBI,YAAI,IAAAH,OAAN,EAAdA,EAAsBI,UAAW,yCACtDV,EAAMC,MAAMM,EACd,CAAC,QACC9B,GAAa,EACf,MAzBEuB,EAAMC,MAAM,2BAqCmB9B,SAAA,EAC3BoB,EAAAA,EAAAA,MAACoB,EAAAA,GAAW,CAACrB,UAAU,YAAWnB,SAAA,EAChCoB,EAAAA,EAAAA,MAAA,OAAKD,UAAU,YAAWnB,SAAA,EACxBV,EAAAA,EAAAA,KAACmD,EAAAA,EAAK,CAACC,QAAQ,OAAM1C,SAAC,eACtBV,EAAAA,EAAAA,KAACqD,EAAAA,EAAK,CACJC,GAAG,OACHlC,KAAK,OACLmC,KAAK,OACLC,UAAQ,EACR9B,MAAOR,EAASE,KAChBqC,SAAUjC,EACVkC,SAAU3C,EACV4C,YAAY,iBAGhB7B,EAAAA,EAAAA,MAAA,OAAKD,UAAU,YAAWnB,SAAA,EACxBV,EAAAA,EAAAA,KAACmD,EAAAA,EAAK,CAACC,QAAQ,QAAO1C,SAAC,WACvBV,EAAAA,EAAAA,KAACqD,EAAAA,EAAK,CACJC,GAAG,QACHlC,KAAK,QACLmC,KAAK,QACLC,UAAQ,EACR9B,MAAOR,EAASG,MAChBoC,SAAUjC,EACVkC,SAAU3C,EACV4C,YAAY,yBAGhB7B,EAAAA,EAAAA,MAAA,OAAKD,UAAU,YAAWnB,SAAA,EACxBV,EAAAA,EAAAA,KAACmD,EAAAA,EAAK,CAACC,QAAQ,WAAU1C,SAAC,cAC1BV,EAAAA,EAAAA,KAACqD,EAAAA,EAAK,CACJC,GAAG,WACHlC,KAAK,WACLmC,KAAK,WACLC,UAAQ,EACR9B,MAAOR,EAASI,SAChBmC,SAAUjC,EACVkC,SAAU3C,EACV4C,YAAY,yDAGhB7B,EAAAA,EAAAA,MAAA,OAAKD,UAAU,YAAWnB,SAAA,EACxBV,EAAAA,EAAAA,KAACmD,EAAAA,EAAK,CAACC,QAAQ,kBAAiB1C,SAAC,sBACjCV,EAAAA,EAAAA,KAACqD,EAAAA,EAAK,CACJC,GAAG,kBACHlC,KAAK,kBACLmC,KAAK,WACLC,UAAQ,EACR9B,MAAOR,EAASK,gBAChBkC,SAAUjC,EACVkC,SAAU3C,EACV4C,YAAY,4DAIlB7B,EAAAA,EAAAA,MAAC8B,EAAAA,GAAU,CAAC/B,UAAU,0BAAyBnB,SAAA,EAC7CoB,EAAAA,EAAAA,MAAC+B,EAAAA,EAAM,CAACN,KAAK,SAAS1B,UAAU,SAAS6B,SAAU3C,EAAUL,SAAA,CAC1DK,IACCf,EAAAA,EAAAA,KAACF,EAAa,CAAC+B,UAAU,8BACzB,qBAGJC,EAAAA,EAAAA,MAAA,OAAKD,UAAU,4CAA2CnB,SAAA,CAAC,2BAChC,KACzBV,EAAAA,EAAAA,KAAC8D,EAAAA,GAAI,CAACC,GAAG,SAASlC,UAAU,+BAA8BnB,SAAC,0B,4ECnIzE,MAAMsD,GAAgBC,EAAAA,EAAAA,GACpB,8FAGId,EAAQe,EAAAA,WAGZ,CAAAC,EAA0BC,KAAG,IAA5B,UAAEvC,KAAc9B,GAAOoE,EAAA,OACxBnE,EAAAA,EAAAA,KAAA,SACEoE,IAAKA,EACLvC,WAAWwC,EAAAA,EAAAA,IAAGL,IAAiBnC,MAC3B9B,MAGRoD,EAAMmB,YAAc,O,iHCfpB,MAAMvC,EAAOmC,EAAAA,WAGX,CAAAC,EAA0BC,KAAG,IAA5B,UAAEvC,KAAc9B,GAAOoE,EAAA,OACxBnE,EAAAA,EAAAA,KAAA,OACEoE,IAAKA,EACLvC,WAAWwC,EAAAA,EAAAA,IACT,2DACAxC,MAEE9B,MAGRgC,EAAKuC,YAAc,OAEnB,MAAMtC,EAAakC,EAAAA,WAGjB,CAAAK,EAA0BH,KAAG,IAA5B,UAAEvC,KAAc9B,GAAOwE,EAAA,OACxBvE,EAAAA,EAAAA,KAAA,OACEoE,IAAKA,EACLvC,WAAWwC,EAAAA,EAAAA,IAAG,gCAAiCxC,MAC3C9B,MAGRiC,EAAWsC,YAAc,aAEzB,MAAMrC,EAAYiC,EAAAA,WAGhB,CAAAM,EAA0BJ,KAAG,IAA5B,UAAEvC,KAAc9B,GAAOyE,EAAA,OACxBxE,EAAAA,EAAAA,KAAA,MACEoE,IAAKA,EACLvC,WAAWwC,EAAAA,EAAAA,IACT,qDACAxC,MAEE9B,MAGRkC,EAAUqC,YAAc,YAExB,MAAMpC,EAAkBgC,EAAAA,WAGtB,CAAAO,EAA0BL,KAAG,IAA5B,UAAEvC,KAAc9B,GAAO0E,EAAA,OACxBzE,EAAAA,EAAAA,KAAA,KACEoE,IAAKA,EACLvC,WAAWwC,EAAAA,EAAAA,IAAG,gCAAiCxC,MAC3C9B,MAGRmC,EAAgBoC,YAAc,kBAE9B,MAAMpB,EAAcgB,EAAAA,WAGlB,CAAAQ,EAA0BN,KAAG,IAA5B,UAAEvC,KAAc9B,GAAO2E,EAAA,OACxB1E,EAAAA,EAAAA,KAAA,OAAKoE,IAAKA,EAAKvC,WAAWwC,EAAAA,EAAAA,IAAG,WAAYxC,MAAgB9B,MAE3DmD,EAAYoB,YAAc,cAE1B,MAAMV,EAAaM,EAAAA,WAGjB,CAAAS,EAA0BP,KAAG,IAA5B,UAAEvC,KAAc9B,GAAO4E,EAAA,OACxB3E,EAAAA,EAAAA,KAAA,OACEoE,IAAKA,EACLvC,WAAWwC,EAAAA,EAAAA,IAAG,6BAA8BxC,MACxC9B,MAGR6D,EAAWU,YAAc,Y,sFCtEzB,MAAMM,GAAiBX,EAAAA,EAAAA,GACrB,yRACA,CACEY,SAAU,CACRC,QAAS,CACPC,QAAS,yDACTC,YACE,qEACFC,QACE,iFACFC,UACE,+DACFC,MAAO,+CACPC,KAAM,mDAERC,KAAM,CACJN,QAAS,iBACTO,GAAI,sBACJC,GAAI,uBACJC,KAAM,cAGVC,gBAAiB,CACfX,QAAS,UACTO,KAAM,aAWNxB,EAASK,EAAAA,WACb,CAAAC,EAA0DC,KAAS,IAAlE,UAAEvC,EAAS,QAAEiD,EAAO,KAAEO,EAAI,QAAEK,GAAU,KAAU3F,GAAOoE,EACtD,MAAMwB,EAAOD,EAAUE,EAAAA,GAAO,SAC9B,OACE5F,EAAAA,EAAAA,KAAC2F,EAAI,CACH9D,WAAWwC,EAAAA,EAAAA,IAAGO,EAAe,CAAEE,UAASO,OAAMxD,eAC9CuC,IAAKA,KACDrE,MAKZ8D,EAAOS,YAAc,Q,mEC9CrB,MAAMjB,EAAQa,EAAAA,WACZ,CAAAC,EAAgCC,KAAS,IAAxC,UAAEvC,EAAS,KAAE0B,KAASxD,GAAOoE,EAC5B,OACEnE,EAAAA,EAAAA,KAAA,SACEuD,KAAMA,EACN1B,WAAWwC,EAAAA,EAAAA,IACT,+VACAxC,GAEFuC,IAAKA,KACDrE,MAKZsD,EAAMiB,YAAc,O", "sources": ["components/icons.tsx", "pages/Register.tsx", "components/ui/label.tsx", "components/ui/card.tsx", "components/ui/button.tsx", "components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\ntype IconProps = {\n  className?: string\n  size?: number\n  [key: string]: any\n}\n\nexport const Icons = {\n  spinner: (props: IconProps) => (\n    <svg\n      xmlns=\"http://www.w3.org/2000/svg\"\n      width=\"24\"\n      height=\"24\"\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      stroke=\"currentColor\"\n      strokeWidth=\"2\"\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      {...props}\n    >\n      <path d=\"M21 12a9 9 0 1 1-6.219-8.56\" />\n    </svg>\n  ),\n  logo: (props: IconProps) => (\n    <svg\n      xmlns=\"http://www.w3.org/2000/svg\"\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      stroke=\"currentColor\"\n      strokeWidth=\"2\"\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      {...props}\n    >\n      <path d=\"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5\" />\n    </svg>\n  ),\n  google: (props: IconProps) => (\n    <svg\n      xmlns=\"http://www.w3.org/2000/svg\"\n      viewBox=\"0 0 24 24\"\n      fill=\"currentColor\"\n      {...props}\n    >\n      <path d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\" fill=\"#4285F4\" />\n      <path d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\" fill=\"#34A853\" />\n      <path d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l3.66-2.84z\" fill=\"#FBBC05\" />\n      <path d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\" fill=\"#EA4335\" />\n    </svg>\n  ),\n  github: (props: IconProps) => (\n    <svg\n      xmlns=\"http://www.w3.org/2000/svg\"\n      viewBox=\"0 0 24 24\"\n      fill=\"currentColor\"\n      {...props}\n    >\n      <path d=\"M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.11-4.555-4.943 0-1.091.39-1.984 1.029-2.683-.103-.253-.446-1.27.098-2.647 0 0 .84-.269 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.699 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.919.678 1.852 0 1.336-.012 2.415-.012 2.743 0 .267.18.578.688.48A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z\" />\n    </svg>\n  ),\n  // Add more icons as needed\n}\n", "import React, { useState } from 'react';\nimport { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';\nimport axios from 'axios';\nimport { toast } from 'react-toastify';\nimport { Button } from \"../components/ui/button\";\nimport { Input } from \"../components/ui/input\";\nimport { Label } from \"../components/ui/label\";\nimport { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from \"../components/ui/card\";\nimport { Icons } from \"../components/icons\";\nimport { useAuth } from \"../contexts/AuthContext\";\nimport { useToast } from \"../hooks/use-toast\";\n\nconst Register = () => {\n  const navigate = useNavigate();\n  const [isLoading, setIsLoading] = useState(false);\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n  });\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value,\n    }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    // Basic validation\n    if (formData.password !== formData.confirmPassword) {\n      toast.error('Passwords do not match');\n      return;\n    }\n\n    if (formData.password.length < 6) {\n      toast.error('Password must be at least 6 characters long');\n      return;\n    }\n\n    try {\n      setIsLoading(true);\n      \n      const response = await axios.post(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/api/v1/auth/register`, {\n        name: formData.name,\n        email: formData.email,\n        password: formData.password,\n      });\n\n      toast.success('Registration successful! Please log in.');\n      navigate('/login');\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.message || 'Registration failed. Please try again.';\n      toast.error(errorMessage);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\n      <Card className=\"w-full max-w-md\">\n        <CardHeader className=\"space-y-1\">\n          <CardTitle className=\"text-2xl font-bold text-center\">Create an account</CardTitle>\n          <CardDescription className=\"text-center\">\n            Enter your information to create an account\n          </CardDescription>\n        </CardHeader>\n        <form onSubmit={handleSubmit}>\n          <CardContent className=\"space-y-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"name\">Full Name</Label>\n              <Input\n                id=\"name\"\n                name=\"name\"\n                type=\"text\"\n                required\n                value={formData.name}\n                onChange={handleChange}\n                disabled={isLoading}\n                placeholder=\"John Doe\"\n              />\n            </div>\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"email\">Email</Label>\n              <Input\n                id=\"email\"\n                name=\"email\"\n                type=\"email\"\n                required\n                value={formData.email}\n                onChange={handleChange}\n                disabled={isLoading}\n                placeholder=\"<EMAIL>\"\n              />\n            </div>\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"password\">Password</Label>\n              <Input\n                id=\"password\"\n                name=\"password\"\n                type=\"password\"\n                required\n                value={formData.password}\n                onChange={handleChange}\n                disabled={isLoading}\n                placeholder=\"••••••••\"\n              />\n            </div>\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"confirmPassword\">Confirm Password</Label>\n              <Input\n                id=\"confirmPassword\"\n                name=\"confirmPassword\"\n                type=\"password\"\n                required\n                value={formData.confirmPassword}\n                onChange={handleChange}\n                disabled={isLoading}\n                placeholder=\"••••••••\"\n              />\n            </div>\n          </CardContent>\n          <CardFooter className=\"flex flex-col space-y-4\">\n            <Button type=\"submit\" className=\"w-full\" disabled={isLoading}>\n              {isLoading && (\n                <Icons.spinner className=\"mr-2 h-4 w-4 animate-spin\" />\n              )}\n              Create Account\n            </Button>\n            <div className=\"text-sm text-center text-muted-foreground\">\n              Already have an account?{' '}\n              <Link to=\"/login\" className=\"text-primary hover:underline\">\n                Sign in\n              </Link>\n            </div>\n          </CardFooter>\n        </form>\n      </Card>\n    </div>\n  );\n};\n\nexport default Register;\n", "import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  HTMLLabelElement,\n  React.LabelHTMLAttributes<HTMLLabelElement> & VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <label\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = \"Label\"\n\nexport { Label }\n", "import * as React from \"react\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardDescription,\n  CardContent,\n}\n", "import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n", "import * as React from \"react\"\n\nimport { cn } from \"../../lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": ["Icons", "props", "_jsx", "xmlns", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "children", "d", "Register", "navigate", "useNavigate", "isLoading", "setIsLoading", "useState", "formData", "setFormData", "name", "email", "password", "confirmPassword", "handleChange", "e", "value", "target", "prev", "className", "_jsxs", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "CardDescription", "onSubmit", "async", "preventDefault", "length", "toast", "error", "axios", "post", "success", "_error$response", "_error$response$data", "errorMessage", "response", "data", "message", "<PERSON><PERSON><PERSON><PERSON>", "Label", "htmlFor", "Input", "id", "type", "required", "onChange", "disabled", "placeholder", "<PERSON><PERSON><PERSON>er", "<PERSON><PERSON>", "Link", "to", "labelVariants", "cva", "React", "_ref", "ref", "cn", "displayName", "_ref2", "_ref3", "_ref4", "_ref5", "_ref6", "buttonVariants", "variants", "variant", "default", "destructive", "outline", "secondary", "ghost", "link", "size", "sm", "lg", "icon", "defaultVariants", "<PERSON><PERSON><PERSON><PERSON>", "Comp", "Slot"], "sourceRoot": ""}