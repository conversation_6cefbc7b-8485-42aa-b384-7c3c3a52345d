const ReserveStrategy = require('../models/ReserveStrategy');
const ReserveActivity = require('../models/ReserveActivity');
const { SuccessResponse, ErrorResponse } = require('../utils/apiResponse');
const asyncHandler = require('../middleware/async');
const mongoose = require('mongoose');
const { Types: { ObjectId } } = mongoose;

/**
 * @desc    Create a new reserve strategy
 * @route   POST /api/v1/reserve-strategies
 * @access  Private/Admin
 */
exports.createReserveStrategy = asyncHandler(async (req, res, next) => {
  const {
    name,
    description,
    rules = [],
    isDefault = false,
    status = 'draft',
    effectiveFrom,
    effectiveTo,
    tags = [],
    config = {}
  } = req.body;

  // Create the reserve strategy
  const reserveStrategy = new ReserveStrategy({
    name,
    description,
    rules,
    isDefault,
    status,
    effectiveFrom: effectiveFrom || Date.now(),
    effectiveTo,
    tags,
    config,
    createdBy: req.user.id
  });

  await reserveStrategy.save();

  return new SuccessResponse(
    201,
    'Reserve strategy created successfully',
    reserveStrategy
  ).send(res);
});

/**
 * @desc    Get all reserve strategies
 * @route   GET /api/v1/reserve-strategies
 * @access  Private/Admin
 */
exports.getReserveStrategies = asyncHandler(async (req, res, next) => {
  const { 
    page = 1, 
    limit = 10, 
    status, 
    isDefault,
    search,
    sortBy = 'createdAt',
    sortOrder = 'desc'
  } = req.query;

  // Build query
  const query = {};
  
  if (status) query.status = status;
  if (isDefault !== undefined) query.isDefault = isDefault === 'true';
  
  if (search) {
    query.$or = [
      { name: { $regex: search, $options: 'i' } },
      { description: { $regex: search, $options: 'i' } },
      { tags: { $in: [new RegExp(search, 'i')] } }
    ];
  }

  // Sorting
  const sort = {};
  sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

  // Pagination
  const options = {
    page: parseInt(page, 10),
    limit: parseInt(limit, 10),
    sort,
    populate: [
      { path: 'createdBy', select: 'name email' },
      { path: 'updatedBy', select: 'name email' }
    ]
  };

  const result = await ReserveStrategy.paginate(query, options);

  return new SuccessResponse(
    200,
    'Reserve strategies retrieved successfully',
    {
      strategies: result.docs,
      pagination: {
        total: result.totalDocs,
        pages: result.totalPages,
        page: result.page,
        limit: result.limit,
        hasNext: result.hasNextPage,
        hasPrev: result.hasPrevPage
      }
    }
  ).send(res);
});

/**
 * @desc    Get single reserve strategy
 * @route   GET /api/v1/reserve-strategies/:id
 * @access  Private/Admin
 */
exports.getReserveStrategy = asyncHandler(async (req, res, next) => {
  const { id } = req.params;
  
  if (!mongoose.Types.ObjectId.isValid(id)) {
    return next(new ErrorResponse('Invalid reserve strategy ID', 400));
  }

  const strategy = await ReserveStrategy.findById(id)
    .populate('createdBy', 'name email')
    .populate('updatedBy', 'name email');

  if (!strategy) {
    return next(new ErrorResponse('Reserve strategy not found', 404));
  }

  return new SuccessResponse(
    200,
    'Reserve strategy retrieved successfully',
    strategy
  ).send(res);
});

/**
 * @desc    Update reserve strategy
 * @route   PUT /api/v1/reserve-strategies/:id
 * @access  Private/Admin
 */
exports.updateReserveStrategy = asyncHandler(async (req, res, next) => {
  const { id } = req.params;
  
  if (!mongoose.Types.ObjectId.isValid(id)) {
    return next(new ErrorResponse('Invalid reserve strategy ID', 400));
  }

  // Check if strategy exists
  let strategy = await ReserveStrategy.findById(id);
  if (!strategy) {
    return next(new ErrorResponse('Reserve strategy not found', 404));
  }

  // Prevent updating certain fields if strategy is in use
  if (strategy.status === 'active' && req.body.status === 'draft') {
    return next(new ErrorResponse('Cannot set an active strategy to draft status', 400));
  }

  // Update fields
  const updates = { 
    ...req.body, 
    updatedBy: req.user.id,
    // Prevent changing createdBy
    createdBy: strategy.createdBy
  };

  // Update the strategy
  strategy = await ReserveStrategy.findByIdAndUpdate(
    id,
    { $set: updates },
    { new: true, runValidators: true }
  )
    .populate('createdBy', 'name email')
    .populate('updatedBy', 'name email');

  return new SuccessResponse(
    200,
    'Reserve strategy updated successfully',
    strategy
  ).send(res);
});

/**
 * @desc    Delete reserve strategy
 * @route   DELETE /api/v1/reserve-strategies/:id
 * @access  Private/Admin
 */
exports.deleteReserveStrategy = asyncHandler(async (req, res, next) => {
  const { id } = req.params;
  
  if (!mongoose.Types.ObjectId.isValid(id)) {
    return next(new ErrorResponse('Invalid reserve strategy ID', 400));
  }

  // Check if strategy exists
  const strategy = await ReserveStrategy.findById(id);
  if (!strategy) {
    return next(new ErrorResponse('Reserve strategy not found', 404));
  }

  // Prevent deleting default or active strategies
  if (strategy.isDefault) {
    return next(new ErrorResponse('Default reserve strategy cannot be deleted', 400));
  }

  if (strategy.status === 'active') {
    return next(new ErrorResponse('Active reserve strategy cannot be deleted', 400));
  }

  await strategy.remove();

  return new SuccessResponse(
    200,
    'Reserve strategy deleted successfully'
  ).send(res);
});

/**
 * @desc    Calculate reserve for a transaction
 * @route   POST /api/v1/reserve-strategies/calculate
 * @access  Private/Admin
 */
exports.calculateReserve = asyncHandler(async (req, res, next) => {
  const { 
    amount, 
    strategyId,
    effectiveDate,
    currentReserve
  } = req.body;

  // If strategyId is provided, use that specific strategy
  if (strategyId) {
    if (!mongoose.Types.ObjectId.isValid(strategyId)) {
      return next(new ErrorResponse('Invalid reserve strategy ID', 400));
    }

    const strategy = await ReserveStrategy.findById(strategyId);
    if (!strategy) {
      return next(new ErrorResponse('Reserve strategy not found', 404));
    }

    const result = strategy.calculateReserve(amount, { effectiveDate });
    
    // Add reserve status check if currentReserve is provided
    if (currentReserve !== undefined) {
      result.reserveStatus = strategy.checkReserveStatus(currentReserve);
    }

    return new SuccessResponse(
      200,
      'Reserve calculated successfully',
      result
    ).send(res);
  }
  
  // If no strategyId is provided, find the default active strategy
  const defaultStrategy = await ReserveStrategy.findOne({ 
    isDefault: true, 
    status: 'active',
    $and: [
      { $or: [
        { effectiveFrom: { $exists: false } },
        { effectiveFrom: { $lte: new Date(effectiveDate || Date.now()) } }
      ]},
      { $or: [
        { effectiveTo: { $exists: false } },
        { effectiveTo: { $gte: new Date(effectiveDate || Date.now()) } }
      ]}
    ]
  });

  if (!defaultStrategy) {
    return next(new ErrorResponse('No default active reserve strategy found', 404));
  }

  const result = defaultStrategy.calculateReserve(amount, { effectiveDate });
  
  // Add reserve status check if currentReserve is provided
  if (currentReserve !== undefined) {
    result.reserveStatus = defaultStrategy.checkReserveStatus(currentReserve);
  }

  return new SuccessResponse(
    200,
    'Reserve calculated successfully',
    {
      ...result,
      strategy: {
        id: defaultStrategy._id,
        name: defaultStrategy.name,
        description: defaultStrategy.description
      }
    }
  ).send(res);
});

/**
 * @desc    Check reserve status
 * @route   POST /api/v1/reserve-strategies/check-status
 * @access  Private/Admin
 */
exports.checkReserveStatus = asyncHandler(async (req, res, next) => {
  const { 
    currentReserve,
    strategyId
  } = req.body;

  if (currentReserve === undefined) {
    return next(new ErrorResponse('currentReserve is required', 400));
  }

  // If strategyId is provided, use that specific strategy
  if (strategyId) {
    if (!mongoose.Types.ObjectId.isValid(strategyId)) {
      return next(new ErrorResponse('Invalid reserve strategy ID', 400));
    }

    const strategy = await ReserveStrategy.findById(strategyId);
    if (!strategy) {
      return next(new ErrorResponse('Reserve strategy not found', 404));
    }

    const status = strategy.checkReserveStatus(currentReserve);
    
    return new SuccessResponse(
      200,
      'Reserve status checked successfully',
      {
        ...status,
        strategy: {
          id: strategy._id,
          name: strategy.name
        }
      }
    ).send(res);
  }
  
  // If no strategyId is provided, find the default active strategy
  const defaultStrategy = await ReserveStrategy.findOne({ 
    isDefault: true, 
    status: 'active'
  });

  if (!defaultStrategy) {
    return next(new ErrorResponse('No default active reserve strategy found', 404));
  }

  const status = defaultStrategy.checkReserveStatus(currentReserve);
  
  return new SuccessResponse(
    200,
    'Reserve status checked successfully',
    {
      ...status,
      strategy: {
        id: defaultStrategy._id,
        name: defaultStrategy.name
      }
    }
  ).send(res);
});

/**
 * @desc    Get activities for reserve strategies
 * @route   GET /api/v1/reserve-strategies/activities
 * @access  Private
 */
exports.getActivities = asyncHandler(async (req, res, next) => {
  const {
    page = 1,
    limit = 10,
    status,
    type,
    search,
    startDate,
    endDate,
    sort = '-createdAt'
  } = req.query;

  // Build query
  const query = {};
  
  // Filter by status
  if (status && status !== 'all') {
    query.status = status;
  }
  
  // Filter by activity type
  if (type && type !== 'all') {
    query.type = type;
  }
  
  // Text search
  if (search) {
    query.$or = [
      { 'strategy.name': { $regex: search, $options: 'i' } },
      { 'user.name': { $regex: search, $options: 'i' } },
      { description: { $regex: search, $options: 'i' } }
    ];
  }
  
  // Date range filter
  if (startDate || endDate) {
    query.createdAt = {};
    if (startDate) query.createdAt.$gte = new Date(startDate);
    if (endDate) {
      const endOfDay = new Date(endDate);
      endOfDay.setHours(23, 59, 59, 999);
      query.createdAt.$lte = endOfDay;
    }
  }

  // Execute query with pagination
  const activities = await ReserveActivity.find(query)
    .sort(sort)
    .limit(limit * 1)
    .skip((page - 1) * limit)
    .populate('strategy', 'name')
    .populate('user', 'name email')
    .populate('reviewedBy', 'name email')
    .lean();

  // Get total count for pagination
  const total = await ReserveActivity.countDocuments(query);

  return new SuccessResponse(
    200,
    'Activities retrieved successfully',
    {
      docs: activities,
      total,
      page: parseInt(page, 10),
      pages: Math.ceil(total / limit),
      limit: parseInt(limit, 10)
    }
  ).send(res);
});

/**
 * @desc    Get single activity details
 * @route   GET /api/v1/reserve-strategies/activities/:id
 * @access  Private/Admin,Finance
 */
exports.getActivity = asyncHandler(async (req, res, next) => {
  const { id } = req.params;

  if (!ObjectId.isValid(id)) {
    return next(new ErrorResponse('Invalid activity ID', 400));
  }

  const activity = await ReserveActivity.findById(id)
    .populate('strategy', 'name description')
    .populate('user', 'name email')
    .populate('reviewedBy', 'name email')
    .lean();

  if (!activity) {
    return next(new ErrorResponse('Activity not found', 404));
  }

  return new SuccessResponse(
    200,
    'Activity retrieved successfully',
    activity
  ).send(res);
});

/**
 * @desc    Review an activity (approve/flag)
 * @route   POST /api/v1/reserve-strategies/activities/:id/review
 * @access  Private/Admin,Finance
 */
exports.reviewActivity = asyncHandler(async (req, res, next) => {
  const { id } = req.params;
  const { action, note } = req.body;

  if (!ObjectId.isValid(id)) {
    return next(new ErrorResponse('Invalid activity ID', 400));
  }

  // Validate action
  if (!['approve', 'flag'].includes(action)) {
    return next(new ErrorResponse('Invalid action. Must be either "approve" or "flag"', 400));
  }

  // Find the activity
  const activity = await ReserveActivity.findById(id);
  
  if (!activity) {
    return next(new ErrorResponse('Activity not found', 404));
  }

  // Check if already reviewed
  if (activity.status !== 'pending') {
    return next(new ErrorResponse('This activity has already been reviewed', 400));
  }

  // Update activity status based on action
  activity.status = action === 'approve' ? 'approved' : 'flagged';
  activity.reviewedBy = req.user.id;
  activity.reviewedAt = Date.now();
  activity.reviewNote = note;

  await activity.save();

  // If approved, apply the activity to the strategy if needed
  if (action === 'approve' && activity.strategy) {
    // This would be implemented based on the specific activity type
    // For example, if it's a rule update, apply the rule changes
    await this.applyActivityToStrategy(activity);
  }

  // Populate the response
  const updatedActivity = await ReserveActivity.findById(activity._id)
    .populate('strategy', 'name')
    .populate('user', 'name email')
    .populate('reviewedBy', 'name email');

  return new SuccessResponse(
    200,
    `Activity ${action === 'approve' ? 'approved' : 'flagged'} successfully`,
    updatedActivity
  ).send(res);
});

/**
 * @desc    Get activities for a specific reserve strategy
 * @route   GET /api/v1/reserve-strategies/:id/activities
 * @access  Private/Admin,Finance
 */
exports.getStrategyActivities = asyncHandler(async (req, res, next) => {
  const { id } = req.params;
  const { page = 1, limit = 10, status, type } = req.query;

  if (!ObjectId.isValid(id)) {
    return next(new ErrorResponse('Invalid reserve strategy ID', 400));
  }

  // Build query
  const query = { strategy: id };
  
  // Filter by status
  if (status && status !== 'all') {
    query.status = status;
  }
  
  // Filter by activity type
  if (type && type !== 'all') {
    query.type = type;
  }

  // Execute query with pagination
  const activities = await ReserveActivity.find(query)
    .sort('-createdAt')
    .limit(limit * 1)
    .skip((page - 1) * limit)
    .populate('user', 'name email')
    .populate('reviewedBy', 'name email')
    .lean();

  // Get total count for pagination
  const total = await ReserveActivity.countDocuments(query);

  return new SuccessResponse(
    200,
    'Strategy activities retrieved successfully',
    {
      docs: activities,
      total,
      page: parseInt(page, 10),
      pages: Math.ceil(total / limit),
      limit: parseInt(limit, 10)
    }
  ).send(res);
});

/**
 * Helper method to apply activity changes to a strategy
 * @private
 */
const applyActivityToStrategy = async (activity) => {
  try {
    if (!activity.strategy) return;
    
    const strategy = await ReserveStrategy.findById(activity.strategy);
    if (!strategy) return;
    
    switch (activity.type) {
      case 'rule_update':
        // Apply rule updates
        if (activity.details?.ruleId && activity.details?.changes) {
          const ruleIndex = strategy.rules.findIndex(
            r => r._id.toString() === activity.details.ruleId
          );
          
          if (ruleIndex !== -1) {
            // Apply each change to the rule
            Object.entries(activity.details.changes).forEach(([field, value]) => {
              strategy.rules[ruleIndex][field] = value;
            });
            
            strategy.markModified('rules');
            await strategy.save();
          }
        }
        break;
        
      case 'status_change':
        // Apply status change
        if (activity.details?.status) {
          strategy.status = activity.details.status;
          await strategy.save();
        }
        break;
        
      // Add more activity types as needed
    }
  } catch (error) {
    console.error('Error applying activity to strategy:', error);
    // Log the error but don't fail the request
  }
};

/**
 * @desc    Check status of multiple reserve strategies
 * @route   POST /api/reserve-strategies/check-status
 * @access  Private/Admin,Finance
 */
exports.checkStatus = asyncHandler(async (req, res, next) => {
  const { strategyIds } = req.body;

  if (!Array.isArray(strategyIds) || strategyIds.length === 0) {
    return next(new ErrorResponse('Please provide an array of strategy IDs', 400));
  }

  // Validate all IDs are valid MongoDB ObjectIds
  const invalidIds = strategyIds.filter(id => !mongoose.Types.ObjectId.isValid(id));
  if (invalidIds.length > 0) {
    return next(new ErrorResponse(`Invalid strategy IDs: ${invalidIds.join(', ')}`, 400));
  }

  // Find all strategies with the given IDs
  const strategies = await ReserveStrategy.find({
    _id: { $in: strategyIds }
  }).select('_id name status effectiveFrom effectiveTo isDefault');

  // Create a map of found strategies for quick lookup
  const strategyMap = new Map();
  strategies.forEach(strategy => {
    strategyMap.set(strategy._id.toString(), strategy);
  });

  // Prepare the response with status for each strategy
  const results = strategyIds.map(id => {
    const strategy = strategyMap.get(id);
    if (!strategy) {
      return {
        strategyId: id,
        exists: false,
        status: 'not_found',
        checkedAt: new Date().toISOString()
      };
    }

    return {
      strategyId: strategy._id,
      exists: true,
      name: strategy.name,
      status: strategy.status,
      isDefault: strategy.isDefault,
      effectiveFrom: strategy.effectiveFrom,
      effectiveTo: strategy.effectiveTo,
      isActive: strategy.status === 'active',
      checkedAt: new Date().toISOString()
    };
  });

  return new SuccessResponse(
    200,
    'Status checked successfully',
    {
      results,
      timestamp: new Date().toISOString()
    }
  ).send(res);
});
