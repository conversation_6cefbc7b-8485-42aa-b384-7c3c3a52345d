const ApiError = require("../utils/ApiError");
const User = require("../models/User");

/**
 * Development authentication middleware that bypasses actual authentication
 * and auto-logs in as a test user
 */
const devAuth = async (req, res, next) => {
  try {
    // Skip in production
    if (process.env.NODE_ENV === 'production') {
      return next(new ApiError('Development auth bypass not allowed in production', 403));
    }

    // Find or create a test user
    let testUser = await User.findOne({ email: '<EMAIL>' });
    
    if (!testUser) {
      // Create a test user if it doesn't exist
      testUser = await User.create({
        name: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
        role: 'admin',
        emailVerified: true,
        twoFactorEnabled: false
      });
    }

    // Attach user to request
    req.user = testUser;
    req.userId = testUser._id;
    
    // Mock session
    req.session = {
      user: {
        id: testUser._id,
        email: testUser.email,
        role: testUser.role,
        twoFactorEnabled: testUser.twoFactorEnabled,
        emailVerified: testUser.emailVerified
      }
    };

    next();
  } catch (error) {
    console.error('Dev auth error:', error);
    next(new ApiError('Authentication bypass failed', 500));
  }
};

// Export the dev auth middleware
module.exports = {
  auth: devAuth,
  // Make sure other middleware functions are available
  ...require('./auth')
};
