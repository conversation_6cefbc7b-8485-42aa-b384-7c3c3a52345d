import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, RefreshCw, Search, Filter } from 'lucide-react';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Badge } from '../../components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';
import { useToast } from '../../components/ui/use-toast';
import { menuService, MenuItem, mockMenuData } from '../../services/menuService';

const MenuManagement = () => {
  const { toast } = useToast();
  const [menus, setMenus] = useState<MenuItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Filters
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState<string>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');

  useEffect(() => {
    fetchMenus();
  }, []);

  const fetchMenus = async () => {
    try {
      setLoading(true);
      setError(null);

      // Try to fetch from API
      try {
        const response = await menuService.getAllMenus(true); // Include inactive
        if (response.success) {
          setMenus(response.data);
        } else {
          throw new Error('Failed to fetch menus');
        }
      } catch (apiError) {
        console.log('API not available, using mock data');
        // Fallback to mock data
        const allMockMenus = [
          ...mockMenuData.admin,
          ...mockMenuData.merchant,
          ...mockMenuData.trader
        ];
        setMenus(allMockMenus);
      }
    } catch (err) {
      setError('Failed to load menus');
      console.error('Menu fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteMenu = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this menu?')) {
      return;
    }

    try {
      await menuService.deleteMenu(id);
      toast({
        title: 'Success',
        description: 'Menu deleted successfully',
      });
      fetchMenus(); // Refresh data
    } catch (error) {
      console.error('Error deleting menu:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete menu. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Filter menus based on search and filters
  const filteredMenus = menus.filter(menu => {
    const matchesSearch = searchTerm 
      ? menu.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
        menu.to.toLowerCase().includes(searchTerm.toLowerCase()) ||
        menu.description?.toLowerCase().includes(searchTerm.toLowerCase())
      : true;
    
    const matchesRole = roleFilter !== 'all' 
      ? menu.role === roleFilter 
      : true;
    
    const matchesCategory = categoryFilter !== 'all'
      ? menu.metadata?.category === categoryFilter
      : true;
    
    return matchesSearch && matchesRole && matchesCategory;
  });

  // Get unique categories for filter
  const categories = Array.from(new Set(menus.map(menu => menu.metadata?.category).filter(Boolean)));

  const renderStatusBadge = (isActive: boolean) => {
    return (
      <Badge variant={isActive ? "default" : "secondary"}>
        {isActive ? 'Active' : 'Inactive'}
      </Badge>
    );
  };

  const renderRoleBadge = (role: string) => {
    const colors: Record<string, "default" | "secondary" | "destructive" | "outline"> = {
      admin: "destructive",
      merchant: "default",
      trader: "secondary",
      all: "outline"
    };
    
    return (
      <Badge variant={colors[role] || "outline"}>
        {role.toUpperCase()}
      </Badge>
    );
  };

  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading menus...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold">Menu Management</h1>
            <p className="text-muted-foreground">Manage system navigation menus and submenus</p>
          </div>
          <div className="flex gap-2">
            <Button onClick={fetchMenus} variant="outline" size="sm">
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </Button>
            <Button>
              <Plus className="mr-2 h-4 w-4" /> New Menu
            </Button>
          </div>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
        )}

        {/* Filters */}
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search menus by label, path, or description..."
              className="w-full pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <Select value={roleFilter} onValueChange={setRoleFilter}>
            <SelectTrigger className="w-[150px]">
              <Filter className="mr-2 h-4 w-4" />
              <SelectValue placeholder="Role" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Roles</SelectItem>
              <SelectItem value="admin">Admin</SelectItem>
              <SelectItem value="merchant">Merchant</SelectItem>
              <SelectItem value="trader">Trader</SelectItem>
            </SelectContent>
          </Select>
          <Select value={categoryFilter} onValueChange={setCategoryFilter}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {categories.map(category => (
                <SelectItem key={category} value={category!}>
                  {category!.charAt(0).toUpperCase() + category!.slice(1)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Menus ({filteredMenus.length})</CardTitle>
            <CardDescription>
              Manage navigation menus for different user roles
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Label</TableHead>
                  <TableHead>Path</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Order</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredMenus.length > 0 ? (
                  filteredMenus.map((menu) => (
                    <TableRow key={menu._id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{menu.label}</div>
                          {menu.description && (
                            <div className="text-sm text-muted-foreground">
                              {menu.description}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="font-mono text-sm">{menu.to}</TableCell>
                      <TableCell>{renderRoleBadge(menu.role)}</TableCell>
                      <TableCell className="capitalize">
                        {menu.metadata?.category || 'N/A'}
                      </TableCell>
                      <TableCell>{menu.order}</TableCell>
                      <TableCell>{renderStatusBadge(menu.isActive)}</TableCell>
                      <TableCell className="text-right space-x-2">
                        <Button variant="ghost" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteMenu(menu._id)}
                        >
                          <Trash2 className="h-4 w-4 text-destructive" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                      No menus found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Menus</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{menus.length}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Admin Menus</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {menus.filter(m => m.role === 'admin').length}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Merchant Menus</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {menus.filter(m => m.role === 'merchant').length}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Trader Menus</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {menus.filter(m => m.role === 'trader').length}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default MenuManagement;
