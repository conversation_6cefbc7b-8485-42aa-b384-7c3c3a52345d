import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  Chip,
  Divider,
  IconButton,
  Menu,
  MenuItem,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TableSortLabel,
  TextField,
  Toolbar,
  Tooltip,
  Typography,
  useTheme,
} from '@mui/material';
import {
  Add as AddIcon,
  FilterList as FilterListIcon,
  MoreVert as MoreVertIcon,
  Search as SearchIcon,
  CheckCircle as ActiveIcon,
  Cancel as InactiveIcon,
  Star as DefaultIcon,
  StarBorder as NotDefaultIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import { format } from 'date-fns';
import { visuallyHidden } from '@mui/utils';
import PageHeader from '../../../components/layout/PageHeader';
import api from '../../../services/api';

// Table header columns
const headCells = [
  { id: 'name', label: 'Strategy Name', sortable: true },
  { id: 'status', label: 'Status', sortable: true, align: 'center' },
  { id: 'isDefault', label: 'Default', sortable: true, align: 'center' },
  { id: 'rules', label: 'Rules', sortable: false },
  { id: 'effectiveDate', label: 'Effective Date', sortable: true },
  { id: 'createdAt', label: 'Created', sortable: true },
  { id: 'actions', label: 'Actions', sortable: false, align: 'right' },
];

// Enhanced Table Head
function EnhancedTableHead(props) {
  const { order, orderBy, onRequestSort } = props;
  const createSortHandler = (property) => (event) => {
    onRequestSort(event, property);
  };

  return (
    <TableHead>
      <TableRow>
        {headCells.map((headCell) => (
          <TableCell
            key={headCell.id}
            align={headCell.align || 'left'}
            sortDirection={orderBy === headCell.id ? order : false}
            sx={{ fontWeight: 'bold' }}
          >
            {headCell.sortable ? (
              <TableSortLabel
                active={orderBy === headCell.id}
                direction={orderBy === headCell.id ? order : 'asc'}
                onClick={createSortHandler(headCell.id)}
              >
                {headCell.label}
                {orderBy === headCell.id ? (
                  <Box component="span" sx={visuallyHidden}>
                    {order === 'desc' ? 'sorted descending' : 'sorted ascending'}
                  </Box>
                ) : null}
              </TableSortLabel>
            ) : (
              headCell.label
            )}
          </TableCell>
        ))}
      </TableRow>
    </TableHead>
  );
}

// Status Chip Component
const StatusChip = ({ status }) => {
  const theme = useTheme();
  
  const statusMap = {
    active: {
      label: 'Active',
      color: 'success',
      icon: <ActiveIcon fontSize="small" sx={{ mr: 0.5 }} />
    },
    inactive: {
      label: 'Inactive',
      color: 'error',
      icon: <InactiveIcon fontSize="small" sx={{ mr: 0.5 }} />
    },
    draft: {
      label: 'Draft',
      color: 'warning',
      icon: <InactiveIcon fontSize="small" sx={{ mr: 0.5 }} />
    }
  };

  const { label, color, icon } = statusMap[status] || { label: status, color: 'default' };

  return (
    <Chip
      icon={icon}
      label={label}
      color={color}
      size="small"
      variant="outlined"
      sx={{ 
        minWidth: 90,
        '& .MuiChip-label': {
          pl: 0.5,
        },
      }}
    />
  );
};

const ReserveStrategyListPage = () => {
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  const theme = useTheme();
  
  // State for table data and pagination
  const [strategies, setStrategies] = useState([]);
  const [loading, setLoading] = useState(true);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [order, setOrder] = useState('desc');
  const [orderBy, setOrderBy] = useState('createdAt');
  
  // State for filters and search
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [defaultFilter, setDefaultFilter] = useState('all');
  
  // State for action menu
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedStrategy, setSelectedStrategy] = useState(null);
  const menuOpen = Boolean(anchorEl);

  // Fetch strategies from API
  const fetchStrategies = async () => {
    try {
      setLoading(true);
      
      // Build query params
      const params = {
        page: page + 1,
        limit: rowsPerPage,
        sort: `${order === 'desc' ? '-' : ''}${orderBy}`,
        search: searchTerm || undefined,
        status: statusFilter !== 'all' ? statusFilter : undefined,
        isDefault: defaultFilter !== 'all' ? (defaultFilter === 'default') : undefined,
      };
      
      const response = await api.get('/reserve-strategies', { params });
      setStrategies(response.data.docs);
      setTotal(response.data.total);
    } catch (error) {
      console.error('Error fetching reserve strategies:', error);
      enqueueSnackbar('Failed to load reserve strategies', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  // Initial data load
  useEffect(() => {
    fetchStrategies();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page, rowsPerPage, order, orderBy, statusFilter, defaultFilter]);

  // Handle search with debounce
  useEffect(() => {
    const timerId = setTimeout(() => {
      if (page === 0) {
        fetchStrategies();
      } else {
        setPage(0);
      }
    }, 500);
    
    return () => clearTimeout(timerId);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchTerm]);

  // Handle sort request
  const handleRequestSort = (event, property) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };

  // Handle change page
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  // Handle change rows per page
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Handle action menu open
  const handleMenuOpen = (event, strategy) => {
    setAnchorEl(event.currentTarget);
    setSelectedStrategy(strategy);
  };

  // Handle action menu close
  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedStrategy(null);
  };

  // Handle set as default
  const handleSetAsDefault = async () => {
    if (!selectedStrategy) return;
    
    try {
      await api.patch(`/reserve-strategies/${selectedStrategy._id}/set-default`);
      enqueueSnackbar('Default reserve strategy updated', { variant: 'success' });
      fetchStrategies();
    } catch (error) {
      console.error('Error setting default strategy:', error);
      enqueueSnackbar(
        error.response?.data?.message || 'Failed to set default strategy',
        { variant: 'error' }
      );
    } finally {
      handleMenuClose();
    }
  };

  // Handle delete strategy
  const handleDelete = async () => {
    if (!selectedStrategy) return;
    
    if (!window.confirm(`Are you sure you want to delete "${selectedStrategy.name}"?`)) {
      handleMenuClose();
      return;
    }
    
    try {
      await api.delete(`/reserve-strategies/${selectedStrategy._id}`);
      enqueueSnackbar('Reserve strategy deleted', { variant: 'success' });
      fetchStrategies();
    } catch (error) {
      console.error('Error deleting strategy:', error);
      enqueueSnackbar(
        error.response?.data?.message || 'Failed to delete strategy',
        { variant: 'error' }
      );
    } finally {
      handleMenuClose();
    }
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return format(new Date(dateString), 'MMM d, yyyy');
  };

  // Format date range
  const formatDateRange = (from, to) => {
    const fromStr = from ? format(new Date(from), 'MMM d, yyyy') : 'N/A';
    const toStr = to ? format(new Date(to), 'MMM d, yyyy') : 'No end date';
    return `${fromStr} - ${toStr}`;
  };

  // Empty state
  if (loading && strategies.length === 0) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography>Loading reserve strategies...</Typography>
      </Box>
    );
  }

  return (
    <Box>
      <PageHeader
        title="Reserve Strategies"
        subtitle="Manage reserve fund allocation rules and settings"
        action={
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={() => navigate('/reserve-strategies/new')}
          >
            New Strategy
          </Button>
        }
      />

      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 2 }}>
            <TextField
              variant="outlined"
              placeholder="Search strategies..."
              size="small"
              sx={{ minWidth: 250, flex: 1 }}
              InputProps={{
                startAdornment: <SearchIcon color="action" sx={{ mr: 1 }} />,
              }}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            
            <FormControl variant="outlined" size="small" sx={{ minWidth: 150 }}>
              <InputLabel>Status</InputLabel>
              <Select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                label="Status"
              >
                <MenuItem value="all">All Statuses</MenuItem>
                <MenuItem value="active">Active</MenuItem>
                <MenuItem value="inactive">Inactive</MenuItem>
                <MenuItem value="draft">Draft</MenuItem>
              </Select>
            </FormControl>
            
            <FormControl variant="outlined" size="small" sx={{ minWidth: 150 }}>
              <InputLabel>Default</InputLabel>
              <Select
                value={defaultFilter}
                onChange={(e) => setDefaultFilter(e.target.value)}
                label="Default"
              >
                <MenuItem value="all">All</MenuItem>
                <MenuItem value="default">Default Only</MenuItem>
                <MenuItem value="not-default">Not Default</MenuItem>
              </Select>
            </FormControl>
            
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={fetchStrategies}
              disabled={loading}
            >
              Refresh
            </Button>
          </Box>
        </CardContent>
      </Card>

      <Paper variant="outlined">
        <TableContainer>
          <Table size="small">
            <EnhancedTableHead
              order={order}
              orderBy={orderBy}
              onRequestSort={handleRequestSort}
              rowCount={strategies.length}
            />
            <TableBody>
              {strategies.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={headCells.length} align="center" sx={{ py: 4 }}>
                    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                      <SearchIcon sx={{ fontSize: 48, color: 'text.disabled', mb: 1 }} />
                      <Typography variant="subtitle1" color="textSecondary">
                        No reserve strategies found
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        {searchTerm || statusFilter !== 'all' || defaultFilter !== 'all'
                          ? 'Try adjusting your search or filter criteria'
                          : 'Create a new reserve strategy to get started'}
                      </Typography>
                      {!searchTerm && statusFilter === 'all' && defaultFilter === 'all' && (
                        <Button
                          variant="outlined"
                          color="primary"
                          startIcon={<AddIcon />}
                          onClick={() => navigate('/reserve-strategies/new')}
                          sx={{ mt: 2 }}
                        >
                          Create Strategy
                        </Button>
                      )}
                    </Box>
                  </TableCell>
                </TableRow>
              ) : (
                strategies.map((strategy) => (
                  <TableRow
                    key={strategy._id}
                    hover
                    sx={{
                      '&:hover': {
                        backgroundColor: theme.palette.action.hover,
                      },
                      cursor: 'pointer',
                    }}
                    onClick={() => navigate(`/reserve-strategies/${strategy._id}`)}
                  >
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Typography variant="subtitle2" sx={{ fontWeight: 600, mr: 1 }}>
                          {strategy.name}
                        </Typography>
                        {strategy.tags && strategy.tags.length > 0 && (
                          <Box sx={{ display: 'flex', gap: 0.5, ml: 1 }}>
                            {strategy.tags.slice(0, 2).map((tag) => (
                              <Chip
                                key={tag}
                                label={tag}
                                size="small"
                                sx={{
                                  height: 20,
                                  fontSize: '0.65rem',
                                  '& .MuiChip-label': {
                                    px: 0.75,
                                  },
                                }}
                              />
                            ))}
                            {strategy.tags.length > 2 && (
                              <Chip
                                label={`+${strategy.tags.length - 2}`}
                                size="small"
                                sx={{
                                  height: 20,
                                  fontSize: '0.65rem',
                                  '& .MuiChip-label': {
                                    px: 0.5,
                                  },
                                }}
                              />
                            )}
                          </Box>
                        )}
                      </Box>
                      {strategy.description && (
                        <Typography variant="body2" color="textSecondary" noWrap>
                          {strategy.description}
                        </Typography>
                      )}
                    </TableCell>
                    
                    <TableCell align="center">
                      <StatusChip status={strategy.status} />
                    </TableCell>
                    
                    <TableCell align="center">
                      <Tooltip 
                        title={strategy.isDefault ? 'Default strategy' : 'Set as default'}
                        arrow
                      >
                        <IconButton
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                            if (!strategy.isDefault) {
                              handleSetAsDefault(strategy);
                            }
                          }}
                          color={strategy.isDefault ? 'primary' : 'default'}
                          disabled={strategy.isDefault}
                        >
                          {strategy.isDefault ? (
                            <DefaultIcon color="primary" />
                          ) : (
                            <NotDefaultIcon />
                          )}
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                    
                    <TableCell>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                        {strategy.rules && strategy.rules.slice(0, 2).map((rule, idx) => (
                          <Chip
                            key={idx}
                            label={`${rule.type === 'percentage' ? `${rule.percentage}%` : `$${rule.fixedAmount}`}`}
                            size="small"
                            variant="outlined"
                            color={rule.isActive ? 'primary' : 'default'}
                            sx={{
                              height: 24,
                              fontSize: '0.7rem',
                              '& .MuiChip-label': {
                                px: 1,
                              },
                            }}
                          />
                        ))}
                        {strategy.rules && strategy.rules.length > 2 && (
                          <Chip
                            label={`+${strategy.rules.length - 2} more`}
                            size="small"
                            variant="outlined"
                            sx={{
                              height: 24,
                              fontSize: '0.7rem',
                              '& .MuiChip-label': {
                                px: 1,
                              },
                            }}
                          />
                        )}
                      </Box>
                    </TableCell>
                    
                    <TableCell>
                      {formatDateRange(strategy.effectiveFrom, strategy.effectiveTo)}
                    </TableCell>
                    
                    <TableCell>
                      {formatDate(strategy.createdAt)}
                    </TableCell>
                    
                    <TableCell align="right">
                      <IconButton
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleMenuOpen(e, strategy);
                        }}
                        aria-label="actions"
                      >
                        <MoreVertIcon fontSize="small" />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
        
        <TablePagination
          rowsPerPageOptions={[5, 10, 25, 50]}
          component="div"
          count={total}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          sx={{
            borderTop: `1px solid ${theme.palette.divider}`,
            '& .MuiTablePagination-toolbar': {
              minHeight: 56,
            },
          }}
        />
      </Paper>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={menuOpen}
        onClose={handleMenuClose}
        onClick={(e) => e.stopPropagation()}
      >
        <MenuItem 
          onClick={() => {
            navigate(`/reserve-strategies/${selectedStrategy?._id}`);
            handleMenuClose();
          }}
        >
          <ViewIcon fontSize="small" sx={{ mr: 1 }} />
          View Details
        </MenuItem>
        <MenuItem 
          onClick={() => {
            navigate(`/reserve-strategies/${selectedStrategy?._id}/edit`);
            handleMenuClose();
          }}
        >
          <EditIcon fontSize="small" sx={{ mr: 1 }} />
          Edit
        </MenuItem>
        {!selectedStrategy?.isDefault && (
          <MenuItem onClick={handleSetAsDefault}>
            <DefaultIcon fontSize="small" sx={{ mr: 1 }} />
            Set as Default
          </MenuItem>
        )}
        <Divider />
        <MenuItem 
          onClick={handleDelete}
          sx={{ color: 'error.main' }}
        >
          <DeleteIcon fontSize="small" sx={{ mr: 1 }} />
          Delete
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default ReserveStrategyListPage;
