// API Key Types
export interface ApiKey {
  id: string;
  name: string;
  key: string;
  lastUsed: string | null;
  createdAt: string;
  expiresAt?: string;
  isActive: boolean;
  permissions?: string[];
}

// User and Merchant Types
export interface User {
  _id: string;
  id?: string; // For compatibility with AuthContext
  name: string;
  email: string;
  phone?: string;
  role: string;
  avatar?: string;
  isVerified?: boolean;
  twoFactorEnabled?: boolean;
  lastLogin?: string;
  permissions?: string[];
  createdAt?: string;
  updatedAt?: string;
  // For type compatibility with AuthContext
  merchantId?: string;
  businessName?: string;
  businessType?: string;
  businessAddress?: {
    street?: string;
    city?: string;
    state?: string;
    country?: string;
    postalCode?: string;
  };
  balance?: number;
  traderId?: string;
  level?: number;
  totalTrades?: number;
}

export interface Merchant extends User {
  businessName: string;
  businessType?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  taxId?: string;
  website?: string;
  status?: 'active' | 'inactive' | 'suspended';
}

// Assignment Types
export type AssignmentStatus = 'active' | 'inactive' | 'completed' | 'pending' | 'suspended';
export type AssignmentType = 'temporary' | 'permanent' | 'project_based';
export type CollectionPeriod = 'daily' | 'weekly' | 'monthly' | 'quarterly';

export interface CollectionTarget {
  amount: number;
  period: CollectionPeriod;
  currency?: string;
}

export interface PerformanceMetrics {
  totalCollected: number;
  successfulTransactions: number;
  pendingTransactions: number;
  failedTransactions: number;
  lastUpdated: string;
  currency?: string;
  periodStart?: string;
  periodEnd?: string;
}

export interface TraderAssignment {
  _id: string;
  trader: User | string;
  merchant: Merchant | string;
  assignedBy: User | string;
  assignmentType: AssignmentType;
  startDate: string | Date;
  endDate?: string | Date;
  status: AssignmentStatus;
  collectionTarget?: CollectionTarget;
  permissions: string[];
  notes?: string;
  createdAt?: string | Date;
  updatedAt?: string | Date;
  performanceMetrics?: PerformanceMetrics;
}

// Request/Response Types
export interface PaginatedResponse<T> {
  success: boolean;
  data: T[];
  pagination: PaginationMeta;
}

export interface AssignmentFilters {
  traderId?: string;
  merchantId?: string;
  status?: AssignmentStatus;
  page?: number;
  limit?: number;
  search?: string;
  startDate?: string;
  endDate?: string;
}

// Form Data Types
export interface AssignmentFormData {
  traderId: string;
  merchantId: string;
  assignmentType: AssignmentType;
  startDate: Date | null;
  endDate?: Date | null;
  collectionTarget?: {
    amount: number;
    period: CollectionPeriod;
    currency: string;
  };
  permissions: string[];
  notes?: string;
}

// Admin Trader Types
export interface TraderListItem {
  _id: string;
  name: string;
  email: string;
  phone?: string;
  status: 'active' | 'inactive' | 'suspended';
  lastActive?: string;
  createdAt: string;
  updatedAt: string;
  assignedMerchants: number;
  totalCollections: number;
  totalAmountCollected: number;
}

export interface TraderListResponse {
  data: TraderListItem[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// API Response Types
export interface ApiResponse<T = unknown> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
  code?: number;
}

export interface PaginationMeta {
  total: number;
  page: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
  limit: number;
}
