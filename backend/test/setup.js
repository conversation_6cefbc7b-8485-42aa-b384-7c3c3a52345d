// Test setup file for global test configuration
const nodemailer = require('nodemailer');

// This file is loaded by <PERSON><PERSON>'s setupFiles configuration
// It runs before the test framework is installed in the environment
// So we can't use Jest globals like beforeAll, afterAll, or jest.mock here

// Instead, we'll export a function that can be called from setupFilesAfterEnv
const setupTestEnvironment = () => {
  // Mock nodemailer for all tests
  jest.mock('nodemailer', () => ({
    createTransport: jest.fn().mockImplementation(() => ({
      verify: jest.fn((callback) => callback(null, true)),
      sendMail: jest.fn().mockResolvedValue({ messageId: 'test-message-id' }),
      on: jest.fn(),
      close: jest.fn()
    })),
  }));

  // This will be called by setupFilesAfterEnv
  beforeAll(() => {
    console.log('Running global test setup...');
  });

  afterAll(() => {
    console.log('Running global test teardown...');
  });
};

// Export the setup function for use in setupFilesAfterEnv
module.exports = {
  setupTestEnvironment,
  nodemailerMock: {
    transporter: {
      verify: jest.fn((callback) => callback(null, true)),
      sendMail: jest.fn().mockResolvedValue({ messageId: 'test-message-id' }),
      on: jest.fn(),
      close: jest.fn()
    },
    mockCreateTransport: jest.fn()
  }
};
