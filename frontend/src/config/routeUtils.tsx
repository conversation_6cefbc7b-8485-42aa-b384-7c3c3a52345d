import { ReactNode } from 'react';
import { Suspense } from 'react';
import { ErrorBoundary } from '../components/ErrorBoundary';
import LoadingSpinner from '../components/ui/LoadingSpinner';
import DashboardLayout from '../layouts/DashboardLayout';
import ProtectedRoute from '../components/ProtectedRoute';

type RouteWrapperProps = {
  children: ReactNode;
  title?: string;
};

// Loading component for Suspense fallback
const Loading = () => (
  <div className="flex items-center justify-center min-h-screen">
    <LoadingSpinner size="lg" />
  </div>
);

// Wrapper component for routes with loading and error boundaries
export const RouteWrapper = ({ children, title }: RouteWrapperProps) => (
  <ErrorBoundary fallback={
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-4">Something went wrong</h2>
        <button
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          onClick={() => window.location.reload()}
        >
          Reload Page
        </button>
      </div>
    </div>
  }>
    <Suspense fallback={<Loading />}>
      {children}
    </Suspense>
  </ErrorBoundary>
);

// Create a protected route with role-based access
export const createProtectedRoute = (
  Component: React.ComponentType,
  role: 'merchant' | 'admin' | 'trader',
  title?: string
) => {
  return (
    <ProtectedRoute role={role}>
      <DashboardLayout title={title}>
        <RouteWrapper>
          <Component />
        </RouteWrapper>
      </DashboardLayout>
    </ProtectedRoute>
  );
};
