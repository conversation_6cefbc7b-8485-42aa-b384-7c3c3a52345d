import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableContainer, 
  TableHead, 
  TableRow, 
  Paper, 
  Button, 
  Chip, 
  IconButton, 
  Tooltip,
  TextField,
  InputAdornment,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Box,
  Typography
} from '@mui/material';
import { 
  Add as AddIcon, 
  Edit as EditIcon, 
  Delete as DeleteIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import api from '../../../services/api';
import ConfirmDialog from '../../../components/common/ConfirmDialog';
import PageHeader from '../../../components/layout/PageHeader';

const ReserveStrategyList = () => {
  const [strategies, setStrategies] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [pagination, setPagination] = useState({
    page: 0,
    limit: 10,
    total: 0
  });
  const [deleteDialog, setDeleteDialog] = useState({
    open: false,
    strategyId: null,
    strategyName: ''
  });
  
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();

  const fetchStrategies = async () => {
    try {
      setLoading(true);
      const params = {
        page: pagination.page + 1,
        limit: pagination.limit,
        search: searchTerm,
        status: statusFilter === 'all' ? '' : statusFilter
      };
      
      const response = await api.get('/reserve-strategies', { params });
      
      setStrategies(response.data.strategies);
      setPagination(prev => ({
        ...prev,
        total: response.data.pagination.total
      }));
    } catch (error) {
      console.error('Error fetching reserve strategies:', error);
      enqueueSnackbar('Failed to load reserve strategies', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStrategies();
  }, [pagination.page, pagination.limit, searchTerm, statusFilter]);

  const handleDeleteClick = (strategy) => {
    setDeleteDialog({
      open: true,
      strategyId: strategy._id,
      strategyName: strategy.name
    });
  };

  const handleDeleteConfirm = async () => {
    try {
      await api.delete(`/reserve-strategies/${deleteDialog.strategyId}`);
      enqueueSnackbar('Reserve strategy deleted successfully', { variant: 'success' });
      fetchStrategies();
    } catch (error) {
      console.error('Error deleting reserve strategy:', error);
      enqueueSnackbar('Failed to delete reserve strategy', { variant: 'error' });
    } finally {
      setDeleteDialog({ open: false, strategyId: null, strategyName: '' });
    }
  };

  const handlePageChange = (event, newPage) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  const handleRowsPerPageChange = (event) => {
    setPagination(prev => ({
      ...prev,
      limit: parseInt(event.target.value, 10),
      page: 0
    }));
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'error';
      case 'draft':
        return 'warning';
      default:
        return 'default';
    }
  };

  return (
    <>
      <PageHeader 
        title="Reserve Strategies"
        subtitle="Manage your fund reserve strategies"
        action={
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={() => navigate('/reserve-strategies/create')}
          >
            New Strategy
          </Button>
        }
      />

      <Paper sx={{ p: 2, mb: 3 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <TextField
            variant="outlined"
            size="small"
            placeholder="Search strategies..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            sx={{ width: 300 }}
          />
          
          <Box display="flex" alignItems="center">
            <FormControl variant="outlined" size="small" sx={{ minWidth: 150, mr: 2 }}>
              <InputLabel>Status</InputLabel>
              <Select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                label="Status"
              >
                <MenuItem value="all">All Statuses</MenuItem>
                <MenuItem value="active">Active</MenuItem>
                <MenuItem value="inactive">Inactive</MenuItem>
                <MenuItem value="draft">Draft</MenuItem>
              </Select>
            </FormControl>
            
            <Tooltip title="Refresh">
              <IconButton onClick={fetchStrategies}>
                <RefreshIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Rules</TableCell>
                <TableCell>Default</TableCell>
                <TableCell>Created</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={7} align="center">
                    Loading...
                  </TableCell>
                </TableRow>
              ) : strategies.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} align="center">
                    No reserve strategies found
                  </TableCell>
                </TableRow>
              ) : (
                strategies.map((strategy) => (
                  <TableRow key={strategy._id} hover>
                    <TableCell>
                      <Typography variant="body1" fontWeight="medium">
                        {strategy.name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {strategy.description || 'No description'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={strategy.status} 
                        color={getStatusColor(strategy.status)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      {strategy.rules?.[0]?.type || 'N/A'}
                      {strategy.rules?.length > 1 && ` +${strategy.rules.length - 1} more`}
                    </TableCell>
                    <TableCell>
                      {strategy.rules?.length || 0} rule{strategy.rules?.length !== 1 ? 's' : ''}
                    </TableCell>
                    <TableCell>
                      {strategy.isDefault ? (
                        <Chip label="Default" color="primary" size="small" />
                      ) : (
                        <Chip label="-" variant="outlined" size="small" />
                      )}
                    </TableCell>
                    <TableCell>
                      {new Date(strategy.createdAt).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      <Tooltip title="Edit">
                        <IconButton 
                          size="small" 
                          onClick={() => navigate(`/reserve-strategies/edit/${strategy._id}`)}
                        >
                          <EditIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete">
                        <IconButton 
                          size="small" 
                          color="error"
                          onClick={() => handleDeleteClick(strategy)}
                          disabled={strategy.isDefault}
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>

        <Box display="flex" justifyContent="space-between" alignItems="center" mt={2} px={2}>
          <Typography variant="body2" color="text.secondary">
            Showing {strategies.length} of {pagination.total} strategies
          </Typography>
          
          <Box display="flex" alignItems="center">
            <Typography variant="body2" sx={{ mr: 1 }}>
              Rows per page:
            </Typography>
            <Select
              value={pagination.limit}
              onChange={handleRowsPerPageChange}
              size="small"
              variant="standard"
              disableUnderline
              sx={{ minWidth: 50 }}
            >
              <MenuItem value={5}>5</MenuItem>
              <MenuItem value={10}>10</MenuItem>
              <MenuItem value={25}>25</MenuItem>
              <MenuItem value={50}>50</MenuItem>
            </Select>
            
            <Button 
              disabled={pagination.page === 0}
              onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
              sx={{ ml: 2 }}
            >
              Previous
            </Button>
            <Button 
              disabled={(pagination.page + 1) * pagination.limit >= pagination.total}
              onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
            >
              Next
            </Button>
          </Box>
        </Box>
      </Paper>

      <ConfirmDialog
        open={deleteDialog.open}
        title="Delete Reserve Strategy"
        content={`Are you sure you want to delete "${deleteDialog.strategyName}"? This action cannot be undone.`}
        onClose={() => setDeleteDialog({ open: false, strategyId: null, strategyName: '' })}
        onConfirm={handleDeleteConfirm}
        confirmText="Delete"
        confirmColor="error"
      />
    </>
  );
};

export default ReserveStrategyList;
