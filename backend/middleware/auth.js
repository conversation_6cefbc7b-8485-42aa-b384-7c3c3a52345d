const User = require("../models/User");
const ApiError = require("../utils/ApiError");
const logger = require('../utils/logger');

// Security headers middleware
const securityHeaders = (req, res, next) => {
  // Prevent MIME type sniffing
  res.set('X-Content-Type-Options', 'nosniff');
  
  // Prevent clickjacking
  res.set('X-Frame-Options', 'DENY');
  
  // Enable XSS protection
  res.set('X-XSS-Protection', '1; mode=block');
  
  // Content Security Policy
  res.set('Content-Security-Policy', "default-src 'self'; script-src 'self' 'unsafe-inline'");
  
  next();
};

// Role-based access control middleware
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.session || !req.session.user) {
      return res.status(401).json({ message: 'Not authenticated' });
    }
    
    if (!roles.includes(req.session.user.role)) {
      return next(
        new ApiError(
          `User role ${req.session.user.role} is not authorized to access this route`,
          403
        )
      );
    }
    
    next();
  };
};

// Authentication middleware
const auth = async (req, res, next) => {
  try {
    // Get session ID from cookie
    const sessionId = req.cookies.sessionId || req.headers['x-session-id'];
    
    if (!sessionId) {
      return next(new ApiError(401, 'No session ID found'));
    }
    
    // Find user with this session ID
    const user = await User.findOne({
      'sessions._id': new mongoose.Types.ObjectId(sessionId)
    });
    
    if (!user) {
      // Clear invalid session cookie
      res.clearCookie('sessionId');
      return next(new ApiError(401, 'Invalid or expired session'));
    }
    
    // Find the session and update last used
    const session = user.sessions.id(sessionId);
    if (!session) {
      res.clearCookie('sessionId');
      return next(new ApiError(401, 'Invalid or expired session'));
    }
    
    // Update session last used
    session.lastUsed = new Date();
    await user.save();
    
    // Check if user is active
    if (user.status !== 'active') {
      return next(new ApiError(403, 'Account is deactivated'));
    }
    
    // Add user to request
    req.user = user;
    next();
    
  } catch (error) {
    logger.error('Authentication error:', error);
    return next(new ApiError(500, 'Authentication failed'));
  }
};

/**
 * Middleware to authorize roles
 */
exports.authorize = (...roles) => {
  return (req, res, next) => {
    if (!roles.includes(req.user.role)) {
      return next(
        new ApiError(
          `User role ${req.user.role} is not authorized to access this route`,
          403
        )
      );
    }
    next();
  };
};

/**
 * Middleware to check if email is verified
 */
exports.requireEmailVerification = (req, res, next) => {
  if (!req.user.isVerified) {
    return next(new ApiError('Please verify your email address', 403));
  }
  next();
};

/**
 * Middleware to check if 2FA is required
 */
const require2FA = async (req, res, next) => {
  try {
    const user = await User.findById(req.user.id);
    
    if (user.twoFactorEnabled) {
      // Check if 2FA was already verified in this session
      if (!req.session?.twoFAPassed) {
        return res.status(202).json({
          success: true,
          requires2FA: true,
          message: 'Two-factor authentication required'
        });
      }
    }
    
    next();
  } catch (error) {
    next(error);
  }
};

// Alias auth as protect for backward compatibility
const protect = auth;

module.exports = {
  auth,
  protect, // Export as both auth and protect for backward compatibility
  authorize,
  securityHeaders,
  require2FA
}
