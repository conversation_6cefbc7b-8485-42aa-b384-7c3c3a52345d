const express = require('express');
const router = express.Router();
const dashboardController = require('../controllers/dashboardController');
const { authenticate, authorize } = require('../middleware/auth');

// Apply authentication middleware to all dashboard routes
router.use(authenticate);

// Dashboard statistics
router.get('/stats', authorize(['admin']), dashboardController.getDashboardStats);

// System status
router.get('/system/status', authorize(['admin']), dashboardController.getSystemStatus);

// Activity log
router.get('/activity-log', authorize(['admin']), dashboardController.getActivityLog);

// Generate report
router.get('/reports/generate', authorize(['admin']), dashboardController.generateReport);

module.exports = router;
