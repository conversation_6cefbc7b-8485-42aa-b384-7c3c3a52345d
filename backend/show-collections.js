const mongoose = require('mongoose');
require('dotenv').config();

async function showCollections() {
  try {
    console.log('🔍 CONNECTING TO PAYMENT-GATEWAY DATABASE...');
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/payment-gateway');
    console.log('✅ Connected successfully!\n');
    
    // Get all collections
    const collections = await mongoose.connection.db.listCollections().toArray();
    
    console.log('📊 PAYMENT-GATEWAY DATABASE COLLECTIONS:');
    console.log('='.repeat(80));
    
    for (const collection of collections) {
      const count = await mongoose.connection.db.collection(collection.name).countDocuments();
      console.log(`\n🗂️  ${collection.name.toUpperCase()}`);
      console.log(`   📈 Documents: ${count}`);
      
      if (count > 0) {
        // Get sample document
        const sample = await mongoose.connection.db.collection(collection.name).findOne();
        const fields = Object.keys(sample).filter(key => !key.startsWith('__'));
        console.log(`   📋 Fields: ${fields.join(', ')}`);
        
        // Show first few documents for small collections
        if (count <= 5) {
          const docs = await mongoose.connection.db.collection(collection.name).find({}).limit(3).toArray();
          console.log(`   📄 Sample Data:`);
          docs.forEach((doc, index) => {
            console.log(`      ${index + 1}. ${JSON.stringify(doc, null, 2).substring(0, 200)}...`);
          });
        }
        
        // Show relationships
        const relationships = [];
        fields.forEach(field => {
          if (field.includes('Id') || field.includes('_id') || field.endsWith('id')) {
            relationships.push(field);
          }
        });
        
        if (relationships.length > 0) {
          console.log(`   🔗 Relationships: ${relationships.join(', ')}`);
        }
      } else {
        console.log(`   ⚠️  Empty collection`);
      }
      
      console.log('   ' + '-'.repeat(60));
    }
    
    // Show collection relationships
    console.log('\n🔗 COLLECTION RELATIONSHIPS:');
    console.log('='.repeat(80));
    
    // Users relationships
    const userCount = await mongoose.connection.db.collection('users').countDocuments();
    if (userCount > 0) {
      const userRoles = await mongoose.connection.db.collection('users').aggregate([
        { $group: { _id: '$role', count: { $sum: 1 } } }
      ]).toArray();
      
      console.log('\n👥 USERS:');
      userRoles.forEach(role => {
        console.log(`   - ${role._id}: ${role.count} users`);
      });
    }
    
    // Trader assignments relationships
    const assignmentCount = await mongoose.connection.db.collection('traderassignments').countDocuments();
    if (assignmentCount > 0) {
      console.log('\n📋 TRADER ASSIGNMENTS:');
      console.log(`   - Total assignments: ${assignmentCount}`);
      
      const assignmentStatus = await mongoose.connection.db.collection('traderassignments').aggregate([
        { $group: { _id: '$status', count: { $sum: 1 } } }
      ]).toArray();
      
      assignmentStatus.forEach(status => {
        console.log(`   - ${status._id}: ${status.count} assignments`);
      });
    }
    
    // Transaction relationships
    const transactionCount = await mongoose.connection.db.collection('transactions').countDocuments();
    if (transactionCount > 0) {
      console.log('\n💳 TRANSACTIONS:');
      console.log(`   - Total transactions: ${transactionCount}`);
      
      const transactionStatus = await mongoose.connection.db.collection('transactions').aggregate([
        { $group: { _id: '$status', count: { $sum: 1 } } }
      ]).toArray();
      
      transactionStatus.forEach(status => {
        console.log(`   - ${status._id}: ${status.count} transactions`);
      });
    }
    
    console.log('\n✅ Collection analysis completed!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await mongoose.connection.close();
  }
}

showCollections();
