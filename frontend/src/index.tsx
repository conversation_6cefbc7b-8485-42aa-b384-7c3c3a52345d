import React from "react"
import <PERSON>actD<PERSON> from "react-dom/client"
import App from "./App"
import "./index.css"

// Configure axios defaults
import axios from "axios"

axios.defaults.baseURL = process.env.REACT_APP_API_URL || "http://localhost:5000"
axios.defaults.timeout = 10000

// Add request interceptor for debugging
axios.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("token")
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  },
)

// Authentication removed - no interceptors needed

const root = ReactDOM.createRoot(document.getElementById("root") as HTMLElement)

root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)
