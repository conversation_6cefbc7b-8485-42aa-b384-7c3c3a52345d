class WebhookError extends Error {
  constructor(message, statusCode = 400, code = 'WEBHOOK_ERROR') {
    super(message);
    this.name = 'WebhookError';
    this.statusCode = statusCode;
    this.code = code;
    Error.captureStackTrace(this, this.constructor);
  }
}

class WebhookValidationError extends WebhookError {
  constructor(message = 'Invalid webhook data') {
    super(message, 400, 'WEBHOOK_VALIDATION_ERROR');
  }
}

class WebhookNotFoundError extends WebhookError {
  constructor(webhookId) {
    super(`Webhook ${webhookId} not found`, 404, 'WEBHOOK_NOT_FOUND');
  }
}

class WebhookDeliveryError extends WebhookError {
  constructor(message = 'Failed to deliver webhook') {
    super(message, 500, 'WEBHOOK_DELIVERY_ERROR');
  }
}

module.exports = {
  WebhookError,
  WebhookValidationError,
  WebhookNotFoundError,
  WebhookDeliveryError
};
