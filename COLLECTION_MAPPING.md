# 📊 PAYMENT-GATEWAY DATABASE COLLECTIONS MAPPING

## 🗄️ Database Overview
**Database Name:** `payment-gateway`  
**MongoDB URI:** `mongodb://localhost:27017/payment-gateway`

---

## 📋 COLLECTIONS → MODELS → CONTROLLERS → COMPONENTS

### 🔥 **CORE COLLECTIONS (Fully Integrated)**

#### 1. **users** Collection
- **Model:** `backend/models/User.js`
- **Controller:** `backend/routes/users.js`
- **Components:** 
  - `frontend/src/pages/admin/RealAdminDashboard.tsx` (Users tab)
  - `frontend/src/pages/admin/TradersPage.tsx`
  - `frontend/src/pages/admin/MerchantsPage.tsx`
- **API Endpoints:**
  - `GET /api/users` - Get all users
  - `GET /api/users/role/:role` - Get users by role
  - `POST /api/users` - Create user
  - `PUT /api/users/:id` - Update user
- **Data Fields:** `name, email, role, businessName, phone, isActive, createdAt`
- **Status:** ✅ **FULLY CONNECTED**

#### 2. **traderassignments** Collection
- **Model:** `backend/models/TraderAssignment.js`
- **Controller:** `backend/routes/traderAssignments.js`
- **Components:**
  - `frontend/src/pages/admin/RealTraderAssignments.tsx` ⭐ **NEW**
  - `frontend/src/pages/admin/TraderAssignments.tsx` (Legacy)
- **API Endpoints:**
  - `GET /api/trader-assignments` - Get all assignments
  - `GET /api/trader-assignments/:id` - Get assignment by ID
  - `POST /api/trader-assignments` - Create assignment
  - `PUT /api/trader-assignments/:id` - Update assignment
  - `DELETE /api/trader-assignments/:id` - Delete assignment
  - `GET /api/trader-assignments/trader/:traderId` - Get by trader
  - `GET /api/trader-assignments/merchant/:merchantId` - Get by merchant
- **Data Fields:** `traderId, merchantId, assignmentType, status, collectionTarget, permissions, startDate, endDate`
- **Status:** ✅ **FULLY CONNECTED**

#### 3. **transactions** Collection
- **Model:** `backend/models/Transaction.js`
- **Controller:** `backend/routes/transaction.js`
- **Components:**
  - `frontend/src/pages/admin/RealAdminDashboard.tsx` (Transactions tab)
  - `frontend/src/pages/admin/PaymentsPage.tsx`
- **API Endpoints:**
  - `GET /api/transactions` - Get all transactions
  - `GET /api/transactions/:id` - Get transaction by ID
  - `POST /api/transactions` - Create transaction
- **Data Fields:** `transactionId, amount, status, paymentMethod, customerInfo, merchantId, createdAt`
- **Status:** ✅ **FULLY CONNECTED**

#### 4. **settlements** Collection
- **Model:** `backend/models/Settlement.js`
- **Controller:** `backend/routes/settlementRoutes.js`
- **Components:**
  - `frontend/src/pages/admin/RealAdminDashboard.tsx` (Settlements tab)
- **API Endpoints:**
  - `GET /api/settlements` - Get all settlements
  - `GET /api/collections/settlements` - Get settlements via collections API
- **Data Fields:** `settlementId, type, fromParty, toParty, amount, status, createdAt`
- **Status:** ✅ **FULLY CONNECTED**

---

### 🔧 **BUSINESS LOGIC COLLECTIONS (Partially Integrated)**

#### 5. **merchants** Collection
- **Model:** `backend/models/Merchant.js` + `backend/models/MerchantProfile.js`
- **Controller:** `backend/routes/merchant.js`
- **Components:**
  - `frontend/src/pages/admin/RealAdminDashboard.tsx` (Merchants tab)
  - `frontend/src/pages/admin/MerchantsPage.tsx`
- **API Endpoints:**
  - `GET /api/merchants` - Get all merchants
  - `GET /api/collections/merchants` - Get merchants via collections API
- **Data Fields:** `businessName, businessType, userId, bankAccount, kycStatus`
- **Status:** 🟡 **PARTIALLY CONNECTED**

#### 6. **reconciliations** Collection
- **Model:** `backend/models/Reconciliation.js`
- **Controller:** `backend/routes/admin.js` (partial)
- **Components:** 
  - `frontend/src/pages/admin/RealAdminDashboard.tsx` (Collections overview)
- **API Endpoints:**
  - `GET /api/collections/reconciliations` - Get reconciliations
- **Data Fields:** `reconciliationId, period, status, discrepancies, totalAmount`
- **Status:** 🟡 **PARTIALLY CONNECTED**

#### 7. **reserveactivities** Collection
- **Model:** `backend/models/ReserveActivity.js`
- **Controller:** `backend/routes/reserveStrategyRoutes.js`
- **Components:**
  - `frontend/src/pages/admin/RealAdminDashboard.tsx` (Collections overview)
- **API Endpoints:**
  - `GET /api/collections/reserve-activities` - Get reserve activities
- **Data Fields:** `activityType, amount, balance, description, createdAt`
- **Status:** 🟡 **PARTIALLY CONNECTED**

#### 8. **commissionstructures** Collection
- **Model:** `backend/models/CommissionStructure.js`
- **Controller:** `backend/routes/commissionRoutes.js`
- **Components:**
  - `frontend/src/pages/admin/RealAdminDashboard.tsx` (Collections overview)
- **API Endpoints:**
  - `GET /api/collections/commission-structures` - Get commission structures
- **Data Fields:** `name, type, rate, conditions, isActive`
- **Status:** 🟡 **PARTIALLY CONNECTED**

---

### 🔍 **SYSTEM COLLECTIONS (Available for Integration)**

#### 9. **apikeys** Collection
- **Model:** `backend/models/ApiKey.js`
- **Controller:** Not yet implemented
- **Components:** Not yet implemented
- **Potential Use:** API key management interface
- **Status:** 🔴 **NOT CONNECTED**

#### 10. **groups** Collection
- **Model:** `backend/models/Group.js`
- **Controller:** Not yet implemented
- **Components:** Not yet implemented
- **Potential Use:** User group management
- **Status:** 🔴 **NOT CONNECTED**

#### 11. **reservestrategies** Collection
- **Model:** `backend/models/ReserveStrategy.js`
- **Controller:** `backend/routes/reserveStrategyRoutes.js`
- **Components:** Not yet implemented
- **Potential Use:** Reserve fund strategy management
- **Status:** 🟡 **CONTROLLER READY**

---

## 🔗 **COLLECTION RELATIONSHIPS**

### Primary Relationships:
```
users (role: trader) ←→ traderassignments.traderId
users (role: merchant) ←→ traderassignments.merchantId
users (role: admin) ←→ traderassignments.assignedBy

users (role: merchant) ←→ transactions.merchantId
users ←→ merchants.userId

settlements.fromParty ←→ users
settlements.toParty ←→ users

commissionstructures ←→ traderassignments
reconciliations ←→ settlements
reserveactivities ←→ settlements
```

---

## 📊 **INTEGRATION STATUS SUMMARY**

| Collection | Model | Controller | Component | Status |
|------------|-------|------------|-----------|---------|
| users | ✅ | ✅ | ✅ | **COMPLETE** |
| traderassignments | ✅ | ✅ | ✅ | **COMPLETE** |
| transactions | ✅ | ✅ | ✅ | **COMPLETE** |
| settlements | ✅ | ✅ | ✅ | **COMPLETE** |
| merchants | ✅ | ✅ | 🟡 | **PARTIAL** |
| reconciliations | ✅ | 🟡 | 🟡 | **PARTIAL** |
| reserveactivities | ✅ | ✅ | 🟡 | **PARTIAL** |
| commissionstructures | ✅ | ✅ | 🟡 | **PARTIAL** |
| apikeys | ✅ | 🔴 | 🔴 | **PENDING** |
| groups | ✅ | 🔴 | 🔴 | **PENDING** |
| reservestrategies | ✅ | ✅ | 🔴 | **PENDING** |

---

## 🎯 **NEXT INTEGRATION OPPORTUNITIES**

### High Priority:
1. **Complete Merchants Integration** - Full CRUD operations
2. **Reconciliations Management** - Financial reconciliation interface
3. **Commission Structures** - Rate and fee management
4. **API Keys Management** - Security and access control

### Medium Priority:
5. **Reserve Activities** - Fund management interface
6. **Groups Management** - User organization
7. **Reserve Strategies** - Strategic fund allocation

---

## 🚀 **CURRENT ACCESS POINTS**

### Frontend Routes:
- **Main Dashboard:** `http://localhost:3000/admin`
- **Trader Assignments:** `http://localhost:3000/admin/trader-assignments`
- **Merchants:** `http://localhost:3000/admin/merchants`
- **Payments:** `http://localhost:3000/admin/payments`

### API Endpoints:
- **Collections API:** `http://localhost:5000/api/collections/*`
- **Trader Assignments:** `http://localhost:5000/api/trader-assignments`
- **Users:** `http://localhost:5000/api/users`
- **Transactions:** `http://localhost:5000/api/transactions`

---

## 🔌 **DETAILED API ENDPOINT MAPPING**

### ✅ **FULLY IMPLEMENTED ENDPOINTS**

#### Users API (`/api/users`)
```
GET    /api/users                    - Get all users
GET    /api/users/role/:role         - Get users by role (trader/merchant/admin)
GET    /api/users/:id                - Get user by ID
POST   /api/users                    - Create new user
PUT    /api/users/:id                - Update user
DELETE /api/users/:id                - Delete user
```

#### Trader Assignments API (`/api/trader-assignments`)
```
GET    /api/trader-assignments                      - Get all assignments (with pagination)
GET    /api/trader-assignments/:id                  - Get assignment by ID
POST   /api/trader-assignments                      - Create new assignment
PUT    /api/trader-assignments/:id                  - Update assignment
DELETE /api/trader-assignments/:id                  - Delete assignment
GET    /api/trader-assignments/trader/:traderId     - Get assignments by trader
GET    /api/trader-assignments/merchant/:merchantId - Get assignments by merchant
```

#### Transactions API (`/api/transactions`)
```
GET    /api/transactions             - Get all transactions
GET    /api/transactions/:id         - Get transaction by ID
POST   /api/transactions             - Create new transaction
PUT    /api/transactions/:id         - Update transaction status
```

#### Collections API (`/api/collections`)
```
GET    /api/collections/overview                - Get all collections with counts
GET    /api/collections/settlements             - Get settlements data
GET    /api/collections/merchants               - Get merchants data
GET    /api/collections/trader-assignments      - Get trader assignments
GET    /api/collections/reconciliations         - Get reconciliations
GET    /api/collections/reserve-activities      - Get reserve activities
GET    /api/collections/commission-structures   - Get commission structures
GET    /api/collections/:collectionName         - Generic collection viewer
```

### 🟡 **PARTIALLY IMPLEMENTED ENDPOINTS**

#### Merchants API (`/api/merchants`)
```
GET    /api/merchants                - Get all merchants (basic)
GET    /api/merchants/:id            - Get merchant by ID
POST   /api/merchants                - Create merchant (needs enhancement)
PUT    /api/merchants/:id            - Update merchant (needs enhancement)
```

#### Settlements API (`/api/settlements`)
```
GET    /api/settlements              - Get all settlements
GET    /api/settlements/:id          - Get settlement by ID
POST   /api/settlements              - Create settlement (needs enhancement)
```

### 🔴 **PENDING IMPLEMENTATION**

#### API Keys Management (`/api/apikeys`) - NOT IMPLEMENTED
```
GET    /api/apikeys                  - Get all API keys
POST   /api/apikeys                  - Create new API key
PUT    /api/apikeys/:id              - Update API key
DELETE /api/apikeys/:id              - Delete API key
```

#### Groups Management (`/api/groups`) - NOT IMPLEMENTED
```
GET    /api/groups                   - Get all groups
POST   /api/groups                   - Create new group
PUT    /api/groups/:id               - Update group
DELETE /api/groups/:id               - Delete group
```
