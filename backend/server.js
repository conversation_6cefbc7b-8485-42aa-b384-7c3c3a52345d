// Simple logger for bootstrapping before the main logger is available
const bootstrapLogger = {
  info: (...args) => console.log('[BOOTSTRAP]', ...args),
  error: (...args) => console.error('[BOOTSTRAP ERROR]', ...args)
};

// Load environment variables and configurations
bootstrapLogger.info('Loading config...');
const config = require('./config/config');
bootstrapLogger.info('Loading app...');
const { createApp, connectDB } = require("./app");
bootstrapLogger.info('Loading logger...');
const logger = require('./utils/logger');

// Destructure configuration
const { port, env, frontendUrl } = config;
bootstrapLogger.info(`Configuration loaded - Port: ${port}, Env: ${env}`);

// Keep track of server instance
let serverInstance = null;

// Start the server
const startServer = async () => {
  try {
    logger.info('Starting server initialization...');
    
    // First connect to the database
    logger.info('Connecting to database...');
    await connectDB();
    logger.info('Successfully connected to database');
    
    // Create the Express app and get the HTTP server
    logger.info('Creating Express app and HTTP server...');
    const { app, server } = createApp();
    serverInstance = server;
    logger.info('Express app and HTTP server created successfully');
    
    // Handle server errors
    server.on('error', (error) => {
      if (error.syscall !== 'listen') {
        throw error;
      }

      // Handle specific listen errors with friendly messages
      switch (error.code) {
        case 'EACCES':
          logger.error(`Port ${port} requires elevated privileges`);
          process.exit(1);
          break;
        case 'EADDRINUSE':
          logger.error(`Port ${port} is already in use`);
          process.exit(1);
          break;
        default:
          throw error;
      }
    });
    
    // Start the server
    logger.info(`Attempting to start server on port ${port}...`);
    
    // Log all available network interfaces
    const os = require('os');
    const networkInterfaces = os.networkInterfaces();
    logger.info('Available network interfaces:', JSON.stringify(networkInterfaces, null, 2));
    
    server.listen(port, '0.0.0.0', () => {
      const addr = server.address();
      const bind = typeof addr === 'string' ? 'pipe ' + addr : 'port ' + addr.port;
      
      logger.info(`🚀 Server running on ${bind}`);
      logger.info(`📊 Environment: ${env}`);
      logger.info(`🌐 Frontend URL: ${frontendUrl}`);
      
      // Log the actual address and port the server is listening on
      logger.info(`Server listening on address: ${JSON.stringify(addr)}`);
      
      // Verify the server is actually listening
      const net = require('net');
      const serverCheck = net.createServer();
      
      serverCheck.once('error', (err) => {
        if (err.code === 'EADDRINUSE') {
          logger.info(`Port ${port} is actually in use by another process`);
        } else {
          logger.error('Error checking server status:', err);
        }
        serverCheck.close();
      });
      
      serverCheck.once('listening', () => {
        logger.info(`Port ${port} is available (this should not happen if the server is running correctly)`);
        serverCheck.close();
      });
      
      serverCheck.listen(port, '0.0.0.0');
      
      // Log bypass status if enabled
      if (config.jwt.enableBypass) {
        logger.warn('⚠️  JWT Bypass is ENABLED. This should only be used in development and testing environments!');
      }
      
      logger.info('✅ Server and database connection are ready');
      
      // Log server status periodically
      setInterval(() => {
        logger.debug('Server keep-alive ping');
      }, 300000); // 5 minutes
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
      // Don't exit the process, just log the error
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', (err) => {
      logger.error('Uncaught Exception:', err);
      // Don't exit the process, just log the error
    });

    // Handle termination signals
    const handleShutdown = (signal) => {
      logger.info(`${signal} signal received: closing HTTP server`);
      if (serverInstance) {
        serverInstance.close(() => {
          logger.info('HTTP server closed');
          process.exit(0);
        });

        // Force close the server after 10 seconds
        setTimeout(() => {
          logger.error('Could not close connections in time, forcefully shutting down');
          process.exit(1);
        }, 10000);
      }
    };

    ['SIGTERM', 'SIGINT', 'SIGUSR2'].forEach((signal) => {
      process.on(signal, () => handleShutdown(signal));
    });

    // Handle nodemon restart
    process.once('SIGUSR2', () => {
      if (serverInstance) {
        serverInstance.close(() => {
          process.kill(process.pid, 'SIGUSR2');
        });
      }
    });

    return server;
  } catch (error) {
    bootstrapLogger.error(`Failed to start server: ${error.message}`);
    process.exit(1);
  }
};

// Start the application
startServer();

module.exports = startServer;
