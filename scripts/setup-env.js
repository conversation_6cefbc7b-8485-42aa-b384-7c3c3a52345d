const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const envExamplePath = path.join(__dirname, '..', '.env.example');
const envPath = path.join(__dirname, '..', '.env');

// Check if .env already exists
if (fs.existsSync(envPath)) {
  console.log('.env file already exists. Do you want to overwrite it? (y/n)');
  rl.question('> ', (answer) => {
    if (answer.toLowerCase() === 'y') {
      setupEnv();
    } else {
      console.log('Setup cancelled.');
      rl.close();
    }
  });
} else {
  setupEnv();
}

async function setupEnv() {
  try {
    // Read the example file
    const envExample = fs.readFileSync(envExamplePath, 'utf8');
    
    // Create a new .env file with the example content
    fs.writeFileSync(envPath, envExample);
    
    console.log('✅ .env file created successfully!');
    console.log('Please update the .env file with your configuration.');
    
    // Generate a secure random string for secrets
    const generateRandomString = (length = 32) => {
      return require('crypto').randomBytes(Math.ceil(length / 2))
        .toString('hex')
        .slice(0, length);
    };

    // Update the .env file with secure random values for secrets
    const envContent = fs.readFileSync(envPath, 'utf8');
    const updatedEnv = envContent
      .replace(/your_jwt_secret_key_here/g, generateRandomString(64))
      .replace(/your_jwt_temp_secret_here/g, generateRandomString(64))
      .replace(/your_cookie_secret_here/g, generateRandomString(64));
    
    fs.writeFileSync(envPath, updatedEnv);
    
    console.log('🔑 Generated secure random values for JWT and cookie secrets.');
    
  } catch (error) {
    console.error('❌ Error setting up .env file:', error.message);
  } finally {
    rl.close();
  }
}
