import { execSync } from 'child_process';
import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the current file's directory in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const config = {
  rootDir: path.join(__dirname, '..'),
  publicDir: path.join(__dirname, '../public'),
  buildDir: path.join(__dirname, '../build'),
};

// Helper function to log with timestamp
function log(message) {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] ${message}`);
}

// Helper function to log error and exit
function errorAndExit(message, error) {
  log(`ERROR: ${message}`);
  if (error) {
    console.error(error);
  }
  process.exit(1);
}

async function main() {
  try {
    log('Starting custom build process...');
    log(`Root directory: ${config.rootDir}`);
    log(`Public directory: ${config.publicDir}`);
    log(`Build directory: ${config.buildDir}`);

    // Verify public directory exists
    if (!fs.existsSync(config.publicDir)) {
      errorAndExit(`Public directory does not exist: ${config.publicDir}`);
    }

    // List public directory contents
    log('Public directory contents:');
    const publicFiles = fs.readdirSync(config.publicDir);
    publicFiles.forEach(file => {
      const filePath = path.join(config.publicDir, file);
      const stats = fs.statSync(filePath);
      log(`  ${file} (${stats.isDirectory() ? 'dir' : 'file'}, ${stats.size} bytes)`);
    });

    // Clean build directory
    log('Cleaning build directory...');
    if (fs.existsSync(config.buildDir)) {
      log('Removing existing build directory...');
      fs.removeSync(config.buildDir);
    }

    // Run react-scripts build using the local binary
    const reactScriptsPath = path.join(config.rootDir, 'node_modules', '.bin', 'react-scripts');
    log(`Running react-scripts build from: ${reactScriptsPath}`);
    try {
      execSync(`"${reactScriptsPath}" build`, { 
        stdio: 'inherit',
        cwd: config.rootDir,
        shell: true
      });
    } catch (error) {
      errorAndExit('Build failed', error);
    }

    // Verify build directory was created
    if (!fs.existsSync(config.buildDir)) {
      errorAndExit(`Build directory was not created: ${config.buildDir}`);
    }

    // List build directory contents after build
    log('Build directory contents after build:');
    const buildFiles = fs.readdirSync(config.buildDir);
    buildFiles.forEach(file => {
      const filePath = path.join(config.buildDir, file);
      const stats = fs.statSync(filePath);
      log(`  ${file} (${stats.isDirectory() ? 'dir' : 'file'}, ${stats.size} bytes)`);
    });

    // Manually copy public files
    log('Copying public files...');
    try {
      // Ensure build directory exists
      fs.ensureDirSync(config.buildDir);

      // Copy all files from public to build directory
      fs.copySync(config.publicDir, config.buildDir, {
        dereference: true,
        filter: (src) => {
          const filename = path.basename(src);
          // Skip copying build directory if it exists in public
          if (filename === 'build') {
            log(`Skipping build directory: ${src}`);
            return false;
          }
          log(`Copying: ${src}`);
          return true;
        },
      });

      log('Successfully copied public files.');
    } catch (error) {
      errorAndExit('Failed to copy public files', error);
    }

    // Final verification
    log('Final build directory contents:');
    const finalFiles = fs.readdirSync(config.buildDir);
    finalFiles.forEach(file => {
      const filePath = path.join(config.buildDir, file);
      const stats = fs.statSync(filePath);
      log(`  ${file} (${stats.isDirectory() ? 'dir' : 'file'}, ${stats.size} bytes)`);
    });

    log('Build completed successfully!');
  } catch (error) {
    errorAndExit('Unexpected error during build', error);
  }
}

// Run the build process
main().catch(error => {
  errorAndExit('Unhandled error in build process', error);
});
