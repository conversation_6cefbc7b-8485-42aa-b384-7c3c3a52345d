const express = require('express');
const mongoose = require('mongoose');
const path = require('path');
const { createServer } = require('http');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const mongoSanitize = require('express-mongo-sanitize');
const xss = require('xss-clean');
const hpp = require('hpp');
const morgan = require('morgan');

// Import config
const { setupSocket } = require('./config/socket');
const { setupSwagger } = require('./config/swagger');
const { securityHeaders, preventNoSqlInjection } = require('./middleware/security');
const { errorHandler } = require('./middleware/errorHandler');
const logger = require('./utils/logger');

// Load environment variables
const envPath = process.env.NODE_ENV === 'test' 
  ? path.join(__dirname, '../.env.test')
  : path.join(__dirname, '../.env');
require('dotenv').config({ path: envPath });

// Ensure required environment variables are set
const requiredEnvVars = [
  'NODE_ENV',
  'MONGODB_URI',
  'FRONTEND_URL',
  'API_VERSION'
];

// Check for missing environment variables
const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
if (missingVars.length > 0) {
  const errorMsg = `Missing required environment variables: ${missingVars.join(', ')}`;
  logger.error(errorMsg);
  
  // Don't exit during tests - let the test framework handle the failure
  if (process.env.NODE_ENV !== 'test') {
    process.exit(1);
  } else {
    console.warn('Test environment - continuing despite missing vars:', errorMsg);
  }
}

// Import API routes
const settlementRoutes = require('./routes/settlementRoutes');
const reserveStrategyRoutes = require('./routes/reserveStrategyRoutes');
const commissionRoutes = require('./routes/commissionRoutes');
const adminRoutes = require('./routes/admin');
const paymentRoutes = require('./routes/payment');
const merchantRoutes = require('./routes/merchant');
const transactionRoutes = require('./routes/transaction');
const usersRoutes = require('./routes/users');
const collectionsRoutes = require('./routes/collections');

/**
 * Create and configure the Express application
 * @returns {Object} Object containing the Express app and HTTP server
 */
const createApp = () => {
  // Initialize express app
  const app = express();
  const server = createServer(app);

  // ====================================
  // Security Middleware
  // ====================================
  
  // Set security HTTP headers
  app.use(helmet());
  
  // Enable CORS with security configuration (development settings)
  const corsOptions = {
    origin: function (origin, callback) {
      // In development, allow all origins - in production, you should restrict this
      callback(null, true);
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'X-Requested-With',
      'Accept',
      'Origin',
      'X-Auth-Token',
      'X-CSRF-Token',
      'Access-Control-Allow-Origin',
      'Access-Control-Allow-Credentials'
    ],
    exposedHeaders: [
      'Content-Length',
      'Content-Type',
      'Authorization',
      'X-Requested-With',
      'Access-Control-Allow-Origin',
      'Access-Control-Allow-Credentials'
    ],
    maxAge: 86400, // 24 hours
    preflightContinue: false,
    optionsSuccessStatus: 204
  };

  // Apply CORS middleware
  app.use(cors(corsOptions));
  
  // Handle preflight requests
  app.options('*', cors(corsOptions));
  
  // Add CORS headers to all responses
  app.use((req, res, next) => {
    const origin = req.headers.origin;
    if (origin) {
      res.header('Access-Control-Allow-Origin', origin);
    }
    res.header('Access-Control-Allow-Credentials', 'true');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, PATCH, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
    
    // Handle preflight requests
    if (req.method === 'OPTIONS') {
      return res.status(200).end();
    }
    
    next();
  });
  
  // Body parser, reading data from body into req.body
  app.use(express.json({ limit: '10kb' }));
  app.use(express.urlencoded({ extended: true, limit: '10kb' }));
  
  // Cookie parser (removed session management)
  
  // Data sanitization against NoSQL query injection
  app.use(mongoSanitize());
  
  // Data sanitization against XSS
  app.use(xss());
  
  // Prevent parameter pollution
  app.use(hpp({
    whitelist: [
      'duration', 'ratingsQuantity', 'ratingsAverage', 'maxGroupSize', 'difficulty', 'price'
    ]
  }));
  
  // Prevent NoSQL injection
  app.use(preventNoSqlInjection);
  
  // Security headers (additional)
  app.use(securityHeaders);
  
  // ====================================
  // Development Logging
  // ====================================
  if (process.env.NODE_ENV === 'development') {
    app.use(morgan('dev', {
      skip: (req) => req.originalUrl.startsWith('/health')
    }));
  }
  
  // ====================================
  // API Routes
  // ====================================
  
  // Health check endpoint
  app.get('/health', (req, res) => {
    try {
      const status = {
        status: 'OK',
        version: process.env.npm_package_version,
        node: process.version,
        environment: process.env.NODE_ENV,
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        database: mongoose.connection.readyState === 1 ? 'connected' : 'disconnected'
      };
      
      return res.status(200).json(status);
    } catch (error) {
      logger.error('Health check failed:', error);
      return res.status(500).json({
        status: 'error',
        message: 'Health check failed',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  });

  // API Documentation
  setupSwagger(app);
  
  // Mount API routes (no authentication required)
  app.use('/api/payments', paymentRoutes);
  app.use('/api/merchants', merchantRoutes);
  app.use('/api/transactions', transactionRoutes);
  app.use('/api/users', usersRoutes);
  app.use('/api/collections', collectionsRoutes);
  app.use('/api/admin', adminRoutes);
  app.use('/api/settlements', settlementRoutes);
  app.use('/api/reserve-strategies', reserveStrategyRoutes);
  app.use('/api/commissions', commissionRoutes);
  
  // Health check endpoint
  app.get('/api/health', (req, res) => {
    res.status(200).json({
      status: 'ok',
      timestamp: new Date().toISOString()
    });
  });
  
  // Handle 404 for undefined routes
  app.use('/api', (req, res) => {
    res.status(404).json({
      success: false,
      error: {
        code: 'NOT_FOUND',
        message: 'The requested resource was not found',
        details: {
          method: req.method,
          path: req.originalUrl,
          timestamp: new Date().toISOString()
        }
      }
    });
  });
  
  // Serve static files in production
  if (process.env.NODE_ENV === 'production') {
    app.use(express.static(path.join(__dirname, '../frontend/build')));
    
    // Handle React routing, return all requests to React app
    app.get('*', (req, res) => {
      res.sendFile(path.join(__dirname, '../frontend/build/index.html'));
    });
  } else {
    // In development, redirect to API docs
    app.get('/', (req, res) => {
      res.redirect('/api-docs');
    });
  }
  
  // ====================================
  // Error Handling
  // ====================================
  
  // Handle 404 - Must be after all other routes
  app.all('*', (req, res, next) => {
    res.status(404).json({
      success: false,
      error: {
        code: 'NOT_FOUND',
        message: 'The requested resource was not found',
        details: {
          method: req.method,
          path: req.originalUrl,
          timestamp: new Date().toISOString()
        }
      }
    });
  });
  
  // Global error handler
  app.use(errorHandler);
  
  // ====================================
  // WebSocket Setup
  // ====================================
  setupSocket(server);
  
  return { app, server };
};

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  // In production, you might want to restart the server here
  if (process.env.NODE_ENV === 'production') {
    process.exit(1);
  }
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  // In production, you might want to restart the server here
  if (process.env.NODE_ENV === 'production') {
    process.exit(1);
  }
});

// Export the createApp function and connectDB
const db = require('./config/db');

// For testing purposes, create and export the app and server directly
const { app, server } = createApp();

// Export for testing
module.exports = {
  app,
  server,
  createApp,
  connectDB: db.connectDB
};
