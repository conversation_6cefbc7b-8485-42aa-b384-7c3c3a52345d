{"name": "payment-gateway-frontend", "version": "1.0.0", "private": true, "type": "module", "dependencies": {"@hookform/resolvers": "^3.3.4", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.4", "@radix-ui/react-avatar": "^1.0.3", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.0.5", "@radix-ui/react-form": "^0.1.7", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^1.2.2", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.0.6", "@tanstack/react-query": "^4.36.1", "@types/react-toastify": "^4.0.2", "ajv": "^8.12.0", "axios": "^1.6.7", "bootstrap": "^5.3.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^2.30.0", "lucide-react": "^0.263.1", "next-themes": "^0.2.1", "react": "^18.2.0", "react-bootstrap": "^2.10.10", "react-dom": "^18.2.0", "react-hook-form": "^7.49.3", "react-router-dom": "^6.21.3", "react-toastify": "^10.0.4", "recharts": "^2.10.4", "tailwind-merge": "^1.14.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4"}, "scripts": {"start": "react-scripts start", "dev": "react-scripts start", "build": "node scripts/custom-build.js", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint . --config eslint.config.js", "lint:fix": "eslint . --fix --config eslint.config.js", "format": "prettier --write \"src/**/*.{ts,tsx,css,scss}\"", "type-check": "tsc --noEmit", "validate": "npm run type-check && npm run lint"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.7", "@mui/material": "^5.15.7", "@tanstack/react-query-devtools": "^4.36.1", "@tanstack/react-table": "^8.10.7", "@types/jest": "^29.5.5", "@types/node": "^20.8.2", "@types/react": "^18.2.21", "@types/react-dom": "^18.2.7", "@types/react-router-dom": "^5.3.3", "@types/recharts": "^1.8.29", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "eslint": "^8.53.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.0.3", "react-scripts": "5.0.1", "tailwindcss": "^3.3.3", "typescript": "^5.2.2"}, "proxy": "http://localhost:5000"}