{"version": 3, "file": "static/js/825.7e4f6110.chunk.js", "mappings": "oJAaM,MAAAA,GAAcC,E,QAAAA,GAAiB,cAAe,CAClD,CAAC,OAAQ,CAAEC,EAAG,eAAgBC,IAAK,Y,yMCdrC,SAASC,EAAMC,EAAfC,GAAoE,IAArCC,EAAKC,GAApCF,EACE,OAAOG,KAAKF,IAAIC,EAAKC,KAAKD,IAAID,EAAKF,G,CCDrC,SAASK,EACPC,EACAC,GAEA,IADA,yBAAEC,GAA2B,GAA7BC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAsC,CAAC,EAEvC,OAAO,SAAqBG,GAG1B,GAFoB,OAApBN,QAAoB,IAApBA,GAAAA,EAAuBM,IAEU,IAA7BJ,IAAyCI,EAA4BC,iBACvE,cAAON,QAAP,IAAOA,OAAP,EAAOA,EAAkBK,E,EC8B/B,SAASE,EAAmBC,GAA+D,IAA5CC,EAAqCP,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GACjFQ,EAAyB,GA0C7B,MAAMC,EAA2BA,KAC/B,MAAMC,EAAgBF,EAAgBG,IAAKC,IAClCC,EAAAA,EAAAA,eAAoBD,IAE7B,OAAO,SAAkBE,GACvB,MAAMC,GAAgB,OAALD,QAAK,IAALA,OAAA,EAAAA,EAAQR,KAAcI,EACvC,OAAOM,EAAAA,EAAAA,SACL,KAAM,CAAG,CAAE,UAASV,KAAc,IAAKQ,EAAO,CAACR,GAAYS,KAC3D,CAACD,EAAOC,G,GAMd,OADAN,EAAYH,UAAYA,EACjB,CAlDP,SACEW,EACAL,GAEA,MAAMM,GAAcL,EAAAA,EAAAA,eAAkDD,GAChEO,EAAQX,EAAgBP,OAG9B,SAASmB,EACPC,GAEA,MAzDNP,MAyDcA,EAzDdQ,SAyDqBA,KAAaC,GAAYF,EAClCG,GAAe,OAALV,QAAK,IAALA,OAAA,EAAAA,EAAQR,GAAWa,KAAUD,EAGvC3B,GAAQyB,EAAAA,EAAAA,SAAc,IAAMO,EAASE,OAAOC,OAAOH,IACzD,OAAOI,EAAAA,EAAAA,eAACH,EAAQJ,SAAhB,CAAyB7B,MAAOA,GAAQ+B,E,CAa1C,OAvBAd,EAAkB,IAAIA,EAAiBI,GAsBvCQ,EAASQ,YAAcX,EAAoB,WACpC,CAACG,EAVR,SAAoBS,EAAsBf,GACxC,MAAMU,GAAe,OAALV,QAAK,IAALA,OAAA,EAAAA,EAAQR,GAAWa,KAAUD,EACvCK,GAAUO,EAAAA,EAAAA,YAAiBN,GACjC,GAAID,EAAS,OAAOA,EACpB,QAAuBrB,IAAnBU,EAA8B,OAAOA,EAEzC,MAAM,IAAImB,MAAO,KAAIF,6BAAwCZ,M,IAyB1Ce,EAAqBvB,KAAgBF,G,CAO9D,SAASyB,IAA+C,QAAAC,EAAAjC,UAAAC,OAAvBiC,EAAjC,IAAAC,MAAAF,GAAAG,EAAA,EAAAA,EAAAH,EAAAG,IAAiCF,EAAjCE,GAAApC,UAAAoC,GACE,MAAMC,EAAYH,EAAO,GACzB,GAAsB,IAAlBA,EAAOjC,OAAc,OAAOoC,EAEhC,MAAMC,EAA2B7B,KAC/B,MAAM8B,EAAaL,EAAOvB,IAAKF,IAAD,CAC5B+B,SAAU/B,IACVH,UAAWG,EAAYH,aAGzB,OAAO,SAA2BmC,GAChC,MAAMC,EAAaH,EAAWI,OAAO,CAACC,EAADpD,KAAyC,IAlHpFgD,SAkH0DA,EAlH1DlC,UAkHoEA,GAAzBd,EAMnC,MAAO,IAAKoD,KAFOJ,EAASC,GACK,UAASnC,OAEzC,CAAC,GAEJ,OAAOU,EAAAA,EAAAA,SAAc,KAAM,CAAG,CAAE,UAASqB,EAAU/B,aAAcoC,IAAe,CAACA,G,GAKrF,OADAJ,EAAYhC,UAAY+B,EAAU/B,UAC3BgC,C,CC5GT,SAASO,IAA0C,QAAAZ,EAAAjC,UAAAC,OAAxB6C,EAA3B,IAAAX,MAAAF,GAAAG,EAAA,EAAAA,EAAAH,EAAAG,IAA2BU,EAA3BV,GAAApC,UAAAoC,GACE,OAAQW,GAAYD,EAAKE,QAASC,GAbpC,SAAmBA,EAAqB1D,GACnB,oBAAR0D,EACTA,EAAI1D,GACa,OAAR0D,QAAwB/C,IAAR+C,IACxBA,EAAkCC,QAAU3D,E,CASL4D,CAAOF,EAAKF,G,CAOxD,SAASK,IAA8C,QAAAC,EAAArD,UAAAC,OAAxB6C,EAA/B,IAAAX,MAAAkB,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAA+BR,EAA/BQ,GAAAtD,UAAAsD,GAEE,OAAOC,EAAAA,EAAAA,aAAkBV,KAAeC,GAAOA,E,CCnBjD,MAAMU,GAAOC,EAAAA,EAAAA,YAAyC,CAACpC,EAAOqC,KAC5D,M,SAAQpC,KAAaqC,GAActC,EAC7BuC,EAAgBC,EAAAA,SAAeC,QAAQxC,GACvCyC,EAAYH,EAAcI,KAAKC,GAErC,GAAIF,EAAW,CAEb,MAAMG,EAAaH,EAAU1C,MAAMC,SAE7B6C,EAAcP,EAAcjD,IAAKyD,GACjCA,IAAUL,EAGRF,EAAAA,SAAeQ,MAAMH,GAAc,EAAUL,EAAAA,SAAeS,KAAK,OAC9DC,EAAAA,EAAAA,gBAAqBL,GACvBA,EAAW7C,MAAMC,SAClB,KAEG8C,GAIX,OACEI,EAAAA,EAAAA,eAACC,GAADC,EAAAA,EAAAA,GAAA,GAAef,EADjB,CAC4BV,IAAKS,KAC5Ba,EAAAA,EAAAA,gBAAqBL,IAClBS,EAAAA,EAAAA,cAAmBT,OAAYhE,EAAWiE,GAC1C,K,CAKV,OACEK,EAAAA,EAAAA,eAACC,GAADC,EAAAA,EAAAA,GAAA,GAAef,EADjB,CAC4BV,IAAKS,IAC5BpC,KAKPkC,EAAK5B,YAAc,OAUnB,MAAM6C,GAAYhB,EAAAA,EAAAA,YAAsC,CAACpC,EAAOqC,KAC9D,M,SAAQpC,KAAaqC,GAActC,EAEnC,OAAIkD,EAAAA,EAAAA,gBAAqBjD,IAChBqD,EAAAA,EAAAA,cAAmBrD,EAAU,IAC/BsD,EAAWjB,EAAWrC,EAASD,OAClC4B,IAAKS,EAAemB,EAAYnB,EAAepC,EAAiB2B,KAAQ3B,EAAiB2B,MAItFY,EAAAA,SAAeQ,MAAM/C,GAAY,EAAIuC,EAAAA,SAAeS,KAAK,MAAQ,OAG1EG,EAAU7C,YAAc,YAMxB,MAAMkD,EAAYtF,IAAiD,I,SAA9C8B,GAAH9B,EAChB,OAAOgF,EAAAA,EAAAA,eAAAO,EAAAA,SAAA,KAAGzD,IAOZ,SAAS2C,EAAYG,GACnB,OAAOG,EAAAA,EAAAA,gBAAqBH,IAAUA,EAAMY,OAASF,C,CAGvD,SAASF,EAAWjB,EAAqBsB,GAEvC,MAAMC,EAAgB,IAAKD,GAE3B,IAAK,MAAME,KAAYF,EAAY,CACjC,MAAMG,EAAgBzB,EAAUwB,GAC1BE,EAAiBJ,EAAWE,GAEhB,WAAWG,KAAKH,GAG5BC,GAAiBC,EACnBH,EAAcC,GAAY,WACxBE,KAAcrF,WACdoF,KAAapF,U,EAIRoF,IACPF,EAAcC,GAAYC,GAIR,UAAbD,EACPD,EAAcC,GAAY,IAAKC,KAAkBC,GAC3B,cAAbF,IACTD,EAAcC,GAAY,CAACC,EAAeC,GAAgBE,OAAOC,SAASC,KAAK,K,CAInF,MAAO,IAAK9B,KAAcuB,E,CCrH5B,MAAMQ,GAAmBC,EAAAA,EAAAA,oBAA2CzF,GAsBpE,MCkBM0F,EAvCQ,CACZ,IACA,SACA,MACA,OACA,KACA,KACA,MACA,QACA,QACA,KACA,MACA,KACA,IACA,OACA,MACA,MAuBsBjD,OAAO,CAACkD,EAAW9C,KACzC,MAAM+C,GAAOC,EAAAA,EAAAA,YAAiB,CAAC1E,EAA2CqC,KACxE,M,QAAQsC,KAAYC,GAAmB5E,EACjC6E,EAAYF,EAAUG,EAAOpD,EAMnC,OAJAqD,EAAAA,EAAAA,WAAgB,KACbC,OAAeC,OAAOC,IAAI,cAAe,GACzC,KAEIC,EAAAA,EAAAA,eAACN,GAADO,EAAAA,EAAAA,GAAA,GAAUR,EAAjB,CAAiChD,IAAKS,OAKxC,OAFAoC,EAAKlE,YAAe,aAAYmB,IAEzB,IAAK8C,EAAW,CAAC9C,GAAO+C,IAC9B,CAAC,GCpDJ,SAASY,EAAkDC,GACzD,MAAMC,GAAcC,EAAAA,EAAAA,QAAaF,GAOjC,OALAG,EAAAA,EAAAA,WAAgB,KACdF,EAAY1D,QAAUyD,KAIjBI,EAAAA,EAAAA,SAAc,IAAO,mBAAAC,EAAA/E,EAAAjC,UAAAC,OAAIgH,EAAJ,IAAA9E,MAAAF,GAAAG,EAAA,EAAAA,EAAAH,EAAAG,IAAI6E,EAAJ7E,GAAApC,UAAAoC,GAAA,eAAA4E,EAAaJ,EAAY1D,eAAzB,IAAA8D,OAAA,EAAaA,EAAAE,KAAAN,KAAyBK,E,EAAa,G,CCDjF,MACME,EAAiB,0BACjBC,EAAuB,sCACvBC,EAAgB,gCAEtB,IAAIC,EAEJ,MAAMC,GAA0BC,EAAAA,EAAAA,eAAoB,CAClDC,OAAQ,IAAIC,IACZC,uCAAwC,IAAID,IAC5CE,SAAU,IAAIF,MAuCVG,GAAmBC,EAAAA,EAAAA,YACvB,CAACzG,EAAOqC,KAAiB,IAAAqE,EACvB,MAAM,4BACJC,GAA8B,E,gBAC9BC,E,qBACAC,E,eACAC,E,kBACAC,E,UACAC,KACGC,GACDjH,EACEE,GAAUgH,EAAAA,EAAAA,YAAiBhB,IAC1BiB,EAAMC,IAAWC,EAAAA,EAAAA,UAA+C,MACjEC,EAAa,QAAAZ,EAAA,OAAGS,QAAH,IAAGA,OAAH,EAAGA,EAAMG,qBAAT,IAAAZ,EAAAA,EAAA,OAA0Ba,iBAA1B,IAA0BA,gBAA1B,EAA0BA,WAAYC,UAClD,CAAEC,IAASJ,EAAAA,EAAAA,UAAe,CAAC,GAC5BK,EAAeC,EAAgBtF,EAAeX,GAAS0F,EAAQ1F,IAC/D0E,EAAStF,MAAM8G,KAAK1H,EAAQkG,SAC3ByB,GAAgD,IAAI3H,EAAQoG,wCAAwCwB,OAAO,GAC5GC,EAAoD3B,EAAO4B,QAAQH,GACnE/H,EAAQqH,EAAOf,EAAO4B,QAAQb,IAAS,EACvCc,EAA8B/H,EAAQoG,uCAAuC4B,KAAO,EACpFC,EAAyBrI,GAASiI,EAElCK,EAyIV,SACEvB,GAEA,IADAS,EAAuB3I,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,UAAG4I,iBAAH,IAAGA,gBAAH,EAAGA,WAAYC,SAEtC,MAAMa,EAA2BC,EAAezB,GAC1C0B,GAA8BC,EAAAA,EAAAA,SAAa,GAC3CC,GAAiBD,EAAAA,EAAAA,QAAa,QA6DpC,OA3DAE,EAAAA,EAAAA,WAAgB,KACd,MAAMC,EAAqB7J,IACzB,GAAIA,EAAM8J,SAAWL,EAA4B1G,QAAS,CACxD,MAAMgH,EAAc,CAAEC,cAAehK,GAErC,SAASiK,IACPC,EACEjD,EACAsC,EACAQ,EACA,CAAEI,UAAU,G,CAgBU,UAAtBnK,EAAMoK,aACR5B,EAAc6B,oBAAoB,QAASV,EAAe5G,SAC1D4G,EAAe5G,QAAUkH,EACzBzB,EAAc8B,iBAAiB,QAASX,EAAe5G,QAAS,CAAEwH,MAAM,KAExEN,G,CAGJR,EAA4B1G,SAAU,GAelCyH,EAAUtE,OAAOuE,WAAW,KAChCjC,EAAc8B,iBAAiB,cAAeT,IAC7C,GACH,MAAO,KACL3D,OAAOwE,aAAaF,GACpBhC,EAAc6B,oBAAoB,cAAeR,GACjDrB,EAAc6B,oBAAoB,QAASV,EAAe5G,WAE3D,CAACyF,EAAee,IAEZ,CAELoB,qBAAsBA,IAAOlB,EAA4B1G,SAAU,E,CA9MxC6H,CAAuB5K,IAChD,MAAM8J,EAAS9J,EAAM8J,OACfe,EAAwB,IAAIzJ,EAAQqG,UAAUqD,KAAMC,GAAWA,EAAOC,SAASlB,IAChFT,IAA0BwB,IACX,OAApB9C,QAAoB,IAApBA,GAAAA,EAAuB/H,GACN,OAAjBiI,QAAiB,IAAjBA,GAAAA,EAAoBjI,GACfA,EAAMC,kBAA2B,OAATiI,QAAS,IAATA,GAAAA,MAC5BM,GAEGyC,EA6MV,SACEjD,GAEA,IADAQ,EAAuB3I,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,UAAG4I,iBAAH,IAAGA,gBAAH,EAAGA,WAAYC,SAEtC,MAAMwC,EAAqB1B,EAAexB,GACpCmD,GAA4BzB,EAAAA,EAAAA,SAAa,GAe/C,OAbAE,EAAAA,EAAAA,WAAgB,KACd,MAAMwB,EAAepL,IACnB,GAAIA,EAAM8J,SAAWqB,EAA0BpI,QAAS,CAEtDmH,EAA6BhD,EAAegE,EADxB,CAAElB,cAAehK,GACwC,CAC3EmK,UAAU,G,GAKhB,OADA3B,EAAc8B,iBAAiB,UAAWc,GACnC,IAAM5C,EAAc6B,oBAAoB,UAAWe,IACzD,CAAC5C,EAAe0C,IAEZ,CACLG,eAAgBA,IAAOF,EAA0BpI,SAAU,EAC3DuI,cAAeA,IAAOH,EAA0BpI,SAAU,E,CAnOrCwI,CAAiBvL,IACpC,MAAM8J,EAAS9J,EAAM8J,OACG,IAAI1I,EAAQqG,UAAUqD,KAAMC,GAAWA,EAAOC,SAASlB,MAEjE,OAAd9B,QAAc,IAAdA,GAAAA,EAAiBhI,GACA,OAAjBiI,QAAiB,IAAjBA,GAAAA,EAAoBjI,GACfA,EAAMC,kBAA2B,OAATiI,QAAS,IAATA,GAAAA,MAC5BM,GAsDH,OCrJJ,SACEgD,GAEA,IADAhD,EAAuB3I,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,UAAG4I,iBAAH,IAAGA,gBAAH,EAAGA,WAAYC,SAEtC,MAAMZ,EAAkB2D,EAAeD,IAEvCE,EAAAA,EAAAA,WAAgB,KACd,MAAMC,EAAiB3L,IACH,WAAdA,EAAMd,KACR4I,EAAgB9H,IAIpB,OADAwI,EAAc8B,iBAAiB,UAAWqB,GACnC,IAAMnD,EAAc6B,oBAAoB,UAAWsB,IACzD,CAAC7D,EAAiBU,G,CDmFnBoD,CAAkB5L,IACOgB,IAAUI,EAAQkG,OAAO8B,KAAO,IAExC,OAAftB,QAAe,IAAfA,GAAAA,EAAkB9H,IACbA,EAAMC,kBAAoBiI,IAC7BlI,EAAM6L,iBACN3D,OAEDM,IAEHoB,EAAAA,EAAAA,WAAgB,KACd,GAAKvB,EAUL,OATIR,IAC0D,IAAxDzG,EAAQoG,uCAAuC4B,OACjDjC,EAA4BqB,EAAcsD,KAAKC,MAAMC,cACrDxD,EAAcsD,KAAKC,MAAMC,cAAgB,QAE3C5K,EAAQoG,uCAAuCyE,IAAI5D,IAErDjH,EAAQkG,OAAO2E,IAAI5D,GACnB6D,IACO,KAEHrE,GACwD,IAAxDzG,EAAQoG,uCAAuC4B,OAE/CZ,EAAcsD,KAAKC,MAAMC,cAAgB7E,KAG5C,CAACkB,EAAMG,EAAeX,EAA6BzG,KAQtDwI,EAAAA,EAAAA,WAAgB,IACP,KACAvB,IACLjH,EAAQkG,OAAO6E,OAAO9D,GACtBjH,EAAQoG,uCAAuC2E,OAAO9D,GACtD6D,MAED,CAAC7D,EAAMjH,KAEVwI,EAAAA,EAAAA,WAAgB,KACd,MAAMwC,EAAeA,IAAMzD,EAAM,CAAC,GAElC,OADAD,SAAS4B,iBAAiBtD,EAAgBoF,GACnC,IAAM1D,SAAS2B,oBAAoBrD,EAAgBoF,IACzD,KAGDC,EAAAA,EAAAA,eAACC,EAAUC,KAAXC,EAAAA,EAAAA,GAAA,GACMrE,EAFR,CAGIrF,IAAK8F,EACLmD,MAAO,CACLC,cAAe7C,EACXE,EACE,OACA,YACFtJ,KACDmB,EAAM6K,OAEXV,eAAgBoB,EAAqBvL,EAAMmK,eAAgBJ,EAAaI,gBACxEC,cAAemB,EAAqBvL,EAAMoK,cAAeL,EAAaK,eACtEX,qBAAsB8B,EACpBvL,EAAMyJ,qBACNrB,EAAmBqB,2BA0J7B,SAASuB,IACP,MAAMlM,EAAQ,IAAI0M,YAAY1F,GAC9B0B,SAASiE,cAAc3M,E,CAGzB,SAASkK,EACP0C,EACAC,EACAC,EAHFzN,GAKE,I,SADE8K,GAJJ9K,EAME,MAAMyK,EAASgD,EAAO9C,cAAcF,OAC9B9J,EAAQ,IAAI0M,YAAYE,EAAM,CAAEG,SAAS,EAAOC,YAAY,E,OAAMF,IACpED,GAAS/C,EAAOQ,iBAAiBsC,EAAMC,EAA0B,CAAEtC,MAAM,IAEzEJ,EF/ON,SAA4DL,EAAqB9J,GAC3E8J,IAAQmD,EAAAA,EAAAA,WAAmB,IAAMnD,EAAO6C,cAAc3M,G,CE+OxDkN,CAA4BpD,EAAQ9J,GAEpC8J,EAAO6C,cAAc3M,E,CEpVzB,IAAImN,EAAQ,EAWZ,SAASC,KACPC,EAAAA,EAAAA,WAAgB,KAAM,IAAAC,EAAAC,EACpB,MAAMC,EAAa9E,SAAS+E,iBAAiB,4BAK7C,OAJA/E,SAASoD,KAAK4B,sBAAsB,aAApC,QAAAJ,EAAkDE,EAAW,UAA7D,IAAAF,EAAAA,EAAmEK,KACnEjF,SAASoD,KAAK4B,sBAAsB,YAApC,QAAAH,EAAiDC,EAAW,UAA5D,IAAAD,EAAAA,EAAkEI,KAClER,IAEO,KACS,IAAVA,GACFzE,SAAS+E,iBAAiB,4BAA4B5K,QAASD,GAASA,EAAKgL,UAE/ET,MAED,G,CAGL,SAASQ,IACP,MAAME,EAAUnF,SAASoF,cAAc,QAIvC,OAHAD,EAAQE,aAAa,yBAA0B,IAC/CF,EAAQG,SAAW,EACnBH,EAAQ9B,MAAMkC,QAAU,mEACjBJ,C,CAGT,MC/BMK,EAAqB,8BACrBC,EAAuB,gCACvBC,EAAgB,CAAErB,SAAS,EAAOC,YAAY,GAwC9CqB,GAAaC,EAAAA,EAAAA,YAAqD,CAACpN,EAAOqC,KAC9E,MAAM,KACJgL,GAAO,EADH,QAEJC,GAAU,EACVC,iBAAkBC,EAClBC,mBAAoBC,KACjBC,GACD3N,GACG4N,EAAWC,IAAgBC,EAAAA,EAAAA,UAAmC,MAC/DP,EAAmBQ,EAAeP,GAClCC,EAAqBM,EAAeL,GACpCM,GAAwBC,EAAAA,EAAAA,QAAiC,MACzDvG,EAAewG,EAAgB7L,EAAeX,GAASmM,EAAanM,IAEpEyM,GAAaF,EAAAA,EAAAA,QAAa,CAC9BG,QAAQ,EACRC,KAAAA,GACEC,KAAKF,QAAS,C,EAEhBG,MAAAA,GACED,KAAKF,QAAS,C,IAEfvM,SAGH2M,EAAAA,EAAAA,WAAgB,KACd,GAAIlB,EAAS,CACX,SAASmB,EAAc3P,GACrB,GAAIqP,EAAWC,SAAWR,EAAW,OACrC,MAAMhF,EAAS9J,EAAM8J,OACjBgF,EAAU9D,SAASlB,GACrBoF,EAAsBnM,QAAU+G,EAEhC8F,EAAMV,EAAsBnM,QAAS,CAAE8M,QAAQ,G,CAInD,SAASC,EAAe9P,GACtB,GAAIqP,EAAWC,SAAWR,EAAW,OACrC,MAAMiB,EAAgB/P,EAAM+P,cAYN,OAAlBA,IAICjB,EAAU9D,SAAS+E,IACtBH,EAAMV,EAAsBnM,QAAS,CAAE8M,QAAQ,I,CAOnD,SAASG,EAAgBC,GACvB,MAAMC,EAAiBxH,SAASyH,cAChC,IAAK,MAAMC,KAAYH,EACjBG,EAASC,aAAavQ,OAAS,IAC7B,OAACgP,QAAD,IAACA,GAAAA,EAAW9D,SAASkF,IAAiBN,EAAMd,G,CAKtDpG,SAAS4B,iBAAiB,UAAWqF,GACrCjH,SAAS4B,iBAAiB,WAAYwF,GACtC,MAAMQ,EAAmB,IAAIC,iBAAiBP,GAG9C,OAFIlB,GAAWwB,EAAiBE,QAAQ1B,EAAW,CAAE2B,WAAW,EAAMC,SAAS,IAExE,KACLhI,SAAS2B,oBAAoB,UAAWsF,GACxCjH,SAAS2B,oBAAoB,WAAYyF,GACzCQ,EAAiBK,a,GAGpB,CAACnC,EAASM,EAAWO,EAAWC,UAEnCI,EAAAA,EAAAA,WAAgB,KACd,GAAIZ,EAAW,CACb8B,EAAiB3E,IAAIoD,GACrB,MAAMwB,EAA2BnI,SAASyH,cAG1C,IAF4BrB,EAAU9D,SAAS6F,GAErB,CACxB,MAAMC,EAAa,IAAIpE,YAAYwB,EAAoBE,GACvDU,EAAUxE,iBAAiB4D,EAAoBO,GAC/CK,EAAUnC,cAAcmE,GACnBA,EAAW7Q,oBA4ExB,SAAoB8Q,GAAoD,IAAzB,OAAElB,GAAS,GAAXhQ,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAqB,CAAC,EACnE,MAAMgR,EAA2BnI,SAASyH,cAC1C,IAAK,MAAMa,KAAaD,EAEtB,GADAnB,EAAMoB,EAAW,C,OAAEnB,IACfnH,SAASyH,gBAAkBU,EAA0B,M,CA/EnDI,EAsMWC,EAtMYC,EAAsBrC,GAuM9CoC,EAAM9L,OAAQgM,GAA0B,MAAjBA,EAAKC,UAvM+B,CAAExB,QAAQ,IAChEnH,SAASyH,gBAAkBU,GAC7BjB,EAAMd,G,CAKZ,MAAO,KACLA,EAAUzE,oBAAoB6D,EAAoBO,GAKlDhE,WAAW,KACT,MAAM6G,EAAe,IAAI5E,YAAYyB,EAAsBC,GAC3DU,EAAUxE,iBAAiB6D,EAAsBQ,GACjDG,EAAUnC,cAAc2E,GACnBA,EAAarR,kBAChB2P,EAAK,OAACiB,QAAD,IAACA,EAAAA,EAA4BnI,SAASoD,KAAM,CAAE+D,QAAQ,IAG7Df,EAAUzE,oBAAoB8D,EAAsBQ,GAEpDiC,EAAiBhD,OAAOyB,IACvB,G,CA8KX,IAAqB6B,GA3KhB,CAACpC,EAAWL,EAAkBE,EAAoBU,IAGrD,MAAM1D,GAAgB4F,EAAAA,EAAAA,aACnBvR,IACC,IAAKuO,IAASC,EAAS,OACvB,GAAIa,EAAWC,OAAQ,OAEvB,MAAMkC,EAAyB,QAAdxR,EAAMd,MAAkBc,EAAMyR,SAAWzR,EAAM0R,UAAY1R,EAAM2R,QAC5EzB,EAAiBxH,SAASyH,cAEhC,GAAIqB,GAAYtB,EAAgB,CAC9B,MAAM0B,EAAY5R,EAAM6R,eACjBC,EAAOC,GA8CtB,SAA0BH,GACxB,MAAMb,EAAaI,EAAsBS,GACnCE,EAAQE,EAAYjB,EAAYa,GAChCG,EAAOC,EAAYjB,EAAWkB,UAAWL,GAC/C,MAAO,CAACE,EAAOC,E,CAlDaG,CAAiBN,GACLE,GAASC,EAMpC/R,EAAMmS,UAAYjC,IAAmB6B,EAG/B/R,EAAMmS,UAAYjC,IAAmB4B,IAC9C9R,EAAM6L,iBACF0C,GAAMqB,EAAMmC,EAAM,CAAElC,QAAQ,MAJhC7P,EAAM6L,iBACF0C,GAAMqB,EAAMkC,EAAO,CAAEjC,QAAQ,KAJ/BK,IAAmB0B,GAAW5R,EAAM6L,gB,GAY9C,CAAC0C,EAAMC,EAASa,EAAWC,SAG7B,OACE8C,EAAAA,EAAAA,eAACC,EAAU9F,KADb+F,EAAAA,EAAAA,GAAA,CACiBtE,UAAW,GAAOa,EAAjC,CAA6C/L,IAAK8F,EAAc2J,UAAW5G,OA0C/E,SAASwF,EAAsBS,GAC7B,MAAMY,EAAuB,GACvBC,EAAS/J,SAASgK,iBAAiBd,EAAWe,WAAWC,aAAc,CAC3EC,WAAajQ,IACX,MAAMkQ,EAAiC,UAAjBlQ,EAAKyO,SAAqC,WAAdzO,EAAKiC,KACvD,OAAIjC,EAAKmQ,UAAYnQ,EAAKoQ,QAAUF,EAAsBH,WAAWM,YAI9DrQ,EAAKoL,UAAY,EAAI2E,WAAWO,cAAgBP,WAAWM,eAGtE,KAAOR,EAAOU,YAAYX,EAAMY,KAAKX,EAAOY,aAG5C,OAAOb,C,CAOT,SAASR,EAAYsB,EAAyB1B,GAC5C,IAAK,MAAM/D,KAAWyF,EAEpB,IAAKC,EAAS1F,EAAS,CAAE2F,KAAM5B,IAAc,OAAO/D,C,CAIxD,SAAS0F,EAAS3Q,EAAlBvD,GAAuE,I,KAAhCmU,GAAvCnU,EACE,GAA0C,WAAtCoU,iBAAiB7Q,GAAM8Q,WAAyB,OAAO,EAC3D,KAAO9Q,GAAM,CAEX,QAAa7C,IAATyT,GAAsB5Q,IAAS4Q,EAAM,OAAO,EAChD,GAAuC,SAAnCC,iBAAiB7Q,GAAM+Q,QAAoB,OAAO,EACtD/Q,EAAOA,EAAKgR,a,CAEd,OAAO,C,CAOT,SAAShE,EAAM/B,GAA2D,IAAzB,OAAEgC,GAAS,GAAXhQ,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAqB,CAAC,EAErE,GAAIgO,GAAWA,EAAQgG,MAAO,CAC5B,MAAMhD,EAA2BnI,SAASyH,cAE1CtC,EAAQgG,MAAM,CAAEC,eAAe,IAE3BjG,IAAYgD,GAXpB,SAA2BhD,GACzB,OAAOA,aAAmBkG,kBAAoB,WAAYlG,C,CAUZmG,CAAkBnG,IAAYgC,GACxEhC,EAAQgC,Q,EASd,MAAMe,EAEN,WAEE,IAAIqD,EAAyB,GAE7B,MAAO,CACLhI,GAAAA,CAAIoD,GAEF,MAAM6E,EAAmBD,EAAM,GAC3B5E,IAAe6E,IACD,OAAhBA,QAAgB,IAAhBA,GAAAA,EAAkB3E,SAGpB0E,EAAQE,EAAYF,EAAO5E,GAC3B4E,EAAMG,QAAQ/E,E,EAGhBzB,MAAAA,CAAOyB,GAA2B,IAAAgF,EAChCJ,EAAQE,EAAYF,EAAO5E,GAC3B,QAAAgF,EAAAJ,EAAM,UAAN,IAAAI,GAAAA,EAAU5E,Q,GApBS6E,GAyBzB,SAASH,EAAeI,EAAYnD,GAClC,MAAMoD,EAAe,IAAID,GACnBvT,EAAQwT,EAAatL,QAAQkI,GAInC,OAHe,IAAXpQ,GACFwT,EAAaC,OAAOzT,EAAO,GAEtBwT,C,CAOT,MClVME,EAAkBrP,QAAO,OAACoD,iBAAD,IAACA,gBAAD,EAACA,WAAYC,UAAYiM,EAAAA,gBAAwB,OCL1EC,EAAcC,EAAc,QAAQC,aAAvB,SACnB,IAAIC,EAAQ,EAEZ,SAASC,EAAMC,GACb,MAAOC,EAAIC,GAASN,EAAAA,SAAmCD,KAKvD,OAHAQ,EAAgB,KACTH,GAAiBE,EAAOE,GAAD,OAAaA,QAAb,IAAaA,EAAAA,EAAWC,OAAOP,OAC1D,CAACE,IACGA,IAAoBC,EAAM,SAAQA,IAAO,G,yBCYlD,MAUMK,EAAc,UAGbC,EAAqBC,GAAqBC,EAAmBH,IAM7DI,EAAgBC,GAAoBJ,EAAwCD,GAK7EM,EAAiC3U,IACrC,M,cAAQ4U,E,SAAe3U,GAAaD,GAC7B6U,EAAQC,IAAaC,EAAAA,EAAAA,UAAkC,MAC9D,OACEC,EAAAA,EAAAA,eAACP,EADH,CACkBhV,MAAOmV,EAAeC,OAAQA,EAAQI,eAAgBH,GACnE7U,IAWDiV,GAAc,eAQdC,IAAeC,EAAAA,EAAAA,YACnB,CAACpV,EAAuCqC,KACtC,M,cAAQuS,E,WAAeS,KAAeC,GAAgBtV,EAChDE,EAAUwU,EAAiBQ,GAAaN,GACxChT,GAAM2T,EAAAA,EAAAA,QAAkC,MACxC7N,EAAe8N,EAAgBnT,EAAcT,GASnD,OAPA6T,EAAAA,EAAAA,WAAgB,KAIdvV,EAAQ+U,gBAAyB,OAAVI,QAAU,IAAVA,OAAA,EAAAA,EAAYxT,UAAWD,EAAIC,WAG7CwT,EAAa,MAAOL,EAAAA,EAAAA,eAACU,EAAUrK,KAAXsK,EAAAA,EAAAA,GAAA,GAAmBL,EAA9C,CAA2D1T,IAAK8F,OAU9DkO,GAAe,iBAUdC,GAAuBC,IAC5BxB,EAA+CsB,IAmB3CG,IAAgBX,EAAAA,EAAAA,YACpB,CAACpV,EAAwCqC,KAAiB,IAAA2T,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EACxD,M,cACE3B,EADI,KAEJ4B,EAAO,SAFH,WAGJC,EAAa,EAHT,MAIJC,EAAQ,SAJJ,YAKJC,EAAc,EALV,aAMJC,EAAe,EANX,kBAOJC,EAAoB,GACpBC,iBAAkBC,EAAuB,EARrC,OASJC,EAAS,UATL,iBAUJC,GAAmB,EAVf,gBAWJC,GAAkB,E,SAClBC,KACGC,GACDpX,EAEEE,EAAUwU,EAAiBkB,GAAchB,IAExCyC,EAASC,IAAcvC,EAAAA,EAAAA,UAAsC,MAC9DrN,EAAe8N,EAAgBnT,EAAeX,GAAS4V,EAAW5V,KAEjE6V,EAAOC,IAAYzC,EAAAA,EAAAA,UAAuC,MAC3D0C,EClJV,SAAiB9K,GACf,MAAOzE,EAAMwP,IAAWC,EAAAA,EAAAA,eAA8D9Y,GAgDtF,OA9CA+Y,EAAgB,KACd,GAAIjL,EAAS,CAEX+K,EAAQ,CAAEG,MAAOlL,EAAQmL,YAAaC,OAAQpL,EAAQqL,eAEtD,MAAMC,EAAiB,IAAIC,eAAgBC,IACzC,IAAKrX,MAAMsX,QAAQD,GACjB,OAKF,IAAKA,EAAQvZ,OACX,OAGF,MAAMyZ,EAAQF,EAAQ,GACtB,IAAIN,EACAE,EAEJ,GAAI,kBAAmBM,EAAO,CAC5B,MAAMC,EAAkBD,EAAK,cAEvBE,EAAazX,MAAMsX,QAAQE,GAAmBA,EAAgB,GAAKA,EACzET,EAAQU,EAAU,WAClBR,EAASQ,EAAU,S,MAInBV,EAAQlL,EAAQmL,YAChBC,EAASpL,EAAQqL,aAGnBN,EAAQ,CAzChBG,MAyCkBA,EAzClBE,OAyCyBA,MAKnB,OAFAE,EAAe3I,QAAQ3C,EAAS,CAAE6L,IAAK,eAEhC,IAAMP,EAAeQ,UAAU9L,E,CAItC+K,OAAQ7Y,IAET,CAAC8N,IAEGzE,C,CDiGawQ,CAAQnB,GACpBoB,EAAU,QAAA3C,EAAA,OAAGyB,QAAH,IAAGA,OAAH,EAAGA,EAAWI,aAAd,IAAA7B,EAAAA,EAAuB,EACjC4C,EAAW,QAAA3C,EAAA,OAAGwB,QAAH,IAAGA,OAAH,EAAGA,EAAWM,cAAd,IAAA9B,EAAAA,EAAwB,EAEnC4C,EAAoBrC,GAAkB,WAAVE,EAAqB,IAAMA,EAAQ,IAE/DI,EAC4B,kBAAzBC,EACHA,EACA,CAAE+B,IAAK,EAAGC,MAAO,EAAGC,OAAQ,EAAGC,KAAM,KAAMlC,GAE3CmC,EAAWpY,MAAMsX,QAAQvB,GAAqBA,EAAoB,CAACA,GACnEsC,EAAwBD,EAASta,OAAS,EAE1Cwa,EAAwB,CAC5BC,QAASvC,EACToC,SAAUA,EAAShV,OAAOoV,IAE1BC,YAAaJ,I,KAGP1X,E,eAAM+X,E,UAAgBC,E,aAAWC,E,eAAcC,IAAmBC,EAAAA,EAAAA,IAAY,CAEpFC,SAAU,QACVJ,UAAWZ,EACXiB,qBAAsBC,EAAAA,GACtB3H,SAAU,CACR4H,UAAW9Z,EAAQ2U,QAErBoF,WAAY,EACVC,EAAAA,EAAAA,IAAO,CAAEC,SAAU1D,EAAamC,EAAawB,cAAezD,IAC5DO,IACEmD,EAAAA,EAAAA,IAAM,CACJF,UAAU,EACVG,WAAW,EACXC,QAAoB,YAAXvD,GAAuBwD,EAAAA,EAAAA,WAAe3b,KAC5Cua,IAEPlC,IAAmBuD,EAAAA,EAAAA,IAAK,IAAKrB,KAC7BsB,EAAAA,EAAAA,IAAK,IACAtB,EACHuB,MAAOxc,IAA0D,I,SAAvDiU,E,MAAUwI,E,eAAOC,E,gBAAgBC,GAApC3c,EACL,MAAQ0Z,MAAOkD,EAAahD,OAAQiD,GAAiBJ,EAAMZ,UACrDiB,EAAe7I,EAAS8I,SAASrQ,MACvCoQ,EAAaE,YAAY,iCAAmC,GAAEN,OAC9DI,EAAaE,YAAY,kCAAoC,GAAEL,OAC/DG,EAAaE,YAAY,8BAAgC,GAAEJ,OAC3DE,EAAaE,YAAY,+BAAiC,GAAEH,UAGhEzD,IAAS6D,EAAAA,EAAAA,IAAgB,CAAEzO,QAAS4K,EAAO8B,QAASzC,IACpDyE,GAAgB,C,WAAE1C,E,YAAYC,IAC9B3B,IAAoBqE,EAAAA,EAAAA,IAAK,CAAEzB,SAAU,wBAIlC0B,EAAYC,GAAeC,GAA6BhC,GAEzDiC,EAAeC,EAAexE,GACpCyE,EAAgB,KACVlC,IACU,OAAZgC,QAAY,IAAZA,GAAAA,MAED,CAAChC,EAAcgC,IAElB,MAAMG,GAAM,QAAA3F,EAAGyD,EAAepC,aAAlB,IAAArB,OAAA,EAAGA,EAAsB4F,EAC/BC,GAAM,QAAA5F,EAAGwD,EAAepC,aAAlB,IAAApB,OAAA,EAAGA,EAAsB6F,EAC/BC,GAA2D,KAAvC,QAAA7F,EAAAuD,EAAepC,aAAf,IAAAnB,OAAA,EAAAA,EAAsB8F,eAEzCC,GAAeC,KAAoBrH,EAAAA,EAAAA,YAK1C,OAJA6G,EAAgB,KACVvE,GAAS+E,GAAiBpX,OAAOuN,iBAAiB8E,GAASgF,SAC9D,CAAChF,KAGFrC,EAAAA,EAAAA,eADF,OAEIpT,IAAKH,EAAK6a,YACV,oCAAkC,GAClCzR,MAAO,IACF2O,EACH+C,UAAW7C,EAAeF,EAAe+C,UAAY,sBACrDC,SAAU,cACVH,OAAQF,GACR,kCAA4C,SAAA9F,EAC1CsD,EAAe8C,uBAD2B,IAAApG,OAAA,EAC1CA,EAAgCyF,EADU,QAAAxF,EAE1CqD,EAAe8C,uBAF2B,IAAAnG,OAAA,EAE1CA,EAAgC0F,GAChC5X,KAAK,MAKTsY,IAAK1c,EAAM0c,MAEX1H,EAAAA,EAAAA,eAACa,GAlBH,CAmBIpW,MAAOmV,EACP2G,WAAYA,EACZoB,cAAenF,EACfqE,OAAQA,GACRE,OAAQA,GACRa,gBAAiBX,KAEjBjH,EAAAA,EAAAA,eAACU,EAAUrK,KARbsK,EAAAA,EAAAA,GAAA,CASI,YAAW4F,EACX,aAAYC,GACRpE,EAHN,CAIExV,IAAK8F,EACLmD,MAAO,IACFuM,EAAavM,MAGhBgS,UAAYnD,OAAwB7a,EAAT,OAE3Bie,QAAS,QAAAvG,EAAAoD,EAAeoD,YAAf,IAAAxG,GAAAA,EAAqByG,gBAAkB,OAAIne,UA+ElE,SAASya,GAAapb,GACpB,OAAiB,OAAVA,C,CAGT,MAAMmd,GAAmB4B,IAAD,CACtBvR,KAAM,kB,QACNuR,EACAC,EAAAA,CAAGC,GAAM,IAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EACP,M,UAAQ/D,E,MAAWmB,E,eAAOjB,GAAmBwD,EAGvCM,EAD2D,KAAvC,QAAAL,EAAAzD,EAAepC,aAAf,IAAA6F,OAAA,EAAAA,EAAsBlB,cAE1CvD,EAAa8E,EAAgB,EAAIR,EAAQtE,WACzCC,EAAc6E,EAAgB,EAAIR,EAAQrE,aAEzC2C,EAAYC,GAAeC,GAA6BhC,GACzDiE,EAAe,CAAEC,MAAO,KAAMC,OAAQ,MAAOC,IAAK,QAASrC,GAE3DsC,GAAe,QAAAT,EAAA,QAAAC,EAAC3D,EAAepC,aAAhB,IAAA+F,OAAA,EAACA,EAAsBxB,SAAvB,IAAAuB,EAAAA,EAA4B,GAAK1E,EAAa,EAC7DoF,GAAe,QAAAR,EAAA,QAAAC,EAAC7D,EAAepC,aAAhB,IAAAiG,OAAA,EAACA,EAAsBxB,SAAvB,IAAAuB,EAAAA,EAA4B,GAAK3E,EAAc,EAEpE,IAAIkD,EAAI,GACJE,EAAI,GAeR,MAbmB,WAAfT,GACFO,EAAI2B,EAAgBC,EAAgB,GAAEI,MACtC9B,GAAQpD,EAAH,MACmB,QAAf2C,GACTO,EAAI2B,EAAgBC,EAAgB,GAAEI,MACtC9B,EAAK,GAAEpB,EAAMM,SAASnD,OAASa,OACP,UAAf2C,GACTO,GAAQlD,EAAH,KACLoD,EAAIyB,EAAgBC,EAAgB,GAAEK,OACd,SAAfxC,IACTO,EAAK,GAAElB,EAAMM,SAASrD,MAAQe,MAC9BoD,EAAIyB,EAAgBC,EAAgB,GAAEK,OAEjC,CAAEZ,KAAM,C,EAAErB,E,EAAGE,G,IAIxB,SAASP,GAA6BhC,GACpC,MAAOjD,EAAME,EAAQ,UAAY+C,EAAUuE,MAAM,KACjD,MAAO,CAACxH,EAAcE,E,CAGxB,MAAMuH,GAAOtJ,EACPuJ,GAAS/I,GACTgJ,GAAUpI,GEpXVqI,IAASC,EAAAA,EAAAA,YAA6C,CAACre,EAAOqC,KAAiB,IAAAic,EACnF,MAAM,UAAE5N,GAAS,OAAGnJ,iBAAH,IAAGA,YAAH,QAAA+W,EAAG/W,WAAYC,gBAAf,IAAA8W,OAAA,EAAGA,EAAsB1T,SAAS2T,GAAgBve,EACnE,OAAO0Q,EACH8N,EAAAA,cAAsBC,EAAAA,EAAAA,eAACC,EAAUrT,KAAXsT,EAAAA,EAAAA,GAAA,GAAmBJ,EAD7C,CAC0D3c,IAAKS,KAAkBqO,GAC7E,OCXN,SAASkO,GAATzgB,GAIkC,IAflC0gB,KAYEA,EAZFC,YAaEA,EAF+B,SAG/BC,EAAWA,QAHb5gB,EAKE,MAAO6gB,EAAkBC,GAqB3B,SAAAC,GAGgD,IAxChDJ,YAsCEA,EAtCFC,SAuCEA,GAFFG,EAIE,MAAMC,GAAoBC,EAAAA,EAAAA,UAA8BN,IACjD5gB,GAASihB,EACVE,GAAeC,EAAAA,EAAAA,QAAaphB,GAC5BqhB,EAAeC,EAAeT,GASpC,OAPAU,EAAAA,EAAAA,WAAgB,KACVJ,EAAaxd,UAAY3D,IAC3BqhB,EAAarhB,GACbmhB,EAAaxd,QAAU3D,IAExB,CAACA,EAAOmhB,EAAcE,IAElBJ,C,CArCyCO,CAAqB,CAhBvEZ,YAgByEA,EAhBzEC,SAgBsFA,IAC9EY,OAAwB9gB,IAATggB,EACfe,EAAQD,EAAed,EAAOG,EAC9BO,EAAeC,EAAeT,GAepC,MAAO,CAACa,GAb8DC,EAAAA,EAAAA,aACnEC,IACC,GAAIH,EAAc,CAChB,MACMzhB,EAA6B,oBAAd4hB,EADNA,EACwCjB,GAAQiB,EAC3D5hB,IAAU2gB,GAAMU,EAAarhB,E,MAEjC+gB,EAAoBa,IAGxB,CAACH,EAAcd,EAAMI,EAAqBM,I,CCX9C,SAAS/d,KAA0C,QAAAZ,EAAAjC,UAAAC,OAAxB6C,EAA3B,IAAAX,MAAAF,GAAAG,EAAA,EAAAA,EAAAH,EAAAG,IAA2BU,EAA3BV,GAAApC,UAAAoC,GACE,OAAQW,GAAYD,EAAKE,QAASC,GAbpC,SAAmBA,EAAqB1D,GACnB,oBAAR0D,EACTA,EAAI1D,GACa,OAAR0D,QAAwB/C,IAAR+C,IACxBA,EAAkCC,QAAU3D,E,CASL4D,CAAOF,EAAKF,G,CCVxD,MAAMS,IAAOC,EAAAA,EAAAA,YAAyC,CAACpC,EAAOqC,KAC5D,M,SAAQpC,KAAaqC,GAActC,EAC7BuC,EAAgBC,EAAAA,SAAeC,QAAQxC,GACvCyC,EAAYH,EAAcI,KAAKC,IAErC,GAAIF,EAAW,CAEb,MAAMG,EAAaH,EAAU1C,MAAMC,SAE7B6C,EAAcP,EAAcjD,IAAKyD,GACjCA,IAAUL,EAGRF,EAAAA,SAAeQ,MAAMH,GAAc,EAAUL,EAAAA,SAAeS,KAAK,OAC9DC,EAAAA,EAAAA,gBAAqBL,GACvBA,EAAW7C,MAAMC,SAClB,KAEG8C,GAIX,OACEI,EAAAA,EAAAA,eAACC,IAADC,EAAAA,EAAAA,GAAA,GAAef,EADjB,CAC4BV,IAAKS,KAC5Ba,EAAAA,EAAAA,gBAAqBL,IAClBS,EAAAA,EAAAA,cAAmBT,OAAYhE,EAAWiE,GAC1C,K,CAKV,OACEK,EAAAA,EAAAA,eAACC,IAADC,EAAAA,EAAAA,GAAA,GAAef,EADjB,CAC4BV,IAAKS,IAC5BpC,KAKPkC,GAAK5B,YAAc,OAUnB,MAAM6C,IAAYhB,EAAAA,EAAAA,YAAsC,CAACpC,EAAOqC,KAC9D,M,SAAQpC,KAAaqC,GAActC,EAEnC,OAAIkD,EAAAA,EAAAA,gBAAqBjD,IAChBqD,EAAAA,EAAAA,cAAmBrD,EAAU,IAC/BsD,GAAWjB,EAAWrC,EAASD,OAClC4B,IAAKS,EAAemB,GAAYnB,EAAepC,EAAiB2B,KAAQ3B,EAAiB2B,MAItFY,EAAAA,SAAeQ,MAAM/C,GAAY,EAAIuC,EAAAA,SAAeS,KAAK,MAAQ,OAG1EG,GAAU7C,YAAc,YAMxB,MAAMkD,GAAYtF,IAAiD,I,SAA9C8B,GAAH9B,EAChB,OAAOgF,EAAAA,EAAAA,eAAAO,EAAAA,SAAA,KAAGzD,IAOZ,SAAS2C,GAAYG,GACnB,OAAOG,EAAAA,EAAAA,gBAAqBH,IAAUA,EAAMY,OAASF,E,CAGvD,SAASF,GAAWjB,EAAqBsB,GAEvC,MAAMC,EAAgB,IAAKD,GAE3B,IAAK,MAAME,KAAYF,EAAY,CACjC,MAAMG,EAAgBzB,EAAUwB,GAC1BE,EAAiBJ,EAAWE,GAEhB,WAAWG,KAAKH,GAG5BC,GAAiBC,EACnBH,EAAcC,GAAY,WACxBE,KAAcrF,WACdoF,KAAapF,U,EAIRoF,IACPF,EAAcC,GAAYC,GAIR,UAAbD,EACPD,EAAcC,GAAY,IAAKC,KAAkBC,GAC3B,cAAbF,IACTD,EAAcC,GAAY,CAACC,EAAeC,GAAgBE,OAAOC,SAASC,KAAK,K,CAInF,MAAO,IAAK9B,KAAcuB,E,CAG5B,MChFMU,GAvCQ,CACZ,IACA,SACA,MACA,OACA,KACA,KACA,MACA,QACA,QACA,KACA,MACA,KACA,IACA,OACA,MACA,MAuBsBjD,OAAO,CAACkD,EAAW9C,KACzC,MAAM+C,GAAOC,EAAAA,EAAAA,YAAiB,CAAC1E,EAA2CqC,KACxE,M,QAAQsC,KAAYC,GAAmB5E,EACjC6E,EAAYF,EAAUG,GAAOpD,EAMnC,OAJAqD,EAAAA,EAAAA,WAAgB,KACbC,OAAeC,OAAOC,IAAI,cAAe,GACzC,KAEIC,EAAAA,EAAAA,eAACN,GAADO,EAAAA,EAAAA,GAAA,GAAUR,EAAjB,CAAiChD,IAAKS,OAKxC,OAFAoC,EAAKlE,YAAe,aAAYmB,IAEzB,IAAK8C,EAAW,CAAC9C,GAAO+C,IAC9B,CAAC,GAiDJ,MC5FMsb,IAAiBC,EAAAA,EAAAA,YACrB,CAAChgB,EAAOqC,KAEJ4d,EAAAA,EAAAA,eAACC,GAAUC,MAAXC,EAAAA,EAAAA,GAAA,GACMpgB,EAFR,CAGI4B,IAAKS,EACLwI,MAAO,CAELwV,SAAU,WACVC,OAAQ,EACRzI,MAAO,EACPE,OAAQ,EACRsB,QAAS,EACTkH,QAAS,EACTC,SAAU,SACVC,KAAM,mBACNC,WAAY,SACZC,SAAU,YACP3gB,EAAM6K,W,gDChCR+V,IAAYC,E,QAAAA,KCInBC,GAAU,WAEd,EAIIC,GAAeC,EAAAA,WAAiB,SAAUhhB,EAAOihB,GACjD,IAAIrf,EAAMof,EAAAA,OAAa,MACnBE,EAAKF,EAAAA,SAAe,CACpBG,gBAAiBL,GACjBM,eAAgBN,GAChBO,mBAAoBP,KACpBQ,EAAYJ,EAAG,GAAIK,EAAeL,EAAG,GACrCM,EAAexhB,EAAMwhB,aAAcvhB,EAAWD,EAAMC,SAAUwhB,EAAYzhB,EAAMyhB,UAAWC,EAAkB1hB,EAAM0hB,gBAAiBC,EAAU3hB,EAAM2hB,QAASC,EAAS5hB,EAAM4hB,OAAQC,EAAU7hB,EAAM6hB,QAASC,EAAc9hB,EAAM8hB,YAAaC,EAAQ/hB,EAAM+hB,MAAOC,EAAiBhiB,EAAMgiB,eAAgBC,EAAKjiB,EAAMkiB,GAAIC,OAAmB,IAAPF,EAAgB,MAAQA,EAAIG,GAAOC,EAAAA,GAAAA,IAAOriB,EAAO,CAAC,eAAgB,WAAY,YAAa,kBAAmB,UAAW,SAAU,UAAW,cAAe,QAAS,iBAAkB,OAC7fsiB,EAAUT,EACVU,GAAeC,EAAAA,GAAAA,GAAa,CAAC5gB,EAAKqf,IAClCwB,GAAiBC,EAAAA,GAAAA,KAASA,EAAAA,GAAAA,IAAS,CAAC,EAAGN,GAAOd,GAClD,OAAQN,EAAAA,cAAoBA,EAAAA,SAAgB,KACxCW,GAAYX,EAAAA,cAAoBsB,EAAS,CAAET,QAASjB,GAAWc,gBAAiBA,EAAiBE,OAAQA,EAAQE,YAAaA,EAAaC,MAAOA,EAAOR,aAAcA,EAAcS,iBAAkBA,EAAgBW,QAAS/gB,IAChO4f,EAAgBR,EAAAA,aAAmBA,EAAAA,SAAe/d,KAAKhD,IAAWyiB,EAAAA,GAAAA,KAASA,EAAAA,GAAAA,IAAS,CAAC,EAAGD,GAAiB,CAAE7gB,IAAK2gB,KAAqBvB,EAAAA,cAAoBmB,GAAWO,EAAAA,GAAAA,IAAS,CAAC,EAAGD,EAAgB,CAAEhB,UAAWA,EAAW7f,IAAK2gB,IAAiBtiB,GACvP,GACA8gB,GAAa6B,aAAe,CACxBjB,SAAS,EACTD,iBAAiB,EACjBK,OAAO,GAEXhB,GAAa8B,WAAa,CACtBC,UAAWC,GAAAA,GACXC,UAAWC,GAAAA,I,qCCjCXC,IAAmB,EACvB,GAAsB,qBAAXle,OACP,IACI,IAAIiY,GAAU7c,OAAO+iB,eAAe,CAAC,EAAG,UAAW,CAC/CC,IAAK,WAED,OADAF,IAAmB,GACZ,CACX,IAGJle,OAAOoE,iBAAiB,OAAQ6T,GAASA,IAEzCjY,OAAOmE,oBAAoB,OAAQ8T,GAASA,GAChD,CACA,MAAOoG,IACHH,IAAmB,CACvB,CAEG,IAAII,KAAaJ,IAAmB,CAAEK,SAAS,GCdlDC,GAAuB,SAAU9hB,EAAM8e,GACvC,IAAIiD,EAASze,OAAOuN,iBAAiB7Q,GACrC,MAEqB,WAArB+hB,EAAOjD,MAEDiD,EAAOC,YAAcD,EAAOE,YAVX,SAAUjiB,GAEjC,MAAwB,aAAjBA,EAAKyO,OAChB,CAOoDyT,CAAqBliB,IAA8B,YAArB+hB,EAAOjD,GACzF,EAGWqD,GAA0B,SAAUC,EAAMpiB,GACjD,IAAIG,EAAUH,EACd,EAAG,CAMC,GAJ0B,qBAAfqiB,YAA8BliB,aAAmBkiB,aACxDliB,EAAUA,EAAQmiB,MAEHC,GAAuBH,EAAMjiB,GAC9B,CACd,IAAIqf,EAAKgD,GAAmBJ,EAAMjiB,GAClC,GADgDqf,EAAG,GAAQA,EAAG,GAE1D,OAAO,CAEf,CACArf,EAAUA,EAAQsiB,UACtB,OAAStiB,GAAWA,IAAY2F,SAASoD,MACzC,OAAO,CACX,EAiBIqZ,GAAyB,SAAUH,EAAMpiB,GACzC,MAAgB,MAAToiB,EArCmB,SAAUpiB,GAAQ,OAAO8hB,GAAqB9hB,EAAM,YAAc,CAqCtE0iB,CAAwB1iB,GApCpB,SAAUA,GAAQ,OAAO8hB,GAAqB9hB,EAAM,YAAc,CAoCtC2iB,CAAwB3iB,EAClF,EACIwiB,GAAqB,SAAUJ,EAAMpiB,GACrC,MAAgB,MAAToiB,EAlBA,EAFyB5C,EAoBUxf,GAnBvB4iB,UAA0BpD,EAAGqD,aAA6BrD,EAAGsD,cAO1D,SAAUtD,GAEhC,MAAO,CADUA,EAAGuD,WAA0BvD,EAAGwD,YAA2BxD,EAAGyD,YAMnF,CAKsDC,CAAoBljB,GApBhD,IAAUwf,CAqBpC,EC/CW2D,GAAa,SAAU/lB,GAC9B,MAAO,mBAAoBA,EAAQ,CAACA,EAAMgmB,eAAe,GAAGC,QAASjmB,EAAMgmB,eAAe,GAAGE,SAAW,CAAC,EAAG,EAChH,EACWC,GAAa,SAAUnmB,GAAS,MAAO,CAACA,EAAMomB,OAAQpmB,EAAMqmB,OAAS,EAC5EC,GAAa,SAAUxjB,GACvB,OAAOA,GAAO,YAAaA,EAAMA,EAAIC,QAAUD,CACnD,EAEIyjB,GAAgB,SAAUrR,GAAM,MAAO,4BAA4BsR,OAAOtR,EAAI,qDAAqDsR,OAAOtR,EAAI,4BAA8B,EAC5KuR,GAAY,EACZC,GAAY,GCbhB,UAAeC,EAAAA,GAAAA,GAAc7E,GDctB,SAA6B5gB,GAChC,IAAI0lB,EAAqB1E,EAAAA,OAAa,IAClC2E,EAAgB3E,EAAAA,OAAa,CAAC,EAAG,IACjC4E,EAAa5E,EAAAA,SACbhN,EAAKgN,EAAAA,SAAeuE,MAAa,GACjCM,EAAQ7E,EAAAA,SAAe,WAAc,OAAO8E,EAAAA,GAAAA,KAAkB,GAAG,GACjEC,EAAY/E,EAAAA,OAAahhB,GAC7BghB,EAAAA,UAAgB,WACZ+E,EAAUlkB,QAAU7B,CACxB,EAAG,CAACA,IACJghB,EAAAA,UAAgB,WACZ,GAAIhhB,EAAM+hB,MAAO,CACbva,SAASoD,KAAKob,UAAUjb,IAAI,uBAAuBua,OAAOtR,IAC1D,IAAIiS,GAAUC,EAAAA,GAAAA,IAAc,CAAClmB,EAAM2iB,QAAQ9gB,UAAW7B,EAAM4hB,QAAU,IAAItiB,IAAI8lB,KAAa,GAAMlhB,OAAOC,SAExG,OADA8hB,EAAQtkB,QAAQ,SAAUwkB,GAAM,OAAOA,EAAGH,UAAUjb,IAAI,uBAAuBua,OAAOtR,GAAM,GACrF,WACHxM,SAASoD,KAAKob,UAAUtZ,OAAO,uBAAuB4Y,OAAOtR,IAC7DiS,EAAQtkB,QAAQ,SAAUwkB,GAAM,OAAOA,EAAGH,UAAUtZ,OAAO,uBAAuB4Y,OAAOtR,GAAM,EACnG,CACJ,CAEJ,EAAG,CAAChU,EAAM+hB,MAAO/hB,EAAM2iB,QAAQ9gB,QAAS7B,EAAM4hB,SAC9C,IAAIwE,EAAoBpF,EAAAA,YAAkB,SAAUliB,EAAOunB,GACvD,GAAI,YAAavnB,GAAkC,IAAzBA,EAAMwnB,QAAQ1nB,OACpC,OAAQmnB,EAAUlkB,QAAQmgB,eAE9B,IAIIuE,EAJAC,EAAQ3B,GAAW/lB,GACnB2nB,EAAad,EAAc9jB,QAC3BqjB,EAAS,WAAYpmB,EAAQA,EAAMomB,OAASuB,EAAW,GAAKD,EAAM,GAClErB,EAAS,WAAYrmB,EAAQA,EAAMqmB,OAASsB,EAAW,GAAKD,EAAM,GAElE5d,EAAS9J,EAAM8J,OACf8d,EAAgBpoB,KAAKqoB,IAAIzB,GAAU5mB,KAAKqoB,IAAIxB,GAAU,IAAM,IAEhE,GAAI,YAAarmB,GAA2B,MAAlB4nB,GAAyC,UAAhB9d,EAAOjF,KACtD,OAAO,EAEX,IAAIijB,EAA+B/C,GAAwB6C,EAAe9d,GAC1E,IAAKge,EACD,OAAO,EAUX,GARIA,EACAL,EAAcG,GAGdH,EAAgC,MAAlBG,EAAwB,IAAM,IAC5CE,EAA+B/C,GAAwB6C,EAAe9d,KAGrEge,EACD,OAAO,EAKX,IAHKhB,EAAW/jB,SAAW,mBAAoB/C,IAAUomB,GAAUC,KAC/DS,EAAW/jB,QAAU0kB,IAEpBA,EACD,OAAO,EAEX,IAAIM,EAAgBjB,EAAW/jB,SAAW0kB,EAC1C,ODdkB,SAAUzC,EAAMgD,EAAWhoB,EAAOioB,EAAaC,GACrE,IAAIC,EATiB,SAAUnD,EAAMoD,GAMrC,MAAgB,MAATpD,GAA8B,QAAdoD,GAAuB,EAAI,CACtD,CAE0BC,CAAmBrD,EAAM9e,OAAOuN,iBAAiBuU,GAAWI,WAC9EE,EAAQH,EAAkBF,EAE1Bne,EAAS9J,EAAM8J,OACfye,EAAeP,EAAUhd,SAASlB,GAClC0e,GAAqB,EACrBC,EAAkBH,EAAQ,EAC1BI,EAAkB,EAClBC,EAAqB,EACzB,EAAG,CACC,IAAIvG,EAAKgD,GAAmBJ,EAAMlb,GAASyX,EAAWa,EAAG,GACrDwG,EADoExG,EAAG,GAAeA,EAAG,GACnD+F,EAAkB5G,GACxDA,GAAYqH,IACRzD,GAAuBH,EAAMlb,KAC7B4e,GAAmBE,EACnBD,GAAsBpH,GAG9BzX,EAASA,EAAOub,UACpB,QAEEkD,GAAgBze,IAAWpB,SAASoD,MAEjCyc,IAAiBP,EAAUhd,SAASlB,IAAWke,IAAcle,IAQlE,OAPI2e,IAAqBP,GAAoC,IAApBQ,IAA4BR,GAAgBI,EAAQI,KAGnFD,IACJP,GAAuC,IAAvBS,IAA+BT,IAAiBI,EAAQK,MAH1EH,GAAqB,GAMlBA,CACX,CCnBeK,CAAad,EAAeR,EAAQvnB,EAAyB,MAAlB+nB,EAAwB3B,EAASC,GAAQ,EAC/F,EAAG,IACCyC,EAAgB5G,EAAAA,YAAkB,SAAU6G,GAC5C,IAAI/oB,EAAQ+oB,EACZ,GAAKrC,GAAU5mB,QAAU4mB,GAAUA,GAAU5mB,OAAS,KAAOinB,EAA7D,CAIA,IAAIuB,EAAQ,WAAYtoB,EAAQmmB,GAAWnmB,GAAS+lB,GAAW/lB,GAC3DgpB,EAAcpC,EAAmB7jB,QAAQqC,OAAO,SAAU6jB,GAAK,OAAOA,EAAErc,OAAS5M,EAAM6E,MAAQokB,EAAEnf,SAAW9J,EAAM8J,SAxEjGkT,EAwEwHiM,EAAEX,MAxEvHpL,EAwE8HoL,EAxElHtL,EAAE,KAAOE,EAAE,IAAMF,EAAE,KAAOE,EAAE,IAArD,IAAUF,EAAGE,CAwEsI,GAAG,GAEjK,GAAI8L,GAAeA,EAAYE,OACvBlpB,EAAMgN,YACNhN,EAAM6L,sBAKd,IAAKmd,EAAa,CACd,IAAIG,GAAclC,EAAUlkB,QAAQ+f,QAAU,IACzCtiB,IAAI8lB,IACJlhB,OAAOC,SACPD,OAAO,SAAUxC,GAAQ,OAAOA,EAAKoI,SAAShL,EAAM8J,OAAS,IACjDqf,EAAWrpB,OAAS,EAAIwnB,EAAkBtnB,EAAOmpB,EAAW,KAAOlC,EAAUlkB,QAAQigB,cAE9FhjB,EAAMgN,YACNhN,EAAM6L,gBAGlB,CAtBA,CAuBJ,EAAG,IACCud,EAAelH,EAAAA,YAAkB,SAAUtV,EAAM0b,EAAOxe,EAAQof,GAChE,IAAIlpB,EAAQ,CAAE4M,KAAMA,EAAM0b,MAAOA,EAAOxe,OAAQA,EAAQof,OAAQA,GAChEtC,EAAmB7jB,QAAQqQ,KAAKpT,GAChCyK,WAAW,WACPmc,EAAmB7jB,QAAU6jB,EAAmB7jB,QAAQqC,OAAO,SAAU6jB,GAAK,OAAOA,IAAMjpB,CAAO,EACtG,EAAG,EACP,EAAG,IACCqpB,EAAmBnH,EAAAA,YAAkB,SAAUliB,GAC/C6mB,EAAc9jB,QAAUgjB,GAAW/lB,GACnC8mB,EAAW/jB,aAAUhD,CACzB,EAAG,IACCupB,EAAcpH,EAAAA,YAAkB,SAAUliB,GAC1CopB,EAAappB,EAAM6E,KAAMshB,GAAWnmB,GAAQA,EAAM8J,OAAQwd,EAAkBtnB,EAAOkB,EAAM2iB,QAAQ9gB,SACrG,EAAG,IACCwmB,EAAkBrH,EAAAA,YAAkB,SAAUliB,GAC9CopB,EAAappB,EAAM6E,KAAMkhB,GAAW/lB,GAAQA,EAAM8J,OAAQwd,EAAkBtnB,EAAOkB,EAAM2iB,QAAQ9gB,SACrG,EAAG,IACHmf,EAAAA,UAAgB,WAUZ,OATAwE,GAAUtT,KAAK2T,GACf7lB,EAAMuhB,aAAa,CACfJ,gBAAiBiH,EACjBhH,eAAgBgH,EAChB/G,mBAAoBgH,IAExB7gB,SAAS4B,iBAAiB,QAASwe,EAAetE,IAClD9b,SAAS4B,iBAAiB,YAAawe,EAAetE,IACtD9b,SAAS4B,iBAAiB,aAAc+e,EAAkB7E,IACnD,WACHkC,GAAYA,GAAUthB,OAAO,SAAUokB,GAAQ,OAAOA,IAASzC,CAAO,GACtEre,SAAS2B,oBAAoB,QAASye,EAAetE,IACrD9b,SAAS2B,oBAAoB,YAAaye,EAAetE,IACzD9b,SAAS2B,oBAAoB,aAAcgf,EAAkB7E,GACjE,CACJ,EAAG,IACH,IAAI5B,EAAkB1hB,EAAM0hB,gBAAiBK,EAAQ/hB,EAAM+hB,MAC3D,OAAQf,EAAAA,cAAoBA,EAAAA,SAAgB,KACxCe,EAAQf,EAAAA,cAAoB6E,EAAO,CAAEpC,OAAQ4B,GAAcrR,KAAS,KACpE0N,EAAkBV,EAAAA,cAAoBuH,GAAAA,GAAiB,CAAEC,QAAS,WAAc,KACxF,GE7IA,IAAIC,GAAoBzH,EAAAA,WAAiB,SAAUhhB,EAAO4B,GAAO,OAAQof,EAAAA,cAAoBD,IAAc2B,EAAAA,GAAAA,IAAS,CAAC,EAAG1iB,EAAO,CAAE4B,IAAKA,EAAKigB,QAASS,KAAc,GAClKmG,GAAkB5F,WAAa9B,GAAa8B,WAC5C,YCwBM6F,GAAY,CAAC,IAAK,QAAS,UAAW,aACtCC,GAAiB,CAAC,IAAK,SAMvBC,GAAc,UAGbC,GAAYC,GAAeC,ICtBlC,SAA0Erd,GAKxE,MAAMsd,EAAgBtd,EAAO,sBACtBud,EAAyBC,GAAyBC,EAAmBH,IAOrEI,EAAwBC,GAAwBJ,EACrDD,EACA,CAAEM,cAAe,CAAEznB,QAAS,MAAQ0nB,QAAS,IAAIC,MAG7CC,EAA4EzpB,IAChF,MArCJP,MAqCYA,EArCZQ,SAqCmBA,GAAaD,EACtB4B,EAAM8nB,EAAAA,OAAgC,MACtCH,EAAUG,EAAAA,OAAsC,IAAIF,KAAO3nB,QACjE,OACE6nB,EAAAA,cAACN,EADH,CAC0B3pB,MAAOA,EAAO8pB,QAASA,EAASD,cAAe1nB,GACpE3B,IAWD0pB,EAAuBje,EAAO,iBAE9Bke,EAAiBF,EAAAA,WACrB,CAAC1pB,EAAOqC,KACN,MAzDN5C,MAyDcA,EAzDdQ,SAyDqBA,GAAaD,EAEtB0H,EAAemiB,EAAgBxnB,EADrBgnB,EAAqBM,EAAsBlqB,GACA6pB,eAC3D,OAAOI,EAAAA,cAACI,EAAR,CAAaloB,IAAK8F,GAAezH,KAU/B8pB,EAAiBre,EAAO,qBACxBse,EAAiB,6BAOjBC,EAAqBP,EAAAA,WACzB,CAAC1pB,EAAOqC,KACN,MAhFN5C,MAgFcA,EAhFdQ,SAgFqBA,KAAaiqB,GAAalqB,EACnC4B,EAAM8nB,EAAAA,OAA0B,MAChChiB,EAAemiB,EAAgBxnB,EAAcT,GAC7C1B,EAAUmpB,EAAqBU,EAAgBtqB,GAOrD,OALAiqB,EAAAA,UAAgB,KACdxpB,EAAQqpB,QAAQY,IAAIvoB,EAAK,CAtFjCA,IAsFmCA,KAASsoB,IAC7B,KAAWhqB,EAAQqpB,QAAQte,OAAOrJ,MAIzC8nB,EAAAA,cAACI,EADH,CACc,CAACE,GAAiB,GAAMpoB,IAAK8F,GACtCzH,KA6BT,MAAO,CACL,CAAEF,SAAU0pB,EAAoBW,KAAMR,EAAgBS,SAAUJ,GAlBlE,SAAuBxqB,GACrB,MAAMS,EAAUmpB,EAAqB3d,EAAO,qBAAsBjM,GAalE,OAXiBiqB,EAAAA,YAAkB,KACjC,MAAMY,EAAiBpqB,EAAQopB,cAAcznB,QAC7C,IAAKyoB,EAAgB,MAAO,GAC5B,MAAMC,EAAezpB,MAAM8G,KAAK0iB,EAAe/d,iBAAkB,IAAGyd,OAKpE,OAJclpB,MAAM8G,KAAK1H,EAAQqpB,QAAQlpB,UACdmqB,KACzB,CAACC,EAAGC,IAAMH,EAAaviB,QAAQyiB,EAAE7oB,IAAIC,SAAY0oB,EAAaviB,QAAQ0iB,EAAE9oB,IAAIC,WAG7E,CAAC3B,EAAQopB,cAAeppB,EAAQqpB,S,EAQnCL,E,CDpFuDyB,CAGzD/B,KAGKgC,GAAqBC,IAAqBC,EAAmBlC,GAAa,CAC/EG,GACAgC,IAEIC,GAAiBD,KAoBhBE,GAAgBC,IAAoBN,GAAwChC,KAQ5EuC,GAA6BC,IAClCR,GAAqDhC,IAiBjDyC,GAAiCrrB,IACrC,M,cACEsrB,E,SACArrB,EACAsrB,KAAMC,E,YACNC,E,aACAC,EACAxtB,MAAOytB,E,aACPC,E,cACAC,E,IACAnP,E,KACAhR,E,aACAogB,E,SACAja,E,SACAka,GACE/rB,EACEgsB,EAAchB,GAAeM,IAC5BW,EAASC,IAAcC,EAAAA,EAAAA,UAA4C,OACnEC,EAAWC,IAAgBF,EAAAA,EAAAA,UAA0C,OACrEG,EAAsBC,IAA2BJ,EAAAA,EAAAA,WAAe,GACjEjF,ExBhGR,SAAsBsF,GACpB,MAAMC,GAAYC,EAAAA,EAAAA,YAAiBroB,GACnC,OAAOmoB,GAAYC,GAAa,K,CwB8FdE,CAAajQ,IACxB6O,GAAO,EAAOqB,GAAWC,GAAqB,CACnDhO,KAAM2M,EACN1M,YAAa2M,EACb1M,SAAU2M,KAELxtB,EAAO4uB,GAAYD,GAAqB,CAC7ChO,KAAM8M,EACN7M,YAAa8M,EACb7M,SAAU8M,IAENkB,GAA2BC,EAAAA,EAAAA,QAA8C,MAGzEC,GAAgBhB,GAAU9nB,QAAQ8nB,EAAQiB,QAAQ,UACjDC,EAAkBC,IAAuBjB,EAAAA,EAAAA,UAAe,IAAI9lB,KAO7DgnB,EAAkBvsB,MAAM8G,KAAKulB,GAChC7tB,IAAKguB,GAAWA,EAAOttB,MAAM9B,OAC7BkG,KAAK,KAER,OACEmpB,EAAAA,EAAAA,eAACC,GAAyBxB,GACxBuB,EAAAA,EAAAA,eAACtC,GAFL,CAGMc,SAAUA,EACVtsB,MAAO6rB,EACPW,QAASA,EACTwB,gBAAiBvB,EACjBE,UAAWA,EACXsB,kBAAmBrB,EACnBC,qBAAsBA,EACtBqB,6BAA8BpB,EAC9BqB,UAAWC,IACX3vB,MAAOA,EACP2tB,cAAeiB,EACfvB,KAAMA,EACNG,aAAckB,EACdlQ,IAAKwK,EACL6F,yBAA0BA,EAC1Blb,SAAUA,IAEV0b,EAAAA,EAAAA,eAAC1E,GAAW9oB,SAlBd,CAkBuBN,MAAO6rB,IAC1BiC,EAAAA,EAAAA,eAACpC,GADH,CAEI1rB,MAAOO,EAAMsrB,cACbwC,mBAAmBC,EAAAA,EAAAA,aAAmBT,IACpCF,EAAqBY,GAAS,IAAI3nB,IAAI2nB,GAAMjjB,IAAIuiB,KAC/C,IACHW,sBAAsBF,EAAAA,EAAAA,aAAmBT,IACvCF,EAAqBY,IACnB,MAAME,EAAa,IAAI7nB,IAAI2nB,GAE3B,OADAE,EAAWjjB,OAAOqiB,GACXY,KAER,KAEFjuB,IAIJgtB,GACCM,EAAAA,EAAAA,eAACY,GAlBD,CAmBEnwB,IAAKqvB,EACL,iBACAtB,SAAUA,EACVjf,UAAW,EACXpB,KAAMA,EACNogB,aAAcA,EACd5tB,MAAOA,EAEP6gB,SAAWjgB,GAAUguB,EAAShuB,EAAM8J,OAAO1K,OAC3C2T,SAAUA,QAEChT,IAAVX,GAAsBqvB,EAAAA,EAAAA,eAZzB,UAYiCrvB,MAAM,KAAQ,KAC5C4C,MAAM8G,KAAKulB,IAEZ,QAYNiB,GAAe,gBAMfC,IAAgBC,EAAAA,EAAAA,YACpB,CAACtuB,EAAwCqC,KACvC,M,cAAQipB,EAAF,SAAiBzZ,GAAW,KAAU0c,GAAiBvuB,EACvDgsB,EAAchB,GAAeM,GAC7BprB,EAAUgrB,GAAiBkD,GAAc9C,GACzCkD,EAAatuB,EAAQ2R,UAAYA,EACjCnK,EAAe+mB,EAAgBpsB,EAAcnC,EAAQutB,iBACrDiB,EAAW5F,GAAcwC,IAExBqD,EAAWC,EAAuBC,GAAkBC,GAAoBC,IAC7E,MAAMC,EAAeN,IAAWxqB,OAAQgM,IAAUA,EAAK2B,UACjDod,EAAcD,EAAarsB,KAAMuN,GAASA,EAAKhS,QAAUgC,EAAQhC,OACjEgxB,EAAWC,GAAaH,EAAcD,EAAQE,QACnCpwB,IAAbqwB,GACFhvB,EAAQ2rB,cAAcqD,EAAShxB,SAI7BkxB,EAAaA,KACZZ,IACHtuB,EAAQwrB,cAAa,GAErBmD,MAIJ,OACEtB,EAAAA,EAAAA,eAAC8B,IADHC,EAAAA,EAAAA,GAAA,CAC0B3qB,SAAA,GAAYqnB,IAClCuB,EAAAA,EAAAA,eAACgC,EAAUC,QADbF,EAAAA,EAAAA,GAAA,CAEI3rB,KAAK,SACL8rB,KAAK,WACL,gBAAevvB,EAAQ0tB,UACvB,gBAAe1tB,EAAQqrB,KACvB,gBAAerrB,EAAQ6rB,SACvB,oBAAkB,OAClBrP,IAAKxc,EAAQwc,IACb,aAAYxc,EAAQqrB,KAAO,OAAS,SACpC1Z,SAAU2c,EACV,gBAAeA,EAAa,QAAK3vB,EACjC,wBAAoCA,IAAlBqB,EAAQhC,MAAsB,QAAKW,GACjD0vB,EAZN,CAaE3sB,IAAK8F,EAELgoB,QAASC,EAAqBpB,EAAamB,QAAU5wB,IAMnDA,EAAM6R,cAAcgC,UAEtBid,cAAeD,EAAqBpB,EAAaqB,cAAgB9wB,IAG/D,MAAM8J,EAAS9J,EAAM8J,OACjBA,EAAOinB,kBAAkB/wB,EAAMgxB,YACjClnB,EAAOmnB,sBAAsBjxB,EAAMgxB,WAKhB,IAAjBhxB,EAAM0wB,SAAkC,IAAlB1wB,EAAM0R,UAC9B4e,IACAlvB,EAAQ6sB,yBAAyBlrB,QAAU,CACzCia,EAAGxd,KAAK0xB,MAAMlxB,EAAMmxB,OACpBjU,EAAG1d,KAAK0xB,MAAMlxB,EAAMoxB,QAGtBpxB,EAAM6L,oBAGV0G,UAAWse,EAAqBpB,EAAald,UAAYvS,IACvD,MAAMqxB,EAAsC,KAAtBxB,EAAU9sB,QACV/C,EAAM0R,SAAW1R,EAAMyR,QAAUzR,EAAM2R,SAClB,IAArB3R,EAAMd,IAAIY,QAAcgwB,EAAsB9vB,EAAMd,KACtEmyB,GAA+B,MAAdrxB,EAAMd,KACvB0qB,GAAU0H,SAAStxB,EAAMd,OAC3BoxB,IACAtwB,EAAM6L,0BAed0lB,GAAa,cAQbC,IAAchC,EAAAA,EAAAA,YAClB,CAACtuB,EAAsCqC,KAErC,M,cAAQipB,E,UAAe7J,E,MAAW5W,E,SAAO5K,E,YAAUswB,KAAgBC,GAAexwB,EAC5EE,EAAUgrB,GAAiBmF,GAAY/E,I,6BACrCqC,GAAiCztB,EACnCuwB,OAA2B5xB,IAAboB,EACdyH,EAAe+mB,EAAgBpsB,EAAcnC,EAAQwtB,mBAM3D,OAJAgD,EAAgB,KACd/C,EAA6B8C,IAC5B,CAAC9C,EAA8B8C,KAGhClD,EAAAA,EAAAA,eAACgC,EAAUpP,MAAXmP,EAAAA,EAAAA,GAAA,GACMkB,EAFR,CAGI5uB,IAAK8F,EAGLmD,MAAO,CAAEC,cAAe,eAELjM,IAAlBqB,EAAQhC,YAAuCW,IAAhB0xB,EAA4BA,EAActwB,KAiB5E0wB,IAAarC,EAAAA,EAAAA,YACjB,CAACtuB,EAAqCqC,KACpC,M,cAAQipB,E,SAAerrB,KAAa2wB,GAAc5wB,EAClD,OACEutB,EAAAA,EAAAA,eAACgC,EAAUpP,MADbmP,EAAAA,EAAAA,GAAA,CACkB,kBAAgBsB,EAAhC,CAA2ChvB,IAAKS,IAC7CpC,GAAY,YAmBf4wB,GAA6C7wB,IAC1CutB,EAAAA,EAAAA,eAACuD,IAARxB,EAAAA,EAAAA,GAAA,CAAwB3qB,SAAA,GAAY3E,IAShC+wB,GAAe,gBAKfC,IAAgB1C,EAAAA,EAAAA,YACpB,CAACtuB,EAAwCqC,KACvC,MAAMnC,EAAUgrB,GAAiB6F,GAAc/wB,EAAMsrB,gBAC9C2F,EAAUC,IAAe/E,EAAAA,EAAAA,YAOhC,GAJAuE,EAAgB,KACdQ,EAAY,IAAIC,mBACf,KAEEjxB,EAAQqrB,KAAM,CACjB,MAAM6F,EAAOH,EACb,OAAOG,GACHC,EAAAA,EAAAA,eACE9D,EAAAA,EAAAA,eAAC+D,GAFP,CAE6B7xB,MAAOO,EAAMsrB,gBAClCiC,EAAAA,EAAAA,eAAC1E,GAAWuB,KADd,CACmB3qB,MAAOO,EAAMsrB,gBAC5BiC,EAAAA,EAAAA,eAAA,WAAMvtB,EAAMC,YAGhBmxB,GAEF,I,CAGN,OAAO7D,EAAAA,EAAAA,eAACgE,IAADjC,EAAAA,EAAAA,GAAA,GAAuBtvB,EAA9B,CAAqC4B,IAAKS,OAUxCmvB,GAAiB,IAqBhBF,GAAuBG,IAC5B7G,GAA+CmG,IAgC3CQ,IAAoBjD,EAAAA,EAAAA,YACxB,CAACtuB,EAA4CqC,KAC3C,M,cACEipB,EADI,SAEJjL,EAAW,e,iBACXqR,E,gBACA9qB,E,qBACAC,E,KAGA2P,E,WACAC,E,MACAC,E,YACAC,E,aACAC,E,kBACAC,E,iBACAC,E,OACAE,E,iBACAC,E,gBACAC,KAEGE,GACDpX,EACEE,EAAUgrB,GAAiB6F,GAAczF,IACxCjU,EAASC,IAAc6U,EAAAA,EAAAA,UAAgD,OACvEwF,EAAUC,IAAezF,EAAAA,EAAAA,UAA6C,MACvEzkB,EAAe+mB,EAAgBpsB,EAAeX,GAAS4V,EAAW5V,KACjEmwB,EAAcC,IAAmB3F,EAAAA,EAAAA,UAAyC,OAC1E4F,EAAkBC,IAAuB7F,EAAAA,EAAAA,UAC9C,MAEIuC,EAAW5F,GAAcwC,IACxB5R,EAAcuY,IAAmB9F,EAAAA,EAAAA,WAAe,GACjD+F,GAAyBlF,EAAAA,EAAAA,SAAa,IAG5CmF,EAAAA,EAAAA,WAAgB,KACd,GAAI9a,EAAS,OAAO+a,EAAAA,GAAAA,IAAW/a,IAC9B,CAACA,IAIJgb,IAEA,MAAMC,GAAavE,EAAAA,EAAAA,aAChBle,IACC,MAAO0iB,KAAcC,GAAa9D,IAAWpvB,IAAK4Q,GAASA,EAAKtO,IAAIC,UAC7D4wB,GAAYD,EAAU1qB,OAAO,GAE9B4qB,EAA6BlrB,SAASyH,cAC5C,IAAK,MAAMa,KAAaD,EAAY,CAElC,GAAIC,IAAc4iB,EAA4B,OAM9C,GALS,OAAT5iB,QAAS,IAATA,GAAAA,EAAW6iB,eAAe,CAAEC,MAAO,YAE/B9iB,IAAcyiB,GAAaZ,IAAUA,EAASrN,UAAY,GAC1DxU,IAAc2iB,GAAYd,IAAUA,EAASrN,UAAYqN,EAASpN,cAC7D,OAATzU,QAAS,IAATA,GAAAA,EAAW6C,QACPnL,SAASyH,gBAAkByjB,EAA4B,M,GAG/D,CAAChE,EAAUiD,IAGPkB,GAAoB9E,EAAAA,EAAAA,aACxB,IAAMuE,EAAW,CAACT,EAAcxa,IAChC,CAACib,EAAYT,EAAcxa,KAK7B8a,EAAAA,EAAAA,WAAgB,KACVzY,GACFmZ,KAED,CAACnZ,EAAcmZ,IAIlB,M,aAAQnH,E,yBAAcqB,GAA6B7sB,GACnDiyB,EAAAA,EAAAA,WAAgB,KACd,GAAI9a,EAAS,CACX,IAAIyb,EAAmB,CAAEhX,EAAG,EAAGE,EAAG,GAElC,MAAM+W,EAAqBj0B,IAAwB,IAAAk0B,EAAAC,EAAAC,EAAAC,EACjDL,EAAmB,CACjBhX,EAAGxd,KAAKqoB,IAAIroB,KAAK0xB,MAAMlxB,EAAMmxB,QAAjB,QAAA+C,EAAA,QAAAC,EAA2BlG,EAAyBlrB,eAApD,IAAAoxB,OAAA,EAA2BA,EAAkCnX,SAA7D,IAAAkX,EAAAA,EAAkE,IAC9EhX,EAAG1d,KAAKqoB,IAAIroB,KAAK0xB,MAAMlxB,EAAMoxB,QAAjB,QAAAgD,EAAA,QAAAC,EAA2BpG,EAAyBlrB,eAApD,IAAAsxB,OAAA,EAA2BA,EAAkCnX,SAA7D,IAAAkX,EAAAA,EAAkE,MAG5EE,EAAmBt0B,IAEnBg0B,EAAiBhX,GAAK,IAAMgX,EAAiB9W,GAAK,GACpDld,EAAM6L,iBAGD0M,EAAQvN,SAAShL,EAAM8J,SAC1B8iB,GAAa,GAGjBlkB,SAAS2B,oBAAoB,cAAe4pB,GAC5ChG,EAAyBlrB,QAAU,MAQrC,OALyC,OAArCkrB,EAAyBlrB,UAC3B2F,SAAS4B,iBAAiB,cAAe2pB,GACzCvrB,SAAS4B,iBAAiB,YAAagqB,EAAiB,CAAEC,SAAS,EAAMhqB,MAAM,KAG1E,KACL7B,SAAS2B,oBAAoB,cAAe4pB,GAC5CvrB,SAAS2B,oBAAoB,YAAaiqB,EAAiB,CAAEC,SAAS,I,GAGzE,CAAChc,EAASqU,EAAcqB,KAE3BoF,EAAAA,EAAAA,WAAgB,KACd,MAAMmB,EAAQA,IAAM5H,GAAa,GAGjC,OAFA1mB,OAAOoE,iBAAiB,OAAQkqB,GAChCtuB,OAAOoE,iBAAiB,SAAUkqB,GAC3B,KACLtuB,OAAOmE,oBAAoB,OAAQmqB,GACnCtuB,OAAOmE,oBAAoB,SAAUmqB,KAEtC,CAAC5H,IAEJ,MAAOiD,EAAWC,GAAyBE,GAAoBC,IAC7D,MAAMC,EAAeN,IAAWxqB,OAAQgM,IAAUA,EAAK2B,UACjDod,EAAcD,EAAarsB,KAAMuN,GAASA,EAAKtO,IAAIC,UAAY2F,SAASyH,eACxEigB,EAAWC,GAAaH,EAAcD,EAAQE,GAChDC,GAKF3lB,WAAW,IAAO2lB,EAASttB,IAAIC,QAAwB8Q,WAIrD4gB,GAAkBxF,EAAAA,EAAAA,aACtB,CAACrsB,EAAgCxD,EAAe2T,KAC9C,MAAM2hB,GAAoBtB,EAAuBrwB,UAAYgQ,QACpBhT,IAAlBqB,EAAQhC,OAAuBgC,EAAQhC,QAAUA,GAClDs1B,KACpB1B,EAAgBpwB,GACZ8xB,IAAkBtB,EAAuBrwB,SAAU,KAG3D,CAAC3B,EAAQhC,QAELu1B,GAAkB1F,EAAAA,EAAAA,aAAkB,WAAM1W,QAAN,IAAMA,OAAN,EAAMA,EAAS1E,QAAS,CAAC0E,IAC7Dqc,GAAsB3F,EAAAA,EAAAA,aAC1B,CAACrsB,EAAoCxD,EAAe2T,KAClD,MAAM2hB,GAAoBtB,EAAuBrwB,UAAYgQ,QACpBhT,IAAlBqB,EAAQhC,OAAuBgC,EAAQhC,QAAUA,GAClDs1B,IACpBxB,EAAoBtwB,IAGxB,CAACxB,EAAQhC,QAGLy1B,EAA8B,WAAbtT,EAAwBuT,GAAuBC,GAGhEC,EACJH,IAAmBC,GACf,C,KACEpd,E,WACAC,E,MACAC,E,YACAC,E,aACAC,E,kBACAC,E,iBACAC,E,OACAE,E,iBACAC,E,gBACAC,GAEF,CAAC,EAEP,OACEqW,EAAAA,EAAAA,eAAC+D,GADH,CAEI7xB,MAAO6rB,EACPjU,QAASA,EACTsa,SAAUA,EACVoC,iBAAkBnC,EAClB2B,gBAAiBA,EACjB1B,aAAcA,EACdmC,YAAaP,EACbC,oBAAqBA,EACrBb,kBAAmBA,EACnBd,iBAAkBA,EAClB1R,SAAUA,EACV3G,aAAcA,EACdiV,UAAWA,IAEXpB,EAAAA,EAAAA,eAAC0G,GAfH,CAegB/R,GAAIgS,EAAMlS,gBAAA,IACtBuL,EAAAA,EAAAA,eAAC4G,EADH,CAEIxvB,SAAO,EAGP2I,QAASpN,EAAQqrB,KACjBhe,iBAAmBzO,IAEjBA,EAAM6L,kBAER8C,mBAAoBkiB,EAAqB+B,EAAmB5yB,IAAU,IAAAs1B,EACpE,QAAAA,EAAAl0B,EAAQ+rB,eAAR,IAAAmI,GAAAA,EAAiBzhB,MAAM,CAAEC,eAAe,IACxC9T,EAAM6L,qBAGR4iB,EAAAA,EAAAA,eAAC8G,EAdH,CAeI1vB,SAAO,EACPgC,6BAA2B,EAC3BC,gBAAiBA,EACjBC,qBAAsBA,EAGtBC,eAAiBhI,GAAUA,EAAM6L,iBACjC3D,UAAWA,IAAM9G,EAAQwrB,cAAa,KAEtC6B,EAAAA,EAAAA,eAACoG,GAVHrE,EAAAA,EAAAA,GAAA,CAWIG,KAAK,UACLzb,GAAI9T,EAAQ0tB,UACZ,aAAY1tB,EAAQqrB,KAAO,OAAS,SACpC7O,IAAKxc,EAAQwc,IACb4X,cAAgBx1B,GAAUA,EAAM6L,kBAC5ByM,EACA0c,EAPN,CAQE3c,SAAUA,IAAM8a,GAAgB,GAChCrwB,IAAK8F,EACLmD,MAAO,CAEL4H,QAAS,OACT8hB,cAAe,SAEfC,QAAS,UACNpd,EAAavM,OAElBwG,UAAWse,EAAqBvY,EAAa/F,UAAYvS,IACvD,MAAM21B,EAAgB31B,EAAM0R,SAAW1R,EAAMyR,QAAUzR,EAAM2R,QAO7D,GAJkB,QAAd3R,EAAMd,KAAec,EAAM6L,iBAE1B8pB,GAAsC,IAArB31B,EAAMd,IAAIY,QAAcgwB,EAAsB9vB,EAAMd,KAEtE,CAAC,UAAW,YAAa,OAAQ,OAAOoyB,SAAStxB,EAAMd,KAAM,CAE/D,IAAI02B,EADUhG,IAAWxqB,OAAQgM,IAAUA,EAAK2B,UACrBvS,IAAK4Q,GAASA,EAAKtO,IAAIC,SAKlD,GAHI,CAAC,UAAW,OAAOuuB,SAAStxB,EAAMd,OACpC02B,EAAiBA,EAAe5sB,QAAQiJ,WAEtC,CAAC,UAAW,aAAaqf,SAAStxB,EAAMd,KAAM,CAChD,MAAM22B,EAAiB71B,EAAM8J,OACvBgsB,EAAeF,EAAe1sB,QAAQ2sB,GAC5CD,EAAiBA,EAAe5sB,MAAM8sB,EAAe,E,CAOvDrrB,WAAW,IAAM+oB,EAAWoC,IAE5B51B,EAAM6L,gB,aAuBpBkpB,IAA4BvF,EAAAA,EAAAA,YAGhC,CAACtuB,EAAoDqC,KACrD,M,cAAQipB,E,SAAenU,KAAa0d,GAAgB70B,EAC9CE,EAAUgrB,GAAiB6F,GAAczF,GACzCwJ,EAAiBrD,GAAwBV,GAAczF,IACtDyJ,EAAgBC,IAAqB7I,EAAAA,EAAAA,UAAsC,OAC3E9U,EAASC,IAAc6U,EAAAA,EAAAA,UAAwD,MAChFzkB,EAAe+mB,EAAgBpsB,EAAeX,GAAS4V,EAAW5V,IAClEgtB,EAAW5F,GAAcwC,GACzB2J,GAA0BjI,EAAAA,EAAAA,SAAa,GACvCkI,GAAsBlI,EAAAA,EAAAA,SAAa,I,SAEjC2E,E,aAAUE,E,iBAAcE,E,kBAAkBc,GAAsBiC,EAClEzU,GAAW0N,EAAAA,EAAAA,aAAkB,KACjC,GACE7tB,EAAQ+rB,SACR/rB,EAAQksB,WACR2I,GACA1d,GACAsa,GACAE,GACAE,EACA,CACA,MAAMoD,EAAcj1B,EAAQ+rB,QAAQmJ,wBAK9BC,EAAche,EAAQ+d,wBACtBE,EAAgBp1B,EAAQksB,UAAUgJ,wBAClCG,EAAexD,EAAiBqD,wBAEtC,GAAoB,QAAhBl1B,EAAQwc,IAAe,CACzB,MAAM8Y,EAAiBD,EAAatc,KAAOoc,EAAYpc,KACjDA,EAAOqc,EAAcrc,KAAOuc,EAC5BC,EAAYN,EAAYlc,KAAOA,EAC/Byc,EAAkBP,EAAYtd,MAAQ4d,EACtCE,EAAer3B,KAAKD,IAAIq3B,EAAiBL,EAAYxd,OACrD+d,EAAY5wB,OAAO6wB,WAAarE,GAChCsE,EAAcC,EAAM9c,EAAM,CAACuY,GAAgBoE,EAAYD,IAE7DZ,EAAelqB,MAAM2R,SAAWkZ,EAAkB,KAClDX,EAAelqB,MAAMoO,KAAO6c,EAAc,I,KACrC,CACL,MAAMN,EAAiBH,EAAYtc,MAAQwc,EAAaxc,MAClDA,EAAQ/T,OAAO6wB,WAAaP,EAAcvc,MAAQyc,EAClDQ,EAAahxB,OAAO6wB,WAAaV,EAAYpc,MAAQA,EACrD2c,EAAkBP,EAAYtd,MAAQme,EACtCL,EAAer3B,KAAKD,IAAIq3B,EAAiBL,EAAYxd,OACrDoe,EAAWjxB,OAAO6wB,WAAarE,GAC/B0E,EAAeH,EAAMhd,EAAO,CAACyY,GAAgByE,EAAWN,IAE9DZ,EAAelqB,MAAM2R,SAAWkZ,EAAkB,KAClDX,EAAelqB,MAAMkO,MAAQmd,EAAe,I,CAM9C,MAAMlmB,EAAQ0e,IACR5T,EAAkB9V,OAAOmxB,YAA+B,EAAjB3E,GACvC4E,EAAczE,EAASpN,aAEvB8R,EAAgBrxB,OAAOuN,iBAAiB8E,GACxCif,EAAwBC,SAASF,EAAcG,eAAgB,IAC/DC,EAAoBF,SAASF,EAAcK,WAAY,IACvDC,EAA2BJ,SAASF,EAAcO,kBAAmB,IAErEC,EAAoBP,EAAwBG,EAAoBL,EADzCG,SAASF,EAAcS,cAAe,IACwCH,EACrGI,EAAmBz4B,KAAKF,IAAgC,EAA5ByzB,EAAa7Z,aAAkB6e,GAE3DG,EAAiBhyB,OAAOuN,iBAAiBof,GACzCsF,EAAqBV,SAASS,EAAeN,WAAY,IACzDQ,EAAwBX,SAASS,EAAeF,cAAe,IAE/DK,EAAyBhC,EAAYrc,IAAMqc,EAAYpd,OAAS,EAAIyZ,GACpE4F,EAA4Btc,EAAkBqc,EAE9CE,EAAyBxF,EAAa7Z,aAAe,EAErDsf,EAAyBhB,EAAwBG,GAD9B5E,EAAa0F,UAAYF,GAE5CG,EAA4BX,EAAoBS,EAItD,GAFoCA,GAA0BH,EAE7B,CAC/B,MAAMM,EAAa5F,IAAiB7hB,EAAMA,EAAMpR,OAAS,GAAGgD,IAAIC,QAChEkzB,EAAelqB,MAAMmO,OAAS,MAC9B,MAAM0e,EACJrgB,EAAQmN,aAAemN,EAAS4F,UAAY5F,EAAS3Z,aASjDD,EAASuf,EAR0Bh5B,KAAKD,IAC5C+4B,EACAC,GAEGI,EAAaP,EAAwB,GACtCQ,EACAf,GAGJ5B,EAAelqB,MAAMkN,OAASA,EAAS,I,KAClC,CACL,MAAM4f,EAAc9F,IAAiB7hB,EAAM,GAAGpO,IAAIC,QAClDkzB,EAAelqB,MAAMiO,IAAM,MAC3B,MAQMf,EARgCzZ,KAAKD,IACzC84B,EACAb,EACE3E,EAAS4F,WAERI,EAAcV,EAAqB,GACpCI,GAE2CG,EAC/CzC,EAAelqB,MAAMkN,OAASA,EAAS,KACvC4Z,EAASrN,UAAYgT,EAAyBH,EAAyBxF,EAAS4F,S,CAGlFxC,EAAelqB,MAAM0V,OAAU,GAAEiR,SACjCuD,EAAelqB,MAAM+sB,UAAYb,EAAmB,KACpDhC,EAAelqB,MAAMgtB,UAAY/c,EAAkB,KAG3C,OAAR3D,QAAQ,IAARA,GAAAA,IAIA2gB,sBAAsB,IAAO7C,EAAwBpzB,SAAU,E,GAEhE,CACD6sB,EACAxuB,EAAQ+rB,QACR/rB,EAAQksB,UACR2I,EACA1d,EACAsa,EACAE,EACAE,EACA7xB,EAAQwc,IACRvF,IAGFuZ,EAAgB,IAAMrQ,IAAY,CAACA,IAGnC,MAAOlE,EAAeC,IAAoB+P,EAAAA,EAAAA,YAC1CuE,EAAgB,KACVrZ,GAAS+E,EAAiBpX,OAAOuN,iBAAiB8E,GAASgF,SAC9D,CAAChF,IAMJ,MAAM0gB,GAA2BhK,EAAAA,EAAAA,aAC9BrsB,IACKA,IAAwC,IAAhCwzB,EAAoBrzB,UAC9Bwe,IACiB,OAAjBwS,QAAiB,IAAjBA,GAAAA,IACAqC,EAAoBrzB,SAAU,IAGlC,CAACwe,EAAUwS,IAGb,OACEtF,EAAAA,EAAAA,eAACyK,GADH,CAEIv4B,MAAO6rB,EACPyJ,eAAgBA,EAChBE,wBAAyBA,EACzBgD,qBAAsBF,IAEtBxK,EAAAA,EAAAA,eANF,OAOI3rB,IAAKozB,EACLnqB,MAAO,CACL4H,QAAS,OACT8hB,cAAe,SACflU,SAAU,QACVhE,OAAQF,KAGVoR,EAAAA,EAAAA,eAACgC,EAAUlkB,KAAXikB,EAAAA,EAAAA,GAAA,GACMuF,EAVR,CAWIjzB,IAAK8F,EACLmD,MAAO,CAGLqtB,UAAW,aAEXL,UAAW,UACRhD,EAAYhqB,cAoBrB+oB,IAAuBtF,EAAAA,EAAAA,YAG3B,CAACtuB,EAA+CqC,KAChD,M,cACEipB,EADI,MAEJ5U,EAAQ,QAFJ,iBAGJI,EAAmB0a,MAChBqD,GACD70B,EACEgsB,EAAchB,GAAeM,GAEnC,OACEiC,EAAAA,EAAAA,eAAC4K,IAAD7I,EAAAA,EAAAA,GAAA,GACMtD,EACA6I,EAHR,CAIIjzB,IAAKS,EACLqU,MAAOA,EACPI,iBAAkBA,EAClBjM,MAAO,CAELqtB,UAAW,gBACRrD,EAAYhqB,MAGb,0CAA2C,uCAC3C,yCAA0C,sCAC1C,0CAA2C,uCAC3C,+BAAgC,mCAChC,gCAAiC,2CAmBpCmtB,GAAwBI,IAC7BxN,GAAgDmG,GAAc,CAAC,GAE3DsH,GAAgB,iBAMhBC,IAAiBhK,EAAAA,EAAAA,YACrB,CAACtuB,EAAyCqC,KACxC,M,cAAQipB,KAAkBiN,GAAkBv4B,EACtC80B,EAAiBrD,GAAwB4G,GAAe/M,GACxDkN,EAAkBJ,GAAyBC,GAAe/M,GAC1D5jB,EAAe+mB,EAAgBpsB,EAAcyyB,EAAef,kBAC5D0E,GAAmBzL,EAAAA,EAAAA,QAAa,GACtC,OACEO,EAAAA,EAAAA,eAAAmL,EAAAA,SAAA,MAEEnL,EAAAA,EAAAA,eAHJ,SAIMoL,wBAAyB,CACvBC,OAAS,gLAGbrL,EAAAA,EAAAA,eAAC1E,GAAWuB,KALZ,CAKiB3qB,MAAO6rB,IACtBiC,EAAAA,EAAAA,eAACgC,EAAUlkB,KADbikB,EAAAA,EAAAA,GAAA,CAEI,6BAA2B,GAC3BG,KAAK,gBACD8I,EAHN,CAIE32B,IAAK8F,EACLmD,MAAO,CAILwV,SAAU,WACVwY,KAAM,EACNrY,SAAU,UACP+X,EAAc1tB,OAEnBiuB,SAAUnJ,EAAqB4I,EAAcO,SAAWh6B,IACtD,MAAM6yB,EAAW7yB,EAAM6R,e,eACfokB,E,wBAAgBE,GAA4BuD,EACpD,GAA2B,OAAvBvD,QAAuB,IAAvBA,GAAAA,EAAyBpzB,SAAWkzB,EAAgB,CACtD,MAAMgE,EAAaz6B,KAAKqoB,IAAI8R,EAAiB52B,QAAU8vB,EAASrN,WAChE,GAAIyU,EAAa,EAAG,CAClB,MAAMje,EAAkB9V,OAAOmxB,YAA+B,EAAjB3E,GACvCwH,EAAeC,WAAWlE,EAAelqB,MAAM+sB,WAC/CsB,EAAYD,WAAWlE,EAAelqB,MAAMkN,QAC5CohB,EAAa76B,KAAKD,IAAI26B,EAAcE,GAE1C,GAAIC,EAAare,EAAiB,CAChC,MAAMse,EAAaD,EAAaJ,EAC1BM,EAAoB/6B,KAAKF,IAAI0c,EAAiBse,GAC9CE,EAAaF,EAAaC,EAEhCtE,EAAelqB,MAAMkN,OAASshB,EAAoB,KACd,QAAhCtE,EAAelqB,MAAMmO,SACvB2Y,EAASrN,UAAYgV,EAAa,EAAIA,EAAa,EAEnDvE,EAAelqB,MAAM0uB,eAAiB,W,GAK9Cd,EAAiB52B,QAAU8vB,EAASrN,mBAe5CkV,GAAa,eAIZC,GAA4BC,IACjC9O,GAA6C4O,IAKzCG,IAAcrL,EAAAA,EAAAA,YAClB,CAACtuB,EAAsCqC,KACrC,M,cAAQipB,KAAkBsO,GAAe55B,EACnC65B,EAAUhM,IAChB,OACEN,EAAAA,EAAAA,eAACkM,GADH,CAC8Bh6B,MAAO6rB,EAAetX,GAAI6lB,IACpDtM,EAAAA,EAAAA,eAACgC,EAAUlkB,KADbikB,EAAAA,EAAAA,GAAA,CACiBG,KAAK,QAAQ,kBAAiBoK,GAAaD,EAA1D,CAAsEh4B,IAAKS,QAY7Ey3B,GAAa,cAKbC,IAAczL,EAAAA,EAAAA,YAClB,CAACtuB,EAAsCqC,KACrC,M,cAAQipB,KAAkB0O,GAAeh6B,EACnCi6B,EAAeP,GAAsBI,GAAYxO,GACvD,OAAOiC,EAAAA,EAAAA,eAACgC,EAAUlkB,KAAlBikB,EAAAA,EAAAA,GAAA,CAAsBtb,GAAIimB,EAAajmB,IAAQgmB,EAAxC,CAAoDp4B,IAAKS,OAU9D63B,GAAY,cAUXC,GAA2BC,IAChCxP,GAA4CsP,IASxCG,IAAa/L,EAAAA,EAAAA,YACjB,CAACtuB,EAAqCqC,KACpC,M,cACEipB,E,MACAptB,EAFI,SAGJ2T,GAAW,EACXyoB,UAAWC,KACRC,GACDx6B,EACEE,EAAUgrB,GAAiBgP,GAAW5O,GACtCwJ,EAAiBrD,GAAwByI,GAAW5O,GACpDmP,EAAav6B,EAAQhC,QAAUA,GAC9Bo8B,EAAWI,IAAgBvO,EAAAA,EAAAA,UAAA,OAAeoO,QAAf,IAAeA,EAAAA,EAAiB,KAC3DI,EAAWC,IAAgBzO,EAAAA,EAAAA,WAAe,GAC3CzkB,EAAe+mB,EAAgBpsB,EAAeX,IAAD,IAAAm5B,EAAA,eAAAA,EACjD/F,EAAevB,uBADkC,IAAAsH,OAAA,EACjDA,EAAAh1B,KAAAivB,EAAiCpzB,EAAMxD,EAAO2T,KAE1CipB,EAASjN,IAETkN,EAAeA,KACdlpB,IACH3R,EAAQ2rB,cAAc3tB,GACtBgC,EAAQwrB,cAAa,KAIzB,OACE6B,EAAAA,EAAAA,eAAC4M,GADH,CAEI16B,MAAO6rB,EACPptB,MAAOA,EACP2T,SAAUA,EACVipB,OAAQA,EACRL,WAAYA,EACZO,kBAAkBjN,EAAAA,EAAAA,aAAmBrsB,IACnCg5B,EAAcO,IAAD,IAAAC,EAAA,OAAmBD,IAAiB,QAAAC,EAAA,OAACx5B,QAAD,IAACA,OAAD,EAACA,EAAMy5B,mBAAP,IAAAD,EAAAA,EAAsB,IAAIE,UAC1E,MAEH7N,EAAAA,EAAAA,eAAC1E,GAAWwB,SAVd,CAWI5qB,MAAO6rB,EACPptB,MAAOA,EACP2T,SAAUA,EACVyoB,UAAWA,IAEX/M,EAAAA,EAAAA,eAACgC,EAAUlkB,KANbikB,EAAAA,EAAAA,GAAA,CAOIG,KAAK,SACL,kBAAiBqL,EACjB,mBAAkBH,EAAY,QAAK97B,EAEnC,gBAAe47B,GAAcE,EAC7B,aAAYF,EAAa,UAAY,YACrC,gBAAe5oB,QAAYhT,EAC3B,gBAAegT,EAAW,QAAKhT,EAC/BiO,SAAU+E,OAAWhT,GAAa,GAC9B27B,EAVN,CAWE54B,IAAK8F,EACL2zB,QAAS1L,EAAqB6K,EAAUa,QAAS,IAAMT,GAAa,IACpEU,OAAQ3L,EAAqB6K,EAAUc,OAAQ,IAAMV,GAAa,IAClEW,YAAa5L,EAAqB6K,EAAUe,YAAaR,GACzDS,cAAe7L,EAAqB6K,EAAUgB,cAAgB18B,IAC9C,IAAA28B,EAAV5pB,EACF,QAAA4pB,EAAA3G,EAAed,mBAAf,IAAAyH,GAAAA,EAAA51B,KAAAivB,GAIAh2B,EAAM6R,cAAcgC,MAAM,CAAEC,eAAe,MAG/C8oB,eAAgB/L,EAAqB6K,EAAUkB,eAAiB58B,IACV,IAAA68B,EAAhD78B,EAAM6R,gBAAkBnJ,SAASyH,gBACnC,QAAA0sB,EAAA7G,EAAed,mBAAf,IAAA2H,GAAAA,EAAA91B,KAAAivB,MAGJzjB,UAAWse,EAAqB6K,EAAUnpB,UAAYvS,IAAU,IAAA88B,EACF,MAAtC,QAAAA,EAAA9G,EAAenG,iBAAf,IAAAiN,OAAA,EAAAA,EAA0B/5B,UACb,MAAd/C,EAAMd,MACvB2qB,GAAeyH,SAAStxB,EAAMd,MAAM+8B,IAEtB,MAAdj8B,EAAMd,KAAac,EAAM6L,2BAerCkxB,GAAiB,iBAKjBC,IAAiBxN,EAAAA,EAAAA,YACrB,CAACtuB,EAAyCqC,KAExC,M,cAAQipB,E,UAAe7J,E,MAAW5W,KAAUkxB,GAAkB/7B,EACxDE,EAAUgrB,GAAiB2Q,GAAgBvQ,GAC3CwJ,EAAiBrD,GAAwBoK,GAAgBvQ,GACzD0Q,EAAc5B,GAAqByB,GAAgBvQ,GACnD2Q,EAAuB7Q,GAA8ByQ,GAAgBvQ,IACpE4Q,EAAcC,IAAmBhQ,EAAAA,EAAAA,UAA6C,MAC/EzkB,EAAe+mB,EACnBpsB,EACCX,GAASy6B,EAAgBz6B,GAC1Bs6B,EAAYhB,iBACXt5B,IAAD,IAAA06B,EAAA,eAAAA,EAAUtH,EAAepB,2BAAzB,IAAA0I,OAAA,EAAUA,EAAAv2B,KAAAivB,EAAqCpzB,EAAMs6B,EAAY99B,MAAO89B,EAAYnqB,YAGhFspB,EAAW,OAAGe,QAAH,IAAGA,OAAH,EAAGA,EAAcf,YAC5BkB,GAAeC,EAAAA,EAAAA,SACnB,KACE/O,EAAAA,EAAAA,eAFJ,UAEYvvB,IAAKg+B,EAAY99B,MAAOA,MAAO89B,EAAY99B,MAAO2T,SAAUmqB,EAAYnqB,UAC7EspB,GAGL,CAACa,EAAYnqB,SAAUmqB,EAAY99B,MAAOi9B,K,kBAGpCrN,E,qBAAmBG,GAAyBgO,EAMpD,OALAvL,EAAgB,KACd5C,EAAkBuO,GACX,IAAMpO,EAAqBoO,IACjC,CAACvO,EAAmBG,EAAsBoO,KAG3C9O,EAAAA,EAAAA,eAAAmL,EAAAA,SAAA,MACEnL,EAAAA,EAAAA,eAACgC,EAAUpP,MAFfmP,EAAAA,EAAAA,GAAA,CAEoBtb,GAAIgoB,EAAYlB,QAAYiB,EAA5C,CAA2Dn6B,IAAK8F,KAG/Ds0B,EAAYvB,YAAcv6B,EAAQksB,YAAclsB,EAAQosB,sBACrD+E,EAAAA,EAAAA,cAAsB0K,EAAc97B,SAAUC,EAAQksB,WACtD,QAYNmQ,GAAsB,sBAKtBC,IAAsBlO,EAAAA,EAAAA,YAC1B,CAACtuB,EAA8CqC,KAC7C,M,cAAQipB,KAAkBmR,GAAuBz8B,EAEjD,OADoBo6B,GAAqBmC,GAAqBjR,GAC3CmP,YACjBlN,EAAAA,EAAAA,eAACgC,EAAUpP,MADbmP,EAAAA,EAAAA,GAAA,CACkB,kBAAgBmN,EAAhC,CAAoD76B,IAAKS,KACvD,OA0KFq6B,IAAkBpO,EAAAA,EAAAA,YACtB,CAACtuB,EAA0CqC,KACzC,M,cAAQipB,KAAkBqR,GAAmB38B,EAC7C,OAAOutB,EAAAA,EAAAA,eAACgC,EAAUlkB,KAAlBikB,EAAAA,EAAAA,GAAA,CAAsB,kBAAgBqN,EAA/B,CAA+C/6B,IAAKS,OAgCzD8rB,IAAeG,EAAAA,EAAAA,YACnB,CAACtuB,EAAOqC,KACN,M,MAAQnE,KAAU0+B,GAAgB58B,EAC5B4B,GAAMorB,EAAAA,EAAAA,QAAgC,MACtCtlB,EAAe+mB,EAAgBpsB,EAAcT,GAC7Ci7B,EErgDV,SAAwB3+B,GACtB,MAAM0D,GAAMk7B,EAAAA,EAAAA,QAAa,CAH3B5+B,MAG6BA,EAAO6+B,SAAU7+B,IAK5C,OAAO8+B,EAAAA,EAAAA,SAAc,KACfp7B,EAAIC,QAAQ3D,QAAUA,IACxB0D,EAAIC,QAAQk7B,SAAWn7B,EAAIC,QAAQ3D,MACnC0D,EAAIC,QAAQ3D,MAAQA,GAEf0D,EAAIC,QAAQk7B,UAClB,CAAC7+B,G,CFy/CgB++B,CAAY/+B,GA8B9B,OA3BAi0B,EAAAA,EAAAA,WAAgB,KACd,MAAMxjB,EAAS/M,EAAIC,QACbq7B,EAAcl4B,OAAOm4B,kBAAkBC,UAKvCtQ,EAJa1sB,OAAOi9B,yBACxBH,EACA,SAE0B/S,IAC5B,GAAI0S,IAAc3+B,GAAS4uB,EAAU,CACnC,MAAMhuB,EAAQ,IAAIw+B,MAAM,SAAU,CAAEzxB,SAAS,IAC7CihB,EAASjnB,KAAK8I,EAAQzQ,GACtByQ,EAAOlD,cAAc3M,E,GAEtB,CAAC+9B,EAAW3+B,KAebqvB,EAAAA,EAAAA,eAACgQ,GADH,CACkB54B,SAAA,IACd4oB,EAAAA,EAAAA,eAAA,UAAA+B,EAAAA,EAAAA,GAAA,GAAYsN,EADd,CAC2Bh7B,IAAK8F,EAAckkB,aAAc1tB,QAQlE,SAAS4wB,GAAmB0O,GAC1B,MAAMC,EAAqBC,EAAeF,GACpC7O,GAAY3B,EAAAA,EAAAA,QAAa,IACzB2Q,GAAW3Q,EAAAA,EAAAA,QAAa,GAExB4B,GAAwBb,EAAAA,EAAAA,aAC3B/vB,IACC,MAAM+wB,EAASJ,EAAU9sB,QAAU7D,EACnCy/B,EAAmB1O,GAElB,SAAS6O,EAAa1/B,GACrBywB,EAAU9sB,QAAU3D,EACpB8G,OAAOwE,aAAam0B,EAAS97B,SAEf,KAAV3D,IAAcy/B,EAAS97B,QAAUmD,OAAOuE,WAAW,IAAMq0B,EAAa,IAAK,KAJjF,CAAC,CAKE7O,IAEL,CAAC0O,IAGG5O,GAAiBd,EAAAA,EAAAA,aAAkB,KACvCY,EAAU9sB,QAAU,GACpBmD,OAAOwE,aAAam0B,EAAS97B,UAC5B,IAMH,OAJAswB,EAAAA,EAAAA,WAAgB,IACP,IAAMntB,OAAOwE,aAAam0B,EAAS97B,SACzC,IAEI,CAAC8sB,EAAWC,EAAuBC,E,CAoB5C,SAASM,GACPnf,EACA+e,EACAE,GAEA,MACM4O,EADa9O,EAAOnwB,OAAS,GAAKkC,MAAM8G,KAAKmnB,GAAQ+O,MAAOC,GAASA,IAAShP,EAAO,IACrDA,EAAO,GAAKA,EAC5CiP,EAAmB/O,EAAcjf,EAAMhI,QAAQinB,IAAgB,EACrE,IAAIgP,GAagB5qB,EAbSrD,EAaGkuB,EAbI5/B,KAAKD,IAAI2/B,EAAkB,GAcxD3qB,EAAM/T,IAAI,CAAC6+B,EAAGr+B,IAAUuT,GAAO6qB,EAAap+B,GAASuT,EAAMzU,UADpE,IAAsByU,EAAY6qB,EAZuB,IAA5BL,EAAiBj/B,SACpBq/B,EAAeA,EAAa/5B,OAAQk6B,GAAMA,IAAMnP,IACxE,MAAMC,EAAW+O,EAAat7B,KAAMuN,GAClCA,EAAKoqB,UAAU+D,cAAcC,WAAWT,EAAiBQ,gBAE3D,OAAOnP,IAAaD,EAAcC,OAAWrwB,C,CAjE/CsvB,GAAa5tB,YAAc,eA4E3B,MAAMg+B,GAAOlT,GACPmT,GAAUnQ,GACVoQ,GAAQnO,GACRoO,GAAO/N,GACPgO,GAAS9N,GACT+N,GAAU5N,GACV6N,GAAWvG,GACXwG,GAAQnF,GACRoF,GAAQhF,GACRiF,GAAO3E,GACP4E,GAAWnD,GACXoD,GAAgB1C,GAGhB2C,GAAYzC,E", "sources": ["../node_modules/lucide-react/src/icons/chevron-down.ts", "../node_modules/@radix-ui/number/dist/packages/core/number/src/number.ts", "../node_modules/@radix-ui/react-select/node_modules/@radix-ui/primitive/dist/packages/core/primitive/src/primitive.tsx", "../node_modules/@radix-ui/react-select/node_modules/@radix-ui/react-context/dist/packages/react/context/src/createContext.tsx", "../node_modules/@radix-ui/react-select/node_modules/@radix-ui/react-compose-refs/dist/packages/react/compose-refs/src/composeRefs.tsx", "../node_modules/@radix-ui/react-select/node_modules/@radix-ui/react-slot/dist/packages/react/slot/src/Slot.tsx", "../node_modules/@radix-ui/react-select/node_modules/@radix-ui/react-direction/dist/packages/react/direction/src/Direction.tsx", "../node_modules/@radix-ui/react-select/node_modules/@radix-ui/react-primitive/dist/packages/react/primitive/src/Primitive.tsx", "../node_modules/@radix-ui/react-select/node_modules/@radix-ui/react-use-callback-ref/dist/packages/react/use-callback-ref/src/useCallbackRef.tsx", "../node_modules/@radix-ui/react-select/node_modules/@radix-ui/react-dismissable-layer/dist/packages/react/dismissable-layer/src/DismissableLayer.tsx", "../node_modules/@radix-ui/react-select/node_modules/@radix-ui/react-use-escape-keydown/dist/packages/react/use-escape-keydown/src/useEscapeKeydown.tsx", "../node_modules/@radix-ui/react-select/node_modules/@radix-ui/react-focus-guards/dist/packages/react/focus-guards/src/FocusGuards.tsx", "../node_modules/@radix-ui/react-select/node_modules/@radix-ui/react-focus-scope/dist/packages/react/focus-scope/src/FocusScope.tsx", "../node_modules/@radix-ui/react-select/node_modules/@radix-ui/react-use-layout-effect/dist/packages/react/use-layout-effect/src/useLayoutEffect.tsx", "../node_modules/@radix-ui/react-select/node_modules/@radix-ui/react-id/dist/packages/react/id/src/id.tsx", "../node_modules/@radix-ui/react-select/node_modules/@radix-ui/react-popper/dist/packages/react/popper/src/Popper.tsx", "../node_modules/@radix-ui/react-select/node_modules/@radix-ui/react-use-size/dist/packages/react/use-size/src/useSize.tsx", "../node_modules/@radix-ui/react-select/node_modules/@radix-ui/react-portal/dist/packages/react/portal/src/Portal.tsx", "../node_modules/@radix-ui/react-select/node_modules/@radix-ui/react-use-controllable-state/dist/packages/react/use-controllable-state/src/useControllableState.tsx", "../node_modules/@radix-ui/react-visually-hidden/node_modules/@radix-ui/react-compose-refs/dist/packages/react/compose-refs/src/composeRefs.tsx", "../node_modules/@radix-ui/react-visually-hidden/node_modules/@radix-ui/react-slot/dist/packages/react/slot/src/Slot.tsx", "../node_modules/@radix-ui/react-visually-hidden/node_modules/@radix-ui/react-primitive/dist/packages/react/primitive/src/Primitive.tsx", "../node_modules/@radix-ui/react-visually-hidden/dist/packages/react/visually-hidden/src/VisuallyHidden.tsx", "../node_modules/@radix-ui/react-select/node_modules/react-remove-scroll/dist/es2015/medium.js", "../node_modules/@radix-ui/react-select/node_modules/react-remove-scroll/dist/es2015/UI.js", "../node_modules/@radix-ui/react-select/node_modules/react-remove-scroll/dist/es2015/aggresiveCapture.js", "../node_modules/@radix-ui/react-select/node_modules/react-remove-scroll/dist/es2015/handleScroll.js", "../node_modules/@radix-ui/react-select/node_modules/react-remove-scroll/dist/es2015/SideEffect.js", "../node_modules/@radix-ui/react-select/node_modules/react-remove-scroll/dist/es2015/sidecar.js", "../node_modules/@radix-ui/react-select/node_modules/react-remove-scroll/dist/es2015/Combination.js", "../node_modules/@radix-ui/react-select/dist/packages/react/select/src/Select.tsx", "../node_modules/@radix-ui/react-select/node_modules/@radix-ui/react-collection/dist/packages/react/collection/src/Collection.tsx", "../node_modules/@radix-ui/react-select/node_modules/@radix-ui/react-use-previous/dist/packages/react/use-previous/src/usePrevious.tsx"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ChevronDown\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtNiA5IDYgNiA2LTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/chevron-down\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronDown = createLucideIcon('ChevronDown', [\n  ['path', { d: 'm6 9 6 6 6-6', key: 'qrunsl' }],\n]);\n\nexport default ChevronDown;\n", "function clamp(value: number, [min, max]: [number, number]): number {\n  return Math.min(max, Math.max(min, value));\n}\n\nexport { clamp };\n", "function composeEventHandlers<E>(\n  originalEventHandler?: (event: E) => void,\n  ourEventHandler?: (event: E) => void,\n  { checkForDefaultPrevented = true } = {}\n) {\n  return function handleEvent(event: E) {\n    originalEventHandler?.(event);\n\n    if (checkForDefaultPrevented === false || !((event as unknown) as Event).defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\n\nexport { composeEventHandlers };\n", "import * as React from 'react';\n\nfunction createContext<ContextValueType extends object | null>(\n  rootComponentName: string,\n  defaultContext?: ContextValueType\n) {\n  const Context = React.createContext<ContextValueType | undefined>(defaultContext);\n\n  function Provider(props: ContextValueType & { children: React.ReactNode }) {\n    const { children, ...context } = props;\n    // Only re-memoize when prop values change\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    const value = React.useMemo(() => context, Object.values(context)) as ContextValueType;\n    return <Context.Provider value={value}>{children}</Context.Provider>;\n  }\n\n  function useContext(consumerName: string) {\n    const context = React.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== undefined) return defaultContext;\n    // if a defaultContext wasn't specified, it's a required context.\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n\n  Provider.displayName = rootComponentName + 'Provider';\n  return [Provider, useContext] as const;\n}\n\n/* -------------------------------------------------------------------------------------------------\n * createContextScope\n * -----------------------------------------------------------------------------------------------*/\n\ntype Scope<C = any> = { [scopeName: string]: React.Context<C>[] } | undefined;\ntype ScopeHook = (scope: Scope) => { [__scopeProp: string]: Scope };\ninterface CreateScope {\n  scopeName: string;\n  (): ScopeHook;\n}\n\nfunction createContextScope(scopeName: string, createContextScopeDeps: CreateScope[] = []) {\n  let defaultContexts: any[] = [];\n\n  /* -----------------------------------------------------------------------------------------------\n   * createContext\n   * ---------------------------------------------------------------------------------------------*/\n\n  function createContext<ContextValueType extends object | null>(\n    rootComponentName: string,\n    defaultContext?: ContextValueType\n  ) {\n    const BaseContext = React.createContext<ContextValueType | undefined>(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n\n    function Provider(\n      props: ContextValueType & { scope: Scope<ContextValueType>; children: React.ReactNode }\n    ) {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName][index] || BaseContext;\n      // Only re-memoize when prop values change\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      const value = React.useMemo(() => context, Object.values(context)) as ContextValueType;\n      return <Context.Provider value={value}>{children}</Context.Provider>;\n    }\n\n    function useContext(consumerName: string, scope: Scope<ContextValueType | undefined>) {\n      const Context = scope?.[scopeName][index] || BaseContext;\n      const context = React.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== undefined) return defaultContext;\n      // if a defaultContext wasn't specified, it's a required context.\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n\n    Provider.displayName = rootComponentName + 'Provider';\n    return [Provider, useContext] as const;\n  }\n\n  /* -----------------------------------------------------------------------------------------------\n   * createScope\n   * ---------------------------------------------------------------------------------------------*/\n\n  const createScope: CreateScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return React.createContext(defaultContext);\n    });\n    return function useScope(scope: Scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return React.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n\n  createScope.scopeName = scopeName;\n  return [createContext, composeContextScopes(createScope, ...createContextScopeDeps)] as const;\n}\n\n/* -------------------------------------------------------------------------------------------------\n * composeContextScopes\n * -----------------------------------------------------------------------------------------------*/\n\nfunction composeContextScopes(...scopes: CreateScope[]) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n\n  const createScope: CreateScope = () => {\n    const scopeHooks = scopes.map((createScope) => ({\n      useScope: createScope(),\n      scopeName: createScope.scopeName,\n    }));\n\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes, { useScope, scopeName }) => {\n        // We are calling a hook inside a callback which React warns against to avoid inconsistent\n        // renders, however, scoping doesn't have render side effects so we ignore the rule.\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes, ...currentScope };\n      }, {});\n\n      return React.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nexport { createContext, createContextScope };\nexport type { CreateScope, Scope };\n", "import * as React from 'react';\n\ntype PossibleRef<T> = React.Ref<T> | undefined;\n\n/**\n * Set a given ref to a given value\n * This utility takes care of different types of refs: callback refs and RefObject(s)\n */\nfunction setRef<T>(ref: PossibleRef<T>, value: T) {\n  if (typeof ref === 'function') {\n    ref(value);\n  } else if (ref !== null && ref !== undefined) {\n    (ref as React.MutableRefObject<T>).current = value;\n  }\n}\n\n/**\n * A utility to compose multiple refs together\n * Accepts callback refs and RefObject(s)\n */\nfunction composeRefs<T>(...refs: PossibleRef<T>[]) {\n  return (node: T) => refs.forEach((ref) => setRef(ref, node));\n}\n\n/**\n * A custom hook that composes multiple refs\n * Accepts callback refs and RefObject(s)\n */\nfunction useComposedRefs<T>(...refs: PossibleRef<T>[]) {\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  return React.useCallback(composeRefs(...refs), refs);\n}\n\nexport { composeRefs, useComposedRefs };\n", "import * as React from 'react';\nimport { composeRefs } from '@radix-ui/react-compose-refs';\n\n/* -------------------------------------------------------------------------------------------------\n * Slot\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SlotProps extends React.HTMLAttributes<HTMLElement> {\n  children?: React.ReactNode;\n}\n\nconst Slot = React.forwardRef<HTMLElement, SlotProps>((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n  const childrenArray = React.Children.toArray(children);\n  const slottable = childrenArray.find(isSlottable);\n\n  if (slottable) {\n    // the new element to render is the one passed as a child of `Slottable`\n    const newElement = slottable.props.children as React.ReactNode;\n\n    const newChildren = childrenArray.map((child) => {\n      if (child === slottable) {\n        // because the new element will be the one rendered, we are only interested\n        // in grabbing its children (`newElement.props.children`)\n        if (React.Children.count(newElement) > 1) return React.Children.only(null);\n        return React.isValidElement(newElement)\n          ? (newElement.props.children as React.ReactNode)\n          : null;\n      } else {\n        return child;\n      }\n    });\n\n    return (\n      <SlotClone {...slotProps} ref={forwardedRef}>\n        {React.isValidElement(newElement)\n          ? React.cloneElement(newElement, undefined, newChildren)\n          : null}\n      </SlotClone>\n    );\n  }\n\n  return (\n    <SlotClone {...slotProps} ref={forwardedRef}>\n      {children}\n    </SlotClone>\n  );\n});\n\nSlot.displayName = 'Slot';\n\n/* -------------------------------------------------------------------------------------------------\n * SlotClone\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SlotCloneProps {\n  children: React.ReactNode;\n}\n\nconst SlotClone = React.forwardRef<any, SlotCloneProps>((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n\n  if (React.isValidElement(children)) {\n    return React.cloneElement(children, {\n      ...mergeProps(slotProps, children.props),\n      ref: forwardedRef ? composeRefs(forwardedRef, (children as any).ref) : (children as any).ref,\n    });\n  }\n\n  return React.Children.count(children) > 1 ? React.Children.only(null) : null;\n});\n\nSlotClone.displayName = 'SlotClone';\n\n/* -------------------------------------------------------------------------------------------------\n * Slottable\n * -----------------------------------------------------------------------------------------------*/\n\nconst Slottable = ({ children }: { children: React.ReactNode }) => {\n  return <>{children}</>;\n};\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype AnyProps = Record<string, any>;\n\nfunction isSlottable(child: React.ReactNode): child is React.ReactElement {\n  return React.isValidElement(child) && child.type === Slottable;\n}\n\nfunction mergeProps(slotProps: AnyProps, childProps: AnyProps) {\n  // all child props should override\n  const overrideProps = { ...childProps };\n\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      // if the handler exists on both, we compose them\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args: unknown[]) => {\n          childPropValue(...args);\n          slotPropValue(...args);\n        };\n      }\n      // but if it exists only on the slot, we use only this one\n      else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    }\n    // if it's `style`, we merge them\n    else if (propName === 'style') {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === 'className') {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(' ');\n    }\n  }\n\n  return { ...slotProps, ...overrideProps };\n}\n\nconst Root = Slot;\n\nexport {\n  Slot,\n  Slottable,\n  //\n  Root,\n};\nexport type { SlotProps };\n", "import * as React from 'react';\n\ntype Direction = 'ltr' | 'rtl';\nconst DirectionContext = React.createContext<Direction | undefined>(undefined);\n\n/* -------------------------------------------------------------------------------------------------\n * Direction\n * -----------------------------------------------------------------------------------------------*/\n\ninterface DirectionProviderProps {\n  children?: React.ReactNode;\n  dir: Direction;\n}\nconst DirectionProvider: React.FC<DirectionProviderProps> = (props) => {\n  const { dir, children } = props;\n  return <DirectionContext.Provider value={dir}>{children}</DirectionContext.Provider>;\n};\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction useDirection(localDir?: Direction) {\n  const globalDir = React.useContext(DirectionContext);\n  return localDir || globalDir || 'ltr';\n}\n\nconst Provider = DirectionProvider;\n\nexport {\n  useDirection,\n  //\n  Provider,\n  //\n  DirectionProvider,\n};\n", "import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { Slot } from '@radix-ui/react-slot';\n\nconst NODES = [\n  'a',\n  'button',\n  'div',\n  'form',\n  'h2',\n  'h3',\n  'img',\n  'input',\n  'label',\n  'li',\n  'nav',\n  'ol',\n  'p',\n  'span',\n  'svg',\n  'ul',\n] as const;\n\n// Temporary while we await merge of this fix:\n// https://github.com/DefinitelyTyped/DefinitelyTyped/pull/55396\n// prettier-ignore\ntype PropsWithoutRef<P> = P extends any ? ('ref' extends keyof P ? Pick<P, Exclude<keyof P, 'ref'>> : P) : P;\ntype ComponentPropsWithoutRef<T extends React.ElementType> = PropsWithoutRef<\n  React.ComponentProps<T>\n>;\n\ntype Primitives = { [E in typeof NODES[number]]: PrimitiveForwardRefComponent<E> };\ntype PrimitivePropsWithRef<E extends React.ElementType> = React.ComponentPropsWithRef<E> & {\n  asChild?: boolean;\n};\n\ninterface PrimitiveForwardRefComponent<E extends React.ElementType>\n  extends React.ForwardRefExoticComponent<PrimitivePropsWithRef<E>> {}\n\n/* -------------------------------------------------------------------------------------------------\n * Primitive\n * -----------------------------------------------------------------------------------------------*/\n\nconst Primitive = NODES.reduce((primitive, node) => {\n  const Node = React.forwardRef((props: PrimitivePropsWithRef<typeof node>, forwardedRef: any) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp: any = asChild ? Slot : node;\n\n    React.useEffect(() => {\n      (window as any)[Symbol.for('radix-ui')] = true;\n    }, []);\n\n    return <Comp {...primitiveProps} ref={forwardedRef} />;\n  });\n\n  Node.displayName = `Primitive.${node}`;\n\n  return { ...primitive, [node]: Node };\n}, {} as Primitives);\n\n/* -------------------------------------------------------------------------------------------------\n * Utils\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * Flush custom event dispatch\n * https://github.com/radix-ui/primitives/pull/1378\n *\n * React batches *all* event handlers since version 18, this introduces certain considerations when using custom event types.\n *\n * Internally, React prioritises events in the following order:\n *  - discrete\n *  - continuous\n *  - default\n *\n * https://github.com/facebook/react/blob/a8a4742f1c54493df00da648a3f9d26e3db9c8b5/packages/react-dom/src/events/ReactDOMEventListener.js#L294-L350\n *\n * `discrete` is an  important distinction as updates within these events are applied immediately.\n * React however, is not able to infer the priority of custom event types due to how they are detected internally.\n * Because of this, it's possible for updates from custom events to be unexpectedly batched when\n * dispatched by another `discrete` event.\n *\n * In order to ensure that updates from custom events are applied predictably, we need to manually flush the batch.\n * This utility should be used when dispatching a custom event from within another `discrete` event, this utility\n * is not nessesary when dispatching known event types, or if dispatching a custom type inside a non-discrete event.\n * For example:\n *\n * dispatching a known click 👎\n * target.dispatchEvent(new Event(‘click’))\n *\n * dispatching a custom type within a non-discrete event 👎\n * onScroll={(event) => event.target.dispatchEvent(new CustomEvent(‘customType’))}\n *\n * dispatching a custom type within a `discrete` event 👍\n * onPointerDown={(event) => dispatchDiscreteCustomEvent(event.target, new CustomEvent(‘customType’))}\n *\n * Note: though React classifies `focus`, `focusin` and `focusout` events as `discrete`, it's  not recommended to use\n * this utility with them. This is because it's possible for those handlers to be called implicitly during render\n * e.g. when focus is within a component as it is unmounted, or when managing focus on mount.\n */\n\nfunction dispatchDiscreteCustomEvent<E extends CustomEvent>(target: E['target'], event: E) {\n  if (target) ReactDOM.flushSync(() => target.dispatchEvent(event));\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Primitive;\n\nexport {\n  Primitive,\n  //\n  Root,\n  //\n  dispatchDiscreteCustomEvent,\n};\nexport type { ComponentPropsWithoutRef, PrimitivePropsWithRef };\n", "import * as React from 'react';\n\n/**\n * A custom hook that converts a callback to a ref to avoid triggering re-renders when passed as a\n * prop or avoid re-executing effects when passed as a dependency\n */\nfunction useCallbackRef<T extends (...args: any[]) => any>(callback: T | undefined): T {\n  const callbackRef = React.useRef(callback);\n\n  React.useEffect(() => {\n    callbackRef.current = callback;\n  });\n\n  // https://github.com/facebook/react/issues/19240\n  return React.useMemo(() => ((...args) => callbackRef.current?.(...args)) as T, []);\n}\n\nexport { useCallbackRef };\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { Primitive, dispatchDiscreteCustomEvent } from '@radix-ui/react-primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useEscapeKeydown } from '@radix-ui/react-use-escape-keydown';\n\nimport type * as Radix from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * DismissableLayer\n * -----------------------------------------------------------------------------------------------*/\n\nconst DISMISSABLE_LAYER_NAME = 'DismissableLayer';\nconst CONTEXT_UPDATE = 'dismissableLayer.update';\nconst POINTER_DOWN_OUTSIDE = 'dismissableLayer.pointerDownOutside';\nconst FOCUS_OUTSIDE = 'dismissableLayer.focusOutside';\n\nlet originalBodyPointerEvents: string;\n\nconst DismissableLayerContext = React.createContext({\n  layers: new Set<DismissableLayerElement>(),\n  layersWithOutsidePointerEventsDisabled: new Set<DismissableLayerElement>(),\n  branches: new Set<DismissableLayerBranchElement>(),\n});\n\ntype DismissableLayerElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = Radix.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface DismissableLayerProps extends PrimitiveDivProps {\n  /**\n   * When `true`, hover/focus/click interactions will be disabled on elements outside\n   * the `DismissableLayer`. Users will need to click twice on outside elements to\n   * interact with them: once to close the `DismissableLayer`, and again to trigger the element.\n   */\n  disableOutsidePointerEvents?: boolean;\n  /**\n   * Event handler called when the escape key is down.\n   * Can be prevented.\n   */\n  onEscapeKeyDown?: (event: KeyboardEvent) => void;\n  /**\n   * Event handler called when the a `pointerdown` event happens outside of the `DismissableLayer`.\n   * Can be prevented.\n   */\n  onPointerDownOutside?: (event: PointerDownOutsideEvent) => void;\n  /**\n   * Event handler called when the focus moves outside of the `DismissableLayer`.\n   * Can be prevented.\n   */\n  onFocusOutside?: (event: FocusOutsideEvent) => void;\n  /**\n   * Event handler called when an interaction happens outside the `DismissableLayer`.\n   * Specifically, when a `pointerdown` event happens outside or focus moves outside of it.\n   * Can be prevented.\n   */\n  onInteractOutside?: (event: PointerDownOutsideEvent | FocusOutsideEvent) => void;\n  /**\n   * Handler called when the `DismissableLayer` should be dismissed\n   */\n  onDismiss?: () => void;\n}\n\nconst DismissableLayer = React.forwardRef<DismissableLayerElement, DismissableLayerProps>(\n  (props, forwardedRef) => {\n    const {\n      disableOutsidePointerEvents = false,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      onFocusOutside,\n      onInteractOutside,\n      onDismiss,\n      ...layerProps\n    } = props;\n    const context = React.useContext(DismissableLayerContext);\n    const [node, setNode] = React.useState<DismissableLayerElement | null>(null);\n    const ownerDocument = node?.ownerDocument ?? globalThis?.document;\n    const [, force] = React.useState({});\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setNode(node));\n    const layers = Array.from(context.layers);\n    const [highestLayerWithOutsidePointerEventsDisabled] = [...context.layersWithOutsidePointerEventsDisabled].slice(-1); // prettier-ignore\n    const highestLayerWithOutsidePointerEventsDisabledIndex = layers.indexOf(highestLayerWithOutsidePointerEventsDisabled); // prettier-ignore\n    const index = node ? layers.indexOf(node) : -1;\n    const isBodyPointerEventsDisabled = context.layersWithOutsidePointerEventsDisabled.size > 0;\n    const isPointerEventsEnabled = index >= highestLayerWithOutsidePointerEventsDisabledIndex;\n\n    const pointerDownOutside = usePointerDownOutside((event) => {\n      const target = event.target as HTMLElement;\n      const isPointerDownOnBranch = [...context.branches].some((branch) => branch.contains(target));\n      if (!isPointerEventsEnabled || isPointerDownOnBranch) return;\n      onPointerDownOutside?.(event);\n      onInteractOutside?.(event);\n      if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n\n    const focusOutside = useFocusOutside((event) => {\n      const target = event.target as HTMLElement;\n      const isFocusInBranch = [...context.branches].some((branch) => branch.contains(target));\n      if (isFocusInBranch) return;\n      onFocusOutside?.(event);\n      onInteractOutside?.(event);\n      if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n\n    useEscapeKeydown((event) => {\n      const isHighestLayer = index === context.layers.size - 1;\n      if (!isHighestLayer) return;\n      onEscapeKeyDown?.(event);\n      if (!event.defaultPrevented && onDismiss) {\n        event.preventDefault();\n        onDismiss();\n      }\n    }, ownerDocument);\n\n    React.useEffect(() => {\n      if (!node) return;\n      if (disableOutsidePointerEvents) {\n        if (context.layersWithOutsidePointerEventsDisabled.size === 0) {\n          originalBodyPointerEvents = ownerDocument.body.style.pointerEvents;\n          ownerDocument.body.style.pointerEvents = 'none';\n        }\n        context.layersWithOutsidePointerEventsDisabled.add(node);\n      }\n      context.layers.add(node);\n      dispatchUpdate();\n      return () => {\n        if (\n          disableOutsidePointerEvents &&\n          context.layersWithOutsidePointerEventsDisabled.size === 1\n        ) {\n          ownerDocument.body.style.pointerEvents = originalBodyPointerEvents;\n        }\n      };\n    }, [node, ownerDocument, disableOutsidePointerEvents, context]);\n\n    /**\n     * We purposefully prevent combining this effect with the `disableOutsidePointerEvents` effect\n     * because a change to `disableOutsidePointerEvents` would remove this layer from the stack\n     * and add it to the end again so the layering order wouldn't be _creation order_.\n     * We only want them to be removed from context stacks when unmounted.\n     */\n    React.useEffect(() => {\n      return () => {\n        if (!node) return;\n        context.layers.delete(node);\n        context.layersWithOutsidePointerEventsDisabled.delete(node);\n        dispatchUpdate();\n      };\n    }, [node, context]);\n\n    React.useEffect(() => {\n      const handleUpdate = () => force({});\n      document.addEventListener(CONTEXT_UPDATE, handleUpdate);\n      return () => document.removeEventListener(CONTEXT_UPDATE, handleUpdate);\n    }, []);\n\n    return (\n      <Primitive.div\n        {...layerProps}\n        ref={composedRefs}\n        style={{\n          pointerEvents: isBodyPointerEventsDisabled\n            ? isPointerEventsEnabled\n              ? 'auto'\n              : 'none'\n            : undefined,\n          ...props.style,\n        }}\n        onFocusCapture={composeEventHandlers(props.onFocusCapture, focusOutside.onFocusCapture)}\n        onBlurCapture={composeEventHandlers(props.onBlurCapture, focusOutside.onBlurCapture)}\n        onPointerDownCapture={composeEventHandlers(\n          props.onPointerDownCapture,\n          pointerDownOutside.onPointerDownCapture\n        )}\n      />\n    );\n  }\n);\n\nDismissableLayer.displayName = DISMISSABLE_LAYER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DismissableLayerBranch\n * -----------------------------------------------------------------------------------------------*/\n\nconst BRANCH_NAME = 'DismissableLayerBranch';\n\ntype DismissableLayerBranchElement = React.ElementRef<typeof Primitive.div>;\ninterface DismissableLayerBranchProps extends PrimitiveDivProps {}\n\nconst DismissableLayerBranch = React.forwardRef<\n  DismissableLayerBranchElement,\n  DismissableLayerBranchProps\n>((props, forwardedRef) => {\n  const context = React.useContext(DismissableLayerContext);\n  const ref = React.useRef<DismissableLayerBranchElement>(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n\n  React.useEffect(() => {\n    const node = ref.current;\n    if (node) {\n      context.branches.add(node);\n      return () => {\n        context.branches.delete(node);\n      };\n    }\n  }, [context.branches]);\n\n  return <Primitive.div {...props} ref={composedRefs} />;\n});\n\nDismissableLayerBranch.displayName = BRANCH_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype PointerDownOutsideEvent = CustomEvent<{ originalEvent: PointerEvent }>;\ntype FocusOutsideEvent = CustomEvent<{ originalEvent: FocusEvent }>;\n\n/**\n * Listens for `pointerdown` outside a react subtree. We use `pointerdown` rather than `pointerup`\n * to mimic layer dismissing behaviour present in OS.\n * Returns props to pass to the node we want to check for outside events.\n */\nfunction usePointerDownOutside(\n  onPointerDownOutside?: (event: PointerDownOutsideEvent) => void,\n  ownerDocument: Document = globalThis?.document\n) {\n  const handlePointerDownOutside = useCallbackRef(onPointerDownOutside) as EventListener;\n  const isPointerInsideReactTreeRef = React.useRef(false);\n  const handleClickRef = React.useRef(() => {});\n\n  React.useEffect(() => {\n    const handlePointerDown = (event: PointerEvent) => {\n      if (event.target && !isPointerInsideReactTreeRef.current) {\n        const eventDetail = { originalEvent: event };\n\n        function handleAndDispatchPointerDownOutsideEvent() {\n          handleAndDispatchCustomEvent(\n            POINTER_DOWN_OUTSIDE,\n            handlePointerDownOutside,\n            eventDetail,\n            { discrete: true }\n          );\n        }\n\n        /**\n         * On touch devices, we need to wait for a click event because browsers implement\n         * a ~350ms delay between the time the user stops touching the display and when the\n         * browser executres events. We need to ensure we don't reactivate pointer-events within\n         * this timeframe otherwise the browser may execute events that should have been prevented.\n         *\n         * Additionally, this also lets us deal automatically with cancellations when a click event\n         * isn't raised because the page was considered scrolled/drag-scrolled, long-pressed, etc.\n         *\n         * This is why we also continuously remove the previous listener, because we cannot be\n         * certain that it was raised, and therefore cleaned-up.\n         */\n        if (event.pointerType === 'touch') {\n          ownerDocument.removeEventListener('click', handleClickRef.current);\n          handleClickRef.current = handleAndDispatchPointerDownOutsideEvent;\n          ownerDocument.addEventListener('click', handleClickRef.current, { once: true });\n        } else {\n          handleAndDispatchPointerDownOutsideEvent();\n        }\n      }\n      isPointerInsideReactTreeRef.current = false;\n    };\n    /**\n     * if this hook executes in a component that mounts via a `pointerdown` event, the event\n     * would bubble up to the document and trigger a `pointerDownOutside` event. We avoid\n     * this by delaying the event listener registration on the document.\n     * This is not React specific, but rather how the DOM works, ie:\n     * ```\n     * button.addEventListener('pointerdown', () => {\n     *   console.log('I will log');\n     *   document.addEventListener('pointerdown', () => {\n     *     console.log('I will also log');\n     *   })\n     * });\n     */\n    const timerId = window.setTimeout(() => {\n      ownerDocument.addEventListener('pointerdown', handlePointerDown);\n    }, 0);\n    return () => {\n      window.clearTimeout(timerId);\n      ownerDocument.removeEventListener('pointerdown', handlePointerDown);\n      ownerDocument.removeEventListener('click', handleClickRef.current);\n    };\n  }, [ownerDocument, handlePointerDownOutside]);\n\n  return {\n    // ensures we check React component tree (not just DOM tree)\n    onPointerDownCapture: () => (isPointerInsideReactTreeRef.current = true),\n  };\n}\n\n/**\n * Listens for when focus happens outside a react subtree.\n * Returns props to pass to the root (node) of the subtree we want to check.\n */\nfunction useFocusOutside(\n  onFocusOutside?: (event: FocusOutsideEvent) => void,\n  ownerDocument: Document = globalThis?.document\n) {\n  const handleFocusOutside = useCallbackRef(onFocusOutside) as EventListener;\n  const isFocusInsideReactTreeRef = React.useRef(false);\n\n  React.useEffect(() => {\n    const handleFocus = (event: FocusEvent) => {\n      if (event.target && !isFocusInsideReactTreeRef.current) {\n        const eventDetail = { originalEvent: event };\n        handleAndDispatchCustomEvent(FOCUS_OUTSIDE, handleFocusOutside, eventDetail, {\n          discrete: false,\n        });\n      }\n    };\n    ownerDocument.addEventListener('focusin', handleFocus);\n    return () => ownerDocument.removeEventListener('focusin', handleFocus);\n  }, [ownerDocument, handleFocusOutside]);\n\n  return {\n    onFocusCapture: () => (isFocusInsideReactTreeRef.current = true),\n    onBlurCapture: () => (isFocusInsideReactTreeRef.current = false),\n  };\n}\n\nfunction dispatchUpdate() {\n  const event = new CustomEvent(CONTEXT_UPDATE);\n  document.dispatchEvent(event);\n}\n\nfunction handleAndDispatchCustomEvent<E extends CustomEvent, OriginalEvent extends Event>(\n  name: string,\n  handler: ((event: E) => void) | undefined,\n  detail: { originalEvent: OriginalEvent } & (E extends CustomEvent<infer D> ? D : never),\n  { discrete }: { discrete: boolean }\n) {\n  const target = detail.originalEvent.target;\n  const event = new CustomEvent(name, { bubbles: false, cancelable: true, detail });\n  if (handler) target.addEventListener(name, handler as EventListener, { once: true });\n\n  if (discrete) {\n    dispatchDiscreteCustomEvent(target, event);\n  } else {\n    target.dispatchEvent(event);\n  }\n}\n\nconst Root = DismissableLayer;\nconst Branch = DismissableLayerBranch;\n\nexport {\n  DismissableLayer,\n  DismissableLayerBranch,\n  //\n  Root,\n  Branch,\n};\nexport type { DismissableLayerProps };\n", "import * as React from 'react';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\n\n/**\n * Listens for when the escape key is down\n */\nfunction useEscapeKeydown(\n  onEscapeKeyDownProp?: (event: KeyboardEvent) => void,\n  ownerDocument: Document = globalThis?.document\n) {\n  const onEscapeKeyDown = useCallbackRef(onEscapeKeyDownProp);\n\n  React.useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (event.key === 'Escape') {\n        onEscapeKeyDown(event);\n      }\n    };\n    ownerDocument.addEventListener('keydown', handleKeyDown);\n    return () => ownerDocument.removeEventListener('keydown', handleKeyDown);\n  }, [onEscapeKeyDown, ownerDocument]);\n}\n\nexport { useEscapeKeydown };\n", "import * as React from 'react';\n\n/** Number of components which have requested interest to have focus guards */\nlet count = 0;\n\nfunction FocusGuards(props: any) {\n  useFocusGuards();\n  return props.children;\n}\n\n/**\n * Injects a pair of focus guards at the edges of the whole DOM tree\n * to ensure `focusin` & `focusout` events can be caught consistently.\n */\nfunction useFocusGuards() {\n  React.useEffect(() => {\n    const edgeGuards = document.querySelectorAll('[data-radix-focus-guard]');\n    document.body.insertAdjacentElement('afterbegin', edgeGuards[0] ?? createFocusGuard());\n    document.body.insertAdjacentElement('beforeend', edgeGuards[1] ?? createFocusGuard());\n    count++;\n\n    return () => {\n      if (count === 1) {\n        document.querySelectorAll('[data-radix-focus-guard]').forEach((node) => node.remove());\n      }\n      count--;\n    };\n  }, []);\n}\n\nfunction createFocusGuard() {\n  const element = document.createElement('span');\n  element.setAttribute('data-radix-focus-guard', '');\n  element.tabIndex = 0;\n  element.style.cssText = 'outline: none; opacity: 0; position: fixed; pointer-events: none';\n  return element;\n}\n\nconst Root = FocusGuards;\n\nexport {\n  FocusGuards,\n  //\n  Root,\n  //\n  useFocusGuards,\n};\n", "import * as React from 'react';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\n\nimport type * as Radix from '@radix-ui/react-primitive';\n\nconst AUTOFOCUS_ON_MOUNT = 'focusScope.autoFocusOnMount';\nconst AUTOFOCUS_ON_UNMOUNT = 'focusScope.autoFocusOnUnmount';\nconst EVENT_OPTIONS = { bubbles: false, cancelable: true };\n\ntype FocusableTarget = HTMLElement | { focus(): void };\n\n/* -------------------------------------------------------------------------------------------------\n * FocusScope\n * -----------------------------------------------------------------------------------------------*/\n\nconst FOCUS_SCOPE_NAME = 'FocusScope';\n\ntype FocusScopeElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = Radix.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface FocusScopeProps extends PrimitiveDivProps {\n  /**\n   * When `true`, tabbing from last item will focus first tabbable\n   * and shift+tab from first item will focus last tababble.\n   * @defaultValue false\n   */\n  loop?: boolean;\n\n  /**\n   * When `true`, focus cannot escape the focus scope via keyboard,\n   * pointer, or a programmatic focus.\n   * @defaultValue false\n   */\n  trapped?: boolean;\n\n  /**\n   * Event handler called when auto-focusing on mount.\n   * Can be prevented.\n   */\n  onMountAutoFocus?: (event: Event) => void;\n\n  /**\n   * Event handler called when auto-focusing on unmount.\n   * Can be prevented.\n   */\n  onUnmountAutoFocus?: (event: Event) => void;\n}\n\nconst FocusScope = React.forwardRef<FocusScopeElement, FocusScopeProps>((props, forwardedRef) => {\n  const {\n    loop = false,\n    trapped = false,\n    onMountAutoFocus: onMountAutoFocusProp,\n    onUnmountAutoFocus: onUnmountAutoFocusProp,\n    ...scopeProps\n  } = props;\n  const [container, setContainer] = React.useState<HTMLElement | null>(null);\n  const onMountAutoFocus = useCallbackRef(onMountAutoFocusProp);\n  const onUnmountAutoFocus = useCallbackRef(onUnmountAutoFocusProp);\n  const lastFocusedElementRef = React.useRef<HTMLElement | null>(null);\n  const composedRefs = useComposedRefs(forwardedRef, (node) => setContainer(node));\n\n  const focusScope = React.useRef({\n    paused: false,\n    pause() {\n      this.paused = true;\n    },\n    resume() {\n      this.paused = false;\n    },\n  }).current;\n\n  // Takes care of trapping focus if focus is moved outside programmatically for example\n  React.useEffect(() => {\n    if (trapped) {\n      function handleFocusIn(event: FocusEvent) {\n        if (focusScope.paused || !container) return;\n        const target = event.target as HTMLElement | null;\n        if (container.contains(target)) {\n          lastFocusedElementRef.current = target;\n        } else {\n          focus(lastFocusedElementRef.current, { select: true });\n        }\n      }\n\n      function handleFocusOut(event: FocusEvent) {\n        if (focusScope.paused || !container) return;\n        const relatedTarget = event.relatedTarget as HTMLElement | null;\n\n        // A `focusout` event with a `null` `relatedTarget` will happen in at least two cases:\n        //\n        // 1. When the user switches app/tabs/windows/the browser itself loses focus.\n        // 2. In Google Chrome, when the focused element is removed from the DOM.\n        //\n        // We let the browser do its thing here because:\n        //\n        // 1. The browser already keeps a memory of what's focused for when the page gets refocused.\n        // 2. In Google Chrome, if we try to focus the deleted focused element (as per below), it\n        //    throws the CPU to 100%, so we avoid doing anything for this reason here too.\n        if (relatedTarget === null) return;\n\n        // If the focus has moved to an actual legitimate element (`relatedTarget !== null`)\n        // that is outside the container, we move focus to the last valid focused element inside.\n        if (!container.contains(relatedTarget)) {\n          focus(lastFocusedElementRef.current, { select: true });\n        }\n      }\n\n      // When the focused element gets removed from the DOM, browsers move focus\n      // back to the document.body. In this case, we move focus to the container\n      // to keep focus trapped correctly.\n      function handleMutations(mutations: MutationRecord[]) {\n        const focusedElement = document.activeElement as HTMLElement | null;\n        for (const mutation of mutations) {\n          if (mutation.removedNodes.length > 0) {\n            if (!container?.contains(focusedElement)) focus(container);\n          }\n        }\n      }\n\n      document.addEventListener('focusin', handleFocusIn);\n      document.addEventListener('focusout', handleFocusOut);\n      const mutationObserver = new MutationObserver(handleMutations);\n      if (container) mutationObserver.observe(container, { childList: true, subtree: true });\n\n      return () => {\n        document.removeEventListener('focusin', handleFocusIn);\n        document.removeEventListener('focusout', handleFocusOut);\n        mutationObserver.disconnect();\n      };\n    }\n  }, [trapped, container, focusScope.paused]);\n\n  React.useEffect(() => {\n    if (container) {\n      focusScopesStack.add(focusScope);\n      const previouslyFocusedElement = document.activeElement as HTMLElement | null;\n      const hasFocusedCandidate = container.contains(previouslyFocusedElement);\n\n      if (!hasFocusedCandidate) {\n        const mountEvent = new CustomEvent(AUTOFOCUS_ON_MOUNT, EVENT_OPTIONS);\n        container.addEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n        container.dispatchEvent(mountEvent);\n        if (!mountEvent.defaultPrevented) {\n          focusFirst(removeLinks(getTabbableCandidates(container)), { select: true });\n          if (document.activeElement === previouslyFocusedElement) {\n            focus(container);\n          }\n        }\n      }\n\n      return () => {\n        container.removeEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n\n        // We hit a react bug (fixed in v17) with focusing in unmount.\n        // We need to delay the focus a little to get around it for now.\n        // See: https://github.com/facebook/react/issues/17894\n        setTimeout(() => {\n          const unmountEvent = new CustomEvent(AUTOFOCUS_ON_UNMOUNT, EVENT_OPTIONS);\n          container.addEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n          container.dispatchEvent(unmountEvent);\n          if (!unmountEvent.defaultPrevented) {\n            focus(previouslyFocusedElement ?? document.body, { select: true });\n          }\n          // we need to remove the listener after we `dispatchEvent`\n          container.removeEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n\n          focusScopesStack.remove(focusScope);\n        }, 0);\n      };\n    }\n  }, [container, onMountAutoFocus, onUnmountAutoFocus, focusScope]);\n\n  // Takes care of looping focus (when tabbing whilst at the edges)\n  const handleKeyDown = React.useCallback(\n    (event: React.KeyboardEvent) => {\n      if (!loop && !trapped) return;\n      if (focusScope.paused) return;\n\n      const isTabKey = event.key === 'Tab' && !event.altKey && !event.ctrlKey && !event.metaKey;\n      const focusedElement = document.activeElement as HTMLElement | null;\n\n      if (isTabKey && focusedElement) {\n        const container = event.currentTarget as HTMLElement;\n        const [first, last] = getTabbableEdges(container);\n        const hasTabbableElementsInside = first && last;\n\n        // we can only wrap focus if we have tabbable edges\n        if (!hasTabbableElementsInside) {\n          if (focusedElement === container) event.preventDefault();\n        } else {\n          if (!event.shiftKey && focusedElement === last) {\n            event.preventDefault();\n            if (loop) focus(first, { select: true });\n          } else if (event.shiftKey && focusedElement === first) {\n            event.preventDefault();\n            if (loop) focus(last, { select: true });\n          }\n        }\n      }\n    },\n    [loop, trapped, focusScope.paused]\n  );\n\n  return (\n    <Primitive.div tabIndex={-1} {...scopeProps} ref={composedRefs} onKeyDown={handleKeyDown} />\n  );\n});\n\nFocusScope.displayName = FOCUS_SCOPE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * Utils\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * Attempts focusing the first element in a list of candidates.\n * Stops when focus has actually moved.\n */\nfunction focusFirst(candidates: HTMLElement[], { select = false } = {}) {\n  const previouslyFocusedElement = document.activeElement;\n  for (const candidate of candidates) {\n    focus(candidate, { select });\n    if (document.activeElement !== previouslyFocusedElement) return;\n  }\n}\n\n/**\n * Returns the first and last tabbable elements inside a container.\n */\nfunction getTabbableEdges(container: HTMLElement) {\n  const candidates = getTabbableCandidates(container);\n  const first = findVisible(candidates, container);\n  const last = findVisible(candidates.reverse(), container);\n  return [first, last] as const;\n}\n\n/**\n * Returns a list of potential tabbable candidates.\n *\n * NOTE: This is only a close approximation. For example it doesn't take into account cases like when\n * elements are not visible. This cannot be worked out easily by just reading a property, but rather\n * necessitate runtime knowledge (computed styles, etc). We deal with these cases separately.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/TreeWalker\n * Credit: https://github.com/discord/focus-layers/blob/master/src/util/wrapFocus.tsx#L1\n */\nfunction getTabbableCandidates(container: HTMLElement) {\n  const nodes: HTMLElement[] = [];\n  const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n    acceptNode: (node: any) => {\n      const isHiddenInput = node.tagName === 'INPUT' && node.type === 'hidden';\n      if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n      // `.tabIndex` is not the same as the `tabindex` attribute. It works on the\n      // runtime's understanding of tabbability, so this automatically accounts\n      // for any kind of element that could be tabbed to.\n      return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n    },\n  });\n  while (walker.nextNode()) nodes.push(walker.currentNode as HTMLElement);\n  // we do not take into account the order of nodes with positive `tabIndex` as it\n  // hinders accessibility to have tab order different from visual order.\n  return nodes;\n}\n\n/**\n * Returns the first visible element in a list.\n * NOTE: Only checks visibility up to the `container`.\n */\nfunction findVisible(elements: HTMLElement[], container: HTMLElement) {\n  for (const element of elements) {\n    // we stop checking if it's hidden at the `container` level (excluding)\n    if (!isHidden(element, { upTo: container })) return element;\n  }\n}\n\nfunction isHidden(node: HTMLElement, { upTo }: { upTo?: HTMLElement }) {\n  if (getComputedStyle(node).visibility === 'hidden') return true;\n  while (node) {\n    // we stop at `upTo` (excluding it)\n    if (upTo !== undefined && node === upTo) return false;\n    if (getComputedStyle(node).display === 'none') return true;\n    node = node.parentElement as HTMLElement;\n  }\n  return false;\n}\n\nfunction isSelectableInput(element: any): element is FocusableTarget & { select: () => void } {\n  return element instanceof HTMLInputElement && 'select' in element;\n}\n\nfunction focus(element?: FocusableTarget | null, { select = false } = {}) {\n  // only focus if that element is focusable\n  if (element && element.focus) {\n    const previouslyFocusedElement = document.activeElement;\n    // NOTE: we prevent scrolling on focus, to minimize jarring transitions for users\n    element.focus({ preventScroll: true });\n    // only select if its not the same element, it supports selection and we need to select\n    if (element !== previouslyFocusedElement && isSelectableInput(element) && select)\n      element.select();\n  }\n}\n\n/* -------------------------------------------------------------------------------------------------\n * FocusScope stack\n * -----------------------------------------------------------------------------------------------*/\n\ntype FocusScopeAPI = { paused: boolean; pause(): void; resume(): void };\nconst focusScopesStack = createFocusScopesStack();\n\nfunction createFocusScopesStack() {\n  /** A stack of focus scopes, with the active one at the top */\n  let stack: FocusScopeAPI[] = [];\n\n  return {\n    add(focusScope: FocusScopeAPI) {\n      // pause the currently active focus scope (at the top of the stack)\n      const activeFocusScope = stack[0];\n      if (focusScope !== activeFocusScope) {\n        activeFocusScope?.pause();\n      }\n      // remove in case it already exists (because we'll re-add it at the top of the stack)\n      stack = arrayRemove(stack, focusScope);\n      stack.unshift(focusScope);\n    },\n\n    remove(focusScope: FocusScopeAPI) {\n      stack = arrayRemove(stack, focusScope);\n      stack[0]?.resume();\n    },\n  };\n}\n\nfunction arrayRemove<T>(array: T[], item: T) {\n  const updatedArray = [...array];\n  const index = updatedArray.indexOf(item);\n  if (index !== -1) {\n    updatedArray.splice(index, 1);\n  }\n  return updatedArray;\n}\n\nfunction removeLinks(items: HTMLElement[]) {\n  return items.filter((item) => item.tagName !== 'A');\n}\n\nconst Root = FocusScope;\n\nexport {\n  FocusScope,\n  //\n  Root,\n};\nexport type { FocusScopeProps };\n", "import * as React from 'react';\n\n/**\n * On the server, <PERSON>act emits a warning when calling `useLayoutEffect`.\n * This is because neither `useLayoutEffect` nor `useEffect` run on the server.\n * We use this safe version which suppresses the warning by replacing it with a noop on the server.\n *\n * See: https://reactjs.org/docs/hooks-reference.html#uselayouteffect\n */\nconst useLayoutEffect = Boolean(globalThis?.document) ? React.useLayoutEffect : () => {};\n\nexport { useLayoutEffect };\n", "import * as React from 'react';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\n\n// We `toString()` to prevent bundlers from trying to `import { useId } from 'react';`\nconst useReactId = (React as any)['useId'.toString()] || (() => undefined);\nlet count = 0;\n\nfunction useId(deterministicId?: string): string {\n  const [id, setId] = React.useState<string | undefined>(useReactId());\n  // React versions older than 18 will have client-side ids only.\n  useLayoutEffect(() => {\n    if (!deterministicId) setId((reactId) => reactId ?? String(count++));\n  }, [deterministicId]);\n  return deterministicId || (id ? `radix-${id}` : '');\n}\n\nexport { useId };\n", "import * as React from 'react';\nimport {\n  useFloating,\n  autoUpdate,\n  offset,\n  shift,\n  limitShift,\n  hide,\n  arrow as floatingUIarrow,\n  flip,\n  size,\n} from '@floating-ui/react-dom';\nimport * as ArrowPrimitive from '@radix-ui/react-arrow';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { useSize } from '@radix-ui/react-use-size';\n\nimport type { Placement, Middleware } from '@floating-ui/react-dom';\nimport type * as Radix from '@radix-ui/react-primitive';\nimport type { Scope } from '@radix-ui/react-context';\nimport type { Measurable } from '@radix-ui/rect';\n\nconst SIDE_OPTIONS = ['top', 'right', 'bottom', 'left'] as const;\nconst ALIGN_OPTIONS = ['start', 'center', 'end'] as const;\n\ntype Side = typeof SIDE_OPTIONS[number];\ntype Align = typeof ALIGN_OPTIONS[number];\n\n/* -------------------------------------------------------------------------------------------------\n * Popper\n * -----------------------------------------------------------------------------------------------*/\n\nconst POPPER_NAME = 'Popper';\n\ntype ScopedProps<P> = P & { __scopePopper?: Scope };\nconst [createPopperContext, createPopperScope] = createContextScope(POPPER_NAME);\n\ntype PopperContextValue = {\n  anchor: Measurable | null;\n  onAnchorChange(anchor: Measurable | null): void;\n};\nconst [PopperProvider, usePopperContext] = createPopperContext<PopperContextValue>(POPPER_NAME);\n\ninterface PopperProps {\n  children?: React.ReactNode;\n}\nconst Popper: React.FC<PopperProps> = (props: ScopedProps<PopperProps>) => {\n  const { __scopePopper, children } = props;\n  const [anchor, setAnchor] = React.useState<Measurable | null>(null);\n  return (\n    <PopperProvider scope={__scopePopper} anchor={anchor} onAnchorChange={setAnchor}>\n      {children}\n    </PopperProvider>\n  );\n};\n\nPopper.displayName = POPPER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopperAnchor\n * -----------------------------------------------------------------------------------------------*/\n\nconst ANCHOR_NAME = 'PopperAnchor';\n\ntype PopperAnchorElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = Radix.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface PopperAnchorProps extends PrimitiveDivProps {\n  virtualRef?: React.RefObject<Measurable>;\n}\n\nconst PopperAnchor = React.forwardRef<PopperAnchorElement, PopperAnchorProps>(\n  (props: ScopedProps<PopperAnchorProps>, forwardedRef) => {\n    const { __scopePopper, virtualRef, ...anchorProps } = props;\n    const context = usePopperContext(ANCHOR_NAME, __scopePopper);\n    const ref = React.useRef<PopperAnchorElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n\n    React.useEffect(() => {\n      // Consumer can anchor the popper to something that isn't\n      // a DOM node e.g. pointer position, so we override the\n      // `anchorRef` with their virtual ref in this case.\n      context.onAnchorChange(virtualRef?.current || ref.current);\n    });\n\n    return virtualRef ? null : <Primitive.div {...anchorProps} ref={composedRefs} />;\n  }\n);\n\nPopperAnchor.displayName = ANCHOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopperContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'PopperContent';\n\ntype PopperContentContextValue = {\n  placedSide: Side;\n  onArrowChange(arrow: HTMLSpanElement | null): void;\n  arrowX?: number;\n  arrowY?: number;\n  shouldHideArrow: boolean;\n};\n\nconst [PopperContentProvider, useContentContext] =\n  createPopperContext<PopperContentContextValue>(CONTENT_NAME);\n\ntype Boundary = Element | null;\n\ntype PopperContentElement = React.ElementRef<typeof Primitive.div>;\ninterface PopperContentProps extends PrimitiveDivProps {\n  side?: Side;\n  sideOffset?: number;\n  align?: Align;\n  alignOffset?: number;\n  arrowPadding?: number;\n  collisionBoundary?: Boundary | Boundary[];\n  collisionPadding?: number | Partial<Record<Side, number>>;\n  sticky?: 'partial' | 'always';\n  hideWhenDetached?: boolean;\n  avoidCollisions?: boolean;\n  onPlaced?: () => void;\n}\n\nconst PopperContent = React.forwardRef<PopperContentElement, PopperContentProps>(\n  (props: ScopedProps<PopperContentProps>, forwardedRef) => {\n    const {\n      __scopePopper,\n      side = 'bottom',\n      sideOffset = 0,\n      align = 'center',\n      alignOffset = 0,\n      arrowPadding = 0,\n      collisionBoundary = [],\n      collisionPadding: collisionPaddingProp = 0,\n      sticky = 'partial',\n      hideWhenDetached = false,\n      avoidCollisions = true,\n      onPlaced,\n      ...contentProps\n    } = props;\n\n    const context = usePopperContext(CONTENT_NAME, __scopePopper);\n\n    const [content, setContent] = React.useState<HTMLDivElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n\n    const [arrow, setArrow] = React.useState<HTMLSpanElement | null>(null);\n    const arrowSize = useSize(arrow);\n    const arrowWidth = arrowSize?.width ?? 0;\n    const arrowHeight = arrowSize?.height ?? 0;\n\n    const desiredPlacement = (side + (align !== 'center' ? '-' + align : '')) as Placement;\n\n    const collisionPadding =\n      typeof collisionPaddingProp === 'number'\n        ? collisionPaddingProp\n        : { top: 0, right: 0, bottom: 0, left: 0, ...collisionPaddingProp };\n\n    const boundary = Array.isArray(collisionBoundary) ? collisionBoundary : [collisionBoundary];\n    const hasExplicitBoundaries = boundary.length > 0;\n\n    const detectOverflowOptions = {\n      padding: collisionPadding,\n      boundary: boundary.filter(isNotNull),\n      // with `strategy: 'fixed'`, this is the only way to get it to respect boundaries\n      altBoundary: hasExplicitBoundaries,\n    };\n\n    const { refs, floatingStyles, placement, isPositioned, middlewareData } = useFloating({\n      // default to `fixed` strategy so users don't have to pick and we also avoid focus scroll issues\n      strategy: 'fixed',\n      placement: desiredPlacement,\n      whileElementsMounted: autoUpdate,\n      elements: {\n        reference: context.anchor,\n      },\n      middleware: [\n        offset({ mainAxis: sideOffset + arrowHeight, alignmentAxis: alignOffset }),\n        avoidCollisions &&\n          shift({\n            mainAxis: true,\n            crossAxis: false,\n            limiter: sticky === 'partial' ? limitShift() : undefined,\n            ...detectOverflowOptions,\n          }),\n        avoidCollisions && flip({ ...detectOverflowOptions }),\n        size({\n          ...detectOverflowOptions,\n          apply: ({ elements, rects, availableWidth, availableHeight }) => {\n            const { width: anchorWidth, height: anchorHeight } = rects.reference;\n            const contentStyle = elements.floating.style;\n            contentStyle.setProperty('--radix-popper-available-width', `${availableWidth}px`);\n            contentStyle.setProperty('--radix-popper-available-height', `${availableHeight}px`);\n            contentStyle.setProperty('--radix-popper-anchor-width', `${anchorWidth}px`);\n            contentStyle.setProperty('--radix-popper-anchor-height', `${anchorHeight}px`);\n          },\n        }),\n        arrow && floatingUIarrow({ element: arrow, padding: arrowPadding }),\n        transformOrigin({ arrowWidth, arrowHeight }),\n        hideWhenDetached && hide({ strategy: 'referenceHidden' }),\n      ],\n    });\n\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n\n    const handlePlaced = useCallbackRef(onPlaced);\n    useLayoutEffect(() => {\n      if (isPositioned) {\n        handlePlaced?.();\n      }\n    }, [isPositioned, handlePlaced]);\n\n    const arrowX = middlewareData.arrow?.x;\n    const arrowY = middlewareData.arrow?.y;\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n\n    const [contentZIndex, setContentZIndex] = React.useState<string>();\n    useLayoutEffect(() => {\n      if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n    }, [content]);\n\n    return (\n      <div\n        ref={refs.setFloating}\n        data-radix-popper-content-wrapper=\"\"\n        style={{\n          ...floatingStyles,\n          transform: isPositioned ? floatingStyles.transform : 'translate(0, -200%)', // keep off the page when measuring\n          minWidth: 'max-content',\n          zIndex: contentZIndex,\n          ['--radix-popper-transform-origin' as any]: [\n            middlewareData.transformOrigin?.x,\n            middlewareData.transformOrigin?.y,\n          ].join(' '),\n        }}\n        // Floating UI interally calculates logical alignment based the `dir` attribute on\n        // the reference/floating node, we must add this attribute here to ensure\n        // this is calculated when portalled as well as inline.\n        dir={props.dir}\n      >\n        <PopperContentProvider\n          scope={__scopePopper}\n          placedSide={placedSide}\n          onArrowChange={setArrow}\n          arrowX={arrowX}\n          arrowY={arrowY}\n          shouldHideArrow={cannotCenterArrow}\n        >\n          <Primitive.div\n            data-side={placedSide}\n            data-align={placedAlign}\n            {...contentProps}\n            ref={composedRefs}\n            style={{\n              ...contentProps.style,\n              // if the PopperContent hasn't been placed yet (not all measurements done)\n              // we prevent animations so that users's animation don't kick in too early referring wrong sides\n              animation: !isPositioned ? 'none' : undefined,\n              // hide the content if using the hide middleware and should be hidden\n              opacity: middlewareData.hide?.referenceHidden ? 0 : undefined,\n            }}\n          />\n        </PopperContentProvider>\n      </div>\n    );\n  }\n);\n\nPopperContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopperArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'PopperArrow';\n\nconst OPPOSITE_SIDE: Record<Side, Side> = {\n  top: 'bottom',\n  right: 'left',\n  bottom: 'top',\n  left: 'right',\n};\n\ntype PopperArrowElement = React.ElementRef<typeof ArrowPrimitive.Root>;\ntype ArrowProps = Radix.ComponentPropsWithoutRef<typeof ArrowPrimitive.Root>;\ninterface PopperArrowProps extends ArrowProps {}\n\nconst PopperArrow = React.forwardRef<PopperArrowElement, PopperArrowProps>(function PopperArrow(\n  props: ScopedProps<PopperArrowProps>,\n  forwardedRef\n) {\n  const { __scopePopper, ...arrowProps } = props;\n  const contentContext = useContentContext(ARROW_NAME, __scopePopper);\n  const baseSide = OPPOSITE_SIDE[contentContext.placedSide];\n\n  return (\n    // we have to use an extra wrapper because `ResizeObserver` (used by `useSize`)\n    // doesn't report size as we'd expect on SVG elements.\n    // it reports their bounding box which is effectively the largest path inside the SVG.\n    <span\n      ref={contentContext.onArrowChange}\n      style={{\n        position: 'absolute',\n        left: contentContext.arrowX,\n        top: contentContext.arrowY,\n        [baseSide]: 0,\n        transformOrigin: {\n          top: '',\n          right: '0 0',\n          bottom: 'center 0',\n          left: '100% 0',\n        }[contentContext.placedSide],\n        transform: {\n          top: 'translateY(100%)',\n          right: 'translateY(50%) rotate(90deg) translateX(-50%)',\n          bottom: `rotate(180deg)`,\n          left: 'translateY(50%) rotate(-90deg) translateX(50%)',\n        }[contentContext.placedSide],\n        visibility: contentContext.shouldHideArrow ? 'hidden' : undefined,\n      }}\n    >\n      <ArrowPrimitive.Root\n        {...arrowProps}\n        ref={forwardedRef}\n        style={{\n          ...arrowProps.style,\n          // ensures the element can be measured correctly (mostly for if SVG)\n          display: 'block',\n        }}\n      />\n    </span>\n  );\n});\n\nPopperArrow.displayName = ARROW_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction isNotNull<T>(value: T | null): value is T {\n  return value !== null;\n}\n\nconst transformOrigin = (options: { arrowWidth: number; arrowHeight: number }): Middleware => ({\n  name: 'transformOrigin',\n  options,\n  fn(data) {\n    const { placement, rects, middlewareData } = data;\n\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n    const isArrowHidden = cannotCenterArrow;\n    const arrowWidth = isArrowHidden ? 0 : options.arrowWidth;\n    const arrowHeight = isArrowHidden ? 0 : options.arrowHeight;\n\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n    const noArrowAlign = { start: '0%', center: '50%', end: '100%' }[placedAlign];\n\n    const arrowXCenter = (middlewareData.arrow?.x ?? 0) + arrowWidth / 2;\n    const arrowYCenter = (middlewareData.arrow?.y ?? 0) + arrowHeight / 2;\n\n    let x = '';\n    let y = '';\n\n    if (placedSide === 'bottom') {\n      x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n      y = `${-arrowHeight}px`;\n    } else if (placedSide === 'top') {\n      x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n      y = `${rects.floating.height + arrowHeight}px`;\n    } else if (placedSide === 'right') {\n      x = `${-arrowHeight}px`;\n      y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n    } else if (placedSide === 'left') {\n      x = `${rects.floating.width + arrowHeight}px`;\n      y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n    }\n    return { data: { x, y } };\n  },\n});\n\nfunction getSideAndAlignFromPlacement(placement: Placement) {\n  const [side, align = 'center'] = placement.split('-');\n  return [side as Side, align as Align] as const;\n}\n\nconst Root = Popper;\nconst Anchor = PopperAnchor;\nconst Content = PopperContent;\nconst Arrow = PopperArrow;\n\nexport {\n  createPopperScope,\n  //\n  Popper,\n  PopperAnchor,\n  PopperContent,\n  PopperArrow,\n  //\n  Root,\n  Anchor,\n  Content,\n  Arrow,\n  //\n  SIDE_OPTIONS,\n  ALIGN_OPTIONS,\n};\nexport type { PopperProps, PopperAnchorProps, PopperContentProps, PopperArrowProps };\n", "/// <reference types=\"resize-observer-browser\" />\n\nimport * as React from 'react';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\n\nfunction useSize(element: HTMLElement | null) {\n  const [size, setSize] = React.useState<{ width: number; height: number } | undefined>(undefined);\n\n  useLayoutEffect(() => {\n    if (element) {\n      // provide size as early as possible\n      setSize({ width: element.offsetWidth, height: element.offsetHeight });\n\n      const resizeObserver = new ResizeObserver((entries) => {\n        if (!Array.isArray(entries)) {\n          return;\n        }\n\n        // Since we only observe the one element, we don't need to loop over the\n        // array\n        if (!entries.length) {\n          return;\n        }\n\n        const entry = entries[0];\n        let width: number;\n        let height: number;\n\n        if ('borderBoxSize' in entry) {\n          const borderSizeEntry = entry['borderBoxSize'];\n          // iron out differences between browsers\n          const borderSize = Array.isArray(borderSizeEntry) ? borderSizeEntry[0] : borderSizeEntry;\n          width = borderSize['inlineSize'];\n          height = borderSize['blockSize'];\n        } else {\n          // for browsers that don't support `borderBoxSize`\n          // we calculate it ourselves to get the correct border box.\n          width = element.offsetWidth;\n          height = element.offsetHeight;\n        }\n\n        setSize({ width, height });\n      });\n\n      resizeObserver.observe(element, { box: 'border-box' });\n\n      return () => resizeObserver.unobserve(element);\n    } else {\n      // We only want to reset to `undefined` when the element becomes `null`,\n      // not if it changes to another element.\n      setSize(undefined);\n    }\n  }, [element]);\n\n  return size;\n}\n\nexport { useSize };\n", "import * as React from 'react';\nimport ReactDOM from 'react-dom';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type * as Radix from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * Portal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'Portal';\n\ntype PortalElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = Radix.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface PortalProps extends PrimitiveDivProps {\n  container?: HTMLElement | null;\n}\n\nconst Portal = React.forwardRef<PortalElement, PortalProps>((props, forwardedRef) => {\n  const { container = globalThis?.document?.body, ...portalProps } = props;\n  return container\n    ? ReactDOM.createPortal(<Primitive.div {...portalProps} ref={forwardedRef} />, container)\n    : null;\n});\n\nPortal.displayName = PORTAL_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Portal;\n\nexport {\n  Portal,\n  //\n  Root,\n};\nexport type { PortalProps };\n", "import * as React from 'react';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\n\ntype UseControllableStateParams<T> = {\n  prop?: T | undefined;\n  defaultProp?: T | undefined;\n  onChange?: (state: T) => void;\n};\n\ntype SetStateFn<T> = (prevState?: T) => T;\n\nfunction useControllableState<T>({\n  prop,\n  defaultProp,\n  onChange = () => {},\n}: UseControllableStateParams<T>) {\n  const [uncontrolledProp, setUncontrolledProp] = useUncontrolledState({ defaultProp, onChange });\n  const isControlled = prop !== undefined;\n  const value = isControlled ? prop : uncontrolledProp;\n  const handleChange = useCallbackRef(onChange);\n\n  const setValue: React.Dispatch<React.SetStateAction<T | undefined>> = React.useCallback(\n    (nextValue) => {\n      if (isControlled) {\n        const setter = nextValue as SetStateFn<T>;\n        const value = typeof nextValue === 'function' ? setter(prop) : nextValue;\n        if (value !== prop) handleChange(value as T);\n      } else {\n        setUncontrolledProp(nextValue);\n      }\n    },\n    [isControlled, prop, setUncontrolledProp, handleChange]\n  );\n\n  return [value, setValue] as const;\n}\n\nfunction useUncontrolledState<T>({\n  defaultProp,\n  onChange,\n}: Omit<UseControllableStateParams<T>, 'prop'>) {\n  const uncontrolledState = React.useState<T | undefined>(defaultProp);\n  const [value] = uncontrolledState;\n  const prevValueRef = React.useRef(value);\n  const handleChange = useCallbackRef(onChange);\n\n  React.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      handleChange(value as T);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef, handleChange]);\n\n  return uncontrolledState;\n}\n\nexport { useControllableState };\n", "import * as React from 'react';\n\ntype PossibleRef<T> = React.Ref<T> | undefined;\n\n/**\n * Set a given ref to a given value\n * This utility takes care of different types of refs: callback refs and RefObject(s)\n */\nfunction setRef<T>(ref: PossibleRef<T>, value: T) {\n  if (typeof ref === 'function') {\n    ref(value);\n  } else if (ref !== null && ref !== undefined) {\n    (ref as React.MutableRefObject<T>).current = value;\n  }\n}\n\n/**\n * A utility to compose multiple refs together\n * Accepts callback refs and RefObject(s)\n */\nfunction composeRefs<T>(...refs: PossibleRef<T>[]) {\n  return (node: T) => refs.forEach((ref) => setRef(ref, node));\n}\n\n/**\n * A custom hook that composes multiple refs\n * Accepts callback refs and RefObject(s)\n */\nfunction useComposedRefs<T>(...refs: PossibleRef<T>[]) {\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  return React.useCallback(composeRefs(...refs), refs);\n}\n\nexport { composeRefs, useComposedRefs };\n", "import * as React from 'react';\nimport { composeRefs } from '@radix-ui/react-compose-refs';\n\n/* -------------------------------------------------------------------------------------------------\n * Slot\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SlotProps extends React.HTMLAttributes<HTMLElement> {\n  children?: React.ReactNode;\n}\n\nconst Slot = React.forwardRef<HTMLElement, SlotProps>((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n  const childrenArray = React.Children.toArray(children);\n  const slottable = childrenArray.find(isSlottable);\n\n  if (slottable) {\n    // the new element to render is the one passed as a child of `Slottable`\n    const newElement = slottable.props.children as React.ReactNode;\n\n    const newChildren = childrenArray.map((child) => {\n      if (child === slottable) {\n        // because the new element will be the one rendered, we are only interested\n        // in grabbing its children (`newElement.props.children`)\n        if (React.Children.count(newElement) > 1) return React.Children.only(null);\n        return React.isValidElement(newElement)\n          ? (newElement.props.children as React.ReactNode)\n          : null;\n      } else {\n        return child;\n      }\n    });\n\n    return (\n      <SlotClone {...slotProps} ref={forwardedRef}>\n        {React.isValidElement(newElement)\n          ? React.cloneElement(newElement, undefined, newChildren)\n          : null}\n      </SlotClone>\n    );\n  }\n\n  return (\n    <SlotClone {...slotProps} ref={forwardedRef}>\n      {children}\n    </SlotClone>\n  );\n});\n\nSlot.displayName = 'Slot';\n\n/* -------------------------------------------------------------------------------------------------\n * SlotClone\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SlotCloneProps {\n  children: React.ReactNode;\n}\n\nconst SlotClone = React.forwardRef<any, SlotCloneProps>((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n\n  if (React.isValidElement(children)) {\n    return React.cloneElement(children, {\n      ...mergeProps(slotProps, children.props),\n      ref: forwardedRef ? composeRefs(forwardedRef, (children as any).ref) : (children as any).ref,\n    });\n  }\n\n  return React.Children.count(children) > 1 ? React.Children.only(null) : null;\n});\n\nSlotClone.displayName = 'SlotClone';\n\n/* -------------------------------------------------------------------------------------------------\n * Slottable\n * -----------------------------------------------------------------------------------------------*/\n\nconst Slottable = ({ children }: { children: React.ReactNode }) => {\n  return <>{children}</>;\n};\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype AnyProps = Record<string, any>;\n\nfunction isSlottable(child: React.ReactNode): child is React.ReactElement {\n  return React.isValidElement(child) && child.type === Slottable;\n}\n\nfunction mergeProps(slotProps: AnyProps, childProps: AnyProps) {\n  // all child props should override\n  const overrideProps = { ...childProps };\n\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      // if the handler exists on both, we compose them\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args: unknown[]) => {\n          childPropValue(...args);\n          slotPropValue(...args);\n        };\n      }\n      // but if it exists only on the slot, we use only this one\n      else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    }\n    // if it's `style`, we merge them\n    else if (propName === 'style') {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === 'className') {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(' ');\n    }\n  }\n\n  return { ...slotProps, ...overrideProps };\n}\n\nconst Root = Slot;\n\nexport {\n  Slot,\n  Slottable,\n  //\n  Root,\n};\nexport type { SlotProps };\n", "import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { Slot } from '@radix-ui/react-slot';\n\nconst NODES = [\n  'a',\n  'button',\n  'div',\n  'form',\n  'h2',\n  'h3',\n  'img',\n  'input',\n  'label',\n  'li',\n  'nav',\n  'ol',\n  'p',\n  'span',\n  'svg',\n  'ul',\n] as const;\n\n// Temporary while we await merge of this fix:\n// https://github.com/DefinitelyTyped/DefinitelyTyped/pull/55396\n// prettier-ignore\ntype PropsWithoutRef<P> = P extends any ? ('ref' extends keyof P ? Pick<P, Exclude<keyof P, 'ref'>> : P) : P;\ntype ComponentPropsWithoutRef<T extends React.ElementType> = PropsWithoutRef<\n  React.ComponentProps<T>\n>;\n\ntype Primitives = { [E in typeof NODES[number]]: PrimitiveForwardRefComponent<E> };\ntype PrimitivePropsWithRef<E extends React.ElementType> = React.ComponentPropsWithRef<E> & {\n  asChild?: boolean;\n};\n\ninterface PrimitiveForwardRefComponent<E extends React.ElementType>\n  extends React.ForwardRefExoticComponent<PrimitivePropsWithRef<E>> {}\n\n/* -------------------------------------------------------------------------------------------------\n * Primitive\n * -----------------------------------------------------------------------------------------------*/\n\nconst Primitive = NODES.reduce((primitive, node) => {\n  const Node = React.forwardRef((props: PrimitivePropsWithRef<typeof node>, forwardedRef: any) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp: any = asChild ? Slot : node;\n\n    React.useEffect(() => {\n      (window as any)[Symbol.for('radix-ui')] = true;\n    }, []);\n\n    return <Comp {...primitiveProps} ref={forwardedRef} />;\n  });\n\n  Node.displayName = `Primitive.${node}`;\n\n  return { ...primitive, [node]: Node };\n}, {} as Primitives);\n\n/* -------------------------------------------------------------------------------------------------\n * Utils\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * Flush custom event dispatch\n * https://github.com/radix-ui/primitives/pull/1378\n *\n * React batches *all* event handlers since version 18, this introduces certain considerations when using custom event types.\n *\n * Internally, React prioritises events in the following order:\n *  - discrete\n *  - continuous\n *  - default\n *\n * https://github.com/facebook/react/blob/a8a4742f1c54493df00da648a3f9d26e3db9c8b5/packages/react-dom/src/events/ReactDOMEventListener.js#L294-L350\n *\n * `discrete` is an  important distinction as updates within these events are applied immediately.\n * React however, is not able to infer the priority of custom event types due to how they are detected internally.\n * Because of this, it's possible for updates from custom events to be unexpectedly batched when\n * dispatched by another `discrete` event.\n *\n * In order to ensure that updates from custom events are applied predictably, we need to manually flush the batch.\n * This utility should be used when dispatching a custom event from within another `discrete` event, this utility\n * is not nessesary when dispatching known event types, or if dispatching a custom type inside a non-discrete event.\n * For example:\n *\n * dispatching a known click 👎\n * target.dispatchEvent(new Event(‘click’))\n *\n * dispatching a custom type within a non-discrete event 👎\n * onScroll={(event) => event.target.dispatchEvent(new CustomEvent(‘customType’))}\n *\n * dispatching a custom type within a `discrete` event 👍\n * onPointerDown={(event) => dispatchDiscreteCustomEvent(event.target, new CustomEvent(‘customType’))}\n *\n * Note: though React classifies `focus`, `focusin` and `focusout` events as `discrete`, it's  not recommended to use\n * this utility with them. This is because it's possible for those handlers to be called implicitly during render\n * e.g. when focus is within a component as it is unmounted, or when managing focus on mount.\n */\n\nfunction dispatchDiscreteCustomEvent<E extends CustomEvent>(target: E['target'], event: E) {\n  if (target) ReactDOM.flushSync(() => target.dispatchEvent(event));\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Primitive;\n\nexport {\n  Primitive,\n  //\n  Root,\n  //\n  dispatchDiscreteCustomEvent,\n};\nexport type { ComponentPropsWithoutRef, PrimitivePropsWithRef };\n", "import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type * as Radix from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * VisuallyHidden\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'VisuallyHidden';\n\ntype VisuallyHiddenElement = React.ElementRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = Radix.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface VisuallyHiddenProps extends PrimitiveSpanProps {}\n\nconst VisuallyHidden = React.forwardRef<VisuallyHiddenElement, VisuallyHiddenProps>(\n  (props, forwardedRef) => {\n    return (\n      <Primitive.span\n        {...props}\n        ref={forwardedRef}\n        style={{\n          // See: https://github.com/twbs/bootstrap/blob/master/scss/mixins/_screen-reader.scss\n          position: 'absolute',\n          border: 0,\n          width: 1,\n          height: 1,\n          padding: 0,\n          margin: -1,\n          overflow: 'hidden',\n          clip: 'rect(0, 0, 0, 0)',\n          whiteSpace: 'nowrap',\n          wordWrap: 'normal',\n          ...props.style,\n        }}\n      />\n    );\n  }\n);\n\nVisuallyHidden.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = VisuallyHidden;\n\nexport {\n  VisuallyHidden,\n  //\n  Root,\n};\nexport type { VisuallyHiddenProps };\n", "import { createSidecarMedium } from 'use-sidecar';\nexport var effectCar = createSidecarMedium();\n", "import { __assign, __rest } from \"tslib\";\nimport * as React from 'react';\nimport { fullWidthClassName, zeroRightClassName } from 'react-remove-scroll-bar/constants';\nimport { useMergeRefs } from 'use-callback-ref';\nimport { effectCar } from './medium';\nvar nothing = function () {\n    return;\n};\n/**\n * Removes scrollbar from the page and contain the scroll within the Lock\n */\nvar RemoveScroll = React.forwardRef(function (props, parentRef) {\n    var ref = React.useRef(null);\n    var _a = React.useState({\n        onScrollCapture: nothing,\n        onWheelCapture: nothing,\n        onTouchMoveCapture: nothing,\n    }), callbacks = _a[0], setCallbacks = _a[1];\n    var forwardProps = props.forwardProps, children = props.children, className = props.className, removeScrollBar = props.removeScrollBar, enabled = props.enabled, shards = props.shards, sideCar = props.sideCar, noIsolation = props.noIsolation, inert = props.inert, allowPinchZoom = props.allowPinchZoom, _b = props.as, Container = _b === void 0 ? 'div' : _b, rest = __rest(props, [\"forwardProps\", \"children\", \"className\", \"removeScrollBar\", \"enabled\", \"shards\", \"sideCar\", \"noIsolation\", \"inert\", \"allowPinchZoom\", \"as\"]);\n    var SideCar = sideCar;\n    var containerRef = useMergeRefs([ref, parentRef]);\n    var containerProps = __assign(__assign({}, rest), callbacks);\n    return (React.createElement(React.Fragment, null,\n        enabled && (React.createElement(SideCar, { sideCar: effectCar, removeScrollBar: removeScrollBar, shards: shards, noIsolation: noIsolation, inert: inert, setCallbacks: setCallbacks, allowPinchZoom: !!allowPinchZoom, lockRef: ref })),\n        forwardProps ? (React.cloneElement(React.Children.only(children), __assign(__assign({}, containerProps), { ref: containerRef }))) : (React.createElement(Container, __assign({}, containerProps, { className: className, ref: containerRef }), children))));\n});\nRemoveScroll.defaultProps = {\n    enabled: true,\n    removeScrollBar: true,\n    inert: false,\n};\nRemoveScroll.classNames = {\n    fullWidth: fullWidthClassName,\n    zeroRight: zeroRightClassName,\n};\nexport { RemoveScroll };\n", "var passiveSupported = false;\nif (typeof window !== 'undefined') {\n    try {\n        var options = Object.defineProperty({}, 'passive', {\n            get: function () {\n                passiveSupported = true;\n                return true;\n            },\n        });\n        // @ts-ignore\n        window.addEventListener('test', options, options);\n        // @ts-ignore\n        window.removeEventListener('test', options, options);\n    }\n    catch (err) {\n        passiveSupported = false;\n    }\n}\nexport var nonPassive = passiveSupported ? { passive: false } : false;\n", "var alwaysContainsScroll = function (node) {\n    // textarea will always _contain_ scroll inside self. It only can be hidden\n    return node.tagName === 'TEXTAREA';\n};\nvar elementCanBeScrolled = function (node, overflow) {\n    var styles = window.getComputedStyle(node);\n    return (\n    // not-not-scrollable\n    styles[overflow] !== 'hidden' &&\n        // contains scroll inside self\n        !(styles.overflowY === styles.overflowX && !alwaysContainsScroll(node) && styles[overflow] === 'visible'));\n};\nvar elementCouldBeVScrolled = function (node) { return elementCanBeScrolled(node, 'overflowY'); };\nvar elementCouldBeHScrolled = function (node) { return elementCanBeScrolled(node, 'overflowX'); };\nexport var locationCouldBeScrolled = function (axis, node) {\n    var current = node;\n    do {\n        // Skip over shadow root\n        if (typeof ShadowRoot !== 'undefined' && current instanceof ShadowRoot) {\n            current = current.host;\n        }\n        var isScrollable = elementCouldBeScrolled(axis, current);\n        if (isScrollable) {\n            var _a = getScrollVariables(axis, current), s = _a[1], d = _a[2];\n            if (s > d) {\n                return true;\n            }\n        }\n        current = current.parentNode;\n    } while (current && current !== document.body);\n    return false;\n};\nvar getVScrollVariables = function (_a) {\n    var scrollTop = _a.scrollTop, scrollHeight = _a.scrollHeight, clientHeight = _a.clientHeight;\n    return [\n        scrollTop,\n        scrollHeight,\n        clientHeight,\n    ];\n};\nvar getHScrollVariables = function (_a) {\n    var scrollLeft = _a.scrollLeft, scrollWidth = _a.scrollWidth, clientWidth = _a.clientWidth;\n    return [\n        scrollLeft,\n        scrollWidth,\n        clientWidth,\n    ];\n};\nvar elementCouldBeScrolled = function (axis, node) {\n    return axis === 'v' ? elementCouldBeVScrolled(node) : elementCouldBeHScrolled(node);\n};\nvar getScrollVariables = function (axis, node) {\n    return axis === 'v' ? getVScrollVariables(node) : getHScrollVariables(node);\n};\nvar getDirectionFactor = function (axis, direction) {\n    /**\n     * If the element's direction is rtl (right-to-left), then scrollLeft is 0 when the scrollbar is at its rightmost position,\n     * and then increasingly negative as you scroll towards the end of the content.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/Element/scrollLeft\n     */\n    return axis === 'h' && direction === 'rtl' ? -1 : 1;\n};\nexport var handleScroll = function (axis, endTarget, event, sourceDelta, noOverscroll) {\n    var directionFactor = getDirectionFactor(axis, window.getComputedStyle(endTarget).direction);\n    var delta = directionFactor * sourceDelta;\n    // find scrollable target\n    var target = event.target;\n    var targetInLock = endTarget.contains(target);\n    var shouldCancelScroll = false;\n    var isDeltaPositive = delta > 0;\n    var availableScroll = 0;\n    var availableScrollTop = 0;\n    do {\n        var _a = getScrollVariables(axis, target), position = _a[0], scroll_1 = _a[1], capacity = _a[2];\n        var elementScroll = scroll_1 - capacity - directionFactor * position;\n        if (position || elementScroll) {\n            if (elementCouldBeScrolled(axis, target)) {\n                availableScroll += elementScroll;\n                availableScrollTop += position;\n            }\n        }\n        target = target.parentNode;\n    } while (\n    // portaled content\n    (!targetInLock && target !== document.body) ||\n        // self content\n        (targetInLock && (endTarget.contains(target) || endTarget === target)));\n    if (isDeltaPositive && ((noOverscroll && availableScroll === 0) || (!noOverscroll && delta > availableScroll))) {\n        shouldCancelScroll = true;\n    }\n    else if (!isDeltaPositive &&\n        ((noOverscroll && availableScrollTop === 0) || (!noOverscroll && -delta > availableScrollTop))) {\n        shouldCancelScroll = true;\n    }\n    return shouldCancelScroll;\n};\n", "import { __spreadArray } from \"tslib\";\nimport * as React from 'react';\nimport { RemoveScrollBar } from 'react-remove-scroll-bar';\nimport { styleSingleton } from 'react-style-singleton';\nimport { nonPassive } from './aggresiveCapture';\nimport { handleScroll, locationCouldBeScrolled } from './handleScroll';\nexport var getTouchXY = function (event) {\n    return 'changedTouches' in event ? [event.changedTouches[0].clientX, event.changedTouches[0].clientY] : [0, 0];\n};\nexport var getDeltaXY = function (event) { return [event.deltaX, event.deltaY]; };\nvar extractRef = function (ref) {\n    return ref && 'current' in ref ? ref.current : ref;\n};\nvar deltaCompare = function (x, y) { return x[0] === y[0] && x[1] === y[1]; };\nvar generateStyle = function (id) { return \"\\n  .block-interactivity-\".concat(id, \" {pointer-events: none;}\\n  .allow-interactivity-\").concat(id, \" {pointer-events: all;}\\n\"); };\nvar idCounter = 0;\nvar lockStack = [];\nexport function RemoveScrollSideCar(props) {\n    var shouldPreventQueue = React.useRef([]);\n    var touchStartRef = React.useRef([0, 0]);\n    var activeAxis = React.useRef();\n    var id = React.useState(idCounter++)[0];\n    var Style = React.useState(function () { return styleSingleton(); })[0];\n    var lastProps = React.useRef(props);\n    React.useEffect(function () {\n        lastProps.current = props;\n    }, [props]);\n    React.useEffect(function () {\n        if (props.inert) {\n            document.body.classList.add(\"block-interactivity-\".concat(id));\n            var allow_1 = __spreadArray([props.lockRef.current], (props.shards || []).map(extractRef), true).filter(Boolean);\n            allow_1.forEach(function (el) { return el.classList.add(\"allow-interactivity-\".concat(id)); });\n            return function () {\n                document.body.classList.remove(\"block-interactivity-\".concat(id));\n                allow_1.forEach(function (el) { return el.classList.remove(\"allow-interactivity-\".concat(id)); });\n            };\n        }\n        return;\n    }, [props.inert, props.lockRef.current, props.shards]);\n    var shouldCancelEvent = React.useCallback(function (event, parent) {\n        if ('touches' in event && event.touches.length === 2) {\n            return !lastProps.current.allowPinchZoom;\n        }\n        var touch = getTouchXY(event);\n        var touchStart = touchStartRef.current;\n        var deltaX = 'deltaX' in event ? event.deltaX : touchStart[0] - touch[0];\n        var deltaY = 'deltaY' in event ? event.deltaY : touchStart[1] - touch[1];\n        var currentAxis;\n        var target = event.target;\n        var moveDirection = Math.abs(deltaX) > Math.abs(deltaY) ? 'h' : 'v';\n        // allow horizontal touch move on Range inputs. They will not cause any scroll\n        if ('touches' in event && moveDirection === 'h' && target.type === 'range') {\n            return false;\n        }\n        var canBeScrolledInMainDirection = locationCouldBeScrolled(moveDirection, target);\n        if (!canBeScrolledInMainDirection) {\n            return true;\n        }\n        if (canBeScrolledInMainDirection) {\n            currentAxis = moveDirection;\n        }\n        else {\n            currentAxis = moveDirection === 'v' ? 'h' : 'v';\n            canBeScrolledInMainDirection = locationCouldBeScrolled(moveDirection, target);\n            // other axis might be not scrollable\n        }\n        if (!canBeScrolledInMainDirection) {\n            return false;\n        }\n        if (!activeAxis.current && 'changedTouches' in event && (deltaX || deltaY)) {\n            activeAxis.current = currentAxis;\n        }\n        if (!currentAxis) {\n            return true;\n        }\n        var cancelingAxis = activeAxis.current || currentAxis;\n        return handleScroll(cancelingAxis, parent, event, cancelingAxis === 'h' ? deltaX : deltaY, true);\n    }, []);\n    var shouldPrevent = React.useCallback(function (_event) {\n        var event = _event;\n        if (!lockStack.length || lockStack[lockStack.length - 1] !== Style) {\n            // not the last active\n            return;\n        }\n        var delta = 'deltaY' in event ? getDeltaXY(event) : getTouchXY(event);\n        var sourceEvent = shouldPreventQueue.current.filter(function (e) { return e.name === event.type && e.target === event.target && deltaCompare(e.delta, delta); })[0];\n        // self event, and should be canceled\n        if (sourceEvent && sourceEvent.should) {\n            if (event.cancelable) {\n                event.preventDefault();\n            }\n            return;\n        }\n        // outside or shard event\n        if (!sourceEvent) {\n            var shardNodes = (lastProps.current.shards || [])\n                .map(extractRef)\n                .filter(Boolean)\n                .filter(function (node) { return node.contains(event.target); });\n            var shouldStop = shardNodes.length > 0 ? shouldCancelEvent(event, shardNodes[0]) : !lastProps.current.noIsolation;\n            if (shouldStop) {\n                if (event.cancelable) {\n                    event.preventDefault();\n                }\n            }\n        }\n    }, []);\n    var shouldCancel = React.useCallback(function (name, delta, target, should) {\n        var event = { name: name, delta: delta, target: target, should: should };\n        shouldPreventQueue.current.push(event);\n        setTimeout(function () {\n            shouldPreventQueue.current = shouldPreventQueue.current.filter(function (e) { return e !== event; });\n        }, 1);\n    }, []);\n    var scrollTouchStart = React.useCallback(function (event) {\n        touchStartRef.current = getTouchXY(event);\n        activeAxis.current = undefined;\n    }, []);\n    var scrollWheel = React.useCallback(function (event) {\n        shouldCancel(event.type, getDeltaXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n    }, []);\n    var scrollTouchMove = React.useCallback(function (event) {\n        shouldCancel(event.type, getTouchXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n    }, []);\n    React.useEffect(function () {\n        lockStack.push(Style);\n        props.setCallbacks({\n            onScrollCapture: scrollWheel,\n            onWheelCapture: scrollWheel,\n            onTouchMoveCapture: scrollTouchMove,\n        });\n        document.addEventListener('wheel', shouldPrevent, nonPassive);\n        document.addEventListener('touchmove', shouldPrevent, nonPassive);\n        document.addEventListener('touchstart', scrollTouchStart, nonPassive);\n        return function () {\n            lockStack = lockStack.filter(function (inst) { return inst !== Style; });\n            document.removeEventListener('wheel', shouldPrevent, nonPassive);\n            document.removeEventListener('touchmove', shouldPrevent, nonPassive);\n            document.removeEventListener('touchstart', scrollTouchStart, nonPassive);\n        };\n    }, []);\n    var removeScrollBar = props.removeScrollBar, inert = props.inert;\n    return (React.createElement(React.Fragment, null,\n        inert ? React.createElement(Style, { styles: generateStyle(id) }) : null,\n        removeScrollBar ? React.createElement(RemoveScrollBar, { gapMode: \"margin\" }) : null));\n}\n", "import { exportSidecar } from 'use-sidecar';\nimport { RemoveScrollSideCar } from './SideEffect';\nimport { effectCar } from './medium';\nexport default exportSidecar(effectCar, RemoveScrollSideCar);\n", "import { __assign } from \"tslib\";\nimport * as React from 'react';\nimport { RemoveScroll } from './UI';\nimport SideCar from './sidecar';\nvar ReactRemoveScroll = React.forwardRef(function (props, ref) { return (React.createElement(RemoveScroll, __assign({}, props, { ref: ref, sideCar: SideCar }))); });\nReactRemoveScroll.classNames = RemoveScroll.classNames;\nexport default ReactRemoveScroll;\n", "import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { clamp } from '@radix-ui/number';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { useFocusGuards } from '@radix-ui/react-focus-guards';\nimport { FocusScope } from '@radix-ui/react-focus-scope';\nimport { useId } from '@radix-ui/react-id';\nimport * as PopperPrimitive from '@radix-ui/react-popper';\nimport { createPopperScope } from '@radix-ui/react-popper';\nimport { Portal as PortalPrimitive } from '@radix-ui/react-portal';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { Slot } from '@radix-ui/react-slot';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { usePrevious } from '@radix-ui/react-use-previous';\nimport { VisuallyHidden } from '@radix-ui/react-visually-hidden';\nimport { hideOthers } from 'aria-hidden';\nimport { RemoveScroll } from 'react-remove-scroll';\n\nimport type * as Radix from '@radix-ui/react-primitive';\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Direction = 'ltr' | 'rtl';\n\nconst OPEN_KEYS = [' ', 'Enter', 'ArrowUp', 'ArrowDown'];\nconst SELECTION_KEYS = [' ', 'Enter'];\n\n/* -------------------------------------------------------------------------------------------------\n * Select\n * -----------------------------------------------------------------------------------------------*/\n\nconst SELECT_NAME = 'Select';\n\ntype ItemData = { value: string; disabled: boolean; textValue: string };\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  SelectItemElement,\n  ItemData\n>(SELECT_NAME);\n\ntype ScopedProps<P> = P & { __scopeSelect?: Scope };\nconst [createSelectContext, createSelectScope] = createContextScope(SELECT_NAME, [\n  createCollectionScope,\n  createPopperScope,\n]);\nconst usePopperScope = createPopperScope();\n\ntype SelectContextValue = {\n  trigger: SelectTriggerElement | null;\n  onTriggerChange(node: SelectTriggerElement | null): void;\n  valueNode: SelectValueElement | null;\n  onValueNodeChange(node: SelectValueElement): void;\n  valueNodeHasChildren: boolean;\n  onValueNodeHasChildrenChange(hasChildren: boolean): void;\n  contentId: string;\n  value?: string;\n  onValueChange(value: string): void;\n  open: boolean;\n  required?: boolean;\n  onOpenChange(open: boolean): void;\n  dir: SelectProps['dir'];\n  triggerPointerDownPosRef: React.MutableRefObject<{ x: number; y: number } | null>;\n  disabled?: boolean;\n};\n\nconst [SelectProvider, useSelectContext] = createSelectContext<SelectContextValue>(SELECT_NAME);\n\ntype NativeOption = React.ReactElement<React.ComponentProps<'option'>>;\n\ntype SelectNativeOptionsContextValue = {\n  onNativeOptionAdd(option: NativeOption): void;\n  onNativeOptionRemove(option: NativeOption): void;\n};\nconst [SelectNativeOptionsProvider, useSelectNativeOptionsContext] =\n  createSelectContext<SelectNativeOptionsContextValue>(SELECT_NAME);\n\ninterface SelectProps {\n  children?: React.ReactNode;\n  value?: string;\n  defaultValue?: string;\n  onValueChange?(value: string): void;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n  dir?: Direction;\n  name?: string;\n  autoComplete?: string;\n  disabled?: boolean;\n  required?: boolean;\n}\n\nconst Select: React.FC<SelectProps> = (props: ScopedProps<SelectProps>) => {\n  const {\n    __scopeSelect,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    value: valueProp,\n    defaultValue,\n    onValueChange,\n    dir,\n    name,\n    autoComplete,\n    disabled,\n    required,\n  } = props;\n  const popperScope = usePopperScope(__scopeSelect);\n  const [trigger, setTrigger] = React.useState<SelectTriggerElement | null>(null);\n  const [valueNode, setValueNode] = React.useState<SelectValueElement | null>(null);\n  const [valueNodeHasChildren, setValueNodeHasChildren] = React.useState(false);\n  const direction = useDirection(dir);\n  const [open = false, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen,\n    onChange: onOpenChange,\n  });\n  const [value, setValue] = useControllableState({\n    prop: valueProp,\n    defaultProp: defaultValue,\n    onChange: onValueChange,\n  });\n  const triggerPointerDownPosRef = React.useRef<{ x: number; y: number } | null>(null);\n\n  // We set this to true by default so that events bubble to forms without JS (SSR)\n  const isFormControl = trigger ? Boolean(trigger.closest('form')) : true;\n  const [nativeOptionsSet, setNativeOptionsSet] = React.useState(new Set<NativeOption>());\n\n  // The native `select` only associates the correct default value if the corresponding\n  // `option` is rendered as a child **at the same time** as itself.\n  // Because it might take a few renders for our items to gather the information to build\n  // the native `option`(s), we generate a key on the `select` to make sure React re-builds it\n  // each time the options change.\n  const nativeSelectKey = Array.from(nativeOptionsSet)\n    .map((option) => option.props.value)\n    .join(';');\n\n  return (\n    <PopperPrimitive.Root {...popperScope}>\n      <SelectProvider\n        required={required}\n        scope={__scopeSelect}\n        trigger={trigger}\n        onTriggerChange={setTrigger}\n        valueNode={valueNode}\n        onValueNodeChange={setValueNode}\n        valueNodeHasChildren={valueNodeHasChildren}\n        onValueNodeHasChildrenChange={setValueNodeHasChildren}\n        contentId={useId()}\n        value={value}\n        onValueChange={setValue}\n        open={open}\n        onOpenChange={setOpen}\n        dir={direction}\n        triggerPointerDownPosRef={triggerPointerDownPosRef}\n        disabled={disabled}\n      >\n        <Collection.Provider scope={__scopeSelect}>\n          <SelectNativeOptionsProvider\n            scope={props.__scopeSelect}\n            onNativeOptionAdd={React.useCallback((option) => {\n              setNativeOptionsSet((prev) => new Set(prev).add(option));\n            }, [])}\n            onNativeOptionRemove={React.useCallback((option) => {\n              setNativeOptionsSet((prev) => {\n                const optionsSet = new Set(prev);\n                optionsSet.delete(option);\n                return optionsSet;\n              });\n            }, [])}\n          >\n            {children}\n          </SelectNativeOptionsProvider>\n        </Collection.Provider>\n\n        {isFormControl ? (\n          <BubbleSelect\n            key={nativeSelectKey}\n            aria-hidden\n            required={required}\n            tabIndex={-1}\n            name={name}\n            autoComplete={autoComplete}\n            value={value}\n            // enable form autofill\n            onChange={(event) => setValue(event.target.value)}\n            disabled={disabled}\n          >\n            {value === undefined ? <option value=\"\" /> : null}\n            {Array.from(nativeOptionsSet)}\n          </BubbleSelect>\n        ) : null}\n      </SelectProvider>\n    </PopperPrimitive.Root>\n  );\n};\n\nSelect.displayName = SELECT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'SelectTrigger';\n\ntype SelectTriggerElement = React.ElementRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = Radix.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface SelectTriggerProps extends PrimitiveButtonProps {}\n\nconst SelectTrigger = React.forwardRef<SelectTriggerElement, SelectTriggerProps>(\n  (props: ScopedProps<SelectTriggerProps>, forwardedRef) => {\n    const { __scopeSelect, disabled = false, ...triggerProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(TRIGGER_NAME, __scopeSelect);\n    const isDisabled = context.disabled || disabled;\n    const composedRefs = useComposedRefs(forwardedRef, context.onTriggerChange);\n    const getItems = useCollection(__scopeSelect);\n\n    const [searchRef, handleTypeaheadSearch, resetTypeahead] = useTypeaheadSearch((search) => {\n      const enabledItems = getItems().filter((item) => !item.disabled);\n      const currentItem = enabledItems.find((item) => item.value === context.value);\n      const nextItem = findNextItem(enabledItems, search, currentItem);\n      if (nextItem !== undefined) {\n        context.onValueChange(nextItem.value);\n      }\n    });\n\n    const handleOpen = () => {\n      if (!isDisabled) {\n        context.onOpenChange(true);\n        // reset typeahead when we open\n        resetTypeahead();\n      }\n    };\n\n    return (\n      <PopperPrimitive.Anchor asChild {...popperScope}>\n        <Primitive.button\n          type=\"button\"\n          role=\"combobox\"\n          aria-controls={context.contentId}\n          aria-expanded={context.open}\n          aria-required={context.required}\n          aria-autocomplete=\"none\"\n          dir={context.dir}\n          data-state={context.open ? 'open' : 'closed'}\n          disabled={isDisabled}\n          data-disabled={isDisabled ? '' : undefined}\n          data-placeholder={context.value === undefined ? '' : undefined}\n          {...triggerProps}\n          ref={composedRefs}\n          // Enable compatibility with native label or custom `Label` \"click\" for Safari:\n          onClick={composeEventHandlers(triggerProps.onClick, (event) => {\n            // Whilst browsers generally have no issue focusing the trigger when clicking\n            // on a label, Safari seems to struggle with the fact that there's no `onClick`.\n            // We force `focus` in this case. Note: this doesn't create any other side-effect\n            // because we are preventing default in `onPointerDown` so effectively\n            // this only runs for a label \"click\"\n            event.currentTarget.focus();\n          })}\n          onPointerDown={composeEventHandlers(triggerProps.onPointerDown, (event) => {\n            // prevent implicit pointer capture\n            // https://www.w3.org/TR/pointerevents3/#implicit-pointer-capture\n            const target = event.target as HTMLElement;\n            if (target.hasPointerCapture(event.pointerId)) {\n              target.releasePointerCapture(event.pointerId);\n            }\n\n            // only call handler if it's the left button (mousedown gets triggered by all mouse buttons)\n            // but not when the control key is pressed (avoiding MacOS right click)\n            if (event.button === 0 && event.ctrlKey === false) {\n              handleOpen();\n              context.triggerPointerDownPosRef.current = {\n                x: Math.round(event.pageX),\n                y: Math.round(event.pageY),\n              };\n              // prevent trigger from stealing focus from the active item after opening.\n              event.preventDefault();\n            }\n          })}\n          onKeyDown={composeEventHandlers(triggerProps.onKeyDown, (event) => {\n            const isTypingAhead = searchRef.current !== '';\n            const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n            if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n            if (isTypingAhead && event.key === ' ') return;\n            if (OPEN_KEYS.includes(event.key)) {\n              handleOpen();\n              event.preventDefault();\n            }\n          })}\n        />\n      </PopperPrimitive.Anchor>\n    );\n  }\n);\n\nSelectTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectValue\n * -----------------------------------------------------------------------------------------------*/\n\nconst VALUE_NAME = 'SelectValue';\n\ntype SelectValueElement = React.ElementRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = Radix.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface SelectValueProps extends Omit<PrimitiveSpanProps, 'placeholder'> {\n  placeholder?: React.ReactNode;\n}\n\nconst SelectValue = React.forwardRef<SelectValueElement, SelectValueProps>(\n  (props: ScopedProps<SelectValueProps>, forwardedRef) => {\n    // We ignore `className` and `style` as this part shouldn't be styled.\n    const { __scopeSelect, className, style, children, placeholder, ...valueProps } = props;\n    const context = useSelectContext(VALUE_NAME, __scopeSelect);\n    const { onValueNodeHasChildrenChange } = context;\n    const hasChildren = children !== undefined;\n    const composedRefs = useComposedRefs(forwardedRef, context.onValueNodeChange);\n\n    useLayoutEffect(() => {\n      onValueNodeHasChildrenChange(hasChildren);\n    }, [onValueNodeHasChildrenChange, hasChildren]);\n\n    return (\n      <Primitive.span\n        {...valueProps}\n        ref={composedRefs}\n        // we don't want events from the portalled `SelectValue` children to bubble\n        // through the item they came from\n        style={{ pointerEvents: 'none' }}\n      >\n        {context.value === undefined && placeholder !== undefined ? placeholder : children}\n      </Primitive.span>\n    );\n  }\n);\n\nSelectValue.displayName = VALUE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectIcon\n * -----------------------------------------------------------------------------------------------*/\n\nconst ICON_NAME = 'SelectIcon';\n\ntype SelectIconElement = React.ElementRef<typeof Primitive.span>;\ninterface SelectIconProps extends PrimitiveSpanProps {}\n\nconst SelectIcon = React.forwardRef<SelectIconElement, SelectIconProps>(\n  (props: ScopedProps<SelectIconProps>, forwardedRef) => {\n    const { __scopeSelect, children, ...iconProps } = props;\n    return (\n      <Primitive.span aria-hidden {...iconProps} ref={forwardedRef}>\n        {children || '▼'}\n      </Primitive.span>\n    );\n  }\n);\n\nSelectIcon.displayName = ICON_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'SelectPortal';\n\ntype PortalProps = React.ComponentPropsWithoutRef<typeof PortalPrimitive>;\ninterface SelectPortalProps extends Omit<PortalProps, 'asChild'> {\n  children?: React.ReactNode;\n}\n\nconst SelectPortal: React.FC<SelectPortalProps> = (props: ScopedProps<SelectPortalProps>) => {\n  return <PortalPrimitive asChild {...props} />;\n};\n\nSelectPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'SelectContent';\n\ntype SelectContentElement = SelectContentImplElement;\ninterface SelectContentProps extends SelectContentImplProps {}\n\nconst SelectContent = React.forwardRef<SelectContentElement, SelectContentProps>(\n  (props: ScopedProps<SelectContentProps>, forwardedRef) => {\n    const context = useSelectContext(CONTENT_NAME, props.__scopeSelect);\n    const [fragment, setFragment] = React.useState<DocumentFragment>();\n\n    // setting the fragment in `useLayoutEffect` as `DocumentFragment` doesn't exist on the server\n    useLayoutEffect(() => {\n      setFragment(new DocumentFragment());\n    }, []);\n\n    if (!context.open) {\n      const frag = fragment as Element | undefined;\n      return frag\n        ? ReactDOM.createPortal(\n            <SelectContentProvider scope={props.__scopeSelect}>\n              <Collection.Slot scope={props.__scopeSelect}>\n                <div>{props.children}</div>\n              </Collection.Slot>\n            </SelectContentProvider>,\n            frag\n          )\n        : null;\n    }\n\n    return <SelectContentImpl {...props} ref={forwardedRef} />;\n  }\n);\n\nSelectContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectContentImpl\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_MARGIN = 10;\n\ntype SelectContentContextValue = {\n  content?: SelectContentElement | null;\n  viewport?: SelectViewportElement | null;\n  onViewportChange?: (node: SelectViewportElement | null) => void;\n  itemRefCallback?: (node: SelectItemElement | null, value: string, disabled: boolean) => void;\n  selectedItem?: SelectItemElement | null;\n  onItemLeave?: () => void;\n  itemTextRefCallback?: (\n    node: SelectItemTextElement | null,\n    value: string,\n    disabled: boolean\n  ) => void;\n  focusSelectedItem?: () => void;\n  selectedItemText?: SelectItemTextElement | null;\n  position?: SelectContentProps['position'];\n  isPositioned?: boolean;\n  searchRef?: React.RefObject<string>;\n};\n\nconst [SelectContentProvider, useSelectContentContext] =\n  createSelectContext<SelectContentContextValue>(CONTENT_NAME);\n\nconst CONTENT_IMPL_NAME = 'SelectContentImpl';\n\ntype SelectContentImplElement = SelectPopperPositionElement | SelectItemAlignedPositionElement;\ntype DismissableLayerProps = React.ComponentPropsWithoutRef<typeof DismissableLayer>;\ntype FocusScopeProps = Radix.ComponentPropsWithoutRef<typeof FocusScope>;\n\ntype SelectPopperPrivateProps = { onPlaced?: PopperContentProps['onPlaced'] };\n\ninterface SelectContentImplProps\n  extends Omit<SelectPopperPositionProps, keyof SelectPopperPrivateProps>,\n    Omit<SelectItemAlignedPositionProps, keyof SelectPopperPrivateProps> {\n  /**\n   * Event handler called when auto-focusing on close.\n   * Can be prevented.\n   */\n  onCloseAutoFocus?: FocusScopeProps['onUnmountAutoFocus'];\n  /**\n   * Event handler called when the escape key is down.\n   * Can be prevented.\n   */\n  onEscapeKeyDown?: DismissableLayerProps['onEscapeKeyDown'];\n  /**\n   * Event handler called when the a `pointerdown` event happens outside of the `DismissableLayer`.\n   * Can be prevented.\n   */\n  onPointerDownOutside?: DismissableLayerProps['onPointerDownOutside'];\n\n  position?: 'item-aligned' | 'popper';\n}\n\nconst SelectContentImpl = React.forwardRef<SelectContentImplElement, SelectContentImplProps>(\n  (props: ScopedProps<SelectContentImplProps>, forwardedRef) => {\n    const {\n      __scopeSelect,\n      position = 'item-aligned',\n      onCloseAutoFocus,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      //\n      // PopperContent props\n      side,\n      sideOffset,\n      align,\n      alignOffset,\n      arrowPadding,\n      collisionBoundary,\n      collisionPadding,\n      sticky,\n      hideWhenDetached,\n      avoidCollisions,\n      //\n      ...contentProps\n    } = props;\n    const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n    const [content, setContent] = React.useState<SelectContentImplElement | null>(null);\n    const [viewport, setViewport] = React.useState<SelectViewportElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n    const [selectedItem, setSelectedItem] = React.useState<SelectItemElement | null>(null);\n    const [selectedItemText, setSelectedItemText] = React.useState<SelectItemTextElement | null>(\n      null\n    );\n    const getItems = useCollection(__scopeSelect);\n    const [isPositioned, setIsPositioned] = React.useState(false);\n    const firstValidItemFoundRef = React.useRef(false);\n\n    // aria-hide everything except the content (better supported equivalent to setting aria-modal)\n    React.useEffect(() => {\n      if (content) return hideOthers(content);\n    }, [content]);\n\n    // Make sure the whole tree has focus guards as our `Select` may be\n    // the last element in the DOM (because of the `Portal`)\n    useFocusGuards();\n\n    const focusFirst = React.useCallback(\n      (candidates: Array<HTMLElement | null>) => {\n        const [firstItem, ...restItems] = getItems().map((item) => item.ref.current);\n        const [lastItem] = restItems.slice(-1);\n\n        const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n        for (const candidate of candidates) {\n          // if focus is already where we want to go, we don't want to keep going through the candidates\n          if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n          candidate?.scrollIntoView({ block: 'nearest' });\n          // viewport might have padding so scroll to its edges when focusing first/last items.\n          if (candidate === firstItem && viewport) viewport.scrollTop = 0;\n          if (candidate === lastItem && viewport) viewport.scrollTop = viewport.scrollHeight;\n          candidate?.focus();\n          if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n        }\n      },\n      [getItems, viewport]\n    );\n\n    const focusSelectedItem = React.useCallback(\n      () => focusFirst([selectedItem, content]),\n      [focusFirst, selectedItem, content]\n    );\n\n    // Since this is not dependent on layout, we want to ensure this runs at the same time as\n    // other effects across components. Hence why we don't call `focusSelectedItem` inside `position`.\n    React.useEffect(() => {\n      if (isPositioned) {\n        focusSelectedItem();\n      }\n    }, [isPositioned, focusSelectedItem]);\n\n    // prevent selecting items on `pointerup` in some cases after opening from `pointerdown`\n    // and close on `pointerup` outside.\n    const { onOpenChange, triggerPointerDownPosRef } = context;\n    React.useEffect(() => {\n      if (content) {\n        let pointerMoveDelta = { x: 0, y: 0 };\n\n        const handlePointerMove = (event: PointerEvent) => {\n          pointerMoveDelta = {\n            x: Math.abs(Math.round(event.pageX) - (triggerPointerDownPosRef.current?.x ?? 0)),\n            y: Math.abs(Math.round(event.pageY) - (triggerPointerDownPosRef.current?.y ?? 0)),\n          };\n        };\n        const handlePointerUp = (event: PointerEvent) => {\n          // If the pointer hasn't moved by a certain threshold then we prevent selecting item on `pointerup`.\n          if (pointerMoveDelta.x <= 10 && pointerMoveDelta.y <= 10) {\n            event.preventDefault();\n          } else {\n            // otherwise, if the event was outside the content, close.\n            if (!content.contains(event.target as HTMLElement)) {\n              onOpenChange(false);\n            }\n          }\n          document.removeEventListener('pointermove', handlePointerMove);\n          triggerPointerDownPosRef.current = null;\n        };\n\n        if (triggerPointerDownPosRef.current !== null) {\n          document.addEventListener('pointermove', handlePointerMove);\n          document.addEventListener('pointerup', handlePointerUp, { capture: true, once: true });\n        }\n\n        return () => {\n          document.removeEventListener('pointermove', handlePointerMove);\n          document.removeEventListener('pointerup', handlePointerUp, { capture: true });\n        };\n      }\n    }, [content, onOpenChange, triggerPointerDownPosRef]);\n\n    React.useEffect(() => {\n      const close = () => onOpenChange(false);\n      window.addEventListener('blur', close);\n      window.addEventListener('resize', close);\n      return () => {\n        window.removeEventListener('blur', close);\n        window.removeEventListener('resize', close);\n      };\n    }, [onOpenChange]);\n\n    const [searchRef, handleTypeaheadSearch] = useTypeaheadSearch((search) => {\n      const enabledItems = getItems().filter((item) => !item.disabled);\n      const currentItem = enabledItems.find((item) => item.ref.current === document.activeElement);\n      const nextItem = findNextItem(enabledItems, search, currentItem);\n      if (nextItem) {\n        /**\n         * Imperative focus during keydown is risky so we prevent React's batching updates\n         * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n         */\n        setTimeout(() => (nextItem.ref.current as HTMLElement).focus());\n      }\n    });\n\n    const itemRefCallback = React.useCallback(\n      (node: SelectItemElement | null, value: string, disabled: boolean) => {\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== undefined && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n          setSelectedItem(node);\n          if (isFirstValidItem) firstValidItemFoundRef.current = true;\n        }\n      },\n      [context.value]\n    );\n    const handleItemLeave = React.useCallback(() => content?.focus(), [content]);\n    const itemTextRefCallback = React.useCallback(\n      (node: SelectItemTextElement | null, value: string, disabled: boolean) => {\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== undefined && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n          setSelectedItemText(node);\n        }\n      },\n      [context.value]\n    );\n\n    const SelectPosition = position === 'popper' ? SelectPopperPosition : SelectItemAlignedPosition;\n\n    // Silently ignore props that are not supported by `SelectItemAlignedPosition`\n    const popperContentProps =\n      SelectPosition === SelectPopperPosition\n        ? {\n            side,\n            sideOffset,\n            align,\n            alignOffset,\n            arrowPadding,\n            collisionBoundary,\n            collisionPadding,\n            sticky,\n            hideWhenDetached,\n            avoidCollisions,\n          }\n        : {};\n\n    return (\n      <SelectContentProvider\n        scope={__scopeSelect}\n        content={content}\n        viewport={viewport}\n        onViewportChange={setViewport}\n        itemRefCallback={itemRefCallback}\n        selectedItem={selectedItem}\n        onItemLeave={handleItemLeave}\n        itemTextRefCallback={itemTextRefCallback}\n        focusSelectedItem={focusSelectedItem}\n        selectedItemText={selectedItemText}\n        position={position}\n        isPositioned={isPositioned}\n        searchRef={searchRef}\n      >\n        <RemoveScroll as={Slot} allowPinchZoom>\n          <FocusScope\n            asChild\n            // we make sure we're not trapping once it's been closed\n            // (closed !== unmounted when animating out)\n            trapped={context.open}\n            onMountAutoFocus={(event) => {\n              // we prevent open autofocus because we manually focus the selected item\n              event.preventDefault();\n            }}\n            onUnmountAutoFocus={composeEventHandlers(onCloseAutoFocus, (event) => {\n              context.trigger?.focus({ preventScroll: true });\n              event.preventDefault();\n            })}\n          >\n            <DismissableLayer\n              asChild\n              disableOutsidePointerEvents\n              onEscapeKeyDown={onEscapeKeyDown}\n              onPointerDownOutside={onPointerDownOutside}\n              // When focus is trapped, a focusout event may still happen.\n              // We make sure we don't trigger our `onDismiss` in such case.\n              onFocusOutside={(event) => event.preventDefault()}\n              onDismiss={() => context.onOpenChange(false)}\n            >\n              <SelectPosition\n                role=\"listbox\"\n                id={context.contentId}\n                data-state={context.open ? 'open' : 'closed'}\n                dir={context.dir}\n                onContextMenu={(event) => event.preventDefault()}\n                {...contentProps}\n                {...popperContentProps}\n                onPlaced={() => setIsPositioned(true)}\n                ref={composedRefs}\n                style={{\n                  // flex layout so we can place the scroll buttons properly\n                  display: 'flex',\n                  flexDirection: 'column',\n                  // reset the outline by default as the content MAY get focused\n                  outline: 'none',\n                  ...contentProps.style,\n                }}\n                onKeyDown={composeEventHandlers(contentProps.onKeyDown, (event) => {\n                  const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n\n                  // select should not be navigated using tab key so we prevent it\n                  if (event.key === 'Tab') event.preventDefault();\n\n                  if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n\n                  if (['ArrowUp', 'ArrowDown', 'Home', 'End'].includes(event.key)) {\n                    const items = getItems().filter((item) => !item.disabled);\n                    let candidateNodes = items.map((item) => item.ref.current!);\n\n                    if (['ArrowUp', 'End'].includes(event.key)) {\n                      candidateNodes = candidateNodes.slice().reverse();\n                    }\n                    if (['ArrowUp', 'ArrowDown'].includes(event.key)) {\n                      const currentElement = event.target as SelectItemElement;\n                      const currentIndex = candidateNodes.indexOf(currentElement);\n                      candidateNodes = candidateNodes.slice(currentIndex + 1);\n                    }\n\n                    /**\n                     * Imperative focus during keydown is risky so we prevent React's batching updates\n                     * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n                     */\n                    setTimeout(() => focusFirst(candidateNodes));\n\n                    event.preventDefault();\n                  }\n                })}\n              />\n            </DismissableLayer>\n          </FocusScope>\n        </RemoveScroll>\n      </SelectContentProvider>\n    );\n  }\n);\n\nSelectContentImpl.displayName = CONTENT_IMPL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItemAlignedPosition\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_ALIGNED_POSITION_NAME = 'SelectItemAlignedPosition';\n\ntype SelectItemAlignedPositionElement = React.ElementRef<typeof Primitive.div>;\ninterface SelectItemAlignedPositionProps extends PrimitiveDivProps, SelectPopperPrivateProps {}\n\nconst SelectItemAlignedPosition = React.forwardRef<\n  SelectItemAlignedPositionElement,\n  SelectItemAlignedPositionProps\n>((props: ScopedProps<SelectItemAlignedPositionProps>, forwardedRef) => {\n  const { __scopeSelect, onPlaced, ...popperProps } = props;\n  const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n  const contentContext = useSelectContentContext(CONTENT_NAME, __scopeSelect);\n  const [contentWrapper, setContentWrapper] = React.useState<HTMLDivElement | null>(null);\n  const [content, setContent] = React.useState<SelectItemAlignedPositionElement | null>(null);\n  const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n  const getItems = useCollection(__scopeSelect);\n  const shouldExpandOnScrollRef = React.useRef(false);\n  const shouldRepositionRef = React.useRef(true);\n\n  const { viewport, selectedItem, selectedItemText, focusSelectedItem } = contentContext;\n  const position = React.useCallback(() => {\n    if (\n      context.trigger &&\n      context.valueNode &&\n      contentWrapper &&\n      content &&\n      viewport &&\n      selectedItem &&\n      selectedItemText\n    ) {\n      const triggerRect = context.trigger.getBoundingClientRect();\n\n      // -----------------------------------------------------------------------------------------\n      //  Horizontal positioning\n      // -----------------------------------------------------------------------------------------\n      const contentRect = content.getBoundingClientRect();\n      const valueNodeRect = context.valueNode.getBoundingClientRect();\n      const itemTextRect = selectedItemText.getBoundingClientRect();\n\n      if (context.dir !== 'rtl') {\n        const itemTextOffset = itemTextRect.left - contentRect.left;\n        const left = valueNodeRect.left - itemTextOffset;\n        const leftDelta = triggerRect.left - left;\n        const minContentWidth = triggerRect.width + leftDelta;\n        const contentWidth = Math.max(minContentWidth, contentRect.width);\n        const rightEdge = window.innerWidth - CONTENT_MARGIN;\n        const clampedLeft = clamp(left, [CONTENT_MARGIN, rightEdge - contentWidth]);\n\n        contentWrapper.style.minWidth = minContentWidth + 'px';\n        contentWrapper.style.left = clampedLeft + 'px';\n      } else {\n        const itemTextOffset = contentRect.right - itemTextRect.right;\n        const right = window.innerWidth - valueNodeRect.right - itemTextOffset;\n        const rightDelta = window.innerWidth - triggerRect.right - right;\n        const minContentWidth = triggerRect.width + rightDelta;\n        const contentWidth = Math.max(minContentWidth, contentRect.width);\n        const leftEdge = window.innerWidth - CONTENT_MARGIN;\n        const clampedRight = clamp(right, [CONTENT_MARGIN, leftEdge - contentWidth]);\n\n        contentWrapper.style.minWidth = minContentWidth + 'px';\n        contentWrapper.style.right = clampedRight + 'px';\n      }\n\n      // -----------------------------------------------------------------------------------------\n      // Vertical positioning\n      // -----------------------------------------------------------------------------------------\n      const items = getItems();\n      const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n      const itemsHeight = viewport.scrollHeight;\n\n      const contentStyles = window.getComputedStyle(content);\n      const contentBorderTopWidth = parseInt(contentStyles.borderTopWidth, 10);\n      const contentPaddingTop = parseInt(contentStyles.paddingTop, 10);\n      const contentBorderBottomWidth = parseInt(contentStyles.borderBottomWidth, 10);\n      const contentPaddingBottom = parseInt(contentStyles.paddingBottom, 10);\n      const fullContentHeight = contentBorderTopWidth + contentPaddingTop + itemsHeight + contentPaddingBottom + contentBorderBottomWidth; // prettier-ignore\n      const minContentHeight = Math.min(selectedItem.offsetHeight * 5, fullContentHeight);\n\n      const viewportStyles = window.getComputedStyle(viewport);\n      const viewportPaddingTop = parseInt(viewportStyles.paddingTop, 10);\n      const viewportPaddingBottom = parseInt(viewportStyles.paddingBottom, 10);\n\n      const topEdgeToTriggerMiddle = triggerRect.top + triggerRect.height / 2 - CONTENT_MARGIN;\n      const triggerMiddleToBottomEdge = availableHeight - topEdgeToTriggerMiddle;\n\n      const selectedItemHalfHeight = selectedItem.offsetHeight / 2;\n      const itemOffsetMiddle = selectedItem.offsetTop + selectedItemHalfHeight;\n      const contentTopToItemMiddle = contentBorderTopWidth + contentPaddingTop + itemOffsetMiddle;\n      const itemMiddleToContentBottom = fullContentHeight - contentTopToItemMiddle;\n\n      const willAlignWithoutTopOverflow = contentTopToItemMiddle <= topEdgeToTriggerMiddle;\n\n      if (willAlignWithoutTopOverflow) {\n        const isLastItem = selectedItem === items[items.length - 1].ref.current;\n        contentWrapper.style.bottom = 0 + 'px';\n        const viewportOffsetBottom =\n          content.clientHeight - viewport.offsetTop - viewport.offsetHeight;\n        const clampedTriggerMiddleToBottomEdge = Math.max(\n          triggerMiddleToBottomEdge,\n          selectedItemHalfHeight +\n            // viewport might have padding bottom, include it to avoid a scrollable viewport\n            (isLastItem ? viewportPaddingBottom : 0) +\n            viewportOffsetBottom +\n            contentBorderBottomWidth\n        );\n        const height = contentTopToItemMiddle + clampedTriggerMiddleToBottomEdge;\n        contentWrapper.style.height = height + 'px';\n      } else {\n        const isFirstItem = selectedItem === items[0].ref.current;\n        contentWrapper.style.top = 0 + 'px';\n        const clampedTopEdgeToTriggerMiddle = Math.max(\n          topEdgeToTriggerMiddle,\n          contentBorderTopWidth +\n            viewport.offsetTop +\n            // viewport might have padding top, include it to avoid a scrollable viewport\n            (isFirstItem ? viewportPaddingTop : 0) +\n            selectedItemHalfHeight\n        );\n        const height = clampedTopEdgeToTriggerMiddle + itemMiddleToContentBottom;\n        contentWrapper.style.height = height + 'px';\n        viewport.scrollTop = contentTopToItemMiddle - topEdgeToTriggerMiddle + viewport.offsetTop;\n      }\n\n      contentWrapper.style.margin = `${CONTENT_MARGIN}px 0`;\n      contentWrapper.style.minHeight = minContentHeight + 'px';\n      contentWrapper.style.maxHeight = availableHeight + 'px';\n      // -----------------------------------------------------------------------------------------\n\n      onPlaced?.();\n\n      // we don't want the initial scroll position adjustment to trigger \"expand on scroll\"\n      // so we explicitly turn it on only after they've registered.\n      requestAnimationFrame(() => (shouldExpandOnScrollRef.current = true));\n    }\n  }, [\n    getItems,\n    context.trigger,\n    context.valueNode,\n    contentWrapper,\n    content,\n    viewport,\n    selectedItem,\n    selectedItemText,\n    context.dir,\n    onPlaced,\n  ]);\n\n  useLayoutEffect(() => position(), [position]);\n\n  // copy z-index from content to wrapper\n  const [contentZIndex, setContentZIndex] = React.useState<string>();\n  useLayoutEffect(() => {\n    if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n  }, [content]);\n\n  // When the viewport becomes scrollable at the top, the scroll up button will mount.\n  // Because it is part of the normal flow, it will push down the viewport, thus throwing our\n  // trigger => selectedItem alignment off by the amount the viewport was pushed down.\n  // We wait for this to happen and then re-run the positining logic one more time to account for it.\n  const handleScrollButtonChange = React.useCallback(\n    (node: SelectScrollButtonImplElement | null) => {\n      if (node && shouldRepositionRef.current === true) {\n        position();\n        focusSelectedItem?.();\n        shouldRepositionRef.current = false;\n      }\n    },\n    [position, focusSelectedItem]\n  );\n\n  return (\n    <SelectViewportProvider\n      scope={__scopeSelect}\n      contentWrapper={contentWrapper}\n      shouldExpandOnScrollRef={shouldExpandOnScrollRef}\n      onScrollButtonChange={handleScrollButtonChange}\n    >\n      <div\n        ref={setContentWrapper}\n        style={{\n          display: 'flex',\n          flexDirection: 'column',\n          position: 'fixed',\n          zIndex: contentZIndex,\n        }}\n      >\n        <Primitive.div\n          {...popperProps}\n          ref={composedRefs}\n          style={{\n            // When we get the height of the content, it includes borders. If we were to set\n            // the height without having `boxSizing: 'border-box'` it would be too big.\n            boxSizing: 'border-box',\n            // We need to ensure the content doesn't get taller than the wrapper\n            maxHeight: '100%',\n            ...popperProps.style,\n          }}\n        />\n      </div>\n    </SelectViewportProvider>\n  );\n});\n\nSelectItemAlignedPosition.displayName = ITEM_ALIGNED_POSITION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectPopperPosition\n * -----------------------------------------------------------------------------------------------*/\n\nconst POPPER_POSITION_NAME = 'SelectPopperPosition';\n\ntype SelectPopperPositionElement = React.ElementRef<typeof PopperPrimitive.Content>;\ntype PopperContentProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Content>;\ninterface SelectPopperPositionProps extends PopperContentProps, SelectPopperPrivateProps {}\n\nconst SelectPopperPosition = React.forwardRef<\n  SelectPopperPositionElement,\n  SelectPopperPositionProps\n>((props: ScopedProps<SelectPopperPositionProps>, forwardedRef) => {\n  const {\n    __scopeSelect,\n    align = 'start',\n    collisionPadding = CONTENT_MARGIN,\n    ...popperProps\n  } = props;\n  const popperScope = usePopperScope(__scopeSelect);\n\n  return (\n    <PopperPrimitive.Content\n      {...popperScope}\n      {...popperProps}\n      ref={forwardedRef}\n      align={align}\n      collisionPadding={collisionPadding}\n      style={{\n        // Ensure border-box for floating-ui calculations\n        boxSizing: 'border-box',\n        ...popperProps.style,\n        // re-namespace exposed content custom properties\n        ...{\n          '--radix-select-content-transform-origin': 'var(--radix-popper-transform-origin)',\n          '--radix-select-content-available-width': 'var(--radix-popper-available-width)',\n          '--radix-select-content-available-height': 'var(--radix-popper-available-height)',\n          '--radix-select-trigger-width': 'var(--radix-popper-anchor-width)',\n          '--radix-select-trigger-height': 'var(--radix-popper-anchor-height)',\n        },\n      }}\n    />\n  );\n});\n\nSelectPopperPosition.displayName = POPPER_POSITION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectViewport\n * -----------------------------------------------------------------------------------------------*/\n\ntype SelectViewportContextValue = {\n  contentWrapper?: HTMLDivElement | null;\n  shouldExpandOnScrollRef?: React.RefObject<boolean>;\n  onScrollButtonChange?: (node: SelectScrollButtonImplElement | null) => void;\n};\n\nconst [SelectViewportProvider, useSelectViewportContext] =\n  createSelectContext<SelectViewportContextValue>(CONTENT_NAME, {});\n\nconst VIEWPORT_NAME = 'SelectViewport';\n\ntype SelectViewportElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = Radix.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface SelectViewportProps extends PrimitiveDivProps {}\n\nconst SelectViewport = React.forwardRef<SelectViewportElement, SelectViewportProps>(\n  (props: ScopedProps<SelectViewportProps>, forwardedRef) => {\n    const { __scopeSelect, ...viewportProps } = props;\n    const contentContext = useSelectContentContext(VIEWPORT_NAME, __scopeSelect);\n    const viewportContext = useSelectViewportContext(VIEWPORT_NAME, __scopeSelect);\n    const composedRefs = useComposedRefs(forwardedRef, contentContext.onViewportChange);\n    const prevScrollTopRef = React.useRef(0);\n    return (\n      <>\n        {/* Hide scrollbars cross-browser and enable momentum scroll for touch devices */}\n        <style\n          dangerouslySetInnerHTML={{\n            __html: `[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}`,\n          }}\n        />\n        <Collection.Slot scope={__scopeSelect}>\n          <Primitive.div\n            data-radix-select-viewport=\"\"\n            role=\"presentation\"\n            {...viewportProps}\n            ref={composedRefs}\n            style={{\n              // we use position: 'relative' here on the `viewport` so that when we call\n              // `selectedItem.offsetTop` in calculations, the offset is relative to the viewport\n              // (independent of the scrollUpButton).\n              position: 'relative',\n              flex: 1,\n              overflow: 'auto',\n              ...viewportProps.style,\n            }}\n            onScroll={composeEventHandlers(viewportProps.onScroll, (event) => {\n              const viewport = event.currentTarget;\n              const { contentWrapper, shouldExpandOnScrollRef } = viewportContext;\n              if (shouldExpandOnScrollRef?.current && contentWrapper) {\n                const scrolledBy = Math.abs(prevScrollTopRef.current - viewport.scrollTop);\n                if (scrolledBy > 0) {\n                  const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n                  const cssMinHeight = parseFloat(contentWrapper.style.minHeight);\n                  const cssHeight = parseFloat(contentWrapper.style.height);\n                  const prevHeight = Math.max(cssMinHeight, cssHeight);\n\n                  if (prevHeight < availableHeight) {\n                    const nextHeight = prevHeight + scrolledBy;\n                    const clampedNextHeight = Math.min(availableHeight, nextHeight);\n                    const heightDiff = nextHeight - clampedNextHeight;\n\n                    contentWrapper.style.height = clampedNextHeight + 'px';\n                    if (contentWrapper.style.bottom === '0px') {\n                      viewport.scrollTop = heightDiff > 0 ? heightDiff : 0;\n                      // ensure the content stays pinned to the bottom\n                      contentWrapper.style.justifyContent = 'flex-end';\n                    }\n                  }\n                }\n              }\n              prevScrollTopRef.current = viewport.scrollTop;\n            })}\n          />\n        </Collection.Slot>\n      </>\n    );\n  }\n);\n\nSelectViewport.displayName = VIEWPORT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'SelectGroup';\n\ntype SelectGroupContextValue = { id: string };\n\nconst [SelectGroupContextProvider, useSelectGroupContext] =\n  createSelectContext<SelectGroupContextValue>(GROUP_NAME);\n\ntype SelectGroupElement = React.ElementRef<typeof Primitive.div>;\ninterface SelectGroupProps extends PrimitiveDivProps {}\n\nconst SelectGroup = React.forwardRef<SelectGroupElement, SelectGroupProps>(\n  (props: ScopedProps<SelectGroupProps>, forwardedRef) => {\n    const { __scopeSelect, ...groupProps } = props;\n    const groupId = useId();\n    return (\n      <SelectGroupContextProvider scope={__scopeSelect} id={groupId}>\n        <Primitive.div role=\"group\" aria-labelledby={groupId} {...groupProps} ref={forwardedRef} />\n      </SelectGroupContextProvider>\n    );\n  }\n);\n\nSelectGroup.displayName = GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectLabel\n * -----------------------------------------------------------------------------------------------*/\n\nconst LABEL_NAME = 'SelectLabel';\n\ntype SelectLabelElement = React.ElementRef<typeof Primitive.div>;\ninterface SelectLabelProps extends PrimitiveDivProps {}\n\nconst SelectLabel = React.forwardRef<SelectLabelElement, SelectLabelProps>(\n  (props: ScopedProps<SelectLabelProps>, forwardedRef) => {\n    const { __scopeSelect, ...labelProps } = props;\n    const groupContext = useSelectGroupContext(LABEL_NAME, __scopeSelect);\n    return <Primitive.div id={groupContext.id} {...labelProps} ref={forwardedRef} />;\n  }\n);\n\nSelectLabel.displayName = LABEL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'SelectItem';\n\ntype SelectItemContextValue = {\n  value: string;\n  disabled: boolean;\n  textId: string;\n  isSelected: boolean;\n  onItemTextChange(node: SelectItemTextElement | null): void;\n};\n\nconst [SelectItemContextProvider, useSelectItemContext] =\n  createSelectContext<SelectItemContextValue>(ITEM_NAME);\n\ntype SelectItemElement = React.ElementRef<typeof Primitive.div>;\ninterface SelectItemProps extends PrimitiveDivProps {\n  value: string;\n  disabled?: boolean;\n  textValue?: string;\n}\n\nconst SelectItem = React.forwardRef<SelectItemElement, SelectItemProps>(\n  (props: ScopedProps<SelectItemProps>, forwardedRef) => {\n    const {\n      __scopeSelect,\n      value,\n      disabled = false,\n      textValue: textValueProp,\n      ...itemProps\n    } = props;\n    const context = useSelectContext(ITEM_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_NAME, __scopeSelect);\n    const isSelected = context.value === value;\n    const [textValue, setTextValue] = React.useState(textValueProp ?? '');\n    const [isFocused, setIsFocused] = React.useState(false);\n    const composedRefs = useComposedRefs(forwardedRef, (node) =>\n      contentContext.itemRefCallback?.(node, value, disabled)\n    );\n    const textId = useId();\n\n    const handleSelect = () => {\n      if (!disabled) {\n        context.onValueChange(value);\n        context.onOpenChange(false);\n      }\n    };\n\n    return (\n      <SelectItemContextProvider\n        scope={__scopeSelect}\n        value={value}\n        disabled={disabled}\n        textId={textId}\n        isSelected={isSelected}\n        onItemTextChange={React.useCallback((node) => {\n          setTextValue((prevTextValue) => prevTextValue || (node?.textContent ?? '').trim());\n        }, [])}\n      >\n        <Collection.ItemSlot\n          scope={__scopeSelect}\n          value={value}\n          disabled={disabled}\n          textValue={textValue}\n        >\n          <Primitive.div\n            role=\"option\"\n            aria-labelledby={textId}\n            data-highlighted={isFocused ? '' : undefined}\n            // `isFocused` caveat fixes stuttering in VoiceOver\n            aria-selected={isSelected && isFocused}\n            data-state={isSelected ? 'checked' : 'unchecked'}\n            aria-disabled={disabled || undefined}\n            data-disabled={disabled ? '' : undefined}\n            tabIndex={disabled ? undefined : -1}\n            {...itemProps}\n            ref={composedRefs}\n            onFocus={composeEventHandlers(itemProps.onFocus, () => setIsFocused(true))}\n            onBlur={composeEventHandlers(itemProps.onBlur, () => setIsFocused(false))}\n            onPointerUp={composeEventHandlers(itemProps.onPointerUp, handleSelect)}\n            onPointerMove={composeEventHandlers(itemProps.onPointerMove, (event) => {\n              if (disabled) {\n                contentContext.onItemLeave?.();\n              } else {\n                // even though safari doesn't support this option, it's acceptable\n                // as it only means it might scroll a few pixels when using the pointer.\n                event.currentTarget.focus({ preventScroll: true });\n              }\n            })}\n            onPointerLeave={composeEventHandlers(itemProps.onPointerLeave, (event) => {\n              if (event.currentTarget === document.activeElement) {\n                contentContext.onItemLeave?.();\n              }\n            })}\n            onKeyDown={composeEventHandlers(itemProps.onKeyDown, (event) => {\n              const isTypingAhead = contentContext.searchRef?.current !== '';\n              if (isTypingAhead && event.key === ' ') return;\n              if (SELECTION_KEYS.includes(event.key)) handleSelect();\n              // prevent page scroll if using the space key to select an item\n              if (event.key === ' ') event.preventDefault();\n            })}\n          />\n        </Collection.ItemSlot>\n      </SelectItemContextProvider>\n    );\n  }\n);\n\nSelectItem.displayName = ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItemText\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_TEXT_NAME = 'SelectItemText';\n\ntype SelectItemTextElement = React.ElementRef<typeof Primitive.span>;\ninterface SelectItemTextProps extends PrimitiveSpanProps {}\n\nconst SelectItemText = React.forwardRef<SelectItemTextElement, SelectItemTextProps>(\n  (props: ScopedProps<SelectItemTextProps>, forwardedRef) => {\n    // We ignore `className` and `style` as this part shouldn't be styled.\n    const { __scopeSelect, className, style, ...itemTextProps } = props;\n    const context = useSelectContext(ITEM_TEXT_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_TEXT_NAME, __scopeSelect);\n    const itemContext = useSelectItemContext(ITEM_TEXT_NAME, __scopeSelect);\n    const nativeOptionsContext = useSelectNativeOptionsContext(ITEM_TEXT_NAME, __scopeSelect);\n    const [itemTextNode, setItemTextNode] = React.useState<SelectItemTextElement | null>(null);\n    const composedRefs = useComposedRefs(\n      forwardedRef,\n      (node) => setItemTextNode(node),\n      itemContext.onItemTextChange,\n      (node) => contentContext.itemTextRefCallback?.(node, itemContext.value, itemContext.disabled)\n    );\n\n    const textContent = itemTextNode?.textContent;\n    const nativeOption = React.useMemo(\n      () => (\n        <option key={itemContext.value} value={itemContext.value} disabled={itemContext.disabled}>\n          {textContent}\n        </option>\n      ),\n      [itemContext.disabled, itemContext.value, textContent]\n    );\n\n    const { onNativeOptionAdd, onNativeOptionRemove } = nativeOptionsContext;\n    useLayoutEffect(() => {\n      onNativeOptionAdd(nativeOption);\n      return () => onNativeOptionRemove(nativeOption);\n    }, [onNativeOptionAdd, onNativeOptionRemove, nativeOption]);\n\n    return (\n      <>\n        <Primitive.span id={itemContext.textId} {...itemTextProps} ref={composedRefs} />\n\n        {/* Portal the select item text into the trigger value node */}\n        {itemContext.isSelected && context.valueNode && !context.valueNodeHasChildren\n          ? ReactDOM.createPortal(itemTextProps.children, context.valueNode)\n          : null}\n      </>\n    );\n  }\n);\n\nSelectItemText.displayName = ITEM_TEXT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItemIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_INDICATOR_NAME = 'SelectItemIndicator';\n\ntype SelectItemIndicatorElement = React.ElementRef<typeof Primitive.span>;\ninterface SelectItemIndicatorProps extends PrimitiveSpanProps {}\n\nconst SelectItemIndicator = React.forwardRef<SelectItemIndicatorElement, SelectItemIndicatorProps>(\n  (props: ScopedProps<SelectItemIndicatorProps>, forwardedRef) => {\n    const { __scopeSelect, ...itemIndicatorProps } = props;\n    const itemContext = useSelectItemContext(ITEM_INDICATOR_NAME, __scopeSelect);\n    return itemContext.isSelected ? (\n      <Primitive.span aria-hidden {...itemIndicatorProps} ref={forwardedRef} />\n    ) : null;\n  }\n);\n\nSelectItemIndicator.displayName = ITEM_INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectScrollUpButton\n * -----------------------------------------------------------------------------------------------*/\n\nconst SCROLL_UP_BUTTON_NAME = 'SelectScrollUpButton';\n\ntype SelectScrollUpButtonElement = SelectScrollButtonImplElement;\ninterface SelectScrollUpButtonProps extends Omit<SelectScrollButtonImplProps, 'onAutoScroll'> {}\n\nconst SelectScrollUpButton = React.forwardRef<\n  SelectScrollUpButtonElement,\n  SelectScrollUpButtonProps\n>((props: ScopedProps<SelectScrollUpButtonProps>, forwardedRef) => {\n  const contentContext = useSelectContentContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n  const viewportContext = useSelectViewportContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n  const [canScrollUp, setCanScrollUp] = React.useState(false);\n  const composedRefs = useComposedRefs(forwardedRef, viewportContext.onScrollButtonChange);\n\n  useLayoutEffect(() => {\n    if (contentContext.viewport && contentContext.isPositioned) {\n      const viewport = contentContext.viewport;\n      function handleScroll() {\n        const canScrollUp = viewport.scrollTop > 0;\n        setCanScrollUp(canScrollUp);\n      }\n      handleScroll();\n      viewport.addEventListener('scroll', handleScroll);\n      return () => viewport.removeEventListener('scroll', handleScroll);\n    }\n  }, [contentContext.viewport, contentContext.isPositioned]);\n\n  return canScrollUp ? (\n    <SelectScrollButtonImpl\n      {...props}\n      ref={composedRefs}\n      onAutoScroll={() => {\n        const { viewport, selectedItem } = contentContext;\n        if (viewport && selectedItem) {\n          viewport.scrollTop = viewport.scrollTop - selectedItem.offsetHeight;\n        }\n      }}\n    />\n  ) : null;\n});\n\nSelectScrollUpButton.displayName = SCROLL_UP_BUTTON_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectScrollDownButton\n * -----------------------------------------------------------------------------------------------*/\n\nconst SCROLL_DOWN_BUTTON_NAME = 'SelectScrollDownButton';\n\ntype SelectScrollDownButtonElement = SelectScrollButtonImplElement;\ninterface SelectScrollDownButtonProps extends Omit<SelectScrollButtonImplProps, 'onAutoScroll'> {}\n\nconst SelectScrollDownButton = React.forwardRef<\n  SelectScrollDownButtonElement,\n  SelectScrollDownButtonProps\n>((props: ScopedProps<SelectScrollDownButtonProps>, forwardedRef) => {\n  const contentContext = useSelectContentContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n  const viewportContext = useSelectViewportContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n  const [canScrollDown, setCanScrollDown] = React.useState(false);\n  const composedRefs = useComposedRefs(forwardedRef, viewportContext.onScrollButtonChange);\n\n  useLayoutEffect(() => {\n    if (contentContext.viewport && contentContext.isPositioned) {\n      const viewport = contentContext.viewport;\n      function handleScroll() {\n        const maxScroll = viewport.scrollHeight - viewport.clientHeight;\n        // we use Math.ceil here because if the UI is zoomed-in\n        // `scrollTop` is not always reported as an integer\n        const canScrollDown = Math.ceil(viewport.scrollTop) < maxScroll;\n        setCanScrollDown(canScrollDown);\n      }\n      handleScroll();\n      viewport.addEventListener('scroll', handleScroll);\n      return () => viewport.removeEventListener('scroll', handleScroll);\n    }\n  }, [contentContext.viewport, contentContext.isPositioned]);\n\n  return canScrollDown ? (\n    <SelectScrollButtonImpl\n      {...props}\n      ref={composedRefs}\n      onAutoScroll={() => {\n        const { viewport, selectedItem } = contentContext;\n        if (viewport && selectedItem) {\n          viewport.scrollTop = viewport.scrollTop + selectedItem.offsetHeight;\n        }\n      }}\n    />\n  ) : null;\n});\n\nSelectScrollDownButton.displayName = SCROLL_DOWN_BUTTON_NAME;\n\ntype SelectScrollButtonImplElement = React.ElementRef<typeof Primitive.div>;\ninterface SelectScrollButtonImplProps extends PrimitiveDivProps {\n  onAutoScroll(): void;\n}\n\nconst SelectScrollButtonImpl = React.forwardRef<\n  SelectScrollButtonImplElement,\n  SelectScrollButtonImplProps\n>((props: ScopedProps<SelectScrollButtonImplProps>, forwardedRef) => {\n  const { __scopeSelect, onAutoScroll, ...scrollIndicatorProps } = props;\n  const contentContext = useSelectContentContext('SelectScrollButton', __scopeSelect);\n  const autoScrollTimerRef = React.useRef<number | null>(null);\n  const getItems = useCollection(__scopeSelect);\n\n  const clearAutoScrollTimer = React.useCallback(() => {\n    if (autoScrollTimerRef.current !== null) {\n      window.clearInterval(autoScrollTimerRef.current);\n      autoScrollTimerRef.current = null;\n    }\n  }, []);\n\n  React.useEffect(() => {\n    return () => clearAutoScrollTimer();\n  }, [clearAutoScrollTimer]);\n\n  // When the viewport becomes scrollable on either side, the relevant scroll button will mount.\n  // Because it is part of the normal flow, it will push down (top button) or shrink (bottom button)\n  // the viewport, potentially causing the active item to now be partially out of view.\n  // We re-run the `scrollIntoView` logic to make sure it stays within the viewport.\n  useLayoutEffect(() => {\n    const activeItem = getItems().find((item) => item.ref.current === document.activeElement);\n    activeItem?.ref.current?.scrollIntoView({ block: 'nearest' });\n  }, [getItems]);\n\n  return (\n    <Primitive.div\n      aria-hidden\n      {...scrollIndicatorProps}\n      ref={forwardedRef}\n      style={{ flexShrink: 0, ...scrollIndicatorProps.style }}\n      onPointerDown={composeEventHandlers(scrollIndicatorProps.onPointerDown, () => {\n        if (autoScrollTimerRef.current === null) {\n          autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n        }\n      })}\n      onPointerMove={composeEventHandlers(scrollIndicatorProps.onPointerMove, () => {\n        contentContext.onItemLeave?.();\n        if (autoScrollTimerRef.current === null) {\n          autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n        }\n      })}\n      onPointerLeave={composeEventHandlers(scrollIndicatorProps.onPointerLeave, () => {\n        clearAutoScrollTimer();\n      })}\n    />\n  );\n});\n\n/* -------------------------------------------------------------------------------------------------\n * SelectSeparator\n * -----------------------------------------------------------------------------------------------*/\n\nconst SEPARATOR_NAME = 'SelectSeparator';\n\ntype SelectSeparatorElement = React.ElementRef<typeof Primitive.div>;\ninterface SelectSeparatorProps extends PrimitiveDivProps {}\n\nconst SelectSeparator = React.forwardRef<SelectSeparatorElement, SelectSeparatorProps>(\n  (props: ScopedProps<SelectSeparatorProps>, forwardedRef) => {\n    const { __scopeSelect, ...separatorProps } = props;\n    return <Primitive.div aria-hidden {...separatorProps} ref={forwardedRef} />;\n  }\n);\n\nSelectSeparator.displayName = SEPARATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'SelectArrow';\n\ntype SelectArrowElement = React.ElementRef<typeof PopperPrimitive.Arrow>;\ntype PopperArrowProps = Radix.ComponentPropsWithoutRef<typeof PopperPrimitive.Arrow>;\ninterface SelectArrowProps extends PopperArrowProps {}\n\nconst SelectArrow = React.forwardRef<SelectArrowElement, SelectArrowProps>(\n  (props: ScopedProps<SelectArrowProps>, forwardedRef) => {\n    const { __scopeSelect, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(ARROW_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ARROW_NAME, __scopeSelect);\n    return context.open && contentContext.position === 'popper' ? (\n      <PopperPrimitive.Arrow {...popperScope} {...arrowProps} ref={forwardedRef} />\n    ) : null;\n  }\n);\n\nSelectArrow.displayName = ARROW_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst BubbleSelect = React.forwardRef<HTMLSelectElement, React.ComponentPropsWithoutRef<'select'>>(\n  (props, forwardedRef) => {\n    const { value, ...selectProps } = props;\n    const ref = React.useRef<HTMLSelectElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const prevValue = usePrevious(value);\n\n    // Bubble value change to parents (e.g form change event)\n    React.useEffect(() => {\n      const select = ref.current!;\n      const selectProto = window.HTMLSelectElement.prototype;\n      const descriptor = Object.getOwnPropertyDescriptor(\n        selectProto,\n        'value'\n      ) as PropertyDescriptor;\n      const setValue = descriptor.set;\n      if (prevValue !== value && setValue) {\n        const event = new Event('change', { bubbles: true });\n        setValue.call(select, value);\n        select.dispatchEvent(event);\n      }\n    }, [prevValue, value]);\n\n    /**\n     * We purposefully use a `select` here to support form autofill as much\n     * as possible.\n     *\n     * We purposefully do not add the `value` attribute here to allow the value\n     * to be set programatically and bubble to any parent form `onChange` event.\n     * Adding the `value` will cause React to consider the programatic\n     * dispatch a duplicate and it will get swallowed.\n     *\n     * We use `VisuallyHidden` rather than `display: \"none\"` because Safari autofill\n     * won't work otherwise.\n     */\n    return (\n      <VisuallyHidden asChild>\n        <select {...selectProps} ref={composedRefs} defaultValue={value} />\n      </VisuallyHidden>\n    );\n  }\n);\n\nBubbleSelect.displayName = 'BubbleSelect';\n\nfunction useTypeaheadSearch(onSearchChange: (search: string) => void) {\n  const handleSearchChange = useCallbackRef(onSearchChange);\n  const searchRef = React.useRef('');\n  const timerRef = React.useRef(0);\n\n  const handleTypeaheadSearch = React.useCallback(\n    (key: string) => {\n      const search = searchRef.current + key;\n      handleSearchChange(search);\n\n      (function updateSearch(value: string) {\n        searchRef.current = value;\n        window.clearTimeout(timerRef.current);\n        // Reset `searchRef` 1 second after it was last updated\n        if (value !== '') timerRef.current = window.setTimeout(() => updateSearch(''), 1000);\n      })(search);\n    },\n    [handleSearchChange]\n  );\n\n  const resetTypeahead = React.useCallback(() => {\n    searchRef.current = '';\n    window.clearTimeout(timerRef.current);\n  }, []);\n\n  React.useEffect(() => {\n    return () => window.clearTimeout(timerRef.current);\n  }, []);\n\n  return [searchRef, handleTypeaheadSearch, resetTypeahead] as const;\n}\n\n/**\n * This is the \"meat\" of the typeahead matching logic. It takes in a list of items,\n * the search and the current item, and returns the next item (or `undefined`).\n *\n * We normalize the search because if a user has repeatedly pressed a character,\n * we want the exact same behavior as if we only had that one character\n * (ie. cycle through items starting with that character)\n *\n * We also reorder the items by wrapping the array around the current item.\n * This is so we always look forward from the current item, and picking the first\n * item will always be the correct one.\n *\n * Finally, if the normalized search is exactly one character, we exclude the\n * current item from the values because otherwise it would be the first to match always\n * and focus would never move. This is as opposed to the regular case, where we\n * don't want focus to move if the current item still matches.\n */\nfunction findNextItem<T extends { textValue: string }>(\n  items: T[],\n  search: string,\n  currentItem?: T\n) {\n  const isRepeated = search.length > 1 && Array.from(search).every((char) => char === search[0]);\n  const normalizedSearch = isRepeated ? search[0] : search;\n  const currentItemIndex = currentItem ? items.indexOf(currentItem) : -1;\n  let wrappedItems = wrapArray(items, Math.max(currentItemIndex, 0));\n  const excludeCurrentItem = normalizedSearch.length === 1;\n  if (excludeCurrentItem) wrappedItems = wrappedItems.filter((v) => v !== currentItem);\n  const nextItem = wrappedItems.find((item) =>\n    item.textValue.toLowerCase().startsWith(normalizedSearch.toLowerCase())\n  );\n  return nextItem !== currentItem ? nextItem : undefined;\n}\n\n/**\n * Wraps an array around itself at a given start index\n * Example: `wrapArray(['a', 'b', 'c', 'd'], 2) === ['c', 'd', 'a', 'b']`\n */\nfunction wrapArray<T>(array: T[], startIndex: number) {\n  return array.map((_, index) => array[(startIndex + index) % array.length]);\n}\n\nconst Root = Select;\nconst Trigger = SelectTrigger;\nconst Value = SelectValue;\nconst Icon = SelectIcon;\nconst Portal = SelectPortal;\nconst Content = SelectContent;\nconst Viewport = SelectViewport;\nconst Group = SelectGroup;\nconst Label = SelectLabel;\nconst Item = SelectItem;\nconst ItemText = SelectItemText;\nconst ItemIndicator = SelectItemIndicator;\nconst ScrollUpButton = SelectScrollUpButton;\nconst ScrollDownButton = SelectScrollDownButton;\nconst Separator = SelectSeparator;\nconst Arrow = SelectArrow;\n\nexport {\n  createSelectScope,\n  //\n  Select,\n  SelectTrigger,\n  SelectValue,\n  SelectIcon,\n  SelectPortal,\n  SelectContent,\n  SelectViewport,\n  SelectGroup,\n  SelectLabel,\n  SelectItem,\n  SelectItemText,\n  SelectItemIndicator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n  SelectSeparator,\n  SelectArrow,\n  //\n  Root,\n  Trigger,\n  Value,\n  Icon,\n  Portal,\n  Content,\n  Viewport,\n  Group,\n  Label,\n  Item,\n  ItemText,\n  ItemIndicator,\n  ScrollUpButton,\n  ScrollDownButton,\n  Separator,\n  Arrow,\n};\nexport type {\n  SelectProps,\n  SelectTriggerProps,\n  SelectValueProps,\n  SelectIconProps,\n  SelectPortalProps,\n  SelectContentProps,\n  SelectViewportProps,\n  SelectGroupProps,\n  SelectLabelProps,\n  SelectItemProps,\n  SelectItemTextProps,\n  SelectItemIndicatorProps,\n  SelectScrollUpButtonProps,\n  SelectScrollDownButtonProps,\n  SelectSeparatorProps,\n  SelectArrowProps,\n};\n", "import React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { Slot } from '@radix-ui/react-slot';\n\nimport type * as Radix from '@radix-ui/react-primitive';\n\ntype SlotProps = Radix.ComponentPropsWithoutRef<typeof Slot>;\ntype CollectionElement = HTMLElement;\ninterface CollectionProps extends SlotProps {\n  scope: any;\n}\n\n// We have resorted to returning slots directly rather than exposing primitives that can then\n// be slotted like `<CollectionItem as={Slot}>…</CollectionItem>`.\n// This is because we encountered issues with generic types that cannot be statically analysed\n// due to creating them dynamically via createCollection.\n\nfunction createCollection<ItemElement extends HTMLElement, ItemData = {}>(name: string) {\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionProvider\n   * ---------------------------------------------------------------------------------------------*/\n\n  const PROVIDER_NAME = name + 'CollectionProvider';\n  const [createCollectionContext, createCollectionScope] = createContextScope(PROVIDER_NAME);\n\n  type ContextValue = {\n    collectionRef: React.RefObject<CollectionElement>;\n    itemMap: Map<React.RefObject<ItemElement>, { ref: React.RefObject<ItemElement> } & ItemData>;\n  };\n\n  const [CollectionProviderImpl, useCollectionContext] = createCollectionContext<ContextValue>(\n    PROVIDER_NAME,\n    { collectionRef: { current: null }, itemMap: new Map() }\n  );\n\n  const CollectionProvider: React.FC<{ children?: React.ReactNode; scope: any }> = (props) => {\n    const { scope, children } = props;\n    const ref = React.useRef<CollectionElement>(null);\n    const itemMap = React.useRef<ContextValue['itemMap']>(new Map()).current;\n    return (\n      <CollectionProviderImpl scope={scope} itemMap={itemMap} collectionRef={ref}>\n        {children}\n      </CollectionProviderImpl>\n    );\n  };\n\n  CollectionProvider.displayName = PROVIDER_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionSlot\n   * ---------------------------------------------------------------------------------------------*/\n\n  const COLLECTION_SLOT_NAME = name + 'CollectionSlot';\n\n  const CollectionSlot = React.forwardRef<CollectionElement, CollectionProps>(\n    (props, forwardedRef) => {\n      const { scope, children } = props;\n      const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n      const composedRefs = useComposedRefs(forwardedRef, context.collectionRef);\n      return <Slot ref={composedRefs}>{children}</Slot>;\n    }\n  );\n\n  CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionItem\n   * ---------------------------------------------------------------------------------------------*/\n\n  const ITEM_SLOT_NAME = name + 'CollectionItemSlot';\n  const ITEM_DATA_ATTR = 'data-radix-collection-item';\n\n  type CollectionItemSlotProps = ItemData & {\n    children: React.ReactNode;\n    scope: any;\n  };\n\n  const CollectionItemSlot = React.forwardRef<ItemElement, CollectionItemSlotProps>(\n    (props, forwardedRef) => {\n      const { scope, children, ...itemData } = props;\n      const ref = React.useRef<ItemElement>(null);\n      const composedRefs = useComposedRefs(forwardedRef, ref);\n      const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n\n      React.useEffect(() => {\n        context.itemMap.set(ref, { ref, ...(itemData as unknown as ItemData) });\n        return () => void context.itemMap.delete(ref);\n      });\n\n      return (\n        <Slot {...{ [ITEM_DATA_ATTR]: '' }} ref={composedRefs}>\n          {children}\n        </Slot>\n      );\n    }\n  );\n\n  CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * useCollection\n   * ---------------------------------------------------------------------------------------------*/\n\n  function useCollection(scope: any) {\n    const context = useCollectionContext(name + 'CollectionConsumer', scope);\n\n    const getItems = React.useCallback(() => {\n      const collectionNode = context.collectionRef.current;\n      if (!collectionNode) return [];\n      const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));\n      const items = Array.from(context.itemMap.values());\n      const orderedItems = items.sort(\n        (a, b) => orderedNodes.indexOf(a.ref.current!) - orderedNodes.indexOf(b.ref.current!)\n      );\n      return orderedItems;\n    }, [context.collectionRef, context.itemMap]);\n\n    return getItems;\n  }\n\n  return [\n    { Provider: CollectionProvider, Slot: CollectionSlot, ItemSlot: CollectionItemSlot },\n    useCollection,\n    createCollectionScope,\n  ] as const;\n}\n\nexport { createCollection };\nexport type { CollectionProps };\n", "import * as React from 'react';\n\nfunction usePrevious<T>(value: T) {\n  const ref = React.useRef({ value, previous: value });\n\n  // We compare values before making an update to ensure that\n  // a change has been made. This ensures the previous value is\n  // persisted correctly between renders.\n  return React.useMemo(() => {\n    if (ref.current.value !== value) {\n      ref.current.previous = ref.current.value;\n      ref.current.value = value;\n    }\n    return ref.current.previous;\n  }, [value]);\n}\n\nexport { usePrevious };\n"], "names": ["ChevronDown", "createLucideIcon", "d", "key", "$ae6933e535247d3d$export$7d15b64cf5a3a4c4", "value", "_ref", "min", "max", "Math", "$e42e1063c40fb3ef$export$b9ecd428b558ff10", "originalEventHandler", "ourEventHandler", "checkForDefaultPrevented", "arguments", "length", "undefined", "event", "defaultPrevented", "$c512c27ab02ef895$export$50c7b4e9d9f19c1", "scopeName", "createContextScopeDeps", "defaultContexts", "createScope", "scopeContexts", "map", "defaultContext", "$3bkAK$createContext", "scope", "contexts", "$3bkAK$useMemo", "rootComponentName", "BaseContext", "index", "Provider", "props", "children", "context", "Context", "Object", "values", "$3bkAK$createElement", "displayName", "consumerName", "$3bkAK$useContext", "Error", "$c512c27ab02ef895$var$composeContextScopes", "_len", "scopes", "Array", "_key", "baseScope", "createScope1", "scopeHooks", "useScope", "overrideScopes", "nextScopes1", "reduce", "nextScopes", "$6ed0406888f73fc4$export$43e446d32b3d21af", "refs", "node", "for<PERSON>ach", "ref", "current", "$6ed0406888f73fc4$var$setRef", "$6ed0406888f73fc4$export$c7b2cbe3552a0d05", "_len2", "_key2", "$3vqmr$useCallback", "$5e63c961fc1ce211$export$8c6ed5c666ac1360", "$9IrjX$forwardRef", "forwardedRef", "slotProps", "childrenA<PERSON>y", "$9IrjX$Children", "toArray", "slottable", "find", "$5e63c961fc1ce211$var$isSlottable", "newElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "child", "count", "only", "$9IrjX$isValidElement", "$9IrjX$createElement", "$5e63c961fc1ce211$var$SlotClone", "$9IrjX$babelruntimehelpersesmextends", "$9IrjX$cloneElement", "$5e63c961fc1ce211$var$mergeProps", "$9IrjX$composeRefs", "$5e63c961fc1ce211$export$d9f1ccf0bdb05d45", "$9IrjX$Fragment", "type", "childProps", "overrideProps", "propName", "slotPropValue", "childPropV<PERSON>ue", "test", "filter", "Boolean", "join", "$f631663db3294ace$var$DirectionContext", "$7Gjcd$createContext", "$8927f6f2acc4f386$export$250ffa63cdc0d034", "primitive", "Node", "$4q5Fq$forwardRef", "<PERSON><PERSON><PERSON><PERSON>", "primitiveProps", "Comp", "$4q5Fq$Slot", "$4q5Fq$useEffect", "window", "Symbol", "for", "$4q5Fq$createElement", "$4q5Fq$babelruntimehelpersesmextends", "$b1b2314f5f9a1d84$export$25bec8c6f54ee79a", "callback", "callback<PERSON><PERSON>", "$lwiWj$useRef", "$lwiWj$useEffect", "$lwiWj$useMemo", "_callbackRef$current", "args", "call", "$5cb92bef7577960e$var$CONTEXT_UPDATE", "$5cb92bef7577960e$var$POINTER_DOWN_OUTSIDE", "$5cb92bef7577960e$var$FOCUS_OUTSIDE", "$5cb92bef7577960e$var$originalBodyPointerEvents", "$5cb92bef7577960e$var$DismissableLayerContext", "$kqwpH$createContext", "layers", "Set", "layersWithOutsidePointerEventsDisabled", "branches", "$5cb92bef7577960e$export$177fb62ff3ec1f22", "$kqwpH$forwardRef", "_node$ownerDocument", "disableOutsidePointerEvents", "onEscapeKeyDown", "onPointerDownOutside", "onFocusOutside", "onInteractOutside", "on<PERSON><PERSON><PERSON>", "layerProps", "$kqwpH$useContext", "node1", "setNode", "$kqwpH$useState", "ownerDocument", "globalThis", "document", "force", "composedRefs", "$kqwpH$useComposedRefs", "from", "highestLayerWithOutsidePointerEventsDisabled", "slice", "highestLayerWithOutsidePointerEventsDisabledIndex", "indexOf", "isBodyPointerEventsDisabled", "size", "isPointerEventsEnabled", "pointerDownOutside", "handlePointerDownOutside", "$kqwpH$useCallbackRef", "isPointerInsideReactTreeRef", "$kqwpH$useRef", "handleClickRef", "$kqwpH$useEffect", "handlePointerDown", "target", "eventDetail", "originalEvent", "handleAndDispatchPointerDownOutsideEvent", "$5cb92bef7577960e$var$handleAndDispatchCustomEvent", "discrete", "pointerType", "removeEventListener", "addEventListener", "once", "timerId", "setTimeout", "clearTimeout", "onPointerDownCapture", "$5cb92bef7577960e$var$usePointerDownOutside", "isPointerDownOnBranch", "some", "branch", "contains", "focusOutside", "handleFocusOutside", "isFocusInsideReactTreeRef", "handleFocus", "onFocusCapture", "onBlurCapture", "$5cb92bef7577960e$var$useFocusOutside", "onEscapeKeyDownProp", "$hPSQ5$useCallbackRef", "$hPSQ5$useEffect", "handleKeyDown", "$kqwpH$useEscapeKeydown", "preventDefault", "body", "style", "pointerEvents", "add", "$5cb92bef7577960e$var$dispatchUpdate", "delete", "handleUpdate", "$kqwpH$createElement", "$kqwpH$Primitive", "div", "$kqwpH$babelruntimehelpersesmextends", "$kqwpH$composeEventHandlers", "CustomEvent", "dispatchEvent", "name", "handler", "detail", "bubbles", "cancelable", "$4q5Fq$flushSync", "$kqwpH$dispatchDiscreteCustomEvent", "$3db38b7d1fb3fe6a$var$count", "$3db38b7d1fb3fe6a$export$b7ece24a22aeda8c", "$1wErz$useEffect", "_edgeGuards$", "_edgeGuards$2", "edgeGuards", "querySelectorAll", "insertAdjacentElement", "$3db38b7d1fb3fe6a$var$createFocusGuard", "remove", "element", "createElement", "setAttribute", "tabIndex", "cssText", "$d3863c46a17e8a28$var$AUTOFOCUS_ON_MOUNT", "$d3863c46a17e8a28$var$AUTOFOCUS_ON_UNMOUNT", "$d3863c46a17e8a28$var$EVENT_OPTIONS", "$d3863c46a17e8a28$export$20e40289641fbbb6", "$45QHv$forwardRef", "loop", "trapped", "onMountAutoFocus", "onMountAutoFocusProp", "onUnmountAutoFocus", "onUnmountAutoFocusProp", "scopeProps", "container1", "<PERSON><PERSON><PERSON><PERSON>", "$45QHv$useState", "$45QHv$useCallbackRef", "lastFocusedElementRef", "$45QHv$useRef", "$45QHv$useComposedRefs", "focusScope", "paused", "pause", "this", "resume", "$45QHv$useEffect", "handleFocusIn", "$d3863c46a17e8a28$var$focus", "select", "handleFocusOut", "relatedTarget", "handleMutations", "mutations", "focusedElement", "activeElement", "mutation", "removedNodes", "mutationObserver", "MutationObserver", "observe", "childList", "subtree", "disconnect", "$d3863c46a17e8a28$var$focusScopesStack", "previouslyFocusedElement", "mountEvent", "candidates", "candidate", "$d3863c46a17e8a28$var$focusFirst", "items", "$d3863c46a17e8a28$var$getTabbableCandidates", "item", "tagName", "unmountEvent", "$45QHv$useCallback", "isTabKey", "altKey", "ctrl<PERSON>ey", "metaKey", "container", "currentTarget", "first", "last", "$d3863c46a17e8a28$var$findVisible", "reverse", "$d3863c46a17e8a28$var$getTabbableEdges", "shift<PERSON>ey", "$45QHv$createElement", "$45QHv$Primitive", "$45QHv$babelruntimehelpersesmextends", "onKeyDown", "nodes", "walker", "createTreeWalker", "Node<PERSON><PERSON><PERSON>", "SHOW_ELEMENT", "acceptNode", "isHiddenInput", "disabled", "hidden", "FILTER_SKIP", "FILTER_ACCEPT", "nextNode", "push", "currentNode", "elements", "$d3863c46a17e8a28$var$isHidden", "upTo", "getComputedStyle", "visibility", "display", "parentElement", "focus", "preventScroll", "HTMLInputElement", "$d3863c46a17e8a28$var$isSelectableInput", "stack", "activeFocusScope", "$d3863c46a17e8a28$var$arrayRemove", "unshift", "_stack$", "$d3863c46a17e8a28$var$createFocusScopesStack", "array", "updatedArray", "splice", "$9f79659886946c16$export$e5c5a5f917a5871c", "$dxlwH$useLayoutEffect", "$1746a345f3d73bb7$var$useReactId", "$2AODx$react", "toString", "$1746a345f3d73bb7$var$count", "$1746a345f3d73bb7$export$f680877a34711e37", "deterministicId", "id", "setId", "$2AODx$useLayoutEffect", "reactId", "String", "$cf1ac5d9fe0e8206$var$POPPER_NAME", "$cf1ac5d9fe0e8206$var$createPopperContext", "$cf1ac5d9fe0e8206$export$722aac194ae923", "$kY93V$createContextScope", "$cf1ac5d9fe0e8206$var$PopperProvider", "$cf1ac5d9fe0e8206$var$usePopperContext", "$cf1ac5d9fe0e8206$export$badac9ada3a0bdf9", "__scope<PERSON>opper", "anchor", "setAnchor", "$kY93V$useState", "$kY93V$createElement", "onAnchorChange", "$cf1ac5d9fe0e8206$var$ANCHOR_NAME", "$cf1ac5d9fe0e8206$export$ecd4e1ccab6ed6d", "$kY93V$forwardRef", "virtualRef", "anchorProps", "$kY93V$useRef", "$kY93V$useComposedRefs", "$kY93V$useEffect", "$kY93V$Primitive", "$kY93V$babelruntimehelpersesmextends", "$cf1ac5d9fe0e8206$var$CONTENT_NAME", "$cf1ac5d9fe0e8206$var$PopperContentProvider", "$cf1ac5d9fe0e8206$var$useContentContext", "$cf1ac5d9fe0e8206$export$bc4ae5855d3c4fc", "_arrowSize$width", "_arrowSize$height", "_middlewareData$arrow", "_middlewareData$arrow2", "_middlewareData$arrow3", "_middlewareData$trans", "_middlewareData$trans2", "_middlewareData$hide", "side", "sideOffset", "align", "alignOffset", "arrowPadding", "collisionBoundary", "collisionPadding", "collisionPaddingProp", "sticky", "hideWhenDetached", "avoidCollisions", "onPlaced", "contentProps", "content", "<PERSON><PERSON><PERSON><PERSON>", "arrow", "setArrow", "arrowSize", "setSize", "$9gyGR$useState", "$9gyGR$useLayoutEffect", "width", "offsetWidth", "height", "offsetHeight", "resizeObserver", "ResizeObserver", "entries", "isArray", "entry", "borderSizeEntry", "borderSize", "box", "unobserve", "$kY93V$useSize", "arrow<PERSON>idth", "arrowHeight", "desiredPlacement", "top", "right", "bottom", "left", "boundary", "hasExplicitBoundaries", "detectOverflowOptions", "padding", "$cf1ac5d9fe0e8206$var$isNotNull", "altBoundary", "floatingStyles", "placement", "isPositioned", "middlewareData", "$kY93V$useFloating", "strategy", "whileElementsMounted", "$kY93V$autoUpdate", "reference", "middleware", "$kY93V$offset", "mainAxis", "alignmentAxis", "$kY93V$shift", "crossAxis", "limiter", "$kY93V$limitShift", "$kY93V$flip", "$kY93V$size", "apply", "rects", "availableWidth", "availableHeight", "anchorWidth", "anchorHeight", "contentStyle", "floating", "setProperty", "$kY93V$arrow", "$cf1ac5d9fe0e8206$var$transformOrigin", "$kY93V$hide", "placedSide", "placedAlign", "$cf1ac5d9fe0e8206$var$getSideAndAlignFromPlacement", "handlePlaced", "$kY93V$useCallbackRef", "$kY93V$useLayoutEffect", "arrowX", "x", "arrowY", "y", "cannotCenterArrow", "centerOffset", "contentZIndex", "setContentZIndex", "zIndex", "setFloating", "transform", "min<PERSON><PERSON><PERSON>", "transform<PERSON><PERSON>in", "dir", "onArrowChange", "shouldHideArrow", "animation", "opacity", "hide", "referenceHidden", "options", "fn", "data", "_middlewareData$arrow4", "_middlewareData$arrow5", "_middlewareData$arrow6", "_middlewareData$arrow7", "_middlewareData$arrow8", "isArrowHidden", "noArrowAlign", "start", "center", "end", "arrowXCenter", "arrowYCenter", "split", "$cf1ac5d9fe0e8206$export$be92b6f5f03c0fe9", "$cf1ac5d9fe0e8206$export$b688253958b8dfe7", "$cf1ac5d9fe0e8206$export$7c6e2c02157bb7d2", "$f1701beae083dbae$export$602eac185826482c", "$7SXl2$forwardRef", "_globalThis$document", "portalProps", "$7SXl2$reactdom", "$7SXl2$createElement", "$7SXl2$Primitive", "$7SXl2$babelruntimehelpersesmextends", "$71cd76cc60e0454e$export$6f32135080cb4c3", "prop", "defaultProp", "onChange", "uncontrolledProp", "setUncontrolledProp", "_ref2", "uncontrolledState", "$bnPw9$useState", "prevValueRef", "$bnPw9$useRef", "handleChange", "$bnPw9$useCallbackRef", "$bnPw9$useEffect", "$71cd76cc60e0454e$var$useUncontrolledState", "isControlled", "value1", "$bnPw9$useCallback", "nextValue", "$ea1ef594cf570d83$export$439d29a4e110a164", "$kVwnw$forwardRef", "$kVwnw$createElement", "$kVwnw$Primitive", "span", "$kVwnw$babelruntimehelpersesmextends", "position", "border", "margin", "overflow", "clip", "whiteSpace", "wordWrap", "effectCar", "createSidecarMedium", "nothing", "RemoveScroll", "React", "parentRef", "_a", "onScrollCapture", "onWheelCapture", "onTouchMoveCapture", "callbacks", "setCallbacks", "forwardProps", "className", "removeScrollBar", "enabled", "shards", "sideCar", "noIsolation", "inert", "allowPinchZoom", "_b", "as", "Container", "rest", "__rest", "SideCar", "containerRef", "useMergeRefs", "containerProps", "__assign", "lockRef", "defaultProps", "classNames", "fullWidth", "fullWidthClassName", "zeroRight", "zeroRightClassName", "passiveSupported", "defineProperty", "get", "err", "nonPassive", "passive", "elementCanBeScrolled", "styles", "overflowY", "overflowX", "alwaysContainsScroll", "locationCouldBeScrolled", "axis", "ShadowRoot", "host", "elementCouldBeScrolled", "getScrollVariables", "parentNode", "elementCouldBeVScrolled", "elementCouldBeHScrolled", "scrollTop", "scrollHeight", "clientHeight", "scrollLeft", "scrollWidth", "clientWidth", "getHScrollVariables", "getTouchXY", "changedTouches", "clientX", "clientY", "getDeltaXY", "deltaX", "deltaY", "extractRef", "generateStyle", "concat", "idCounter", "lockStack", "exportSidecar", "shouldPreventQueue", "touchStartRef", "activeAxis", "Style", "styleSingleton", "lastProps", "classList", "allow_1", "__spread<PERSON><PERSON>y", "el", "shouldCancelEvent", "parent", "touches", "currentAxis", "touch", "touchStart", "moveDirection", "abs", "canBeScrolledInMainDirection", "cancelingAxis", "end<PERSON>ar<PERSON>", "sourceDelta", "noOverscroll", "directionFactor", "direction", "getDirectionFactor", "delta", "targetInLock", "shouldCancelScroll", "isDeltaPositive", "availableScroll", "availableScrollTop", "elementScroll", "handleScroll", "shouldPrevent", "_event", "sourceEvent", "e", "should", "shardNodes", "shouldCancel", "scrollTouchStart", "scrollWheel", "scrollTouchMove", "inst", "RemoveScrollBar", "gapMode", "ReactRemoveScroll", "$cc7e05a45900e73f$var$OPEN_KEYS", "$cc7e05a45900e73f$var$SELECTION_KEYS", "$cc7e05a45900e73f$var$SELECT_NAME", "$cc7e05a45900e73f$var$Collection", "$cc7e05a45900e73f$var$useCollection", "$cc7e05a45900e73f$var$createCollectionScope", "PROVIDER_NAME", "createCollectionContext", "createCollectionScope", "$6vYhU$createContextScope", "CollectionProviderImpl", "useCollectionContext", "collectionRef", "itemMap", "Map", "CollectionProvider", "$6vYhU$react", "COLLECTION_SLOT_NAME", "CollectionSlot", "$6vYhU$useComposedRefs", "$6vYhU$Slot", "ITEM_SLOT_NAME", "ITEM_DATA_ATTR", "CollectionItemSlot", "itemData", "set", "Slot", "ItemSlot", "collectionNode", "orderedNodes", "sort", "a", "b", "$01b9c$createCollection", "$cc7e05a45900e73f$var$createSelectContext", "$cc7e05a45900e73f$export$286727a75dc039bd", "$01b9c$createContextScope", "$01b9c$createPopperScope", "$cc7e05a45900e73f$var$usePopperScope", "$cc7e05a45900e73f$var$SelectProvider", "$cc7e05a45900e73f$var$useSelectContext", "$cc7e05a45900e73f$var$SelectNativeOptionsProvider", "$cc7e05a45900e73f$var$useSelectNativeOptionsContext", "$cc7e05a45900e73f$export$ef9b1a59e592288f", "__scopeSelect", "open", "openProp", "defaultOpen", "onOpenChange", "valueProp", "defaultValue", "onValueChange", "autoComplete", "required", "popperScope", "trigger", "setTrigger", "$01b9c$useState", "valueNode", "setValueNode", "valueNodeHasChildren", "setValueNodeHasChildren", "localDir", "globalDir", "$7Gjcd$useContext", "$01b9c$useDirection", "<PERSON><PERSON><PERSON>", "$01b9c$useControllableState", "setValue", "triggerPointerDownPosRef", "$01b9c$useRef", "isFormControl", "closest", "nativeOptionsSet", "setNativeOptionsSet", "nativeSelectKey", "option", "$01b9c$createElement", "$01b9c$Root", "onTriggerChange", "onValueNodeChange", "onValueNodeHasChildrenChange", "contentId", "$01b9c$useId", "onNativeOptionAdd", "$01b9c$useCallback", "prev", "onNativeOptionRemove", "optionsSet", "$cc7e05a45900e73f$var$BubbleSelect", "$cc7e05a45900e73f$var$TRIGGER_NAME", "$cc7e05a45900e73f$export$3ac1e88a1c0b9f1", "$01b9c$forwardRef", "triggerProps", "isDisabled", "$01b9c$useComposedRefs", "getItems", "searchRef", "handleTypeaheadSearch", "resetTypeahead", "$cc7e05a45900e73f$var$useTypeaheadSearch", "search", "enabledItems", "currentItem", "nextItem", "$cc7e05a45900e73f$var$findNextItem", "handleOpen", "$01b9c$Anchor", "$01b9c$babelruntimehelpersesmextends", "$01b9c$Primitive", "button", "role", "onClick", "$01b9c$composeEventHandlers", "onPointerDown", "hasPointerCapture", "pointerId", "releasePointerCapture", "round", "pageX", "pageY", "isTypingAhead", "includes", "$cc7e05a45900e73f$var$VALUE_NAME", "$cc7e05a45900e73f$export$e288731fd71264f0", "placeholder", "valueProps", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "$01b9c$useLayoutEffect", "$cc7e05a45900e73f$export$99b400cabb58c515", "iconProps", "$cc7e05a45900e73f$export$b2af6c9944296213", "$01b9c$Portal", "$cc7e05a45900e73f$var$CONTENT_NAME", "$cc7e05a45900e73f$export$c973a4b3cb86a03d", "fragment", "setFragment", "DocumentFragment", "frag", "$01b9c$createPortal", "$cc7e05a45900e73f$var$SelectContentProvider", "$cc7e05a45900e73f$var$SelectContentImpl", "$cc7e05a45900e73f$var$CONTENT_MARGIN", "$cc7e05a45900e73f$var$useSelectContentContext", "onCloseAutoFocus", "viewport", "setViewport", "selectedItem", "setSelectedItem", "selectedItemText", "setSelectedItemText", "setIsPositioned", "firstValidItemFoundRef", "$01b9c$useEffect", "$01b9c$hideOthers", "$01b9c$useFocusGuards", "focusFirst", "firstItem", "restItems", "lastItem", "PREVIOUSLY_FOCUSED_ELEMENT", "scrollIntoView", "block", "focusSelectedItem", "pointerMoveDel<PERSON>", "handlePointerMove", "_triggerPointerDownPo", "_triggerPointerDownPo2", "_triggerPointerDownPo3", "_triggerPointerDownPo4", "handlePointerUp", "capture", "close", "itemRefCallback", "isFirstValidItem", "handleItemLeave", "itemTextRefCallback", "SelectPosition", "$cc7e05a45900e73f$var$SelectPopperPosition", "$cc7e05a45900e73f$var$SelectItemAlignedPosition", "popperContentProps", "onViewportChange", "onItemLeave", "$01b9c$RemoveScroll", "$01b9c$Slot", "$01b9c$FocusScope", "_context$trigger", "$01b9c$DismissableLayer", "onContextMenu", "flexDirection", "outline", "isModifierKey", "candidateNodes", "currentElement", "currentIndex", "popperProps", "contentContext", "contentWrapper", "setContentWrapper", "shouldExpandOnScrollRef", "shouldRepositionRef", "triggerRect", "getBoundingClientRect", "contentRect", "valueNodeRect", "itemTextRect", "itemTextOffset", "leftDelta", "minC<PERSON>nt<PERSON>id<PERSON>", "contentWidth", "rightEdge", "innerWidth", "clampedLeft", "$01b9c$clamp", "<PERSON><PERSON><PERSON><PERSON>", "leftEdge", "clampedRight", "innerHeight", "itemsHeight", "contentStyles", "contentBorderTopWidth", "parseInt", "borderTopWidth", "contentPaddingTop", "paddingTop", "contentBorderBottomWidth", "borderBottomWidth", "fullContentHeight", "paddingBottom", "minContentHeight", "viewportStyles", "viewportPaddingTop", "viewportPaddingBottom", "topEdgeToTriggerMiddle", "triggerMiddleToBottomEdge", "selectedItemHalfHeight", "contentTopToItemMiddle", "offsetTop", "itemMiddleToContentBottom", "isLastItem", "viewportOffsetBottom", "isFirstItem", "minHeight", "maxHeight", "requestAnimationFrame", "handleScrollButtonChange", "$cc7e05a45900e73f$var$SelectViewportProvider", "onScrollButtonChange", "boxSizing", "$01b9c$Content", "$cc7e05a45900e73f$var$useSelectViewportContext", "$cc7e05a45900e73f$var$VIEWPORT_NAME", "$cc7e05a45900e73f$export$9ed6e7b40248d36d", "viewportProps", "viewportContext", "prevScrollTopRef", "$01b9c$Fragment", "dangerouslySetInnerHTML", "__html", "flex", "onScroll", "scrolledBy", "cssMinHeight", "parseFloat", "cssHeight", "prevHeight", "nextHeight", "clampedNextHeight", "heightDiff", "justifyContent", "$cc7e05a45900e73f$var$GROUP_NAME", "$cc7e05a45900e73f$var$SelectGroupContextProvider", "$cc7e05a45900e73f$var$useSelectGroupContext", "$cc7e05a45900e73f$export$ee25a334c55de1f4", "groupProps", "groupId", "$cc7e05a45900e73f$var$LABEL_NAME", "$cc7e05a45900e73f$export$f67338d29bd972f8", "labelProps", "groupContext", "$cc7e05a45900e73f$var$ITEM_NAME", "$cc7e05a45900e73f$var$SelectItemContextProvider", "$cc7e05a45900e73f$var$useSelectItemContext", "$cc7e05a45900e73f$export$13ef48a934230896", "textValue", "textValueProp", "itemProps", "isSelected", "setTextValue", "isFocused", "setIsFocused", "_contentContext$itemR", "textId", "handleSelect", "onItemTextChange", "prevTextValue", "_node$textContent", "textContent", "trim", "onFocus", "onBlur", "onPointerUp", "onPointerMove", "_contentContext$onIte", "onPointerLeave", "_contentContext$onIte2", "_contentContext$searc", "$cc7e05a45900e73f$var$ITEM_TEXT_NAME", "$cc7e05a45900e73f$export$3572fb0fb821ff49", "itemTextProps", "itemContext", "nativeOptionsContext", "itemTextNode", "setItemTextNode", "_contentContext$itemT", "nativeOption", "$01b9c$useMemo", "$cc7e05a45900e73f$var$ITEM_INDICATOR_NAME", "$cc7e05a45900e73f$export$6b9198de19accfe6", "itemIndicatorProps", "$cc7e05a45900e73f$export$eba4b1df07cb1d3", "separatorProps", "selectProps", "prevValue", "$8LvvK$useRef", "previous", "$8LvvK$useMemo", "$01b9c$usePrevious", "selectProto", "HTMLSelectElement", "prototype", "getOwnPropertyDescriptor", "Event", "$01b9c$VisuallyHidden", "onSearchChange", "handleSearchChange", "$01b9c$useCallbackRef", "timerRef", "updateSearch", "normalizedSearch", "every", "char", "currentItemIndex", "wrappedItems", "startIndex", "_", "v", "toLowerCase", "startsWith", "$cc7e05a45900e73f$export$be92b6f5f03c0fe9", "$cc7e05a45900e73f$export$41fb9f06171c75f4", "$cc7e05a45900e73f$export$4c8d1a57a761ef94", "$cc7e05a45900e73f$export$f04a61298a47a40f", "$cc7e05a45900e73f$export$602eac185826482c", "$cc7e05a45900e73f$export$7c6e2c02157bb7d2", "$cc7e05a45900e73f$export$d5c6c08dc2d3ca7", "$cc7e05a45900e73f$export$eb2fcfdbd7ba97d4", "$cc7e05a45900e73f$export$b04be29aa201d4f5", "$cc7e05a45900e73f$export$6d08773d2e66f8f2", "$cc7e05a45900e73f$export$d6e5bf9c43ea9319", "$cc7e05a45900e73f$export$c3468e2714d175fa", "$cc7e05a45900e73f$export$1ff3c3f08ae963c0"], "sourceRoot": ""}