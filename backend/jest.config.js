module.exports = {
  testEnvironment: 'node',
  testMatch: ['**/test/__tests__/**/*.test.js'],
  collectCoverage: false, // Disable coverage for now to focus on test execution
  coverageDirectory: 'coverage',
  collectCoverageFrom: [
    '**/*.{js,jsx}',
    '!**/node_modules/**',
    '!**/coverage/**',
    '!**/test/**',
    '!**/jest.config.js',
    '!**/server.js',
  ],
  testPathIgnorePatterns: [
    '/node_modules/'
  ],
  transform: {},
  transformIgnorePatterns: [
    '/node_modules/(?!(mongodb-memory-server|@babel/runtime/helpers/esm/))',
  ],
  setupFiles: [
    '<rootDir>/test/setupEnv.js', // Load environment variables first
    '<rootDir>/test/setup.js'     // Then setup test environment
  ],
  setupFilesAfterEnv: ['<rootDir>/test/setupAfterEnv.js'],
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/$1',
    '^@config/email$': '<rootDir>/__mocks__/email.js',
  },
  verbose: true,
  detectOpenHandles: true,
  forceExit: true,
  testTimeout: 30000, // Increase test timeout to 30 seconds
};
