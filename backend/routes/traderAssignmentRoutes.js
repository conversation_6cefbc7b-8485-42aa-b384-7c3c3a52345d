const express = require('express');
const router = express.Router();
const { protect, authorize } = require('../middleware/auth');
const {
  createAssignment,
  getAssignments,
  updateAssignment,
  deleteAssignment,
  getMyAssignments
} = require('../controllers/traderAssignmentController');

// Admin routes
router.route('/')
  .post(protect, authorize('admin'), createAssignment)
  .get(protect, authorize('admin'), getAssignments);

router.route('/:id')
  .put(protect, authorize('admin'), updateAssignment)
  .delete(protect, authorize('admin'), deleteAssignment);

// Trader route to get their own assignments
router.get('/me/assignments', protect, authorize('trader'), getMyAssignments);

module.exports = router;
