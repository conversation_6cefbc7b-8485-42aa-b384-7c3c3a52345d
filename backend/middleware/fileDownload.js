const fs = require('fs');
const path = require('path');
const mime = require('mime-types');
const { ApiError } = require('../utils/ApiError');
const logger = require('../utils/logger');

// Default download configuration
const defaultConfig = {
  // Root directory for file downloads
  root: process.env.UPLOAD_DIR || path.join(process.cwd(), 'uploads'),
  
  // Allowed file extensions (empty array allows all)
  allowedExtensions: [
    // Images
    '.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg',
    
    // Documents
    '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
    '.txt', '.csv', '.json',
    
    // Archives
    '.zip', '.rar', '.7z', '.tar', '.gz',
  ],
  
  // Maximum file size in bytes (50MB)
  maxFileSize: 50 * 1024 * 1024,
  
  // Whether to allow directory traversal (not recommended)
  allowDirectoryTraversal: false,
  
  // Custom error messages
  errorMessages: {
    notFound: 'File not found',
    forbidden: 'Access to this file is forbidden',
    invalidPath: 'Invalid file path',
    tooLarge: 'File is too large',
    invalidType: 'File type not allowed',
  }
};

/**
 * Middleware to handle secure file downloads
 * @param {Object} options - Configuration options
 * @returns {Function} Express middleware function
 */
const fileDownload = (options = {}) => {
  const config = { ...defaultConfig, ...options };
  
  return async (req, res, next) => {
    try {
      // Get the file path from the URL parameters
      const filePath = decodeURIComponent(req.params[0] || '');
      
      // Prevent directory traversal attacks
      if (config.allowDirectoryTraversal === false && (filePath.includes('../') || filePath.includes('..\\'))) {
        throw new ApiError(400, config.errorMessages.invalidPath);
      }
      
      // Resolve the full file path
      const fullPath = path.join(config.root, filePath);
      
      // Verify the resolved path is within the root directory
      if (!fullPath.startsWith(path.resolve(config.root))) {
        throw new ApiError(403, config.errorMessages.forbidden);
      }
      
      // Check if the file exists
      if (!fs.existsSync(fullPath) || !fs.statSync(fullPath).isFile()) {
        throw new ApiError(404, config.errorMessages.notFound);
      }
      
      // Get file stats
      const stats = fs.statSync(fullPath);
      
      // Check file size
      if (stats.size > config.maxFileSize) {
        throw new ApiError(413, config.errorMessages.tooLarge);
      }
      
      // Check file extension if allowedExtensions is specified
      const ext = path.extname(fullPath).toLowerCase();
      if (config.allowedExtensions.length > 0 && !config.allowedExtensions.includes(ext)) {
        throw new ApiError(415, config.errorMessages.invalidType);
      }
      
      // Get MIME type based on file extension
      const mimeType = mime.lookup(fullPath) || 'application/octet-stream';
      
      // Set response headers
      res.set({
        'Content-Type': mimeType,
        'Content-Length': stats.size,
        'Content-Disposition': `attachment; filename="${path.basename(fullPath)}"`,
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'X-Content-Type-Options': 'nosniff',
      });
      
      // Log the download
      logger.info('File download', {
        filePath: fullPath,
        size: stats.size,
        mimeType,
        ip: req.ip,
        user: req.user ? req.user.id : 'anonymous',
      });
      
      // Stream the file to the response
      const fileStream = fs.createReadStream(fullPath);
      
      // Handle errors during streaming
      fileStream.on('error', (err) => {
        logger.error('Error streaming file', {
          error: err.message,
          stack: err.stack,
          filePath: fullPath,
        });
        
        if (!res.headersSent) {
          res.status(500).end('Error streaming file');
        } else {
          res.end();
        }
      });
      
      // Pipe the file to the response
      fileStream.pipe(res);
      
    } catch (error) {
      next(error);
    }
  };
};

/**
 * Middleware to serve files as attachments (force download)
 * @param {Object} options - Configuration options
 * @returns {Function} Express middleware function
 */
const serveAsAttachment = (options = {}) => {
  return (req, res, next) => {
    // Set Content-Disposition header to force download
    res.attachment = (filename) => {
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      return res;
    };
    
    next();
  };
};

module.exports = {
  fileDownload,
  serveAsAttachment,
  defaultConfig,
};
