import http from './httpService';
import { ApiResponse } from '../types/api';

interface DashboardStats {
  totalRevenue: number;
  activeMerchants: number;
  totalTransactions: number;
  activeTraders: number;
  revenueTrend: number;
  transactionTrend: number;
}

interface RecentTransaction {
  id: string;
  merchantName: string;
  amount: number;
  date: string;
  status: 'completed' | 'pending' | 'failed';
}

interface DashboardStatsParams {
  timeRange?: 'week' | 'month' | 'year';
}

const adminService = {
  // Get dashboard statistics
  async getDashboardStats(params?: DashboardStatsParams): Promise<DashboardStats | undefined> {
    try {
      const response = await http.get('/api/admin/stats');

      // Handle the response from our new endpoint
      if (response.data && response.data.success) {
        return {
          totalRevenue: 0, // Will be updated when we have revenue data
          activeMerchants: response.data.data.activeMerchants || 0,
          totalTransactions: 0, // Will be updated when we have transaction data
          activeTraders: response.data.data.activeTraders || 0,
          revenueTrend: 0, // Will be updated when we have trend data
          transactionTrend: 0 // Will be updated when we have trend data
        };
      }

      throw new Error('Failed to fetch dashboard stats');
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      // Fallback to mock data if API fails
      return {
        totalRevenue: 0,
        activeMerchants: 0,
        totalTransactions: 0,
        activeTraders: 0,
        revenueTrend: 12.5,
        transactionTrend: 8.3
      };
    }
  },

  // Get recent transactions
  async getRecentTransactions(limit: number = 5): Promise<RecentTransaction[] | undefined> {
    try {
      const response = await http.get('/admin/transactions', {
        params: { limit }
      });

      // Handle the actual API response structure
      if (response.data && response.data.transactions) {
        return response.data.transactions.slice(0, limit).map((tx: any) => ({
          id: tx._id,
          merchantName: tx.merchantId?.businessName || 'Unknown Merchant',
          amount: tx.amount,
          status: tx.status,
          date: tx.createdAt
        }));
      }

      return [];
    } catch (error) {
      console.log('Recent transactions API not available, using mock data');
      return [
        {
          id: '1',
          merchantName: 'Tech Solutions Inc',
          amount: 2500,
          status: 'completed',
          date: new Date().toISOString()
        },
        {
          id: '2',
          merchantName: 'Fashion Store',
          amount: 1800,
          status: 'pending',
          date: new Date(Date.now() - 86400000).toISOString()
        }
      ];
    }
  },

  // Get merchant statistics
  async getMerchantStats(merchantId: string) {
    const response = await http.get<ApiResponse<any>>(`/admin/merchants/${merchantId}/stats`);
    return response.data.data;
  },

  // Get system status
  async getSystemStatus() {
    const response = await http.get<ApiResponse<{
      services: Array<{
        name: string;
        status: 'operational' | 'degraded' | 'outage';
        responseTime: number;
        lastChecked: string;
      }>;
    }>>('/admin/system/status');
    return response.data.data;
  },

  // Process payouts
  async processPayouts(merchantIds: string[]) {
    const response = await http.post<ApiResponse<{ success: boolean; message?: string }>>(
      '/admin/payouts/process',
      { merchantIds }
    );
    return response.data;
  },

  // Generate report
  async generateReport(params: {
    type: 'transactions' | 'merchants' | 'traders';
    startDate: string;
    endDate: string;
    format: 'csv' | 'pdf' | 'xlsx';
  }) {
    const response = await http.get('/admin/reports/generate', {
      params,
      responseType: 'blob',
    });
    return response.data;
  },

  // Get activity log
  async getActivityLog(params: {
    page?: number;
    limit?: number;
    type?: string;
    userId?: string;
    startDate?: string;
    endDate?: string;
  }) {
    const response = await http.get<ApiResponse<{
      data: Array<{
        id: string;
        action: string;
        entityType: string;
        entityId: string;
        userId: string;
        userEmail: string;
        metadata: Record<string, any>;
        createdAt: string;
      }>;
      total: number;
      page: number;
      limit: number;
      totalPages: number;
    }>>('/admin/activity-log', { params });
    return response.data;
  },
};

export default adminService;
