/**
 * Success response formatter
 * @param {Object} res - Express response object
 * @param {*} data - Data to be sent in the response
 * @param {string} [message='Success'] - Success message
 * @param {number} [statusCode=200] - HTTP status code
 * @returns {Object} - Formatted success response
 */
const successResponse = (res, data = null, message = 'Success', statusCode = 200) => {
  const response = {
    success: true,
    message,
  };

  if (data !== null) {
    response.data = data;
  }

  return res.status(statusCode).json(response);
};

/**
 * Error response formatter
 * @param {Object} res - Express response object
 * @param {string} message - Error message
 * @param {number} statusCode - HTTP status code
 * @param {*} [error=null] - Error object or additional error details
 * @param {string} [code] - Custom error code
 * @returns {Object} - Formatted error response
 */
const errorResponse = (res, message, statusCode = 500, error = null, code) => {
  const response = {
    success: false,
    message,
  };

  // Add error code if provided
  if (code) {
    response.code = code;
  }

  // Include error details in development
  if (error && process.env.NODE_ENV === 'development') {
    response.error = error;
  }

  return res.status(statusCode).json(response);
};

/**
 * Validation error response formatter
 * @param {Object} res - Express response object
 * @param {Array} errors - Array of validation errors
 * @param {string} [message='Validation Failed'] - Error message
 * @returns {Object} - Formatted validation error response
 */
const validationError = (res, errors, message = 'Validation Failed') => {
  return res.status(400).json({
    success: false,
    message,
    errors: errors.map(err => ({
      field: err.param,
      message: err.msg,
      value: err.value,
      location: err.location,
    })),
  });
};

/**
 * Not found response formatter
 * @param {Object} res - Express response object
 * @param {string} [message='Resource not found'] - Not found message
 * @returns {Object} - Formatted not found response
 */
const notFoundResponse = (res, message = 'Resource not found') => {
  return errorResponse(res, message, 404);
};

/**
 * Unauthorized response formatter
 * @param {Object} res - Express response object
 * @param {string} [message='Unauthorized'] - Unauthorized message
 * @returns {Object} - Formatted unauthorized response
 */
const unauthorizedResponse = (res, message = 'Unauthorized') => {
  return errorResponse(res, message, 401);
};

/**
 * Forbidden response formatter
 * @param {Object} res - Express response object
 * @param {string} [message='Forbidden'] - Forbidden message
 * @returns {Object} - Formatted forbidden response
 */
const forbiddenResponse = (res, message = 'Forbidden') => {
  return errorResponse(res, message, 403);
};

/**
 * Bad request response formatter
 * @param {Object} res - Express response object
 * @param {string} message - Error message
 * @param {*} [error=null] - Error object or additional error details
 * @returns {Object} - Formatted bad request response
 */
const badRequestResponse = (res, message, error = null) => {
  return errorResponse(res, message, 400, error);
};

/**
 * Conflict response formatter
 * @param {Object} res - Express response object
 * @param {string} message - Error message
 * @param {*} [error=null] - Error object or additional error details
 * @returns {Object} - Formatted conflict response
 */
const conflictResponse = (res, message, error = null) => {
  return errorResponse(res, message, 409, error);
};

/**
 * Internal server error response formatter
 * @param {Object} res - Express response object
 * @param {string} [message='Internal Server Error'] - Error message
 * @param {*} [error=null] - Error object or additional error details
 * @returns {Object} - Formatted internal server error response
 */
const serverErrorResponse = (res, message = 'Internal Server Error', error = null) => {
  return errorResponse(res, message, 500, error);
};

/**
 * Rate limit exceeded response formatter
 * @param {Object} res - Express response object
 * @param {string} [message='Too many requests'] - Error message
 * @returns {Object} - Formatted rate limit exceeded response
 */
const tooManyRequestsResponse = (res, message = 'Too many requests') => {
  return errorResponse(res, message, 429);
};

module.exports = {
  successResponse,
  errorResponse,
  validationError,
  notFoundResponse,
  unauthorizedResponse,
  forbiddenResponse,
  badRequestResponse,
  conflictResponse,
  serverErrorResponse,
  tooManyRequestsResponse,
};
