{"version": 3, "file": "static/js/620.cbf32622.chunk.js", "mappings": "ybAaMA,EAAcC,EAAAA,WAAqC,CAACC,EAAOC,KAC/D,MAAM,SAAEC,EAAA,MAAUC,EAAQ,GAAE,OAAEC,EAAS,KAAMC,GAAeL,EAC5D,OACEM,EAAAA,EAAAA,KAACC,EAAAA,GAAUC,IAAV,IACKH,EACJI,IAAKR,EACLE,QACAC,SACAM,QAAQ,YACRC,oBAAoB,OAGnBT,SAAAF,EAAMY,QAAUV,GAAWI,EAAAA,EAAAA,KAAC,WAAQO,OAAO,uBAKlDf,EAAMgB,YAvBO,QA2Bb,IAAMC,EAAOjB,E,mBCVb,IAUMkB,EAAc,UAGbC,EAAqBC,IAAqBC,EAAAA,EAAAA,GAAmBH,IAM7DI,EAAgBC,GAAoBJ,EAAwCD,GAK7EM,EAAiCtB,IACrC,MAAM,cAAEuB,EAAA,SAAerB,GAAaF,GAC7BwB,EAAQC,GAAmB1B,EAAAA,SAA4B,MAC9D,OACEO,EAAAA,EAAAA,KAACc,EAAA,CAAeM,MAAOH,EAAeC,SAAgBG,eAAgBF,EACnEvB,cAKPoB,EAAOR,YAAcE,EAMrB,IAAMY,EAAc,eAQdC,EAAqB9B,EAAAA,WACzB,CAACC,EAAuCC,KACtC,MAAM,cAAEsB,EAAA,WAAeO,KAAeC,GAAgB/B,EAChDgC,EAAUX,EAAiBO,EAAaL,GACxCd,EAAYV,EAAAA,OAA4B,MACxCkC,GAAeC,EAAAA,EAAAA,GAAgBjC,EAAcQ,GASnD,OAPMV,EAAAA,UAAU,KAIdiC,EAAQL,eAAeG,GAAYK,SAAW1B,EAAI0B,WAG7CL,EAAa,MAAOxB,EAAAA,EAAAA,KAACC,EAAAA,GAAU6B,IAAV,IAAkBL,EAAatB,IAAKwB,MAIpEJ,EAAaf,YAAcc,EAM3B,IAAMS,EAAe,iBAUdC,EAAuBC,GAC5BtB,EAA+CoB,GAoB3CG,EAAsBzC,EAAAA,WAC1B,CAACC,EAAwCC,KACvC,MAAM,cACJsB,EAAA,KACAkB,EAAO,oBACPC,EAAa,EAAC,MACdC,EAAQ,qBACRC,EAAc,EAAC,aACfC,EAAe,EAAC,gBAChBC,GAAkB,EAAI,kBACtBC,EAAoB,GACpBC,iBAAkBC,EAAuB,EAAC,OAC1CC,EAAS,2BACTC,GAAmB,EAAK,uBACxBC,EAAyB,qBACzBC,KACGC,GACDtD,EAEEgC,EAAUX,EAAiBgB,EAAcd,IAExCgC,EAASC,GAAoBzD,EAAAA,SAAgC,MAC9DkC,GAAeC,EAAAA,EAAAA,GAAgBjC,EAAewD,GAASD,EAAWC,KAEjEC,EAAOC,GAAkB5D,EAAAA,SAAiC,MAC3D6D,ECnJV,SAAiBC,GACf,MAAOC,EAAMC,GAAiBhE,EAAAA,cAAwD,GAgDtF,OA9CAiE,EAAAA,EAAAA,GAAgB,KACd,GAAIH,EAAS,CAEXE,EAAQ,CAAE5D,MAAO0D,EAAQI,YAAa7D,OAAQyD,EAAQK,eAEtD,MAAMC,EAAiB,IAAIC,eAAgBC,IACzC,IAAKC,MAAMC,QAAQF,GACjB,OAKF,IAAKA,EAAQG,OACX,OAGF,MAAMC,EAAQJ,EAAQ,GACtB,IAAIlE,EACAC,EAEJ,GAAI,kBAAmBqE,EAAO,CAC5B,MAAMC,EAAkBD,EAAqB,cAEvCE,EAAaL,MAAMC,QAAQG,GAAmBA,EAAgB,GAAKA,EACzEvE,EAAQwE,EAAuB,WAC/BvE,EAASuE,EAAsB,SACjC,MAGExE,EAAQ0D,EAAQI,YAChB7D,EAASyD,EAAQK,aAGnBH,EAAQ,CAAE5D,QAAOC,aAKnB,OAFA+D,EAAeS,QAAQf,EAAS,CAAEgB,IAAK,eAEhC,IAAMV,EAAeW,UAAUjB,EACxC,CAGEE,OAAQ,IAET,CAACF,IAEGC,CACT,CDiGsBiB,CAAQrB,GACpBsB,EAAapB,GAAWzD,OAAS,EACjC8E,EAAcrB,GAAWxD,QAAU,EAEnC8E,EAAoBzC,GAAkB,WAAVE,EAAqB,IAAMA,EAAQ,IAE/DK,EAC4B,kBAAzBC,EACHA,EACA,CAAEkC,IAAK,EAAGC,MAAO,EAAGC,OAAQ,EAAGC,KAAM,KAAMrC,GAE3CsC,EAAWjB,MAAMC,QAAQxB,GAAqBA,EAAoB,CAACA,GACnEyC,EAAwBD,EAASf,OAAS,EAE1CiB,EAAwB,CAC5BC,QAAS1C,EACTuC,SAAUA,EAASI,OAAOC,GAE1BC,YAAaL,IAGT,KAAEM,EAAA,eAAMC,EAAA,UAAgBC,EAAA,aAAWC,EAAA,eAAcC,IAAmBC,EAAAA,EAAAA,IAAY,CAEpFC,SAAU,QACVJ,UAAWd,EACXmB,qBAAsB,WAAa,QAAAC,EAAAC,UAAA/B,OAATgC,EAAA,IAAAlC,MAAAgC,GAAAG,EAAA,EAAAA,EAAAH,EAAAG,IAAAD,EAAAC,GAAAF,UAAAE,GAIxB,OAHgBC,EAAAA,EAAAA,OAAcF,EAAM,CAClCG,eAA2C,WAA3BvD,GAGpB,EACAwD,SAAU,CACRC,UAAW7E,EAAQR,QAErBsF,WAAY,EACVC,EAAAA,EAAAA,IAAO,CAAEC,SAAUtE,EAAauC,EAAagC,cAAerE,IAC5DE,IACEoE,EAAAA,EAAAA,IAAM,CACJF,UAAU,EACVG,WAAW,EACXC,QAAoB,YAAXlE,GAAuBmE,EAAAA,EAAAA,WAAe,KAC5C5B,IAEP3C,IAAmBwE,EAAAA,EAAAA,IAAK,IAAK7B,KAC7B3B,EAAAA,EAAAA,IAAK,IACA2B,EACH8B,MAAOC,IAA0D,IAAzD,SAAEZ,EAAA,MAAUa,EAAA,eAAOC,EAAA,gBAAgBC,GAAgBH,EACzD,MAAQrH,MAAOyH,EAAaxH,OAAQyH,GAAiBJ,EAAMZ,UACrDiB,EAAelB,EAASmB,SAASC,MACvCF,EAAaG,YAAY,iCAAkC,GAAGP,OAC9DI,EAAaG,YAAY,kCAAmC,GAAGN,OAC/DG,EAAaG,YAAY,8BAA+B,GAAGL,OAC3DE,EAAaG,YAAY,+BAAgC,GAAGJ,UAGhEnE,IAASwE,EAAAA,EAAAA,IAAgB,CAAErE,QAASH,EAAOgC,QAAS7C,IACpDsF,EAAgB,CAAEnD,aAAYC,gBAC9B9B,IAAoBiF,EAAAA,EAAAA,IAAK,CAAEhC,SAAU,qBAAsBX,QAIxD4C,EAAYC,GAAeC,EAA6BvC,GAEzDwC,GAAeC,EAAAA,EAAAA,GAAepF,IACpCW,EAAAA,EAAAA,GAAgB,KACViC,GACFuC,OAED,CAACvC,EAAcuC,IAElB,MAAME,EAASxC,EAAexC,OAAOiF,EAC/BC,EAAS1C,EAAexC,OAAOmF,EAC/BC,GAA2D,IAAvC5C,EAAexC,OAAOqF,cAEzCC,GAAeC,IAA0BlJ,EAAAA,WAKhD,OAJAiE,EAAAA,EAAAA,GAAgB,KACVT,GAAS0F,GAAiBC,OAAOC,iBAAiB5F,GAAS6F,SAC9D,CAAC7F,KAGFjD,EAAAA,EAAAA,KAAC,OACCG,IAAKqF,EAAKuD,YACV,oCAAkC,GAClCrB,MAAO,IACFjC,EACHuD,UAAWrD,EAAeF,EAAeuD,UAAY,sBACrDC,SAAU,cACVH,OAAQJ,GACR,kCAA4C,CAC1C9C,EAAeiC,iBAAiBQ,EAChCzC,EAAeiC,iBAAiBU,GAChCW,KAAK,QAKHtD,EAAekC,MAAMqB,iBAAmB,CAC1CC,WAAY,SACZC,cAAe,SAMnBC,IAAK5J,EAAM4J,IAEX1J,UAAAI,EAAAA,EAAAA,KAACgC,EAAA,CACCZ,MAAOH,EACP8G,aACAwB,cAAelG,EACf+E,SACAE,SACAkB,gBAAiBhB,GAEjB5I,UAAAI,EAAAA,EAAAA,KAACC,EAAAA,GAAU6B,IAAV,CACC,YAAWiG,EACX,aAAYC,KACRhF,EACJ7C,IAAKwB,EACL+F,MAAO,IACF1E,EAAa0E,MAGhB+B,UAAY9D,OAAwB,EAAT,gBASzCzD,EAAc1B,YAAcuB,EAM5B,IAAM2H,EAAa,cAEbC,EAAoC,CACxC9E,IAAK,SACLC,MAAO,OACPC,OAAQ,MACRC,KAAM,SAOF4E,EAAoBnK,EAAAA,WAAiD,SACzEC,EACAC,GAEA,MAAM,cAAEsB,KAAkBlB,GAAeL,EACnCmK,EAAiB5H,EAAkByH,EAAYzI,GAC/C6I,EAAWH,EAAcE,EAAe9B,YAE9C,OAIE/H,EAAAA,EAAAA,KAAC,QACCG,IAAK0J,EAAeN,cACpB7B,MAAO,CACLqC,SAAU,WACV/E,KAAM6E,EAAezB,OACrBvD,IAAKgF,EAAevB,OACpB,CAACwB,GAAW,EACZjC,gBAAiB,CACfhD,IAAK,GACLC,MAAO,MACPC,OAAQ,WACRC,KAAM,UACN6E,EAAe9B,YACjBiB,UAAW,CACTnE,IAAK,mBACLC,MAAO,iDACPC,OAAQ,iBACRC,KAAM,kDACN6E,EAAe9B,YACjBqB,WAAYS,EAAeL,gBAAkB,cAAW,GAG1D5J,UAAAI,EAAAA,EAAAA,KAAgBgK,EAAf,IACKjK,EACJI,IAAKR,EACL+H,MAAO,IACF3H,EAAW2H,MAEduC,QAAS,YAKnB,GAMA,SAAS3E,EAAa4E,GACpB,OAAiB,OAAVA,CACT,CANAN,EAAYpJ,YAAckJ,EAQ1B,IAAM7B,EAAmBsC,IAAA,CACvBC,KAAM,kBACND,UACAE,EAAAA,CAAGC,GACD,MAAM,UAAE5E,EAAA,MAAWyB,EAAA,eAAOvB,GAAmB0E,EAGvCC,EAD2D,IAAvC3E,EAAexC,OAAOqF,aAE1C/D,EAAa6F,EAAgB,EAAIJ,EAAQzF,WACzCC,EAAc4F,EAAgB,EAAIJ,EAAQxF,aAEzCoD,EAAYC,GAAeC,EAA6BvC,GACzD8E,EAAe,CAAEC,MAAO,KAAMC,OAAQ,MAAOC,IAAK,QAAS3C,GAE3D4C,GAAgBhF,EAAexC,OAAOiF,GAAK,GAAK3D,EAAa,EAC7DmG,GAAgBjF,EAAexC,OAAOmF,GAAK,GAAK5D,EAAc,EAEpE,IAAI0D,EAAI,GACJE,EAAI,GAeR,MAbmB,WAAfR,GACFM,EAAIkC,EAAgBC,EAAe,GAAGI,MACtCrC,GAAQ5D,EAAJ,MACoB,QAAfoD,GACTM,EAAIkC,EAAgBC,EAAe,GAAGI,MACtCrC,EAAI,GAAGpB,EAAMM,SAAS3H,OAAS6E,OACP,UAAfoD,GACTM,GAAQ1D,EAAJ,KACJ4D,EAAIgC,EAAgBC,EAAe,GAAGK,OACd,SAAf9C,IACTM,EAAI,GAAGlB,EAAMM,SAAS5H,MAAQ8E,MAC9B4D,EAAIgC,EAAgBC,EAAe,GAAGK,OAEjC,CAAEP,KAAM,CAAEjC,IAAGE,KACtB,IAGF,SAASN,EAA6BvC,GACpC,MAAOvD,EAAME,EAAQ,UAAYqD,EAAUoF,MAAM,KACjD,MAAO,CAAC3I,EAAcE,EACxB,CAEA,IAAM0I,EAAO/J,EACPgK,EAASzJ,EACT0J,EAAU/I,EACV1C,EAAQoK,E,4DEzXRsB,EAAiB,CAAC,QAAS,KAE3BC,EAAY,CAAC,UAAW,WAAY,OACpCC,EAAkB,CAFJ,YAAa,SAAU,UAEAD,GACrCE,EAA6C,CACjDC,IAAK,IAAIJ,EAAgB,cACzBK,IAAK,IAAIL,EAAgB,cAErBM,GAA8C,CAClDF,IAAK,CAAC,aACNC,IAAK,CAAC,eAOFE,GAAY,QAGXC,GAAYC,GAAeC,KAAyBC,EAAAA,EAAAA,GAGzDJ,KAGKK,GAAmBC,KAAmBlL,EAAAA,EAAAA,GAAmB4K,GAAW,CACzEG,GACAhL,EACAoL,EAAAA,KAEIC,GAAiBrL,IACjBsL,IAA2BF,EAAAA,EAAAA,OAS1BG,GAAcC,IAAkBN,GAAoCL,KASpEY,GAAkBC,IAAsBR,GAAwCL,IAUjFc,GAA6B7M,IACjC,MAAM,YAAE8M,EAAA,KAAaC,GAAO,EAAK,SAAE7M,EAAA,IAAU0J,EAAA,aAAKoD,EAAA,MAAcC,GAAQ,GAASjN,EAC3EkN,EAAcX,GAAeO,IAC5BvJ,EAASC,GAAoBzD,EAAAA,SAAoC,MAClEoN,EAA2BpN,EAAAA,QAAO,GAClCqN,GAAmB3E,EAAAA,EAAAA,GAAeuE,GAClCK,GAAYC,EAAAA,EAAAA,IAAa1D,GAmB/B,OAjBM7J,EAAAA,UAAU,KAGd,MAAMwN,EAAgBA,KACpBJ,EAAmBhL,SAAU,EAC7BqL,SAASC,iBAAiB,cAAeC,EAAe,CAAEC,SAAS,EAAMC,MAAM,IAC/EJ,SAASC,iBAAiB,cAAeC,EAAe,CAAEC,SAAS,EAAMC,MAAM,KAE3EF,EAAgBA,IAAOP,EAAmBhL,SAAU,EAE1D,OADAqL,SAASC,iBAAiB,UAAWF,EAAe,CAAEI,SAAS,IACxD,KACLH,SAASK,oBAAoB,UAAWN,EAAe,CAAEI,SAAS,IAClEH,SAASK,oBAAoB,cAAeH,EAAe,CAAEC,SAAS,IACtEH,SAASK,oBAAoB,cAAeH,EAAe,CAAEC,SAAS,MAEvE,KAGDrN,EAAAA,EAAAA,KAAiBwN,EAAhB,IAAyBZ,EACxBhN,UAAAI,EAAAA,EAAAA,KAACmM,GAAA,CACC/K,MAAOoL,EACPC,OACAC,aAAcI,EACd7J,UACAwK,gBAAiBvK,EAEjBtD,UAAAI,EAAAA,EAAAA,KAACqM,GAAA,CACCjL,MAAOoL,EACPkB,QAAejO,EAAAA,YAAY,IAAMqN,GAAiB,GAAQ,CAACA,IAC3DD,qBACAvD,IAAKyD,EACLJ,QAEC/M,kBAOX2M,GAAK/L,YAAciL,GAMnB,IAMMkC,GAAmBlO,EAAAA,WACvB,CAACC,EAAqCC,KACpC,MAAM,YAAE6M,KAAgB/K,GAAgB/B,EAClCkN,EAAcX,GAAeO,GACnC,OAAOxM,EAAAA,EAAAA,KAAiBwN,EAAhB,IAA2BZ,KAAiBnL,EAAatB,IAAKR,MAI1EgO,GAAWnN,YAdS,aAoBpB,IAAMoN,GAAc,cAGbC,GAAgBC,IAAoBhC,GAAsC8B,GAAa,CAC5FG,gBAAY,IAiBRC,GAAyCtO,IAC7C,MAAM,YAAE8M,EAAA,WAAauB,EAAA,SAAYnO,EAAA,UAAUqO,GAAcvO,EACnDgC,EAAU0K,GAAewB,GAAapB,GAC5C,OACExM,EAAAA,EAAAA,KAAC6N,GAAA,CAAezM,MAAOoL,EAAauB,aAClCnO,UAAAI,EAAAA,EAAAA,KAACkO,EAAAA,EAAA,CAASC,QAASJ,GAAcrM,EAAQ+K,KACvC7M,UAAAI,EAAAA,EAAAA,KAACoO,EAAAA,EAAA,CAAgB9N,SAAO,EAAC2N,YACtBrO,kBAOXoO,GAAWxN,YAAcoN,GAMzB,IAAM7L,GAAe,eAUdsM,GAAqBC,IAC1BxC,GAA2C/J,IAgBvCwM,GAAoB9O,EAAAA,WACxB,CAACC,EAAsCC,KACrC,MAAM6O,EAAgBV,GAAiB/L,GAAcrC,EAAM8M,cACrD,WAAEuB,EAAaS,EAAcT,cAAe/K,GAAiBtD,EAC7DgC,EAAU0K,GAAerK,GAAcrC,EAAM8M,aAC7CiC,EAAcnC,GAAmBvK,GAAcrC,EAAM8M,aAE3D,OACExM,EAAAA,EAAAA,KAAC0L,GAAWgD,SAAX,CAAoBtN,MAAO1B,EAAM8M,YAChC5M,UAAAI,EAAAA,EAAAA,KAACkO,EAAAA,EAAA,CAASC,QAASJ,GAAcrM,EAAQ+K,KACvC7M,UAAAI,EAAAA,EAAAA,KAAC0L,GAAWiD,KAAX,CAAgBvN,MAAO1B,EAAM8M,YAC3B5M,SAAA6O,EAAY9B,OACX3M,EAAAA,EAAAA,KAAC4O,GAAA,IAAyB5L,EAAc7C,IAAKR,KAE7CK,EAAAA,EAAAA,KAAC6O,GAAA,IAA4B7L,EAAc7C,IAAKR,YAexDiP,GAA6BnP,EAAAA,WACjC,CAACC,EAA8CC,KAC7C,MAAM+B,EAAU0K,GAAerK,GAAcrC,EAAM8M,aAC7CrM,EAAYV,EAAAA,OAAmC,MAC/CkC,GAAeC,EAAAA,EAAAA,GAAgBjC,EAAcQ,GAQnD,OALMV,EAAAA,UAAU,KACd,MAAMwD,EAAU9C,EAAI0B,QACpB,GAAIoB,EAAS,OAAO6L,EAAAA,EAAAA,IAAW7L,IAC9B,KAGDjD,EAAAA,EAAAA,KAAC+O,GAAA,IACKrP,EACJS,IAAKwB,EAGLqN,UAAWtN,EAAQ+K,KAGnBwC,4BAA6BvN,EAAQ+K,KACrCyC,sBAAoB,EAGpBC,gBAAgBC,EAAAA,EAAAA,GACd1P,EAAMyP,eACLE,GAAUA,EAAMC,iBACjB,CAAEC,0BAA0B,IAE9BC,UAAWA,IAAM9N,EAAQgL,cAAa,OAMxCmC,GAAgCpP,EAAAA,WAGpC,CAACC,EAA8CC,KAC/C,MAAM+B,EAAU0K,GAAerK,GAAcrC,EAAM8M,aACnD,OACExM,EAAAA,EAAAA,KAAC+O,GAAA,IACKrP,EACJS,IAAKR,EACLqP,WAAW,EACXC,6BAA6B,EAC7BC,sBAAsB,EACtBM,UAAWA,IAAM9N,EAAQgL,cAAa,OAmDtCiC,IAAOc,EAAAA,EAAAA,IAAW,0BAElBV,GAAwBtP,EAAAA,WAC5B,CAACC,EAA0CC,KACzC,MAAM,YACJ6M,EAAA,KACAkD,GAAO,EAAK,UACZV,EAAA,gBACAW,EAAA,iBACAC,EAAA,4BACAX,EAAA,aACAY,EAAA,gBACAC,EAAA,qBACAC,EAAA,eACAZ,EAAA,kBACAa,EAAA,UACAR,EAAA,qBACAN,KACGlM,GACDtD,EACEgC,EAAU0K,GAAerK,GAAcyK,GACvCiC,EAAcnC,GAAmBvK,GAAcyK,GAC/CI,EAAcX,GAAeO,GAC7ByD,EAAwB/D,GAAyBM,GACjD0D,EAAWvE,GAAca,IACxB2D,EAAeC,GAA0B3Q,EAAAA,SAAwB,MAClE4Q,EAAmB5Q,EAAAA,OAAuB,MAC1CkC,GAAeC,EAAAA,EAAAA,GAAgBjC,EAAc0Q,EAAY3O,EAAQ+L,iBACjE6C,EAAiB7Q,EAAAA,OAAO,GACxB8Q,EAAkB9Q,EAAAA,OAAO,IACzB+Q,EAA6B/Q,EAAAA,OAAO,GACpCgR,EAA8BhR,EAAAA,OAA2B,MACzDiR,EAAsBjR,EAAAA,OAAa,SACnCkR,EAAwBlR,EAAAA,OAAO,GAE/BmR,EAAoB1B,EAAuB2B,EAAAA,EAAqBpR,EAAAA,SAChEqR,EAAyB5B,EAC3B,CAAE6B,GAAIpC,GAAMqC,gBAAgB,QAC5B,EAEEC,EAAyBC,IAC7B,MAAMC,EAASZ,EAAU1O,QAAUqP,EAC7BE,EAAQlB,IAAW7K,OAAQgM,IAAUA,EAAKC,UAC1CC,EAAcrE,SAASsE,cACvBC,EAAeL,EAAMM,KAAML,GAASA,EAAKlR,IAAI0B,UAAY0P,IAAcI,UAEvEC,EAw2BZ,SAAsBC,EAAkBV,EAAgBM,GACtD,MAAMK,EAAaX,EAAOjN,OAAS,GAAKF,MAAM+N,KAAKZ,GAAQa,MAAOC,GAASA,IAASd,EAAO,IACrFe,EAAmBJ,EAAaX,EAAO,GAAMA,EAC7CgB,EAAoBV,EAAeI,EAAOO,QAAQX,IAAiB,EACzE,IAAIY,GAzBgBC,EAyBUT,EAzBEU,EAyBMC,KAAKC,IAAIN,EAAmB,GAxB3DG,EAAMI,IAAO,CAACC,EAAGC,IAAUN,GAAOC,EAAaK,GAASN,EAAMpO,UADvE,IAAsBoO,EAAYC,EA0BwB,IAA5BL,EAAiBhO,SACpBmO,EAAgBA,EAAchN,OAAQwN,GAAMA,IAAMpB,IAC3E,MAAMG,EAAYS,EAAcX,KAAMxH,GACpCA,EAAM4I,cAAcC,WAAWb,EAAiBY,gBAElD,OAAOlB,IAAcH,EAAeG,OAAY,CAClD,CAn3BwBoB,CADH5B,EAAMsB,IAAKrB,GAASA,EAAKM,WACDR,EAAQM,GACzCwB,EAAU7B,EAAMM,KAAML,GAASA,EAAKM,YAAcC,IAAYzR,IAAI0B,SAGxE,SAAUqR,EAAahJ,GACrBqG,EAAU1O,QAAUqI,EACpBtB,OAAOuK,aAAa7C,EAASzO,SACf,KAAVqI,IAAcoG,EAASzO,QAAU+G,OAAOwK,WAAW,IAAMF,EAAa,IAAK,KAChF,CAJD,CAIG/B,GAEC8B,GAKFG,WAAW,IAAOH,EAAwBI,UAIxC5T,EAAAA,UAAU,IACP,IAAMmJ,OAAOuK,aAAa7C,EAASzO,SACzC,KAIHyR,EAAAA,EAAAA,MAEA,MAAMC,EAAiC9T,EAAAA,YAAa4P,GAC1BqB,EAAc7O,UAAY4O,EAAsB5O,SAASM,MAm3BvF,SAA8BkN,EAA2BmE,GACvD,IAAKA,EAAM,OAAO,EAClB,MAAMC,EAAY,CAAEpL,EAAGgH,EAAMqE,QAASnL,EAAG8G,EAAMsE,SAC/C,OAtBF,SAA0BC,EAAcC,GACtC,MAAM,EAAExL,EAAA,EAAGE,GAAMqL,EACjB,IAAIE,GAAS,EACb,IAAK,IAAIC,EAAI,EAAGC,EAAIH,EAAQ3P,OAAS,EAAG6P,EAAIF,EAAQ3P,OAAQ8P,EAAID,IAAK,CACnE,MAAME,EAAKJ,EAAQE,GACbG,EAAKL,EAAQG,GACbG,EAAKF,EAAG5L,EACR+L,EAAKH,EAAG1L,EACR8L,EAAKH,EAAG7L,EACRiM,EAAKJ,EAAG3L,EAGM6L,EAAK7L,IAAQ+L,EAAK/L,GAAQF,GAAKgM,EAAKF,IAAO5L,EAAI6L,IAAOE,EAAKF,GAAMD,IACtEL,GAAUA,EAC3B,CAEA,OAAOA,CACT,CAKSS,CAAiBd,EAAWD,EACrC,CAt3BgCgB,CAAqBnF,EAAOoB,EAAsB5O,SAAS2R,MACpF,IAEH,OACExT,EAAAA,EAAAA,KAACqO,GAAA,CACCjN,MAAOoL,EACP+D,YACAkE,YAAmBhV,EAAAA,YAChB4P,IACKkE,EAAyBlE,IAAQA,EAAMC,kBAE7C,CAACiE,IAEHmB,YAAmBjV,EAAAA,YAChB4P,IACKkE,EAAyBlE,KAC7BgB,EAAWxO,SAASwR,QACpBjD,EAAiB,QAEnB,CAACmD,IAEHoB,eAAsBlV,EAAAA,YACnB4P,IACKkE,EAAyBlE,IAAQA,EAAMC,kBAE7C,CAACiE,IAEH/C,uBACAoE,2BAAkCnV,EAAAA,YAAaoV,IAC7CpE,EAAsB5O,QAAUgT,GAC/B,IAEHjV,UAAAI,EAAAA,EAAAA,KAAC4Q,EAAA,IAAsBE,EACrBlR,UAAAI,EAAAA,EAAAA,KAAC8U,EAAAA,EAAA,CACCxU,SAAO,EACPyU,QAAS/F,EACTgG,kBAAkB5F,EAAAA,EAAAA,GAAqBO,EAAkBN,IAGvDA,EAAMC,iBACNe,EAAWxO,SAASwR,MAAM,CAAE4B,eAAe,MAE7CC,mBAAoBtF,EAEpBhQ,UAAAI,EAAAA,EAAAA,KAACmV,EAAAA,GAAA,CACC7U,SAAO,EACP2O,8BACAa,kBACAC,uBACAZ,iBACAa,oBACAR,YAEA5P,UAAAI,EAAAA,EAAAA,KAAkBoV,EAAAA,GAAjB,CACC9U,SAAO,KACH2P,EACJ3G,IAAKmF,EAAYnF,IACjB+L,YAAY,WACZ3F,OACA4F,iBAAkBnF,EAClBoF,yBAA0BnF,EAC1BP,cAAcT,EAAAA,EAAAA,GAAqBS,EAAeR,IAE3CZ,EAAY5B,mBAAmBhL,SAASwN,EAAMC,mBAErDkG,2BAAyB,EAEzB5V,UAAAI,EAAAA,EAAAA,KAAiBwN,EAAhB,CACCiI,KAAK,OACL,mBAAiB,WACjB,aAAYC,GAAahU,EAAQ+K,MACjC,0BAAwB,GACxBnD,IAAKmF,EAAYnF,OACbsD,KACA5J,EACJ7C,IAAKwB,EACL+F,MAAO,CAAEiO,QAAS,UAAW3S,EAAa0E,OAC1CkO,WAAWxG,EAAAA,EAAAA,GAAqBpM,EAAa4S,UAAYvG,IAEvD,MACMwG,EADSxG,EAAMyG,OAEZC,QAAQ,+BAAiC1G,EAAM2G,cAClDC,EAAgB5G,EAAM6G,SAAW7G,EAAM8G,QAAU9G,EAAM+G,QACvDC,EAAsC,IAArBhH,EAAM6B,IAAIhN,OAC7B2R,IAEgB,QAAdxG,EAAM6B,KAAe7B,EAAMC,kBAC1B2G,GAAiBI,GAAgBpF,EAAsB5B,EAAM6B,MAGpE,MAAMjO,EAAUoN,EAAWxO,QAC3B,GAAIwN,EAAMyG,SAAW7S,EAAS,OAC9B,IAAKmI,EAAgBkL,SAASjH,EAAM6B,KAAM,OAC1C7B,EAAMC,iBACN,MACMiH,EADQrG,IAAW7K,OAAQgM,IAAUA,EAAKC,UACnBoB,IAAKrB,GAASA,EAAKlR,IAAI0B,SAChDsJ,EAAUmL,SAASjH,EAAM6B,MAAMqF,EAAeC,UAwsBtE,SAAoBC,GAClB,MAAMC,EAA6BxJ,SAASsE,cAC5C,IAAK,MAAMmF,KAAaF,EAAY,CAElC,GAAIE,IAAcD,EAA4B,OAE9C,GADAC,EAAUtD,QACNnG,SAASsE,gBAAkBkF,EAA4B,MAC7D,CACF,CA/sBoBE,CAAWL,KAEbM,QAAQzH,EAAAA,EAAAA,GAAqB1P,EAAMmX,OAASxH,IAErCA,EAAM2G,cAAcc,SAASzH,EAAMyG,UACtClN,OAAOuK,aAAa7C,EAASzO,SAC7B0O,EAAU1O,QAAU,MAGxBkV,eAAe3H,EAAAA,EAAAA,GACb1P,EAAMqX,cACNC,GAAW3H,IACT,MAAMyG,EAASzG,EAAMyG,OACfmB,EAAqBtG,EAAgB9O,UAAYwN,EAAMqE,QAI7D,GAAIrE,EAAM2G,cAAcc,SAAShB,IAAWmB,EAAoB,CAC9D,MAAMC,EAAS7H,EAAMqE,QAAU/C,EAAgB9O,QAAU,QAAU,OACnE6O,EAAc7O,QAAUqV,EACxBvG,EAAgB9O,QAAUwN,EAAMqE,OAClC,mBAatBnF,GAAY/N,YAAcuB,GAM1B,IAMMoV,GAAkB1X,EAAAA,WACtB,CAACC,EAAoCC,KACnC,MAAM,YAAE6M,KAAgB4K,GAAe1X,EACvC,OAAOM,EAAAA,EAAAA,KAACC,EAAAA,GAAU6B,IAAV,CAAc2T,KAAK,WAAY2B,EAAYjX,IAAKR,MAI5DwX,GAAU3W,YAbS,YAmBnB,IAKM6W,GAAkB5X,EAAAA,WACtB,CAACC,EAAoCC,KACnC,MAAM,YAAE6M,KAAgB8K,GAAe5X,EACvC,OAAOM,EAAAA,EAAAA,KAACC,EAAAA,GAAU6B,IAAV,IAAkBwV,EAAYnX,IAAKR,MAI/C0X,GAAU7W,YAZS,YAkBnB,IAAM+W,GAAY,WACZC,GAAc,kBAOdC,GAAiBhY,EAAAA,WACrB,CAACC,EAAmCC,KAClC,MAAM,SAAE2R,GAAW,EAAK,SAAEoG,KAAaC,GAAcjY,EAC/CS,EAAYV,EAAAA,OAAuB,MACnCgP,EAAcnC,GAAmBiL,GAAW7X,EAAM8M,aAClD3C,EAAiByE,GAAsBiJ,GAAW7X,EAAM8M,aACxD7K,GAAeC,EAAAA,EAAAA,GAAgBjC,EAAcQ,GAC7CyX,EAAyBnY,EAAAA,QAAO,GAgBtC,OACEO,EAAAA,EAAAA,KAAC6X,GAAA,IACKF,EACJxX,IAAKwB,EACL2P,WACAwG,SAAS1I,EAAAA,EAAAA,GAAqB1P,EAAMoY,QAnBnBC,KACnB,MAAMC,EAAW7X,EAAI0B,QACrB,IAAKyP,GAAY0G,EAAU,CACzB,MAAMC,EAAkB,IAAIC,YAAYV,GAAa,CAAEW,SAAS,EAAMC,YAAY,IAClFJ,EAAS7K,iBAAiBqK,GAAcnI,GAAUqI,IAAWrI,GAAQ,CAAE/B,MAAM,KAC7E+K,EAAAA,EAAAA,IAA4BL,EAAUC,GAClCA,EAAgBK,iBAClBV,EAAiB/V,SAAU,EAE3B4M,EAAYf,SAEhB,IASE6K,cAAgBlJ,IACd3P,EAAM6Y,gBAAgBlJ,GACtBuI,EAAiB/V,SAAU,GAE7B2W,aAAapJ,EAAAA,EAAAA,GAAqB1P,EAAM8Y,YAAcnJ,IAI/CuI,EAAiB/V,SAASwN,EAAM2G,eAAeyC,UAEtD7C,WAAWxG,EAAAA,EAAAA,GAAqB1P,EAAMkW,UAAYvG,IAChD,MAAMqJ,EAAqD,KAArC7O,EAAe0G,UAAU1O,QAC3CyP,GAAaoH,GAA+B,MAAdrJ,EAAM6B,KACpChG,EAAeoL,SAASjH,EAAM6B,OAChC7B,EAAM2G,cAAcyC,QAOpBpJ,EAAMC,wBAQlBmI,GAASjX,YAAc+W,GAUvB,IAAMM,GAAqBpY,EAAAA,WACzB,CAACC,EAAuCC,KACtC,MAAM,YAAE6M,EAAA,SAAa8E,GAAW,EAAK,UAAEK,KAAcgG,GAAcjY,EAC7DmK,EAAiByE,GAAsBiJ,GAAW/K,GAClDyD,EAAwB/D,GAAyBM,GACjDrM,EAAYV,EAAAA,OAAuB,MACnCkC,GAAeC,EAAAA,EAAAA,GAAgBjC,EAAcQ,IAC5CwY,EAAWC,GAAsBnZ,EAAAA,UAAS,IAG1CoZ,EAAaC,GAAwBrZ,EAAAA,SAAS,IAQrD,OAPMA,EAAAA,UAAU,KACd,MAAMuY,EAAW7X,EAAI0B,QACjBmW,GACFc,GAAgBd,EAASa,aAAe,IAAIE,SAE7C,CAACpB,EAAU/X,YAGZI,EAAAA,EAAAA,KAAC0L,GAAWsN,SAAX,CACC5X,MAAOoL,EACP8E,WACAK,UAAWA,GAAakH,EAExBjZ,UAAAI,EAAAA,EAAAA,KAAkBoV,EAAAA,GAAjB,CAAsB9U,SAAO,KAAK2P,EAAuBgJ,WAAY3H,EACpE1R,UAAAI,EAAAA,EAAAA,KAACC,EAAAA,GAAU6B,IAAV,CACC2T,KAAK,WACL,mBAAkBkD,EAAY,QAAK,EACnC,gBAAerH,QAAY,EAC3B,gBAAeA,EAAW,QAAK,KAC3BqG,EACJxX,IAAKwB,EAYLoV,eAAe3H,EAAAA,EAAAA,GACb1P,EAAMqX,cACNC,GAAW3H,IACT,GAAIiC,EACFzH,EAAe6K,YAAYrF,QAG3B,GADAxF,EAAe4K,YAAYpF,IACtBA,EAAMiJ,iBAAkB,CACdjJ,EAAM2G,cACd3C,MAAM,CAAE4B,eAAe,GAC9B,KAINiE,gBAAgB9J,EAAAA,EAAAA,GACd1P,EAAMwZ,eACNlC,GAAW3H,GAAUxF,EAAe6K,YAAYrF,KAElD8J,SAAS/J,EAAAA,EAAAA,GAAqB1P,EAAMyZ,QAAS,IAAMP,GAAa,IAChE/B,QAAQzH,EAAAA,EAAAA,GAAqB1P,EAAMmX,OAAQ,IAAM+B,GAAa,YAwBpEQ,GAAyB3Z,EAAAA,WAC7B,CAACC,EAA2CC,KAC1C,MAAM,QAAE0Z,GAAU,EAAK,gBAAEC,KAAoBC,GAAsB7Z,EACnE,OACEM,EAAAA,EAAAA,KAACwZ,GAAA,CAAsBpY,MAAO1B,EAAM8M,YAAa6M,UAC/CzZ,UAAAI,EAAAA,EAAAA,KAACyX,GAAA,CACChC,KAAK,mBACL,eAAcgE,GAAgBJ,GAAW,QAAUA,KAC/CE,EACJpZ,IAAKR,EACL,aAAY+Z,GAAgBL,GAC5B3B,UAAUtI,EAAAA,EAAAA,GACRmK,EAAkB7B,SAClB,IAAM4B,MAAkBG,GAAgBJ,KAAmBA,GAC3D,CAAE9J,0BAA0B,UAQxC6J,GAAiB5Y,YAlCU,mBAwC3B,IAAMmZ,GAAmB,kBAElBC,GAAoBC,IAAwB/N,GACjD6N,GACA,CAAEzP,WAAO,EAAW4P,cAAeA,SAS/BC,GAAuBta,EAAAA,WAC3B,CAACC,EAAyCC,KACxC,MAAM,MAAEuK,EAAA,cAAO4P,KAAkB1C,GAAe1X,EAC1Csa,GAAoB7R,EAAAA,EAAAA,GAAe2R,GACzC,OACE9Z,EAAAA,EAAAA,KAAC4Z,GAAA,CAAmBxY,MAAO1B,EAAM8M,YAAatC,QAAc4P,cAAeE,EACzEpa,UAAAI,EAAAA,EAAAA,KAACmX,GAAA,IAAcC,EAAYjX,IAAKR,QAMxCoa,GAAevZ,YAAcmZ,GAM7B,IAAMM,GAAkB,gBAOlBC,GAAsBza,EAAAA,WAC1B,CAACC,EAAwCC,KACvC,MAAM,MAAEuK,KAAUiQ,GAAmBza,EAC/BgC,EAAUmY,GAAqBI,GAAiBva,EAAM8M,aACtD6M,EAAUnP,IAAUxI,EAAQwI,MAClC,OACElK,EAAAA,EAAAA,KAACwZ,GAAA,CAAsBpY,MAAO1B,EAAM8M,YAAa6M,UAC/CzZ,UAAAI,EAAAA,EAAAA,KAACyX,GAAA,CACChC,KAAK,gBACL,eAAc4D,KACVc,EACJha,IAAKR,EACL,aAAY+Z,GAAgBL,GAC5B3B,UAAUtI,EAAAA,EAAAA,GACR+K,EAAezC,SACf,IAAMhW,EAAQoY,gBAAgB5P,GAC9B,CAAEqF,0BAA0B,UAQxC2K,GAAc1Z,YAAcyZ,GAM5B,IAAMG,GAAsB,qBAIrBZ,GAAuBa,IAA2BvO,GACvDsO,GACA,CAAEf,SAAS,IAaPiB,GAA0B7a,EAAAA,WAC9B,CAACC,EAA4CC,KAC3C,MAAM,YAAE6M,EAAA,WAAauB,KAAewM,GAAuB7a,EACrD8a,EAAmBH,GAAwBD,GAAqB5N,GACtE,OACExM,EAAAA,EAAAA,KAACkO,EAAAA,EAAA,CACCC,QACEJ,GACA0L,GAAgBe,EAAiBnB,WACJ,IAA7BmB,EAAiBnB,QAGnBzZ,UAAAI,EAAAA,EAAAA,KAACC,EAAAA,GAAUwa,KAAV,IACKF,EACJpa,IAAKR,EACL,aAAY+Z,GAAgBc,EAAiBnB,eAOvDiB,GAAkB9Z,YAAc4Z,GAMhC,IAKMM,GAAsBjb,EAAAA,WAC1B,CAACC,EAAwCC,KACvC,MAAM,YAAE6M,KAAgBmO,GAAmBjb,EAC3C,OACEM,EAAAA,EAAAA,KAACC,EAAAA,GAAU6B,IAAV,CACC2T,KAAK,YACL,mBAAiB,gBACbkF,EACJxa,IAAKR,MAMb+a,GAAcla,YAnBS,gBAyBvB,IAMMoa,GAAkBnb,EAAAA,WACtB,CAACC,EAAoCC,KACnC,MAAM,YAAE6M,KAAgBzM,GAAeL,EACjCkN,EAAcX,GAAeO,GACnC,OAAOxM,EAAAA,EAAAA,KAAiBwN,EAAhB,IAA0BZ,KAAiB7M,EAAYI,IAAKR,MAIxEib,GAAUpa,YAdS,YAoBnB,IAAMqa,GAAW,WASVC,GAAiBC,IAAqBjP,GAAuC+O,IAQ9EG,GAAmCtb,IACvC,MAAM,YAAE8M,EAAA,SAAa5M,EAAA,KAAU6M,GAAO,EAAK,aAAEC,GAAiBhN,EACxDub,EAAoB7O,GAAeyO,GAAUrO,GAC7CI,EAAcX,GAAeO,IAC5B0O,EAASC,GAAoB1b,EAAAA,SAAuC,OACpEwD,EAASC,GAAoBzD,EAAAA,SAAoC,MAClEqN,GAAmB3E,EAAAA,EAAAA,GAAeuE,GAQxC,OALMjN,EAAAA,UAAU,MACiB,IAA3Bwb,EAAkBxO,MAAgBK,GAAiB,GAChD,IAAMA,GAAiB,IAC7B,CAACmO,EAAkBxO,KAAMK,KAG1B9M,EAAAA,EAAAA,KAAiBwN,EAAhB,IAAyBZ,EACxBhN,UAAAI,EAAAA,EAAAA,KAACmM,GAAA,CACC/K,MAAOoL,EACPC,OACAC,aAAcI,EACd7J,UACAwK,gBAAiBvK,EAEjBtD,UAAAI,EAAAA,EAAAA,KAAC8a,GAAA,CACC1Z,MAAOoL,EACP4O,WAAWC,EAAAA,EAAAA,KACXC,WAAWD,EAAAA,EAAAA,KACXH,UACAK,gBAAiBJ,EAEhBvb,kBAOXob,GAAQxa,YAAcqa,GAMtB,IAAMW,GAAmB,iBAKnBC,GAAuBhc,EAAAA,WAC3B,CAACC,EAAyCC,KACxC,MAAM+B,EAAU0K,GAAeoP,GAAkB9b,EAAM8M,aACjDiC,EAAcnC,GAAmBkP,GAAkB9b,EAAM8M,aACzDkP,EAAaX,GAAkBS,GAAkB9b,EAAM8M,aACvD3C,EAAiByE,GAAsBkN,GAAkB9b,EAAM8M,aAC/DmP,EAAqBlc,EAAAA,OAAsB,OAC3C,qBAAE+Q,EAAA,2BAAsBoE,GAA+B/K,EACvDzI,EAAQ,CAAEoL,YAAa9M,EAAM8M,aAE7BoP,EAAuBnc,EAAAA,YAAY,KACnCkc,EAAa9Z,SAAS+G,OAAOuK,aAAawI,EAAa9Z,SAC3D8Z,EAAa9Z,QAAU,MACtB,IAYH,OAVMpC,EAAAA,UAAU,IAAMmc,EAAgB,CAACA,IAEjCnc,EAAAA,UAAU,KACd,MAAMoc,EAAoBrL,EAAqB3O,QAC/C,MAAO,KACL+G,OAAOuK,aAAa0I,GACpBjH,EAA2B,QAE5B,CAACpE,EAAsBoE,KAGxB5U,EAAAA,EAAAA,KAAC2N,GAAA,CAAWrN,SAAO,KAAKc,EACtBxB,UAAAI,EAAAA,EAAAA,KAAC6X,GAAA,CACCiE,GAAIJ,EAAWJ,UACf,gBAAc,OACd,gBAAe5Z,EAAQ+K,KACvB,gBAAeiP,EAAWN,UAC1B,aAAY1F,GAAahU,EAAQ+K,SAC7B/M,EACJS,KAAK4b,EAAAA,EAAAA,GAAYpc,EAAc+b,EAAWH,iBAG1CzD,QAAUzI,IACR3P,EAAMoY,UAAUzI,GACZ3P,EAAM4R,UAAYjC,EAAMiJ,mBAM5BjJ,EAAM2G,cAAc3C,QACf3R,EAAQ+K,MAAM/K,EAAQgL,cAAa,KAE1CqK,eAAe3H,EAAAA,EAAAA,GACb1P,EAAMqX,cACNC,GAAW3H,IACTxF,EAAe4K,YAAYpF,GACvBA,EAAMiJ,kBACL5Y,EAAM4R,UAAa5P,EAAQ+K,MAASkP,EAAa9Z,UACpDgI,EAAe+K,2BAA2B,MAC1C+G,EAAa9Z,QAAU+G,OAAOwK,WAAW,KACvC1R,EAAQgL,cAAa,GACrBkP,KACC,SAIT1C,gBAAgB9J,EAAAA,EAAAA,GACd1P,EAAMwZ,eACNlC,GAAW3H,IACTuM,IAEA,MAAMI,EAActa,EAAQuB,SAASgZ,wBACrC,GAAID,EAAa,CAEf,MAAM7Z,EAAOT,EAAQuB,SAASiZ,QAAQ/Z,KAChCga,EAAqB,UAATha,EACZia,EAAQD,GAAa,EAAI,EACzBE,EAAkBL,EAAYG,EAAY,OAAS,SACnDG,EAAiBN,EAAYG,EAAY,QAAU,QAEzDtS,EAAe+K,2BAA2B,CACxCpB,KAAM,CAGJ,CAAEnL,EAAGgH,EAAMqE,QAAU0I,EAAO7T,EAAG8G,EAAMsE,SACrC,CAAEtL,EAAGgU,EAAiB9T,EAAGyT,EAAYnX,KACrC,CAAEwD,EAAGiU,EAAgB/T,EAAGyT,EAAYnX,KACpC,CAAEwD,EAAGiU,EAAgB/T,EAAGyT,EAAYjX,QACpC,CAAEsD,EAAGgU,EAAiB9T,EAAGyT,EAAYjX,SAEvC5C,SAGFyG,OAAOuK,aAAa3C,EAAqB3O,SACzC2O,EAAqB3O,QAAU+G,OAAOwK,WACpC,IAAMvJ,EAAe+K,2BAA2B,MAChD,IAEJ,KAAO,CAEL,GADA/K,EAAe8K,eAAetF,GAC1BA,EAAMiJ,iBAAkB,OAG5BzO,EAAe+K,2BAA2B,KAC5C,KAGJgB,WAAWxG,EAAAA,EAAAA,GAAqB1P,EAAMkW,UAAYvG,IAChD,MAAMqJ,EAAqD,KAArC7O,EAAe0G,UAAU1O,QAC3CnC,EAAM4R,UAAaoH,GAA+B,MAAdrJ,EAAM6B,KAC1C7F,EAAcoD,EAAYnF,KAAKgN,SAASjH,EAAM6B,OAChDxP,EAAQgL,cAAa,GAGrBhL,EAAQuB,SAASoQ,QAEjBhE,EAAMC,0BASpBmM,GAAejb,YAAcgb,GAM7B,IAAMe,GAAmB,iBAenBC,GAAuB/c,EAAAA,WAC3B,CAACC,EAAyCC,KACxC,MAAM6O,EAAgBV,GAAiB/L,GAAcrC,EAAM8M,cACrD,WAAEuB,EAAaS,EAAcT,cAAe0O,GAAoB/c,EAChEgC,EAAU0K,GAAerK,GAAcrC,EAAM8M,aAC7CiC,EAAcnC,GAAmBvK,GAAcrC,EAAM8M,aACrDkP,EAAaX,GAAkBwB,GAAkB7c,EAAM8M,aACvDrM,EAAYV,EAAAA,OAA8B,MAC1CkC,GAAeC,EAAAA,EAAAA,GAAgBjC,EAAcQ,GACnD,OACEH,EAAAA,EAAAA,KAAC0L,GAAWgD,SAAX,CAAoBtN,MAAO1B,EAAM8M,YAChC5M,UAAAI,EAAAA,EAAAA,KAACkO,EAAAA,EAAA,CAASC,QAASJ,GAAcrM,EAAQ+K,KACvC7M,UAAAI,EAAAA,EAAAA,KAAC0L,GAAWiD,KAAX,CAAgBvN,MAAO1B,EAAM8M,YAC5B5M,UAAAI,EAAAA,EAAAA,KAAC+O,GAAA,CACC+M,GAAIJ,EAAWN,UACf,kBAAiBM,EAAWJ,aACxBmB,EACJtc,IAAKwB,EACLU,MAAM,QACNF,KAA0B,QAApBsM,EAAYnF,IAAgB,OAAS,QAC3C2F,6BAA6B,EAC7BC,sBAAsB,EACtBF,WAAW,EACXW,gBAAkBN,IAEZZ,EAAY5B,mBAAmBhL,SAAS1B,EAAI0B,SAASwR,QACzDhE,EAAMC,kBAIRM,iBAAmBP,GAAUA,EAAMC,iBACnCH,gBAAgBC,EAAAA,EAAAA,GAAqB1P,EAAMyP,eAAiBE,IAGtDA,EAAMyG,SAAW4F,EAAWR,SAASxZ,EAAQgL,cAAa,KAEhEoD,iBAAiBV,EAAAA,EAAAA,GAAqB1P,EAAMoQ,gBAAkBT,IAC5DZ,EAAYf,UAEZ2B,EAAMC,mBAERsG,WAAWxG,EAAAA,EAAAA,GAAqB1P,EAAMkW,UAAYvG,IAEhD,MAAMwG,EAAkBxG,EAAM2G,cAAcc,SAASzH,EAAMyG,QACrD4G,EAAalR,GAAeiD,EAAYnF,KAAKgN,SAASjH,EAAM6B,KAC9D2E,GAAmB6G,IACrBhb,EAAQgL,cAAa,GAErBgP,EAAWR,SAAS7H,QAEpBhE,EAAMC,8BAexB,SAASoG,GAAajJ,GACpB,OAAOA,EAAO,OAAS,QACzB,CAEA,SAASgN,GAAgBJ,GACvB,MAAmB,kBAAZA,CACT,CAEA,SAASK,GAAgBL,GACvB,OAAOI,GAAgBJ,GAAW,gBAAkBA,EAAU,UAAY,WAC5E,CAkFA,SAASrC,GAAa2F,GACpB,OAAQtN,GAAiC,UAAtBA,EAAMuN,YAA0BD,EAAQtN,QAAS,CACtE,CAlGAmN,GAAehc,YAAc+b,GAoG7B,IAAMM,GAAOtQ,GACPuQ,GAASnP,GACToP,GAAS/O,GACTgP,GAAUzO,GACV0O,GAAQ9F,GACR+F,GAAQ7F,GACR8F,GAAO1F,GACP2F,GAAehE,GACfiE,GAAatD,GACbuD,GAAYpD,GACZqD,GAAgBjD,GAChBkD,GAAY9C,GACZ+C,GAAQ7C,GACR8C,GAAM1C,GACN2C,GAAalC,GACbmC,GAAapB,GCxyCbqB,GAAqB,gBAGpBC,GAA2BC,KAA2Bld,EAAAA,EAAAA,GAC3Dgd,GACA,CAAC9R,KAEGiS,GAAejS,MAYdkS,GAAsBC,IAC3BJ,GAAoDD,IAWhDM,GAA6Cze,IACjD,MAAM,oBACJ0e,EAAA,SACAxe,EAAA,IACA0J,EACAmD,KAAM4R,EAAA,YACNC,EAAA,aACA5R,EAAA,MACAC,GAAQ,GACNjN,EACE6e,EAAYP,GAAaI,GACzBI,EAAmB/e,EAAAA,OAA0B,OAC5CgN,EAAMgS,IAAWC,EAAAA,EAAAA,GAAqB,CAC3CC,KAAMN,EACNO,YAAaN,IAAe,EAC5BO,SAAUnS,EACVoS,OAAQjB,KAGV,OACE7d,EAAAA,EAAAA,KAACie,GAAA,CACC7c,MAAOgd,EACP9C,WAAWD,EAAAA,EAAAA,KACXmD,aACApD,WAAWC,EAAAA,EAAAA,KACX5O,OACAC,aAAc+R,EACdM,aAAoBtf,EAAAA,YAAY,IAAMgf,EAASO,IAAcA,GAAW,CAACP,IACzE9R,QAEA/M,UAAAI,EAAAA,EAAAA,KAAeif,GAAd,IAAuBV,EAAW9R,OAAYC,aAAc+R,EAASnV,MAAUqD,QAC7E/M,gBAMTue,GAAa3d,YAAcqd,GAM3B,IAAMqB,GAAe,sBAMfC,GAA4B1f,EAAAA,WAChC,CAACC,EAA8CC,KAC7C,MAAM,oBAAEye,EAAA,SAAqB9M,GAAW,KAAU8N,GAAiB1f,EAC7DgC,EAAUwc,GAAuBgB,GAAcd,GAC/CG,EAAYP,GAAaI,GAC/B,OACEpe,EAAAA,EAAAA,KAAeif,GAAd,CAAqB3e,SAAO,KAAKie,EAChC3e,UAAAI,EAAAA,EAAAA,KAACC,EAAAA,GAAUof,OAAV,CACCC,KAAK,SACLxD,GAAIpa,EAAQ4Z,UACZ,gBAAc,OACd,gBAAe5Z,EAAQ+K,KACvB,gBAAe/K,EAAQ+K,KAAO/K,EAAQ0Z,eAAY,EAClD,aAAY1Z,EAAQ+K,KAAO,OAAS,SACpC,gBAAe6E,EAAW,QAAK,EAC/BA,cACI8N,EACJjf,KAAK4b,EAAAA,EAAAA,GAAYpc,EAAc+B,EAAQ8c,YACvCjG,eAAenJ,EAAAA,EAAAA,GAAqB1P,EAAM6Y,cAAgBlJ,IAGnDiC,GAA6B,IAAjBjC,EAAMgQ,SAAkC,IAAlBhQ,EAAM6G,UAC3CxU,EAAQqd,eAGHrd,EAAQ+K,MAAM4C,EAAMC,oBAG7BsG,WAAWxG,EAAAA,EAAAA,GAAqB1P,EAAMkW,UAAYvG,IAC5CiC,IACA,CAAC,QAAS,KAAKgF,SAASjH,EAAM6B,MAAMxP,EAAQqd,eAC9B,cAAd1P,EAAM6B,KAAqBxP,EAAQgL,cAAa,GAGhD,CAAC,QAAS,IAAK,aAAa4J,SAASjH,EAAM6B,MAAM7B,EAAMC,0BAQvE6P,GAAoB3e,YAAc0e,GAMlC,IAKMK,GACJ7f,IAEA,MAAM,oBAAE0e,KAAwBoB,GAAgB9f,EAC1C6e,EAAYP,GAAaI,GAC/B,OAAOpe,EAAAA,EAAAA,KAAeif,GAAd,IAAyBV,KAAeiB,KAGlDD,GAAmB/e,YAbC,qBAmBpB,IAAMuB,GAAe,sBAMf0d,GAA4BhgB,EAAAA,WAChC,CAACC,EAA8CC,KAC7C,MAAM,oBAAEye,KAAwBpb,GAAiBtD,EAC3CgC,EAAUwc,GAAuBnc,GAAcqc,GAC/CG,EAAYP,GAAaI,GACzBsB,EAAgCjgB,EAAAA,QAAO,GAE7C,OACEO,EAAAA,EAAAA,KAAeif,GAAd,CACCnD,GAAIpa,EAAQ0Z,UACZ,kBAAiB1Z,EAAQ4Z,aACrBiD,KACAvb,EACJ7C,IAAKR,EACLiQ,kBAAkBR,EAAAA,EAAAA,GAAqB1P,EAAMkQ,iBAAmBP,IACzDqQ,EAAwB7d,SAASH,EAAQ8c,WAAW3c,SAASwR,QAClEqM,EAAwB7d,SAAU,EAElCwN,EAAMC,mBAERU,mBAAmBZ,EAAAA,EAAAA,GAAqB1P,EAAMsQ,kBAAoBX,IAChE,MAAMsQ,EAAgBtQ,EAAMuQ,OAAOD,cAC7BE,EAAyC,IAAzBF,EAAcN,SAA0C,IAA1BM,EAAczJ,QAC5D4J,EAAwC,IAAzBH,EAAcN,QAAgBQ,EAC9Cne,EAAQiL,QAASmT,IAAcJ,EAAwB7d,SAAU,KAExE6F,MAAO,IACFhI,EAAMgI,MAGP,iDACE,uCACF,gDAAiD,sCACjD,iDACE,uCACF,sCAAuC,mCACvC,uCAAwC,yCAQpD+X,GAAoBjf,YAAcuB,GAMlC,IAMMge,GAA0BtgB,EAAAA,WAC9B,CAACC,EAA4CC,KAC3C,MAAM,oBAAEye,KAAwBhH,GAAe1X,EACzC6e,EAAYP,GAAaI,GAC/B,OAAOpe,EAAAA,EAAAA,KAAeif,GAAd,IAAwBV,KAAenH,EAAYjX,IAAKR,MAIpEogB,GAAkBvf,YAdC,oBAoBnB,IAMMwf,GAA0BvgB,EAAAA,WAC9B,CAACC,EAA4CC,KAC3C,MAAM,oBAAEye,KAAwB9G,GAAe5X,EACzC6e,EAAYP,GAAaI,GAC/B,OAAOpe,EAAAA,EAAAA,KAAeif,GAAd,IAAwBV,KAAejH,EAAYnX,IAAKR,MAIpEqgB,GAAkBxf,YAdC,oBAoBnB,IAMMyf,GAAyBxgB,EAAAA,WAC7B,CAACC,EAA2CC,KAC1C,MAAM,oBAAEye,KAAwBzG,GAAcjY,EACxC6e,EAAYP,GAAaI,GAC/B,OAAOpe,EAAAA,EAAAA,KAAeif,GAAd,IAAuBV,KAAe5G,EAAWxX,IAAKR,MAIlEsgB,GAAiBzf,YAdC,mBAoBlB,IAMM0f,GAAiCzgB,EAAAA,WAGrC,CAACC,EAAmDC,KACpD,MAAM,oBAAEye,KAAwB7E,GAAsB7Z,EAChD6e,EAAYP,GAAaI,GAC/B,OAAOpe,EAAAA,EAAAA,KAAeif,GAAd,IAA+BV,KAAehF,EAAmBpZ,IAAKR,MAGhFugB,GAAyB1f,YAfE,2BAqB3B,IAMM2f,GAA+B1gB,EAAAA,WAGnC,CAACC,EAAiDC,KAClD,MAAM,oBAAEye,KAAwBgC,GAAoB1gB,EAC9C6e,EAAYP,GAAaI,GAC/B,OAAOpe,EAAAA,EAAAA,KAAeif,GAAd,IAA6BV,KAAe6B,EAAiBjgB,IAAKR,MAG5EwgB,GAAuB3f,YAfE,yBAqBzB,IAMM6f,GAA8B5gB,EAAAA,WAGlC,CAACC,EAAgDC,KACjD,MAAM,oBAAEye,KAAwBjE,GAAmBza,EAC7C6e,EAAYP,GAAaI,GAC/B,OAAOpe,EAAAA,EAAAA,KAAeif,GAAd,IAA4BV,KAAepE,EAAgBha,IAAKR,MAG1E0gB,GAAsB7f,YAfE,wBAqBxB,IAMM8f,GAAkC7gB,EAAAA,WAGtC,CAACC,EAAoDC,KACrD,MAAM,oBAAEye,KAAwB7D,GAAuB7a,EACjD6e,EAAYP,GAAaI,GAC/B,OAAOpe,EAAAA,EAAAA,KAAeif,GAAd,IAAgCV,KAAehE,EAAoBpa,IAAKR,MAGlF2gB,GAA0B9f,YAfH,4BAqBvB,IAMM+f,GAA8B9gB,EAAAA,WAGlC,CAACC,EAAgDC,KACjD,MAAM,oBAAEye,KAAwBzD,GAAmBjb,EAC7C6e,EAAYP,GAAaI,GAC/B,OAAOpe,EAAAA,EAAAA,KAAeif,GAAd,IAA4BV,KAAe5D,EAAgBxa,IAAKR,MAG1E4gB,GAAsB/f,YAfC,wBAqBvB,IAMMggB,GAA0B/gB,EAAAA,WAC9B,CAACC,EAA4CC,KAC3C,MAAM,oBAAEye,KAAwBre,GAAeL,EACzC6e,EAAYP,GAAaI,GAC/B,OAAOpe,EAAAA,EAAAA,KAAeif,GAAd,IAAwBV,KAAexe,EAAYI,IAAKR,MAIpE6gB,GAAkBhgB,YAdC,oBA2BnB,IA6BMigB,GAA+BhhB,EAAAA,WAGnC,CAACC,EAAiDC,KAClD,MAAM,oBAAEye,KAAwBsC,GAAoBhhB,EAC9C6e,EAAYP,GAAaI,GAC/B,OAAOpe,EAAAA,EAAAA,KAAeif,GAAd,IAA6BV,KAAemC,EAAiBvgB,IAAKR,MAG5E8gB,GAAuBjgB,YAfE,yBAqBzB,IAMMmgB,GAA+BlhB,EAAAA,WAGnC,CAACC,EAAiDC,KAClD,MAAM,oBAAEye,KAAwB3B,GAAoB/c,EAC9C6e,EAAYP,GAAaI,GAE/B,OACEpe,EAAAA,EAAAA,KAAeif,GAAd,IACKV,KACA9B,EACJtc,IAAKR,EACL+H,MAAO,IACFhI,EAAMgI,MAGP,iDAAkD,uCAClD,gDAAiD,sCACjD,iDAAkD,uCAClD,sCAAuC,mCACvC,uCAAwC,yCAOlDiZ,GAAuBngB,YAjCE,yBAqCzB,IAAMuK,GAAOoT,GACPyC,GAAUzB,GACV0B,GAAStB,GACTvC,GAAUyC,GACVqB,GAAQf,GACRgB,GAAQf,GACR7C,GAAO8C,GACPe,GAAed,GACfe,GAAad,GACbe,GAAYb,GACZc,GAAgBb,GAChBc,GAAYb,GAEZc,GA7FJ3hB,IAEA,MAAM,oBAAE0e,EAAA,SAAqBxe,EAAU6M,KAAM4R,EAAA,aAAU3R,EAAA,YAAc4R,GAAgB5e,EAC/E6e,EAAYP,GAAaI,IACxB3R,EAAMgS,IAAWC,EAAAA,EAAAA,GAAqB,CAC3CC,KAAMN,EACNO,YAAaN,IAAe,EAC5BO,SAAUnS,EACVoS,OAAQ,oBAGV,OACE9e,EAAAA,EAAAA,KAAeif,GAAd,IAAsBV,EAAW9R,OAAYC,aAAc+R,EACzD7e,cAiFD0hB,GAAab,GACbc,GAAaZ,E,kCChfb,MAAAa,GAASC,E,QAAAA,GAAiB,SAAU,CACxC,CAAC,SAAU,CAAEC,GAAI,KAAMC,GAAI,KAAMC,EAAG,KAAM1Q,IAAK,Y,iDCX3C2Q,G,OAAyBpiB,EAAAA,mBAAqC,IAiBpE,SAASuN,EAAa8U,GACpB,MAAMC,EAAkBtiB,EAAAA,WAAWoiB,GACnC,OAAOC,GAAYC,GAAa,KAClC,C,qKCVMC,EAAc,gCACdC,EAAgB,CAAE9J,SAAS,EAAOC,YAAY,GAM9C8J,EAAa,oBAGZxW,EAAYC,EAAeC,IAAyBC,EAAAA,EAAAA,GAGzDqW,IAGKC,EAA+BnW,IAA+BnL,EAAAA,EAAAA,GACnEqhB,EACA,CAACtW,KA+BIwW,EAAqBC,GAC1BF,EAAkDD,GAK9C9M,EAAyB3V,EAAAA,WAC7B,CAACC,EAA2CC,KAExCK,EAAAA,EAAAA,KAAC0L,EAAWgD,SAAX,CAAoBtN,MAAO1B,EAAM4iB,wBAChC1iB,UAAAI,EAAAA,EAAAA,KAAC0L,EAAWiD,KAAX,CAAgBvN,MAAO1B,EAAM4iB,wBAC5B1iB,UAAAI,EAAAA,EAAAA,KAACuiB,EAAA,IAAyB7iB,EAAOS,IAAKR,SAOhDyV,EAAiB5U,YAAc0hB,EAgB/B,IAAMK,EAA6B9iB,EAAAA,WAGjC,CAACC,EAA+CC,KAChD,MAAM,wBACJ2iB,EAAA,YACAjN,EAAA,KACA3F,GAAO,EAAK,IACZpG,EACAgM,iBAAkBkN,EAAA,wBAClBC,EAAA,yBACAlN,EAAA,aACA1F,EAAA,0BACA2F,GAA4B,KACzB4B,GACD1X,EACES,EAAYV,EAAAA,OAAoC,MAChDkC,GAAeC,EAAAA,EAAAA,GAAgBjC,EAAcQ,GAC7C4M,GAAYC,EAAAA,EAAAA,IAAa1D,IACxBgM,EAAkBoN,IAAuBhE,EAAAA,EAAAA,GAAqB,CACnEC,KAAM6D,EACN5D,YAAa6D,GAA2B,KACxC5D,SAAUtJ,EACVuJ,OAAQoD,KAEHS,EAAkBC,GAA6BnjB,EAAAA,UAAS,GACzDojB,GAAmB1a,EAAAA,EAAAA,GAAe0H,GAClCK,EAAWvE,EAAc2W,GACzBQ,EAAwBrjB,EAAAA,QAAO,IAC9BsjB,EAAqBC,GAAgCvjB,EAAAA,SAAS,GAUrE,OARMA,EAAAA,UAAU,KACd,MAAM0D,EAAOhD,EAAI0B,QACjB,GAAIsB,EAEF,OADAA,EAAKgK,iBAAiB6U,EAAaa,GAC5B,IAAM1f,EAAKoK,oBAAoByU,EAAaa,IAEpD,CAACA,KAGF7iB,EAAAA,EAAAA,KAACoiB,EAAA,CACChhB,MAAOkhB,EACPjN,cACA/L,IAAKyD,EACL2C,OACA4F,mBACA2N,YAAmBxjB,EAAAA,YAChByjB,GAAcR,EAAoBQ,GACnC,CAACR,IAEHS,eAAsB1jB,EAAAA,YAAY,IAAMmjB,GAAoB,GAAO,IACnEQ,mBAA0B3jB,EAAAA,YACxB,IAAMujB,EAAwBK,GAAcA,EAAY,GACxD,IAEFC,sBAA6B7jB,EAAAA,YAC3B,IAAMujB,EAAwBK,GAAcA,EAAY,GACxD,IAGFzjB,UAAAI,EAAAA,EAAAA,KAACC,EAAAA,GAAU6B,IAAV,CACCyhB,SAAUZ,GAA4C,IAAxBI,GAA6B,EAAI,EAC/D,mBAAkB1N,KACd+B,EACJjX,IAAKwB,EACL+F,MAAO,CAAEiO,QAAS,UAAWjW,EAAMgI,OACnC8b,aAAapU,EAAAA,EAAAA,GAAqB1P,EAAM8jB,YAAa,KACnDV,EAAgBjhB,SAAU,IAE5BsX,SAAS/J,EAAAA,EAAAA,GAAqB1P,EAAMyZ,QAAU9J,IAK5C,MAAMoU,GAAmBX,EAAgBjhB,QAEzC,GAAIwN,EAAMyG,SAAWzG,EAAM2G,eAAiByN,IAAoBd,EAAkB,CAChF,MAAMe,EAAkB,IAAIxL,YAAY8J,EAAaC,GAGrD,GAFA5S,EAAM2G,cAAc2N,cAAcD,IAE7BA,EAAgBpL,iBAAkB,CACrC,MAAMlH,EAAQlB,IAAW7K,OAAQgM,GAASA,EAAK4H,WAO/CrC,EAJuB,CAFJxF,EAAMM,KAAML,GAASA,EAAKuS,QACzBxS,EAAMM,KAAML,GAASA,EAAKyK,KAAOxG,MACDlE,GAAO/L,OACzDwe,SAEoCnR,IAAKrB,GAASA,EAAKlR,IAAI0B,SAClC2T,EAC7B,CACF,CAEAsN,EAAgBjhB,SAAU,IAE5BgV,QAAQzH,EAAAA,EAAAA,GAAqB1P,EAAMmX,OAAQ,IAAM+L,GAAoB,UAUvErL,EAAY,uBAaZuM,EAA6BrkB,EAAAA,WACjC,CAACC,EAA0CC,KACzC,MAAM,wBACJ2iB,EAAA,UACArJ,GAAY,EAAI,OAChB2K,GAAS,EAAK,UACdV,EAAA,SACAtjB,KACG+X,GACDjY,EACEqkB,GAAS1I,EAAAA,EAAAA,KACTS,EAAKoH,GAAaa,EAClBriB,EAAU2gB,EAAsB9K,EAAW+K,GAC3C0B,EAAmBtiB,EAAQ4T,mBAAqBwG,EAChD5L,EAAWvE,EAAc2W,IAEzB,mBAAEc,EAAA,sBAAoBE,EAAA,iBAAuBhO,GAAqB5T,EASxE,OAPMjC,EAAAA,UAAU,KACd,GAAIwZ,EAEF,OADAmK,IACO,IAAME,KAEd,CAACrK,EAAWmK,EAAoBE,KAGjCtjB,EAAAA,EAAAA,KAAC0L,EAAWsN,SAAX,CACC5X,MAAOkhB,EACPxG,KACA7C,YACA2K,SAEAhkB,UAAAI,EAAAA,EAAAA,KAACC,EAAAA,GAAUwa,KAAV,CACC8I,SAAUS,EAAmB,GAAK,EAClC,mBAAkBtiB,EAAQ2T,eACtBsC,EACJxX,IAAKR,EACL6jB,aAAapU,EAAAA,EAAAA,GAAqB1P,EAAM8jB,YAAcnU,IAG/C4J,EAEAvX,EAAQuhB,YAAYnH,GAFTzM,EAAMC,mBAIxB6J,SAAS/J,EAAAA,EAAAA,GAAqB1P,EAAMyZ,QAAS,IAAMzX,EAAQuhB,YAAYnH,IACvElG,WAAWxG,EAAAA,EAAAA,GAAqB1P,EAAMkW,UAAYvG,IAChD,GAAkB,QAAdA,EAAM6B,KAAiB7B,EAAM4U,SAE/B,YADAviB,EAAQyhB,iBAIV,GAAI9T,EAAMyG,SAAWzG,EAAM2G,cAAe,OAE1C,MAAMkO,EAqDlB,SAAwB7U,EAA4BgG,EAA2B/L,GAC7E,MAAM4H,EARR,SAA8BA,EAAa5H,GACzC,MAAY,QAARA,EAAsB4H,EACX,cAARA,EAAsB,aAAuB,eAARA,EAAuB,YAAcA,CACnF,CAKciT,CAAqB9U,EAAM6B,IAAK5H,GAC5C,MAAoB,aAAhB+L,GAA8B,CAAC,YAAa,cAAciB,SAASpF,IACnD,eAAhBmE,GAAgC,CAAC,UAAW,aAAaiB,SAASpF,QADO,EAEtEkT,EAAwBlT,EACjC,CA1DgCmT,CAAehV,EAAO3N,EAAQ2T,YAAa3T,EAAQ4H,KAEvE,QAAoB,IAAhB4a,EAA2B,CAC7B,GAAI7U,EAAM+G,SAAW/G,EAAM6G,SAAW7G,EAAM8G,QAAU9G,EAAM4U,SAAU,OACtE5U,EAAMC,iBAEN,IAAIiH,EADUrG,IAAW7K,OAAQgM,GAASA,EAAK4H,WACpBvG,IAAKrB,GAASA,EAAKlR,IAAI0B,SAElD,GAAoB,SAAhBqiB,EAAwB3N,EAAeC,eAAQ,GAC1B,SAAhB0N,GAA0C,SAAhBA,EAAwB,CACrC,SAAhBA,GAAwB3N,EAAeC,UAC3C,MAAM8N,EAAe/N,EAAenE,QAAQ/C,EAAM2G,eAClDO,EAAiB7U,EAAQgO,MA8DP6C,EA7DY+R,EAAe,GA6DvChS,EA7DQiE,GA8Df7D,IAAO,CAACC,EAAGC,IAAUN,GAAOC,EAAaK,GAASN,EAAMpO,UA7DnDqS,EAAegO,MAAMD,EAAe,EAC1C,CAMAlR,WAAW,IAAMwD,EAAWL,GAC9B,CAoDZ,IAAsBjE,EAAYC,IAjDvB3S,SAAoB,oBAAbA,EACJA,EAAS,CAAEokB,mBAAkBQ,WAAgC,MAApBlP,IACzC1V,QAOdkkB,EAAqBtjB,YAAc+W,EAKnC,IAAM6M,EAAuD,CAC3DK,UAAW,OAAQC,QAAS,OAC5BC,WAAY,OAAQC,UAAW,OAC/BC,OAAQ,QAASC,KAAM,QACvBC,SAAU,OAAQC,IAAK,QAiBzB,SAASpO,EAAWH,GAAkD,IAAvBxB,EAAAhP,UAAA/B,OAAA,QAAA+gB,IAAAhf,UAAA,IAAAA,UAAA,GAC7C,MAAMyQ,EAA6BxJ,SAASsE,cAC5C,IAAK,MAAMmF,KAAaF,EAAY,CAElC,GAAIE,IAAcD,EAA4B,OAE9C,GADAC,EAAUtD,MAAM,CAAE4B,kBACd/H,SAASsE,gBAAkBkF,EAA4B,MAC7D,CACF,CAUA,IAAMjW,EAAO2U,EACP8P,EAAOpB,C,kCC5UP,MAAAqB,GAAe1D,E,QAAAA,GAAiB,eAAgB,CACpD,CAAC,OAAQ,CAAE2D,EAAG,gBAAiBlU,IAAK,Y", "sources": ["../node_modules/@radix-ui/react-arrow/src/arrow.tsx", "../node_modules/@radix-ui/react-popper/src/popper.tsx", "../node_modules/@radix-ui/react-use-size/src/use-size.tsx", "../node_modules/@radix-ui/react-menu/src/menu.tsx", "../node_modules/@radix-ui/react-dropdown-menu/src/dropdown-menu.tsx", "../node_modules/lucide-react/src/icons/circle.ts", "../node_modules/@radix-ui/react-direction/src/direction.tsx", "../node_modules/@radix-ui/react-roving-focus/src/roving-focus-group.tsx", "../node_modules/lucide-react/src/icons/chevron-right.ts"], "sourcesContent": ["import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * Arrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'Arrow';\n\ntype ArrowElement = React.ComponentRef<typeof Primitive.svg>;\ntype PrimitiveSvgProps = React.ComponentPropsWithoutRef<typeof Primitive.svg>;\ninterface ArrowProps extends PrimitiveSvgProps {}\n\nconst Arrow = React.forwardRef<ArrowElement, ArrowProps>((props, forwardedRef) => {\n  const { children, width = 10, height = 5, ...arrowProps } = props;\n  return (\n    <Primitive.svg\n      {...arrowProps}\n      ref={forwardedRef}\n      width={width}\n      height={height}\n      viewBox=\"0 0 30 10\"\n      preserveAspectRatio=\"none\"\n    >\n      {/* We use their children if they're slotting to replace the whole svg */}\n      {props.asChild ? children : <polygon points=\"0,0 30,0 15,10\" />}\n    </Primitive.svg>\n  );\n});\n\nArrow.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Arrow;\n\nexport {\n  Arrow,\n  //\n  Root,\n};\nexport type { ArrowProps };\n", "import * as React from 'react';\nimport {\n  useFloating,\n  autoUpdate,\n  offset,\n  shift,\n  limitShift,\n  hide,\n  arrow as floatingUIarrow,\n  flip,\n  size,\n} from '@floating-ui/react-dom';\nimport * as ArrowPrimitive from '@radix-ui/react-arrow';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { useSize } from '@radix-ui/react-use-size';\n\nimport type { Placement, Middleware } from '@floating-ui/react-dom';\nimport type { Scope } from '@radix-ui/react-context';\nimport type { Measurable } from '@radix-ui/rect';\n\nconst SIDE_OPTIONS = ['top', 'right', 'bottom', 'left'] as const;\nconst ALIGN_OPTIONS = ['start', 'center', 'end'] as const;\n\ntype Side = (typeof SIDE_OPTIONS)[number];\ntype Align = (typeof ALIGN_OPTIONS)[number];\n\n/* -------------------------------------------------------------------------------------------------\n * Popper\n * -----------------------------------------------------------------------------------------------*/\n\nconst POPPER_NAME = 'Popper';\n\ntype ScopedProps<P> = P & { __scopePopper?: Scope };\nconst [createPopperContext, createPopperScope] = createContextScope(POPPER_NAME);\n\ntype PopperContextValue = {\n  anchor: Measurable | null;\n  onAnchorChange(anchor: Measurable | null): void;\n};\nconst [PopperProvider, usePopperContext] = createPopperContext<PopperContextValue>(POPPER_NAME);\n\ninterface PopperProps {\n  children?: React.ReactNode;\n}\nconst Popper: React.FC<PopperProps> = (props: ScopedProps<PopperProps>) => {\n  const { __scopePopper, children } = props;\n  const [anchor, setAnchor] = React.useState<Measurable | null>(null);\n  return (\n    <PopperProvider scope={__scopePopper} anchor={anchor} onAnchorChange={setAnchor}>\n      {children}\n    </PopperProvider>\n  );\n};\n\nPopper.displayName = POPPER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopperAnchor\n * -----------------------------------------------------------------------------------------------*/\n\nconst ANCHOR_NAME = 'PopperAnchor';\n\ntype PopperAnchorElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface PopperAnchorProps extends PrimitiveDivProps {\n  virtualRef?: React.RefObject<Measurable>;\n}\n\nconst PopperAnchor = React.forwardRef<PopperAnchorElement, PopperAnchorProps>(\n  (props: ScopedProps<PopperAnchorProps>, forwardedRef) => {\n    const { __scopePopper, virtualRef, ...anchorProps } = props;\n    const context = usePopperContext(ANCHOR_NAME, __scopePopper);\n    const ref = React.useRef<PopperAnchorElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n\n    React.useEffect(() => {\n      // Consumer can anchor the popper to something that isn't\n      // a DOM node e.g. pointer position, so we override the\n      // `anchorRef` with their virtual ref in this case.\n      context.onAnchorChange(virtualRef?.current || ref.current);\n    });\n\n    return virtualRef ? null : <Primitive.div {...anchorProps} ref={composedRefs} />;\n  }\n);\n\nPopperAnchor.displayName = ANCHOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopperContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'PopperContent';\n\ntype PopperContentContextValue = {\n  placedSide: Side;\n  onArrowChange(arrow: HTMLSpanElement | null): void;\n  arrowX?: number;\n  arrowY?: number;\n  shouldHideArrow: boolean;\n};\n\nconst [PopperContentProvider, useContentContext] =\n  createPopperContext<PopperContentContextValue>(CONTENT_NAME);\n\ntype Boundary = Element | null;\n\ntype PopperContentElement = React.ComponentRef<typeof Primitive.div>;\ninterface PopperContentProps extends PrimitiveDivProps {\n  side?: Side;\n  sideOffset?: number;\n  align?: Align;\n  alignOffset?: number;\n  arrowPadding?: number;\n  avoidCollisions?: boolean;\n  collisionBoundary?: Boundary | Boundary[];\n  collisionPadding?: number | Partial<Record<Side, number>>;\n  sticky?: 'partial' | 'always';\n  hideWhenDetached?: boolean;\n  updatePositionStrategy?: 'optimized' | 'always';\n  onPlaced?: () => void;\n}\n\nconst PopperContent = React.forwardRef<PopperContentElement, PopperContentProps>(\n  (props: ScopedProps<PopperContentProps>, forwardedRef) => {\n    const {\n      __scopePopper,\n      side = 'bottom',\n      sideOffset = 0,\n      align = 'center',\n      alignOffset = 0,\n      arrowPadding = 0,\n      avoidCollisions = true,\n      collisionBoundary = [],\n      collisionPadding: collisionPaddingProp = 0,\n      sticky = 'partial',\n      hideWhenDetached = false,\n      updatePositionStrategy = 'optimized',\n      onPlaced,\n      ...contentProps\n    } = props;\n\n    const context = usePopperContext(CONTENT_NAME, __scopePopper);\n\n    const [content, setContent] = React.useState<HTMLDivElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n\n    const [arrow, setArrow] = React.useState<HTMLSpanElement | null>(null);\n    const arrowSize = useSize(arrow);\n    const arrowWidth = arrowSize?.width ?? 0;\n    const arrowHeight = arrowSize?.height ?? 0;\n\n    const desiredPlacement = (side + (align !== 'center' ? '-' + align : '')) as Placement;\n\n    const collisionPadding =\n      typeof collisionPaddingProp === 'number'\n        ? collisionPaddingProp\n        : { top: 0, right: 0, bottom: 0, left: 0, ...collisionPaddingProp };\n\n    const boundary = Array.isArray(collisionBoundary) ? collisionBoundary : [collisionBoundary];\n    const hasExplicitBoundaries = boundary.length > 0;\n\n    const detectOverflowOptions = {\n      padding: collisionPadding,\n      boundary: boundary.filter(isNotNull),\n      // with `strategy: 'fixed'`, this is the only way to get it to respect boundaries\n      altBoundary: hasExplicitBoundaries,\n    };\n\n    const { refs, floatingStyles, placement, isPositioned, middlewareData } = useFloating({\n      // default to `fixed` strategy so users don't have to pick and we also avoid focus scroll issues\n      strategy: 'fixed',\n      placement: desiredPlacement,\n      whileElementsMounted: (...args) => {\n        const cleanup = autoUpdate(...args, {\n          animationFrame: updatePositionStrategy === 'always',\n        });\n        return cleanup;\n      },\n      elements: {\n        reference: context.anchor,\n      },\n      middleware: [\n        offset({ mainAxis: sideOffset + arrowHeight, alignmentAxis: alignOffset }),\n        avoidCollisions &&\n          shift({\n            mainAxis: true,\n            crossAxis: false,\n            limiter: sticky === 'partial' ? limitShift() : undefined,\n            ...detectOverflowOptions,\n          }),\n        avoidCollisions && flip({ ...detectOverflowOptions }),\n        size({\n          ...detectOverflowOptions,\n          apply: ({ elements, rects, availableWidth, availableHeight }) => {\n            const { width: anchorWidth, height: anchorHeight } = rects.reference;\n            const contentStyle = elements.floating.style;\n            contentStyle.setProperty('--radix-popper-available-width', `${availableWidth}px`);\n            contentStyle.setProperty('--radix-popper-available-height', `${availableHeight}px`);\n            contentStyle.setProperty('--radix-popper-anchor-width', `${anchorWidth}px`);\n            contentStyle.setProperty('--radix-popper-anchor-height', `${anchorHeight}px`);\n          },\n        }),\n        arrow && floatingUIarrow({ element: arrow, padding: arrowPadding }),\n        transformOrigin({ arrowWidth, arrowHeight }),\n        hideWhenDetached && hide({ strategy: 'referenceHidden', ...detectOverflowOptions }),\n      ],\n    });\n\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n\n    const handlePlaced = useCallbackRef(onPlaced);\n    useLayoutEffect(() => {\n      if (isPositioned) {\n        handlePlaced?.();\n      }\n    }, [isPositioned, handlePlaced]);\n\n    const arrowX = middlewareData.arrow?.x;\n    const arrowY = middlewareData.arrow?.y;\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n\n    const [contentZIndex, setContentZIndex] = React.useState<string>();\n    useLayoutEffect(() => {\n      if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n    }, [content]);\n\n    return (\n      <div\n        ref={refs.setFloating}\n        data-radix-popper-content-wrapper=\"\"\n        style={{\n          ...floatingStyles,\n          transform: isPositioned ? floatingStyles.transform : 'translate(0, -200%)', // keep off the page when measuring\n          minWidth: 'max-content',\n          zIndex: contentZIndex,\n          ['--radix-popper-transform-origin' as any]: [\n            middlewareData.transformOrigin?.x,\n            middlewareData.transformOrigin?.y,\n          ].join(' '),\n\n          // hide the content if using the hide middleware and should be hidden\n          // set visibility to hidden and disable pointer events so the UI behaves\n          // as if the PopperContent isn't there at all\n          ...(middlewareData.hide?.referenceHidden && {\n            visibility: 'hidden',\n            pointerEvents: 'none',\n          }),\n        }}\n        // Floating UI interally calculates logical alignment based the `dir` attribute on\n        // the reference/floating node, we must add this attribute here to ensure\n        // this is calculated when portalled as well as inline.\n        dir={props.dir}\n      >\n        <PopperContentProvider\n          scope={__scopePopper}\n          placedSide={placedSide}\n          onArrowChange={setArrow}\n          arrowX={arrowX}\n          arrowY={arrowY}\n          shouldHideArrow={cannotCenterArrow}\n        >\n          <Primitive.div\n            data-side={placedSide}\n            data-align={placedAlign}\n            {...contentProps}\n            ref={composedRefs}\n            style={{\n              ...contentProps.style,\n              // if the PopperContent hasn't been placed yet (not all measurements done)\n              // we prevent animations so that users's animation don't kick in too early referring wrong sides\n              animation: !isPositioned ? 'none' : undefined,\n            }}\n          />\n        </PopperContentProvider>\n      </div>\n    );\n  }\n);\n\nPopperContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopperArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'PopperArrow';\n\nconst OPPOSITE_SIDE: Record<Side, Side> = {\n  top: 'bottom',\n  right: 'left',\n  bottom: 'top',\n  left: 'right',\n};\n\ntype PopperArrowElement = React.ComponentRef<typeof ArrowPrimitive.Root>;\ntype ArrowProps = React.ComponentPropsWithoutRef<typeof ArrowPrimitive.Root>;\ninterface PopperArrowProps extends ArrowProps {}\n\nconst PopperArrow = React.forwardRef<PopperArrowElement, PopperArrowProps>(function PopperArrow(\n  props: ScopedProps<PopperArrowProps>,\n  forwardedRef\n) {\n  const { __scopePopper, ...arrowProps } = props;\n  const contentContext = useContentContext(ARROW_NAME, __scopePopper);\n  const baseSide = OPPOSITE_SIDE[contentContext.placedSide];\n\n  return (\n    // we have to use an extra wrapper because `ResizeObserver` (used by `useSize`)\n    // doesn't report size as we'd expect on SVG elements.\n    // it reports their bounding box which is effectively the largest path inside the SVG.\n    <span\n      ref={contentContext.onArrowChange}\n      style={{\n        position: 'absolute',\n        left: contentContext.arrowX,\n        top: contentContext.arrowY,\n        [baseSide]: 0,\n        transformOrigin: {\n          top: '',\n          right: '0 0',\n          bottom: 'center 0',\n          left: '100% 0',\n        }[contentContext.placedSide],\n        transform: {\n          top: 'translateY(100%)',\n          right: 'translateY(50%) rotate(90deg) translateX(-50%)',\n          bottom: `rotate(180deg)`,\n          left: 'translateY(50%) rotate(-90deg) translateX(50%)',\n        }[contentContext.placedSide],\n        visibility: contentContext.shouldHideArrow ? 'hidden' : undefined,\n      }}\n    >\n      <ArrowPrimitive.Root\n        {...arrowProps}\n        ref={forwardedRef}\n        style={{\n          ...arrowProps.style,\n          // ensures the element can be measured correctly (mostly for if SVG)\n          display: 'block',\n        }}\n      />\n    </span>\n  );\n});\n\nPopperArrow.displayName = ARROW_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction isNotNull<T>(value: T | null): value is T {\n  return value !== null;\n}\n\nconst transformOrigin = (options: { arrowWidth: number; arrowHeight: number }): Middleware => ({\n  name: 'transformOrigin',\n  options,\n  fn(data) {\n    const { placement, rects, middlewareData } = data;\n\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n    const isArrowHidden = cannotCenterArrow;\n    const arrowWidth = isArrowHidden ? 0 : options.arrowWidth;\n    const arrowHeight = isArrowHidden ? 0 : options.arrowHeight;\n\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n    const noArrowAlign = { start: '0%', center: '50%', end: '100%' }[placedAlign];\n\n    const arrowXCenter = (middlewareData.arrow?.x ?? 0) + arrowWidth / 2;\n    const arrowYCenter = (middlewareData.arrow?.y ?? 0) + arrowHeight / 2;\n\n    let x = '';\n    let y = '';\n\n    if (placedSide === 'bottom') {\n      x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n      y = `${-arrowHeight}px`;\n    } else if (placedSide === 'top') {\n      x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n      y = `${rects.floating.height + arrowHeight}px`;\n    } else if (placedSide === 'right') {\n      x = `${-arrowHeight}px`;\n      y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n    } else if (placedSide === 'left') {\n      x = `${rects.floating.width + arrowHeight}px`;\n      y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n    }\n    return { data: { x, y } };\n  },\n});\n\nfunction getSideAndAlignFromPlacement(placement: Placement) {\n  const [side, align = 'center'] = placement.split('-');\n  return [side as Side, align as Align] as const;\n}\n\nconst Root = Popper;\nconst Anchor = PopperAnchor;\nconst Content = PopperContent;\nconst Arrow = PopperArrow;\n\nexport {\n  createPopperScope,\n  //\n  Popper,\n  PopperAnchor,\n  PopperContent,\n  PopperArrow,\n  //\n  Root,\n  Anchor,\n  Content,\n  Arrow,\n  //\n  SIDE_OPTIONS,\n  ALIGN_OPTIONS,\n};\nexport type { PopperProps, PopperAnchorProps, PopperContentProps, PopperArrowProps };\n", "/// <reference types=\"resize-observer-browser\" />\n\nimport * as React from 'react';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\n\nfunction useSize(element: HTMLElement | null) {\n  const [size, setSize] = React.useState<{ width: number; height: number } | undefined>(undefined);\n\n  useLayoutEffect(() => {\n    if (element) {\n      // provide size as early as possible\n      setSize({ width: element.offsetWidth, height: element.offsetHeight });\n\n      const resizeObserver = new ResizeObserver((entries) => {\n        if (!Array.isArray(entries)) {\n          return;\n        }\n\n        // Since we only observe the one element, we don't need to loop over the\n        // array\n        if (!entries.length) {\n          return;\n        }\n\n        const entry = entries[0];\n        let width: number;\n        let height: number;\n\n        if ('borderBoxSize' in entry) {\n          const borderSizeEntry = entry['borderBoxSize'];\n          // iron out differences between browsers\n          const borderSize = Array.isArray(borderSizeEntry) ? borderSizeEntry[0] : borderSizeEntry;\n          width = borderSize['inlineSize'];\n          height = borderSize['blockSize'];\n        } else {\n          // for browsers that don't support `borderBoxSize`\n          // we calculate it ourselves to get the correct border box.\n          width = element.offsetWidth;\n          height = element.offsetHeight;\n        }\n\n        setSize({ width, height });\n      });\n\n      resizeObserver.observe(element, { box: 'border-box' });\n\n      return () => resizeObserver.unobserve(element);\n    } else {\n      // We only want to reset to `undefined` when the element becomes `null`,\n      // not if it changes to another element.\n      setSize(undefined);\n    }\n  }, [element]);\n\n  return size;\n}\n\nexport { useSize };\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs, composeRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { useFocusGuards } from '@radix-ui/react-focus-guards';\nimport { FocusScope } from '@radix-ui/react-focus-scope';\nimport { useId } from '@radix-ui/react-id';\nimport * as PopperPrimitive from '@radix-ui/react-popper';\nimport { createPopperScope } from '@radix-ui/react-popper';\nimport { Portal as PortalPrimitive } from '@radix-ui/react-portal';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive, dispatchDiscreteCustomEvent } from '@radix-ui/react-primitive';\nimport * as RovingFocusGroup from '@radix-ui/react-roving-focus';\nimport { createRovingFocusGroupScope } from '@radix-ui/react-roving-focus';\nimport { createSlot } from '@radix-ui/react-slot';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { hideOthers } from 'aria-hidden';\nimport { RemoveScroll } from 'react-remove-scroll';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Direction = 'ltr' | 'rtl';\n\nconst SELECTION_KEYS = ['Enter', ' '];\nconst FIRST_KEYS = ['ArrowDown', 'PageUp', 'Home'];\nconst LAST_KEYS = ['ArrowUp', 'PageDown', 'End'];\nconst FIRST_LAST_KEYS = [...FIRST_KEYS, ...LAST_KEYS];\nconst SUB_OPEN_KEYS: Record<Direction, string[]> = {\n  ltr: [...SELECTION_KEYS, 'ArrowRight'],\n  rtl: [...SELECTION_KEYS, 'ArrowLeft'],\n};\nconst SUB_CLOSE_KEYS: Record<Direction, string[]> = {\n  ltr: ['ArrowLeft'],\n  rtl: ['ArrowRight'],\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Menu\n * -----------------------------------------------------------------------------------------------*/\n\nconst MENU_NAME = 'Menu';\n\ntype ItemData = { disabled: boolean; textValue: string };\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  MenuItemElement,\n  ItemData\n>(MENU_NAME);\n\ntype ScopedProps<P> = P & { __scopeMenu?: Scope };\nconst [createMenuContext, createMenuScope] = createContextScope(MENU_NAME, [\n  createCollectionScope,\n  createPopperScope,\n  createRovingFocusGroupScope,\n]);\nconst usePopperScope = createPopperScope();\nconst useRovingFocusGroupScope = createRovingFocusGroupScope();\n\ntype MenuContextValue = {\n  open: boolean;\n  onOpenChange(open: boolean): void;\n  content: MenuContentElement | null;\n  onContentChange(content: MenuContentElement | null): void;\n};\n\nconst [MenuProvider, useMenuContext] = createMenuContext<MenuContextValue>(MENU_NAME);\n\ntype MenuRootContextValue = {\n  onClose(): void;\n  isUsingKeyboardRef: React.RefObject<boolean>;\n  dir: Direction;\n  modal: boolean;\n};\n\nconst [MenuRootProvider, useMenuRootContext] = createMenuContext<MenuRootContextValue>(MENU_NAME);\n\ninterface MenuProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  onOpenChange?(open: boolean): void;\n  dir?: Direction;\n  modal?: boolean;\n}\n\nconst Menu: React.FC<MenuProps> = (props: ScopedProps<MenuProps>) => {\n  const { __scopeMenu, open = false, children, dir, onOpenChange, modal = true } = props;\n  const popperScope = usePopperScope(__scopeMenu);\n  const [content, setContent] = React.useState<MenuContentElement | null>(null);\n  const isUsingKeyboardRef = React.useRef(false);\n  const handleOpenChange = useCallbackRef(onOpenChange);\n  const direction = useDirection(dir);\n\n  React.useEffect(() => {\n    // Capture phase ensures we set the boolean before any side effects execute\n    // in response to the key or pointer event as they might depend on this value.\n    const handleKeyDown = () => {\n      isUsingKeyboardRef.current = true;\n      document.addEventListener('pointerdown', handlePointer, { capture: true, once: true });\n      document.addEventListener('pointermove', handlePointer, { capture: true, once: true });\n    };\n    const handlePointer = () => (isUsingKeyboardRef.current = false);\n    document.addEventListener('keydown', handleKeyDown, { capture: true });\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown, { capture: true });\n      document.removeEventListener('pointerdown', handlePointer, { capture: true });\n      document.removeEventListener('pointermove', handlePointer, { capture: true });\n    };\n  }, []);\n\n  return (\n    <PopperPrimitive.Root {...popperScope}>\n      <MenuProvider\n        scope={__scopeMenu}\n        open={open}\n        onOpenChange={handleOpenChange}\n        content={content}\n        onContentChange={setContent}\n      >\n        <MenuRootProvider\n          scope={__scopeMenu}\n          onClose={React.useCallback(() => handleOpenChange(false), [handleOpenChange])}\n          isUsingKeyboardRef={isUsingKeyboardRef}\n          dir={direction}\n          modal={modal}\n        >\n          {children}\n        </MenuRootProvider>\n      </MenuProvider>\n    </PopperPrimitive.Root>\n  );\n};\n\nMenu.displayName = MENU_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuAnchor\n * -----------------------------------------------------------------------------------------------*/\n\nconst ANCHOR_NAME = 'MenuAnchor';\n\ntype MenuAnchorElement = React.ComponentRef<typeof PopperPrimitive.Anchor>;\ntype PopperAnchorProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Anchor>;\ninterface MenuAnchorProps extends PopperAnchorProps {}\n\nconst MenuAnchor = React.forwardRef<MenuAnchorElement, MenuAnchorProps>(\n  (props: ScopedProps<MenuAnchorProps>, forwardedRef) => {\n    const { __scopeMenu, ...anchorProps } = props;\n    const popperScope = usePopperScope(__scopeMenu);\n    return <PopperPrimitive.Anchor {...popperScope} {...anchorProps} ref={forwardedRef} />;\n  }\n);\n\nMenuAnchor.displayName = ANCHOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'MenuPortal';\n\ntype PortalContextValue = { forceMount?: true };\nconst [PortalProvider, usePortalContext] = createMenuContext<PortalContextValue>(PORTAL_NAME, {\n  forceMount: undefined,\n});\n\ntype PortalProps = React.ComponentPropsWithoutRef<typeof PortalPrimitive>;\ninterface MenuPortalProps {\n  children?: React.ReactNode;\n  /**\n   * Specify a container element to portal the content into.\n   */\n  container?: PortalProps['container'];\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst MenuPortal: React.FC<MenuPortalProps> = (props: ScopedProps<MenuPortalProps>) => {\n  const { __scopeMenu, forceMount, children, container } = props;\n  const context = useMenuContext(PORTAL_NAME, __scopeMenu);\n  return (\n    <PortalProvider scope={__scopeMenu} forceMount={forceMount}>\n      <Presence present={forceMount || context.open}>\n        <PortalPrimitive asChild container={container}>\n          {children}\n        </PortalPrimitive>\n      </Presence>\n    </PortalProvider>\n  );\n};\n\nMenuPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'MenuContent';\n\ntype MenuContentContextValue = {\n  onItemEnter(event: React.PointerEvent): void;\n  onItemLeave(event: React.PointerEvent): void;\n  onTriggerLeave(event: React.PointerEvent): void;\n  searchRef: React.RefObject<string>;\n  pointerGraceTimerRef: React.MutableRefObject<number>;\n  onPointerGraceIntentChange(intent: GraceIntent | null): void;\n};\nconst [MenuContentProvider, useMenuContentContext] =\n  createMenuContext<MenuContentContextValue>(CONTENT_NAME);\n\ntype MenuContentElement = MenuRootContentTypeElement;\n/**\n * We purposefully don't union MenuRootContent and MenuSubContent props here because\n * they have conflicting prop types. We agreed that we would allow MenuSubContent to\n * accept props that it would just ignore.\n */\ninterface MenuContentProps extends MenuRootContentTypeProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst MenuContent = React.forwardRef<MenuContentElement, MenuContentProps>(\n  (props: ScopedProps<MenuContentProps>, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeMenu);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, props.__scopeMenu);\n\n    return (\n      <Collection.Provider scope={props.__scopeMenu}>\n        <Presence present={forceMount || context.open}>\n          <Collection.Slot scope={props.__scopeMenu}>\n            {rootContext.modal ? (\n              <MenuRootContentModal {...contentProps} ref={forwardedRef} />\n            ) : (\n              <MenuRootContentNonModal {...contentProps} ref={forwardedRef} />\n            )}\n          </Collection.Slot>\n        </Presence>\n      </Collection.Provider>\n    );\n  }\n);\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype MenuRootContentTypeElement = MenuContentImplElement;\ninterface MenuRootContentTypeProps\n  extends Omit<MenuContentImplProps, keyof MenuContentImplPrivateProps> {}\n\nconst MenuRootContentModal = React.forwardRef<MenuRootContentTypeElement, MenuRootContentTypeProps>(\n  (props: ScopedProps<MenuRootContentTypeProps>, forwardedRef) => {\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const ref = React.useRef<MenuRootContentTypeElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n\n    // Hide everything from ARIA except the `MenuContent`\n    React.useEffect(() => {\n      const content = ref.current;\n      if (content) return hideOthers(content);\n    }, []);\n\n    return (\n      <MenuContentImpl\n        {...props}\n        ref={composedRefs}\n        // we make sure we're not trapping once it's been closed\n        // (closed !== unmounted when animating out)\n        trapFocus={context.open}\n        // make sure to only disable pointer events when open\n        // this avoids blocking interactions while animating out\n        disableOutsidePointerEvents={context.open}\n        disableOutsideScroll\n        // When focus is trapped, a `focusout` event may still happen.\n        // We make sure we don't trigger our `onDismiss` in such case.\n        onFocusOutside={composeEventHandlers(\n          props.onFocusOutside,\n          (event) => event.preventDefault(),\n          { checkForDefaultPrevented: false }\n        )}\n        onDismiss={() => context.onOpenChange(false)}\n      />\n    );\n  }\n);\n\nconst MenuRootContentNonModal = React.forwardRef<\n  MenuRootContentTypeElement,\n  MenuRootContentTypeProps\n>((props: ScopedProps<MenuRootContentTypeProps>, forwardedRef) => {\n  const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n  return (\n    <MenuContentImpl\n      {...props}\n      ref={forwardedRef}\n      trapFocus={false}\n      disableOutsidePointerEvents={false}\n      disableOutsideScroll={false}\n      onDismiss={() => context.onOpenChange(false)}\n    />\n  );\n});\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype MenuContentImplElement = React.ComponentRef<typeof PopperPrimitive.Content>;\ntype FocusScopeProps = React.ComponentPropsWithoutRef<typeof FocusScope>;\ntype DismissableLayerProps = React.ComponentPropsWithoutRef<typeof DismissableLayer>;\ntype RovingFocusGroupProps = React.ComponentPropsWithoutRef<typeof RovingFocusGroup.Root>;\ntype PopperContentProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Content>;\ntype MenuContentImplPrivateProps = {\n  onOpenAutoFocus?: FocusScopeProps['onMountAutoFocus'];\n  onDismiss?: DismissableLayerProps['onDismiss'];\n  disableOutsidePointerEvents?: DismissableLayerProps['disableOutsidePointerEvents'];\n\n  /**\n   * Whether scrolling outside the `MenuContent` should be prevented\n   * (default: `false`)\n   */\n  disableOutsideScroll?: boolean;\n\n  /**\n   * Whether focus should be trapped within the `MenuContent`\n   * (default: false)\n   */\n  trapFocus?: FocusScopeProps['trapped'];\n};\ninterface MenuContentImplProps\n  extends MenuContentImplPrivateProps,\n    Omit<PopperContentProps, 'dir' | 'onPlaced'> {\n  /**\n   * Event handler called when auto-focusing on close.\n   * Can be prevented.\n   */\n  onCloseAutoFocus?: FocusScopeProps['onUnmountAutoFocus'];\n\n  /**\n   * Whether keyboard navigation should loop around\n   * @defaultValue false\n   */\n  loop?: RovingFocusGroupProps['loop'];\n\n  onEntryFocus?: RovingFocusGroupProps['onEntryFocus'];\n  onEscapeKeyDown?: DismissableLayerProps['onEscapeKeyDown'];\n  onPointerDownOutside?: DismissableLayerProps['onPointerDownOutside'];\n  onFocusOutside?: DismissableLayerProps['onFocusOutside'];\n  onInteractOutside?: DismissableLayerProps['onInteractOutside'];\n}\n\nconst Slot = createSlot('MenuContent.ScrollLock');\n\nconst MenuContentImpl = React.forwardRef<MenuContentImplElement, MenuContentImplProps>(\n  (props: ScopedProps<MenuContentImplProps>, forwardedRef) => {\n    const {\n      __scopeMenu,\n      loop = false,\n      trapFocus,\n      onOpenAutoFocus,\n      onCloseAutoFocus,\n      disableOutsidePointerEvents,\n      onEntryFocus,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      onFocusOutside,\n      onInteractOutside,\n      onDismiss,\n      disableOutsideScroll,\n      ...contentProps\n    } = props;\n    const context = useMenuContext(CONTENT_NAME, __scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, __scopeMenu);\n    const popperScope = usePopperScope(__scopeMenu);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeMenu);\n    const getItems = useCollection(__scopeMenu);\n    const [currentItemId, setCurrentItemId] = React.useState<string | null>(null);\n    const contentRef = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, contentRef, context.onContentChange);\n    const timerRef = React.useRef(0);\n    const searchRef = React.useRef('');\n    const pointerGraceTimerRef = React.useRef(0);\n    const pointerGraceIntentRef = React.useRef<GraceIntent | null>(null);\n    const pointerDirRef = React.useRef<Side>('right');\n    const lastPointerXRef = React.useRef(0);\n\n    const ScrollLockWrapper = disableOutsideScroll ? RemoveScroll : React.Fragment;\n    const scrollLockWrapperProps = disableOutsideScroll\n      ? { as: Slot, allowPinchZoom: true }\n      : undefined;\n\n    const handleTypeaheadSearch = (key: string) => {\n      const search = searchRef.current + key;\n      const items = getItems().filter((item) => !item.disabled);\n      const currentItem = document.activeElement;\n      const currentMatch = items.find((item) => item.ref.current === currentItem)?.textValue;\n      const values = items.map((item) => item.textValue);\n      const nextMatch = getNextMatch(values, search, currentMatch);\n      const newItem = items.find((item) => item.textValue === nextMatch)?.ref.current;\n\n      // Reset `searchRef` 1 second after it was last updated\n      (function updateSearch(value: string) {\n        searchRef.current = value;\n        window.clearTimeout(timerRef.current);\n        if (value !== '') timerRef.current = window.setTimeout(() => updateSearch(''), 1000);\n      })(search);\n\n      if (newItem) {\n        /**\n         * Imperative focus during keydown is risky so we prevent React's batching updates\n         * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n         */\n        setTimeout(() => (newItem as HTMLElement).focus());\n      }\n    };\n\n    React.useEffect(() => {\n      return () => window.clearTimeout(timerRef.current);\n    }, []);\n\n    // Make sure the whole tree has focus guards as our `MenuContent` may be\n    // the last element in the DOM (because of the `Portal`)\n    useFocusGuards();\n\n    const isPointerMovingToSubmenu = React.useCallback((event: React.PointerEvent) => {\n      const isMovingTowards = pointerDirRef.current === pointerGraceIntentRef.current?.side;\n      return isMovingTowards && isPointerInGraceArea(event, pointerGraceIntentRef.current?.area);\n    }, []);\n\n    return (\n      <MenuContentProvider\n        scope={__scopeMenu}\n        searchRef={searchRef}\n        onItemEnter={React.useCallback(\n          (event) => {\n            if (isPointerMovingToSubmenu(event)) event.preventDefault();\n          },\n          [isPointerMovingToSubmenu]\n        )}\n        onItemLeave={React.useCallback(\n          (event) => {\n            if (isPointerMovingToSubmenu(event)) return;\n            contentRef.current?.focus();\n            setCurrentItemId(null);\n          },\n          [isPointerMovingToSubmenu]\n        )}\n        onTriggerLeave={React.useCallback(\n          (event) => {\n            if (isPointerMovingToSubmenu(event)) event.preventDefault();\n          },\n          [isPointerMovingToSubmenu]\n        )}\n        pointerGraceTimerRef={pointerGraceTimerRef}\n        onPointerGraceIntentChange={React.useCallback((intent) => {\n          pointerGraceIntentRef.current = intent;\n        }, [])}\n      >\n        <ScrollLockWrapper {...scrollLockWrapperProps}>\n          <FocusScope\n            asChild\n            trapped={trapFocus}\n            onMountAutoFocus={composeEventHandlers(onOpenAutoFocus, (event) => {\n              // when opening, explicitly focus the content area only and leave\n              // `onEntryFocus` in  control of focusing first item\n              event.preventDefault();\n              contentRef.current?.focus({ preventScroll: true });\n            })}\n            onUnmountAutoFocus={onCloseAutoFocus}\n          >\n            <DismissableLayer\n              asChild\n              disableOutsidePointerEvents={disableOutsidePointerEvents}\n              onEscapeKeyDown={onEscapeKeyDown}\n              onPointerDownOutside={onPointerDownOutside}\n              onFocusOutside={onFocusOutside}\n              onInteractOutside={onInteractOutside}\n              onDismiss={onDismiss}\n            >\n              <RovingFocusGroup.Root\n                asChild\n                {...rovingFocusGroupScope}\n                dir={rootContext.dir}\n                orientation=\"vertical\"\n                loop={loop}\n                currentTabStopId={currentItemId}\n                onCurrentTabStopIdChange={setCurrentItemId}\n                onEntryFocus={composeEventHandlers(onEntryFocus, (event) => {\n                  // only focus first item when using keyboard\n                  if (!rootContext.isUsingKeyboardRef.current) event.preventDefault();\n                })}\n                preventScrollOnEntryFocus\n              >\n                <PopperPrimitive.Content\n                  role=\"menu\"\n                  aria-orientation=\"vertical\"\n                  data-state={getOpenState(context.open)}\n                  data-radix-menu-content=\"\"\n                  dir={rootContext.dir}\n                  {...popperScope}\n                  {...contentProps}\n                  ref={composedRefs}\n                  style={{ outline: 'none', ...contentProps.style }}\n                  onKeyDown={composeEventHandlers(contentProps.onKeyDown, (event) => {\n                    // submenu key events bubble through portals. We only care about keys in this menu.\n                    const target = event.target as HTMLElement;\n                    const isKeyDownInside =\n                      target.closest('[data-radix-menu-content]') === event.currentTarget;\n                    const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n                    const isCharacterKey = event.key.length === 1;\n                    if (isKeyDownInside) {\n                      // menus should not be navigated using tab key so we prevent it\n                      if (event.key === 'Tab') event.preventDefault();\n                      if (!isModifierKey && isCharacterKey) handleTypeaheadSearch(event.key);\n                    }\n                    // focus first/last item based on key pressed\n                    const content = contentRef.current;\n                    if (event.target !== content) return;\n                    if (!FIRST_LAST_KEYS.includes(event.key)) return;\n                    event.preventDefault();\n                    const items = getItems().filter((item) => !item.disabled);\n                    const candidateNodes = items.map((item) => item.ref.current!);\n                    if (LAST_KEYS.includes(event.key)) candidateNodes.reverse();\n                    focusFirst(candidateNodes);\n                  })}\n                  onBlur={composeEventHandlers(props.onBlur, (event) => {\n                    // clear search buffer when leaving the menu\n                    if (!event.currentTarget.contains(event.target)) {\n                      window.clearTimeout(timerRef.current);\n                      searchRef.current = '';\n                    }\n                  })}\n                  onPointerMove={composeEventHandlers(\n                    props.onPointerMove,\n                    whenMouse((event) => {\n                      const target = event.target as HTMLElement;\n                      const pointerXHasChanged = lastPointerXRef.current !== event.clientX;\n\n                      // We don't use `event.movementX` for this check because Safari will\n                      // always return `0` on a pointer event.\n                      if (event.currentTarget.contains(target) && pointerXHasChanged) {\n                        const newDir = event.clientX > lastPointerXRef.current ? 'right' : 'left';\n                        pointerDirRef.current = newDir;\n                        lastPointerXRef.current = event.clientX;\n                      }\n                    })\n                  )}\n                />\n              </RovingFocusGroup.Root>\n            </DismissableLayer>\n          </FocusScope>\n        </ScrollLockWrapper>\n      </MenuContentProvider>\n    );\n  }\n);\n\nMenuContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'MenuGroup';\n\ntype MenuGroupElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface MenuGroupProps extends PrimitiveDivProps {}\n\nconst MenuGroup = React.forwardRef<MenuGroupElement, MenuGroupProps>(\n  (props: ScopedProps<MenuGroupProps>, forwardedRef) => {\n    const { __scopeMenu, ...groupProps } = props;\n    return <Primitive.div role=\"group\" {...groupProps} ref={forwardedRef} />;\n  }\n);\n\nMenuGroup.displayName = GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuLabel\n * -----------------------------------------------------------------------------------------------*/\n\nconst LABEL_NAME = 'MenuLabel';\n\ntype MenuLabelElement = React.ComponentRef<typeof Primitive.div>;\ninterface MenuLabelProps extends PrimitiveDivProps {}\n\nconst MenuLabel = React.forwardRef<MenuLabelElement, MenuLabelProps>(\n  (props: ScopedProps<MenuLabelProps>, forwardedRef) => {\n    const { __scopeMenu, ...labelProps } = props;\n    return <Primitive.div {...labelProps} ref={forwardedRef} />;\n  }\n);\n\nMenuLabel.displayName = LABEL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'MenuItem';\nconst ITEM_SELECT = 'menu.itemSelect';\n\ntype MenuItemElement = MenuItemImplElement;\ninterface MenuItemProps extends Omit<MenuItemImplProps, 'onSelect'> {\n  onSelect?: (event: Event) => void;\n}\n\nconst MenuItem = React.forwardRef<MenuItemElement, MenuItemProps>(\n  (props: ScopedProps<MenuItemProps>, forwardedRef) => {\n    const { disabled = false, onSelect, ...itemProps } = props;\n    const ref = React.useRef<HTMLDivElement>(null);\n    const rootContext = useMenuRootContext(ITEM_NAME, props.__scopeMenu);\n    const contentContext = useMenuContentContext(ITEM_NAME, props.__scopeMenu);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const isPointerDownRef = React.useRef(false);\n\n    const handleSelect = () => {\n      const menuItem = ref.current;\n      if (!disabled && menuItem) {\n        const itemSelectEvent = new CustomEvent(ITEM_SELECT, { bubbles: true, cancelable: true });\n        menuItem.addEventListener(ITEM_SELECT, (event) => onSelect?.(event), { once: true });\n        dispatchDiscreteCustomEvent(menuItem, itemSelectEvent);\n        if (itemSelectEvent.defaultPrevented) {\n          isPointerDownRef.current = false;\n        } else {\n          rootContext.onClose();\n        }\n      }\n    };\n\n    return (\n      <MenuItemImpl\n        {...itemProps}\n        ref={composedRefs}\n        disabled={disabled}\n        onClick={composeEventHandlers(props.onClick, handleSelect)}\n        onPointerDown={(event) => {\n          props.onPointerDown?.(event);\n          isPointerDownRef.current = true;\n        }}\n        onPointerUp={composeEventHandlers(props.onPointerUp, (event) => {\n          // Pointer down can move to a different menu item which should activate it on pointer up.\n          // We dispatch a click for selection to allow composition with click based triggers and to\n          // prevent Firefox from getting stuck in text selection mode when the menu closes.\n          if (!isPointerDownRef.current) event.currentTarget?.click();\n        })}\n        onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n          const isTypingAhead = contentContext.searchRef.current !== '';\n          if (disabled || (isTypingAhead && event.key === ' ')) return;\n          if (SELECTION_KEYS.includes(event.key)) {\n            event.currentTarget.click();\n            /**\n             * We prevent default browser behaviour for selection keys as they should trigger\n             * a selection only:\n             * - prevents space from scrolling the page.\n             * - if keydown causes focus to move, prevents keydown from firing on the new target.\n             */\n            event.preventDefault();\n          }\n        })}\n      />\n    );\n  }\n);\n\nMenuItem.displayName = ITEM_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype MenuItemImplElement = React.ComponentRef<typeof Primitive.div>;\ninterface MenuItemImplProps extends PrimitiveDivProps {\n  disabled?: boolean;\n  textValue?: string;\n}\n\nconst MenuItemImpl = React.forwardRef<MenuItemImplElement, MenuItemImplProps>(\n  (props: ScopedProps<MenuItemImplProps>, forwardedRef) => {\n    const { __scopeMenu, disabled = false, textValue, ...itemProps } = props;\n    const contentContext = useMenuContentContext(ITEM_NAME, __scopeMenu);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeMenu);\n    const ref = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const [isFocused, setIsFocused] = React.useState(false);\n\n    // get the item's `.textContent` as default strategy for typeahead `textValue`\n    const [textContent, setTextContent] = React.useState('');\n    React.useEffect(() => {\n      const menuItem = ref.current;\n      if (menuItem) {\n        setTextContent((menuItem.textContent ?? '').trim());\n      }\n    }, [itemProps.children]);\n\n    return (\n      <Collection.ItemSlot\n        scope={__scopeMenu}\n        disabled={disabled}\n        textValue={textValue ?? textContent}\n      >\n        <RovingFocusGroup.Item asChild {...rovingFocusGroupScope} focusable={!disabled}>\n          <Primitive.div\n            role=\"menuitem\"\n            data-highlighted={isFocused ? '' : undefined}\n            aria-disabled={disabled || undefined}\n            data-disabled={disabled ? '' : undefined}\n            {...itemProps}\n            ref={composedRefs}\n            /**\n             * We focus items on `pointerMove` to achieve the following:\n             *\n             * - Mouse over an item (it focuses)\n             * - Leave mouse where it is and use keyboard to focus a different item\n             * - Wiggle mouse without it leaving previously focused item\n             * - Previously focused item should re-focus\n             *\n             * If we used `mouseOver`/`mouseEnter` it would not re-focus when the mouse\n             * wiggles. This is to match native menu implementation.\n             */\n            onPointerMove={composeEventHandlers(\n              props.onPointerMove,\n              whenMouse((event) => {\n                if (disabled) {\n                  contentContext.onItemLeave(event);\n                } else {\n                  contentContext.onItemEnter(event);\n                  if (!event.defaultPrevented) {\n                    const item = event.currentTarget;\n                    item.focus({ preventScroll: true });\n                  }\n                }\n              })\n            )}\n            onPointerLeave={composeEventHandlers(\n              props.onPointerLeave,\n              whenMouse((event) => contentContext.onItemLeave(event))\n            )}\n            onFocus={composeEventHandlers(props.onFocus, () => setIsFocused(true))}\n            onBlur={composeEventHandlers(props.onBlur, () => setIsFocused(false))}\n          />\n        </RovingFocusGroup.Item>\n      </Collection.ItemSlot>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * MenuCheckboxItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst CHECKBOX_ITEM_NAME = 'MenuCheckboxItem';\n\ntype MenuCheckboxItemElement = MenuItemElement;\n\ntype CheckedState = boolean | 'indeterminate';\n\ninterface MenuCheckboxItemProps extends MenuItemProps {\n  checked?: CheckedState;\n  // `onCheckedChange` can never be called with `\"indeterminate\"` from the inside\n  onCheckedChange?: (checked: boolean) => void;\n}\n\nconst MenuCheckboxItem = React.forwardRef<MenuCheckboxItemElement, MenuCheckboxItemProps>(\n  (props: ScopedProps<MenuCheckboxItemProps>, forwardedRef) => {\n    const { checked = false, onCheckedChange, ...checkboxItemProps } = props;\n    return (\n      <ItemIndicatorProvider scope={props.__scopeMenu} checked={checked}>\n        <MenuItem\n          role=\"menuitemcheckbox\"\n          aria-checked={isIndeterminate(checked) ? 'mixed' : checked}\n          {...checkboxItemProps}\n          ref={forwardedRef}\n          data-state={getCheckedState(checked)}\n          onSelect={composeEventHandlers(\n            checkboxItemProps.onSelect,\n            () => onCheckedChange?.(isIndeterminate(checked) ? true : !checked),\n            { checkForDefaultPrevented: false }\n          )}\n        />\n      </ItemIndicatorProvider>\n    );\n  }\n);\n\nMenuCheckboxItem.displayName = CHECKBOX_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuRadioGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_GROUP_NAME = 'MenuRadioGroup';\n\nconst [RadioGroupProvider, useRadioGroupContext] = createMenuContext<MenuRadioGroupProps>(\n  RADIO_GROUP_NAME,\n  { value: undefined, onValueChange: () => {} }\n);\n\ntype MenuRadioGroupElement = React.ComponentRef<typeof MenuGroup>;\ninterface MenuRadioGroupProps extends MenuGroupProps {\n  value?: string;\n  onValueChange?: (value: string) => void;\n}\n\nconst MenuRadioGroup = React.forwardRef<MenuRadioGroupElement, MenuRadioGroupProps>(\n  (props: ScopedProps<MenuRadioGroupProps>, forwardedRef) => {\n    const { value, onValueChange, ...groupProps } = props;\n    const handleValueChange = useCallbackRef(onValueChange);\n    return (\n      <RadioGroupProvider scope={props.__scopeMenu} value={value} onValueChange={handleValueChange}>\n        <MenuGroup {...groupProps} ref={forwardedRef} />\n      </RadioGroupProvider>\n    );\n  }\n);\n\nMenuRadioGroup.displayName = RADIO_GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuRadioItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_ITEM_NAME = 'MenuRadioItem';\n\ntype MenuRadioItemElement = React.ComponentRef<typeof MenuItem>;\ninterface MenuRadioItemProps extends MenuItemProps {\n  value: string;\n}\n\nconst MenuRadioItem = React.forwardRef<MenuRadioItemElement, MenuRadioItemProps>(\n  (props: ScopedProps<MenuRadioItemProps>, forwardedRef) => {\n    const { value, ...radioItemProps } = props;\n    const context = useRadioGroupContext(RADIO_ITEM_NAME, props.__scopeMenu);\n    const checked = value === context.value;\n    return (\n      <ItemIndicatorProvider scope={props.__scopeMenu} checked={checked}>\n        <MenuItem\n          role=\"menuitemradio\"\n          aria-checked={checked}\n          {...radioItemProps}\n          ref={forwardedRef}\n          data-state={getCheckedState(checked)}\n          onSelect={composeEventHandlers(\n            radioItemProps.onSelect,\n            () => context.onValueChange?.(value),\n            { checkForDefaultPrevented: false }\n          )}\n        />\n      </ItemIndicatorProvider>\n    );\n  }\n);\n\nMenuRadioItem.displayName = RADIO_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuItemIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_INDICATOR_NAME = 'MenuItemIndicator';\n\ntype CheckboxContextValue = { checked: CheckedState };\n\nconst [ItemIndicatorProvider, useItemIndicatorContext] = createMenuContext<CheckboxContextValue>(\n  ITEM_INDICATOR_NAME,\n  { checked: false }\n);\n\ntype MenuItemIndicatorElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface MenuItemIndicatorProps extends PrimitiveSpanProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst MenuItemIndicator = React.forwardRef<MenuItemIndicatorElement, MenuItemIndicatorProps>(\n  (props: ScopedProps<MenuItemIndicatorProps>, forwardedRef) => {\n    const { __scopeMenu, forceMount, ...itemIndicatorProps } = props;\n    const indicatorContext = useItemIndicatorContext(ITEM_INDICATOR_NAME, __scopeMenu);\n    return (\n      <Presence\n        present={\n          forceMount ||\n          isIndeterminate(indicatorContext.checked) ||\n          indicatorContext.checked === true\n        }\n      >\n        <Primitive.span\n          {...itemIndicatorProps}\n          ref={forwardedRef}\n          data-state={getCheckedState(indicatorContext.checked)}\n        />\n      </Presence>\n    );\n  }\n);\n\nMenuItemIndicator.displayName = ITEM_INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuSeparator\n * -----------------------------------------------------------------------------------------------*/\n\nconst SEPARATOR_NAME = 'MenuSeparator';\n\ntype MenuSeparatorElement = React.ComponentRef<typeof Primitive.div>;\ninterface MenuSeparatorProps extends PrimitiveDivProps {}\n\nconst MenuSeparator = React.forwardRef<MenuSeparatorElement, MenuSeparatorProps>(\n  (props: ScopedProps<MenuSeparatorProps>, forwardedRef) => {\n    const { __scopeMenu, ...separatorProps } = props;\n    return (\n      <Primitive.div\n        role=\"separator\"\n        aria-orientation=\"horizontal\"\n        {...separatorProps}\n        ref={forwardedRef}\n      />\n    );\n  }\n);\n\nMenuSeparator.displayName = SEPARATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'MenuArrow';\n\ntype MenuArrowElement = React.ComponentRef<typeof PopperPrimitive.Arrow>;\ntype PopperArrowProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Arrow>;\ninterface MenuArrowProps extends PopperArrowProps {}\n\nconst MenuArrow = React.forwardRef<MenuArrowElement, MenuArrowProps>(\n  (props: ScopedProps<MenuArrowProps>, forwardedRef) => {\n    const { __scopeMenu, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeMenu);\n    return <PopperPrimitive.Arrow {...popperScope} {...arrowProps} ref={forwardedRef} />;\n  }\n);\n\nMenuArrow.displayName = ARROW_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuSub\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_NAME = 'MenuSub';\n\ntype MenuSubContextValue = {\n  contentId: string;\n  triggerId: string;\n  trigger: MenuSubTriggerElement | null;\n  onTriggerChange(trigger: MenuSubTriggerElement | null): void;\n};\n\nconst [MenuSubProvider, useMenuSubContext] = createMenuContext<MenuSubContextValue>(SUB_NAME);\n\ninterface MenuSubProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  onOpenChange?(open: boolean): void;\n}\n\nconst MenuSub: React.FC<MenuSubProps> = (props: ScopedProps<MenuSubProps>) => {\n  const { __scopeMenu, children, open = false, onOpenChange } = props;\n  const parentMenuContext = useMenuContext(SUB_NAME, __scopeMenu);\n  const popperScope = usePopperScope(__scopeMenu);\n  const [trigger, setTrigger] = React.useState<MenuSubTriggerElement | null>(null);\n  const [content, setContent] = React.useState<MenuContentElement | null>(null);\n  const handleOpenChange = useCallbackRef(onOpenChange);\n\n  // Prevent the parent menu from reopening with open submenus.\n  React.useEffect(() => {\n    if (parentMenuContext.open === false) handleOpenChange(false);\n    return () => handleOpenChange(false);\n  }, [parentMenuContext.open, handleOpenChange]);\n\n  return (\n    <PopperPrimitive.Root {...popperScope}>\n      <MenuProvider\n        scope={__scopeMenu}\n        open={open}\n        onOpenChange={handleOpenChange}\n        content={content}\n        onContentChange={setContent}\n      >\n        <MenuSubProvider\n          scope={__scopeMenu}\n          contentId={useId()}\n          triggerId={useId()}\n          trigger={trigger}\n          onTriggerChange={setTrigger}\n        >\n          {children}\n        </MenuSubProvider>\n      </MenuProvider>\n    </PopperPrimitive.Root>\n  );\n};\n\nMenuSub.displayName = SUB_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuSubTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_TRIGGER_NAME = 'MenuSubTrigger';\n\ntype MenuSubTriggerElement = MenuItemImplElement;\ninterface MenuSubTriggerProps extends MenuItemImplProps {}\n\nconst MenuSubTrigger = React.forwardRef<MenuSubTriggerElement, MenuSubTriggerProps>(\n  (props: ScopedProps<MenuSubTriggerProps>, forwardedRef) => {\n    const context = useMenuContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const subContext = useMenuSubContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const contentContext = useMenuContentContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const openTimerRef = React.useRef<number | null>(null);\n    const { pointerGraceTimerRef, onPointerGraceIntentChange } = contentContext;\n    const scope = { __scopeMenu: props.__scopeMenu };\n\n    const clearOpenTimer = React.useCallback(() => {\n      if (openTimerRef.current) window.clearTimeout(openTimerRef.current);\n      openTimerRef.current = null;\n    }, []);\n\n    React.useEffect(() => clearOpenTimer, [clearOpenTimer]);\n\n    React.useEffect(() => {\n      const pointerGraceTimer = pointerGraceTimerRef.current;\n      return () => {\n        window.clearTimeout(pointerGraceTimer);\n        onPointerGraceIntentChange(null);\n      };\n    }, [pointerGraceTimerRef, onPointerGraceIntentChange]);\n\n    return (\n      <MenuAnchor asChild {...scope}>\n        <MenuItemImpl\n          id={subContext.triggerId}\n          aria-haspopup=\"menu\"\n          aria-expanded={context.open}\n          aria-controls={subContext.contentId}\n          data-state={getOpenState(context.open)}\n          {...props}\n          ref={composeRefs(forwardedRef, subContext.onTriggerChange)}\n          // This is redundant for mouse users but we cannot determine pointer type from\n          // click event and we cannot use pointerup event (see git history for reasons why)\n          onClick={(event) => {\n            props.onClick?.(event);\n            if (props.disabled || event.defaultPrevented) return;\n            /**\n             * We manually focus because iOS Safari doesn't always focus on click (e.g. buttons)\n             * and we rely heavily on `onFocusOutside` for submenus to close when switching\n             * between separate submenus.\n             */\n            event.currentTarget.focus();\n            if (!context.open) context.onOpenChange(true);\n          }}\n          onPointerMove={composeEventHandlers(\n            props.onPointerMove,\n            whenMouse((event) => {\n              contentContext.onItemEnter(event);\n              if (event.defaultPrevented) return;\n              if (!props.disabled && !context.open && !openTimerRef.current) {\n                contentContext.onPointerGraceIntentChange(null);\n                openTimerRef.current = window.setTimeout(() => {\n                  context.onOpenChange(true);\n                  clearOpenTimer();\n                }, 100);\n              }\n            })\n          )}\n          onPointerLeave={composeEventHandlers(\n            props.onPointerLeave,\n            whenMouse((event) => {\n              clearOpenTimer();\n\n              const contentRect = context.content?.getBoundingClientRect();\n              if (contentRect) {\n                // TODO: make sure to update this when we change positioning logic\n                const side = context.content?.dataset.side as Side;\n                const rightSide = side === 'right';\n                const bleed = rightSide ? -5 : +5;\n                const contentNearEdge = contentRect[rightSide ? 'left' : 'right'];\n                const contentFarEdge = contentRect[rightSide ? 'right' : 'left'];\n\n                contentContext.onPointerGraceIntentChange({\n                  area: [\n                    // Apply a bleed on clientX to ensure that our exit point is\n                    // consistently within polygon bounds\n                    { x: event.clientX + bleed, y: event.clientY },\n                    { x: contentNearEdge, y: contentRect.top },\n                    { x: contentFarEdge, y: contentRect.top },\n                    { x: contentFarEdge, y: contentRect.bottom },\n                    { x: contentNearEdge, y: contentRect.bottom },\n                  ],\n                  side,\n                });\n\n                window.clearTimeout(pointerGraceTimerRef.current);\n                pointerGraceTimerRef.current = window.setTimeout(\n                  () => contentContext.onPointerGraceIntentChange(null),\n                  300\n                );\n              } else {\n                contentContext.onTriggerLeave(event);\n                if (event.defaultPrevented) return;\n\n                // There's 100ms where the user may leave an item before the submenu was opened.\n                contentContext.onPointerGraceIntentChange(null);\n              }\n            })\n          )}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            const isTypingAhead = contentContext.searchRef.current !== '';\n            if (props.disabled || (isTypingAhead && event.key === ' ')) return;\n            if (SUB_OPEN_KEYS[rootContext.dir].includes(event.key)) {\n              context.onOpenChange(true);\n              // The trigger may hold focus if opened via pointer interaction\n              // so we ensure content is given focus again when switching to keyboard.\n              context.content?.focus();\n              // prevent window from scrolling\n              event.preventDefault();\n            }\n          })}\n        />\n      </MenuAnchor>\n    );\n  }\n);\n\nMenuSubTrigger.displayName = SUB_TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuSubContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_CONTENT_NAME = 'MenuSubContent';\n\ntype MenuSubContentElement = MenuContentImplElement;\ninterface MenuSubContentProps\n  extends Omit<\n    MenuContentImplProps,\n    keyof MenuContentImplPrivateProps | 'onCloseAutoFocus' | 'onEntryFocus' | 'side' | 'align'\n  > {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst MenuSubContent = React.forwardRef<MenuSubContentElement, MenuSubContentProps>(\n  (props: ScopedProps<MenuSubContentProps>, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeMenu);\n    const { forceMount = portalContext.forceMount, ...subContentProps } = props;\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, props.__scopeMenu);\n    const subContext = useMenuSubContext(SUB_CONTENT_NAME, props.__scopeMenu);\n    const ref = React.useRef<MenuSubContentElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    return (\n      <Collection.Provider scope={props.__scopeMenu}>\n        <Presence present={forceMount || context.open}>\n          <Collection.Slot scope={props.__scopeMenu}>\n            <MenuContentImpl\n              id={subContext.contentId}\n              aria-labelledby={subContext.triggerId}\n              {...subContentProps}\n              ref={composedRefs}\n              align=\"start\"\n              side={rootContext.dir === 'rtl' ? 'left' : 'right'}\n              disableOutsidePointerEvents={false}\n              disableOutsideScroll={false}\n              trapFocus={false}\n              onOpenAutoFocus={(event) => {\n                // when opening a submenu, focus content for keyboard users only\n                if (rootContext.isUsingKeyboardRef.current) ref.current?.focus();\n                event.preventDefault();\n              }}\n              // The menu might close because of focusing another menu item in the parent menu. We\n              // don't want it to refocus the trigger in that case so we handle trigger focus ourselves.\n              onCloseAutoFocus={(event) => event.preventDefault()}\n              onFocusOutside={composeEventHandlers(props.onFocusOutside, (event) => {\n                // We prevent closing when the trigger is focused to avoid triggering a re-open animation\n                // on pointer interaction.\n                if (event.target !== subContext.trigger) context.onOpenChange(false);\n              })}\n              onEscapeKeyDown={composeEventHandlers(props.onEscapeKeyDown, (event) => {\n                rootContext.onClose();\n                // ensure pressing escape in submenu doesn't escape full screen mode\n                event.preventDefault();\n              })}\n              onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n                // Submenu key events bubble through portals. We only care about keys in this menu.\n                const isKeyDownInside = event.currentTarget.contains(event.target as HTMLElement);\n                const isCloseKey = SUB_CLOSE_KEYS[rootContext.dir].includes(event.key);\n                if (isKeyDownInside && isCloseKey) {\n                  context.onOpenChange(false);\n                  // We focus manually because we prevented it in `onCloseAutoFocus`\n                  subContext.trigger?.focus();\n                  // prevent window from scrolling\n                  event.preventDefault();\n                }\n              })}\n            />\n          </Collection.Slot>\n        </Presence>\n      </Collection.Provider>\n    );\n  }\n);\n\nMenuSubContent.displayName = SUB_CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getOpenState(open: boolean) {\n  return open ? 'open' : 'closed';\n}\n\nfunction isIndeterminate(checked?: CheckedState): checked is 'indeterminate' {\n  return checked === 'indeterminate';\n}\n\nfunction getCheckedState(checked: CheckedState) {\n  return isIndeterminate(checked) ? 'indeterminate' : checked ? 'checked' : 'unchecked';\n}\n\nfunction focusFirst(candidates: HTMLElement[]) {\n  const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n  for (const candidate of candidates) {\n    // if focus is already where we want to go, we don't want to keep going through the candidates\n    if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n    candidate.focus();\n    if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n  }\n}\n\n/**\n * Wraps an array around itself at a given start index\n * Example: `wrapArray(['a', 'b', 'c', 'd'], 2) === ['c', 'd', 'a', 'b']`\n */\nfunction wrapArray<T>(array: T[], startIndex: number) {\n  return array.map<T>((_, index) => array[(startIndex + index) % array.length]!);\n}\n\n/**\n * This is the \"meat\" of the typeahead matching logic. It takes in all the values,\n * the search and the current match, and returns the next match (or `undefined`).\n *\n * We normalize the search because if a user has repeatedly pressed a character,\n * we want the exact same behavior as if we only had that one character\n * (ie. cycle through options starting with that character)\n *\n * We also reorder the values by wrapping the array around the current match.\n * This is so we always look forward from the current match, and picking the first\n * match will always be the correct one.\n *\n * Finally, if the normalized search is exactly one character, we exclude the\n * current match from the values because otherwise it would be the first to match always\n * and focus would never move. This is as opposed to the regular case, where we\n * don't want focus to move if the current match still matches.\n */\nfunction getNextMatch(values: string[], search: string, currentMatch?: string) {\n  const isRepeated = search.length > 1 && Array.from(search).every((char) => char === search[0]);\n  const normalizedSearch = isRepeated ? search[0]! : search;\n  const currentMatchIndex = currentMatch ? values.indexOf(currentMatch) : -1;\n  let wrappedValues = wrapArray(values, Math.max(currentMatchIndex, 0));\n  const excludeCurrentMatch = normalizedSearch.length === 1;\n  if (excludeCurrentMatch) wrappedValues = wrappedValues.filter((v) => v !== currentMatch);\n  const nextMatch = wrappedValues.find((value) =>\n    value.toLowerCase().startsWith(normalizedSearch.toLowerCase())\n  );\n  return nextMatch !== currentMatch ? nextMatch : undefined;\n}\n\ntype Point = { x: number; y: number };\ntype Polygon = Point[];\ntype Side = 'left' | 'right';\ntype GraceIntent = { area: Polygon; side: Side };\n\n// Determine if a point is inside of a polygon.\n// Based on https://github.com/substack/point-in-polygon\nfunction isPointInPolygon(point: Point, polygon: Polygon) {\n  const { x, y } = point;\n  let inside = false;\n  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {\n    const ii = polygon[i]!;\n    const jj = polygon[j]!;\n    const xi = ii.x;\n    const yi = ii.y;\n    const xj = jj.x;\n    const yj = jj.y;\n\n    // prettier-ignore\n    const intersect = ((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi);\n    if (intersect) inside = !inside;\n  }\n\n  return inside;\n}\n\nfunction isPointerInGraceArea(event: React.PointerEvent, area?: Polygon) {\n  if (!area) return false;\n  const cursorPos = { x: event.clientX, y: event.clientY };\n  return isPointInPolygon(cursorPos, area);\n}\n\nfunction whenMouse<E>(handler: React.PointerEventHandler<E>): React.PointerEventHandler<E> {\n  return (event) => (event.pointerType === 'mouse' ? handler(event) : undefined);\n}\n\nconst Root = Menu;\nconst Anchor = MenuAnchor;\nconst Portal = MenuPortal;\nconst Content = MenuContent;\nconst Group = MenuGroup;\nconst Label = MenuLabel;\nconst Item = MenuItem;\nconst CheckboxItem = MenuCheckboxItem;\nconst RadioGroup = MenuRadioGroup;\nconst RadioItem = MenuRadioItem;\nconst ItemIndicator = MenuItemIndicator;\nconst Separator = MenuSeparator;\nconst Arrow = MenuArrow;\nconst Sub = MenuSub;\nconst SubTrigger = MenuSubTrigger;\nconst SubContent = MenuSubContent;\n\nexport {\n  createMenuScope,\n  //\n  Menu,\n  MenuAnchor,\n  MenuPortal,\n  MenuContent,\n  MenuGroup,\n  MenuLabel,\n  MenuItem,\n  MenuCheckboxItem,\n  MenuRadioGroup,\n  MenuRadioItem,\n  MenuItemIndicator,\n  MenuSeparator,\n  MenuArrow,\n  MenuSub,\n  MenuSubTrigger,\n  MenuSubContent,\n  //\n  Root,\n  Anchor,\n  Portal,\n  Content,\n  Group,\n  Label,\n  Item,\n  CheckboxItem,\n  RadioGroup,\n  RadioItem,\n  ItemIndicator,\n  Separator,\n  Arrow,\n  Sub,\n  SubTrigger,\n  SubContent,\n};\nexport type {\n  MenuProps,\n  MenuAnchorProps,\n  MenuPortalProps,\n  MenuContentProps,\n  MenuGroupProps,\n  MenuLabelProps,\n  MenuItemProps,\n  MenuCheckboxItemProps,\n  MenuRadioGroupProps,\n  MenuRadioItemProps,\n  MenuItemIndicatorProps,\n  MenuSeparatorProps,\n  MenuArrowProps,\n  MenuSubProps,\n  MenuSubTriggerProps,\n  MenuSubContentProps,\n};\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { composeRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport * as MenuPrimitive from '@radix-ui/react-menu';\nimport { createMenuScope } from '@radix-ui/react-menu';\nimport { useId } from '@radix-ui/react-id';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Direction = 'ltr' | 'rtl';\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenu\n * -----------------------------------------------------------------------------------------------*/\n\nconst DROPDOWN_MENU_NAME = 'DropdownMenu';\n\ntype ScopedProps<P> = P & { __scopeDropdownMenu?: Scope };\nconst [createDropdownMenuContext, createDropdownMenuScope] = createContextScope(\n  DROPDOWN_MENU_NAME,\n  [createMenuScope]\n);\nconst useMenuScope = createMenuScope();\n\ntype DropdownMenuContextValue = {\n  triggerId: string;\n  triggerRef: React.RefObject<HTMLButtonElement | null>;\n  contentId: string;\n  open: boolean;\n  onOpenChange(open: boolean): void;\n  onOpenToggle(): void;\n  modal: boolean;\n};\n\nconst [DropdownMenuProvider, useDropdownMenuContext] =\n  createDropdownMenuContext<DropdownMenuContextValue>(DROPDOWN_MENU_NAME);\n\ninterface DropdownMenuProps {\n  children?: React.ReactNode;\n  dir?: Direction;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n  modal?: boolean;\n}\n\nconst DropdownMenu: React.FC<DropdownMenuProps> = (props: ScopedProps<DropdownMenuProps>) => {\n  const {\n    __scopeDropdownMenu,\n    children,\n    dir,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    modal = true,\n  } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  const triggerRef = React.useRef<HTMLButtonElement>(null);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: DROPDOWN_MENU_NAME,\n  });\n\n  return (\n    <DropdownMenuProvider\n      scope={__scopeDropdownMenu}\n      triggerId={useId()}\n      triggerRef={triggerRef}\n      contentId={useId()}\n      open={open}\n      onOpenChange={setOpen}\n      onOpenToggle={React.useCallback(() => setOpen((prevOpen) => !prevOpen), [setOpen])}\n      modal={modal}\n    >\n      <MenuPrimitive.Root {...menuScope} open={open} onOpenChange={setOpen} dir={dir} modal={modal}>\n        {children}\n      </MenuPrimitive.Root>\n    </DropdownMenuProvider>\n  );\n};\n\nDropdownMenu.displayName = DROPDOWN_MENU_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'DropdownMenuTrigger';\n\ntype DropdownMenuTriggerElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface DropdownMenuTriggerProps extends PrimitiveButtonProps {}\n\nconst DropdownMenuTrigger = React.forwardRef<DropdownMenuTriggerElement, DropdownMenuTriggerProps>(\n  (props: ScopedProps<DropdownMenuTriggerProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, disabled = false, ...triggerProps } = props;\n    const context = useDropdownMenuContext(TRIGGER_NAME, __scopeDropdownMenu);\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return (\n      <MenuPrimitive.Anchor asChild {...menuScope}>\n        <Primitive.button\n          type=\"button\"\n          id={context.triggerId}\n          aria-haspopup=\"menu\"\n          aria-expanded={context.open}\n          aria-controls={context.open ? context.contentId : undefined}\n          data-state={context.open ? 'open' : 'closed'}\n          data-disabled={disabled ? '' : undefined}\n          disabled={disabled}\n          {...triggerProps}\n          ref={composeRefs(forwardedRef, context.triggerRef)}\n          onPointerDown={composeEventHandlers(props.onPointerDown, (event) => {\n            // only call handler if it's the left button (mousedown gets triggered by all mouse buttons)\n            // but not when the control key is pressed (avoiding MacOS right click)\n            if (!disabled && event.button === 0 && event.ctrlKey === false) {\n              context.onOpenToggle();\n              // prevent trigger focusing when opening\n              // this allows the content to be given focus without competition\n              if (!context.open) event.preventDefault();\n            }\n          })}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            if (disabled) return;\n            if (['Enter', ' '].includes(event.key)) context.onOpenToggle();\n            if (event.key === 'ArrowDown') context.onOpenChange(true);\n            // prevent keydown from scrolling window / first focused item to execute\n            // that keydown (inadvertently closing the menu)\n            if (['Enter', ' ', 'ArrowDown'].includes(event.key)) event.preventDefault();\n          })}\n        />\n      </MenuPrimitive.Anchor>\n    );\n  }\n);\n\nDropdownMenuTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'DropdownMenuPortal';\n\ntype MenuPortalProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Portal>;\ninterface DropdownMenuPortalProps extends MenuPortalProps {}\n\nconst DropdownMenuPortal: React.FC<DropdownMenuPortalProps> = (\n  props: ScopedProps<DropdownMenuPortalProps>\n) => {\n  const { __scopeDropdownMenu, ...portalProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.Portal {...menuScope} {...portalProps} />;\n};\n\nDropdownMenuPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'DropdownMenuContent';\n\ntype DropdownMenuContentElement = React.ComponentRef<typeof MenuPrimitive.Content>;\ntype MenuContentProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Content>;\ninterface DropdownMenuContentProps extends Omit<MenuContentProps, 'onEntryFocus'> {}\n\nconst DropdownMenuContent = React.forwardRef<DropdownMenuContentElement, DropdownMenuContentProps>(\n  (props: ScopedProps<DropdownMenuContentProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...contentProps } = props;\n    const context = useDropdownMenuContext(CONTENT_NAME, __scopeDropdownMenu);\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    const hasInteractedOutsideRef = React.useRef(false);\n\n    return (\n      <MenuPrimitive.Content\n        id={context.contentId}\n        aria-labelledby={context.triggerId}\n        {...menuScope}\n        {...contentProps}\n        ref={forwardedRef}\n        onCloseAutoFocus={composeEventHandlers(props.onCloseAutoFocus, (event) => {\n          if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n          hasInteractedOutsideRef.current = false;\n          // Always prevent auto focus because we either focus manually or want user agent focus\n          event.preventDefault();\n        })}\n        onInteractOutside={composeEventHandlers(props.onInteractOutside, (event) => {\n          const originalEvent = event.detail.originalEvent as PointerEvent;\n          const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n          const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n          if (!context.modal || isRightClick) hasInteractedOutsideRef.current = true;\n        })}\n        style={{\n          ...props.style,\n          // re-namespace exposed content custom properties\n          ...{\n            '--radix-dropdown-menu-content-transform-origin':\n              'var(--radix-popper-transform-origin)',\n            '--radix-dropdown-menu-content-available-width': 'var(--radix-popper-available-width)',\n            '--radix-dropdown-menu-content-available-height':\n              'var(--radix-popper-available-height)',\n            '--radix-dropdown-menu-trigger-width': 'var(--radix-popper-anchor-width)',\n            '--radix-dropdown-menu-trigger-height': 'var(--radix-popper-anchor-height)',\n          },\n        }}\n      />\n    );\n  }\n);\n\nDropdownMenuContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'DropdownMenuGroup';\n\ntype DropdownMenuGroupElement = React.ComponentRef<typeof MenuPrimitive.Group>;\ntype MenuGroupProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Group>;\ninterface DropdownMenuGroupProps extends MenuGroupProps {}\n\nconst DropdownMenuGroup = React.forwardRef<DropdownMenuGroupElement, DropdownMenuGroupProps>(\n  (props: ScopedProps<DropdownMenuGroupProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...groupProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return <MenuPrimitive.Group {...menuScope} {...groupProps} ref={forwardedRef} />;\n  }\n);\n\nDropdownMenuGroup.displayName = GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuLabel\n * -----------------------------------------------------------------------------------------------*/\n\nconst LABEL_NAME = 'DropdownMenuLabel';\n\ntype DropdownMenuLabelElement = React.ComponentRef<typeof MenuPrimitive.Label>;\ntype MenuLabelProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Label>;\ninterface DropdownMenuLabelProps extends MenuLabelProps {}\n\nconst DropdownMenuLabel = React.forwardRef<DropdownMenuLabelElement, DropdownMenuLabelProps>(\n  (props: ScopedProps<DropdownMenuLabelProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...labelProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return <MenuPrimitive.Label {...menuScope} {...labelProps} ref={forwardedRef} />;\n  }\n);\n\nDropdownMenuLabel.displayName = LABEL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'DropdownMenuItem';\n\ntype DropdownMenuItemElement = React.ComponentRef<typeof MenuPrimitive.Item>;\ntype MenuItemProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Item>;\ninterface DropdownMenuItemProps extends MenuItemProps {}\n\nconst DropdownMenuItem = React.forwardRef<DropdownMenuItemElement, DropdownMenuItemProps>(\n  (props: ScopedProps<DropdownMenuItemProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...itemProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return <MenuPrimitive.Item {...menuScope} {...itemProps} ref={forwardedRef} />;\n  }\n);\n\nDropdownMenuItem.displayName = ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuCheckboxItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst CHECKBOX_ITEM_NAME = 'DropdownMenuCheckboxItem';\n\ntype DropdownMenuCheckboxItemElement = React.ComponentRef<typeof MenuPrimitive.CheckboxItem>;\ntype MenuCheckboxItemProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.CheckboxItem>;\ninterface DropdownMenuCheckboxItemProps extends MenuCheckboxItemProps {}\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  DropdownMenuCheckboxItemElement,\n  DropdownMenuCheckboxItemProps\n>((props: ScopedProps<DropdownMenuCheckboxItemProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...checkboxItemProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.CheckboxItem {...menuScope} {...checkboxItemProps} ref={forwardedRef} />;\n});\n\nDropdownMenuCheckboxItem.displayName = CHECKBOX_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuRadioGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_GROUP_NAME = 'DropdownMenuRadioGroup';\n\ntype DropdownMenuRadioGroupElement = React.ComponentRef<typeof MenuPrimitive.RadioGroup>;\ntype MenuRadioGroupProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.RadioGroup>;\ninterface DropdownMenuRadioGroupProps extends MenuRadioGroupProps {}\n\nconst DropdownMenuRadioGroup = React.forwardRef<\n  DropdownMenuRadioGroupElement,\n  DropdownMenuRadioGroupProps\n>((props: ScopedProps<DropdownMenuRadioGroupProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...radioGroupProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.RadioGroup {...menuScope} {...radioGroupProps} ref={forwardedRef} />;\n});\n\nDropdownMenuRadioGroup.displayName = RADIO_GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuRadioItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_ITEM_NAME = 'DropdownMenuRadioItem';\n\ntype DropdownMenuRadioItemElement = React.ComponentRef<typeof MenuPrimitive.RadioItem>;\ntype MenuRadioItemProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.RadioItem>;\ninterface DropdownMenuRadioItemProps extends MenuRadioItemProps {}\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  DropdownMenuRadioItemElement,\n  DropdownMenuRadioItemProps\n>((props: ScopedProps<DropdownMenuRadioItemProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...radioItemProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.RadioItem {...menuScope} {...radioItemProps} ref={forwardedRef} />;\n});\n\nDropdownMenuRadioItem.displayName = RADIO_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuItemIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'DropdownMenuItemIndicator';\n\ntype DropdownMenuItemIndicatorElement = React.ComponentRef<typeof MenuPrimitive.ItemIndicator>;\ntype MenuItemIndicatorProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.ItemIndicator>;\ninterface DropdownMenuItemIndicatorProps extends MenuItemIndicatorProps {}\n\nconst DropdownMenuItemIndicator = React.forwardRef<\n  DropdownMenuItemIndicatorElement,\n  DropdownMenuItemIndicatorProps\n>((props: ScopedProps<DropdownMenuItemIndicatorProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...itemIndicatorProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.ItemIndicator {...menuScope} {...itemIndicatorProps} ref={forwardedRef} />;\n});\n\nDropdownMenuItemIndicator.displayName = INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuSeparator\n * -----------------------------------------------------------------------------------------------*/\n\nconst SEPARATOR_NAME = 'DropdownMenuSeparator';\n\ntype DropdownMenuSeparatorElement = React.ComponentRef<typeof MenuPrimitive.Separator>;\ntype MenuSeparatorProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Separator>;\ninterface DropdownMenuSeparatorProps extends MenuSeparatorProps {}\n\nconst DropdownMenuSeparator = React.forwardRef<\n  DropdownMenuSeparatorElement,\n  DropdownMenuSeparatorProps\n>((props: ScopedProps<DropdownMenuSeparatorProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...separatorProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.Separator {...menuScope} {...separatorProps} ref={forwardedRef} />;\n});\n\nDropdownMenuSeparator.displayName = SEPARATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'DropdownMenuArrow';\n\ntype DropdownMenuArrowElement = React.ComponentRef<typeof MenuPrimitive.Arrow>;\ntype MenuArrowProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Arrow>;\ninterface DropdownMenuArrowProps extends MenuArrowProps {}\n\nconst DropdownMenuArrow = React.forwardRef<DropdownMenuArrowElement, DropdownMenuArrowProps>(\n  (props: ScopedProps<DropdownMenuArrowProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...arrowProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return <MenuPrimitive.Arrow {...menuScope} {...arrowProps} ref={forwardedRef} />;\n  }\n);\n\nDropdownMenuArrow.displayName = ARROW_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuSub\n * -----------------------------------------------------------------------------------------------*/\n\ninterface DropdownMenuSubProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n}\n\nconst DropdownMenuSub: React.FC<DropdownMenuSubProps> = (\n  props: ScopedProps<DropdownMenuSubProps>\n) => {\n  const { __scopeDropdownMenu, children, open: openProp, onOpenChange, defaultOpen } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: 'DropdownMenuSub',\n  });\n\n  return (\n    <MenuPrimitive.Sub {...menuScope} open={open} onOpenChange={setOpen}>\n      {children}\n    </MenuPrimitive.Sub>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuSubTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_TRIGGER_NAME = 'DropdownMenuSubTrigger';\n\ntype DropdownMenuSubTriggerElement = React.ComponentRef<typeof MenuPrimitive.SubTrigger>;\ntype MenuSubTriggerProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.SubTrigger>;\ninterface DropdownMenuSubTriggerProps extends MenuSubTriggerProps {}\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  DropdownMenuSubTriggerElement,\n  DropdownMenuSubTriggerProps\n>((props: ScopedProps<DropdownMenuSubTriggerProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...subTriggerProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.SubTrigger {...menuScope} {...subTriggerProps} ref={forwardedRef} />;\n});\n\nDropdownMenuSubTrigger.displayName = SUB_TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuSubContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_CONTENT_NAME = 'DropdownMenuSubContent';\n\ntype DropdownMenuSubContentElement = React.ComponentRef<typeof MenuPrimitive.Content>;\ntype MenuSubContentProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.SubContent>;\ninterface DropdownMenuSubContentProps extends MenuSubContentProps {}\n\nconst DropdownMenuSubContent = React.forwardRef<\n  DropdownMenuSubContentElement,\n  DropdownMenuSubContentProps\n>((props: ScopedProps<DropdownMenuSubContentProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...subContentProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n\n  return (\n    <MenuPrimitive.SubContent\n      {...menuScope}\n      {...subContentProps}\n      ref={forwardedRef}\n      style={{\n        ...props.style,\n        // re-namespace exposed content custom properties\n        ...{\n          '--radix-dropdown-menu-content-transform-origin': 'var(--radix-popper-transform-origin)',\n          '--radix-dropdown-menu-content-available-width': 'var(--radix-popper-available-width)',\n          '--radix-dropdown-menu-content-available-height': 'var(--radix-popper-available-height)',\n          '--radix-dropdown-menu-trigger-width': 'var(--radix-popper-anchor-width)',\n          '--radix-dropdown-menu-trigger-height': 'var(--radix-popper-anchor-height)',\n        },\n      }}\n    />\n  );\n});\n\nDropdownMenuSubContent.displayName = SUB_CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = DropdownMenu;\nconst Trigger = DropdownMenuTrigger;\nconst Portal = DropdownMenuPortal;\nconst Content = DropdownMenuContent;\nconst Group = DropdownMenuGroup;\nconst Label = DropdownMenuLabel;\nconst Item = DropdownMenuItem;\nconst CheckboxItem = DropdownMenuCheckboxItem;\nconst RadioGroup = DropdownMenuRadioGroup;\nconst RadioItem = DropdownMenuRadioItem;\nconst ItemIndicator = DropdownMenuItemIndicator;\nconst Separator = DropdownMenuSeparator;\nconst Arrow = DropdownMenuArrow;\nconst Sub = DropdownMenuSub;\nconst SubTrigger = DropdownMenuSubTrigger;\nconst SubContent = DropdownMenuSubContent;\n\nexport {\n  createDropdownMenuScope,\n  //\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuPortal,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuItemIndicator,\n  DropdownMenuSeparator,\n  DropdownMenuArrow,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n  //\n  Root,\n  Trigger,\n  Portal,\n  Content,\n  Group,\n  Label,\n  Item,\n  CheckboxItem,\n  RadioGroup,\n  RadioItem,\n  ItemIndicator,\n  Separator,\n  Arrow,\n  Sub,\n  SubTrigger,\n  SubContent,\n};\nexport type {\n  DropdownMenuProps,\n  DropdownMenuTriggerProps,\n  DropdownMenuPortalProps,\n  DropdownMenuContentProps,\n  DropdownMenuGroupProps,\n  DropdownMenuLabelProps,\n  DropdownMenuItemProps,\n  DropdownMenuCheckboxItemProps,\n  DropdownMenuRadioGroupProps,\n  DropdownMenuRadioItemProps,\n  DropdownMenuItemIndicatorProps,\n  DropdownMenuSeparatorProps,\n  DropdownMenuArrowProps,\n  DropdownMenuSubProps,\n  DropdownMenuSubTriggerProps,\n  DropdownMenuSubContentProps,\n};\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Circle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Circle = createLucideIcon('Circle', [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n]);\n\nexport default Circle;\n", "import * as React from 'react';\n\ntype Direction = 'ltr' | 'rtl';\nconst DirectionContext = React.createContext<Direction | undefined>(undefined);\n\n/* -------------------------------------------------------------------------------------------------\n * Direction\n * -----------------------------------------------------------------------------------------------*/\n\ninterface DirectionProviderProps {\n  children?: React.ReactNode;\n  dir: Direction;\n}\nconst DirectionProvider: React.FC<DirectionProviderProps> = (props) => {\n  const { dir, children } = props;\n  return <DirectionContext.Provider value={dir}>{children}</DirectionContext.Provider>;\n};\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction useDirection(localDir?: Direction) {\n  const globalDir = React.useContext(DirectionContext);\n  return localDir || globalDir || 'ltr';\n}\n\nconst Provider = DirectionProvider;\n\nexport {\n  useDirection,\n  //\n  Provider,\n  //\n  DirectionProvider,\n};\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useId } from '@radix-ui/react-id';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useDirection } from '@radix-ui/react-direction';\n\nimport type { Scope } from '@radix-ui/react-context';\n\nconst ENTRY_FOCUS = 'rovingFocusGroup.onEntryFocus';\nconst EVENT_OPTIONS = { bubbles: false, cancelable: true };\n\n/* -------------------------------------------------------------------------------------------------\n * RovingFocusGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'RovingFocusGroup';\n\ntype ItemData = { id: string; focusable: boolean; active: boolean };\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  HTMLSpanElement,\n  ItemData\n>(GROUP_NAME);\n\ntype ScopedProps<P> = P & { __scopeRovingFocusGroup?: Scope };\nconst [createRovingFocusGroupContext, createRovingFocusGroupScope] = createContextScope(\n  GROUP_NAME,\n  [createCollectionScope]\n);\n\ntype Orientation = React.AriaAttributes['aria-orientation'];\ntype Direction = 'ltr' | 'rtl';\n\ninterface RovingFocusGroupOptions {\n  /**\n   * The orientation of the group.\n   * Mainly so arrow navigation is done accordingly (left & right vs. up & down)\n   */\n  orientation?: Orientation;\n  /**\n   * The direction of navigation between items.\n   */\n  dir?: Direction;\n  /**\n   * Whether keyboard navigation should loop around\n   * @defaultValue false\n   */\n  loop?: boolean;\n}\n\ntype RovingContextValue = RovingFocusGroupOptions & {\n  currentTabStopId: string | null;\n  onItemFocus(tabStopId: string): void;\n  onItemShiftTab(): void;\n  onFocusableItemAdd(): void;\n  onFocusableItemRemove(): void;\n};\n\nconst [RovingFocusProvider, useRovingFocusContext] =\n  createRovingFocusGroupContext<RovingContextValue>(GROUP_NAME);\n\ntype RovingFocusGroupElement = RovingFocusGroupImplElement;\ninterface RovingFocusGroupProps extends RovingFocusGroupImplProps {}\n\nconst RovingFocusGroup = React.forwardRef<RovingFocusGroupElement, RovingFocusGroupProps>(\n  (props: ScopedProps<RovingFocusGroupProps>, forwardedRef) => {\n    return (\n      <Collection.Provider scope={props.__scopeRovingFocusGroup}>\n        <Collection.Slot scope={props.__scopeRovingFocusGroup}>\n          <RovingFocusGroupImpl {...props} ref={forwardedRef} />\n        </Collection.Slot>\n      </Collection.Provider>\n    );\n  }\n);\n\nRovingFocusGroup.displayName = GROUP_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype RovingFocusGroupImplElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface RovingFocusGroupImplProps\n  extends Omit<PrimitiveDivProps, 'dir'>,\n    RovingFocusGroupOptions {\n  currentTabStopId?: string | null;\n  defaultCurrentTabStopId?: string;\n  onCurrentTabStopIdChange?: (tabStopId: string | null) => void;\n  onEntryFocus?: (event: Event) => void;\n  preventScrollOnEntryFocus?: boolean;\n}\n\nconst RovingFocusGroupImpl = React.forwardRef<\n  RovingFocusGroupImplElement,\n  RovingFocusGroupImplProps\n>((props: ScopedProps<RovingFocusGroupImplProps>, forwardedRef) => {\n  const {\n    __scopeRovingFocusGroup,\n    orientation,\n    loop = false,\n    dir,\n    currentTabStopId: currentTabStopIdProp,\n    defaultCurrentTabStopId,\n    onCurrentTabStopIdChange,\n    onEntryFocus,\n    preventScrollOnEntryFocus = false,\n    ...groupProps\n  } = props;\n  const ref = React.useRef<RovingFocusGroupImplElement>(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  const direction = useDirection(dir);\n  const [currentTabStopId, setCurrentTabStopId] = useControllableState({\n    prop: currentTabStopIdProp,\n    defaultProp: defaultCurrentTabStopId ?? null,\n    onChange: onCurrentTabStopIdChange,\n    caller: GROUP_NAME,\n  });\n  const [isTabbingBackOut, setIsTabbingBackOut] = React.useState(false);\n  const handleEntryFocus = useCallbackRef(onEntryFocus);\n  const getItems = useCollection(__scopeRovingFocusGroup);\n  const isClickFocusRef = React.useRef(false);\n  const [focusableItemsCount, setFocusableItemsCount] = React.useState(0);\n\n  React.useEffect(() => {\n    const node = ref.current;\n    if (node) {\n      node.addEventListener(ENTRY_FOCUS, handleEntryFocus);\n      return () => node.removeEventListener(ENTRY_FOCUS, handleEntryFocus);\n    }\n  }, [handleEntryFocus]);\n\n  return (\n    <RovingFocusProvider\n      scope={__scopeRovingFocusGroup}\n      orientation={orientation}\n      dir={direction}\n      loop={loop}\n      currentTabStopId={currentTabStopId}\n      onItemFocus={React.useCallback(\n        (tabStopId) => setCurrentTabStopId(tabStopId),\n        [setCurrentTabStopId]\n      )}\n      onItemShiftTab={React.useCallback(() => setIsTabbingBackOut(true), [])}\n      onFocusableItemAdd={React.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount + 1),\n        []\n      )}\n      onFocusableItemRemove={React.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount - 1),\n        []\n      )}\n    >\n      <Primitive.div\n        tabIndex={isTabbingBackOut || focusableItemsCount === 0 ? -1 : 0}\n        data-orientation={orientation}\n        {...groupProps}\n        ref={composedRefs}\n        style={{ outline: 'none', ...props.style }}\n        onMouseDown={composeEventHandlers(props.onMouseDown, () => {\n          isClickFocusRef.current = true;\n        })}\n        onFocus={composeEventHandlers(props.onFocus, (event) => {\n          // We normally wouldn't need this check, because we already check\n          // that the focus is on the current target and not bubbling to it.\n          // We do this because Safari doesn't focus buttons when clicked, and\n          // instead, the wrapper will get focused and not through a bubbling event.\n          const isKeyboardFocus = !isClickFocusRef.current;\n\n          if (event.target === event.currentTarget && isKeyboardFocus && !isTabbingBackOut) {\n            const entryFocusEvent = new CustomEvent(ENTRY_FOCUS, EVENT_OPTIONS);\n            event.currentTarget.dispatchEvent(entryFocusEvent);\n\n            if (!entryFocusEvent.defaultPrevented) {\n              const items = getItems().filter((item) => item.focusable);\n              const activeItem = items.find((item) => item.active);\n              const currentItem = items.find((item) => item.id === currentTabStopId);\n              const candidateItems = [activeItem, currentItem, ...items].filter(\n                Boolean\n              ) as typeof items;\n              const candidateNodes = candidateItems.map((item) => item.ref.current!);\n              focusFirst(candidateNodes, preventScrollOnEntryFocus);\n            }\n          }\n\n          isClickFocusRef.current = false;\n        })}\n        onBlur={composeEventHandlers(props.onBlur, () => setIsTabbingBackOut(false))}\n      />\n    </RovingFocusProvider>\n  );\n});\n\n/* -------------------------------------------------------------------------------------------------\n * RovingFocusGroupItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'RovingFocusGroupItem';\n\ntype RovingFocusItemElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface RovingFocusItemProps extends Omit<PrimitiveSpanProps, 'children'> {\n  tabStopId?: string;\n  focusable?: boolean;\n  active?: boolean;\n  children?:\n    | React.ReactNode\n    | ((props: { hasTabStop: boolean; isCurrentTabStop: boolean }) => React.ReactNode);\n}\n\nconst RovingFocusGroupItem = React.forwardRef<RovingFocusItemElement, RovingFocusItemProps>(\n  (props: ScopedProps<RovingFocusItemProps>, forwardedRef) => {\n    const {\n      __scopeRovingFocusGroup,\n      focusable = true,\n      active = false,\n      tabStopId,\n      children,\n      ...itemProps\n    } = props;\n    const autoId = useId();\n    const id = tabStopId || autoId;\n    const context = useRovingFocusContext(ITEM_NAME, __scopeRovingFocusGroup);\n    const isCurrentTabStop = context.currentTabStopId === id;\n    const getItems = useCollection(__scopeRovingFocusGroup);\n\n    const { onFocusableItemAdd, onFocusableItemRemove, currentTabStopId } = context;\n\n    React.useEffect(() => {\n      if (focusable) {\n        onFocusableItemAdd();\n        return () => onFocusableItemRemove();\n      }\n    }, [focusable, onFocusableItemAdd, onFocusableItemRemove]);\n\n    return (\n      <Collection.ItemSlot\n        scope={__scopeRovingFocusGroup}\n        id={id}\n        focusable={focusable}\n        active={active}\n      >\n        <Primitive.span\n          tabIndex={isCurrentTabStop ? 0 : -1}\n          data-orientation={context.orientation}\n          {...itemProps}\n          ref={forwardedRef}\n          onMouseDown={composeEventHandlers(props.onMouseDown, (event) => {\n            // We prevent focusing non-focusable items on `mousedown`.\n            // Even though the item has tabIndex={-1}, that only means take it out of the tab order.\n            if (!focusable) event.preventDefault();\n            // Safari doesn't focus a button when clicked so we run our logic on mousedown also\n            else context.onItemFocus(id);\n          })}\n          onFocus={composeEventHandlers(props.onFocus, () => context.onItemFocus(id))}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            if (event.key === 'Tab' && event.shiftKey) {\n              context.onItemShiftTab();\n              return;\n            }\n\n            if (event.target !== event.currentTarget) return;\n\n            const focusIntent = getFocusIntent(event, context.orientation, context.dir);\n\n            if (focusIntent !== undefined) {\n              if (event.metaKey || event.ctrlKey || event.altKey || event.shiftKey) return;\n              event.preventDefault();\n              const items = getItems().filter((item) => item.focusable);\n              let candidateNodes = items.map((item) => item.ref.current!);\n\n              if (focusIntent === 'last') candidateNodes.reverse();\n              else if (focusIntent === 'prev' || focusIntent === 'next') {\n                if (focusIntent === 'prev') candidateNodes.reverse();\n                const currentIndex = candidateNodes.indexOf(event.currentTarget);\n                candidateNodes = context.loop\n                  ? wrapArray(candidateNodes, currentIndex + 1)\n                  : candidateNodes.slice(currentIndex + 1);\n              }\n\n              /**\n               * Imperative focus during keydown is risky so we prevent React's batching updates\n               * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n               */\n              setTimeout(() => focusFirst(candidateNodes));\n            }\n          })}\n        >\n          {typeof children === 'function'\n            ? children({ isCurrentTabStop, hasTabStop: currentTabStopId != null })\n            : children}\n        </Primitive.span>\n      </Collection.ItemSlot>\n    );\n  }\n);\n\nRovingFocusGroupItem.displayName = ITEM_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\n// prettier-ignore\nconst MAP_KEY_TO_FOCUS_INTENT: Record<string, FocusIntent> = {\n  ArrowLeft: 'prev', ArrowUp: 'prev',\n  ArrowRight: 'next', ArrowDown: 'next',\n  PageUp: 'first', Home: 'first',\n  PageDown: 'last', End: 'last',\n};\n\nfunction getDirectionAwareKey(key: string, dir?: Direction) {\n  if (dir !== 'rtl') return key;\n  return key === 'ArrowLeft' ? 'ArrowRight' : key === 'ArrowRight' ? 'ArrowLeft' : key;\n}\n\ntype FocusIntent = 'first' | 'last' | 'prev' | 'next';\n\nfunction getFocusIntent(event: React.KeyboardEvent, orientation?: Orientation, dir?: Direction) {\n  const key = getDirectionAwareKey(event.key, dir);\n  if (orientation === 'vertical' && ['ArrowLeft', 'ArrowRight'].includes(key)) return undefined;\n  if (orientation === 'horizontal' && ['ArrowUp', 'ArrowDown'].includes(key)) return undefined;\n  return MAP_KEY_TO_FOCUS_INTENT[key];\n}\n\nfunction focusFirst(candidates: HTMLElement[], preventScroll = false) {\n  const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n  for (const candidate of candidates) {\n    // if focus is already where we want to go, we don't want to keep going through the candidates\n    if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n    candidate.focus({ preventScroll });\n    if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n  }\n}\n\n/**\n * Wraps an array around itself at a given start index\n * Example: `wrapArray(['a', 'b', 'c', 'd'], 2) === ['c', 'd', 'a', 'b']`\n */\nfunction wrapArray<T>(array: T[], startIndex: number) {\n  return array.map<T>((_, index) => array[(startIndex + index) % array.length]!);\n}\n\nconst Root = RovingFocusGroup;\nconst Item = RovingFocusGroupItem;\n\nexport {\n  createRovingFocusGroupScope,\n  //\n  RovingFocusGroup,\n  RovingFocusGroupItem,\n  //\n  Root,\n  Item,\n};\nexport type { RovingFocusGroupProps, RovingFocusItemProps };\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ChevronRight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtOSAxOCA2LTYtNi02IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/chevron-right\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronRight = createLucideIcon('ChevronRight', [\n  ['path', { d: 'm9 18 6-6-6-6', key: 'mthhwq' }],\n]);\n\nexport default ChevronRight;\n"], "names": ["Arrow", "React", "props", "forwardedRef", "children", "width", "height", "arrowProps", "jsx", "Primitive", "svg", "ref", "viewBox", "preserveAspectRatio", "<PERSON><PERSON><PERSON><PERSON>", "points", "displayName", "Root", "POPPER_NAME", "createPopperContext", "createPopperScope", "createContextScope", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "usePopperContext", "<PERSON><PERSON>", "__scope<PERSON>opper", "anchor", "setAnchor", "scope", "onAnchorChange", "ANCHOR_NAME", "PopperA<PERSON><PERSON>", "virtualRef", "anchorProps", "context", "composedRefs", "useComposedRefs", "current", "div", "CONTENT_NAME", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useContentContext", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "side", "sideOffset", "align", "alignOffset", "arrowPadding", "avoidCollisions", "collisionBoundary", "collisionPadding", "collisionPaddingProp", "sticky", "hideWhenDetached", "updatePositionStrategy", "onPlaced", "contentProps", "content", "<PERSON><PERSON><PERSON><PERSON>", "node", "arrow", "setArrow", "arrowSize", "element", "size", "setSize", "useLayoutEffect", "offsetWidth", "offsetHeight", "resizeObserver", "ResizeObserver", "entries", "Array", "isArray", "length", "entry", "borderSizeEntry", "borderSize", "observe", "box", "unobserve", "useSize", "arrow<PERSON>idth", "arrowHeight", "desiredPlacement", "top", "right", "bottom", "left", "boundary", "hasExplicitBoundaries", "detectOverflowOptions", "padding", "filter", "isNotNull", "altBoundary", "refs", "floatingStyles", "placement", "isPositioned", "middlewareData", "useFloating", "strategy", "whileElementsMounted", "_len", "arguments", "args", "_key", "autoUpdate", "animationFrame", "elements", "reference", "middleware", "offset", "mainAxis", "alignmentAxis", "shift", "crossAxis", "limiter", "limitShift", "flip", "apply", "_ref", "rects", "availableWidth", "availableHeight", "anchorWidth", "anchorHeight", "contentStyle", "floating", "style", "setProperty", "floatingUIarrow", "transform<PERSON><PERSON>in", "hide", "placedSide", "placedAlign", "getSideAndAlignFromPlacement", "handlePlaced", "useCallbackRef", "arrowX", "x", "arrowY", "y", "cannotCenterArrow", "centerOffset", "contentZIndex", "setContentZIndex", "window", "getComputedStyle", "zIndex", "setFloating", "transform", "min<PERSON><PERSON><PERSON>", "join", "referenceHidden", "visibility", "pointerEvents", "dir", "onArrowChange", "shouldHideArrow", "animation", "ARROW_NAME", "OPPOSITE_SIDE", "PopperArrow", "contentContext", "baseSide", "position", "ArrowPrimitive", "display", "value", "options", "name", "fn", "data", "isArrowHidden", "noArrowAlign", "start", "center", "end", "arrowXCenter", "arrowYCenter", "split", "Root2", "<PERSON><PERSON>", "Content", "SELECTION_KEYS", "LAST_KEYS", "FIRST_LAST_KEYS", "SUB_OPEN_KEYS", "ltr", "rtl", "SUB_CLOSE_KEYS", "MENU_NAME", "Collection", "useCollection", "createCollectionScope", "createCollection", "createMenuContext", "createMenuScope", "createRovingFocusGroupScope", "usePopperScope", "useRovingFocusGroupScope", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useMenuContext", "MenuRootProvider", "useMenuRootContext", "<PERSON><PERSON>", "__scopeMenu", "open", "onOpenChange", "modal", "popperScope", "isUsingKeyboardRef", "handleOpenChange", "direction", "useDirection", "handleKeyDown", "document", "addEventListener", "handlePointer", "capture", "once", "removeEventListener", "PopperPrimitive", "onContentChange", "onClose", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PORTAL_NAME", "PortalProvider", "usePortalContext", "forceMount", "<PERSON>uPort<PERSON>", "container", "Presence", "present", "PortalPrimitive", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useMenuContentContext", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "portalContext", "rootContext", "Provider", "Slot", "MenuRootContentModal", "MenuRootContentNonModal", "hideOthers", "MenuContentImpl", "trapFocus", "disableOutsidePointerEvents", "disableOutsideScroll", "onFocusOutside", "composeEventHandlers", "event", "preventDefault", "checkForDefaultPrevented", "on<PERSON><PERSON><PERSON>", "createSlot", "loop", "onOpenAutoFocus", "onCloseAutoFocus", "onEntryFocus", "onEscapeKeyDown", "onPointerDownOutside", "onInteractOutside", "rovingFocusGroupScope", "getItems", "currentItemId", "setCurrentItemId", "contentRef", "timerRef", "searchRef", "pointerGraceTimerRef", "pointerGraceIntentRef", "pointerDirRef", "lastPointerXRef", "ScrollLockWrapper", "RemoveScroll", "scrollLockWrapperProps", "as", "allowPinchZoom", "handleTypeaheadSearch", "key", "search", "items", "item", "disabled", "currentItem", "activeElement", "currentMatch", "find", "textValue", "nextMatch", "values", "isRepeated", "from", "every", "char", "normalizedSearch", "currentMatchIndex", "indexOf", "wrappedValues", "array", "startIndex", "Math", "max", "map", "_", "index", "v", "toLowerCase", "startsWith", "getNextMatch", "newItem", "updateSearch", "clearTimeout", "setTimeout", "focus", "useFocusGuards", "isPointerMovingToSubmenu", "area", "cursorPos", "clientX", "clientY", "point", "polygon", "inside", "i", "j", "ii", "jj", "xi", "yi", "xj", "yj", "isPointInPolygon", "isPointerInGraceArea", "onItemEnter", "onItemLeave", "onTriggerLeave", "onPointerGraceIntentChange", "intent", "FocusScope", "trapped", "onMountAutoFocus", "preventScroll", "onUnmountAutoFocus", "Dismissa<PERSON><PERSON><PERSON><PERSON>", "RovingFocusGroup", "orientation", "currentTabStopId", "onCurrentTabStopIdChange", "preventScrollOnEntryFocus", "role", "getOpenState", "outline", "onKeyDown", "isKeyDownInside", "target", "closest", "currentTarget", "isModifierKey", "ctrl<PERSON>ey", "altKey", "metaKey", "isCharacterKey", "includes", "candidateNodes", "reverse", "candidates", "PREVIOUSLY_FOCUSED_ELEMENT", "candidate", "focusFirst", "onBlur", "contains", "onPointerMove", "whenMouse", "pointer<PERSON><PERSON>as<PERSON><PERSON><PERSON>", "newDir", "MenuGroup", "groupProps", "<PERSON>u<PERSON><PERSON><PERSON>", "labelProps", "ITEM_NAME", "ITEM_SELECT", "MenuItem", "onSelect", "itemProps", "isPointerDownRef", "MenuItemImpl", "onClick", "handleSelect", "menuItem", "itemSelectEvent", "CustomEvent", "bubbles", "cancelable", "dispatchDiscreteCustomEvent", "defaultPrevented", "onPointerDown", "onPointerUp", "click", "isTypingAhead", "isFocused", "setIsFocused", "textContent", "setTextContent", "trim", "ItemSlot", "focusable", "onPointerLeave", "onFocus", "MenuCheckboxItem", "checked", "onCheckedChange", "checkboxItemProps", "ItemIndicatorProvider", "isIndeterminate", "getCheckedState", "RADIO_GROUP_NAME", "RadioGroupProvider", "useRadioGroupContext", "onValueChange", "MenuRadioGroup", "handleValueChange", "RADIO_ITEM_NAME", "MenuRadioItem", "radioItemProps", "ITEM_INDICATOR_NAME", "useItemIndicatorContext", "MenuItemIndicator", "itemIndicatorProps", "indicatorContext", "span", "MenuSeparator", "separatorProps", "MenuArrow", "SUB_NAME", "MenuSub<PERSON><PERSON><PERSON>", "useMenuSubContext", "MenuSub", "parentMenuContext", "trigger", "setTrigger", "contentId", "useId", "triggerId", "onTriggerChange", "SUB_TRIGGER_NAME", "MenuSubTrigger", "subContext", "openTimerRef", "clearOpenTimer", "pointerGraceTimer", "id", "composeRefs", "contentRect", "getBoundingClientRect", "dataset", "rightSide", "bleed", "contentNearEdge", "contentFarEdge", "SUB_CONTENT_NAME", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subContentProps", "isClose<PERSON>ey", "handler", "pointerType", "Root3", "Anchor2", "Portal", "Content2", "Group", "Label", "Item2", "CheckboxItem", "RadioGroup", "RadioItem", "ItemIndicator", "Separator", "Arrow2", "Sub", "SubTrigger", "SubContent", "DROPDOWN_MENU_NAME", "createDropdownMenuContext", "createDropdownMenuScope", "useMenuScope", "DropdownMenuProvider", "useDropdownMenuContext", "DropdownMenu", "__scopeDropdownMenu", "openProp", "defaultOpen", "menuScope", "triggerRef", "<PERSON><PERSON><PERSON>", "useControllableState", "prop", "defaultProp", "onChange", "caller", "onOpenToggle", "prevOpen", "MenuPrimitive", "TRIGGER_NAME", "DropdownMenuTrigger", "triggerProps", "button", "type", "DropdownMenuPortal", "portalProps", "DropdownMenuContent", "hasInteractedOutsideRef", "originalEvent", "detail", "ctrlLeftClick", "isRightClick", "DropdownMenuGroup", "DropdownMenuLabel", "DropdownMenuItem", "DropdownMenuCheckboxItem", "DropdownMenuRadioGroup", "radioGroupProps", "DropdownMenuRadioItem", "DropdownMenuItemIndicator", "DropdownMenuSeparator", "DropdownMenuArrow", "DropdownMenuSubTrigger", "subTriggerProps", "DropdownMenuSubContent", "<PERSON><PERSON>", "Portal2", "Group2", "Label2", "CheckboxItem2", "RadioGroup2", "RadioItem2", "ItemIndicator2", "Separator2", "Sub2", "SubTrigger2", "SubContent2", "Circle", "createLucideIcon", "cx", "cy", "r", "DirectionContext", "localDir", "globalDir", "ENTRY_FOCUS", "EVENT_OPTIONS", "GROUP_NAME", "createRovingFocusGroupContext", "RovingFocus<PERSON><PERSON>ider", "useRovingFocusContext", "__scopeRovingFocusGroup", "RovingFocusGroupImpl", "currentTabStopIdProp", "defaultCurrentTabStopId", "setCurrentTabStopId", "isTabbingBackOut", "setIsTabbingBackOut", "handleEntryFocus", "isClickFocusRef", "focusableItemsCount", "setFocusableItemsCount", "onItemFocus", "tabStopId", "onItemShiftTab", "onFocusableItemAdd", "prevCount", "onFocusableItemRemove", "tabIndex", "onMouseDown", "isKeyboardFocus", "entryFocusEvent", "dispatchEvent", "active", "Boolean", "RovingFocusGroupItem", "autoId", "isCurrentTabStop", "shift<PERSON>ey", "focusIntent", "getDirectionAwareKey", "MAP_KEY_TO_FOCUS_INTENT", "getFocusIntent", "currentIndex", "slice", "hasTabStop", "ArrowLeft", "ArrowUp", "ArrowRight", "ArrowDown", "PageUp", "Home", "PageDown", "End", "undefined", "<PERSON><PERSON>", "ChevronRight", "d"], "sourceRoot": ""}