const express = require('express');
const router = express.Router();
const traderController = require('../controllers/traderController');

// Create trader
router.post('/', traderController.createTrader);

// Update trader
router.put('/:id', traderController.updateTrader);

// Delete trader
router.delete('/:id', traderController.deleteTrader);

// Get trader profile
router.get('/:id', traderController.getTraderProfile);

module.exports = router;