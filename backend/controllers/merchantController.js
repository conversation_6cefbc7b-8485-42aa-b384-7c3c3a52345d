const Merchant = require('../models/Merchant');
const User = require('../models/User');
const { uploadToS3 } = require('../services/storage');
const { v4: uuidv4 } = require('uuid');

// Create or update merchant profile
exports.createOrUpdateMerchant = async (req, res) => {
  try {
    const userId = req.user.id;
    const merchantData = req.body;
    
    // Handle file uploads if present
    if (req.files) {
      if (req.files.documentFront) {
        const frontUrl = await uploadToS3(
          req.files.documentFront[0], 
          `merchants/${userId}/kyc/front-${uuidv4()}`
        );
        merchantData.kyc.documentFrontUrl = frontUrl;
      }
      if (req.files.documentBack) {
        const backUrl = await uploadToS3(
          req.files.documentBack[0],
          `merchants/${userId}/kyc/back-${uuidv4()}`
        );
        merchantData.kyc.documentBackUrl = backUrl;
      }
    }

    // Update or create merchant
    const merchant = await Merchant.findOneAndUpdate(
      { userId },
      { $set: merchantData, status: 'draft' },
      { new: true, upsert: true, runValidators: true }
    );

    res.json(merchant);
  } catch (error) {
    console.error('Error saving merchant:', error);
    res.status(500).json({ message: 'Error saving merchant profile' });
  }
};

// Submit for verification
exports.submitForVerification = async (req, res) => {
  try {
    const merchant = await Merchant.findOneAndUpdate(
      { userId: req.user.id },
      { status: 'pending' },
      { new: true }
    );
    
    if (!merchant) {
      return res.status(404).json({ message: 'Merchant profile not found' });
    }

    // TODO: Trigger verification process (KYC check, etc.)
    
    res.json({ message: 'Merchant submitted for verification' });
  } catch (error) {
    console.error('Error submitting for verification:', error);
    res.status(500).json({ message: 'Error submitting for verification' });
  }
};

// Get merchant profile
exports.getMerchantProfile = async (req, res) => {
  try {
    const merchant = await Merchant.findOne({ userId: req.user.id });
    if (!merchant) {
      return res.status(404).json({ message: 'Merchant profile not found' });
    }
    res.json(merchant);
  } catch (error) {
    console.error('Error fetching merchant profile:', error);
    res.status(500).json({ message: 'Error fetching merchant profile' });
  }
};

// Admin: Get all merchants (with pagination)
exports.getAllMerchants = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const [merchants, total] = await Promise.all([
      Merchant.find().skip(skip).limit(limit).populate('userId', 'email name'),
      Merchant.countDocuments()
    ]);

    res.json({
      data: merchants,
      pagination: {
        total,
        page,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching merchants:', error);
    res.status(500).json({ message: 'Error fetching merchants' });
  }
};

// Admin: Update merchant status
exports.updateMerchantStatus = async (req, res) => {
  try {
    const { status, rejectionReason } = req.body;
    
    const merchant = await Merchant.findByIdAndUpdate(
      req.params.id,
      { 
        status,
        ...(status === 'rejected' && { rejectionReason }),
        ...(status === 'verified' && { 
          'kyc.verified': true,
          'kyc.verifiedAt': new Date(),
          isActive: true
        })
      },
      { new: true }
    );

    if (!merchant) {
      return res.status(404).json({ message: 'Merchant not found' });
    }

    // TODO: Send email notification to merchant about status update

    res.json(merchant);
  } catch (error) {
    console.error('Error updating merchant status:', error);
    res.status(500).json({ message: 'Error updating merchant status' });
  }
};

// Delete merchant
exports.deleteMerchant = async (req, res) => {
  try {
    const merchant = await Merchant.findByIdAndDelete(req.params.id);
    if (!merchant) return res.status(404).json({ message: 'Merchant not found' });
    // Also delete associated user if needed
    await User.findByIdAndDelete(merchant.userId);
    res.json({ message: 'Merchant deleted successfully' });
  } catch (error) {
    console.error('Error deleting merchant:', error);
    res.status(500).json({ message: 'Error deleting merchant' });
  }
};

// Admin: Create merchant profile (for admin panel)
exports.createMerchantProfile = async (req, res) => {
  try {
    const { name, email, businessName, businessType, phone, website, address, bankDetails } = req.body;

    // Create user first
    const user = new User({
      name,
      email,
      phone,
      role: 'merchant',
      isActive: true,
      isVerified: false
    });

    await user.save();

    // Create merchant profile
    const merchant = new Merchant({
      userId: user._id,
      businessName,
      businessType,
      website,
      address,
      bankDetails,
      status: 'pending',
      verificationStatus: 'pending'
    });

    await merchant.save();

    // Return populated merchant
    const populatedMerchant = await Merchant.findById(merchant._id).populate('userId', 'name email phone isActive');

    res.status(201).json(populatedMerchant);
  } catch (error) {
    console.error('Error creating merchant profile:', error);
    res.status(500).json({ message: 'Error creating merchant profile' });
  }
};

// Admin: Update merchant profile
exports.updateMerchantProfile = async (req, res) => {
  try {
    const { name, email, businessName, businessType, phone, website, address, bankDetails } = req.body;

    const merchant = await Merchant.findById(req.params.id).populate('userId');
    if (!merchant) {
      return res.status(404).json({ message: 'Merchant not found' });
    }

    // Update user information
    if (merchant.userId) {
      await User.findByIdAndUpdate(merchant.userId._id, {
        name,
        email,
        phone
      });
    }

    // Update merchant information
    const updatedMerchant = await Merchant.findByIdAndUpdate(
      req.params.id,
      {
        businessName,
        businessType,
        website,
        address,
        bankDetails
      },
      { new: true }
    ).populate('userId', 'name email phone isActive');

    res.json(updatedMerchant);
  } catch (error) {
    console.error('Error updating merchant profile:', error);
    res.status(500).json({ message: 'Error updating merchant profile' });
  }
};
