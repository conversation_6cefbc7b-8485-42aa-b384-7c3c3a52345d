"use strict";(self.webpackChunkpayment_gateway_frontend=self.webpackChunkpayment_gateway_frontend||[]).push([[396],{1490:(e,s,t)=>{t.d(s,{A:()=>r});const r=(0,t(3797).A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},6742:(e,s,t)=>{t.d(s,{BT:()=>c,Wu:()=>o,ZB:()=>d,Zp:()=>i,aR:()=>l,wL:()=>m});var r=t(5043),a=t(3009),n=t(579);const i=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,n.jsx)("div",{ref:s,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...r})});i.displayName="Card";const l=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,n.jsx)("div",{ref:s,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",t),...r})});l.displayName="CardHeader";const d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,n.jsx)("h3",{ref:s,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",t),...r})});d.displayName="CardTitle";const c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,n.jsx)("p",{ref:s,className:(0,a.cn)("text-sm text-muted-foreground",t),...r})});c.displayName="CardDescription";const o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,n.jsx)("div",{ref:s,className:(0,a.cn)("p-6 pt-0",t),...r})});o.displayName="CardContent";const m=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,n.jsx)("div",{ref:s,className:(0,a.cn)("flex items-center p-6 pt-0",t),...r})});m.displayName="CardFooter"},6875:(e,s,t)=>{t.d(s,{A:()=>r});const r=(0,t(3797).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},7008:(e,s,t)=>{t.d(s,{A:()=>r});const r=(0,t(3797).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},8396:(e,s,t)=>{t.r(s),t.d(s,{default:()=>o});t(5043);var r=t(6742),a=t(9772),n=t(7008),i=t(1490),l=t(6875),d=t(9954),c=t(579);const o=()=>(0,c.jsxs)("div",{className:"space-y-6",children:[(0,c.jsxs)("div",{className:"flex items-center justify-between",children:[(0,c.jsxs)("div",{children:[(0,c.jsx)("h2",{className:"text-2xl font-bold tracking-tight",children:"Billing & Settlements"}),(0,c.jsx)("p",{className:"text-muted-foreground",children:"View your billing history and manage subscriptions"})]}),(0,c.jsx)("div",{className:"flex items-center space-x-2",children:(0,c.jsxs)(a.$,{variant:"outline",size:"sm",className:"h-8",children:[(0,c.jsx)(n.A,{className:"mr-2 h-4 w-4"}),"Export"]})})]}),(0,c.jsxs)(r.Zp,{children:[(0,c.jsx)(r.aR,{children:(0,c.jsxs)("div",{className:"flex items-center justify-between",children:[(0,c.jsx)(r.ZB,{children:"Billing History"}),(0,c.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,c.jsxs)("div",{className:"relative",children:[(0,c.jsx)(l.A,{className:"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground"}),(0,c.jsx)(d.p,{type:"search",placeholder:"Search invoices...",className:"pl-8 sm:w-[300px]"})]}),(0,c.jsxs)(a.$,{variant:"outline",size:"sm",className:"h-9",children:[(0,c.jsx)(i.A,{className:"mr-2 h-4 w-4"}),"Filter"]})]})]})}),(0,c.jsx)(r.Wu,{children:(0,c.jsx)("div",{className:"space-y-4",children:[{id:"1",date:"2023-06-15",description:"Monthly Subscription",amount:99.99,status:"paid",invoice:"INV-001"}].map(e=>(0,c.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,c.jsxs)("div",{className:"space-y-1",children:[(0,c.jsx)("p",{className:"font-medium",children:e.description}),(0,c.jsxs)("p",{className:"text-sm text-muted-foreground",children:[e.date," \u2022 ",e.invoice]})]}),(0,c.jsxs)("div",{className:"text-right",children:[(0,c.jsxs)("p",{className:"font-medium",children:["$",e.amount.toFixed(2)]}),(0,c.jsx)("span",{className:"text-xs px-2 py-1 rounded-full "+("paid"===e.status?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"),children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})]})]},e.id))})})]})]})},9772:(e,s,t)=>{t.d(s,{$:()=>c});var r=t(5043),a=t(6851),n=t(917),i=t(3009),l=t(579);const d=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=r.forwardRef((e,s)=>{let{className:t,variant:r,size:n,asChild:c=!1,...o}=e;const m=c?a.DX:"button";return(0,l.jsx)(m,{className:(0,i.cn)(d({variant:r,size:n,className:t})),ref:s,...o})});c.displayName="Button"},9954:(e,s,t)=>{t.d(s,{p:()=>i});var r=t(5043),a=t(3009),n=t(579);const i=r.forwardRef((e,s)=>{let{className:t,type:r,...i}=e;return(0,n.jsx)("input",{type:r,className:(0,a.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...i})});i.displayName="Input"}}]);
//# sourceMappingURL=396.d0bd5c17.chunk.js.map