const Merchant = require('../models/Merchant');
const User = require('../models/User');
const { uploadToS3 } = require('../services/storage');
const { v4: uuidv4 } = require('uuid');

// Create or update merchant profile
exports.createOrUpdateMerchant = async (req, res) => {
  try {
    const userId = req.user.id;
    const merchantData = req.body;
    
    // Handle file uploads if present
    if (req.files) {
      if (req.files.documentFront) {
        const frontUrl = await uploadToS3(
          req.files.documentFront[0], 
          `merchants/${userId}/kyc/front-${uuidv4()}`
        );
        merchantData.kyc.documentFrontUrl = frontUrl;
      }
      if (req.files.documentBack) {
        const backUrl = await uploadToS3(
          req.files.documentBack[0],
          `merchants/${userId}/kyc/back-${uuidv4()}`
        );
        merchantData.kyc.documentBackUrl = backUrl;
      }
    }

    // Update or create merchant
    const merchant = await Merchant.findOneAndUpdate(
      { userId },
      { $set: merchantData, status: 'draft' },
      { new: true, upsert: true, runValidators: true }
    );

    res.json(merchant);
  } catch (error) {
    console.error('Error saving merchant:', error);
    res.status(500).json({ message: 'Error saving merchant profile' });
  }
};

// Submit for verification
exports.submitForVerification = async (req, res) => {
  try {
    const merchant = await Merchant.findOneAndUpdate(
      { userId: req.user.id },
      { status: 'pending' },
      { new: true }
    );
    
    if (!merchant) {
      return res.status(404).json({ message: 'Merchant profile not found' });
    }

    // TODO: Trigger verification process (KYC check, etc.)
    
    res.json({ message: 'Merchant submitted for verification' });
  } catch (error) {
    console.error('Error submitting for verification:', error);
    res.status(500).json({ message: 'Error submitting for verification' });
  }
};

// Get merchant profile
exports.getMerchantProfile = async (req, res) => {
  try {
    const merchant = await Merchant.findOne({ userId: req.user.id });
    if (!merchant) {
      return res.status(404).json({ message: 'Merchant profile not found' });
    }
    res.json(merchant);
  } catch (error) {
    console.error('Error fetching merchant profile:', error);
    res.status(500).json({ message: 'Error fetching merchant profile' });
  }
};

// Admin: Get all merchants (with pagination)
exports.getAllMerchants = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const [merchants, total] = await Promise.all([
      Merchant.find().skip(skip).limit(limit).populate('userId', 'email name'),
      Merchant.countDocuments()
    ]);

    res.json({
      data: merchants,
      pagination: {
        total,
        page,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching merchants:', error);
    res.status(500).json({ message: 'Error fetching merchants' });
  }
};

// Admin: Update merchant status
exports.updateMerchantStatus = async (req, res) => {
  try {
    const { status, rejectionReason } = req.body;
    
    const merchant = await Merchant.findByIdAndUpdate(
      req.params.id,
      { 
        status,
        ...(status === 'rejected' && { rejectionReason }),
        ...(status === 'verified' && { 
          'kyc.verified': true,
          'kyc.verifiedAt': new Date(),
          isActive: true
        })
      },
      { new: true }
    );

    if (!merchant) {
      return res.status(404).json({ message: 'Merchant not found' });
    }

    // TODO: Send email notification to merchant about status update

    res.json(merchant);
  } catch (error) {
    console.error('Error updating merchant status:', error);
    res.status(500).json({ message: 'Error updating merchant status' });
  }
};

// Delete merchant
exports.deleteMerchant = async (req, res) => {
  try {
    const merchant = await Merchant.findByIdAndDelete(req.params.id);
    if (!merchant) return res.status(404).json({ message: 'Merchant not found' });
    // Also delete associated user if needed
    await User.findByIdAndDelete(merchant.userId);
    res.json({ message: 'Merchant deleted successfully' });
  } catch (error) {
    console.error('Error deleting merchant:', error);
    res.status(500).json({ message: 'Error deleting merchant' });
  }
};

// Helper function to log changes
const logChange = (field, oldValue, newValue, changedBy, changeType = 'update', reason = '') => {
  return {
    field,
    oldValue,
    newValue,
    changedBy,
    changedAt: new Date(),
    changeType,
    reason
  };
};

// Admin: Create merchant profile (for admin panel)
exports.createMerchantProfile = async (req, res) => {
  try {
    const {
      name,
      email,
      businessName,
      businessType,
      phone,
      website,
      address,
      bankDetails,
      customFields = [],
      adminUserId = '507f1f77bcf86cd799439011' // Default admin ID for demo
    } = req.body;

    // Create user first
    const user = new User({
      name,
      email,
      phone,
      role: 'merchant',
      isActive: true,
      isVerified: false
    });

    await user.save();

    // Process custom fields
    const processedCustomFields = customFields.map(field => ({
      ...field,
      addedBy: adminUserId,
      addedAt: new Date()
    }));

    // Create merchant profile
    const merchant = new Merchant({
      userId: user._id,
      businessName,
      businessType,
      website,
      address,
      bankDetails,
      status: 'pending',
      verificationStatus: 'pending',
      customFields: processedCustomFields,
      lastModifiedBy: adminUserId,
      changeLog: [
        logChange('merchant_created', null, 'Created new merchant profile', adminUserId, 'create')
      ]
    });

    await merchant.save();

    // Return populated merchant
    const populatedMerchant = await Merchant.findById(merchant._id)
      .populate('userId', 'name email phone isActive')
      .populate('customFields.addedBy', 'name email')
      .populate('changeLog.changedBy', 'name email')
      .populate('lastModifiedBy', 'name email');

    res.status(201).json(populatedMerchant);
  } catch (error) {
    console.error('Error creating merchant profile:', error);
    res.status(500).json({ message: 'Error creating merchant profile' });
  }
};

// Admin: Update merchant profile
exports.updateMerchantProfile = async (req, res) => {
  try {
    const {
      name,
      email,
      businessName,
      businessType,
      phone,
      website,
      address,
      bankDetails,
      customFields = [],
      adminUserId = '507f1f77bcf86cd799439011', // Default admin ID for demo
      changeReason = ''
    } = req.body;

    const merchant = await Merchant.findById(req.params.id).populate('userId');
    if (!merchant) {
      return res.status(404).json({ message: 'Merchant not found' });
    }

    const changeLogs = [];

    // Track changes in user information
    if (merchant.userId) {
      const oldUserData = {
        name: merchant.userId.name,
        email: merchant.userId.email,
        phone: merchant.userId.phone
      };

      const newUserData = { name, email, phone };

      // Log user field changes
      Object.keys(newUserData).forEach(key => {
        if (oldUserData[key] !== newUserData[key]) {
          changeLogs.push(logChange(
            `user_${key}`,
            oldUserData[key],
            newUserData[key],
            adminUserId,
            'update',
            changeReason
          ));
        }
      });

      await User.findByIdAndUpdate(merchant.userId._id, newUserData);
    }

    // Track changes in merchant information
    const oldMerchantData = {
      businessName: merchant.businessName,
      businessType: merchant.businessType,
      website: merchant.website,
      address: merchant.address,
      bankDetails: merchant.bankDetails
    };

    const newMerchantData = { businessName, businessType, website, address, bankDetails };

    // Log merchant field changes
    Object.keys(newMerchantData).forEach(key => {
      if (JSON.stringify(oldMerchantData[key]) !== JSON.stringify(newMerchantData[key])) {
        changeLogs.push(logChange(
          key,
          oldMerchantData[key],
          newMerchantData[key],
          adminUserId,
          'update',
          changeReason
        ));
      }
    });

    // Process custom fields with change tracking
    const processedCustomFields = customFields.map(field => {
      const existingField = merchant.customFields.find(cf => cf.fieldName === field.fieldName);

      if (existingField && existingField.fieldValue !== field.fieldValue) {
        // Log custom field change
        changeLogs.push(logChange(
          `custom_${field.fieldName}`,
          existingField.fieldValue,
          field.fieldValue,
          adminUserId,
          'update',
          changeReason
        ));
      }

      return {
        ...field,
        addedBy: existingField ? existingField.addedBy : adminUserId,
        addedAt: existingField ? existingField.addedAt : new Date()
      };
    });

    // Update merchant information
    const updatedMerchant = await Merchant.findByIdAndUpdate(
      req.params.id,
      {
        ...newMerchantData,
        customFields: processedCustomFields,
        lastModifiedBy: adminUserId,
        $push: { changeLog: { $each: changeLogs } }
      },
      { new: true }
    ).populate('userId', 'name email phone isActive')
     .populate('customFields.addedBy', 'name email')
     .populate('changeLog.changedBy', 'name email')
     .populate('lastModifiedBy', 'name email');

    res.json(updatedMerchant);
  } catch (error) {
    console.error('Error updating merchant profile:', error);
    res.status(500).json({ message: 'Error updating merchant profile' });
  }
};

// Add custom field to merchant
exports.addCustomField = async (req, res) => {
  try {
    const { fieldName, fieldType, fieldValue, isRequired, options, adminUserId } = req.body;

    const merchant = await Merchant.findById(req.params.id);
    if (!merchant) {
      return res.status(404).json({ message: 'Merchant not found' });
    }

    // Check if field already exists
    const existingField = merchant.customFields.find(cf => cf.fieldName === fieldName);
    if (existingField) {
      return res.status(400).json({ message: 'Custom field with this name already exists' });
    }

    const newCustomField = {
      fieldName,
      fieldType,
      fieldValue,
      isRequired,
      options,
      addedBy: adminUserId,
      addedAt: new Date()
    };

    const changeLog = logChange(
      `custom_${fieldName}`,
      null,
      fieldValue,
      adminUserId,
      'create',
      `Added custom field: ${fieldName}`
    );

    const updatedMerchant = await Merchant.findByIdAndUpdate(
      req.params.id,
      {
        $push: {
          customFields: newCustomField,
          changeLog: changeLog
        },
        lastModifiedBy: adminUserId
      },
      { new: true }
    ).populate('customFields.addedBy', 'name email')
     .populate('changeLog.changedBy', 'name email');

    res.json(updatedMerchant);
  } catch (error) {
    console.error('Error adding custom field:', error);
    res.status(500).json({ message: 'Error adding custom field' });
  }
};

// Update custom field
exports.updateCustomField = async (req, res) => {
  try {
    const { fieldName, fieldValue, adminUserId, changeReason } = req.body;

    const merchant = await Merchant.findById(req.params.id);
    if (!merchant) {
      return res.status(404).json({ message: 'Merchant not found' });
    }

    const fieldIndex = merchant.customFields.findIndex(cf => cf.fieldName === fieldName);
    if (fieldIndex === -1) {
      return res.status(404).json({ message: 'Custom field not found' });
    }

    const oldValue = merchant.customFields[fieldIndex].fieldValue;

    // Update the custom field
    merchant.customFields[fieldIndex].fieldValue = fieldValue;

    // Add change log
    const changeLog = logChange(
      `custom_${fieldName}`,
      oldValue,
      fieldValue,
      adminUserId,
      'update',
      changeReason || `Updated custom field: ${fieldName}`
    );

    merchant.changeLog.push(changeLog);
    merchant.lastModifiedBy = adminUserId;

    await merchant.save();

    const populatedMerchant = await Merchant.findById(merchant._id)
      .populate('customFields.addedBy', 'name email')
      .populate('changeLog.changedBy', 'name email')
      .populate('lastModifiedBy', 'name email');

    res.json(populatedMerchant);
  } catch (error) {
    console.error('Error updating custom field:', error);
    res.status(500).json({ message: 'Error updating custom field' });
  }
};

// Delete custom field
exports.deleteCustomField = async (req, res) => {
  try {
    const { fieldName, adminUserId, changeReason } = req.body;

    const merchant = await Merchant.findById(req.params.id);
    if (!merchant) {
      return res.status(404).json({ message: 'Merchant not found' });
    }

    const fieldToDelete = merchant.customFields.find(cf => cf.fieldName === fieldName);
    if (!fieldToDelete) {
      return res.status(404).json({ message: 'Custom field not found' });
    }

    // Remove the custom field
    merchant.customFields = merchant.customFields.filter(cf => cf.fieldName !== fieldName);

    // Add change log
    const changeLog = logChange(
      `custom_${fieldName}`,
      fieldToDelete.fieldValue,
      null,
      adminUserId,
      'delete',
      changeReason || `Deleted custom field: ${fieldName}`
    );

    merchant.changeLog.push(changeLog);
    merchant.lastModifiedBy = adminUserId;

    await merchant.save();

    const populatedMerchant = await Merchant.findById(merchant._id)
      .populate('customFields.addedBy', 'name email')
      .populate('changeLog.changedBy', 'name email')
      .populate('lastModifiedBy', 'name email');

    res.json(populatedMerchant);
  } catch (error) {
    console.error('Error deleting custom field:', error);
    res.status(500).json({ message: 'Error deleting custom field' });
  }
};

// Get merchant change history
exports.getMerchantChangeHistory = async (req, res) => {
  try {
    const merchant = await Merchant.findById(req.params.id)
      .populate('changeLog.changedBy', 'name email')
      .select('changeLog');

    if (!merchant) {
      return res.status(404).json({ message: 'Merchant not found' });
    }

    // Sort change log by date (newest first)
    const sortedChangeLog = merchant.changeLog.sort((a, b) => new Date(b.changedAt) - new Date(a.changedAt));

    res.json({
      merchantId: req.params.id,
      changeHistory: sortedChangeLog
    });
  } catch (error) {
    console.error('Error fetching change history:', error);
    res.status(500).json({ message: 'Error fetching change history' });
  }
};
