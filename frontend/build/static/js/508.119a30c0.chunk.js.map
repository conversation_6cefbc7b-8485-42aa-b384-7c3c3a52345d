{"version": 3, "file": "static/js/508.119a30c0.chunk.js", "mappings": "kOAIA,MAAMA,EAAQC,EAAAA,WAGZ,CAAAC,EAA0BC,KAAG,IAA5B,UAAEC,KAAcC,GAAOH,EAAA,OACxBI,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uBAAsBG,UACnCD,EAAAA,EAAAA,KAAA,SACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IAAG,gCAAiCJ,MAC3CC,QAIVL,EAAMS,YAAc,QAEpB,MAAMC,EAAcT,EAAAA,WAGlB,CAAAU,EAA0BR,KAAG,IAA5B,UAAEC,KAAcC,GAAOM,EAAA,OACxBL,EAAAA,EAAAA,KAAA,SAAOH,IAAKA,EAAKC,WAAWI,EAAAA,EAAAA,IAAG,kBAAmBJ,MAAgBC,MAEpEK,EAAYD,YAAc,cAE1B,MAAMG,EAAYX,EAAAA,WAGhB,CAAAY,EAA0BV,KAAG,IAA5B,UAAEC,KAAcC,GAAOQ,EAAA,OACxBP,EAAAA,EAAAA,KAAA,SACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IAAG,6BAA8BJ,MACxCC,MAGRO,EAAUH,YAAc,YAEJR,EAAAA,WAGlB,CAAAa,EAA0BX,KAAG,IAA5B,UAAEC,KAAcC,GAAOS,EAAA,OACxBR,EAAAA,EAAAA,KAAA,SACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IAAG,iDAAkDJ,MAC5DC,MAGII,YAAc,cAE1B,MAAMM,EAAWd,EAAAA,WAGf,CAAAe,EAA0Bb,KAAG,IAA5B,UAAEC,KAAcC,GAAOW,EAAA,OACxBV,EAAAA,EAAAA,KAAA,MACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IACT,8EACAJ,MAEEC,MAGRU,EAASN,YAAc,WAEvB,MAAMQ,EAAYhB,EAAAA,WAGhB,CAAAiB,EAA0Bf,KAAG,IAA5B,UAAEC,KAAcC,GAAOa,EAAA,OACxBZ,EAAAA,EAAAA,KAAA,MACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IACT,mGACAJ,MAEEC,MAGRY,EAAUR,YAAc,YAExB,MAAMU,EAAYlB,EAAAA,WAGhB,CAAAmB,EAA0BjB,KAAG,IAA5B,UAAEC,KAAcC,GAAOe,EAAA,OACxBd,EAAAA,EAAAA,KAAA,MACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IAAG,iDAAkDJ,MAC5DC,MAGRc,EAAUV,YAAc,YAEHR,EAAAA,WAGnB,CAAAoB,EAA0BlB,KAAG,IAA5B,UAAEC,KAAcC,GAAOgB,EAAA,OACxBf,EAAAA,EAAAA,KAAA,WACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IAAG,qCAAsCJ,MAChDC,MAGKI,YAAc,c,6ICjG3B,MAAMa,EAASC,EAAAA,GAETC,EAAgBD,EAAAA,GAOhBE,EAAevB,IAIK,IAJJ,UACpBE,EAAS,SACTG,KACGF,GACeH,EAClB,MAAM,UAAEwB,EAAS,WAAEC,KAAeC,GAAcvB,EAC1CwB,EAAc,CAClBH,YACAC,WAAYA,GAEd,OACErB,EAAAA,EAAAA,KAACiB,EAAAA,GAAsB,IAAKM,EAAWtB,UACrCD,EAAAA,EAAAA,KAAA,OAAKF,WAAWI,EAAAA,EAAAA,IACd,qEACAJ,GACAG,SACCA,OAKTkB,EAAahB,YAAcc,EAAAA,GAAuBd,YAElD,MAAMqB,EAAgB7B,EAAAA,WAGpB,CAAAU,EAA0BR,KAAG,IAA5B,UAAEC,KAAcC,GAAOM,EAAA,OACxBL,EAAAA,EAAAA,KAACiB,EAAAA,GAAuB,CACtBpB,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IACT,qKACAJ,MAEEC,MAGRyB,EAAcrB,YAAcc,EAAAA,GAAwBd,YAEpD,MAAMsB,EAAgB9B,EAAAA,WAGpB,CAAAY,EAAoCV,KAAG,IAAtC,UAAEC,EAAS,SAAEG,KAAaF,GAAOQ,EAAA,OAClCmB,EAAAA,EAAAA,MAACP,EAAY,CAAAlB,SAAA,EACXD,EAAAA,EAAAA,KAACwB,EAAa,KACdE,EAAAA,EAAAA,MAACT,EAAAA,GAAuB,CACtBpB,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IACT,+NACA,mBACAJ,MAEEC,EAAKE,SAAA,CAERA,GACDyB,EAAAA,EAAAA,MAACT,EAAAA,GAAqB,CAACnB,UAAU,mTAAkTG,SAAA,EACjVD,EAAAA,EAAAA,KAAC2B,EAAAA,EAAC,CAAC7B,UAAU,aACbE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,UAASG,SAAC,qBAKlCwB,EAActB,YAAcc,EAAAA,GAAwBd,YAEpD,MAAMyB,EAAepB,IAAA,IAAC,UACpBV,KACGC,GACkCS,EAAA,OACrCR,EAAAA,EAAAA,KAAA,OACEF,WAAWI,EAAAA,EAAAA,IACT,mDACAJ,MAEEC,KAGR6B,EAAazB,YAAc,eAE3B,MAAM0B,EAAenB,IAAA,IAAC,UACpBZ,KACGC,GACkCW,EAAA,OACrCV,EAAAA,EAAAA,KAAA,OACEF,WAAWI,EAAAA,EAAAA,IACT,gEACAJ,MAEEC,KAGR8B,EAAa1B,YAAc,eAE3B,MAAM2B,EAAcnC,EAAAA,WAGlB,CAAAiB,EAA0Bf,KAAG,IAA5B,UAAEC,KAAcC,GAAOa,EAAA,OACxBZ,EAAAA,EAAAA,KAACiB,EAAAA,GAAqB,CACpBpB,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IACT,sCACA,oBACAJ,MAEEC,MAGR+B,EAAY3B,YAAcc,EAAAA,GAAsBd,YAEhD,MAAM4B,EAAoBpC,EAAAA,WAGxB,CAAAmB,EAA0BjB,KAAG,IAA5B,UAAEC,KAAcC,GAAOe,EAAA,OACxBd,EAAAA,EAAAA,KAACiB,EAAAA,GAA2B,CAC1BpB,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IAAG,wBAAyB,qBAAsBJ,MACzDC,MAGRgC,EAAkB5B,YAAcc,EAAAA,GAA4Bd,W,4EC9H5D,MAAM6B,GAAgBC,EAAAA,EAAAA,GACpB,8FAGIC,EAAQvC,EAAAA,WAGZ,CAAAC,EAA0BC,KAAG,IAA5B,UAAEC,KAAcC,GAAOH,EAAA,OACxBI,EAAAA,EAAAA,KAAA,SACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IAAG8B,IAAiBlC,MAC3BC,MAGRmC,EAAM/B,YAAc,O,yQCGdgC,EAAc,UAGbC,EAAqBC,IAAqBC,EAAAA,EAAAA,GAAmBH,IAc7DI,EAAgBC,GAAoBJ,EAAwCD,GAU7EnB,EAAiCjB,IACrC,MAAM,cACJ0C,EAAA,SACAxC,EACAyC,KAAMC,EAAA,YACNC,EAAA,aACAC,EAAA,MACAC,GAAQ,GACN/C,EACEgD,EAAmBpD,EAAAA,OAA0B,MAC7CqD,EAAmBrD,EAAAA,OAA6B,OAC/C+C,EAAMO,IAAWC,EAAAA,EAAAA,GAAqB,CAC3CC,KAAMR,EACNS,YAAaR,IAAe,EAC5BS,SAAUR,EACVS,OAAQnB,IAGV,OACEoB,EAAAA,EAAAA,KAAChB,EAAA,CACCiB,MAAOf,EACPM,aACAC,aACAS,WAAWC,EAAAA,EAAAA,KACXC,SAASD,EAAAA,EAAAA,KACTE,eAAeF,EAAAA,EAAAA,KACfhB,OACAG,aAAcI,EACdY,aAAoBlE,EAAAA,YAAY,IAAMsD,EAASa,IAAcA,GAAW,CAACb,IACzEH,QAEC7C,cAKPe,EAAOb,YAAcgC,EAMrB,IAAM4B,EAAe,gBAMf7C,EAAsBvB,EAAAA,WAC1B,CAACI,EAAwCiE,KACvC,MAAM,cAAEvB,KAAkBwB,GAAiBlE,EACrCmE,EAAU1B,EAAiBuB,EAActB,GACzC0B,GAAqBC,EAAAA,EAAAA,GAAgBJ,EAAcE,EAAQnB,YACjE,OACEQ,EAAAA,EAAAA,KAACc,EAAAA,GAAUC,OAAV,CACCC,KAAK,SACL,gBAAc,SACd,gBAAeL,EAAQxB,KACvB,gBAAewB,EAAQT,UACvB,aAAYe,EAASN,EAAQxB,SACzBuB,EACJpE,IAAKsE,EACLM,SAASC,EAAAA,EAAAA,GAAqB3E,EAAM0E,QAASP,EAAQL,kBAM7D3C,EAAcf,YAAc4D,EAM5B,IAAMY,EAAc,gBAGbC,EAAgBC,GAAoBzC,EAAwCuC,EAAa,CAC9FtD,gBAAY,IAiBRF,EAA6CpB,IACjD,MAAM,cAAE0C,EAAA,WAAepB,EAAA,SAAYpB,EAAA,UAAUmB,GAAcrB,EACrDmE,EAAU1B,EAAiBmC,EAAalC,GAC9C,OACEc,EAAAA,EAAAA,KAACqB,EAAA,CAAepB,MAAOf,EAAepB,aACnCpB,SAAMN,EAAAA,SAASmF,IAAI7E,EAAW8E,IAC7BxB,EAAAA,EAAAA,KAACyB,EAAAA,EAAA,CAASC,QAAS5D,GAAc6C,EAAQxB,KACvCzC,UAAAsD,EAAAA,EAAAA,KAAC2B,EAAAA,EAAA,CAAgBC,SAAO,EAAC/D,YACtBnB,SAAA8E,UAQb5D,EAAahB,YAAcwE,EAM3B,IAAMS,EAAe,gBAWf5D,EAAsB7B,EAAAA,WAC1B,CAACI,EAAwCiE,KACvC,MAAMqB,EAAgBR,EAAiBO,EAAcrF,EAAM0C,gBACrD,WAAEpB,EAAagE,EAAchE,cAAeiE,GAAiBvF,EAC7DmE,EAAU1B,EAAiB4C,EAAcrF,EAAM0C,eACrD,OAAOyB,EAAQpB,OACbS,EAAAA,EAAAA,KAACyB,EAAAA,EAAA,CAASC,QAAS5D,GAAc6C,EAAQxB,KACvCzC,UAAAsD,EAAAA,EAAAA,KAACgC,EAAA,IAAsBD,EAAczF,IAAKmE,MAE1C,OAIRxC,EAAcrB,YAAciF,EAM5B,IAAMI,GAAOC,EAAAA,EAAAA,IAAW,8BAElBF,EAA0B5F,EAAAA,WAC9B,CAACI,EAA4CiE,KAC3C,MAAM,cAAEvB,KAAkB6C,GAAiBvF,EACrCmE,EAAU1B,EAAiB4C,EAAc3C,GAC/C,OAGEc,EAAAA,EAAAA,KAACmC,EAAAA,EAAA,CAAaC,GAAIH,EAAMI,gBAAc,EAACC,OAAQ,CAAC3B,EAAQlB,YACtD/C,UAAAsD,EAAAA,EAAAA,KAACc,EAAAA,GAAUyB,IAAV,CACC,aAAYtB,EAASN,EAAQxB,SACzB4C,EACJzF,IAAKmE,EAEL+B,MAAO,CAAEC,cAAe,UAAWV,EAAaS,aAWpDE,EAAe,gBAWfxE,EAAsB9B,EAAAA,WAC1B,CAACI,EAAwCiE,KACvC,MAAMqB,EAAgBR,EAAiBoB,EAAclG,EAAM0C,gBACrD,WAAEpB,EAAagE,EAAchE,cAAe6E,GAAiBnG,EAC7DmE,EAAU1B,EAAiByD,EAAclG,EAAM0C,eACrD,OACEc,EAAAA,EAAAA,KAACyB,EAAAA,EAAA,CAASC,QAAS5D,GAAc6C,EAAQxB,KACtCzC,SAAAiE,EAAQpB,OACPS,EAAAA,EAAAA,KAAC4C,EAAA,IAAuBD,EAAcrG,IAAKmE,KAE3CT,EAAAA,EAAAA,KAAC6C,EAAA,IAA0BF,EAAcrG,IAAKmE,QAOxDvC,EAActB,YAAc8F,EAQ5B,IAAME,EAA2BxG,EAAAA,WAC/B,CAACI,EAA4CiE,KAC3C,MAAME,EAAU1B,EAAiByD,EAAclG,EAAM0C,eAC/CO,EAAmBrD,EAAAA,OAAuB,MAC1C0G,GAAejC,EAAAA,EAAAA,GAAgBJ,EAAcE,EAAQlB,WAAYA,GAQvE,OALMrD,EAAAA,UAAU,KACd,MAAM2G,EAAUtD,EAAWuD,QAC3B,GAAID,EAAS,OAAOE,EAAAA,EAAAA,IAAWF,IAC9B,KAGD/C,EAAAA,EAAAA,KAACkD,EAAA,IACK1G,EACJF,IAAKwG,EAGLK,UAAWxC,EAAQxB,KACnBiE,6BAA2B,EAC3BC,kBAAkBlC,EAAAA,EAAAA,GAAqB3E,EAAM6G,iBAAmBC,IAC9DA,EAAMC,iBACN5C,EAAQnB,WAAWwD,SAASQ,UAE9BC,sBAAsBtC,EAAAA,EAAAA,GAAqB3E,EAAMiH,qBAAuBH,IACtE,MAAMI,EAAgBJ,EAAMK,OAAOD,cAC7BE,EAAyC,IAAzBF,EAAc3C,SAA0C,IAA1B2C,EAAcG,SACpB,IAAzBH,EAAc3C,QAAgB6C,IAIjCN,EAAMC,mBAI1BO,gBAAgB3C,EAAAA,EAAAA,GAAqB3E,EAAMsH,eAAiBR,GAC1DA,EAAMC,sBASVV,EAA8BzG,EAAAA,WAClC,CAACI,EAA4CiE,KAC3C,MAAME,EAAU1B,EAAiByD,EAAclG,EAAM0C,eAC/C6E,EAAgC3H,EAAAA,QAAO,GACvC4H,EAAiC5H,EAAAA,QAAO,GAE9C,OACE4D,EAAAA,EAAAA,KAACkD,EAAA,IACK1G,EACJF,IAAKmE,EACL0C,WAAW,EACXC,6BAA6B,EAC7BC,iBAAmBC,IACjB9G,EAAM6G,mBAAmBC,GAEpBA,EAAMW,mBACJF,EAAwBf,SAASrC,EAAQnB,WAAWwD,SAASQ,QAElEF,EAAMC,kBAGRQ,EAAwBf,SAAU,EAClCgB,EAAyBhB,SAAU,GAErCkB,kBAAoBZ,IAClB9G,EAAM0H,oBAAoBZ,GAErBA,EAAMW,mBACTF,EAAwBf,SAAU,EACM,gBAApCM,EAAMK,OAAOD,cAAc1C,OAC7BgD,EAAyBhB,SAAU,IAOvC,MAAMmB,EAASb,EAAMa,OACfC,EAAkBzD,EAAQnB,WAAWwD,SAASqB,SAASF,GACzDC,GAAiBd,EAAMC,iBAMa,YAApCD,EAAMK,OAAOD,cAAc1C,MAAsBgD,EAAyBhB,SAC5EM,EAAMC,sBAkCZL,EAA0B9G,EAAAA,WAC9B,CAACI,EAA4CiE,KAC3C,MAAM,cAAEvB,EAAA,UAAeiE,EAAA,gBAAWmB,EAAA,iBAAiBjB,KAAqBV,GAAiBnG,EACnFmE,EAAU1B,EAAiByD,EAAcxD,GACzCO,EAAmBrD,EAAAA,OAAuB,MAC1C0G,GAAejC,EAAAA,EAAAA,GAAgBJ,EAAchB,GAMnD,OAFA8E,EAAAA,EAAAA,OAGEC,EAAAA,EAAAA,MAAAC,EAAAA,SAAA,CACE/H,SAAA,EAAAsD,EAAAA,EAAAA,KAAC0E,EAAAA,EAAA,CACC9C,SAAO,EACP+C,MAAI,EACJC,QAASzB,EACT0B,iBAAkBP,EAClBQ,mBAAoBzB,EAEpB3G,UAAAsD,EAAAA,EAAAA,KAAC+E,EAAAA,GAAA,CACCC,KAAK,SACLC,GAAItE,EAAQT,UACZ,mBAAkBS,EAAQN,cAC1B,kBAAiBM,EAAQP,QACzB,aAAYa,EAASN,EAAQxB,SACzBwD,EACJrG,IAAKwG,EACLoC,UAAWA,IAAMvE,EAAQrB,cAAa,QAIxCkF,EAAAA,EAAAA,MAAAC,EAAAA,SAAA,CACE/H,SAAA,EAAAsD,EAAAA,EAAAA,KAACmF,EAAA,CAAa/E,QAASO,EAAQP,WAC/BJ,EAAAA,EAAAA,KAACoF,EAAA,CAAmB3F,aAAwBY,cAAeM,EAAQN,wBAYzEgF,EAAa,cAMb9G,EAAoBnC,EAAAA,WACxB,CAACI,EAAsCiE,KACrC,MAAM,cAAEvB,KAAkBoG,GAAe9I,EACnCmE,EAAU1B,EAAiBoG,EAAYnG,GAC7C,OAAOc,EAAAA,EAAAA,KAACc,EAAAA,GAAUyE,GAAV,CAAaN,GAAItE,EAAQP,WAAakF,EAAYhJ,IAAKmE,MAInElC,EAAY3B,YAAcyI,EAM1B,IAAMG,EAAmB,oBAMnBhH,EAA0BpC,EAAAA,WAC9B,CAACI,EAA4CiE,KAC3C,MAAM,cAAEvB,KAAkBuG,GAAqBjJ,EACzCmE,EAAU1B,EAAiBuG,EAAkBtG,GACnD,OAAOc,EAAAA,EAAAA,KAACc,EAAAA,GAAU4E,EAAV,CAAYT,GAAItE,EAAQN,iBAAmBoF,EAAkBnJ,IAAKmE,MAI9EjC,EAAkB5B,YAAc4I,EAMhC,IAAMG,EAAa,cAKbC,EAAoBxJ,EAAAA,WACxB,CAACI,EAAsCiE,KACrC,MAAM,cAAEvB,KAAkB2G,GAAerJ,EACnCmE,EAAU1B,EAAiB0G,EAAYzG,GAC7C,OACEc,EAAAA,EAAAA,KAACc,EAAAA,GAAUC,OAAV,CACCC,KAAK,YACD6E,EACJvJ,IAAKmE,EACLS,SAASC,EAAAA,EAAAA,GAAqB3E,EAAM0E,QAAS,IAAMP,EAAQrB,cAAa,QAUhF,SAAS2B,EAAS9B,GAChB,OAAOA,EAAO,OAAS,QACzB,CANAyG,EAAYhJ,YAAc+I,EAQ1B,IAAMG,EAAqB,sBAEpBC,EAAiBC,IAAqBC,EAAAA,EAAAA,GAAcH,EAAoB,CAC7EI,YAAaxD,EACbyD,UAAWd,EACXe,SAAU,WAKNjB,EAA4C9I,IAAiB,IAAhB,QAAE+D,GAAQ/D,EAC3D,MAAMgK,EAAsBL,EAAkBF,GAExCQ,EAAU,KAAKD,EAAoBH,8BAA8BG,EAAoBF,wGAEjEE,EAAoBF,gJAE4BE,EAAoBD,WAS9F,OAPMhK,EAAAA,UAAU,KACd,GAAIgE,EAAS,CACMmG,SAASC,eAAepG,IAC1BqG,QAAQC,MAAMJ,EAC/B,GACC,CAACA,EAASlG,IAEN,MAUHgF,EAAwDtI,IAAmC,IAAlC,WAAE2C,EAAA,cAAYY,GAAcvD,EACzF,MACMwJ,EAAU,6EADkBN,EARH,4BASwFE,gBAWvH,OATM9J,EAAAA,UAAU,KACd,MAAMuK,EAAgBlH,EAAWuD,SAAS4D,aAAa,oBAEvD,GAAIvG,GAAiBsG,EAAe,CACXJ,SAASC,eAAenG,IAC1BoG,QAAQI,KAAKP,EACpC,GACC,CAACA,EAAS7G,EAAYY,IAElB,MAGHyG,EAAOrJ,EACPsJ,EAAUpJ,EACVqJ,GAASpJ,EACTqJ,GAAUhJ,EACViJ,GAAUhJ,EACViJ,GAAQ5I,EACR6I,GAAc5I,EACd6I,GAAQzB,C,sICtiBd,MAAM0B,EAASC,EAAAA,GAITC,GAFcD,EAAAA,GAEAA,EAAAA,IAEdE,EAAgBrL,EAAAA,WAGpB,CAAAC,EAAoCC,KAAG,IAAtC,UAAEC,EAAS,SAAEG,KAAaF,GAAOH,EAAA,OAClC8B,EAAAA,EAAAA,MAACoJ,EAAAA,GAAuB,CACtBjL,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IACT,4RACAJ,MAEEC,EAAKE,SAAA,CAERA,GACDD,EAAAA,EAAAA,KAAC8K,EAAAA,GAAoB,CAAC3F,SAAO,EAAAlF,UAC3BD,EAAAA,EAAAA,KAACiL,EAAAA,EAAW,CAACnL,UAAU,8BAI7BkL,EAAc7K,YAAc2K,EAAAA,GAAwB3K,YAEpD,MAAM+K,EAAgBvL,EAAAA,WAGpB,CAAAU,EAAyDR,KAAG,IAA3D,UAAEC,EAAS,SAAEG,EAAQ,SAAEkL,EAAW,YAAapL,GAAOM,EAAA,OACvDL,EAAAA,EAAAA,KAAC8K,EAAAA,GAAsB,CAAA7K,UACrBD,EAAAA,EAAAA,KAAC8K,EAAAA,GAAuB,CACtBjL,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IACT,6bACa,WAAbiL,GACE,kIACFrL,GAEFqL,SAAUA,KACNpL,EAAKE,UAETD,EAAAA,EAAAA,KAAC8K,EAAAA,GAAwB,CACvBhL,WAAWI,EAAAA,EAAAA,IACT,MACa,WAAbiL,GACE,2FACFlL,SAEDA,UAKTiL,EAAc/K,YAAc2K,EAAAA,GAAwB3K,YAEhCR,EAAAA,WAGlB,CAAAY,EAA0BV,KAAG,IAA5B,UAAEC,KAAcC,GAAOQ,EAAA,OACxBP,EAAAA,EAAAA,KAAC8K,EAAAA,GAAqB,CACpBjL,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IAAG,yCAA0CJ,MACpDC,MAGII,YAAc2K,EAAAA,GAAsB3K,YAEhD,MAAMiL,EAAazL,EAAAA,WAGjB,CAAAa,EAAoCX,KAAG,IAAtC,UAAEC,EAAS,SAAEG,KAAaF,GAAOS,EAAA,OAClCkB,EAAAA,EAAAA,MAACoJ,EAAAA,GAAoB,CACnBjL,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IACT,4NACAJ,MAEEC,EAAKE,SAAA,EAETD,EAAAA,EAAAA,KAAA,QAAMF,UAAU,+DAA8DG,UAC5ED,EAAAA,EAAAA,KAAC8K,EAAAA,GAA6B,CAAA7K,UAC5BD,EAAAA,EAAAA,KAACqL,EAAAA,EAAK,CAACvL,UAAU,iBAIrBE,EAAAA,EAAAA,KAAC8K,EAAAA,GAAwB,CAAA7K,SAAEA,SAG/BmL,EAAWjL,YAAc2K,EAAAA,GAAqB3K,YAEtBR,EAAAA,WAGtB,CAAAe,EAA0Bb,KAAG,IAA5B,UAAEC,KAAcC,GAAOW,EAAA,OACxBV,EAAAA,EAAAA,KAAC8K,EAAAA,GAAyB,CACxBjL,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IAAG,2BAA4BJ,MACtCC,MAGQI,YAAc2K,EAAAA,GAA0B3K,W,0DC3FxD,IAAImL,EAAQ,EA+BZ,MAAMC,EAAgB,IAAIC,IAEpBC,EAAoBC,IACxB,GAAIH,EAAcI,IAAID,GACpB,OAGF,MAAME,EAAUC,WAAW,KACzBN,EAAcO,OAAOJ,GACrBK,EAAS,CACPxH,KAAM,eACNmH,QAASA,KAEV,KAEHH,EAAcS,IAAIN,EAASE,IAGhBK,EAAUA,CAACC,EAAcC,KACpC,OAAQA,EAAO5H,MACb,IAAK,YACH,MAAO,IACF2H,EACHE,OAAQ,CAACD,EAAOE,SAAUH,EAAME,QAAQE,MAAM,EAAG,IAGrD,IAAK,eACH,MAAO,IACFJ,EACHE,OAAQF,EAAME,OAAOtH,IAAKyH,GACxBA,EAAE/D,KAAO2D,EAAOE,MAAM7D,GAAK,IAAK+D,KAAMJ,EAAOE,OAAUE,IAI7D,IAAK,gBAAiB,CACpB,MAAM,QAAEb,GAAYS,EAYpB,OARIT,EACFD,EAAiBC,GAEjBQ,EAAME,OAAOI,QAASH,IACpBZ,EAAiBY,EAAM7D,MAIpB,IACF0D,EACHE,OAAQF,EAAME,OAAOtH,IAAKyH,GACxBA,EAAE/D,KAAOkD,QAAuBe,IAAZf,EAChB,IACKa,EACH7J,MAAM,GAER6J,GAGV,CACA,IAAK,eACH,YAAuBE,IAAnBN,EAAOT,QACF,IACFQ,EACHE,OAAQ,IAGL,IACFF,EACHE,OAAQF,EAAME,OAAOM,OAAQH,GAAMA,EAAE/D,KAAO2D,EAAOT,YAKrDiB,EAA2C,GAEjD,IAAIC,EAAqB,CAAER,OAAQ,IAEnC,SAASL,EAASI,GAChBS,EAAcX,EAAQW,EAAaT,GACnCQ,EAAUH,QAASK,IACjBA,EAASD,IAEb,CAIA,SAASP,EAAKzM,GAAuB,OAAjBG,GAAcH,EAChC,MAAM4I,GAnHN8C,GAASA,EAAQ,GAAKwB,OAAOC,iBACtBzB,EAAM0B,YAyHPC,EAAUA,IAAMlB,EAAS,CAAExH,KAAM,gBAAiBmH,QAASlD,IAcjE,OAZAuD,EAAS,CACPxH,KAAM,YACN8H,MAAO,IACFtM,EACHyI,KACA9F,MAAM,EACNG,aAAeH,IACRA,GAAMuK,QAKV,CACLzE,GAAIA,EACJyE,UACAC,OAtBcnN,GACdgM,EAAS,CACPxH,KAAM,eACN8H,MAAO,IAAKtM,EAAOyI,QAqBzB,CAEA,SAAS2E,IACP,MAAOjB,EAAOkB,GAAYzN,EAAAA,SAAsBiN,GAYhD,OAVAjN,EAAAA,UAAgB,KACdgN,EAAUU,KAAKD,GACR,KACL,MAAME,EAAQX,EAAUY,QAAQH,GAC5BE,GAAS,GACXX,EAAUa,OAAOF,EAAO,KAG3B,CAACpB,IAEG,IACFA,EACHG,QACAY,QAAUvB,GAAqBK,EAAS,CAAExH,KAAM,gBAAiBmH,YAErE,C,+LC3IA,MAAM+B,EAAUC,4BAEHC,EAA6BA,KACxC,MAAM,KAAEC,EAAI,OAAEC,IAAWC,EAAAA,EAAAA,KACnBC,EAAQC,aAAaC,QAAQ,UAC7B,MAAE5B,IAAUc,EAAAA,EAAAA,OACXe,EAAaC,IAAkBC,EAAAA,EAAAA,UAA6B,KAC5DC,EAASC,IAAcF,EAAAA,EAAAA,UAAyB,KAChDG,EAAWC,IAAgBJ,EAAAA,EAAAA,UAAyB,KACpDK,EAASC,IAAcN,EAAAA,EAAAA,WAAS,IAChCO,EAAcC,IAAmBR,EAAAA,EAAAA,WAAS,IAC1CS,EAAUC,IAAeV,EAAAA,EAAAA,UAAS,CACvCW,WAAY,GACZC,SAAU,GACVC,WAAWC,EAAAA,EAAAA,GAAO,IAAIC,KAAQ,cAC9BC,QAAS,GACTC,MAAO,KAGHC,EAAU,CACd,eAAgB,sBACZvB,EAAQ,CAAEwB,cAAe,UAAUxB,KAAY,CAAC,IAGtDyB,EAAAA,EAAAA,WAAU,KACRC,IACAC,KACC,IAEH,MAAMD,EAAYE,UAChB,IACE,MAAOC,SAAwBC,QAAQC,IAAI,CACzCC,EAAAA,EAAMC,IAAI,GAAGvC,uBAA8B,CAAE6B,cAE/CnB,EAAeyB,EAAeK,KAAKA,KACrC,CAAE,MAAOhG,GACPD,QAAQC,MAAM,uBAAwBA,GACtCoC,EAAM,CACJ6D,MAAO,QACPC,YAAa,uBACbC,QAAS,eAEb,CAAC,QACC1B,GAAW,EACb,GAGIgB,EAAaC,UACjB,IACE,MAAOU,EAAYC,SAAsBT,QAAQC,IAAI,CACnDC,EAAAA,EAAMC,IAAI,GAAGvC,4BAAmC,CAAE6B,YAClDS,EAAAA,EAAMC,IAAI,GAAGvC,8BAAqC,CAAE6B,cAEtDhB,EAAW+B,EAAWJ,KAAKA,MAC3BzB,EAAa8B,EAAaL,KAAKA,KACjC,CAAE,MAAOhG,GACPD,QAAQC,MAAM,wBAAyBA,GACvCoC,EAAM,CACJ6D,MAAO,QACPC,YAAa,wBACbC,QAAS,eAEb,GAiEIG,EAAe3C,GACdA,EACe,kBAATA,EAA0B,aAC7BA,EAAsB4C,MAAS5C,EAAsB6C,OAAS,UAFpD,UAKdC,EAAmB9C,IACvB,IAAKA,EAAM,MAAO,UAClB,GAAoB,kBAATA,EAAmB,MAAO,aACrC,MAAM+C,EAAW/C,EACjB,OAAO+C,EAASC,cAAgBD,EAASH,MAAQG,EAASF,OAAS,WAGrE,OAAIhC,GACKzO,EAAAA,EAAAA,KAAA,OAAAC,SAAK,gBAIZyB,EAAAA,EAAAA,MAAA,OAAK5B,UAAU,wBAAuBG,SAAA,EACpCyB,EAAAA,EAAAA,MAAA,OAAK5B,UAAU,yCAAwCG,SAAA,EACrDD,EAAAA,EAAAA,KAAA,MAAIF,UAAU,qBAAoBG,SAAC,uBACnCyB,EAAAA,EAAAA,MAACV,EAAAA,GAAM,CAAC0B,KAAMiM,EAAc9L,aAAc+L,EAAgB3O,SAAA,EACxDD,EAAAA,EAAAA,KAACkB,EAAAA,GAAa,CAACiE,SAAO,EAAAlF,UACpBD,EAAAA,EAAAA,KAAC6Q,EAAAA,EAAM,CAAA5Q,SAAC,qBAEVyB,EAAAA,EAAAA,MAACD,EAAAA,GAAa,CAAAxB,SAAA,EACZD,EAAAA,EAAAA,KAAC4B,EAAAA,GAAY,CAAA3B,UACXD,EAAAA,EAAAA,KAAC8B,EAAAA,GAAW,CAAA7B,SAAC,iCAEfyB,EAAAA,EAAAA,MAAA,QAAMoP,SA3FKnB,UACnBoB,EAAEjK,iBACF,UACQiJ,EAAAA,EAAMiB,KACV,GAAGvD,uBACH,IACKoB,EACHI,UAAW,IAAIE,KAAKN,EAASI,WAAWgC,cACxC7B,QAASP,EAASO,QAAU,IAAID,KAAKN,EAASO,SAAS6B,mBAAgBxE,GAEzE,CAAE6C,YAGJjD,EAAM,CACJ6D,MAAO,UACPC,YAAa,iCAGfvB,GAAgB,GAChBE,EAAY,CACVC,WAAY,GACZC,SAAU,GACVC,WAAWC,EAAAA,EAAAA,GAAO,IAAIC,KAAQ,cAC9BC,QAAS,GACTC,MAAO,KAGTI,GACF,CAAE,MAAOxF,GACPD,QAAQC,MAAM,6BAA8BA,GAC5CoC,EAAM,CACJ6D,MAAO,QACPC,YAAa,0BACbC,QAAS,eAEb,GAwDsCtQ,UAAU,YAAWG,SAAA,EACjDyB,EAAAA,EAAAA,MAAA,OAAK5B,UAAU,YAAWG,SAAA,EACxBD,EAAAA,EAAAA,KAACkC,EAAAA,EAAK,CAACgP,QAAQ,aAAYjR,SAAC,cAC5ByB,EAAAA,EAAAA,MAACmJ,EAAAA,GAAM,CACLsG,MAAOtC,EAASE,WAChBqC,cAAgBD,GAAUrC,EAAY,IAAKD,EAAUE,WAAYoC,IACjEE,UAAQ,EAAApR,SAAA,EAERD,EAAAA,EAAAA,KAACgL,EAAAA,GAAa,CAAA/K,UACZD,EAAAA,EAAAA,KAAC+K,EAAAA,GAAW,CAACuG,YAAY,yBAE3BtR,EAAAA,EAAAA,KAACkL,EAAAA,GAAa,CAAAjL,SACXsO,EAAUzJ,IAAKyM,IACdvR,EAAAA,EAAAA,KAACoL,EAAAA,GAAU,CAAoB+F,MAAOI,EAASC,KAAO,GAAGvR,SACtDyQ,EAAgBa,IADFA,EAASC,eAQlC9P,EAAAA,EAAAA,MAAA,OAAK5B,UAAU,YAAWG,SAAA,EACxBD,EAAAA,EAAAA,KAACkC,EAAAA,EAAK,CAACgP,QAAQ,WAAUjR,SAAC,YAC1ByB,EAAAA,EAAAA,MAACmJ,EAAAA,GAAM,CACLsG,MAAOtC,EAASG,SAChBoC,cAAgBD,GAAUrC,EAAY,IAAKD,EAAUG,SAAUmC,IAC/DE,UAAQ,EAAApR,SAAA,EAERD,EAAAA,EAAAA,KAACgL,EAAAA,GAAa,CAAA/K,UACZD,EAAAA,EAAAA,KAAC+K,EAAAA,GAAW,CAACuG,YAAY,uBAE3BtR,EAAAA,EAAAA,KAACkL,EAAAA,GAAa,CAAAjL,SACXoO,EAAQvJ,IAAK2M,IACZzR,EAAAA,EAAAA,KAACoL,EAAAA,GAAU,CAAkB+F,MAAOM,EAAOD,KAAO,GAAGvR,SACjDwR,EAAgBjB,MAASiB,EAAgBhB,OAAS,kBADrCgB,EAAOD,eAQhC9P,EAAAA,EAAAA,MAAA,OAAK5B,UAAU,yBAAwBG,SAAA,EACrCyB,EAAAA,EAAAA,MAAA,OAAK5B,UAAU,YAAWG,SAAA,EACxBD,EAAAA,EAAAA,KAACkC,EAAAA,EAAK,CAACgP,QAAQ,YAAWjR,SAAC,gBAC3BD,EAAAA,EAAAA,KAAC0R,EAAAA,EAAK,CACJnN,KAAK,OACLiE,GAAG,YACH2I,MAAOtC,EAASI,UAChB5L,SAAW0N,GAAMjC,EAAY,IAAKD,EAAUI,UAAW8B,EAAErJ,OAAOyJ,QAChEE,UAAQ,QAGZ3P,EAAAA,EAAAA,MAAA,OAAK5B,UAAU,YAAWG,SAAA,EACxBD,EAAAA,EAAAA,KAACkC,EAAAA,EAAK,CAACgP,QAAQ,UAASjR,SAAC,yBACzBD,EAAAA,EAAAA,KAAC0R,EAAAA,EAAK,CACJnN,KAAK,OACLiE,GAAG,UACH2I,MAAOtC,EAASO,QAChB/L,SAAW0N,GAAMjC,EAAY,IAAKD,EAAUO,QAAS2B,EAAErJ,OAAOyJ,QAC9DQ,IAAK9C,EAASI,mBAKpBvN,EAAAA,EAAAA,MAAA,OAAK5B,UAAU,YAAWG,SAAA,EACxBD,EAAAA,EAAAA,KAACkC,EAAAA,EAAK,CAACgP,QAAQ,QAAOjR,SAAC,sBACvBD,EAAAA,EAAAA,KAAC0R,EAAAA,EAAK,CACJlJ,GAAG,QACH2I,MAAOtC,EAASQ,MAChBhM,SAAW0N,GAAMjC,EAAY,IAAKD,EAAUQ,MAAO0B,EAAErJ,OAAOyJ,QAC5DG,YAAY,+CAIhB5P,EAAAA,EAAAA,MAAA,OAAK5B,UAAU,kCAAiCG,SAAA,EAC9CD,EAAAA,EAAAA,KAAC6Q,EAAAA,EAAM,CACLtM,KAAK,SACL6L,QAAQ,UACR3L,QAASA,IAAMmK,GAAgB,GAAO3O,SACvC,YAGDD,EAAAA,EAAAA,KAAC6Q,EAAAA,EAAM,CAACtM,KAAK,SAAQtE,SAAC,kCAOhCD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oBAAmBG,UAChCyB,EAAAA,EAAAA,MAAChC,EAAAA,GAAK,CAAAO,SAAA,EACJD,EAAAA,EAAAA,KAACI,EAAAA,GAAW,CAAAH,UACVyB,EAAAA,EAAAA,MAACjB,EAAAA,GAAQ,CAAAR,SAAA,EACPD,EAAAA,EAAAA,KAACW,EAAAA,GAAS,CAAAV,SAAC,cACXD,EAAAA,EAAAA,KAACW,EAAAA,GAAS,CAAAV,SAAC,YACXD,EAAAA,EAAAA,KAACW,EAAAA,GAAS,CAAAV,SAAC,gBACXD,EAAAA,EAAAA,KAACW,EAAAA,GAAS,CAAAV,SAAC,cACXD,EAAAA,EAAAA,KAACW,EAAAA,GAAS,CAAAV,SAAC,YACXD,EAAAA,EAAAA,KAACW,EAAAA,GAAS,CAAAV,SAAC,kBAGfD,EAAAA,EAAAA,KAACM,EAAAA,GAAS,CAAAL,SACgB,IAAvBiO,EAAY0D,QACX5R,EAAAA,EAAAA,KAACS,EAAAA,GAAQ,CAAAR,UACPD,EAAAA,EAAAA,KAACa,EAAAA,GAAS,CAACgR,QAAS,EAAG/R,UAAU,mBAAkBG,SAAC,2BAKtDiO,EAAYpJ,IAAKgN,IACfpQ,EAAAA,EAAAA,MAACjB,EAAAA,GAAQ,CAAAR,SAAA,EACPD,EAAAA,EAAAA,KAACa,EAAAA,GAAS,CAAAZ,SAAEsQ,EAAYuB,EAAWP,aACnCvR,EAAAA,EAAAA,KAACa,EAAAA,GAAS,CAAAZ,SAAEsQ,EAAYuB,EAAWL,WACnCzR,EAAAA,EAAAA,KAACa,EAAAA,GAAS,CAAAZ,UAAEiP,EAAAA,EAAAA,GAAO,IAAIC,KAAK2C,EAAW7C,WAAY,kBACnDjP,EAAAA,EAAAA,KAACa,EAAAA,GAAS,CAAAZ,SACP6R,EAAW1C,SAAUF,EAAAA,EAAAA,GAAO,IAAIC,KAAK2C,EAAW1C,SAAU,eAAiB,SAE9EpP,EAAAA,EAAAA,KAACa,EAAAA,GAAS,CAAAZ,UACRD,EAAAA,EAAAA,KAAA,QACEF,UAAW,mCACa,WAAtBgS,EAAWC,OACP,8BACsB,cAAtBD,EAAWC,OACX,4BACA,6BACH9R,SAEF6R,EAAWC,OAAOC,OAAO,GAAGC,cAAgBH,EAAWC,OAAOzF,MAAM,QAGzEtM,EAAAA,EAAAA,KAACa,EAAAA,GAAS,CAAAZ,SACe,WAAtB6R,EAAWC,SACV/R,EAAAA,EAAAA,KAAC6Q,EAAAA,EAAM,CACLT,QAAQ,UACR8B,KAAK,KACLpS,UAAU,OACV2E,QAASA,IA7LAkL,OAAOnH,EAAYuJ,KAChD,UACQhC,EAAAA,EAAMoC,IACV,GAAG1E,wBAA8BjF,IACjC,CAAEuJ,UACF,CAAEzC,YAGJjD,EAAM,CACJ6D,MAAO,UACPC,YAAa,oCAGfV,GACF,CAAE,MAAOxF,GACPD,QAAQC,MAAM,6BAA8BA,GAC5CoC,EAAM,CACJ6D,MAAO,QACPC,YAAa,8BACbC,QAAS,eAEb,GAwKmCgC,CAAuBN,EAAWN,IAAK,aAAavR,SACpE,sBA3BQ6R,EAAWN,iBA0C1C,G,sFCvVA,MAAMa,GAAiBpQ,EAAAA,EAAAA,GACrB,yRACA,CACEqQ,SAAU,CACRlC,QAAS,CACPmC,QAAS,yDACTC,YACE,qEACFC,QACE,iFACFC,UACE,+DACFC,MAAO,+CACPC,KAAM,mDAERV,KAAM,CACJK,QAAS,iBACTM,GAAI,sBACJC,GAAI,uBACJC,KAAM,cAGVC,gBAAiB,CACf5C,QAAS,UACT8B,KAAM,aAWNrB,EAASlR,EAAAA,WACb,CAAAC,EAA0DC,KAAS,IAAlE,UAAEC,EAAS,QAAEsQ,EAAO,KAAE8B,EAAI,QAAE/M,GAAU,KAAUpF,GAAOH,EACtD,MAAMqT,EAAO9N,EAAUK,EAAAA,GAAO,SAC9B,OACExF,EAAAA,EAAAA,KAACiT,EAAI,CACHnT,WAAWI,EAAAA,EAAAA,IAAGmS,EAAe,CAAEjC,UAAS8B,OAAMpS,eAC9CD,IAAKA,KACDE,MAKZ8Q,EAAO1Q,YAAc,Q,mEC9CrB,MAAMuR,EAAQ/R,EAAAA,WACZ,CAAAC,EAAgCC,KAAS,IAAxC,UAAEC,EAAS,KAAEyE,KAASxE,GAAOH,EAC5B,OACEI,EAAAA,EAAAA,KAAA,SACEuE,KAAMA,EACNzE,WAAWI,EAAAA,EAAAA,IACT,+VACAJ,GAEFD,IAAKA,KACDE,MAKZ2R,EAAMvR,YAAc,O", "sources": ["components/ui/table.tsx", "components/ui/dialog.tsx", "components/ui/label.tsx", "../node_modules/@radix-ui/react-dialog/src/dialog.tsx", "components/ui/select.tsx", "components/ui/use-toast.ts", "components/TraderManagement.tsx", "components/ui/button.tsx", "components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst Table = React.forwardRef<\n  HTMLTableElement,\n  React.HTMLAttributes<HTMLTableElement>\n>(({ className, ...props }, ref) => (\n  <div className=\"w-full overflow-auto\">\n    <table\n      ref={ref}\n      className={cn(\"w-full caption-bottom text-sm\", className)}\n      {...props}\n    />\n  </div>\n))\nTable.displayName = \"Table\"\n\nconst TableHeader = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <thead ref={ref} className={cn(\"[&_tr]:border-b\", className)} {...props} />\n))\nTableHeader.displayName = \"TableHeader\"\n\nconst TableBody = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tbody\n    ref={ref}\n    className={cn(\"[&_tr:last-child]:border-0\", className)}\n    {...props}\n  />\n))\nTableBody.displayName = \"TableBody\"\n\nconst TableFooter = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tfoot\n    ref={ref}\n    className={cn(\"bg-primary font-medium text-primary-foreground\", className)}\n    {...props}\n  />\n))\nTableFooter.displayName = \"TableFooter\"\n\nconst TableRow = React.forwardRef<\n  HTMLTableRowElement,\n  React.HTMLAttributes<HTMLTableRowElement>\n>(({ className, ...props }, ref) => (\n  <tr\n    ref={ref}\n    className={cn(\n      \"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nTableRow.displayName = \"TableRow\"\n\nconst TableHead = React.forwardRef<\n  HTMLTableCellElement,\n  React.ThHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <th\n    ref={ref}\n    className={cn(\n      \"h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0\",\n      className\n    )}\n    {...props}\n  />\n))\nTableHead.displayName = \"TableHead\"\n\nconst TableCell = React.forwardRef<\n  HTMLTableCellElement,\n  React.TdHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <td\n    ref={ref}\n    className={cn(\"p-4 align-middle [&:has([role=checkbox])]:pr-0\", className)}\n    {...props}\n  />\n))\nTableCell.displayName = \"TableCell\"\n\nconst TableCaption = React.forwardRef<\n  HTMLTableCaptionElement,\n  React.HTMLAttributes<HTMLTableCaptionElement>\n>(({ className, ...props }, ref) => (\n  <caption\n    ref={ref}\n    className={cn(\"mt-4 text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nTableCaption.displayName = \"TableCaption\"\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n", "import * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { X } from \"lucide-react\"\nimport { cn } from \"../../lib/utils\"\n\nconst Dialog = DialogPrimitive.Root\n\nconst DialogTrigger = DialogPrimitive.Trigger\n\ninterface DialogPortalProps extends DialogPrimitive.DialogPortalProps {\n  className?: string;\n  children?: React.ReactNode;\n}\n\nconst DialogPortal = ({\n  className,\n  children,\n  ...props\n}: DialogPortalProps) => {\n  const { container, forceMount, ...restProps } = props;\n  const portalProps = { \n    container,\n    forceMount: forceMount as true | undefined \n  };\n  return (\n    <DialogPrimitive.Portal {...portalProps}>\n      <div className={cn(\n        \"fixed inset-0 z-50 flex items-start justify-center sm:items-center\",\n        className\n      )}>\n        {children}\n      </div>\n    </DialogPrimitive.Portal>\n  )\n}\nDialogPortal.displayName = DialogPrimitive.Portal.displayName\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/50 backdrop-blur-sm transition-all duration-100 data-[state=closed]:animate-out data-[state=closed]:fade-out data-[state=open]:fade-in\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed z-50 grid w-full gap-4 rounded-b-lg bg-white p-6 animate-in data-[state=open]:fade-in-90 data-[state=open]:slide-in-from-bottom-10 sm:max-w-lg sm:rounded-lg sm:zoom-in-90 data-[state=open]:sm:slide-in-from-bottom-0\",\n        \"dark:bg-gray-900\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-gray-100 dark:focus:ring-gray-400 dark:focus:ring-offset-gray-900 dark:data-[state=open]:bg-gray-800\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n))\nDialogContent.displayName = DialogPrimitive.Content.displayName\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold text-gray-900\",\n      \"dark:text-gray-50\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = DialogPrimitive.Title.displayName\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-gray-500\", \"dark:text-gray-400\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = DialogPrimitive.Description.displayName\n\nexport {\n  Dialog,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n}\n", "import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  HTMLLabelElement,\n  React.LabelHTMLAttributes<HTMLLabelElement> & VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <label\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = \"Label\"\n\nexport { Label }\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContext, createContextScope } from '@radix-ui/react-context';\nimport { useId } from '@radix-ui/react-id';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { FocusScope } from '@radix-ui/react-focus-scope';\nimport { Portal as PortalPrimitive } from '@radix-ui/react-portal';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useFocusGuards } from '@radix-ui/react-focus-guards';\nimport { RemoveScroll } from 'react-remove-scroll';\nimport { hideOthers } from 'aria-hidden';\nimport { createSlot } from '@radix-ui/react-slot';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Dialog\n * -----------------------------------------------------------------------------------------------*/\n\nconst DIALOG_NAME = 'Dialog';\n\ntype ScopedProps<P> = P & { __scopeDialog?: Scope };\nconst [createDialogContext, createDialogScope] = createContextScope(DIALOG_NAME);\n\ntype DialogContextValue = {\n  triggerRef: React.RefObject<HTMLButtonElement | null>;\n  contentRef: React.RefObject<DialogContentElement | null>;\n  contentId: string;\n  titleId: string;\n  descriptionId: string;\n  open: boolean;\n  onOpenChange(open: boolean): void;\n  onOpenToggle(): void;\n  modal: boolean;\n};\n\nconst [DialogProvider, useDialogContext] = createDialogContext<DialogContextValue>(DIALOG_NAME);\n\ninterface DialogProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n  modal?: boolean;\n}\n\nconst Dialog: React.FC<DialogProps> = (props: ScopedProps<DialogProps>) => {\n  const {\n    __scopeDialog,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    modal = true,\n  } = props;\n  const triggerRef = React.useRef<HTMLButtonElement>(null);\n  const contentRef = React.useRef<DialogContentElement>(null);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: DIALOG_NAME,\n  });\n\n  return (\n    <DialogProvider\n      scope={__scopeDialog}\n      triggerRef={triggerRef}\n      contentRef={contentRef}\n      contentId={useId()}\n      titleId={useId()}\n      descriptionId={useId()}\n      open={open}\n      onOpenChange={setOpen}\n      onOpenToggle={React.useCallback(() => setOpen((prevOpen) => !prevOpen), [setOpen])}\n      modal={modal}\n    >\n      {children}\n    </DialogProvider>\n  );\n};\n\nDialog.displayName = DIALOG_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'DialogTrigger';\n\ntype DialogTriggerElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface DialogTriggerProps extends PrimitiveButtonProps {}\n\nconst DialogTrigger = React.forwardRef<DialogTriggerElement, DialogTriggerProps>(\n  (props: ScopedProps<DialogTriggerProps>, forwardedRef) => {\n    const { __scopeDialog, ...triggerProps } = props;\n    const context = useDialogContext(TRIGGER_NAME, __scopeDialog);\n    const composedTriggerRef = useComposedRefs(forwardedRef, context.triggerRef);\n    return (\n      <Primitive.button\n        type=\"button\"\n        aria-haspopup=\"dialog\"\n        aria-expanded={context.open}\n        aria-controls={context.contentId}\n        data-state={getState(context.open)}\n        {...triggerProps}\n        ref={composedTriggerRef}\n        onClick={composeEventHandlers(props.onClick, context.onOpenToggle)}\n      />\n    );\n  }\n);\n\nDialogTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'DialogPortal';\n\ntype PortalContextValue = { forceMount?: true };\nconst [PortalProvider, usePortalContext] = createDialogContext<PortalContextValue>(PORTAL_NAME, {\n  forceMount: undefined,\n});\n\ntype PortalProps = React.ComponentPropsWithoutRef<typeof PortalPrimitive>;\ninterface DialogPortalProps {\n  children?: React.ReactNode;\n  /**\n   * Specify a container element to portal the content into.\n   */\n  container?: PortalProps['container'];\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst DialogPortal: React.FC<DialogPortalProps> = (props: ScopedProps<DialogPortalProps>) => {\n  const { __scopeDialog, forceMount, children, container } = props;\n  const context = useDialogContext(PORTAL_NAME, __scopeDialog);\n  return (\n    <PortalProvider scope={__scopeDialog} forceMount={forceMount}>\n      {React.Children.map(children, (child) => (\n        <Presence present={forceMount || context.open}>\n          <PortalPrimitive asChild container={container}>\n            {child}\n          </PortalPrimitive>\n        </Presence>\n      ))}\n    </PortalProvider>\n  );\n};\n\nDialogPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogOverlay\n * -----------------------------------------------------------------------------------------------*/\n\nconst OVERLAY_NAME = 'DialogOverlay';\n\ntype DialogOverlayElement = DialogOverlayImplElement;\ninterface DialogOverlayProps extends DialogOverlayImplProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst DialogOverlay = React.forwardRef<DialogOverlayElement, DialogOverlayProps>(\n  (props: ScopedProps<DialogOverlayProps>, forwardedRef) => {\n    const portalContext = usePortalContext(OVERLAY_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, props.__scopeDialog);\n    return context.modal ? (\n      <Presence present={forceMount || context.open}>\n        <DialogOverlayImpl {...overlayProps} ref={forwardedRef} />\n      </Presence>\n    ) : null;\n  }\n);\n\nDialogOverlay.displayName = OVERLAY_NAME;\n\ntype DialogOverlayImplElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface DialogOverlayImplProps extends PrimitiveDivProps {}\n\nconst Slot = createSlot('DialogOverlay.RemoveScroll');\n\nconst DialogOverlayImpl = React.forwardRef<DialogOverlayImplElement, DialogOverlayImplProps>(\n  (props: ScopedProps<DialogOverlayImplProps>, forwardedRef) => {\n    const { __scopeDialog, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, __scopeDialog);\n    return (\n      // Make sure `Content` is scrollable even when it doesn't live inside `RemoveScroll`\n      // ie. when `Overlay` and `Content` are siblings\n      <RemoveScroll as={Slot} allowPinchZoom shards={[context.contentRef]}>\n        <Primitive.div\n          data-state={getState(context.open)}\n          {...overlayProps}\n          ref={forwardedRef}\n          // We re-enable pointer-events prevented by `Dialog.Content` to allow scrolling the overlay.\n          style={{ pointerEvents: 'auto', ...overlayProps.style }}\n        />\n      </RemoveScroll>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * DialogContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'DialogContent';\n\ntype DialogContentElement = DialogContentTypeElement;\ninterface DialogContentProps extends DialogContentTypeProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst DialogContent = React.forwardRef<DialogContentElement, DialogContentProps>(\n  (props: ScopedProps<DialogContentProps>, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    return (\n      <Presence present={forceMount || context.open}>\n        {context.modal ? (\n          <DialogContentModal {...contentProps} ref={forwardedRef} />\n        ) : (\n          <DialogContentNonModal {...contentProps} ref={forwardedRef} />\n        )}\n      </Presence>\n    );\n  }\n);\n\nDialogContent.displayName = CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype DialogContentTypeElement = DialogContentImplElement;\ninterface DialogContentTypeProps\n  extends Omit<DialogContentImplProps, 'trapFocus' | 'disableOutsidePointerEvents'> {}\n\nconst DialogContentModal = React.forwardRef<DialogContentTypeElement, DialogContentTypeProps>(\n  (props: ScopedProps<DialogContentTypeProps>, forwardedRef) => {\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const contentRef = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, context.contentRef, contentRef);\n\n    // aria-hide everything except the content (better supported equivalent to setting aria-modal)\n    React.useEffect(() => {\n      const content = contentRef.current;\n      if (content) return hideOthers(content);\n    }, []);\n\n    return (\n      <DialogContentImpl\n        {...props}\n        ref={composedRefs}\n        // we make sure focus isn't trapped once `DialogContent` has been closed\n        // (closed !== unmounted when animating out)\n        trapFocus={context.open}\n        disableOutsidePointerEvents\n        onCloseAutoFocus={composeEventHandlers(props.onCloseAutoFocus, (event) => {\n          event.preventDefault();\n          context.triggerRef.current?.focus();\n        })}\n        onPointerDownOutside={composeEventHandlers(props.onPointerDownOutside, (event) => {\n          const originalEvent = event.detail.originalEvent;\n          const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n          const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n\n          // If the event is a right-click, we shouldn't close because\n          // it is effectively as if we right-clicked the `Overlay`.\n          if (isRightClick) event.preventDefault();\n        })}\n        // When focus is trapped, a `focusout` event may still happen.\n        // We make sure we don't trigger our `onDismiss` in such case.\n        onFocusOutside={composeEventHandlers(props.onFocusOutside, (event) =>\n          event.preventDefault()\n        )}\n      />\n    );\n  }\n);\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst DialogContentNonModal = React.forwardRef<DialogContentTypeElement, DialogContentTypeProps>(\n  (props: ScopedProps<DialogContentTypeProps>, forwardedRef) => {\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const hasInteractedOutsideRef = React.useRef(false);\n    const hasPointerDownOutsideRef = React.useRef(false);\n\n    return (\n      <DialogContentImpl\n        {...props}\n        ref={forwardedRef}\n        trapFocus={false}\n        disableOutsidePointerEvents={false}\n        onCloseAutoFocus={(event) => {\n          props.onCloseAutoFocus?.(event);\n\n          if (!event.defaultPrevented) {\n            if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n            // Always prevent auto focus because we either focus manually or want user agent focus\n            event.preventDefault();\n          }\n\n          hasInteractedOutsideRef.current = false;\n          hasPointerDownOutsideRef.current = false;\n        }}\n        onInteractOutside={(event) => {\n          props.onInteractOutside?.(event);\n\n          if (!event.defaultPrevented) {\n            hasInteractedOutsideRef.current = true;\n            if (event.detail.originalEvent.type === 'pointerdown') {\n              hasPointerDownOutsideRef.current = true;\n            }\n          }\n\n          // Prevent dismissing when clicking the trigger.\n          // As the trigger is already setup to close, without doing so would\n          // cause it to close and immediately open.\n          const target = event.target as HTMLElement;\n          const targetIsTrigger = context.triggerRef.current?.contains(target);\n          if (targetIsTrigger) event.preventDefault();\n\n          // On Safari if the trigger is inside a container with tabIndex={0}, when clicked\n          // we will get the pointer down outside event on the trigger, but then a subsequent\n          // focus outside event on the container, we ignore any focus outside event when we've\n          // already had a pointer down outside event.\n          if (event.detail.originalEvent.type === 'focusin' && hasPointerDownOutsideRef.current) {\n            event.preventDefault();\n          }\n        }}\n      />\n    );\n  }\n);\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype DialogContentImplElement = React.ComponentRef<typeof DismissableLayer>;\ntype DismissableLayerProps = React.ComponentPropsWithoutRef<typeof DismissableLayer>;\ntype FocusScopeProps = React.ComponentPropsWithoutRef<typeof FocusScope>;\ninterface DialogContentImplProps extends Omit<DismissableLayerProps, 'onDismiss'> {\n  /**\n   * When `true`, focus cannot escape the `Content` via keyboard,\n   * pointer, or a programmatic focus.\n   * @defaultValue false\n   */\n  trapFocus?: FocusScopeProps['trapped'];\n\n  /**\n   * Event handler called when auto-focusing on open.\n   * Can be prevented.\n   */\n  onOpenAutoFocus?: FocusScopeProps['onMountAutoFocus'];\n\n  /**\n   * Event handler called when auto-focusing on close.\n   * Can be prevented.\n   */\n  onCloseAutoFocus?: FocusScopeProps['onUnmountAutoFocus'];\n}\n\nconst DialogContentImpl = React.forwardRef<DialogContentImplElement, DialogContentImplProps>(\n  (props: ScopedProps<DialogContentImplProps>, forwardedRef) => {\n    const { __scopeDialog, trapFocus, onOpenAutoFocus, onCloseAutoFocus, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, __scopeDialog);\n    const contentRef = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, contentRef);\n\n    // Make sure the whole tree has focus guards as our `Dialog` will be\n    // the last element in the DOM (because of the `Portal`)\n    useFocusGuards();\n\n    return (\n      <>\n        <FocusScope\n          asChild\n          loop\n          trapped={trapFocus}\n          onMountAutoFocus={onOpenAutoFocus}\n          onUnmountAutoFocus={onCloseAutoFocus}\n        >\n          <DismissableLayer\n            role=\"dialog\"\n            id={context.contentId}\n            aria-describedby={context.descriptionId}\n            aria-labelledby={context.titleId}\n            data-state={getState(context.open)}\n            {...contentProps}\n            ref={composedRefs}\n            onDismiss={() => context.onOpenChange(false)}\n          />\n        </FocusScope>\n        {process.env.NODE_ENV !== 'production' && (\n          <>\n            <TitleWarning titleId={context.titleId} />\n            <DescriptionWarning contentRef={contentRef} descriptionId={context.descriptionId} />\n          </>\n        )}\n      </>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * DialogTitle\n * -----------------------------------------------------------------------------------------------*/\n\nconst TITLE_NAME = 'DialogTitle';\n\ntype DialogTitleElement = React.ComponentRef<typeof Primitive.h2>;\ntype PrimitiveHeading2Props = React.ComponentPropsWithoutRef<typeof Primitive.h2>;\ninterface DialogTitleProps extends PrimitiveHeading2Props {}\n\nconst DialogTitle = React.forwardRef<DialogTitleElement, DialogTitleProps>(\n  (props: ScopedProps<DialogTitleProps>, forwardedRef) => {\n    const { __scopeDialog, ...titleProps } = props;\n    const context = useDialogContext(TITLE_NAME, __scopeDialog);\n    return <Primitive.h2 id={context.titleId} {...titleProps} ref={forwardedRef} />;\n  }\n);\n\nDialogTitle.displayName = TITLE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogDescription\n * -----------------------------------------------------------------------------------------------*/\n\nconst DESCRIPTION_NAME = 'DialogDescription';\n\ntype DialogDescriptionElement = React.ComponentRef<typeof Primitive.p>;\ntype PrimitiveParagraphProps = React.ComponentPropsWithoutRef<typeof Primitive.p>;\ninterface DialogDescriptionProps extends PrimitiveParagraphProps {}\n\nconst DialogDescription = React.forwardRef<DialogDescriptionElement, DialogDescriptionProps>(\n  (props: ScopedProps<DialogDescriptionProps>, forwardedRef) => {\n    const { __scopeDialog, ...descriptionProps } = props;\n    const context = useDialogContext(DESCRIPTION_NAME, __scopeDialog);\n    return <Primitive.p id={context.descriptionId} {...descriptionProps} ref={forwardedRef} />;\n  }\n);\n\nDialogDescription.displayName = DESCRIPTION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogClose\n * -----------------------------------------------------------------------------------------------*/\n\nconst CLOSE_NAME = 'DialogClose';\n\ntype DialogCloseElement = React.ComponentRef<typeof Primitive.button>;\ninterface DialogCloseProps extends PrimitiveButtonProps {}\n\nconst DialogClose = React.forwardRef<DialogCloseElement, DialogCloseProps>(\n  (props: ScopedProps<DialogCloseProps>, forwardedRef) => {\n    const { __scopeDialog, ...closeProps } = props;\n    const context = useDialogContext(CLOSE_NAME, __scopeDialog);\n    return (\n      <Primitive.button\n        type=\"button\"\n        {...closeProps}\n        ref={forwardedRef}\n        onClick={composeEventHandlers(props.onClick, () => context.onOpenChange(false))}\n      />\n    );\n  }\n);\n\nDialogClose.displayName = CLOSE_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getState(open: boolean) {\n  return open ? 'open' : 'closed';\n}\n\nconst TITLE_WARNING_NAME = 'DialogTitleWarning';\n\nconst [WarningProvider, useWarningContext] = createContext(TITLE_WARNING_NAME, {\n  contentName: CONTENT_NAME,\n  titleName: TITLE_NAME,\n  docsSlug: 'dialog',\n});\n\ntype TitleWarningProps = { titleId?: string };\n\nconst TitleWarning: React.FC<TitleWarningProps> = ({ titleId }) => {\n  const titleWarningContext = useWarningContext(TITLE_WARNING_NAME);\n\n  const MESSAGE = `\\`${titleWarningContext.contentName}\\` requires a \\`${titleWarningContext.titleName}\\` for the component to be accessible for screen reader users.\n\nIf you want to hide the \\`${titleWarningContext.titleName}\\`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/${titleWarningContext.docsSlug}`;\n\n  React.useEffect(() => {\n    if (titleId) {\n      const hasTitle = document.getElementById(titleId);\n      if (!hasTitle) console.error(MESSAGE);\n    }\n  }, [MESSAGE, titleId]);\n\n  return null;\n};\n\nconst DESCRIPTION_WARNING_NAME = 'DialogDescriptionWarning';\n\ntype DescriptionWarningProps = {\n  contentRef: React.RefObject<DialogContentElement | null>;\n  descriptionId?: string;\n};\n\nconst DescriptionWarning: React.FC<DescriptionWarningProps> = ({ contentRef, descriptionId }) => {\n  const descriptionWarningContext = useWarningContext(DESCRIPTION_WARNING_NAME);\n  const MESSAGE = `Warning: Missing \\`Description\\` or \\`aria-describedby={undefined}\\` for {${descriptionWarningContext.contentName}}.`;\n\n  React.useEffect(() => {\n    const describedById = contentRef.current?.getAttribute('aria-describedby');\n    // if we have an id and the user hasn't set aria-describedby={undefined}\n    if (descriptionId && describedById) {\n      const hasDescription = document.getElementById(descriptionId);\n      if (!hasDescription) console.warn(MESSAGE);\n    }\n  }, [MESSAGE, contentRef, descriptionId]);\n\n  return null;\n};\n\nconst Root = Dialog;\nconst Trigger = DialogTrigger;\nconst Portal = DialogPortal;\nconst Overlay = DialogOverlay;\nconst Content = DialogContent;\nconst Title = DialogTitle;\nconst Description = DialogDescription;\nconst Close = DialogClose;\n\nexport {\n  createDialogScope,\n  //\n  Dialog,\n  DialogTrigger,\n  DialogPortal,\n  DialogOverlay,\n  DialogContent,\n  DialogTitle,\n  DialogDescription,\n  DialogClose,\n  //\n  Root,\n  Trigger,\n  Portal,\n  Overlay,\n  Content,\n  Title,\n  Description,\n  Close,\n  //\n  WarningProvider,\n};\nexport type {\n  DialogProps,\n  DialogTriggerProps,\n  DialogPortalProps,\n  DialogOverlayProps,\n  DialogContentProps,\n  DialogTitleProps,\n  DialogDescriptionProps,\n  DialogCloseProps,\n};\n", "import * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown } from \"lucide-react\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n}\n", "import * as React from 'react';\nimport { ToastProps, ToastActionElement } from './toast';\n\ntype ToasterToast = ToastProps & {\n  id: string;\n  title?: React.ReactNode;\n  description?: React.ReactNode;\n};\n\nconst actionTypes = {\n  ADD_TOAST: 'ADD_TOAST',\n  UPDATE_TOAST: 'UPDATE_TOAST',\n  DISMISS_TOAST: 'DISMISS_TOAST',\n  REMOVE_TOAST: 'REMOVE_TOAST',\n} as const;\n\nlet count = 0;\n\nfunction genId() {\n  count = (count + 1) % Number.MAX_SAFE_INTEGER;\n  return count.toString();\n}\n\ntype ActionType = typeof actionTypes;\n\ntype Action =\n  | {\n      type: ActionType['ADD_TOAST'];\n      toast: ToasterToast;\n    }\n  | {\n      type: ActionType['UPDATE_TOAST'];\n      toast: Partial<ToasterToast>;\n    }\n  | {\n      type: ActionType['DISMISS_TOAST'];\n      toastId?: ToasterToast['id'];\n    }\n  | {\n      type: ActionType['REMOVE_TOAST'];\n      toastId?: ToasterToast['id'];\n    };\n\ninterface State {\n  toasts: ToasterToast[];\n}\n\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>();\n\nconst addToRemoveQueue = (toastId: string) => {\n  if (toastTimeouts.has(toastId)) {\n    return;\n  }\n\n  const timeout = setTimeout(() => {\n    toastTimeouts.delete(toastId);\n    dispatch({\n      type: 'REMOVE_TOAST',\n      toastId: toastId,\n    });\n  }, 1000);\n\n  toastTimeouts.set(toastId, timeout);\n};\n\nexport const reducer = (state: State, action: Action): State => {\n  switch (action.type) {\n    case 'ADD_TOAST':\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, 5),\n      };\n\n    case 'UPDATE_TOAST':\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\n        ),\n      };\n\n    case 'DISMISS_TOAST': {\n      const { toastId } = action;\n\n      // ! Side effects ! - This could be extracted into a dismissToast() action,\n      // but I'll keep it here for simplicity\n      if (toastId) {\n        addToRemoveQueue(toastId);\n      } else {\n        state.toasts.forEach((toast) => {\n          addToRemoveQueue(toast.id);\n        });\n      }\n\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                open: false,\n              }\n            : t\n        ),\n      };\n    }\n    case 'REMOVE_TOAST':\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        };\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\n      };\n  }\n};\n\nconst listeners: Array<(state: State) => void> = [];\n\nlet memoryState: State = { toasts: [] };\n\nfunction dispatch(action: Action) {\n  memoryState = reducer(memoryState, action);\n  listeners.forEach((listener) => {\n    listener(memoryState);\n  });\n}\n\ntype Toast = Omit<ToasterToast, 'id'>;\n\nfunction toast({ ...props }: Toast) {\n  const id = genId();\n\n  const update = (props: ToasterToast) =>\n    dispatch({\n      type: 'UPDATE_TOAST',\n      toast: { ...props, id },\n    });\n  const dismiss = () => dispatch({ type: 'DISMISS_TOAST', toastId: id });\n\n  dispatch({\n    type: 'ADD_TOAST',\n    toast: {\n      ...props,\n      id,\n      open: true,\n      onOpenChange: (open: boolean) => {\n        if (!open) dismiss();\n      },\n    },\n  });\n\n  return {\n    id: id,\n    dismiss,\n    update,\n  };\n}\n\nfunction useToast() {\n  const [state, setState] = React.useState<State>(memoryState);\n\n  React.useEffect(() => {\n    listeners.push(setState);\n    return () => {\n      const index = listeners.indexOf(setState);\n      if (index > -1) {\n        listeners.splice(index, 1);\n      }\n    };\n  }, [state]);\n\n  return {\n    ...state,\n    toast,\n    dismiss: (toastId?: string) => dispatch({ type: 'DISMISS_TOAST', toastId }),\n  };\n}\n\nexport { useToast, toast };\n", "import React, { useState, useEffect } from 'react';\nimport { useAuth, type User, type BaseUser } from '../contexts/AuthContext';\nimport axios from 'axios';\nimport { format } from 'date-fns';\nimport {\n  Table,\n  TableBody,\n  TableCell,\n  TableHead,\n  TableHeader,\n  TableRow,\n} from './ui/table';\nimport { Button } from './ui/button';\nimport { Input } from './ui/input';\nimport { Label } from './ui/label';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';\nimport { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog';\nimport { useToast } from './ui/use-toast';\n\n// Extended user type that includes all properties from BaseUser\ninterface ExtendedUser extends Omit<BaseUser, 'role'> {\n  _id: string;\n  role: User['role'];\n  businessName?: string;\n  name: string;\n  email: string;\n}\n\ninterface TraderAssignment {\n  _id: string;\n  merchant: ExtendedUser | string;\n  trader: ExtendedUser | string;\n  startDate: string;\n  endDate?: string;\n  status: 'active' | 'inactive' | 'completed' | 'pending';\n  notes?: string;\n  assignedBy: User | string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nconst API_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api/v1';\n\nexport const TraderManagement: React.FC = () => {\n  const { user, logout } = useAuth();\n  const token = localStorage.getItem('token');\n  const { toast } = useToast();\n  const [assignments, setAssignments] = useState<TraderAssignment[]>([]);\n  const [traders, setTraders] = useState<ExtendedUser[]>([]);\n  const [merchants, setMerchants] = useState<ExtendedUser[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [isDialogOpen, setIsDialogOpen] = useState(false);\n  const [formData, setFormData] = useState({\n    merchantId: '',\n    traderId: '',\n    startDate: format(new Date(), 'yyyy-MM-dd'),\n    endDate: '',\n    notes: '',\n  });\n\n  const headers = {\n    'Content-Type': 'application/json',\n    ...(token ? { Authorization: `Bearer ${token}` } : {}),\n  };\n\n  useEffect(() => {\n    fetchData();\n    fetchUsers();\n  }, []);\n\n  const fetchData = async () => {\n    try {\n      const [assignmentsRes] = await Promise.all([\n        axios.get(`${API_URL}/trader/assignments`, { headers }),\n      ]);\n      setAssignments(assignmentsRes.data.data);\n    } catch (error) {\n      console.error('Error fetching data:', error);\n      toast({\n        title: 'Error',\n        description: 'Failed to fetch data',\n        variant: 'destructive',\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchUsers = async () => {\n    try {\n      const [tradersRes, merchantsRes] = await Promise.all([\n        axios.get(`${API_URL}/admin/users?role=trader`, { headers }),\n        axios.get(`${API_URL}/admin/users?role=merchant`, { headers }),\n      ]);\n      setTraders(tradersRes.data.data);\n      setMerchants(merchantsRes.data.data);\n    } catch (error) {\n      console.error('Error fetching users:', error);\n      toast({\n        title: 'Error',\n        description: 'Failed to fetch users',\n        variant: 'destructive',\n      });\n    }\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    try {\n      await axios.post(\n        `${API_URL}/trader/assignments`,\n        {\n          ...formData,\n          startDate: new Date(formData.startDate).toISOString(),\n          endDate: formData.endDate ? new Date(formData.endDate).toISOString() : undefined,\n        },\n        { headers }\n      );\n      \n      toast({\n        title: 'Success',\n        description: 'Trader assigned successfully',\n      });\n      \n      setIsDialogOpen(false);\n      setFormData({\n        merchantId: '',\n        traderId: '',\n        startDate: format(new Date(), 'yyyy-MM-dd'),\n        endDate: '',\n        notes: '',\n      });\n      \n      fetchData();\n    } catch (error) {\n      console.error('Error creating assignment:', error);\n      toast({\n        title: 'Error',\n        description: 'Failed to assign trader',\n        variant: 'destructive',\n      });\n    }\n  };\n\n  const updateAssignmentStatus = async (id: string, status: string) => {\n    try {\n      await axios.put(\n        `${API_URL}/trader/assignments/${id}`,\n        { status },\n        { headers }\n      );\n      \n      toast({\n        title: 'Success',\n        description: 'Assignment updated successfully',\n      });\n      \n      fetchData();\n    } catch (error) {\n      console.error('Error updating assignment:', error);\n      toast({\n        title: 'Error',\n        description: 'Failed to update assignment',\n        variant: 'destructive',\n      });\n    }\n  };\n\n  const getUserName = (user: ExtendedUser | string | undefined) => {\n    if (!user) return 'Unknown';\n    if (typeof user === 'string') return 'Loading...';\n    return (user as ExtendedUser).name || (user as ExtendedUser).email || 'Unknown';\n  };\n\n  const getMerchantName = (user: ExtendedUser | string | undefined) => {\n    if (!user) return 'Unknown';\n    if (typeof user === 'string') return 'Loading...';\n    const userData = user as ExtendedUser;\n    return userData.businessName || userData.name || userData.email || 'Unknown';\n  };\n\n  if (loading) {\n    return <div>Loading...</div>;\n  }\n\n  return (\n    <div className=\"container mx-auto p-6\">\n      <div className=\"flex justify-between items-center mb-6\">\n        <h1 className=\"text-2xl font-bold\">Trader Management</h1>\n        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>\n          <DialogTrigger asChild>\n            <Button>Assign Trader</Button>\n          </DialogTrigger>\n          <DialogContent>\n            <DialogHeader>\n              <DialogTitle>Assign Trader to Merchant</DialogTitle>\n            </DialogHeader>\n            <form onSubmit={handleSubmit} className=\"space-y-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"merchantId\">Merchant</Label>\n                <Select\n                  value={formData.merchantId}\n                  onValueChange={(value) => setFormData({ ...formData, merchantId: value })}\n                  required\n                >\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Select a merchant\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {merchants.map((merchant) => (\n                      <SelectItem key={merchant._id} value={merchant._id || ''}>\n                        {getMerchantName(merchant)}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"traderId\">Trader</Label>\n                <Select\n                  value={formData.traderId}\n                  onValueChange={(value) => setFormData({ ...formData, traderId: value })}\n                  required\n                >\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Select a trader\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {traders.map((trader) => (\n                      <SelectItem key={trader._id} value={trader._id || ''}>\n                        {(trader as User).name || (trader as User).email || 'Unknown Trader'}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"startDate\">Start Date</Label>\n                  <Input\n                    type=\"date\"\n                    id=\"startDate\"\n                    value={formData.startDate}\n                    onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}\n                    required\n                  />\n                </div>\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"endDate\">End Date (Optional)</Label>\n                  <Input\n                    type=\"date\"\n                    id=\"endDate\"\n                    value={formData.endDate}\n                    onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}\n                    min={formData.startDate}\n                  />\n                </div>\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"notes\">Notes (Optional)</Label>\n                <Input\n                  id=\"notes\"\n                  value={formData.notes}\n                  onChange={(e) => setFormData({ ...formData, notes: e.target.value })}\n                  placeholder=\"Additional notes about this assignment\"\n                />\n              </div>\n\n              <div className=\"flex justify-end space-x-2 pt-4\">\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  onClick={() => setIsDialogOpen(false)}\n                >\n                  Cancel\n                </Button>\n                <Button type=\"submit\">Assign Trader</Button>\n              </div>\n            </form>\n          </DialogContent>\n        </Dialog>\n      </div>\n\n      <div className=\"rounded-md border\">\n        <Table>\n          <TableHeader>\n            <TableRow>\n              <TableHead>Merchant</TableHead>\n              <TableHead>Trader</TableHead>\n              <TableHead>Start Date</TableHead>\n              <TableHead>End Date</TableHead>\n              <TableHead>Status</TableHead>\n              <TableHead>Actions</TableHead>\n            </TableRow>\n          </TableHeader>\n          <TableBody>\n            {assignments.length === 0 ? (\n              <TableRow>\n                <TableCell colSpan={6} className=\"text-center py-4\">\n                  No assignments found\n                </TableCell>\n              </TableRow>\n            ) : (\n              assignments.map((assignment) => (\n                <TableRow key={assignment._id}>\n                  <TableCell>{getUserName(assignment.merchant)}</TableCell>\n                  <TableCell>{getUserName(assignment.trader)}</TableCell>\n                  <TableCell>{format(new Date(assignment.startDate), 'MMM d, yyyy')}</TableCell>\n                  <TableCell>\n                    {assignment.endDate ? format(new Date(assignment.endDate), 'MMM d, yyyy') : 'N/A'}\n                  </TableCell>\n                  <TableCell>\n                    <span\n                      className={`px-2 py-1 rounded-full text-xs ${\n                        assignment.status === 'active'\n                          ? 'bg-green-100 text-green-800'\n                          : assignment.status === 'completed'\n                          ? 'bg-blue-100 text-blue-800'\n                          : 'bg-gray-100 text-gray-800'\n                      }`}\n                    >\n                      {assignment.status.charAt(0).toUpperCase() + assignment.status.slice(1)}\n                    </span>\n                  </TableCell>\n                  <TableCell>\n                    {assignment.status === 'active' && (\n                      <Button\n                        variant=\"outline\"\n                        size=\"sm\"\n                        className=\"mr-2\"\n                        onClick={() => updateAssignmentStatus(assignment._id, 'completed')}\n                      >\n                        Mark Complete\n                      </Button>\n                    )}\n                  </TableCell>\n                </TableRow>\n              ))\n            )}\n          </TableBody>\n        </Table>\n      </div>\n    </div>\n  );\n};\n\nexport default TraderManagement;\n", "import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n", "import * as React from \"react\"\n\nimport { cn } from \"../../lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": ["Table", "React", "_ref", "ref", "className", "props", "_jsx", "children", "cn", "displayName", "TableHeader", "_ref2", "TableBody", "_ref3", "_ref4", "TableRow", "_ref5", "TableHead", "_ref6", "TableCell", "_ref7", "_ref8", "Dialog", "DialogPrimitive", "DialogTrigger", "DialogPortal", "container", "forceMount", "restProps", "portalProps", "DialogOverlay", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_jsxs", "X", "DialogHeader", "<PERSON><PERSON><PERSON><PERSON>er", "DialogTitle", "DialogDescription", "labelVariants", "cva", "Label", "DIALOG_NAME", "createDialogContext", "createDialogScope", "createContextScope", "Dialog<PERSON><PERSON>", "useDialogContext", "__scopeDialog", "open", "openProp", "defaultOpen", "onOpenChange", "modal", "triggerRef", "contentRef", "<PERSON><PERSON><PERSON>", "useControllableState", "prop", "defaultProp", "onChange", "caller", "jsx", "scope", "contentId", "useId", "titleId", "descriptionId", "onOpenToggle", "prevOpen", "TRIGGER_NAME", "forwardedRef", "triggerProps", "context", "composedTriggerRef", "useComposedRefs", "Primitive", "button", "type", "getState", "onClick", "composeEventHandlers", "PORTAL_NAME", "PortalProvider", "usePortalContext", "map", "child", "Presence", "present", "PortalPrimitive", "<PERSON><PERSON><PERSON><PERSON>", "OVERLAY_NAME", "portalContext", "overlayProps", "DialogOverlayImpl", "Slot", "createSlot", "RemoveScroll", "as", "allowPinchZoom", "shards", "div", "style", "pointerEvents", "CONTENT_NAME", "contentProps", "DialogContentModal", "DialogContentNonModal", "composedRefs", "content", "current", "hideOthers", "DialogContentImpl", "trapFocus", "disableOutsidePointerEvents", "onCloseAutoFocus", "event", "preventDefault", "focus", "onPointerDownOutside", "originalEvent", "detail", "ctrlLeftClick", "ctrl<PERSON>ey", "onFocusOutside", "hasInteractedOutsideRef", "hasPointerDownOutsideRef", "defaultPrevented", "onInteractOutside", "target", "targetIsTrigger", "contains", "onOpenAutoFocus", "useFocusGuards", "jsxs", "Fragment", "FocusScope", "loop", "trapped", "onMountAutoFocus", "onUnmountAutoFocus", "Dismissa<PERSON><PERSON><PERSON><PERSON>", "role", "id", "on<PERSON><PERSON><PERSON>", "TitleWarning", "DescriptionWarning", "TITLE_NAME", "titleProps", "h2", "DESCRIPTION_NAME", "descriptionProps", "p", "CLOSE_NAME", "DialogClose", "closeProps", "TITLE_WARNING_NAME", "WarningProvider", "useWarningContext", "createContext", "contentName", "<PERSON><PERSON><PERSON>", "docs<PERSON>lug", "titleWarningContext", "MESSAGE", "document", "getElementById", "console", "error", "describedById", "getAttribute", "warn", "Root", "<PERSON><PERSON>", "Portal", "Overlay", "Content", "Title", "Description", "Close", "Select", "SelectPrimitive", "SelectValue", "SelectTrigger", "ChevronDown", "SelectContent", "position", "SelectItem", "Check", "count", "toastTimeouts", "Map", "addToRemoveQueue", "toastId", "has", "timeout", "setTimeout", "delete", "dispatch", "set", "reducer", "state", "action", "toasts", "toast", "slice", "t", "for<PERSON>ach", "undefined", "filter", "listeners", "memoryState", "listener", "Number", "MAX_SAFE_INTEGER", "toString", "dismiss", "update", "useToast", "setState", "push", "index", "indexOf", "splice", "API_URL", "process", "TraderManagement", "user", "logout", "useAuth", "token", "localStorage", "getItem", "assignments", "setAssignments", "useState", "traders", "setTraders", "merchants", "setMerchants", "loading", "setLoading", "isDialogOpen", "setIsDialogOpen", "formData", "setFormData", "merchantId", "traderId", "startDate", "format", "Date", "endDate", "notes", "headers", "Authorization", "useEffect", "fetchData", "fetchUsers", "async", "assignmentsRes", "Promise", "all", "axios", "get", "data", "title", "description", "variant", "tradersRes", "merchantsRes", "getUserName", "name", "email", "getMerchantName", "userData", "businessName", "<PERSON><PERSON>", "onSubmit", "e", "post", "toISOString", "htmlFor", "value", "onValueChange", "required", "placeholder", "merchant", "_id", "trader", "Input", "min", "length", "colSpan", "assignment", "status", "char<PERSON>t", "toUpperCase", "size", "put", "updateAssignmentStatus", "buttonVariants", "variants", "default", "destructive", "outline", "secondary", "ghost", "link", "sm", "lg", "icon", "defaultVariants", "Comp"], "sourceRoot": ""}