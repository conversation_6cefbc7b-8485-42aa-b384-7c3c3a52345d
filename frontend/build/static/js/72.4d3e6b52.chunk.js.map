{"version": 3, "file": "static/js/72.4d3e6b52.chunk.js", "mappings": "iOAIA,MAAMA,EAAQC,EAAAA,WAGZ,CAAAC,EAA0BC,KAAG,IAA5B,UAAEC,KAAcC,GAAOH,EAAA,OACxBI,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uBAAsBG,UACnCD,EAAAA,EAAAA,KAAA,SACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IAAG,gCAAiCJ,MAC3CC,QAIVL,EAAMS,YAAc,QAEpB,MAAMC,EAAcT,EAAAA,WAGlB,CAAAU,EAA0BR,KAAG,IAA5B,UAAEC,KAAcC,GAAOM,EAAA,OACxBL,EAAAA,EAAAA,KAAA,SAAOH,IAAKA,EAAKC,WAAWI,EAAAA,EAAAA,IAAG,kBAAmBJ,MAAgBC,MAEpEK,EAAYD,YAAc,cAE1B,MAAMG,EAAYX,EAAAA,WAGhB,CAAAY,EAA0BV,KAAG,IAA5B,UAAEC,KAAcC,GAAOQ,EAAA,OACxBP,EAAAA,EAAAA,KAAA,SACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IAAG,6BAA8BJ,MACxCC,MAGRO,EAAUH,YAAc,YAEJR,EAAAA,WAGlB,CAAAa,EAA0BX,KAAG,IAA5B,UAAEC,KAAcC,GAAOS,EAAA,OACxBR,EAAAA,EAAAA,KAAA,SACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IAAG,iDAAkDJ,MAC5DC,MAGII,YAAc,cAE1B,MAAMM,EAAWd,EAAAA,WAGf,CAAAe,EAA0Bb,KAAG,IAA5B,UAAEC,KAAcC,GAAOW,EAAA,OACxBV,EAAAA,EAAAA,KAAA,MACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IACT,8EACAJ,MAEEC,MAGRU,EAASN,YAAc,WAEvB,MAAMQ,EAAYhB,EAAAA,WAGhB,CAAAiB,EAA0Bf,KAAG,IAA5B,UAAEC,KAAcC,GAAOa,EAAA,OACxBZ,EAAAA,EAAAA,KAAA,MACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IACT,mGACAJ,MAEEC,MAGRY,EAAUR,YAAc,YAExB,MAAMU,EAAYlB,EAAAA,WAGhB,CAAAmB,EAA0BjB,KAAG,IAA5B,UAAEC,KAAcC,GAAOe,EAAA,OACxBd,EAAAA,EAAAA,KAAA,MACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IAAG,iDAAkDJ,MAC5DC,MAGRc,EAAUV,YAAc,YAEHR,EAAAA,WAGnB,CAAAoB,EAA0BlB,KAAG,IAA5B,UAAEC,KAAcC,GAAOgB,EAAA,OACxBf,EAAAA,EAAAA,KAAA,WACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IAAG,qCAAsCJ,MAChDC,MAGKI,YAAc,c,+DClGrBa,GAAcrB,IAAAA,EAAAA,EAAAA,EAAAA,EAAAA,KAAc,UAAUsB,OAAOC,aAAe,MAAO,GACrEC,EAAQ,EAEZ,SAASC,EAAMC,GACb,MAAOC,EAAIC,GAAe5B,EAAAA,SAA6BqB,KAKvD,OAHAQ,EAAAA,EAAAA,GAAgB,KACTH,GAAiBE,EAAOE,GAAYA,GAAWC,OAAOP,OAC1D,CAACE,IACGA,IAAoBC,EAAK,SAASA,IAAO,GAClD,C,iKCDM,MAAAK,GAAWC,EAAAA,EAAAA,GAAiB,WAAY,CAC5C,CAAC,OAAQ,CAAEC,EAAG,4BAA6BC,IAAK,aCD5CC,GAAYH,EAAAA,EAAAA,GAAiB,YAAa,CAC9C,CAAC,OAAQ,CAAEI,GAAI,KAAMC,GAAI,KAAMC,GAAI,KAAMC,GAAI,KAAML,IAAK,WACxD,CAAC,OAAQ,CAAEE,GAAI,KAAMC,GAAI,KAAMC,GAAI,KAAMC,GAAI,IAAKL,IAAK,WACvD,CAAC,OAAQ,CAAEE,GAAI,IAAKC,GAAI,IAAKC,GAAI,KAAMC,GAAI,KAAML,IAAK,aCHlDM,GAAaR,EAAAA,EAAAA,GAAiB,aAAc,CAChD,CAAC,WAAY,CAAES,OAAQ,+BAAgCP,IAAK,WAC5D,CAAC,WAAY,CAAEO,OAAQ,kBAAmBP,IAAK,a,4LCf7CQ,EAAY,CAAC,OAAQ,SAAU,eAAgB,OACjDC,EAAa,CAAC,OAChB,SAASC,EAAQC,GAAgC,OAAOD,EAAU,mBAAqBE,QAAU,iBAAmBA,OAAOC,SAAW,SAAUF,GAAK,cAAcA,CAAG,EAAI,SAAUA,GAAK,OAAOA,GAAK,mBAAqBC,QAAUD,EAAEG,cAAgBF,QAAUD,IAAMC,OAAOG,UAAY,gBAAkBJ,CAAG,EAAGD,EAAQC,EAAI,CAC7T,SAASK,EAAyBC,EAAQC,GAAY,GAAc,MAAVD,EAAgB,MAAO,CAAC,EAAG,IAAkEjB,EAAKmB,EAAnEC,EACzF,SAAuCH,EAAQC,GAAY,GAAc,MAAVD,EAAgB,MAAO,CAAC,EAAG,IAAIG,EAAS,CAAC,EAAG,IAAK,IAAIpB,KAAOiB,EAAU,GAAII,OAAON,UAAUO,eAAeC,KAAKN,EAAQjB,GAAM,CAAE,GAAIkB,EAASM,QAAQxB,IAAQ,EAAG,SAAUoB,EAAOpB,GAAOiB,EAAOjB,EAAM,CAAI,OAAOoB,CAAQ,CADpLK,CAA8BR,EAAQC,GAAuB,GAAIG,OAAOK,sBAAuB,CAAE,IAAIC,EAAmBN,OAAOK,sBAAsBT,GAAS,IAAKE,EAAI,EAAGA,EAAIQ,EAAiBC,OAAQT,IAAOnB,EAAM2B,EAAiBR,GAAQD,EAASM,QAAQxB,IAAQ,GAAkBqB,OAAON,UAAUc,qBAAqBN,KAAKN,EAAQjB,KAAgBoB,EAAOpB,GAAOiB,EAAOjB,GAAQ,CAAE,OAAOoB,CAAQ,CAE3e,SAASU,IAAiS,OAApRA,EAAWT,OAAOU,OAASV,OAAOU,OAAOC,OAAS,SAAUZ,GAAU,IAAK,IAAID,EAAI,EAAGA,EAAIc,UAAUL,OAAQT,IAAK,CAAE,IAAIF,EAASgB,UAAUd,GAAI,IAAK,IAAInB,KAAOiB,EAAcI,OAAON,UAAUO,eAAeC,KAAKN,EAAQjB,KAAQoB,EAAOpB,GAAOiB,EAAOjB,GAAU,CAAE,OAAOoB,CAAQ,EAAUU,EAASI,MAAMC,KAAMF,UAAY,CAClV,SAASG,EAAQC,EAAGC,GAAK,IAAIC,EAAIlB,OAAOmB,KAAKH,GAAI,GAAIhB,OAAOK,sBAAuB,CAAE,IAAIf,EAAIU,OAAOK,sBAAsBW,GAAIC,IAAM3B,EAAIA,EAAE8B,OAAO,SAAUH,GAAK,OAAOjB,OAAOqB,yBAAyBL,EAAGC,GAAGK,UAAY,IAAKJ,EAAEK,KAAKV,MAAMK,EAAG5B,EAAI,CAAE,OAAO4B,CAAG,CAC9P,SAASM,EAAcR,GAAK,IAAK,IAAIC,EAAI,EAAGA,EAAIL,UAAUL,OAAQU,IAAK,CAAE,IAAIC,EAAI,MAAQN,UAAUK,GAAKL,UAAUK,GAAK,CAAC,EAAGA,EAAI,EAAIF,EAAQf,OAAOkB,IAAI,GAAIO,QAAQ,SAAUR,GAAKS,EAAgBV,EAAGC,EAAGC,EAAED,GAAK,GAAKjB,OAAO2B,0BAA4B3B,OAAO4B,iBAAiBZ,EAAGhB,OAAO2B,0BAA0BT,IAAMH,EAAQf,OAAOkB,IAAIO,QAAQ,SAAUR,GAAKjB,OAAO6B,eAAeb,EAAGC,EAAGjB,OAAOqB,yBAAyBH,EAAGD,GAAK,EAAI,CAAE,OAAOD,CAAG,CACtb,SAASc,EAAmBC,GAAO,OAInC,SAA4BA,GAAO,GAAIC,MAAMC,QAAQF,GAAM,OAAOG,EAAkBH,EAAM,CAJhDI,CAAmBJ,IAG7D,SAA0BK,GAAQ,GAAsB,qBAAX7C,QAAmD,MAAzB6C,EAAK7C,OAAOC,WAA2C,MAAtB4C,EAAK,cAAuB,OAAOJ,MAAMK,KAAKD,EAAO,CAHxFE,CAAiBP,IAEtF,SAAqCzC,EAAGiD,GAAU,IAAKjD,EAAG,OAAQ,GAAiB,kBAANA,EAAgB,OAAO4C,EAAkB5C,EAAGiD,GAAS,IAAIC,EAAIxC,OAAON,UAAU3B,SAASmC,KAAKZ,GAAGmD,MAAM,GAAI,GAAc,WAAND,GAAkBlD,EAAEG,cAAa+C,EAAIlD,EAAEG,YAAYiD,MAAM,GAAU,QAANF,GAAqB,QAANA,EAAa,OAAOR,MAAMK,KAAK/C,GAAI,GAAU,cAANkD,GAAqB,2CAA2CG,KAAKH,GAAI,OAAON,EAAkB5C,EAAGiD,EAAS,CAFjUK,CAA4Bb,IAC1H,WAAgC,MAAM,IAAIc,UAAU,uIAAyI,CAD3DC,EAAsB,CAKxJ,SAASZ,EAAkBH,EAAKgB,IAAkB,MAAPA,GAAeA,EAAMhB,EAAIxB,UAAQwC,EAAMhB,EAAIxB,QAAQ,IAAK,IAAIT,EAAI,EAAGkD,EAAO,IAAIhB,MAAMe,GAAMjD,EAAIiD,EAAKjD,IAAKkD,EAAKlD,GAAKiC,EAAIjC,GAAI,OAAOkD,CAAM,CAElL,SAASC,EAAkBlD,EAAQnD,GAAS,IAAK,IAAIkD,EAAI,EAAGA,EAAIlD,EAAM2D,OAAQT,IAAK,CAAE,IAAIoD,EAAatG,EAAMkD,GAAIoD,EAAW5B,WAAa4B,EAAW5B,aAAc,EAAO4B,EAAWC,cAAe,EAAU,UAAWD,IAAYA,EAAWE,UAAW,GAAMpD,OAAO6B,eAAe9B,EAAQsD,EAAeH,EAAWvE,KAAMuE,EAAa,CAAE,CAE5U,SAASI,EAAWpC,EAAG5B,EAAG0B,GAAK,OAAO1B,EAAIiE,EAAgBjE,GAC1D,SAAoCkE,EAAMtD,GAAQ,GAAIA,IAA2B,WAAlBb,EAAQa,IAAsC,oBAATA,GAAwB,OAAOA,EAAa,QAAa,IAATA,EAAmB,MAAM,IAAI2C,UAAU,4DAA+D,OAC1P,SAAgCW,GAAQ,QAAa,IAATA,EAAmB,MAAM,IAAIC,eAAe,6DAAgE,OAAOD,CAAM,CAD4FE,CAAuBF,EAAO,CADjOG,CAA2BzC,EAAG0C,IAA8BC,QAAQC,UAAUxE,EAAG0B,GAAK,GAAIuC,EAAgBrC,GAAGzB,aAAeH,EAAEuB,MAAMK,EAAGF,GAAK,CAG1M,SAAS4C,IAA8B,IAAM,IAAI1C,GAAK6C,QAAQrE,UAAUsE,QAAQ9D,KAAK2D,QAAQC,UAAUC,QAAS,GAAI,WAAa,GAAK,CAAE,MAAO7C,GAAI,CAAE,OAAQ0C,EAA4B,WAAuC,QAAS1C,CAAG,IAAM,CAClP,SAASqC,EAAgBjE,GAA+J,OAA1JiE,EAAkBvD,OAAOiE,eAAiBjE,OAAOkE,eAAevD,OAAS,SAAyBrB,GAAK,OAAOA,EAAE6E,WAAanE,OAAOkE,eAAe5E,EAAI,EAAUiE,EAAgBjE,EAAI,CAEnN,SAAS8E,EAAgB9E,EAAG+E,GAA6I,OAAxID,EAAkBpE,OAAOiE,eAAiBjE,OAAOiE,eAAetD,OAAS,SAAyBrB,EAAG+E,GAAsB,OAAjB/E,EAAE6E,UAAYE,EAAU/E,CAAG,EAAU8E,EAAgB9E,EAAG+E,EAAI,CACvM,SAAS3C,EAAgB4C,EAAK3F,EAAK4F,GAA4L,OAAnL5F,EAAM0E,EAAe1E,MAAiB2F,EAAOtE,OAAO6B,eAAeyC,EAAK3F,EAAK,CAAE4F,MAAOA,EAAOjD,YAAY,EAAM6B,cAAc,EAAMC,UAAU,IAAkBkB,EAAI3F,GAAO4F,EAAgBD,CAAK,CAC3O,SAASjB,EAAenC,GAAK,IAAIpB,EACjC,SAAsBoB,EAAGD,GAAK,GAAI,UAAY5B,EAAQ6B,KAAOA,EAAG,OAAOA,EAAG,IAAIF,EAAIE,EAAE3B,OAAOiF,aAAc,QAAI,IAAWxD,EAAG,CAAE,IAAIlB,EAAIkB,EAAEd,KAAKgB,EAAGD,GAAK,WAAY,GAAI,UAAY5B,EAAQS,GAAI,OAAOA,EAAG,MAAM,IAAI+C,UAAU,+CAAiD,CAAE,OAAQ,WAAa5B,EAAI1C,OAASkG,QAAQvD,EAAI,CADtRwD,CAAaxD,EAAG,UAAW,MAAO,UAAY7B,EAAQS,GAAKA,EAAIA,EAAI,EAAI,CAoBrG,IAAI6E,EAAoB,SAAUC,GACvC,SAASD,IACP,IAAIE,GAjCR,SAAyBC,EAAUC,GAAe,KAAMD,aAAoBC,GAAgB,MAAM,IAAIlC,UAAU,oCAAwC,CAkCpJmC,CAAgBlE,KAAM6D,GACtB,IAAK,IAAIM,EAAOrE,UAAUL,OAAQ2E,EAAO,IAAIlD,MAAMiD,GAAOE,EAAO,EAAGA,EAAOF,EAAME,IAC/ED,EAAKC,GAAQvE,UAAUuE,GAsDzB,OAnDAzD,EADAmD,EAAQvB,EAAWxC,KAAM6D,EAAM,GAAGS,OAAOF,IAClB,QAAS,CAC9BG,qBAAqB,EACrBC,YAAa,IAEf5D,EAAgBmD,EAAO,gCAAiC,SAAUS,EAAa/E,GAC7E,MAAO,GAAG6E,OAAO7E,EAAQ,OAAO6E,OAAOE,EAAc/E,EAAQ,KAC/D,GACAmB,EAAgBmD,EAAO,qBAAsB,SAAUtE,EAAQ+E,EAAaC,GAC1E,IAAIC,EAAaD,EAAME,OAAO,SAAUC,EAAKC,GAC3C,OAAOD,EAAMC,CACf,GAGA,IAAKH,EACH,OAAOX,EAAMe,8BAA8BN,EAAa/E,GAM1D,IAJA,IAAIvC,EAAQ6H,KAAKC,MAAMvF,EAASiF,GAC5BO,EAAexF,EAASiF,EACxBQ,EAAaV,EAAc/E,EAC3B0F,EAAc,GACTnG,EAAI,EAAGoG,EAAM,EAAGpG,EAAIyF,EAAMhF,OAAQ2F,GAAOX,EAAMzF,KAAMA,EAC5D,GAAIoG,EAAMX,EAAMzF,GAAKiG,EAAc,CACjCE,EAAc,GAAGb,OAAOtD,EAAmByD,EAAM9C,MAAM,EAAG3C,IAAK,CAACiG,EAAeG,IAC/E,KACF,CAEF,IAAIC,EAAaF,EAAY1F,OAAS,IAAM,EAAI,CAAC,EAAGyF,GAAc,CAACA,GACnE,MAAO,GAAGZ,OAAOtD,EAAmB6C,EAAKyB,OAAOb,EAAOvH,IAAS8D,EAAmBmE,GAAcE,GAAYE,IAAI,SAAUC,GACzH,MAAO,GAAGlB,OAAOkB,EAAM,KACzB,GAAGC,KAAK,KACV,GACA7E,EAAgBmD,EAAO,MAAM2B,EAAAA,EAAAA,IAAS,mBACtC9E,EAAgBmD,EAAO,UAAW,SAAU4B,GAC1C5B,EAAM6B,UAAYD,CACpB,GACA/E,EAAgBmD,EAAO,qBAAsB,WAC3CA,EAAM8B,SAAS,CACbtB,qBAAqB,IAEnBR,EAAMjI,MAAMgK,gBACd/B,EAAMjI,MAAMgK,gBAEhB,GACAlF,EAAgBmD,EAAO,uBAAwB,WAC7CA,EAAM8B,SAAS,CACbtB,qBAAqB,IAEnBR,EAAMjI,MAAMiK,kBACdhC,EAAMjI,MAAMiK,kBAEhB,GACOhC,CACT,CAEA,OArFF,SAAmBiC,EAAUC,GAAc,GAA0B,oBAAfA,GAA4C,OAAfA,EAAuB,MAAM,IAAIlE,UAAU,sDAAyDiE,EAASpH,UAAYM,OAAOgH,OAAOD,GAAcA,EAAWrH,UAAW,CAAED,YAAa,CAAE8E,MAAOuC,EAAU1D,UAAU,EAAMD,cAAc,KAAWnD,OAAO6B,eAAeiF,EAAU,YAAa,CAAE1D,UAAU,IAAc2D,GAAY3C,EAAgB0C,EAAUC,EAAa,CAoFjcE,CAAUtC,EAAMC,GA1FIG,EA2FAJ,EA3FyBuC,EAqXzC,CAAC,CACHvI,IAAK,2BACL4F,MAAO,SAAkC4C,EAAWC,GAClD,OAAID,EAAUE,cAAgBD,EAAUE,gBAC/B,CACLA,gBAAiBH,EAAUE,YAC3BE,UAAWJ,EAAUjI,OACrBsI,WAAYJ,EAAUG,WAGtBJ,EAAUjI,SAAWkI,EAAUG,UAC1B,CACLA,UAAWJ,EAAUjI,QAGlB,IACT,GACC,CACDP,IAAK,SACL4F,MAAO,SAAgBgB,EAAOvH,GAG5B,IAFA,IAAIyJ,EAAYlC,EAAMhF,OAAS,IAAM,EAAI,GAAG6E,OAAOtD,EAAmByD,GAAQ,CAAC,IAAMA,EACjFmC,EAAS,GACJ5H,EAAI,EAAGA,EAAI9B,IAAS8B,EAC3B4H,EAAS,GAAGtC,OAAOtD,EAAmB4F,GAAS5F,EAAmB2F,IAEpE,OAAOC,CACT,GACC,CACD/I,IAAK,gBACL4F,MAAO,SAAuBoD,EAAQ/K,GACpC,IAAIgL,EACJ,GAAkBpL,EAAAA,eAAqBmL,GACrCC,EAAuBpL,EAAAA,aAAmBmL,EAAQ/K,QAC7C,GAAIiL,IAAWF,GACpBC,EAAUD,EAAO/K,OACZ,CACL,IAAI+B,EAAM/B,EAAM+B,IACdmJ,EAAWnI,EAAyB/C,EAAOwC,GACzCzC,GAAYoL,EAAAA,EAAAA,GAAK,oBAAuC,mBAAXJ,EAAuBA,EAAOhL,UAAY,IAC3FiL,EAAuBpL,EAAAA,cAAoBwL,EAAAA,EAAKvH,EAAS,CACvD9B,IAAKA,GACJmJ,EAAU,CACXnL,UAAWA,IAEf,CACA,OAAOiL,CACT,KAna+BK,EA2FP,CAAC,CACzBtJ,IAAK,oBACL4F,MAAO,WACL,GAAKzD,KAAKlE,MAAMsL,kBAAhB,CAGA,IAAI5C,EAAcxE,KAAKqH,iBACvBrH,KAAK6F,SAAS,CACZrB,YAAaA,GAHf,CAKF,GACC,CACD3G,IAAK,qBACL4F,MAAO,WACL,GAAKzD,KAAKlE,MAAMsL,kBAAhB,CAGA,IAAI5C,EAAcxE,KAAKqH,iBACnB7C,IAAgBxE,KAAKsH,MAAM9C,aAC7BxE,KAAK6F,SAAS,CACZrB,YAAaA,GAJjB,CAOF,GACC,CACD3G,IAAK,iBACL4F,MAAO,WACL,IAAI8D,EAAWvH,KAAK4F,UACpB,IACE,OAAO2B,GAAYA,EAASF,gBAAkBE,EAASF,kBAAoB,CAC7E,CAAE,MAAOG,GACP,OAAO,CACT,CACF,GACC,CACD3J,IAAK,iBACL4F,MAAO,SAAwBgE,EAAUC,GACvC,GAAI1H,KAAKlE,MAAMsL,oBAAsBpH,KAAKsH,MAAM/C,oBAC9C,OAAO,KAET,IAAIoD,EAAc3H,KAAKlE,MACrBsC,EAASuJ,EAAYvJ,OACrBwJ,EAAQD,EAAYC,MACpBC,EAAQF,EAAYE,MACpBC,EAASH,EAAYG,OACrB9L,EAAW2L,EAAY3L,SACrB+L,GAAgBC,EAAAA,EAAAA,IAAchM,EAAUiM,EAAAA,GAC5C,IAAKF,EACH,OAAO,KAET,IAAIG,EAAqB,SAA4BC,EAAWC,GAC9D,MAAO,CACLC,EAAGF,EAAUE,EACbC,EAAGH,EAAUG,EACb7E,MAAO0E,EAAU1E,MACjB8E,UAAUC,EAAAA,EAAAA,IAAkBL,EAAUM,QAASL,GAEnD,EACIM,EAAgB,CAClBC,SAAUlB,EAAW,iBAAiBnD,OAAOoD,EAAY,KAAO,MAElE,OAAoBhM,EAAAA,cAAoBkN,EAAAA,EAAOF,EAAeX,EAAcxC,IAAI,SAAUsD,GACxF,OAAoBnN,EAAAA,aAAmBmN,EAAM,CAC3ChL,IAAK,OAAOyG,OAAOuE,EAAK/M,MAAMsM,SAC9BU,KAAM1K,EACNwJ,MAAOA,EACPC,MAAOA,EACPC,OAAQA,EACRI,mBAAoBA,GAExB,GACF,GACC,CACDrK,IAAK,aACL4F,MAAO,SAAoBgE,EAAUsB,EAASrB,GAE5C,GADwB1H,KAAKlE,MAAMsL,oBACTpH,KAAKsH,MAAM/C,oBACnC,OAAO,KAET,IAAIyE,EAAehJ,KAAKlE,MACtBmN,EAAMD,EAAaC,IACnB7K,EAAS4K,EAAa5K,OACtBgK,EAAUY,EAAaZ,QACrBc,GAAYC,EAAAA,EAAAA,IAAYnJ,KAAKlE,OAAO,GACpCsN,GAAiBD,EAAAA,EAAAA,IAAYF,GAAK,GAClCI,EAAOjL,EAAOmH,IAAI,SAAU+D,EAAOtK,GACrC,IAAIgI,EAAWtG,EAAcA,EAAcA,EAAc,CACvD7C,IAAK,OAAOyG,OAAOtF,GACnBmB,EAAG,GACF+I,GAAYE,GAAiB,CAAC,EAAG,CAClCG,MAAOvK,EACPwK,GAAIF,EAAMjB,EACVoB,GAAIH,EAAMhB,EACV7E,MAAO6F,EAAM7F,MACb2E,QAASA,EACTK,QAASa,EAAMb,QACfrK,OAAQA,IAEV,OAAOyF,EAAK6F,cAAcT,EAAKjC,EACjC,GACI2C,EAAY,CACdhB,SAAUlB,EAAW,iBAAiBnD,OAAOyE,EAAU,GAAK,SAASzE,OAAOoD,EAAY,KAAO,MAEjG,OAAoBhM,EAAAA,cAAoBkN,EAAAA,EAAOjJ,EAAS,CACtD9D,UAAW,qBACXgC,IAAK,QACJ8L,GAAYN,EACjB,GACC,CACDxL,IAAK,wBACL4F,MAAO,SAA+BrF,EAAQqJ,EAAUC,EAAY5L,GAClE,IAAI8N,EAAe5J,KAAKlE,MACtB+N,EAAOD,EAAaC,KACpB/B,EAAS8B,EAAa9B,OACtBgC,EAAeF,EAAaE,aAE5BC,GADMH,EAAahO,IACViD,EAAyB+K,EAAcvL,IAC9C2L,EAAatJ,EAAcA,EAAcA,EAAc,CAAC,GAAGyI,EAAAA,EAAAA,IAAYY,GAAQ,IAAQ,CAAC,EAAG,CAC7FE,KAAM,OACNpO,UAAW,sBACX8M,SAAUlB,EAAW,iBAAiBnD,OAAOoD,EAAY,KAAO,KAChEtJ,OAAQA,GACPtC,GAAQ,CAAC,EAAG,CACb+N,KAAMA,EACN/B,OAAQA,EACRgC,aAAcA,IAEhB,OAAoBpO,EAAAA,cAAoBwO,EAAAA,EAAOvK,EAAS,CAAC,EAAGqK,EAAY,CACtEG,QAASnK,KAAKmK,UAElB,GACC,CACDtM,IAAK,2BACL4F,MAAO,SAAkCgE,EAAUC,GACjD,IAAI0C,EAASpK,KACTqK,EAAerK,KAAKlE,MACtBsC,EAASiM,EAAajM,OACtBkM,EAAkBD,EAAaC,gBAC/BlD,EAAoBiD,EAAajD,kBACjCmD,EAAiBF,EAAaE,eAC9BC,EAAoBH,EAAaG,kBACjCC,EAAkBJ,EAAaI,gBAC/BlE,EAAc8D,EAAa9D,YAC3BmE,EAAmBL,EAAaK,iBAChCC,EAAQN,EAAaM,MACrBC,EAASP,EAAaO,OACpBC,EAAc7K,KAAKsH,MACrBZ,EAAamE,EAAYnE,WACzBlC,EAAcqG,EAAYrG,YAC5B,OAAoB9I,EAAAA,cAAoBoP,EAAAA,GAAS,CAC/CC,MAAOR,EACPS,SAAUR,EACVS,SAAU7D,EACV8D,OAAQT,EACRlJ,KAAM,CACJnB,EAAG,GAEL+K,GAAI,CACF/K,EAAG,GAELvC,IAAK,QAAQyG,OAAOiC,GACpBT,eAAgB9F,KAAKoL,mBACrBrF,iBAAkB/F,KAAKqL,sBACtB,SAAU1P,GACX,IAAIyE,EAAIzE,EAAKyE,EACb,GAAIsG,EAAY,CACd,IAAI4E,EAAuB5E,EAAWjH,OAASrB,EAAOqB,OAClD8L,EAAWnN,EAAOmH,IAAI,SAAU+D,EAAOC,GACzC,IAAIiC,EAAiBzG,KAAKC,MAAMuE,EAAQ+B,GACxC,GAAI5E,EAAW8E,GAAiB,CAC9B,IAAIC,EAAO/E,EAAW8E,GAClBE,GAAgBC,EAAAA,EAAAA,IAAkBF,EAAKpD,EAAGiB,EAAMjB,GAChDuD,GAAgBD,EAAAA,EAAAA,IAAkBF,EAAKnD,EAAGgB,EAAMhB,GACpD,OAAO5H,EAAcA,EAAc,CAAC,EAAG4I,GAAQ,CAAC,EAAG,CACjDjB,EAAGqD,EAActL,GACjBkI,EAAGsD,EAAcxL,IAErB,CAGA,GAAIsK,EAAkB,CACpB,IAAImB,GAAiBF,EAAAA,EAAAA,IAA0B,EAARhB,EAAWrB,EAAMjB,GACpDyD,GAAiBH,EAAAA,EAAAA,IAAkBf,EAAS,EAAGtB,EAAMhB,GACzD,OAAO5H,EAAcA,EAAc,CAAC,EAAG4I,GAAQ,CAAC,EAAG,CACjDjB,EAAGwD,EAAezL,GAClBkI,EAAGwD,EAAe1L,IAEtB,CACA,OAAOM,EAAcA,EAAc,CAAC,EAAG4I,GAAQ,CAAC,EAAG,CACjDjB,EAAGiB,EAAMjB,EACTC,EAAGgB,EAAMhB,GAEb,GACA,OAAO8B,EAAO2B,sBAAsBR,EAAU9D,EAAUC,EAC1D,CACA,IAEIsE,EADAC,GADeN,EAAAA,EAAAA,IAAkB,EAAGnH,EACxB0H,CAAa9L,GAE7B,GAAIkK,EAAiB,CACnB,IAAI7F,EAAQ,GAAGH,OAAOgG,GAAiB6B,MAAM,aAAa5G,IAAI,SAAU6G,GACtE,OAAOC,WAAWD,EACpB,GACAJ,EAAyB5B,EAAOkC,mBAAmBL,EAAWzH,EAAaC,EAC7E,MACEuH,EAAyB5B,EAAOtF,8BAA8BN,EAAayH,GAE7E,OAAO7B,EAAO2B,sBAAsB3N,EAAQqJ,EAAUC,EAAY,CAChE4C,gBAAiB0B,GAErB,EACF,GACC,CACDnO,IAAK,cACL4F,MAAO,SAAqBgE,EAAUC,GACpC,IAAI6E,EAAevM,KAAKlE,MACtBsC,EAASmO,EAAanO,OACtBgJ,EAAoBmF,EAAanF,kBAC/BoF,EAAexM,KAAKsH,MACtBZ,EAAa8F,EAAa9F,WAC1BlC,EAAcgI,EAAahI,YAC7B,OAAI4C,GAAqBhJ,GAAUA,EAAOqB,UAAYiH,GAAclC,EAAc,IAAMiI,IAAQ/F,EAAYtI,IACnG4B,KAAK0M,yBAAyBjF,EAAUC,GAE1C1H,KAAK+L,sBAAsB3N,EAAQqJ,EAAUC,EACtD,GACC,CACD7J,IAAK,SACL4F,MAAO,WACL,IAAIkJ,EACAC,EAAe5M,KAAKlE,MACtB+Q,EAAOD,EAAaC,KACpB5D,EAAM2D,EAAa3D,IACnB7K,EAASwO,EAAaxO,OACtBvC,EAAY+Q,EAAa/Q,UACzB+L,EAAQgF,EAAahF,MACrBC,EAAQ+E,EAAa/E,MACrBiF,EAAMF,EAAaE,IACnBC,EAAOH,EAAaG,KACpBpC,EAAQiC,EAAajC,MACrBC,EAASgC,EAAahC,OACtBxD,EAAoBwF,EAAaxF,kBACjC/J,EAAKuP,EAAavP,GACpB,GAAIwP,IAASzO,IAAWA,EAAOqB,OAC7B,OAAO,KAET,IAAI8E,EAAsBvE,KAAKsH,MAAM/C,oBACjCyI,EAAmC,IAAlB5O,EAAOqB,OACxBwN,GAAahG,EAAAA,EAAAA,GAAK,gBAAiBpL,GACnCqR,EAAYtF,GAASA,EAAMuF,kBAC3BC,EAAYvF,GAASA,EAAMsF,kBAC3B1F,EAAWyF,GAAaE,EACxB1F,EAAa2F,IAAMhQ,GAAM2C,KAAK3C,GAAKA,EACnCjB,EAAqD,QAA5CuQ,GAAexD,EAAAA,EAAAA,IAAYF,GAAK,UAAqC,IAAjB0D,EAA0BA,EAAe,CACtGxM,EAAG,EACHmN,YAAa,GAEfC,EAAUnR,EAAM+D,EAChBA,OAAgB,IAAZoN,EAAqB,EAAIA,EAC7BC,EAAoBpR,EAAMkR,YAC1BA,OAAoC,IAAtBE,EAA+B,EAAIA,EAEjDC,IADUC,EAAAA,EAAAA,IAAWzE,GAAOA,EAAM,CAAC,GACbF,QACtBA,OAA4B,IAAlB0E,GAAkCA,EAC1CE,EAAc,EAAJxN,EAAQmN,EACtB,OAAoB5R,EAAAA,cAAoBkN,EAAAA,EAAO,CAC7C/M,UAAWoR,GACVC,GAAaE,EAAyB1R,EAAAA,cAAoB,OAAQ,KAAmBA,EAAAA,cAAoB,WAAY,CACtH2B,GAAI,YAAYiH,OAAOoD,IACThM,EAAAA,cAAoB,OAAQ,CAC1C2M,EAAG6E,EAAYH,EAAOA,EAAOpC,EAAQ,EACrCrC,EAAG8E,EAAYN,EAAMA,EAAMlC,EAAS,EACpCD,MAAOuC,EAAYvC,EAAgB,EAARA,EAC3BC,OAAQwC,EAAYxC,EAAkB,EAATA,MACzB7B,GAAwBrN,EAAAA,cAAoB,WAAY,CAC5D2B,GAAI,iBAAiBiH,OAAOoD,IACdhM,EAAAA,cAAoB,OAAQ,CAC1C2M,EAAG0E,EAAOY,EAAU,EACpBrF,EAAGwE,EAAMa,EAAU,EACnBhD,MAAOA,EAAQgD,EACf/C,OAAQA,EAAS+C,MACZ,MAAOX,GAAkBhN,KAAK4N,YAAYnG,EAAUC,GAAa1H,KAAK6N,eAAepG,EAAUC,IAAcsF,GAAkB/D,IAAQjJ,KAAK8N,WAAWrG,EAAUsB,EAASrB,KAAeN,GAAqB7C,IAAwBwJ,EAAAA,EAAUC,mBAAmBhO,KAAKlE,MAAOsC,GACxR,MApX0E+D,EAAkB8B,EAAYrF,UAAWuI,GAAiBf,GAAajE,EAAkB8B,EAAamC,GAAclH,OAAO6B,eAAekD,EAAa,YAAa,CAAE3B,UAAU,IAAiB2B,EAA/Q,IAAsBA,EAAakD,EAAYf,CAqa/C,CAxY+B,CAwY7B6H,EAAAA,eACFrN,EAAgBiD,EAAM,cAAe,QACrCjD,EAAgBiD,EAAM,eAAgB,CACpCqK,QAAS,EACTC,QAAS,EACTrE,cAAc,EACdsE,WAAW,EACXnF,KAAK,EACLoF,WAAY,OACZC,OAAQ,UACRhB,YAAa,EACbrD,KAAM,OACN7L,OAAQ,GACRgJ,mBAAoBmH,EAAAA,EAAOC,MAC3B9D,kBAAkB,EAClBH,eAAgB,EAChBC,kBAAmB,KACnBC,gBAAiB,OACjBoC,MAAM,EACN4B,OAAO,IAUT7N,EAAgBiD,EAAM,kBAAmB,SAAUtH,GACjD,IAAIT,EAAQS,EAAMT,MAChB8L,EAAQrL,EAAMqL,MACdC,EAAQtL,EAAMsL,MACd6G,EAAanS,EAAMmS,WACnBC,EAAapS,EAAMoS,WACnBvG,EAAU7L,EAAM6L,QAChBwG,EAAWrS,EAAMqS,SACjBC,EAAgBtS,EAAMsS,cACtBC,EAASvS,EAAMuS,OACbhH,EAAShM,EAAMgM,OA8BnB,OAAOpH,EAAc,CACnBtC,OA9BWyQ,EAActJ,IAAI,SAAU+D,EAAOC,GAC9C,IAAI9F,GAAQ+E,EAAAA,EAAAA,IAAkBc,EAAOlB,GACrC,MAAe,eAAXN,EACK,CACLO,GAAG0G,EAAAA,EAAAA,IAAwB,CACzBC,KAAMpH,EACNqH,MAAOP,EACPE,SAAUA,EACVtF,MAAOA,EACPC,MAAOA,IAETjB,EAAG+E,IAAM5J,GAAS,KAAOoE,EAAMqH,MAAMzL,GACrCA,MAAOA,EACPgF,QAASa,GAGN,CACLjB,EAAGgF,IAAM5J,GAAS,KAAOmE,EAAMsH,MAAMzL,GACrC6E,GAAGyG,EAAAA,EAAAA,IAAwB,CACzBC,KAAMnH,EACNoH,MAAON,EACPC,SAAUA,EACVtF,MAAOA,EACPC,MAAOA,IAET9F,MAAOA,EACPgF,QAASa,EAEb,GAGExB,OAAQA,GACPgH,EACL,G,mCCtfWK,IAAYC,EAAAA,EAAAA,IAAyB,CAC9CC,UAAW,YACXC,eAAgBzL,EAChB0L,eAAgB,CAAC,CACfC,SAAU,QACVC,SAAUC,EAAAA,GACT,CACDF,SAAU,QACVC,SAAUE,EAAAA,IAEZC,cAAeA,GAAAA,K,gCCgDjB,MAuTA,GArTkCC,KAChC,MAAM,KAAEC,EAAI,OAAEC,IAAWC,EAAAA,EAAAA,MACnB,MAAEC,IAAUC,EAAAA,EAAAA,MACZC,GAAWC,EAAAA,EAAAA,OAEVC,EAAWC,IAAgBC,EAAAA,EAAAA,WAAS,IACpCC,EAAWC,IAAgBF,EAAAA,EAAAA,UAAS,aACpCG,EAAWC,IAAgBJ,EAAAA,EAAAA,UAAS,QACpCK,EAAcC,IAAmBN,EAAAA,EAAAA,WAAS,GAY3CO,EAAQ,CACZC,YAAa,SACbC,YAAa,KACbC,YAAa,GACbC,YAAa,QACbC,aAAc,IACdC,IAAK,QACLC,WAAY,KACZC,QAAS,KACTC,aAAc,QAIVC,EAAyB,CAC7B,CACE5P,KAAM,eACN6B,MAAO,IAAIqN,EAAMC,YAAYU,oBAAeC,EAAW,CAAEC,sBAAuB,EAAGC,sBAAuB,MAC1GC,OAAQf,EAAMK,YACdW,KAAMC,EAAAA,GAER,CACEnQ,KAAM,eACN6B,MAAOqN,EAAME,YAAYS,iBACzBK,KAAMpU,GAER,CACEkE,KAAM,eACN6B,MAAOqN,EAAMG,YACba,KAAMhU,GAER,CACE8D,KAAM,YACN6B,MAAO,IAAIqN,EAAMM,IAAIK,oBAAeC,EAAW,CAAEC,sBAAuB,EAAGC,sBAAuB,MAClGC,OAAQf,EAAMO,WACdS,KAAM3T,IAIJ6T,EAAwB,CAC5B,CACE3U,GAAI,SACJ4U,KAAM,UACNpI,KAAM,MACNqI,MAAO,SACPC,OAAQ,GACRC,MAAO,SACPC,IAAK,GACLC,OAAQ,YACRC,MAAM,IAAIC,MAAOC,cACjBC,KAAM,YAER,CACErV,GAAI,SACJ4U,KAAM,UACNpI,KAAM,OACNqI,MAAO,QACPC,OAAQ,IACRC,MAAO,QACPC,IAAK,MACLC,OAAQ,YACRC,MAAM,IAAIC,MAAOC,cACjBC,KAAM,YAER,CACErV,GAAI,SACJ4U,KAAM,UACNpI,KAAM,MACNqI,MAAO,OACPC,OAAQ,GACRC,MAAO,OACPC,IAAK,MACLC,OAAQ,UACRC,MAAM,IAAIC,MAAOC,cACjBC,KAAM,aA6DV,OACEC,EAAAA,GAAAA,MAAA,OAAK9W,UAAU,0BAAyBG,SAAA,EACtCD,EAAAA,GAAAA,KAAA,UAAQF,UAAU,kBAAiBG,UACjC2W,EAAAA,GAAAA,MAAA,OAAK9W,UAAU,gFAA+EG,SAAA,EAC5FD,EAAAA,GAAAA,KAAA,MAAIF,UAAU,mCAAkCG,SAAC,sBACjD2W,EAAAA,GAAAA,MAAA,OAAK9W,UAAU,8BAA6BG,SAAA,EAC1C2W,EAAAA,GAAAA,MAAA,OAAK9W,UAAU,aAAYG,SAAA,EACzBD,EAAAA,GAAAA,KAAA,KAAGF,UAAU,wBAAuBG,SAAC,kBACrCD,EAAAA,GAAAA,KAAA,KAAGF,UAAU,cAAaG,SAAM,OAAJ8T,QAAI,IAAJA,OAAI,EAAJA,EAAM8C,YAEpC7W,EAAAA,GAAAA,KAAC8W,EAAAA,EAAM,CAACC,QAfGC,KACnBhD,IACAI,EAAS,WAa8B6C,QAAQ,UAAShX,SAAC,oBAOvD2W,EAAAA,GAAAA,MAAA,QAAM9W,UAAU,yCAAwCG,SAAA,EAEtDD,EAAAA,GAAAA,KAAA,OAAKF,UAAU,gDAA+CG,SAC3DwV,EAAWjM,IAAK0N,IACf,MAAMC,EAAOD,EAAKnB,KAClB,OACEa,EAAAA,GAAAA,MAACQ,EAAAA,GAAI,CAAAnX,SAAA,EACH2W,EAAAA,GAAAA,MAACS,EAAAA,GAAU,CAACvX,UAAU,4DAA2DG,SAAA,EAC/ED,EAAAA,GAAAA,KAACsX,EAAAA,GAAS,CAACxX,UAAU,sBAAqBG,SACvCiX,EAAKrR,QAER7F,EAAAA,GAAAA,KAACmX,EAAI,CAACrX,UAAU,sCAElB8W,EAAAA,GAAAA,MAACW,EAAAA,GAAW,CAAAtX,SAAA,EACVD,EAAAA,GAAAA,KAAA,OAAKF,UAAU,qBAAoBG,SAAEiX,EAAKxP,aACzBiO,IAAhBuB,EAAKpB,SACJc,EAAAA,GAAAA,MAAA,KAAG9W,UAAW,YAAWoX,EAAKpB,QAAU,EAAI,iBAAmB,gBAAiB7V,SAAA,CAC7EiX,EAAKpB,QAAU,EAAI,IAAM,GAAIoB,EAAKpB,OAAO,2BAXvCoB,EAAKrR,WAoBtB+Q,EAAAA,GAAAA,MAAA,OAAK9W,UAAU,4BAA2BG,SAAA,EACxCD,EAAAA,GAAAA,KAAA,OAAKF,UAAU,gBAAeG,UAC5B2W,EAAAA,GAAAA,MAACQ,EAAAA,GAAI,CAAAnX,SAAA,EACHD,EAAAA,GAAAA,KAACqX,EAAAA,GAAU,CAAApX,UACTD,EAAAA,GAAAA,KAACsX,EAAAA,GAAS,CAAArX,SAAC,qBAEbD,EAAAA,GAAAA,KAACuX,EAAAA,GAAW,CAACzX,UAAU,OAAMG,UAC3BD,EAAAA,GAAAA,KAAA,OAAKF,UAAU,YAAWG,UACxBD,EAAAA,GAAAA,KAACwX,EAAAA,EAAmB,CAAC5I,MAAM,OAAOC,OAAO,OAAM5O,UAC7C2W,EAAAA,GAAAA,MAACxD,GAAS,CAACrG,KAvGsB,CACjD,CAAElH,KAAM,OAAQsQ,MAAO,KACvB,CAAEtQ,KAAM,QAASsQ,MAAO,OACxB,CAAEtQ,KAAM,QAASsQ,MAAO,OACxB,CAAEtQ,KAAM,QAASsQ,MAAO,MACxB,CAAEtQ,KAAM,QAASsQ,MAAO,OACxB,CAAEtQ,KAAM,QAASsQ,MAAO,OACxB,CAAEtQ,KAAM,QAASsQ,MAAO,QAgGmBlW,SAAA,EACzBD,EAAAA,GAAAA,KAACyX,GAAAA,EAAa,CAAClJ,gBAAgB,SAC/BvO,EAAAA,GAAAA,KAAC2T,EAAAA,EAAK,CAACtH,QAAQ,UACfrM,EAAAA,GAAAA,KAAC4T,EAAAA,EAAK,CAAC8D,OAAQ,CAAC,OAAQ,WACxB1X,EAAAA,GAAAA,KAAC2X,GAAAA,EAAO,KACR3X,EAAAA,GAAAA,KAAC8H,EAAI,CACHgG,KAAK,WACLzB,QAAQ,QACRkG,OAAO,UACPhB,YAAa,EACbrE,KAAK,EACLmF,UAAW,CAAEjO,EAAG,oBAS9BpE,EAAAA,GAAAA,KAAA,OAAAC,UACE2W,EAAAA,GAAAA,MAACQ,EAAAA,GAAI,CAAAnX,SAAA,EACHD,EAAAA,GAAAA,KAACqX,EAAAA,GAAU,CAAApX,UACTD,EAAAA,GAAAA,KAACsX,EAAAA,GAAS,CAAArX,SAAC,qBAEbD,EAAAA,GAAAA,KAACuX,EAAAA,GAAW,CAAAtX,UACV2W,EAAAA,GAAAA,MAAClX,EAAAA,GAAK,CAAAO,SAAA,EACJD,EAAAA,GAAAA,KAACI,EAAAA,GAAW,CAAAH,UACV2W,EAAAA,GAAAA,MAACnW,EAAAA,GAAQ,CAAAR,SAAA,EACPD,EAAAA,GAAAA,KAACW,EAAAA,GAAS,CAAAV,SAAC,UACXD,EAAAA,GAAAA,KAACW,EAAAA,GAAS,CAAAV,SAAC,UACXD,EAAAA,GAAAA,KAACW,EAAAA,GAAS,CAAAV,SAAC,WACXD,EAAAA,GAAAA,KAACW,EAAAA,GAAS,CAAAV,SAAC,YACXD,EAAAA,GAAAA,KAACW,EAAAA,GAAS,CAAAV,SAAC,gBAGfD,EAAAA,GAAAA,KAACM,EAAAA,GAAS,CAAAL,SACPgW,EAAazM,IAAKoO,IACjBhB,EAAAA,GAAAA,MAACnW,EAAAA,GAAQ,CAAAR,SAAA,EACPD,EAAAA,GAAAA,KAACa,EAAAA,GAAS,CAACf,UAAU,cAAaG,SAAE2X,EAAM1B,QAC1ClW,EAAAA,GAAAA,KAACa,EAAAA,GAAS,CAACf,UAAwC,QAA7B8X,EAAM9J,KAAK+J,cAA0B,iBAAmB,eAAe5X,SAC1F2X,EAAM9J,KAAKgK,OAAO,GAAGC,cAAgBH,EAAM9J,KAAKlI,MAAM,MAEzD5F,EAAAA,GAAAA,KAACa,EAAAA,GAAS,CAAAZ,SAAE2X,EAAMzB,SAClBnW,EAAAA,GAAAA,KAACa,EAAAA,GAAS,CAAAZ,SAAE2X,EAAMxB,UAClBpW,EAAAA,GAAAA,KAACa,EAAAA,GAAS,CAAAZ,SAAE2X,EAAMvB,UAPLuB,EAAMtW,qBAiBnCtB,EAAAA,GAAAA,KAAA,OAAKF,UAAU,QACdG,UACC2W,EAAAA,GAAAA,MAACoB,EAAAA,GAAI,CAACC,aAAa,cAAcnY,UAAU,YAAWG,SAAA,EACpD2W,EAAAA,GAAAA,MAACsB,EAAAA,GAAQ,CAAAjY,SAAA,EACPD,EAAAA,GAAAA,KAACmY,EAAAA,GAAW,CAACzQ,MAAM,cAAazH,SAAC,iBACjCD,EAAAA,GAAAA,KAACmY,EAAAA,GAAW,CAACzQ,MAAM,gBAAezH,SAAC,mBACnCD,EAAAA,GAAAA,KAACmY,EAAAA,GAAW,CAACzQ,MAAM,YAAWzH,SAAC,kBAGjCD,EAAAA,GAAAA,KAACoY,EAAAA,GAAW,CAAC1Q,MAAM,cAAazH,UAC9B2W,EAAAA,GAAAA,MAACQ,EAAAA,GAAI,CAAAnX,SAAA,EACHD,EAAAA,GAAAA,KAACqX,EAAAA,GAAU,CAAApX,UACTD,EAAAA,GAAAA,KAACsX,EAAAA,GAAS,CAAArX,SAAC,mBAEbD,EAAAA,GAAAA,KAACuX,EAAAA,GAAW,CAAAtX,UACVD,EAAAA,GAAAA,KAAA,KAAGF,UAAU,iCAAgCG,SAAC,2BAKpDD,EAAAA,GAAAA,KAACoY,EAAAA,GAAW,CAAC1Q,MAAM,gBAAezH,UAChC2W,EAAAA,GAAAA,MAACQ,EAAAA,GAAI,CAAAnX,SAAA,EACHD,EAAAA,GAAAA,KAACqX,EAAAA,GAAU,CAAApX,UACTD,EAAAA,GAAAA,KAACsX,EAAAA,GAAS,CAAArX,SAAC,qBAEbD,EAAAA,GAAAA,KAACuX,EAAAA,GAAW,CAAAtX,UACVD,EAAAA,GAAAA,KAAA,KAAGF,UAAU,iCAAgCG,SAAC,uCAKpDD,EAAAA,GAAAA,KAACoY,EAAAA,GAAW,CAAC1Q,MAAM,YAAWzH,UAC5B2W,EAAAA,GAAAA,MAACQ,EAAAA,GAAI,CAAAnX,SAAA,EACHD,EAAAA,GAAAA,KAACqX,EAAAA,GAAU,CAAApX,UACTD,EAAAA,GAAAA,KAACsX,EAAAA,GAAS,CAAArX,SAAC,iBAEbD,EAAAA,GAAAA,KAACuX,EAAAA,GAAW,CAAAtX,UACVD,EAAAA,GAAAA,KAAA,KAAGF,UAAU,iCAAgCG,SAAC,wC,yGCzWhE,MAAM+X,EAAOK,EAAAA,GAEPH,EAAWvY,EAAAA,WAGf,CAAAC,EAA0BC,KAAG,IAA5B,UAAEC,KAAcC,GAAOH,EAAA,OACxBI,EAAAA,EAAAA,KAACqY,EAAAA,GAAkB,CACjBxY,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IACT,6FACAJ,MAEEC,MAGRmY,EAAS/X,YAAckY,EAAAA,GAAmBlY,YAE1C,MAAMgY,EAAcxY,EAAAA,WAGlB,CAAAU,EAA0BR,KAAG,IAA5B,UAAEC,KAAcC,GAAOM,EAAA,OACxBL,EAAAA,EAAAA,KAACqY,EAAAA,GAAqB,CACpBxY,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IACT,sYACAJ,MAEEC,MAGRoY,EAAYhY,YAAckY,EAAAA,GAAsBlY,YAEhD,MAAMiY,EAAczY,EAAAA,WAGlB,CAAAY,EAA0BV,KAAG,IAA5B,UAAEC,KAAcC,GAAOQ,EAAA,OACxBP,EAAAA,EAAAA,KAACqY,EAAAA,GAAqB,CACpBxY,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IACT,kIACAJ,MAEEC,MAGRqY,EAAYjY,YAAckY,EAAAA,GAAsBlY,W,iHC9ChD,MAAMiX,EAAOzX,EAAAA,WAGX,CAAAC,EAA0BC,KAAG,IAA5B,UAAEC,KAAcC,GAAOH,EAAA,OACxBI,EAAAA,EAAAA,KAAA,OACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IACT,2DACAJ,MAEEC,MAGRqX,EAAKjX,YAAc,OAEnB,MAAMkX,EAAa1X,EAAAA,WAGjB,CAAAU,EAA0BR,KAAG,IAA5B,UAAEC,KAAcC,GAAOM,EAAA,OACxBL,EAAAA,EAAAA,KAAA,OACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IAAG,gCAAiCJ,MAC3CC,MAGRsX,EAAWlX,YAAc,aAEzB,MAAMmX,EAAY3X,EAAAA,WAGhB,CAAAY,EAA0BV,KAAG,IAA5B,UAAEC,KAAcC,GAAOQ,EAAA,OACxBP,EAAAA,EAAAA,KAAA,MACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IACT,qDACAJ,MAEEC,MAGRuX,EAAUnX,YAAc,YAExB,MAAMmY,EAAkB3Y,EAAAA,WAGtB,CAAAa,EAA0BX,KAAG,IAA5B,UAAEC,KAAcC,GAAOS,EAAA,OACxBR,EAAAA,EAAAA,KAAA,KACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IAAG,gCAAiCJ,MAC3CC,MAGRuY,EAAgBnY,YAAc,kBAE9B,MAAMoX,EAAc5X,EAAAA,WAGlB,CAAAe,EAA0Bb,KAAG,IAA5B,UAAEC,KAAcC,GAAOW,EAAA,OACxBV,EAAAA,EAAAA,KAAA,OAAKH,IAAKA,EAAKC,WAAWI,EAAAA,EAAAA,IAAG,WAAYJ,MAAgBC,MAE3DwX,EAAYpX,YAAc,cAE1B,MAAMoY,EAAa5Y,EAAAA,WAGjB,CAAAiB,EAA0Bf,KAAG,IAA5B,UAAEC,KAAcC,GAAOa,EAAA,OACxBZ,EAAAA,EAAAA,KAAA,OACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IAAG,6BAA8BJ,MACxCC,MAGRwY,EAAWpY,YAAc,Y,0DC5DzB,IAAIgB,EAAQ,EA+BZ,MAAMqX,EAAgB,IAAIC,IAEpBC,EAAoBC,IACxB,GAAIH,EAAcI,IAAID,GACpB,OAGF,MAAME,EAAUC,WAAW,KACzBN,EAAcO,OAAOJ,GACrBK,EAAS,CACPlL,KAAM,eACN6K,QAASA,KAEV,KAEHH,EAAcS,IAAIN,EAASE,IAGhBK,EAAUA,CAAC3N,EAAc4N,KACpC,OAAQA,EAAOrL,MACb,IAAK,YACH,MAAO,IACFvC,EACH6N,OAAQ,CAACD,EAAOjF,SAAU3I,EAAM6N,QAAQxT,MAAM,EAAG,IAGrD,IAAK,eACH,MAAO,IACF2F,EACH6N,OAAQ7N,EAAM6N,OAAO5P,IAAKnF,GACxBA,EAAE/C,KAAO6X,EAAOjF,MAAM5S,GAAK,IAAK+C,KAAM8U,EAAOjF,OAAU7P,IAI7D,IAAK,gBAAiB,CACpB,MAAM,QAAEsU,GAAYQ,EAYpB,OARIR,EACFD,EAAiBC,GAEjBpN,EAAM6N,OAAOxU,QAASsP,IACpBwE,EAAiBxE,EAAM5S,MAIpB,IACFiK,EACH6N,OAAQ7N,EAAM6N,OAAO5P,IAAKnF,GACxBA,EAAE/C,KAAOqX,QAAuBhD,IAAZgD,EAChB,IACKtU,EACHgV,MAAM,GAERhV,GAGV,CACA,IAAK,eACH,YAAuBsR,IAAnBwD,EAAOR,QACF,IACFpN,EACH6N,OAAQ,IAGL,IACF7N,EACH6N,OAAQ7N,EAAM6N,OAAO7U,OAAQF,GAAMA,EAAE/C,KAAO6X,EAAOR,YAKrDW,EAA2C,GAEjD,IAAIC,EAAqB,CAAEH,OAAQ,IAEnC,SAASJ,EAASG,GAChBI,EAAcL,EAAQK,EAAaJ,GACnCG,EAAU1U,QAAS4U,IACjBA,EAASD,IAEb,CAIA,SAASrF,EAAKtU,GAAuB,OAAjBG,GAAcH,EAChC,MAAM0B,GAnHNH,GAASA,EAAQ,GAAKyG,OAAO6R,iBACtBtY,EAAMD,YAyHPwY,EAAUA,IAAMV,EAAS,CAAElL,KAAM,gBAAiB6K,QAASrX,IAcjE,OAZA0X,EAAS,CACPlL,KAAM,YACNoG,MAAO,IACFnU,EACHuB,KACA+X,MAAM,EACNM,aAAeN,IACRA,GAAMK,QAKV,CACLpY,GAAIA,EACJoY,UACAE,OAtBc7Z,GACdiZ,EAAS,CACPlL,KAAM,eACNoG,MAAO,IAAKnU,EAAOuB,QAqBzB,CAEA,SAAS6S,IACP,MAAO5I,EAAOzB,GAAYnK,EAAAA,SAAsB4Z,GAYhD,OAVA5Z,EAAAA,UAAgB,KACd2Z,EAAU5U,KAAKoF,GACR,KACL,MAAM0D,EAAQ8L,EAAUhW,QAAQwG,GAC5B0D,GAAS,GACX8L,EAAUO,OAAOrM,EAAO,KAG3B,CAACjC,IAEG,IACFA,EACH2I,QACAwF,QAAUf,GAAqBK,EAAS,CAAElL,KAAM,gBAAiB6K,YAErE,C,sFC9KA,MAAMmB,GAAiBC,EAAAA,EAAAA,GACrB,yRACA,CACEC,SAAU,CACR/C,QAAS,CACPgD,QAAS,yDACTC,YACE,qEACFC,QACE,iFACFC,UACE,+DACFC,MAAO,+CACPC,KAAM,mDAERC,KAAM,CACJN,QAAS,iBACTO,GAAI,sBACJC,GAAI,uBACJ1E,KAAM,cAGV2E,gBAAiB,CACfzD,QAAS,UACTsD,KAAM,aAWNzD,EAASnX,EAAAA,WACb,CAAAC,EAA0DC,KAAS,IAAlE,UAAEC,EAAS,QAAEmX,EAAO,KAAEsD,EAAI,QAAEI,GAAU,KAAU5a,GAAOH,EACtD,MAAMgb,EAAOD,EAAUE,EAAAA,GAAO,SAC9B,OACE7a,EAAAA,EAAAA,KAAC4a,EAAI,CACH9a,WAAWI,EAAAA,EAAAA,IAAG4Z,EAAe,CAAE7C,UAASsD,OAAMza,eAC9CD,IAAKA,KACDE,MAKZ+W,EAAO3W,YAAc,Q", "sources": ["components/ui/table.tsx", "../node_modules/@radix-ui/react-id/src/id.tsx", "../node_modules/lucide-react/src/icons/activity.ts", "../node_modules/lucide-react/src/icons/bar-chart-2.ts", "../node_modules/lucide-react/src/icons/trending-up.ts", "../node_modules/recharts/es6/cartesian/Line.js", "../node_modules/recharts/es6/chart/LineChart.js", "pages/TraderDashboard.tsx", "components/ui/tabs.tsx", "components/ui/card.tsx", "components/ui/use-toast.ts", "components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst Table = React.forwardRef<\n  HTMLTableElement,\n  React.HTMLAttributes<HTMLTableElement>\n>(({ className, ...props }, ref) => (\n  <div className=\"w-full overflow-auto\">\n    <table\n      ref={ref}\n      className={cn(\"w-full caption-bottom text-sm\", className)}\n      {...props}\n    />\n  </div>\n))\nTable.displayName = \"Table\"\n\nconst TableHeader = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <thead ref={ref} className={cn(\"[&_tr]:border-b\", className)} {...props} />\n))\nTableHeader.displayName = \"TableHeader\"\n\nconst TableBody = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tbody\n    ref={ref}\n    className={cn(\"[&_tr:last-child]:border-0\", className)}\n    {...props}\n  />\n))\nTableBody.displayName = \"TableBody\"\n\nconst TableFooter = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tfoot\n    ref={ref}\n    className={cn(\"bg-primary font-medium text-primary-foreground\", className)}\n    {...props}\n  />\n))\nTableFooter.displayName = \"TableFooter\"\n\nconst TableRow = React.forwardRef<\n  HTMLTableRowElement,\n  React.HTMLAttributes<HTMLTableRowElement>\n>(({ className, ...props }, ref) => (\n  <tr\n    ref={ref}\n    className={cn(\n      \"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nTableRow.displayName = \"TableRow\"\n\nconst TableHead = React.forwardRef<\n  HTMLTableCellElement,\n  React.ThHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <th\n    ref={ref}\n    className={cn(\n      \"h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0\",\n      className\n    )}\n    {...props}\n  />\n))\nTableHead.displayName = \"TableHead\"\n\nconst TableCell = React.forwardRef<\n  HTMLTableCellElement,\n  React.TdHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <td\n    ref={ref}\n    className={cn(\"p-4 align-middle [&:has([role=checkbox])]:pr-0\", className)}\n    {...props}\n  />\n))\nTableCell.displayName = \"TableCell\"\n\nconst TableCaption = React.forwardRef<\n  HTMLTableCaptionElement,\n  React.HTMLAttributes<HTMLTableCaptionElement>\n>(({ className, ...props }, ref) => (\n  <caption\n    ref={ref}\n    className={cn(\"mt-4 text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nTableCaption.displayName = \"TableCaption\"\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n", "import * as React from 'react';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\n\n// We spaces with `.trim().toString()` to prevent bundlers from trying to `import { useId } from 'react';`\nconst useReactId = (React as any)[' useId '.trim().toString()] || (() => undefined);\nlet count = 0;\n\nfunction useId(deterministicId?: string): string {\n  const [id, setId] = React.useState<string | undefined>(useReactId());\n  // React versions older than 18 will have client-side ids only.\n  useLayoutEffect(() => {\n    if (!deterministicId) setId((reactId) => reactId ?? String(count++));\n  }, [deterministicId]);\n  return deterministicId || (id ? `radix-${id}` : '');\n}\n\nexport { useId };\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Activity\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjIgMTJoLTRsLTMgOUw5IDNsLTMgOUgyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/activity\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Activity = createLucideIcon('Activity', [\n  ['path', { d: 'M22 12h-4l-3 9L9 3l-3 9H2', key: 'd5dnw9' }],\n]);\n\nexport default Activity;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name BarChart2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iMTgiIHgyPSIxOCIgeTE9IjIwIiB5Mj0iMTAiIC8+CiAgPGxpbmUgeDE9IjEyIiB4Mj0iMTIiIHkxPSIyMCIgeTI9IjQiIC8+CiAgPGxpbmUgeDE9IjYiIHgyPSI2IiB5MT0iMjAiIHkyPSIxNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/bar-chart-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BarChart2 = createLucideIcon('BarChart2', [\n  ['line', { x1: '18', x2: '18', y1: '20', y2: '10', key: '1xfpm4' }],\n  ['line', { x1: '12', x2: '12', y1: '20', y2: '4', key: 'be30l9' }],\n  ['line', { x1: '6', x2: '6', y1: '20', y2: '14', key: '1r4le6' }],\n]);\n\nexport default BarChart2;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name TrendingUp\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWxpbmUgcG9pbnRzPSIyMiA3IDEzLjUgMTUuNSA4LjUgMTAuNSAyIDE3IiAvPgogIDxwb2x5bGluZSBwb2ludHM9IjE2IDcgMjIgNyAyMiAxMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/trending-up\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TrendingUp = createLucideIcon('TrendingUp', [\n  ['polyline', { points: '22 7 13.5 15.5 8.5 10.5 2 17', key: '126l90' }],\n  ['polyline', { points: '16 7 22 7 22 13', key: 'kwv8wd' }],\n]);\n\nexport default TrendingUp;\n", "var _excluded = [\"type\", \"layout\", \"connectNulls\", \"ref\"],\n  _excluded2 = [\"key\"];\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } } return target; }\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/**\n * @fileOverview Line\n */\nimport React, { PureComponent } from 'react';\nimport Animate from 'react-smooth';\nimport isFunction from 'lodash/isFunction';\nimport isNil from 'lodash/isNil';\nimport isEqual from 'lodash/isEqual';\nimport clsx from 'clsx';\nimport { Curve } from '../shape/Curve';\nimport { Dot } from '../shape/Dot';\nimport { Layer } from '../container/Layer';\nimport { LabelList } from '../component/LabelList';\nimport { ErrorBar } from './ErrorBar';\nimport { uniqueId, interpolateNumber } from '../util/DataUtils';\nimport { findAllByType, filterProps, hasClipDot } from '../util/ReactUtils';\nimport { Global } from '../util/Global';\nimport { getCateCoordinateOfLine, getValueByDataKey } from '../util/ChartUtils';\nexport var Line = /*#__PURE__*/function (_PureComponent) {\n  function Line() {\n    var _this;\n    _classCallCheck(this, Line);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _callSuper(this, Line, [].concat(args));\n    _defineProperty(_this, \"state\", {\n      isAnimationFinished: true,\n      totalLength: 0\n    });\n    _defineProperty(_this, \"generateSimpleStrokeDasharray\", function (totalLength, length) {\n      return \"\".concat(length, \"px \").concat(totalLength - length, \"px\");\n    });\n    _defineProperty(_this, \"getStrokeDasharray\", function (length, totalLength, lines) {\n      var lineLength = lines.reduce(function (pre, next) {\n        return pre + next;\n      });\n\n      // if lineLength is 0 return the default when no strokeDasharray is provided\n      if (!lineLength) {\n        return _this.generateSimpleStrokeDasharray(totalLength, length);\n      }\n      var count = Math.floor(length / lineLength);\n      var remainLength = length % lineLength;\n      var restLength = totalLength - length;\n      var remainLines = [];\n      for (var i = 0, sum = 0; i < lines.length; sum += lines[i], ++i) {\n        if (sum + lines[i] > remainLength) {\n          remainLines = [].concat(_toConsumableArray(lines.slice(0, i)), [remainLength - sum]);\n          break;\n        }\n      }\n      var emptyLines = remainLines.length % 2 === 0 ? [0, restLength] : [restLength];\n      return [].concat(_toConsumableArray(Line.repeat(lines, count)), _toConsumableArray(remainLines), emptyLines).map(function (line) {\n        return \"\".concat(line, \"px\");\n      }).join(', ');\n    });\n    _defineProperty(_this, \"id\", uniqueId('recharts-line-'));\n    _defineProperty(_this, \"pathRef\", function (node) {\n      _this.mainCurve = node;\n    });\n    _defineProperty(_this, \"handleAnimationEnd\", function () {\n      _this.setState({\n        isAnimationFinished: true\n      });\n      if (_this.props.onAnimationEnd) {\n        _this.props.onAnimationEnd();\n      }\n    });\n    _defineProperty(_this, \"handleAnimationStart\", function () {\n      _this.setState({\n        isAnimationFinished: false\n      });\n      if (_this.props.onAnimationStart) {\n        _this.props.onAnimationStart();\n      }\n    });\n    return _this;\n  }\n  _inherits(Line, _PureComponent);\n  return _createClass(Line, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      if (!this.props.isAnimationActive) {\n        return;\n      }\n      var totalLength = this.getTotalLength();\n      this.setState({\n        totalLength: totalLength\n      });\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate() {\n      if (!this.props.isAnimationActive) {\n        return;\n      }\n      var totalLength = this.getTotalLength();\n      if (totalLength !== this.state.totalLength) {\n        this.setState({\n          totalLength: totalLength\n        });\n      }\n    }\n  }, {\n    key: \"getTotalLength\",\n    value: function getTotalLength() {\n      var curveDom = this.mainCurve;\n      try {\n        return curveDom && curveDom.getTotalLength && curveDom.getTotalLength() || 0;\n      } catch (err) {\n        return 0;\n      }\n    }\n  }, {\n    key: \"renderErrorBar\",\n    value: function renderErrorBar(needClip, clipPathId) {\n      if (this.props.isAnimationActive && !this.state.isAnimationFinished) {\n        return null;\n      }\n      var _this$props = this.props,\n        points = _this$props.points,\n        xAxis = _this$props.xAxis,\n        yAxis = _this$props.yAxis,\n        layout = _this$props.layout,\n        children = _this$props.children;\n      var errorBarItems = findAllByType(children, ErrorBar);\n      if (!errorBarItems) {\n        return null;\n      }\n      var dataPointFormatter = function dataPointFormatter(dataPoint, dataKey) {\n        return {\n          x: dataPoint.x,\n          y: dataPoint.y,\n          value: dataPoint.value,\n          errorVal: getValueByDataKey(dataPoint.payload, dataKey)\n        };\n      };\n      var errorBarProps = {\n        clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null\n      };\n      return /*#__PURE__*/React.createElement(Layer, errorBarProps, errorBarItems.map(function (item) {\n        return /*#__PURE__*/React.cloneElement(item, {\n          key: \"bar-\".concat(item.props.dataKey),\n          data: points,\n          xAxis: xAxis,\n          yAxis: yAxis,\n          layout: layout,\n          dataPointFormatter: dataPointFormatter\n        });\n      }));\n    }\n  }, {\n    key: \"renderDots\",\n    value: function renderDots(needClip, clipDot, clipPathId) {\n      var isAnimationActive = this.props.isAnimationActive;\n      if (isAnimationActive && !this.state.isAnimationFinished) {\n        return null;\n      }\n      var _this$props2 = this.props,\n        dot = _this$props2.dot,\n        points = _this$props2.points,\n        dataKey = _this$props2.dataKey;\n      var lineProps = filterProps(this.props, false);\n      var customDotProps = filterProps(dot, true);\n      var dots = points.map(function (entry, i) {\n        var dotProps = _objectSpread(_objectSpread(_objectSpread({\n          key: \"dot-\".concat(i),\n          r: 3\n        }, lineProps), customDotProps), {}, {\n          index: i,\n          cx: entry.x,\n          cy: entry.y,\n          value: entry.value,\n          dataKey: dataKey,\n          payload: entry.payload,\n          points: points\n        });\n        return Line.renderDotItem(dot, dotProps);\n      });\n      var dotsProps = {\n        clipPath: needClip ? \"url(#clipPath-\".concat(clipDot ? '' : 'dots-').concat(clipPathId, \")\") : null\n      };\n      return /*#__PURE__*/React.createElement(Layer, _extends({\n        className: \"recharts-line-dots\",\n        key: \"dots\"\n      }, dotsProps), dots);\n    }\n  }, {\n    key: \"renderCurveStatically\",\n    value: function renderCurveStatically(points, needClip, clipPathId, props) {\n      var _this$props3 = this.props,\n        type = _this$props3.type,\n        layout = _this$props3.layout,\n        connectNulls = _this$props3.connectNulls,\n        ref = _this$props3.ref,\n        others = _objectWithoutProperties(_this$props3, _excluded);\n      var curveProps = _objectSpread(_objectSpread(_objectSpread({}, filterProps(others, true)), {}, {\n        fill: 'none',\n        className: 'recharts-line-curve',\n        clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null,\n        points: points\n      }, props), {}, {\n        type: type,\n        layout: layout,\n        connectNulls: connectNulls\n      });\n      return /*#__PURE__*/React.createElement(Curve, _extends({}, curveProps, {\n        pathRef: this.pathRef\n      }));\n    }\n  }, {\n    key: \"renderCurveWithAnimation\",\n    value: function renderCurveWithAnimation(needClip, clipPathId) {\n      var _this2 = this;\n      var _this$props4 = this.props,\n        points = _this$props4.points,\n        strokeDasharray = _this$props4.strokeDasharray,\n        isAnimationActive = _this$props4.isAnimationActive,\n        animationBegin = _this$props4.animationBegin,\n        animationDuration = _this$props4.animationDuration,\n        animationEasing = _this$props4.animationEasing,\n        animationId = _this$props4.animationId,\n        animateNewValues = _this$props4.animateNewValues,\n        width = _this$props4.width,\n        height = _this$props4.height;\n      var _this$state = this.state,\n        prevPoints = _this$state.prevPoints,\n        totalLength = _this$state.totalLength;\n      return /*#__PURE__*/React.createElement(Animate, {\n        begin: animationBegin,\n        duration: animationDuration,\n        isActive: isAnimationActive,\n        easing: animationEasing,\n        from: {\n          t: 0\n        },\n        to: {\n          t: 1\n        },\n        key: \"line-\".concat(animationId),\n        onAnimationEnd: this.handleAnimationEnd,\n        onAnimationStart: this.handleAnimationStart\n      }, function (_ref) {\n        var t = _ref.t;\n        if (prevPoints) {\n          var prevPointsDiffFactor = prevPoints.length / points.length;\n          var stepData = points.map(function (entry, index) {\n            var prevPointIndex = Math.floor(index * prevPointsDiffFactor);\n            if (prevPoints[prevPointIndex]) {\n              var prev = prevPoints[prevPointIndex];\n              var interpolatorX = interpolateNumber(prev.x, entry.x);\n              var interpolatorY = interpolateNumber(prev.y, entry.y);\n              return _objectSpread(_objectSpread({}, entry), {}, {\n                x: interpolatorX(t),\n                y: interpolatorY(t)\n              });\n            }\n\n            // magic number of faking previous x and y location\n            if (animateNewValues) {\n              var _interpolatorX = interpolateNumber(width * 2, entry.x);\n              var _interpolatorY = interpolateNumber(height / 2, entry.y);\n              return _objectSpread(_objectSpread({}, entry), {}, {\n                x: _interpolatorX(t),\n                y: _interpolatorY(t)\n              });\n            }\n            return _objectSpread(_objectSpread({}, entry), {}, {\n              x: entry.x,\n              y: entry.y\n            });\n          });\n          return _this2.renderCurveStatically(stepData, needClip, clipPathId);\n        }\n        var interpolator = interpolateNumber(0, totalLength);\n        var curLength = interpolator(t);\n        var currentStrokeDasharray;\n        if (strokeDasharray) {\n          var lines = \"\".concat(strokeDasharray).split(/[,\\s]+/gim).map(function (num) {\n            return parseFloat(num);\n          });\n          currentStrokeDasharray = _this2.getStrokeDasharray(curLength, totalLength, lines);\n        } else {\n          currentStrokeDasharray = _this2.generateSimpleStrokeDasharray(totalLength, curLength);\n        }\n        return _this2.renderCurveStatically(points, needClip, clipPathId, {\n          strokeDasharray: currentStrokeDasharray\n        });\n      });\n    }\n  }, {\n    key: \"renderCurve\",\n    value: function renderCurve(needClip, clipPathId) {\n      var _this$props5 = this.props,\n        points = _this$props5.points,\n        isAnimationActive = _this$props5.isAnimationActive;\n      var _this$state2 = this.state,\n        prevPoints = _this$state2.prevPoints,\n        totalLength = _this$state2.totalLength;\n      if (isAnimationActive && points && points.length && (!prevPoints && totalLength > 0 || !isEqual(prevPoints, points))) {\n        return this.renderCurveWithAnimation(needClip, clipPathId);\n      }\n      return this.renderCurveStatically(points, needClip, clipPathId);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _filterProps;\n      var _this$props6 = this.props,\n        hide = _this$props6.hide,\n        dot = _this$props6.dot,\n        points = _this$props6.points,\n        className = _this$props6.className,\n        xAxis = _this$props6.xAxis,\n        yAxis = _this$props6.yAxis,\n        top = _this$props6.top,\n        left = _this$props6.left,\n        width = _this$props6.width,\n        height = _this$props6.height,\n        isAnimationActive = _this$props6.isAnimationActive,\n        id = _this$props6.id;\n      if (hide || !points || !points.length) {\n        return null;\n      }\n      var isAnimationFinished = this.state.isAnimationFinished;\n      var hasSinglePoint = points.length === 1;\n      var layerClass = clsx('recharts-line', className);\n      var needClipX = xAxis && xAxis.allowDataOverflow;\n      var needClipY = yAxis && yAxis.allowDataOverflow;\n      var needClip = needClipX || needClipY;\n      var clipPathId = isNil(id) ? this.id : id;\n      var _ref2 = (_filterProps = filterProps(dot, false)) !== null && _filterProps !== void 0 ? _filterProps : {\n          r: 3,\n          strokeWidth: 2\n        },\n        _ref2$r = _ref2.r,\n        r = _ref2$r === void 0 ? 3 : _ref2$r,\n        _ref2$strokeWidth = _ref2.strokeWidth,\n        strokeWidth = _ref2$strokeWidth === void 0 ? 2 : _ref2$strokeWidth;\n      var _ref3 = hasClipDot(dot) ? dot : {},\n        _ref3$clipDot = _ref3.clipDot,\n        clipDot = _ref3$clipDot === void 0 ? true : _ref3$clipDot;\n      var dotSize = r * 2 + strokeWidth;\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: layerClass\n      }, needClipX || needClipY ? /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n        id: \"clipPath-\".concat(clipPathId)\n      }, /*#__PURE__*/React.createElement(\"rect\", {\n        x: needClipX ? left : left - width / 2,\n        y: needClipY ? top : top - height / 2,\n        width: needClipX ? width : width * 2,\n        height: needClipY ? height : height * 2\n      })), !clipDot && /*#__PURE__*/React.createElement(\"clipPath\", {\n        id: \"clipPath-dots-\".concat(clipPathId)\n      }, /*#__PURE__*/React.createElement(\"rect\", {\n        x: left - dotSize / 2,\n        y: top - dotSize / 2,\n        width: width + dotSize,\n        height: height + dotSize\n      }))) : null, !hasSinglePoint && this.renderCurve(needClip, clipPathId), this.renderErrorBar(needClip, clipPathId), (hasSinglePoint || dot) && this.renderDots(needClip, clipDot, clipPathId), (!isAnimationActive || isAnimationFinished) && LabelList.renderCallByParent(this.props, points));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      if (nextProps.animationId !== prevState.prevAnimationId) {\n        return {\n          prevAnimationId: nextProps.animationId,\n          curPoints: nextProps.points,\n          prevPoints: prevState.curPoints\n        };\n      }\n      if (nextProps.points !== prevState.curPoints) {\n        return {\n          curPoints: nextProps.points\n        };\n      }\n      return null;\n    }\n  }, {\n    key: \"repeat\",\n    value: function repeat(lines, count) {\n      var linesUnit = lines.length % 2 !== 0 ? [].concat(_toConsumableArray(lines), [0]) : lines;\n      var result = [];\n      for (var i = 0; i < count; ++i) {\n        result = [].concat(_toConsumableArray(result), _toConsumableArray(linesUnit));\n      }\n      return result;\n    }\n  }, {\n    key: \"renderDotItem\",\n    value: function renderDotItem(option, props) {\n      var dotItem;\n      if ( /*#__PURE__*/React.isValidElement(option)) {\n        dotItem = /*#__PURE__*/React.cloneElement(option, props);\n      } else if (isFunction(option)) {\n        dotItem = option(props);\n      } else {\n        var key = props.key,\n          dotProps = _objectWithoutProperties(props, _excluded2);\n        var className = clsx('recharts-line-dot', typeof option !== 'boolean' ? option.className : '');\n        dotItem = /*#__PURE__*/React.createElement(Dot, _extends({\n          key: key\n        }, dotProps, {\n          className: className\n        }));\n      }\n      return dotItem;\n    }\n  }]);\n}(PureComponent);\n_defineProperty(Line, \"displayName\", 'Line');\n_defineProperty(Line, \"defaultProps\", {\n  xAxisId: 0,\n  yAxisId: 0,\n  connectNulls: false,\n  activeDot: true,\n  dot: true,\n  legendType: 'line',\n  stroke: '#3182bd',\n  strokeWidth: 1,\n  fill: '#fff',\n  points: [],\n  isAnimationActive: !Global.isSsr,\n  animateNewValues: true,\n  animationBegin: 0,\n  animationDuration: 1500,\n  animationEasing: 'ease',\n  hide: false,\n  label: false\n});\n/**\n * Compose the data of each group\n * @param {Object} props The props from the component\n * @param  {Object} xAxis   The configuration of x-axis\n * @param  {Object} yAxis   The configuration of y-axis\n * @param  {String} dataKey The unique key of a group\n * @return {Array}  Composed data\n */\n_defineProperty(Line, \"getComposedData\", function (_ref4) {\n  var props = _ref4.props,\n    xAxis = _ref4.xAxis,\n    yAxis = _ref4.yAxis,\n    xAxisTicks = _ref4.xAxisTicks,\n    yAxisTicks = _ref4.yAxisTicks,\n    dataKey = _ref4.dataKey,\n    bandSize = _ref4.bandSize,\n    displayedData = _ref4.displayedData,\n    offset = _ref4.offset;\n  var layout = props.layout;\n  var points = displayedData.map(function (entry, index) {\n    var value = getValueByDataKey(entry, dataKey);\n    if (layout === 'horizontal') {\n      return {\n        x: getCateCoordinateOfLine({\n          axis: xAxis,\n          ticks: xAxisTicks,\n          bandSize: bandSize,\n          entry: entry,\n          index: index\n        }),\n        y: isNil(value) ? null : yAxis.scale(value),\n        value: value,\n        payload: entry\n      };\n    }\n    return {\n      x: isNil(value) ? null : xAxis.scale(value),\n      y: getCateCoordinateOfLine({\n        axis: yAxis,\n        ticks: yAxisTicks,\n        bandSize: bandSize,\n        entry: entry,\n        index: index\n      }),\n      value: value,\n      payload: entry\n    };\n  });\n  return _objectSpread({\n    points: points,\n    layout: layout\n  }, offset);\n});", "/**\n * @fileOverview Line Chart\n */\nimport { generateCategoricalChart } from './generateCategoricalChart';\nimport { Line } from '../cartesian/Line';\nimport { XAxis } from '../cartesian/XAxis';\nimport { YAxis } from '../cartesian/YAxis';\nimport { formatAxisMap } from '../util/CartesianUtils';\nexport var LineChart = generateCategoricalChart({\n  chartName: 'LineChart',\n  GraphicalChild: Line,\n  axisComponents: [{\n    axisType: 'xAxis',\n    AxisComp: XAxis\n  }, {\n    axisType: 'yAxis',\n    AxisComp: YAxis\n  }],\n  formatAxisMap: formatAxisMap\n});", "import React, { useState, useEffect } from \"react\"\nimport { useAuth } from \"../contexts/AuthContext\"\nimport { useNavigate } from \"react-router-dom\"\nimport { format } from \"date-fns\"\n\n// UI Components\nimport { Button } from \"../components/ui/button\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from \"../components/ui/card\"\nimport { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from \"../components/ui/tabs\"\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"../components/ui/table\"\nimport { Badge } from \"../components/ui/badge\"\nimport { Skeleton } from \"../components/ui/skeleton\"\nimport { useToast } from \"../components/ui/use-toast\"\n\n// Icons\nimport { \n  DollarSign, \n  CreditCard, \n  TrendingUp, \n  RefreshCw, \n  ArrowUpRight, \n  ArrowDownRight,\n  Activity,\n  Copy,\n  BarChart2,\n  <PERSON><PERSON><PERSON> as <PERSON><PERSON>hartI<PERSON>,\n  <PERSON><PERSON><PERSON>,\n  Filter,\n  Download,\n  Plus,\n  ArrowRight,\n  CheckCircle,\n  XCircle,\n  Clock,\n  AlertCircle,\n  ChevronDown,\n  ChevronUp,\n  ExternalLink\n} from \"lucide-react\"\n\n// Charts\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, Legend } from \"recharts\"\n\n// Types\ninterface Trade {\n  id: string;\n  pair: string;\n  type: 'buy' | 'sell';\n  price: number;\n  amount: number;\n  total: number;\n  fee: number;\n  status: 'completed' | 'pending' | 'canceled';\n  date: string;\n  time?: string; // Add optional time property for display\n}\n\ninterface Balance {\n  asset: string;\n  free: string;\n  used: string;\n  total: string;\n  btcValue: number;\n  usdValue: number;\n}\n\nconst API_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api/v1';\n\nconst TraderDashboard: React.FC = () => {\n  const { user, logout } = useAuth();\n  const { toast } = useToast();\n  const navigate = useNavigate();\n  \n  const [isLoading, setIsLoading] = useState(true);\n  const [activeTab, setActiveTab] = useState('overview');\n  const [timeframe, setTimeframe] = useState('24h');\n  const [showBalances, setShowBalances] = useState(true);\n  \n  // Stats interface\n  interface StatItem {\n    name: string;\n    value: string | number;\n    change?: number;\n    icon: any; // You might want to replace 'any' with the correct icon type\n  }\n\n  // Mock data - replace with actual API calls\n  // Using const instead of state since we're not updating these values\n  const stats = {\n    totalVolume: 12345.67,\n    totalTrades: 1234,\n    activePairs: 24,\n    dailyVolume: 1234.56,\n    dailyChange: -2.3,\n    pnl: 1234.56,\n    pnlPercent: 12.5,\n    winRate: 67.8,\n    avgTradeSize: 456.78,\n  };\n\n  // Stats cards data\n  const statsCards: StatItem[] = [\n    {\n      name: 'Total Volume',\n      value: `$${stats.totalVolume.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`,\n      change: stats.dailyChange,\n      icon: DollarSign\n    },\n    {\n      name: 'Total Trades',\n      value: stats.totalTrades.toLocaleString(),\n      icon: Activity\n    },\n    {\n      name: 'Active Pairs',\n      value: stats.activePairs,\n      icon: BarChart2\n    },\n    {\n      name: 'Daily P&L',\n      value: `$${stats.pnl.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`,\n      change: stats.pnlPercent,\n      icon: TrendingUp\n    }\n  ];\n  \n  const recentTrades: Trade[] = [\n    { \n      id: 'TRD123', \n      pair: 'BTC/USD', \n      type: 'buy', \n      price: 42123.45, \n      amount: 0.5, \n      total: 21061.73, \n      fee: 25, \n      status: 'completed', \n      date: new Date().toISOString(),\n      time: '10:23:45' \n    },\n    { \n      id: 'TRD124', \n      pair: 'ETH/USD', \n      type: 'sell', \n      price: 2345.67, \n      amount: 2.1, \n      total: 4925.91, \n      fee: 12.31, \n      status: 'completed', \n      date: new Date().toISOString(),\n      time: '09:45:12' \n    },\n    { \n      id: 'TRD125', \n      pair: 'SOL/USD', \n      type: 'buy', \n      price: 123.45, \n      amount: 50, \n      total: 6172.50, \n      fee: 15.43, \n      status: 'pending', \n      date: new Date().toISOString(),\n      time: '08:12:33' \n    }\n  ];\n  \n  const balances: Balance[] = [\n    { asset: 'BTC', free: '0.5', used: '0.1', total: '0.6', btcValue: 0.6, usdValue: 30000 },\n    { asset: 'ETH', free: '5', used: '1.5', total: '6.5', btcValue: 0.5, usdValue: 25000 },\n    { asset: 'USDT', free: '10000', used: '2000', total: '12000', btcValue: 0.24, usdValue: 12000 },\n  ];\n  \n  const priceData: {name: string; price: number}[] = [\n    { name: '9:00', price: 40000 },\n    { name: '10:00', price: 40500 },\n    { name: '11:00', price: 39800 },\n    { name: '12:00', price: 41000 },\n    { name: '13:00', price: 41500 },\n    { name: '14:00', price: 41200 },\n    { name: '15:00', price: 41800 }\n  ];\n\n  // Format currency\n  const formatCurrency = (amount: number, currency: string = 'USD'): string => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency,\n      minimumFractionDigits: 2,\n      maximumFractionDigits: 2\n    }).format(amount);\n  };\n\n  // Format date\n  const formatDate = (dateString: string, formatStr: string = 'MMM dd, yyyy HH:mm'): string => {\n    return format(new Date(dateString), formatStr);\n  };\n\n  // Copy text to clipboard\n  const copyToClipboard = (text: string, message: string) => {\n    navigator.clipboard.writeText(text);\n    toast({\n      title: 'Copied!',\n      description: message,\n      variant: 'default',\n    });\n  };\n\n  // Fetch dashboard data - using mock data for now\n  const fetchDashboardData = async () => {\n    try {\n      // Simulate API call with mock data\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      console.log('Fetched dashboard data');\n    } catch (error) {\n      console.error('Error fetching dashboard data:', error);\n    }\n  };\n\n  const handleLogout = () => {\n    logout()\n    navigate('/login')\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <header className=\"bg-white shadow\">\n        <div className=\"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8 flex justify-between items-center\">\n          <h1 className=\"text-3xl font-bold text-gray-900\">Trader Dashboard</h1>\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"text-right\">\n              <p className=\"text-sm text-gray-500\">Connected as</p>\n              <p className=\"font-medium\">{user?.email}</p>\n            </div>\n            <Button onClick={handleLogout} variant=\"outline\">\n              Logout\n            </Button>\n          </div>\n        </div>\n      </header>\n\n      <main className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n        {/* Stats */}\n        <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-8\">\n          {statsCards.map((stat) => {\n            const Icon = stat.icon;\n            return (\n              <Card key={stat.name}>\n                <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                  <CardTitle className=\"text-sm font-medium\">\n                    {stat.name}\n                  </CardTitle>\n                  <Icon className=\"h-4 w-4 text-muted-foreground\" />\n                </CardHeader>\n                <CardContent>\n                  <div className=\"text-2xl font-bold\">{stat.value}</div>\n                  {stat.change !== undefined && (\n                    <p className={`text-xs ${stat.change >= 0 ? 'text-green-500' : 'text-red-500'}`}>\n                      {stat.change >= 0 ? '+' : ''}{stat.change}% from yesterday\n                    </p>\n                  )}\n                </CardContent>\n              </Card>\n            );\n          })}\n        </div>\n\n        <div className=\"grid gap-4 md:grid-cols-3\">\n          <div className=\"md:col-span-2\">\n            <Card>\n              <CardHeader>\n                <CardTitle>BTC/USD Price</CardTitle>\n              </CardHeader>\n              <CardContent className=\"pl-2\">\n                <div className=\"h-[300px]\">\n                  <ResponsiveContainer width=\"100%\" height=\"100%\">\n                    <LineChart data={priceData}>\n                      <CartesianGrid strokeDasharray=\"3 3\" />\n                      <XAxis dataKey=\"name\" />\n                      <YAxis domain={['auto', 'auto']} />\n                      <Tooltip />\n                      <Line \n                        type=\"monotone\" \n                        dataKey=\"price\" \n                        stroke=\"#8884d8\" \n                        strokeWidth={2}\n                        dot={false}\n                        activeDot={{ r: 6 }} \n                      />\n                    </LineChart>\n                  </ResponsiveContainer>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n          \n          <div>\n            <Card>\n              <CardHeader>\n                <CardTitle>Recent Trades</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <Table>\n                  <TableHeader>\n                    <TableRow>\n                      <TableHead>Pair</TableHead>\n                      <TableHead>Type</TableHead>\n                      <TableHead>Price</TableHead>\n                      <TableHead>Amount</TableHead>\n                      <TableHead>Total</TableHead>\n                    </TableRow>\n                  </TableHeader>\n                  <TableBody>\n                    {recentTrades.map((trade) => (\n                      <TableRow key={trade.id}>\n                        <TableCell className=\"font-medium\">{trade.pair}</TableCell>\n                        <TableCell className={trade.type.toLowerCase() === 'buy' ? 'text-green-500' : 'text-red-500'}>\n                          {trade.type.charAt(0).toUpperCase() + trade.type.slice(1)}\n                        </TableCell>\n                        <TableCell>{trade.price}</TableCell>\n                        <TableCell>{trade.amount}</TableCell>\n                        <TableCell>{trade.total}</TableCell>\n                      </TableRow>\n                    ))}\n                  </TableBody>\n                </Table>\n              </CardContent>\n            </Card>\n          </div>\n        </div>\n\n        <div className=\"mt-8\n        \">\n          <Tabs defaultValue=\"open-orders\" className=\"space-y-4\">\n            <TabsList>\n              <TabsTrigger value=\"open-orders\">Open Orders</TabsTrigger>\n              <TabsTrigger value=\"order-history\">Order History</TabsTrigger>\n              <TabsTrigger value=\"positions\">Positions</TabsTrigger>\n            </TabsList>\n            \n            <TabsContent value=\"open-orders\">\n              <Card>\n                <CardHeader>\n                  <CardTitle>Open Orders</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <p className=\"text-gray-500 text-center py-8\">No open orders</p>\n                </CardContent>\n              </Card>\n            </TabsContent>\n\n            <TabsContent value=\"order-history\">\n              <Card>\n                <CardHeader>\n                  <CardTitle>Order History</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <p className=\"text-gray-500 text-center py-8\">No order history available</p>\n                </CardContent>\n              </Card>\n            </TabsContent>\n\n            <TabsContent value=\"positions\">\n              <Card>\n                <CardHeader>\n                  <CardTitle>Positions</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <p className=\"text-gray-500 text-center py-8\">No open positions</p>\n                </CardContent>\n              </Card>\n            </TabsContent>\n          </Tabs>\n        </div>\n      </main>\n    </div>\n  )\n}\n\nexport default TraderDashboard;\n", "import * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n", "import * as React from \"react\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardDescription,\n  CardContent,\n}\n", "import * as React from 'react';\nimport { ToastProps, ToastActionElement } from './toast';\n\ntype ToasterToast = ToastProps & {\n  id: string;\n  title?: React.ReactNode;\n  description?: React.ReactNode;\n};\n\nconst actionTypes = {\n  ADD_TOAST: 'ADD_TOAST',\n  UPDATE_TOAST: 'UPDATE_TOAST',\n  DISMISS_TOAST: 'DISMISS_TOAST',\n  REMOVE_TOAST: 'REMOVE_TOAST',\n} as const;\n\nlet count = 0;\n\nfunction genId() {\n  count = (count + 1) % Number.MAX_SAFE_INTEGER;\n  return count.toString();\n}\n\ntype ActionType = typeof actionTypes;\n\ntype Action =\n  | {\n      type: ActionType['ADD_TOAST'];\n      toast: ToasterToast;\n    }\n  | {\n      type: ActionType['UPDATE_TOAST'];\n      toast: Partial<ToasterToast>;\n    }\n  | {\n      type: ActionType['DISMISS_TOAST'];\n      toastId?: ToasterToast['id'];\n    }\n  | {\n      type: ActionType['REMOVE_TOAST'];\n      toastId?: ToasterToast['id'];\n    };\n\ninterface State {\n  toasts: ToasterToast[];\n}\n\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>();\n\nconst addToRemoveQueue = (toastId: string) => {\n  if (toastTimeouts.has(toastId)) {\n    return;\n  }\n\n  const timeout = setTimeout(() => {\n    toastTimeouts.delete(toastId);\n    dispatch({\n      type: 'REMOVE_TOAST',\n      toastId: toastId,\n    });\n  }, 1000);\n\n  toastTimeouts.set(toastId, timeout);\n};\n\nexport const reducer = (state: State, action: Action): State => {\n  switch (action.type) {\n    case 'ADD_TOAST':\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, 5),\n      };\n\n    case 'UPDATE_TOAST':\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\n        ),\n      };\n\n    case 'DISMISS_TOAST': {\n      const { toastId } = action;\n\n      // ! Side effects ! - This could be extracted into a dismissToast() action,\n      // but I'll keep it here for simplicity\n      if (toastId) {\n        addToRemoveQueue(toastId);\n      } else {\n        state.toasts.forEach((toast) => {\n          addToRemoveQueue(toast.id);\n        });\n      }\n\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                open: false,\n              }\n            : t\n        ),\n      };\n    }\n    case 'REMOVE_TOAST':\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        };\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\n      };\n  }\n};\n\nconst listeners: Array<(state: State) => void> = [];\n\nlet memoryState: State = { toasts: [] };\n\nfunction dispatch(action: Action) {\n  memoryState = reducer(memoryState, action);\n  listeners.forEach((listener) => {\n    listener(memoryState);\n  });\n}\n\ntype Toast = Omit<ToasterToast, 'id'>;\n\nfunction toast({ ...props }: Toast) {\n  const id = genId();\n\n  const update = (props: ToasterToast) =>\n    dispatch({\n      type: 'UPDATE_TOAST',\n      toast: { ...props, id },\n    });\n  const dismiss = () => dispatch({ type: 'DISMISS_TOAST', toastId: id });\n\n  dispatch({\n    type: 'ADD_TOAST',\n    toast: {\n      ...props,\n      id,\n      open: true,\n      onOpenChange: (open: boolean) => {\n        if (!open) dismiss();\n      },\n    },\n  });\n\n  return {\n    id: id,\n    dismiss,\n    update,\n  };\n}\n\nfunction useToast() {\n  const [state, setState] = React.useState<State>(memoryState);\n\n  React.useEffect(() => {\n    listeners.push(setState);\n    return () => {\n      const index = listeners.indexOf(setState);\n      if (index > -1) {\n        listeners.splice(index, 1);\n      }\n    };\n  }, [state]);\n\n  return {\n    ...state,\n    toast,\n    dismiss: (toastId?: string) => dispatch({ type: 'DISMISS_TOAST', toastId }),\n  };\n}\n\nexport { useToast, toast };\n", "import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": ["Table", "React", "_ref", "ref", "className", "props", "_jsx", "children", "cn", "displayName", "TableHeader", "_ref2", "TableBody", "_ref3", "_ref4", "TableRow", "_ref5", "TableHead", "_ref6", "TableCell", "_ref7", "_ref8", "useReactId", "trim", "toString", "count", "useId", "deterministicId", "id", "setId", "useLayoutEffect", "reactId", "String", "Activity", "createLucideIcon", "d", "key", "BarChart2", "x1", "x2", "y1", "y2", "TrendingUp", "points", "_excluded", "_excluded2", "_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "_objectWithoutProperties", "source", "excluded", "i", "target", "Object", "hasOwnProperty", "call", "indexOf", "_objectWithoutPropertiesLoose", "getOwnPropertySymbols", "sourceSymbolKeys", "length", "propertyIsEnumerable", "_extends", "assign", "bind", "arguments", "apply", "this", "ownKeys", "e", "r", "t", "keys", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_toConsumableArray", "arr", "Array", "isArray", "_arrayLikeToArray", "_arrayWithoutHoles", "iter", "from", "_iterableToArray", "minLen", "n", "slice", "name", "test", "_unsupportedIterableToArray", "TypeError", "_nonIterableSpread", "len", "arr2", "_defineProperties", "descriptor", "configurable", "writable", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_callSuper", "_getPrototypeOf", "self", "ReferenceError", "_assertThisInitialized", "_possibleConstructorReturn", "_isNativeReflectConstruct", "Reflect", "construct", "Boolean", "valueOf", "setPrototypeOf", "getPrototypeOf", "__proto__", "_setPrototypeOf", "p", "obj", "value", "toPrimitive", "Number", "_toPrimitive", "Line", "_PureComponent", "_this", "instance", "<PERSON><PERSON><PERSON><PERSON>", "_classCallCheck", "_len", "args", "_key", "concat", "isAnimationFinished", "totalLength", "lines", "lineLength", "reduce", "pre", "next", "generateSimpleStrokeDasharray", "Math", "floor", "<PERSON><PERSON><PERSON><PERSON>", "restLength", "remainLines", "sum", "emptyLines", "repeat", "map", "line", "join", "uniqueId", "node", "mainCurve", "setState", "onAnimationEnd", "onAnimationStart", "subClass", "superClass", "create", "_inherits", "staticProps", "nextProps", "prevState", "animationId", "prevAnimationId", "curPoints", "prevPoints", "linesUnit", "result", "option", "dotItem", "isFunction", "dotProps", "clsx", "Dot", "protoProps", "isAnimationActive", "getTotalLength", "state", "curveDom", "err", "needClip", "clipPathId", "_this$props", "xAxis", "yAxis", "layout", "errorBarItems", "findAllByType", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataPointFormatter", "dataPoint", "dataKey", "x", "y", "errorVal", "getValueByDataKey", "payload", "errorBarProps", "clipPath", "Layer", "item", "data", "clipDot", "_this$props2", "dot", "lineProps", "filterProps", "customDotProps", "dots", "entry", "index", "cx", "cy", "renderDotItem", "dotsProps", "_this$props3", "type", "connectNulls", "others", "curveProps", "fill", "Curve", "pathRef", "_this2", "_this$props4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animationBegin", "animationDuration", "animationEasing", "animate<PERSON>ew<PERSON><PERSON><PERSON>", "width", "height", "_this$state", "Animate", "begin", "duration", "isActive", "easing", "to", "handleAnimationEnd", "handleAnimationStart", "prevPointsDiffFactor", "stepData", "prevPointIndex", "prev", "interpolatorX", "interpolateNumber", "interpolatorY", "_interpolatorX", "_interpolatorY", "renderCurveStatically", "currentStrokeDasharray", "curL<PERSON>th", "interpolator", "split", "num", "parseFloat", "getStrokeDasharray", "_this$props5", "_this$state2", "isEqual", "renderCurveWithAnimation", "_filterProps", "_this$props6", "hide", "top", "left", "hasSinglePoint", "layerClass", "needClipX", "allowDataOverflow", "needClipY", "isNil", "strokeWidth", "_ref2$r", "_ref2$strokeWidth", "_ref3$clipDot", "hasClipDot", "dotSize", "renderCurve", "renderErrorBar", "renderDots", "LabelList", "renderCallByParent", "PureComponent", "xAxisId", "yAxisId", "activeDot", "legendType", "stroke", "Global", "isSsr", "label", "xAxisTicks", "yAxisTicks", "bandSize", "displayedData", "offset", "getCateCoordinateOfLine", "axis", "ticks", "scale", "Line<PERSON>hart", "generateCategoricalChart", "chartName", "GraphicalChild", "axisComponents", "axisType", "AxisComp", "XAxis", "YA<PERSON>s", "formatAxisMap", "TraderDashboard", "user", "logout", "useAuth", "toast", "useToast", "navigate", "useNavigate", "isLoading", "setIsLoading", "useState", "activeTab", "setActiveTab", "timeframe", "setTimeframe", "showBalances", "setShowBalances", "stats", "totalVolume", "totalTrades", "activePairs", "dailyVolume", "dailyChange", "pnl", "pnlPercent", "winRate", "avgTradeSize", "statsCards", "toLocaleString", "undefined", "minimumFractionDigits", "maximumFractionDigits", "change", "icon", "DollarSign", "recentTrades", "pair", "price", "amount", "total", "fee", "status", "date", "Date", "toISOString", "time", "_jsxs", "email", "<PERSON><PERSON>", "onClick", "handleLogout", "variant", "stat", "Icon", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "<PERSON><PERSON><PERSON><PERSON>", "ResponsiveContainer", "Cartesian<PERSON><PERSON>", "domain", "<PERSON><PERSON><PERSON>", "trade", "toLowerCase", "char<PERSON>t", "toUpperCase", "Tabs", "defaultValue", "TabsList", "TabsTrigger", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TabsPrimitive", "CardDescription", "<PERSON><PERSON><PERSON>er", "toastTimeouts", "Map", "addToRemoveQueue", "toastId", "has", "timeout", "setTimeout", "delete", "dispatch", "set", "reducer", "action", "toasts", "open", "listeners", "memoryState", "listener", "MAX_SAFE_INTEGER", "dismiss", "onOpenChange", "update", "splice", "buttonVariants", "cva", "variants", "default", "destructive", "outline", "secondary", "ghost", "link", "size", "sm", "lg", "defaultVariants", "<PERSON><PERSON><PERSON><PERSON>", "Comp", "Slot"], "sourceRoot": ""}