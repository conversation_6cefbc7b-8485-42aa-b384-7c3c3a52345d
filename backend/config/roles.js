/**
 * User roles and permissions configuration
 * Defines the available roles and their associated permissions
 */

// Available roles in the system
const ROLES = {
  ADMIN: 'admin',
  MERCHANT: 'merchant',
  TRADER: 'trader',
  USER: 'user'
};

// Role hierarchy (higher roles inherit permissions from lower roles)
const ROLE_HIERARCHY = [
  ROLES.USER,
  ROLES.TRADER,
  ROLES.MERCHANT,
  ROLES.ADMIN
];

// Permissions for each role
const PERMISSIONS = {
  [ROLES.ADMIN]: [
    'manage_users',
    'manage_merchants',
    'manage_traders',
    'view_dashboard',
    'manage_settings',
    'view_reports',
    'process_refunds',
    'view_transactions',
    'manage_roles',
    'manage_permissions'
  ],
  [ROLES.MERCHANT]: [
    'manage_own_store',
    'view_dashboard',
    'view_own_transactions',
    'process_refunds',
    'view_reports',
    'manage_own_products',
    'manage_own_orders'
  ],
  [ROLES.TRADER]: [
    'view_dashboard',
    'view_own_transactions',
    'view_reports',
    'manage_own_products',
    'manage_own_orders'
  ],
  [ROLES.USER]: [
    'view_own_profile',
    'update_own_profile',
    'view_own_transactions',
    'make_payments'
  ]
};

// Export the roles and permissions
module.exports = {
  ROLES,
  ROLE_HIERARCHY,
  PERMISSIONS,
  
  /**
   * Check if a role has a specific permission
   * @param {string} role - The role to check
   * @param {string} permission - The permission to verify
   * @returns {boolean} True if the role has the permission
   */
  hasPermission(role, permission) {
    // Admin has all permissions
    if (role === ROLES.ADMIN) return true;
    
    // Check if the role exists and has the permission
    return PERMISSIONS[role] && PERMISSIONS[role].includes(permission);
  },
  
  /**
   * Check if a role has access to another role's resources
   * @param {string} userRole - The role of the user making the request
   * @param {string} targetRole - The role of the target resource
   * @returns {boolean} True if the user role can access the target role's resources
   */
  canAccessRole(userRole, targetRole) {
    const userLevel = ROLE_HIERARCHY.indexOf(userRole);
    const targetLevel = ROLE_HIERARCHY.indexOf(targetRole);
    
    // If either role is not found, deny access
    if (userLevel === -1 || targetLevel === -1) return false;
    
    // User can access roles at or below their level
    return userLevel >= targetLevel;
  },
  
  /**
   * Get all roles that the specified role can manage
   * @param {string} role - The role to check
   * @returns {string[]} Array of manageable roles
   */
  getManageableRoles(role) {
    const roleLevel = ROLE_HIERARCHY.indexOf(role);
    return roleLevel >= 0 ? ROLE_HIERARCHY.slice(0, roleLevel + 1) : [];
  }
};
