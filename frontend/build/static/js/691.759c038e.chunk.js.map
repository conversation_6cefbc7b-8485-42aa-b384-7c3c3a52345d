{"version": 3, "file": "static/js/691.759c038e.chunk.js", "mappings": "8LASO,SAASA,EAAoBC,GAGL,IAHM,MACnCC,EAAK,YACLC,EAAc,+D<PERSON><PERSON><PERSON>,EAC1B,MAAMG,GAAWC,EAAAA,EAAAA,MAEjB,OACEC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yBAAwBC,UACrCC,EAAAA,EAAAA,MAACC,EAAAA,GAAI,CAAAF,SAAA,EACHF,EAAAA,EAAAA,KAACK,EAAAA,GAAU,CAAAH,UACTF,EAAAA,EAAAA,KAACM,EAAAA,GAAS,CAACL,UAAU,qBAAoBC,SAAEN,OAE7CO,EAAAA,EAAAA,MAACI,EAAAA,GAAW,CAACN,UAAU,YAAWC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wBAAuBC,SAAEL,KACtCM,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBC,SAAA,EAC7BF,EAAAA,EAAAA,KAACQ,EAAAA,EAAM,CAACC,QAASA,IAAMX,GAAU,GAAIY,QAAQ,UAASR,SAAC,aAGvDF,EAAAA,EAAAA,KAACQ,EAAAA,EAAM,CAACC,QAASA,IAAMX,EAAS,UAAUI,SAAC,kCAQvD,C,iHC/BA,MAAME,EAAOO,EAAAA,WAGX,CAAAhB,EAA0BiB,KAAG,IAA5B,UAAEX,KAAcY,GAAOlB,EAAA,OACxBK,EAAAA,EAAAA,KAAA,OACEY,IAAKA,EACLX,WAAWa,EAAAA,EAAAA,IACT,2DACAb,MAEEY,MAGRT,EAAKW,YAAc,OAEnB,MAAMV,EAAaM,EAAAA,WAGjB,CAAAK,EAA0BJ,KAAG,IAA5B,UAAEX,KAAcY,GAAOG,EAAA,OACxBhB,EAAAA,EAAAA,KAAA,OACEY,IAAKA,EACLX,WAAWa,EAAAA,EAAAA,IAAG,gCAAiCb,MAC3CY,MAGRR,EAAWU,YAAc,aAEzB,MAAMT,EAAYK,EAAAA,WAGhB,CAAAM,EAA0BL,KAAG,IAA5B,UAAEX,KAAcY,GAAOI,EAAA,OACxBjB,EAAAA,EAAAA,KAAA,MACEY,IAAKA,EACLX,WAAWa,EAAAA,EAAAA,IACT,qDACAb,MAEEY,MAGRP,EAAUS,YAAc,YAExB,MAAMG,EAAkBP,EAAAA,WAGtB,CAAAQ,EAA0BP,KAAG,IAA5B,UAAEX,KAAcY,GAAOM,EAAA,OACxBnB,EAAAA,EAAAA,KAAA,KACEY,IAAKA,EACLX,WAAWa,EAAAA,EAAAA,IAAG,gCAAiCb,MAC3CY,MAGRK,EAAgBH,YAAc,kBAE9B,MAAMR,EAAcI,EAAAA,WAGlB,CAAAS,EAA0BR,KAAG,IAA5B,UAAEX,KAAcY,GAAOO,EAAA,OACxBpB,EAAAA,EAAAA,KAAA,OAAKY,IAAKA,EAAKX,WAAWa,EAAAA,EAAAA,IAAG,WAAYb,MAAgBY,MAE3DN,EAAYQ,YAAc,cAE1B,MAAMM,EAAaV,EAAAA,WAGjB,CAAAW,EAA0BV,KAAG,IAA5B,UAAEX,KAAcY,GAAOS,EAAA,OACxBtB,EAAAA,EAAAA,KAAA,OACEY,IAAKA,EACLX,WAAWa,EAAAA,EAAAA,IAAG,6BAA8Bb,MACxCY,MAGRQ,EAAWN,YAAc,Y,uFC1ElB,SAASQ,IACd,OACEvB,EAAAA,EAAAA,KAACN,EAAAA,EAAoB,CACnBE,MAAM,sBACNC,YAAY,sEAGlB,CAEA,S,sFCLA,MAAM2B,GAAiBC,EAAAA,EAAAA,GACrB,yRACA,CACEC,SAAU,CACRhB,QAAS,CACPiB,QAAS,yDACTC,YACE,qEACFC,QACE,iFACFC,UACE,+DACFC,MAAO,+CACPC,KAAM,mDAERC,KAAM,CACJN,QAAS,iBACTO,GAAI,sBACJC,GAAI,uBACJC,KAAM,cAGVC,gBAAiB,CACf3B,QAAS,UACTuB,KAAM,aAWNzB,EAASG,EAAAA,WACb,CAAAhB,EAA0DiB,KAAS,IAAlE,UAAEX,EAAS,QAAES,EAAO,KAAEuB,EAAI,QAAEK,GAAU,KAAUzB,GAAOlB,EACtD,MAAM4C,EAAOD,EAAUE,EAAAA,GAAO,SAC9B,OACExC,EAAAA,EAAAA,KAACuC,EAAI,CACHtC,WAAWa,EAAAA,EAAAA,IAAGU,EAAe,CAAEd,UAASuB,OAAMhC,eAC9CW,IAAKA,KACDC,MAKZL,EAAOO,YAAc,Q", "sources": ["pages/admin/AdminPagePlaceholder.tsx", "components/ui/card.tsx", "pages/admin/ReportsPage.tsx", "components/ui/button.tsx"], "sourcesContent": ["import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from \"../../components/ui/card\";\nimport { Button } from \"../../components/ui/button\";\nimport { useNavigate } from \"react-router-dom\";\n\ninterface AdminPagePlaceholderProps {\n  title: string;\n  description?: string;\n}\n\nexport function AdminPagePlaceholder({ \n  title, \n  description = \"This page is under construction and will be available soon.\" \n}: AdminPagePlaceholderProps) {\n  const navigate = useNavigate();\n  \n  return (\n    <div className=\"container mx-auto py-8\">\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"text-2xl font-bold\">{title}</CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <p className=\"text-muted-foreground\">{description}</p>\n          <div className=\"flex space-x-4\">\n            <Button onClick={() => navigate(-1)} variant=\"outline\">\n              Go Back\n            </Button>\n            <Button onClick={() => navigate('/admin')}>\n              Return to Dashboard\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n\nexport default AdminPagePlaceholder;\n", "import * as React from \"react\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardDescription,\n  CardContent,\n}\n", "import { AdminPagePlaceholder } from './AdminPagePlaceholder';\n\nexport function ReportsPage() {\n  return (\n    <AdminPagePlaceholder \n      title=\"Reports & Analytics\"\n      description=\"Generate and view detailed reports and analytics for the platform.\"\n    />\n  );\n}\n\nexport default ReportsPage;\n", "import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": ["AdminPagePlaceholder", "_ref", "title", "description", "navigate", "useNavigate", "_jsx", "className", "children", "_jsxs", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "onClick", "variant", "React", "ref", "props", "cn", "displayName", "_ref2", "_ref3", "CardDescription", "_ref4", "_ref5", "<PERSON><PERSON><PERSON>er", "_ref6", "ReportsPage", "buttonVariants", "cva", "variants", "default", "destructive", "outline", "secondary", "ghost", "link", "size", "sm", "lg", "icon", "defaultVariants", "<PERSON><PERSON><PERSON><PERSON>", "Comp", "Slot"], "sourceRoot": ""}