import { 
  useMutation, 
  useQuery, 
  useQueryClient, 
  UseMutationOptions, 
  UseQueryOptions, 
  QueryKey,
  InvalidateQueryFilters
} from '@tanstack/react-query';
import { useAuth } from '../contexts/AuthContext';

// Define API response types
interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

// Define a type for the API request function
interface ApiRequest {
  get<T = any>(url: string): Promise<ApiResponse<T>>;
  post<T = any>(url: string, data?: any): Promise<ApiResponse<T>>;
  put<T = any>(url: string, data?: any): Promise<ApiResponse<T>>;
  delete<T = any>(url: string): Promise<ApiResponse<T>>;
}

// Mock API client - replace with your actual API client
const apiRequest: ApiRequest = {
  get: async <T = any>(url: string): Promise<ApiResponse<T>> => {
    const response = await fetch(`/api${url}`);
    if (!response.ok) {
      throw new Error('Network response was not ok');
    }
    return response.json();
  },
  post: async <T = any>(url: string, data: any = {}): Promise<ApiResponse<T>> => {
    const response = await fetch(`/api${url}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      throw new Error('Network response was not ok');
    }
    return response.json();
  },
  put: async <T = any>(url: string, data: any = {}): Promise<ApiResponse<T>> => {
    const response = await fetch(`/api${url}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      throw new Error('Network response was not ok');
    }
    return response.json();
  },
  delete: async <T = any>(url: string): Promise<ApiResponse<T>> => {
    const response = await fetch(`/api${url}`, {
      method: 'DELETE',
    });
    if (!response.ok) {
      throw new Error('Network response was not ok');
    }
    return response.json();
  },
};

// Types
type ApiError = {
  message: string;
  statusCode: number;
  errors?: Record<string, string[]>;
};

// Types
type UserProfile = {
  id: string;
  name: string;
  email: string;
  role: string;
  businessName?: string;
  businessType?: string;
  businessAddress?: {
    street?: string;
    city?: string;
    state?: string;
    country?: string;
    postalCode?: string;
  };
  twoFactorEnabled: boolean;
  createdAt: string;
  updatedAt: string;
};

type UpdateProfileData = {
  name: string;
  businessName?: string;
  businessType?: string;
  businessAddress?: {
    street?: string;
    city?: string;
    state?: string;
    country?: string;
    postalCode?: string;
  };
};

export type ChangePasswordData = {
  currentPassword: string;
  newPassword: string;
};

// Hooks
export const useCurrentUser = () => {
  // Mock user for now - this will be replaced with actual auth context
  const user = {
    id: '1',
    email: '<EMAIL>',
    role: 'admin' as const,
    name: 'Admin User',
    isVerified: true
  };
  
  // Uncomment when auth is properly set up
  // const { user } = useAuth();
  
  return useQuery<UserProfile, ApiError>({
    queryKey: ['currentUser'],
    queryFn: async () => {
      const response = await apiRequest.get<UserProfile>('/auth/me');
      return response.data;
    },
    enabled: !!user,
  });
};

type MutationOptions<TData = unknown, TVariables = unknown> = Omit<
  UseMutationOptions<TData, ApiError, TVariables>,
  'mutationFn'
>;

export const useUpdateProfile = (options: MutationOptions<UserProfile, UpdateProfileData> = {}) => {
  const queryClient = useQueryClient();
  
  return useMutation<UserProfile, ApiError, UpdateProfileData>({
    mutationFn: async (profileData) => {
      const response = await apiRequest.put<UserProfile>('/users/me', profileData);
      return response.data;
    },
    onSuccess: (data, variables, context) => {
      queryClient.setQueryData(['currentUser'], data);
      if (options.onSuccess) {
        options.onSuccess(data, variables, context);
      }
    },
    onError: options.onError,
    onSettled: options.onSettled,
    onMutate: options.onMutate,
  });
};

export const useChangePassword = (options: MutationOptions<{ success: boolean }, ChangePasswordData> = {}) => {
  return useMutation<{ success: boolean }, ApiError, ChangePasswordData>({
    mutationFn: async (passwordData) => {
      const response = await apiRequest.post<{ success: boolean }>('/auth/change-password', passwordData);
      return response.data;
    },
    onSuccess: options.onSuccess,
    onError: options.onError,
    onSettled: options.onSettled,
    onMutate: options.onMutate,
  });
};

export const useSetupTwoFactor = (options: MutationOptions<{ qrCodeData: string; secret: string }> = {}) => {
  return useMutation<{ qrCodeData: string; secret: string }, ApiError, void>({
    mutationFn: async () => {
      const response = await apiRequest.post<{ qrCodeData: string; secret: string }>('/auth/2fa/setup');
      return response.data;
    },
    onSuccess: options.onSuccess,
    onError: options.onError,
    onSettled: options.onSettled,
    onMutate: options.onMutate,
  });
};

export const useVerifyTwoFactor = (options: MutationOptions<{ recoveryCodes: string[] }, { code: string }> = {}) => {
  return useMutation<{ recoveryCodes: string[] }, ApiError, { code: string }>({
    mutationFn: async ({ code }) => {
      const response = await apiRequest.post<{ recoveryCodes: string[] }>('/auth/2fa/verify', { code });
      return response.data;
    },
    onSuccess: options.onSuccess,
    onError: options.onError,
    onSettled: options.onSettled,
    onMutate: options.onMutate,
  });
};

export const useDisableTwoFactor = (options: MutationOptions<{ success: boolean }> = {}) => {
  const queryClient = useQueryClient();
  
  return useMutation<{ success: boolean }, ApiError, void>({
    mutationFn: async () => {
      const response = await apiRequest.post<{ success: boolean }>('/auth/2fa/disable');
      return response.data;
    },
    onSuccess: (data, variables, context) => {
      queryClient.invalidateQueries(
        { queryKey: ['currentUser'] },
        { cancelRefetch: true }
      ).catch(console.error);
      if (options.onSuccess) {
        options.onSuccess(data, variables, context);
      }
    },
    onError: options.onError,
    onSettled: options.onSettled,
    onMutate: options.onMutate,
  });
};

export const useApiKeys = () => {
  return useQuery<Array<{
    id: string;
    name: string;
    key: string;
    lastUsed: string | null;
    createdAt: string;
  }>, ApiError>({
    queryKey: ['apiKeys'],
    queryFn: async () => {
      const response = await apiRequest.get<Array<{
        id: string;
        name: string;
        key: string;
        lastUsed: string | null;
        createdAt: string;
      }>>('/users/me/api-keys');
      return response.data;
    },
  });
};

type ApiKey = {
  id: string;
  name: string;
  key: string;
  lastUsed: string | null;
  createdAt: string;
};

export const useCreateApiKey = (options: MutationOptions<ApiKey, { name: string }> = {}) => {
  const queryClient = useQueryClient();
  
  return useMutation<ApiKey, ApiError, { name: string }>({
    mutationFn: async ({ name }) => {
      const response = await apiRequest.post<ApiKey>('/users/me/api-keys', { name });
      return response.data;
    },
    onSuccess: (data, variables, context) => {
      queryClient.invalidateQueries(
        { queryKey: ['apiKeys'] },
        { cancelRefetch: true }
      ).catch(console.error);
      if (options.onSuccess) {
        options.onSuccess(data, variables, context);
      }
    },
    onError: options.onError,
    onSettled: options.onSettled,
    onMutate: options.onMutate,
  });
};

export const useDeleteApiKey = (options: MutationOptions<{ success: boolean }, string> = {}) => {
  const queryClient = useQueryClient();
  
  return useMutation<{ success: boolean }, ApiError, string>({
    mutationFn: async (id) => {
      const response = await apiRequest.delete<{ success: boolean }>(`/users/me/api-keys/${id}`);
      return response.data;
    },
    onSuccess: (data, variables, context) => {
      queryClient.invalidateQueries(
        { queryKey: ['apiKeys'] },
        { cancelRefetch: true }
      ).catch(console.error);
      if (options.onSuccess) {
        options.onSuccess(data, variables, context);
      }
    },
    onError: options.onError,
    onSettled: options.onSettled,
    onMutate: options.onMutate,
  });
};

export const useActivityLogs = () => {
  return useQuery<Array<{
    id: string;
    action: string;
    description: string;
    timestamp: string;
    status: 'success' | 'failed' | 'warning';
    ip?: string;
    userAgent?: string;
  }>, ApiError>({
    queryKey: ['activityLogs'],
    queryFn: async () => {
      const response = await apiRequest.get<Array<{
        id: string;
        action: string;
        description: string;
        timestamp: string;
        status: 'success' | 'failed' | 'warning';
        ip?: string;
        userAgent?: string;
      }>>('/users/me/activity-logs');
      return response.data;
    },
  });
};
