// Test writing to a file directly
const fs = require('fs');

const testContent = `Test content written at: ${new Date().toISOString()}
` +
`Node.js version: ${process.version}
` +
`Platform: ${process.platform} (${process.arch})
`;

const outputFile = 'node_output_test.txt';

fs.writeFileSync(outputFile, testContent);
console.log(`Test content written to ${outputFile}`);

// Also write to stderr
process.stderr.write('This is a test message to stderr\n');

// Check if we can read the file back
const readContent = fs.readFileSync(outputFile, 'utf8');
console.log(`Read back from file: ${readContent}`);
