// Set up test environment variables
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-secret-key';
process.env.MONGODB_URI = 'mongodb://localhost:27017/payment-gateway-test';
process.env.PORT = 5000;

// Mock logger to prevent logging during tests
jest.mock('../utils/logger', () => ({
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
}));

// Mock external services if needed
jest.mock('nodemailer', () => ({
  createTransport: jest.fn().mockImplementation(() => ({
    verify: jest.fn((callback) => callback(null, true)),
    sendMail: jest.fn().mockResolvedValue({ messageId: 'test-message-id' }),
    on: jest.fn(),
  })),
}));

// Add global test timeout
jest.setTimeout(30000);
