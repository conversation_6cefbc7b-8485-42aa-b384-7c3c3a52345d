import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import DashboardLayout from '../layouts/DashboardLayout';
import ProtectedRoute from '../components/ProtectedRoute';
import LoadingSpinner from '../components/ui/LoadingSpinner';

// Loading component for Suspense fallback
const Loading = () => (
  <div className="flex items-center justify-center min-h-screen">
    <LoadingSpinner size="lg" />
  </div>
);

// Lazy load merchant components
const DashboardPage = lazy(() => import('../pages/merchant/DashboardPage'));
const TransactionsPage = lazy(() => import('../pages/merchant/TransactionsPage'));
const CollectionsPage = lazy(() => import('../pages/merchant/CollectionsPage'));
const BillingPage = lazy(() => import('../pages/merchant/Billing'));
const TradersPage = lazy(() => import('../pages/merchant/MerchantTraders'));
const SettingsPage = lazy(() => import('../pages/merchant/IntegrationSettings'));
const ProfilePage = lazy(() => import('../pages/merchant/Profile'));
const SupportPage = lazy(() => import('../pages/merchant/Support'));
const NotificationsPage = lazy(() => import('../pages/merchant/Notifications'));

// Merchant route configuration
export const merchantRoutes: RouteObject[] = [
  {
    path: '/merchant',
    element: (
      <ProtectedRoute role="merchant">
        <DashboardLayout title="Merchant Dashboard">
          <DashboardPage />
        </DashboardLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: '/merchant/dashboard',
    element: (
      <ProtectedRoute role="merchant">
        <DashboardLayout title="Dashboard">
          <DashboardPage />
        </DashboardLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: '/merchant/transactions',
    element: (
      <ProtectedRoute role="merchant">
        <DashboardLayout title="Transactions">
          <TransactionsPage />
        </DashboardLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: '/merchant/collections',
    element: (
      <ProtectedRoute role="merchant">
        <DashboardLayout title="Collections">
          <CollectionsPage />
        </DashboardLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: '/merchant/billing',
    element: (
      <ProtectedRoute role="merchant">
        <DashboardLayout title="Billing">
          <BillingPage />
        </DashboardLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: '/merchant/traders',
    element: (
      <ProtectedRoute role="merchant">
        <DashboardLayout title="Traders">
          <TradersPage />
        </DashboardLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: '/merchant/settings',
    element: (
      <ProtectedRoute role="merchant">
        <DashboardLayout title="Settings">
          <SettingsPage />
        </DashboardLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: '/merchant/profile',
    element: (
      <ProtectedRoute role="merchant">
        <DashboardLayout title="Profile">
          <ProfilePage />
        </DashboardLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: '/merchant/support',
    element: (
      <ProtectedRoute role="merchant">
        <DashboardLayout title="Support">
          <SupportPage />
        </DashboardLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: '/merchant/notifications',
    element: (
      <ProtectedRoute role="merchant">
        <DashboardLayout title="Notifications">
          <NotificationsPage />
        </DashboardLayout>
      </ProtectedRoute>
    ),
  },
];

// Export all page components for easier imports
export {
  DashboardPage,
  TransactionsPage,
  CollectionsPage,
  BillingPage,
  TradersPage,
  SettingsPage,
  ProfilePage,
  SupportPage,
  NotificationsPage,
};
