import { apiRequest } from '../utils/api';
import {
  TraderAssignment,
  AssignmentFilters,
  PaginatedResponse,
  PerformanceMetrics,
  AssignmentStatus,
  AssignmentFormData,
  User,
  Merchant,
  PaginationMeta
} from '../types/trader';

interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
  code?: number;
}

interface PaginatedApiResponse<T> extends ApiResponse<{ data: T[]; pagination: PaginationMeta }> {}

/**
 * Fetches trader assignments with optional filters
 */
export const getAssignments = async (
  filters: AssignmentFilters = {}
): Promise<PaginatedResponse<TraderAssignment>> => {
  const params: Record<string, string> = {};
  
  if (filters.traderId) params.traderId = filters.traderId;
  if (filters.merchantId) params.merchantId = filters.merchantId;
  if (filters.status) params.status = filters.status;
  if (filters.search) params.search = filters.search;
  if (filters.startDate) params.startDate = new Date(filters.startDate).toISOString();
  if (filters.endDate) params.endDate = new Date(filters.endDate).toISOString();
  if (filters.page) params.page = filters.page.toString();
  if (filters.limit) params.limit = filters.limit.toString();
  
  const response = await apiRequest.get<{
    success: boolean;
    data: {
      data: TraderAssignment[];
      pagination: PaginationMeta;
    };
  }>('/trader/assignments', { params });
  
  return {
    success: true,
    data: response.data.data.data,
    pagination: response.data.data.pagination
  };
};

/**
 * Fetches a single assignment by ID
 */
export const getAssignment = async (id: string): Promise<TraderAssignment> => {
  const response = await apiRequest.get<ApiResponse<TraderAssignment>>(
    `/trader/assignments/${id}`
  );
  return response.data.data;
};

/**
 * Creates a new trader assignment
 */
export const createAssignment = async (
  data: AssignmentFormData
): Promise<TraderAssignment> => {
  const response = await apiRequest.post<ApiResponse<TraderAssignment>>(
    '/trader/assignments', 
    data
  );
  return response.data.data;
};

/**
 * Updates an existing assignment
 */
export const updateAssignment = async (
  id: string, 
  data: Partial<AssignmentFormData>
): Promise<TraderAssignment> => {
  const response = await apiRequest.put<ApiResponse<TraderAssignment>>(
    `/trader/assignments/${id}`, 
    data
  );
  return response.data.data;
};

/**
 * Deletes an assignment (soft delete)
 */
export const deleteAssignment = async (
  id: string
): Promise<{ success: boolean; message: string }> => {
  const response = await apiRequest.delete<{
    success: boolean;
    message: string;
  }>(`/trader/assignments/${id}`);
  
  return response.data;
};

/**
 * Fetches assignments for the currently authenticated trader
 */
export const getMyAssignments = async (
  filters: Omit<AssignmentFilters, 'traderId'> = {}
): Promise<PaginatedResponse<TraderAssignment>> => {
  const params: Record<string, string> = {};
  
  if (filters.status) params.status = filters.status;
  if (filters.merchantId) params.merchantId = filters.merchantId;
  if (filters.search) params.search = filters.search;
  if (filters.startDate) params.startDate = new Date(filters.startDate).toISOString();
  if (filters.endDate) params.endDate = new Date(filters.endDate).toISOString();
  if (filters.page) params.page = filters.page.toString();
  if (filters.limit) params.limit = filters.limit.toString();
  
  const response = await apiRequest.get<{
    success: boolean;
    data: {
      data: TraderAssignment[];
      pagination: any;
    };
  }>('/trader/me/assignments', { params });
  
  return {
    success: true,
    data: response.data.data.data,
    pagination: response.data.data.pagination
  };
};

/**
 * Fetches performance metrics for a specific assignment
 */
export const getAssignmentMetrics = async (
  assignmentId: string
): Promise<PerformanceMetrics> => {
  const response = await apiRequest.get<ApiResponse<PerformanceMetrics>>(
    `/trader/assignments/${assignmentId}/metrics`
  );
  return response.data.data;
};

/**
 * Updates the status of an assignment
 */
export const updateAssignmentStatus = async (
  assignmentId: string, 
  status: AssignmentStatus
): Promise<TraderAssignment> => {
  const response = await apiRequest.patch<ApiResponse<TraderAssignment>>(
    `/trader/assignments/${assignmentId}/status`, 
    { status }
  );
  return response.data.data;
};

/**
 * Fetches available traders for assignment
 */
export const getAvailableTraders = async (): Promise<User[]> => {
  const response = await apiRequest.get<ApiResponse<User[]>>(
    '/trader/available-traders'
  );
  return response.data.data;
};

/**
 * Fetches merchants available for assignment to a trader
 */
export const getAvailableMerchants = async (traderId?: string): Promise<Merchant[]> => {
  const params: Record<string, string> = {};
  if (traderId) params.traderId = traderId;
  
  const response = await apiRequest.get<ApiResponse<Merchant[]>>(
    '/merchants/available',
    { params }
  );
  return response.data.data;
};
