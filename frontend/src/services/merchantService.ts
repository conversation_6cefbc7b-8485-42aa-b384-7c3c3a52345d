import axios from 'axios'
import { API_BASE_URL } from '../config'
import { CreateMerchantDto, MerchantProfile, UpdateMerchantDto } from '../types/merchant'

const API_URL = `${API_BASE_URL}/merchants`

const createFormData = (data: Record<string, unknown>) => {
  const formData = new FormData()
  Object.entries(data).forEach(([key, value]: [string, unknown]) => {
    if (value !== undefined && value !== null) {
      if (value instanceof File) {
        formData.append(key, value)
      } else if (typeof value === 'object') {
        formData.append(key, JSON.stringify(value))
      } else {
        formData.append(key, String(value))
      }
    }
  })
  return formData
}

// Extend the merchant service with admin-specific methods
export const merchantAdminService = {
  async getAllMerchants(params: { 
    page?: number; 
    limit?: number; 
    search?: string;
    status?: string;
    sortBy?: string;
    order?: 'asc' | 'desc';
  } = {}) {
    const { page = 1, limit = 10, search, status, sortBy, order } = params;
    
    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      ...(search && { search }),
      ...(status && { status }),
      ...(sortBy && { sortBy }),
      ...(order && { order })
    });
    
    const response = await axios.get(`${API_URL}/admin/merchants?${queryParams}`);
    return response.data;
  },
  
  async updateMerchantStatus(id: string, status: string, reason?: string) {
    const response = await axios.patch(
      `${API_URL}/admin/merchants/${id}/status`, 
      { status, reason },
      { headers: { 'Content-Type': 'application/json' } }
    );
    return response.data;
  },
  
  async getMerchantDetails(id: string) {
    const response = await axios.get(`${API_URL}/admin/merchants/${id}`);
    return response.data;
  },
};

export const merchantService = {
  async getMerchantProfile(): Promise<MerchantProfile> {
    const response = await axios.get(`${API_URL}/profile`)
    return response.data
  },

  async createMerchantProfile(data: CreateMerchantDto): Promise<MerchantProfile> {
    const formData = createFormData(data as Record<string, unknown>)
    const response = await axios.post(API_URL, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
    return response.data
  },

  async updateMerchantProfile(data: UpdateMerchantDto): Promise<MerchantProfile> {
    const formData = createFormData(data)
    const response = await axios.patch(API_URL, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
    return response.data
  },

  async submitForVerification(): Promise<MerchantProfile> {
    const response = await axios.post(`${API_URL}/submit-verification`)
    return response.data
  },

  async uploadDocument(file: File, type: 'documentFront' | 'documentBack'): Promise<string> {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('type', type)
    
    const response = await axios.post(`${API_URL}/documents`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
    return response.data.url
  }
}

export default merchantService
