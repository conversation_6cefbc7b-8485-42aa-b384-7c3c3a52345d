// Mock nodemailer for testing
const nodemailer = {
  createTransport: jest.fn().mockReturnThis(),
  verify: jest.fn((callback) => callback(null, true)),
  sendMail: jest.fn().mockResolvedValue({ messageId: 'test-message-id' }),
};

// Mock the transporter object
const transporter = {
  verify: jest.fn((callback) => callback(null, true)),
  sendMail: jest.fn().mockResolvedValue({ messageId: 'test-message-id' }),
  on: jest.fn(),
};

// Mock the createTransport function to return our mock transporter
nodemailer.createTransport.mockReturnValue(transporter);

module.exports = nodemailer;
