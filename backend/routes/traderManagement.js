const express = require('express');
const router = express.Router();
const { body, param, query } = require('express-validator');
const { protect, authorize } = require('../middleware/auth');
const validate = require('../middleware/validate');
const TraderAssignment = require('../models/TraderAssignment');
const User = require('../models/User');
const { asyncHandler } = require('../utils/asyncHandler');
const ApiError = require('../utils/ApiError');
const moment = require('moment');
const { checkPermission } = require('../middleware/checkPermission');

/**
 * @swagger
 * components:
 *   schemas:
 *     TraderAssignment:
 *       type: object
 *       required:
 *         - trader
 *         - merchant
 *         - startDate
 *         - status
 *       properties:
 *         id:
 *           type: string
 *           description: The auto-generated ID of the assignment
 *         trader:
 *           type: string
 *           description: ID of the trader user
 *         merchant:
 *           type: string
 *           description: ID of the merchant
 *         startDate:
 *           type: string
 *           format: date-time
 *           description: When the assignment starts
 *         endDate:
 *           type: string
 *           format: date-time
 *           description: When the assignment ends (optional)
 *         status:
 *           type: string
 *           enum: [active, inactive, completed, pending]
 *           default: pending
 *         notes:
 *           type: string
 *           description: Additional notes about the assignment
 *         assignedBy:
 *           type: string
 *           description: ID of the admin who created the assignment
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: When the assignment was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: When the assignment was last updated
 */

/**
 * @swagger
 * /api/trader/assignments:
 *   get:
 *     summary: Get all trader assignments
 *     description: Retrieve a list of trader assignments with optional filtering. Admins can view all assignments, while traders can only view their own.
 *     tags: [Trader Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: merchantId
 *         schema:
 *           type: string
 *         description: Filter by merchant ID
 *       - in: query
 *         name: traderId
 *         schema:
 *           type: string
 *         description: Filter by trader ID (admin only)
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, inactive, completed, pending]
 *           default: active
 *         description: Filter by assignment status
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter assignments starting on or after this date (YYYY-MM-DD)
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter assignments starting on or before this date (YYYY-MM-DD)
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: A paginated list of trader assignments
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 count:
 *                   type: integer
 *                   description: Number of items in current page
 *                   example: 5
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     currentPage:
 *                       type: integer
 *                       example: 1
 *                     totalPages:
 *                       type: integer
 *                       example: 3
 *                     totalItems:
 *                       type: integer
 *                       example: 25
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/TraderAssignment'
 *       400:
 *         description: Bad request - invalid query parameters
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       403:
 *         $ref: '#/components/responses/Forbidden'
 *       500:
 *         description: Internal server error
 */
const getTraderAssignments = asyncHandler(async (req, res, next) => {
  const { traderId, merchantId, status, startDate, endDate } = req.query;
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const skip = (page - 1) * limit;

  const query = {};
  if (traderId) query.trader = traderId;
  if (merchantId) query.merchant = merchantId;
  if (status) query.status = status;
  
  // Add date range filtering
  if (startDate || endDate) {
    query.startDate = {};
    if (startDate) query.startDate.$gte = new Date(startDate);
    if (endDate) query.startDate.$lte = new Date(endDate);
  }

  // For traders, only show their own assignments
  if (req.user.role === 'trader' && !traderId) {
    query.trader = req.user._id;
  }

  const assignments = await TraderAssignment.find(query)
    .populate('trader', 'name email')
    .populate('merchant', 'businessName contactEmail')
    .populate('assignedBy', 'name email')
    .sort('-createdAt')
    .skip(skip)
    .limit(limit);

  const total = await TraderAssignment.countDocuments(query);

  res.status(200).json({
    success: true,
    count: assignments.length,
    pagination: {
      currentPage: page,
      totalPages: Math.ceil(total / limit),
      totalItems: total
    },
    data: assignments
  });
});

/**
 * @swagger
 * /api/trader/my-assignments:
 *   get:
 *     summary: Get current trader's assignments
 *     description: Retrieve a list of assignments for the currently authenticated trader
 *     tags: [Trader Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, inactive, completed, pending]
 *         description: Filter by assignment status
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter assignments starting on or after this date (YYYY-MM-DD)
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter assignments starting on or before this date (YYYY-MM-DD)
 *     responses:
 *       200:
 *         description: A list of the trader's assignments
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 count:
 *                   type: integer
 *                   description: Number of assignments
 *                   example: 3
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/TraderAssignment'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       403:
 *         $ref: '#/components/responses/Forbidden'
 *       500:
 *         description: Internal server error
 */
const getMyAssignments = asyncHandler(async (req, res, next) => {
  const { status, startDate, endDate } = req.query;
  
  const query = { trader: req.user._id };
  if (status) query.status = status;
  
  // Add date range filtering
  if (startDate || endDate) {
    query.startDate = {};
    if (startDate) query.startDate.$gte = new Date(startDate);
    if (endDate) query.startDate.$lte = new Date(endDate);
  }

  const assignments = await TraderAssignment.find(query)
    .populate('merchant', 'businessName contactEmail address')
    .populate('assignedBy', 'name email')
    .sort('-createdAt');

  res.status(200).json({
    success: true,
    count: assignments.length,
    data: assignments
  });
});

/**
 * @swagger
 * /api/trader/assignments:
 *   post:
 *     summary: Create a new trader assignment
 *     description: Assign a trader to a merchant (admin only)
 *     tags: [Trader Management]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - merchantId
 *               - traderId
 *               - startDate
 *             properties:
 *               merchantId:
 *                 type: string
 *                 description: ID of the merchant to assign
 *                 example: 5f8d0d55b54764421b7156c3
 *               traderId:
 *                 type: string
 *                 description: ID of the trader to be assigned
 *                 example: 5f8d0d55b54764421b7156c4
 *               startDate:
 *                 type: string
 *                 format: date-time
 *                 description: When the assignment starts (ISO 8601 format)
 *                 example: 2023-01-01T00:00:00.000Z
 *               endDate:
 *                 type: string
 *                 format: date-time
 *                 description: When the assignment ends (optional, for temporary assignments)
 *                 example: 2023-12-31T23:59:59.000Z
 *               assignmentType:
 *                 type: string
 *                 enum: [permanent, temporary]
 *                 default: permanent
 *                 description: Type of assignment
 *               collectionTarget:
 *                 type: number
 *                 minimum: 0
 *                 description: Collection target amount for the assignment (in base currency)
 *                 example: 10000
 *               permissions:
 *                 type: array
 *                 items:
 *                   type: string
 *                   enum: [view_merchant, edit_merchant, process_payments, view_reports]
 *                 description: Permissions granted to the trader for this assignment
 *                 example: ["view_merchant", "process_payments"]
 *               notes:
 *                 type: string
 *                 description: Additional notes about the assignment
 *                 example: "Special assignment for Q4 collections"
 *     responses:
 *       201:
 *         description: Trader assignment created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/TraderAssignment'
 *       400:
 *         description: Bad request - invalid input data
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       403:
 *         $ref: '#/components/responses/Forbidden'
 *       404:
 *         description: Trader or merchant not found
 *       409:
 *         description: Conflict - duplicate assignment or overlapping dates
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 * 
 * @swagger
 * /api/trader/assignments:
 *   post:
 *     summary: Create a new trader assignment
 *     description: Assign a trader to a merchant (admin only)
 *     tags: [Trader Management]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - merchantId
 *               - traderId
 *               - startDate
 *             properties:
 *               merchantId:
 *                 type: string
 *                 description: ID of the merchant to assign
 *                 example: 5f8d0d55b54764421b7156c3
 *               traderId:
 *                 type: string
 *                 description: ID of the trader to be assigned
 *                 example: 5f8d0d55b54764421b7156c4
 *               startDate:
 *                 type: string
 *                 format: date-time
 *                 description: When the assignment starts (ISO 8601 format)
 *                 example: 2023-01-01T00:00:00.000Z
 *               endDate:
 *                 type: string
 *                 format: date-time
 *                 description: When the assignment ends (optional, for temporary assignments)
 *                 example: 2023-12-31T23:59:59.000Z
 *               assignmentType:
 *                 type: string
 *                 enum: [permanent, temporary]
 *                 default: permanent
 *                 description: Type of assignment
 *               collectionTarget:
 *                 type: number
 *                 minimum: 0
 *                 description: Collection target amount for the assignment (in base currency)
 *                 example: 10000
 *               permissions:
 *                 type: array
 *                 items:
 *                   type: string
 *                   enum: [view_transactions, process_payments, generate_reports, contact_customers]
 *                 description: Permissions granted to the trader for this assignment
 *                 example: ["view_transactions", "process_payments"]
 *               notes:
 *                 type: string
 *                 description: Additional notes about the assignment
 *                 example: "Special assignment for Q4 collections"
 *     responses:
 *       '201':
 *         description: Trader assignment created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/TraderAssignment'
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       403:
 *         $ref: '#/components/responses/Forbidden'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 */
// @route   POST /api/trader/assignments
// @desc    Create a new trader assignment (admin only)
// @access  Private/Admin
const createTraderAssignment = asyncHandler(async (req, res, next) => {
  const { traderId, merchantId, startDate, endDate, assignmentType, collectionTarget, permissions, notes } = req.body;

  // Check if trader exists and has trader role
  const trader = await User.findOne({ _id: traderId, role: 'trader' });
  if (!trader) {
    return next(new ApiError('Trader not found', 404));
  }
  
  // Check if merchant exists
  const merchant = await User.findOne({ _id: merchantId, role: 'merchant' });
  if (!merchant) {
    return next(new ApiError('Merchant not found', 404));
  }
  
  // Check for existing active assignment
  const existingAssignment = await TraderAssignment.findOne({
    trader: traderId,
    merchant: merchantId,
    status: { $in: ['active', 'pending'] }
  });
  
  if (existingAssignment) {
    return next(new ApiError('An active or pending assignment already exists for this trader and merchant', 400));
  }
  
  // Validate collection target if provided
  if (collectionTarget) {
    if (typeof collectionTarget.amount !== 'number' || collectionTarget.amount < 0) {
      return next(new ApiError('Invalid collection target amount', 400));
    }
    
    const validPeriods = ['daily', 'weekly', 'monthly', 'quarterly'];
    if (!validPeriods.includes(collectionTarget.period)) {
      return next(new ApiError('Invalid collection target period', 400));
    }
  }
  
  // Validate permissions if provided
  if (permissions && Array.isArray(permissions)) {
    const validPermissions = ['view_transactions', 'process_payments', 'generate_reports', 'contact_customers'];
    const invalidPermissions = permissions.filter(p => !validPermissions.includes(p));
    
    if (invalidPermissions.length > 0) {
      return next(new ApiError(`Invalid permissions: ${invalidPermissions.join(', ')}`, 400));
    }
  }
  
  // Create new assignment
  const assignment = new TraderAssignment({
    trader: traderId,
    merchant: merchantId,
    startDate: startDate || new Date(),
    endDate,
    assignmentType: assignmentType || 'temporary',
    collectionTarget,
    permissions: permissions || ['view_transactions', 'process_payments'],
    notes,
    assignedBy: req.user._id,
    status: 'active'
  });
  
  await assignment.save();
  
  // Populate the response
  const populatedAssignment = await TraderAssignment.findById(assignment._id)
    .populate('trader', 'name email phone')
    .populate('merchant', 'businessName email phone')
    .populate('assignedBy', 'name email');
  
  // Emit socket event for real-time updates
  if (req.io) {
    req.io.to(`trader:${traderId}`).emit('assignment:created', populatedAssignment);
  }
  
  res.status(201).json({
    success: true,
    data: populatedAssignment
  });
});

// Validation rules
const createAssignmentRules = [
  body('merchantId').isMongoId(),
  body('traderId').isMongoId(),
  body('startDate').optional().isISO8601(),
  body('endDate').optional().isISO8601(),
  body('assignmentType').optional().isIn(['temporary', 'permanent', 'project_based']),
  body('notes').optional().isString(),
  body('status').optional().isIn(['active', 'inactive', 'completed', 'pending', 'suspended']),
  body('collectionTarget').optional().isObject(),
  body('collectionTarget.amount').if(body('collectionTarget').exists()).isFloat({ min: 0 }),
  body('collectionTarget.period').if(body('collectionTarget').exists())
    .isIn(['daily', 'weekly', 'monthly', 'quarterly']),
  body('permissions').optional().isArray(),
  body('permissions.*').optional().isIn(['view_transactions', 'process_payments', 'generate_reports', 'contact_customers'])
];

const getPerformanceRules = [
  query('traderId').optional().isMongoId(),
  query('startDate').optional().isISO8601(),
  query('endDate').optional().isISO8601(),
];

// Define routes with validation and rate limiting
router
  .route('/assignments')
  .get(
    protect,
    authorize('admin'),
    checkPermission('view_trader_assignments'),
    validate([
      query('traderId').optional().isMongoId(),
      query('merchantId').optional().isMongoId(),
      query('status').optional().isIn(['active', 'inactive', 'completed', 'pending', 'suspended']),
      query('startDate').optional().isISO8601(),
      query('endDate').optional().isISO8601(),
      query('page').optional().isInt({ min: 1 }),
      query('limit').optional().isInt({ min: 1, max: 100 })
    ]),
    getTraderAssignments
  )
  .post(
    protect,
    authorize('admin'),
    checkPermission('create_trader_assignment'),
    validate(createAssignmentRules),
    createTraderAssignment
  );

router
  .route('/assignments/:id')
  .put(
    protect,
    authorize('admin', 'trader'),
    validate(updateAssignmentRules),
    updateTraderAssignment
  );

router
  .route('/performance')
  .get(
    protect,
    authorize('admin', 'trader'),
    validate(getPerformanceRules),
    getTraderPerformance
  );

module.exports = router;
