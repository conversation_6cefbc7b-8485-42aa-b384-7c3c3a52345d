"use client"

import type React from "react"
import { createContext, useContext, useState, type ReactNode } from "react"
import axios from "axios"

interface Transaction {
  id: string
  amount: number
  currency: string
  status: "pending" | "completed" | "failed" | "refunded"
  merchantId: string
  traderId?: string
  createdAt: string
  paymentMethod: string
}

interface PaymentContextType {
  transactions: Transaction[]
  processPayment: (paymentData: any) => Promise<any>
  getTransactions: () => Promise<void>
  refundTransaction: (transactionId: string) => Promise<void>
  loading: boolean
}

const PaymentContext = createContext<PaymentContextType | undefined>(undefined)

export const usePayment = () => {
  const context = useContext(PaymentContext)
  if (context === undefined) {
    throw new Error("usePayment must be used within a PaymentProvider")
  }
  return context
}

interface PaymentProviderProps {
  children: ReactNode
}

export const PaymentProvider: React.FC<PaymentProviderProps> = ({ children }) => {
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [loading, setLoading] = useState(false)

  const processPayment = async (paymentData: any) => {
    setLoading(true)
    try {
      const response = await axios.post("/api/payments/process", paymentData)
      await getTransactions()
      return response.data
    } finally {
      setLoading(false)
    }
  }

  const getTransactions = async () => {
    try {
      const response = await axios.get("/api/transactions")
      setTransactions(response.data.transactions)
    } catch (error) {
      console.error("Failed to fetch transactions:", error)
    }
  }

  const refundTransaction = async (transactionId: string) => {
    setLoading(true)
    try {
      await axios.post(`/api/payments/refund/${transactionId}`)
      await getTransactions()
    } finally {
      setLoading(false)
    }
  }

  const value = {
    transactions,
    processPayment,
    getTransactions,
    refundTransaction,
    loading,
  }

  return <PaymentContext.Provider value={value}>{children}</PaymentContext.Provider>
}
