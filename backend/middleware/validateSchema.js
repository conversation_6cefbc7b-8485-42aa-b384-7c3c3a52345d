const Joi = require('joi');
const { ApiError } = require('../utils/ApiError');
const logger = require('../utils/logger');

/**
 * Middleware to validate request data against a Joi schema
 * @param {Object} schema - Joi validation schema
 * @param {string} source - Where to get the data from (body, query, params)
 * @returns {Function} Express middleware function
 */
const validateSchema = (schema, source = 'body') => {
  return (req, res, next) => {
    try {
      // Get the data to validate
      const data = req[source];
      
      // Validate the data against the schema
      const { error, value } = schema.validate(data, {
        abortEarly: false, // Return all validation errors, not just the first one
        allowUnknown: true, // Allow unknown keys (they'll be stripped)
        stripUnknown: true, // Remove unknown keys from the validated data
        convert: true, // Attempt to convert values to the required types
      });

      // If validation fails, throw an error
      if (error) {
        const errors = error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message.replace(/"/g, "'"), // Replace double quotes with single quotes
          type: detail.type,
          context: detail.context,
        }));

        logger.warn('Schema validation failed', {
          path: req.path,
          method: req.method,
          source,
          errors,
          originalData: data,
          validatedData: value,
        });

        throw new ApiError(400, 'Validation failed', errors);
      }

      // Replace the request data with the validated and sanitized data
      req[source] = value;

      // Log successful validation
      logger.debug('Schema validation successful', {
        path: req.path,
        method: req.method,
        source,
        validatedData: value,
      });

      next();
    } catch (error) {
      next(error);
    }
  };
};

/**
 * Middleware to validate request body against a Joi schema
 * @param {Object} schema - Joi validation schema
 * @returns {Function} Express middleware function
 */
const validateBody = (schema) => validateSchema(schema, 'body');

/**
 * Middleware to validate query parameters against a Joi schema
 * @param {Object} schema - Joi validation schema
 * @returns {Function} Express middleware function
 */
const validateQuery = (schema) => validateSchema(schema, 'query');

/**
 * Middleware to validate route parameters against a Joi schema
 * @param {Object} schema - Joi validation schema
 * @returns {Function} Express middleware function
 */
const validateParams = (schema) => validateSchema(schema, 'params');

/**
 * Middleware to validate request headers against a Joi schema
 * @param {Object} schema - Joi validation schema
 * @returns {Function} Express middleware function
 */
const validateHeaders = (schema) => validateSchema(schema, 'headers');

// Common validation schemas
const schemas = {
  // Authentication
  login: Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().min(6).required(),
    rememberMe: Joi.boolean().default(false),
  }),
  
  // User registration
  register: Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().min(8).required(),
    name: Joi.string().min(2).required(),
    role: Joi.string().valid('user', 'admin').default('user'),
  }),
  
  // Pagination
  pagination: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(10),
    sort: Joi.string().pattern(/^[a-zA-Z0-9_,.\s-]+$/),
    search: Joi.string().trim(),
  }),
  
  // ID parameter
  idParam: Joi.object({
    id: Joi.string().pattern(/^[0-9a-fA-F]{24}$/).required(),
  }),
  
  // Email
  email: Joi.object({
    email: Joi.string().email().required(),
  }),
  
  // Password reset
  passwordReset: Joi.object({
    token: Joi.string().required(),
    password: Joi.string().min(8).required(),
    confirmPassword: Joi.string().valid(Joi.ref('password')).required(),
  }),
};

module.exports = {
  validateSchema,
  validateBody,
  validateQuery,
  validateParams,
  validateHeaders,
  schemas,
};
