/*! For license information please see 280.5fd627be.chunk.js.LICENSE.txt */
(self.webpackChunkpayment_gateway_frontend=self.webpackChunkpayment_gateway_frontend||[]).push([[280],{14:t=>{t.exports=function(){return!1}},61:t=>{t.exports=function(t,e){return t<e}},108:(t,e,r)=>{"use strict";r.d(e,{u:()=>v});var n=r(8387),o=r(5043),i=r(9889),a=r.n(i),u=r(6307),c=r(155),l=r(240);function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}function f(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function p(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f(Object(r),!0).forEach(function(e){h(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function h(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=s(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function d(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return y(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return y(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function y(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var v=(0,o.forwardRef)(function(t,e){var r=t.aspect,i=t.initialDimension,s=void 0===i?{width:-1,height:-1}:i,f=t.width,h=void 0===f?"100%":f,y=t.height,v=void 0===y?"100%":y,m=t.minWidth,b=void 0===m?0:m,g=t.minHeight,x=t.maxHeight,w=t.children,O=t.debounce,j=void 0===O?0:O,S=t.id,A=t.className,P=t.onResize,E=t.style,k=void 0===E?{}:E,M=(0,o.useRef)(null),_=(0,o.useRef)();_.current=P,(0,o.useImperativeHandle)(e,function(){return Object.defineProperty(M.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),M.current},configurable:!0})});var T=d((0,o.useState)({containerWidth:s.width,containerHeight:s.height}),2),C=T[0],I=T[1],D=(0,o.useCallback)(function(t,e){I(function(r){var n=Math.round(t),o=Math.round(e);return r.containerWidth===n&&r.containerHeight===o?r:{containerWidth:n,containerHeight:o}})},[]);(0,o.useEffect)(function(){var t=function(t){var e,r=t[0].contentRect,n=r.width,o=r.height;D(n,o),null===(e=_.current)||void 0===e||e.call(_,n,o)};j>0&&(t=a()(t,j,{trailing:!0,leading:!1}));var e=new ResizeObserver(t),r=M.current.getBoundingClientRect(),n=r.width,o=r.height;return D(n,o),e.observe(M.current),function(){e.disconnect()}},[D,j]);var N=(0,o.useMemo)(function(){var t=C.containerWidth,e=C.containerHeight;if(t<0||e<0)return null;(0,c.R)((0,u._3)(h)||(0,u._3)(v),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",h,v),(0,c.R)(!r||r>0,"The aspect(%s) must be greater than zero.",r);var n=(0,u._3)(h)?t:h,i=(0,u._3)(v)?e:v;r&&r>0&&(n?i=n/r:i&&(n=i*r),x&&i>x&&(i=x)),(0,c.R)(n>0||i>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",n,i,h,v,b,g,r);var a=!Array.isArray(w)&&(0,l.Mn)(w.type).endsWith("Chart");return o.Children.map(w,function(t){return o.isValidElement(t)?(0,o.cloneElement)(t,p({width:n,height:i},a?{style:p({height:"100%",width:"100%",maxHeight:i,maxWidth:n},t.props.style)}:{})):t})},[r,w,v,x,g,b,C,h]);return o.createElement("div",{id:S?"".concat(S):void 0,className:(0,n.A)("recharts-responsive-container",A),style:p(p({},k),{},{width:h,height:v,minWidth:b,minHeight:g,maxHeight:x}),ref:M},N)})},143:t=>{var e=/\s/;t.exports=function(t){for(var r=t.length;r--&&e.test(t.charAt(r)););return r}},149:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,o=Array(n);++r<n;)o[r]=e(t[r],r,t);return o}},155:(t,e,r)=>{"use strict";r.d(e,{R:()=>n});var n=function(t,e){for(var r=arguments.length,n=new Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o]}},165:(t,e,r)=>{"use strict";r.d(e,{IZ:()=>l,Kg:()=>u,yy:()=>p});r(9686),r(5043),r(1629);function n(t){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n(t)}function o(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function i(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?o(Object(r),!0).forEach(function(e){a(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function a(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=n(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var o=r.call(t,e||"default");if("object"!=n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==n(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var u=Math.PI/180,c=function(t){return 180*t/Math.PI},l=function(t,e,r,n){return{x:t+Math.cos(-u*n)*r,y:e+Math.sin(-u*n)*r}},s=function(t,e){var r=t.x,n=t.y,o=e.cx,i=e.cy,a=function(t,e){var r=t.x,n=t.y,o=e.x,i=e.y;return Math.sqrt(Math.pow(r-o,2)+Math.pow(n-i,2))}({x:r,y:n},{x:o,y:i});if(a<=0)return{radius:a};var u=(r-o)/a,l=Math.acos(u);return n>i&&(l=2*Math.PI-l),{radius:a,angle:c(l),angleInRadian:l}},f=function(t,e){var r=e.startAngle,n=e.endAngle,o=Math.floor(r/360),i=Math.floor(n/360);return t+360*Math.min(o,i)},p=function(t,e){var r=t.x,n=t.y,o=s({x:r,y:n},e),a=o.radius,u=o.angle,c=e.innerRadius,l=e.outerRadius;if(a<c||a>l)return!1;if(0===a)return!0;var p,h=function(t){var e=t.startAngle,r=t.endAngle,n=Math.floor(e/360),o=Math.floor(r/360),i=Math.min(n,o);return{startAngle:e-360*i,endAngle:r-360*i}}(e),d=h.startAngle,y=h.endAngle,v=u;if(d<=y){for(;v>y;)v-=360;for(;v<d;)v+=360;p=v>=d&&v<=y}else{for(;v>d;)v-=360;for(;v<y;)v+=360;p=v>=y&&v<=d}return p?i(i({},e),{},{radius:a,angle:f(v,e)}):null}},202:(t,e,r)=>{"use strict";r.d(e,{s0:()=>wi,gH:()=>mi,YB:()=>Ci,HQ:()=>Mi,xi:()=>Ii,Hj:()=>Gi,BX:()=>xi,tA:()=>gi,DW:()=>Fi,y2:()=>Ui,nb:()=>zi,PW:()=>Pi,Ay:()=>vi,vf:()=>Si,Mk:()=>Wi,Ps:()=>bi,Mn:()=>Ri,kA:()=>$i,Rh:()=>Ei,w7:()=>Li,zb:()=>Ki,kr:()=>yi,_L:()=>Ai,KC:()=>Vi,A1:()=>ji,W7:()=>_i,AQ:()=>Hi,_f:()=>Di});var n={};r.r(n),r.d(n,{scaleBand:()=>o.A,scaleDiverging:()=>Qn,scaleDivergingLog:()=>to,scaleDivergingPow:()=>ro,scaleDivergingSqrt:()=>no,scaleDivergingSymlog:()=>eo,scaleIdentity:()=>Xt,scaleImplicit:()=>ie.h,scaleLinear:()=>qt,scaleLog:()=>te,scaleOrdinal:()=>ie.A,scalePoint:()=>o.z,scalePow:()=>se,scaleQuantile:()=>Oe,scaleQuantize:()=>je,scaleRadial:()=>he,scaleSequential:()=>Hn,scaleSequentialLog:()=>Gn,scaleSequentialPow:()=>Kn,scaleSequentialQuantile:()=>Jn,scaleSequentialSqrt:()=>Yn,scaleSequentialSymlog:()=>Vn,scaleSqrt:()=>fe,scaleSymlog:()=>oe,scaleThreshold:()=>Se,scaleTime:()=>$n,scaleUtc:()=>Wn,tickFormat:()=>$t});var o=r(2099);const i=Math.sqrt(50),a=Math.sqrt(10),u=Math.sqrt(2);function c(t,e,r){const n=(e-t)/Math.max(0,r),o=Math.floor(Math.log10(n)),l=n/Math.pow(10,o),s=l>=i?10:l>=a?5:l>=u?2:1;let f,p,h;return o<0?(h=Math.pow(10,-o)/s,f=Math.round(t*h),p=Math.round(e*h),f/h<t&&++f,p/h>e&&--p,h=-h):(h=Math.pow(10,o)*s,f=Math.round(t/h),p=Math.round(e/h),f*h<t&&++f,p*h>e&&--p),p<f&&.5<=r&&r<2?c(t,e,2*r):[f,p,h]}function l(t,e,r){if(!((r=+r)>0))return[];if((t=+t)===(e=+e))return[t];const n=e<t,[o,i,a]=n?c(e,t,r):c(t,e,r);if(!(i>=o))return[];const u=i-o+1,l=new Array(u);if(n)if(a<0)for(let c=0;c<u;++c)l[c]=(i-c)/-a;else for(let c=0;c<u;++c)l[c]=(i-c)*a;else if(a<0)for(let c=0;c<u;++c)l[c]=(o+c)/-a;else for(let c=0;c<u;++c)l[c]=(o+c)*a;return l}function s(t,e,r){return c(t=+t,e=+e,r=+r)[2]}function f(t,e,r){r=+r;const n=(e=+e)<(t=+t),o=n?s(e,t,r):s(t,e,r);return(n?-1:1)*(o<0?1/-o:o)}function p(t,e){return null==t||null==e?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function h(t,e){return null==t||null==e?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function d(t){let e,r,n;function o(t,n){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:t.length;if(o<i){if(0!==e(n,n))return i;do{const e=o+i>>>1;r(t[e],n)<0?o=e+1:i=e}while(o<i)}return o}return 2!==t.length?(e=p,r=(e,r)=>p(t(e),r),n=(e,r)=>t(e)-r):(e=t===p||t===h?t:y,r=t,n=t),{left:o,center:function(t,e){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;const i=o(t,e,r,(arguments.length>3&&void 0!==arguments[3]?arguments[3]:t.length)-1);return i>r&&n(t[i-1],e)>-n(t[i],e)?i-1:i},right:function(t,n){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:t.length;if(o<i){if(0!==e(n,n))return i;do{const e=o+i>>>1;r(t[e],n)<=0?o=e+1:i=e}while(o<i)}return o}}}function y(){return 0}function v(t){return null===t?NaN:+t}const m=d(p),b=m.right,g=(m.left,d(v).center,b);function x(t,e,r){t.prototype=e.prototype=r,r.constructor=t}function w(t,e){var r=Object.create(t.prototype);for(var n in e)r[n]=e[n];return r}function O(){}var j=.7,S=1/j,A="\\s*([+-]?\\d+)\\s*",P="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",E="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",k=/^#([0-9a-f]{3,8})$/,M=new RegExp(`^rgb\\(${A},${A},${A}\\)$`),_=new RegExp(`^rgb\\(${E},${E},${E}\\)$`),T=new RegExp(`^rgba\\(${A},${A},${A},${P}\\)$`),C=new RegExp(`^rgba\\(${E},${E},${E},${P}\\)$`),I=new RegExp(`^hsl\\(${P},${E},${E}\\)$`),D=new RegExp(`^hsla\\(${P},${E},${E},${P}\\)$`),N={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function B(){return this.rgb().formatHex()}function R(){return this.rgb().formatRgb()}function L(t){var e,r;return t=(t+"").trim().toLowerCase(),(e=k.exec(t))?(r=e[1].length,e=parseInt(e[1],16),6===r?z(e):3===r?new $(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===r?U(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===r?U(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=M.exec(t))?new $(e[1],e[2],e[3],1):(e=_.exec(t))?new $(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=T.exec(t))?U(e[1],e[2],e[3],e[4]):(e=C.exec(t))?U(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=I.exec(t))?V(e[1],e[2]/100,e[3]/100,1):(e=D.exec(t))?V(e[1],e[2]/100,e[3]/100,e[4]):N.hasOwnProperty(t)?z(N[t]):"transparent"===t?new $(NaN,NaN,NaN,0):null}function z(t){return new $(t>>16&255,t>>8&255,255&t,1)}function U(t,e,r,n){return n<=0&&(t=e=r=NaN),new $(t,e,r,n)}function F(t,e,r,n){return 1===arguments.length?((o=t)instanceof O||(o=L(o)),o?new $((o=o.rgb()).r,o.g,o.b,o.opacity):new $):new $(t,e,r,null==n?1:n);var o}function $(t,e,r,n){this.r=+t,this.g=+e,this.b=+r,this.opacity=+n}function W(){return`#${G(this.r)}${G(this.g)}${G(this.b)}`}function q(){const t=X(this.opacity);return`${1===t?"rgb(":"rgba("}${H(this.r)}, ${H(this.g)}, ${H(this.b)}${1===t?")":`, ${t})`}`}function X(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function H(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function G(t){return((t=H(t))<16?"0":"")+t.toString(16)}function V(t,e,r,n){return n<=0?t=e=r=NaN:r<=0||r>=1?t=e=NaN:e<=0&&(t=NaN),new Y(t,e,r,n)}function K(t){if(t instanceof Y)return new Y(t.h,t.s,t.l,t.opacity);if(t instanceof O||(t=L(t)),!t)return new Y;if(t instanceof Y)return t;var e=(t=t.rgb()).r/255,r=t.g/255,n=t.b/255,o=Math.min(e,r,n),i=Math.max(e,r,n),a=NaN,u=i-o,c=(i+o)/2;return u?(a=e===i?(r-n)/u+6*(r<n):r===i?(n-e)/u+2:(e-r)/u+4,u/=c<.5?i+o:2-i-o,a*=60):u=c>0&&c<1?0:a,new Y(a,u,c,t.opacity)}function Y(t,e,r,n){this.h=+t,this.s=+e,this.l=+r,this.opacity=+n}function J(t){return(t=(t||0)%360)<0?t+360:t}function Z(t){return Math.max(0,Math.min(1,t||0))}function Q(t,e,r){return 255*(t<60?e+(r-e)*t/60:t<180?r:t<240?e+(r-e)*(240-t)/60:e)}function tt(t,e,r,n,o){var i=t*t,a=i*t;return((1-3*t+3*i-a)*e+(4-6*i+3*a)*r+(1+3*t+3*i-3*a)*n+a*o)/6}x(O,L,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:B,formatHex:B,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return K(this).formatHsl()},formatRgb:R,toString:R}),x($,F,w(O,{brighter(t){return t=null==t?S:Math.pow(S,t),new $(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?j:Math.pow(j,t),new $(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new $(H(this.r),H(this.g),H(this.b),X(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:W,formatHex:W,formatHex8:function(){return`#${G(this.r)}${G(this.g)}${G(this.b)}${G(255*(isNaN(this.opacity)?1:this.opacity))}`},formatRgb:q,toString:q})),x(Y,function(t,e,r,n){return 1===arguments.length?K(t):new Y(t,e,r,null==n?1:n)},w(O,{brighter(t){return t=null==t?S:Math.pow(S,t),new Y(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?j:Math.pow(j,t),new Y(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+360*(this.h<0),e=isNaN(t)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*e,o=2*r-n;return new $(Q(t>=240?t-240:t+120,o,n),Q(t,o,n),Q(t<120?t+240:t-120,o,n),this.opacity)},clamp(){return new Y(J(this.h),Z(this.s),Z(this.l),X(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const t=X(this.opacity);return`${1===t?"hsl(":"hsla("}${J(this.h)}, ${100*Z(this.s)}%, ${100*Z(this.l)}%${1===t?")":`, ${t})`}`}}));const et=t=>()=>t;function rt(t,e){return function(r){return t+r*e}}function nt(t){return 1===(t=+t)?ot:function(e,r){return r-e?function(t,e,r){return t=Math.pow(t,r),e=Math.pow(e,r)-t,r=1/r,function(n){return Math.pow(t+n*e,r)}}(e,r,t):et(isNaN(e)?r:e)}}function ot(t,e){var r=e-t;return r?rt(t,r):et(isNaN(t)?e:t)}const it=function t(e){var r=nt(e);function n(t,e){var n=r((t=F(t)).r,(e=F(e)).r),o=r(t.g,e.g),i=r(t.b,e.b),a=ot(t.opacity,e.opacity);return function(e){return t.r=n(e),t.g=o(e),t.b=i(e),t.opacity=a(e),t+""}}return n.gamma=t,n}(1);function at(t){return function(e){var r,n,o=e.length,i=new Array(o),a=new Array(o),u=new Array(o);for(r=0;r<o;++r)n=F(e[r]),i[r]=n.r||0,a[r]=n.g||0,u[r]=n.b||0;return i=t(i),a=t(a),u=t(u),n.opacity=1,function(t){return n.r=i(t),n.g=a(t),n.b=u(t),n+""}}}at(function(t){var e=t.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,e-1):Math.floor(r*e),o=t[n],i=t[n+1],a=n>0?t[n-1]:2*o-i,u=n<e-1?t[n+2]:2*i-o;return tt((r-n/e)*e,a,o,i,u)}}),at(function(t){var e=t.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*e),o=t[(n+e-1)%e],i=t[n%e],a=t[(n+1)%e],u=t[(n+2)%e];return tt((r-n/e)*e,o,i,a,u)}});function ut(t,e){var r,n=e?e.length:0,o=t?Math.min(n,t.length):0,i=new Array(o),a=new Array(n);for(r=0;r<o;++r)i[r]=yt(t[r],e[r]);for(;r<n;++r)a[r]=e[r];return function(t){for(r=0;r<o;++r)a[r]=i[r](t);return a}}function ct(t,e){var r=new Date;return t=+t,e=+e,function(n){return r.setTime(t*(1-n)+e*n),r}}function lt(t,e){return t=+t,e=+e,function(r){return t*(1-r)+e*r}}function st(t,e){var r,n={},o={};for(r in null!==t&&"object"===typeof t||(t={}),null!==e&&"object"===typeof e||(e={}),e)r in t?n[r]=yt(t[r],e[r]):o[r]=e[r];return function(t){for(r in n)o[r]=n[r](t);return o}}var ft=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,pt=new RegExp(ft.source,"g");function ht(t,e){var r,n,o,i=ft.lastIndex=pt.lastIndex=0,a=-1,u=[],c=[];for(t+="",e+="";(r=ft.exec(t))&&(n=pt.exec(e));)(o=n.index)>i&&(o=e.slice(i,o),u[a]?u[a]+=o:u[++a]=o),(r=r[0])===(n=n[0])?u[a]?u[a]+=n:u[++a]=n:(u[++a]=null,c.push({i:a,x:lt(r,n)})),i=pt.lastIndex;return i<e.length&&(o=e.slice(i),u[a]?u[a]+=o:u[++a]=o),u.length<2?c[0]?function(t){return function(e){return t(e)+""}}(c[0].x):function(t){return function(){return t}}(e):(e=c.length,function(t){for(var r,n=0;n<e;++n)u[(r=c[n]).i]=r.x(t);return u.join("")})}function dt(t,e){e||(e=[]);var r,n=t?Math.min(e.length,t.length):0,o=e.slice();return function(i){for(r=0;r<n;++r)o[r]=t[r]*(1-i)+e[r]*i;return o}}function yt(t,e){var r,n,o=typeof e;return null==e||"boolean"===o?et(e):("number"===o?lt:"string"===o?(r=L(e))?(e=r,it):ht:e instanceof L?it:e instanceof Date?ct:(n=e,!ArrayBuffer.isView(n)||n instanceof DataView?Array.isArray(e)?ut:"function"!==typeof e.valueOf&&"function"!==typeof e.toString||isNaN(e)?st:lt:dt))(t,e)}function vt(t,e){return t=+t,e=+e,function(r){return Math.round(t*(1-r)+e*r)}}function mt(t){return+t}var bt=[0,1];function gt(t){return t}function xt(t,e){return(e-=t=+t)?function(r){return(r-t)/e}:(r=isNaN(e)?NaN:.5,function(){return r});var r}function wt(t,e,r){var n=t[0],o=t[1],i=e[0],a=e[1];return o<n?(n=xt(o,n),i=r(a,i)):(n=xt(n,o),i=r(i,a)),function(t){return i(n(t))}}function Ot(t,e,r){var n=Math.min(t.length,e.length)-1,o=new Array(n),i=new Array(n),a=-1;for(t[n]<t[0]&&(t=t.slice().reverse(),e=e.slice().reverse());++a<n;)o[a]=xt(t[a],t[a+1]),i[a]=r(e[a],e[a+1]);return function(e){var r=g(t,e,1,n)-1;return i[r](o[r](e))}}function jt(t,e){return e.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function St(){var t,e,r,n,o,i,a=bt,u=bt,c=yt,l=gt;function s(){var t=Math.min(a.length,u.length);return l!==gt&&(l=function(t,e){var r;return t>e&&(r=t,t=e,e=r),function(r){return Math.max(t,Math.min(e,r))}}(a[0],a[t-1])),n=t>2?Ot:wt,o=i=null,f}function f(e){return null==e||isNaN(e=+e)?r:(o||(o=n(a.map(t),u,c)))(t(l(e)))}return f.invert=function(r){return l(e((i||(i=n(u,a.map(t),lt)))(r)))},f.domain=function(t){return arguments.length?(a=Array.from(t,mt),s()):a.slice()},f.range=function(t){return arguments.length?(u=Array.from(t),s()):u.slice()},f.rangeRound=function(t){return u=Array.from(t),c=vt,s()},f.clamp=function(t){return arguments.length?(l=!!t||gt,s()):l!==gt},f.interpolate=function(t){return arguments.length?(c=t,s()):c},f.unknown=function(t){return arguments.length?(r=t,f):r},function(r,n){return t=r,e=n,s()}}function At(){return St()(gt,gt)}var Pt,Et=r(4402),kt=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function Mt(t){if(!(e=kt.exec(t)))throw new Error("invalid format: "+t);var e;return new _t({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}function _t(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function Tt(t,e){if((r=(t=e?t.toExponential(e-1):t.toExponential()).indexOf("e"))<0)return null;var r,n=t.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+t.slice(r+1)]}function Ct(t){return(t=Tt(Math.abs(t)))?t[1]:NaN}function It(t,e){var r=Tt(t,e);if(!r)return t+"";var n=r[0],o=r[1];return o<0?"0."+new Array(-o).join("0")+n:n.length>o+1?n.slice(0,o+1)+"."+n.slice(o+1):n+new Array(o-n.length+2).join("0")}Mt.prototype=_t.prototype,_t.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};const Dt={"%":(t,e)=>(100*t).toFixed(e),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,e)=>t.toExponential(e),f:(t,e)=>t.toFixed(e),g:(t,e)=>t.toPrecision(e),o:t=>Math.round(t).toString(8),p:(t,e)=>It(100*t,e),r:It,s:function(t,e){var r=Tt(t,e);if(!r)return t+"";var n=r[0],o=r[1],i=o-(Pt=3*Math.max(-8,Math.min(8,Math.floor(o/3))))+1,a=n.length;return i===a?n:i>a?n+new Array(i-a+1).join("0"):i>0?n.slice(0,i)+"."+n.slice(i):"0."+new Array(1-i).join("0")+Tt(t,Math.max(0,e+i-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function Nt(t){return t}var Bt,Rt,Lt,zt=Array.prototype.map,Ut=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function Ft(t){var e,r,n=void 0===t.grouping||void 0===t.thousands?Nt:(e=zt.call(t.grouping,Number),r=t.thousands+"",function(t,n){for(var o=t.length,i=[],a=0,u=e[0],c=0;o>0&&u>0&&(c+u+1>n&&(u=Math.max(1,n-c)),i.push(t.substring(o-=u,o+u)),!((c+=u+1)>n));)u=e[a=(a+1)%e.length];return i.reverse().join(r)}),o=void 0===t.currency?"":t.currency[0]+"",i=void 0===t.currency?"":t.currency[1]+"",a=void 0===t.decimal?".":t.decimal+"",u=void 0===t.numerals?Nt:function(t){return function(e){return e.replace(/[0-9]/g,function(e){return t[+e]})}}(zt.call(t.numerals,String)),c=void 0===t.percent?"%":t.percent+"",l=void 0===t.minus?"\u2212":t.minus+"",s=void 0===t.nan?"NaN":t.nan+"";function f(t){var e=(t=Mt(t)).fill,r=t.align,f=t.sign,p=t.symbol,h=t.zero,d=t.width,y=t.comma,v=t.precision,m=t.trim,b=t.type;"n"===b?(y=!0,b="g"):Dt[b]||(void 0===v&&(v=12),m=!0,b="g"),(h||"0"===e&&"="===r)&&(h=!0,e="0",r="=");var g="$"===p?o:"#"===p&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",x="$"===p?i:/[%p]/.test(b)?c:"",w=Dt[b],O=/[defgprs%]/.test(b);function j(t){var o,i,c,p=g,j=x;if("c"===b)j=w(t)+j,t="";else{var S=(t=+t)<0||1/t<0;if(t=isNaN(t)?s:w(Math.abs(t),v),m&&(t=function(t){t:for(var e,r=t.length,n=1,o=-1;n<r;++n)switch(t[n]){case".":o=e=n;break;case"0":0===o&&(o=n),e=n;break;default:if(!+t[n])break t;o>0&&(o=0)}return o>0?t.slice(0,o)+t.slice(e+1):t}(t)),S&&0===+t&&"+"!==f&&(S=!1),p=(S?"("===f?f:l:"-"===f||"("===f?"":f)+p,j=("s"===b?Ut[8+Pt/3]:"")+j+(S&&"("===f?")":""),O)for(o=-1,i=t.length;++o<i;)if(48>(c=t.charCodeAt(o))||c>57){j=(46===c?a+t.slice(o+1):t.slice(o))+j,t=t.slice(0,o);break}}y&&!h&&(t=n(t,1/0));var A=p.length+t.length+j.length,P=A<d?new Array(d-A+1).join(e):"";switch(y&&h&&(t=n(P+t,P.length?d-j.length:1/0),P=""),r){case"<":t=p+t+j+P;break;case"=":t=p+P+t+j;break;case"^":t=P.slice(0,A=P.length>>1)+p+t+j+P.slice(A);break;default:t=P+p+t+j}return u(t)}return v=void 0===v?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),j.toString=function(){return t+""},j}return{format:f,formatPrefix:function(t,e){var r=f(((t=Mt(t)).type="f",t)),n=3*Math.max(-8,Math.min(8,Math.floor(Ct(e)/3))),o=Math.pow(10,-n),i=Ut[8+n/3];return function(t){return r(o*t)+i}}}}function $t(t,e,r,n){var o,i=f(t,e,r);switch((n=Mt(null==n?",f":n)).type){case"s":var a=Math.max(Math.abs(t),Math.abs(e));return null!=n.precision||isNaN(o=function(t,e){return Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(Ct(e)/3)))-Ct(Math.abs(t)))}(i,a))||(n.precision=o),Lt(n,a);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(o=function(t,e){return t=Math.abs(t),e=Math.abs(e)-t,Math.max(0,Ct(e)-Ct(t))+1}(i,Math.max(Math.abs(t),Math.abs(e))))||(n.precision=o-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(o=function(t){return Math.max(0,-Ct(Math.abs(t)))}(i))||(n.precision=o-2*("%"===n.type))}return Rt(n)}function Wt(t){var e=t.domain;return t.ticks=function(t){var r=e();return l(r[0],r[r.length-1],null==t?10:t)},t.tickFormat=function(t,r){var n=e();return $t(n[0],n[n.length-1],null==t?10:t,r)},t.nice=function(r){null==r&&(r=10);var n,o,i=e(),a=0,u=i.length-1,c=i[a],l=i[u],f=10;for(l<c&&(o=c,c=l,l=o,o=a,a=u,u=o);f-- >0;){if((o=s(c,l,r))===n)return i[a]=c,i[u]=l,e(i);if(o>0)c=Math.floor(c/o)*o,l=Math.ceil(l/o)*o;else{if(!(o<0))break;c=Math.ceil(c*o)/o,l=Math.floor(l*o)/o}n=o}return t},t}function qt(){var t=At();return t.copy=function(){return jt(t,qt())},Et.C.apply(t,arguments),Wt(t)}function Xt(t){var e;function r(t){return null==t||isNaN(t=+t)?e:t}return r.invert=r,r.domain=r.range=function(e){return arguments.length?(t=Array.from(e,mt),r):t.slice()},r.unknown=function(t){return arguments.length?(e=t,r):e},r.copy=function(){return Xt(t).unknown(e)},t=arguments.length?Array.from(t,mt):[0,1],Wt(r)}function Ht(t,e){var r,n=0,o=(t=t.slice()).length-1,i=t[n],a=t[o];return a<i&&(r=n,n=o,o=r,r=i,i=a,a=r),t[n]=e.floor(i),t[o]=e.ceil(a),t}function Gt(t){return Math.log(t)}function Vt(t){return Math.exp(t)}function Kt(t){return-Math.log(-t)}function Yt(t){return-Math.exp(-t)}function Jt(t){return isFinite(t)?+("1e"+t):t<0?0:t}function Zt(t){return(e,r)=>-t(-e,r)}function Qt(t){const e=t(Gt,Vt),r=e.domain;let n,o,i=10;function a(){return n=function(t){return t===Math.E?Math.log:10===t&&Math.log10||2===t&&Math.log2||(t=Math.log(t),e=>Math.log(e)/t)}(i),o=function(t){return 10===t?Jt:t===Math.E?Math.exp:e=>Math.pow(t,e)}(i),r()[0]<0?(n=Zt(n),o=Zt(o),t(Kt,Yt)):t(Gt,Vt),e}return e.base=function(t){return arguments.length?(i=+t,a()):i},e.domain=function(t){return arguments.length?(r(t),a()):r()},e.ticks=t=>{const e=r();let a=e[0],u=e[e.length-1];const c=u<a;c&&([a,u]=[u,a]);let s,f,p=n(a),h=n(u);const d=null==t?10:+t;let y=[];if(!(i%1)&&h-p<d){if(p=Math.floor(p),h=Math.ceil(h),a>0){for(;p<=h;++p)for(s=1;s<i;++s)if(f=p<0?s/o(-p):s*o(p),!(f<a)){if(f>u)break;y.push(f)}}else for(;p<=h;++p)for(s=i-1;s>=1;--s)if(f=p>0?s/o(-p):s*o(p),!(f<a)){if(f>u)break;y.push(f)}2*y.length<d&&(y=l(a,u,d))}else y=l(p,h,Math.min(h-p,d)).map(o);return c?y.reverse():y},e.tickFormat=(t,r)=>{if(null==t&&(t=10),null==r&&(r=10===i?"s":","),"function"!==typeof r&&(i%1||null!=(r=Mt(r)).precision||(r.trim=!0),r=Rt(r)),t===1/0)return r;const a=Math.max(1,i*t/e.ticks().length);return t=>{let e=t/o(Math.round(n(t)));return e*i<i-.5&&(e*=i),e<=a?r(t):""}},e.nice=()=>r(Ht(r(),{floor:t=>o(Math.floor(n(t))),ceil:t=>o(Math.ceil(n(t)))})),e}function te(){const t=Qt(St()).domain([1,10]);return t.copy=()=>jt(t,te()).base(t.base()),Et.C.apply(t,arguments),t}function ee(t){return function(e){return Math.sign(e)*Math.log1p(Math.abs(e/t))}}function re(t){return function(e){return Math.sign(e)*Math.expm1(Math.abs(e))*t}}function ne(t){var e=1,r=t(ee(e),re(e));return r.constant=function(r){return arguments.length?t(ee(e=+r),re(e)):e},Wt(r)}function oe(){var t=ne(St());return t.copy=function(){return jt(t,oe()).constant(t.constant())},Et.C.apply(t,arguments)}Bt=Ft({thousands:",",grouping:[3],currency:["$",""]}),Rt=Bt.format,Lt=Bt.formatPrefix;var ie=r(5186);function ae(t){return function(e){return e<0?-Math.pow(-e,t):Math.pow(e,t)}}function ue(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function ce(t){return t<0?-t*t:t*t}function le(t){var e=t(gt,gt),r=1;return e.exponent=function(e){return arguments.length?1===(r=+e)?t(gt,gt):.5===r?t(ue,ce):t(ae(r),ae(1/r)):r},Wt(e)}function se(){var t=le(St());return t.copy=function(){return jt(t,se()).exponent(t.exponent())},Et.C.apply(t,arguments),t}function fe(){return se.apply(null,arguments).exponent(.5)}function pe(t){return Math.sign(t)*t*t}function he(){var t,e=At(),r=[0,1],n=!1;function o(r){var o=function(t){return Math.sign(t)*Math.sqrt(Math.abs(t))}(e(r));return isNaN(o)?t:n?Math.round(o):o}return o.invert=function(t){return e.invert(pe(t))},o.domain=function(t){return arguments.length?(e.domain(t),o):e.domain()},o.range=function(t){return arguments.length?(e.range((r=Array.from(t,mt)).map(pe)),o):r.slice()},o.rangeRound=function(t){return o.range(t).round(!0)},o.round=function(t){return arguments.length?(n=!!t,o):n},o.clamp=function(t){return arguments.length?(e.clamp(t),o):e.clamp()},o.unknown=function(e){return arguments.length?(t=e,o):t},o.copy=function(){return he(e.domain(),r).round(n).clamp(e.clamp()).unknown(t)},Et.C.apply(o,arguments),Wt(o)}function de(t,e){let r;if(void 0===e)for(const n of t)null!=n&&(r<n||void 0===r&&n>=n)&&(r=n);else{let n=-1;for(let o of t)null!=(o=e(o,++n,t))&&(r<o||void 0===r&&o>=o)&&(r=o)}return r}function ye(t,e){let r;if(void 0===e)for(const n of t)null!=n&&(r>n||void 0===r&&n>=n)&&(r=n);else{let n=-1;for(let o of t)null!=(o=e(o,++n,t))&&(r>o||void 0===r&&o>=o)&&(r=o)}return r}function ve(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:p;if(t===p)return me;if("function"!==typeof t)throw new TypeError("compare is not a function");return(e,r)=>{const n=t(e,r);return n||0===n?n:(0===t(r,r))-(0===t(e,e))}}function me(t,e){return(null==t||!(t>=t))-(null==e||!(e>=e))||(t<e?-1:t>e?1:0)}function be(t,e){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1/0,o=arguments.length>4?arguments[4]:void 0;if(e=Math.floor(e),r=Math.floor(Math.max(0,r)),n=Math.floor(Math.min(t.length-1,n)),!(r<=e&&e<=n))return t;for(o=void 0===o?me:ve(o);n>r;){if(n-r>600){const i=n-r+1,a=e-r+1,u=Math.log(i),c=.5*Math.exp(2*u/3),l=.5*Math.sqrt(u*c*(i-c)/i)*(a-i/2<0?-1:1);be(t,e,Math.max(r,Math.floor(e-a*c/i+l)),Math.min(n,Math.floor(e+(i-a)*c/i+l)),o)}const i=t[e];let a=r,u=n;for(ge(t,r,e),o(t[n],i)>0&&ge(t,r,n);a<u;){for(ge(t,a,u),++a,--u;o(t[a],i)<0;)++a;for(;o(t[u],i)>0;)--u}0===o(t[r],i)?ge(t,r,u):(++u,ge(t,u,n)),u<=e&&(r=u+1),e<=u&&(n=u-1)}return t}function ge(t,e,r){const n=t[e];t[e]=t[r],t[r]=n}function xe(t,e,r){if(t=Float64Array.from(function*(t,e){if(void 0===e)for(let r of t)null!=r&&(r=+r)>=r&&(yield r);else{let r=-1;for(let n of t)null!=(n=e(n,++r,t))&&(n=+n)>=n&&(yield n)}}(t,r)),(n=t.length)&&!isNaN(e=+e)){if(e<=0||n<2)return ye(t);if(e>=1)return de(t);var n,o=(n-1)*e,i=Math.floor(o),a=de(be(t,i).subarray(0,i+1));return a+(ye(t.subarray(i+1))-a)*(o-i)}}function we(t,e){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:v;if((n=t.length)&&!isNaN(e=+e)){if(e<=0||n<2)return+r(t[0],0,t);if(e>=1)return+r(t[n-1],n-1,t);var n,o=(n-1)*e,i=Math.floor(o),a=+r(t[i],i,t);return a+(+r(t[i+1],i+1,t)-a)*(o-i)}}function Oe(){var t,e=[],r=[],n=[];function o(){var t=0,o=Math.max(1,r.length);for(n=new Array(o-1);++t<o;)n[t-1]=we(e,t/o);return i}function i(e){return null==e||isNaN(e=+e)?t:r[g(n,e)]}return i.invertExtent=function(t){var o=r.indexOf(t);return o<0?[NaN,NaN]:[o>0?n[o-1]:e[0],o<n.length?n[o]:e[e.length-1]]},i.domain=function(t){if(!arguments.length)return e.slice();e=[];for(let r of t)null==r||isNaN(r=+r)||e.push(r);return e.sort(p),o()},i.range=function(t){return arguments.length?(r=Array.from(t),o()):r.slice()},i.unknown=function(e){return arguments.length?(t=e,i):t},i.quantiles=function(){return n.slice()},i.copy=function(){return Oe().domain(e).range(r).unknown(t)},Et.C.apply(i,arguments)}function je(){var t,e=0,r=1,n=1,o=[.5],i=[0,1];function a(e){return null!=e&&e<=e?i[g(o,e,0,n)]:t}function u(){var t=-1;for(o=new Array(n);++t<n;)o[t]=((t+1)*r-(t-n)*e)/(n+1);return a}return a.domain=function(t){return arguments.length?([e,r]=t,e=+e,r=+r,u()):[e,r]},a.range=function(t){return arguments.length?(n=(i=Array.from(t)).length-1,u()):i.slice()},a.invertExtent=function(t){var a=i.indexOf(t);return a<0?[NaN,NaN]:a<1?[e,o[0]]:a>=n?[o[n-1],r]:[o[a-1],o[a]]},a.unknown=function(e){return arguments.length?(t=e,a):a},a.thresholds=function(){return o.slice()},a.copy=function(){return je().domain([e,r]).range(i).unknown(t)},Et.C.apply(Wt(a),arguments)}function Se(){var t,e=[.5],r=[0,1],n=1;function o(o){return null!=o&&o<=o?r[g(e,o,0,n)]:t}return o.domain=function(t){return arguments.length?(e=Array.from(t),n=Math.min(e.length,r.length-1),o):e.slice()},o.range=function(t){return arguments.length?(r=Array.from(t),n=Math.min(e.length,r.length-1),o):r.slice()},o.invertExtent=function(t){var n=r.indexOf(t);return[e[n-1],e[n]]},o.unknown=function(e){return arguments.length?(t=e,o):t},o.copy=function(){return Se().domain(e).range(r).unknown(t)},Et.C.apply(o,arguments)}const Ae=1e3,Pe=6e4,Ee=36e5,ke=864e5,Me=6048e5,_e=2592e6,Te=31536e6,Ce=new Date,Ie=new Date;function De(t,e,r,n){function o(e){return t(e=0===arguments.length?new Date:new Date(+e)),e}return o.floor=e=>(t(e=new Date(+e)),e),o.ceil=r=>(t(r=new Date(r-1)),e(r,1),t(r),r),o.round=t=>{const e=o(t),r=o.ceil(t);return t-e<r-t?e:r},o.offset=(t,r)=>(e(t=new Date(+t),null==r?1:Math.floor(r)),t),o.range=(r,n,i)=>{const a=[];if(r=o.ceil(r),i=null==i?1:Math.floor(i),!(r<n)||!(i>0))return a;let u;do{a.push(u=new Date(+r)),e(r,i),t(r)}while(u<r&&r<n);return a},o.filter=r=>De(e=>{if(e>=e)for(;t(e),!r(e);)e.setTime(e-1)},(t,n)=>{if(t>=t)if(n<0)for(;++n<=0;)for(;e(t,-1),!r(t););else for(;--n>=0;)for(;e(t,1),!r(t););}),r&&(o.count=(e,n)=>(Ce.setTime(+e),Ie.setTime(+n),t(Ce),t(Ie),Math.floor(r(Ce,Ie))),o.every=t=>(t=Math.floor(t),isFinite(t)&&t>0?t>1?o.filter(n?e=>n(e)%t===0:e=>o.count(0,e)%t===0):o:null)),o}const Ne=De(()=>{},(t,e)=>{t.setTime(+t+e)},(t,e)=>e-t);Ne.every=t=>(t=Math.floor(t),isFinite(t)&&t>0?t>1?De(e=>{e.setTime(Math.floor(e/t)*t)},(e,r)=>{e.setTime(+e+r*t)},(e,r)=>(r-e)/t):Ne:null);Ne.range;const Be=De(t=>{t.setTime(t-t.getMilliseconds())},(t,e)=>{t.setTime(+t+e*Ae)},(t,e)=>(e-t)/Ae,t=>t.getUTCSeconds()),Re=(Be.range,De(t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*Ae)},(t,e)=>{t.setTime(+t+e*Pe)},(t,e)=>(e-t)/Pe,t=>t.getMinutes())),Le=(Re.range,De(t=>{t.setUTCSeconds(0,0)},(t,e)=>{t.setTime(+t+e*Pe)},(t,e)=>(e-t)/Pe,t=>t.getUTCMinutes())),ze=(Le.range,De(t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*Ae-t.getMinutes()*Pe)},(t,e)=>{t.setTime(+t+e*Ee)},(t,e)=>(e-t)/Ee,t=>t.getHours())),Ue=(ze.range,De(t=>{t.setUTCMinutes(0,0,0)},(t,e)=>{t.setTime(+t+e*Ee)},(t,e)=>(e-t)/Ee,t=>t.getUTCHours())),Fe=(Ue.range,De(t=>t.setHours(0,0,0,0),(t,e)=>t.setDate(t.getDate()+e),(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*Pe)/ke,t=>t.getDate()-1)),$e=(Fe.range,De(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/ke,t=>t.getUTCDate()-1)),We=($e.range,De(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/ke,t=>Math.floor(t/ke)));We.range;function qe(t){return De(e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)},(t,e)=>{t.setDate(t.getDate()+7*e)},(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*Pe)/Me)}const Xe=qe(0),He=qe(1),Ge=qe(2),Ve=qe(3),Ke=qe(4),Ye=qe(5),Je=qe(6);Xe.range,He.range,Ge.range,Ve.range,Ke.range,Ye.range,Je.range;function Ze(t){return De(e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+7*e)},(t,e)=>(e-t)/Me)}const Qe=Ze(0),tr=Ze(1),er=Ze(2),rr=Ze(3),nr=Ze(4),or=Ze(5),ir=Ze(6),ar=(Qe.range,tr.range,er.range,rr.range,nr.range,or.range,ir.range,De(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,e)=>{t.setMonth(t.getMonth()+e)},(t,e)=>e.getMonth()-t.getMonth()+12*(e.getFullYear()-t.getFullYear()),t=>t.getMonth())),ur=(ar.range,De(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)},(t,e)=>e.getUTCMonth()-t.getUTCMonth()+12*(e.getUTCFullYear()-t.getUTCFullYear()),t=>t.getUTCMonth())),cr=(ur.range,De(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,e)=>{t.setFullYear(t.getFullYear()+e)},(t,e)=>e.getFullYear()-t.getFullYear(),t=>t.getFullYear()));cr.every=t=>isFinite(t=Math.floor(t))&&t>0?De(e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)},(e,r)=>{e.setFullYear(e.getFullYear()+r*t)}):null;cr.range;const lr=De(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)},(t,e)=>e.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());lr.every=t=>isFinite(t=Math.floor(t))&&t>0?De(e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,r)=>{e.setUTCFullYear(e.getUTCFullYear()+r*t)}):null;lr.range;function sr(t,e,r,n,o,i){const a=[[Be,1,Ae],[Be,5,5e3],[Be,15,15e3],[Be,30,3e4],[i,1,Pe],[i,5,3e5],[i,15,9e5],[i,30,18e5],[o,1,Ee],[o,3,108e5],[o,6,216e5],[o,12,432e5],[n,1,ke],[n,2,1728e5],[r,1,Me],[e,1,_e],[e,3,7776e6],[t,1,Te]];function u(e,r,n){const o=Math.abs(r-e)/n,i=d(t=>{let[,,e]=t;return e}).right(a,o);if(i===a.length)return t.every(f(e/Te,r/Te,n));if(0===i)return Ne.every(Math.max(f(e,r,n),1));const[u,c]=a[o/a[i-1][2]<a[i][2]/o?i-1:i];return u.every(c)}return[function(t,e,r){const n=e<t;n&&([t,e]=[e,t]);const o=r&&"function"===typeof r.range?r:u(t,e,r),i=o?o.range(t,+e+1):[];return n?i.reverse():i},u]}const[fr,pr]=sr(lr,ur,Qe,We,Ue,Le),[hr,dr]=sr(cr,ar,Xe,Fe,ze,Re);function yr(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function vr(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function mr(t,e,r){return{y:t,m:e,d:r,H:0,M:0,S:0,L:0}}var br,gr,xr,wr={"-":"",_:" ",0:"0"},Or=/^\s*\d+/,jr=/^%/,Sr=/[\\^$*+?|[\]().{}]/g;function Ar(t,e,r){var n=t<0?"-":"",o=(n?-t:t)+"",i=o.length;return n+(i<r?new Array(r-i+1).join(e)+o:o)}function Pr(t){return t.replace(Sr,"\\$&")}function Er(t){return new RegExp("^(?:"+t.map(Pr).join("|")+")","i")}function kr(t){return new Map(t.map((t,e)=>[t.toLowerCase(),e]))}function Mr(t,e,r){var n=Or.exec(e.slice(r,r+1));return n?(t.w=+n[0],r+n[0].length):-1}function _r(t,e,r){var n=Or.exec(e.slice(r,r+1));return n?(t.u=+n[0],r+n[0].length):-1}function Tr(t,e,r){var n=Or.exec(e.slice(r,r+2));return n?(t.U=+n[0],r+n[0].length):-1}function Cr(t,e,r){var n=Or.exec(e.slice(r,r+2));return n?(t.V=+n[0],r+n[0].length):-1}function Ir(t,e,r){var n=Or.exec(e.slice(r,r+2));return n?(t.W=+n[0],r+n[0].length):-1}function Dr(t,e,r){var n=Or.exec(e.slice(r,r+4));return n?(t.y=+n[0],r+n[0].length):-1}function Nr(t,e,r){var n=Or.exec(e.slice(r,r+2));return n?(t.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function Br(t,e,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(r,r+6));return n?(t.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function Rr(t,e,r){var n=Or.exec(e.slice(r,r+1));return n?(t.q=3*n[0]-3,r+n[0].length):-1}function Lr(t,e,r){var n=Or.exec(e.slice(r,r+2));return n?(t.m=n[0]-1,r+n[0].length):-1}function zr(t,e,r){var n=Or.exec(e.slice(r,r+2));return n?(t.d=+n[0],r+n[0].length):-1}function Ur(t,e,r){var n=Or.exec(e.slice(r,r+3));return n?(t.m=0,t.d=+n[0],r+n[0].length):-1}function Fr(t,e,r){var n=Or.exec(e.slice(r,r+2));return n?(t.H=+n[0],r+n[0].length):-1}function $r(t,e,r){var n=Or.exec(e.slice(r,r+2));return n?(t.M=+n[0],r+n[0].length):-1}function Wr(t,e,r){var n=Or.exec(e.slice(r,r+2));return n?(t.S=+n[0],r+n[0].length):-1}function qr(t,e,r){var n=Or.exec(e.slice(r,r+3));return n?(t.L=+n[0],r+n[0].length):-1}function Xr(t,e,r){var n=Or.exec(e.slice(r,r+6));return n?(t.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function Hr(t,e,r){var n=jr.exec(e.slice(r,r+1));return n?r+n[0].length:-1}function Gr(t,e,r){var n=Or.exec(e.slice(r));return n?(t.Q=+n[0],r+n[0].length):-1}function Vr(t,e,r){var n=Or.exec(e.slice(r));return n?(t.s=+n[0],r+n[0].length):-1}function Kr(t,e){return Ar(t.getDate(),e,2)}function Yr(t,e){return Ar(t.getHours(),e,2)}function Jr(t,e){return Ar(t.getHours()%12||12,e,2)}function Zr(t,e){return Ar(1+Fe.count(cr(t),t),e,3)}function Qr(t,e){return Ar(t.getMilliseconds(),e,3)}function tn(t,e){return Qr(t,e)+"000"}function en(t,e){return Ar(t.getMonth()+1,e,2)}function rn(t,e){return Ar(t.getMinutes(),e,2)}function nn(t,e){return Ar(t.getSeconds(),e,2)}function on(t){var e=t.getDay();return 0===e?7:e}function an(t,e){return Ar(Xe.count(cr(t)-1,t),e,2)}function un(t){var e=t.getDay();return e>=4||0===e?Ke(t):Ke.ceil(t)}function cn(t,e){return t=un(t),Ar(Ke.count(cr(t),t)+(4===cr(t).getDay()),e,2)}function ln(t){return t.getDay()}function sn(t,e){return Ar(He.count(cr(t)-1,t),e,2)}function fn(t,e){return Ar(t.getFullYear()%100,e,2)}function pn(t,e){return Ar((t=un(t)).getFullYear()%100,e,2)}function hn(t,e){return Ar(t.getFullYear()%1e4,e,4)}function dn(t,e){var r=t.getDay();return Ar((t=r>=4||0===r?Ke(t):Ke.ceil(t)).getFullYear()%1e4,e,4)}function yn(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+Ar(e/60|0,"0",2)+Ar(e%60,"0",2)}function vn(t,e){return Ar(t.getUTCDate(),e,2)}function mn(t,e){return Ar(t.getUTCHours(),e,2)}function bn(t,e){return Ar(t.getUTCHours()%12||12,e,2)}function gn(t,e){return Ar(1+$e.count(lr(t),t),e,3)}function xn(t,e){return Ar(t.getUTCMilliseconds(),e,3)}function wn(t,e){return xn(t,e)+"000"}function On(t,e){return Ar(t.getUTCMonth()+1,e,2)}function jn(t,e){return Ar(t.getUTCMinutes(),e,2)}function Sn(t,e){return Ar(t.getUTCSeconds(),e,2)}function An(t){var e=t.getUTCDay();return 0===e?7:e}function Pn(t,e){return Ar(Qe.count(lr(t)-1,t),e,2)}function En(t){var e=t.getUTCDay();return e>=4||0===e?nr(t):nr.ceil(t)}function kn(t,e){return t=En(t),Ar(nr.count(lr(t),t)+(4===lr(t).getUTCDay()),e,2)}function Mn(t){return t.getUTCDay()}function _n(t,e){return Ar(tr.count(lr(t)-1,t),e,2)}function Tn(t,e){return Ar(t.getUTCFullYear()%100,e,2)}function Cn(t,e){return Ar((t=En(t)).getUTCFullYear()%100,e,2)}function In(t,e){return Ar(t.getUTCFullYear()%1e4,e,4)}function Dn(t,e){var r=t.getUTCDay();return Ar((t=r>=4||0===r?nr(t):nr.ceil(t)).getUTCFullYear()%1e4,e,4)}function Nn(){return"+0000"}function Bn(){return"%"}function Rn(t){return+t}function Ln(t){return Math.floor(+t/1e3)}function zn(t){return new Date(t)}function Un(t){return t instanceof Date?+t:+new Date(+t)}function Fn(t,e,r,n,o,i,a,u,c,l){var s=At(),f=s.invert,p=s.domain,h=l(".%L"),d=l(":%S"),y=l("%I:%M"),v=l("%I %p"),m=l("%a %d"),b=l("%b %d"),g=l("%B"),x=l("%Y");function w(t){return(c(t)<t?h:u(t)<t?d:a(t)<t?y:i(t)<t?v:n(t)<t?o(t)<t?m:b:r(t)<t?g:x)(t)}return s.invert=function(t){return new Date(f(t))},s.domain=function(t){return arguments.length?p(Array.from(t,Un)):p().map(zn)},s.ticks=function(e){var r=p();return t(r[0],r[r.length-1],null==e?10:e)},s.tickFormat=function(t,e){return null==e?w:l(e)},s.nice=function(t){var r=p();return t&&"function"===typeof t.range||(t=e(r[0],r[r.length-1],null==t?10:t)),t?p(Ht(r,t)):s},s.copy=function(){return jt(s,Fn(t,e,r,n,o,i,a,u,c,l))},s}function $n(){return Et.C.apply(Fn(hr,dr,cr,ar,Xe,Fe,ze,Re,Be,gr).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function Wn(){return Et.C.apply(Fn(fr,pr,lr,ur,Qe,$e,Ue,Le,Be,xr).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function qn(){var t,e,r,n,o,i=0,a=1,u=gt,c=!1;function l(e){return null==e||isNaN(e=+e)?o:u(0===r?.5:(e=(n(e)-t)*r,c?Math.max(0,Math.min(1,e)):e))}function s(t){return function(e){var r,n;return arguments.length?([r,n]=e,u=t(r,n),l):[u(0),u(1)]}}return l.domain=function(o){return arguments.length?([i,a]=o,t=n(i=+i),e=n(a=+a),r=t===e?0:1/(e-t),l):[i,a]},l.clamp=function(t){return arguments.length?(c=!!t,l):c},l.interpolator=function(t){return arguments.length?(u=t,l):u},l.range=s(yt),l.rangeRound=s(vt),l.unknown=function(t){return arguments.length?(o=t,l):o},function(o){return n=o,t=o(i),e=o(a),r=t===e?0:1/(e-t),l}}function Xn(t,e){return e.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function Hn(){var t=Wt(qn()(gt));return t.copy=function(){return Xn(t,Hn())},Et.K.apply(t,arguments)}function Gn(){var t=Qt(qn()).domain([1,10]);return t.copy=function(){return Xn(t,Gn()).base(t.base())},Et.K.apply(t,arguments)}function Vn(){var t=ne(qn());return t.copy=function(){return Xn(t,Vn()).constant(t.constant())},Et.K.apply(t,arguments)}function Kn(){var t=le(qn());return t.copy=function(){return Xn(t,Kn()).exponent(t.exponent())},Et.K.apply(t,arguments)}function Yn(){return Kn.apply(null,arguments).exponent(.5)}function Jn(){var t=[],e=gt;function r(r){if(null!=r&&!isNaN(r=+r))return e((g(t,r,1)-1)/(t.length-1))}return r.domain=function(e){if(!arguments.length)return t.slice();t=[];for(let r of e)null==r||isNaN(r=+r)||t.push(r);return t.sort(p),r},r.interpolator=function(t){return arguments.length?(e=t,r):e},r.range=function(){return t.map((r,n)=>e(n/(t.length-1)))},r.quantiles=function(e){return Array.from({length:e+1},(r,n)=>xe(t,n/e))},r.copy=function(){return Jn(e).domain(t)},Et.K.apply(r,arguments)}function Zn(){var t,e,r,n,o,i,a,u=0,c=.5,l=1,s=1,f=gt,p=!1;function h(t){return isNaN(t=+t)?a:(t=.5+((t=+i(t))-e)*(s*t<s*e?n:o),f(p?Math.max(0,Math.min(1,t)):t))}function d(t){return function(e){var r,n,o;return arguments.length?([r,n,o]=e,f=function(t,e){void 0===e&&(e=t,t=yt);for(var r=0,n=e.length-1,o=e[0],i=new Array(n<0?0:n);r<n;)i[r]=t(o,o=e[++r]);return function(t){var e=Math.max(0,Math.min(n-1,Math.floor(t*=n)));return i[e](t-e)}}(t,[r,n,o]),h):[f(0),f(.5),f(1)]}}return h.domain=function(a){return arguments.length?([u,c,l]=a,t=i(u=+u),e=i(c=+c),r=i(l=+l),n=t===e?0:.5/(e-t),o=e===r?0:.5/(r-e),s=e<t?-1:1,h):[u,c,l]},h.clamp=function(t){return arguments.length?(p=!!t,h):p},h.interpolator=function(t){return arguments.length?(f=t,h):f},h.range=d(yt),h.rangeRound=d(vt),h.unknown=function(t){return arguments.length?(a=t,h):a},function(a){return i=a,t=a(u),e=a(c),r=a(l),n=t===e?0:.5/(e-t),o=e===r?0:.5/(r-e),s=e<t?-1:1,h}}function Qn(){var t=Wt(Zn()(gt));return t.copy=function(){return Xn(t,Qn())},Et.K.apply(t,arguments)}function to(){var t=Qt(Zn()).domain([.1,1,10]);return t.copy=function(){return Xn(t,to()).base(t.base())},Et.K.apply(t,arguments)}function eo(){var t=ne(Zn());return t.copy=function(){return Xn(t,eo()).constant(t.constant())},Et.K.apply(t,arguments)}function ro(){var t=le(Zn());return t.copy=function(){return Xn(t,ro()).exponent(t.exponent())},Et.K.apply(t,arguments)}function no(){return ro.apply(null,arguments).exponent(.5)}function oo(t,e){if((o=t.length)>1)for(var r,n,o,i=1,a=t[e[0]],u=a.length;i<o;++i)for(n=a,a=t[e[i]],r=0;r<u;++r)a[r][1]+=a[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}!function(t){br=function(t){var e=t.dateTime,r=t.date,n=t.time,o=t.periods,i=t.days,a=t.shortDays,u=t.months,c=t.shortMonths,l=Er(o),s=kr(o),f=Er(i),p=kr(i),h=Er(a),d=kr(a),y=Er(u),v=kr(u),m=Er(c),b=kr(c),g={a:function(t){return a[t.getDay()]},A:function(t){return i[t.getDay()]},b:function(t){return c[t.getMonth()]},B:function(t){return u[t.getMonth()]},c:null,d:Kr,e:Kr,f:tn,g:pn,G:dn,H:Yr,I:Jr,j:Zr,L:Qr,m:en,M:rn,p:function(t){return o[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:Rn,s:Ln,S:nn,u:on,U:an,V:cn,w:ln,W:sn,x:null,X:null,y:fn,Y:hn,Z:yn,"%":Bn},x={a:function(t){return a[t.getUTCDay()]},A:function(t){return i[t.getUTCDay()]},b:function(t){return c[t.getUTCMonth()]},B:function(t){return u[t.getUTCMonth()]},c:null,d:vn,e:vn,f:wn,g:Cn,G:Dn,H:mn,I:bn,j:gn,L:xn,m:On,M:jn,p:function(t){return o[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:Rn,s:Ln,S:Sn,u:An,U:Pn,V:kn,w:Mn,W:_n,x:null,X:null,y:Tn,Y:In,Z:Nn,"%":Bn},w={a:function(t,e,r){var n=h.exec(e.slice(r));return n?(t.w=d.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(t,e,r){var n=f.exec(e.slice(r));return n?(t.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(t,e,r){var n=m.exec(e.slice(r));return n?(t.m=b.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(t,e,r){var n=y.exec(e.slice(r));return n?(t.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(t,r,n){return S(t,e,r,n)},d:zr,e:zr,f:Xr,g:Nr,G:Dr,H:Fr,I:Fr,j:Ur,L:qr,m:Lr,M:$r,p:function(t,e,r){var n=l.exec(e.slice(r));return n?(t.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:Rr,Q:Gr,s:Vr,S:Wr,u:_r,U:Tr,V:Cr,w:Mr,W:Ir,x:function(t,e,n){return S(t,r,e,n)},X:function(t,e,r){return S(t,n,e,r)},y:Nr,Y:Dr,Z:Br,"%":Hr};function O(t,e){return function(r){var n,o,i,a=[],u=-1,c=0,l=t.length;for(r instanceof Date||(r=new Date(+r));++u<l;)37===t.charCodeAt(u)&&(a.push(t.slice(c,u)),null!=(o=wr[n=t.charAt(++u)])?n=t.charAt(++u):o="e"===n?" ":"0",(i=e[n])&&(n=i(r,o)),a.push(n),c=u+1);return a.push(t.slice(c,u)),a.join("")}}function j(t,e){return function(r){var n,o,i=mr(1900,void 0,1);if(S(i,t,r+="",0)!=r.length)return null;if("Q"in i)return new Date(i.Q);if("s"in i)return new Date(1e3*i.s+("L"in i?i.L:0));if(e&&!("Z"in i)&&(i.Z=0),"p"in i&&(i.H=i.H%12+12*i.p),void 0===i.m&&(i.m="q"in i?i.q:0),"V"in i){if(i.V<1||i.V>53)return null;"w"in i||(i.w=1),"Z"in i?(o=(n=vr(mr(i.y,0,1))).getUTCDay(),n=o>4||0===o?tr.ceil(n):tr(n),n=$e.offset(n,7*(i.V-1)),i.y=n.getUTCFullYear(),i.m=n.getUTCMonth(),i.d=n.getUTCDate()+(i.w+6)%7):(o=(n=yr(mr(i.y,0,1))).getDay(),n=o>4||0===o?He.ceil(n):He(n),n=Fe.offset(n,7*(i.V-1)),i.y=n.getFullYear(),i.m=n.getMonth(),i.d=n.getDate()+(i.w+6)%7)}else("W"in i||"U"in i)&&("w"in i||(i.w="u"in i?i.u%7:"W"in i?1:0),o="Z"in i?vr(mr(i.y,0,1)).getUTCDay():yr(mr(i.y,0,1)).getDay(),i.m=0,i.d="W"in i?(i.w+6)%7+7*i.W-(o+5)%7:i.w+7*i.U-(o+6)%7);return"Z"in i?(i.H+=i.Z/100|0,i.M+=i.Z%100,vr(i)):yr(i)}}function S(t,e,r,n){for(var o,i,a=0,u=e.length,c=r.length;a<u;){if(n>=c)return-1;if(37===(o=e.charCodeAt(a++))){if(o=e.charAt(a++),!(i=w[o in wr?e.charAt(a++):o])||(n=i(t,r,n))<0)return-1}else if(o!=r.charCodeAt(n++))return-1}return n}return g.x=O(r,g),g.X=O(n,g),g.c=O(e,g),x.x=O(r,x),x.X=O(n,x),x.c=O(e,x),{format:function(t){var e=O(t+="",g);return e.toString=function(){return t},e},parse:function(t){var e=j(t+="",!1);return e.toString=function(){return t},e},utcFormat:function(t){var e=O(t+="",x);return e.toString=function(){return t},e},utcParse:function(t){var e=j(t+="",!0);return e.toString=function(){return t},e}}}(t),gr=br.format,br.parse,xr=br.utcFormat,br.utcParse}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});var io=r(9236),ao=r(3809);function uo(t){for(var e=t.length,r=new Array(e);--e>=0;)r[e]=e;return r}function co(t,e){return t[e]}function lo(t){const e=[];return e.key=t,e}var so=r(539),fo=r.n(so),po=r(6745),ho=r.n(po),yo=r(9686),vo=r.n(yo),mo=r(1629),bo=r.n(mo),go=r(620),xo=r.n(go),wo=r(3097),Oo=r.n(wo),jo=r(3538),So=r.n(jo),Ao=r(5268),Po=r.n(Ao),Eo=r(643),ko=r.n(Eo),Mo=r(9853),_o=r.n(Mo),To=r(7424),Co=r.n(To),Io=r(8210),Do=r.n(Io);function No(t){return function(t){if(Array.isArray(t))return Bo(t)}(t)||function(t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return Bo(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Bo(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Bo(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Ro=function(t){return t},Lo={"@@functional/placeholder":!0},zo=function(t){return t===Lo},Uo=function(t){return function e(){return 0===arguments.length||1===arguments.length&&zo(arguments.length<=0?void 0:arguments[0])?e:t.apply(void 0,arguments)}},Fo=function t(e,r){return 1===e?r:Uo(function(){for(var n=arguments.length,o=new Array(n),i=0;i<n;i++)o[i]=arguments[i];var a=o.filter(function(t){return t!==Lo}).length;return a>=e?r.apply(void 0,o):t(e-a,Uo(function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];var i=o.map(function(t){return zo(t)?e.shift():t});return r.apply(void 0,No(i).concat(e))}))})},$o=function(t){return Fo(t.length,t)},Wo=function(t,e){for(var r=[],n=t;n<e;++n)r[n-t]=n;return r},qo=$o(function(t,e){return Array.isArray(e)?e.map(t):Object.keys(e).map(function(t){return e[t]}).map(t)}),Xo=function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];if(!e.length)return Ro;var n=e.reverse(),o=n[0],i=n.slice(1);return function(){return i.reduce(function(t,e){return e(t)},o.apply(void 0,arguments))}},Ho=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},Go=function(t){var e=null,r=null;return function(){for(var n=arguments.length,o=new Array(n),i=0;i<n;i++)o[i]=arguments[i];return e&&o.every(function(t,r){return t===e[r]})?r:(e=o,r=t.apply(void 0,o))}};const Vo={rangeStep:function(t,e,r){for(var n=new(Do())(t),o=0,i=[];n.lt(e)&&o<1e5;)i.push(n.toNumber()),n=n.add(r),o++;return i},getDigitCount:function(t){return 0===t?1:Math.floor(new(Do())(t).abs().log(10).toNumber())+1},interpolateNumber:$o(function(t,e,r){var n=+t;return n+r*(+e-n)}),uninterpolateNumber:$o(function(t,e,r){var n=e-+t;return(r-t)/(n=n||1/0)}),uninterpolateTruncation:$o(function(t,e,r){var n=e-+t;return n=n||1/0,Math.max(0,Math.min(1,(r-t)/n))})};function Ko(t){return function(t){if(Array.isArray(t))return Zo(t)}(t)||function(t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||Jo(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Yo(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"===typeof Symbol||!(Symbol.iterator in Object(t)))return;var r=[],n=!0,o=!1,i=void 0;try{for(var a,u=t[Symbol.iterator]();!(n=(a=u.next()).done)&&(r.push(a.value),!e||r.length!==e);n=!0);}catch(c){o=!0,i=c}finally{try{n||null==u.return||u.return()}finally{if(o)throw i}}return r}(t,e)||Jo(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Jo(t,e){if(t){if("string"===typeof t)return Zo(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Zo(t,e):void 0}}function Zo(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Qo(t){var e=Yo(t,2),r=e[0],n=e[1],o=r,i=n;return r>n&&(o=n,i=r),[o,i]}function ti(t,e,r){if(t.lte(0))return new(Do())(0);var n=Vo.getDigitCount(t.toNumber()),o=new(Do())(10).pow(n),i=t.div(o),a=1!==n?.05:.1,u=new(Do())(Math.ceil(i.div(a).toNumber())).add(r).mul(a).mul(o);return e?u:new(Do())(Math.ceil(u))}function ei(t,e,r){var n=1,o=new(Do())(t);if(!o.isint()&&r){var i=Math.abs(t);i<1?(n=new(Do())(10).pow(Vo.getDigitCount(t)-1),o=new(Do())(Math.floor(o.div(n).toNumber())).mul(n)):i>1&&(o=new(Do())(Math.floor(t)))}else 0===t?o=new(Do())(Math.floor((e-1)/2)):r||(o=new(Do())(Math.floor(t)));var a=Math.floor((e-1)/2);return Xo(qo(function(t){return o.add(new(Do())(t-a).mul(n)).toNumber()}),Wo)(0,e)}function ri(t,e,r,n){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((e-t)/(r-1)))return{step:new(Do())(0),tickMin:new(Do())(0),tickMax:new(Do())(0)};var i,a=ti(new(Do())(e).sub(t).div(r-1),n,o);i=t<=0&&e>=0?new(Do())(0):(i=new(Do())(t).add(e).div(2)).sub(new(Do())(i).mod(a));var u=Math.ceil(i.sub(t).div(a).toNumber()),c=Math.ceil(new(Do())(e).sub(i).div(a).toNumber()),l=u+c+1;return l>r?ri(t,e,r,n,o+1):(l<r&&(c=e>0?c+(r-l):c,u=e>0?u:u+(r-l)),{step:a,tickMin:i.sub(new(Do())(u).mul(a)),tickMax:i.add(new(Do())(c).mul(a))})}var ni=Go(function(t){var e=Yo(t,2),r=e[0],n=e[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],a=Math.max(o,2),u=Yo(Qo([r,n]),2),c=u[0],l=u[1];if(c===-1/0||l===1/0){var s=l===1/0?[c].concat(Ko(Wo(0,o-1).map(function(){return 1/0}))):[].concat(Ko(Wo(0,o-1).map(function(){return-1/0})),[l]);return r>n?Ho(s):s}if(c===l)return ei(c,o,i);var f=ri(c,l,a,i),p=f.step,h=f.tickMin,d=f.tickMax,y=Vo.rangeStep(h,d.add(new(Do())(.1).mul(p)),p);return r>n?Ho(y):y}),oi=(Go(function(t){var e=Yo(t,2),r=e[0],n=e[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],a=Math.max(o,2),u=Yo(Qo([r,n]),2),c=u[0],l=u[1];if(c===-1/0||l===1/0)return[r,n];if(c===l)return ei(c,o,i);var s=ti(new(Do())(l).sub(c).div(a-1),i,0),f=Xo(qo(function(t){return new(Do())(c).add(new(Do())(t).mul(s)).toNumber()}),Wo)(0,a).filter(function(t){return t>=c&&t<=l});return r>n?Ho(f):f}),Go(function(t,e){var r=Yo(t,2),n=r[0],o=r[1],i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],a=Yo(Qo([n,o]),2),u=a[0],c=a[1];if(u===-1/0||c===1/0)return[n,o];if(u===c)return[u];var l=Math.max(e,2),s=ti(new(Do())(c).sub(u).div(l-1),i,0),f=[].concat(Ko(Vo.rangeStep(new(Do())(u),new(Do())(c).sub(new(Do())(.99).mul(s)),s)),[c]);return n>o?Ho(f):f})),ii=r(8813),ai=r(6307),ui=r(240),ci=r(7165);function li(t){return li="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},li(t)}function si(t){return function(t){if(Array.isArray(t))return fi(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return fi(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return fi(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function fi(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function pi(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function hi(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?pi(Object(r),!0).forEach(function(e){di(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):pi(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function di(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=li(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=li(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==li(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function yi(t,e,r){return vo()(t)||vo()(e)?r:(0,ai.vh)(e)?Oo()(t,e,r):bo()(e)?e(t):r}function vi(t,e,r,n){var o=So()(t,function(t){return yi(t,e)});if("number"===r){var i=o.filter(function(t){return(0,ai.Et)(t)||parseFloat(t)});return i.length?[ho()(i),fo()(i)]:[1/0,-1/0]}return(n?o.filter(function(t){return!vo()(t)}):o).map(function(t){return(0,ai.vh)(t)||t instanceof Date?t:""})}var mi=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0,i=-1,a=null!==(e=null===r||void 0===r?void 0:r.length)&&void 0!==e?e:0;if(a<=1)return 0;if(o&&"angleAxis"===o.axisType&&Math.abs(Math.abs(o.range[1]-o.range[0])-360)<=1e-6)for(var u=o.range,c=0;c<a;c++){var l=c>0?n[c-1].coordinate:n[a-1].coordinate,s=n[c].coordinate,f=c>=a-1?n[0].coordinate:n[c+1].coordinate,p=void 0;if((0,ai.sA)(s-l)!==(0,ai.sA)(f-s)){var h=[];if((0,ai.sA)(f-s)===(0,ai.sA)(u[1]-u[0])){p=f;var d=s+u[1]-u[0];h[0]=Math.min(d,(d+l)/2),h[1]=Math.max(d,(d+l)/2)}else{p=l;var y=f+u[1]-u[0];h[0]=Math.min(s,(y+s)/2),h[1]=Math.max(s,(y+s)/2)}var v=[Math.min(s,(p+s)/2),Math.max(s,(p+s)/2)];if(t>v[0]&&t<=v[1]||t>=h[0]&&t<=h[1]){i=n[c].index;break}}else{var m=Math.min(l,f),b=Math.max(l,f);if(t>(m+s)/2&&t<=(b+s)/2){i=n[c].index;break}}}else for(var g=0;g<a;g++)if(0===g&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g>0&&g<a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g===a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2){i=r[g].index;break}return i},bi=function(t){var e,r,n=t.type.displayName,o=null!==(e=t.type)&&void 0!==e&&e.defaultProps?hi(hi({},t.type.defaultProps),t.props):t.props,i=o.stroke,a=o.fill;switch(n){case"Line":r=i;break;case"Area":case"Radar":r=i&&"none"!==i?i:a;break;default:r=a}return r},gi=function(t){var e=t.barSize,r=t.totalSize,n=t.stackGroups,o=void 0===n?{}:n;if(!o)return{};for(var i={},a=Object.keys(o),u=0,c=a.length;u<c;u++)for(var l=o[a[u]].stackGroups,s=Object.keys(l),f=0,p=s.length;f<p;f++){var h=l[s[f]],d=h.items,y=h.cateAxisId,v=d.filter(function(t){return(0,ui.Mn)(t.type).indexOf("Bar")>=0});if(v&&v.length){var m=v[0].type.defaultProps,b=void 0!==m?hi(hi({},m),v[0].props):v[0].props,g=b.barSize,x=b[y];i[x]||(i[x]=[]);var w=vo()(g)?e:g;i[x].push({item:v[0],stackList:v.slice(1),barSize:vo()(w)?void 0:(0,ai.F4)(w,r,0)})}}return i},xi=function(t){var e=t.barGap,r=t.barCategoryGap,n=t.bandSize,o=t.sizeList,i=void 0===o?[]:o,a=t.maxBarSize,u=i.length;if(u<1)return null;var c,l=(0,ai.F4)(e,n,0,!0),s=[];if(i[0].barSize===+i[0].barSize){var f=!1,p=n/u,h=i.reduce(function(t,e){return t+e.barSize||0},0);(h+=(u-1)*l)>=n&&(h-=(u-1)*l,l=0),h>=n&&p>0&&(f=!0,h=u*(p*=.9));var d={offset:((n-h)/2|0)-l,size:0};c=i.reduce(function(t,e){var r={item:e.item,position:{offset:d.offset+d.size+l,size:f?p:e.barSize}},n=[].concat(si(t),[r]);return d=n[n.length-1].position,e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:d})}),n},s)}else{var y=(0,ai.F4)(r,n,0,!0);n-2*y-(u-1)*l<=0&&(l=0);var v=(n-2*y-(u-1)*l)/u;v>1&&(v>>=0);var m=a===+a?Math.min(v,a):v;c=i.reduce(function(t,e,r){var n=[].concat(si(t),[{item:e.item,position:{offset:y+(v+l)*r+(v-m)/2,size:m}}]);return e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:n[n.length-1].position})}),n},s)}return c},wi=function(t,e,r,n){var o=r.children,i=r.width,a=r.margin,u=i-(a.left||0)-(a.right||0),c=(0,ci.g)({children:o,legendWidth:u});if(c){var l=n||{},s=l.width,f=l.height,p=c.align,h=c.verticalAlign,d=c.layout;if(("vertical"===d||"horizontal"===d&&"middle"===h)&&"center"!==p&&(0,ai.Et)(t[p]))return hi(hi({},t),{},di({},p,t[p]+(s||0)));if(("horizontal"===d||"vertical"===d&&"center"===p)&&"middle"!==h&&(0,ai.Et)(t[h]))return hi(hi({},t),{},di({},h,t[h]+(f||0)))}return t},Oi=function(t,e,r,n,o){var i=e.props.children,a=(0,ui.aS)(i,ii.u).filter(function(t){return function(t,e,r){return!!vo()(e)||("horizontal"===t?"yAxis"===e:"vertical"===t||"x"===r?"xAxis"===e:"y"!==r||"yAxis"===e)}(n,o,t.props.direction)});if(a&&a.length){var u=a.map(function(t){return t.props.dataKey});return t.reduce(function(t,e){var n=yi(e,r);if(vo()(n))return t;var o=Array.isArray(n)?[ho()(n),fo()(n)]:[n,n],i=u.reduce(function(t,r){var n=yi(e,r,0),i=o[0]-Math.abs(Array.isArray(n)?n[0]:n),a=o[1]+Math.abs(Array.isArray(n)?n[1]:n);return[Math.min(i,t[0]),Math.max(a,t[1])]},[1/0,-1/0]);return[Math.min(i[0],t[0]),Math.max(i[1],t[1])]},[1/0,-1/0])}return null},ji=function(t,e,r,n,o){var i=e.map(function(e){return Oi(t,e,r,o,n)}).filter(function(t){return!vo()(t)});return i&&i.length?i.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]):null},Si=function(t,e,r,n,o){var i=e.map(function(e){var i=e.props.dataKey;return"number"===r&&i&&Oi(t,e,i,n)||vi(t,i,r,o)});if("number"===r)return i.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]);var a={};return i.reduce(function(t,e){for(var r=0,n=e.length;r<n;r++)a[e[r]]||(a[e[r]]=!0,t.push(e[r]));return t},[])},Ai=function(t,e){return"horizontal"===t&&"xAxis"===e||"vertical"===t&&"yAxis"===e||"centric"===t&&"angleAxis"===e||"radial"===t&&"radiusAxis"===e},Pi=function(t,e,r,n){if(n)return t.map(function(t){return t.coordinate});var o,i,a=t.map(function(t){return t.coordinate===e&&(o=!0),t.coordinate===r&&(i=!0),t.coordinate});return o||a.push(e),i||a.push(r),a},Ei=function(t,e,r){if(!t)return null;var n=t.scale,o=t.duplicateDomain,i=t.type,a=t.range,u="scaleBand"===t.realScaleType?n.bandwidth()/2:2,c=(e||r)&&"category"===i&&n.bandwidth?n.bandwidth()/u:0;return c="angleAxis"===t.axisType&&(null===a||void 0===a?void 0:a.length)>=2?2*(0,ai.sA)(a[0]-a[1])*c:c,e&&(t.ticks||t.niceTicks)?(t.ticks||t.niceTicks).map(function(t){var e=o?o.indexOf(t):t;return{coordinate:n(e)+c,value:t,offset:c}}).filter(function(t){return!Po()(t.coordinate)}):t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(t,e){return{coordinate:n(t)+c,value:t,index:e,offset:c}}):n.ticks&&!r?n.ticks(t.tickCount).map(function(t){return{coordinate:n(t)+c,value:t,offset:c}}):n.domain().map(function(t,e){return{coordinate:n(t)+c,value:o?o[t]:t,index:e,offset:c}})},ki=new WeakMap,Mi=function(t,e){if("function"!==typeof e)return t;ki.has(t)||ki.set(t,new WeakMap);var r=ki.get(t);if(r.has(e))return r.get(e);var n=function(){t.apply(void 0,arguments),e.apply(void 0,arguments)};return r.set(e,n),n},_i=function(t,e,r){var i=t.scale,a=t.type,u=t.layout,c=t.axisType;if("auto"===i)return"radial"===u&&"radiusAxis"===c?{scale:o.A(),realScaleType:"band"}:"radial"===u&&"angleAxis"===c?{scale:qt(),realScaleType:"linear"}:"category"===a&&e&&(e.indexOf("LineChart")>=0||e.indexOf("AreaChart")>=0||e.indexOf("ComposedChart")>=0&&!r)?{scale:o.z(),realScaleType:"point"}:"category"===a?{scale:o.A(),realScaleType:"band"}:{scale:qt(),realScaleType:"linear"};if(xo()(i)){var l="scale".concat(ko()(i));return{scale:(n[l]||o.z)(),realScaleType:n[l]?l:"point"}}return bo()(i)?{scale:i}:{scale:o.z(),realScaleType:"point"}},Ti=1e-4,Ci=function(t){var e=t.domain();if(e&&!(e.length<=2)){var r=e.length,n=t.range(),o=Math.min(n[0],n[1])-Ti,i=Math.max(n[0],n[1])+Ti,a=t(e[0]),u=t(e[r-1]);(a<o||a>i||u<o||u>i)&&t.domain([e[0],e[r-1]])}},Ii=function(t,e){if(!t)return null;for(var r=0,n=t.length;r<n;r++)if(t[r].item===e)return t[r].position;return null},Di=function(t,e){if(!e||2!==e.length||!(0,ai.Et)(e[0])||!(0,ai.Et)(e[1]))return t;var r=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]),o=[t[0],t[1]];return(!(0,ai.Et)(t[0])||t[0]<r)&&(o[0]=r),(!(0,ai.Et)(t[1])||t[1]>n)&&(o[1]=n),o[0]>n&&(o[0]=n),o[1]<r&&(o[1]=r),o},Ni={sign:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0,a=0;a<e;++a){var u=Po()(t[a][r][1])?t[a][r][0]:t[a][r][1];u>=0?(t[a][r][0]=o,t[a][r][1]=o+u,o=t[a][r][1]):(t[a][r][0]=i,t[a][r][1]=i+u,i=t[a][r][1])}},expand:function(t,e){if((n=t.length)>0){for(var r,n,o,i=0,a=t[0].length;i<a;++i){for(o=r=0;r<n;++r)o+=t[r][i][1]||0;if(o)for(r=0;r<n;++r)t[r][i][1]/=o}oo(t,e)}},none:oo,silhouette:function(t,e){if((r=t.length)>0){for(var r,n=0,o=t[e[0]],i=o.length;n<i;++n){for(var a=0,u=0;a<r;++a)u+=t[a][n][1]||0;o[n][1]+=o[n][0]=-u/2}oo(t,e)}},wiggle:function(t,e){if((o=t.length)>0&&(n=(r=t[e[0]]).length)>0){for(var r,n,o,i=0,a=1;a<n;++a){for(var u=0,c=0,l=0;u<o;++u){for(var s=t[e[u]],f=s[a][1]||0,p=(f-(s[a-1][1]||0))/2,h=0;h<u;++h){var d=t[e[h]];p+=(d[a][1]||0)-(d[a-1][1]||0)}c+=f,l+=p*f}r[a-1][1]+=r[a-1][0]=i,c&&(i-=l/c)}r[a-1][1]+=r[a-1][0]=i,oo(t,e)}},positive:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0;i<e;++i){var a=Po()(t[i][r][1])?t[i][r][0]:t[i][r][1];a>=0?(t[i][r][0]=o,t[i][r][1]=o+a,o=t[i][r][1]):(t[i][r][0]=0,t[i][r][1]=0)}}},Bi=function(t,e,r){var n=e.map(function(t){return t.props.dataKey}),o=Ni[r],i=function(){var t=(0,ao.A)([]),e=uo,r=oo,n=co;function o(o){var i,a,u=Array.from(t.apply(this,arguments),lo),c=u.length,l=-1;for(const t of o)for(i=0,++l;i<c;++i)(u[i][l]=[0,+n(t,u[i].key,l,o)]).data=t;for(i=0,a=(0,io.A)(e(u));i<c;++i)u[a[i]].index=i;return r(u,a),u}return o.keys=function(e){return arguments.length?(t="function"===typeof e?e:(0,ao.A)(Array.from(e)),o):t},o.value=function(t){return arguments.length?(n="function"===typeof t?t:(0,ao.A)(+t),o):n},o.order=function(t){return arguments.length?(e=null==t?uo:"function"===typeof t?t:(0,ao.A)(Array.from(t)),o):e},o.offset=function(t){return arguments.length?(r=null==t?oo:t,o):r},o}().keys(n).value(function(t,e){return+yi(t,e,0)}).order(uo).offset(o);return i(t)},Ri=function(t,e,r,n,o,i){if(!t)return null;var a=(i?e.reverse():e).reduce(function(t,e){var o,i=null!==(o=e.type)&&void 0!==o&&o.defaultProps?hi(hi({},e.type.defaultProps),e.props):e.props,a=i.stackId;if(i.hide)return t;var u=i[r],c=t[u]||{hasStack:!1,stackGroups:{}};if((0,ai.vh)(a)){var l=c.stackGroups[a]||{numericAxisId:r,cateAxisId:n,items:[]};l.items.push(e),c.hasStack=!0,c.stackGroups[a]=l}else c.stackGroups[(0,ai.NF)("_stackId_")]={numericAxisId:r,cateAxisId:n,items:[e]};return hi(hi({},t),{},di({},u,c))},{});return Object.keys(a).reduce(function(e,i){var u=a[i];if(u.hasStack){u.stackGroups=Object.keys(u.stackGroups).reduce(function(e,i){var a=u.stackGroups[i];return hi(hi({},e),{},di({},i,{numericAxisId:r,cateAxisId:n,items:a.items,stackedData:Bi(t,a.items,o)}))},{})}return hi(hi({},e),{},di({},i,u))},{})},Li=function(t,e){var r=e.realScaleType,n=e.type,o=e.tickCount,i=e.originalDomain,a=e.allowDecimals,u=r||e.scale;if("auto"!==u&&"linear"!==u)return null;if(o&&"number"===n&&i&&("auto"===i[0]||"auto"===i[1])){var c=t.domain();if(!c.length)return null;var l=ni(c,o,a);return t.domain([ho()(l),fo()(l)]),{niceTicks:l}}if(o&&"number"===n){var s=t.domain();return{niceTicks:oi(s,o,a)}}return null};function zi(t){var e=t.axis,r=t.ticks,n=t.bandSize,o=t.entry,i=t.index,a=t.dataKey;if("category"===e.type){if(!e.allowDuplicatedCategory&&e.dataKey&&!vo()(o[e.dataKey])){var u=(0,ai.eP)(r,"value",o[e.dataKey]);if(u)return u.coordinate+n/2}return r[i]?r[i].coordinate+n/2:null}var c=yi(o,vo()(a)?e.dataKey:a);return vo()(c)?null:e.scale(c)}var Ui=function(t){var e=t.axis,r=t.ticks,n=t.offset,o=t.bandSize,i=t.entry,a=t.index;if("category"===e.type)return r[a]?r[a].coordinate+n:null;var u=yi(i,e.dataKey,e.domain[a]);return vo()(u)?null:e.scale(u)-o/2+n},Fi=function(t){var e=t.numericAxis,r=e.scale.domain();if("number"===e.type){var n=Math.min(r[0],r[1]),o=Math.max(r[0],r[1]);return n<=0&&o>=0?0:o<0?o:n}return r[0]},$i=function(t,e){var r,n=(null!==(r=t.type)&&void 0!==r&&r.defaultProps?hi(hi({},t.type.defaultProps),t.props):t.props).stackId;if((0,ai.vh)(n)){var o=e[n];if(o){var i=o.items.indexOf(t);return i>=0?o.stackedData[i]:null}}return null},Wi=function(t,e,r){return Object.keys(t).reduce(function(n,o){var i=t[o].stackedData.reduce(function(t,n){var o=n.slice(e,r+1).reduce(function(t,e){return[ho()(e.concat([t[0]]).filter(ai.Et)),fo()(e.concat([t[1]]).filter(ai.Et))]},[1/0,-1/0]);return[Math.min(t[0],o[0]),Math.max(t[1],o[1])]},[1/0,-1/0]);return[Math.min(i[0],n[0]),Math.max(i[1],n[1])]},[1/0,-1/0]).map(function(t){return t===1/0||t===-1/0?0:t})},qi=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,Xi=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,Hi=function(t,e,r){if(bo()(t))return t(e,r);if(!Array.isArray(t))return e;var n=[];if((0,ai.Et)(t[0]))n[0]=r?t[0]:Math.min(t[0],e[0]);else if(qi.test(t[0])){var o=+qi.exec(t[0])[1];n[0]=e[0]-o}else bo()(t[0])?n[0]=t[0](e[0]):n[0]=e[0];if((0,ai.Et)(t[1]))n[1]=r?t[1]:Math.max(t[1],e[1]);else if(Xi.test(t[1])){var i=+Xi.exec(t[1])[1];n[1]=e[1]+i}else bo()(t[1])?n[1]=t[1](e[1]):n[1]=e[1];return n},Gi=function(t,e,r){if(t&&t.scale&&t.scale.bandwidth){var n=t.scale.bandwidth();if(!r||n>0)return n}if(t&&e&&e.length>=2){for(var o=Co()(e,function(t){return t.coordinate}),i=1/0,a=1,u=o.length;a<u;a++){var c=o[a],l=o[a-1];i=Math.min((c.coordinate||0)-(l.coordinate||0),i)}return i===1/0?0:i}return r?void 0:0},Vi=function(t,e,r){return t&&t.length?_o()(t,Oo()(r,"type.defaultProps.domain"))?e:t:e},Ki=function(t,e){var r=t.type.defaultProps?hi(hi({},t.type.defaultProps),t.props):t.props,n=r.dataKey,o=r.name,i=r.unit,a=r.formatter,u=r.tooltipType,c=r.chartType,l=r.hide;return hi(hi({},(0,ui.J9)(t,!1)),{},{dataKey:n,unit:i,formatter:a,name:o||n,color:bi(t),value:yi(e,n),type:u,payload:e,chartType:c,hide:l})}},240:(t,e,r)=>{"use strict";r.d(e,{AW:()=>L,BU:()=>k,J9:()=>I,Me:()=>M,Mn:()=>j,OV:()=>D,X_:()=>R,aS:()=>E,ee:()=>B,sT:()=>C});var n=r(3097),o=r.n(n),i=r(9686),a=r.n(i),u=r(620),c=r.n(u),l=r(1629),s=r.n(l),f=r(6686),p=r.n(f),h=r(5043),d=r(9062),y=r(6307),v=r(5248),m=r(7287),b=["children"],g=["children"];function x(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function w(t){return w="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},w(t)}var O={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},j=function(t){return"string"===typeof t?t:t?t.displayName||t.name||"Component":""},S=null,A=null,P=function t(e){if(e===S&&Array.isArray(A))return A;var r=[];return h.Children.forEach(e,function(e){a()(e)||((0,d.isFragment)(e)?r=r.concat(t(e.props.children)):r.push(e))}),A=r,S=e,r};function E(t,e){var r=[],n=[];return n=Array.isArray(e)?e.map(function(t){return j(t)}):[j(e)],P(t).forEach(function(t){var e=o()(t,"type.displayName")||o()(t,"type.name");-1!==n.indexOf(e)&&r.push(t)}),r}function k(t,e){var r=E(t,e);return r&&r[0]}var M=function(t){if(!t||!t.props)return!1;var e=t.props,r=e.width,n=e.height;return!(!(0,y.Et)(r)||r<=0||!(0,y.Et)(n)||n<=0)},_=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],T=function(t){return t&&t.type&&c()(t.type)&&_.indexOf(t.type)>=0},C=function(t){return t&&"object"===w(t)&&"clipDot"in t},I=function(t,e,r){if(!t||"function"===typeof t||"boolean"===typeof t)return null;var n=t;if((0,h.isValidElement)(t)&&(n=t.props),!p()(n))return null;var o={};return Object.keys(n).forEach(function(t){var i;(function(t,e,r,n){var o,i=null!==(o=null===m.VU||void 0===m.VU?void 0:m.VU[n])&&void 0!==o?o:[];return e.startsWith("data-")||!s()(t)&&(n&&i.includes(e)||m.QQ.includes(e))||r&&m.j2.includes(e)})(null===(i=n)||void 0===i?void 0:i[t],t,e,r)&&(o[t]=n[t])}),o},D=function t(e,r){if(e===r)return!0;var n=h.Children.count(e);if(n!==h.Children.count(r))return!1;if(0===n)return!0;if(1===n)return N(Array.isArray(e)?e[0]:e,Array.isArray(r)?r[0]:r);for(var o=0;o<n;o++){var i=e[o],a=r[o];if(Array.isArray(i)||Array.isArray(a)){if(!t(i,a))return!1}else if(!N(i,a))return!1}return!0},N=function(t,e){if(a()(t)&&a()(e))return!0;if(!a()(t)&&!a()(e)){var r=t.props||{},n=r.children,o=x(r,b),i=e.props||{},u=i.children,c=x(i,g);return n&&u?(0,v.b)(o,c)&&D(n,u):!n&&!u&&(0,v.b)(o,c)}return!1},B=function(t,e){var r=[],n={};return P(t).forEach(function(t,o){if(T(t))r.push(t);else if(t){var i=j(t.type),a=e[i]||{},u=a.handler,c=a.once;if(u&&(!c||!n[i])){var l=u(t,i,o);r.push(l),n[i]=!0}}}),r},R=function(t){var e=t&&t.type;return e&&O[e]?O[e]:null},L=function(t,e){return P(e).indexOf(t)}},320:t=>{t.exports=function(t,e){var r=t.length;for(t.sort(e);r--;)t[r]=t[r].value;return t}},396:t=>{t.exports=function(t){return function(e){return null==e?void 0:e[t]}}},438:(t,e,r)=>{var n=r(2622);t.exports=function(t){return n(this,t).get(t)}},539:(t,e,r)=>{var n=r(9742),o=r(7498),i=r(3279);t.exports=function(t){return t&&t.length?n(t,i,o):void 0}},620:(t,e,r)=>{var n=r(6913),o=r(4052),i=r(2761);t.exports=function(t){return"string"==typeof t||!o(t)&&i(t)&&"[object String]"==n(t)}},643:(t,e,r)=>{var n=r(7676)("toUpperCase");t.exports=n},644:t=>{t.exports=function(t){return t!==t}},677:(t,e,r)=>{"use strict";r.d(e,{h:()=>v});var n=r(5043),o=r(8387),i=r(240),a=r(165),u=r(6307);function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function l(){return l=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},l.apply(this,arguments)}function s(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function f(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s(Object(r),!0).forEach(function(e){p(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function p(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=c(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=c(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==c(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var h=function(t){var e=t.cx,r=t.cy,n=t.radius,o=t.angle,i=t.sign,u=t.isExternal,c=t.cornerRadius,l=t.cornerIsExternal,s=c*(u?1:-1)+n,f=Math.asin(c/s)/a.Kg,p=l?o:o+i*f,h=l?o-i*f:o;return{center:(0,a.IZ)(e,r,s,p),circleTangency:(0,a.IZ)(e,r,n,p),lineTangency:(0,a.IZ)(e,r,s*Math.cos(f*a.Kg),h),theta:f}},d=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,o=t.outerRadius,i=t.startAngle,c=function(t,e){return(0,u.sA)(e-t)*Math.min(Math.abs(e-t),359.999)}(i,t.endAngle),l=i+c,s=(0,a.IZ)(e,r,o,i),f=(0,a.IZ)(e,r,o,l),p="M ".concat(s.x,",").concat(s.y,"\n    A ").concat(o,",").concat(o,",0,\n    ").concat(+(Math.abs(c)>180),",").concat(+(i>l),",\n    ").concat(f.x,",").concat(f.y,"\n  ");if(n>0){var h=(0,a.IZ)(e,r,n,i),d=(0,a.IZ)(e,r,n,l);p+="L ".concat(d.x,",").concat(d.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(c)>180),",").concat(+(i<=l),",\n            ").concat(h.x,",").concat(h.y," Z")}else p+="L ".concat(e,",").concat(r," Z");return p},y={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},v=function(t){var e=f(f({},y),t),r=e.cx,a=e.cy,c=e.innerRadius,s=e.outerRadius,p=e.cornerRadius,v=e.forceCornerRadius,m=e.cornerIsExternal,b=e.startAngle,g=e.endAngle,x=e.className;if(s<c||b===g)return null;var w,O=(0,o.A)("recharts-sector",x),j=s-c,S=(0,u.F4)(p,j,0,!0);return w=S>0&&Math.abs(b-g)<360?function(t){var e=t.cx,r=t.cy,n=t.innerRadius,o=t.outerRadius,i=t.cornerRadius,a=t.forceCornerRadius,c=t.cornerIsExternal,l=t.startAngle,s=t.endAngle,f=(0,u.sA)(s-l),p=h({cx:e,cy:r,radius:o,angle:l,sign:f,cornerRadius:i,cornerIsExternal:c}),y=p.circleTangency,v=p.lineTangency,m=p.theta,b=h({cx:e,cy:r,radius:o,angle:s,sign:-f,cornerRadius:i,cornerIsExternal:c}),g=b.circleTangency,x=b.lineTangency,w=b.theta,O=c?Math.abs(l-s):Math.abs(l-s)-m-w;if(O<0)return a?"M ".concat(v.x,",").concat(v.y,"\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*i,",0\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*-i,",0\n      "):d({cx:e,cy:r,innerRadius:n,outerRadius:o,startAngle:l,endAngle:s});var j="M ".concat(v.x,",").concat(v.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(y.x,",").concat(y.y,"\n    A").concat(o,",").concat(o,",0,").concat(+(O>180),",").concat(+(f<0),",").concat(g.x,",").concat(g.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(x.x,",").concat(x.y,"\n  ");if(n>0){var S=h({cx:e,cy:r,radius:n,angle:l,sign:f,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),A=S.circleTangency,P=S.lineTangency,E=S.theta,k=h({cx:e,cy:r,radius:n,angle:s,sign:-f,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),M=k.circleTangency,_=k.lineTangency,T=k.theta,C=c?Math.abs(l-s):Math.abs(l-s)-E-T;if(C<0&&0===i)return"".concat(j,"L").concat(e,",").concat(r,"Z");j+="L".concat(_.x,",").concat(_.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(M.x,",").concat(M.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(C>180),",").concat(+(f>0),",").concat(A.x,",").concat(A.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(P.x,",").concat(P.y,"Z")}else j+="L".concat(e,",").concat(r,"Z");return j}({cx:r,cy:a,innerRadius:c,outerRadius:s,cornerRadius:Math.min(S,j/2),forceCornerRadius:v,cornerIsExternal:m,startAngle:b,endAngle:g}):d({cx:r,cy:a,innerRadius:c,outerRadius:s,startAngle:b,endAngle:g}),n.createElement("path",l({},(0,i.J9)(e,!0),{className:O,d:w,role:"img"}))}},705:t=>{t.exports=function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t}},715:t=>{var e="\\ud800-\\udfff",r="["+e+"]",n="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",o="\\ud83c[\\udffb-\\udfff]",i="[^"+e+"]",a="(?:\\ud83c[\\udde6-\\uddff]){2}",u="[\\ud800-\\udbff][\\udc00-\\udfff]",c="(?:"+n+"|"+o+")"+"?",l="[\\ufe0e\\ufe0f]?",s=l+c+("(?:\\u200d(?:"+[i,a,u].join("|")+")"+l+c+")*"),f="(?:"+[i+n+"?",n,a,u,r].join("|")+")",p=RegExp(o+"(?="+o+")|"+f+s,"g");t.exports=function(t){return t.match(p)||[]}},755:(t,e,r)=>{var n=r(8895),o=r(7116);t.exports=function t(e,r,i,a,u){var c=-1,l=e.length;for(i||(i=o),u||(u=[]);++c<l;){var s=e[c];r>0&&i(s)?r>1?t(s,r-1,i,a,u):n(u,s):a||(u[u.length]=s)}return u}},793:t=>{t.exports=function(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r}},801:(t,e,r)=>{var n=r(1141),o=r(6686),i=r(9841),a=/^[-+]0x[0-9a-f]+$/i,u=/^0b[01]+$/i,c=/^0o[0-7]+$/i,l=parseInt;t.exports=function(t){if("number"==typeof t)return t;if(i(t))return NaN;if(o(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=o(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=n(t);var r=u.test(t);return r||c.test(t)?l(t.slice(2),r?2:8):a.test(t)?NaN:+t}},879:(t,e,r)=>{"use strict";r.d(e,{yp:()=>R,GG:()=>q,NE:()=>L,nZ:()=>z,xQ:()=>U});var n=r(5043),o=r(1629),i=r.n(o),a=r(2322),u=r.n(a),c=r(6361),l=r.n(c),s=r(9853),f=r.n(s),p=r(4342),h=r(8387),d=r(1744),y=r(240);function v(t){return v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},v(t)}function m(){return m=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},m.apply(this,arguments)}function b(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return g(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return g(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function g(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function x(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function w(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?x(Object(r),!0).forEach(function(e){O(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):x(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function O(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=v(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=v(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==v(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var j=function(t,e,r,n,o){var i,a=r-n;return i="M ".concat(t,",").concat(e),i+="L ".concat(t+r,",").concat(e),i+="L ".concat(t+r-a/2,",").concat(e+o),i+="L ".concat(t+r-a/2-n,",").concat(e+o),i+="L ".concat(t,",").concat(e," Z")},S={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},A=function(t){var e=w(w({},S),t),r=(0,n.useRef)(),o=b((0,n.useState)(-1),2),i=o[0],a=o[1];(0,n.useEffect)(function(){if(r.current&&r.current.getTotalLength)try{var t=r.current.getTotalLength();t&&a(t)}catch(e){}},[]);var u=e.x,c=e.y,l=e.upperWidth,s=e.lowerWidth,f=e.height,p=e.className,v=e.animationEasing,g=e.animationDuration,x=e.animationBegin,O=e.isUpdateAnimationActive;if(u!==+u||c!==+c||l!==+l||s!==+s||f!==+f||0===l&&0===s||0===f)return null;var A=(0,h.A)("recharts-trapezoid",p);return O?n.createElement(d.Ay,{canBegin:i>0,from:{upperWidth:0,lowerWidth:0,height:f,x:u,y:c},to:{upperWidth:l,lowerWidth:s,height:f,x:u,y:c},duration:g,animationEasing:v,isActive:O},function(t){var o=t.upperWidth,a=t.lowerWidth,u=t.height,c=t.x,l=t.y;return n.createElement(d.Ay,{canBegin:i>0,from:"0px ".concat(-1===i?1:i,"px"),to:"".concat(i,"px 0px"),attributeName:"strokeDasharray",begin:x,duration:g,easing:v},n.createElement("path",m({},(0,y.J9)(e,!0),{className:A,d:j(c,l,o,a,u),ref:r})))}):n.createElement("g",null,n.createElement("path",m({},(0,y.J9)(e,!0),{className:A,d:j(u,c,l,s,f)})))},P=r(677),E=r(1639),k=r(1985),M=["option","shapeType","propTransformer","activeClassName","isActive"];function _(t){return _="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_(t)}function T(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function C(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function I(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?C(Object(r),!0).forEach(function(e){D(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):C(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function D(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=_(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=_(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==_(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function N(t,e){return I(I({},e),t)}function B(t){var e=t.shapeType,r=t.elementProps;switch(e){case"rectangle":return n.createElement(p.M,r);case"trapezoid":return n.createElement(A,r);case"sector":return n.createElement(P.h,r);case"symbols":if(function(t){return"symbols"===t}(e))return n.createElement(k.i,r);break;default:return null}}function R(t){var e,r=t.option,o=t.shapeType,a=t.propTransformer,c=void 0===a?N:a,s=t.activeClassName,f=void 0===s?"recharts-active-shape":s,p=t.isActive,h=T(t,M);if((0,n.isValidElement)(r))e=(0,n.cloneElement)(r,I(I({},h),function(t){return(0,n.isValidElement)(t)?t.props:t}(r)));else if(i()(r))e=r(h);else if(u()(r)&&!l()(r)){var d=c(r,h);e=n.createElement(B,{shapeType:o,elementProps:d})}else{var y=h;e=n.createElement(B,{shapeType:o,elementProps:y})}return p?n.createElement(E.W,{className:f},e):e}function L(t,e){return null!=e&&"trapezoids"in t.props}function z(t,e){return null!=e&&"sectors"in t.props}function U(t,e){return null!=e&&"points"in t.props}function F(t,e){var r,n,o=t.x===(null===e||void 0===e||null===(r=e.labelViewBox)||void 0===r?void 0:r.x)||t.x===e.x,i=t.y===(null===e||void 0===e||null===(n=e.labelViewBox)||void 0===n?void 0:n.y)||t.y===e.y;return o&&i}function $(t,e){var r=t.endAngle===e.endAngle,n=t.startAngle===e.startAngle;return r&&n}function W(t,e){var r=t.x===e.x,n=t.y===e.y,o=t.z===e.z;return r&&n&&o}function q(t){var e=t.activeTooltipItem,r=t.graphicalItem,n=t.itemData,o=function(t,e){var r;return L(t,e)?r="trapezoids":z(t,e)?r="sectors":U(t,e)&&(r="points"),r}(r,e),i=function(t,e){var r,n;return L(t,e)?null===(r=e.tooltipPayload)||void 0===r||null===(r=r[0])||void 0===r||null===(r=r.payload)||void 0===r?void 0:r.payload:z(t,e)?null===(n=e.tooltipPayload)||void 0===n||null===(n=n[0])||void 0===n||null===(n=n.payload)||void 0===n?void 0:n.payload:U(t,e)?e.payload:{}}(r,e),a=n.filter(function(t,n){var a=f()(i,t),u=r.props[o].filter(function(t){var n=function(t,e){var r;return L(t,e)?r=F:z(t,e)?r=$:U(t,e)&&(r=W),r}(r,e);return n(t,e)}),c=r.props[o].indexOf(u[u.length-1]);return a&&n===c});return n.indexOf(a[a.length-1])}},914:(t,e,r)=>{var n=r(9841);t.exports=function(t){if("string"==typeof t||n(t))return t;var e=t+"";return"0"==e&&1/t==-1/0?"-0":e}},929:(t,e,r)=>{var n=r(3211),o=r(6571),i=r(9194),a=r(6686);t.exports=function(t,e,r){if(!a(r))return!1;var u=typeof e;return!!("number"==u?o(r)&&i(e,r.length):"string"==u&&e in r)&&n(r[e],t)}},977:(t,e,r)=>{var n=r(9096),o=r(4416);t.exports=function(t,e){return t&&t.length?o(t,n(e,2)):[]}},1069:(t,e,r)=>{var n=r(8541);t.exports=function(t){return null==t?"":n(t)}},1141:(t,e,r)=>{var n=r(143),o=/^\s+/;t.exports=function(t){return t?t.slice(0,n(t)+1).replace(o,""):t}},1143:(t,e,r)=>{var n=r(3028)(Object.keys,Object);t.exports=n},1170:t=>{t.exports=function(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)}},1268:(t,e,r)=>{var n=r(5428),o=r(7574),i=r(6832),a=i&&i.isTypedArray,u=a?o(a):n;t.exports=u},1310:t=>{t.exports=function(t,e){return function(r){return null!=r&&(r[t]===e&&(void 0!==e||t in Object(r)))}}},1327:(t,e,r)=>{"use strict";r.d(e,{s:()=>L});var n=r(5043),o=r(1629),i=r.n(o),a=r(8387),u=r(155),c=r(4794),l=r(1985),s=r(7287);function f(t){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},f(t)}function p(){return p=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},p.apply(this,arguments)}function h(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function d(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,x(n.key),n)}}function y(t,e,r){return e=m(e),function(t,e){if(e&&("object"===f(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,v()?Reflect.construct(e,r||[],m(t).constructor):e.apply(t,r))}function v(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(v=function(){return!!t})()}function m(t){return m=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},m(t)}function b(t,e){return b=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},b(t,e)}function g(t,e,r){return(e=x(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function x(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:e+""}var w=32,O=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),y(this,e,arguments)}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&b(t,e)}(e,t),r=e,o=[{key:"renderIcon",value:function(t){var e=this.props.inactiveColor,r=16,o=w/6,i=w/3,a=t.inactive?e:t.color;if("plainline"===t.type)return n.createElement("line",{strokeWidth:4,fill:"none",stroke:a,strokeDasharray:t.payload.strokeDasharray,x1:0,y1:r,x2:w,y2:r,className:"recharts-legend-icon"});if("line"===t.type)return n.createElement("path",{strokeWidth:4,fill:"none",stroke:a,d:"M0,".concat(r,"h").concat(i,"\n            A").concat(o,",").concat(o,",0,1,1,").concat(2*i,",").concat(r,"\n            H").concat(w,"M").concat(2*i,",").concat(r,"\n            A").concat(o,",").concat(o,",0,1,1,").concat(i,",").concat(r),className:"recharts-legend-icon"});if("rect"===t.type)return n.createElement("path",{stroke:"none",fill:a,d:"M0,".concat(4,"h").concat(w,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(n.isValidElement(t.legendIcon)){var u=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?h(Object(r),!0).forEach(function(e){g(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({},t);return delete u.legendIcon,n.cloneElement(t.legendIcon,u)}return n.createElement(l.i,{fill:a,cx:r,cy:r,size:w,sizeType:"diameter",type:t.type})}},{key:"renderItems",value:function(){var t=this,e=this.props,r=e.payload,o=e.iconSize,l=e.layout,f=e.formatter,h=e.inactiveColor,d={x:0,y:0,width:w,height:w},y={display:"horizontal"===l?"inline-block":"block",marginRight:10},v={display:"inline-block",verticalAlign:"middle",marginRight:4};return r.map(function(e,r){var l=e.formatter||f,m=(0,a.A)(g(g({"recharts-legend-item":!0},"legend-item-".concat(r),!0),"inactive",e.inactive));if("none"===e.type)return null;var b=i()(e.value)?null:e.value;(0,u.R)(!i()(e.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var x=e.inactive?h:e.color;return n.createElement("li",p({className:m,style:y,key:"legend-item-".concat(r)},(0,s.XC)(t.props,e,r)),n.createElement(c.u,{width:o,height:o,viewBox:d,style:v},t.renderIcon(e)),n.createElement("span",{className:"recharts-legend-item-text",style:{color:x}},l?l(b,e,r):b))})}},{key:"render",value:function(){var t=this.props,e=t.payload,r=t.layout,o=t.align;if(!e||!e.length)return null;var i={padding:0,margin:0,textAlign:"horizontal"===r?o:"left"};return n.createElement("ul",{className:"recharts-default-legend",style:i},this.renderItems())}}],o&&d(r.prototype,o),f&&d(r,f),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,f}(n.PureComponent);g(O,"displayName","Legend"),g(O,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var j=r(6307),S=r(2598);function A(t){return A="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},A(t)}var P=["ref"];function E(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function k(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?E(Object(r),!0).forEach(function(e){D(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):E(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function M(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,N(n.key),n)}}function _(t,e,r){return e=C(e),function(t,e){if(e&&("object"===A(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,T()?Reflect.construct(e,r||[],C(t).constructor):e.apply(t,r))}function T(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(T=function(){return!!t})()}function C(t){return C=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},C(t)}function I(t,e){return I=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},I(t,e)}function D(t,e,r){return(e=N(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function N(t){var e=function(t,e){if("object"!=A(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=A(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==A(e)?e:e+""}function B(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function R(t){return t.value}var L=function(t){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return D(t=_(this,e,[].concat(n)),"lastBoundingBox",{width:-1,height:-1}),t}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&I(t,e)}(e,t),r=e,i=[{key:"getWithHeight",value:function(t,e){var r=k(k({},this.defaultProps),t.props).layout;return"vertical"===r&&(0,j.Et)(t.props.height)?{height:t.props.height}:"horizontal"===r?{width:t.props.width||e}:null}}],(o=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();return t.height=this.wrapperNode.offsetHeight,t.width=this.wrapperNode.offsetWidth,t}return null}},{key:"updateBBox",value:function(){var t=this.props.onBBoxUpdate,e=this.getBBox();e?(Math.abs(e.width-this.lastBoundingBox.width)>1||Math.abs(e.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=e.width,this.lastBoundingBox.height=e.height,t&&t(e)):-1===this.lastBoundingBox.width&&-1===this.lastBoundingBox.height||(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,t&&t(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?k({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(t){var e,r,n=this.props,o=n.layout,i=n.align,a=n.verticalAlign,u=n.margin,c=n.chartWidth,l=n.chartHeight;return t&&(void 0!==t.left&&null!==t.left||void 0!==t.right&&null!==t.right)||(e="center"===i&&"vertical"===o?{left:((c||0)-this.getBBoxSnapshot().width)/2}:"right"===i?{right:u&&u.right||0}:{left:u&&u.left||0}),t&&(void 0!==t.top&&null!==t.top||void 0!==t.bottom&&null!==t.bottom)||(r="middle"===a?{top:((l||0)-this.getBBoxSnapshot().height)/2}:"bottom"===a?{bottom:u&&u.bottom||0}:{top:u&&u.top||0}),k(k({},e),r)}},{key:"render",value:function(){var t=this,e=this.props,r=e.content,o=e.width,i=e.height,a=e.wrapperStyle,u=e.payloadUniqBy,c=e.payload,l=k(k({position:"absolute",width:o||"auto",height:i||"auto"},this.getDefaultPosition(a)),a);return n.createElement("div",{className:"recharts-legend-wrapper",style:l,ref:function(e){t.wrapperNode=e}},function(t,e){if(n.isValidElement(t))return n.cloneElement(t,e);if("function"===typeof t)return n.createElement(t,e);e.ref;var r=B(e,P);return n.createElement(O,r)}(r,k(k({},this.props),{},{payload:(0,S.s)(c,u,R)})))}}])&&M(r.prototype,o),i&&M(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,i}(n.PureComponent);D(L,"displayName","Legend"),D(L,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"})},1340:(t,e,r)=>{var n=r(3211);t.exports=function(t,e){for(var r=t.length;r--;)if(n(t[r][0],e))return r;return-1}},1497:(t,e,r)=>{"use strict";var n=r(3218);function o(){}function i(){}i.resetWarningCache=o,t.exports=function(){function t(t,e,r,o,i,a){if(a!==n){var u=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw u.name="Invariant Violation",u}}function e(){return t}t.isRequired=t;var r={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,elementType:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e,checkPropTypes:i,resetWarningCache:o};return r.PropTypes=r,r}},1519:(t,e,r)=>{"use strict";r.d(e,{Z:()=>E});var n=r(5043),o=r(9686),i=r.n(o),a=r(6686),u=r.n(a),c=r(1629),l=r.n(c),s=r(4065),f=r.n(s),p=r(2647),h=r(1639),d=r(240),y=r(202);function v(t){return v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},v(t)}var m=["valueAccessor"],b=["data","dataKey","clockWise","id","textBreakAll"];function g(t){return function(t){if(Array.isArray(t))return x(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return x(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return x(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function x(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function w(){return w=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},w.apply(this,arguments)}function O(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function j(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?O(Object(r),!0).forEach(function(e){S(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):O(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function S(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=v(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=v(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==v(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function A(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var P=function(t){return Array.isArray(t.value)?f()(t.value):t.value};function E(t){var e=t.valueAccessor,r=void 0===e?P:e,o=A(t,m),a=o.data,u=o.dataKey,c=o.clockWise,l=o.id,s=o.textBreakAll,f=A(o,b);return a&&a.length?n.createElement(h.W,{className:"recharts-label-list"},a.map(function(t,e){var o=i()(u)?r(t,e):(0,y.kr)(t&&t.payload,u),a=i()(l)?{}:{id:"".concat(l,"-").concat(e)};return n.createElement(p.J,w({},(0,d.J9)(t,!0),f,a,{parentViewBox:t.parentViewBox,value:o,textBreakAll:s,viewBox:p.J.parseViewBox(i()(c)?t:j(j({},t),{},{clockWise:c})),key:"label-".concat(e),index:e}))})):null}E.displayName="LabelList",E.renderCallByParent=function(t,e){var r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!t||!t.children&&r&&!t.label)return null;var o=t.children,i=(0,d.aS)(o,E).map(function(t,r){return(0,n.cloneElement)(t,{data:e,key:"labelList-".concat(r)})});return r?[function(t,e){return t?!0===t?n.createElement(E,{key:"labelList-implicit",data:e}):n.isValidElement(t)||l()(t)?n.createElement(E,{key:"labelList-implicit",data:e,content:t}):u()(t)?n.createElement(E,w({data:e},t,{key:"labelList-implicit"})):null:null}(t.label,e)].concat(g(i)):i}},1558:t=>{t.exports=function(t,e,r){for(var n=-1,o=null==t?0:t.length;++n<o;)if(r(e,t[n]))return!0;return!1}},1629:(t,e,r)=>{var n=r(6913),o=r(6686);t.exports=function(t){if(!o(t))return!1;var e=n(t);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e}},1639:(t,e,r)=>{"use strict";r.d(e,{W:()=>l});var n=r(5043),o=r(8387),i=r(240),a=["children","className"];function u(){return u=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},u.apply(this,arguments)}function c(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var l=n.forwardRef(function(t,e){var r=t.children,l=t.className,s=c(t,a),f=(0,o.A)("recharts-layer",l);return n.createElement("g",u({className:f},(0,i.J9)(s,!0),{ref:e}),r)})},1714:(t,e,r)=>{var n=r(1340);t.exports=function(t,e){var r=this.__data__,o=n(r,t);return o<0?(++this.size,r.push([t,e])):r[o][1]=e,this}},1733:(t,e,r)=>{var n=r(1775),o=r(4664),i=r(9096);t.exports=function(t,e){var r={};return e=i(e,3),o(t,function(t,o,i){n(r,o,e(t,o,i))}),r}},1744:(t,e,r)=>{"use strict";r.d(e,{Ay:()=>Ct});var n=r(5043),o=r(5173),i=r.n(o),a=Object.getOwnPropertyNames,u=Object.getOwnPropertySymbols,c=Object.prototype.hasOwnProperty;function l(t,e){return function(r,n,o){return t(r,n,o)&&e(r,n,o)}}function s(t){return function(e,r,n){if(!e||!r||"object"!==typeof e||"object"!==typeof r)return t(e,r,n);var o=n.cache,i=o.get(e),a=o.get(r);if(i&&a)return i===r&&a===e;o.set(e,r),o.set(r,e);var u=t(e,r,n);return o.delete(e),o.delete(r),u}}function f(t){return a(t).concat(u(t))}var p=Object.hasOwn||function(t,e){return c.call(t,e)};function h(t,e){return t===e||!t&&!e&&t!==t&&e!==e}var d=Object.getOwnPropertyDescriptor,y=Object.keys;function v(t,e,r){var n=t.length;if(e.length!==n)return!1;for(;n-- >0;)if(!r.equals(t[n],e[n],n,n,t,e,r))return!1;return!0}function m(t,e){return h(t.getTime(),e.getTime())}function b(t,e){return t.name===e.name&&t.message===e.message&&t.cause===e.cause&&t.stack===e.stack}function g(t,e){return t===e}function x(t,e,r){var n=t.size;if(n!==e.size)return!1;if(!n)return!0;for(var o,i,a=new Array(n),u=t.entries(),c=0;(o=u.next())&&!o.done;){for(var l=e.entries(),s=!1,f=0;(i=l.next())&&!i.done;)if(a[f])f++;else{var p=o.value,h=i.value;if(r.equals(p[0],h[0],c,f,t,e,r)&&r.equals(p[1],h[1],p[0],h[0],t,e,r)){s=a[f]=!0;break}f++}if(!s)return!1;c++}return!0}var w=h;function O(t,e,r){var n=y(t),o=n.length;if(y(e).length!==o)return!1;for(;o-- >0;)if(!M(t,e,r,n[o]))return!1;return!0}function j(t,e,r){var n,o,i,a=f(t),u=a.length;if(f(e).length!==u)return!1;for(;u-- >0;){if(!M(t,e,r,n=a[u]))return!1;if(o=d(t,n),i=d(e,n),(o||i)&&(!o||!i||o.configurable!==i.configurable||o.enumerable!==i.enumerable||o.writable!==i.writable))return!1}return!0}function S(t,e){return h(t.valueOf(),e.valueOf())}function A(t,e){return t.source===e.source&&t.flags===e.flags}function P(t,e,r){var n=t.size;if(n!==e.size)return!1;if(!n)return!0;for(var o,i,a=new Array(n),u=t.values();(o=u.next())&&!o.done;){for(var c=e.values(),l=!1,s=0;(i=c.next())&&!i.done;){if(!a[s]&&r.equals(o.value,i.value,o.value,i.value,t,e,r)){l=a[s]=!0;break}s++}if(!l)return!1}return!0}function E(t,e){var r=t.length;if(e.length!==r)return!1;for(;r-- >0;)if(t[r]!==e[r])return!1;return!0}function k(t,e){return t.hostname===e.hostname&&t.pathname===e.pathname&&t.protocol===e.protocol&&t.port===e.port&&t.hash===e.hash&&t.username===e.username&&t.password===e.password}function M(t,e,r,n){return!("_owner"!==n&&"__o"!==n&&"__v"!==n||!t.$$typeof&&!e.$$typeof)||p(e,n)&&r.equals(t[n],e[n],n,n,t,e,r)}var _=Array.isArray,T="function"===typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,C=Object.assign,I=Object.prototype.toString.call.bind(Object.prototype.toString);var D=N();N({strict:!0}),N({circular:!0}),N({circular:!0,strict:!0}),N({createInternalComparator:function(){return h}}),N({strict:!0,createInternalComparator:function(){return h}}),N({circular:!0,createInternalComparator:function(){return h}}),N({circular:!0,createInternalComparator:function(){return h},strict:!0});function N(t){void 0===t&&(t={});var e,r=t.circular,n=void 0!==r&&r,o=t.createInternalComparator,i=t.createState,a=t.strict,u=void 0!==a&&a,c=function(t){var e=t.circular,r=t.createCustomConfig,n=t.strict,o={areArraysEqual:n?j:v,areDatesEqual:m,areErrorsEqual:b,areFunctionsEqual:g,areMapsEqual:n?l(x,j):x,areNumbersEqual:w,areObjectsEqual:n?j:O,arePrimitiveWrappersEqual:S,areRegExpsEqual:A,areSetsEqual:n?l(P,j):P,areTypedArraysEqual:n?j:E,areUrlsEqual:k};if(r&&(o=C({},o,r(o))),e){var i=s(o.areArraysEqual),a=s(o.areMapsEqual),u=s(o.areObjectsEqual),c=s(o.areSetsEqual);o=C({},o,{areArraysEqual:i,areMapsEqual:a,areObjectsEqual:u,areSetsEqual:c})}return o}(t),f=function(t){var e=t.areArraysEqual,r=t.areDatesEqual,n=t.areErrorsEqual,o=t.areFunctionsEqual,i=t.areMapsEqual,a=t.areNumbersEqual,u=t.areObjectsEqual,c=t.arePrimitiveWrappersEqual,l=t.areRegExpsEqual,s=t.areSetsEqual,f=t.areTypedArraysEqual,p=t.areUrlsEqual;return function(t,h,d){if(t===h)return!0;if(null==t||null==h)return!1;var y=typeof t;if(y!==typeof h)return!1;if("object"!==y)return"number"===y?a(t,h,d):"function"===y&&o(t,h,d);var v=t.constructor;if(v!==h.constructor)return!1;if(v===Object)return u(t,h,d);if(_(t))return e(t,h,d);if(null!=T&&T(t))return f(t,h,d);if(v===Date)return r(t,h,d);if(v===RegExp)return l(t,h,d);if(v===Map)return i(t,h,d);if(v===Set)return s(t,h,d);var m=I(t);return"[object Date]"===m?r(t,h,d):"[object RegExp]"===m?l(t,h,d):"[object Map]"===m?i(t,h,d):"[object Set]"===m?s(t,h,d):"[object Object]"===m?"function"!==typeof t.then&&"function"!==typeof h.then&&u(t,h,d):"[object URL]"===m?p(t,h,d):"[object Error]"===m?n(t,h,d):"[object Arguments]"===m?u(t,h,d):("[object Boolean]"===m||"[object Number]"===m||"[object String]"===m)&&c(t,h,d)}}(c);return function(t){var e=t.circular,r=t.comparator,n=t.createState,o=t.equals,i=t.strict;if(n)return function(t,a){var u=n(),c=u.cache,l=void 0===c?e?new WeakMap:void 0:c,s=u.meta;return r(t,a,{cache:l,equals:o,meta:s,strict:i})};if(e)return function(t,e){return r(t,e,{cache:new WeakMap,equals:o,meta:void 0,strict:i})};var a={cache:void 0,equals:o,meta:void 0,strict:i};return function(t,e){return r(t,e,a)}}({circular:n,comparator:f,createState:i,equals:o?o(f):(e=f,function(t,r,n,o,i,a,u){return e(t,r,u)}),strict:u})}function B(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=-1;requestAnimationFrame(function n(o){r<0&&(r=o),o-r>e?(t(o),r=-1):function(t){"undefined"!==typeof requestAnimationFrame&&requestAnimationFrame(t)}(n)})}function R(t){return R="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},R(t)}function L(t){return function(t){if(Array.isArray(t))return t}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return z(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return z(t,e)}(t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function z(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function U(){var t=function(){return null},e=!1,r=function r(n){if(!e){if(Array.isArray(n)){if(!n.length)return;var o=L(n),i=o[0],a=o.slice(1);return"number"===typeof i?void B(r.bind(null,a),i):(r(i),void B(r.bind(null,a)))}"object"===R(n)&&t(n),"function"===typeof n&&n()}};return{stop:function(){e=!0},start:function(t){e=!1,r(t)},subscribe:function(e){return t=e,function(){t=function(){return null}}}}}function F(t){return F="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},F(t)}function $(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function W(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?$(Object(r),!0).forEach(function(e){q(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):$(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function q(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==F(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==F(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===F(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var X=function(t){return t},H=function(t,e){return Object.keys(e).reduce(function(r,n){return W(W({},r),{},q({},n,t(n,e[n])))},{})},G=function(t,e,r){return t.map(function(t){return"".concat((n=t,n.replace(/([A-Z])/g,function(t){return"-".concat(t.toLowerCase())}))," ").concat(e,"ms ").concat(r);var n}).join(",")};function V(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||Y(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function K(t){return function(t){if(Array.isArray(t))return J(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||Y(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Y(t,e){if(t){if("string"===typeof t)return J(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?J(t,e):void 0}}function J(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Z=1e-4,Q=function(t,e){return[0,3*t,3*e-6*t,3*t-3*e+1]},tt=function(t,e){return t.map(function(t,r){return t*Math.pow(e,r)}).reduce(function(t,e){return t+e})},et=function(t,e){return function(r){var n=Q(t,e);return tt(n,r)}},rt=function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e[0],o=e[1],i=e[2],a=e[3];if(1===e.length)switch(e[0]){case"linear":n=0,o=0,i=1,a=1;break;case"ease":n=.25,o=.1,i=.25,a=1;break;case"ease-in":n=.42,o=0,i=1,a=1;break;case"ease-out":n=.42,o=0,i=.58,a=1;break;case"ease-in-out":n=0,o=0,i=.58,a=1;break;default:var u=e[0].split("(");if("cubic-bezier"===u[0]&&4===u[1].split(")")[0].split(",").length){var c=V(u[1].split(")")[0].split(",").map(function(t){return parseFloat(t)}),4);n=c[0],o=c[1],i=c[2],a=c[3]}}[n,i,o,a].every(function(t){return"number"===typeof t&&t>=0&&t<=1});var l,s,f=et(n,i),p=et(o,a),h=(l=n,s=i,function(t){var e=Q(l,s),r=[].concat(K(e.map(function(t,e){return t*e}).slice(1)),[0]);return tt(r,t)}),d=function(t){return t>1?1:t<0?0:t},y=function(t){for(var e=t>1?1:t,r=e,n=0;n<8;++n){var o=f(r)-e,i=h(r);if(Math.abs(o-e)<Z||i<Z)return p(r);r=d(r-o/i)}return p(r)};return y.isStepper=!1,y},nt=function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e[0];if("string"===typeof n)switch(n){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return rt(n);case"spring":return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.stiff,r=void 0===e?100:e,n=t.damping,o=void 0===n?8:n,i=t.dt,a=void 0===i?17:i,u=function(t,e,n){var i=n+(-(t-e)*r-n*o)*a/1e3,u=n*a/1e3+t;return Math.abs(u-e)<Z&&Math.abs(i)<Z?[e,0]:[u,i]};return u.isStepper=!0,u.dt=a,u}();default:if("cubic-bezier"===n.split("(")[0])return rt(n)}return"function"===typeof n?n:null};function ot(t){return ot="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ot(t)}function it(t){return function(t){if(Array.isArray(t))return ft(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||st(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function at(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ut(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?at(Object(r),!0).forEach(function(e){ct(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):at(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function ct(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==ot(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==ot(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===ot(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function lt(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||st(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function st(t,e){if(t){if("string"===typeof t)return ft(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?ft(t,e):void 0}}function ft(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var pt=function(t,e,r){return t+(e-t)*r},ht=function(t){return t.from!==t.to},dt=function t(e,r,n){var o=H(function(t,r){if(ht(r)){var n=lt(e(r.from,r.to,r.velocity),2),o=n[0],i=n[1];return ut(ut({},r),{},{from:o,velocity:i})}return r},r);return n<1?H(function(t,e){return ht(e)?ut(ut({},e),{},{velocity:pt(e.velocity,o[t].velocity,n),from:pt(e.from,o[t].from,n)}):e},r):t(e,o,n-1)};const yt=function(t,e,r,n,o){var i,a,u,c,l=(i=t,a=e,[Object.keys(i),Object.keys(a)].reduce(function(t,e){return t.filter(function(t){return e.includes(t)})})),s=l.reduce(function(r,n){return ut(ut({},r),{},ct({},n,[t[n],e[n]]))},{}),f=l.reduce(function(r,n){return ut(ut({},r),{},ct({},n,{from:t[n],velocity:0,to:e[n]}))},{}),p=-1,h=function(){return null};return h=r.isStepper?function(n){u||(u=n);var i=(n-u)/r.dt;f=dt(r,f,i),o(ut(ut(ut({},t),e),H(function(t,e){return e.from},f))),u=n,Object.values(f).filter(ht).length&&(p=requestAnimationFrame(h))}:function(i){c||(c=i);var a=(i-c)/n,u=H(function(t,e){return pt.apply(void 0,it(e).concat([r(a)]))},s);if(o(ut(ut(ut({},t),e),u)),a<1)p=requestAnimationFrame(h);else{var l=H(function(t,e){return pt.apply(void 0,it(e).concat([r(1)]))},s);o(ut(ut(ut({},t),e),l))}},function(){return requestAnimationFrame(h),function(){cancelAnimationFrame(p)}}};function vt(t){return vt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},vt(t)}var mt=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function bt(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r,n,o={},i=Object.keys(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||(o[r]=t[r]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function gt(t){return function(t){if(Array.isArray(t))return xt(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return xt(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return xt(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function xt(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function wt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function Ot(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?wt(Object(r),!0).forEach(function(e){jt(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):wt(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function jt(t,e,r){return(e=At(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function St(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,At(n.key),n)}}function At(t){var e=function(t,e){if("object"!==vt(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==vt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===vt(e)?e:String(e)}function Pt(t,e){return Pt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Pt(t,e)}function Et(t){var e=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var r,n=_t(t);if(e){var o=_t(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return kt(this,r)}}function kt(t,e){if(e&&("object"===vt(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return Mt(t)}function Mt(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function _t(t){return _t=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},_t(t)}var Tt=function(t){!function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Pt(t,e)}(a,t);var e,r,o,i=Et(a);function a(t,e){var r;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,a);var n=(r=i.call(this,t,e)).props,o=n.isActive,u=n.attributeName,c=n.from,l=n.to,s=n.steps,f=n.children,p=n.duration;if(r.handleStyleChange=r.handleStyleChange.bind(Mt(r)),r.changeStyle=r.changeStyle.bind(Mt(r)),!o||p<=0)return r.state={style:{}},"function"===typeof f&&(r.state={style:l}),kt(r);if(s&&s.length)r.state={style:s[0].style};else if(c){if("function"===typeof f)return r.state={style:c},kt(r);r.state={style:u?jt({},u,c):c}}else r.state={style:{}};return r}return e=a,(r=[{key:"componentDidMount",value:function(){var t=this.props,e=t.isActive,r=t.canBegin;this.mounted=!0,e&&r&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(t){var e=this.props,r=e.isActive,n=e.canBegin,o=e.attributeName,i=e.shouldReAnimate,a=e.to,u=e.from,c=this.state.style;if(n)if(r){if(!(D(t.to,a)&&t.canBegin&&t.isActive)){var l=!t.canBegin||!t.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var s=l||i?u:t.to;if(this.state&&c){var f={style:o?jt({},o,s):s};(o&&c[o]!==s||!o&&c!==s)&&this.setState(f)}this.runAnimation(Ot(Ot({},this.props),{},{from:s,begin:0}))}}else{var p={style:o?jt({},o,a):a};this.state&&c&&(o&&c[o]!==a||!o&&c!==a)&&this.setState(p)}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var t=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),t&&t()}},{key:"handleStyleChange",value:function(t){this.changeStyle(t)}},{key:"changeStyle",value:function(t){this.mounted&&this.setState({style:t})}},{key:"runJSAnimation",value:function(t){var e=this,r=t.from,n=t.to,o=t.duration,i=t.easing,a=t.begin,u=t.onAnimationEnd,c=t.onAnimationStart,l=yt(r,n,nt(i),o,this.changeStyle);this.manager.start([c,a,function(){e.stopJSAnimation=l()},o,u])}},{key:"runStepAnimation",value:function(t){var e=this,r=t.steps,n=t.begin,o=t.onAnimationStart,i=r[0],a=i.style,u=i.duration,c=void 0===u?0:u;return this.manager.start([o].concat(gt(r.reduce(function(t,n,o){if(0===o)return t;var i=n.duration,a=n.easing,u=void 0===a?"ease":a,c=n.style,l=n.properties,s=n.onAnimationEnd,f=o>0?r[o-1]:n,p=l||Object.keys(c);if("function"===typeof u||"spring"===u)return[].concat(gt(t),[e.runJSAnimation.bind(e,{from:f.style,to:c,duration:i,easing:u}),i]);var h=G(p,i,u),d=Ot(Ot(Ot({},f.style),c),{},{transition:h});return[].concat(gt(t),[d,i,s]).filter(X)},[a,Math.max(c,n)])),[t.onAnimationEnd]))}},{key:"runAnimation",value:function(t){this.manager||(this.manager=U());var e=t.begin,r=t.duration,n=t.attributeName,o=t.to,i=t.easing,a=t.onAnimationStart,u=t.onAnimationEnd,c=t.steps,l=t.children,s=this.manager;if(this.unSubscribe=s.subscribe(this.handleStyleChange),"function"!==typeof i&&"function"!==typeof l&&"spring"!==i)if(c.length>1)this.runStepAnimation(t);else{var f=n?jt({},n,o):o,p=G(Object.keys(f),r,i);s.start([a,e,Ot(Ot({},f),{},{transition:p}),r,u])}else this.runJSAnimation(t)}},{key:"render",value:function(){var t=this.props,e=t.children,r=(t.begin,t.duration),o=(t.attributeName,t.easing,t.isActive),i=(t.steps,t.from,t.to,t.canBegin,t.onAnimationEnd,t.shouldReAnimate,t.onAnimationReStart,bt(t,mt)),a=n.Children.count(e),u=this.state.style;if("function"===typeof e)return e(u);if(!o||0===a||r<=0)return e;var c=function(t){var e=t.props,r=e.style,o=void 0===r?{}:r,a=e.className;return(0,n.cloneElement)(t,Ot(Ot({},i),{},{style:Ot(Ot({},o),u),className:a}))};return 1===a?c(n.Children.only(e)):n.createElement("div",null,n.Children.map(e,function(t){return c(t)}))}}])&&St(e.prototype,r),o&&St(e,o),Object.defineProperty(e,"prototype",{writable:!1}),a}(n.PureComponent);Tt.displayName="Animate",Tt.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},Tt.propTypes={from:i().oneOfType([i().object,i().string]),to:i().oneOfType([i().object,i().string]),attributeName:i().string,duration:i().number,begin:i().number,easing:i().oneOfType([i().string,i().func]),steps:i().arrayOf(i().shape({duration:i().number.isRequired,style:i().object.isRequired,easing:i().oneOfType([i().oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),i().func]),properties:i().arrayOf("string"),onAnimationEnd:i().func})),children:i().oneOfType([i().node,i().func]),isActive:i().bool,canBegin:i().bool,onAnimationEnd:i().func,shouldReAnimate:i().bool,onAnimationStart:i().func,onAnimationReStart:i().func};const Ct=Tt},1775:(t,e,r)=>{var n=r(5654);t.exports=function(t,e,r){"__proto__"==e&&n?n(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r}},1946:(t,e,r)=>{var n=r(1340);t.exports=function(t){return n(this.__data__,t)>-1}},1985:(t,e,r)=>{"use strict";r.d(e,{i:()=>F});var n=r(5043),o=r(643),i=r.n(o);Math.abs,Math.atan2;const a=Math.cos,u=(Math.max,Math.min,Math.sin),c=Math.sqrt,l=Math.PI,s=2*l;const f={draw(t,e){const r=c(e/l);t.moveTo(r,0),t.arc(0,0,r,0,s)}},p={draw(t,e){const r=c(e/5)/2;t.moveTo(-3*r,-r),t.lineTo(-r,-r),t.lineTo(-r,-3*r),t.lineTo(r,-3*r),t.lineTo(r,-r),t.lineTo(3*r,-r),t.lineTo(3*r,r),t.lineTo(r,r),t.lineTo(r,3*r),t.lineTo(-r,3*r),t.lineTo(-r,r),t.lineTo(-3*r,r),t.closePath()}},h=c(1/3),d=2*h,y={draw(t,e){const r=c(e/d),n=r*h;t.moveTo(0,-r),t.lineTo(n,0),t.lineTo(0,r),t.lineTo(-n,0),t.closePath()}},v={draw(t,e){const r=c(e),n=-r/2;t.rect(n,n,r,r)}},m=u(l/10)/u(7*l/10),b=u(s/10)*m,g=-a(s/10)*m,x={draw(t,e){const r=c(.8908130915292852*e),n=b*r,o=g*r;t.moveTo(0,-r),t.lineTo(n,o);for(let i=1;i<5;++i){const e=s*i/5,c=a(e),l=u(e);t.lineTo(l*r,-c*r),t.lineTo(c*n-l*o,l*n+c*o)}t.closePath()}},w=c(3),O={draw(t,e){const r=-c(e/(3*w));t.moveTo(0,2*r),t.lineTo(-w*r,-r),t.lineTo(w*r,-r),t.closePath()}},j=-.5,S=c(3)/2,A=1/c(12),P=3*(A/2+1),E={draw(t,e){const r=c(e/P),n=r/2,o=r*A,i=n,a=r*A+r,u=-i,l=a;t.moveTo(n,o),t.lineTo(i,a),t.lineTo(u,l),t.lineTo(j*n-S*o,S*n+j*o),t.lineTo(j*i-S*a,S*i+j*a),t.lineTo(j*u-S*l,S*u+j*l),t.lineTo(j*n+S*o,j*o-S*n),t.lineTo(j*i+S*a,j*a-S*i),t.lineTo(j*u+S*l,j*l-S*u),t.closePath()}};var k=r(3809),M=r(7371);c(3),c(3);var _=r(8387),T=r(240);function C(t){return C="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},C(t)}var I=["type","size","sizeType"];function D(){return D=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},D.apply(this,arguments)}function N(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function B(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?N(Object(r),!0).forEach(function(e){R(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):N(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function R(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=C(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=C(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==C(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function L(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var z={symbolCircle:f,symbolCross:p,symbolDiamond:y,symbolSquare:v,symbolStar:x,symbolTriangle:O,symbolWye:E},U=Math.PI/180,F=function(t){var e=t.type,r=void 0===e?"circle":e,o=t.size,a=void 0===o?64:o,u=t.sizeType,c=void 0===u?"area":u,l=B(B({},L(t,I)),{},{type:r,size:a,sizeType:c}),s=l.className,p=l.cx,h=l.cy,d=(0,T.J9)(l,!0);return p===+p&&h===+h&&a===+a?n.createElement("path",D({},d,{className:(0,_.A)("recharts-symbols",s),transform:"translate(".concat(p,", ").concat(h,")"),d:function(){var t=function(t){var e="symbol".concat(i()(t));return z[e]||f}(r),e=function(t,e){let r=null,n=(0,M.i)(o);function o(){let o;if(r||(r=o=n()),t.apply(this,arguments).draw(r,+e.apply(this,arguments)),o)return r=null,o+""||null}return t="function"===typeof t?t:(0,k.A)(t||f),e="function"===typeof e?e:(0,k.A)(void 0===e?64:+e),o.type=function(e){return arguments.length?(t="function"===typeof e?e:(0,k.A)(e),o):t},o.size=function(t){return arguments.length?(e="function"===typeof t?t:(0,k.A)(+t),o):e},o.context=function(t){return arguments.length?(r=null==t?null:t,o):r},o}().type(t).size(function(t,e,r){if("area"===e)return t;switch(r){case"cross":return 5*t*t/9;case"diamond":return.5*t*t/Math.sqrt(3);case"square":return t*t;case"star":var n=18*U;return 1.25*t*t*(Math.tan(n)-Math.tan(2*n)*Math.pow(Math.tan(n),2));case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}}(a,c,r));return e()}()})):null};F.registerSymbol=function(t,e){z["symbol".concat(i()(t))]=e}},2070:(t,e,r)=>{var n=r(7937)(r(6552),"Set");t.exports=n},2074:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t){r[++e]=t}),r}},2099:(t,e,r)=>{"use strict";r.d(e,{A:()=>i,z:()=>u});var n=r(4402),o=r(5186);function i(){var t,e,r=(0,o.A)().unknown(void 0),a=r.domain,u=r.range,c=0,l=1,s=!1,f=0,p=0,h=.5;function d(){var r=a().length,n=l<c,o=n?l:c,i=n?c:l;t=(i-o)/Math.max(1,r-f+2*p),s&&(t=Math.floor(t)),o+=(i-o-t*(r-f))*h,e=t*(1-f),s&&(o=Math.round(o),e=Math.round(e));var d=function(t,e,r){t=+t,e=+e,r=(o=arguments.length)<2?(e=t,t=0,1):o<3?1:+r;for(var n=-1,o=0|Math.max(0,Math.ceil((e-t)/r)),i=new Array(o);++n<o;)i[n]=t+n*r;return i}(r).map(function(e){return o+t*e});return u(n?d.reverse():d)}return delete r.unknown,r.domain=function(t){return arguments.length?(a(t),d()):a()},r.range=function(t){return arguments.length?([c,l]=t,c=+c,l=+l,d()):[c,l]},r.rangeRound=function(t){return[c,l]=t,c=+c,l=+l,s=!0,d()},r.bandwidth=function(){return e},r.step=function(){return t},r.round=function(t){return arguments.length?(s=!!t,d()):s},r.padding=function(t){return arguments.length?(f=Math.min(1,p=+t),d()):f},r.paddingInner=function(t){return arguments.length?(f=Math.min(1,t),d()):f},r.paddingOuter=function(t){return arguments.length?(p=+t,d()):p},r.align=function(t){return arguments.length?(h=Math.max(0,Math.min(1,t)),d()):h},r.copy=function(){return i(a(),[c,l]).round(s).paddingInner(f).paddingOuter(p).align(h)},n.C.apply(d(),arguments)}function a(t){var e=t.copy;return t.padding=t.paddingOuter,delete t.paddingInner,delete t.paddingOuter,t.copy=function(){return a(e())},t}function u(){return a(i.apply(null,arguments).paddingInner(1))}},2103:(t,e,r)=>{"use strict";r.d(e,{DR:()=>g,pj:()=>O,rY:()=>k,yi:()=>E,Yp:()=>x,hj:()=>P,sk:()=>A,AF:()=>w,Nk:()=>S,$G:()=>j});var n=r(5043),o=r(3404),i=r(8990),a=r.n(i),u=r(7002),c=r.n(u),l=r(5797),s=r.n(l)()(function(t){return{x:t.left,y:t.top,width:t.width,height:t.height}},function(t){return["l",t.left,"t",t.top,"w",t.width,"h",t.height].join("")}),f=r(6307);var p=(0,n.createContext)(void 0),h=(0,n.createContext)(void 0),d=(0,n.createContext)(void 0),y=(0,n.createContext)({}),v=(0,n.createContext)(void 0),m=(0,n.createContext)(0),b=(0,n.createContext)(0),g=function(t){var e=t.state,r=e.xAxisMap,o=e.yAxisMap,i=e.offset,a=t.clipPathId,u=t.children,c=t.width,l=t.height,f=s(i);return n.createElement(p.Provider,{value:r},n.createElement(h.Provider,{value:o},n.createElement(y.Provider,{value:i},n.createElement(d.Provider,{value:f},n.createElement(v.Provider,{value:a},n.createElement(m.Provider,{value:l},n.createElement(b.Provider,{value:c},u)))))))},x=function(){return(0,n.useContext)(v)};var w=function(t){var e=(0,n.useContext)(p);null==e&&(0,o.A)(!1);var r=e[t];return null==r&&(0,o.A)(!1),r},O=function(){var t=(0,n.useContext)(p);return(0,f.lX)(t)},j=function(){var t=(0,n.useContext)(h);return a()(t,function(t){return c()(t.domain,Number.isFinite)})||(0,f.lX)(t)},S=function(t){var e=(0,n.useContext)(h);null==e&&(0,o.A)(!1);var r=e[t];return null==r&&(0,o.A)(!1),r},A=function(){return(0,n.useContext)(d)},P=function(){return(0,n.useContext)(y)},E=function(){return(0,n.useContext)(b)},k=function(){return(0,n.useContext)(m)}},2154:(t,e,r)=>{var n=r(5575),o=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;if(n){var r=e[t];return"__lodash_hash_undefined__"===r?void 0:r}return o.call(e,t)?e[t]:void 0}},2165:(t,e,r)=>{var n=r(5652);t.exports=function(t,e){var r;return n(t,function(t,n,o){return!(r=e(t,n,o))}),!!r}},2185:(t,e,r)=>{"use strict";r.d(e,{W:()=>b});var n=r(5043),o=r(8387),i=r(2103),a=r(7671),u=r(202);function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function l(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,y(n.key),n)}}function s(t,e,r){return e=p(e),function(t,e){if(e&&("object"===c(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,f()?Reflect.construct(e,r||[],p(t).constructor):e.apply(t,r))}function f(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(f=function(){return!!t})()}function p(t){return p=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},p(t)}function h(t,e){return h=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},h(t,e)}function d(t,e,r){return(e=y(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function y(t){var e=function(t,e){if("object"!=c(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=c(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==c(e)?e:e+""}function v(){return v=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},v.apply(this,arguments)}function m(t){var e=t.xAxisId,r=(0,i.yi)(),c=(0,i.rY)(),l=(0,i.AF)(e);return null==l?null:n.createElement(a.u,v({},l,{className:(0,o.A)("recharts-".concat(l.axisType," ").concat(l.axisType),l.className),viewBox:{x:0,y:0,width:r,height:c},ticksGenerator:function(t){return(0,u.Rh)(t,!0)}}))}var b=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),s(this,e,arguments)}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&h(t,e)}(e,t),r=e,(o=[{key:"render",value:function(){return n.createElement(m,this.props)}}])&&l(r.prototype,o),i&&l(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,i}(n.Component);d(b,"displayName","XAxis"),d(b,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0})},2322:(t,e,r)=>{var n=r(6913),o=r(5990),i=r(2761),a=Function.prototype,u=Object.prototype,c=a.toString,l=u.hasOwnProperty,s=c.call(Object);t.exports=function(t){if(!i(t)||"[object Object]"!=n(t))return!1;var e=o(t);if(null===e)return!0;var r=l.call(e,"constructor")&&e.constructor;return"function"==typeof r&&r instanceof r&&c.call(r)==s}},2520:(t,e,r)=>{var n=r(5816),o=r(9096),i=r(9140),a=Math.max;t.exports=function(t,e,r){var u=null==t?0:t.length;if(!u)return-1;var c=null==r?0:i(r);return c<0&&(c=a(u+c,0)),n(t,o(e,3),c)}},2536:(t,e,r)=>{var n=r(149),o=r(2969),i=r(9096),a=r(8883),u=r(320),c=r(7574),l=r(5893),s=r(3279),f=r(4052);t.exports=function(t,e,r){e=e.length?n(e,function(t){return f(t)?function(e){return o(e,1===t.length?t[0]:t)}:t}):[s];var p=-1;e=n(e,c(i));var h=a(t,function(t,r,o){return{criteria:n(e,function(e){return e(t)}),index:++p,value:t}});return u(h,function(t,e){return l(t,e,r)})}},2541:t=>{t.exports=function(t){return function(){return t}}},2587:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(e(t[r],r,t))return!0;return!1}},2597:(t,e,r)=>{var n=r(4052),o=r(9841),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.exports=function(t,e){if(n(t))return!1;var r=typeof t;return!("number"!=r&&"symbol"!=r&&"boolean"!=r&&null!=t&&!o(t))||(a.test(t)||!i.test(t)||null!=e&&t in Object(e))}},2598:(t,e,r)=>{"use strict";r.d(e,{s:()=>u});var n=r(977),o=r.n(n),i=r(1629),a=r.n(i);function u(t,e,r){return!0===e?o()(t,r):a()(e)?o()(t,e):t}},2622:(t,e,r)=>{var n=r(705);t.exports=function(t,e){var r=t.__data__;return n(e)?r["string"==typeof e?"string":"hash"]:r.map}},2647:(t,e,r)=>{"use strict";r.d(e,{J:()=>M});var n=r(5043),o=r(9686),i=r.n(o),a=r(1629),u=r.n(a),c=r(6686),l=r.n(c),s=r(8387),f=r(4140),p=r(240),h=r(6307),d=r(165);function y(t){return y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},y(t)}var v=["offset"];function m(t){return function(t){if(Array.isArray(t))return b(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return b(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return b(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function b(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function g(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function x(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function w(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?x(Object(r),!0).forEach(function(e){O(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):x(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function O(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=y(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=y(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==y(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function j(){return j=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},j.apply(this,arguments)}var S=function(t){var e=t.value,r=t.formatter,n=i()(t.children)?e:t.children;return u()(r)?r(n):n},A=function(t,e,r){var o,a,u=t.position,c=t.viewBox,l=t.offset,f=t.className,p=c,y=p.cx,v=p.cy,m=p.innerRadius,b=p.outerRadius,g=p.startAngle,x=p.endAngle,w=p.clockWise,O=(m+b)/2,S=function(t,e){return(0,h.sA)(e-t)*Math.min(Math.abs(e-t),360)}(g,x),A=S>=0?1:-1;"insideStart"===u?(o=g+A*l,a=w):"insideEnd"===u?(o=x-A*l,a=!w):"end"===u&&(o=x+A*l,a=w),a=S<=0?a:!a;var P=(0,d.IZ)(y,v,O,o),E=(0,d.IZ)(y,v,O,o+359*(a?1:-1)),k="M".concat(P.x,",").concat(P.y,"\n    A").concat(O,",").concat(O,",0,1,").concat(a?0:1,",\n    ").concat(E.x,",").concat(E.y),M=i()(t.id)?(0,h.NF)("recharts-radial-line-"):t.id;return n.createElement("text",j({},r,{dominantBaseline:"central",className:(0,s.A)("recharts-radial-bar-label",f)}),n.createElement("defs",null,n.createElement("path",{id:M,d:k})),n.createElement("textPath",{xlinkHref:"#".concat(M)},e))},P=function(t){var e=t.viewBox,r=t.offset,n=t.position,o=e,i=o.cx,a=o.cy,u=o.innerRadius,c=o.outerRadius,l=(o.startAngle+o.endAngle)/2;if("outside"===n){var s=(0,d.IZ)(i,a,c+r,l),f=s.x;return{x:f,y:s.y,textAnchor:f>=i?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"end"};var p=(u+c)/2,h=(0,d.IZ)(i,a,p,l);return{x:h.x,y:h.y,textAnchor:"middle",verticalAnchor:"middle"}},E=function(t){var e=t.viewBox,r=t.parentViewBox,n=t.offset,o=t.position,i=e,a=i.x,u=i.y,c=i.width,s=i.height,f=s>=0?1:-1,p=f*n,d=f>0?"end":"start",y=f>0?"start":"end",v=c>=0?1:-1,m=v*n,b=v>0?"end":"start",g=v>0?"start":"end";if("top"===o)return w(w({},{x:a+c/2,y:u-f*n,textAnchor:"middle",verticalAnchor:d}),r?{height:Math.max(u-r.y,0),width:c}:{});if("bottom"===o)return w(w({},{x:a+c/2,y:u+s+p,textAnchor:"middle",verticalAnchor:y}),r?{height:Math.max(r.y+r.height-(u+s),0),width:c}:{});if("left"===o){var x={x:a-m,y:u+s/2,textAnchor:b,verticalAnchor:"middle"};return w(w({},x),r?{width:Math.max(x.x-r.x,0),height:s}:{})}if("right"===o){var O={x:a+c+m,y:u+s/2,textAnchor:g,verticalAnchor:"middle"};return w(w({},O),r?{width:Math.max(r.x+r.width-O.x,0),height:s}:{})}var j=r?{width:c,height:s}:{};return"insideLeft"===o?w({x:a+m,y:u+s/2,textAnchor:g,verticalAnchor:"middle"},j):"insideRight"===o?w({x:a+c-m,y:u+s/2,textAnchor:b,verticalAnchor:"middle"},j):"insideTop"===o?w({x:a+c/2,y:u+p,textAnchor:"middle",verticalAnchor:y},j):"insideBottom"===o?w({x:a+c/2,y:u+s-p,textAnchor:"middle",verticalAnchor:d},j):"insideTopLeft"===o?w({x:a+m,y:u+p,textAnchor:g,verticalAnchor:y},j):"insideTopRight"===o?w({x:a+c-m,y:u+p,textAnchor:b,verticalAnchor:y},j):"insideBottomLeft"===o?w({x:a+m,y:u+s-p,textAnchor:g,verticalAnchor:d},j):"insideBottomRight"===o?w({x:a+c-m,y:u+s-p,textAnchor:b,verticalAnchor:d},j):l()(o)&&((0,h.Et)(o.x)||(0,h._3)(o.x))&&((0,h.Et)(o.y)||(0,h._3)(o.y))?w({x:a+(0,h.F4)(o.x,c),y:u+(0,h.F4)(o.y,s),textAnchor:"end",verticalAnchor:"end"},j):w({x:a+c/2,y:u+s/2,textAnchor:"middle",verticalAnchor:"middle"},j)},k=function(t){return"cx"in t&&(0,h.Et)(t.cx)};function M(t){var e,r=t.offset,o=w({offset:void 0===r?5:r},g(t,v)),a=o.viewBox,c=o.position,l=o.value,h=o.children,d=o.content,y=o.className,m=void 0===y?"":y,b=o.textBreakAll;if(!a||i()(l)&&i()(h)&&!(0,n.isValidElement)(d)&&!u()(d))return null;if((0,n.isValidElement)(d))return(0,n.cloneElement)(d,o);if(u()(d)){if(e=(0,n.createElement)(d,o),(0,n.isValidElement)(e))return e}else e=S(o);var x=k(a),O=(0,p.J9)(o,!0);if(x&&("insideStart"===c||"insideEnd"===c||"end"===c))return A(o,e,O);var M=x?P(o):E(o);return n.createElement(f.E,j({className:(0,s.A)("recharts-label",m)},O,M,{breakAll:b}),e)}M.displayName="Label";var _=function(t){var e=t.cx,r=t.cy,n=t.angle,o=t.startAngle,i=t.endAngle,a=t.r,u=t.radius,c=t.innerRadius,l=t.outerRadius,s=t.x,f=t.y,p=t.top,d=t.left,y=t.width,v=t.height,m=t.clockWise,b=t.labelViewBox;if(b)return b;if((0,h.Et)(y)&&(0,h.Et)(v)){if((0,h.Et)(s)&&(0,h.Et)(f))return{x:s,y:f,width:y,height:v};if((0,h.Et)(p)&&(0,h.Et)(d))return{x:p,y:d,width:y,height:v}}return(0,h.Et)(s)&&(0,h.Et)(f)?{x:s,y:f,width:0,height:0}:(0,h.Et)(e)&&(0,h.Et)(r)?{cx:e,cy:r,startAngle:o||n||0,endAngle:i||n||0,innerRadius:c||0,outerRadius:l||u||a||0,clockWise:m}:t.viewBox?t.viewBox:{}};M.parseViewBox=_,M.renderCallByParent=function(t,e){var r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!t||!t.children&&r&&!t.label)return null;var o=t.children,i=_(t),a=(0,p.aS)(o,M).map(function(t,r){return(0,n.cloneElement)(t,{viewBox:e||i,key:"label-".concat(r)})});if(!r)return a;var c=function(t,e){return t?!0===t?n.createElement(M,{key:"label-implicit",viewBox:e}):(0,h.vh)(t)?n.createElement(M,{key:"label-implicit",viewBox:e,value:t}):(0,n.isValidElement)(t)?t.type===M?(0,n.cloneElement)(t,{key:"label-implicit",viewBox:e}):n.createElement(M,{key:"label-implicit",content:t,viewBox:e}):u()(t)?n.createElement(M,{key:"label-implicit",content:t,viewBox:e}):l()(t)?n.createElement(M,j({viewBox:e},t,{key:"label-implicit"})):null:null}(t.label,e||i);return[c].concat(m(a))}},2662:(t,e,r)=>{var n=r(5575);t.exports=function(t,e){var r=this.__data__;return this.size+=this.has(t)?0:1,r[t]=n&&void 0===e?"__lodash_hash_undefined__":e,this}},2761:t=>{t.exports=function(t){return null!=t&&"object"==typeof t}},2777:(t,e,r)=>{var n=r(5193),o=r(2761),i=Object.prototype,a=i.hasOwnProperty,u=i.propertyIsEnumerable,c=n(function(){return arguments}())?n:function(t){return o(t)&&a.call(t,"callee")&&!u.call(t,"callee")};t.exports=c},2866:(t,e,r)=>{var n=r(2969);t.exports=function(t){return function(e){return n(e,t)}}},2929:(t,e,r)=>{var n=r(6552).Uint8Array;t.exports=n},2969:(t,e,r)=>{var n=r(5324),o=r(914);t.exports=function(t,e){for(var r=0,i=(e=n(e,t)).length;null!=t&&r<i;)t=t[o(e[r++])];return r&&r==i?t:void 0}},3028:t=>{t.exports=function(t,e){return function(r){return t(e(r))}}},3097:(t,e,r)=>{var n=r(2969);t.exports=function(t,e,r){var o=null==t?void 0:n(t,e);return void 0===o?r:o}},3204:(t,e,r)=>{var n=r(3343),o=r(2777),i=r(4052),a=r(4543),u=r(9194),c=r(1268),l=Object.prototype.hasOwnProperty;t.exports=function(t,e){var r=i(t),s=!r&&o(t),f=!r&&!s&&a(t),p=!r&&!s&&!f&&c(t),h=r||s||f||p,d=h?n(t.length,String):[],y=d.length;for(var v in t)!e&&!l.call(t,v)||h&&("length"==v||f&&("offset"==v||"parent"==v)||p&&("buffer"==v||"byteLength"==v||"byteOffset"==v)||u(v,y))||d.push(v);return d}},3211:t=>{t.exports=function(t,e){return t===e||t!==t&&e!==e}},3218:t=>{"use strict";t.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},3279:t=>{t.exports=function(t){return t}},3331:(t,e,r)=>{var n=r(9676),o=r(929),i=r(7303);t.exports=function(t){return function(e,r,a){return a&&"number"!=typeof a&&o(e,r,a)&&(r=a=void 0),e=i(e),void 0===r?(r=e,e=0):r=i(r),a=void 0===a?e<r?1:-1:i(a),n(e,r,a,t)}}},3343:t=>{t.exports=function(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n}},3366:(t,e,r)=>{var n=r(7894),o=r(9057);t.exports=function(t,e){return null!=t&&o(t,e,n)}},3404:(t,e,r)=>{"use strict";r.d(e,{A:()=>i});var n=!0,o="Invariant failed";function i(t,e){if(!t){if(n)throw new Error(o);var r="function"===typeof e?e():e,i=r?"".concat(o,": ").concat(r):o;throw new Error(i)}}},3411:(t,e,r)=>{var n=r(149),o=r(9096),i=r(8883),a=r(4052);t.exports=function(t,e){return(a(t)?n:i)(t,o(e,3))}},3440:(t,e,r)=>{var n=r(6552)["__core-js_shared__"];t.exports=n},3538:(t,e,r)=>{var n=r(755),o=r(3411);t.exports=function(t,e){return n(o(t,e),1)}},3668:(t,e,r)=>{var n=r(8902),o=r(2587),i=r(8114);t.exports=function(t,e,r,a,u,c){var l=1&r,s=t.length,f=e.length;if(s!=f&&!(l&&f>s))return!1;var p=c.get(t),h=c.get(e);if(p&&h)return p==e&&h==t;var d=-1,y=!0,v=2&r?new n:void 0;for(c.set(t,e),c.set(e,t);++d<s;){var m=t[d],b=e[d];if(a)var g=l?a(b,m,d,e,t,c):a(m,b,d,t,e,c);if(void 0!==g){if(g)continue;y=!1;break}if(v){if(!o(e,function(t,e){if(!i(v,e)&&(m===t||u(m,t,r,a,c)))return v.push(e)})){y=!1;break}}else if(m!==b&&!u(m,b,r,a,c)){y=!1;break}}return c.delete(t),c.delete(e),y}},3713:(t,e,r)=>{var n=r(6140),o=r(1143),i=Object.prototype.hasOwnProperty;t.exports=function(t){if(!n(t))return o(t);var e=[];for(var r in Object(t))i.call(t,r)&&"constructor"!=r&&e.push(r);return e}},3781:(t,e,r)=>{var n=r(9417),o=r(8673);t.exports=function(t){for(var e=o(t),r=e.length;r--;){var i=e[r],a=t[i];e[r]=[i,a,n(a)]}return e}},3809:(t,e,r)=>{"use strict";function n(t){return function(){return t}}r.d(e,{A:()=>n})},3831:(t,e,r)=>{"use strict";r.d(e,{P2:()=>w,bx:()=>O,pr:()=>m,sl:()=>b,vh:()=>g});var n=r(1733),o=r.n(n),i=r(7002),a=r.n(i),u=r(202),c=r(240),l=r(6307),s=r(8763);function f(t){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},f(t)}function p(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,v(n.key),n)}}function h(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function d(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?h(Object(r),!0).forEach(function(e){y(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function y(t,e,r){return(e=v(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function v(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:e+""}var m=function(t,e,r,n,o){var i=t.width,a=t.height,f=t.layout,p=t.children,h=Object.keys(e),v={left:r.left,leftMirror:r.left,right:i-r.right,rightMirror:i-r.right,top:r.top,topMirror:r.top,bottom:a-r.bottom,bottomMirror:a-r.bottom},m=!!(0,c.BU)(p,s.y);return h.reduce(function(i,a){var c,s,p,h,b,g=e[a],x=g.orientation,w=g.domain,O=g.padding,j=void 0===O?{}:O,S=g.mirror,A=g.reversed,P="".concat(x).concat(S?"Mirror":"");if("number"===g.type&&("gap"===g.padding||"no-gap"===g.padding)){var E=w[1]-w[0],k=1/0,M=g.categoricalDomain.sort(l.ck);if(M.forEach(function(t,e){e>0&&(k=Math.min((t||0)-(M[e-1]||0),k))}),Number.isFinite(k)){var _=k/E,T="vertical"===g.layout?r.height:r.width;if("gap"===g.padding&&(c=_*T/2),"no-gap"===g.padding){var C=(0,l.F4)(t.barCategoryGap,_*T),I=_*T/2;c=I-C-(I-C)/T*C}}}s="xAxis"===n?[r.left+(j.left||0)+(c||0),r.left+r.width-(j.right||0)-(c||0)]:"yAxis"===n?"horizontal"===f?[r.top+r.height-(j.bottom||0),r.top+(j.top||0)]:[r.top+(j.top||0)+(c||0),r.top+r.height-(j.bottom||0)-(c||0)]:g.range,A&&(s=[s[1],s[0]]);var D=(0,u.W7)(g,o,m),N=D.scale,B=D.realScaleType;N.domain(w).range(s),(0,u.YB)(N);var R=(0,u.w7)(N,d(d({},g),{},{realScaleType:B}));"xAxis"===n?(b="top"===x&&!S||"bottom"===x&&S,p=r.left,h=v[P]-b*g.height):"yAxis"===n&&(b="left"===x&&!S||"right"===x&&S,p=v[P]-b*g.width,h=r.top);var L=d(d(d({},g),R),{},{realScaleType:B,x:p,y:h,scale:N,width:"xAxis"===n?r.width:g.width,height:"yAxis"===n?r.height:g.height});return L.bandSize=(0,u.Hj)(L,R),g.hide||"xAxis"!==n?g.hide||(v[P]+=(b?-1:1)*L.width):v[P]+=(b?-1:1)*L.height,d(d({},i),{},y({},a,L))},{})},b=function(t,e){var r=t.x,n=t.y,o=e.x,i=e.y;return{x:Math.min(r,o),y:Math.min(n,i),width:Math.abs(o-r),height:Math.abs(i-n)}},g=function(t){var e=t.x1,r=t.y1,n=t.x2,o=t.y2;return b({x:e,y:r},{x:n,y:o})},x=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.scale=e}return e=t,r=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.bandAware,n=e.position;if(void 0!==t){if(n)switch(n){case"start":default:return this.scale(t);case"middle":var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+o;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(t)+i}if(r){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+a}return this.scale(t)}}},{key:"isInRange",value:function(t){var e=this.range(),r=e[0],n=e[e.length-1];return r<=n?t>=r&&t<=n:t>=n&&t<=r}}],n=[{key:"create",value:function(e){return new t(e)}}],r&&p(e.prototype,r),n&&p(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,r,n}();y(x,"EPS",1e-4);var w=function(t){var e=Object.keys(t).reduce(function(e,r){return d(d({},e),{},y({},r,x.create(t[r])))},{});return d(d({},e),{},{apply:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.bandAware,i=r.position;return o()(t,function(t,r){return e[r].apply(t,{bandAware:n,position:i})})},isInRange:function(t){return a()(t,function(t,r){return e[r].isInRange(t)})}})};var O=function(t){var e=t.width,r=t.height,n=function(t){return(t%180+180)%180}(arguments.length>1&&void 0!==arguments[1]?arguments[1]:0),o=n*Math.PI/180,i=Math.atan(r/e),a=o>i&&o<Math.PI-i?r/Math.sin(o):e/Math.cos(o);return Math.abs(a)}},3871:t=>{t.exports=function(t,e,r){var n=-1,o=t.length;e<0&&(e=-e>o?0:o+e),(r=r>o?o:r)<0&&(r+=o),o=e>r?0:r-e>>>0,e>>>=0;for(var i=Array(o);++n<o;)i[n]=t[n+e];return i}},3892:t=>{t.exports=function(t){return this.__data__.has(t)}},3932:(t,e,r)=>{var n=r(396),o=r(2866),i=r(2597),a=r(914);t.exports=function(t){return i(t)?n(a(t)):o(t)}},3950:(t,e,r)=>{var n=r(6686),o=r(4757),i=r(801),a=Math.max,u=Math.min;t.exports=function(t,e,r){var c,l,s,f,p,h,d=0,y=!1,v=!1,m=!0;if("function"!=typeof t)throw new TypeError("Expected a function");function b(e){var r=c,n=l;return c=l=void 0,d=e,f=t.apply(n,r)}function g(t){var r=t-h;return void 0===h||r>=e||r<0||v&&t-d>=s}function x(){var t=o();if(g(t))return w(t);p=setTimeout(x,function(t){var r=e-(t-h);return v?u(r,s-(t-d)):r}(t))}function w(t){return p=void 0,m&&c?b(t):(c=l=void 0,f)}function O(){var t=o(),r=g(t);if(c=arguments,l=this,h=t,r){if(void 0===p)return function(t){return d=t,p=setTimeout(x,e),y?b(t):f}(h);if(v)return clearTimeout(p),p=setTimeout(x,e),b(h)}return void 0===p&&(p=setTimeout(x,e)),f}return e=i(e)||0,n(r)&&(y=!!r.leading,s=(v="maxWait"in r)?a(i(r.maxWait)||0,e):s,m="trailing"in r?!!r.trailing:m),O.cancel=function(){void 0!==p&&clearTimeout(p),d=0,c=h=l=p=void 0},O.flush=function(){return void 0===p?f:w(o())},O}},4020:t=>{t.exports=function(t,e,r){for(var n=r-1,o=t.length;++n<o;)if(t[n]===e)return n;return-1}},4052:t=>{var e=Array.isArray;t.exports=e},4065:t=>{t.exports=function(t){var e=null==t?0:t.length;return e?t[e-1]:void 0}},4079:(t,e,r)=>{var n=r(8259),o=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,i=/\\(\\)?/g,a=n(function(t){var e=[];return 46===t.charCodeAt(0)&&e.push(""),t.replace(o,function(t,r,n,o){e.push(n?o.replace(i,"$1"):r||t)}),e});t.exports=a},4140:(t,e,r)=>{"use strict";r.d(e,{E:()=>z});var n=r(5043),o=r(9686),i=r.n(o),a=r(8387),u=r(6307),c=r(6015),l=r(240),s=r(7213);function f(t){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},f(t)}function p(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return h(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return h(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function d(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,y(n.key),n)}}function y(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:e+""}var v=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,m=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,b=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,g=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,x={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},w=Object.keys(x),O="NaN";var j=function(){function t(e,r){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.num=e,this.unit=r,this.num=e,this.unit=r,Number.isNaN(e)&&(this.unit=""),""===r||b.test(r)||(this.num=NaN,this.unit=""),w.includes(r)&&(this.num=function(t,e){return t*x[e]}(e,r),this.unit="px")}return e=t,n=[{key:"parse",value:function(e){var r,n=p(null!==(r=g.exec(e))&&void 0!==r?r:[],3),o=n[1],i=n[2];return new t(parseFloat(o),null!==i&&void 0!==i?i:"")}}],(r=[{key:"add",value:function(e){return this.unit!==e.unit?new t(NaN,""):new t(this.num+e.num,this.unit)}},{key:"subtract",value:function(e){return this.unit!==e.unit?new t(NaN,""):new t(this.num-e.num,this.unit)}},{key:"multiply",value:function(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new t(NaN,""):new t(this.num*e.num,this.unit||e.unit)}},{key:"divide",value:function(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new t(NaN,""):new t(this.num/e.num,this.unit||e.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}])&&d(e.prototype,r),n&&d(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,r,n}();function S(t){if(t.includes(O))return O;for(var e=t;e.includes("*")||e.includes("/");){var r,n=p(null!==(r=v.exec(e))&&void 0!==r?r:[],4),o=n[1],i=n[2],a=n[3],u=j.parse(null!==o&&void 0!==o?o:""),c=j.parse(null!==a&&void 0!==a?a:""),l="*"===i?u.multiply(c):u.divide(c);if(l.isNaN())return O;e=e.replace(v,l.toString())}for(;e.includes("+")||/.-\d+(?:\.\d+)?/.test(e);){var s,f=p(null!==(s=m.exec(e))&&void 0!==s?s:[],4),h=f[1],d=f[2],y=f[3],b=j.parse(null!==h&&void 0!==h?h:""),g=j.parse(null!==y&&void 0!==y?y:""),x="+"===d?b.add(g):b.subtract(g);if(x.isNaN())return O;e=e.replace(m,x.toString())}return e}var A=/\(([^()]*)\)/;function P(t){var e=t.replace(/\s+/g,"");return e=function(t){for(var e=t;e.includes("(");){var r=p(A.exec(e),2)[1];e=e.replace(A,S(r))}return e}(e),e=S(e)}function E(t){var e=function(t){try{return P(t)}catch(e){return O}}(t.slice(5,-1));return e===O?"":e}var k=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],M=["dx","dy","angle","className","breakAll"];function _(){return _=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},_.apply(this,arguments)}function T(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function C(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return I(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return I(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function I(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var D=/[ \f\n\r\t\v\u2028\u2029]+/,N=function(t){var e=t.children,r=t.breakAll,n=t.style;try{var o=[];return i()(e)||(o=r?e.toString().split(""):e.toString().split(D)),{wordsWithComputedWidth:o.map(function(t){return{word:t,width:(0,s.Pu)(t,n).width}}),spaceWidth:r?0:(0,s.Pu)("\xa0",n).width}}catch(a){return null}},B=function(t){return[{words:i()(t)?[]:t.toString().split(D)}]},R=function(t){var e=t.width,r=t.scaleToFit,n=t.children,o=t.style,i=t.breakAll,a=t.maxLines;if((e||r)&&!c.m.isSsr){var l=N({breakAll:i,children:n,style:o});return l?function(t,e,r,n,o){var i=t.maxLines,a=t.children,c=t.style,l=t.breakAll,s=(0,u.Et)(i),f=a,p=function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).reduce(function(t,e){var i=e.word,a=e.width,u=t[t.length-1];if(u&&(null==n||o||u.width+a+r<Number(n)))u.words.push(i),u.width+=a+r;else{var c={words:[i],width:a};t.push(c)}return t},[])},h=p(e);if(!s)return h;for(var d,y=function(t){var e=f.slice(0,t),r=N({breakAll:l,style:c,children:e+"\u2026"}).wordsWithComputedWidth,o=p(r),a=o.length>i||function(t){return t.reduce(function(t,e){return t.width>e.width?t:e})}(o).width>Number(n);return[a,o]},v=0,m=f.length-1,b=0;v<=m&&b<=f.length-1;){var g=Math.floor((v+m)/2),x=C(y(g-1),2),w=x[0],O=x[1],j=C(y(g),1)[0];if(w||j||(v=g+1),w&&j&&(m=g-1),!w&&j){d=O;break}b++}return d||h}({breakAll:i,children:n,maxLines:a,style:o},l.wordsWithComputedWidth,l.spaceWidth,e,r):B(n)}return B(n)},L="#808080",z=function(t){var e=t.x,r=void 0===e?0:e,o=t.y,i=void 0===o?0:o,c=t.lineHeight,s=void 0===c?"1em":c,f=t.capHeight,p=void 0===f?"0.71em":f,h=t.scaleToFit,d=void 0!==h&&h,y=t.textAnchor,v=void 0===y?"start":y,m=t.verticalAnchor,b=void 0===m?"end":m,g=t.fill,x=void 0===g?L:g,w=T(t,k),O=(0,n.useMemo)(function(){return R({breakAll:w.breakAll,children:w.children,maxLines:w.maxLines,scaleToFit:d,style:w.style,width:w.width})},[w.breakAll,w.children,w.maxLines,d,w.style,w.width]),j=w.dx,S=w.dy,A=w.angle,P=w.className,C=w.breakAll,I=T(w,M);if(!(0,u.vh)(r)||!(0,u.vh)(i))return null;var D,N=r+((0,u.Et)(j)?j:0),B=i+((0,u.Et)(S)?S:0);switch(b){case"start":D=E("calc(".concat(p,")"));break;case"middle":D=E("calc(".concat((O.length-1)/2," * -").concat(s," + (").concat(p," / 2))"));break;default:D=E("calc(".concat(O.length-1," * -").concat(s,")"))}var z=[];if(d){var U=O[0].width,F=w.width;z.push("scale(".concat(((0,u.Et)(F)?F/U:1)/U,")"))}return A&&z.push("rotate(".concat(A,", ").concat(N,", ").concat(B,")")),z.length&&(I.transform=z.join(" ")),n.createElement("text",_({},(0,l.J9)(I,!0),{x:N,y:B,className:(0,a.A)("recharts-text",P),textAnchor:v,fill:x.includes("url")?L:x}),O.map(function(t,e){var r=t.words.join(C?"":" ");return n.createElement("tspan",{x:N,dy:0===e?D:s,key:"".concat(r,"-").concat(e)},r)}))}},4160:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t,n){r[++e]=[n,t]}),r}},4190:(t,e,r)=>{var n=r(1340);t.exports=function(t){var e=this.__data__,r=n(e,t);return r<0?void 0:e[r][1]}},4204:(t,e,r)=>{"use strict";r.d(e,{jH:()=>i});var n=r(5043),o=(r(579),n.createContext(void 0));function i(t){const e=n.useContext(o);return t||e||"ltr"}},4258:(t,e,r)=>{var n=r(5906)();t.exports=n},4262:(t,e,r)=>{var n=r(8895),o=r(4052);t.exports=function(t,e,r){var i=e(t);return o(t)?i:n(i,r(t))}},4342:(t,e,r)=>{"use strict";r.d(e,{J:()=>y,M:()=>m});var n=r(5043),o=r(8387),i=r(1744),a=r(240);function u(t){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function c(){return c=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},c.apply(this,arguments)}function l(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return s(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return s(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function f(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function p(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f(Object(r),!0).forEach(function(e){h(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function h(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=u(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=u(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==u(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var d=function(t,e,r,n,o){var i,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),u=n>=0?1:-1,c=r>=0?1:-1,l=n>=0&&r>=0||n<0&&r<0?1:0;if(a>0&&o instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=o[f]>a?a:o[f];i="M".concat(t,",").concat(e+u*s[0]),s[0]>0&&(i+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(l,",").concat(t+c*s[0],",").concat(e)),i+="L ".concat(t+r-c*s[1],",").concat(e),s[1]>0&&(i+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(l,",\n        ").concat(t+r,",").concat(e+u*s[1])),i+="L ".concat(t+r,",").concat(e+n-u*s[2]),s[2]>0&&(i+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(l,",\n        ").concat(t+r-c*s[2],",").concat(e+n)),i+="L ".concat(t+c*s[3],",").concat(e+n),s[3]>0&&(i+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(l,",\n        ").concat(t,",").concat(e+n-u*s[3])),i+="Z"}else if(a>0&&o===+o&&o>0){var p=Math.min(a,o);i="M ".concat(t,",").concat(e+u*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+c*p,",").concat(e,"\n            L ").concat(t+r-c*p,",").concat(e,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r,",").concat(e+u*p,"\n            L ").concat(t+r,",").concat(e+n-u*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r-c*p,",").concat(e+n,"\n            L ").concat(t+c*p,",").concat(e+n,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t,",").concat(e+n-u*p," Z")}else i="M ".concat(t,",").concat(e," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return i},y=function(t,e){if(!t||!e)return!1;var r=t.x,n=t.y,o=e.x,i=e.y,a=e.width,u=e.height;if(Math.abs(a)>0&&Math.abs(u)>0){var c=Math.min(o,o+a),l=Math.max(o,o+a),s=Math.min(i,i+u),f=Math.max(i,i+u);return r>=c&&r<=l&&n>=s&&n<=f}return!1},v={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},m=function(t){var e=p(p({},v),t),r=(0,n.useRef)(),u=l((0,n.useState)(-1),2),s=u[0],f=u[1];(0,n.useEffect)(function(){if(r.current&&r.current.getTotalLength)try{var t=r.current.getTotalLength();t&&f(t)}catch(e){}},[]);var h=e.x,y=e.y,m=e.width,b=e.height,g=e.radius,x=e.className,w=e.animationEasing,O=e.animationDuration,j=e.animationBegin,S=e.isAnimationActive,A=e.isUpdateAnimationActive;if(h!==+h||y!==+y||m!==+m||b!==+b||0===m||0===b)return null;var P=(0,o.A)("recharts-rectangle",x);return A?n.createElement(i.Ay,{canBegin:s>0,from:{width:m,height:b,x:h,y:y},to:{width:m,height:b,x:h,y:y},duration:O,animationEasing:w,isActive:A},function(t){var o=t.width,u=t.height,l=t.x,f=t.y;return n.createElement(i.Ay,{canBegin:s>0,from:"0px ".concat(-1===s?1:s,"px"),to:"".concat(s,"px 0px"),attributeName:"strokeDasharray",begin:j,duration:O,isActive:S,easing:w},n.createElement("path",c({},(0,a.J9)(e,!0),{className:P,d:d(l,f,o,u,g),ref:r})))}):n.createElement("path",c({},(0,a.J9)(e,!0),{className:P,d:d(h,y,m,b,g)}))}},4402:(t,e,r)=>{"use strict";function n(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t)}return this}function o(t,e){switch(arguments.length){case 0:break;case 1:"function"===typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"===typeof e?this.interpolator(e):this.range(e)}return this}r.d(e,{C:()=>n,K:()=>o})},4416:(t,e,r)=>{var n=r(8902),o=r(5866),i=r(1558),a=r(8114),u=r(8182),c=r(2074);t.exports=function(t,e,r){var l=-1,s=o,f=t.length,p=!0,h=[],d=h;if(r)p=!1,s=i;else if(f>=200){var y=e?null:u(t);if(y)return c(y);p=!1,s=a,d=new n}else d=e?[]:h;t:for(;++l<f;){var v=t[l],m=e?e(v):v;if(v=r||0!==v?v:0,p&&m===m){for(var b=d.length;b--;)if(d[b]===m)continue t;e&&d.push(m),h.push(v)}else s(d,m,r)||(d!==h&&d.push(m),h.push(v))}return h}},4543:(t,e,r)=>{t=r.nmd(t);var n=r(6552),o=r(14),i=e&&!e.nodeType&&e,a=i&&t&&!t.nodeType&&t,u=a&&a.exports===i?n.Buffer:void 0,c=(u?u.isBuffer:void 0)||o;t.exports=c},4545:(t,e,r)=>{var n=r(7160);t.exports=function(){this.__data__=new n,this.size=0}},4552:(t,e,r)=>{var n=r(9812),o=Object.prototype,i=o.hasOwnProperty,a=o.toString,u=n?n.toStringTag:void 0;t.exports=function(t){var e=i.call(t,u),r=t[u];try{t[u]=void 0;var n=!0}catch(c){}var o=a.call(t);return n&&(e?t[u]=r:delete t[u]),o}},4597:(t,e,r)=>{var n=r(2587),o=r(9096),i=r(2165),a=r(4052),u=r(929);t.exports=function(t,e,r){var c=a(t)?n:i;return r&&u(t,e,r)&&(e=void 0),c(t,o(e,3))}},4657:t=>{t.exports=function(t,e){return null==t?void 0:t[e]}},4664:(t,e,r)=>{var n=r(4258),o=r(8673);t.exports=function(t,e){return t&&n(t,e,o)}},4746:(t,e,r)=>{var n=r(5652);t.exports=function(t,e){var r=!0;return n(t,function(t,n,o){return r=!!e(t,n,o)}),r}},4757:(t,e,r)=>{var n=r(6552);t.exports=function(){return n.Date.now()}},4794:(t,e,r)=>{"use strict";r.d(e,{u:()=>l});var n=r(5043),o=r(8387),i=r(240),a=["children","width","height","viewBox","className","style","title","desc"];function u(){return u=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},u.apply(this,arguments)}function c(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function l(t){var e=t.children,r=t.width,l=t.height,s=t.viewBox,f=t.className,p=t.style,h=t.title,d=t.desc,y=c(t,a),v=s||{width:r,height:l,x:0,y:0},m=(0,o.A)("recharts-surface",f);return n.createElement("svg",u({},(0,i.J9)(y,!0,"svg"),{className:m,width:r,height:l,style:p,viewBox:"".concat(v.x," ").concat(v.y," ").concat(v.width," ").concat(v.height)}),n.createElement("title",null,h),n.createElement("desc",null,d),e)}},4816:(t,e,r)=>{var n=r(7251),o=r(7159),i=r(438),a=r(9394),u=r(6874);function c(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}c.prototype.clear=n,c.prototype.delete=o,c.prototype.get=i,c.prototype.has=a,c.prototype.set=u,t.exports=c},5029:(t,e,r)=>{var n=r(6989),o=r(3097),i=r(3366),a=r(2597),u=r(9417),c=r(1310),l=r(914);t.exports=function(t,e){return a(t)&&u(e)?c(l(t),e):function(r){var a=o(r,t);return void 0===a&&a===e?i(r,t):n(e,a,3)}}},5051:t=>{t.exports=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e}},5173:(t,e,r)=>{t.exports=r(1497)()},5186:(t,e,r)=>{"use strict";r.d(e,{A:()=>s,h:()=>l});class n extends Map{constructor(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:u;if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(const[r,n]of t)this.set(r,n)}get(t){return super.get(o(this,t))}has(t){return super.has(o(this,t))}set(t,e){return super.set(i(this,t),e)}delete(t){return super.delete(a(this,t))}}Set;function o(t,e){let{_intern:r,_key:n}=t;const o=n(e);return r.has(o)?r.get(o):e}function i(t,e){let{_intern:r,_key:n}=t;const o=n(e);return r.has(o)?r.get(o):(r.set(o,e),e)}function a(t,e){let{_intern:r,_key:n}=t;const o=n(e);return r.has(o)&&(e=r.get(o),r.delete(o)),e}function u(t){return null!==t&&"object"===typeof t?t.valueOf():t}var c=r(4402);const l=Symbol("implicit");function s(){var t=new n,e=[],r=[],o=l;function i(n){let i=t.get(n);if(void 0===i){if(o!==l)return o;t.set(n,i=e.push(n)-1)}return r[i%r.length]}return i.domain=function(r){if(!arguments.length)return e.slice();e=[],t=new n;for(const n of r)t.has(n)||t.set(n,e.push(n)-1);return i},i.range=function(t){return arguments.length?(r=Array.from(t),i):r.slice()},i.unknown=function(t){return arguments.length?(o=t,i):o},i.copy=function(){return s(e,r).unknown(o)},c.C.apply(i,arguments),i}},5193:(t,e,r)=>{var n=r(6913),o=r(2761);t.exports=function(t){return o(t)&&"[object Arguments]"==n(t)}},5204:(t,e,r)=>{var n=r(7937)(r(6552),"Map");t.exports=n},5248:(t,e,r)=>{"use strict";function n(t,e){for(var r in t)if({}.hasOwnProperty.call(t,r)&&(!{}.hasOwnProperty.call(e,r)||t[r]!==e[r]))return!1;for(var n in e)if({}.hasOwnProperty.call(e,n)&&!{}.hasOwnProperty.call(t,n))return!1;return!0}r.d(e,{b:()=>n})},5268:(t,e,r)=>{var n=r(9160);t.exports=function(t){return n(t)&&t!=+t}},5295:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(!e(t[r],r,t))return!1;return!0}},5324:(t,e,r)=>{var n=r(4052),o=r(2597),i=r(4079),a=r(1069);t.exports=function(t,e){return n(t)?t:o(t,e)?[t]:i(a(t))}},5387:(t,e,r)=>{var n=r(7937)(r(6552),"Promise");t.exports=n},5428:(t,e,r)=>{var n=r(6913),o=r(6173),i=r(2761),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,t.exports=function(t){return i(t)&&o(t.length)&&!!a[n(t)]}},5538:(t,e,r)=>{var n=r(7160),o=r(4545),i=r(793),a=r(7760),u=r(3892),c=r(6788);function l(t){var e=this.__data__=new n(t);this.size=e.size}l.prototype.clear=o,l.prototype.delete=i,l.prototype.get=a,l.prototype.has=u,l.prototype.set=c,t.exports=l},5575:(t,e,r)=>{var n=r(7937)(Object,"create");t.exports=n},5636:(t,e,r)=>{var n=r(1170),o=Math.max;t.exports=function(t,e,r){return e=o(void 0===e?t.length-1:e,0),function(){for(var i=arguments,a=-1,u=o(i.length-e,0),c=Array(u);++a<u;)c[a]=i[e+a];a=-1;for(var l=Array(e+1);++a<e;)l[a]=i[a];return l[e]=r(c),n(t,this,l)}}},5647:(t,e,r)=>{var n=r(3279),o=r(5636),i=r(6350);t.exports=function(t,e){return i(o(t,e,n),t+"")}},5652:(t,e,r)=>{var n=r(4664),o=r(6516)(n);t.exports=o},5654:(t,e,r)=>{var n=r(7937),o=function(){try{var t=n(Object,"defineProperty");return t({},"",{}),t}catch(e){}}();t.exports=o},5713:t=>{t.exports=function(){}},5752:(t,e,r)=>{var n=r(9395),o=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,i,a,u){var c=1&r,l=n(t),s=l.length;if(s!=n(e).length&&!c)return!1;for(var f=s;f--;){var p=l[f];if(!(c?p in e:o.call(e,p)))return!1}var h=u.get(t),d=u.get(e);if(h&&d)return h==e&&d==t;var y=!0;u.set(t,e),u.set(e,t);for(var v=c;++f<s;){var m=t[p=l[f]],b=e[p];if(i)var g=c?i(b,m,p,e,t,u):i(m,b,p,t,e,u);if(!(void 0===g?m===b||a(m,b,r,i,u):g)){y=!1;break}v||(v="constructor"==p)}if(y&&!v){var x=t.constructor,w=e.constructor;x==w||!("constructor"in t)||!("constructor"in e)||"function"==typeof x&&x instanceof x&&"function"==typeof w&&w instanceof w||(y=!1)}return u.delete(t),u.delete(e),y}},5797:(t,e,r)=>{var n=r(4816);function o(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw new TypeError("Expected a function");var r=function(){var n=arguments,o=e?e.apply(this,n):n[0],i=r.cache;if(i.has(o))return i.get(o);var a=t.apply(this,n);return r.cache=i.set(o,a)||i,a};return r.cache=new(o.Cache||n),r}o.Cache=n,t.exports=o},5816:t=>{t.exports=function(t,e,r,n){for(var o=t.length,i=r+(n?1:-1);n?i--:++i<o;)if(e(t[i],i,t))return i;return-1}},5866:(t,e,r)=>{var n=r(8468);t.exports=function(t,e){return!!(null==t?0:t.length)&&n(t,e,0)>-1}},5893:(t,e,r)=>{var n=r(6599);t.exports=function(t,e,r){for(var o=-1,i=t.criteria,a=e.criteria,u=i.length,c=r.length;++o<u;){var l=n(i[o],a[o]);if(l)return o>=c?l:l*("desc"==r[o]?-1:1)}return t.index-e.index}},5906:t=>{t.exports=function(t){return function(e,r,n){for(var o=-1,i=Object(e),a=n(e),u=a.length;u--;){var c=a[t?u:++o];if(!1===r(i[c],c,i))break}return e}}},5967:t=>{t.exports=function(t){return t.split("")}},5990:(t,e,r)=>{var n=r(3028)(Object.getPrototypeOf,Object);t.exports=n},6015:(t,e,r)=>{"use strict";r.d(e,{m:()=>n});var n={isSsr:!("undefined"!==typeof window&&window.document&&window.document.createElement&&window.setTimeout),get:function(t){return n[t]},set:function(t,e){if("string"===typeof t)n[t]=e;else{var r=Object.keys(t);r&&r.length&&r.forEach(function(e){n[e]=t[e]})}}}},6026:(t,e,r)=>{"use strict";r.d(e,{h:()=>b});var n=r(5043),o=r(8387),i=r(2103),a=r(7671),u=r(202);function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function l(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,y(n.key),n)}}function s(t,e,r){return e=p(e),function(t,e){if(e&&("object"===c(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,f()?Reflect.construct(e,r||[],p(t).constructor):e.apply(t,r))}function f(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(f=function(){return!!t})()}function p(t){return p=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},p(t)}function h(t,e){return h=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},h(t,e)}function d(t,e,r){return(e=y(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function y(t){var e=function(t,e){if("object"!=c(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=c(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==c(e)?e:e+""}function v(){return v=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},v.apply(this,arguments)}var m=function(t){var e=t.yAxisId,r=(0,i.yi)(),c=(0,i.rY)(),l=(0,i.Nk)(e);return null==l?null:n.createElement(a.u,v({},l,{className:(0,o.A)("recharts-".concat(l.axisType," ").concat(l.axisType),l.className),viewBox:{x:0,y:0,width:r,height:c},ticksGenerator:function(t){return(0,u.Rh)(t,!0)}}))},b=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),s(this,e,arguments)}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&h(t,e)}(e,t),r=e,(o=[{key:"render",value:function(){return n.createElement(m,this.props)}}])&&l(r.prototype,o),i&&l(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,i}(n.Component);d(b,"displayName","YAxis"),d(b,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1})},6095:t=>{var e=Object.prototype.toString;t.exports=function(t){return e.call(t)}},6140:t=>{var e=Object.prototype;t.exports=function(t){var r=t&&t.constructor;return t===("function"==typeof r&&r.prototype||e)}},6150:(t,e,r)=>{"use strict";r.d(e,{m:()=>K});var n=r(5043),o=r(7424),i=r.n(o),a=r(9686),u=r.n(a),c=r(8387),l=r(6307);function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}function f(){return f=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},f.apply(this,arguments)}function p(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return h(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return h(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function d(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function y(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?d(Object(r),!0).forEach(function(e){v(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function v(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=s(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function m(t){return Array.isArray(t)&&(0,l.vh)(t[0])&&(0,l.vh)(t[1])?t.join(" ~ "):t}var b=function(t){var e=t.separator,r=void 0===e?" : ":e,o=t.contentStyle,a=void 0===o?{}:o,s=t.itemStyle,h=void 0===s?{}:s,d=t.labelStyle,v=void 0===d?{}:d,b=t.payload,g=t.formatter,x=t.itemSorter,w=t.wrapperClassName,O=t.labelClassName,j=t.label,S=t.labelFormatter,A=t.accessibilityLayer,P=void 0!==A&&A,E=y({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},a),k=y({margin:0},v),M=!u()(j),_=M?j:"",T=(0,c.A)("recharts-default-tooltip",w),C=(0,c.A)("recharts-tooltip-label",O);M&&S&&void 0!==b&&null!==b&&(_=S(j,b));var I=P?{role:"status","aria-live":"assertive"}:{};return n.createElement("div",f({className:T,style:E},I),n.createElement("p",{className:C,style:k},n.isValidElement(_)?_:"".concat(_)),function(){if(b&&b.length){var t=(x?i()(b,x):b).map(function(t,e){if("none"===t.type)return null;var o=y({display:"block",paddingTop:4,paddingBottom:4,color:t.color||"#000"},h),i=t.formatter||g||m,a=t.value,u=t.name,c=a,s=u;if(i&&null!=c&&null!=s){var f=i(a,u,t,e,b);if(Array.isArray(f)){var d=p(f,2);c=d[0],s=d[1]}else c=f}return n.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(e),style:o},(0,l.vh)(s)?n.createElement("span",{className:"recharts-tooltip-item-name"},s):null,(0,l.vh)(s)?n.createElement("span",{className:"recharts-tooltip-item-separator"},r):null,n.createElement("span",{className:"recharts-tooltip-item-value"},c),n.createElement("span",{className:"recharts-tooltip-item-unit"},t.unit||""))});return n.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},t)}return null}())};function g(t){return g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},g(t)}function x(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=g(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=g(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==g(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var w="recharts-tooltip-wrapper",O={visibility:"hidden"};function j(t){var e=t.coordinate,r=t.translateX,n=t.translateY;return(0,c.A)(w,x(x(x(x({},"".concat(w,"-right"),(0,l.Et)(r)&&e&&(0,l.Et)(e.x)&&r>=e.x),"".concat(w,"-left"),(0,l.Et)(r)&&e&&(0,l.Et)(e.x)&&r<e.x),"".concat(w,"-bottom"),(0,l.Et)(n)&&e&&(0,l.Et)(e.y)&&n>=e.y),"".concat(w,"-top"),(0,l.Et)(n)&&e&&(0,l.Et)(e.y)&&n<e.y))}function S(t){var e=t.allowEscapeViewBox,r=t.coordinate,n=t.key,o=t.offsetTopLeft,i=t.position,a=t.reverseDirection,u=t.tooltipDimension,c=t.viewBox,s=t.viewBoxDimension;if(i&&(0,l.Et)(i[n]))return i[n];var f=r[n]-u-o,p=r[n]+o;return e[n]?a[n]?f:p:a[n]?f<c[n]?Math.max(p,c[n]):Math.max(f,c[n]):p+u>c[n]+s?Math.max(f,c[n]):Math.max(p,c[n])}function A(t){return A="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},A(t)}function P(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function E(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?P(Object(r),!0).forEach(function(e){I(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):P(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function k(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,D(n.key),n)}}function M(t,e,r){return e=T(e),function(t,e){if(e&&("object"===A(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,_()?Reflect.construct(e,r||[],T(t).constructor):e.apply(t,r))}function _(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(_=function(){return!!t})()}function T(t){return T=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},T(t)}function C(t,e){return C=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},C(t,e)}function I(t,e,r){return(e=D(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function D(t){var e=function(t,e){if("object"!=A(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=A(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==A(e)?e:e+""}var N=function(t){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return I(t=M(this,e,[].concat(n)),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),I(t,"handleKeyDown",function(e){var r,n,o,i;"Escape"===e.key&&t.setState({dismissed:!0,dismissedAtCoordinate:{x:null!==(r=null===(n=t.props.coordinate)||void 0===n?void 0:n.x)&&void 0!==r?r:0,y:null!==(o=null===(i=t.props.coordinate)||void 0===i?void 0:i.y)&&void 0!==o?o:0}})}),t}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&C(t,e)}(e,t),r=e,(o=[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();(Math.abs(t.width-this.state.lastBoundingBox.width)>1||Math.abs(t.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:t.width,height:t.height}})}else-1===this.state.lastBoundingBox.width&&-1===this.state.lastBoundingBox.height||this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var t,e;this.props.active&&this.updateBBox(),this.state.dismissed&&((null===(t=this.props.coordinate)||void 0===t?void 0:t.x)===this.state.dismissedAtCoordinate.x&&(null===(e=this.props.coordinate)||void 0===e?void 0:e.y)===this.state.dismissedAtCoordinate.y||(this.state.dismissed=!1))}},{key:"render",value:function(){var t=this,e=this.props,r=e.active,o=e.allowEscapeViewBox,i=e.animationDuration,a=e.animationEasing,u=e.children,c=e.coordinate,l=e.hasPayload,s=e.isAnimationActive,f=e.offset,p=e.position,h=e.reverseDirection,d=e.useTranslate3d,y=e.viewBox,v=e.wrapperStyle,m=function(t){var e,r,n=t.allowEscapeViewBox,o=t.coordinate,i=t.offsetTopLeft,a=t.position,u=t.reverseDirection,c=t.tooltipBox,l=t.useTranslate3d,s=t.viewBox;return{cssProperties:c.height>0&&c.width>0&&o?function(t){var e=t.translateX,r=t.translateY;return{transform:t.useTranslate3d?"translate3d(".concat(e,"px, ").concat(r,"px, 0)"):"translate(".concat(e,"px, ").concat(r,"px)")}}({translateX:e=S({allowEscapeViewBox:n,coordinate:o,key:"x",offsetTopLeft:i,position:a,reverseDirection:u,tooltipDimension:c.width,viewBox:s,viewBoxDimension:s.width}),translateY:r=S({allowEscapeViewBox:n,coordinate:o,key:"y",offsetTopLeft:i,position:a,reverseDirection:u,tooltipDimension:c.height,viewBox:s,viewBoxDimension:s.height}),useTranslate3d:l}):O,cssClasses:j({translateX:e,translateY:r,coordinate:o})}}({allowEscapeViewBox:o,coordinate:c,offsetTopLeft:f,position:p,reverseDirection:h,tooltipBox:this.state.lastBoundingBox,useTranslate3d:d,viewBox:y}),b=m.cssClasses,g=m.cssProperties,x=E(E({transition:s&&r?"transform ".concat(i,"ms ").concat(a):void 0},g),{},{pointerEvents:"none",visibility:!this.state.dismissed&&r&&l?"visible":"hidden",position:"absolute",top:0,left:0},v);return n.createElement("div",{tabIndex:-1,className:b,style:x,ref:function(e){t.wrapperNode=e}},u)}}])&&k(r.prototype,o),i&&k(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,i}(n.PureComponent),B=r(6015),R=r(2598);function L(t){return L="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},L(t)}function z(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function U(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?z(Object(r),!0).forEach(function(e){H(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):z(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function F(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,G(n.key),n)}}function $(t,e,r){return e=q(e),function(t,e){if(e&&("object"===L(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,W()?Reflect.construct(e,r||[],q(t).constructor):e.apply(t,r))}function W(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(W=function(){return!!t})()}function q(t){return q=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},q(t)}function X(t,e){return X=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},X(t,e)}function H(t,e,r){return(e=G(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function G(t){var e=function(t,e){if("object"!=L(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=L(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==L(e)?e:e+""}function V(t){return t.dataKey}var K=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),$(this,e,arguments)}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&X(t,e)}(e,t),r=e,(o=[{key:"render",value:function(){var t=this,e=this.props,r=e.active,o=e.allowEscapeViewBox,i=e.animationDuration,a=e.animationEasing,u=e.content,c=e.coordinate,l=e.filterNull,s=e.isAnimationActive,f=e.offset,p=e.payload,h=e.payloadUniqBy,d=e.position,y=e.reverseDirection,v=e.useTranslate3d,m=e.viewBox,g=e.wrapperStyle,x=null!==p&&void 0!==p?p:[];l&&x.length&&(x=(0,R.s)(p.filter(function(e){return null!=e.value&&(!0!==e.hide||t.props.includeHidden)}),h,V));var w=x.length>0;return n.createElement(N,{allowEscapeViewBox:o,animationDuration:i,animationEasing:a,isAnimationActive:s,active:r,coordinate:c,hasPayload:w,offset:f,position:d,reverseDirection:y,useTranslate3d:v,viewBox:m,wrapperStyle:g},function(t,e){return n.isValidElement(t)?n.cloneElement(t,e):"function"===typeof t?n.createElement(t,e):n.createElement(b,e)}(u,U(U({},this.props),{},{payload:x})))}}])&&F(r.prototype,o),i&&F(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,i}(n.PureComponent);H(K,"displayName","Tooltip"),H(K,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!B.m.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}})},6173:t=>{t.exports=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=9007199254740991}},6179:t=>{t.exports=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this}},6307:(t,e,r)=>{"use strict";r.d(e,{CG:()=>O,Dj:()=>j,Et:()=>y,F4:()=>x,NF:()=>g,_3:()=>d,ck:()=>A,eP:()=>S,lX:()=>w,sA:()=>h,uy:()=>v,vh:()=>m});var n=r(620),o=r.n(n),i=r(5268),a=r.n(i),u=r(3097),c=r.n(u),l=r(9160),s=r.n(l),f=r(9686),p=r.n(f),h=function(t){return 0===t?0:t>0?1:-1},d=function(t){return o()(t)&&t.indexOf("%")===t.length-1},y=function(t){return s()(t)&&!a()(t)},v=function(t){return p()(t)},m=function(t){return y(t)||o()(t)},b=0,g=function(t){var e=++b;return"".concat(t||"").concat(e)},x=function(t,e){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!y(t)&&!o()(t))return n;if(d(t)){var u=t.indexOf("%");r=e*parseFloat(t.slice(0,u))/100}else r=+t;return a()(r)&&(r=n),i&&r>e&&(r=e),r},w=function(t){if(!t)return null;var e=Object.keys(t);return e&&e.length?t[e[0]]:null},O=function(t){if(!Array.isArray(t))return!1;for(var e=t.length,r={},n=0;n<e;n++){if(r[t[n]])return!0;r[t[n]]=!0}return!1},j=function(t,e){return y(t)&&y(e)?function(r){return t+r*(e-t)}:function(){return e}};function S(t,e,r){return t&&t.length?t.find(function(t){return t&&("function"===typeof e?e(t):c()(t,e))===r}):null}var A=function(t,e){return y(t)&&y(e)?t-e:o()(t)&&o()(e)?t.localeCompare(e):t instanceof Date&&e instanceof Date?t.getTime()-e.getTime():String(t).localeCompare(String(e))}},6311:t=>{var e=RegExp("[\\u200d\\ud800-\\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");t.exports=function(t){return e.test(t)}},6350:(t,e,r)=>{var n=r(8325),o=r(6578)(n);t.exports=o},6361:(t,e,r)=>{var n=r(6913),o=r(2761);t.exports=function(t){return!0===t||!1===t||o(t)&&"[object Boolean]"==n(t)}},6378:(t,e)=>{"use strict";var r,n=Symbol.for("react.element"),o=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),c=Symbol.for("react.provider"),l=Symbol.for("react.context"),s=Symbol.for("react.server_context"),f=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),h=Symbol.for("react.suspense_list"),d=Symbol.for("react.memo"),y=Symbol.for("react.lazy"),v=Symbol.for("react.offscreen");function m(t){if("object"===typeof t&&null!==t){var e=t.$$typeof;switch(e){case n:switch(t=t.type){case i:case u:case a:case p:case h:return t;default:switch(t=t&&t.$$typeof){case s:case l:case f:case y:case d:case c:return t;default:return e}}case o:return e}}}r=Symbol.for("react.module.reference"),e.isFragment=function(t){return m(t)===i}},6399:(t,e,r)=>{var n=r(5538),o=r(3668),i=r(9987),a=r(5752),u=r(6924),c=r(4052),l=r(4543),s=r(1268),f="[object Arguments]",p="[object Array]",h="[object Object]",d=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,y,v,m){var b=c(t),g=c(e),x=b?p:u(t),w=g?p:u(e),O=(x=x==f?h:x)==h,j=(w=w==f?h:w)==h,S=x==w;if(S&&l(t)){if(!l(e))return!1;b=!0,O=!1}if(S&&!O)return m||(m=new n),b||s(t)?o(t,e,r,y,v,m):i(t,e,x,r,y,v,m);if(!(1&r)){var A=O&&d.call(t,"__wrapped__"),P=j&&d.call(e,"__wrapped__");if(A||P){var E=A?t.value():t,k=P?e.value():e;return m||(m=new n),v(E,k,r,y,m)}}return!!S&&(m||(m=new n),a(t,e,r,y,v,m))}},6516:(t,e,r)=>{var n=r(6571);t.exports=function(t,e){return function(r,o){if(null==r)return r;if(!n(r))return t(r,o);for(var i=r.length,a=e?i:-1,u=Object(r);(e?a--:++a<i)&&!1!==o(u[a],a,u););return r}}},6532:(t,e,r)=>{var n=r(5538),o=r(6989);t.exports=function(t,e,r,i){var a=r.length,u=a,c=!i;if(null==t)return!u;for(t=Object(t);a--;){var l=r[a];if(c&&l[2]?l[1]!==t[l[0]]:!(l[0]in t))return!1}for(;++a<u;){var s=(l=r[a])[0],f=t[s],p=l[1];if(c&&l[2]){if(void 0===f&&!(s in t))return!1}else{var h=new n;if(i)var d=i(f,p,s,t,e,h);if(!(void 0===d?o(p,f,3,i,h):d))return!1}}return!0}},6552:(t,e,r)=>{var n=r(7105),o="object"==typeof self&&self&&self.Object===Object&&self,i=n||o||Function("return this")();t.exports=i},6571:(t,e,r)=>{var n=r(1629),o=r(6173);t.exports=function(t){return null!=t&&o(t.length)&&!n(t)}},6578:t=>{var e=Date.now;t.exports=function(t){var r=0,n=0;return function(){var o=e(),i=16-(o-n);if(n=o,i>0){if(++r>=800)return arguments[0]}else r=0;return t.apply(void 0,arguments)}}},6599:(t,e,r)=>{var n=r(9841);t.exports=function(t,e){if(t!==e){var r=void 0!==t,o=null===t,i=t===t,a=n(t),u=void 0!==e,c=null===e,l=e===e,s=n(e);if(!c&&!s&&!a&&t>e||a&&u&&l&&!c&&!s||o&&u&&l||!r&&l||!i)return 1;if(!o&&!a&&!s&&t<e||s&&r&&i&&!o&&!a||c&&r&&i||!u&&i||!l)return-1}return 0}},6600:(t,e,r)=>{var n=r(7937)(r(6552),"WeakMap");t.exports=n},6604:(t,e,r)=>{var n=r(3331)();t.exports=n},6686:t=>{t.exports=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}},6704:t=>{t.exports=function(t){return this.__data__.has(t)}},6711:(t,e,r)=>{"use strict";r.d(e,{RG:()=>w,bL:()=>_,q7:()=>T});var n=r(5043),o=r(858),i=r(5463),a=r(2814),u=r(1862),c=r(4490),l=r(7920),s=r(7490),f=r(3642),p=r(4204),h=r(579),d="rovingFocusGroup.onEntryFocus",y={bubbles:!1,cancelable:!0},v="RovingFocusGroup",[m,b,g]=(0,i.N)(v),[x,w]=(0,u.A)(v,[g]),[O,j]=x(v),S=n.forwardRef((t,e)=>(0,h.jsx)(m.Provider,{scope:t.__scopeRovingFocusGroup,children:(0,h.jsx)(m.Slot,{scope:t.__scopeRovingFocusGroup,children:(0,h.jsx)(A,{...t,ref:e})})}));S.displayName=v;var A=n.forwardRef((t,e)=>{const{__scopeRovingFocusGroup:r,orientation:i,loop:u=!1,dir:c,currentTabStopId:m,defaultCurrentTabStopId:g,onCurrentTabStopIdChange:x,onEntryFocus:w,preventScrollOnEntryFocus:j=!1,...S}=t,A=n.useRef(null),P=(0,a.s)(e,A),E=(0,p.jH)(c),[k,_]=(0,f.i)({prop:m,defaultProp:g??null,onChange:x,caller:v}),[T,C]=n.useState(!1),I=(0,s.c)(w),D=b(r),N=n.useRef(!1),[B,R]=n.useState(0);return n.useEffect(()=>{const t=A.current;if(t)return t.addEventListener(d,I),()=>t.removeEventListener(d,I)},[I]),(0,h.jsx)(O,{scope:r,orientation:i,dir:E,loop:u,currentTabStopId:k,onItemFocus:n.useCallback(t=>_(t),[_]),onItemShiftTab:n.useCallback(()=>C(!0),[]),onFocusableItemAdd:n.useCallback(()=>R(t=>t+1),[]),onFocusableItemRemove:n.useCallback(()=>R(t=>t-1),[]),children:(0,h.jsx)(l.sG.div,{tabIndex:T||0===B?-1:0,"data-orientation":i,...S,ref:P,style:{outline:"none",...t.style},onMouseDown:(0,o.m)(t.onMouseDown,()=>{N.current=!0}),onFocus:(0,o.m)(t.onFocus,t=>{const e=!N.current;if(t.target===t.currentTarget&&e&&!T){const e=new CustomEvent(d,y);if(t.currentTarget.dispatchEvent(e),!e.defaultPrevented){const t=D().filter(t=>t.focusable);M([t.find(t=>t.active),t.find(t=>t.id===k),...t].filter(Boolean).map(t=>t.ref.current),j)}}N.current=!1}),onBlur:(0,o.m)(t.onBlur,()=>C(!1))})})}),P="RovingFocusGroupItem",E=n.forwardRef((t,e)=>{const{__scopeRovingFocusGroup:r,focusable:i=!0,active:a=!1,tabStopId:u,children:s,...f}=t,p=(0,c.B)(),d=u||p,y=j(P,r),v=y.currentTabStopId===d,g=b(r),{onFocusableItemAdd:x,onFocusableItemRemove:w,currentTabStopId:O}=y;return n.useEffect(()=>{if(i)return x(),()=>w()},[i,x,w]),(0,h.jsx)(m.ItemSlot,{scope:r,id:d,focusable:i,active:a,children:(0,h.jsx)(l.sG.span,{tabIndex:v?0:-1,"data-orientation":y.orientation,...f,ref:e,onMouseDown:(0,o.m)(t.onMouseDown,t=>{i?y.onItemFocus(d):t.preventDefault()}),onFocus:(0,o.m)(t.onFocus,()=>y.onItemFocus(d)),onKeyDown:(0,o.m)(t.onKeyDown,t=>{if("Tab"===t.key&&t.shiftKey)return void y.onItemShiftTab();if(t.target!==t.currentTarget)return;const e=function(t,e,r){const n=function(t,e){return"rtl"!==e?t:"ArrowLeft"===t?"ArrowRight":"ArrowRight"===t?"ArrowLeft":t}(t.key,r);return"vertical"===e&&["ArrowLeft","ArrowRight"].includes(n)||"horizontal"===e&&["ArrowUp","ArrowDown"].includes(n)?void 0:k[n]}(t,y.orientation,y.dir);if(void 0!==e){if(t.metaKey||t.ctrlKey||t.altKey||t.shiftKey)return;t.preventDefault();let o=g().filter(t=>t.focusable).map(t=>t.ref.current);if("last"===e)o.reverse();else if("prev"===e||"next"===e){"prev"===e&&o.reverse();const i=o.indexOf(t.currentTarget);o=y.loop?(n=i+1,(r=o).map((t,e)=>r[(n+e)%r.length])):o.slice(i+1)}setTimeout(()=>M(o))}var r,n}),children:"function"===typeof s?s({isCurrentTabStop:v,hasTabStop:null!=O}):s})})});E.displayName=P;var k={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function M(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const r=document.activeElement;for(const n of t){if(n===r)return;if(n.focus({preventScroll:e}),document.activeElement!==r)return}}var _=S,T=E},6745:(t,e,r)=>{var n=r(9742),o=r(61),i=r(3279);t.exports=function(t){return t&&t.length?n(t,i,o):void 0}},6788:(t,e,r)=>{var n=r(7160),o=r(5204),i=r(4816);t.exports=function(t,e){var r=this.__data__;if(r instanceof n){var a=r.__data__;if(!o||a.length<199)return a.push([t,e]),this.size=++r.size,this;r=this.__data__=new i(a)}return r.set(t,e),this.size=r.size,this}},6832:(t,e,r)=>{t=r.nmd(t);var n=r(7105),o=e&&!e.nodeType&&e,i=o&&t&&!t.nodeType&&t,a=i&&i.exports===o&&n.process,u=function(){try{var t=i&&i.require&&i.require("util").types;return t||a&&a.binding&&a.binding("util")}catch(e){}}();t.exports=u},6874:(t,e,r)=>{var n=r(2622);t.exports=function(t,e){var r=n(this,t),o=r.size;return r.set(t,e),this.size+=r.size==o?0:1,this}},6913:(t,e,r)=>{var n=r(9812),o=r(4552),i=r(6095),a=n?n.toStringTag:void 0;t.exports=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":a&&a in Object(t)?o(t):i(t)}},6924:(t,e,r)=>{var n=r(7685),o=r(5204),i=r(5387),a=r(2070),u=r(6600),c=r(6913),l=r(6996),s="[object Map]",f="[object Promise]",p="[object Set]",h="[object WeakMap]",d="[object DataView]",y=l(n),v=l(o),m=l(i),b=l(a),g=l(u),x=c;(n&&x(new n(new ArrayBuffer(1)))!=d||o&&x(new o)!=s||i&&x(i.resolve())!=f||a&&x(new a)!=p||u&&x(new u)!=h)&&(x=function(t){var e=c(t),r="[object Object]"==e?t.constructor:void 0,n=r?l(r):"";if(n)switch(n){case y:return d;case v:return s;case m:return f;case b:return p;case g:return h}return e}),t.exports=x},6954:(t,e,r)=>{var n=r(1629),o=r(7857),i=r(6686),a=r(6996),u=/^\[object .+?Constructor\]$/,c=Function.prototype,l=Object.prototype,s=c.toString,f=l.hasOwnProperty,p=RegExp("^"+s.call(f).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");t.exports=function(t){return!(!i(t)||o(t))&&(n(t)?p:u).test(a(t))}},6989:(t,e,r)=>{var n=r(6399),o=r(2761);t.exports=function t(e,r,i,a,u){return e===r||(null==e||null==r||!o(e)&&!o(r)?e!==e&&r!==r:n(e,r,i,a,t,u))}},6996:t=>{var e=Function.prototype.toString;t.exports=function(t){if(null!=t){try{return e.call(t)}catch(r){}try{return t+""}catch(r){}}return""}},7002:(t,e,r)=>{var n=r(5295),o=r(4746),i=r(9096),a=r(4052),u=r(929);t.exports=function(t,e,r){var c=a(t)?n:o;return r&&u(t,e,r)&&(e=void 0),c(t,i(e,3))}},7105:(t,e,r)=>{var n="object"==typeof r.g&&r.g&&r.g.Object===Object&&r.g;t.exports=n},7116:(t,e,r)=>{var n=r(9812),o=r(2777),i=r(4052),a=n?n.isConcatSpreadable:void 0;t.exports=function(t){return i(t)||o(t)||!!(a&&t&&t[a])}},7127:(t,e,r)=>{"use strict";r.d(e,{B8:()=>M,UC:()=>T,bL:()=>k,l9:()=>_});var n=r(5043),o=r(858),i=r(1862),a=r(6711),u=r(2894),c=r(7920),l=r(4204),s=r(3642),f=r(4490),p=r(579),h="Tabs",[d,y]=(0,i.A)(h,[a.RG]),v=(0,a.RG)(),[m,b]=d(h),g=n.forwardRef((t,e)=>{const{__scopeTabs:r,value:n,onValueChange:o,defaultValue:i,orientation:a="horizontal",dir:u,activationMode:d="automatic",...y}=t,v=(0,l.jH)(u),[b,g]=(0,s.i)({prop:n,onChange:o,defaultProp:i??"",caller:h});return(0,p.jsx)(m,{scope:r,baseId:(0,f.B)(),value:b,onValueChange:g,orientation:a,dir:v,activationMode:d,children:(0,p.jsx)(c.sG.div,{dir:v,"data-orientation":a,...y,ref:e})})});g.displayName=h;var x="TabsList",w=n.forwardRef((t,e)=>{const{__scopeTabs:r,loop:n=!0,...o}=t,i=b(x,r),u=v(r);return(0,p.jsx)(a.bL,{asChild:!0,...u,orientation:i.orientation,dir:i.dir,loop:n,children:(0,p.jsx)(c.sG.div,{role:"tablist","aria-orientation":i.orientation,...o,ref:e})})});w.displayName=x;var O="TabsTrigger",j=n.forwardRef((t,e)=>{const{__scopeTabs:r,value:n,disabled:i=!1,...u}=t,l=b(O,r),s=v(r),f=P(l.baseId,n),h=E(l.baseId,n),d=n===l.value;return(0,p.jsx)(a.q7,{asChild:!0,...s,focusable:!i,active:d,children:(0,p.jsx)(c.sG.button,{type:"button",role:"tab","aria-selected":d,"aria-controls":h,"data-state":d?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:f,...u,ref:e,onMouseDown:(0,o.m)(t.onMouseDown,t=>{i||0!==t.button||!1!==t.ctrlKey?t.preventDefault():l.onValueChange(n)}),onKeyDown:(0,o.m)(t.onKeyDown,t=>{[" ","Enter"].includes(t.key)&&l.onValueChange(n)}),onFocus:(0,o.m)(t.onFocus,()=>{const t="manual"!==l.activationMode;d||i||!t||l.onValueChange(n)})})})});j.displayName=O;var S="TabsContent",A=n.forwardRef((t,e)=>{const{__scopeTabs:r,value:o,forceMount:i,children:a,...l}=t,s=b(S,r),f=P(s.baseId,o),h=E(s.baseId,o),d=o===s.value,y=n.useRef(d);return n.useEffect(()=>{const t=requestAnimationFrame(()=>y.current=!1);return()=>cancelAnimationFrame(t)},[]),(0,p.jsx)(u.C,{present:i||d,children:r=>{let{present:n}=r;return(0,p.jsx)(c.sG.div,{"data-state":d?"active":"inactive","data-orientation":s.orientation,role:"tabpanel","aria-labelledby":f,hidden:!n,id:h,tabIndex:0,...l,ref:e,style:{...t.style,animationDuration:y.current?"0s":void 0},children:n&&a})}})});function P(t,e){return`${t}-trigger-${e}`}function E(t,e){return`${t}-content-${e}`}A.displayName=S;var k=g,M=w,_=j,T=A},7159:(t,e,r)=>{var n=r(2622);t.exports=function(t){var e=n(this,t).delete(t);return this.size-=e?1:0,e}},7160:(t,e,r)=>{var n=r(7563),o=r(9935),i=r(4190),a=r(1946),u=r(1714);function c(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}c.prototype.clear=n,c.prototype.delete=o,c.prototype.get=i,c.prototype.has=a,c.prototype.set=u,t.exports=c},7165:(t,e,r)=>{"use strict";r.d(e,{g:()=>s});var n=r(1327),o=r(202),i=r(240);function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}function u(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function c(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?u(Object(r),!0).forEach(function(e){l(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function l(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=a(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=a(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==a(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var s=function(t){var e=t.children,r=t.formattedGraphicalItems,a=t.legendWidth,u=t.legendContent,l=(0,i.BU)(e,n.s);if(!l)return null;var s,f=n.s.defaultProps,p=void 0!==f?c(c({},f),l.props):{};return s=l.props&&l.props.payload?l.props&&l.props.payload:"children"===u?(r||[]).reduce(function(t,e){var r=e.item,n=e.props,o=n.sectors||n.data||[];return t.concat(o.map(function(t){return{type:l.props.iconType||r.props.legendType,value:t.name,color:t.fill,payload:t}}))},[]):(r||[]).map(function(t){var e=t.item,r=e.type.defaultProps,n=void 0!==r?c(c({},r),e.props):{},i=n.dataKey,a=n.name,u=n.legendType;return{inactive:n.hide,dataKey:i,type:p.iconType||u||"square",color:(0,o.Ps)(e),value:a||i,payload:n}}),c(c(c({},p),n.s.getWithHeight(l,a)),{},{payload:s,item:l})}},7213:(t,e,r)=>{"use strict";r.d(e,{A3:()=>p,Pu:()=>f});var n=r(6015);function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}function i(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function a(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?i(Object(r),!0).forEach(function(e){u(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function u(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=o(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==o(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var c={widthCache:{},cacheCount:0},l={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},s="recharts_measurement_span";var f=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(void 0===t||null===t||n.m.isSsr)return{width:0,height:0};var r=function(t){var e=a({},t);return Object.keys(e).forEach(function(t){e[t]||delete e[t]}),e}(e),o=JSON.stringify({text:t,copyStyle:r});if(c.widthCache[o])return c.widthCache[o];try{var i=document.getElementById(s);i||((i=document.createElement("span")).setAttribute("id",s),i.setAttribute("aria-hidden","true"),document.body.appendChild(i));var u=a(a({},l),r);Object.assign(i.style,u),i.textContent="".concat(t);var f=i.getBoundingClientRect(),p={width:f.width,height:f.height};return c.widthCache[o]=p,++c.cacheCount>2e3&&(c.cacheCount=0,c.widthCache={}),p}catch(h){return{width:0,height:0}}},p=function(t){return{top:t.top+window.scrollY-document.documentElement.clientTop,left:t.left+window.scrollX-document.documentElement.clientLeft}}},7251:(t,e,r)=>{var n=r(8724),o=r(7160),i=r(5204);t.exports=function(){this.size=0,this.__data__={hash:new n,map:new(i||o),string:new n}}},7283:t=>{"use strict";var e=Object.prototype.hasOwnProperty,r="~";function n(){}function o(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function i(t,e,n,i,a){if("function"!==typeof n)throw new TypeError("The listener must be a function");var u=new o(n,i||t,a),c=r?r+e:e;return t._events[c]?t._events[c].fn?t._events[c]=[t._events[c],u]:t._events[c].push(u):(t._events[c]=u,t._eventsCount++),t}function a(t,e){0===--t._eventsCount?t._events=new n:delete t._events[e]}function u(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),(new n).__proto__||(r=!1)),u.prototype.eventNames=function(){var t,n,o=[];if(0===this._eventsCount)return o;for(n in t=this._events)e.call(t,n)&&o.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(t)):o},u.prototype.listeners=function(t){var e=r?r+t:t,n=this._events[e];if(!n)return[];if(n.fn)return[n.fn];for(var o=0,i=n.length,a=new Array(i);o<i;o++)a[o]=n[o].fn;return a},u.prototype.listenerCount=function(t){var e=r?r+t:t,n=this._events[e];return n?n.fn?1:n.length:0},u.prototype.emit=function(t,e,n,o,i,a){var u=r?r+t:t;if(!this._events[u])return!1;var c,l,s=this._events[u],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(t,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,e),!0;case 3:return s.fn.call(s.context,e,n),!0;case 4:return s.fn.call(s.context,e,n,o),!0;case 5:return s.fn.call(s.context,e,n,o,i),!0;case 6:return s.fn.call(s.context,e,n,o,i,a),!0}for(l=1,c=new Array(f-1);l<f;l++)c[l-1]=arguments[l];s.fn.apply(s.context,c)}else{var p,h=s.length;for(l=0;l<h;l++)switch(s[l].once&&this.removeListener(t,s[l].fn,void 0,!0),f){case 1:s[l].fn.call(s[l].context);break;case 2:s[l].fn.call(s[l].context,e);break;case 3:s[l].fn.call(s[l].context,e,n);break;case 4:s[l].fn.call(s[l].context,e,n,o);break;default:if(!c)for(p=1,c=new Array(f-1);p<f;p++)c[p-1]=arguments[p];s[l].fn.apply(s[l].context,c)}}return!0},u.prototype.on=function(t,e,r){return i(this,t,e,r,!1)},u.prototype.once=function(t,e,r){return i(this,t,e,r,!0)},u.prototype.removeListener=function(t,e,n,o){var i=r?r+t:t;if(!this._events[i])return this;if(!e)return a(this,i),this;var u=this._events[i];if(u.fn)u.fn!==e||o&&!u.once||n&&u.context!==n||a(this,i);else{for(var c=0,l=[],s=u.length;c<s;c++)(u[c].fn!==e||o&&!u[c].once||n&&u[c].context!==n)&&l.push(u[c]);l.length?this._events[i]=1===l.length?l[0]:l:a(this,i)}return this},u.prototype.removeAllListeners=function(t){var e;return t?(e=r?r+t:t,this._events[e]&&a(this,e)):(this._events=new n,this._eventsCount=0),this},u.prototype.off=u.prototype.removeListener,u.prototype.addListener=u.prototype.on,u.prefixed=r,u.EventEmitter=u,t.exports=u},7287:(t,e,r)=>{"use strict";r.d(e,{QQ:()=>u,VU:()=>l,XC:()=>p,_U:()=>f,j2:()=>s});var n=r(5043),o=r(6686),i=r.n(o);function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}var u=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],c=["points","pathLength"],l={svg:["viewBox","children"],polygon:c,polyline:c},s=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],f=function(t,e){if(!t||"function"===typeof t||"boolean"===typeof t)return null;var r=t;if((0,n.isValidElement)(t)&&(r=t.props),!i()(r))return null;var o={};return Object.keys(r).forEach(function(t){s.includes(t)&&(o[t]=e||function(e){return r[t](r,e)})}),o},p=function(t,e,r){if(!i()(t)||"object"!==a(t))return null;var n=null;return Object.keys(t).forEach(function(o){var i=t[o];s.includes(o)&&"function"===typeof i&&(n||(n={}),n[o]=function(t,e,r){return function(n){return t(e,r,n),null}}(i,e,r))}),n}},7303:(t,e,r)=>{var n=r(801),o=1/0;t.exports=function(t){return t?(t=n(t))===o||t===-1/0?17976931348623157e292*(t<0?-1:1):t===t?t:0:0===t?t:0}},7371:(t,e,r)=>{"use strict";r.d(e,{i:()=>l});const n=Math.PI,o=2*n,i=1e-6,a=o-i;function u(t){this._+=t[0];for(let e=1,r=t.length;e<r;++e)this._+=arguments[e]+t[e]}class c{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?u:function(t){let e=Math.floor(t);if(!(e>=0))throw new Error(`invalid digits: ${t}`);if(e>15)return u;const r=10**e;return function(t){this._+=t[0];for(let e=1,n=t.length;e<n;++e)this._+=Math.round(arguments[e]*r)/r+t[e]}}(t)}moveTo(t,e){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,e){this._append`L${this._x1=+t},${this._y1=+e}`}quadraticCurveTo(t,e,r,n){this._append`Q${+t},${+e},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(t,e,r,n,o,i){this._append`C${+t},${+e},${+r},${+n},${this._x1=+o},${this._y1=+i}`}arcTo(t,e,r,o,a){if(t=+t,e=+e,r=+r,o=+o,(a=+a)<0)throw new Error(`negative radius: ${a}`);let u=this._x1,c=this._y1,l=r-t,s=o-e,f=u-t,p=c-e,h=f*f+p*p;if(null===this._x1)this._append`M${this._x1=t},${this._y1=e}`;else if(h>i)if(Math.abs(p*l-s*f)>i&&a){let d=r-u,y=o-c,v=l*l+s*s,m=d*d+y*y,b=Math.sqrt(v),g=Math.sqrt(h),x=a*Math.tan((n-Math.acos((v+h-m)/(2*b*g)))/2),w=x/g,O=x/b;Math.abs(w-1)>i&&this._append`L${t+w*f},${e+w*p}`,this._append`A${a},${a},0,0,${+(p*d>f*y)},${this._x1=t+O*l},${this._y1=e+O*s}`}else this._append`L${this._x1=t},${this._y1=e}`;else;}arc(t,e,r,u,c,l){if(t=+t,e=+e,l=!!l,(r=+r)<0)throw new Error(`negative radius: ${r}`);let s=r*Math.cos(u),f=r*Math.sin(u),p=t+s,h=e+f,d=1^l,y=l?u-c:c-u;null===this._x1?this._append`M${p},${h}`:(Math.abs(this._x1-p)>i||Math.abs(this._y1-h)>i)&&this._append`L${p},${h}`,r&&(y<0&&(y=y%o+o),y>a?this._append`A${r},${r},0,1,${d},${t-s},${e-f}A${r},${r},0,1,${d},${this._x1=p},${this._y1=h}`:y>i&&this._append`A${r},${r},0,${+(y>=n)},${d},${this._x1=t+r*Math.cos(c)},${this._y1=e+r*Math.sin(c)}`)}rect(t,e,r,n){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}h${r=+r}v${+n}h${-r}Z`}toString(){return this._}}function l(t){let e=3;return t.digits=function(r){if(!arguments.length)return e;if(null==r)e=null;else{const t=Math.floor(r);if(!(t>=0))throw new RangeError(`invalid digits: ${r}`);e=t}return t},()=>new c(e)}c.prototype},7424:(t,e,r)=>{var n=r(755),o=r(2536),i=r(5647),a=r(929),u=i(function(t,e){if(null==t)return[];var r=e.length;return r>1&&a(t,e[0],e[1])?e=[]:r>2&&a(e[0],e[1],e[2])&&(e=[e[0]]),o(t,n(e,1),[])});t.exports=u},7498:t=>{t.exports=function(t,e){return t>e}},7529:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,o=0,i=[];++r<n;){var a=t[r];e(a,r,t)&&(i[o++]=a)}return i}},7563:t=>{t.exports=function(){this.__data__=[],this.size=0}},7574:t=>{t.exports=function(t){return function(e){return t(e)}}},7615:(t,e,r)=>{var n=r(5575);t.exports=function(){this.__data__=n?n(null):{},this.size=0}},7671:(t,e,r)=>{"use strict";r.d(e,{u:()=>C});var n=r(5043),o=r(1629),i=r.n(o),a=r(3097),u=r.n(a),c=r(8387),l=r(5248),s=r(1639),f=r(4140),p=r(2647),h=r(6307),d=r(7287),y=r(240),v=r(8854),m=["viewBox"],b=["viewBox"],g=["ticks"];function x(t){return x="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},x(t)}function w(){return w=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},w.apply(this,arguments)}function O(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function j(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?O(Object(r),!0).forEach(function(e){_(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):O(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function S(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function A(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,T(n.key),n)}}function P(t,e,r){return e=k(e),function(t,e){if(e&&("object"===x(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,E()?Reflect.construct(e,r||[],k(t).constructor):e.apply(t,r))}function E(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(E=function(){return!!t})()}function k(t){return k=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},k(t)}function M(t,e){return M=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},M(t,e)}function _(t,e,r){return(e=T(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function T(t){var e=function(t,e){if("object"!=x(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=x(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==x(e)?e:e+""}var C=function(t){function e(t){var r;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),(r=P(this,e,[t])).state={fontSize:"",letterSpacing:""},r}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&M(t,e)}(e,t),r=e,a=[{key:"renderTickItem",value:function(t,e,r){var o=(0,c.A)(e.className,"recharts-cartesian-axis-tick-value");return n.isValidElement(t)?n.cloneElement(t,j(j({},e),{},{className:o})):i()(t)?t(j(j({},e),{},{className:o})):n.createElement(f.E,w({},e,{className:"recharts-cartesian-axis-tick-value"}),r)}}],(o=[{key:"shouldComponentUpdate",value:function(t,e){var r=t.viewBox,n=S(t,m),o=this.props,i=o.viewBox,a=S(o,b);return!(0,l.b)(r,i)||!(0,l.b)(n,a)||!(0,l.b)(e,this.state)}},{key:"componentDidMount",value:function(){var t=this.layerReference;if(t){var e=t.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];e&&this.setState({fontSize:window.getComputedStyle(e).fontSize,letterSpacing:window.getComputedStyle(e).letterSpacing})}}},{key:"getTickLineCoord",value:function(t){var e,r,n,o,i,a,u=this.props,c=u.x,l=u.y,s=u.width,f=u.height,p=u.orientation,d=u.tickSize,y=u.mirror,v=u.tickMargin,m=y?-1:1,b=t.tickSize||d,g=(0,h.Et)(t.tickCoord)?t.tickCoord:t.coordinate;switch(p){case"top":e=r=t.coordinate,a=(n=(o=l+ +!y*f)-m*b)-m*v,i=g;break;case"left":n=o=t.coordinate,i=(e=(r=c+ +!y*s)-m*b)-m*v,a=g;break;case"right":n=o=t.coordinate,i=(e=(r=c+ +y*s)+m*b)+m*v,a=g;break;default:e=r=t.coordinate,a=(n=(o=l+ +y*f)+m*b)+m*v,i=g}return{line:{x1:e,y1:n,x2:r,y2:o},tick:{x:i,y:a}}}},{key:"getTickTextAnchor",value:function(){var t,e=this.props,r=e.orientation,n=e.mirror;switch(r){case"left":t=n?"start":"end";break;case"right":t=n?"end":"start";break;default:t="middle"}return t}},{key:"getTickVerticalAnchor",value:function(){var t=this.props,e=t.orientation,r=t.mirror,n="end";switch(e){case"left":case"right":n="middle";break;case"top":n=r?"start":"end";break;default:n=r?"end":"start"}return n}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.x,r=t.y,o=t.width,i=t.height,a=t.orientation,l=t.mirror,s=t.axisLine,f=j(j(j({},(0,y.J9)(this.props,!1)),(0,y.J9)(s,!1)),{},{fill:"none"});if("top"===a||"bottom"===a){var p=+("top"===a&&!l||"bottom"===a&&l);f=j(j({},f),{},{x1:e,y1:r+p*i,x2:e+o,y2:r+p*i})}else{var h=+("left"===a&&!l||"right"===a&&l);f=j(j({},f),{},{x1:e+h*o,y1:r,x2:e+h*o,y2:r+i})}return n.createElement("line",w({},f,{className:(0,c.A)("recharts-cartesian-axis-line",u()(s,"className"))}))}},{key:"renderTicks",value:function(t,r,o){var a=this,l=this.props,f=l.tickLine,p=l.stroke,h=l.tick,m=l.tickFormatter,b=l.unit,g=(0,v.f)(j(j({},this.props),{},{ticks:t}),r,o),x=this.getTickTextAnchor(),O=this.getTickVerticalAnchor(),S=(0,y.J9)(this.props,!1),A=(0,y.J9)(h,!1),P=j(j({},S),{},{fill:"none"},(0,y.J9)(f,!1)),E=g.map(function(t,r){var o=a.getTickLineCoord(t),l=o.line,y=o.tick,v=j(j(j(j({textAnchor:x,verticalAnchor:O},S),{},{stroke:"none",fill:p},A),y),{},{index:r,payload:t,visibleTicksCount:g.length,tickFormatter:m});return n.createElement(s.W,w({className:"recharts-cartesian-axis-tick",key:"tick-".concat(t.value,"-").concat(t.coordinate,"-").concat(t.tickCoord)},(0,d.XC)(a.props,t,r)),f&&n.createElement("line",w({},P,l,{className:(0,c.A)("recharts-cartesian-axis-tick-line",u()(f,"className"))})),h&&e.renderTickItem(h,v,"".concat(i()(m)?m(t.value,r):t.value).concat(b||"")))});return n.createElement("g",{className:"recharts-cartesian-axis-ticks"},E)}},{key:"render",value:function(){var t=this,e=this.props,r=e.axisLine,o=e.width,a=e.height,u=e.ticksGenerator,l=e.className;if(e.hide)return null;var f=this.props,h=f.ticks,d=S(f,g),y=h;return i()(u)&&(y=h&&h.length>0?u(this.props):u(d)),o<=0||a<=0||!y||!y.length?null:n.createElement(s.W,{className:(0,c.A)("recharts-cartesian-axis",l),ref:function(e){t.layerReference=e}},r&&this.renderAxisLine(),this.renderTicks(y,this.state.fontSize,this.state.letterSpacing),p.J.renderCallByParent(this.props))}}])&&A(r.prototype,o),a&&A(r,a),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,a}(n.Component);_(C,"displayName","CartesianAxis"),_(C,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"})},7676:(t,e,r)=>{var n=r(8189),o=r(6311),i=r(9115),a=r(1069);t.exports=function(t){return function(e){e=a(e);var r=o(e)?i(e):void 0,u=r?r[0]:e.charAt(0),c=r?n(r,1).join(""):e.slice(1);return u[t]()+c}}},7685:(t,e,r)=>{var n=r(7937)(r(6552),"DataView");t.exports=n},7734:(t,e,r)=>{"use strict";r.d(e,{d:()=>_});var n=r(5043),o=r(1629),i=r.n(o),a=r(155),u=r(6307),c=r(240),l=r(202),s=r(8854),f=r(7671),p=r(2103),h=["x1","y1","x2","y2","key"],d=["offset"];function y(t){return y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},y(t)}function v(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function m(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?v(Object(r),!0).forEach(function(e){b(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):v(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function b(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=y(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=y(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==y(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function g(){return g=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},g.apply(this,arguments)}function x(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var w=function(t){var e=t.fill;if(!e||"none"===e)return null;var r=t.fillOpacity,o=t.x,i=t.y,a=t.width,u=t.height,c=t.ry;return n.createElement("rect",{x:o,y:i,ry:c,width:a,height:u,stroke:"none",fill:e,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function O(t,e){var r;if(n.isValidElement(t))r=n.cloneElement(t,e);else if(i()(t))r=t(e);else{var o=e.x1,a=e.y1,u=e.x2,l=e.y2,s=e.key,f=x(e,h),p=(0,c.J9)(f,!1),y=(p.offset,x(p,d));r=n.createElement("line",g({},y,{x1:o,y1:a,x2:u,y2:l,fill:"none",key:s}))}return r}function j(t){var e=t.x,r=t.width,o=t.horizontal,i=void 0===o||o,a=t.horizontalPoints;if(!i||!a||!a.length)return null;var u=a.map(function(n,o){var a=m(m({},t),{},{x1:e,y1:n,x2:e+r,y2:n,key:"line-".concat(o),index:o});return O(i,a)});return n.createElement("g",{className:"recharts-cartesian-grid-horizontal"},u)}function S(t){var e=t.y,r=t.height,o=t.vertical,i=void 0===o||o,a=t.verticalPoints;if(!i||!a||!a.length)return null;var u=a.map(function(n,o){var a=m(m({},t),{},{x1:n,y1:e,x2:n,y2:e+r,key:"line-".concat(o),index:o});return O(i,a)});return n.createElement("g",{className:"recharts-cartesian-grid-vertical"},u)}function A(t){var e=t.horizontalFill,r=t.fillOpacity,o=t.x,i=t.y,a=t.width,u=t.height,c=t.horizontalPoints,l=t.horizontal;if(!(void 0===l||l)||!e||!e.length)return null;var s=c.map(function(t){return Math.round(t+i-i)}).sort(function(t,e){return t-e});i!==s[0]&&s.unshift(0);var f=s.map(function(t,c){var l=!s[c+1]?i+u-t:s[c+1]-t;if(l<=0)return null;var f=c%e.length;return n.createElement("rect",{key:"react-".concat(c),y:t,x:o,height:l,width:a,stroke:"none",fill:e[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function P(t){var e=t.vertical,r=void 0===e||e,o=t.verticalFill,i=t.fillOpacity,a=t.x,u=t.y,c=t.width,l=t.height,s=t.verticalPoints;if(!r||!o||!o.length)return null;var f=s.map(function(t){return Math.round(t+a-a)}).sort(function(t,e){return t-e});a!==f[0]&&f.unshift(0);var p=f.map(function(t,e){var r=!f[e+1]?a+c-t:f[e+1]-t;if(r<=0)return null;var s=e%o.length;return n.createElement("rect",{key:"react-".concat(e),x:t,y:u,width:r,height:l,stroke:"none",fill:o[s],fillOpacity:i,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},p)}var E=function(t,e){var r=t.xAxis,n=t.width,o=t.height,i=t.offset;return(0,l.PW)((0,s.f)(m(m(m({},f.u.defaultProps),r),{},{ticks:(0,l.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.left,i.left+i.width,e)},k=function(t,e){var r=t.yAxis,n=t.width,o=t.height,i=t.offset;return(0,l.PW)((0,s.f)(m(m(m({},f.u.defaultProps),r),{},{ticks:(0,l.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.top,i.top+i.height,e)},M={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function _(t){var e,r,o,c,l,s,f=(0,p.yi)(),h=(0,p.rY)(),d=(0,p.hj)(),v=m(m({},t),{},{stroke:null!==(e=t.stroke)&&void 0!==e?e:M.stroke,fill:null!==(r=t.fill)&&void 0!==r?r:M.fill,horizontal:null!==(o=t.horizontal)&&void 0!==o?o:M.horizontal,horizontalFill:null!==(c=t.horizontalFill)&&void 0!==c?c:M.horizontalFill,vertical:null!==(l=t.vertical)&&void 0!==l?l:M.vertical,verticalFill:null!==(s=t.verticalFill)&&void 0!==s?s:M.verticalFill,x:(0,u.Et)(t.x)?t.x:d.left,y:(0,u.Et)(t.y)?t.y:d.top,width:(0,u.Et)(t.width)?t.width:d.width,height:(0,u.Et)(t.height)?t.height:d.height}),b=v.x,x=v.y,O=v.width,_=v.height,T=v.syncWithTicks,C=v.horizontalValues,I=v.verticalValues,D=(0,p.pj)(),N=(0,p.$G)();if(!(0,u.Et)(O)||O<=0||!(0,u.Et)(_)||_<=0||!(0,u.Et)(b)||b!==+b||!(0,u.Et)(x)||x!==+x)return null;var B=v.verticalCoordinatesGenerator||E,R=v.horizontalCoordinatesGenerator||k,L=v.horizontalPoints,z=v.verticalPoints;if((!L||!L.length)&&i()(R)){var U=C&&C.length,F=R({yAxis:N?m(m({},N),{},{ticks:U?C:N.ticks}):void 0,width:f,height:h,offset:d},!!U||T);(0,a.R)(Array.isArray(F),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(y(F),"]")),Array.isArray(F)&&(L=F)}if((!z||!z.length)&&i()(B)){var $=I&&I.length,W=B({xAxis:D?m(m({},D),{},{ticks:$?I:D.ticks}):void 0,width:f,height:h,offset:d},!!$||T);(0,a.R)(Array.isArray(W),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(y(W),"]")),Array.isArray(W)&&(z=W)}return n.createElement("g",{className:"recharts-cartesian-grid"},n.createElement(w,{fill:v.fill,fillOpacity:v.fillOpacity,x:v.x,y:v.y,width:v.width,height:v.height,ry:v.ry}),n.createElement(j,g({},v,{offset:d,horizontalPoints:L,xAxis:D,yAxis:N})),n.createElement(S,g({},v,{offset:d,verticalPoints:z,xAxis:D,yAxis:N})),n.createElement(A,g({},v,{horizontalPoints:L})),n.createElement(P,g({},v,{verticalPoints:z})))}_.displayName="CartesianGrid"},7760:t=>{t.exports=function(t){return this.__data__.get(t)}},7828:t=>{t.exports=function(){return[]}},7857:(t,e,r)=>{var n=r(3440),o=function(){var t=/[^.]+$/.exec(n&&n.keys&&n.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}();t.exports=function(t){return!!o&&o in t}},7894:t=>{t.exports=function(t,e){return null!=t&&e in Object(t)}},7937:(t,e,r)=>{var n=r(6954),o=r(4657);t.exports=function(t,e){var r=o(t,e);return n(r)?r:void 0}},8114:t=>{t.exports=function(t,e){return t.has(e)}},8182:(t,e,r)=>{var n=r(2070),o=r(5713),i=r(2074),a=n&&1/i(new n([,-0]))[1]==1/0?function(t){return new n(t)}:o;t.exports=a},8189:(t,e,r)=>{var n=r(3871);t.exports=function(t,e,r){var o=t.length;return r=void 0===r?o:r,!e&&r>=o?t:n(t,e,r)}},8210:function(t,e,r){var n;!function(){"use strict";var o,i=1e9,a={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},u=!0,c="[DecimalError] ",l=c+"Invalid argument: ",s=c+"Exponent out of range: ",f=Math.floor,p=Math.pow,h=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,d=1e7,y=9007199254740991,v=f(1286742750677284.5),m={};function b(t,e){var r,n,o,i,a,c,l,s,f=t.constructor,p=f.precision;if(!t.s||!e.s)return e.s||(e=new f(t)),u?k(e,p):e;if(l=t.d,s=e.d,a=t.e,o=e.e,l=l.slice(),i=a-o){for(i<0?(n=l,i=-i,c=s.length):(n=s,o=a,c=l.length),i>(c=(a=Math.ceil(p/7))>c?a+1:c+1)&&(i=c,n.length=1),n.reverse();i--;)n.push(0);n.reverse()}for((c=l.length)-(i=s.length)<0&&(i=c,n=s,s=l,l=n),r=0;i;)r=(l[--i]=l[i]+s[i]+r)/d|0,l[i]%=d;for(r&&(l.unshift(r),++o),c=l.length;0==l[--c];)l.pop();return e.d=l,e.e=o,u?k(e,p):e}function g(t,e,r){if(t!==~~t||t<e||t>r)throw Error(l+t)}function x(t){var e,r,n,o=t.length-1,i="",a=t[0];if(o>0){for(i+=a,e=1;e<o;e++)(r=7-(n=t[e]+"").length)&&(i+=A(r)),i+=n;(r=7-(n=(a=t[e])+"").length)&&(i+=A(r))}else if(0===a)return"0";for(;a%10===0;)a/=10;return i+a}m.absoluteValue=m.abs=function(){var t=new this.constructor(this);return t.s&&(t.s=1),t},m.comparedTo=m.cmp=function(t){var e,r,n,o,i=this;if(t=new i.constructor(t),i.s!==t.s)return i.s||-t.s;if(i.e!==t.e)return i.e>t.e^i.s<0?1:-1;for(e=0,r=(n=i.d.length)<(o=t.d.length)?n:o;e<r;++e)if(i.d[e]!==t.d[e])return i.d[e]>t.d[e]^i.s<0?1:-1;return n===o?0:n>o^i.s<0?1:-1},m.decimalPlaces=m.dp=function(){var t=this,e=t.d.length-1,r=7*(e-t.e);if(e=t.d[e])for(;e%10==0;e/=10)r--;return r<0?0:r},m.dividedBy=m.div=function(t){return w(this,new this.constructor(t))},m.dividedToIntegerBy=m.idiv=function(t){var e=this.constructor;return k(w(this,new e(t),0,1),e.precision)},m.equals=m.eq=function(t){return!this.cmp(t)},m.exponent=function(){return j(this)},m.greaterThan=m.gt=function(t){return this.cmp(t)>0},m.greaterThanOrEqualTo=m.gte=function(t){return this.cmp(t)>=0},m.isInteger=m.isint=function(){return this.e>this.d.length-2},m.isNegative=m.isneg=function(){return this.s<0},m.isPositive=m.ispos=function(){return this.s>0},m.isZero=function(){return 0===this.s},m.lessThan=m.lt=function(t){return this.cmp(t)<0},m.lessThanOrEqualTo=m.lte=function(t){return this.cmp(t)<1},m.logarithm=m.log=function(t){var e,r=this,n=r.constructor,i=n.precision,a=i+5;if(void 0===t)t=new n(10);else if((t=new n(t)).s<1||t.eq(o))throw Error(c+"NaN");if(r.s<1)throw Error(c+(r.s?"NaN":"-Infinity"));return r.eq(o)?new n(0):(u=!1,e=w(P(r,a),P(t,a),a),u=!0,k(e,i))},m.minus=m.sub=function(t){var e=this;return t=new e.constructor(t),e.s==t.s?M(e,t):b(e,(t.s=-t.s,t))},m.modulo=m.mod=function(t){var e,r=this,n=r.constructor,o=n.precision;if(!(t=new n(t)).s)throw Error(c+"NaN");return r.s?(u=!1,e=w(r,t,0,1).times(t),u=!0,r.minus(e)):k(new n(r),o)},m.naturalExponential=m.exp=function(){return O(this)},m.naturalLogarithm=m.ln=function(){return P(this)},m.negated=m.neg=function(){var t=new this.constructor(this);return t.s=-t.s||0,t},m.plus=m.add=function(t){var e=this;return t=new e.constructor(t),e.s==t.s?b(e,t):M(e,(t.s=-t.s,t))},m.precision=m.sd=function(t){var e,r,n,o=this;if(void 0!==t&&t!==!!t&&1!==t&&0!==t)throw Error(l+t);if(e=j(o)+1,r=7*(n=o.d.length-1)+1,n=o.d[n]){for(;n%10==0;n/=10)r--;for(n=o.d[0];n>=10;n/=10)r++}return t&&e>r?e:r},m.squareRoot=m.sqrt=function(){var t,e,r,n,o,i,a,l=this,s=l.constructor;if(l.s<1){if(!l.s)return new s(0);throw Error(c+"NaN")}for(t=j(l),u=!1,0==(o=Math.sqrt(+l))||o==1/0?(((e=x(l.d)).length+t)%2==0&&(e+="0"),o=Math.sqrt(e),t=f((t+1)/2)-(t<0||t%2),n=new s(e=o==1/0?"5e"+t:(e=o.toExponential()).slice(0,e.indexOf("e")+1)+t)):n=new s(o.toString()),o=a=(r=s.precision)+3;;)if(n=(i=n).plus(w(l,i,a+2)).times(.5),x(i.d).slice(0,a)===(e=x(n.d)).slice(0,a)){if(e=e.slice(a-3,a+1),o==a&&"4999"==e){if(k(i,r+1,0),i.times(i).eq(l)){n=i;break}}else if("9999"!=e)break;a+=4}return u=!0,k(n,r)},m.times=m.mul=function(t){var e,r,n,o,i,a,c,l,s,f=this,p=f.constructor,h=f.d,y=(t=new p(t)).d;if(!f.s||!t.s)return new p(0);for(t.s*=f.s,r=f.e+t.e,(l=h.length)<(s=y.length)&&(i=h,h=y,y=i,a=l,l=s,s=a),i=[],n=a=l+s;n--;)i.push(0);for(n=s;--n>=0;){for(e=0,o=l+n;o>n;)c=i[o]+y[n]*h[o-n-1]+e,i[o--]=c%d|0,e=c/d|0;i[o]=(i[o]+e)%d|0}for(;!i[--a];)i.pop();return e?++r:i.shift(),t.d=i,t.e=r,u?k(t,p.precision):t},m.toDecimalPlaces=m.todp=function(t,e){var r=this,n=r.constructor;return r=new n(r),void 0===t?r:(g(t,0,i),void 0===e?e=n.rounding:g(e,0,8),k(r,t+j(r)+1,e))},m.toExponential=function(t,e){var r,n=this,o=n.constructor;return void 0===t?r=_(n,!0):(g(t,0,i),void 0===e?e=o.rounding:g(e,0,8),r=_(n=k(new o(n),t+1,e),!0,t+1)),r},m.toFixed=function(t,e){var r,n,o=this,a=o.constructor;return void 0===t?_(o):(g(t,0,i),void 0===e?e=a.rounding:g(e,0,8),r=_((n=k(new a(o),t+j(o)+1,e)).abs(),!1,t+j(n)+1),o.isneg()&&!o.isZero()?"-"+r:r)},m.toInteger=m.toint=function(){var t=this,e=t.constructor;return k(new e(t),j(t)+1,e.rounding)},m.toNumber=function(){return+this},m.toPower=m.pow=function(t){var e,r,n,i,a,l,s=this,p=s.constructor,h=+(t=new p(t));if(!t.s)return new p(o);if(!(s=new p(s)).s){if(t.s<1)throw Error(c+"Infinity");return s}if(s.eq(o))return s;if(n=p.precision,t.eq(o))return k(s,n);if(l=(e=t.e)>=(r=t.d.length-1),a=s.s,l){if((r=h<0?-h:h)<=y){for(i=new p(o),e=Math.ceil(n/7+4),u=!1;r%2&&T((i=i.times(s)).d,e),0!==(r=f(r/2));)T((s=s.times(s)).d,e);return u=!0,t.s<0?new p(o).div(i):k(i,n)}}else if(a<0)throw Error(c+"NaN");return a=a<0&&1&t.d[Math.max(e,r)]?-1:1,s.s=1,u=!1,i=t.times(P(s,n+12)),u=!0,(i=O(i)).s=a,i},m.toPrecision=function(t,e){var r,n,o=this,a=o.constructor;return void 0===t?n=_(o,(r=j(o))<=a.toExpNeg||r>=a.toExpPos):(g(t,1,i),void 0===e?e=a.rounding:g(e,0,8),n=_(o=k(new a(o),t,e),t<=(r=j(o))||r<=a.toExpNeg,t)),n},m.toSignificantDigits=m.tosd=function(t,e){var r=this.constructor;return void 0===t?(t=r.precision,e=r.rounding):(g(t,1,i),void 0===e?e=r.rounding:g(e,0,8)),k(new r(this),t,e)},m.toString=m.valueOf=m.val=m.toJSON=function(){var t=this,e=j(t),r=t.constructor;return _(t,e<=r.toExpNeg||e>=r.toExpPos)};var w=function(){function t(t,e){var r,n=0,o=t.length;for(t=t.slice();o--;)r=t[o]*e+n,t[o]=r%d|0,n=r/d|0;return n&&t.unshift(n),t}function e(t,e,r,n){var o,i;if(r!=n)i=r>n?1:-1;else for(o=i=0;o<r;o++)if(t[o]!=e[o]){i=t[o]>e[o]?1:-1;break}return i}function r(t,e,r){for(var n=0;r--;)t[r]-=n,n=t[r]<e[r]?1:0,t[r]=n*d+t[r]-e[r];for(;!t[0]&&t.length>1;)t.shift()}return function(n,o,i,a){var u,l,s,f,p,h,y,v,m,b,g,x,w,O,S,A,P,E,M=n.constructor,_=n.s==o.s?1:-1,T=n.d,C=o.d;if(!n.s)return new M(n);if(!o.s)throw Error(c+"Division by zero");for(l=n.e-o.e,P=C.length,S=T.length,v=(y=new M(_)).d=[],s=0;C[s]==(T[s]||0);)++s;if(C[s]>(T[s]||0)&&--l,(x=null==i?i=M.precision:a?i+(j(n)-j(o))+1:i)<0)return new M(0);if(x=x/7+2|0,s=0,1==P)for(f=0,C=C[0],x++;(s<S||f)&&x--;s++)w=f*d+(T[s]||0),v[s]=w/C|0,f=w%C|0;else{for((f=d/(C[0]+1)|0)>1&&(C=t(C,f),T=t(T,f),P=C.length,S=T.length),O=P,b=(m=T.slice(0,P)).length;b<P;)m[b++]=0;(E=C.slice()).unshift(0),A=C[0],C[1]>=d/2&&++A;do{f=0,(u=e(C,m,P,b))<0?(g=m[0],P!=b&&(g=g*d+(m[1]||0)),(f=g/A|0)>1?(f>=d&&(f=d-1),1==(u=e(p=t(C,f),m,h=p.length,b=m.length))&&(f--,r(p,P<h?E:C,h))):(0==f&&(u=f=1),p=C.slice()),(h=p.length)<b&&p.unshift(0),r(m,p,b),-1==u&&(u=e(C,m,P,b=m.length))<1&&(f++,r(m,P<b?E:C,b)),b=m.length):0===u&&(f++,m=[0]),v[s++]=f,u&&m[0]?m[b++]=T[O]||0:(m=[T[O]],b=1)}while((O++<S||void 0!==m[0])&&x--)}return v[0]||v.shift(),y.e=l,k(y,a?i+j(y)+1:i)}}();function O(t,e){var r,n,i,a,c,l=0,f=0,h=t.constructor,d=h.precision;if(j(t)>16)throw Error(s+j(t));if(!t.s)return new h(o);for(null==e?(u=!1,c=d):c=e,a=new h(.03125);t.abs().gte(.1);)t=t.times(a),f+=5;for(c+=Math.log(p(2,f))/Math.LN10*2+5|0,r=n=i=new h(o),h.precision=c;;){if(n=k(n.times(t),c),r=r.times(++l),x((a=i.plus(w(n,r,c))).d).slice(0,c)===x(i.d).slice(0,c)){for(;f--;)i=k(i.times(i),c);return h.precision=d,null==e?(u=!0,k(i,d)):i}i=a}}function j(t){for(var e=7*t.e,r=t.d[0];r>=10;r/=10)e++;return e}function S(t,e,r){if(e>t.LN10.sd())throw u=!0,r&&(t.precision=r),Error(c+"LN10 precision limit exceeded");return k(new t(t.LN10),e)}function A(t){for(var e="";t--;)e+="0";return e}function P(t,e){var r,n,i,a,l,s,f,p,h,d=1,y=t,v=y.d,m=y.constructor,b=m.precision;if(y.s<1)throw Error(c+(y.s?"NaN":"-Infinity"));if(y.eq(o))return new m(0);if(null==e?(u=!1,p=b):p=e,y.eq(10))return null==e&&(u=!0),S(m,p);if(p+=10,m.precision=p,n=(r=x(v)).charAt(0),a=j(y),!(Math.abs(a)<15e14))return f=S(m,p+2,b).times(a+""),y=P(new m(n+"."+r.slice(1)),p-10).plus(f),m.precision=b,null==e?(u=!0,k(y,b)):y;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=x((y=y.times(t)).d)).charAt(0),d++;for(a=j(y),n>1?(y=new m("0."+r),a++):y=new m(n+"."+r.slice(1)),s=l=y=w(y.minus(o),y.plus(o),p),h=k(y.times(y),p),i=3;;){if(l=k(l.times(h),p),x((f=s.plus(w(l,new m(i),p))).d).slice(0,p)===x(s.d).slice(0,p))return s=s.times(2),0!==a&&(s=s.plus(S(m,p+2,b).times(a+""))),s=w(s,new m(d),p),m.precision=b,null==e?(u=!0,k(s,b)):s;s=f,i+=2}}function E(t,e){var r,n,o;for((r=e.indexOf("."))>-1&&(e=e.replace(".","")),(n=e.search(/e/i))>0?(r<0&&(r=n),r+=+e.slice(n+1),e=e.substring(0,n)):r<0&&(r=e.length),n=0;48===e.charCodeAt(n);)++n;for(o=e.length;48===e.charCodeAt(o-1);)--o;if(e=e.slice(n,o)){if(o-=n,r=r-n-1,t.e=f(r/7),t.d=[],n=(r+1)%7,r<0&&(n+=7),n<o){for(n&&t.d.push(+e.slice(0,n)),o-=7;n<o;)t.d.push(+e.slice(n,n+=7));n=7-(e=e.slice(n)).length}else n-=o;for(;n--;)e+="0";if(t.d.push(+e),u&&(t.e>v||t.e<-v))throw Error(s+r)}else t.s=0,t.e=0,t.d=[0];return t}function k(t,e,r){var n,o,i,a,c,l,h,y,m=t.d;for(a=1,i=m[0];i>=10;i/=10)a++;if((n=e-a)<0)n+=7,o=e,h=m[y=0];else{if((y=Math.ceil((n+1)/7))>=(i=m.length))return t;for(h=i=m[y],a=1;i>=10;i/=10)a++;o=(n%=7)-7+a}if(void 0!==r&&(c=h/(i=p(10,a-o-1))%10|0,l=e<0||void 0!==m[y+1]||h%i,l=r<4?(c||l)&&(0==r||r==(t.s<0?3:2)):c>5||5==c&&(4==r||l||6==r&&(n>0?o>0?h/p(10,a-o):0:m[y-1])%10&1||r==(t.s<0?8:7))),e<1||!m[0])return l?(i=j(t),m.length=1,e=e-i-1,m[0]=p(10,(7-e%7)%7),t.e=f(-e/7)||0):(m.length=1,m[0]=t.e=t.s=0),t;if(0==n?(m.length=y,i=1,y--):(m.length=y+1,i=p(10,7-n),m[y]=o>0?(h/p(10,a-o)%p(10,o)|0)*i:0),l)for(;;){if(0==y){(m[0]+=i)==d&&(m[0]=1,++t.e);break}if(m[y]+=i,m[y]!=d)break;m[y--]=0,i=1}for(n=m.length;0===m[--n];)m.pop();if(u&&(t.e>v||t.e<-v))throw Error(s+j(t));return t}function M(t,e){var r,n,o,i,a,c,l,s,f,p,h=t.constructor,y=h.precision;if(!t.s||!e.s)return e.s?e.s=-e.s:e=new h(t),u?k(e,y):e;if(l=t.d,p=e.d,n=e.e,s=t.e,l=l.slice(),a=s-n){for((f=a<0)?(r=l,a=-a,c=p.length):(r=p,n=s,c=l.length),a>(o=Math.max(Math.ceil(y/7),c)+2)&&(a=o,r.length=1),r.reverse(),o=a;o--;)r.push(0);r.reverse()}else{for((f=(o=l.length)<(c=p.length))&&(c=o),o=0;o<c;o++)if(l[o]!=p[o]){f=l[o]<p[o];break}a=0}for(f&&(r=l,l=p,p=r,e.s=-e.s),c=l.length,o=p.length-c;o>0;--o)l[c++]=0;for(o=p.length;o>a;){if(l[--o]<p[o]){for(i=o;i&&0===l[--i];)l[i]=d-1;--l[i],l[o]+=d}l[o]-=p[o]}for(;0===l[--c];)l.pop();for(;0===l[0];l.shift())--n;return l[0]?(e.d=l,e.e=n,u?k(e,y):e):new h(0)}function _(t,e,r){var n,o=j(t),i=x(t.d),a=i.length;return e?(r&&(n=r-a)>0?i=i.charAt(0)+"."+i.slice(1)+A(n):a>1&&(i=i.charAt(0)+"."+i.slice(1)),i=i+(o<0?"e":"e+")+o):o<0?(i="0."+A(-o-1)+i,r&&(n=r-a)>0&&(i+=A(n))):o>=a?(i+=A(o+1-a),r&&(n=r-o-1)>0&&(i=i+"."+A(n))):((n=o+1)<a&&(i=i.slice(0,n)+"."+i.slice(n)),r&&(n=r-a)>0&&(o+1===a&&(i+="."),i+=A(n))),t.s<0?"-"+i:i}function T(t,e){if(t.length>e)return t.length=e,!0}function C(t){if(!t||"object"!==typeof t)throw Error(c+"Object expected");var e,r,n,o=["precision",1,i,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(e=0;e<o.length;e+=3)if(void 0!==(n=t[r=o[e]])){if(!(f(n)===n&&n>=o[e+1]&&n<=o[e+2]))throw Error(l+r+": "+n);this[r]=n}if(void 0!==(n=t[r="LN10"])){if(n!=Math.LN10)throw Error(l+r+": "+n);this[r]=new this(n)}return this}a=function t(e){var r,n,o;function i(t){var e=this;if(!(e instanceof i))return new i(t);if(e.constructor=i,t instanceof i)return e.s=t.s,e.e=t.e,void(e.d=(t=t.d)?t.slice():t);if("number"===typeof t){if(0*t!==0)throw Error(l+t);if(t>0)e.s=1;else{if(!(t<0))return e.s=0,e.e=0,void(e.d=[0]);t=-t,e.s=-1}return t===~~t&&t<1e7?(e.e=0,void(e.d=[t])):E(e,t.toString())}if("string"!==typeof t)throw Error(l+t);if(45===t.charCodeAt(0)?(t=t.slice(1),e.s=-1):e.s=1,!h.test(t))throw Error(l+t);E(e,t)}if(i.prototype=m,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=t,i.config=i.set=C,void 0===e&&(e={}),e)for(o=["precision","rounding","toExpNeg","toExpPos","LN10"],r=0;r<o.length;)e.hasOwnProperty(n=o[r++])||(e[n]=this[n]);return i.config(e),i}(a),a.default=a.Decimal=a,o=new a(1),void 0===(n=function(){return a}.call(e,r,e,t))||(t.exports=n)}()},8259:(t,e,r)=>{var n=r(5797);t.exports=function(t){var e=n(t,function(t){return 500===r.size&&r.clear(),t}),r=e.cache;return e}},8325:(t,e,r)=>{var n=r(2541),o=r(5654),i=r(3279),a=o?function(t,e){return o(t,"toString",{configurable:!0,enumerable:!1,value:n(e),writable:!0})}:i;t.exports=a},8420:(t,e,r)=>{"use strict";r.d(e,{gu:()=>Ye});var n=r(5043),o=r(9686),i=r.n(o),a=r(1629),u=r.n(a),c=r(6604),l=r.n(c),s=r(3097),f=r.n(s),p=r(7424),h=r.n(p),d=r(9889),y=r.n(d),v=r(8387),m=r(3404),b=r(4794),g=r(1639),x=r(6150),w=r(1327),O=r(8892),j=r(4342),S=r(240),A=r(2099),P=r(4140),E=r(202),k=r(6307);function M(t){return M="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},M(t)}function _(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function T(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?_(Object(r),!0).forEach(function(e){C(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):_(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function C(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=M(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=M(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==M(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var I=["Webkit","Moz","O","ms"];function D(t){return D="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},D(t)}function N(){return N=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},N.apply(this,arguments)}function B(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function R(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?B(Object(r),!0).forEach(function(e){W(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):B(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function L(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,q(n.key),n)}}function z(t,e,r){return e=F(e),function(t,e){if(e&&("object"===D(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,U()?Reflect.construct(e,r||[],F(t).constructor):e.apply(t,r))}function U(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(U=function(){return!!t})()}function F(t){return F=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},F(t)}function $(t,e){return $=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},$(t,e)}function W(t,e,r){return(e=q(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function q(t){var e=function(t,e){if("object"!=D(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=D(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==D(e)?e:e+""}var X=function(t){return t.changedTouches&&!!t.changedTouches.length},H=function(t){function e(t){var r;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),W(r=z(this,e,[t]),"handleDrag",function(t){r.leaveTimer&&(clearTimeout(r.leaveTimer),r.leaveTimer=null),r.state.isTravellerMoving?r.handleTravellerMove(t):r.state.isSlideMoving&&r.handleSlideDrag(t)}),W(r,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&r.handleDrag(t.changedTouches[0])}),W(r,"handleDragEnd",function(){r.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var t=r.props,e=t.endIndex,n=t.onDragEnd,o=t.startIndex;null===n||void 0===n||n({endIndex:e,startIndex:o})}),r.detachDragEndListener()}),W(r,"handleLeaveWrapper",function(){(r.state.isTravellerMoving||r.state.isSlideMoving)&&(r.leaveTimer=window.setTimeout(r.handleDragEnd,r.props.leaveTimeOut))}),W(r,"handleEnterSlideOrTraveller",function(){r.setState({isTextActive:!0})}),W(r,"handleLeaveSlideOrTraveller",function(){r.setState({isTextActive:!1})}),W(r,"handleSlideDragStart",function(t){var e=X(t)?t.changedTouches[0]:t;r.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:e.pageX}),r.attachDragEndListener()}),r.travellerDragStartHandlers={startX:r.handleTravellerDragStart.bind(r,"startX"),endX:r.handleTravellerDragStart.bind(r,"endX")},r.state={},r}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&$(t,e)}(e,t),r=e,i=[{key:"renderDefaultTraveller",value:function(t){var e=t.x,r=t.y,o=t.width,i=t.height,a=t.stroke,u=Math.floor(r+i/2)-1;return n.createElement(n.Fragment,null,n.createElement("rect",{x:e,y:r,width:o,height:i,fill:a,stroke:"none"}),n.createElement("line",{x1:e+1,y1:u,x2:e+o-1,y2:u,fill:"none",stroke:"#fff"}),n.createElement("line",{x1:e+1,y1:u+2,x2:e+o-1,y2:u+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(t,r){return n.isValidElement(t)?n.cloneElement(t,r):u()(t)?t(r):e.renderDefaultTraveller(r)}},{key:"getDerivedStateFromProps",value:function(t,e){var r=t.data,n=t.width,o=t.x,i=t.travellerWidth,a=t.updateId,u=t.startIndex,c=t.endIndex;if(r!==e.prevData||a!==e.prevUpdateId)return R({prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n},r&&r.length?function(t){var e=t.data,r=t.startIndex,n=t.endIndex,o=t.x,i=t.width,a=t.travellerWidth;if(!e||!e.length)return{};var u=e.length,c=(0,A.z)().domain(l()(0,u)).range([o,o+i-a]),s=c.domain().map(function(t){return c(t)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:c(r),endX:c(n),scale:c,scaleValues:s}}({data:r,width:n,x:o,travellerWidth:i,startIndex:u,endIndex:c}):{scale:null,scaleValues:null});if(e.scale&&(n!==e.prevWidth||o!==e.prevX||i!==e.prevTravellerWidth)){e.scale.range([o,o+n-i]);var s=e.scale.domain().map(function(t){return e.scale(t)});return{prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n,startX:e.scale(t.startIndex),endX:e.scale(t.endIndex),scaleValues:s}}return null}},{key:"getIndexInRange",value:function(t,e){for(var r=0,n=t.length-1;n-r>1;){var o=Math.floor((r+n)/2);t[o]>e?n=o:r=o}return e>=t[n]?n:r}}],(o=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(t){var r=t.startX,n=t.endX,o=this.state.scaleValues,i=this.props,a=i.gap,u=i.data.length-1,c=Math.min(r,n),l=Math.max(r,n),s=e.getIndexInRange(o,c),f=e.getIndexInRange(o,l);return{startIndex:s-s%a,endIndex:f===u?u:f-f%a}}},{key:"getTextOfTick",value:function(t){var e=this.props,r=e.data,n=e.tickFormatter,o=e.dataKey,i=(0,E.kr)(r[t],o,t);return u()(n)?n(i,t):i}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(t){var e=this.state,r=e.slideMoveStartX,n=e.startX,o=e.endX,i=this.props,a=i.x,u=i.width,c=i.travellerWidth,l=i.startIndex,s=i.endIndex,f=i.onChange,p=t.pageX-r;p>0?p=Math.min(p,a+u-c-o,a+u-c-n):p<0&&(p=Math.max(p,a-n,a-o));var h=this.getIndex({startX:n+p,endX:o+p});h.startIndex===l&&h.endIndex===s||!f||f(h),this.setState({startX:n+p,endX:o+p,slideMoveStartX:t.pageX})}},{key:"handleTravellerDragStart",value:function(t,e){var r=X(e)?e.changedTouches[0]:e;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:t,brushMoveStartX:r.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(t){var e=this.state,r=e.brushMoveStartX,n=e.movingTravellerId,o=e.endX,i=e.startX,a=this.state[n],u=this.props,c=u.x,l=u.width,s=u.travellerWidth,f=u.onChange,p=u.gap,h=u.data,d={startX:this.state.startX,endX:this.state.endX},y=t.pageX-r;y>0?y=Math.min(y,c+l-s-a):y<0&&(y=Math.max(y,c-a)),d[n]=a+y;var v=this.getIndex(d),m=v.startIndex,b=v.endIndex;this.setState(W(W({},n,a+y),"brushMoveStartX",t.pageX),function(){f&&function(){var t=h.length-1;return"startX"===n&&(o>i?m%p===0:b%p===0)||o<i&&b===t||"endX"===n&&(o>i?b%p===0:m%p===0)||o>i&&b===t}()&&f(v)})}},{key:"handleTravellerMoveKeyboard",value:function(t,e){var r=this,n=this.state,o=n.scaleValues,i=n.startX,a=n.endX,u=this.state[e],c=o.indexOf(u);if(-1!==c){var l=c+t;if(!(-1===l||l>=o.length)){var s=o[l];"startX"===e&&s>=a||"endX"===e&&s<=i||this.setState(W({},e,s),function(){r.props.onChange(r.getIndex({startX:r.state.startX,endX:r.state.endX}))})}}}},{key:"renderBackground",value:function(){var t=this.props,e=t.x,r=t.y,o=t.width,i=t.height,a=t.fill,u=t.stroke;return n.createElement("rect",{stroke:u,fill:a,x:e,y:r,width:o,height:i})}},{key:"renderPanorama",value:function(){var t=this.props,e=t.x,r=t.y,o=t.width,i=t.height,a=t.data,u=t.children,c=t.padding,l=n.Children.only(u);return l?n.cloneElement(l,{x:e,y:r,width:o,height:i,margin:c,compact:!0,data:a}):null}},{key:"renderTravellerLayer",value:function(t,r){var o,i,a=this,u=this.props,c=u.y,l=u.travellerWidth,s=u.height,f=u.traveller,p=u.ariaLabel,h=u.data,d=u.startIndex,y=u.endIndex,v=Math.max(t,this.props.x),m=R(R({},(0,S.J9)(this.props,!1)),{},{x:v,y:c,width:l,height:s}),b=p||"Min value: ".concat(null===(o=h[d])||void 0===o?void 0:o.name,", Max value: ").concat(null===(i=h[y])||void 0===i?void 0:i.name);return n.createElement(g.W,{tabIndex:0,role:"slider","aria-label":b,"aria-valuenow":t,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[r],onTouchStart:this.travellerDragStartHandlers[r],onKeyDown:function(t){["ArrowLeft","ArrowRight"].includes(t.key)&&(t.preventDefault(),t.stopPropagation(),a.handleTravellerMoveKeyboard("ArrowRight"===t.key?1:-1,r))},onFocus:function(){a.setState({isTravellerFocused:!0})},onBlur:function(){a.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},e.renderTraveller(f,m))}},{key:"renderSlide",value:function(t,e){var r=this.props,o=r.y,i=r.height,a=r.stroke,u=r.travellerWidth,c=Math.min(t,e)+u,l=Math.max(Math.abs(e-t)-u,0);return n.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:a,fillOpacity:.2,x:c,y:o,width:l,height:i})}},{key:"renderText",value:function(){var t=this.props,e=t.startIndex,r=t.endIndex,o=t.y,i=t.height,a=t.travellerWidth,u=t.stroke,c=this.state,l=c.startX,s=c.endX,f={pointerEvents:"none",fill:u};return n.createElement(g.W,{className:"recharts-brush-texts"},n.createElement(P.E,N({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,s)-5,y:o+i/2},f),this.getTextOfTick(e)),n.createElement(P.E,N({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,s)+a+5,y:o+i/2},f),this.getTextOfTick(r)))}},{key:"render",value:function(){var t=this.props,e=t.data,r=t.className,o=t.children,i=t.x,a=t.y,u=t.width,c=t.height,l=t.alwaysShowText,s=this.state,f=s.startX,p=s.endX,h=s.isTextActive,d=s.isSlideMoving,y=s.isTravellerMoving,m=s.isTravellerFocused;if(!e||!e.length||!(0,k.Et)(i)||!(0,k.Et)(a)||!(0,k.Et)(u)||!(0,k.Et)(c)||u<=0||c<=0)return null;var b=(0,v.A)("recharts-brush",r),x=1===n.Children.count(o),w=function(t,e){if(!t)return null;var r=t.replace(/(\w)/,function(t){return t.toUpperCase()}),n=I.reduce(function(t,n){return T(T({},t),{},C({},n+r,e))},{});return n[t]=e,n}("userSelect","none");return n.createElement(g.W,{className:b,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:w},this.renderBackground(),x&&this.renderPanorama(),this.renderSlide(f,p),this.renderTravellerLayer(f,"startX"),this.renderTravellerLayer(p,"endX"),(h||d||y||m||l)&&this.renderText())}}])&&L(r.prototype,o),i&&L(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,i}(n.PureComponent);W(H,"displayName","Brush"),W(H,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var G=r(7213),V=r(7165),K=r(2647),Y=function(t,e){var r=t.alwaysShow,n=t.ifOverflow;return r&&(n="extendDomain"),n===e},J=r(3831),Z=r(155);function Q(){return Q=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Q.apply(this,arguments)}function tt(t){return tt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},tt(t)}function et(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function rt(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?et(Object(r),!0).forEach(function(e){ct(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):et(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function nt(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,lt(n.key),n)}}function ot(t,e,r){return e=at(e),function(t,e){if(e&&("object"===tt(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,it()?Reflect.construct(e,r||[],at(t).constructor):e.apply(t,r))}function it(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(it=function(){return!!t})()}function at(t){return at=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},at(t)}function ut(t,e){return ut=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},ut(t,e)}function ct(t,e,r){return(e=lt(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function lt(t){var e=function(t,e){if("object"!=tt(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tt(e)?e:e+""}var st=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),ot(this,e,arguments)}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&ut(t,e)}(e,t),r=e,(o=[{key:"render",value:function(){var t=this.props,r=t.x,o=t.y,i=t.r,a=t.alwaysShow,u=t.clipPathId,c=(0,k.vh)(r),l=(0,k.vh)(o);if((0,Z.R)(void 0===a,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!c||!l)return null;var s=function(t){var e=t.x,r=t.y,n=t.xAxis,o=t.yAxis,i=(0,J.P2)({x:n.scale,y:o.scale}),a=i.apply({x:e,y:r},{bandAware:!0});return Y(t,"discard")&&!i.isInRange(a)?null:a}(this.props);if(!s)return null;var f=s.x,p=s.y,h=this.props,d=h.shape,y=h.className,m=rt(rt({clipPath:Y(this.props,"hidden")?"url(#".concat(u,")"):void 0},(0,S.J9)(this.props,!0)),{},{cx:f,cy:p});return n.createElement(g.W,{className:(0,v.A)("recharts-reference-dot",y)},e.renderDot(d,m),K.J.renderCallByParent(this.props,{x:f-i,y:p-i,width:2*i,height:2*i}))}}])&&nt(r.prototype,o),i&&nt(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,i}(n.Component);ct(st,"displayName","ReferenceDot"),ct(st,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),ct(st,"renderDot",function(t,e){return n.isValidElement(t)?n.cloneElement(t,e):u()(t)?t(e):n.createElement(O.c,Q({},e,{cx:e.cx,cy:e.cy,className:"recharts-reference-dot-dot"}))});var ft=r(4597),pt=r.n(ft),ht=r(2103);function dt(t){return dt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},dt(t)}function yt(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,jt(n.key),n)}}function vt(t,e,r){return e=bt(e),function(t,e){if(e&&("object"===dt(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,mt()?Reflect.construct(e,r||[],bt(t).constructor):e.apply(t,r))}function mt(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(mt=function(){return!!t})()}function bt(t){return bt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},bt(t)}function gt(t,e){return gt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},gt(t,e)}function xt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function wt(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?xt(Object(r),!0).forEach(function(e){Ot(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):xt(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function Ot(t,e,r){return(e=jt(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function jt(t){var e=function(t,e){if("object"!=dt(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=dt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==dt(e)?e:e+""}function St(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return At(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return At(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function At(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Pt(){return Pt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Pt.apply(this,arguments)}function Et(t){var e=t.x,r=t.y,o=t.segment,i=t.xAxisId,a=t.yAxisId,c=t.shape,l=t.className,s=t.alwaysShow,f=(0,ht.Yp)(),p=(0,ht.AF)(i),h=(0,ht.Nk)(a),d=(0,ht.sk)();if(!f||!d)return null;(0,Z.R)(void 0===s,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var y=function(t,e,r,n,o,i,a,u,c){var l=o.x,s=o.y,f=o.width,p=o.height;if(r){var h=c.y,d=t.y.apply(h,{position:i});if(Y(c,"discard")&&!t.y.isInRange(d))return null;var y=[{x:l+f,y:d},{x:l,y:d}];return"left"===u?y.reverse():y}if(e){var v=c.x,m=t.x.apply(v,{position:i});if(Y(c,"discard")&&!t.x.isInRange(m))return null;var b=[{x:m,y:s+p},{x:m,y:s}];return"top"===a?b.reverse():b}if(n){var g=c.segment.map(function(e){return t.apply(e,{position:i})});return Y(c,"discard")&&pt()(g,function(e){return!t.isInRange(e)})?null:g}return null}((0,J.P2)({x:p.scale,y:h.scale}),(0,k.vh)(e),(0,k.vh)(r),o&&2===o.length,d,t.position,p.orientation,h.orientation,t);if(!y)return null;var m=St(y,2),b=m[0],x=b.x,w=b.y,O=m[1],j=O.x,A=O.y,P=wt(wt({clipPath:Y(t,"hidden")?"url(#".concat(f,")"):void 0},(0,S.J9)(t,!0)),{},{x1:x,y1:w,x2:j,y2:A});return n.createElement(g.W,{className:(0,v.A)("recharts-reference-line",l)},function(t,e){return n.isValidElement(t)?n.cloneElement(t,e):u()(t)?t(e):n.createElement("line",Pt({},e,{className:"recharts-reference-line-line"}))}(c,P),K.J.renderCallByParent(t,(0,J.vh)({x1:x,y1:w,x2:j,y2:A})))}var kt=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),vt(this,e,arguments)}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&gt(t,e)}(e,t),r=e,(o=[{key:"render",value:function(){return n.createElement(Et,this.props)}}])&&yt(r.prototype,o),i&&yt(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,i}(n.Component);function Mt(){return Mt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Mt.apply(this,arguments)}function _t(t){return _t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_t(t)}function Tt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function Ct(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Tt(Object(r),!0).forEach(function(e){Lt(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Tt(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function It(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,zt(n.key),n)}}function Dt(t,e,r){return e=Bt(e),function(t,e){if(e&&("object"===_t(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Nt()?Reflect.construct(e,r||[],Bt(t).constructor):e.apply(t,r))}function Nt(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(Nt=function(){return!!t})()}function Bt(t){return Bt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Bt(t)}function Rt(t,e){return Rt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Rt(t,e)}function Lt(t,e,r){return(e=zt(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function zt(t){var e=function(t,e){if("object"!=_t(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=_t(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==_t(e)?e:e+""}Ot(kt,"displayName","ReferenceLine"),Ot(kt,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});var Ut=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),Dt(this,e,arguments)}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Rt(t,e)}(e,t),r=e,(o=[{key:"render",value:function(){var t=this.props,r=t.x1,o=t.x2,i=t.y1,a=t.y2,u=t.className,c=t.alwaysShow,l=t.clipPathId;(0,Z.R)(void 0===c,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var s=(0,k.vh)(r),f=(0,k.vh)(o),p=(0,k.vh)(i),h=(0,k.vh)(a),d=this.props.shape;if(!s&&!f&&!p&&!h&&!d)return null;var y=function(t,e,r,n,o){var i=o.x1,a=o.x2,u=o.y1,c=o.y2,l=o.xAxis,s=o.yAxis;if(!l||!s)return null;var f=(0,J.P2)({x:l.scale,y:s.scale}),p={x:t?f.x.apply(i,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(u,{position:"start"}):f.y.rangeMin},h={x:e?f.x.apply(a,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(c,{position:"end"}):f.y.rangeMax};return!Y(o,"discard")||f.isInRange(p)&&f.isInRange(h)?(0,J.sl)(p,h):null}(s,f,p,h,this.props);if(!y&&!d)return null;var m=Y(this.props,"hidden")?"url(#".concat(l,")"):void 0;return n.createElement(g.W,{className:(0,v.A)("recharts-reference-area",u)},e.renderRect(d,Ct(Ct({clipPath:m},(0,S.J9)(this.props,!0)),y)),K.J.renderCallByParent(this.props,y))}}])&&It(r.prototype,o),i&&It(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,i}(n.Component);function Ft(t){return function(t){if(Array.isArray(t))return $t(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return $t(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return $t(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function $t(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}Lt(Ut,"displayName","ReferenceArea"),Lt(Ut,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),Lt(Ut,"renderRect",function(t,e){return n.isValidElement(t)?n.cloneElement(t,e):u()(t)?t(e):n.createElement(j.M,Mt({},e,{className:"recharts-reference-area-rect"}))});var Wt=function(t,e,r,n,o){var i=(0,S.aS)(t,kt),a=(0,S.aS)(t,st),u=[].concat(Ft(i),Ft(a)),c=(0,S.aS)(t,Ut),l="".concat(n,"Id"),s=n[0],f=e;if(u.length&&(f=u.reduce(function(t,e){if(e.props[l]===r&&Y(e.props,"extendDomain")&&(0,k.Et)(e.props[s])){var n=e.props[s];return[Math.min(t[0],n),Math.max(t[1],n)]}return t},f)),c.length){var p="".concat(s,"1"),h="".concat(s,"2");f=c.reduce(function(t,e){if(e.props[l]===r&&Y(e.props,"extendDomain")&&(0,k.Et)(e.props[p])&&(0,k.Et)(e.props[h])){var n=e.props[p],o=e.props[h];return[Math.min(t[0],n,o),Math.max(t[1],n,o)]}return t},f)}return o&&o.length&&(f=o.reduce(function(t,e){return(0,k.Et)(e)?[Math.min(t[0],e),Math.max(t[1],e)]:t},f)),f},qt=r(165),Xt=r(5248),Ht=r(7283),Gt=new(r.n(Ht)()),Vt="recharts.syncMouseEvents",Kt=r(7287);function Yt(t){return Yt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Yt(t)}function Jt(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Qt(n.key),n)}}function Zt(t,e,r){return(e=Qt(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Qt(t){var e=function(t,e){if("object"!=Yt(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Yt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Yt(e)?e:e+""}var te=function(){return t=function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),Zt(this,"activeIndex",0),Zt(this,"coordinateList",[]),Zt(this,"layout","horizontal")},(e=[{key:"setDetails",value:function(t){var e,r=t.coordinateList,n=void 0===r?null:r,o=t.container,i=void 0===o?null:o,a=t.layout,u=void 0===a?null:a,c=t.offset,l=void 0===c?null:c,s=t.mouseHandlerCallback,f=void 0===s?null:s;this.coordinateList=null!==(e=null!==n&&void 0!==n?n:this.coordinateList)&&void 0!==e?e:[],this.container=null!==i&&void 0!==i?i:this.container,this.layout=null!==u&&void 0!==u?u:this.layout,this.offset=null!==l&&void 0!==l?l:this.offset,this.mouseHandlerCallback=null!==f&&void 0!==f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(t){if(0!==this.coordinateList.length)switch(t.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(t){this.activeIndex=t}},{key:"spoofMouse",value:function(){var t,e;if("horizontal"===this.layout&&0!==this.coordinateList.length){var r=this.container.getBoundingClientRect(),n=r.x,o=r.y,i=r.height,a=this.coordinateList[this.activeIndex].coordinate,u=(null===(t=window)||void 0===t?void 0:t.scrollX)||0,c=(null===(e=window)||void 0===e?void 0:e.scrollY)||0,l=n+a+u,s=o+this.offset.top+i/2+c;this.mouseHandlerCallback({pageX:l,pageY:s})}}}])&&Jt(t.prototype,e),r&&Jt(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,r}();var ee=r(879),re=r(8471);function ne(t){return ne="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ne(t)}var oe=["x","y","top","left","width","height","className"];function ie(){return ie=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ie.apply(this,arguments)}function ae(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ue(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=ne(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ne(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ne(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ce(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var le=function(t,e,r,n,o,i){return"M".concat(t,",").concat(o,"v").concat(n,"M").concat(i,",").concat(e,"h").concat(r)},se=function(t){var e=t.x,r=void 0===e?0:e,o=t.y,i=void 0===o?0:o,a=t.top,u=void 0===a?0:a,c=t.left,l=void 0===c?0:c,s=t.width,f=void 0===s?0:s,p=t.height,h=void 0===p?0:p,d=t.className,y=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ae(Object(r),!0).forEach(function(e){ue(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ae(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({x:r,y:i,top:u,left:l,width:f,height:h},ce(t,oe));return(0,k.Et)(r)&&(0,k.Et)(i)&&(0,k.Et)(f)&&(0,k.Et)(h)&&(0,k.Et)(u)&&(0,k.Et)(l)?n.createElement("path",ie({},(0,S.J9)(y,!0),{className:(0,v.A)("recharts-cross",d),d:le(r,i,f,h,u,l)})):null};function fe(t){var e=t.cx,r=t.cy,n=t.radius,o=t.startAngle,i=t.endAngle;return{points:[(0,qt.IZ)(e,r,n,o),(0,qt.IZ)(e,r,n,i)],cx:e,cy:r,radius:n,startAngle:o,endAngle:i}}var pe=r(677);function he(t,e,r){var n,o,i,a;if("horizontal"===t)i=n=e.x,o=r.top,a=r.top+r.height;else if("vertical"===t)a=o=e.y,n=r.left,i=r.left+r.width;else if(null!=e.cx&&null!=e.cy){if("centric"!==t)return fe(e);var u=e.cx,c=e.cy,l=e.innerRadius,s=e.outerRadius,f=e.angle,p=(0,qt.IZ)(u,c,l,f),h=(0,qt.IZ)(u,c,s,f);n=p.x,o=p.y,i=h.x,a=h.y}return[{x:n,y:o},{x:i,y:a}]}function de(t){return de="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},de(t)}function ye(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ve(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ye(Object(r),!0).forEach(function(e){me(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ye(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function me(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=de(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=de(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==de(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function be(t){var e,r,o,i=t.element,a=t.tooltipEventType,u=t.isActive,c=t.activeCoordinate,l=t.activePayload,s=t.offset,f=t.activeTooltipIndex,p=t.tooltipAxisBandSize,h=t.layout,d=t.chartName,y=null!==(e=i.props.cursor)&&void 0!==e?e:null===(r=i.type.defaultProps)||void 0===r?void 0:r.cursor;if(!i||!y||!u||!c||"ScatterChart"!==d&&"axis"!==a)return null;var m=re.I;if("ScatterChart"===d)o=c,m=se;else if("BarChart"===d)o=function(t,e,r,n){var o=n/2;return{stroke:"none",fill:"#ccc",x:"horizontal"===t?e.x-o:r.left+.5,y:"horizontal"===t?r.top+.5:e.y-o,width:"horizontal"===t?n:r.width-1,height:"horizontal"===t?r.height-1:n}}(h,c,s,p),m=j.M;else if("radial"===h){var b=fe(c),g=b.cx,x=b.cy,w=b.radius;o={cx:g,cy:x,startAngle:b.startAngle,endAngle:b.endAngle,innerRadius:w,outerRadius:w},m=pe.h}else o={points:he(h,c,s)},m=re.I;var O=ve(ve(ve(ve({stroke:"#ccc",pointerEvents:"none"},s),o),(0,S.J9)(y,!1)),{},{payload:l,payloadIndex:f,className:(0,v.A)("recharts-tooltip-cursor",y.className)});return(0,n.isValidElement)(y)?(0,n.cloneElement)(y,O):(0,n.createElement)(m,O)}var ge=["item"],xe=["children","className","width","height","style","compact","title","desc"];function we(t){return we="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},we(t)}function Oe(){return Oe=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Oe.apply(this,arguments)}function je(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||Te(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Se(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function Ae(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Be(n.key),n)}}function Pe(t,e,r){return e=ke(e),function(t,e){if(e&&("object"===we(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Ee()?Reflect.construct(e,r||[],ke(t).constructor):e.apply(t,r))}function Ee(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(Ee=function(){return!!t})()}function ke(t){return ke=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},ke(t)}function Me(t,e){return Me=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Me(t,e)}function _e(t){return function(t){if(Array.isArray(t))return Ce(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||Te(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Te(t,e){if(t){if("string"===typeof t)return Ce(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Ce(t,e):void 0}}function Ce(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Ie(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function De(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Ie(Object(r),!0).forEach(function(e){Ne(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ie(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function Ne(t,e,r){return(e=Be(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Be(t){var e=function(t,e){if("object"!=we(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=we(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==we(e)?e:e+""}var Re={xAxis:["bottom","top"],yAxis:["left","right"]},Le={width:"100%",height:"100%"},ze={x:0,y:0};function Ue(t){return t}var Fe=function(t,e){var r=e.graphicalItems,n=e.dataStartIndex,o=e.dataEndIndex,i=(null!==r&&void 0!==r?r:[]).reduce(function(t,e){var r=e.props.data;return r&&r.length?[].concat(_e(t),_e(r)):t},[]);return i.length>0?i:t&&t.length&&(0,k.Et)(n)&&(0,k.Et)(o)?t.slice(n,o+1):[]};function $e(t){return"number"===t?[0,"auto"]:void 0}var We=function(t,e,r,n){var o=t.graphicalItems,i=t.tooltipAxis,a=Fe(e,t);return r<0||!o||!o.length||r>=a.length?null:o.reduce(function(o,u){var c,l,s=null!==(c=u.props.data)&&void 0!==c?c:e;if(s&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=r&&(s=s.slice(t.dataStartIndex,t.dataEndIndex+1)),i.dataKey&&!i.allowDuplicatedCategory){var f=void 0===s?a:s;l=(0,k.eP)(f,i.dataKey,n)}else l=s&&s[r]||a[r];return l?[].concat(_e(o),[(0,E.zb)(u,l)]):o},[])},qe=function(t,e,r,n){var o=n||{x:t.chartX,y:t.chartY},i=function(t,e){return"horizontal"===e?t.x:"vertical"===e?t.y:"centric"===e?t.angle:t.radius}(o,r),a=t.orderedTooltipTicks,u=t.tooltipAxis,c=t.tooltipTicks,l=(0,E.gH)(i,a,c,u);if(l>=0&&c){var s=c[l]&&c[l].value,f=We(t,e,l,s),p=function(t,e,r,n){var o=e.find(function(t){return t&&t.index===r});if(o){if("horizontal"===t)return{x:o.coordinate,y:n.y};if("vertical"===t)return{x:n.x,y:o.coordinate};if("centric"===t){var i=o.coordinate,a=n.radius;return De(De(De({},n),(0,qt.IZ)(n.cx,n.cy,a,i)),{},{angle:i,radius:a})}var u=o.coordinate,c=n.angle;return De(De(De({},n),(0,qt.IZ)(n.cx,n.cy,u,c)),{},{angle:c,radius:u})}return ze}(r,a,l,o);return{activeTooltipIndex:l,activeLabel:s,activePayload:f,activeCoordinate:p}}return null},Xe=function(t,e){var r=e.axes,n=e.graphicalItems,o=e.axisType,a=e.axisIdKey,u=e.stackGroups,c=e.dataStartIndex,s=e.dataEndIndex,f=t.layout,p=t.children,h=t.stackOffset,d=(0,E._L)(f,o);return r.reduce(function(e,r){var y,v=void 0!==r.type.defaultProps?De(De({},r.type.defaultProps),r.props):r.props,m=v.type,b=v.dataKey,g=v.allowDataOverflow,x=v.allowDuplicatedCategory,w=v.scale,O=v.ticks,j=v.includeHidden,S=v[a];if(e[S])return e;var A,P,M,_=Fe(t.data,{graphicalItems:n.filter(function(t){var e;return(a in t.props?t.props[a]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[a])===S}),dataStartIndex:c,dataEndIndex:s}),T=_.length;(function(t,e,r){if("number"===r&&!0===e&&Array.isArray(t)){var n=null===t||void 0===t?void 0:t[0],o=null===t||void 0===t?void 0:t[1];if(n&&o&&(0,k.Et)(n)&&(0,k.Et)(o))return!0}return!1})(v.domain,g,m)&&(A=(0,E.AQ)(v.domain,null,g),!d||"number"!==m&&"auto"===w||(M=(0,E.Ay)(_,b,"category")));var C=$e(m);if(!A||0===A.length){var I,D=null!==(I=v.domain)&&void 0!==I?I:C;if(b){if(A=(0,E.Ay)(_,b,m),"category"===m&&d){var N=(0,k.CG)(A);x&&N?(P=A,A=l()(0,T)):x||(A=(0,E.KC)(D,A,r).reduce(function(t,e){return t.indexOf(e)>=0?t:[].concat(_e(t),[e])},[]))}else if("category"===m)A=x?A.filter(function(t){return""!==t&&!i()(t)}):(0,E.KC)(D,A,r).reduce(function(t,e){return t.indexOf(e)>=0||""===e||i()(e)?t:[].concat(_e(t),[e])},[]);else if("number"===m){var B=(0,E.A1)(_,n.filter(function(t){var e,r,n=a in t.props?t.props[a]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[a],o="hide"in t.props?t.props.hide:null===(r=t.type.defaultProps)||void 0===r?void 0:r.hide;return n===S&&(j||!o)}),b,o,f);B&&(A=B)}!d||"number"!==m&&"auto"===w||(M=(0,E.Ay)(_,b,"category"))}else A=d?l()(0,T):u&&u[S]&&u[S].hasStack&&"number"===m?"expand"===h?[0,1]:(0,E.Mk)(u[S].stackGroups,c,s):(0,E.vf)(_,n.filter(function(t){var e=a in t.props?t.props[a]:t.type.defaultProps[a],r="hide"in t.props?t.props.hide:t.type.defaultProps.hide;return e===S&&(j||!r)}),m,f,!0);if("number"===m)A=Wt(p,A,S,o,O),D&&(A=(0,E.AQ)(D,A,g));else if("category"===m&&D){var R=D;A.every(function(t){return R.indexOf(t)>=0})&&(A=R)}}return De(De({},e),{},Ne({},S,De(De({},v),{},{axisType:o,domain:A,categoricalDomain:M,duplicateDomain:P,originalDomain:null!==(y=v.domain)&&void 0!==y?y:C,isCategorical:d,layout:f})))},{})},He=function(t,e){var r=e.axisType,n=void 0===r?"xAxis":r,o=e.AxisComp,i=e.graphicalItems,a=e.stackGroups,u=e.dataStartIndex,c=e.dataEndIndex,s=t.children,p="".concat(n,"Id"),h=(0,S.aS)(s,o),d={};return h&&h.length?d=Xe(t,{axes:h,graphicalItems:i,axisType:n,axisIdKey:p,stackGroups:a,dataStartIndex:u,dataEndIndex:c}):i&&i.length&&(d=function(t,e){var r=e.graphicalItems,n=e.Axis,o=e.axisType,i=e.axisIdKey,a=e.stackGroups,u=e.dataStartIndex,c=e.dataEndIndex,s=t.layout,p=t.children,h=Fe(t.data,{graphicalItems:r,dataStartIndex:u,dataEndIndex:c}),d=h.length,y=(0,E._L)(s,o),v=-1;return r.reduce(function(t,e){var m,b=(void 0!==e.type.defaultProps?De(De({},e.type.defaultProps),e.props):e.props)[i],g=$e("number");return t[b]?t:(v++,y?m=l()(0,d):a&&a[b]&&a[b].hasStack?(m=(0,E.Mk)(a[b].stackGroups,u,c),m=Wt(p,m,b,o)):(m=(0,E.AQ)(g,(0,E.vf)(h,r.filter(function(t){var e,r,n=i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i],o="hide"in t.props?t.props.hide:null===(r=t.type.defaultProps)||void 0===r?void 0:r.hide;return n===b&&!o}),"number",s),n.defaultProps.allowDataOverflow),m=Wt(p,m,b,o)),De(De({},t),{},Ne({},b,De(De({axisType:o},n.defaultProps),{},{hide:!0,orientation:f()(Re,"".concat(o,".").concat(v%2),null),domain:m,originalDomain:g,isCategorical:y,layout:s}))))},{})}(t,{Axis:o,graphicalItems:i,axisType:n,axisIdKey:p,stackGroups:a,dataStartIndex:u,dataEndIndex:c})),d},Ge=function(t){var e=t.children,r=t.defaultShowTooltip,n=(0,S.BU)(e,H),o=0,i=0;return t.data&&0!==t.data.length&&(i=t.data.length-1),n&&n.props&&(n.props.startIndex>=0&&(o=n.props.startIndex),n.props.endIndex>=0&&(i=n.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:o,dataEndIndex:i,activeTooltipIndex:-1,isTooltipActive:Boolean(r)}},Ve=function(t){return"horizontal"===t?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===t?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===t?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},Ke=function(t,e){return"xAxis"===e?t[e].width:"yAxis"===e?t[e].height:void 0},Ye=function(t){var e=t.chartName,r=t.GraphicalChild,o=t.defaultTooltipEventType,a=void 0===o?"axis":o,c=t.validateTooltipEventTypes,l=void 0===c?["axis"]:c,s=t.axisComponents,p=t.legendContent,d=t.formatAxisMap,A=t.defaultProps,P=function(t,e){var r=e.graphicalItems,n=e.stackGroups,o=e.offset,a=e.updateId,u=e.dataStartIndex,c=e.dataEndIndex,l=t.barSize,f=t.layout,p=t.barGap,h=t.barCategoryGap,d=t.maxBarSize,y=Ve(f),v=y.numericAxisName,b=y.cateAxisName,g=function(t){return!(!t||!t.length)&&t.some(function(t){var e=(0,S.Mn)(t&&t.type);return e&&e.indexOf("Bar")>=0})}(r),x=[];return r.forEach(function(r,y){var w=Fe(t.data,{graphicalItems:[r],dataStartIndex:u,dataEndIndex:c}),O=void 0!==r.type.defaultProps?De(De({},r.type.defaultProps),r.props):r.props,j=O.dataKey,A=O.maxBarSize,P=O["".concat(v,"Id")],k=O["".concat(b,"Id")],M=s.reduce(function(t,r){var n=e["".concat(r.axisType,"Map")],o=O["".concat(r.axisType,"Id")];n&&n[o]||"zAxis"===r.axisType||(0,m.A)(!1);var i=n[o];return De(De({},t),{},Ne(Ne({},r.axisType,i),"".concat(r.axisType,"Ticks"),(0,E.Rh)(i)))},{}),_=M[b],T=M["".concat(b,"Ticks")],C=n&&n[P]&&n[P].hasStack&&(0,E.kA)(r,n[P].stackGroups),I=(0,S.Mn)(r.type).indexOf("Bar")>=0,D=(0,E.Hj)(_,T),N=[],B=g&&(0,E.tA)({barSize:l,stackGroups:n,totalSize:Ke(M,b)});if(I){var R,L,z=i()(A)?d:A,U=null!==(R=null!==(L=(0,E.Hj)(_,T,!0))&&void 0!==L?L:z)&&void 0!==R?R:0;N=(0,E.BX)({barGap:p,barCategoryGap:h,bandSize:U!==D?U:D,sizeList:B[k],maxBarSize:z}),U!==D&&(N=N.map(function(t){return De(De({},t),{},{position:De(De({},t.position),{},{offset:t.position.offset-U/2})})}))}var F=r&&r.type&&r.type.getComposedData;F&&x.push({props:De(De({},F(De(De({},M),{},{displayedData:w,props:t,dataKey:j,item:r,bandSize:D,barPosition:N,offset:o,stackedData:C,layout:f,dataStartIndex:u,dataEndIndex:c}))),{},Ne(Ne(Ne({key:r.key||"item-".concat(y)},v,M[v]),b,M[b]),"animationId",a)),childIndex:(0,S.AW)(r,t.children),item:r})}),x},M=function(t,n){var o=t.props,i=t.dataStartIndex,a=t.dataEndIndex,u=t.updateId;if(!(0,S.Me)({props:o}))return null;var c=o.children,l=o.layout,p=o.stackOffset,y=o.data,v=o.reverseStackOrder,m=Ve(l),b=m.numericAxisName,g=m.cateAxisName,x=(0,S.aS)(c,r),O=(0,E.Mn)(y,x,"".concat(b,"Id"),"".concat(g,"Id"),p,v),j=s.reduce(function(t,e){var r="".concat(e.axisType,"Map");return De(De({},t),{},Ne({},r,He(o,De(De({},e),{},{graphicalItems:x,stackGroups:e.axisType===b&&O,dataStartIndex:i,dataEndIndex:a}))))},{}),A=function(t,e){var r=t.props,n=t.graphicalItems,o=t.xAxisMap,i=void 0===o?{}:o,a=t.yAxisMap,u=void 0===a?{}:a,c=r.width,l=r.height,s=r.children,p=r.margin||{},h=(0,S.BU)(s,H),d=(0,S.BU)(s,w.s),y=Object.keys(u).reduce(function(t,e){var r=u[e],n=r.orientation;return r.mirror||r.hide?t:De(De({},t),{},Ne({},n,t[n]+r.width))},{left:p.left||0,right:p.right||0}),v=Object.keys(i).reduce(function(t,e){var r=i[e],n=r.orientation;return r.mirror||r.hide?t:De(De({},t),{},Ne({},n,f()(t,"".concat(n))+r.height))},{top:p.top||0,bottom:p.bottom||0}),m=De(De({},v),y),b=m.bottom;h&&(m.bottom+=h.props.height||H.defaultProps.height),d&&e&&(m=(0,E.s0)(m,n,r,e));var g=c-m.left-m.right,x=l-m.top-m.bottom;return De(De({brushBottom:b},m),{},{width:Math.max(g,0),height:Math.max(x,0)})}(De(De({},j),{},{props:o,graphicalItems:x}),null===n||void 0===n?void 0:n.legendBBox);Object.keys(j).forEach(function(t){j[t]=d(o,j[t],A,t.replace("Map",""),e)});var M=function(t){var e=(0,k.lX)(t),r=(0,E.Rh)(e,!1,!0);return{tooltipTicks:r,orderedTooltipTicks:h()(r,function(t){return t.coordinate}),tooltipAxis:e,tooltipAxisBandSize:(0,E.Hj)(e,r)}}(j["".concat(g,"Map")]),_=P(o,De(De({},j),{},{dataStartIndex:i,dataEndIndex:a,updateId:u,graphicalItems:x,stackGroups:O,offset:A}));return De(De({formattedGraphicalItems:_,graphicalItems:x,offset:A,stackGroups:O},M),j)},_=function(t){function r(t){var o,a,c;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,r),Ne(c=Pe(this,r,[t]),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),Ne(c,"accessibilityManager",new te),Ne(c,"handleLegendBBoxUpdate",function(t){if(t){var e=c.state,r=e.dataStartIndex,n=e.dataEndIndex,o=e.updateId;c.setState(De({legendBBox:t},M({props:c.props,dataStartIndex:r,dataEndIndex:n,updateId:o},De(De({},c.state),{},{legendBBox:t}))))}}),Ne(c,"handleReceiveSyncEvent",function(t,e,r){if(c.props.syncId===t){if(r===c.eventEmitterSymbol&&"function"!==typeof c.props.syncMethod)return;c.applySyncEvent(e)}}),Ne(c,"handleBrushChange",function(t){var e=t.startIndex,r=t.endIndex;if(e!==c.state.dataStartIndex||r!==c.state.dataEndIndex){var n=c.state.updateId;c.setState(function(){return De({dataStartIndex:e,dataEndIndex:r},M({props:c.props,dataStartIndex:e,dataEndIndex:r,updateId:n},c.state))}),c.triggerSyncEvent({dataStartIndex:e,dataEndIndex:r})}}),Ne(c,"handleMouseEnter",function(t){var e=c.getMouseInfo(t);if(e){var r=De(De({},e),{},{isTooltipActive:!0});c.setState(r),c.triggerSyncEvent(r);var n=c.props.onMouseEnter;u()(n)&&n(r,t)}}),Ne(c,"triggeredAfterMouseMove",function(t){var e=c.getMouseInfo(t),r=e?De(De({},e),{},{isTooltipActive:!0}):{isTooltipActive:!1};c.setState(r),c.triggerSyncEvent(r);var n=c.props.onMouseMove;u()(n)&&n(r,t)}),Ne(c,"handleItemMouseEnter",function(t){c.setState(function(){return{isTooltipActive:!0,activeItem:t,activePayload:t.tooltipPayload,activeCoordinate:t.tooltipPosition||{x:t.cx,y:t.cy}}})}),Ne(c,"handleItemMouseLeave",function(){c.setState(function(){return{isTooltipActive:!1}})}),Ne(c,"handleMouseMove",function(t){t.persist(),c.throttleTriggeredAfterMouseMove(t)}),Ne(c,"handleMouseLeave",function(t){c.throttleTriggeredAfterMouseMove.cancel();var e={isTooltipActive:!1};c.setState(e),c.triggerSyncEvent(e);var r=c.props.onMouseLeave;u()(r)&&r(e,t)}),Ne(c,"handleOuterEvent",function(t){var e,r=(0,S.X_)(t),n=f()(c.props,"".concat(r));r&&u()(n)&&n(null!==(e=/.*touch.*/i.test(r)?c.getMouseInfo(t.changedTouches[0]):c.getMouseInfo(t))&&void 0!==e?e:{},t)}),Ne(c,"handleClick",function(t){var e=c.getMouseInfo(t);if(e){var r=De(De({},e),{},{isTooltipActive:!0});c.setState(r),c.triggerSyncEvent(r);var n=c.props.onClick;u()(n)&&n(r,t)}}),Ne(c,"handleMouseDown",function(t){var e=c.props.onMouseDown;u()(e)&&e(c.getMouseInfo(t),t)}),Ne(c,"handleMouseUp",function(t){var e=c.props.onMouseUp;u()(e)&&e(c.getMouseInfo(t),t)}),Ne(c,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.throttleTriggeredAfterMouseMove(t.changedTouches[0])}),Ne(c,"handleTouchStart",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.handleMouseDown(t.changedTouches[0])}),Ne(c,"handleTouchEnd",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.handleMouseUp(t.changedTouches[0])}),Ne(c,"handleDoubleClick",function(t){var e=c.props.onDoubleClick;u()(e)&&e(c.getMouseInfo(t),t)}),Ne(c,"handleContextMenu",function(t){var e=c.props.onContextMenu;u()(e)&&e(c.getMouseInfo(t),t)}),Ne(c,"triggerSyncEvent",function(t){void 0!==c.props.syncId&&Gt.emit(Vt,c.props.syncId,t,c.eventEmitterSymbol)}),Ne(c,"applySyncEvent",function(t){var e=c.props,r=e.layout,n=e.syncMethod,o=c.state.updateId,i=t.dataStartIndex,a=t.dataEndIndex;if(void 0!==t.dataStartIndex||void 0!==t.dataEndIndex)c.setState(De({dataStartIndex:i,dataEndIndex:a},M({props:c.props,dataStartIndex:i,dataEndIndex:a,updateId:o},c.state)));else if(void 0!==t.activeTooltipIndex){var u=t.chartX,l=t.chartY,s=t.activeTooltipIndex,f=c.state,p=f.offset,h=f.tooltipTicks;if(!p)return;if("function"===typeof n)s=n(h,t);else if("value"===n){s=-1;for(var d=0;d<h.length;d++)if(h[d].value===t.activeLabel){s=d;break}}var y=De(De({},p),{},{x:p.left,y:p.top}),v=Math.min(u,y.x+y.width),m=Math.min(l,y.y+y.height),b=h[s]&&h[s].value,g=We(c.state,c.props.data,s),x=h[s]?{x:"horizontal"===r?h[s].coordinate:v,y:"horizontal"===r?m:h[s].coordinate}:ze;c.setState(De(De({},t),{},{activeLabel:b,activeCoordinate:x,activePayload:g,activeTooltipIndex:s}))}else c.setState(t)}),Ne(c,"renderCursor",function(t){var r,o=c.state,i=o.isTooltipActive,a=o.activeCoordinate,u=o.activePayload,l=o.offset,s=o.activeTooltipIndex,f=o.tooltipAxisBandSize,p=c.getTooltipEventType(),h=null!==(r=t.props.active)&&void 0!==r?r:i,d=c.props.layout,y=t.key||"_recharts-cursor";return n.createElement(be,{key:y,activeCoordinate:a,activePayload:u,activeTooltipIndex:s,chartName:e,element:t,isActive:h,layout:d,offset:l,tooltipAxisBandSize:f,tooltipEventType:p})}),Ne(c,"renderPolarAxis",function(t,e,r){var o=f()(t,"type.axisType"),i=f()(c.state,"".concat(o,"Map")),a=t.type.defaultProps,u=void 0!==a?De(De({},a),t.props):t.props,l=i&&i[u["".concat(o,"Id")]];return(0,n.cloneElement)(t,De(De({},l),{},{className:(0,v.A)(o,l.className),key:t.key||"".concat(e,"-").concat(r),ticks:(0,E.Rh)(l,!0)}))}),Ne(c,"renderPolarGrid",function(t){var e=t.props,r=e.radialLines,o=e.polarAngles,i=e.polarRadius,a=c.state,u=a.radiusAxisMap,l=a.angleAxisMap,s=(0,k.lX)(u),f=(0,k.lX)(l),p=f.cx,h=f.cy,d=f.innerRadius,y=f.outerRadius;return(0,n.cloneElement)(t,{polarAngles:Array.isArray(o)?o:(0,E.Rh)(f,!0).map(function(t){return t.coordinate}),polarRadius:Array.isArray(i)?i:(0,E.Rh)(s,!0).map(function(t){return t.coordinate}),cx:p,cy:h,innerRadius:d,outerRadius:y,key:t.key||"polar-grid",radialLines:r})}),Ne(c,"renderLegend",function(){var t=c.state.formattedGraphicalItems,e=c.props,r=e.children,o=e.width,i=e.height,a=c.props.margin||{},u=o-(a.left||0)-(a.right||0),l=(0,V.g)({children:r,formattedGraphicalItems:t,legendWidth:u,legendContent:p});if(!l)return null;var s=l.item,f=Se(l,ge);return(0,n.cloneElement)(s,De(De({},f),{},{chartWidth:o,chartHeight:i,margin:a,onBBoxUpdate:c.handleLegendBBoxUpdate}))}),Ne(c,"renderTooltip",function(){var t,e=c.props,r=e.children,o=e.accessibilityLayer,i=(0,S.BU)(r,x.m);if(!i)return null;var a=c.state,u=a.isTooltipActive,l=a.activeCoordinate,s=a.activePayload,f=a.activeLabel,p=a.offset,h=null!==(t=i.props.active)&&void 0!==t?t:u;return(0,n.cloneElement)(i,{viewBox:De(De({},p),{},{x:p.left,y:p.top}),active:h,label:f,payload:h?s:[],coordinate:l,accessibilityLayer:o})}),Ne(c,"renderBrush",function(t){var e=c.props,r=e.margin,o=e.data,i=c.state,a=i.offset,u=i.dataStartIndex,l=i.dataEndIndex,s=i.updateId;return(0,n.cloneElement)(t,{key:t.key||"_recharts-brush",onChange:(0,E.HQ)(c.handleBrushChange,t.props.onChange),data:o,x:(0,k.Et)(t.props.x)?t.props.x:a.left,y:(0,k.Et)(t.props.y)?t.props.y:a.top+a.height+a.brushBottom-(r.bottom||0),width:(0,k.Et)(t.props.width)?t.props.width:a.width,startIndex:u,endIndex:l,updateId:"brush-".concat(s)})}),Ne(c,"renderReferenceElement",function(t,e,r){if(!t)return null;var o=c.clipPathId,i=c.state,a=i.xAxisMap,u=i.yAxisMap,l=i.offset,s=t.type.defaultProps||{},f=t.props,p=f.xAxisId,h=void 0===p?s.xAxisId:p,d=f.yAxisId,y=void 0===d?s.yAxisId:d;return(0,n.cloneElement)(t,{key:t.key||"".concat(e,"-").concat(r),xAxis:a[h],yAxis:u[y],viewBox:{x:l.left,y:l.top,width:l.width,height:l.height},clipPathId:o})}),Ne(c,"renderActivePoints",function(t){var e=t.item,n=t.activePoint,o=t.basePoint,i=t.childIndex,a=t.isRange,u=[],c=e.props.key,l=void 0!==e.item.type.defaultProps?De(De({},e.item.type.defaultProps),e.item.props):e.item.props,s=l.activeDot,f=De(De({index:i,dataKey:l.dataKey,cx:n.x,cy:n.y,r:4,fill:(0,E.Ps)(e.item),strokeWidth:2,stroke:"#fff",payload:n.payload,value:n.value},(0,S.J9)(s,!1)),(0,Kt._U)(s));return u.push(r.renderActiveDot(s,f,"".concat(c,"-activePoint-").concat(i))),o?u.push(r.renderActiveDot(s,De(De({},f),{},{cx:o.x,cy:o.y}),"".concat(c,"-basePoint-").concat(i))):a&&u.push(null),u}),Ne(c,"renderGraphicChild",function(t,e,r){var o=c.filterFormatItem(t,e,r);if(!o)return null;var a=c.getTooltipEventType(),u=c.state,l=u.isTooltipActive,s=u.tooltipAxis,f=u.activeTooltipIndex,p=u.activeLabel,h=c.props.children,d=(0,S.BU)(h,x.m),y=o.props,v=y.points,m=y.isRange,b=y.baseLine,g=void 0!==o.item.type.defaultProps?De(De({},o.item.type.defaultProps),o.item.props):o.item.props,w=g.activeDot,O=g.hide,j=g.activeBar,A=g.activeShape,P=Boolean(!O&&l&&d&&(w||j||A)),M={};"axis"!==a&&d&&"click"===d.props.trigger?M={onClick:(0,E.HQ)(c.handleItemMouseEnter,t.props.onClick)}:"axis"!==a&&(M={onMouseLeave:(0,E.HQ)(c.handleItemMouseLeave,t.props.onMouseLeave),onMouseEnter:(0,E.HQ)(c.handleItemMouseEnter,t.props.onMouseEnter)});var _=(0,n.cloneElement)(t,De(De({},o.props),M));if(P){if(!(f>=0)){var T,C=(null!==(T=c.getItemByXY(c.state.activeCoordinate))&&void 0!==T?T:{graphicalItem:_}).graphicalItem,I=C.item,D=void 0===I?t:I,N=C.childIndex,B=De(De(De({},o.props),M),{},{activeIndex:N});return[(0,n.cloneElement)(D,B),null,null]}var R,L;if(s.dataKey&&!s.allowDuplicatedCategory){var z="function"===typeof s.dataKey?function(t){return"function"===typeof s.dataKey?s.dataKey(t.payload):null}:"payload.".concat(s.dataKey.toString());R=(0,k.eP)(v,z,p),L=m&&b&&(0,k.eP)(b,z,p)}else R=null===v||void 0===v?void 0:v[f],L=m&&b&&b[f];if(A||j){var U=void 0!==t.props.activeIndex?t.props.activeIndex:f;return[(0,n.cloneElement)(t,De(De(De({},o.props),M),{},{activeIndex:U})),null,null]}if(!i()(R))return[_].concat(_e(c.renderActivePoints({item:o,activePoint:R,basePoint:L,childIndex:f,isRange:m})))}return m?[_,null,null]:[_,null]}),Ne(c,"renderCustomized",function(t,e,r){return(0,n.cloneElement)(t,De(De({key:"recharts-customized-".concat(r)},c.props),c.state))}),Ne(c,"renderMap",{CartesianGrid:{handler:Ue,once:!0},ReferenceArea:{handler:c.renderReferenceElement},ReferenceLine:{handler:Ue},ReferenceDot:{handler:c.renderReferenceElement},XAxis:{handler:Ue},YAxis:{handler:Ue},Brush:{handler:c.renderBrush,once:!0},Bar:{handler:c.renderGraphicChild},Line:{handler:c.renderGraphicChild},Area:{handler:c.renderGraphicChild},Radar:{handler:c.renderGraphicChild},RadialBar:{handler:c.renderGraphicChild},Scatter:{handler:c.renderGraphicChild},Pie:{handler:c.renderGraphicChild},Funnel:{handler:c.renderGraphicChild},Tooltip:{handler:c.renderCursor,once:!0},PolarGrid:{handler:c.renderPolarGrid,once:!0},PolarAngleAxis:{handler:c.renderPolarAxis},PolarRadiusAxis:{handler:c.renderPolarAxis},Customized:{handler:c.renderCustomized}}),c.clipPathId="".concat(null!==(o=t.id)&&void 0!==o?o:(0,k.NF)("recharts"),"-clip"),c.throttleTriggeredAfterMouseMove=y()(c.triggeredAfterMouseMove,null!==(a=t.throttleDelay)&&void 0!==a?a:1e3/60),c.state={},c}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Me(t,e)}(r,t),o=r,c=[{key:"componentDidMount",value:function(){var t,e;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!==(t=this.props.margin.left)&&void 0!==t?t:0,top:null!==(e=this.props.margin.top)&&void 0!==e?e:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var t=this.props,e=t.children,r=t.data,n=t.height,o=t.layout,i=(0,S.BU)(e,x.m);if(i){var a=i.props.defaultIndex;if(!("number"!==typeof a||a<0||a>this.state.tooltipTicks.length-1)){var u=this.state.tooltipTicks[a]&&this.state.tooltipTicks[a].value,c=We(this.state,r,a,u),l=this.state.tooltipTicks[a].coordinate,s=(this.state.offset.top+n)/2,f="horizontal"===o?{x:l,y:s}:{y:l,x:s},p=this.state.formattedGraphicalItems.find(function(t){return"Scatter"===t.item.type.name});p&&(f=De(De({},f),p.props.points[a].tooltipPosition),c=p.props.points[a].tooltipPayload);var h={activeTooltipIndex:a,isTooltipActive:!0,activeLabel:u,activePayload:c,activeCoordinate:f};this.setState(h),this.renderCursor(i),this.accessibilityManager.setIndex(a)}}}},{key:"getSnapshotBeforeUpdate",value:function(t,e){return this.props.accessibilityLayer?(this.state.tooltipTicks!==e.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==t.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==t.margin&&this.accessibilityManager.setDetails({offset:{left:null!==(r=this.props.margin.left)&&void 0!==r?r:0,top:null!==(n=this.props.margin.top)&&void 0!==n?n:0}}),null):null;var r,n}},{key:"componentDidUpdate",value:function(t){(0,S.OV)([(0,S.BU)(t.children,x.m)],[(0,S.BU)(this.props.children,x.m)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var t=(0,S.BU)(this.props.children,x.m);if(t&&"boolean"===typeof t.props.shared){var e=t.props.shared?"axis":"item";return l.indexOf(e)>=0?e:a}return a}},{key:"getMouseInfo",value:function(t){if(!this.container)return null;var e=this.container,r=e.getBoundingClientRect(),n=(0,G.A3)(r),o={chartX:Math.round(t.pageX-n.left),chartY:Math.round(t.pageY-n.top)},i=r.width/e.offsetWidth||1,a=this.inRange(o.chartX,o.chartY,i);if(!a)return null;var u=this.state,c=u.xAxisMap,l=u.yAxisMap,s=this.getTooltipEventType(),f=qe(this.state,this.props.data,this.props.layout,a);if("axis"!==s&&c&&l){var p=(0,k.lX)(c).scale,h=(0,k.lX)(l).scale,d=p&&p.invert?p.invert(o.chartX):null,y=h&&h.invert?h.invert(o.chartY):null;return De(De({},o),{},{xValue:d,yValue:y},f)}return f?De(De({},o),f):null}},{key:"inRange",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.props.layout,o=t/r,i=e/r;if("horizontal"===n||"vertical"===n){var a=this.state.offset;return o>=a.left&&o<=a.left+a.width&&i>=a.top&&i<=a.top+a.height?{x:o,y:i}:null}var u=this.state,c=u.angleAxisMap,l=u.radiusAxisMap;if(c&&l){var s=(0,k.lX)(c);return(0,qt.yy)({x:o,y:i},s)}return null}},{key:"parseEventsOfWrapper",value:function(){var t=this.props.children,e=this.getTooltipEventType(),r=(0,S.BU)(t,x.m),n={};return r&&"axis"===e&&(n="click"===r.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu}),De(De({},(0,Kt._U)(this.props,this.handleOuterEvent)),n)}},{key:"addListener",value:function(){Gt.on(Vt,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){Gt.removeListener(Vt,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(t,e,r){for(var n=this.state.formattedGraphicalItems,o=0,i=n.length;o<i;o++){var a=n[o];if(a.item===t||a.props.key===t.key||e===(0,S.Mn)(a.item.type)&&r===a.childIndex)return a}return null}},{key:"renderClipPath",value:function(){var t=this.clipPathId,e=this.state.offset,r=e.left,o=e.top,i=e.height,a=e.width;return n.createElement("defs",null,n.createElement("clipPath",{id:t},n.createElement("rect",{x:r,y:o,height:i,width:a})))}},{key:"getXScales",value:function(){var t=this.state.xAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=je(e,2),n=r[0],o=r[1];return De(De({},t),{},Ne({},n,o.scale))},{}):null}},{key:"getYScales",value:function(){var t=this.state.yAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=je(e,2),n=r[0],o=r[1];return De(De({},t),{},Ne({},n,o.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(t){var e;return null===(e=this.state.xAxisMap)||void 0===e||null===(e=e[t])||void 0===e?void 0:e.scale}},{key:"getYScaleByAxisId",value:function(t){var e;return null===(e=this.state.yAxisMap)||void 0===e||null===(e=e[t])||void 0===e?void 0:e.scale}},{key:"getItemByXY",value:function(t){var e=this.state,r=e.formattedGraphicalItems,n=e.activeItem;if(r&&r.length)for(var o=0,i=r.length;o<i;o++){var a=r[o],u=a.props,c=a.item,l=void 0!==c.type.defaultProps?De(De({},c.type.defaultProps),c.props):c.props,s=(0,S.Mn)(c.type);if("Bar"===s){var f=(u.data||[]).find(function(e){return(0,j.J)(t,e)});if(f)return{graphicalItem:a,payload:f}}else if("RadialBar"===s){var p=(u.data||[]).find(function(e){return(0,qt.yy)(t,e)});if(p)return{graphicalItem:a,payload:p}}else if((0,ee.NE)(a,n)||(0,ee.nZ)(a,n)||(0,ee.xQ)(a,n)){var h=(0,ee.GG)({graphicalItem:a,activeTooltipItem:n,itemData:l.data}),d=void 0===l.activeIndex?h:l.activeIndex;return{graphicalItem:De(De({},a),{},{childIndex:d}),payload:(0,ee.xQ)(a,n)?l.data[h]:a.props.data[h]}}}return null}},{key:"render",value:function(){var t=this;if(!(0,S.Me)(this))return null;var e,r,o=this.props,i=o.children,a=o.className,u=o.width,c=o.height,l=o.style,s=o.compact,f=o.title,p=o.desc,h=Se(o,xe),d=(0,S.J9)(h,!1);if(s)return n.createElement(ht.DR,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},n.createElement(b.u,Oe({},d,{width:u,height:c,title:f,desc:p}),this.renderClipPath(),(0,S.ee)(i,this.renderMap)));this.props.accessibilityLayer&&(d.tabIndex=null!==(e=this.props.tabIndex)&&void 0!==e?e:0,d.role=null!==(r=this.props.role)&&void 0!==r?r:"application",d.onKeyDown=function(e){t.accessibilityManager.keyboardEvent(e)},d.onFocus=function(){t.accessibilityManager.focus()});var y=this.parseEventsOfWrapper();return n.createElement(ht.DR,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},n.createElement("div",Oe({className:(0,v.A)("recharts-wrapper",a),style:De({position:"relative",cursor:"default",width:u,height:c},l)},y,{ref:function(e){t.container=e}}),n.createElement(b.u,Oe({},d,{width:u,height:c,title:f,desc:p,style:Le}),this.renderClipPath(),(0,S.ee)(i,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}],c&&Ae(o.prototype,c),s&&Ae(o,s),Object.defineProperty(o,"prototype",{writable:!1}),o;var o,c,s}(n.Component);Ne(_,"displayName",e),Ne(_,"defaultProps",De({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},A)),Ne(_,"getDerivedStateFromProps",function(t,e){var r=t.dataKey,n=t.data,o=t.children,a=t.width,u=t.height,c=t.layout,l=t.stackOffset,s=t.margin,f=e.dataStartIndex,p=e.dataEndIndex;if(void 0===e.updateId){var h=Ge(t);return De(De(De({},h),{},{updateId:0},M(De(De({props:t},h),{},{updateId:0}),e)),{},{prevDataKey:r,prevData:n,prevWidth:a,prevHeight:u,prevLayout:c,prevStackOffset:l,prevMargin:s,prevChildren:o})}if(r!==e.prevDataKey||n!==e.prevData||a!==e.prevWidth||u!==e.prevHeight||c!==e.prevLayout||l!==e.prevStackOffset||!(0,Xt.b)(s,e.prevMargin)){var d=Ge(t),y={chartX:e.chartX,chartY:e.chartY,isTooltipActive:e.isTooltipActive},v=De(De({},qe(e,n,c)),{},{updateId:e.updateId+1}),m=De(De(De({},d),y),v);return De(De(De({},m),M(De({props:t},m),e)),{},{prevDataKey:r,prevData:n,prevWidth:a,prevHeight:u,prevLayout:c,prevStackOffset:l,prevMargin:s,prevChildren:o})}if(!(0,S.OV)(o,e.prevChildren)){var b,g,x,w,O=(0,S.BU)(o,H),j=O&&null!==(b=null===(g=O.props)||void 0===g?void 0:g.startIndex)&&void 0!==b?b:f,A=O&&null!==(x=null===(w=O.props)||void 0===w?void 0:w.endIndex)&&void 0!==x?x:p,P=j!==f||A!==p,E=!i()(n)&&!P?e.updateId:e.updateId+1;return De(De({updateId:E},M(De(De({props:t},e),{},{updateId:E,dataStartIndex:j,dataEndIndex:A}),e)),{},{prevChildren:o,dataStartIndex:j,dataEndIndex:A})}return null}),Ne(_,"renderActiveDot",function(t,e,r){var o;return o=(0,n.isValidElement)(t)?(0,n.cloneElement)(t,e):u()(t)?t(e):n.createElement(O.c,e),n.createElement(g.W,{className:"recharts-active-dot",key:r},o)});var T=(0,n.forwardRef)(function(t,e){return n.createElement(_,Oe({},t,{ref:e}))});return T.displayName=_.displayName,T}},8468:(t,e,r)=>{var n=r(5816),o=r(644),i=r(4020);t.exports=function(t,e,r){return e===e?i(t,e,r):n(t,o,r)}},8471:(t,e,r)=>{"use strict";r.d(e,{I:()=>V});var n=r(5043);function o(){}function i(t,e,r){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+e)/6,(t._y0+4*t._y1+r)/6)}function a(t){this._context=t}function u(t){this._context=t}function c(t){this._context=t}a.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:i(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:i(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},u.prototype={areaStart:o,areaEnd:o,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._x2=t,this._y2=e;break;case 1:this._point=2,this._x3=t,this._y3=e;break;case 2:this._point=3,this._x4=t,this._y4=e,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+e)/6);break;default:i(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},c.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+t)/6,n=(this._y0+4*this._y1+e)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:i(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}};class l{constructor(t,e){this._context=t,this._x=e}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,e,t,e):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+e)/2,t,this._y0,t,e)}this._x0=t,this._y0=e}}function s(t){this._context=t}function f(t){this._context=t}function p(t){return new f(t)}function h(t){return t<0?-1:1}function d(t,e,r){var n=t._x1-t._x0,o=e-t._x1,i=(t._y1-t._y0)/(n||o<0&&-0),a=(r-t._y1)/(o||n<0&&-0),u=(i*o+a*n)/(n+o);return(h(i)+h(a))*Math.min(Math.abs(i),Math.abs(a),.5*Math.abs(u))||0}function y(t,e){var r=t._x1-t._x0;return r?(3*(t._y1-t._y0)/r-e)/2:e}function v(t,e,r){var n=t._x0,o=t._y0,i=t._x1,a=t._y1,u=(i-n)/3;t._context.bezierCurveTo(n+u,o+u*e,i-u,a-u*r,i,a)}function m(t){this._context=t}function b(t){this._context=new g(t)}function g(t){this._context=t}function x(t){this._context=t}function w(t){var e,r,n=t.length-1,o=new Array(n),i=new Array(n),a=new Array(n);for(o[0]=0,i[0]=2,a[0]=t[0]+2*t[1],e=1;e<n-1;++e)o[e]=1,i[e]=4,a[e]=4*t[e]+2*t[e+1];for(o[n-1]=2,i[n-1]=7,a[n-1]=8*t[n-1]+t[n],e=1;e<n;++e)r=o[e]/i[e-1],i[e]-=r,a[e]-=r*a[e-1];for(o[n-1]=a[n-1]/i[n-1],e=n-2;e>=0;--e)o[e]=(a[e]-o[e+1])/i[e];for(i[n-1]=(t[n]+o[n-1])/2,e=0;e<n-1;++e)i[e]=2*t[e+1]-o[e+1];return[o,i]}function O(t,e){this._context=t,this._t=e}s.prototype={areaStart:o,areaEnd:o,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,e){t=+t,e=+e,this._point?this._context.lineTo(t,e):(this._point=1,this._context.moveTo(t,e))}},f.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._context.lineTo(t,e)}}},m.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:v(this,this._t0,y(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){var r=NaN;if(e=+e,(t=+t)!==this._x1||e!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,v(this,y(this,r=d(this,t,e)),r);break;default:v(this,this._t0,r=d(this,t,e))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e,this._t0=r}}},(b.prototype=Object.create(m.prototype)).point=function(t,e){m.prototype.point.call(this,e,t)},g.prototype={moveTo:function(t,e){this._context.moveTo(e,t)},closePath:function(){this._context.closePath()},lineTo:function(t,e){this._context.lineTo(e,t)},bezierCurveTo:function(t,e,r,n,o,i){this._context.bezierCurveTo(e,t,n,r,i,o)}},x.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,e=this._y,r=t.length;if(r)if(this._line?this._context.lineTo(t[0],e[0]):this._context.moveTo(t[0],e[0]),2===r)this._context.lineTo(t[1],e[1]);else for(var n=w(t),o=w(e),i=0,a=1;a<r;++i,++a)this._context.bezierCurveTo(n[0][i],o[0][i],n[1][i],o[1][i],t[a],e[a]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,e){this._x.push(+t),this._y.push(+e)}},O.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,e),this._context.lineTo(t,e);else{var r=this._x*(1-this._t)+t*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,e)}}this._x=t,this._y=e}};var j=r(9236),S=r(3809),A=r(7371);function P(t){return t[0]}function E(t){return t[1]}function k(t,e){var r=(0,S.A)(!0),n=null,o=p,i=null,a=(0,A.i)(u);function u(u){var c,l,s,f=(u=(0,j.A)(u)).length,p=!1;for(null==n&&(i=o(s=a())),c=0;c<=f;++c)!(c<f&&r(l=u[c],c,u))===p&&((p=!p)?i.lineStart():i.lineEnd()),p&&i.point(+t(l,c,u),+e(l,c,u));if(s)return i=null,s+""||null}return t="function"===typeof t?t:void 0===t?P:(0,S.A)(t),e="function"===typeof e?e:void 0===e?E:(0,S.A)(e),u.x=function(e){return arguments.length?(t="function"===typeof e?e:(0,S.A)(+e),u):t},u.y=function(t){return arguments.length?(e="function"===typeof t?t:(0,S.A)(+t),u):e},u.defined=function(t){return arguments.length?(r="function"===typeof t?t:(0,S.A)(!!t),u):r},u.curve=function(t){return arguments.length?(o=t,null!=n&&(i=o(n)),u):o},u.context=function(t){return arguments.length?(null==t?n=i=null:i=o(n=t),u):n},u}function M(t,e,r){var n=null,o=(0,S.A)(!0),i=null,a=p,u=null,c=(0,A.i)(l);function l(l){var s,f,p,h,d,y=(l=(0,j.A)(l)).length,v=!1,m=new Array(y),b=new Array(y);for(null==i&&(u=a(d=c())),s=0;s<=y;++s){if(!(s<y&&o(h=l[s],s,l))===v)if(v=!v)f=s,u.areaStart(),u.lineStart();else{for(u.lineEnd(),u.lineStart(),p=s-1;p>=f;--p)u.point(m[p],b[p]);u.lineEnd(),u.areaEnd()}v&&(m[s]=+t(h,s,l),b[s]=+e(h,s,l),u.point(n?+n(h,s,l):m[s],r?+r(h,s,l):b[s]))}if(d)return u=null,d+""||null}function s(){return k().defined(o).curve(a).context(i)}return t="function"===typeof t?t:void 0===t?P:(0,S.A)(+t),e="function"===typeof e?e:void 0===e?(0,S.A)(0):(0,S.A)(+e),r="function"===typeof r?r:void 0===r?E:(0,S.A)(+r),l.x=function(e){return arguments.length?(t="function"===typeof e?e:(0,S.A)(+e),n=null,l):t},l.x0=function(e){return arguments.length?(t="function"===typeof e?e:(0,S.A)(+e),l):t},l.x1=function(t){return arguments.length?(n=null==t?null:"function"===typeof t?t:(0,S.A)(+t),l):n},l.y=function(t){return arguments.length?(e="function"===typeof t?t:(0,S.A)(+t),r=null,l):e},l.y0=function(t){return arguments.length?(e="function"===typeof t?t:(0,S.A)(+t),l):e},l.y1=function(t){return arguments.length?(r=null==t?null:"function"===typeof t?t:(0,S.A)(+t),l):r},l.lineX0=l.lineY0=function(){return s().x(t).y(e)},l.lineY1=function(){return s().x(t).y(r)},l.lineX1=function(){return s().x(n).y(e)},l.defined=function(t){return arguments.length?(o="function"===typeof t?t:(0,S.A)(!!t),l):o},l.curve=function(t){return arguments.length?(a=t,null!=i&&(u=a(i)),l):a},l.context=function(t){return arguments.length?(null==t?i=u=null:u=a(i=t),l):i},l}var _=r(643),T=r.n(_),C=r(1629),I=r.n(C),D=r(8387),N=r(7287),B=r(240),R=r(6307);function L(t){return L="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},L(t)}function z(){return z=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},z.apply(this,arguments)}function U(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function F(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?U(Object(r),!0).forEach(function(e){$(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):U(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function $(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=L(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=L(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==L(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var W={curveBasisClosed:function(t){return new u(t)},curveBasisOpen:function(t){return new c(t)},curveBasis:function(t){return new a(t)},curveBumpX:function(t){return new l(t,!0)},curveBumpY:function(t){return new l(t,!1)},curveLinearClosed:function(t){return new s(t)},curveLinear:p,curveMonotoneX:function(t){return new m(t)},curveMonotoneY:function(t){return new b(t)},curveNatural:function(t){return new x(t)},curveStep:function(t){return new O(t,.5)},curveStepAfter:function(t){return new O(t,1)},curveStepBefore:function(t){return new O(t,0)}},q=function(t){return t.x===+t.x&&t.y===+t.y},X=function(t){return t.x},H=function(t){return t.y},G=function(t){var e,r=t.type,n=void 0===r?"linear":r,o=t.points,i=void 0===o?[]:o,a=t.baseLine,u=t.layout,c=t.connectNulls,l=void 0!==c&&c,s=function(t,e){if(I()(t))return t;var r="curve".concat(T()(t));return"curveMonotone"!==r&&"curveBump"!==r||!e?W[r]||p:W["".concat(r).concat("vertical"===e?"Y":"X")]}(n,u),f=l?i.filter(function(t){return q(t)}):i;if(Array.isArray(a)){var h=l?a.filter(function(t){return q(t)}):a,d=f.map(function(t,e){return F(F({},t),{},{base:h[e]})});return(e="vertical"===u?M().y(H).x1(X).x0(function(t){return t.base.x}):M().x(X).y1(H).y0(function(t){return t.base.y})).defined(q).curve(s),e(d)}return(e="vertical"===u&&(0,R.Et)(a)?M().y(H).x1(X).x0(a):(0,R.Et)(a)?M().x(X).y1(H).y0(a):k().x(X).y(H)).defined(q).curve(s),e(f)},V=function(t){var e=t.className,r=t.points,o=t.path,i=t.pathRef;if((!r||!r.length)&&!o)return null;var a=r&&r.length?G(t):o;return n.createElement("path",z({},(0,B.J9)(t,!1),(0,N._U)(t),{className:(0,D.A)("recharts-curve",e),d:a,ref:i}))}},8541:(t,e,r)=>{var n=r(9812),o=r(149),i=r(4052),a=r(9841),u=n?n.prototype:void 0,c=u?u.toString:void 0;t.exports=function t(e){if("string"==typeof e)return e;if(i(e))return o(e,t)+"";if(a(e))return c?c.call(e):"";var r=e+"";return"0"==r&&1/e==-1/0?"-0":r}},8673:(t,e,r)=>{var n=r(3204),o=r(3713),i=r(6571);t.exports=function(t){return i(t)?n(t):o(t)}},8724:(t,e,r)=>{var n=r(7615),o=r(5051),i=r(2154),a=r(8734),u=r(2662);function c(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}c.prototype.clear=n,c.prototype.delete=o,c.prototype.get=i,c.prototype.has=a,c.prototype.set=u,t.exports=c},8734:(t,e,r)=>{var n=r(5575),o=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;return n?void 0!==e[t]:o.call(e,t)}},8763:(t,e,r)=>{"use strict";r.d(e,{y:()=>q});var n=r(5043),o=r(8387),i=r(1744),a=r(9853),u=r.n(a),c=r(9686),l=r.n(c),s=r(1639),f=r(8813),p=function(t){return null};p.displayName="Cell";var h=r(1519),d=r(6307),y=r(240),v=r(6015),m=r(202),b=r(7287),g=r(3404),x=r(879),w=["x","y"];function O(t){return O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},O(t)}function j(){return j=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},j.apply(this,arguments)}function S(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function A(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?S(Object(r),!0).forEach(function(e){P(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):S(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function P(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=O(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=O(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==O(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function E(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function k(t,e){var r=t.x,n=t.y,o=E(t,w),i="".concat(r),a=parseInt(i,10),u="".concat(n),c=parseInt(u,10),l="".concat(e.height||o.height),s=parseInt(l,10),f="".concat(e.width||o.width),p=parseInt(f,10);return A(A(A(A(A({},e),o),a?{x:a}:{}),c?{y:c}:{}),{},{height:s,width:p,name:e.name,radius:e.radius})}function M(t){return n.createElement(x.yp,j({shapeType:"rectangle",propTransformer:k,activeClassName:"recharts-active-bar"},t))}var _,T=["value","background"];function C(t){return C="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},C(t)}function I(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function D(){return D=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},D.apply(this,arguments)}function N(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function B(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?N(Object(r),!0).forEach(function(e){$(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):N(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function R(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,W(n.key),n)}}function L(t,e,r){return e=U(e),function(t,e){if(e&&("object"===C(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,z()?Reflect.construct(e,r||[],U(t).constructor):e.apply(t,r))}function z(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(z=function(){return!!t})()}function U(t){return U=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},U(t)}function F(t,e){return F=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},F(t,e)}function $(t,e,r){return(e=W(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function W(t){var e=function(t,e){if("object"!=C(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=C(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==C(e)?e:e+""}var q=function(t){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return $(t=L(this,e,[].concat(n)),"state",{isAnimationFinished:!1}),$(t,"id",(0,d.NF)("recharts-bar-")),$(t,"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),e&&e()}),$(t,"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),e&&e()}),t}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&F(t,e)}(e,t),r=e,c=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curData:t.data,prevData:e.curData}:t.data!==e.curData?{curData:t.data}:null}}],(a=[{key:"renderRectanglesStatically",value:function(t){var e=this,r=this.props,o=r.shape,i=r.dataKey,a=r.activeIndex,u=r.activeBar,c=(0,y.J9)(this.props,!1);return t&&t.map(function(t,r){var l=r===a,f=l?u:o,p=B(B(B({},c),t),{},{isActive:l,option:f,index:r,dataKey:i,onAnimationStart:e.handleAnimationStart,onAnimationEnd:e.handleAnimationEnd});return n.createElement(s.W,D({className:"recharts-bar-rectangle"},(0,b.XC)(e.props,t,r),{key:"rectangle-".concat(null===t||void 0===t?void 0:t.x,"-").concat(null===t||void 0===t?void 0:t.y,"-").concat(null===t||void 0===t?void 0:t.value,"-").concat(r)}),n.createElement(M,p))})}},{key:"renderRectanglesWithAnimation",value:function(){var t=this,e=this.props,r=e.data,o=e.layout,a=e.isAnimationActive,u=e.animationBegin,c=e.animationDuration,l=e.animationEasing,f=e.animationId,p=this.state.prevData;return n.createElement(i.Ay,{begin:u,duration:c,isActive:a,easing:l,from:{t:0},to:{t:1},key:"bar-".concat(f),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(e){var i=e.t,a=r.map(function(t,e){var r=p&&p[e];if(r){var n=(0,d.Dj)(r.x,t.x),a=(0,d.Dj)(r.y,t.y),u=(0,d.Dj)(r.width,t.width),c=(0,d.Dj)(r.height,t.height);return B(B({},t),{},{x:n(i),y:a(i),width:u(i),height:c(i)})}if("horizontal"===o){var l=(0,d.Dj)(0,t.height)(i);return B(B({},t),{},{y:t.y+t.height-l,height:l})}var s=(0,d.Dj)(0,t.width)(i);return B(B({},t),{},{width:s})});return n.createElement(s.W,null,t.renderRectanglesStatically(a))})}},{key:"renderRectangles",value:function(){var t=this.props,e=t.data,r=t.isAnimationActive,n=this.state.prevData;return!(r&&e&&e.length)||n&&u()(n,e)?this.renderRectanglesStatically(e):this.renderRectanglesWithAnimation()}},{key:"renderBackground",value:function(){var t=this,e=this.props,r=e.data,o=e.dataKey,i=e.activeIndex,a=(0,y.J9)(this.props.background,!1);return r.map(function(e,r){e.value;var u=e.background,c=I(e,T);if(!u)return null;var l=B(B(B(B(B({},c),{},{fill:"#eee"},u),a),(0,b.XC)(t.props,e,r)),{},{onAnimationStart:t.handleAnimationStart,onAnimationEnd:t.handleAnimationEnd,dataKey:o,index:r,className:"recharts-bar-background-rectangle"});return n.createElement(M,D({key:"background-bar-".concat(r),option:t.props.background,isActive:r===i},l))})}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,o=r.data,i=r.xAxis,a=r.yAxis,u=r.layout,c=r.children,l=(0,y.aS)(c,f.u);if(!l)return null;var p="vertical"===u?o[0].height/2:o[0].width/2,h=function(t,e){var r=Array.isArray(t.value)?t.value[1]:t.value;return{x:t.x,y:t.y,value:r,errorVal:(0,m.kr)(t,e)}},d={clipPath:t?"url(#clipPath-".concat(e,")"):null};return n.createElement(s.W,d,l.map(function(t){return n.cloneElement(t,{key:"error-bar-".concat(e,"-").concat(t.props.dataKey),data:o,xAxis:i,yAxis:a,layout:u,offset:p,dataPointFormatter:h})}))}},{key:"render",value:function(){var t=this.props,e=t.hide,r=t.data,i=t.className,a=t.xAxis,u=t.yAxis,c=t.left,f=t.top,p=t.width,d=t.height,y=t.isAnimationActive,v=t.background,m=t.id;if(e||!r||!r.length)return null;var b=this.state.isAnimationFinished,g=(0,o.A)("recharts-bar",i),x=a&&a.allowDataOverflow,w=u&&u.allowDataOverflow,O=x||w,j=l()(m)?this.id:m;return n.createElement(s.W,{className:g},x||w?n.createElement("defs",null,n.createElement("clipPath",{id:"clipPath-".concat(j)},n.createElement("rect",{x:x?c:c-p/2,y:w?f:f-d/2,width:x?p:2*p,height:w?d:2*d}))):null,n.createElement(s.W,{className:"recharts-bar-rectangles",clipPath:O?"url(#clipPath-".concat(j,")"):null},v?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(O,j),(!y||b)&&h.Z.renderCallByParent(this.props,r))}}])&&R(r.prototype,a),c&&R(r,c),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,a,c}(n.PureComponent);_=q,$(q,"displayName","Bar"),$(q,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!v.m.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"}),$(q,"getComposedData",function(t){var e=t.props,r=t.item,n=t.barPosition,o=t.bandSize,i=t.xAxis,a=t.yAxis,u=t.xAxisTicks,c=t.yAxisTicks,l=t.stackedData,s=t.dataStartIndex,f=t.displayedData,h=t.offset,v=(0,m.xi)(n,r);if(!v)return null;var b=e.layout,x=r.type.defaultProps,w=void 0!==x?B(B({},x),r.props):r.props,O=w.dataKey,j=w.children,S=w.minPointSize,A="horizontal"===b?a:i,P=l?A.scale.domain():null,E=(0,m.DW)({numericAxis:A}),k=(0,y.aS)(j,p),M=f.map(function(t,e){var n,f,p,h,y,x;l?n=(0,m._f)(l[s+e],P):(n=(0,m.kr)(t,O),Array.isArray(n)||(n=[E,n]));var w=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(r,n){if("number"===typeof t)return t;var o=(0,d.Et)(r)||(0,d.uy)(r);return o?t(r,n):(o||(0,g.A)(!1),e)}}(S,_.defaultProps.minPointSize)(n[1],e);if("horizontal"===b){var j,A=[a.scale(n[0]),a.scale(n[1])],M=A[0],T=A[1];f=(0,m.y2)({axis:i,ticks:u,bandSize:o,offset:v.offset,entry:t,index:e}),p=null!==(j=null!==T&&void 0!==T?T:M)&&void 0!==j?j:void 0,h=v.size;var C=M-T;if(y=Number.isNaN(C)?0:C,x={x:f,y:a.y,width:h,height:a.height},Math.abs(w)>0&&Math.abs(y)<Math.abs(w)){var I=(0,d.sA)(y||w)*(Math.abs(w)-Math.abs(y));p-=I,y+=I}}else{var D=[i.scale(n[0]),i.scale(n[1])],N=D[0],R=D[1];if(f=N,p=(0,m.y2)({axis:a,ticks:c,bandSize:o,offset:v.offset,entry:t,index:e}),h=R-N,y=v.size,x={x:i.x,y:p,width:i.width,height:y},Math.abs(w)>0&&Math.abs(h)<Math.abs(w))h+=(0,d.sA)(h||w)*(Math.abs(w)-Math.abs(h))}return B(B(B({},t),{},{x:f,y:p,width:h,height:y,value:l?n:n[1],payload:t,background:x},k&&k[e]&&k[e].props),{},{tooltipPayload:[(0,m.zb)(r,t)],tooltipPosition:{x:f+h/2,y:p+y/2}})});return B({data:M,layout:b},h)})},8813:(t,e,r)=>{"use strict";r.d(e,{u:()=>x});var n=r(5043),o=r(3404),i=r(1639),a=r(240),u=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function l(){return l=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},l.apply(this,arguments)}function s(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return f(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return f(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function p(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function h(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,g(n.key),n)}}function d(t,e,r){return e=v(e),function(t,e){if(e&&("object"===c(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,y()?Reflect.construct(e,r||[],v(t).constructor):e.apply(t,r))}function y(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(y=function(){return!!t})()}function v(t){return v=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},v(t)}function m(t,e){return m=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},m(t,e)}function b(t,e,r){return(e=g(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function g(t){var e=function(t,e){if("object"!=c(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=c(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==c(e)?e:e+""}var x=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),d(this,e,arguments)}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&m(t,e)}(e,t),r=e,(c=[{key:"render",value:function(){var t=this.props,e=t.offset,r=t.layout,c=t.width,f=t.dataKey,h=t.data,d=t.dataPointFormatter,y=t.xAxis,v=t.yAxis,m=p(t,u),b=(0,a.J9)(m,!1);"x"===this.props.direction&&"number"!==y.type&&(0,o.A)(!1);var g=h.map(function(t){var o=d(t,f),a=o.x,u=o.y,p=o.value,h=o.errorVal;if(!h)return null;var m,g,x=[];if(Array.isArray(h)){var w=s(h,2);m=w[0],g=w[1]}else m=g=h;if("vertical"===r){var O=y.scale,j=u+e,S=j+c,A=j-c,P=O(p-m),E=O(p+g);x.push({x1:E,y1:S,x2:E,y2:A}),x.push({x1:P,y1:j,x2:E,y2:j}),x.push({x1:P,y1:S,x2:P,y2:A})}else if("horizontal"===r){var k=v.scale,M=a+e,_=M-c,T=M+c,C=k(p-m),I=k(p+g);x.push({x1:_,y1:I,x2:T,y2:I}),x.push({x1:M,y1:C,x2:M,y2:I}),x.push({x1:_,y1:C,x2:T,y2:C})}return n.createElement(i.W,l({className:"recharts-errorBar",key:"bar-".concat(x.map(function(t){return"".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))},b),x.map(function(t){return n.createElement("line",l({},t,{key:"line-".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))}))});return n.createElement(i.W,{className:"recharts-errorBars"},g)}}])&&h(r.prototype,c),f&&h(r,f),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,c,f}(n.Component);b(x,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),b(x,"displayName","ErrorBar")},8854:(t,e,r)=>{"use strict";r.d(e,{f:()=>y});var n=r(1629),o=r.n(n),i=r(6307),a=r(7213),u=r(6015),c=r(3831);function l(t,e,r){if(e<1)return[];if(1===e&&void 0===r)return t;for(var n=[],o=0;o<t.length;o+=e){if(void 0!==r&&!0!==r(t[o]))return;n.push(t[o])}return n}function s(t,e,r,n,o){if(t*e<t*n||t*e>t*o)return!1;var i=r();return t*(e-t*i/2-n)>=0&&t*(e+t*i/2-o)<=0}function f(t){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},f(t)}function p(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function h(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?p(Object(r),!0).forEach(function(e){d(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function d(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function y(t,e,r){var n=t.tick,f=t.ticks,p=t.viewBox,d=t.minTickGap,y=t.orientation,v=t.interval,m=t.tickFormatter,b=t.unit,g=t.angle;if(!f||!f.length||!n)return[];if((0,i.Et)(v)||u.m.isSsr)return function(t,e){return l(t,e+1)}(f,"number"===typeof v&&(0,i.Et)(v)?v:0);var x=[],w="top"===y||"bottom"===y?"width":"height",O=b&&"width"===w?(0,a.Pu)(b,{fontSize:e,letterSpacing:r}):{width:0,height:0},j=function(t,n){var i=o()(m)?m(t.value,n):t.value;return"width"===w?function(t,e,r){var n={width:t.width+e.width,height:t.height+e.height};return(0,c.bx)(n,r)}((0,a.Pu)(i,{fontSize:e,letterSpacing:r}),O,g):(0,a.Pu)(i,{fontSize:e,letterSpacing:r})[w]},S=f.length>=2?(0,i.sA)(f[1].coordinate-f[0].coordinate):1,A=function(t,e,r){var n="width"===r,o=t.x,i=t.y,a=t.width,u=t.height;return 1===e?{start:n?o:i,end:n?o+a:i+u}:{start:n?o+a:i+u,end:n?o:i}}(p,S,w);return"equidistantPreserveStart"===v?function(t,e,r,n,o){for(var i,a=(n||[]).slice(),u=e.start,c=e.end,f=0,p=1,h=u,d=function(){var e=null===n||void 0===n?void 0:n[f];if(void 0===e)return{v:l(n,p)};var i,a=f,d=function(){return void 0===i&&(i=r(e,a)),i},y=e.coordinate,v=0===f||s(t,y,d,h,c);v||(f=0,h=u,p+=1),v&&(h=y+t*(d()/2+o),f+=p)};p<=a.length;)if(i=d())return i.v;return[]}(S,A,j,f,d):(x="preserveStart"===v||"preserveStartEnd"===v?function(t,e,r,n,o,i){var a=(n||[]).slice(),u=a.length,c=e.start,l=e.end;if(i){var f=n[u-1],p=r(f,u-1),d=t*(f.coordinate+t*p/2-l);a[u-1]=f=h(h({},f),{},{tickCoord:d>0?f.coordinate-d*t:f.coordinate}),s(t,f.tickCoord,function(){return p},c,l)&&(l=f.tickCoord-t*(p/2+o),a[u-1]=h(h({},f),{},{isShow:!0}))}for(var y=i?u-1:u,v=function(e){var n,i=a[e],u=function(){return void 0===n&&(n=r(i,e)),n};if(0===e){var f=t*(i.coordinate-t*u()/2-c);a[e]=i=h(h({},i),{},{tickCoord:f<0?i.coordinate-f*t:i.coordinate})}else a[e]=i=h(h({},i),{},{tickCoord:i.coordinate});s(t,i.tickCoord,u,c,l)&&(c=i.tickCoord+t*(u()/2+o),a[e]=h(h({},i),{},{isShow:!0}))},m=0;m<y;m++)v(m);return a}(S,A,j,f,d,"preserveStartEnd"===v):function(t,e,r,n,o){for(var i=(n||[]).slice(),a=i.length,u=e.start,c=e.end,l=function(e){var n,l=i[e],f=function(){return void 0===n&&(n=r(l,e)),n};if(e===a-1){var p=t*(l.coordinate+t*f()/2-c);i[e]=l=h(h({},l),{},{tickCoord:p>0?l.coordinate-p*t:l.coordinate})}else i[e]=l=h(h({},l),{},{tickCoord:l.coordinate});s(t,l.tickCoord,f,u,c)&&(c=l.tickCoord-t*(f()/2+o),i[e]=h(h({},l),{},{isShow:!0}))},f=a-1;f>=0;f--)l(f);return i}(S,A,j,f,d),x.filter(function(t){return t.isShow}))}},8883:(t,e,r)=>{var n=r(5652),o=r(6571);t.exports=function(t,e){var r=-1,i=o(t)?Array(t.length):[];return n(t,function(t,n,o){i[++r]=e(t,n,o)}),i}},8892:(t,e,r)=>{"use strict";r.d(e,{c:()=>c});var n=r(5043),o=r(8387),i=r(7287),a=r(240);function u(){return u=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},u.apply(this,arguments)}var c=function(t){var e=t.cx,r=t.cy,c=t.r,l=t.className,s=(0,o.A)("recharts-dot",l);return e===+e&&r===+r&&c===+c?n.createElement("circle",u({},(0,a.J9)(t,!1),(0,i._U)(t),{className:s,cx:e,cy:r,r:c})):null}},8895:t=>{t.exports=function(t,e){for(var r=-1,n=e.length,o=t.length;++r<n;)t[o+r]=e[r];return t}},8902:(t,e,r)=>{var n=r(4816),o=r(6179),i=r(6704);function a(t){var e=-1,r=null==t?0:t.length;for(this.__data__=new n;++e<r;)this.add(t[e])}a.prototype.add=a.prototype.push=o,a.prototype.has=i,t.exports=a},8990:(t,e,r)=>{var n=r(9995)(r(2520));t.exports=n},9057:(t,e,r)=>{var n=r(5324),o=r(2777),i=r(4052),a=r(9194),u=r(6173),c=r(914);t.exports=function(t,e,r){for(var l=-1,s=(e=n(e,t)).length,f=!1;++l<s;){var p=c(e[l]);if(!(f=null!=t&&r(t,p)))break;t=t[p]}return f||++l!=s?f:!!(s=null==t?0:t.length)&&u(s)&&a(p,s)&&(i(t)||o(t))}},9062:(t,e,r)=>{"use strict";t.exports=r(6378)},9096:(t,e,r)=>{var n=r(9256),o=r(5029),i=r(3279),a=r(4052),u=r(3932);t.exports=function(t){return"function"==typeof t?t:null==t?i:"object"==typeof t?a(t)?o(t[0],t[1]):n(t):u(t)}},9115:(t,e,r)=>{var n=r(5967),o=r(6311),i=r(715);t.exports=function(t){return o(t)?i(t):n(t)}},9120:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});const n=(0,r(3797).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},9140:(t,e,r)=>{var n=r(7303);t.exports=function(t){var e=n(t),r=e%1;return e===e?r?e-r:e:0}},9160:(t,e,r)=>{var n=r(6913),o=r(2761);t.exports=function(t){return"number"==typeof t||o(t)&&"[object Number]"==n(t)}},9194:t=>{var e=/^(?:0|[1-9]\d*)$/;t.exports=function(t,r){var n=typeof t;return!!(r=null==r?9007199254740991:r)&&("number"==n||"symbol"!=n&&e.test(t))&&t>-1&&t%1==0&&t<r}},9236:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});Array.prototype.slice;function n(t){return"object"===typeof t&&"length"in t?t:Array.from(t)}},9256:(t,e,r)=>{var n=r(6532),o=r(3781),i=r(1310);t.exports=function(t){var e=o(t);return 1==e.length&&e[0][2]?i(e[0][0],e[0][1]):function(r){return r===t||n(r,t,e)}}},9394:(t,e,r)=>{var n=r(2622);t.exports=function(t){return n(this,t).has(t)}},9395:(t,e,r)=>{var n=r(4262),o=r(9621),i=r(8673);t.exports=function(t){return n(t,i,o)}},9417:(t,e,r)=>{var n=r(6686);t.exports=function(t){return t===t&&!n(t)}},9621:(t,e,r)=>{var n=r(7529),o=r(7828),i=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols,u=a?function(t){return null==t?[]:(t=Object(t),n(a(t),function(e){return i.call(t,e)}))}:o;t.exports=u},9676:t=>{var e=Math.ceil,r=Math.max;t.exports=function(t,n,o,i){for(var a=-1,u=r(e((n-t)/(o||1)),0),c=Array(u);u--;)c[i?u:++a]=t,t+=o;return c}},9686:t=>{t.exports=function(t){return null==t}},9742:(t,e,r)=>{var n=r(9841);t.exports=function(t,e,r){for(var o=-1,i=t.length;++o<i;){var a=t[o],u=e(a);if(null!=u&&(void 0===c?u===u&&!n(u):r(u,c)))var c=u,l=a}return l}},9812:(t,e,r)=>{var n=r(6552).Symbol;t.exports=n},9841:(t,e,r)=>{var n=r(6913),o=r(2761);t.exports=function(t){return"symbol"==typeof t||o(t)&&"[object Symbol]"==n(t)}},9853:(t,e,r)=>{var n=r(6989);t.exports=function(t,e){return n(t,e)}},9889:(t,e,r)=>{var n=r(3950),o=r(6686);t.exports=function(t,e,r){var i=!0,a=!0;if("function"!=typeof t)throw new TypeError("Expected a function");return o(r)&&(i="leading"in r?!!r.leading:i,a="trailing"in r?!!r.trailing:a),n(t,e,{leading:i,maxWait:e,trailing:a})}},9935:(t,e,r)=>{var n=r(1340),o=Array.prototype.splice;t.exports=function(t){var e=this.__data__,r=n(e,t);return!(r<0)&&(r==e.length-1?e.pop():o.call(e,r,1),--this.size,!0)}},9987:(t,e,r)=>{var n=r(9812),o=r(2929),i=r(3211),a=r(3668),u=r(4160),c=r(2074),l=n?n.prototype:void 0,s=l?l.valueOf:void 0;t.exports=function(t,e,r,n,l,f,p){switch(r){case"[object DataView]":if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)return!1;t=t.buffer,e=e.buffer;case"[object ArrayBuffer]":return!(t.byteLength!=e.byteLength||!f(new o(t),new o(e)));case"[object Boolean]":case"[object Date]":case"[object Number]":return i(+t,+e);case"[object Error]":return t.name==e.name&&t.message==e.message;case"[object RegExp]":case"[object String]":return t==e+"";case"[object Map]":var h=u;case"[object Set]":var d=1&n;if(h||(h=c),t.size!=e.size&&!d)return!1;var y=p.get(t);if(y)return y==e;n|=2,p.set(t,e);var v=a(h(t),h(e),n,l,f,p);return p.delete(t),v;case"[object Symbol]":if(s)return s.call(t)==s.call(e)}return!1}},9995:(t,e,r)=>{var n=r(9096),o=r(6571),i=r(8673);t.exports=function(t){return function(e,r,a){var u=Object(e);if(!o(e)){var c=n(r,3);e=i(e),r=function(t){return c(u[t],t,u)}}var l=t(e,r,a);return l>-1?u[c?e[l]:l]:void 0}}}}]);
//# sourceMappingURL=280.5fd627be.chunk.js.map