import axios from 'axios';

const API_BASE_URL = 'http://localhost:5000/api';

export interface MerchantFormData {
  name: string;
  email: string;
  businessName: string;
  businessType: string;
  phone: string;
  website?: string;
  address?: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  bankDetails?: {
    accountNumber: string;
    routingNumber: string;
    bankName: string;
  };
}

export interface Merchant {
  _id: string;
  name: string;
  email: string;
  businessName?: string;
  businessType?: string;
  phone?: string;
  website?: string;
  address?: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  bankDetails?: {
    accountNumber: string;
    routingNumber: string;
    bankName: string;
  };
  isActive: boolean;
  isVerified: boolean;
  role: string;
  status: 'pending' | 'active' | 'inactive' | 'suspended';
  verificationStatus: 'pending' | 'verified' | 'rejected';
  createdAt: string;
  updatedAt: string;
}

export interface MerchantsResponse {
  success: boolean;
  data: Merchant[];
  total: number;
  page: number;
  totalPages: number;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

class MerchantAdminService {
  // Get all merchants with pagination and filtering
  async getMerchants(params?: {
    page?: number;
    limit?: number;
    status?: string;
    search?: string;
  }): Promise<MerchantsResponse> {
    try {
      const response = await axios.get(`${API_BASE_URL}/admin/merchants`, {
        params: {
          page: params?.page || 1,
          limit: params?.limit || 10,
          status: params?.status,
          search: params?.search,
        },
      });
      
      return {
        success: true,
        data: response.data.merchants || [],
        total: response.data.total || 0,
        page: response.data.currentPage || 1,
        totalPages: response.data.totalPages || 1,
      };
    } catch (error) {
      console.error('Error fetching merchants:', error);
      // Return mock data as fallback
      return this.getMockMerchants();
    }
  }

  // Get single merchant by ID
  async getMerchant(id: string): Promise<ApiResponse<Merchant>> {
    try {
      const response = await axios.get(`${API_BASE_URL}/admin/merchants/${id}`);
      return {
        success: true,
        data: response.data,
      };
    } catch (error) {
      console.error('Error fetching merchant:', error);
      throw new Error('Failed to fetch merchant');
    }
  }

  // Create new merchant
  async createMerchant(merchantData: MerchantFormData): Promise<ApiResponse<Merchant>> {
    try {
      const response = await axios.post(`${API_BASE_URL}/admin/merchants`, merchantData);
      return {
        success: true,
        data: response.data,
        message: 'Merchant created successfully',
      };
    } catch (error: any) {
      console.error('Error creating merchant:', error);
      throw new Error(error.response?.data?.message || 'Failed to create merchant');
    }
  }

  // Update merchant
  async updateMerchant(id: string, merchantData: Partial<MerchantFormData>): Promise<ApiResponse<Merchant>> {
    try {
      const response = await axios.put(`${API_BASE_URL}/admin/merchants/${id}`, merchantData);
      return {
        success: true,
        data: response.data,
        message: 'Merchant updated successfully',
      };
    } catch (error: any) {
      console.error('Error updating merchant:', error);
      throw new Error(error.response?.data?.message || 'Failed to update merchant');
    }
  }

  // Delete merchant
  async deleteMerchant(id: string): Promise<ApiResponse<void>> {
    try {
      await axios.delete(`${API_BASE_URL}/admin/merchants/${id}`);
      return {
        success: true,
        data: undefined,
        message: 'Merchant deleted successfully',
      };
    } catch (error: any) {
      console.error('Error deleting merchant:', error);
      throw new Error(error.response?.data?.message || 'Failed to delete merchant');
    }
  }

  // Approve merchant
  async approveMerchant(id: string): Promise<ApiResponse<Merchant>> {
    try {
      const response = await axios.post(`${API_BASE_URL}/admin/merchants/${id}/approve`);
      return {
        success: true,
        data: response.data,
        message: 'Merchant approved successfully',
      };
    } catch (error: any) {
      console.error('Error approving merchant:', error);
      throw new Error(error.response?.data?.message || 'Failed to approve merchant');
    }
  }

  // Reject merchant
  async rejectMerchant(id: string, reason: string): Promise<ApiResponse<Merchant>> {
    try {
      const response = await axios.post(`${API_BASE_URL}/admin/merchants/${id}/reject`, { reason });
      return {
        success: true,
        data: response.data,
        message: 'Merchant rejected successfully',
      };
    } catch (error: any) {
      console.error('Error rejecting merchant:', error);
      throw new Error(error.response?.data?.message || 'Failed to reject merchant');
    }
  }

  // Mock data fallback
  private getMockMerchants(): MerchantsResponse {
    const mockMerchants: Merchant[] = [
      {
        _id: '1',
        name: 'John Doe',
        email: '<EMAIL>',
        businessName: 'Tech Solutions Inc',
        businessType: 'Technology',
        phone: '+1234567890',
        website: 'https://techsolutions.com',
        address: {
          street: '123 Tech Street',
          city: 'San Francisco',
          state: 'CA',
          zipCode: '94105',
          country: 'USA',
        },
        isActive: true,
        isVerified: true,
        role: 'merchant',
        status: 'active',
        verificationStatus: 'verified',
        createdAt: '2025-07-26T19:53:01.095Z',
        updatedAt: '2025-07-26T19:53:01.095Z',
      },
      {
        _id: '2',
        name: 'Jane Smith',
        email: '<EMAIL>',
        businessName: 'Fashion Store',
        businessType: 'Retail',
        phone: '+1234567891',
        website: 'https://fashionstore.com',
        address: {
          street: '456 Fashion Ave',
          city: 'New York',
          state: 'NY',
          zipCode: '10001',
          country: 'USA',
        },
        isActive: true,
        isVerified: false,
        role: 'merchant',
        status: 'pending',
        verificationStatus: 'pending',
        createdAt: '2025-07-26T19:53:01.711Z',
        updatedAt: '2025-07-26T19:53:01.711Z',
      },
    ];

    return {
      success: true,
      data: mockMerchants,
      total: mockMerchants.length,
      page: 1,
      totalPages: 1,
    };
  }
}

export const merchantAdminService = new MerchantAdminService();
export default merchantAdminService;
