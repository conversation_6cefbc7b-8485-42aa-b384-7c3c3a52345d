const request = require('supertest');
const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const app = require('../app');
const User = require('../models/User');
const ReserveStrategy = require('../models/ReserveStrategy');
const ReserveActivity = require('../models/ReserveActivity');

// Test data
let mongoServer;
let adminUser;
let adminToken;
let testStrategy;

// Set up in-memory database before tests run
beforeAll(async () => {
  mongoServer = await MongoMemoryServer.create();
  const uri = mongoServer.getUri();
  await mongoose.connect(uri);
  
  // Create an admin user
  adminUser = await User.create({
    name: 'Admin User',
    email: '<EMAIL>',
    password: 'password123',
    roles: ['admin']
  });
  
  // Login to get token
  const loginRes = await request(app)
    .post('/api/v1/auth/login')
    .send({
      email: '<EMAIL>',
      password: 'password123'
    });
  
  adminToken = loginRes.body.token;
  
  // Create a test strategy
  testStrategy = await ReserveStrategy.create({
    name: 'Test Strategy',
    description: 'Test Description',
    rules: [{
      name: 'Test Rule',
      type: 'percentage',
      percentage: 10,
      isActive: true
    }],
    createdBy: adminUser._id,
    status: 'active'
  });
  
  // Create some test activities
  await ReserveActivity.create([
    {
      strategy: testStrategy._id,
      type: 'create',
      description: 'Created strategy',
      user: adminUser._id,
      status: 'approved'
    },
    {
      strategy: testStrategy._id,
      type: 'rule_update',
      description: 'Updated rule',
      user: adminUser._id,
      status: 'pending',
      details: {
        ruleId: testStrategy.rules[0]._id,
        changes: { percentage: 15 }
      }
    },
    {
      strategy: testStrategy._id,
      type: 'status_change',
      description: 'Changed status',
      user: adminUser._id,
      status: 'pending',
      details: {
        status: 'inactive'
      }
    }
  ]);
});

// Clean up after each test
afterEach(async () => {
  // Clean up activities if needed
});

// Disconnect and close server after all tests
afterAll(async () => {
  await mongoose.connection.dropDatabase();
  await mongoose.connection.close();
  await mongoServer.stop();
});

describe('Reserve Strategy Controller', () => {
  describe('GET /api/v1/reserve-strategies/activities', () => {
    it('should get all activities with default pagination', async () => {
      const res = await request(app)
        .get('/api/v1/reserve-strategies/activities')
        .set('Authorization', `Bearer ${adminToken}`);
      
      expect(res.statusCode).toBe(200);
      expect(res.body.success).toBe(true);
      expect(res.body.data.docs).toHaveLength(3);
      expect(res.body.data.total).toBe(3);
      expect(res.body.data.page).toBe(1);
      expect(res.body.data.pages).toBe(1);
      expect(res.body.data.limit).toBe(10);
    });
    
    it('should filter activities by status', async () => {
      const res = await request(app)
        .get('/api/v1/reserve-strategies/activities?status=pending')
        .set('Authorization', `Bearer ${adminToken}`);
      
      expect(res.statusCode).toBe(200);
      expect(res.body.success).toBe(true);
      expect(res.body.data.docs).toHaveLength(2);
      expect(res.body.data.docs.every(a => a.status === 'pending')).toBe(true);
    });
    
    it('should filter activities by type', async () => {
      const res = await request(app)
        .get('/api/v1/reserve-strategies/activities?type=create')
        .set('Authorization', `Bearer ${adminToken}`);
      
      expect(res.statusCode).toBe(200);
      expect(res.body.success).toBe(true);
      expect(res.body.data.docs).toHaveLength(1);
      expect(res.body.data.docs[0].type).toBe('create');
    });
    
    it('should search activities by description', async () => {
      const res = await request(app)
        .get('/api/v1/reserve-strategies/activities?search=updated')
        .set('Authorization', `Bearer ${adminToken}`);
      
      expect(res.statusCode).toBe(200);
      expect(res.body.success).toBe(true);
      expect(res.body.data.docs).toHaveLength(1);
      expect(res.body.data.docs[0].description).toContain('Updated');
    });
  });
  
  describe('GET /api/v1/reserve-strategies/activities/:id', () => {
    it('should get a single activity by ID', async () => {
      const activity = await ReserveActivity.findOne({ type: 'create' });
      
      const res = await request(app)
        .get(`/api/v1/reserve-strategies/activities/${activity._id}`)
        .set('Authorization', `Bearer ${adminToken}`);
      
      expect(res.statusCode).toBe(200);
      expect(res.body.success).toBe(true);
      expect(res.body.data._id).toBe(activity._id.toString());
      expect(res.body.data.type).toBe('create');
    });
    
    it('should return 404 for non-existent activity', async () => {
      const nonExistentId = new mongoose.Types.ObjectId();
      
      const res = await request(app)
        .get(`/api/v1/reserve-strategies/activities/${nonExistentId}`)
        .set('Authorization', `Bearer ${adminToken}`);
      
      expect(res.statusCode).toBe(404);
      expect(res.body.success).toBe(false);
    });
    
    it('should return 400 for invalid activity ID', async () => {
      const res = await request(app)
        .get('/api/v1/reserve-strategies/activities/invalid-id')
        .set('Authorization', `Bearer ${adminToken}`);
      
      expect(res.statusCode).toBe(400);
      expect(res.body.success).toBe(false);
    });
  });
  
  describe('POST /api/v1/reserve-strategies/activities/:id/review', () => {
    it('should approve a pending activity', async () => {
      const activity = await ReserveActivity.findOne({ type: 'rule_update' });
      
      const res = await request(app)
        .post(`/api/v1/reserve-strategies/activities/${activity._id}/review`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          action: 'approve',
          note: 'Approving the rule update'
        });
      
      expect(res.statusCode).toBe(200);
      expect(res.body.success).toBe(true);
      expect(res.body.data.status).toBe('approved');
      expect(res.body.data.reviewNote).toBe('Approving the rule update');
      
      // Verify the strategy was updated
      const updatedStrategy = await ReserveStrategy.findById(testStrategy._id);
      expect(updatedStrategy.rules[0].percentage).toBe(15);
    });
    
    it('should flag a pending activity', async () => {
      const activity = await ReserveActivity.findOne({ type: 'status_change' });
      
      const res = await request(app)
        .post(`/api/v1/reserve-strategies/activities/${activity._id}/review`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          action: 'flag',
          note: 'Need more information'
        });
      
      expect(res.statusCode).toBe(200);
      expect(res.body.success).toBe(true);
      expect(res.body.data.status).toBe('flagged');
      expect(res.body.data.reviewNote).toBe('Need more information');
      
      // Verify the strategy was NOT updated
      const updatedStrategy = await ReserveStrategy.findById(testStrategy._id);
      expect(updatedStrategy.status).toBe('active');
    });
    
    it('should return 400 for invalid action', async () => {
      const activity = await ReserveActivity.findOne({ type: 'status_change' });
      
      const res = await request(app)
        .post(`/api/v1/reserve-strategies/activities/${activity._id}/review`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          action: 'invalid',
          note: 'Test note'
        });
      
      expect(res.statusCode).toBe(400);
      expect(res.body.success).toBe(false);
    });
    
    it('should return 400 if activity already reviewed', async () => {
      const activity = await ReserveActivity.findOne({ type: 'create' });
      
      const res = await request(app)
        .post(`/api/v1/reserve-strategies/activities/${activity._id}/review`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          action: 'approve',
          note: 'Test note'
        });
      
      expect(res.statusCode).toBe(400);
      expect(res.body.success).toBe(false);
    });
  });
  
  describe('GET /api/v1/reserve-strategies/:id/activities', () => {
    it('should get activities for a specific strategy', async () => {
      const res = await request(app)
        .get(`/api/v1/reserve-strategies/${testStrategy._id}/activities`)
        .set('Authorization', `Bearer ${adminToken}`);
      
      expect(res.statusCode).toBe(200);
      expect(res.body.success).toBe(true);
      expect(res.body.data.docs).toHaveLength(3);
      expect(res.body.data.docs.every(a => a.strategy === testStrategy._id.toString())).toBe(true);
    });
    
    it('should filter activities by status', async () => {
      const res = await request(app)
        .get(`/api/v1/reserve-strategies/${testStrategy._id}/activities?status=approved`)
        .set('Authorization', `Bearer ${adminToken}`);
      
      expect(res.statusCode).toBe(200);
      expect(res.body.success).toBe(true);
      expect(res.body.data.docs).toHaveLength(1);
      expect(res.body.data.docs[0].status).toBe('approved');
    });
    
    it('should return 404 for non-existent strategy', async () => {
      const nonExistentId = new mongoose.Types.ObjectId();
      
      const res = await request(app)
        .get(`/api/v1/reserve-strategies/${nonExistentId}/activities`)
        .set('Authorization', `Bearer ${adminToken}`);
      
      expect(res.statusCode).toBe(200);
      expect(res.body.success).toBe(true);
      expect(res.body.data.docs).toHaveLength(0);
    });
  });
});
