/*! For license information please see 604.14d0e974.chunk.js.LICENSE.txt */
"use strict";(self.webpackChunkpayment_gateway_frontend=self.webpackChunkpayment_gateway_frontend||[]).push([[604],{585:(t,e,n)=>{n.d(e,{t:()=>u});var r=n(2078),s=n(8664);const i=["online","offline"];class o extends r.Q{constructor(){super(),this.setup=t=>{if(!s.S$&&window.addEventListener){const e=()=>t();return i.forEach(t=>{window.addEventListener(t,e,!1)}),()=>{i.forEach(t=>{window.removeEventListener(t,e)})}}}}onSubscribe(){this.cleanup||this.setEventListener(this.setup)}onUnsubscribe(){var t;this.hasListeners()||(null==(t=this.cleanup)||t.call(this),this.cleanup=void 0)}setEventListener(t){var e;this.setup=t,null==(e=this.cleanup)||e.call(this),this.cleanup=t(t=>{"boolean"===typeof t?this.setOnline(t):this.onOnline()})}setOnline(t){this.online!==t&&(this.online=t,this.onOnline())}onOnline(){this.listeners.forEach(t=>{let{listener:e}=t;e()})}isOnline(){return"boolean"===typeof this.online?this.online:"undefined"===typeof navigator||"undefined"===typeof navigator.onLine||navigator.onLine}}const u=new o},2078:(t,e,n)=>{n.d(e,{Q:()=>r});class r{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(t){const e={listener:t};return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}}},2330:(t,e,n)=>{var r=n(5043);var s="function"===typeof Object.is?Object.is:function(t,e){return t===e&&(0!==t||1/t===1/e)||t!==t&&e!==e},i=r.useState,o=r.useEffect,u=r.useLayoutEffect,c=r.useDebugValue;function a(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!s(t,n)}catch(r){return!0}}var l="undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement?function(t,e){return e()}:function(t,e){var n=e(),r=i({inst:{value:n,getSnapshot:e}}),s=r[0].inst,l=r[1];return u(function(){s.value=n,s.getSnapshot=e,a(s)&&l({inst:s})},[t,n,e]),o(function(){return a(s)&&l({inst:s}),t(function(){a(s)&&l({inst:s})})},[t]),c(n),n};e.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:l},2540:(t,e,n)=>{n.d(e,{m:()=>o});var r=n(2078),s=n(8664);class i extends r.Q{constructor(){super(),this.setup=t=>{if(!s.S$&&window.addEventListener){const e=()=>t();return window.addEventListener("visibilitychange",e,!1),window.addEventListener("focus",e,!1),()=>{window.removeEventListener("visibilitychange",e),window.removeEventListener("focus",e)}}}}onSubscribe(){this.cleanup||this.setEventListener(this.setup)}onUnsubscribe(){var t;this.hasListeners()||(null==(t=this.cleanup)||t.call(this),this.cleanup=void 0)}setEventListener(t){var e;this.setup=t,null==(e=this.cleanup)||e.call(this),this.cleanup=t(t=>{"boolean"===typeof t?this.setFocused(t):this.onFocus()})}setFocused(t){this.focused!==t&&(this.focused=t,this.onFocus())}onFocus(){this.listeners.forEach(t=>{let{listener:e}=t;e()})}isFocused(){return"boolean"===typeof this.focused?this.focused:"undefined"===typeof document||[void 0,"visible","prerender"].includes(document.visibilityState)}}const o=new i},4084:(t,e,n)=>{function r(t,e){return"function"===typeof t?t(...e):!!t}n.d(e,{G:()=>r})},5604:(t,e,n)=>{n.d(e,{I:()=>F});var r=n(8664),s=n(9939),i=n(2540),o=n(2078),u=n(7988);class c extends o.Q{constructor(t,e){super(),this.client=t,this.options=e,this.trackedProps=new Set,this.selectError=null,this.bindMethods(),this.setOptions(e)}bindMethods(){this.remove=this.remove.bind(this),this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.currentQuery.addObserver(this),a(this.currentQuery,this.options)&&this.executeFetch(),this.updateTimers())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return l(this.currentQuery,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return l(this.currentQuery,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.clearStaleTimeout(),this.clearRefetchInterval(),this.currentQuery.removeObserver(this)}setOptions(t,e){const n=this.options,s=this.currentQuery;if(this.options=this.client.defaultQueryOptions(t),(0,r.f8)(n,this.options)||this.client.getQueryCache().notify({type:"observerOptionsUpdated",query:this.currentQuery,observer:this}),"undefined"!==typeof this.options.enabled&&"boolean"!==typeof this.options.enabled)throw new Error("Expected enabled to be a boolean");this.options.queryKey||(this.options.queryKey=n.queryKey),this.updateQuery();const i=this.hasListeners();i&&h(this.currentQuery,s,this.options,n)&&this.executeFetch(),this.updateResult(e),!i||this.currentQuery===s&&this.options.enabled===n.enabled&&this.options.staleTime===n.staleTime||this.updateStaleTimeout();const o=this.computeRefetchInterval();!i||this.currentQuery===s&&this.options.enabled===n.enabled&&o===this.currentRefetchInterval||this.updateRefetchInterval(o)}getOptimisticResult(t){const e=this.client.getQueryCache().build(this.client,t),n=this.createResult(e,t);return function(t,e,n){if(n.keepPreviousData)return!1;if(void 0!==n.placeholderData)return e.isPlaceholderData;if(!(0,r.f8)(t.getCurrentResult(),e))return!0;return!1}(this,n,t)&&(this.currentResult=n,this.currentResultOptions=this.options,this.currentResultState=this.currentQuery.state),n}getCurrentResult(){return this.currentResult}trackResult(t){const e={};return Object.keys(t).forEach(n=>{Object.defineProperty(e,n,{configurable:!1,enumerable:!0,get:()=>(this.trackedProps.add(n),t[n])})}),e}getCurrentQuery(){return this.currentQuery}remove(){this.client.getQueryCache().remove(this.currentQuery)}refetch(){let{refetchPage:t,...e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.fetch({...e,meta:{refetchPage:t}})}fetchOptimistic(t){const e=this.client.defaultQueryOptions(t),n=this.client.getQueryCache().build(this.client,e);return n.isFetchingOptimistic=!0,n.fetch().then(()=>this.createResult(n,e))}fetch(t){var e;return this.executeFetch({...t,cancelRefetch:null==(e=t.cancelRefetch)||e}).then(()=>(this.updateResult(),this.currentResult))}executeFetch(t){this.updateQuery();let e=this.currentQuery.fetch(this.options,t);return null!=t&&t.throwOnError||(e=e.catch(r.lQ)),e}updateStaleTimeout(){if(this.clearStaleTimeout(),r.S$||this.currentResult.isStale||!(0,r.gn)(this.options.staleTime))return;const t=(0,r.j3)(this.currentResult.dataUpdatedAt,this.options.staleTime)+1;this.staleTimeoutId=setTimeout(()=>{this.currentResult.isStale||this.updateResult()},t)}computeRefetchInterval(){var t;return"function"===typeof this.options.refetchInterval?this.options.refetchInterval(this.currentResult.data,this.currentQuery):null!=(t=this.options.refetchInterval)&&t}updateRefetchInterval(t){this.clearRefetchInterval(),this.currentRefetchInterval=t,!r.S$&&!1!==this.options.enabled&&(0,r.gn)(this.currentRefetchInterval)&&0!==this.currentRefetchInterval&&(this.refetchIntervalId=setInterval(()=>{(this.options.refetchIntervalInBackground||i.m.isFocused())&&this.executeFetch()},this.currentRefetchInterval))}updateTimers(){this.updateStaleTimeout(),this.updateRefetchInterval(this.computeRefetchInterval())}clearStaleTimeout(){this.staleTimeoutId&&(clearTimeout(this.staleTimeoutId),this.staleTimeoutId=void 0)}clearRefetchInterval(){this.refetchIntervalId&&(clearInterval(this.refetchIntervalId),this.refetchIntervalId=void 0)}createResult(t,e){const n=this.currentQuery,s=this.options,i=this.currentResult,o=this.currentResultState,c=this.currentResultOptions,l=t!==n,f=l?t.state:this.currentQueryInitialState,p=l?this.currentResult:this.previousQueryResult,{state:y}=t;let v,{dataUpdatedAt:b,error:R,errorUpdatedAt:m,fetchStatus:S,status:w}=y,g=!1,E=!1;if(e._optimisticResults){const r=this.hasListeners(),i=!r&&a(t,e),o=r&&h(t,n,e,s);(i||o)&&(S=(0,u.v_)(t.options.networkMode)?"fetching":"paused",b||(w="loading")),"isRestoring"===e._optimisticResults&&(S="idle")}if(e.keepPreviousData&&!y.dataUpdatedAt&&null!=p&&p.isSuccess&&"error"!==w)v=p.data,b=p.dataUpdatedAt,w=p.status,g=!0;else if(e.select&&"undefined"!==typeof y.data)if(i&&y.data===(null==o?void 0:o.data)&&e.select===this.selectFn)v=this.selectResult;else try{this.selectFn=e.select,v=e.select(y.data),v=(0,r.pl)(null==i?void 0:i.data,v,e),this.selectResult=v,this.selectError=null}catch(I){0,this.selectError=I}else v=y.data;if("undefined"!==typeof e.placeholderData&&"undefined"===typeof v&&"loading"===w){let t;if(null!=i&&i.isPlaceholderData&&e.placeholderData===(null==c?void 0:c.placeholderData))t=i.data;else if(t="function"===typeof e.placeholderData?e.placeholderData():e.placeholderData,e.select&&"undefined"!==typeof t)try{t=e.select(t),this.selectError=null}catch(I){0,this.selectError=I}"undefined"!==typeof t&&(w="success",v=(0,r.pl)(null==i?void 0:i.data,t,e),E=!0)}this.selectError&&(R=this.selectError,v=this.selectResult,m=Date.now(),w="error");const C="fetching"===S,O="loading"===w,Q="error"===w;return{status:w,fetchStatus:S,isLoading:O,isSuccess:"success"===w,isError:Q,isInitialLoading:O&&C,data:v,dataUpdatedAt:b,error:R,errorUpdatedAt:m,failureCount:y.fetchFailureCount,failureReason:y.fetchFailureReason,errorUpdateCount:y.errorUpdateCount,isFetched:y.dataUpdateCount>0||y.errorUpdateCount>0,isFetchedAfterMount:y.dataUpdateCount>f.dataUpdateCount||y.errorUpdateCount>f.errorUpdateCount,isFetching:C,isRefetching:C&&!O,isLoadingError:Q&&0===y.dataUpdatedAt,isPaused:"paused"===S,isPlaceholderData:E,isPreviousData:g,isRefetchError:Q&&0!==y.dataUpdatedAt,isStale:d(t,e),refetch:this.refetch,remove:this.remove}}updateResult(t){const e=this.currentResult,n=this.createResult(this.currentQuery,this.options);if(this.currentResultState=this.currentQuery.state,this.currentResultOptions=this.options,(0,r.f8)(n,e))return;this.currentResult=n;const s={cache:!0};!1!==(null==t?void 0:t.listeners)&&(()=>{if(!e)return!0;const{notifyOnChangeProps:t}=this.options,n="function"===typeof t?t():t;if("all"===n||!n&&!this.trackedProps.size)return!0;const r=new Set(null!=n?n:this.trackedProps);return this.options.useErrorBoundary&&r.add("error"),Object.keys(this.currentResult).some(t=>{const n=t;return this.currentResult[n]!==e[n]&&r.has(n)})})()&&(s.listeners=!0),this.notify({...s,...t})}updateQuery(){const t=this.client.getQueryCache().build(this.client,this.options);if(t===this.currentQuery)return;const e=this.currentQuery;this.currentQuery=t,this.currentQueryInitialState=t.state,this.previousQueryResult=this.currentResult,this.hasListeners()&&(null==e||e.removeObserver(this),t.addObserver(this))}onQueryUpdate(t){const e={};"success"===t.type?e.onSuccess=!t.manual:"error"!==t.type||(0,u.wm)(t.error)||(e.onError=!0),this.updateResult(e),this.hasListeners()&&this.updateTimers()}notify(t){s.j.batch(()=>{var e,n,r,s;if(t.onSuccess)null==(e=(n=this.options).onSuccess)||e.call(n,this.currentResult.data),null==(r=(s=this.options).onSettled)||r.call(s,this.currentResult.data,null);else if(t.onError){var i,o,u,c;null==(i=(o=this.options).onError)||i.call(o,this.currentResult.error),null==(u=(c=this.options).onSettled)||u.call(c,void 0,this.currentResult.error)}t.listeners&&this.listeners.forEach(t=>{let{listener:e}=t;e(this.currentResult)}),t.cache&&this.client.getQueryCache().notify({query:this.currentQuery,type:"observerResultsUpdated"})})}}function a(t,e){return function(t,e){return!1!==e.enabled&&!t.state.dataUpdatedAt&&!("error"===t.state.status&&!1===e.retryOnMount)}(t,e)||t.state.dataUpdatedAt>0&&l(t,e,e.refetchOnMount)}function l(t,e,n){if(!1!==e.enabled){const r="function"===typeof n?n(t):n;return"always"===r||!1!==r&&d(t,e)}return!1}function h(t,e,n,r){return!1!==n.enabled&&(t!==e||!1===r.enabled)&&(!n.suspense||"error"!==t.state.status)&&d(t,n)}function d(t,e){return t.isStaleByTime(e.staleTime)}var f=n(5043),p=n(8873);function y(){let t=!1;return{clearReset:()=>{t=!1},reset:()=>{t=!0},isReset:()=>t}}const v=f.createContext(y()),b=()=>f.useContext(v);var R=n(9781);const m=f.createContext(!1),S=()=>f.useContext(m);m.Provider;var w=n(4084);const g=(t,e)=>{(t.suspense||t.useErrorBoundary)&&(e.isReset()||(t.retryOnMount=!1))},E=t=>{f.useEffect(()=>{t.clearReset()},[t])},C=t=>{let{result:e,errorResetBoundary:n,useErrorBoundary:r,query:s}=t;return e.isError&&!n.isReset()&&!e.isFetching&&(0,w.G)(r,[e.error,s])},O=t=>{t.suspense&&"number"!==typeof t.staleTime&&(t.staleTime=1e3)},Q=(t,e,n)=>(null==t?void 0:t.suspense)&&((t,e)=>t.isLoading&&t.isFetching&&!e)(e,n),I=(t,e,n)=>e.fetchOptimistic(t).then(e=>{let{data:n}=e;null==t.onSuccess||t.onSuccess(n),null==t.onSettled||t.onSettled(n,null)}).catch(e=>{n.clearReset(),null==t.onError||t.onError(e),null==t.onSettled||t.onSettled(void 0,e)});function F(t,e,n){return function(t,e){const n=(0,R.jE)({context:t.context}),r=S(),i=b(),o=n.defaultQueryOptions(t);o._optimisticResults=r?"isRestoring":"optimistic",o.onError&&(o.onError=s.j.batchCalls(o.onError)),o.onSuccess&&(o.onSuccess=s.j.batchCalls(o.onSuccess)),o.onSettled&&(o.onSettled=s.j.batchCalls(o.onSettled)),O(o),g(o,i),E(i);const[u]=f.useState(()=>new e(n,o)),c=u.getOptimisticResult(o);if((0,p.r)(f.useCallback(t=>{const e=r?()=>{}:u.subscribe(s.j.batchCalls(t));return u.updateResult(),e},[u,r]),()=>u.getCurrentResult(),()=>u.getCurrentResult()),f.useEffect(()=>{u.setOptions(o,{listeners:!1})},[o,u]),Q(o,c,r))throw I(o,u,i);if(C({result:c,errorResetBoundary:i,useErrorBoundary:o.useErrorBoundary,query:u.getCurrentQuery()}))throw c.error;return o.notifyOnChangeProps?c:u.trackResult(c)}((0,r.vh)(t,e,n),c)}},7988:(t,e,n)=>{n.d(e,{II:()=>l,v_:()=>u,wm:()=>a});var r=n(2540),s=n(585),i=n(8664);function o(t){return Math.min(1e3*2**t,3e4)}function u(t){return"online"!==(null!=t?t:"online")||s.t.isOnline()}class c{constructor(t){this.revert=null==t?void 0:t.revert,this.silent=null==t?void 0:t.silent}}function a(t){return t instanceof c}function l(t){let e,n,a,l=!1,h=0,d=!1;const f=new Promise((t,e)=>{n=t,a=e}),p=()=>!r.m.isFocused()||"always"!==t.networkMode&&!s.t.isOnline(),y=r=>{d||(d=!0,null==t.onSuccess||t.onSuccess(r),null==e||e(),n(r))},v=n=>{d||(d=!0,null==t.onError||t.onError(n),null==e||e(),a(n))},b=()=>new Promise(n=>{e=t=>{const e=d||!p();return e&&n(t),e},null==t.onPause||t.onPause()}).then(()=>{e=void 0,d||null==t.onContinue||t.onContinue()}),R=()=>{if(d)return;let e;try{e=t.fn()}catch(n){e=Promise.reject(n)}Promise.resolve(e).then(y).catch(e=>{var n,r;if(d)return;const s=null!=(n=t.retry)?n:3,u=null!=(r=t.retryDelay)?r:o,c="function"===typeof u?u(h,e):u,a=!0===s||"number"===typeof s&&h<s||"function"===typeof s&&s(h,e);!l&&a?(h++,null==t.onFail||t.onFail(h,e),(0,i.yy)(c).then(()=>{if(p())return b()}).then(()=>{l?v(e):R()})):v(e)})};return u(t.networkMode)?R():b().then(R),{promise:f,cancel:e=>{d||(v(new c(e)),null==t.abort||t.abort())},continue:()=>(null==e?void 0:e())?f:Promise.resolve(),cancelRetry:()=>{l=!0},continueRetry:()=>{l=!1}}}},8664:(t,e,n)=>{n.d(e,{Cp:()=>y,F$:()=>f,G6:()=>C,GR:()=>a,MK:()=>h,Od:()=>p,S$:()=>r,Zw:()=>i,b_:()=>l,f8:()=>R,gn:()=>o,j3:()=>u,jY:()=>O,lQ:()=>s,nJ:()=>d,pl:()=>Q,vh:()=>c,yy:()=>E});const r="undefined"===typeof window||"Deno"in window;function s(){}function i(t,e){return"function"===typeof t?t(e):t}function o(t){return"number"===typeof t&&t>=0&&t!==1/0}function u(t,e){return Math.max(t+(e||0)-Date.now(),0)}function c(t,e,n){return g(t)?"function"===typeof e?{...n,queryKey:t,queryFn:e}:{...e,queryKey:t}:t}function a(t,e,n){return g(t)?"function"===typeof e?{...n,mutationKey:t,mutationFn:e}:{...e,mutationKey:t}:"function"===typeof t?{...e,mutationFn:t}:{...t}}function l(t,e,n){return g(t)?[{...e,queryKey:t},n]:[t||{},e]}function h(t,e){const{type:n="all",exact:r,fetchStatus:s,predicate:i,queryKey:o,stale:u}=t;if(g(o))if(r){if(e.queryHash!==f(o,e.options))return!1}else if(!y(e.queryKey,o))return!1;if("all"!==n){const t=e.isActive();if("active"===n&&!t)return!1;if("inactive"===n&&t)return!1}return("boolean"!==typeof u||e.isStale()===u)&&(("undefined"===typeof s||s===e.state.fetchStatus)&&!(i&&!i(e)))}function d(t,e){const{exact:n,fetching:r,predicate:s,mutationKey:i}=t;if(g(i)){if(!e.options.mutationKey)return!1;if(n){if(p(e.options.mutationKey)!==p(i))return!1}else if(!y(e.options.mutationKey,i))return!1}return("boolean"!==typeof r||"loading"===e.state.status===r)&&!(s&&!s(e))}function f(t,e){return((null==e?void 0:e.queryKeyHashFn)||p)(t)}function p(t){return JSON.stringify(t,(t,e)=>S(e)?Object.keys(e).sort().reduce((t,n)=>(t[n]=e[n],t),{}):e)}function y(t,e){return v(t,e)}function v(t,e){return t===e||typeof t===typeof e&&(!(!t||!e||"object"!==typeof t||"object"!==typeof e)&&!Object.keys(e).some(n=>!v(t[n],e[n])))}function b(t,e){if(t===e)return t;const n=m(t)&&m(e);if(n||S(t)&&S(e)){const r=n?t.length:Object.keys(t).length,s=n?e:Object.keys(e),i=s.length,o=n?[]:{};let u=0;for(let c=0;c<i;c++){const r=n?c:s[c];o[r]=b(t[r],e[r]),o[r]===t[r]&&u++}return r===i&&u===r?t:o}return e}function R(t,e){if(t&&!e||e&&!t)return!1;for(const n in t)if(t[n]!==e[n])return!1;return!0}function m(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function S(t){if(!w(t))return!1;const e=t.constructor;if("undefined"===typeof e)return!0;const n=e.prototype;return!!w(n)&&!!n.hasOwnProperty("isPrototypeOf")}function w(t){return"[object Object]"===Object.prototype.toString.call(t)}function g(t){return Array.isArray(t)}function E(t){return new Promise(e=>{setTimeout(e,t)})}function C(t){E(0).then(t)}function O(){if("function"===typeof AbortController)return new AbortController}function Q(t,e,n){return null!=n.isDataEqual&&n.isDataEqual(t,e)?t:"function"===typeof n.structuralSharing?n.structuralSharing(t,e):!1!==n.structuralSharing?b(t,e):e}},8873:(t,e,n)=>{n.d(e,{r:()=>r});const r=n(9461).useSyncExternalStore},9461:(t,e,n)=>{t.exports=n(2330)},9781:(t,e,n)=>{n.d(e,{jE:()=>u});var r=n(5043);const s=r.createContext(void 0),i=r.createContext(!1);function o(t,e){return t||(e&&"undefined"!==typeof window?(window.ReactQueryClientContext||(window.ReactQueryClientContext=s),window.ReactQueryClientContext):s)}const u=function(){let{context:t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const e=r.useContext(o(t,r.useContext(i)));if(!e)throw new Error("No QueryClient set, use QueryClientProvider to set one");return e}},9939:(t,e,n)=>{n.d(e,{j:()=>s});var r=n(8664);const s=function(){let t=[],e=0,n=t=>{t()},s=t=>{t()};const i=s=>{e?t.push(s):(0,r.G6)(()=>{n(s)})},o=()=>{const e=t;t=[],e.length&&(0,r.G6)(()=>{s(()=>{e.forEach(t=>{n(t)})})})};return{batch:t=>{let n;e++;try{n=t()}finally{e--,e||o()}return n},batchCalls:t=>function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];i(()=>{t(...n)})},schedule:i,setNotifyFunction:t=>{n=t},setBatchNotifyFunction:t=>{s=t}}}()}}]);
//# sourceMappingURL=604.14d0e974.chunk.js.map