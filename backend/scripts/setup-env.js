const fs = require('fs');
const path = require('path');
const readline = require('readline');
const crypto = require('crypto');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Paths
const rootDir = path.resolve(__dirname, '../..');
const envExamplePath = path.join(rootDir, '.env.example');
const envPath = path.join(rootDir, '.env');

// Check if .env already exists
if (fs.existsSync(envPath)) {
  console.log('.env file already exists. Do you want to overwrite it? (y/n)');
  rl.question('> ', (answer) => {
    if (answer.toLowerCase() === 'y') {
      setupEnv();
    } else {
      console.log('Setup cancelled.');
      rl.close();
    }
  });
} else {
  setupEnv();
}

async function setupEnv() {
  try {
    // Check if .env.example exists
    if (!fs.existsSync(envExamplePath)) {
      throw new Error('.env.example file not found. Please make sure it exists in the project root.');
    }
    
    // Read the example file
    const envExample = fs.readFileSync(envExamplePath, 'utf8');
    
    // Create a new .env file with the example content
    fs.writeFileSync(envPath, envExample);
    
    console.log('✅ .env file created successfully!');
    console.log('Please update the .env file with your configuration.');
    
    // Generate secure random values for secrets
    const generateRandomString = (length = 32) => {
      return crypto.randomBytes(Math.ceil(length / 2))
        .toString('hex')
        .slice(0, length);
    };

    // Read the newly created .env file
    let envContent = fs.readFileSync(envPath, 'utf8');
    
    // Update the .env file with secure random values for secrets
    const updatedEnv = envContent
      .replace(/your_jwt_secret_key_here/g, generateRandomString(64))
      .replace(/your_jwt_temp_secret_here/g, generateRandomString(64))
      .replace(/your_cookie_secret_here/g, generateRandomString(64));
    
    // Write the updated content back to .env
    fs.writeFileSync(envPath, updatedEnv);
    
    console.log('🔑 Generated secure random values for JWT and cookie secrets.');
    
    // Copy .env to backend/.env if it doesn't exist
    const backendEnvPath = path.join(__dirname, '../.env');
    if (!fs.existsSync(backendEnvPath)) {
      fs.copyFileSync(envPath, backendEnvPath);
      console.log('📁 Copied .env to backend/.env');
    }
    
  } catch (error) {
    console.error('❌ Error setting up .env file:', error.message);
    if (error.code === 'ENOENT') {
      console.log('💡 Make sure you run this script from the project root directory.');
    }
  } finally {
    rl.close();
  }
}
