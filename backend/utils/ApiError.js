const logger = require('./logger');

/**
 * Custom API error class that extends the built-in Error class
 */
class ApiError extends Error {
  /**
   * Create an API error
   * @param {number} statusCode - HTTP status code
   * @param {string} message - Error message
   * @param {Array} errors - Array of error details
   * @param {string} code - Custom error code
   */
  constructor(statusCode, message, errors = [], code = null) {
    super(message);
    
    // Set the prototype explicitly (needed for instanceof checks in ES5)
    Object.setPrototypeOf(this, new.target.prototype);
    
    this.name = this.constructor.name;
    this.statusCode = statusCode;
    this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';
    this.code = code || `ERR_${statusCode}`;
    this.errors = errors;
    this.isOperational = true; // Mark as operational error (trusted error)
    this.timestamp = new Date().toISOString();
    
    // Capture stack trace (excluding constructor call from it)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }
  
  /**
   * Create a bad request error (400)
   * @param {string} message - Error message
   * @param {Array} errors - Array of error details
   * @param {string} code - Custom error code
   * @returns {ApiError} New API error instance
   */
  static badRequest(message = 'Bad Request', errors = [], code = null) {
    return new ApiError(400, message, errors, code);
  }

  /**
   * Create a 401 Unauthorized error
   * @param {string} message - Error message
   * @param {Array} errors - Array of error details
   * @param {string} code - Custom error code
   * @returns {ApiError} New API error instance
   */
  static unauthorized(message = 'Unauthorized', errors = [], code = null) {
    return new ApiError(401, message, errors, code);
  }

  /**
   * Create a 403 Forbidden error
   * @param {string} message - Error message
   * @param {Array} errors - Array of error details
   * @param {string} code - Custom error code
   * @returns {ApiError} New API error instance
   */
  static forbidden(message = 'Forbidden', errors = [], code = null) {
    return new ApiError(403, message, errors, code);
  }

  /**
   * Create a 404 Not Found error
   * @param {string} message - Error message
   * @param {Array} errors - Array of error details
   * @param {string} code - Custom error code
   * @returns {ApiError} New API error instance
   */
  static notFound(message = 'Not Found', errors = [], code = null) {
    return new ApiError(404, message, errors, code);
  }

  /**
   * Create a 409 Conflict error
   * @param {string} message - Error message
   * @param {Array} errors - Array of error details
   * @param {string} code - Custom error code
   * @returns {ApiError} New API error instance
   */
  static conflict(message = 'Conflict', errors = [], code = null) {
    return new ApiError(409, message, errors, code);
  }

  /**
   * Create a 422 Unprocessable Entity error
   * @param {string} message - Error message
   * @param {Array} errors - Array of validation errors
   * @param {string} code - Custom error code
   * @returns {ApiError} New API error instance
   */
  static validationError(message = 'Validation Error', errors = [], code = null) {
    return new ApiError(422, message, errors, code);
  }

  /**
   * Create a 429 Too Many Requests error
   * @param {string} message - Error message
   * @param {Array} errors - Array of error details
   * @param {string} code - Custom error code
   * @returns {ApiError} New API error instance
   */
  static tooManyRequests(message = 'Too Many Requests', errors = [], code = 'TOO_MANY_REQUESTS') {
    return new ApiError(429, message, errors, code);
  }

  /**
   * Create a 500 Internal Server Error
   * @param {string} message - Error message
   * @param {Array} errors - Array of error details
   * @param {string} code - Custom error code
   * @returns {ApiError} New API error instance
   */
  static internal(message = 'Internal Server Error', errors = [], code = null) {
    return new ApiError(500, message, errors, code);
  }
}

module.exports = ApiError;
