"use client";

import { useState } from "react";
import { Card, CardContent } from "../../components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "../../components/ui/table";
import { Badge } from "../../components/ui/badge";
import { Button } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import { useCollections } from "../../services/api/merchant";
import { Skeleton } from "../../components/ui/skeleton";
import { Search, RefreshCw, Download } from "lucide-react";

export default function CollectionsPage() {
  const [searchTerm, setSearchTerm] = useState("");
  
  const { 
    data: collectionsData = { data: [], total: 0, page: 1, limit: 10, totalPages: 1 }, 
    isLoading, 
    refetch 
  } = useCollections({ 
    search: searchTerm || undefined,
    sortBy: 'createdAt',
    sortOrder: 'desc',
    page: 1,
    limit: 10
  });
  
  const collections = collectionsData?.data || [];
  const { total = 0, page = 1, totalPages = 1 } = collectionsData || {};

  const handleExport = () => {
    // TODO: Implement export functionality
    console.log("Exporting collections data");
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold">Collections</h1>
          <p className="text-muted-foreground">
            Payments collected by your traders
          </p>
        </div>
        <div className="flex items-center space-x-2 w-full sm:w-auto">
          <div className="relative w-full sm:w-64">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search collections..."
              className="w-full pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <Button variant="outline" size="icon" onClick={handleExport}>
            <Download className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="icon" onClick={() => refetch()}>
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <Card>
        <CardContent className="p-6">
          {isLoading ? (
            <div className="space-y-4">
              {[1, 2, 3, 4, 5].map((i) => (
                <Skeleton key={i} className="h-12 w-full" />
              ))}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Reference</TableHead>
                  <TableHead>Trader</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Date</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {collections.map((collection) => (
                  <TableRow key={collection.id}>
                    <TableCell className="font-medium">
                      {collection.reference}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <div className="h-8 w-8 rounded-full bg-muted flex items-center justify-center">
                          {collection.trader?.name?.charAt(0) || 'T'}
                        </div>
                        <span>{collection.trader?.name || 'Unknown Trader'}</span>
                      </div>
                    </TableCell>
                    <td className="font-medium">
                      ${collection.amount?.toFixed(2)}
                    </td>
                    <td>
                      <Badge variant={
                        collection.status === 'completed' ? 'success' : 
                        collection.status === 'pending' ? 'warning' : 'destructive'
                      }>
                        {collection.status}
                      </Badge>
                    </td>
                    <td>
                      {new Date(collection.createdAt).toLocaleDateString()}
                    </td>
                  </TableRow>
                ))}
                {collections.length === 0 && !isLoading && (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                      No collections found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
