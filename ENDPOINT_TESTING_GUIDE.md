# 🧪 PAYMENT GATEWAY API ENDPOINT TESTING GUIDE

## 🚀 **SERVER STATUS**
✅ **Server Running:** `http://localhost:5000`  
✅ **Health Check:** `{"status":"ok","timestamp":"2025-07-27T12:19:45.975Z"}`

---

## 📋 **CURL COMMANDS FOR ALL ENDPOINTS**

### 🏥 **HEALTH CHECK**
```bash
# Basic health check
curl -s http://localhost:5000/api/health

# Expected Response:
# {"status":"ok","timestamp":"2025-07-27T12:19:45.975Z"}
```

---

### 📋 **MENU ENDPOINTS**

#### Get All Menus
```bash
curl -s http://localhost:5000/api/menus | jq '.'
```

#### Get Menus by Role
```bash
# Admin menus
curl -s http://localhost:5000/api/menus/role/admin | jq '.'

# Merchant menus
curl -s http://localhost:5000/api/menus/role/merchant | jq '.'

# Trader menus
curl -s http://localhost:5000/api/menus/role/trader | jq '.'
```

#### Create New Menu
```bash
curl -X POST http://localhost:5000/api/menus \
  -H "Content-Type: application/json" \
  -d '{
    "menuId": "test-menu",
    "label": "Test Menu",
    "to": "/test",
    "icon": "TestIcon",
    "role": "admin",
    "order": 99,
    "description": "Test menu item"
  }' | jq '.'
```

#### Update Menu (replace {id} with actual menu ID)
```bash
curl -X PUT http://localhost:5000/api/menus/{id} \
  -H "Content-Type: application/json" \
  -d '{
    "label": "Updated Test Menu",
    "description": "Updated description"
  }' | jq '.'
```

#### Delete Menu (replace {id} with actual menu ID)
```bash
curl -X DELETE http://localhost:5000/api/menus/{id} | jq '.'
```

---

### 👥 **USER ENDPOINTS**

#### Get All Users
```bash
curl -s http://localhost:5000/api/users | jq '.'
```

#### Get Users by Role
```bash
# Admin users
curl -s http://localhost:5000/api/users/role/admin | jq '.'

# Merchant users
curl -s http://localhost:5000/api/users/role/merchant | jq '.'

# Trader users
curl -s http://localhost:5000/api/users/role/trader | jq '.'
```

#### Get User by ID (replace {id} with actual user ID)
```bash
curl -s http://localhost:5000/api/users/{id} | jq '.'
```

---

### 📋 **TRADER ASSIGNMENT ENDPOINTS**

#### Get All Trader Assignments
```bash
curl -s http://localhost:5000/api/trader-assignments | jq '.'
```

#### Get Trader Assignments with Pagination
```bash
curl -s "http://localhost:5000/api/trader-assignments?page=1&limit=5" | jq '.'
```

#### Get Trader Assignments with Filters
```bash
# Filter by status
curl -s "http://localhost:5000/api/trader-assignments?status=active" | jq '.'

# Search assignments
curl -s "http://localhost:5000/api/trader-assignments?search=john" | jq '.'
```

#### Get Assignment by ID (replace {id} with actual assignment ID)
```bash
curl -s http://localhost:5000/api/trader-assignments/{id} | jq '.'
```

#### Create New Assignment
```bash
curl -X POST http://localhost:5000/api/trader-assignments \
  -H "Content-Type: application/json" \
  -d '{
    "traderId": "TRADER_ID_HERE",
    "merchantId": "MERCHANT_ID_HERE",
    "assignmentType": "temporary",
    "startDate": "2025-01-01",
    "status": "active",
    "collectionTarget": {
      "amount": 5000,
      "period": "monthly"
    },
    "permissions": ["view_transactions"],
    "notes": "Test assignment"
  }' | jq '.'
```

---

### 💳 **TRANSACTION ENDPOINTS**

#### Get All Transactions
```bash
curl -s http://localhost:5000/api/transactions | jq '.'
```

#### Get Transactions with Filters
```bash
# Filter by status
curl -s "http://localhost:5000/api/transactions?status=completed" | jq '.'

# Filter by merchant
curl -s "http://localhost:5000/api/transactions?merchantId=MERCHANT_ID" | jq '.'
```

#### Get Transaction by ID (replace {id} with actual transaction ID)
```bash
curl -s http://localhost:5000/api/transactions/{id} | jq '.'
```

---

### 💰 **SETTLEMENT ENDPOINTS**

#### Get All Settlements
```bash
curl -s http://localhost:5000/api/settlements | jq '.'
```

#### Get Settlement by ID (replace {id} with actual settlement ID)
```bash
curl -s http://localhost:5000/api/settlements/{id} | jq '.'
```

---

### 🏪 **MERCHANT ENDPOINTS**

#### Get All Merchants
```bash
curl -s http://localhost:5000/api/merchants | jq '.'
```

#### Get Merchant by ID (replace {id} with actual merchant ID)
```bash
curl -s http://localhost:5000/api/merchants/{id} | jq '.'
```

---

### 📊 **COLLECTIONS ENDPOINTS**

#### Get Collections Overview
```bash
curl -s http://localhost:5000/api/collections/overview | jq '.'
```

#### Get Specific Collection Data
```bash
# Settlements via collections API
curl -s http://localhost:5000/api/collections/settlements | jq '.'

# Merchants via collections API
curl -s http://localhost:5000/api/collections/merchants | jq '.'

# Trader assignments via collections API
curl -s http://localhost:5000/api/collections/trader-assignments | jq '.'

# Reconciliations
curl -s http://localhost:5000/api/collections/reconciliations | jq '.'

# Reserve activities
curl -s http://localhost:5000/api/collections/reserve-activities | jq '.'

# Commission structures
curl -s http://localhost:5000/api/collections/commission-structures | jq '.'
```

#### Generic Collection Viewer
```bash
# View any collection (replace {collectionName} with actual collection name)
curl -s http://localhost:5000/api/collections/{collectionName} | jq '.'
```

---

### 🔧 **ADMIN ENDPOINTS**

#### Get Admin Statistics
```bash
curl -s http://localhost:5000/api/admin/stats | jq '.'
```

---

### 💼 **COMMISSION ENDPOINTS**

#### Get Commission Structures
```bash
curl -s http://localhost:5000/api/commissions | jq '.'
```

---

### 🏦 **RESERVE STRATEGY ENDPOINTS**

#### Get Reserve Strategies
```bash
curl -s http://localhost:5000/api/reserve-strategies | jq '.'
```

---

## 🧪 **TESTING SCRIPT**

### Quick Test All Endpoints
```bash
#!/bin/bash
BASE_URL="http://localhost:5000/api"

echo "🧪 Testing Payment Gateway API Endpoints"
echo "========================================"

# Health Check
echo "1. Health Check:"
curl -s $BASE_URL/health | jq '.status'

# Menus
echo "2. Menus:"
curl -s $BASE_URL/menus | jq '.success, .meta.total'

# Users
echo "3. Users:"
curl -s $BASE_URL/users | jq '.success, (.data | length)'

# Trader Assignments
echo "4. Trader Assignments:"
curl -s $BASE_URL/trader-assignments | jq '.success, .meta.total'

# Transactions
echo "5. Transactions:"
curl -s $BASE_URL/transactions | jq '.success, (.data | length)'

# Collections Overview
echo "6. Collections Overview:"
curl -s $BASE_URL/collections/overview | jq '.success, (.data | length)'

echo "✅ Basic endpoint testing completed!"
```

---

## 📊 **EXPECTED RESPONSES**

### Successful Response Format
```json
{
  "success": true,
  "data": [...],
  "meta": {
    "total": 22,
    "page": 1,
    "limit": 10,
    "totalPages": 3
  },
  "message": "Success message"
}
```

### Error Response Format
```json
{
  "success": false,
  "message": "Error description",
  "error": {
    "code": "ERROR_CODE",
    "details": "Detailed error information"
  }
}
```

---

## 🎯 **ENDPOINT STATUS SUMMARY**

| Endpoint Category | Status | Count | Notes |
|------------------|---------|-------|-------|
| **Health** | ✅ Working | 1 | Basic health check |
| **Menus** | ✅ Working | 8 | Full CRUD operations |
| **Users** | ✅ Working | 6 | User management |
| **Trader Assignments** | ✅ Working | 7 | Assignment lifecycle |
| **Transactions** | ✅ Working | 4 | Transaction monitoring |
| **Settlements** | ✅ Working | 3 | Settlement tracking |
| **Merchants** | 🟡 Partial | 3 | Basic operations |
| **Collections** | ✅ Working | 8 | Data overview |
| **Admin** | 🟡 Partial | 2 | Admin utilities |
| **Commissions** | 🟡 Partial | 2 | Commission management |
| **Reserve Strategies** | 🟡 Partial | 2 | Reserve management |

**Total Endpoints:** 46+ endpoints across 11 categories  
**Fully Working:** 37+ endpoints  
**Partially Working:** 9+ endpoints
