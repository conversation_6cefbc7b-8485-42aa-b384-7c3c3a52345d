import React from 'react';

// UI Components
import { <PERSON><PERSON> } from '../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '../components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { Skeleton } from '../components/ui/skeleton';

// Charts
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts';

// Icons
import { Users, CreditCard, DollarSign, Activity, RefreshCw, LogOut, Shield, UserCheck } from 'lucide-react';

// Components
import { TraderManagement } from '../components/TraderManagement';
import { useToast } from '../components/ui/use-toast';

// Types
interface DashboardStats {
  totalUsers: number;
  totalTransactions: number;
  totalRevenue: number;
  activeMerchants: number;
}

interface Transaction {
  id: string;
  date: string;
  merchant: string;
  amount: number;
  status: 'completed' | 'pending' | 'failed';
  currency: string;
}

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api/v1';

// Format currency
const formatCurrency = (amount: number, currency: string = 'USD'): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
};

// Format date
const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// Stats card type
interface StatCard {
  name: string;
  value: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
  change: string;
  trend: 'up' | 'down';
}

const AdminDashboard: React.FC = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState<DashboardStats>({
    totalUsers: 0,
    totalTransactions: 0,
    totalRevenue: 0,
    activeMerchants: 0
  });
  
  const [recentTransactions, setRecentTransactions] = useState<Transaction[]>([]);
  const [revenueData, setRevenueData] = useState<{name: string; revenue: number}[]>([]);

  // Stats cards data
  const statCards: StatCard[] = [
    {
      name: 'Total Users',
      value: stats.totalUsers.toLocaleString(),
      icon: Users,
      description: 'Total registered users',
      change: '+12.5%',
      trend: 'up'
    },
    {
      name: 'Total Transactions',
      value: stats.totalTransactions.toLocaleString(),
      icon: CreditCard,
      description: 'All-time transactions',
      change: '+8.2%',
      trend: 'up'
    },
    {
      name: 'Total Revenue',
      value: formatCurrency(stats.totalRevenue),
      icon: DollarSign,
      description: 'Total revenue generated',
      change: '+15.3%',
      trend: 'up'
    },
    {
      name: 'Active Merchants',
      value: stats.activeMerchants.toString(),
      icon: UserCheck,
      description: 'Currently active merchants',
      change: '+4.1%',
      trend: 'up'
    }
  ];

  // Fetch dashboard data
  const fetchDashboardData = async () => {
    try {
      setIsLoading(true);
      
      // In a real app, you would make actual API calls here
      // const statsResponse = await axios.get(`${API_URL}/admin/dashboard/stats`);
      // const transactionsResponse = await axios.get(`${API_URL}/admin/transactions/recent`);
      // const revenueResponse = await axios.get(`${API_URL}/admin/revenue/monthly`);
      
      // Simulate API call with timeout
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock data - replace with actual API calls
      setStats({
        totalUsers: 1245,
        totalTransactions: 12478,
        totalRevenue: 124560.78,
        activeMerchants: 92
      });
      
      setRecentTransactions([
        { 
          id: 'TXN' + Math.floor(Math.random() * 1000000),
          date: new Date().toISOString(),
          merchant: 'Test Merchant',
          amount: 100.50,
          status: 'completed',
          currency: 'USD'
        },
        { 
          id: 'TXN' + Math.floor(Math.random() * 1000000),
          date: new Date(Date.now() - 86400000).toISOString(),
          merchant: 'Another Business',
          amount: 75.25,
          status: 'pending',
          currency: 'USD'
        },
        { 
          id: 'TXN' + Math.floor(Math.random() * 1000000),
          date: new Date(Date.now() - 172800000).toISOString(),
          merchant: 'Online Store',
          amount: 200.00,
          status: 'completed',
          currency: 'USD'
        }
      ]);
      
      setRevenueData([
        { name: 'Jan', revenue: 4000 },
        { name: 'Feb', revenue: 3000 },
        { name: 'Mar', revenue: 5000 },
        { name: 'Apr', revenue: 4780 },
        { name: 'May', revenue: 3890 },
        { name: 'Jun', revenue: 6390 },
      ]);
      
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      toast({
        title: 'Error',
        description: 'Failed to load dashboard data. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();
    
    // Set up auto-refresh every 5 minutes
    const interval = setInterval(fetchDashboardData, 5 * 60 * 1000);
    
    return () => clearInterval(interval);
  }, []);
  
  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Logout error:', error);
      toast({
        title: 'Error',
        description: 'Failed to log out. Please try again.',
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8 flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
            <p className="text-sm text-gray-500">Welcome back, {user?.name || 'Admin'}</p>
          </div>
          <div className="flex items-center space-x-4">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={fetchDashboardData}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button variant="outline" size="sm" onClick={handleLogout}>
              <LogOut className="h-4 w-4 mr-2" />
              Logout
            </Button>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {/* Stats Grid */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-8">
          {isLoading ? (
            // Loading skeleton for stats
            Array(4).fill(0).map((_, i) => (
              <Card key={`stat-skeleton-${i}`}>
                <CardHeader className="space-y-2">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-6 w-16" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-4 w-32" />
                </CardContent>
              </Card>
            ))
          ) : (
            // Actual stats cards
            statCards.map((stat) => (
              <Card key={stat.name} className="hover:shadow-md transition-shadow">
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-center">
                    <p className="text-sm font-medium text-muted-foreground">
                      {stat.name}
                    </p>
                    <stat.icon className="h-4 w-4 text-muted-foreground" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stat.value}</div>
                  <p className="text-xs text-muted-foreground mt-1">
                    <span className={stat.trend === 'up' ? 'text-green-500' : 'text-red-500'}>
                      {stat.change}
                    </span>{' '}
                    {stat.description}
                  </p>
                </CardContent>
              </Card>
            ))
          )}
        </div>

        <Tabs defaultValue="overview" className="space-y-4">
          <div className="flex justify-between items-center">
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="transactions">Transactions</TabsTrigger>
              <TabsTrigger value="traders">Traders</TabsTrigger>
              <TabsTrigger value="security">
                <Shield className="h-4 w-4 mr-2" />
                Security
              </TabsTrigger>
            </TabsList>
            <div className="text-sm text-muted-foreground">
              Last updated: {new Date().toLocaleTimeString()}
            </div>
          </div>

          <TabsContent value="overview" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Revenue Overview</CardTitle>
                <CardDescription>
                  Monthly revenue trends and analytics
                </CardDescription>
              </CardHeader>
              <CardContent className="pl-2">
                {isLoading ? (
                  <div className="h-[300px] flex items-center justify-center">
                    <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground" />
                  </div>
                ) : (
                  <div className="h-[300px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={revenueData}>
                        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                        <XAxis 
                          dataKey="name" 
                          tick={{ fill: '#6b7280' }}
                          axisLine={false}
                        />
                        <YAxis 
                          tick={{ fill: '#6b7280' }}
                          axisLine={false}
                          tickFormatter={(value) => `$${value.toLocaleString()}`}
                        />
                        <Tooltip 
                          formatter={(value: number) => [`$${value.toLocaleString()}`, 'Revenue']}
                          labelFormatter={(label) => `Month: ${label}`}
                        />
                        <Legend />
                        <Bar 
                          dataKey="revenue" 
                          name="Monthly Revenue"
                          fill="#6366f1" 
                          radius={[4, 4, 0, 0]}
                        />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="transactions">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>Recent Transactions</CardTitle>
                    <CardDescription>
                      Latest transactions across all merchants
                    </CardDescription>
                  </div>
                  <Button variant="outline" size="sm" onClick={fetchDashboardData}>
                    <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                    Refresh
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="space-y-4">
                    {Array(5).fill(0).map((_, i) => (
                      <Skeleton key={`txn-skeleton-${i}`} className="h-12 w-full" />
                    ))}
                  </div>
                ) : (
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>ID</TableHead>
                          <TableHead>Date & Time</TableHead>
                          <TableHead>Merchant</TableHead>
                          <TableHead className="text-right">Amount</TableHead>
                          <TableHead>Status</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {recentTransactions.length > 0 ? (
                          recentTransactions.map((txn) => (
                            <TableRow key={txn.id} className="hover:bg-gray-50">
                              <TableCell className="font-medium">
                                <span className="font-mono text-xs">{txn.id}</span>
                              </TableCell>
                              <TableCell>
                                <div className="text-sm">
                                  {formatDate(txn.date)}
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="font-medium">{txn.merchant}</div>
                              </TableCell>
                              <TableCell className="text-right font-medium">
                                {formatCurrency(txn.amount, txn.currency)}
                              </TableCell>
                              <TableCell>
                                <span 
                                  className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                    txn.status === 'completed' 
                                      ? 'bg-green-100 text-green-800' 
                                      : txn.status === 'pending'
                                      ? 'bg-yellow-100 text-yellow-800'
                                      : 'bg-red-100 text-red-800'
                                  }`}
                                >
                                  {txn.status.charAt(0).toUpperCase() + txn.status.slice(1)}
                                </span>
                              </TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                              No transactions found
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="traders">
            <Card>
              <CardHeader>
                <CardTitle>Trader Management</CardTitle>
                <CardDescription>
                  Manage traders and their assignments
                </CardDescription>
              </CardHeader>
              <CardContent>
                <TraderManagement />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="security">
            <Card>
              <CardHeader>
                <CardTitle>Security Settings</CardTitle>
                <CardDescription>
                  Manage security settings and access controls
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="p-4 border rounded-lg">
                  <h3 className="font-medium mb-2">Two-Factor Authentication</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Add an extra layer of security to your admin account
                  </p>
                  <Button variant="outline" size="sm">
                    Enable 2FA
                  </Button>
                </div>
                
                <div className="p-4 border rounded-lg">
                  <h3 className="font-medium mb-2">Session Management</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    View and manage active sessions
                  </p>
                  <Button variant="outline" size="sm">
                    View Active Sessions
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </main>
    </div>
  )
}

export default AdminDashboard
