import { Dialog, DialogContent, DialogHeader, DialogTitle } from '../ui/dialog';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { format } from 'date-fns';

interface TraderDetailsModalProps {
  trader: {
    _id: string;
    name: string;
    email: string;
    phone?: string;
    status: 'active' | 'inactive' | 'suspended';
    lastActive?: string;
    createdAt: string;
    updatedAt: string;
    assignedMerchants: number;
    totalCollections: number;
    totalAmountCollected: number;
  };
  isOpen: boolean;
  onClose: () => void;
  onStatusChange: (status: 'active' | 'inactive' | 'suspended') => void;
}

export function TraderDetailsModal({ 
  trader, 
  isOpen, 
  onClose, 
  onStatusChange 
}: TraderDetailsModalProps) {
  if (!trader) return null;

  const statusVariant = {
    active: 'default',
    inactive: 'secondary',
    suspended: 'destructive'
  } as const;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Trader Details</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">{trader.name}</h3>
            <Badge variant={statusVariant[trader.status]}>
              {trader.status.charAt(0).toUpperCase() + trader.status.slice(1)}
            </Badge>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-muted-foreground">Email</p>
              <p>{trader.email}</p>
            </div>
            {trader.phone && (
              <div>
                <p className="text-sm text-muted-foreground">Phone</p>
                <p>{trader.phone}</p>
              </div>
            )}
            <div>
              <p className="text-sm text-muted-foreground">Member Since</p>
              <p>{format(new Date(trader.createdAt), 'MMM d, yyyy')}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Last Active</p>
              <p>{trader.lastActive ? format(new Date(trader.lastActive), 'MMM d, yyyy') : 'N/A'}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Assigned Merchants</p>
              <p>{trader.assignedMerchants}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Total Collections</p>
              <p>{trader.totalCollections}</p>
            </div>
            <div className="col-span-2">
              <p className="text-sm text-muted-foreground">Total Amount Collected</p>
              <p className="text-lg font-semibold">{formatCurrency(trader.totalAmountCollected || 0)}</p>
            </div>
          </div>
          
          <div className="pt-4 border-t">
            <h4 className="text-sm font-medium mb-2">Change Status</h4>
            <div className="flex gap-2">
              {(['active', 'inactive', 'suspended'] as const).map((status) => (
                <Button
                  key={status}
                  variant={trader.status === status ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => onStatusChange(status)}
                  disabled={trader.status === status}
                >
                  {status.charAt(0).toUpperCase() + status.slice(1)}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
