const mongoose = require('mongoose');
const { setupTestDB } = require('../../../fixtures/db');
const checkSettlementAccess = require('../../../../middleware/checkSettlementAccess');
const { createMockRequest, createMockResponse, createNextFunction } = require('../testUtils');
const Settlement = require('../../../../models/Settlement');
const User = require('../../../../models/User');

// Setup test database
setupTestDB();

describe('checkSettlementAccess Middleware', () => {
  let mockReq, mockRes, nextFn;
  let adminUser, financeUser, merchantUser, traderUser;
  let testSettlement;

  beforeEach(async () => {
    // Create test users
    adminUser = await User.create({
      name: 'Admin User',
      email: '<EMAIL>',
      password: 'password123',
      role: 'admin',
      isEmailVerified: true
    });

    // Using 'admin' role since 'finance' is not a valid role in the User model
    financeUser = await User.create({
      name: 'Finance User',
      email: '<EMAIL>',
      password: 'password123',
      role: 'admin', // Changed from 'finance' to 'admin' since 'finance' is not a valid role
      isEmailVerified: true
    });

    merchantUser = await User.create({
      name: 'Merchant User',
      email: '<EMAIL>',
      password: 'password123',
      role: 'merchant',
      isEmailVerified: true
    });

    traderUser = await User.create({
      name: 'Trader User',
      email: '<EMAIL>',
      password: 'password123',
      role: 'trader',
      isEmailVerified: true
    });

    // Create a test settlement between admin and trader with all required fields
    testSettlement = await Settlement.create({
      settlementId: 'SETT' + Date.now(),
      type: 'trader-trader', // Using a valid enum value
      fromParty: adminUser._id,
      toParty: traderUser._id,
      periodStart: new Date('2023-01-01'),
      periodEnd: new Date('2023-01-31'),
      totalAmount: 1000, // Required field
      fees: {
        processing: 10,
        platform: 5,
        commission: 15
      },
      settlementAmount: 970, // totalAmount - sum of fees
      status: 'pending',
      currency: 'USD',
      description: 'Test settlement',
      metadata: { test: true },
      createdBy: adminUser._id,
      paymentMethod: 'bank_transfer'
    });

    // Reset mocks before each test
    mockReq = {
      params: { id: testSettlement._id },
      user: { id: adminUser._id, role: 'admin' }
    };
    mockRes = createMockResponse();
    nextFn = createNextFunction();
  });

  it('should allow admin to access any settlement', async () => {
    mockReq.user = { id: adminUser._id, role: 'admin' };
    
    await checkSettlementAccess(mockReq, mockRes, nextFn);
    
    expect(nextFn).toHaveBeenCalled();
    expect(mockReq.settlement).toBeDefined();
    expect(mockReq.settlement._id.toString()).toBe(testSettlement._id.toString());
  });

  it('should allow finance user to access any settlement', async () => {
    mockReq.user = { id: financeUser._id, role: 'finance' };
    
    await checkSettlementAccess(mockReq, mockRes, nextFn);
    
    expect(nextFn).toHaveBeenCalled();
    expect(mockReq.settlement).toBeDefined();
  });

  it('should allow trader to access their own settlement', async () => {
    // Trader is the toParty in our test settlement
    mockReq.user = { id: traderUser._id, role: 'trader' };
    
    console.log('Test - Trader ID:', traderUser._id);
    console.log('Test - Settlement toParty ID:', testSettlement.toParty);
    
    await checkSettlementAccess(mockReq, mockRes, nextFn);
    
    console.log('Test - After middleware, mockReq.settlement:', mockReq.settlement);
    console.log('Test - nextFn calls:', nextFn.mock.calls);
    
    expect(nextFn).toHaveBeenCalled();
    expect(mockReq.settlement).toBeDefined();
  });

  it('should not allow merchant to access unrelated settlement', async () => {
    mockReq.user = { id: merchantUser._id, role: 'merchant' };
    
    await checkSettlementAccess(mockReq, mockRes, nextFn);
    
    expect(nextFn).toHaveBeenCalledWith(expect.any(Error));
    const error = nextFn.mock.calls[0][0];
    expect(error.statusCode).toBe(403);
    expect(error.message).toMatch(/permission/);
  });

  it('should return 404 for non-existent settlement', async () => {
    mockReq.params.id = new mongoose.Types.ObjectId();
    
    await checkSettlementAccess(mockReq, mockRes, nextFn);
    
    expect(nextFn).toHaveBeenCalledWith(expect.any(Error));
    const error = nextFn.mock.calls[0][0];
    expect(error.statusCode).toBe(404);
  });

  it('should handle invalid settlement ID format', async () => {
    mockReq.params.id = 'invalid-id';
    
    await checkSettlementAccess(mockReq, mockRes, nextFn);
    
    expect(nextFn).toHaveBeenCalledWith(expect.any(Error));
    const error = nextFn.mock.calls[0][0];
    expect(error.statusCode).toBe(400);
  });
});
