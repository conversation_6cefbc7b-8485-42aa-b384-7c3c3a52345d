import { useF<PERSON>, Controller, useFormContext } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '../ui/button';
import {
  Form,
  FormControl,
  FormField as FormFieldPrimitive,
  FormItem,
  FormLabel,
  FormMessage,
} from '../ui/form';
import { Input } from '../ui/input';
import { useToast } from '../ui/use-toast';
import { useUpdateProfile } from '../../hooks/useUser';
import { User, MerchantUser } from '../../contexts/AuthContext';
import { Loader2 } from 'lucide-react';

const profileSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  businessName: z.string().optional(),
  businessType: z.string().optional(),
  businessAddress: z.object({
    street: z.string().optional(),
    city: z.string().optional(),
    state: z.string().optional(),
    country: z.string().optional(),
    postalCode: z.string().optional(),
  }).optional(),
});

type ProfileFormValues = z.infer<typeof profileSchema>;

interface ProfileFormProps {
  user: User;
  onSuccess?: () => void;
}

// Custom FormField component that works with react-hook-form
const FormField = ({
  name,
  label,
  render,
  ...props
}: {
  name: string;
  label: string;
  render: (field: {
    onChange: (value: any) => void;
    onBlur: () => void;
    value: any;
    ref: React.Ref<any>;
  }) => React.ReactNode;
}) => {
  const { control } = useFormContext();
  
  return (
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            {render(field)}
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export function ProfileForm({ user, onSuccess }: ProfileFormProps) {
  const { toast } = useToast();
  const { mutate: updateProfile, isPending } = useUpdateProfile({
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Your profile has been updated successfully.',
      });
      onSuccess?.();
    },
    onError: (error: unknown) => {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update profile';
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    },
  });

  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      name: user.name,
      email: user.email,
      businessName: user.role === 'merchant' ? (user as MerchantUser).businessName : '',
      businessType: '',
      businessAddress: {
        street: '',
        city: '',
        state: '',
        country: '',
        postalCode: '',
      },
    },
  });

  const onSubmit = (data: ProfileFormValues) => {
    updateProfile(data);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            name="name"
            label="Full Name"
            render={(field) => (
              <Input placeholder="John Doe" {...field} />
            )}
          />
          
          <FormField
            name="email"
            label="Email"
            render={(field) => (
              <Input type="email" placeholder="<EMAIL>" disabled {...field} />
            )}
          />

          {user.role === 'merchant' && (
            <>
              <FormField
                name="businessName"
                label="Business Name"
                render={(field) => (
                  <Input placeholder="Acme Inc." {...field} />
                )}
              />
              
              <FormField
                name="businessType"
                label="Business Type"
                render={(field) => (
                  <Input placeholder="Retail" {...field} />
                )}
              />
              
              <div className="col-span-2 space-y-4">
                <h4 className="text-sm font-medium">Business Address</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="md:col-span-2">
                    <FormField
                      name="businessAddress.street"
                      label="Street Address"
                      render={(field) => (
                        <Input placeholder="123 Main St" {...field} />
                      )}
                    />
                  </div>
                  
                  <FormField
                    name="businessAddress.city"
                    label="City"
                    render={(field) => (
                      <Input placeholder="New York" {...field} />
                    )}
                  />
                  
                  <FormField
                    name="businessAddress.state"
                    label="State/Province"
                    render={(field) => (
                      <Input placeholder="NY" {...field} />
                    )}
                  />
                  
                  <FormField
                    name="businessAddress.postalCode"
                    label="Postal Code"
                    render={(field) => (
                      <Input placeholder="10001" {...field} />
                    )}
                  />
                  
                  <FormField
                    name="businessAddress.country"
                    label="Country"
                    render={(field) => (
                      <Input placeholder="United States" {...field} />
                    )}
                  />
                </div>
              </div>
            </>
          )}
        </div>
        
        <div className="flex justify-end">
          <Button type="submit" disabled={isPending}>
            {isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Update Profile
          </Button>
        </div>
      </form>
    </Form>
  );
}
