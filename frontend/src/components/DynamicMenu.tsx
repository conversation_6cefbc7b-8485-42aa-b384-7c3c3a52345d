import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  LayoutDashboard, 
  Users, 
  UserCheck, 
  Store, 
  UserPlus, 
  CreditCard, 
  DollarSign, 
  Calculator, 
  Webhook, 
  FileText, 
  Settings,
  Building,
  Target,
  TrendingUp,
  User,
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import { Badge } from './ui/badge';
import { Button } from './ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from './ui/collapsible';
import { menuService, MenuItem, mockMenuData } from '../services/menuService';

// Icon mapping
const iconMap: Record<string, React.ComponentType<any>> = {
  LayoutDashboard,
  Users,
  UserCheck,
  Store,
  UserPlus,
  CreditCard,
  DollarSign,
  Calculator,
  Webhook,
  FileText,
  Settings,
  Building,
  Target,
  TrendingUp,
  User
};

interface DynamicMenuProps {
  role: 'admin' | 'merchant' | 'trader';
  className?: string;
  onMenuClick?: (menuItem: MenuItem) => void;
}

const DynamicMenu: React.FC<DynamicMenuProps> = ({ role, className = '', onMenuClick }) => {
  const [menus, setMenus] = useState<MenuItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedMenus, setExpandedMenus] = useState<Set<string>>(new Set());
  const location = useLocation();

  useEffect(() => {
    fetchMenus();
  }, [role]);

  const fetchMenus = async () => {
    try {
      setLoading(true);
      setError(null);

      // Try to fetch from API
      try {
        const menus = await menuService.getMenuByRole(role);
        if (menus && menus.length > 0) {
          setMenus(menus);
        } else {
          throw new Error('No menus found for this role');
        }
      } catch (apiError) {
        console.log('API not available, using mock data');
        // Fallback to mock data
        setMenus(mockMenuData[role] || []);
      }
    } catch (err) {
      setError('Failed to load menus');
      console.error('Menu fetch error:', err);
      // Use mock data as final fallback
      setMenus(mockMenuData[role] || []);
    } finally {
      setLoading(false);
    }
  };

  const toggleExpanded = (menuId: string) => {
    const newExpanded = new Set(expandedMenus);
    if (newExpanded.has(menuId)) {
      newExpanded.delete(menuId);
    } else {
      newExpanded.add(menuId);
    }
    setExpandedMenus(newExpanded);
  };

  const isActive = (menuPath: string) => {
    return location.pathname === menuPath || location.pathname.startsWith(menuPath + '/');
  };

  const handleMenuClick = (menuItem: MenuItem) => {
    if (onMenuClick) {
      onMenuClick(menuItem);
    }
  };

  const renderIcon = (iconName: string) => {
    const IconComponent = iconMap[iconName] || LayoutDashboard;
    return <IconComponent className="h-4 w-4" />;
  };

  const renderBadge = (badge?: MenuItem['badge']) => {
    if (!badge) return null;
    
    const colorClasses = {
      red: 'bg-red-100 text-red-800',
      blue: 'bg-blue-100 text-blue-800',
      green: 'bg-green-100 text-green-800',
      yellow: 'bg-yellow-100 text-yellow-800',
      purple: 'bg-purple-100 text-purple-800',
      gray: 'bg-gray-100 text-gray-800'
    };

    return (
      <Badge className={`ml-auto text-xs ${colorClasses[badge.color]}`}>
        {badge.text}
      </Badge>
    );
  };

  const renderMenuItem = (menuItem: MenuItem) => {
    const hasSubmenus = menuItem.submenus && menuItem.submenus.length > 0;
    const isExpanded = expandedMenus.has(menuItem.menuId);
    const active = isActive(menuItem.to);

    if (hasSubmenus) {
      return (
        <Collapsible key={menuItem._id} open={isExpanded} onOpenChange={() => toggleExpanded(menuItem.menuId)}>
          <CollapsibleTrigger asChild>
            <Button
              variant={active ? "secondary" : "ghost"}
              className={`w-full justify-start ${active ? 'bg-secondary' : ''} hover:bg-secondary/80 transition-colors`}
              onClick={() => handleMenuClick(menuItem)}
            >
              {renderIcon(menuItem.icon)}
              <span className="ml-2 flex-1 text-left">{menuItem.label}</span>
              {renderBadge(menuItem.badge)}
              {isExpanded ? (
                <ChevronDown className="h-4 w-4 ml-2 transition-transform" />
              ) : (
                <ChevronRight className="h-4 w-4 ml-2 transition-transform" />
              )}
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="ml-6 mt-1 space-y-1 animate-in slide-in-from-top-1 duration-200">
            {menuItem.submenus?.map(submenu => (
              <Link
                key={submenu._id}
                to={submenu.to}
                className={`flex items-center px-3 py-2 text-sm rounded-md transition-colors ${
                  isActive(submenu.to)
                    ? 'bg-secondary text-secondary-foreground'
                    : 'text-muted-foreground hover:text-foreground hover:bg-secondary/50'
                }`}
                onClick={() => handleMenuClick(submenu)}
              >
                {renderIcon(submenu.icon)}
                <span className="ml-2 flex-1">{submenu.label}</span>
                {renderBadge(submenu.badge)}
              </Link>
            ))}
          </CollapsibleContent>
        </Collapsible>
      );
    }

    return (
      <Link
        key={menuItem._id}
        to={menuItem.to}
        className="block"
        onClick={() => handleMenuClick(menuItem)}
      >
        <Button
          variant={active ? "secondary" : "ghost"}
          className={`w-full justify-start ${active ? 'bg-secondary' : ''} hover:bg-secondary/80 transition-colors`}
        >
          {renderIcon(menuItem.icon)}
          <span className="ml-2 flex-1 text-left">{menuItem.label}</span>
          {renderBadge(menuItem.badge)}
          {menuItem.metadata?.isNew && (
            <Badge className="ml-auto bg-green-100 text-green-800 text-xs">NEW</Badge>
          )}
          {menuItem.metadata?.isBeta && (
            <Badge className="ml-auto bg-orange-100 text-orange-800 text-xs">BETA</Badge>
          )}
        </Button>
      </Link>
    );
  };

  if (loading) {
    return (
      <div className={`space-y-2 ${className}`}>
        {[...Array(5)].map((_, i) => (
          <div key={i} className="h-10 bg-gray-200 rounded animate-pulse" />
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className={`p-4 text-center ${className}`}>
        <p className="text-red-600 text-sm">{error}</p>
        <Button 
          variant="outline" 
          size="sm" 
          className="mt-2"
          onClick={fetchMenus}
        >
          Retry
        </Button>
      </div>
    );
  }

  return (
    <nav className={`space-y-1 ${className}`}>
      {/* Menu Header */}
      <div className="mb-4 pb-2 border-b">
        <h3 className="text-sm font-medium text-muted-foreground uppercase tracking-wider">
          {role} Navigation
        </h3>
        <p className="text-xs text-muted-foreground mt-1">
          {menus.length} menu items
        </p>
      </div>

      {/* Menu Items */}
      <div className="space-y-1">
        {menus.map(renderMenuItem)}
      </div>

      {/* Menu Footer */}
      <div className="mt-6 pt-4 border-t">
        <div className="text-xs text-muted-foreground space-y-1">
          <div>• Dynamic menu system</div>
          <div>• Real-time API integration</div>
          <div>• Role-based access control</div>
        </div>
      </div>
    </nav>
  );
};

export default DynamicMenu;
