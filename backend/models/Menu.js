const mongoose = require('mongoose');

const menuSchema = new mongoose.Schema({
  menuId: {
    type: String,
    required: true,
    unique: true
  },
  label: {
    type: String,
    required: true
  },
  to: {
    type: String,
    required: true
  },
  icon: {
    type: String,
    default: 'Menu'
  },
  role: {
    type: String,
    required: true,
    enum: ['admin', 'merchant', 'trader', 'all']
  },
  parentId: {
    type: String,
    default: null // For submenus
  },
  order: {
    type: Number,
    default: 0
  },
  isActive: {
    type: Boolean,
    default: true
  },
  permissions: [{
    type: String
  }],
  description: {
    type: String
  },
  badge: {
    text: String,
    color: {
      type: String,
      enum: ['red', 'blue', 'green', 'yellow', 'purple', 'gray'],
      default: 'blue'
    }
  },
  metadata: {
    category: String,
    tags: [String],
    isNew: { type: Boolean, default: false },
    isBeta: { type: Boolean, default: false }
  }
}, {
  timestamps: true
});

// Index for efficient queries
menuSchema.index({ role: 1, isActive: 1, order: 1 });
menuSchema.index({ parentId: 1 });
menuSchema.index({ menuId: 1 });

// Virtual for submenus
menuSchema.virtual('submenus', {
  ref: 'Menu',
  localField: 'menuId',
  foreignField: 'parentId'
});

// Ensure virtual fields are serialized
menuSchema.set('toJSON', { virtuals: true });

module.exports = mongoose.model('Menu', menuSchema);
