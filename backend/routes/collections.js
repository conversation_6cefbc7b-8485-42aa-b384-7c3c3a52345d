const express = require('express');
const router = express.Router();
const mongoose = require('mongoose');

// Import models
const Transaction = require('../models/Transaction');
const User = require('../models/User');
const Settlement = require('../models/Settlement');
const TraderAssignment = require('../models/TraderAssignment');
const Merchant = require('../models/Merchant');
const Reconciliation = require('../models/Reconciliation');
const ReserveActivity = require('../models/ReserveActivity');
const CommissionStructure = require('../models/CommissionStructure');

// Get all collections overview
router.get('/overview', async (req, res) => {
  try {
    const collections = await mongoose.connection.db.listCollections().toArray();
    const overview = [];
    
    for (const collection of collections) {
      const count = await mongoose.connection.db.collection(collection.name).countDocuments();
      let sampleDoc = null;
      
      if (count > 0) {
        sampleDoc = await mongoose.connection.db.collection(collection.name).findOne();
      }
      
      overview.push({
        name: collection.name,
        count: count,
        fields: sampleDoc ? Object.keys(sampleDoc).filter(key => !key.startsWith('_')).slice(0, 8) : []
      });
    }
    
    res.json({
      success: true,
      data: overview.sort((a, b) => b.count - a.count)
    });
  } catch (error) {
    console.error('Get collections overview error:', error);
    res.status(500).json({ 
      success: false,
      message: 'Failed to get collections overview' 
    });
  }
});

// Get settlements
router.get('/settlements', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;
    
    const settlements = await Settlement.find()
      .populate('fromParty', 'name email businessName role')
      .populate('toParty', 'name email businessName role')
      .populate('transactions', 'transactionId amount status')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Settlement.countDocuments();

    res.json({
      success: true,
      count: settlements.length,
      total: total,
      page: page,
      totalPages: Math.ceil(total / limit),
      data: settlements
    });
  } catch (error) {
    console.error('Get settlements error:', error);
    res.status(500).json({ 
      success: false,
      message: 'Failed to get settlements' 
    });
  }
});

// Get trader assignments
router.get('/trader-assignments', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;
    
    const assignments = await TraderAssignment.find()
      .populate('traderId', 'name email role')
      .populate('merchantId', 'name email businessName role')
      .populate('assignedBy', 'name email role')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await TraderAssignment.countDocuments();

    res.json({
      success: true,
      count: assignments.length,
      total: total,
      page: page,
      totalPages: Math.ceil(total / limit),
      data: assignments
    });
  } catch (error) {
    console.error('Get trader assignments error:', error);
    res.status(500).json({ 
      success: false,
      message: 'Failed to get trader assignments' 
    });
  }
});

// Get merchants
router.get('/merchants', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;
    
    const merchants = await Merchant.find()
      .populate('userId', 'name email role isActive createdAt')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Merchant.countDocuments();

    res.json({
      success: true,
      count: merchants.length,
      total: total,
      page: page,
      totalPages: Math.ceil(total / limit),
      data: merchants
    });
  } catch (error) {
    console.error('Get merchants error:', error);
    res.status(500).json({ 
      success: false,
      message: 'Failed to get merchants' 
    });
  }
});

// Get reconciliations
router.get('/reconciliations', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;
    
    const reconciliations = await Reconciliation.find()
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Reconciliation.countDocuments();

    res.json({
      success: true,
      count: reconciliations.length,
      total: total,
      page: page,
      totalPages: Math.ceil(total / limit),
      data: reconciliations
    });
  } catch (error) {
    console.error('Get reconciliations error:', error);
    res.status(500).json({ 
      success: false,
      message: 'Failed to get reconciliations' 
    });
  }
});

// Get reserve activities
router.get('/reserve-activities', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;
    
    const activities = await ReserveActivity.find()
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await ReserveActivity.countDocuments();

    res.json({
      success: true,
      count: activities.length,
      total: total,
      page: page,
      totalPages: Math.ceil(total / limit),
      data: activities
    });
  } catch (error) {
    console.error('Get reserve activities error:', error);
    res.status(500).json({ 
      success: false,
      message: 'Failed to get reserve activities' 
    });
  }
});

// Get commission structures
router.get('/commission-structures', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;
    
    const commissions = await CommissionStructure.find()
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await CommissionStructure.countDocuments();

    res.json({
      success: true,
      count: commissions.length,
      total: total,
      page: page,
      totalPages: Math.ceil(total / limit),
      data: commissions
    });
  } catch (error) {
    console.error('Get commission structures error:', error);
    res.status(500).json({ 
      success: false,
      message: 'Failed to get commission structures' 
    });
  }
});

// Get any collection by name
router.get('/:collectionName', async (req, res) => {
  try {
    const { collectionName } = req.params;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;
    
    const collection = mongoose.connection.db.collection(collectionName);
    const documents = await collection.find({}).skip(skip).limit(limit).toArray();
    const total = await collection.countDocuments();

    res.json({
      success: true,
      count: documents.length,
      total: total,
      page: page,
      totalPages: Math.ceil(total / limit),
      data: documents
    });
  } catch (error) {
    console.error(`Get ${req.params.collectionName} error:`, error);
    res.status(500).json({ 
      success: false,
      message: `Failed to get ${req.params.collectionName}` 
    });
  }
});

module.exports = router;
