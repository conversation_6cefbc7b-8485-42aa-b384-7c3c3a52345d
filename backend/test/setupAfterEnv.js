// This file runs after Jest has been set up in the test environment
const { setupTestEnvironment } = require('./setup');

// Initialize the test environment
setupTestEnvironment();

// You can add additional test setup that needs to run after Jest is initialized
beforeEach(() => {
  // Reset all mocks between tests
  jest.clearAllMocks();
});

// Global test timeout (30 seconds)
jest.setTimeout(30000);
