const fs = require('fs');
const path = require('path');

const SRC_DIR = path.join(__dirname, '../src');

// Function to convert @/ imports to relative paths
function convertImportPath(filePath, importPath) {
  if (!importPath.startsWith('@/')) {
    return importPath;
  }

  const relativePath = importPath.replace(/^@\//, '');
  const fileDir = path.dirname(filePath);
  const targetPath = path.join(SRC_DIR, relativePath);
  
  // Check if the target is a directory with an index file
  let actualTargetPath = targetPath;
  if (fs.existsSync(targetPath) && fs.statSync(targetPath).isDirectory()) {
    const indexPath = path.join(targetPath, 'index.ts');
    if (fs.existsSync(indexPath)) {
      actualTargetPath = indexPath;
    } else {
      const indexPathTSX = path.join(targetPath, 'index.tsx');
      if (fs.existsSync(indexPathTSX)) {
        actualTargetPath = indexPathTSX;
      }
    }
  } else if (!fs.existsSync(targetPath)) {
    // Try with .ts extension
    if (fs.existsSync(`${targetPath}.ts`)) {
      actualTargetPath = `${targetPath}.ts`;
    } 
    // Try with .tsx extension
    else if (fs.existsSync(`${targetPath}.tsx`)) {
      actualTargetPath = `${targetPath}.tsx`;
    }
  }

  // Calculate relative path
  const relative = path.relative(fileDir, actualTargetPath);
  let result = relative.startsWith('.') ? relative : `./${relative}`;
  
  // Remove file extensions
  result = result.replace(/\.(tsx?|jsx?)$/, '');
  
  // Handle directory imports
  if (result.endsWith('/index')) {
    result = result.replace(/\/index$/, '');
  }
  
  // Handle parent directory imports
  if (!result.startsWith('.')) {
    result = `./${result}`;
  }
  
  return result;
}

// Process a single file
function processFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let updated = false;

    // Handle import statements
    content = content.replace(
      /(import\s+(?:[^'"\n]+\s+from\s+)?['"])(@\/[^'"\n]+)(['"])/g,
      (match, prefix, importPath, suffix) => {
        const newPath = convertImportPath(filePath, importPath);
        if (newPath !== importPath) {
          updated = true;
          return `${prefix}${newPath}${suffix}`;
        }
        return match;
      }
    );

    // Handle require statements
    content = content.replace(
      /(require\(['"])(@\/[^'"\n]+)(['"]\))/g,
      (match, prefix, importPath, suffix) => {
        const newPath = convertImportPath(filePath, importPath);
        if (newPath !== importPath) {
          updated = true;
          return `${prefix}${newPath}${suffix}`;
        }
        return match;
      }
    );

    if (updated) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`Updated imports in ${path.relative(process.cwd(), filePath)}`);
    }
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
  }
}

// Recursively process all TypeScript and JavaScript files
function processDirectory(directory) {
  const files = fs.readdirSync(directory);
  
  files.forEach(file => {
    const fullPath = path.join(directory, file);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      processDirectory(fullPath);
    } else if (/\.(tsx?|jsx?)$/.test(file)) {
      processFile(fullPath);
    }
  });
}

console.log('Starting to update import paths...');
processDirectory(SRC_DIR);
console.log('Import path update complete!');
