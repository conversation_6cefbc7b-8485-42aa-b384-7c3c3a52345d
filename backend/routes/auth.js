const express = require('express');
const { body } = require('express-validator');
const { auth, authorize } = require('../middleware/auth');
const { validateRequest, sanitizeRequest } = require('../middleware/validation');
const { authLimiter, passwordResetLimiter } = require('../middleware/rateLimiter');
const authController = require('../controllers/authController');

const router = express.Router();

// ======================
// Public Routes
// ======================

// User Registration
router.post(
  '/register',
  [
    body('name').trim().notEmpty().withMessage('Name is required'),
    body('email').isEmail().withMessage('Please include a valid email'),
    body('password')
      .isLength({ min: 8 })
      .withMessage('Password must be at least 8 characters long')
      .matches(/[0-9]/)
      .withMessage('Password must contain a number')
      .matches(/[a-z]/)
      .withMessage('Password must contain a lowercase letter')
      .matches(/[A-Z]/)
      .withMessage('Password must contain an uppercase letter'),
    body('businessName').trim().notEmpty().withMessage('Business name is required'),
    body('phone').trim().notEmpty().withMessage('Phone number is required')
  ],
  validateRequest,
  authController.register
);

// User Login
router.post(
  '/login',
  [
    body('email').isEmail().withMessage('Please include a valid email'),
    body('password').exists().withMessage('Password is required')
  ],
  validateRequest,
  authLimiter,
  authController.login
);

// Email Verification
router.get(
  '/verify-email/:token',
  authController.verifyEmail
);

// Password Reset Request
router.post(
  '/forgot-password',
  [
    body('email').isEmail().withMessage('Please include a valid email')
  ],
  validateRequest,
  passwordResetLimiter,
  authController.forgotPassword
);

// Password Reset
router.patch(
  '/reset-password/:token',
  [
    body('password')
      .isLength({ min: 8 })
      .withMessage('Password must be at least 8 characters long')
      .matches(/[0-9]/)
      .withMessage('Password must contain a number')
      .matches(/[a-z]/)
      .withMessage('Password must contain a lowercase letter')
      .matches(/[A-Z]/)
      .withMessage('Password must contain an uppercase letter')
  ],
  validateRequest,
  authController.resetPassword
);

// ======================
// Protected Routes (require authentication)
// ======================
router.use(auth);

// User Profile
router.get('/me', authController.getMe);
router.put('/update-details', authController.updateDetails);
router.put('/update-password', [
  body('currentPassword').exists().withMessage('Current password is required'),
  body('newPassword')
    .isLength({ min: 8 })
    .withMessage('New password must be at least 8 characters long')
    .matches(/[0-9]/)
    .withMessage('New password must contain a number')
    .matches(/[a-z]/)
    .withMessage('New password must contain a lowercase letter')
    .matches(/[A-Z]/)
    .withMessage('New password must contain an uppercase letter')
], validateRequest, authController.updatePassword);

// 2FA
router.post('/setup-2fa', authController.setup2FA);
router.post('/verify-2fa', [
  body('token').notEmpty().withMessage('Verification token is required')
], validateRequest, authController.verify2FA);
router.post('/verify-2fa-setup', [
  body('token').notEmpty().withMessage('Verification token is required')
], validateRequest, authController.verify2FASetup);
router.delete('/disable-2fa', authController.disable2FA);

// Session Management
router.get('/sessions', authController.getSessions);
router.delete('/sessions/:sessionId', authController.revokeSession);
router.post('/logout', authController.logout);

module.exports = router;

/**
 * @route   POST /api/auth/verify-email
 * @desc    Verify user's email with token
 * @access  Public
 */
router.post(
  '/verify-email',
  [
    body('token')
      .exists()
      .withMessage('Verification token is required'),
  ],
  validateRequest,

  async (req, res) => {
    try {
      const { token } = req.body;

      const user = await User.findOne({ verificationToken: token });
      if (!user) {
        logger.warn('Invalid verification token used');
        return badRequestResponse(res, 'Invalid or expired verification token');
      }

      // Check if already verified
      if (user.isVerified) {
        return successResponse(res, null, 'Email already verified');
      }

      user.isVerified = true;
      user.verificationToken = undefined;
      await user.save();
      logger.info(`Email verified for user: ${user._id}`);

      return successResponse(res, null, 'Email verified successfully');
    } catch (error) {
      logger.error('Email verification error:', error);
      return serverErrorResponse(
        res,
        'Server error during email verification',
        process.env.NODE_ENV === 'development' ? error : null
      );
    }
  }
);

/**
 * @swagger
 * /api/auth/forgot-password:
 *   post:
 *     tags: [Authentication]
 *     summary: Request password reset
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [email]
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *     responses:
 *       200:
 *         description: If the email exists, a reset token will be sent
 *       400:
 *         description: Validation error
 */
router.post(
  '/forgot-password',
  [
    body('email')
      .isEmail()
      .withMessage('Please provide a valid email')
      .normalizeEmail(),
  ],
  validateRequest,
  sanitizeRequest,
  async (req, res) => {
    try {
      const { email } = req.body;

      const user = await User.findOne({ email });
      if (!user) {
        // Don't reveal if user exists or not for security
        logger.warn(`Password reset request for non-existent email: ${email}`);
        return successResponse(
          res,
          null,
          'If an account with that email exists, a password reset link has been sent'
        );
      }

      // Check if there's a recent reset request
      if (user.resetPasswordExpires > Date.now()) {
        const remainingTime = Math.ceil((user.resetPasswordExpires - Date.now()) / 60000); // in minutes
        return tooManyRequestsResponse(
          res,
          `Please wait ${remainingTime} ${remainingTime === 1 ? 'minute' : 'minutes'} before requesting another password reset`
        );
      }

      // Generate reset token
      const resetToken = crypto.randomBytes(32).toString('hex');
      user.resetPasswordToken = resetToken;
      user.resetPasswordExpires = Date.now() + 3600000; // 1 hour
      await user.save();
      logger.info(`Password reset token generated for user: ${user._id}`);

      // Send password reset email
      try {
        await sendPasswordResetEmail(email, resetToken);
        logger.info(`Password reset email sent to: ${email}`);
      } catch (emailError) {
        logger.error('Failed to send password reset email:', emailError);
        return serverErrorResponse(res, 'Failed to send password reset email');
      }

      return successResponse(
        res,
        null,
        'If an account with that email exists, a password reset link has been sent'
      );
    } catch (error) {
      logger.error('Forgot password error:', error);
      return serverErrorResponse(
        res,
        'Server error while processing password reset',
        process.env.NODE_ENV === 'development' ? error : null
      );
    }
  }
);

/**
 * @swagger
 * /api/auth/reset-password:
 *   post:
 *     tags: [Authentication]
 *     summary: Reset password with token
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [token, password]
 *             properties:
 *               token:
 *                 type: string
 *                 description: Password reset token received via email
 *               password:
 *                 type: string
 *                 format: password
 *                 minLength: 8
 *                 description: New password (must be at least 8 characters, with uppercase, lowercase, and number)
 *     responses:
 *       200:
 *         description: Password reset successful
 *       400:
 *         description: Invalid token or validation error
 *       404:
 *         description: Invalid or expired token
 */
router.post(
  '/reset-password',
  [
    body('token')
      .exists()
      .withMessage('Reset token is required'),
    body('password')
      .isLength({ min: 8 })
      .withMessage('Password must be at least 8 characters long')
      .matches(/[a-z]/)
      .withMessage('Password must contain at least one lowercase letter')
      .matches(/[A-Z]/)
      .withMessage('Password must contain at least one uppercase letter')
      .matches(/\d/)
      .withMessage('Password must contain at least one number'),
  ],
  validateRequest,
  sanitizeRequest,
  async (req, res) => {
    try {
      const { token, password } = req.body;

      const user = await User.findOne({
        resetPasswordToken: token,
        resetPasswordExpires: { $gt: Date.now() },
      });

      if (!user) {
        logger.warn('Invalid or expired password reset token used');
        return badRequestResponse(res, 'Invalid or expired reset token');
      }

      // Check if new password is different from current
      const isSamePassword = await user.comparePassword(password);
      if (isSamePassword) {
        return badRequestResponse(res, 'New password must be different from current password');
      }

      // Update password
      user.password = password;
      user.resetPasswordToken = undefined;
      user.resetPasswordExpires = undefined;
      
      // Invalidate all existing sessions/tokens
      user.tokenVersion = (user.tokenVersion || 0) + 1;
      
      await user.save();
      
      logger.info(`Password reset successful for user: ${user._id}`);

      // Send password changed confirmation email
      try {
        await sendPasswordChangedEmail(user.email);
        logger.info(`Password change confirmation sent to: ${user.email}`);
      } catch (emailError) {
        logger.error('Failed to send password change confirmation email:', emailError);
        // Continue even if email fails
      }

      return successResponse(res, null, 'Password reset successfully');
    } catch (error) {
      logger.error('Reset password error:', error);
      return serverErrorResponse(
        res,
        'Server error while resetting password',
        process.env.NODE_ENV === 'development' ? error : null
      );
    }
  }
);

// Logout (client-side token removal, but we can track it)
router.post("/logout", auth, async (req, res) => {
  try {
    // In a more sophisticated setup, you might want to blacklist the token
    res.json({ message: "Logged out successfully" })
  } catch (error) {
    console.error("Logout error:", error)
    res.status(500).json({ message: "Server error" })
  }
});

// ======================
// Admin Routes (Development/Testing Only)
// ======================

/**
 * @route   GET /api/auth/bypass-token/:userId
 * @desc    Generate a bypass token for testing (Development/Testing only)
 * @access  Private/Admin
 */
if (process.env.ENABLE_JWT_BYPASS === 'true') {
  router.get(
    '/bypass-token/:userId',
    securityHeaders, // Add security headers
    auth, // Require authentication
    authorize('admin'), // Require admin role
    bypassController.generateBypassToken
  );
  
  console.warn('⚠️  JWT Bypass is ENABLED. This should only be used in development and testing environments!');
  
  // Add security headers to all routes
  router.use(securityHeaders);
}

module.exports = router;
