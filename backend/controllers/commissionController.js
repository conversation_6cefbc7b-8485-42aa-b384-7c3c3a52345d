const CommissionStructure = require('../models/CommissionStructure');
const Group = require('../models/Group');
const { SuccessResponse, ErrorResponse } = require('../utils/apiResponse');
// Simple async handler replacement
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};
const mongoose = require('mongoose');

/**
 * @desc    Create a new commission structure
 * @route   POST /api/v1/commissions
 * @access  Private/Admin
 */
exports.createCommissionStructure = asyncHandler(async (req, res, next) => {
  const {
    name,
    description,
    fromType,
    toType,
    transactionType,
    commissionType,
    calculationMethod,
    percentage,
    fixedAmount,
    isTiered,
    tiers,
    applyTo,
    groupIds = [],
    userIds = [],
    paymentMethods = [],
    minTransactionAmount,
    maxTransactionAmount,
    hasCapping,
    capAmount,
    capPeriod,
    tags = [],
    notes,
    isDefault,
    priority
  } = req.body;

  // If applying to specific groups, verify they exist
  if (applyTo === 'groups' && groupIds.length > 0) {
    const groups = await Group.find({ _id: { $in: groupIds } });
    if (groups.length !== groupIds.length) {
      return next(new ErrorResponse('One or more groups not found', 404));
    }
  }

  // If applying to specific users, verify they exist (you'll need to implement this check)
  // if (applyTo === 'individuals' && userIds.length > 0) {
  //   const users = await User.find({ _id: { $in: userIds } });
  //   if (users.length !== userIds.length) {
  //     return next(new ErrorResponse('One or more users not found', 404));
  //   }
  // }

  // Create the commission structure
  const commissionStructure = new CommissionStructure({
    name,
    description,
    fromType,
    toType,
    transactionType,
    commissionType,
    calculationMethod,
    percentage,
    fixedAmount,
    isTiered,
    tiers,
    applyTo,
    groups: applyTo === 'groups' ? groupIds : [],
    users: applyTo === 'individuals' ? userIds : [],
    paymentMethods,
    minTransactionAmount,
    maxTransactionAmount,
    hasCapping,
    capAmount,
    capPeriod,
    tags,
    notes,
    isDefault: isDefault || false,
    priority: priority || 0,
    status: 'active',
    createdBy: req.user.id
  });

  await commissionStructure.save();

  return new SuccessResponse(
    201,
    'Commission structure created successfully',
    commissionStructure
  ).send(res);
});

/**
 * @desc    Get all commission structures
 * @route   GET /api/v1/commissions
 * @access  Private/Admin
 */
exports.getCommissionStructures = asyncHandler(async (req, res, next) => {
  const { 
    page = 1, 
    limit = 10, 
    fromType, 
    toType, 
    transactionType,
    status,
    search,
    sortBy = 'createdAt',
    sortOrder = 'desc'
  } = req.query;

  // Build query
  const query = {};
  
  if (fromType) query.fromType = fromType;
  if (toType) query.toType = toType;
  if (transactionType) query.transactionType = transactionType;
  if (status) query.status = status;
  
  if (search) {
    query.$or = [
      { name: { $regex: search, $options: 'i' } },
      { description: { $regex: search, $options: 'i' } },
      { code: { $regex: search, $options: 'i' } }
    ];
  }

  // Sorting
  const sort = {};
  sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

  // Pagination
  const options = {
    page: parseInt(page, 10),
    limit: parseInt(limit, 10),
    sort,
    populate: [
      { path: 'createdBy', select: 'name email' },
      { path: 'groups', select: 'name description' },
      { path: 'users', select: 'name email' }
    ]
  };

  const result = await CommissionStructure.paginate(query, options);

  return new SuccessResponse(
    200,
    'Commission structures retrieved successfully',
    {
      commissionStructures: result.docs,
      pagination: {
        total: result.totalDocs,
        pages: result.totalPages,
        page: result.page,
        limit: result.limit,
        hasNext: result.hasNextPage,
        hasPrev: result.hasPrevPage
      }
    }
  ).send(res);
});

/**
 * @desc    Get commission structure by ID
 * @route   GET /api/v1/commissions/:id
 * @access  Private/Admin
 */
exports.getCommissionStructure = asyncHandler(async (req, res, next) => {
  const { id } = req.params;
  
  if (!mongoose.Types.ObjectId.isValid(id)) {
    return next(new ErrorResponse('Invalid commission structure ID', 400));
  }

  const commissionStructure = await CommissionStructure.findById(id)
    .populate('createdBy', 'name email')
    .populate('groups', 'name description')
    .populate('users', 'name email');

  if (!commissionStructure) {
    return next(new ErrorResponse('Commission structure not found', 404));
  }

  return new SuccessResponse(
    200,
    'Commission structure retrieved successfully',
    commissionStructure
  ).send(res);
});

/**
 * @desc    Update commission structure
 * @route   PUT /api/v1/commissions/:id
 * @access  Private/Admin
 */
exports.updateCommissionStructure = asyncHandler(async (req, res, next) => {
  const { id } = req.params;
  
  if (!mongoose.Types.ObjectId.isValid(id)) {
    return next(new ErrorResponse('Invalid commission structure ID', 400));
  }

  // Check if commission structure exists
  let commissionStructure = await CommissionStructure.findById(id);
  if (!commissionStructure) {
    return next(new ErrorResponse('Commission structure not found', 404));
  }

  // Prevent updating certain fields if structure is in use
  if (commissionStructure.isDefault) {
    return next(new ErrorResponse('Default commission structure cannot be modified', 400));
  }

  // Update fields
  const updates = { ...req.body, updatedBy: req.user.id };
  
  // If updating groups, verify they exist
  if (updates.applyTo === 'groups' && updates.groupIds) {
    const groups = await Group.find({ _id: { $in: updates.groupIds } });
    if (groups.length !== updates.groupIds.length) {
      return next(new ErrorResponse('One or more groups not found', 404));
    }
    updates.groups = updates.groupIds;
    delete updates.groupIds;
  }

  // If updating users, verify they exist (you'll need to implement this check)
  // if (updates.applyTo === 'individuals' && updates.userIds) {
  //   const users = await User.find({ _id: { $in: updates.userIds } });
  //   if (users.length !== updates.userIds.length) {
  //     return next(new ErrorResponse('One or more users not found', 404));
  //   }
  //   updates.users = updates.userIds;
  //   delete updates.userIds;
  // }

  // Update the commission structure
  commissionStructure = await CommissionStructure.findByIdAndUpdate(
    id,
    { $set: updates },
    { new: true, runValidators: true }
  )
    .populate('createdBy', 'name email')
    .populate('groups', 'name description')
    .populate('users', 'name email');

  return new SuccessResponse(
    200,
    'Commission structure updated successfully',
    commissionStructure
  ).send(res);
});

/**
 * @desc    Delete commission structure
 * @route   DELETE /api/v1/commissions/:id
 * @access  Private/Admin
 */
exports.deleteCommissionStructure = asyncHandler(async (req, res, next) => {
  const { id } = req.params;
  
  if (!mongoose.Types.ObjectId.isValid(id)) {
    return next(new ErrorResponse('Invalid commission structure ID', 400));
  }

  // Check if commission structure exists
  const commissionStructure = await CommissionStructure.findById(id);
  if (!commissionStructure) {
    return next(new ErrorResponse('Commission structure not found', 404));
  }

  // Prevent deleting default or active structures
  if (commissionStructure.isDefault) {
    return next(new ErrorResponse('Default commission structure cannot be deleted', 400));
  }

  if (commissionStructure.status === 'active') {
    return next(new ErrorResponse('Active commission structure cannot be deleted', 400));
  }

  await commissionStructure.remove();

  return new SuccessResponse(
    200,
    'Commission structure deleted successfully'
  ).send(res);
});

/**
 * @desc    Calculate commission for a transaction
 * @route   POST /api/v1/commissions/calculate
 * @access  Private/Admin
 */
exports.calculateCommission = asyncHandler(async (req, res, next) => {
  const { 
    fromParty, 
    toParty, 
    fromType, 
    toType, 
    transactionType, 
    amount, 
    currency = 'USD',
    paymentMethod,
    transactionDate = new Date()
  } = req.body;

  // Validate required fields
  if (!fromParty || !toParty || !fromType || !toType || !transactionType || amount === undefined) {
    return next(new ErrorResponse('Missing required fields', 400));
  }

  // Find applicable commission structures
  const query = {
    $or: [
      { fromType: 'any', toType },
      { fromType, toType: 'any' },
      { fromType, toType }
    ],
    $or: [
      { transactionType: 'any' },
      { transactionType }
    ],
    status: 'active',
    $and: [
      { $or: [
        { effectiveFrom: { $exists: false } },
        { effectiveFrom: { $lte: new Date(transactionDate) } }
      ]},
      { $or: [
        { effectiveTo: { $exists: false } },
        { effectiveTo: { $gte: new Date(transactionDate) } }
      ]}
    ],
    $or: [
      { minTransactionAmount: { $exists: false } },
      { minTransactionAmount: { $lte: parseFloat(amount) } }
    ],
    $or: [
      { maxTransactionAmount: { $exists: false } },
      { maxTransactionAmount: { $gte: parseFloat(amount) } }
    ]
  };

  // Add payment method filter if provided
  if (paymentMethod) {
    query.$or.push(
      { paymentMethods: { $size: 0 } },
      { paymentMethods: paymentMethod }
    );
  }

  // Add group/user specific filters
  query.$or = [
    { applyTo: 'all' },
    { 
      applyTo: 'groups',
      groups: { $in: [/* Add group IDs the user belongs to */] }
    },
    {
      applyTo: 'individuals',
      users: { $in: [fromParty, toParty] }
    }
  ];

  // Find matching commission structures
  const commissionStructures = await CommissionStructure.find(query)
    .sort({ priority: -1, createdAt: -1 });

  // Apply the most specific commission structure
  let commission = 0;
  let rate = 0;
  let appliedStructure = null;
  let message = 'No applicable commission structure found';

  if (commissionStructures.length > 0) {
    appliedStructure = commissionStructures[0];
    
    // Calculate commission based on structure type
    if (appliedStructure.isTiered && appliedStructure.tiers?.length > 0) {
      // Find the applicable tier
      const applicableTier = appliedStructure.tiers.find(tier => {
        return amount >= tier.minAmount && 
               (!tier.maxAmount || amount <= tier.maxAmount);
      });
      
      if (applicableTier) {
        if (applicableTier.percentage !== undefined) {
          rate = applicableTier.percentage;
          commission = (amount * rate) / 100;
        } else if (applicableTier.fixedAmount !== undefined) {
          commission = applicableTier.fixedAmount;
        }
      }
    } else if (appliedStructure.percentage !== undefined) {
      rate = appliedStructure.percentage;
      commission = (amount * rate) / 100;
    } else if (appliedStructure.fixedAmount !== undefined) {
      commission = appliedStructure.fixedAmount;
    }

    // Apply capping if enabled
    if (appliedStructure.hasCapping && appliedStructure.capAmount && appliedStructure.capPeriod) {
      // Here you would typically check the total commission for the period
      // and apply the cap if needed
      // This is a simplified example
      const periodStart = getPeriodStart(appliedStructure.capPeriod, new Date(transactionDate));
      
      // Get total commission for the period (you'll need to implement this)
      // const totalCommission = await getTotalCommissionForPeriod(
      //   appliedStructure._id,
      //   periodStart,
      //   new Date(transactionDate)
      // );
      
      // if (totalCommission + commission > appliedStructure.capAmount) {
      //   commission = Math.max(0, appliedStructure.capAmount - totalCommission);
      //   message = 'Commission capped';
      // }
    }

    message = 'Commission calculated successfully';
  }

  return new SuccessResponse(
    200,
    message,
    {
      amount: parseFloat(amount),
      currency,
      commission: parseFloat(commission.toFixed(2)),
      rate,
      commissionStructure: appliedStructure ? {
        id: appliedStructure._id,
        name: appliedStructure.name,
        type: appliedStructure.commissionType,
        isTiered: appliedStructure.isTiered,
        tiers: appliedStructure.isTiered ? appliedStructure.tiers : undefined
      } : null,
      netAmount: parseFloat((amount - commission).toFixed(2))
    }
  ).send(res);
});

// Helper function to get period start date
function getPeriodStart(period, date) {
  const d = new Date(date);
  
  switch (period) {
    case 'day':
      d.setHours(0, 0, 0, 0);
      break;
    case 'week':
      d.setDate(d.getDate() - d.getDay());
      d.setHours(0, 0, 0, 0);
      break;
    case 'month':
      d.setDate(1);
      d.setHours(0, 0, 0, 0);
      break;
    case 'quarter':
      const quarter = Math.floor(d.getMonth() / 3);
      d.setMonth(quarter * 3, 1);
      d.setHours(0, 0, 0, 0);
      break;
    case 'year':
      d.setMonth(0, 1);
      d.setHours(0, 0, 0, 0);
      break;
    default:
      break;
  }
  
  return d;
}
