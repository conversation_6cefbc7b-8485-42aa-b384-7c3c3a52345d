import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Chip,
  Divider,
  LinearProgress,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  MenuItem,
  Pagination,
  Paper,
  Select,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
  useTheme,
} from '@mui/material';
import {
  AccountCircle as UserIcon,
  Add as AddIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  Warning as WarningIcon,
  History as HistoryIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { formatDistanceToNow, parseISO } from 'date-fns';
import { useSnackbar } from 'notistack';
import api from '../../../services/api';

// Event type mappings
const eventTypeMap = {
  created: { label: 'Created', color: 'success', icon: <AddIcon /> },
  updated: { label: 'Updated', color: 'info', icon: <InfoIcon /> },
  deleted: { label: 'Deleted', color: 'error', icon: <ErrorIcon /> },
  activated: { label: 'Activated', color: 'success', icon: <SuccessIcon /> },
  deactivated: { label: 'Deactivated', color: 'warning', icon: <WarningIcon /> },
  default_changed: { label: 'Default Changed', color: 'info', icon: <SuccessIcon /> },
  replenished: { label: 'Replenished', color: 'success', icon: <SuccessIcon /> },
  rule_added: { label: 'Rule Added', color: 'info', icon: <AddIcon /> },
  rule_updated: { label: 'Rule Updated', color: 'info', icon: <InfoIcon /> },
  rule_removed: { label: 'Rule Removed', color: 'error', icon: <ErrorIcon /> },
  notification_sent: { label: 'Notification Sent', color: 'info', icon: <InfoIcon /> },
  error: { label: 'Error', color: 'error', icon: <ErrorIcon /> },
};

// Format event details for display
const formatEventDetails = (event) => {
  const { type, data } = event;
  
  switch (type) {
    case 'created':
      return 'Reserve strategy was created';
      
    case 'updated':
      if (data?.field) {
        return `Updated ${data.field} from "${data.oldValue}" to "${data.newValue}"`;
      }
      return 'Reserve strategy was updated';
      
    case 'deleted':
      return 'Reserve strategy was deleted';
      
    case 'activated':
      return 'Reserve strategy was activated';
      
    case 'deactivated':
      return 'Reserve strategy was deactivated';
      
    case 'default_changed':
      return 'Default status was changed';
      
    case 'replenished':
      return `Reserve was replenished with $${data?.amount || 'N/A'}`;
      
    case 'rule_added':
      return `Rule added: ${data?.description || 'New rule'}`;
      
    case 'rule_updated':
      if (data?.field) {
        return `Rule updated: ${data.field} changed from "${data.oldValue}" to "${data.newValue}"`;
      }
      return 'Rule was updated';
      
    case 'rule_removed':
      return `Rule removed: ${data?.description || 'Rule'}`;
      
    case 'notification_sent':
      return `Notification sent to ${data?.recipients?.join(', ') || 'recipients'}`;
      
    case 'error':
      return `Error: ${data?.message || 'An error occurred'}`;
      
    default:
      return JSON.stringify(data || 'No details available');
  }
};

const ReserveStrategyHistory = ({ strategyId }) => {
  const theme = useTheme();
  const { enqueueSnackbar } = useSnackbar();
  
  // State
  const [loading, setLoading] = useState(true);
  const [events, setEvents] = useState([]);
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 10,
    total: 0,
    totalPages: 1,
  });
  const [filters, setFilters] = useState({
    search: '',
    type: 'all',
    sort: 'newest',
  });
  
  // Fetch history events
  const fetchHistory = async () => {
    if (!strategyId) return;
    
    try {
      setLoading(true);
      
      const params = {
        page: pagination.page,
        limit: pagination.pageSize,
        sort: filters.sort === 'newest' ? '-createdAt' : 'createdAt',
        type: filters.type !== 'all' ? filters.type : undefined,
        search: filters.search || undefined,
      };
      
      const response = await api.get(`/reserve-strategies/${strategyId}/history`, { params });
      
      setEvents(response.data.docs);
      setPagination(prev => ({
        ...prev,
        total: response.data.total,
        totalPages: response.data.pages,
      }));
    } catch (error) {
      console.error('Error fetching history:', error);
      enqueueSnackbar('Failed to load history', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };
  
  // Initial load
  useEffect(() => {
    fetchHistory();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [strategyId, pagination.page, pagination.pageSize, filters]);
  
  // Handle page change
  const handlePageChange = (event, newPage) => {
    setPagination(prev => ({
      ...prev,
      page: newPage,
    }));
  };
  
  // Handle filter change
  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value,
    }));
    
    // Reset to first page when filters change
    if (pagination.page !== 1) {
      setPagination(prev => ({
        ...prev,
        page: 1,
      }));
    }
  };
  
  // Handle page size change
  const handlePageSizeChange = (e) => {
    setPagination(prev => ({
      ...prev,
      pageSize: Number(e.target.value),
      page: 1, // Reset to first page
    }));
  };
  
  // Handle refresh
  const handleRefresh = () => {
    fetchHistory();
  };
  
  // Event type options for filter
  const eventTypeOptions = [
    { value: 'all', label: 'All Events' },
    { value: 'created', label: 'Created' },
    { value: 'updated', label: 'Updated' },
    { value: 'deleted', label: 'Deleted' },
    { value: 'activated', label: 'Activated' },
    { value: 'deactivated', label: 'Deactivated' },
    { value: 'default_changed', label: 'Default Changed' },
    { value: 'replenished', label: 'Replenished' },
    { value: 'rule_added', label: 'Rule Added' },
    { value: 'rule_updated', label: 'Rule Updated' },
    { value: 'rule_removed', label: 'Rule Removed' },
    { value: 'notification_sent', label: 'Notification Sent' },
    { value: 'error', label: 'Errors' },
  ];
  
  return (
    <Box>
      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, alignItems: 'center' }}>
          <TextField
            size="small"
            placeholder="Search history..."
            value={filters.search}
            onChange={(e) => handleFilterChange('search', e.target.value)}
            InputProps={{
              startAdornment: <SearchIcon color="action" sx={{ mr: 1 }} />,
            }}
            sx={{ minWidth: 250, flex: 1 }}
          />
          
          <Select
            size="small"
            value={filters.type}
            onChange={(e) => handleFilterChange('type', e.target.value)}
            startAdornment={<FilterIcon color="action" sx={{ mr: 1 }} />}
            sx={{ minWidth: 200 }}
          >
            {eventTypeOptions.map((option) => (
              <MenuItem key={option.value} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </Select>
          
          <Select
            size="small"
            value={filters.sort}
            onChange={(e) => handleFilterChange('sort', e.target.value)}
            sx={{ minWidth: 150 }}
          >
            <MenuItem value="newest">Newest First</MenuItem>
            <MenuItem value="oldest">Oldest First</MenuItem>
          </Select>
          
          <IconButton 
            onClick={handleRefresh}
            disabled={loading}
            title="Refresh"
          >
            <RefreshIcon />
          </IconButton>
        </Box>
      </Paper>
      
      {/* Loading state */}
      {loading && <LinearProgress />}
      
      {/* Empty state */}
      {!loading && events.length === 0 && (
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <HistoryIcon sx={{ fontSize: 48, color: 'text.disabled', mb: 2 }} />
          <Typography variant="h6" color="textSecondary" gutterBottom>
            No history found
          </Typography>
          <Typography variant="body2" color="textSecondary">
            {filters.type !== 'all' || filters.search
              ? 'Try adjusting your filters to see more results.'
              : 'There are no history events for this reserve strategy yet.'}
          </Typography>
        </Paper>
      )}
      
      {/* Events list */}
      {!loading && events.length > 0 && (
        <>
          <List sx={{ width: '100%', bgcolor: 'background.paper' }}>
            {events.map((event, index) => {
              const eventType = eventTypeMap[event.type] || { 
                label: event.type, 
                color: 'default', 
                icon: <InfoIcon /> 
              };
              
              return (
                <React.Fragment key={event._id}>
                  <ListItem alignItems="flex-start" sx={{ px: 0 }}>
                    <ListItemAvatar sx={{ minWidth: 48, mr: 1 }}>
                      <Box
                        sx={{
                          width: 40,
                          height: 40,
                          borderRadius: '50%',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          bgcolor: `${eventType.color}.light`,
                          color: `${eventType.color}.dark`,
                        }}
                      >
                        {eventType.icon}
                      </Box>
                    </ListItemAvatar>
                    
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                          <Typography
                            component="span"
                            variant="subtitle2"
                            color="text.primary"
                            sx={{ mr: 1 }}
                          >
                            {event.user?.name || 'System'}
                          </Typography>
                          <Typography
                            component="span"
                            variant="body2"
                            color="text.secondary"
                          >
                            {formatDistanceToNow(parseISO(event.createdAt), { addSuffix: true })}
                          </Typography>
                          
                          <Chip
                            label={eventType.label}
                            size="small"
                            variant="outlined"
                            color={eventType.color}
                            sx={{ ml: 'auto' }}
                          />
                        </Box>
                      }
                      secondary={
                        <>
                          <Typography
                            component="span"
                            variant="body2"
                            color="text.primary"
                            display="block"
                            sx={{ mb: 0.5 }}
                          >
                            {formatEventDetails(event)}
                          </Typography>
                          
                          {event.data?.details && (
                            <Typography
                              component="span"
                              variant="caption"
                              color="text.secondary"
                              display="block"
                            >
                              {event.data.details}
                            </Typography>
                          )}
                        </>
                      }
                    />
                  </ListItem>
                  
                  {index < events.length - 1 && <Divider variant="inset" component="li" />}
                </React.Fragment>
              );
            })}
          </List>
          
          {/* Pagination */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Typography variant="body2" color="text.secondary" sx={{ mr: 2 }}>
                Rows per page:
              </Typography>
              <Select
                size="small"
                value={pagination.pageSize}
                onChange={handlePageSizeChange}
                sx={{ mr: 2 }}
              >
                <MenuItem value={5}>5</MenuItem>
                <MenuItem value={10}>10</MenuItem>
                <MenuItem value={25}>25</MenuItem>
                <MenuItem value={50}>50</MenuItem>
              </Select>
              <Typography variant="body2" color="text.secondary">
                {pagination.total === 0 ? '0' : (pagination.page - 1) * pagination.pageSize + 1} -{' '}
                {Math.min(pagination.page * pagination.pageSize, pagination.total)} of {pagination.total}
              </Typography>
            </Box>
            
            <Pagination
              count={pagination.totalPages}
              page={pagination.page}
              onChange={handlePageChange}
              color="primary"
              shape="rounded"
              showFirstButton
              showLastButton
              disabled={loading}
            />
          </Box>
        </>
      )}
    </Box>
  );
};

export default ReserveStrategyHistory;
