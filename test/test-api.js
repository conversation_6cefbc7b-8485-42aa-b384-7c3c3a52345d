const axios = require('axios');
const { v4: uuidv4 } = require('uuid');
const chalk = require('chalk');

// Configuration
const BASE_URL = 'http://localhost:3001';
const TEST_USERS = {
  admin: { email: '<EMAIL>', password: 'admin123' },
  merchant: { email: '<EMAIL>', password: 'merchant1' },
  trader: { email: '<EMAIL>', password: 'trader1' },
};

// Test results
const testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  errors: [],
};

// Helper functions
const log = {
  info: (message) => console.log(chalk.blue(`[INFO] ${message}`)),
  success: (message) => console.log(chalk.green(`[SUCCESS] ${message}`)),
  error: (message) => console.log(chalk.red(`[ERROR] ${message}`)),
  test: (name, passed) => {
    testResults.total++;
    if (passed) {
      testResults.passed++;
      console.log(chalk.green(`✓ ${name}`));
    } else {
      testResults.failed++;
      console.log(chalk.red(`✗ ${name}`));
    }
  },
};

// Test runner
class TestRunner {
  constructor() {
    this.token = null;
    this.user = null;
  }

  async authenticate(role) {
    try {
      const { email, password } = TEST_USERS[role];
      const response = await axios.post(`${BASE_URL}/api/auth/login`, {
        email,
        password,
      });
      
      this.token = response.data.token;
      this.user = response.data.user;
      
      log.success(`Authenticated as ${role}: ${email}`);
      return true;
    } catch (error) {
      log.error(`Authentication failed: ${error.message}`);
      return false;
    }
  }

  async makeRequest(method, endpoint, data = null, auth = true) {
    try {
      const config = {
        method,
        url: `${BASE_URL}${endpoint}`,
        headers: {},
      };

      if (auth && this.token) {
        config.headers.Authorization = `Bearer ${this.token}`;
      }

      if (data) {
        config.data = data;
      }

      const response = await axios(config);
      return { success: true, data: response.data, status: response.status };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data || error.message,
        status: error.response?.status,
      };
    }
  }

  async runTest(name, testFn) {
    try {
      log.info(`Running test: ${name}`);
      await testFn();
      log.test(name, true);
    } catch (error) {
      log.test(name, false);
      testResults.errors.push({
        test: name,
        error: error.message,
      });
    }
  }
}

// Test cases
async function runTests() {
  const runner = new TestRunner();
  
  // Test authentication
  await runner.runTest('Admin authentication', async () => {
    const result = await runner.authenticate('admin');
    if (!result) throw new Error('Admin authentication failed');
  });

  // Test merchant endpoints
  await runner.runTest('Get all merchants (admin)', async () => {
    const { success, data } = await runner.makeRequest('GET', '/api/merchants');
    if (!success || !Array.isArray(data)) {
      throw new Error('Failed to fetch merchants');
    }
  });

  // Test trader endpoints
  await runner.runTest('Get all traders (admin)', async () => {
    const { success, data } = await runner.makeRequest('GET', '/api/traders');
    if (!success || !Array.isArray(data)) {
      throw new Error('Failed to fetch traders');
    }
  });

  // Test transaction endpoints
  await runner.runTest('Get all transactions (admin)', async () => {
    const { success, data } = await runner.makeRequest('GET', '/api/transactions');
    if (!success || !Array.isArray(data)) {
      throw new Error('Failed to fetch transactions');
    }
  });

  // Test settlement endpoints
  await runner.runTest('Get all settlements (admin)', async () => {
    const { success, data } = await runner.makeRequest('GET', '/api/settlements');
    if (!success || !Array.isArray(data)) {
      throw new Error('Failed to fetch settlements');
    }
  });

  // Test reserve strategy endpoints
  await runner.runTest('Get all reserve strategies (admin)', async () => {
    const { success, data } = await runner.makeRequest('GET', '/api/reserve-strategies');
    if (!success || !Array.isArray(data)) {
      throw new Error('Failed to fetch reserve strategies');
    }
  });

  // Test creating a new reserve strategy
  await runner.runTest('Create reserve strategy (admin)', async () => {
    const strategyData = {
      name: 'Test Reserve Strategy',
      description: 'Test strategy for automated testing',
      type: 'percentage',
      value: 5,
      minAmount: 50,
      maxAmount: 5000,
      status: 'active',
      applyTo: 'all',
      autoReplenish: true,
      autoReplenishThreshold: 20,
      autoReplenishAmount: 10,
      notificationThreshold: 30,
    };

    const { success, data } = await runner.makeRequest(
      'POST',
      '/api/reserve-strategies',
      strategyData
    );

    if (!success || !data.id) {
      throw new Error('Failed to create reserve strategy');
    }
  });

  // Test merchant authentication and access
  await runner.runTest('Merchant authentication', async () => {
    const result = await runner.authenticate('merchant');
    if (!result) throw new Error('Merchant authentication failed');
  });

  // Test merchant-specific endpoints
  await runner.runTest('Get merchant transactions', async () => {
    const { success, data } = await runner.makeRequest('GET', '/api/transactions');
    if (!success || !Array.isArray(data)) {
      throw new Error('Failed to fetch merchant transactions');
    }
  });

  // Test trader authentication and access
  await runner.runTest('Trader authentication', async () => {
    const result = await runner.authenticate('trader');
    if (!result) throw new Error('Trader authentication failed');
  });

  // Test trader-specific endpoints
  await runner.runTest('Get trader settlements', async () => {
    const { success, data } = await runner.makeRequest('GET', '/api/settlements');
    if (!success || !Array.isArray(data)) {
      throw new Error('Failed to fetch trader settlements');
    }
  });

  // Print test summary
  console.log('\n' + '='.repeat(60));
  console.log(chalk.bold('TEST SUMMARY'));
  console.log('='.repeat(60));
  console.log(`Total tests: ${testResults.total}`);
  console.log(chalk.green(`Passed: ${testResults.passed}`));
  console.log(chalk.red(`Failed: ${testResults.failed}`));
  console.log('\n' + '='.repeat(60));

  if (testResults.errors.length > 0) {
    console.log(chalk.bold('\nERROR DETAILS:'));
    testResults.errors.forEach((error, index) => {
      console.log(`\n${index + 1}. ${error.test}`);
      console.log(`   ${chalk.red(error.error)}`);
    });
  }

  process.exit(testResults.failed > 0 ? 1 : 0);
}

// Run tests
runTests().catch((error) => {
  console.error('Unhandled error in test suite:', error);
  process.exit(1);
});
