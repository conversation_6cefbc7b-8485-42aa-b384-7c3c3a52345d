const Transaction = require('../models/Transaction');
const Merchant = require('../models/Merchant');
const User = require('../models/User');
const { format, subDays, startOfDay, endOfDay } = require('date-fns');

// Helper function to calculate percentage change
const calculatePercentageChange = (current, previous) => {
  if (previous === 0) return 100;
  return ((current - previous) / previous) * 100;
};

// Get dashboard statistics
exports.getDashboardStats = async (req, res) => {
  try {
    const { timeRange = 'month' } = req.query;
    const today = new Date();
    let startDate, previousPeriodStartDate;

    // Set date ranges based on timeRange
    switch (timeRange) {
      case 'week':
        startDate = subDays(today, 7);
        previousPeriodStartDate = subDays(startDate, 7);
        break;
      case 'year':
        startDate = subDays(today, 365);
        previousPeriodStartDate = subDays(startDate, 365);
        break;
      case 'month':
      default:
        startDate = subDays(today, 30);
        previousPeriodStartDate = subDays(startDate, 30);
    }

    // Get current period stats
    const [
      totalRevenueResult,
      activeMerchants,
      totalTransactions,
      activeTraders,
      previousPeriodRevenue,
      previousPeriodTransactions
    ] = await Promise.all([
      // Current period total revenue
      Transaction.aggregate([
        {
          $match: {
            status: 'completed',
            createdAt: { $gte: startDate }
          }
        },
        {
          $group: {
            _id: null,
            total: { $sum: '$amount' }
          }
        }
      ]),
      
      // Active merchants (with transactions in current period)
      Transaction.distinct('merchantId', {
        createdAt: { $gte: startDate }
      }),
      
      // Total transactions in current period
      Transaction.countDocuments({
        createdAt: { $gte: startDate }
      }),
      
      // Active traders (users with role 'trader' and active status)
      User.countDocuments({
        role: 'trader',
        status: 'active'
      }),
      
      // Previous period revenue for comparison
      Transaction.aggregate([
        {
          $match: {
            status: 'completed',
            createdAt: { $gte: previousPeriodStartDate, $lt: startDate }
          }
        },
        {
          $group: {
            _id: null,
            total: { $sum: '$amount' }
          }
        }
      ]),
      
      // Previous period transaction count for comparison
      Transaction.countDocuments({
        createdAt: { $gte: previousPeriodStartDate, $lt: startDate }
      })
    ]);

    const totalRevenue = totalRevenueResult[0]?.total || 0;
    const prevTotalRevenue = previousPeriodRevenue[0]?.total || 0;
    const revenueChange = calculatePercentageChange(totalRevenue, prevTotalRevenue);
    const transactionChange = calculatePercentageChange(totalTransactions, previousPeriodTransactions);

    // Get daily revenue for the chart
    const daysInPeriod = timeRange === 'week' ? 7 : timeRange === 'year' ? 12 : 30;
    const dailyRevenue = [];
    const transactionCounts = [];
    
    for (let i = daysInPeriod - 1; i >= 0; i--) {
      const currentDate = subDays(today, i);
      const dayStart = startOfDay(currentDate);
      const dayEnd = endOfDay(currentDate);
      
      const [revenueResult, transactionCount] = await Promise.all([
        Transaction.aggregate([
          {
            $match: {
              status: 'completed',
              createdAt: { $gte: dayStart, $lte: dayEnd }
            }
          },
          {
            $group: {
              _id: null,
              total: { $sum: '$amount' }
            }
          }
        ]),
        Transaction.countDocuments({
          createdAt: { $gte: dayStart, $lte: dayEnd }
        })
      ]);
      
      const dateLabel = format(dayStart, timeRange === 'year' ? 'MMM yyyy' : 'MMM dd');
      dailyRevenue.push({
        name: dateLabel,
        revenue: revenueResult[0]?.total || 0
      });
      
      transactionCounts.push({
        name: dateLabel,
        count: transactionCount
      });
    }

    // Get recent transactions
    const recentTransactions = await Transaction.find({
      status: { $in: ['completed', 'pending'] }
    })
      .sort({ createdAt: -1 })
      .limit(5)
      .populate('merchantId', 'businessName')
      .lean();

    // Format response
    const response = {
      stats: {
        totalRevenue,
        activeMerchants: activeMerchants.length,
        totalTransactions,
        activeTraders,
        revenueChange: parseFloat(revenueChange.toFixed(2)),
        transactionChange: parseFloat(transactionChange.toFixed(2)),
        chartData: {
          labels: dailyRevenue.map(item => item.name),
          revenueData: dailyRevenue.map(item => item.revenue),
          transactionData: transactionCounts.map(item => item.count)
        }
      },
      recentTransactions: recentTransactions.map(tx => ({
        id: tx._id,
        merchantName: tx.merchantId?.businessName || 'Unknown Merchant',
        amount: tx.amount,
        date: tx.createdAt,
        status: tx.status
      }))
    };

    res.json(response);
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Error fetching dashboard statistics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get system status
exports.getSystemStatus = async (req, res) => {
  try {
    // In a real application, you would check actual system status
    // This is a simplified version that always returns healthy status
    const currentTime = new Date();
    
    res.json({
      success: true,
      data: {
        services: [
          {
            name: 'API Service',
            status: 'operational',
            responseTime: 42,
            lastChecked: currentTime.toISOString()
          },
          {
            name: 'Database',
            status: 'operational',
            responseTime: 12,
            lastChecked: currentTime.toISOString()
          },
          {
            name: 'Payment Processor',
            status: 'operational',
            responseTime: 128,
            lastChecked: currentTime.toISOString()
          },
          {
            name: 'Email Service',
            status: 'operational',
            responseTime: 56,
            lastChecked: currentTime.toISOString()
          }
        ],
        lastUpdated: currentTime.toISOString()
      }
    });
  } catch (error) {
    console.error('Error getting system status:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Error getting system status',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get activity log
exports.getActivityLog = async (req, res) => {
  try {
    const { page = 1, limit = 10, type, userId, startDate, endDate } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    // Build query
    const query = {};
    if (type) query.type = type;
    if (userId) query.userId = userId;
    if (startDate || endDate) {
      query.timestamp = {};
      if (startDate) query.timestamp.$gte = new Date(startDate);
      if (endDate) query.timestamp.$lte = new Date(endDate);
    }
    
    // In a real application, you would query an activity log collection
    // This is a simplified version with mock data
    const total = 0; // Replace with actual count query
    const activities = []; // Replace with actual query
    
    res.json({
      success: true,
      data: {
        activities,
        pagination: {
          total,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Error fetching activity log:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Error fetching activity log',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Generate report
exports.generateReport = async (req, res) => {
  try {
    const { type, startDate, endDate, format = 'csv' } = req.query;
    
    // In a real application, you would generate a report based on the parameters
    // This is a simplified version that returns a success response
    
    res.json({
      success: true,
      message: 'Report generation started',
      data: {
        reportId: `report_${Date.now()}`,
        status: 'processing',
        downloadUrl: null, // Will be updated when report is ready
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours from now
      }
    });
  } catch (error) {
    console.error('Error generating report:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Error generating report',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};
