const helmet = require('helmet');
const xss = require('xss-clean');
const mongoSanitize = require('express-mongo-sanitize');
const hpp = require('hpp');
const cors = require('cors');
const { body } = require('express-validator');
const ApiError = require('../utils/ApiError');
const logger = require('../utils/logger');

// Security middleware configuration
const securityConfig = {
  // Content Security Policy
  csp: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", 'data:', 'blob:'],
      fontSrc: ["'self'", 'data:'],
      connectSrc: ["'self'"],
      frameAncestors: ["'self'"],
      formAction: ["'self'"],
      baseUri: ["'self'"],
      objectSrc: ["'none'"],
      upgradeInsecureRequests: [],
    },
  },
  // CORS configuration
  corsOptions: {
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    credentials: true,
    maxAge: 86400, // 24 hours
  },
};

/**
 * Set security HTTP headers using Helmet
 */
const securityHeaders = [
  // Helmet security headers
  helmet({
    contentSecurityPolicy: securityConfig.csp,
    hsts: {
      maxAge: 31536000, // 1 year in seconds
      includeSubDomains: true,
      preload: true,
    },
    referrerPolicy: { policy: 'same-origin' },
    frameguard: { action: 'sameorigin' },
    noSniff: true,
    xssFilter: true,
    hidePoweredBy: true,
    ieNoOpen: true,
  }),
  
  // Enable CORS with options
  cors(securityConfig.corsOptions),
  
  // Prevent clickjacking
  (req, res, next) => {
    res.setHeader('X-Frame-Options', 'SAMEORIGIN');
    res.setHeader('Content-Security-Policy', "frame-ancestors 'self'");
    next();
  },
  
  // Prevent MIME type sniffing
  (req, res, next) => {
    res.setHeader('X-Content-Type-Options', 'nosniff');
    next();
  },
  
  // Set XSS protection
  (req, res, next) => {
    res.setHeader('X-XSS-Protection', '1; mode=block');
    next();
  },
  
  // Prevent caching for sensitive data
  (req, res, next) => {
    if (req.method === 'GET') {
      res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');
      res.setHeader('Surrogate-Control', 'no-store');
    }
    next();
  },
];

/**
 * Data sanitization against XSS attacks
 */
const sanitizeData = xss();

/**
 * Prevent parameter pollution with whitelist
 */
const preventParameterPollution = hpp({
  whitelist: [
    'page',
    'limit',
    'sort',
    'fields',
    'search',
    'status',
    'type',
    'category',
  ],
});

/**
 * Sanitize request data
 */
const sanitizeRequest = [
  // Sanitize request body
  body('*').trim().escape(),
  
  // Sanitize request query parameters
  (req, res, next) => {
    try {
      // Sanitize query parameters
      if (req.query) {
        Object.keys(req.query).forEach(key => {
          if (typeof req.query[key] === 'string') {
            req.query[key] = req.query[key].trim();
          }
        });
      }
      
      // Sanitize request body
      if (req.body) {
        Object.keys(req.body).forEach(key => {
          if (typeof req.body[key] === 'string') {
            req.body[key] = req.body[key].trim();
          }
        });
      }
      
      next();
    } catch (error) {
      next(error);
    }
  },
];

/**
 * Prevent NoSQL injection
 */
const preventNoSqlInjection = [
  // Sanitize request
  (req, res, next) => {
    try {
      // Remove $ and . from request body, params, and query
      const sanitize = (data) => {
        if (data && typeof data === 'object') {
          Object.keys(data).forEach(key => {
            // Check for MongoDB operators
            if (key.startsWith('$') || key.includes('.')) {
              delete data[key];
            } else if (typeof data[key] === 'object') {
              sanitize(data[key]);
            }
          });
        }
      };
      
      sanitize(req.body);
      sanitize(req.params);
      sanitize(req.query);
      
      next();
    } catch (error) {
      next(error);
    }
  },
  
  // Use express-mongo-sanitize
  mongoSanitize({
    onSanitize: ({ req, key }) => {
      logger.warn('MongoDB injection attempt detected', {
        ip: req.ip,
        method: req.method,
        url: req.originalUrl,
        key,
      });
    },
  }),
];

module.exports = {
  securityHeaders,
  sanitizeData,
  preventParameterPollution,
  sanitizeRequest,
  preventNoSqlInjection,
};
