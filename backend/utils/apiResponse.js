const logger = require('./logger');

/**
 * Standard API response format
 * @typedef {Object} ApiResponse
 * @property {boolean} success - Indicates if the request was successful
 * @property {string} [message] - Optional message describing the result
 * @property {*} [data] - The response data
 * @property {Object} [meta] - Additional metadata
 * @property {Object} [error] - Error details if the request failed
 */

/**
 * Creates a success response
 * @param {Object} options - Response options
 * @param {*} [options.data] - The response data
 * @param {string} [options.message] - Success message
 * @param {Object} [options.meta] - Additional metadata
 * @param {Object} [options.headers] - Custom headers
 * @returns {Object} Formatted success response
 */
const successResponse = ({
  data = null,
  message = 'Request successful',
  meta = {},
  headers = {},
} = {}) => {
  const response = {
    success: true,
    message,
  };

  if (data !== null && data !== undefined) {
    response.data = data;
  }

  if (Object.keys(meta).length > 0) {
    response.meta = meta;
  }

  // Add timestamp
  response.timestamp = new Date().toISOString();

  return {
    statusCode: 200,
    body: response,
    headers: {
      'Content-Type': 'application/json',
      ...headers,
    },
  };
};

/**
 * Creates an error response
 * @param {Object} options - Error options
 * @param {string} [options.message='An error occurred'] - Error message
 * @param {string} [options.code] - Error code
 * @param {number} [options.statusCode=500] - HTTP status code
 * @param {*} [options.details] - Additional error details
 * @param {Error} [options.error] - Original error object
 * @param {Object} [options.meta] - Additional metadata
 * @param {Object} [options.headers] - Custom headers
 * @returns {Object} Formatted error response
 */
const errorResponse = ({
  message = 'An error occurred',
  code,
  statusCode = 500,
  details,
  error,
  meta = {},
  headers = {},
} = {}) => {
  // Log the error
  if (error) {
    logger.error(message, {
      error: {
        message: error.message,
        stack: error.stack,
        code: error.code || code,
        details: error.details || details,
        ...meta,
      },
    });
  } else {
    logger.error(message, {
      error: {
        message,
        code,
        details,
        ...meta,
      },
    });
  }

  const response = {
    success: false,
    message,
    error: {
      code: code || 'INTERNAL_ERROR',
      message,
      ...(details && { details }),
      ...(process.env.NODE_ENV === 'development' && error?.stack
        ? { stack: error.stack }
        : {}),
    },
    timestamp: new Date().toISOString(),
  };

  if (Object.keys(meta).length > 0) {
    response.meta = meta;
  }

  return {
    statusCode,
    body: response,
    headers: {
      'Content-Type': 'application/json',
      ...headers,
    },
  };
};

/**
 * Creates a validation error response
 * @param {Array|Object} errors - Validation errors
 * @param {string} [message='Validation failed'] - Error message
 * @param {Object} [meta] - Additional metadata
 * @returns {Object} Formatted validation error response
 */
const validationErrorResponse = (
  errors,
  message = 'Validation failed',
  meta = {}
) => {
  const formattedErrors = Array.isArray(errors)
    ? errors.map((err) => ({
        field: err.param || err.field,
        message: err.msg || err.message,
        value: err.value,
        location: err.location,
      }))
    : typeof errors === 'object' && errors !== null
    ? Object.entries(errors).map(([field, messages]) => ({
        field,
        message: Array.isArray(messages) ? messages.join(', ') : messages,
      }))
    : [];

  logger.warn('Validation error', {
    message,
    errors: formattedErrors,
    ...meta,
  });

  return {
    statusCode: 400,
    body: {
      success: false,
      message,
      error: {
        code: 'VALIDATION_ERROR',
        message,
        details: formattedErrors,
      },
      timestamp: new Date().toISOString(),
      ...(Object.keys(meta).length > 0 && { meta }),
    },
    headers: {
      'Content-Type': 'application/json',
    },
  };
};

/**
 * Creates a not found response
 * @param {string} [message='Resource not found'] - Error message
 * @param {string} [resource] - The resource that was not found
 * @param {Object} [meta] - Additional metadata
 * @returns {Object} Formatted not found response
 */
const notFoundResponse = (
  message = 'Resource not found',
  resource = null,
  meta = {}
) => {
  const response = {
    success: false,
    message,
    error: {
      code: 'NOT_FOUND',
      message,
      ...(resource && { resource }),
    },
    timestamp: new Date().toISOString(),
  };

  if (Object.keys(meta).length > 0) {
    response.meta = meta;
  }

  return {
    statusCode: 404,
    body: response,
    headers: {
      'Content-Type': 'application/json',
    },
  };
};

/**
 * Creates an unauthorized response
 * @param {string} [message='Unauthorized'] - Error message
 * @param {Object} [meta] - Additional metadata
 * @returns {Object} Formatted unauthorized response
 */
const unauthorizedResponse = (message = 'Unauthorized', meta = {}) => {
  const response = {
    success: false,
    message,
    error: {
      code: 'UNAUTHORIZED',
      message,
    },
    timestamp: new Date().toISOString(),
  };

  if (Object.keys(meta).length > 0) {
    response.meta = meta;
  }

  return {
    statusCode: 401,
    body: response,
    headers: {
      'Content-Type': 'application/json',
      'WWW-Authenticate': 'Bearer',
    },
  };
};

/**
 * Creates a forbidden response
 * @param {string} [message='Forbidden'] - Error message
 * @param {string} [permission] - The required permission
 * @param {Object} [meta] - Additional metadata
 * @returns {Object} Formatted forbidden response
 */
const forbiddenResponse = (
  message = 'Forbidden',
  permission = null,
  meta = {}
) => {
  const response = {
    success: false,
    message,
    error: {
      code: 'FORBIDDEN',
      message,
      ...(permission && { requiredPermission: permission }),
    },
    timestamp: new Date().toISOString(),
  };

  if (Object.keys(meta).length > 0) {
    response.meta = meta;
  }

  return {
    statusCode: 403,
    body: response,
    headers: {
      'Content-Type': 'application/json',
    },
  };
};

/**
 * Creates a rate limit exceeded response
 * @param {string} [message='Too many requests'] - Error message
 * @param {Object} [meta] - Additional metadata
 * @returns {Object} Formatted rate limit exceeded response
 */
const rateLimitExceededResponse = (
  message = 'Too many requests, please try again later',
  meta = {}
) => {
  const response = {
    success: false,
    message,
    error: {
      code: 'RATE_LIMIT_EXCEEDED',
      message,
    },
    timestamp: new Date().toISOString(),
  };

  if (Object.keys(meta).length > 0) {
    response.meta = meta;
  }

  return {
    statusCode: 429,
    body: response,
    headers: {
      'Content-Type': 'application/json',
      'Retry-After': meta.retryAfter || 60,
    },
  };
};

/**
 * Middleware to handle API responses consistently
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Next middleware function
 */
const apiResponseMiddleware = (req, res, next) => {
  // Success response method
  res.apiSuccess = (data = null, message = 'Request successful', meta = {}) => {
    const { statusCode, body, headers } = successResponse({
      data,
      message,
      meta,
    });

    res.status(statusCode).set(headers).json(body);
  };

  // Error response method
  res.apiError = ({
    message = 'An error occurred',
    code,
    statusCode = 500,
    details,
    error,
    meta = {},
  } = {}) => {
    const { statusCode: responseCode, body, headers } = errorResponse({
      message,
      code,
      statusCode,
      details,
      error,
      meta,
    });

    res.status(code).set(headers).json(body);
  };

  // Not found response method
  res.apiNotFound = (message = 'Resource not found', resource = null) => {
    const { statusCode, body, headers } = notFoundResponse(
      message,
      resource,
      res.locals.meta || {}
    );

    res.status(statusCode).set(headers).json(body);
  };

  // Validation error response method
  res.apiValidationError = (errors, message = 'Validation failed') => {
    const { statusCode, body, headers } = validationErrorResponse(
      errors,
      message,
      res.locals.meta || {}
    );

    res.status(statusCode).set(headers).json(body);
  };

  // Unauthorized response method
  res.apiUnauthorized = (message = 'Unauthorized') => {
    const { statusCode, body, headers } = unauthorizedResponse(
      message,
      res.locals.meta || {}
    );

    res.status(statusCode).set(headers).json(body);
  };

  // Forbidden response method
  res.apiForbidden = (message = 'Forbidden', permission = null) => {
    const { statusCode, body, headers } = forbiddenResponse(
      message,
      permission,
      res.locals.meta || {}
    );

    res.status(statusCode).set(headers).json(body);
  };

  // Rate limit exceeded response method
  res.apiRateLimitExceeded = (message = 'Too many requests') => {
    const { statusCode, body, headers } = rateLimitExceededResponse(
      message,
      res.locals.meta || {}
    );

    res.status(statusCode).set(headers).json(body);
  };

  next();
};

module.exports = {
  successResponse,
  errorResponse,
  validationErrorResponse,
  notFoundResponse,
  unauthorizedResponse,
  forbiddenResponse,
  rateLimitExceededResponse,
  apiResponseMiddleware,
};

// Export types for JSDoc
/**
 * @typedef {Object} ApiSuccessResponse
 * @property {boolean} success - Always true for successful responses
 * @property {string} [message] - Optional success message
 * @property {*} [data] - The response data
 * @property {Object} [meta] - Additional metadata
 * @property {string} timestamp - ISO timestamp of the response
 */

/**
 * @typedef {Object} ApiErrorResponse
 * @property {boolean} success - Always false for error responses
 * @property {string} message - Error message
 * @property {Object} error - Error details
 * @property {string} error.code - Error code
 * @property {string} error.message - Error message
 * @property {*} [error.details] - Additional error details
 * @property {string} [error.stack] - Error stack trace (development only)
 * @property {Object} [meta] - Additional metadata
 * @property {string} timestamp - ISO timestamp of the response
 */
