const express = require('express');
const router = express.Router();
const webhookController = require('../controllers/webhook.controller');
const { authenticateMerchant } = require('../middleware/auth');

// Public webhook endpoint (no auth required, uses signature verification)
router.post('/:webhookId', express.json(), webhookController.handleIncomingWebhook);

// Protected routes (require merchant authentication)
router.use(authenticateMerchant);

// Webhook event management
router.get('/events', webhookController.listEvents);
router.get('/events/:eventId', webhookController.getEventDetails);
router.post('/events/:eventId/retry', webhookController.retryEvent);

// Error handling
router.use(webhookController.errorHandler);

module.exports = router;
