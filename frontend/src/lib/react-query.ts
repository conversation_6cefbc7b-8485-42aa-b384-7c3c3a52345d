import { QueryClient, QueryClientConfig } from '@tanstack/react-query';
import { toast } from '../components/ui/use-toast';

const handleError = (error: unknown) => {
  const errorMessage = error instanceof Error 
    ? error.message 
    : 'An error occurred while fetching data';
  
  toast({
    title: 'Error',
    description: errorMessage,
    variant: 'destructive',
  });
};

// Create a type-safe query client configuration
const queryConfig: QueryClientConfig = {
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      retry: 1,
      refetchOnWindowFocus: false,
    },
    mutations: {
      onError: (error: unknown) => {
        handleError(error);
      },
    },
  },
};

export const queryClient = new QueryClient(queryConfig);

// Custom hooks for common query options
export const queryOptions = {
  defaultOptions: (options = {}) => ({
    ...options,
    refetchOnWindowFocus: false,
    retry: 1,
  }),
  
  // Standard error handling for queries
  withErrorHandling: <T>(options = {}) => ({
    ...options,
    useErrorBoundary: (error: unknown) => {
      handleError(error);
      return true;
    },
  } as const),

  // Helper for paginated queries
  paginated: <T>(options = {}) => ({
    ...options,
    keepPreviousData: true,
    refetchOnWindowFocus: false,
  } as const),
};
