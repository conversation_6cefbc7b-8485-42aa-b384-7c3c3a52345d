{"version": 3, "file": "static/js/585.35b9ad52.chunk.js", "mappings": "mJAaM,MAAAA,GAAgBC,E,QAAAA,GAAiB,gBAAiB,CACtD,CACE,OACA,CACEC,EAAG,gEACHC,IAAK,Y,iCCLL,MAAAC,GAAcH,E,QAAAA,GAAiB,cAAe,CAClD,CAAC,SAAU,CAAEI,GAAI,KAAMC,GAAI,KAAMC,EAAG,KAAMJ,IAAK,WAC/C,CAAC,OAAQ,CAAEK,GAAI,KAAMC,GAAI,KAAMC,GAAI,IAAKC,GAAI,KAAMR,IAAK,WACvD,CAAC,OAAQ,CAAEK,GAAI,KAAMC,GAAI,QAASC,GAAI,KAAMC,GAAI,KAAMR,IAAK,Y,4ECX7D,MAAMS,GAAgBC,EAAAA,EAAAA,GACpB,8FAGIC,EAAQC,EAAAA,WAGZ,CAAAC,EAA0BC,KAAG,IAA5B,UAAEC,KAAcC,GAAOH,EAAA,OACxBI,EAAAA,EAAAA,KAAA,SACEH,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IAAGT,IAAiBM,MAC3BC,MAGRL,EAAMQ,YAAc,O,sICbpB,MAAMC,EAASC,EAAAA,GAITC,GAFcD,EAAAA,GAEAA,EAAAA,IAEdE,EAAgBX,EAAAA,WAGpB,CAAAC,EAAoCC,KAAG,IAAtC,UAAEC,EAAS,SAAES,KAAaR,GAAOH,EAAA,OAClCY,EAAAA,EAAAA,MAACJ,EAAAA,GAAuB,CACtBP,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IACT,4RACAH,MAEEC,EAAKQ,SAAA,CAERA,GACDP,EAAAA,EAAAA,KAACI,EAAAA,GAAoB,CAACK,SAAO,EAAAF,UAC3BP,EAAAA,EAAAA,KAACU,EAAAA,EAAW,CAACZ,UAAU,8BAI7BQ,EAAcJ,YAAcE,EAAAA,GAAwBF,YAEpD,MAAMS,EAAgBhB,EAAAA,WAGpB,CAAAiB,EAAyDf,KAAG,IAA3D,UAAEC,EAAS,SAAES,EAAQ,SAAEM,EAAW,YAAad,GAAOa,EAAA,OACvDZ,EAAAA,EAAAA,KAACI,EAAAA,GAAsB,CAAAG,UACrBP,EAAAA,EAAAA,KAACI,EAAAA,GAAuB,CACtBP,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IACT,6bACa,WAAbY,GACE,kIACFf,GAEFe,SAAUA,KACNd,EAAKQ,UAETP,EAAAA,EAAAA,KAACI,EAAAA,GAAwB,CACvBN,WAAWG,EAAAA,EAAAA,IACT,MACa,WAAbY,GACE,2FACFN,SAEDA,UAKTI,EAAcT,YAAcE,EAAAA,GAAwBF,YAEhCP,EAAAA,WAGlB,CAAAmB,EAA0BjB,KAAG,IAA5B,UAAEC,KAAcC,GAAOe,EAAA,OACxBd,EAAAA,EAAAA,KAACI,EAAAA,GAAqB,CACpBP,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IAAG,yCAA0CH,MACpDC,MAGIG,YAAcE,EAAAA,GAAsBF,YAEhD,MAAMa,EAAapB,EAAAA,WAGjB,CAAAqB,EAAoCnB,KAAG,IAAtC,UAAEC,EAAS,SAAES,KAAaR,GAAOiB,EAAA,OAClCR,EAAAA,EAAAA,MAACJ,EAAAA,GAAoB,CACnBP,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IACT,4NACAH,MAEEC,EAAKQ,SAAA,EAETP,EAAAA,EAAAA,KAAA,QAAMF,UAAU,+DAA8DS,UAC5EP,EAAAA,EAAAA,KAACI,EAAAA,GAA6B,CAAAG,UAC5BP,EAAAA,EAAAA,KAACiB,EAAAA,EAAK,CAACnB,UAAU,iBAIrBE,EAAAA,EAAAA,KAACI,EAAAA,GAAwB,CAAAG,SAAEA,SAG/BQ,EAAWb,YAAcE,EAAAA,GAAqBF,YAEtBP,EAAAA,WAGtB,CAAAuB,EAA0BrB,KAAG,IAA5B,UAAEC,KAAcC,GAAOmB,EAAA,OACxBlB,EAAAA,EAAAA,KAACI,EAAAA,GAAyB,CACxBP,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IAAG,2BAA4BH,MACtCC,MAGQG,YAAcE,EAAAA,GAA0BF,W,8GCrGxD,MAAMiB,EAAWxB,EAAAA,WACf,CAAAC,EAA0BC,KAAS,IAAlC,UAAEC,KAAcC,GAAOH,EACtB,OACEI,EAAAA,EAAAA,KAAA,YACEF,WAAWG,EAAAA,EAAAA,IACT,uSACAH,GAEFD,IAAKA,KACDE,MAKZoB,EAASjB,YAAc,W,8DCPjB,MAAAkB,GAAcvC,E,QAAAA,GAAiB,cAAe,CAClD,CAAC,OAAQ,CAAEC,EAAG,qCAAsCC,IAAK,WACzD,CAAC,WAAY,CAAEsC,OAAQ,wBAAyBtC,IAAK,aCwPvD,EA9PgBuC,KACd,MAAOC,EAAUC,IAAeC,EAAAA,EAAAA,UAAS,CACvCC,QAAS,GACTC,KAAM,UACNC,QAAS,GACTC,aAAc,MAETC,EAAcC,IAAmBN,EAAAA,EAAAA,WAAS,IAC1CO,EAAaC,IAAkBR,EAAAA,EAAAA,WAAS,IACxCS,EAAOC,IAAYV,EAAAA,EAAAA,UAAS,IAE7BW,EAAgBC,IACpB,MAAM,KAAEC,EAAI,MAAEC,GAAUF,EAAEG,OAC1BhB,EAAYiB,IAAI,IACXA,EACH,CAACH,GAAOC,MA0DNG,EAAiB,CACrB,CACEC,MAAM3C,EAAAA,EAAAA,KAACpB,EAAAA,EAAa,CAACkB,UAAU,yBAC/B8C,MAAO,YACPC,YAAa,0CACbC,OAAQ,aACRC,KAAM,KAER,CACEJ,MAAM3C,EAAAA,EAAAA,KAACgD,EAAAA,EAAI,CAAClD,UAAU,yBACtB8C,MAAO,WACPC,YAAa,6BACbC,OAAQ,aACRC,KAAM,qCAER,CACEJ,MAAM3C,EAAAA,EAAAA,KAACiD,EAAAA,EAAK,CAACnD,UAAU,yBACvB8C,MAAO,UACPC,YAAa,oBACbC,OAAQ,WACRC,KAAM,qBAIV,OACEvC,EAAAA,EAAAA,MAAA,OAAKV,UAAU,YAAWS,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEP,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oCAAmCS,SAAC,oBAClDP,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wBAAuBS,SAAC,gEAKvCC,EAAAA,EAAAA,MAAA,OAAKV,UAAU,4BAA2BS,SAAA,EACxCC,EAAAA,EAAAA,MAAC0C,EAAAA,GAAI,CAAA3C,SAAA,EACHP,EAAAA,EAAAA,KAACmD,EAAAA,GAAU,CAAA5C,UACTP,EAAAA,EAAAA,KAACoD,EAAAA,GAAS,CAAA7C,SAAC,uBAEbP,EAAAA,EAAAA,KAACqD,EAAAA,GAAW,CAAA9C,UACVP,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWS,SACvBmC,EAAeY,IAAI,CAACC,EAAQC,KAC3BhD,EAAAA,EAAAA,MAAA,OAAiBV,UAAU,mDAAkDS,SAAA,EAC3EP,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iCAAgCS,SAC5CgD,EAAOZ,QAEVnC,EAAAA,EAAAA,MAAA,OAAKV,UAAU,SAAQS,SAAA,EACrBP,EAAAA,EAAAA,KAAA,MAAIF,UAAU,cAAaS,SAAEgD,EAAOX,SACpC5C,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gCAA+BS,SAAEgD,EAAOV,kBAEvD7C,EAAAA,EAAAA,KAACyD,EAAAA,EAAM,CAACC,QAAQ,UAAUC,KAAK,KAAKlD,SAAO,EAAAF,UACzCP,EAAAA,EAAAA,KAAA,KAAG+C,KAAMQ,EAAOR,KAAKxC,SAAEgD,EAAOT,aATxBU,YAiBlBhD,EAAAA,EAAAA,MAAC0C,EAAAA,GAAI,CAAA3C,SAAA,EACHP,EAAAA,EAAAA,KAACmD,EAAAA,GAAU,CAAA5C,UACTP,EAAAA,EAAAA,KAACoD,EAAAA,GAAS,CAAA7C,SAAC,yBAEbP,EAAAA,EAAAA,KAACqD,EAAAA,GAAW,CAAA9C,UACVC,EAAAA,EAAAA,MAAA,QAAMoD,SA9GKC,UAKnB,GAJAxB,EAAEyB,iBACF3B,EAAS,IAGJZ,EAASG,SAAYH,EAASK,QAAnC,CAKAG,GAAgB,GAEhB,UAEQ,IAAIgC,QAAQC,GAAWC,WAAWD,EAAS,OAGjDxC,EAAY,CACVE,QAAS,GACTC,KAAM,UACNC,QAAS,GACTC,aAAc,KAGhBI,GAAe,GAGfgC,WAAW,KACThC,GAAe,IACd,IAEL,CAAE,MAAOiC,GACP/B,EAAS,yDACX,CAAC,QACCJ,GAAgB,EAClB,CA3BA,MAFEI,EAAS,uCAwG2BrC,UAAU,YAAWS,SAAA,CAChD2B,IACC1B,EAAAA,EAAAA,MAAA,OAAKV,UAAU,wEAAuES,SAAA,EACpFP,EAAAA,EAAAA,KAAChB,EAAAA,EAAW,CAACc,UAAU,aACvBE,EAAAA,EAAAA,KAAA,QAAAO,SAAO2B,OAIVF,IACCxB,EAAAA,EAAAA,MAAA,OAAKV,UAAU,4EAA2ES,SAAA,EACxFP,EAAAA,EAAAA,KAACoB,EAAW,CAACtB,UAAU,aACvBE,EAAAA,EAAAA,KAAA,QAAAO,SAAM,gEAIVC,EAAAA,EAAAA,MAAA,OAAKV,UAAU,YAAWS,SAAA,EACxBP,EAAAA,EAAAA,KAACN,EAAAA,EAAK,CAACyE,QAAQ,UAAS5D,SAAC,eACzBP,EAAAA,EAAAA,KAACoE,EAAAA,EAAK,CACJC,GAAG,UACH/B,KAAK,UACLC,MAAOhB,EAASG,QAChB4C,SAAUlC,EACVmC,YAAY,uBACZC,UAAQ,QAIZhE,EAAAA,EAAAA,MAAA,OAAKV,UAAU,YAAWS,SAAA,EACxBP,EAAAA,EAAAA,KAACN,EAAAA,EAAK,CAACyE,QAAQ,OAAM5D,SAAC,uBACtBC,EAAAA,EAAAA,MAACL,EAAAA,GAAM,CACLoC,MAAOhB,EAASI,KAChB8C,cAAgBlC,GApJLmC,EAACpC,EAAcC,KACxCf,EAAYiB,IAAI,IACXA,EACH,CAACH,GAAOC,MAiJ8BmC,CAAmB,OAAQnC,GAAOhC,SAAA,EAE5DP,EAAAA,EAAAA,KAACM,EAAAA,GAAa,CAAAC,UACZP,EAAAA,EAAAA,KAACK,EAAAA,GAAW,CAACkE,YAAY,2BAE3BvE,EAAAA,EAAAA,KAACW,EAAAA,GAAa,CAAAJ,SA5GT,CACnB,CAAEgC,MAAO,UAAWoC,MAAO,mBAC3B,CAAEpC,MAAO,YAAaoC,MAAO,qBAC7B,CAAEpC,MAAO,UAAWoC,MAAO,sBAC3B,CAAEpC,MAAO,UAAWoC,MAAO,kBAC3B,CAAEpC,MAAO,UAAWoC,MAAO,mBAC3B,CAAEpC,MAAO,QAASoC,MAAO,UAuGKrB,IAAK3B,IACjB3B,EAAAA,EAAAA,KAACe,EAAAA,GAAU,CAAkBwB,MAAOZ,EAAKY,MAAMhC,SAC5CoB,EAAKgD,OADShD,EAAKY,iBAQ9B/B,EAAAA,EAAAA,MAAA,OAAKV,UAAU,YAAWS,SAAA,EACxBP,EAAAA,EAAAA,KAACN,EAAAA,EAAK,CAACyE,QAAQ,UAAS5D,SAAC,eACzBP,EAAAA,EAAAA,KAACmB,EAAQ,CACPkD,GAAG,UACH/B,KAAK,UACLC,MAAOhB,EAASK,QAChB0C,SAAUlC,EACVmC,YAAY,0CACZK,KAAM,EACNJ,UAAQ,QAIZhE,EAAAA,EAAAA,MAAA,OAAKV,UAAU,YAAWS,SAAA,EACxBP,EAAAA,EAAAA,KAACN,EAAAA,EAAK,CAACyE,QAAQ,eAAc5D,SAAC,mBAC9BP,EAAAA,EAAAA,KAACoE,EAAAA,EAAK,CACJC,GAAG,eACH/B,KAAK,eACLX,KAAK,QACLY,MAAOhB,EAASM,aAChByC,SAAUlC,EACVmC,YAAY,4BAEdvE,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gCAA+BS,SAAC,2EAK/CP,EAAAA,EAAAA,KAACyD,EAAAA,EAAM,CAAC9B,KAAK,SAASkD,SAAU/C,EAAchC,UAAU,SAAQS,SAC7DuB,EAAe,aAAe,6BAOzCtB,EAAAA,EAAAA,MAAC0C,EAAAA,GAAI,CAAA3C,SAAA,EACHP,EAAAA,EAAAA,KAACmD,EAAAA,GAAU,CAAA5C,UACTP,EAAAA,EAAAA,KAACoD,EAAAA,GAAS,CAAA7C,SAAC,kCAEbP,EAAAA,EAAAA,KAACqD,EAAAA,GAAW,CAAA9C,UACVP,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWS,SACvB,CACC,CACEuE,SAAU,8BACVC,OAAQ,4FAEV,CACED,SAAU,sCACVC,OAAQ,sFAEV,CACED,SAAU,iDACVC,OAAQ,yFAEV,CACED,SAAU,mCACVC,OAAQ,iGAEVzB,IAAI,CAAC0B,EAAKxB,KACVhD,EAAAA,EAAAA,MAAA,OAAiBV,UAAU,0CAAyCS,SAAA,EAClEP,EAAAA,EAAAA,KAAA,MAAIF,UAAU,cAAaS,SAAEyE,EAAIF,YACjC9E,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qCAAoCS,SAAEyE,EAAID,WAF/CvB,c,kCC9OlB,MAAAP,GAAQpE,E,QAAAA,GAAiB,QAAS,CACtC,CACE,OACA,CACEC,EAAG,gSACHC,IAAK,Y,iHCdX,MAAMmE,EAAOvD,EAAAA,WAGX,CAAAC,EAA0BC,KAAG,IAA5B,UAAEC,KAAcC,GAAOH,EAAA,OACxBI,EAAAA,EAAAA,KAAA,OACEH,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IACT,2DACAH,MAEEC,MAGRmD,EAAKhD,YAAc,OAEnB,MAAMiD,EAAaxD,EAAAA,WAGjB,CAAAiB,EAA0Bf,KAAG,IAA5B,UAAEC,KAAcC,GAAOa,EAAA,OACxBZ,EAAAA,EAAAA,KAAA,OACEH,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IAAG,gCAAiCH,MAC3CC,MAGRoD,EAAWjD,YAAc,aAEzB,MAAMkD,EAAYzD,EAAAA,WAGhB,CAAAmB,EAA0BjB,KAAG,IAA5B,UAAEC,KAAcC,GAAOe,EAAA,OACxBd,EAAAA,EAAAA,KAAA,MACEH,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IACT,qDACAH,MAEEC,MAGRqD,EAAUlD,YAAc,YAExB,MAAM+E,EAAkBtF,EAAAA,WAGtB,CAAAqB,EAA0BnB,KAAG,IAA5B,UAAEC,KAAcC,GAAOiB,EAAA,OACxBhB,EAAAA,EAAAA,KAAA,KACEH,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IAAG,gCAAiCH,MAC3CC,MAGRkF,EAAgB/E,YAAc,kBAE9B,MAAMmD,EAAc1D,EAAAA,WAGlB,CAAAuB,EAA0BrB,KAAG,IAA5B,UAAEC,KAAcC,GAAOmB,EAAA,OACxBlB,EAAAA,EAAAA,KAAA,OAAKH,IAAKA,EAAKC,WAAWG,EAAAA,EAAAA,IAAG,WAAYH,MAAgBC,MAE3DsD,EAAYnD,YAAc,cAE1B,MAAMgF,EAAavF,EAAAA,WAGjB,CAAAwF,EAA0BtF,KAAG,IAA5B,UAAEC,KAAcC,GAAOoF,EAAA,OACxBnF,EAAAA,EAAAA,KAAA,OACEH,IAAKA,EACLC,WAAWG,EAAAA,EAAAA,IAAG,6BAA8BH,MACxCC,MAGRmF,EAAWhF,YAAc,Y,kCC/DnB,MAAA8C,GAAOnE,E,QAAAA,GAAiB,OAAQ,CACpC,CACE,OACA,CAAEuG,MAAO,KAAMC,OAAQ,KAAMC,EAAG,IAAKC,EAAG,IAAKC,GAAI,IAAKzG,IAAK,WAE7D,CAAC,OAAQ,CAAED,EAAG,4CAA6CC,IAAK,Y,sFCZlE,MAAM0G,GAAiBhG,EAAAA,EAAAA,GACrB,yRACA,CACEiG,SAAU,CACRhC,QAAS,CACPiC,QAAS,yDACTC,YACE,qEACFC,QACE,iFACFC,UACE,+DACFC,MAAO,+CACPC,KAAM,mDAERrC,KAAM,CACJgC,QAAS,iBACTM,GAAI,sBACJC,GAAI,uBACJvD,KAAM,cAGVwD,gBAAiB,CACfzC,QAAS,UACTC,KAAM,aAWNF,EAAS9D,EAAAA,WACb,CAAAC,EAA0DC,KAAS,IAAlE,UAAEC,EAAS,QAAE4D,EAAO,KAAEC,EAAI,QAAElD,GAAU,KAAUV,GAAOH,EACtD,MAAMwG,EAAO3F,EAAU4F,EAAAA,GAAO,SAC9B,OACErG,EAAAA,EAAAA,KAACoG,EAAI,CACHtG,WAAWG,EAAAA,EAAAA,IAAGwF,EAAe,CAAE/B,UAASC,OAAM7D,eAC9CD,IAAKA,KACDE,MAKZ0D,EAAOvD,YAAc,Q,mEC9CrB,MAAMkE,EAAQzE,EAAAA,WACZ,CAAAC,EAAgCC,KAAS,IAAxC,UAAEC,EAAS,KAAE6B,KAAS5B,GAAOH,EAC5B,OACEI,EAAAA,EAAAA,KAAA,SACE2B,KAAMA,EACN7B,WAAWG,EAAAA,EAAAA,IACT,+VACAH,GAEFD,IAAKA,KACDE,MAKZqE,EAAMlE,YAAc,O", "sources": ["../node_modules/lucide-react/src/icons/message-square.ts", "../node_modules/lucide-react/src/icons/alert-circle.ts", "components/ui/label.tsx", "components/ui/select.tsx", "components/ui/textarea.tsx", "../node_modules/lucide-react/src/icons/check-circle.ts", "pages/merchant/Support.tsx", "../node_modules/lucide-react/src/icons/phone.ts", "components/ui/card.tsx", "../node_modules/lucide-react/src/icons/mail.ts", "components/ui/button.tsx", "components/ui/input.tsx"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name MessageSquare\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTVhMiAyIDAgMCAxLTIgMkg3bC00IDRWNWEyIDIgMCAwIDEgMi0yaDE0YTIgMiAwIDAgMSAyIDJ6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/message-square\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MessageSquare = createLucideIcon('MessageSquare', [\n  [\n    'path',\n    {\n      d: 'M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z',\n      key: '1lielz',\n    },\n  ],\n]);\n\nexport default MessageSquare;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name AlertCircle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjgiIHkyPSIxMiIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMi4wMSIgeTE9IjE2IiB5Mj0iMTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/alert-circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst AlertCircle = createLucideIcon('AlertCircle', [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['line', { x1: '12', x2: '12', y1: '8', y2: '12', key: '1pkeuh' }],\n  ['line', { x1: '12', x2: '12.01', y1: '16', y2: '16', key: '4dfq90' }],\n]);\n\nexport default AlertCircle;\n", "import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  HTMLLabelElement,\n  React.LabelHTMLAttributes<HTMLLabelElement> & VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <label\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = \"Label\"\n\nexport { Label }\n", "import * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown } from \"lucide-react\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n}\n", "import * as React from 'react';\nimport { cn } from '../../lib/utils';\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          'flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nTextarea.displayName = 'Textarea';\n\nexport { Textarea };\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name CheckCircle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjIgMTEuMDhWMTJhMTAgMTAgMCAxIDEtNS45My05LjE0IiAvPgogIDxwb2x5bGluZSBwb2ludHM9IjIyIDQgMTIgMTQuMDEgOSAxMS4wMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/check-circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CheckCircle = createLucideIcon('CheckCircle', [\n  ['path', { d: 'M22 11.08V12a10 10 0 1 1-5.93-9.14', key: 'g774vq' }],\n  ['polyline', { points: '22 4 12 14.01 9 11.01', key: '6xbx8j' }],\n]);\n\nexport default CheckCircle;\n", "import React, { useState } from 'react';\nimport { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';\nimport { Button } from '../../components/ui/button';\nimport { Input } from '../../components/ui/input';\nimport { Textarea } from '../../components/ui/textarea';\nimport { Label } from '../../components/ui/label';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';\nimport { Mail, MessageSquare, Phone, AlertCircle, CheckCircle } from 'lucide-react';\n\nconst Support = () => {\n  const [formData, setFormData] = useState({\n    subject: '',\n    type: 'general',\n    message: '',\n    contactEmail: '',\n  });\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [isSubmitted, setIsSubmitted] = useState(false);\n  const [error, setError] = useState('');\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSelectChange = (name: string, value: string) => {\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setError('');\n    \n    // Basic validation\n    if (!formData.subject || !formData.message) {\n      setError('Please fill in all required fields');\n      return;\n    }\n\n    setIsSubmitting(true);\n    \n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1500));\n      \n      // Reset form on success\n      setFormData({\n        subject: '',\n        type: 'general',\n        message: '',\n        contactEmail: '',\n      });\n      \n      setIsSubmitted(true);\n      \n      // Reset success message after 5 seconds\n      setTimeout(() => {\n        setIsSubmitted(false);\n      }, 5000);\n      \n    } catch (err) {\n      setError('Failed to submit your request. Please try again later.');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const supportTypes = [\n    { value: 'general', label: 'General Inquiry' },\n    { value: 'technical', label: 'Technical Support' },\n    { value: 'billing', label: 'Billing & Payments' },\n    { value: 'account', label: 'Account Issues' },\n    { value: 'feature', label: 'Feature Request' },\n    { value: 'other', label: 'Other' },\n  ];\n\n  const contactMethods = [\n    {\n      icon: <MessageSquare className=\"h-6 w-6 text-primary\" />,\n      title: 'Live Chat',\n      description: 'Chat with our support team in real-time',\n      action: 'Start Chat',\n      href: '#',\n    },\n    {\n      icon: <Mail className=\"h-6 w-6 text-primary\" />,\n      title: 'Email Us',\n      description: '<EMAIL>',\n      action: 'Send Email',\n      href: 'mailto:<EMAIL>',\n    },\n    {\n      icon: <Phone className=\"h-6 w-6 text-primary\" />,\n      title: 'Call Us',\n      description: '+****************',\n      action: 'Call Now',\n      href: 'tel:+***********',\n    },\n  ];\n\n  return (\n    <div className=\"space-y-6\">\n      <div>\n        <h2 className=\"text-2xl font-bold tracking-tight\">Help & Support</h2>\n        <p className=\"text-muted-foreground\">\n          We're here to help. Get in touch with our support team.\n        </p>\n      </div>\n\n      <div className=\"grid gap-6 md:grid-cols-2\">\n        <Card>\n          <CardHeader>\n            <CardTitle>Contact Support</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-6\">\n              {contactMethods.map((method, index) => (\n                <div key={index} className=\"flex items-start space-x-4 p-4 border rounded-lg\">\n                  <div className=\"p-2 rounded-full bg-primary/10\">\n                    {method.icon}\n                  </div>\n                  <div className=\"flex-1\">\n                    <h4 className=\"font-medium\">{method.title}</h4>\n                    <p className=\"text-sm text-muted-foreground\">{method.description}</p>\n                  </div>\n                  <Button variant=\"outline\" size=\"sm\" asChild>\n                    <a href={method.href}>{method.action}</a>\n                  </Button>\n                </div>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader>\n            <CardTitle>Send us a message</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <form onSubmit={handleSubmit} className=\"space-y-4\">\n              {error && (\n                <div className=\"flex items-center gap-2 p-3 text-sm text-red-600 bg-red-50 rounded-md\">\n                  <AlertCircle className=\"h-4 w-4\" />\n                  <span>{error}</span>\n                </div>\n              )}\n              \n              {isSubmitted && (\n                <div className=\"flex items-center gap-2 p-3 text-sm text-green-600 bg-green-50 rounded-md\">\n                  <CheckCircle className=\"h-4 w-4\" />\n                  <span>Your message has been sent. We'll get back to you soon!</span>\n                </div>\n              )}\n              \n              <div className=\"space-y-2\">\n                <Label htmlFor=\"subject\">Subject *</Label>\n                <Input\n                  id=\"subject\"\n                  name=\"subject\"\n                  value={formData.subject}\n                  onChange={handleChange}\n                  placeholder=\"How can we help you?\"\n                  required\n                />\n              </div>\n              \n              <div className=\"space-y-2\">\n                <Label htmlFor=\"type\">Type of Inquiry *</Label>\n                <Select\n                  value={formData.type}\n                  onValueChange={(value) => handleSelectChange('type', value)}\n                >\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Select inquiry type\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {supportTypes.map((type) => (\n                      <SelectItem key={type.value} value={type.value}>\n                        {type.label}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n              \n              <div className=\"space-y-2\">\n                <Label htmlFor=\"message\">Message *</Label>\n                <Textarea\n                  id=\"message\"\n                  name=\"message\"\n                  value={formData.message}\n                  onChange={handleChange}\n                  placeholder=\"Please describe your issue in detail...\"\n                  rows={5}\n                  required\n                />\n              </div>\n              \n              <div className=\"space-y-2\">\n                <Label htmlFor=\"contactEmail\">Contact Email</Label>\n                <Input\n                  id=\"contactEmail\"\n                  name=\"contactEmail\"\n                  type=\"email\"\n                  value={formData.contactEmail}\n                  onChange={handleChange}\n                  placeholder=\"<EMAIL>\"\n                />\n                <p className=\"text-xs text-muted-foreground\">\n                  We'll use this to respond to your inquiry if you're not logged in.\n                </p>\n              </div>\n              \n              <Button type=\"submit\" disabled={isSubmitting} className=\"w-full\">\n                {isSubmitting ? 'Sending...' : 'Send Message'}\n              </Button>\n            </form>\n          </CardContent>\n        </Card>\n      </div>\n      \n      <Card>\n        <CardHeader>\n          <CardTitle>Frequently Asked Questions</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-4\">\n            {[\n              {\n                question: 'How do I reset my password?',\n                answer: 'You can reset your password by clicking on the \"Forgot Password\" link on the login page.',\n              },\n              {\n                question: 'What payment methods do you accept?',\n                answer: 'We accept all major credit cards, bank transfers, and other local payment methods.',\n              },\n              {\n                question: 'How long does it take to process a withdrawal?',\n                answer: 'Withdrawals are typically processed within 1-3 business days, depending on your bank.',\n              },\n              {\n                question: 'Is there a fee for transactions?',\n                answer: 'Our standard processing fee is 2.9% + $0.30 per transaction. Volume discounts are available.',\n              },\n            ].map((faq, index) => (\n              <div key={index} className=\"border-b pb-4 last:border-b-0 last:pb-0\">\n                <h4 className=\"font-medium\">{faq.question}</h4>\n                <p className=\"text-sm text-muted-foreground mt-1\">{faq.answer}</p>\n              </div>\n            ))}\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n};\n\nexport default Support;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Phone\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjIgMTYuOTJ2M2EyIDIgMCAwIDEtMi4xOCAyIDE5Ljc5IDE5Ljc5IDAgMCAxLTguNjMtMy4wNyAxOS41IDE5LjUgMCAwIDEtNi02IDE5Ljc5IDE5Ljc5IDAgMCAxLTMuMDctOC42N0EyIDIgMCAwIDEgNC4xMSAyaDNhMiAyIDAgMCAxIDIgMS43MiAxMi44NCAxMi44NCAwIDAgMCAuNyAyLjgxIDIgMiAwIDAgMS0uNDUgMi4xMUw4LjA5IDkuOTFhMTYgMTYgMCAwIDAgNiA2bDEuMjctMS4yN2EyIDIgMCAwIDEgMi4xMS0uNDUgMTIuODQgMTIuODQgMCAwIDAgMi44MS43QTIgMiAwIDAgMSAyMiAxNi45MnoiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/phone\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Phone = createLucideIcon('Phone', [\n  [\n    'path',\n    {\n      d: 'M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z',\n      key: 'foiqr5',\n    },\n  ],\n]);\n\nexport default Phone;\n", "import * as React from \"react\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardDescription,\n  CardContent,\n}\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Mail\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iMTYiIHg9IjIiIHk9IjQiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Im0yMiA3LTguOTcgNS43YTEuOTQgMS45NCAwIDAgMS0yLjA2IDBMMiA3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/mail\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Mail = createLucideIcon('Mail', [\n  [\n    'rect',\n    { width: '20', height: '16', x: '2', y: '4', rx: '2', key: '18n3k1' },\n  ],\n  ['path', { d: 'm22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7', key: '1ocrg3' }],\n]);\n\nexport default Mail;\n", "import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n", "import * as React from \"react\"\n\nimport { cn } from \"../../lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": ["MessageSquare", "createLucideIcon", "d", "key", "AlertCircle", "cx", "cy", "r", "x1", "x2", "y1", "y2", "labelVariants", "cva", "Label", "React", "_ref", "ref", "className", "props", "_jsx", "cn", "displayName", "Select", "SelectPrimitive", "SelectValue", "SelectTrigger", "children", "_jsxs", "<PERSON><PERSON><PERSON><PERSON>", "ChevronDown", "SelectContent", "_ref2", "position", "_ref3", "SelectItem", "_ref4", "Check", "_ref5", "Textarea", "CheckCircle", "points", "Support", "formData", "setFormData", "useState", "subject", "type", "message", "contactEmail", "isSubmitting", "setIsSubmitting", "isSubmitted", "setIsSubmitted", "error", "setError", "handleChange", "e", "name", "value", "target", "prev", "contactMethods", "icon", "title", "description", "action", "href", "Mail", "Phone", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "<PERSON><PERSON><PERSON><PERSON>", "map", "method", "index", "<PERSON><PERSON>", "variant", "size", "onSubmit", "async", "preventDefault", "Promise", "resolve", "setTimeout", "err", "htmlFor", "Input", "id", "onChange", "placeholder", "required", "onValueChange", "handleSelectChange", "label", "rows", "disabled", "question", "answer", "faq", "CardDescription", "<PERSON><PERSON><PERSON>er", "_ref6", "width", "height", "x", "y", "rx", "buttonVariants", "variants", "default", "destructive", "outline", "secondary", "ghost", "link", "sm", "lg", "defaultVariants", "Comp", "Slot"], "sourceRoot": ""}