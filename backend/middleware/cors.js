const cors = require('cors');
const logger = require('../utils/logger');

// Default CORS configuration
const defaultOptions = {
  // Origin configuration
  origin: (origin, callback) => {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) {
      return callback(null, true);
    }

    // Parse allowed origins from environment variable or use default
    const allowedOrigins = process.env.CORS_ALLOWED_ORIGINS
      ? process.env.CORS_ALLOWED_ORIGINS.split(',').map(o => o.trim())
      : ['http://localhost:3000', 'http://localhost:5000'];

    // Check if the origin is allowed
    if (allowedOrigins.includes(origin) || allowedOrigins.includes('*')) {
      return callback(null, true);
    }

    // Log blocked origins in production
    if (process.env.NODE_ENV === 'production') {
      logger.warn(`CORS blocked request from origin: ${origin}`);
    }

    return callback(new Error('Not allowed by CORS'));
  },
  
  // Allowed HTTP methods
  methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS', 'HEAD'],
  
  // Allowed request headers
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin',
    'X-API-Key',
    'X-Request-ID',
    'X-Forwarded-For',
    'X-Forwarded-Proto',
    'X-Forwarded-Port',
  ],
  
  // Exposed headers
  exposedHeaders: [
    'Content-Length',
    'Content-Type',
    'Content-Disposition',
    'X-Request-ID',
    'X-Total-Count',
    'X-RateLimit-Limit',
    'X-RateLimit-Remaining',
    'X-RateLimit-Reset',
    'Retry-After',
  ],
  
  // Allow credentials (cookies, authorization headers, etc.)
  credentials: true,
  
  // Cache preflight requests for 1 hour (in seconds)
  maxAge: 3600,
  
  // Set to true to pass the CORS preflight response to the next handler
  preflightContinue: false,
  
  // Set the status code for successful OPTIONS requests
  optionsSuccessStatus: 204,
};

/**
 * Create a CORS middleware with the given options
 * @param {Object} options - CORS options
 * @returns {Function} Express middleware function
 */
const createCors = (options = {}) => {
  // Merge default options with custom options
  const corsOptions = { ...defaultOptions, ...options };
  
  // Return the CORS middleware
  return cors(corsOptions);
};

/**
 * Middleware to handle CORS preflight requests
 * This is needed for some clients that don't send proper preflight requests
 */
const handlePreflight = (req, res, next) => {
  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    // Set CORS headers
    res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
    res.header('Access-Control-Allow-Methods', defaultOptions.methods.join(','));
    res.header('Access-Control-Allow-Headers', defaultOptions.allowedHeaders.join(','));
    res.header('Access-Control-Allow-Credentials', 'true');
    res.header('Access-Control-Max-Age', defaultOptions.maxAge);
    
    // End the request
    return res.status(204).end();
  }
  
  next();
};

/**
 * Middleware to set security headers
 */
const securityHeaders = (req, res, next) => {
  // Set security headers
  res.header('X-Frame-Options', 'DENY');
  res.header('X-Content-Type-Options', 'nosniff');
  res.header('X-XSS-Protection', '1; mode=block');
  res.header('Referrer-Policy', 'strict-origin-when-cross-origin');
  res.header('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');
  
  // Set Content Security Policy
  const csp = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https: http:",
    "font-src 'self' data:",
    "connect-src 'self' https: wss:",
    "frame-ancestors 'none'",
    "form-action 'self'",
    "base-uri 'self'",
    "object-src 'none'",
  ];
  
  res.header('Content-Security-Policy', csp.join('; '));
  
  next();
};

module.exports = {
  createCors,
  handlePreflight,
  securityHeaders,
  defaultOptions,
};
