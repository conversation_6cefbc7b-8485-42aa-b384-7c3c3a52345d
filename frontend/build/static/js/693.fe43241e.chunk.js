"use strict";(self.webpackChunkpayment_gateway_frontend=self.webpackChunkpayment_gateway_frontend||[]).push([[693],{276:(e,t,n)=>{n.d(t,{n:()=>d});var r=n(5043),o=n(2814),c=n(7920),a=n(7490),u=n(579),i="focusScope.autoFocusOnMount",s="focusScope.autoFocusOnUnmount",l={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{const{loop:n=!1,trapped:d=!1,onMountAutoFocus:m,onUnmountAutoFocus:E,...y}=e,[b,g]=r.useState(null),w=(0,a.c)(m),C=(0,a.c)(E),S=r.useRef(null),N=(0,o.s)(t,e=>g(e)),R=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(R.paused||!b)return;const t=e.target;b.contains(t)?S.current=t:h(S.current,{select:!0})},t=function(e){if(R.paused||!b)return;const t=e.relatedTarget;null!==t&&(b.contains(t)||h(S.current,{select:!0}))},n=function(e){if(document.activeElement===document.body)for(const t of e)t.removedNodes.length>0&&h(b)};document.addEventListener("focusin",e),document.addEventListener("focusout",t);const r=new MutationObserver(n);return b&&r.observe(b,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[d,b,R.paused]),r.useEffect(()=>{if(b){p.add(R);const t=document.activeElement;if(!b.contains(t)){const n=new CustomEvent(i,l);b.addEventListener(i,w),b.dispatchEvent(n),n.defaultPrevented||(!function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n=document.activeElement;for(const r of e)if(h(r,{select:t}),document.activeElement!==n)return}((e=f(b),e.filter(e=>"A"!==e.tagName)),{select:!0}),document.activeElement===t&&h(b))}return()=>{b.removeEventListener(i,w),setTimeout(()=>{const e=new CustomEvent(s,l);b.addEventListener(s,C),b.dispatchEvent(e),e.defaultPrevented||h(t??document.body,{select:!0}),b.removeEventListener(s,C),p.remove(R)},0)}}var e},[b,w,C,R]);const T=r.useCallback(e=>{if(!n&&!d)return;if(R.paused)return;const t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){const t=e.currentTarget,[o,c]=function(e){const t=f(e),n=v(t,e),r=v(t.reverse(),e);return[n,r]}(t);o&&c?e.shiftKey||r!==c?e.shiftKey&&r===o&&(e.preventDefault(),n&&h(c,{select:!0})):(e.preventDefault(),n&&h(o,{select:!0})):r===t&&e.preventDefault()}},[n,d,R.paused]);return(0,u.jsx)(c.sG.div,{tabIndex:-1,...y,ref:N,onKeyDown:T})});function f(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{const t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function v(e,t){for(const n of e)if(!m(n,{upTo:t}))return n}function m(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e;){if(void 0!==n&&e===n)return!1;if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}function h(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&function(e){return e instanceof HTMLInputElement&&"select"in e}(e)&&t&&e.select()}}d.displayName="FocusScope";var p=function(){let e=[];return{add(t){const n=e[0];t!==n&&n?.pause(),e=E(e,t),e.unshift(t)},remove(t){e=E(e,t),e[0]?.resume()}}}();function E(e,t){const n=[...e],r=n.indexOf(t);return-1!==r&&n.splice(r,1),n}},4064:(e,t,n)=>{n.d(t,{A:()=>M});var r=n(6326),o=n(5043),c=n(3259),a=n(6934),u=(0,n(1872).f)(),i=function(){},s=o.forwardRef(function(e,t){var n=o.useRef(null),c=o.useState({onScrollCapture:i,onWheelCapture:i,onTouchMoveCapture:i}),s=c[0],l=c[1],d=e.forwardProps,f=e.children,v=e.className,m=e.removeScrollBar,h=e.enabled,p=e.shards,E=e.sideCar,y=e.noRelative,b=e.noIsolation,g=e.inert,w=e.allowPinchZoom,C=e.as,S=void 0===C?"div":C,N=e.gapMode,R=(0,r.Tt)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),T=E,L=(0,a.S)([n,t]),k=(0,r.Cl)((0,r.Cl)({},R),s);return o.createElement(o.Fragment,null,h&&o.createElement(T,{sideCar:u,removeScrollBar:m,shards:p,noRelative:y,noIsolation:b,inert:g,setCallbacks:l,allowPinchZoom:!!w,lockRef:n,gapMode:N}),d?o.cloneElement(o.Children.only(f),(0,r.Cl)((0,r.Cl)({},k),{ref:L})):o.createElement(S,(0,r.Cl)({},k,{className:v,ref:L}),f))});s.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},s.classNames={fullWidth:c.pN,zeroRight:c.Mi};var l=n(4560),d=n(5857),f=n(7513),v=!1;if("undefined"!==typeof window)try{var m=Object.defineProperty({},"passive",{get:function(){return v=!0,!0}});window.addEventListener("test",m,m),window.removeEventListener("test",m,m)}catch(P){v=!1}var h=!!v&&{passive:!1},p=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&!function(e){return"TEXTAREA"===e.tagName}(e)&&"visible"===n[t])},E=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!==typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),y(e,r)){var o=b(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},y=function(e,t){return"v"===e?function(e){return p(e,"overflowY")}(t):function(e){return p(e,"overflowX")}(t)},b=function(e,t){return"v"===e?[(n=t).scrollTop,n.scrollHeight,n.clientHeight]:function(e){return[e.scrollLeft,e.scrollWidth,e.clientWidth]}(t);var n},g=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},w=function(e){return[e.deltaX,e.deltaY]},C=function(e){return e&&"current"in e?e.current:e},S=function(e){return"\n  .block-interactivity-".concat(e," {pointer-events: none;}\n  .allow-interactivity-").concat(e," {pointer-events: all;}\n")},N=0,R=[];function T(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const L=(0,l.m)(u,function(e){var t=o.useRef([]),n=o.useRef([0,0]),c=o.useRef(),a=o.useState(N++)[0],u=o.useState(f.T0)[0],i=o.useRef(e);o.useEffect(function(){i.current=e},[e]),o.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(a));var t=(0,r.fX)([e.lockRef.current],(e.shards||[]).map(C),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(a))}),function(){document.body.classList.remove("block-interactivity-".concat(a)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(a))})}}},[e.inert,e.lockRef.current,e.shards]);var s=o.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!i.current.allowPinchZoom;var r,o=g(e),a=n.current,u="deltaX"in e?e.deltaX:a[0]-o[0],s="deltaY"in e?e.deltaY:a[1]-o[1],l=e.target,d=Math.abs(u)>Math.abs(s)?"h":"v";if("touches"in e&&"h"===d&&"range"===l.type)return!1;var f=E(d,l);if(!f)return!0;if(f?r=d:(r="v"===d?"h":"v",f=E(d,l)),!f)return!1;if(!c.current&&"changedTouches"in e&&(u||s)&&(c.current=r),!r)return!0;var v=c.current||r;return function(e,t,n,r,o){var c=function(e,t){return"h"===e&&"rtl"===t?-1:1}(e,window.getComputedStyle(t).direction),a=c*r,u=n.target,i=t.contains(u),s=!1,l=a>0,d=0,f=0;do{if(!u)break;var v=b(e,u),m=v[0],h=v[1]-v[2]-c*m;(m||h)&&y(e,u)&&(d+=h,f+=m);var p=u.parentNode;u=p&&p.nodeType===Node.DOCUMENT_FRAGMENT_NODE?p.host:p}while(!i&&u!==document.body||i&&(t.contains(u)||t===u));return(l&&(o&&Math.abs(d)<1||!o&&a>d)||!l&&(o&&Math.abs(f)<1||!o&&-a>f))&&(s=!0),s}(v,t,e,"h"===v?u:s,!0)},[]),l=o.useCallback(function(e){var n=e;if(R.length&&R[R.length-1]===u){var r="deltaY"in n?w(n):g(n),o=t.current.filter(function(e){return e.name===n.type&&(e.target===n.target||n.target===e.shadowParent)&&(t=e.delta,o=r,t[0]===o[0]&&t[1]===o[1]);var t,o})[0];if(o&&o.should)n.cancelable&&n.preventDefault();else if(!o){var c=(i.current.shards||[]).map(C).filter(Boolean).filter(function(e){return e.contains(n.target)});(c.length>0?s(n,c[0]):!i.current.noIsolation)&&n.cancelable&&n.preventDefault()}}},[]),v=o.useCallback(function(e,n,r,o){var c={name:e,delta:n,target:r,should:o,shadowParent:T(r)};t.current.push(c),setTimeout(function(){t.current=t.current.filter(function(e){return e!==c})},1)},[]),m=o.useCallback(function(e){n.current=g(e),c.current=void 0},[]),p=o.useCallback(function(t){v(t.type,w(t),t.target,s(t,e.lockRef.current))},[]),L=o.useCallback(function(t){v(t.type,g(t),t.target,s(t,e.lockRef.current))},[]);o.useEffect(function(){return R.push(u),e.setCallbacks({onScrollCapture:p,onWheelCapture:p,onTouchMoveCapture:L}),document.addEventListener("wheel",l,h),document.addEventListener("touchmove",l,h),document.addEventListener("touchstart",m,h),function(){R=R.filter(function(e){return e!==u}),document.removeEventListener("wheel",l,h),document.removeEventListener("touchmove",l,h),document.removeEventListener("touchstart",m,h)}},[]);var k=e.removeScrollBar,M=e.inert;return o.createElement(o.Fragment,null,M?o.createElement(u,{styles:S(a)}):null,k?o.createElement(d.jp,{noRelative:e.noRelative,gapMode:e.gapMode}):null)});var k=o.forwardRef(function(e,t){return o.createElement(s,(0,r.Cl)({},e,{ref:t,sideCar:L}))});k.classNames=s.classNames;const M=k},4490:(e,t,n)=>{var r;n.d(t,{B:()=>i});var o=n(5043),c=n(503),a=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>{}),u=0;function i(e){const[t,n]=o.useState(a());return(0,c.N)(()=>{e||n(e=>e??String(u++))},[e]),e||(t?`radix-${t}`:"")}},6590:(e,t,n)=>{n.d(t,{Oh:()=>c});var r=n(5043),o=0;function c(){r.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??a()),document.body.insertAdjacentElement("beforeend",e[1]??a()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function a(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}}}]);
//# sourceMappingURL=693.fe43241e.chunk.js.map