"use strict";(self.webpackChunkpayment_gateway_frontend=self.webpackChunkpayment_gateway_frontend||[]).push([[102],{2248:(e,a,t)=>{t.d(a,{J:()=>c});var s=t(5043),r=t(917),n=t(3009),l=t(579);const i=(0,r.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,l.jsx)("label",{ref:a,className:(0,n.cn)(i(),t),...s})});c.displayName="Label"},5722:(e,a,t)=>{t.d(a,{A:()=>s});const s=(0,t(3797).A)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},5923:(e,a,t)=>{t.d(a,{A:()=>s});const s=(0,t(3797).A)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},6742:(e,a,t)=>{t.d(a,{BT:()=>d,Wu:()=>o,ZB:()=>c,Zp:()=>l,aR:()=>i,wL:()=>u});var s=t(5043),r=t(3009),n=t(579);const l=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,n.jsx)("div",{ref:a,className:(0,r.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...s})});l.displayName="Card";const i=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,n.jsx)("div",{ref:a,className:(0,r.cn)("flex flex-col space-y-1.5 p-6",t),...s})});i.displayName="CardHeader";const c=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,n.jsx)("h3",{ref:a,className:(0,r.cn)("text-2xl font-semibold leading-none tracking-tight",t),...s})});c.displayName="CardTitle";const d=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,n.jsx)("p",{ref:a,className:(0,r.cn)("text-sm text-muted-foreground",t),...s})});d.displayName="CardDescription";const o=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,n.jsx)("div",{ref:a,className:(0,r.cn)("p-6 pt-0",t),...s})});o.displayName="CardContent";const u=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,n.jsx)("div",{ref:a,className:(0,r.cn)("flex items-center p-6 pt-0",t),...s})});u.displayName="CardFooter"},8102:(e,a,t)=>{t.r(a),t.d(a,{default:()=>b});var s=t(5043),r=t(3216),n=t(5475),l=t(9066),i=t(9772),c=t(9954),d=t(6742),o=t(2248),u=t(1704),m=t(5722),f=t(5923),p=t(8775),x=t(3797);const h=(0,x.A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),y=(0,x.A)("Store",[["path",{d:"m2 7 4.41-4.41A2 2 0 0 1 7.83 2h8.34a2 2 0 0 1 1.42.59L22 7",key:"ztvudi"}],["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["path",{d:"M15 22v-4a2 2 0 0 0-2-2h-2a2 2 0 0 0-2 2v4",key:"2ebpfo"}],["path",{d:"M2 7h20",key:"1fcdvo"}],["path",{d:"M22 7v3a2 2 0 0 1-2 2v0a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 16 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 12 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 8 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 4 12v0a2 2 0 0 1-2-2V7",key:"jon5kx"}]]),g=(0,x.A)("Briefcase",[["rect",{width:"20",height:"14",x:"2",y:"7",rx:"2",ry:"2",key:"eto64e"}],["path",{d:"M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"zwj3tp"}]]);var v=t(579);const b=()=>{const[e,a]=(0,s.useState)(""),[t,x]=(0,s.useState)(""),[b,j]=(0,s.useState)(!1),[w,N]=(0,s.useState)(!1),[k,A]=(0,s.useState)(null),{login:C,loading:S}=(0,l.A)(),M=(0,r.Zp)(),{toast:R}=(0,u.dj)(),[z,$]=(0,s.useState)(!1);return(0,v.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4",children:(0,v.jsxs)(d.Zp,{className:"w-full max-w-md",children:[(0,v.jsxs)(d.aR,{className:"text-center",children:[(0,v.jsx)("div",{className:"flex justify-center mb-4",children:(0,v.jsx)(m.A,{className:"h-12 w-12 text-blue-600"})}),(0,v.jsx)(d.ZB,{className:"text-2xl font-bold",children:"PayGateway"}),(0,v.jsx)(d.BT,{children:"Sign in to your account"})]}),(0,v.jsxs)("form",{onSubmit:async a=>{a.preventDefault(),$(!0),A(null);try{await C(e,t),R({title:"Login successful",description:`Welcome back, ${e.split("@")[0]}!`});M(`/${"<EMAIL>"===e?"admin":"<EMAIL>"===e?"merchant":"trader"}`)}catch(k){const a=k instanceof Error?k.message:"An unknown error occurred";A(a),R({title:"Login failed",description:a,variant:"destructive"})}finally{$(!1)}},children:[(0,v.jsxs)(d.Wu,{className:"space-y-4",children:[(0,v.jsxs)("div",{className:"space-y-2",children:[(0,v.jsx)(o.J,{htmlFor:"email",children:"Email"}),(0,v.jsx)(c.p,{id:"email",type:"email",placeholder:"Enter your email",value:e,onChange:e=>a(e.target.value),required:!0})]}),(0,v.jsxs)("div",{className:"space-y-2",children:[(0,v.jsx)(o.J,{htmlFor:"password",children:"Password"}),(0,v.jsxs)("div",{className:"relative",children:[(0,v.jsx)(c.p,{id:"password",type:b?"text":"password",placeholder:"Enter your password",value:t,onChange:e=>x(e.target.value),required:!0}),(0,v.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>j(!b),children:b?(0,v.jsx)(f.A,{className:"h-4 w-4"}):(0,v.jsx)(p.A,{className:"h-4 w-4"})})]})]})]}),(0,v.jsxs)(d.wL,{className:"flex flex-col space-y-4",children:[(0,v.jsx)(i.$,{type:"submit",className:"w-full",disabled:z||S,children:z||S?"Signing in...":"Sign In"}),(0,v.jsx)("div",{className:"w-full border-t border-gray-200 my-4"}),(0,v.jsxs)("div",{className:"w-full space-y-2",children:[(0,v.jsx)("p",{className:"text-sm text-center text-gray-600 mb-2",children:"Or sign in with a demo account:"}),(0,v.jsxs)(i.$,{type:"button",variant:"outline",className:"w-full flex items-center justify-start gap-2",onClick:()=>{a("<EMAIL>"),x("password123"),A(null)},disabled:z||S,children:[(0,v.jsx)(h,{className:"h-4 w-4 text-blue-600"}),"Admin Account"]}),(0,v.jsxs)(i.$,{type:"button",variant:"outline",className:"w-full flex items-center justify-start gap-2",onClick:()=>{a("<EMAIL>"),x("password123"),A(null)},disabled:z||S,children:[(0,v.jsx)(y,{className:"h-4 w-4 text-green-600"}),"Merchant Account"]}),(0,v.jsxs)(i.$,{type:"button",variant:"outline",className:"w-full flex items-center justify-start gap-2",onClick:()=>{a("<EMAIL>"),x("password123"),A(null)},disabled:z||S,children:[(0,v.jsx)(g,{className:"h-4 w-4 text-purple-600"}),"Trader Account"]})]}),(0,v.jsxs)("p",{className:"text-sm text-center text-gray-600 mt-4",children:["Don't have an account? ",(0,v.jsx)(n.N_,{to:"/register",className:"text-blue-600 hover:underline",children:"Sign up"})]})]})]})]})})}},8775:(e,a,t)=>{t.d(a,{A:()=>s});const s=(0,t(3797).A)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},9772:(e,a,t)=>{t.d(a,{$:()=>d});var s=t(5043),r=t(6851),n=t(917),l=t(3009),i=t(579);const c=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=s.forwardRef((e,a)=>{let{className:t,variant:s,size:n,asChild:d=!1,...o}=e;const u=d?r.DX:"button";return(0,i.jsx)(u,{className:(0,l.cn)(c({variant:s,size:n,className:t})),ref:a,...o})});d.displayName="Button"},9954:(e,a,t)=>{t.d(a,{p:()=>l});var s=t(5043),r=t(3009),n=t(579);const l=s.forwardRef((e,a)=>{let{className:t,type:s,...l}=e;return(0,n.jsx)("input",{type:s,className:(0,r.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:a,...l})});l.displayName="Input"}}]);
//# sourceMappingURL=102.048c77ed.chunk.js.map