# Test Environment Variables
NODE_ENV=test
MONGODB_URI=mongodb://localhost:27017/payment_gateway_test
COOKIE_SECRET=test_cookie_secret_123
FRONTEND_URL=http://localhost:3000
API_VERSION=v1

# Session Configuration
SESSION_SECRET=test_session_secret_123
SESSION_COOKIE_NAME=connect.sid
SESSION_COOKIE_SECURE=false
SESSION_COOKIE_HTTPONLY=true
SESSION_COOKIE_SAMESITE=strict

# Email Configuration (using ethereal.email for testing)
SMTP_HOST=smtp.ethereal.email
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=testpassword
EMAIL_FROM=<EMAIL>

# Security
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=error
