import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { format } from 'date-fns';
// Import enums from the service to ensure type consistency
import { AssignmentType, AssignmentStatus } from '../../services/traderService';

// Re-export enums with different names to avoid conflicts
type AssignmentStatusType = AssignmentStatus;
type AssignmentTypeType = AssignmentType;
import { Plus, Search, Filter, Edit, Trash2, User, Briefcase, Calendar as CalendarIcon, Clock, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';
import type { AxiosError } from 'axios';
// UI Components
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Badge } from '../../components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '../../components/ui/dialog';
import { Label } from '../../components/ui/label';
import { Textarea } from '../../components/ui/textarea';
import { Checkbox } from '../../components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';

// Hooks
import { useToast } from '../../components/ui/use-toast';

// Import types and services from traderService
import { 
  ApiResponse, 
  Assignment, 
  Trader, 
  Merchant, 
  PaginatedResponse,
  AssignmentStatus as OriginalAssignmentStatus,
  AssignmentType as OriginalAssignmentType,
  TraderStatus,
  traderAdminService,
  CreateAssignmentData,
  traderService
} from '../../services/traderService';

// Use the imported enums directly
const { TEMPORARY, PERMANENT, PROJECT_BASED } = OriginalAssignmentType;
const { PENDING, ACTIVE, INACTIVE, COMPLETED } = OriginalAssignmentStatus;
import { merchantAdminService } from '../../services/merchantService';
import { getAssignments } from '../../services/traderAssignmentService';

// Extend the imported types with any additional properties we need
type ExtendedTrader = Trader & {
  phone?: string;
  status?: TraderStatus;
  createdAt?: string;
  updatedAt?: string;
};

type ExtendedMerchant = Merchant & {
  businessType?: string;
  phone?: string;
  status?: 'active' | 'inactive' | 'suspended';
  createdAt?: string;
  updatedAt?: string;
};

interface ExtendedAssignment extends Omit<Assignment, 'trader' | 'merchant' | 'collectionTarget'> {
  _id: string;
  trader: string | (Pick<Trader, '_id' | 'name' | 'email'>);
  merchant: string | (Pick<Merchant, '_id' | 'businessName'>);
  traderName?: string;
  merchantName?: string;
  collectionTarget: {
    amount: number;
    currency: string;
    period: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  };
  permissions: string[];
  notes?: string;
  startDate: string;
  endDate?: string;
  status: AssignmentStatus;
  assignmentType: AssignmentType;
  createdAt: string;
  updatedAt: string;
}

type CollectionPeriod = 'daily' | 'weekly' | 'monthly' | 'quarterly';

interface FormData {
  traderId: string;
  merchantId: string;
  assignmentType: AssignmentType;
  startDate: string;
  endDate?: string;
  status: AssignmentStatus;
  collectionTarget: {
    amount: number;
    currency: string;
    period: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  };
  permissions: string[];
  notes?: string;
  traderName?: string;
  merchantName?: string;
}

// Helper function to safely access data from API responses
const getData = <T,>(response: ApiResponse<T[]> | PaginatedResponse<T> | T[] | undefined): T[] => {
  if (!response) return [];
  if (Array.isArray(response)) return response;
  if ('data' in response) return Array.isArray(response.data) ? response.data : [response.data];
  return [];
};

// Skeleton component for loading states
const Skeleton = ({ className = '' }: { className?: string }) => (
  <div className={`animate-pulse bg-gray-200 rounded ${className}`} />
);

// Helper function to render status badges
const renderStatusBadge = (status: string) => {
  switch (status) {
    case 'active':
      return <Badge className="bg-green-100 text-green-800">Active</Badge>;
    case 'pending':
      return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
    case 'completed':
      return <Badge className="bg-blue-100 text-blue-800">Completed</Badge>;
    case 'inactive':
      return <Badge variant="outline">Inactive</Badge>;
    default:
      return <Badge variant="outline">{status}</Badge>;
  }
};

const TraderAssignments = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedAssignment, setSelectedAssignment] = useState<ExtendedAssignment | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | AssignmentStatus>('all');
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [pageInput, setPageInput] = useState('');
  
  // Form state
  const [formData, setFormData] = useState<FormData>({
    traderId: '',
    merchantId: '',
    assignmentType: AssignmentType.TEMPORARY,
    startDate: new Date().toISOString().split('T')[0],
    endDate: '',
    status: AssignmentStatus.PENDING,
    permissions: [],
    notes: '',
    traderName: '',
    merchantName: '',
    collectionTarget: {
      amount: 0,
      currency: 'USD',
      period: 'monthly'
    }
  });

  // Fetch data
  const { 
    data: assignmentsResponse, 
    isLoading: isLoadingAssignments,
    error: assignmentsError,
    refetch: refetchAssignments 
  } = useQuery<PaginatedResponse<ExtendedAssignment>, AxiosError>({
    queryKey: ['assignments', { page, limit, status: statusFilter, search: searchTerm }],
    queryFn: () => traderService.getAllAssignments({
      status: statusFilter === 'all' 
        ? [OriginalAssignmentStatus.ACTIVE, OriginalAssignmentStatus.PENDING, OriginalAssignmentStatus.COMPLETED, OriginalAssignmentStatus.INACTIVE]
        : [statusFilter as AssignmentStatus],
      page,
      limit,
      search: searchTerm
    }),
  });

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
    setPageInput('');
  };

  // Handle page size change
  const handleLimitChange = (newLimit: number) => {
    setLimit(newLimit);
    setPage(1); // Reset to first page when changing page size
  };

  // Handle direct page input
  const handlePageInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value === '' || /^\d+$/.test(value)) {
      setPageInput(value);
    }
  };

  // Handle page input submission
  const handlePageSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (pageInput) {
      const newPage = Math.max(1, Math.min(Number(pageInput), assignmentsResponse?.meta?.totalPages || 1));
      handlePageChange(newPage);
    }
  };

  const { data: tradersResponse, isLoading: isLoadingTraders, error: tradersError } = useQuery<ExtendedTrader[], AxiosError>({
    queryKey: ['traders'],
    queryFn: async () => {
      try {
        const response = await traderAdminService.getTraders({
          status: TraderStatus.ACTIVE,
          page: 1,
          limit: 100
        });
        return response?.data || [];
      } catch (error) {
        console.error('Error fetching traders:', error);
        toast({
          title: 'Error',
          description: 'Failed to load traders. Please try again.',
          variant: 'destructive',
        });
        return [];
      }
    },
  });

  const { data: merchantsResponse, isLoading: isLoadingMerchants, error: merchantsError } = useQuery<ExtendedMerchant[], AxiosError>({
    queryKey: ['merchants'],
    queryFn: async () => {
      try {
        const response = await merchantAdminService.getAllMerchants({ 
          page: 1, 
          limit: 100,
          status: 'active' as const // Only fetch active merchants
        });
        return response?.data || [];
      } catch (error) {
        console.error('Error fetching merchants:', error);
        toast({
          title: 'Error',
          description: 'Failed to load merchants. Please try again.',
          variant: 'destructive',
        });
        return [];
      }
    },
  });
  
  // Memoize the data with proper typing
  const merchants = useMemo<ExtendedMerchant[]>(() => {
    return Array.isArray(merchantsResponse) ? merchantsResponse : [];
  }, [merchantsResponse]);

  const assignments = useMemo<ExtendedAssignment[]>(() => {
    return Array.isArray(assignmentsResponse?.data) ? assignmentsResponse.data : [];
  }, [assignmentsResponse]);

  const traders = useMemo<ExtendedTrader[]>(() => {
    return Array.isArray(tradersResponse) ? tradersResponse : [];
  }, [tradersResponse]);
  
  const isLoading = isLoadingAssignments || isLoadingTraders || isLoadingMerchants;

  // Memoized filtered assignments
  const filteredAssignmentsList = useMemo(() => {
    if (!assignments || !traders || !merchants) return [];
    
    return (assignments as ExtendedAssignment[]).filter((assignment) => {
      const traderName = typeof assignment.trader === 'string' 
        ? traders.find((t: ExtendedTrader) => t._id === assignment.trader)?.name || ''
        : (assignment.trader as Trader)?.name || '';
      
      const merchantName = typeof assignment.merchant === 'string'
        ? merchants.find((m: ExtendedMerchant) => m._id === assignment.merchant)?.businessName || ''
        : (assignment.merchant as Merchant)?.businessName || '';
      
      const matchesSearch = searchTerm 
        ? traderName.toLowerCase().includes(searchTerm.toLowerCase()) ||
          merchantName.toLowerCase().includes(searchTerm.toLowerCase())
        : true;
      
      const matchesStatus = statusFilter !== 'all' 
        ? assignment.status === statusFilter 
        : true;
      
      return matchesSearch && matchesStatus;
    });
  }, [assignments, searchTerm, statusFilter, traders, merchants]);

  // Handle status change
  const handleStatusChange = (value: AssignmentStatus) => {
    setFormData(prev => ({
      ...prev,
      status: value
    }));
  };

  // Populate form when editing an assignment
  useEffect(() => {
    if (selectedAssignment) {
      // Helper function to get ID from either string or object
      const getId = (item: string | { _id: string } | undefined): string => {
        if (!item) return '';
        return typeof item === 'string' ? item : item._id;
      };
      
      // Helper function to get name from either string or object
      const getName = (item: string | { name?: string; businessName?: string } | undefined): string => {
        if (!item) return '';
        if (typeof item === 'string') return item;
        return 'name' in item ? item.name || '' : item.businessName || '';
      };
      
      const traderId = getId(selectedAssignment.trader);
      const merchantId = getId(selectedAssignment.merchant);
      const traderName = getName(selectedAssignment.trader);
      const merchantName = getName(selectedAssignment.merchant);
      
      setFormData({
        traderId,
        merchantId,
        assignmentType: selectedAssignment.assignmentType,
        startDate: selectedAssignment.startDate,
        endDate: selectedAssignment.endDate || '',
        status: selectedAssignment.status,
        collectionTarget: selectedAssignment.collectionTarget ? {
          amount: selectedAssignment.collectionTarget.amount,
          currency: selectedAssignment.collectionTarget.currency || 'USD',
          period: selectedAssignment.collectionTarget.period
        } : {
          amount: 0,
          currency: 'USD',
          period: 'monthly' as const
        },
        permissions: Array.isArray(selectedAssignment.permissions) 
          ? [...selectedAssignment.permissions] 
          : [],
        notes: selectedAssignment.notes || '',
        traderName,
        merchantName
      });
    } else {
      setFormData({
        traderId: '',
        merchantId: '',
        assignmentType: AssignmentType.TEMPORARY,
        startDate: new Date().toISOString().split('T')[0],
        status: AssignmentStatus.PENDING,
        permissions: [],
        notes: '',
        traderName: '',
        merchantName: '',
        collectionTarget: {
          amount: 0,
          currency: 'USD',
          period: 'monthly' as const
        }
      });
    }
  }, [selectedAssignment]);

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  // Handle permission changes
  const handlePermissionChange = (permission: string, isChecked: boolean) => {
    setFormData(prev => {
      const permissions = new Set(prev.permissions || []);
      if (isChecked) {
        permissions.add(permission);
      } else {
        permissions.delete(permission);
      }
      return { 
        ...prev, 
        permissions: Array.from(permissions) 
      };
    });
  };

  // Handle nested input changes for collection target
  const handleNestedInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    const [parent, child] = name.split('.');
    
    setFormData(prev => ({
      ...prev,
      [parent]: {
        ...(prev as any)[parent],
        [child]: name.includes('amount') ? Number(value) : value
      }
    }));
  };
  
  // Handle form submission
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Basic form validation
    if (!formData.traderId || !formData.merchantId) {
      toast({
        title: 'Error',
        description: 'Please select both trader and merchant',
        variant: 'destructive',
      });
      return;
    }
    
    // Validate dates
    const startDate = new Date(formData.startDate);
    const endDate = formData.endDate ? new Date(formData.endDate) : null;
    
    if (endDate && endDate < startDate) {
      toast({
        title: 'Error',
        description: 'End date cannot be before start date',
        variant: 'destructive',
      });
      return;
    }
    
    // Validate collection target
    if (formData.collectionTarget.amount <= 0) {
      toast({
        title: 'Error',
        description: 'Collection target amount must be greater than 0',
        variant: 'destructive',
      });
      return;
    }
    
    try {
      const basePayload: CreateAssignmentData = {
        traderId: formData.traderId,
        merchantId: formData.merchantId,
        assignmentType: formData.assignmentType,
        startDate: formData.startDate,
        endDate: formData.endDate || undefined,
        status: formData.status,
        collectionTarget: formData.collectionTarget,
        permissions: formData.permissions,
        notes: formData.notes || ''
      };
      
      if (selectedAssignment) {
        // For updates, only send the fields that can be updated
        const updatePayload = {
          status: formData.status,
          notes: formData.notes || '',
          metadata: {
            updatedAt: new Date().toISOString()
          }
        };
        
        await traderService.updateAssignment(selectedAssignment._id, updatePayload);
        toast({
          title: 'Success',
          description: 'Assignment updated successfully',
        });
      } else {
        // For new assignments, use the full payload
        await traderService.createAssignment(basePayload);
        toast({
          title: 'Success',
          description: 'Assignment created successfully',
        });
      }
      
      setIsDialogOpen(false);
      resetForm();
      await queryClient.invalidateQueries({ queryKey: ['assignments'] });
    } catch (error) {
      console.error('Error saving assignment:', error);
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
      toast({
        title: 'Error',
        description: `Failed to save assignment: ${errorMessage}`,
        variant: 'destructive',
      });
    }
  }, [formData, selectedAssignment, queryClient, resetForm]);
  };
  
  // Handle edit assignment
  const handleEditAssignment = (assignment: ExtendedAssignment) => {
    setSelectedAssignment(assignment);
    setIsDialogOpen(true);
  };
  
  // Reset form to initial state
  const resetForm = () => {
    setFormData({
      traderId: '',
      merchantId: '',
      assignmentType: AssignmentType.TEMPORARY,
      startDate: new Date().toISOString().split('T')[0],
      endDate: '',
      status: AssignmentStatus.PENDING,
      permissions: [],
      notes: '',
      traderName: '',
      merchantName: '',
      collectionTarget: {
        amount: 0,
        currency: 'USD',
        period: 'monthly' as const
      }
    });
    setSelectedAssignment(null);
  };
  
  // Handle new assignment button click
  const handleNewAssignment = () => {
    resetForm();
    setIsDialogOpen(true);
  };
  
  // Filter assignments based on search and status
  const assignmentsToDisplay = useMemo(() => {
    if (!filteredAssignmentsList) return [];
    
    return filteredAssignmentsList.filter(assignment => {
      const matchesSearch = searchTerm 
        ? (assignment.traderName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
           assignment.merchantName?.toLowerCase().includes(searchTerm.toLowerCase()))
        : true;
      
      const matchesStatus = statusFilter !== 'all' 
        ? assignment.status === statusFilter 
        : true;
      
      return matchesSearch && matchesStatus;
    });
  }, [filteredAssignmentsList, searchTerm, statusFilter]);

  // Handle assignment deletion
  const handleDeleteAssignment = useCallback(async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this assignment?')) {
      return;
    }

    try {
      await traderService.deleteAssignment(id);
      toast({
        title: 'Success',
        description: 'Assignment deleted successfully',
      });
      await queryClient.invalidateQueries({ queryKey: ['assignments'] });
    } catch (error) {
      console.error('Error deleting assignment:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete assignment. Please try again.',
        variant: 'destructive',
      });
    }
  }, [queryClient]);
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">Trader Assignments</h1>
          <Button onClick={handleNewAssignment}>
            <Plus className="mr-2 h-4 w-4" /> New Assignment
          </Button>
        </div>

        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search assignments..."
              className="w-full pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <Select
            value={statusFilter}
            onValueChange={(value: 'all' | AssignmentStatus) => setStatusFilter(value)}
          >
            <SelectTrigger className="w-[180px]">
              <Filter className="mr-2 h-4 w-4" />
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              {Object.values(AssignmentStatus).map((status) => (
                <SelectItem key={status} value={status}>
                  {status.charAt(0).toUpperCase() + status.slice(1)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Assignments</CardTitle>
            <CardDescription>Manage trader assignments to merchants</CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Trader</TableHead>
                  <TableHead>Merchant</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Start Date</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {assignmentsToDisplay.length > 0 ? (
                  assignmentsToDisplay.map((assignment) => {
                    const trader = typeof assignment.trader === 'string' 
                      ? traders.find(t => t._id === assignment.trader)
                      : assignment.trader;
                    
                    const merchant = typeof assignment.merchant === 'string'
                      ? merchants?.find((m: any) => m._id === assignment.merchant)
                      : assignment.merchant;
                    
                    return (
                      <TableRow key={assignment._id}>
                        <TableCell className="font-medium">
                          {trader?.name || 'Unknown Trader'}
                        </TableCell>
                        <TableCell>{merchant?.businessName || 'Unknown Merchant'}</TableCell>
                        <TableCell className="capitalize">{assignment.assignmentType.replace('_', ' ')}</TableCell>
                        <TableCell>{format(new Date(assignment.startDate), 'MMM d, yyyy')}</TableCell>
                        <TableCell>{renderStatusBadge(assignment.status)}</TableCell>
                        <TableCell className="text-right space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditAssignment(assignment as ExtendedAssignment)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteAssignment(assignment._id)}
                          >
                            <Trash2 className="h-4 w-4 text-destructive" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    );
                  })
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                      No assignments found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
            
            {/* Pagination Controls */}
            <div className="flex flex-col sm:flex-row items-center justify-between p-4 border-t gap-4">
              <div className="text-sm text-muted-foreground">
                Showing <span className="font-medium">
                  {assignmentsResponse?.data?.length || 0}
                </span> of{' '}
                <span className="font-medium">
                  {assignmentsResponse?.meta?.total || 0}
                </span> assignments
              </div>
              
              <div className="flex items-center space-x-2">
                <div className="flex items-center space-x-1">
                  <span className="text-sm text-muted-foreground">Rows per page:</span>
                  <Select
                    value={limit.toString()}
                    onValueChange={(value) => handleLimitChange(Number(value))}
                  >
                    <SelectTrigger className="h-8 w-[70px]">
                      <SelectValue placeholder={limit} />
                    </SelectTrigger>
                    <SelectContent>
                      {[5, 10, 20, 50].map((size) => (
                        <SelectItem key={size} value={size.toString()}>
                          {size}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="flex items-center space-x-1">
                  <Button
                    variant="outline"
                    className="h-8 w-8 p-0"
                    onClick={() => handlePageChange(1)}
                    disabled={page === 1 || isLoadingAssignments}
                  >
                    <ChevronsLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    className="h-8 w-8 p-0"
                    onClick={() => handlePageChange(Math.max(1, page - 1))}
                    disabled={page === 1 || isLoadingAssignments}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <div className="flex items-center space-x-1">
                    <span className="px-2 text-sm">
                      Page {page} of {assignmentsResponse?.meta?.totalPages || 1}
                    </span>
                    <form onSubmit={handlePageSubmit} className="flex items-center space-x-1">
                      <Input
                        type="text"
                        className="h-8 w-12 text-center"
                        value={pageInput}
                        onChange={handlePageInput}
                        placeholder={page.toString()}
                        disabled={isLoadingAssignments}
                      />
                      <Button 
                        type="submit" 
                        variant="ghost" 
                        size="sm"
                        className="h-8 px-2"
                        disabled={!pageInput || isLoadingAssignments}
                      >
                        Go
                      </Button>
                    </form>
                  </div>
                  <Button
                    variant="outline"
                    className="h-8 w-8 p-0"
                    onClick={() => handlePageChange(Math.min((assignmentsResponse?.meta?.totalPages || 1), page + 1))}
                    disabled={page >= (assignmentsResponse?.meta?.totalPages || 1) || isLoadingAssignments}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    className="h-8 w-8 p-0"
                    onClick={() => handlePageChange(assignmentsResponse?.meta?.totalPages || 1)}
                    disabled={page === (assignmentsResponse?.meta?.totalPages || 1) || isLoadingAssignments}
                  >
                    <ChevronsRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>
              {selectedAssignment ? 'Edit Assignment' : 'New Assignment'}
            </DialogTitle>
          </DialogHeader>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="traderId">Trader</Label>
                <Select
                  name="traderId"
                  value={formData.traderId}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, traderId: value }))}
                  required
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a trader" />
                  </SelectTrigger>
                  <SelectContent>
                    {traders.map((trader) => (
                      <SelectItem key={trader._id} value={trader._id}>
                        {trader.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="merchantId">Merchant</Label>
                <Select
                  name="merchantId"
                  value={formData.merchantId}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, merchantId: value }))}
                  required
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a merchant" />
                  </SelectTrigger>
                  <SelectContent>
                    {merchants && merchants.map((merchant: Merchant) => (
                      <SelectItem key={merchant._id} value={merchant._id}>
                        {merchant.businessName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="assignmentType">Assignment Type</Label>
                <Select
                  name="assignmentType"
                  value={formData.assignmentType}
                  onValueChange={(value: AssignmentType) => 
                    setFormData(prev => ({ ...prev, assignmentType: value }))
                  }
                  required
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="permanent">Permanent</SelectItem>
                    <SelectItem value="temporary">Temporary</SelectItem>
                    <SelectItem value="project_based">Project Based</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select
                  name="status"
                  value={formData.status}
                  onValueChange={(value: string) => 
                    setFormData(prev => ({
                      ...prev,
                      status: value as AssignmentStatus
                    }))
                  }
                  required
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={AssignmentStatus.ACTIVE}>Active</SelectItem>
                    <SelectItem value={AssignmentStatus.PENDING}>Pending</SelectItem>
                    <SelectItem value={AssignmentStatus.COMPLETED}>Completed</SelectItem>
                    <SelectItem value={AssignmentStatus.INACTIVE}>Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Permissions</Label>
              <div className="grid grid-cols-2 gap-2">
                {['view_reports', 'process_payments', 'manage_inventory', 'view_analytics'].map((permission) => (
                  <div key={permission} className="flex items-center space-x-2">
                    <Checkbox
                      id={`${permission}_${Date.now()}`}
                      checked={formData.permissions.includes(permission)}
                      onCheckedChange={(checked) => 
                        handlePermissionChange(permission, checked === true)
                      }
                    />
                    <Label htmlFor={`${permission}_${Date.now()}`} className="text-sm font-normal">
                      {permission.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="startDate">Start Date</Label>
                <Input
                  type="date"
                  name="startDate"
                  value={formData.startDate}
                  onChange={handleInputChange}
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="endDate">End Date (Optional)</Label>
                <Input
                  type="date"
                  name="endDate"
                  value={formData.endDate || ''}
                  onChange={handleInputChange}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Collection Target</Label>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Input
                    type="number"
                    name="collectionTarget.amount"
                    placeholder="Amount"
                    value={formData.collectionTarget?.amount || ''}
                    onChange={handleNestedInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Select
                    name="collectionTarget.period"
                    value={formData.collectionTarget?.period || ''}
                    onValueChange={(value: 'daily' | 'weekly' | 'monthly' | 'quarterly') =>
                      setFormData(prev => ({
                        ...prev,
                        collectionTarget: {
                          ...prev.collectionTarget,
                          period: value,
                          amount: prev.collectionTarget?.amount || 0
                        }
                      }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select period" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="daily">Daily</SelectItem>
                      <SelectItem value="weekly">Weekly</SelectItem>
                      <SelectItem value="monthly">Monthly</SelectItem>
                      <SelectItem value="quarterly">Quarterly</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                name="notes"
                value={formData.notes || ''}
                onChange={handleInputChange}
                placeholder="Additional notes about this assignment"
                rows={3}
              />
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button type="submit">
                {selectedAssignment ? 'Update Assignment' : 'Create Assignment'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default TraderAssignments;
