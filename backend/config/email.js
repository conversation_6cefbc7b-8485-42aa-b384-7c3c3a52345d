const nodemailer = require('nodemailer');
const logger = require('../utils/logger');

let transporter;

if (process.env.NODE_ENV === 'production') {
  // Production configuration - use real SMTP
  transporter = nodemailer.createTransport({
    host: process.env.EMAIL_HOST,
    port: process.env.EMAIL_PORT,
    secure: process.env.EMAIL_SECURE === 'true',
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS,
    },
  });

  // Verify connection configuration in production
  transporter.verify((error) => {
    if (error) {
      logger.error('Error connecting to email server:', error);
    } else {
      logger.info('Email server connection established');
    }
  });

  // Handle connection errors in production
  transporter.on('error', (error) => {
    logger.error('SMTP connection error:', error);
    
    // Attempt to reconnect after 30 seconds
    setTimeout(() => {
      logger.info('Attempting to reconnect to email server...');
      transporter.verify();
    }, 30000);
  });
} else {
  // Development configuration - use a mock transport that logs emails
  transporter = {
    sendMail: async (mailOptions) => {
      logger.info('=== EMAIL SENDING MOCKED IN DEVELOPMENT ===', {
        to: mailOptions.to,
        subject: mailOptions.subject,
        from: mailOptions.from,
        html: mailOptions.html ? 'HTML content available' : 'No HTML content',
      });
      
      // Return a mock message ID for testing
      return { 
        messageId: 'test-message-id',
        previewUrl: 'https://ethereal.email/message/test-message-id',
        envelope: {
          from: mailOptions.from,
          to: [mailOptions.to].flat()
        },
        messageSize: 1024,
        response: '250 Message accepted for delivery',
        accepted: [mailOptions.to].flat(),
        rejected: [],
        pending: [],
        rejectedErrors: []
      };
    },
    
    // Add verify method to match the real transporter API
    verify: (callback) => {
      callback(null, true);
    }
  };
  
  logger.info('Using mock email transport in development mode');
}

/**
 * Send an email
 * @param {Object} options - Email options
 * @param {string|string[]} options.to - Recipient email address(es)
 * @param {string} options.subject - Email subject
 * @param {string} options.html - HTML content of the email
 * @param {string} [options.text] - Plain text version of the email
 * @returns {Promise<Object>} - Info about the sent email
 */
const sendEmail = async ({ to, subject, html, text }) => {
  try {
    const mailOptions = {
      from: `"PayGateway" <${process.env.FROM_EMAIL || emailConfig.auth.user}>`,
      to: Array.isArray(to) ? to.join(', ') : to,
      subject,
      html,
      text: text || html.replace(/<[^>]*>?/gm, ''), // Auto-generate text version if not provided
      // Add DKIM signing if configured
      dkim: process.env.DKIM_DOMAIN ? {
        domainName: process.env.DKIM_DOMAIN,
        keySelector: process.env.DKIM_SELECTOR || 'default',
        privateKey: process.env.DKIM_PRIVATE_KEY,
      } : undefined,
    };

    const info = await transporter.sendMail(mailOptions);
    logger.info(`Email sent to ${to} (${info.messageId})`);
    return info;
  } catch (error) {
    logger.error(`Error sending email to ${to}:`, error);
    throw new Error(`Failed to send email: ${error.message}`);
  }
};

/**
 * Send verification email to new user
 * @param {string} email - User's email address
 * @param {string} token - Verification token
 * @returns {Promise<Object>} - Info about the sent email
 */
const sendVerificationEmail = async (email, token) => {
  const verificationUrl = `${process.env.FRONTEND_URL}/verify-email?token=${token}`;
  const subject = 'Verify Your Email - PayGateway';
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h1 style="color: #4a6cf7;">Welcome to PayGateway!</h1>
      <p>Thank you for registering. Please verify your email address to get started.</p>
      <div style="text-align: center; margin: 30px 0;">
        <a href="${verificationUrl}" 
           style="background-color: #4a6cf7; color: white; padding: 12px 24px; 
                  text-decoration: none; border-radius: 4px; font-weight: bold;">
          Verify Email Address
        </a>
      </div>
      <p>Or copy and paste this link into your browser:</p>
      <p style="word-break: break-all;">${verificationUrl}</p>
      <p>This link will expire in 24 hours.</p>
      <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 24px 0;">
      <p style="color: #6b7280; font-size: 14px;">
        If you didn't create an account, you can safely ignore this email.
      </p>
    </div>
  `;

  return sendEmail({ to: email, subject, html });
};

/**
 * Send password reset email
 * @param {string} email - User's email address
 * @param {string} token - Password reset token
 * @returns {Promise<Object>} - Info about the sent email
 */
const sendPasswordResetEmail = async (email, token) => {
  const resetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${token}`;
  const subject = 'Password Reset Request - PayGateway';
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h1 style="color: #4a6cf7;">Password Reset Request</h1>
      <p>We received a request to reset your password. Click the button below to set a new password:</p>
      <div style="text-align: center; margin: 30px 0;">
        <a href="${resetUrl}" 
           style="background-color: #4a6cf7; color: white; padding: 12px 24px; 
                  text-decoration: none; border-radius: 4px; font-weight: bold;">
          Reset Password
        </a>
      </div>
      <p>Or copy and paste this link into your browser:</p>
      <p style="word-break: break-all;">${resetUrl}</p>
      <p>This link will expire in 1 hour.</p>
      <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 24px 0;">
      <p style="color: #6b7280; font-size: 14px;">
        If you didn't request a password reset, you can safely ignore this email.
        Your password will remain unchanged.
      </p>
    </div>
  `;

  return sendEmail({ to: email, subject, html });
};

/**
 * Send password changed confirmation email
 * @param {string} email - User's email address
 * @returns {Promise<Object>} - Info about the sent email
 */
const sendPasswordChangedEmail = async (email) => {
  const subject = 'Your Password Has Been Changed - PayGateway';
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h1 style="color: #4a6cf7;">Password Changed</h1>
      <p>Your password has been successfully changed.</p>
      <p>If you didn't make this change, please contact our support team immediately.</p>
      <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 24px 0;">
      <p style="color: #6b7280; font-size: 14px;">
        This is an automated message, please do not reply to this email.
      </p>
    </div>
  `;

  return sendEmail({ to: email, subject, html });
};

module.exports = {
  transporter,
  sendEmail,
  sendVerificationEmail,
  sendPasswordResetEmail,
  sendPasswordChangedEmail,
};
