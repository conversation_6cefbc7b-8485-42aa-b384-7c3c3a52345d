const express = require('express');
const { auth } = require('../middlewares/auth');
const { menuController } = require('../controllers');
const { role } = require('../config/roles');
const validate = require('../middlewares/validate');
const { menuValidation } = require('../validations');

const router = express.Router();

// Public route to get menu items by role
router.get('/:role', validate(menuValidation.getMenuByRole), menuController.getMenuByRole);

// Protected routes (admin only)
router.use(auth(role.ADMIN));

router
  .route('/')
  .get(menuController.getAllMenuItems)
  .post(validate(menuValidation.createMenuItem), menuController.createMenuItem);

router
  .route('/:menuId')
  .get(validate(menuValidation.getMenuItem), menuController.getMenuItem)
  .patch(validate(menuValidation.updateMenuItem), menuController.updateMenuItem)
  .delete(validate(menuValidation.deleteMenuItem), menuController.deleteMenuItem);

module.exports = router;
