# Payment Gateway Testing Environment

This directory contains the testing environment for the Payment Gateway API. It includes a mock server and test scripts to verify the functionality of all endpoints.

## Prerequisites

- Node.js (v14 or higher)
- npm (v6 or higher)

## Setup

1. Navigate to the test directory:
   ```bash
   cd test
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

## Running the Mock Server

To start the mock server:

```bash
npm run start-mock
```

The server will start on `http://localhost:3001` by default.

## Running Tests

To run the test suite against the mock server:

```bash
npm test
```

For CI environments:

```bash
npm run test:ci
```

## Test Accounts

The following test accounts are pre-configured:

### Admin
- **Email:** <EMAIL>
- **Password:** admin123

### Merchants
- **Email:** merchant1@example.<NAME_EMAIL>
- **Password:** merchant1 to merchant5

### Traders
- **Email:** trader1@example.<NAME_EMAIL>
- **Password:** trader1 to trader5

## API Endpoints

The following endpoints are available for testing:

### Authentication
- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Authenticate a user

### Merchants
- `GET /api/merchants` - Get all merchants (admin only)
- `GET /api/merchants/:id` - Get a specific merchant

### Traders
- `GET /api/traders` - Get all traders (admin only)
- `GET /api/traders/:id` - Get a specific trader

### Transactions
- `GET /api/transactions` - Get transactions (filtered by user role)

### Settlements
- `GET /api/settlements` - Get settlements (filtered by user role)

### Reserve Strategies
- `GET /api/reserve-strategies` - Get all reserve strategies (admin only)
- `POST /api/reserve-strategies` - Create a new reserve strategy (admin only)

### Reserve Activities
- `GET /api/reserve-activities` - Get reserve activities (admin only)

## Test Data

The mock server initializes with sample data including:

- 1 admin user
- 5 merchant accounts
- 5 trader accounts
- 50 sample transactions
- 20 sample settlements
- 1 sample reserve strategy
- 30 sample reserve activities

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
