"use strict";(self.webpackChunkpayment_gateway_frontend=self.webpackChunkpayment_gateway_frontend||[]).push([[790],{382:(e,t,a)=>{a.d(t,{A0:()=>i,BF:()=>d,Hj:()=>c,XI:()=>o,nA:()=>u,nd:()=>l});var r=a(5043),s=a(3009),n=a(579);const o=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)("div",{className:"w-full overflow-auto",children:(0,n.jsx)("table",{ref:t,className:(0,s.cn)("w-full caption-bottom text-sm",a),...r})})});o.displayName="Table";const i=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)("thead",{ref:t,className:(0,s.cn)("[&_tr]:border-b",a),...r})});i.displayName="TableHeader";const d=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)("tbody",{ref:t,className:(0,s.cn)("[&_tr:last-child]:border-0",a),...r})});d.displayName="TableBody";r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)("tfoot",{ref:t,className:(0,s.cn)("bg-primary font-medium text-primary-foreground",a),...r})}).displayName="TableFooter";const c=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)("tr",{ref:t,className:(0,s.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",a),...r})});c.displayName="TableRow";const l=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)("th",{ref:t,className:(0,s.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",a),...r})});l.displayName="TableHead";const u=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)("td",{ref:t,className:(0,s.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",a),...r})});u.displayName="TableCell";r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)("caption",{ref:t,className:(0,s.cn)("mt-4 text-sm text-muted-foreground",a),...r})}).displayName="TableCaption"},519:(e,t,a)=>{a.d(t,{Xd:()=>N,yk:()=>y,nC:()=>b,xG:()=>w});var r=a(5604),s=(a(9781),a(2836),a(6213));const n="auth_token",o=()=>"undefined"===typeof window?null:localStorage.getItem(n);var i=a(6879);const d={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"http://localhost:5001/api",REACT_APP_APP_NAME:"PayGateway",REACT_APP_APP_URL:"http://localhost:3000",REACT_APP_GOOGLE_ANALYTICS_ID:"",REACT_APP_GOOGLE_MAPS_API_KEY:"",REACT_APP_NODE_ENV:"development"}.NEXT_PUBLIC_API_URL||"http://localhost:5000/api";const c=new class{constructor(){this.client=void 0,this.client=s.A.create({baseURL:d,headers:{"Content-Type":"application/json"},withCredentials:!0}),this.setupInterceptors()}setupInterceptors(){this.client.interceptors.request.use(e=>{const t=o();return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,e=>{var t,a;const r=null===(t=e.response)||void 0===t?void 0:t.status,s=null===(a=e.response)||void 0===a?void 0:a.data;return 401===r&&("undefined"!==typeof window&&localStorage.removeItem(n),window.location.href="/login"),null!==s&&void 0!==s&&s.message?(0,i.oR)({title:"Error",description:s.message,variant:"destructive"}):e.message&&(0,i.oR)({title:"Error",description:e.message,variant:"destructive"}),Promise.reject(e)})}async get(e,t){return(await this.client.get(e,t)).data}async post(e,t,a){return(await this.client.post(e,t,a)).data}async put(e,t,a){return(await this.client.put(e,t,a)).data}async delete(e,t){return(await this.client.delete(e,t)).data}};var l=a(6379);const u=e=>{const t=e instanceof Error?e.message:"An error occurred while fetching data";(0,i.oR)({title:"Error",description:t,variant:"destructive"})},f={defaultOptions:{queries:{staleTime:3e5,retry:1,refetchOnWindowFocus:!1},mutations:{onError:e=>{u(e)}}}},m=(new l.E(f),function(){return{...arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},useErrorBoundary:e=>(u(e),!0)}}),p=function(){return{...arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},keepPreviousData:!0,refetchOnWindowFocus:!1}},h="/merchants/me",g="/merchants/me/stats",v="/merchants/me/collections",x="/merchants/me/transactions",y=e=>(0,r.I)({queryKey:["merchant"],queryFn:async()=>(await c.get(h)).data,...m(),...e}),b=e=>(0,r.I)({queryKey:["merchant","stats"],queryFn:async()=>(await c.get(g)).data,...m(),...e}),N=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;return(0,r.I)({queryKey:["merchant","collections",e],queryFn:async()=>(await c.get(v,{params:e})).data,...m(),...t})},w=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;return(0,r.I)({queryKey:["merchant","transactions",e],queryFn:async()=>(await c.get(x,{params:e})).data,...p(),...m(),...t})}},2417:(e,t,a)=>{a.d(t,{E:()=>n});a(5043);var r=a(3009),s=a(579);function n(e){let{className:t,...a}=e;return(0,s.jsx)("div",{className:(0,r.cn)("animate-pulse rounded-md bg-gray-100 dark:bg-gray-800",t),...a})}},6742:(e,t,a)=>{a.d(t,{BT:()=>c,Wu:()=>l,ZB:()=>d,Zp:()=>o,aR:()=>i,wL:()=>u});var r=a(5043),s=a(3009),n=a(579);const o=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)("div",{ref:t,className:(0,s.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...r})});o.displayName="Card";const i=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)("div",{ref:t,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",a),...r})});i.displayName="CardHeader";const d=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)("h3",{ref:t,className:(0,s.cn)("text-2xl font-semibold leading-none tracking-tight",a),...r})});d.displayName="CardTitle";const c=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)("p",{ref:t,className:(0,s.cn)("text-sm text-muted-foreground",a),...r})});c.displayName="CardDescription";const l=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)("div",{ref:t,className:(0,s.cn)("p-6 pt-0",a),...r})});l.displayName="CardContent";const u=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)("div",{ref:t,className:(0,s.cn)("flex items-center p-6 pt-0",a),...r})});u.displayName="CardFooter"},6875:(e,t,a)=>{a.d(t,{A:()=>r});const r=(0,a(3797).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},6879:(e,t,a)=>{a.d(t,{dj:()=>f,oR:()=>u});var r=a(5043);let s=0;const n=new Map,o=e=>{if(n.has(e))return;const t=setTimeout(()=>{n.delete(e),l({type:"REMOVE_TOAST",toastId:e})},1e3);n.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,5)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{const{toastId:a}=t;return a?o(a):e.toasts.forEach(e=>{o(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,open:!1}:e)}}case"REMOVE_TOAST":return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},d=[];let c={toasts:[]};function l(e){c=i(c,e),d.forEach(e=>{e(c)})}function u(e){let{...t}=e;const a=(s=(s+1)%Number.MAX_SAFE_INTEGER,s.toString()),r=()=>l({type:"DISMISS_TOAST",toastId:a});return l({type:"ADD_TOAST",toast:{...t,id:a,open:!0,onOpenChange:e=>{e||r()}}}),{id:a,dismiss:r,update:e=>l({type:"UPDATE_TOAST",toast:{...e,id:a}})}}function f(){const[e,t]=r.useState(c);return r.useEffect(()=>(d.push(t),()=>{const e=d.indexOf(t);e>-1&&d.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>l({type:"DISMISS_TOAST",toastId:e})}}},8567:(e,t,a)=>{a.d(t,{E:()=>i});a(5043);var r=a(917),s=a(3009),n=a(579);const o=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",success:"border-transparent bg-green-500 text-white hover:bg-green-500/80",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-500/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:a,...r}=e;return(0,n.jsx)("div",{className:(0,s.cn)(o({variant:a}),t),...r})}},9772:(e,t,a)=>{a.d(t,{$:()=>c});var r=a(5043),s=a(6851),n=a(917),o=a(3009),i=a(579);const d=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=r.forwardRef((e,t)=>{let{className:a,variant:r,size:n,asChild:c=!1,...l}=e;const u=c?s.DX:"button";return(0,i.jsx)(u,{className:(0,o.cn)(d({variant:r,size:n,className:a})),ref:t,...l})});c.displayName="Button"},9790:(e,t,a)=>{a.r(t),a.d(t,{default:()=>p});var r=a(5043),s=a(6742),n=a(382),o=a(8567),i=a(9772),d=a(9954),c=a(519),l=a(2417),u=a(4068),f=a(6875),m=a(579);function p(){const[e,t]=(0,r.useState)(""),{data:a={data:[],total:0,page:1,limit:10,totalPages:1},isLoading:p,refetch:h}=(0,c.xG)({search:e||void 0,sortBy:"createdAt",sortOrder:"desc",page:1,limit:10}),g=(null===a||void 0===a?void 0:a.data)||[],{total:v=0,page:x=1,totalPages:y=1}=a||{};return(0,m.jsxs)("div",{className:"p-6 space-y-6",children:[(0,m.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,m.jsx)("div",{children:(0,m.jsx)("h1",{className:"text-2xl font-bold",children:"Transactions"})}),(0,m.jsxs)("div",{className:"flex items-center space-x-2 w-full sm:w-96",children:[(0,m.jsxs)("div",{className:"relative w-full",children:[(0,m.jsx)(f.A,{className:"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground"}),(0,m.jsx)(d.p,{type:"search",placeholder:"Search transactions...",className:"w-full pl-8",value:e,onChange:e=>t(e.target.value)})]}),(0,m.jsx)(i.$,{variant:"outline",size:"icon",onClick:()=>h(),children:(0,m.jsx)(u.A,{className:"h-4 w-4"})})]})]}),(0,m.jsx)(s.Zp,{children:(0,m.jsx)(s.Wu,{className:"p-6",children:p?(0,m.jsx)("div",{className:"space-y-4",children:[1,2,3,4,5].map(e=>(0,m.jsx)(l.E,{className:"h-12 w-full"},e))}):(0,m.jsxs)(n.XI,{children:[(0,m.jsx)(n.A0,{children:(0,m.jsxs)(n.Hj,{children:[(0,m.jsx)(n.nd,{children:"ID"}),(0,m.jsx)(n.nd,{children:"Amount"}),(0,m.jsx)(n.nd,{children:"Status"}),(0,m.jsx)(n.nd,{children:"Date"})]})}),(0,m.jsx)(n.BF,{children:g.map(e=>{var t;return(0,m.jsxs)(n.Hj,{children:[(0,m.jsxs)(n.nA,{className:"font-medium",children:[e.id.substring(0,8),"..."]}),(0,m.jsxs)(n.nA,{children:["$",null===(t=e.amount)||void 0===t?void 0:t.toFixed(2)]}),(0,m.jsx)(n.nA,{children:(0,m.jsx)(o.E,{variant:"completed"===e.status?"success":"pending"===e.status?"warning":"destructive",children:e.status})}),(0,m.jsx)(n.nA,{children:new Date(e.createdAt).toLocaleDateString()})]},e.id)})})]})})})]})}},9954:(e,t,a)=>{a.d(t,{p:()=>o});var r=a(5043),s=a(3009),n=a(579);const o=r.forwardRef((e,t)=>{let{className:a,type:r,...o}=e;return(0,n.jsx)("input",{type:r,className:(0,s.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:t,...o})});o.displayName="Input"}}]);
//# sourceMappingURL=790.897734ef.chunk.js.map