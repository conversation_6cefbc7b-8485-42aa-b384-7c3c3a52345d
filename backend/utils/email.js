const ejs = require('ejs');
const path = require('path');
const fs = require('fs').promises;
const ApiError = require('./ApiError');
const { transporter } = require('../config/email');
const logger = require('./logger');

/**
 * Render email template with EJS
 * @param {string} templateName - Name of the template file (without extension)
 * @param {Object} data - Data to pass to the template
 * @returns {Promise<string>} Rendered HTML
 */
const renderTemplate = async (templateName, data = {}) => {
  try {
    const templatePath = path.join(
      __dirname,
      '..',
      'views',
      'emails',
      `${templateName}.ejs`
    );
    
    const template = await fs.readFile(templatePath, 'utf-8');
    return ejs.render(template, { ...data, baseUrl: process.env.FRONTEND_URL });
  } catch (error) {
    logger.error('Error rendering email template:', error);
    throw new Error('Failed to render email template');
  }
};

/**
 * Send an email
 * @param {Object} options - Email options
 * @param {string|Array} options.to - Email recipient(s)
 * @param {string} options.subject - Email subject
 * @param {string} options.template - Name of the template file (without .ejs extension)
 * @param {Object} options.context - Data to pass to the template
 * @returns {Promise<Object>} Result from nodemailer
 */
const sendEmail = async ({ to, subject, template, context = {} }) => {
  try {
    if (!to) {
      throw new ApiError('No recipient specified', 400);
    }

    // Render email template
    const html = await renderTemplate(template, context);

    // Setup email data
    const mailOptions = {
      from: `"${process.env.EMAIL_FROM_NAME}" <${process.env.EMAIL_FROM_ADDRESS}>`,
      to: Array.isArray(to) ? to.join(', ') : to,
      subject,
      html,
    };

    // Send email
    const info = await transporter.sendMail(mailOptions);
    
    logger.info(`Email sent: ${info.messageId}`);
    return info;
  } catch (error) {
    logger.error('Error sending email:', {
      message: error.message,
      stack: error.stack,
      code: error.code,
      response: error.response,
      command: error.command,
      statusCode: error.statusCode,
      to: to,
      subject: subject,
      template: template
    });
    
    // If this is a nodemailer error, extract more details
    if (error.response) {
      logger.error('Nodemailer error response:', error.response);
    }
    
    throw new ApiError(
      error.message || 'Failed to send email',
      error.statusCode || 500,
      'EMAIL_SEND_ERROR',
      { 
        originalError: {
          message: error.message,
          code: error.code,
          response: error.response
        } 
      }
    );
  }
};

/**
 * Send password reset email
 * @param {string} email - Recipient email
 * @param {string} resetToken - Password reset token
 * @param {string} name - User's name
 * @returns {Promise<Object>} Result from nodemailer
 */
const sendPasswordResetEmail = async (email, resetToken, name) => {
  const resetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}`;
  
  return sendEmail({
    to: email,
    subject: 'Password Reset Request',
    template: 'password-reset',
    context: {
      name,
      resetUrl,
      expiresIn: '10 minutes',
    },
  });
};

/**
 * Send email verification email
 * @param {string} email - Recipient email
 * @param {string} verificationToken - Email verification token
 * @param {string} name - User's name
 * @returns {Promise<Object>} Result from nodemailer
 */
const sendVerificationEmail = async (email, verificationToken, name) => {
  const verificationUrl = `${process.env.FRONTEND_URL}/verify-email?token=${verificationToken}`;
  
  return sendEmail({
    to: email,
    subject: 'Verify Your Email',
    template: 'email-verification',
    context: {
      name,
      verificationUrl,
    },
  });
};

/**
 * Send 2FA setup email
 * @param {string} email - Recipient email
 * @param {string} secret - 2FA secret
 * @param {string} name - User's name
 * @returns {Promise<Object>} Result from nodemailer
 */
const send2FASetupEmail = async (email, secret, name) => {
  return sendEmail({
    to: email,
    subject: 'Two-Factor Authentication Setup',
    template: '2fa-setup',
    context: {
      name,
      secret,
    },
  });
};

/**
 * Send login notification email
 * @param {string} email - Recipient email
 * @param {string} name - User's name
 * @param {Object} loginInfo - Login information
 * @param {string} loginInfo.ip - IP address
 * @param {string} loginInfo.browser - Browser information
 * @param {string} loginInfo.location - Location information
 * @param {Date} loginInfo.timestamp - Login timestamp
 * @returns {Promise<Object>} Result from nodemailer
 */
const sendLoginNotificationEmail = async (email, name, loginInfo) => {
  return sendEmail({
    to: email,
    subject: 'New Login Detected',
    template: 'login-notification',
    context: {
      name,
      ...loginInfo,
      timestamp: loginInfo.timestamp || new Date(),
    },
  });
};

module.exports = {
  sendEmail,
  sendPasswordResetEmail,
  sendVerificationEmail,
  send2FASetupEmail,
  sendLoginNotificationEmail,
};
