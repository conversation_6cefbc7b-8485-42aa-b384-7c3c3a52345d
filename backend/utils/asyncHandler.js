const logger = require('./logger');

/**
 * Wraps an async function to catch errors and pass them to the Express error handler
 * @param {Function} fn - The async function to wrap
 * @returns {Function} A middleware function that handles async/await errors
 */
const asyncHandler = (fn) => (req, res, next) => {
  // Return a new promise that resolves the original function
  // and catches any errors to pass them to Express's error handler
  Promise.resolve(fn(req, res, next)).catch((error) => {
    // Log the error with request context
    logger.error('Async handler error:', {
      message: error.message,
      stack: error.stack,
      path: req.path,
      method: req.method,
      ip: req.ip,
      user: req.user ? req.user.id : 'anonymous',
      body: process.env.NODE_ENV === 'development' ? req.body : {},
      query: process.env.NODE_ENV === 'development' ? req.query : {},
      params: process.env.NODE_ENV === 'development' ? req.params : {},
    });
    
    // Pass the error to the next middleware (error handler)
    next(error);
  });
};

/**
 * Wraps a controller method with error handling and response formatting
 * @param {Function} controllerMethod - The controller method to wrap
 * @returns {Function} A wrapped controller method with error handling
 */
const controllerHandler = (controllerMethod) => 
  asyncHandler(async (req, res, next) => {
    try {
      // Execute the controller method
      const result = await controllerMethod(req, res, next);
      
      // If headers are already sent, don't try to send a response
      if (res.headersSent) {
        return;
      }
      
      // If the result is undefined, assume the response was handled in the controller
      if (result === undefined) {
        return;
      }
      
      // Send the response based on the result
      if (result.redirect) {
        // Handle redirects
        return res.redirect(result.redirect);
      } else if (result.file) {
        // Handle file downloads
        return res.download(result.file.path, result.file.filename || undefined, (err) => {
          if (err && !res.headersSent) {
            logger.error('File download error:', { error: err.message });
            res.status(500).json({
              success: false,
              message: 'Error downloading file',
              error: process.env.NODE_ENV === 'development' ? err.message : undefined,
            });
          }
        });
      } else if (result.stream) {
        // Handle streaming responses
        if (result.filename) {
          res.attachment(result.filename);
        }
        result.stream.pipe(res);
      } else {
        // Standard JSON response
        const response = {
          success: true,
          ...(typeof result === 'object' ? result : { data: result }),
        };
        
        // Send the response
        res.status(response.statusCode || 200).json(response);
      }
    } catch (error) {
      // Pass the error to the error handling middleware
      next(error);
    }
  });

/**
 * Wraps a service method with error handling and logging
 * @param {Function} serviceMethod - The service method to wrap
 * @param {Object} options - Options for the wrapper
 * @param {string} options.serviceName - The name of the service (for logging)
 * @param {string} options.methodName - The name of the method (for logging)
 * @returns {Function} A wrapped service method with error handling
 */
const serviceMethodWrapper = (serviceMethod, { serviceName, methodName } = {}) => 
  async (...args) => {
    const startTime = Date.now();
    const requestId = args[0]?.requestId || `req-${Math.random().toString(36).substr(2, 9)}`;
    
    // Add request ID to the first argument if it's an object
    if (args[0] && typeof args[0] === 'object' && !args[0].requestId) {
      args[0] = { ...args[0], requestId };
    }
    
    try {
      logger.debug(`Service method called: ${serviceName || 'unknown'}.${methodName || 'unknown'}`, {
        service: serviceName,
        method: methodName,
        requestId,
        args: process.env.NODE_ENV === 'development' ? 
          JSON.parse(JSON.stringify(args, (key, value) => 
            key === 'password' || key === 'token' || key === 'refreshToken' ? '***' : value
          )) : undefined,
      });
      
      // Execute the service method
      const result = await serviceMethod(...args);
      
      const duration = Date.now() - startTime;
      logger.debug(`Service method completed: ${serviceName || 'unknown'}.${methodName || 'unknown'}`, {
        service: serviceName,
        method: methodName,
        requestId,
        duration: `${duration}ms`,
        hasResult: result !== undefined,
      });
      
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error(`Service method error: ${serviceName || 'unknown'}.${methodName || 'unknown'}`, {
        service: serviceName,
        method: methodName,
        requestId,
        duration: `${duration}ms`,
        error: error.message,
        stack: error.stack,
        code: error.code,
        ...(error.details && { details: error.details }),
      });
      
      // Re-throw the error to be handled by the controller
      throw error;
    }
  };

/**
 * Creates a service wrapper that automatically wraps all methods with error handling
 * @param {Object} service - The service object containing methods to wrap
 * @param {string} serviceName - The name of the service (for logging)
 * @returns {Object} A new service object with wrapped methods
 */
const createServiceWrapper = (service, serviceName) => {
  const wrappedService = {};
  
  // Wrap all methods of the service
  Object.getOwnPropertyNames(Object.getPrototypeOf(service))
    .filter(method => 
      method !== 'constructor' && 
      typeof service[method] === 'function' &&
      !method.startsWith('_')
    )
    .forEach(method => {
      wrappedService[method] = serviceMethodWrapper(service[method].bind(service), {
        serviceName,
        methodName: method,
      });
    });
  
  return wrappedService;
};

/**
 * Handles promise rejections and logs them
 * @param {string} context - The context where the error occurred
 * @param {Error} error - The error that occurred
 * @param {Object} [additionalInfo] - Additional information to log
 * @returns {void}
 */
const handlePromiseRejection = (context, error, additionalInfo = {}) => {
  logger.error(`Unhandled promise rejection in ${context}:`, {
    error: error.message,
    stack: error.stack,
    ...additionalInfo,
  });
};

/**
 * Creates a timeout wrapper for promises
 * @param {Promise} promise - The promise to wrap
 * @param {number} ms - Timeout in milliseconds
 * @param {string} [errorMessage] - Custom error message for timeout
 * @returns {Promise} A promise that rejects after the timeout
 */
const withTimeout = (promise, ms, errorMessage = 'Operation timed out') => {
  const timeout = new Promise((_, reject) => {
    const id = setTimeout(() => {
      clearTimeout(id);
      reject(new Error(errorMessage));
    }, ms);
  });

  return Promise.race([promise, timeout]);
};

/**
 * Retries a function with exponential backoff
 * @param {Function} fn - The async function to retry
 * @param {Object} options - Retry options
 * @param {number} [options.retries=3] - Number of retry attempts
 * @param {number} [options.delay=1000] - Initial delay in milliseconds
 * @param {number} [options.maxDelay=30000] - Maximum delay in milliseconds
 * @param {Function} [options.shouldRetry] - Function to determine if a retry should be attempted
 * @returns {Promise} A promise that resolves with the function result or rejects after all retries
 */
const retryWithBackoff = async (fn, options = {}) => {
  const {
    retries = 3,
    delay = 1000,
    maxDelay = 30000,
    shouldRetry = (error) => true,
  } = options;

  let lastError;
  
  for (let i = 0; i < retries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      // Check if we should retry
      if (i === retries - 1 || !shouldRetry(error)) {
        break;
      }
      
      // Calculate backoff with jitter
      const backoff = Math.min(delay * Math.pow(2, i) + Math.random() * 1000, maxDelay);
      
      logger.warn(`Retry attempt ${i + 1}/${retries} after error: ${error.message}`, {
        attempt: i + 1,
        maxAttempts: retries,
        backoff: `${backoff}ms`,
        error: error.message,
        stack: error.stack,
      });
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, backoff));
    }
  }
  
  throw lastError;
};

module.exports = {
  asyncHandler,
  controllerHandler,
  serviceMethodWrapper,
  createServiceWrapper,
  handlePromiseRejection,
  withTimeout,
  retryWithBackoff,
};
