import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  Chip,
  Divider,
  FormControl,
  FormControlLabel,
  FormHelperText,
  Grid,
  IconButton,
  InputLabel,
  LinearProgress,
  MenuItem,
  Paper,
  Select,
  Switch,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  Tooltip,
  Typography,
  useTheme,
} from '@mui/material';
import {
  CheckCircle as ApproveIcon,
  Flag as FlagIcon,
  Refresh as RefreshIcon,
  Search as SearchIcon,
  Visibility as ViewIcon,
  Warning as WarningIcon,
  FilterList as FilterIcon,
  Clear as ClearIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { useSnackbar } from 'notistack';
import { useConfirm } from 'material-ui-confirm';
import api from '../../../services/api';

// Status chip component
const StatusChip = ({ status }) => {
  const theme = useTheme();
  
  const statusConfig = {
    pending: { label: 'Pending', color: 'warning' },
    approved: { label: 'Approved', color: 'success' },
    flagged: { label: 'Flagged', color: 'error' },
    reviewed: { label: 'Reviewed', color: 'info' },
  };
  
  const config = statusConfig[status] || { label: status, color: 'default' };
  
  return (
    <Chip
      size="small"
      label={config.label}
      color={config.color}
      variant="outlined"
    />
  );
};

// Activity type chip component
const ActivityTypeChip = ({ type }) => {
  const theme = useTheme();
  
  const typeConfig = {
    create: { label: 'Create', color: 'success' },
    update: { label: 'Update', color: 'info' },
    delete: { label: 'Delete', color: 'error' },
    status_change: { label: 'Status Change', color: 'warning' },
    rule_update: { label: 'Rule Update', color: 'primary' },
    auto_replenish: { label: 'Auto Replenish', color: 'secondary' },
  };
  
  const config = typeConfig[type] || { label: type, color: 'default' };
  
  return (
    <Chip
      size="small"
      label={config.label}
      color={config.color}
      variant="outlined"
    />
  );
};

const ReserveStrategyReviewPanel = () => {
  const theme = useTheme();
  const { enqueueSnackbar } = useSnackbar();
  const confirm = useConfirm();
  
  // State
  const [loading, setLoading] = useState(true);
  const [activities, setActivities] = useState([]);
  const [selectedActivity, setSelectedActivity] = useState(null);
  const [reviewDialogOpen, setReviewDialogOpen] = useState(false);
  const [reviewNote, setReviewNote] = useState('');
  const [reviewAction, setReviewAction] = useState('approve');
  const [submitting, setSubmitting] = useState(false);
  
  // Pagination
  const [pagination, setPagination] = useState({
    page: 0,
    pageSize: 10,
    total: 0,
  });
  
  // Filters
  const [filters, setFilters] = useState({
    status: 'pending',
    type: 'all',
    search: '',
    dateRange: {
      start: null,
      end: null,
    },
  });
  
  // Activity types for filter
  const activityTypes = [
    { value: 'all', label: 'All Types' },
    { value: 'create', label: 'Create' },
    { value: 'update', label: 'Update' },
    { value: 'delete', label: 'Delete' },
    { value: 'status_change', label: 'Status Change' },
    { value: 'rule_update', label: 'Rule Update' },
    { value: 'auto_replenish', label: 'Auto Replenish' },
  ];
  
  // Fetch activities
  const fetchActivities = async () => {
    try {
      setLoading(true);
      
      const params = {
        page: pagination.page + 1,
        limit: pagination.pageSize,
        status: filters.status !== 'all' ? filters.status : undefined,
        type: filters.type !== 'all' ? filters.type : undefined,
        search: filters.search || undefined,
        startDate: filters.dateRange.start ? format(filters.dateRange.start, 'yyyy-MM-dd') : undefined,
        endDate: filters.dateRange.end ? format(filters.dateRange.end, 'yyyy-MM-dd') : undefined,
      };
      
      const response = await api.get('/reserve-strategies/activities', { params });
      
      setActivities(response.data.docs);
      setPagination(prev => ({
        ...prev,
        total: response.data.total,
      }));
    } catch (error) {
      console.error('Error fetching activities:', error);
      enqueueSnackbar('Failed to load activities', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };
  
  // Initial load
  useEffect(() => {
    fetchActivities();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pagination.page, pagination.pageSize, filters]);
  
  // Handle page change
  const handleChangePage = (event, newPage) => {
    setPagination(prev => ({
      ...prev,
      page: newPage,
    }));
  };
  
  // Handle rows per page change
  const handleChangeRowsPerPage = (event) => {
    setPagination(prev => ({
      ...prev,
      page: 0,
      pageSize: parseInt(event.target.value, 10),
    }));
  };
  
  // Handle filter change
  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value,
    }));
    
    // Reset to first page when filters change
    if (pagination.page !== 0) {
      setPagination(prev => ({
        ...prev,
        page: 0,
      }));
    }
  };
  
  // Handle date range change
  const handleDateRangeChange = (field, date) => {
    setFilters(prev => ({
      ...prev,
      dateRange: {
        ...prev.dateRange,
        [field]: date,
      },
    }));
  };
  
  // Clear all filters
  const clearFilters = () => {
    setFilters({
      status: 'pending',
      type: 'all',
      search: '',
      dateRange: {
        start: null,
        end: null,
      },
    });
  };
  
  // Open review dialog
  const handleReviewClick = (activity) => {
    setSelectedActivity(activity);
    setReviewNote('');
    setReviewAction('approve');
    setReviewDialogOpen(true);
  };
  
  // Close review dialog
  const handleCloseReviewDialog = () => {
    setReviewDialogOpen(false);
    setSelectedActivity(null);
  };
  
  // Submit review
  const handleSubmitReview = async () => {
    if (!selectedActivity) return;
    
    try {
      setSubmitting(true);
      
      await api.post(`/reserve-strategies/activities/${selectedActivity._id}/review`, {
        action: reviewAction,
        note: reviewNote,
      });
      
      enqueueSnackbar(`Activity ${reviewAction === 'approve' ? 'approved' : 'flagged'} successfully`, { 
        variant: reviewAction === 'approve' ? 'success' : 'warning' 
      });
      
      // Refresh activities
      fetchActivities();
      handleCloseReviewDialog();
    } catch (error) {
      console.error('Error submitting review:', error);
      enqueueSnackbar(
        error.response?.data?.message || 'Failed to submit review', 
        { variant: 'error' }
      );
    } finally {
      setSubmitting(false);
    }
  };
  
  // Format activity details for display
  const formatActivityDetails = (activity) => {
    if (!activity.details) return 'No details available';
    
    try {
      const details = typeof activity.details === 'string' 
        ? JSON.parse(activity.details) 
        : activity.details;
      
      if (activity.type === 'status_change') {
        return `Status changed from "${details.from}" to "${details.to}"`;
      }
      
      if (activity.type === 'rule_update') {
        return `Rule "${details.ruleName || 'Unnamed Rule'}" was updated`;
      }
      
      if (activity.type === 'auto_replenish') {
        return `Auto-replenish triggered: $${details.amount} from ${details.sourceAccount}`;
      }
      
      return JSON.stringify(details, null, 2);
    } catch (e) {
      return activity.details;
    }
  };
  
  return (
    <Box>
      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="flex-end">
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Status</InputLabel>
                <Select
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  label="Status"
                >
                  <MenuItem value="all">All Statuses</MenuItem>
                  <MenuItem value="pending">Pending Review</MenuItem>
                  <MenuItem value="reviewed">Reviewed</MenuItem>
                  <MenuItem value="approved">Approved</MenuItem>
                  <MenuItem value="flagged">Flagged</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Activity Type</InputLabel>
                <Select
                  value={filters.type}
                  onChange={(e) => handleFilterChange('type', e.target.value)}
                  label="Activity Type"
                >
                  {activityTypes.map((type) => (
                    <MenuItem key={type.value} value={type.value}>
                      {type.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={6} md={3}>
              <TextField
                fullWidth
                size="small"
                label="Search"
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                InputProps={{
                  startAdornment: <SearchIcon color="action" sx={{ mr: 1 }} />,
                }}
                placeholder="Search activities..."
              />
            </Grid>
            
            <Grid item xs={12} sm={6} md={3} sx={{ display: 'flex', gap: 1 }}>
              <Button
                variant="outlined"
                startIcon={<FilterIcon />}
                onClick={() => {
                  // Show advanced filters
                  // This would typically open a dialog with more filter options
                  enqueueSnackbar('Advanced filters coming soon', { variant: 'info' });
                }}
                sx={{ flex: 1 }}
              >
                Filters
              </Button>
              
              <Button
                variant="outlined"
                startIcon={<ClearIcon />}
                onClick={clearFilters}
                color="secondary"
              >
                Clear
              </Button>
              
              <Tooltip title="Refresh">
                <IconButton onClick={fetchActivities} disabled={loading}>
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
      
      {/* Activities Table */}
      <Paper variant="outlined">
        {loading ? (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <LinearProgress />
            <Typography variant="body2" color="textSecondary" sx={{ mt: 1 }}>
              Loading activities...
            </Typography>
          </Box>
        ) : activities.length === 0 ? (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="body1" color="textSecondary">
              No activities found matching your criteria
            </Typography>
          </Box>
        ) : (
          <>
            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Type</TableCell>
                    <TableCell>Description</TableCell>
                    <TableCell>Strategy</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Date</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {activities.map((activity) => (
                    <TableRow
                      key={activity._id}
                      hover
                      sx={{
                        '&:nth-of-type(odd)': {
                          backgroundColor: 'action.hover',
                        },
                      }}
                    >
                      <TableCell>
                        <ActivityTypeChip type={activity.type} />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {activity.description || 'No description'}
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          {formatActivityDetails(activity)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {activity.strategy?.name || 'N/A'}
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          {activity.strategy?._id || ''}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <StatusChip status={activity.status} />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {format(new Date(activity.createdAt), 'MMM d, yyyy')}
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          {format(new Date(activity.createdAt), 'h:mm a')}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Tooltip title="View Details">
                            <IconButton
                              size="small"
                              onClick={() => {
                                setSelectedActivity(activity);
                                // This would typically open a dialog with full details
                                enqueueSnackbar('Activity details view coming soon', { variant: 'info' });
                              }}
                            >
                              <ViewIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          
                          {activity.status === 'pending' && (
                            <>
                              <Tooltip title="Approve">
                                <IconButton
                                  size="small"
                                  color="success"
                                  onClick={() => handleReviewClick(activity)}
                                >
                                  <ApproveIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                              
                              <Tooltip title="Flag for Review">
                                <IconButton
                                  size="small"
                                  color="warning"
                                  onClick={() => {
                                    setReviewAction('flag');
                                    handleReviewClick(activity);
                                  }}
                                >
                                  <FlagIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            </>
                          )}
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
            
            <TablePagination
              rowsPerPageOptions={[5, 10, 25, 50]}
              component="div"
              count={pagination.total}
              rowsPerPage={pagination.pageSize}
              page={pagination.page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </>
        )}
      </Paper>
      
      {/* Review Dialog */}
      {selectedActivity && (
        <Dialog
          open={reviewDialogOpen}
          onClose={handleCloseReviewDialog}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            {reviewAction === 'approve' ? 'Approve Activity' : 'Flag Activity'}
          </DialogTitle>
          
          <DialogContent>
            <Box mb={2}>
              <Typography variant="subtitle2" color="textSecondary">Activity Type</Typography>
              <Typography variant="body1" gutterBottom>
                <ActivityTypeChip type={selectedActivity.type} />
              </Typography>
              
              <Typography variant="subtitle2" color="textSecondary" sx={{ mt: 2 }}>
                Description
              </Typography>
              <Typography variant="body1" gutterBottom>
                {selectedActivity.description || 'No description'}
              </Typography>
              
              <Typography variant="subtitle2" color="textSecondary" sx={{ mt: 2 }}>
                Details
              </Typography>
              <Paper 
                variant="outlined" 
                sx={{ 
                  p: 2, 
                  bgcolor: 'background.default',
                  maxHeight: 200,
                  overflow: 'auto',
                  mb: 2,
                }}
              >
                <pre style={{ margin: 0, whiteSpace: 'pre-wrap', wordWrap: 'break-word' }}>
                  {formatActivityDetails(selectedActivity)}
                </pre>
              </Paper>
              
              <FormControl fullWidth margin="normal">
                <TextField
                  label={reviewAction === 'approve' ? 'Approval Notes' : 'Reason for Flagging'}
                  multiline
                  rows={3}
                  value={reviewNote}
                  onChange={(e) => setReviewNote(e.target.value)}
                  variant="outlined"
                  placeholder={reviewAction === 'approve' 
                    ? 'Add any notes about this approval...' 
                    : 'Explain why this activity is being flagged...'}
                />
                <FormHelperText>
                  {reviewAction === 'approve'
                    ? 'Optional notes about this approval'
                    : 'Please provide details about any concerns with this activity'}
                </FormHelperText>
              </FormControl>
              
              {reviewAction === 'approve' && (
                <FormControlLabel
                  control={
                    <Switch
                      checked={reviewAction === 'approve'}
                      onChange={(e) => setReviewAction(e.target.checked ? 'approve' : 'flag')}
                      color="primary"
                    />
                  }
                  label="Approve this activity"
                  sx={{ mt: 1 }}
                />
              )}
              
              {reviewAction === 'flag' && (
                <Box sx={{ mt: 1, p: 1, bgcolor: 'warning.light', borderRadius: 1 }}>
                  <Box display="flex" alignItems="center">
                    <WarningIcon color="warning" sx={{ mr: 1 }} />
                    <Typography variant="body2">
                      Flagging will mark this activity for further review and may trigger additional notifications.
                    </Typography>
                  </Box>
                </Box>
              )}
            </Box>
          </DialogContent>
          
          <DialogActions>
            <Button onClick={handleCloseReviewDialog} disabled={submitting}>
              Cancel
            </Button>
            <Button
              onClick={handleSubmitReview}
              color={reviewAction === 'approve' ? 'primary' : 'warning'}
              variant="contained"
              disabled={submitting}
              startIcon={reviewAction === 'approve' ? <ApproveIcon /> : <FlagIcon />}
            >
              {submitting ? 'Processing...' : reviewAction === 'approve' ? 'Approve' : 'Flag for Review'}
            </Button>
          </DialogActions>
        </Dialog>
      )}
    </Box>
  );
};

export default ReserveStrategyReviewPanel;
