{"version": 3, "file": "static/js/236.16d2aa83.chunk.js", "mappings": "kOAIA,MAAMA,EAAQC,EAAAA,WAGZ,CAAAC,EAA0BC,KAAG,IAA5B,UAAEC,KAAcC,GAAOH,EAAA,OACxBI,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uBAAsBG,UACnCD,EAAAA,EAAAA,KAAA,SACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IAAG,gCAAiCJ,MAC3CC,QAIVL,EAAMS,YAAc,QAEpB,MAAMC,EAAcT,EAAAA,WAGlB,CAAAU,EAA0BR,KAAG,IAA5B,UAAEC,KAAcC,GAAOM,EAAA,OACxBL,EAAAA,EAAAA,KAAA,SAAOH,IAAKA,EAAKC,WAAWI,EAAAA,EAAAA,IAAG,kBAAmBJ,MAAgBC,MAEpEK,EAAYD,YAAc,cAE1B,MAAMG,EAAYX,EAAAA,WAGhB,CAAAY,EAA0BV,KAAG,IAA5B,UAAEC,KAAcC,GAAOQ,EAAA,OACxBP,EAAAA,EAAAA,KAAA,SACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IAAG,6BAA8BJ,MACxCC,MAGRO,EAAUH,YAAc,YAEJR,EAAAA,WAGlB,CAAAa,EAA0BX,KAAG,IAA5B,UAAEC,KAAcC,GAAOS,EAAA,OACxBR,EAAAA,EAAAA,KAAA,SACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IAAG,iDAAkDJ,MAC5DC,MAGII,YAAc,cAE1B,MAAMM,EAAWd,EAAAA,WAGf,CAAAe,EAA0Bb,KAAG,IAA5B,UAAEC,KAAcC,GAAOW,EAAA,OACxBV,EAAAA,EAAAA,KAAA,MACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IACT,8EACAJ,MAEEC,MAGRU,EAASN,YAAc,WAEvB,MAAMQ,EAAYhB,EAAAA,WAGhB,CAAAiB,EAA0Bf,KAAG,IAA5B,UAAEC,KAAcC,GAAOa,EAAA,OACxBZ,EAAAA,EAAAA,KAAA,MACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IACT,mGACAJ,MAEEC,MAGRY,EAAUR,YAAc,YAExB,MAAMU,EAAYlB,EAAAA,WAGhB,CAAAmB,EAA0BjB,KAAG,IAA5B,UAAEC,KAAcC,GAAOe,EAAA,OACxBd,EAAAA,EAAAA,KAAA,MACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IAAG,iDAAkDJ,MAC5DC,MAGRc,EAAUV,YAAc,YAEHR,EAAAA,WAGnB,CAAAoB,EAA0BlB,KAAG,IAA5B,UAAEC,KAAcC,GAAOgB,EAAA,OACxBf,EAAAA,EAAAA,KAAA,WACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IAAG,qCAAsCJ,MAChDC,MAGKI,YAAc,c,6ICjG3B,MAAMa,EAASC,EAAAA,GAETC,EAAgBD,EAAAA,GAOhBE,EAAevB,IAIK,IAJJ,UACpBE,EAAS,SACTG,KACGF,GACeH,EAClB,MAAM,UAAEwB,EAAS,WAAEC,KAAeC,GAAcvB,EAC1CwB,EAAc,CAClBH,YACAC,WAAYA,GAEd,OACErB,EAAAA,EAAAA,KAACiB,EAAAA,GAAsB,IAAKM,EAAWtB,UACrCD,EAAAA,EAAAA,KAAA,OAAKF,WAAWI,EAAAA,EAAAA,IACd,qEACAJ,GACAG,SACCA,OAKTkB,EAAahB,YAAcc,EAAAA,GAAuBd,YAElD,MAAMqB,EAAgB7B,EAAAA,WAGpB,CAAAU,EAA0BR,KAAG,IAA5B,UAAEC,KAAcC,GAAOM,EAAA,OACxBL,EAAAA,EAAAA,KAACiB,EAAAA,GAAuB,CACtBpB,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IACT,qKACAJ,MAEEC,MAGRyB,EAAcrB,YAAcc,EAAAA,GAAwBd,YAEpD,MAAMsB,EAAgB9B,EAAAA,WAGpB,CAAAY,EAAoCV,KAAG,IAAtC,UAAEC,EAAS,SAAEG,KAAaF,GAAOQ,EAAA,OAClCmB,EAAAA,EAAAA,MAACP,EAAY,CAAAlB,SAAA,EACXD,EAAAA,EAAAA,KAACwB,EAAa,KACdE,EAAAA,EAAAA,MAACT,EAAAA,GAAuB,CACtBpB,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IACT,+NACA,mBACAJ,MAEEC,EAAKE,SAAA,CAERA,GACDyB,EAAAA,EAAAA,MAACT,EAAAA,GAAqB,CAACnB,UAAU,mTAAkTG,SAAA,EACjVD,EAAAA,EAAAA,KAAC2B,EAAAA,EAAC,CAAC7B,UAAU,aACbE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,UAASG,SAAC,qBAKlCwB,EAActB,YAAcc,EAAAA,GAAwBd,YAEpD,MAAMyB,EAAepB,IAAA,IAAC,UACpBV,KACGC,GACkCS,EAAA,OACrCR,EAAAA,EAAAA,KAAA,OACEF,WAAWI,EAAAA,EAAAA,IACT,mDACAJ,MAEEC,KAGR6B,EAAazB,YAAc,eAE3B,MAAM0B,EAAenB,IAAA,IAAC,UACpBZ,KACGC,GACkCW,EAAA,OACrCV,EAAAA,EAAAA,KAAA,OACEF,WAAWI,EAAAA,EAAAA,IACT,gEACAJ,MAEEC,KAGR8B,EAAa1B,YAAc,eAE3B,MAAM2B,EAAcnC,EAAAA,WAGlB,CAAAiB,EAA0Bf,KAAG,IAA5B,UAAEC,KAAcC,GAAOa,EAAA,OACxBZ,EAAAA,EAAAA,KAACiB,EAAAA,GAAqB,CACpBpB,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IACT,sCACA,oBACAJ,MAEEC,MAGR+B,EAAY3B,YAAcc,EAAAA,GAAsBd,YAEhD,MAAM4B,EAAoBpC,EAAAA,WAGxB,CAAAmB,EAA0BjB,KAAG,IAA5B,UAAEC,KAAcC,GAAOe,EAAA,OACxBd,EAAAA,EAAAA,KAACiB,EAAAA,GAA2B,CAC1BpB,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IAAG,wBAAyB,qBAAsBJ,MACzDC,MAGRgC,EAAkB5B,YAAcc,EAAAA,GAA4Bd,W,wKCtHtD,MAAA6B,GAAWC,E,QAAAA,GAAiB,WAAY,CAC5C,CAAC,OAAQ,CAAEC,EAAG,4CAA6CC,IAAK,WAChE,CAAC,SAAU,CAAEC,GAAI,IAAKC,GAAI,IAAKC,EAAG,IAAKH,IAAK,UAC5C,CAAC,OAAQ,CAAEI,GAAI,KAAMC,GAAI,KAAMC,GAAI,IAAKC,GAAI,KAAMP,IAAK,WACvD,CAAC,OAAQ,CAAEI,GAAI,KAAMC,GAAI,KAAMC,GAAI,KAAMC,GAAI,KAAMP,IAAK,a,qDCmBnD,MAAMQ,UAAiBC,MAK5BC,WAAAA,CAAYC,EAAiBC,EAAiBC,EAAeC,GAC3DC,MAAMJ,GAAS,KALjBC,YAAM,OACNC,UAAI,OACJC,aAAO,EAILE,KAAKC,KAAO,WACZD,KAAKJ,OAASA,EACdI,KAAKH,KAAOA,EACZG,KAAKF,QAAUA,EAGfI,OAAOC,eAAeH,KAAMR,EAASY,UACvC,EAsBK,IAAKC,EAAgB,SAAhBA,GAAgB,OAAhBA,EAAgB,gBAAhBA,EAAgB,kBAAhBA,EAAgB,sBAAhBA,EAAgB,oBAAhBA,CAAgB,MAOhBC,EAAc,SAAdA,GAAc,OAAdA,EAAc,sBAAdA,EAAc,sBAAdA,EAAc,8BAAdA,CAAc,MAMdC,EAAY,SAAZA,GAAY,OAAZA,EAAY,gBAAZA,EAAY,oBAAZA,EAAY,sBAAZA,CAAY,MA6LxB,MAYaC,EAZUC,EAAAA,GAAS,CAC9BC,IAAKD,EAAAA,KACLR,KAAMQ,EAAAA,KAAWE,IAAI,GAAGC,IAAI,KAC5BC,MAAOJ,EAAAA,KAAWI,QAClBC,MAAOL,EAAAA,KAAWM,WAClBnB,OAAQa,EAAAA,GAAaF,GACrBS,KAAMP,EAAAA,KACNQ,SAAUR,EAAAA,GAASA,EAAAA,KAAYA,EAAAA,MAAaM,WAC5CG,UAAWT,EAAAA,KAAWU,WACtBC,UAAWX,EAAAA,KAAWU,aAGmBE,OAAO,CAChDL,KAAMP,EAAAA,GAA8Ba,GAAgB,WAARA,GAA4B,UAARA,GAChEC,UAAWd,EAAAA,KAAWU,WAAWJ,aAmD7BS,GA7C0Bf,EAAAA,GAAS,CACvCC,IAAKD,EAAAA,KACLgB,OAAQhB,EAAAA,GAAS,CACfC,IAAKD,EAAAA,KACLR,KAAMQ,EAAAA,KACNI,MAAOJ,EAAAA,KAAWI,UAEpBa,SAAUjB,EAAAA,GAAS,CACjBC,IAAKD,EAAAA,KACLkB,aAAclB,EAAAA,OAEhBmB,eAAgBnB,EAAAA,GAAaH,GAC7BuB,UAAWpB,EAAAA,KAAWU,WACtBW,QAASrB,EAAAA,KAAWU,WAAWJ,WAC/BnB,OAAQa,EAAAA,GAAaJ,GACrB0B,iBAAkBtB,EAAAA,GACR,CACNuB,OAAQvB,EAAAA,KAAWwB,WACnBC,SAAUzB,EAAAA,KAAW0B,OAAO,GAC5BC,OAAQ3B,EAAAA,GACLa,GAAQ,CAAC,QAAS,SAAU,UAAW,YAAa,UAAUe,SAASC,OAAOhB,OAGlFP,WACHwB,YAAa9B,EAAAA,GAAQA,EAAAA,MACrB+B,MAAO/B,EAAAA,KAAWM,WAClBE,SAAUR,EAAAA,GAASA,EAAAA,KAAYA,EAAAA,MAAaM,WAC5CG,UAAWT,EAAAA,KAAWU,WACtBC,UAAWX,EAAAA,KAAWU,aAiBsC,CAAC,GAkBzDsB,EAdaC,EAAAA,EAAMC,OAAO,CAC5BC,QAAS,GAAGC,EAAAA,mBACZC,QAXoB,IAYpBC,QAAS,CACP,eAAgB,mBAChB,mBAAoB,kBAEtBC,iBAAiB,IAUrBP,EAAUQ,aAAaC,QAAQC,IAC5BC,IAEC,MAAMC,EAAY,GAAGD,EAAOE,UAAUF,EAAOG,MACzC/B,EAAmB6B,IACrB7B,EAAmB6B,GAAWG,OAAO,uCAIvC,MAAMC,EAASf,EAAAA,EAAMgB,YAAYD,SACjCjC,EAAmB6B,GAAaI,EAChCL,EAAOO,YAAcF,EAAOG,MAG5B,MAAMA,EAAQC,aAAaC,QAAQ,SAQnC,OAPIF,IACFR,EAAOL,QAAQgB,cAAgB,UAAUH,KAI3CR,EAAOL,QAAQ,oBAAqB,IAAIiB,MAAOC,UAAUC,WAElDd,GAERe,GACQC,QAAQC,OAAOF,IAK1B,IAAIG,EAAa,EAwIVC,eAAeC,EACpBpB,EACAqB,GAEA,IAOE,aALuBhC,EAAU,IAC5BW,EACHsB,eAAgBD,KAGFE,KAAKA,IACvB,CAAE,MAAOR,GACP,GAAIA,aAAiB3E,EACnB,MAAM2E,EAIR,GAAIzB,EAAAA,EAAMkC,aAAaT,GAAQ,CAAC,IAADU,EAAAC,EAC7B,MAAMlF,GAAuB,QAAdiF,EAAAV,EAAMY,gBAAQ,IAAAF,OAAA,EAAdA,EAAgBjF,SAAU,EACnC+E,GAAqB,QAAdG,EAAAX,EAAMY,gBAAQ,IAAAD,OAAA,EAAdA,EAAgBH,OAAQ,CAAC,EAEtC,MAAM,IAAInF,EACRmF,EAAKhF,SAAWwE,EAAMxE,QACtBC,EACA+E,EAAK9E,MAAQ,YACb8E,EAEJ,CAGA,MAAMK,EAAeb,GAA0B,kBAAVA,EACjC,CAAExE,QAAS2C,OAAO6B,IAClB,CAAExE,QAAS,6BAEf,MAAM,IAAIH,EACR,4BACA,EACA,gBACAwF,EAEJ,CACF,CAiBA,SAASC,EAAkBC,GACzB,MAAMC,EAAe,IAAIC,gBAYzB,OAVAlF,OAAOmF,QAAQH,GAAQI,QAAQ7I,IAAmB,IAAjBuC,EAAKuG,GAAM9I,OAC5B+I,IAAVD,GAAiC,OAAVA,IAEvBE,MAAMC,QAAQH,GAChBA,EAAMD,QAASK,GAASR,EAAaS,OAAO5G,EAAKsD,OAAOqD,KAExDR,EAAaS,OAAO5G,EAAKsD,OAAOiD,OAI7BJ,EAAajB,UACtB,CA/MAzB,EAAUQ,aAAa8B,SAAS5B,IAC7B4B,IAEC,MAAMc,EAAQd,EAAS3B,OAAOL,QAAQ,mBACtC,GAAI8C,EAAO,CACT,MAAMC,GAAW,IAAI9B,MAAOC,UAAY8B,SAASF,EAAO,IACxDG,QAAQC,MAAM,eAAelB,EAAS3B,OAAOG,oBAAoBuC,MACnE,CAGA,MAAMzC,EAAY,GAAG0B,EAAS3B,OAAOE,UAAUyB,EAAS3B,OAAOG,MAM/D,GALI/B,EAAmB6B,WACd7B,EAAmB6B,GAIxB0B,EAAS3B,OAAOsB,eAAgB,CAClC,MAAMwB,EAAanB,EAAS3B,OAAOsB,eAAeyB,UAAUpB,EAASJ,MACrE,IAAKuB,EAAWE,QAEd,OADAJ,QAAQ7B,MAAM,8BAA+B+B,EAAW/B,OACjDC,QAAQC,OACb,IAAI7E,EACF,0BACAuF,EAASnF,OACT,mBACAsG,EAAW/B,QAIjBY,EAASJ,KAAOuB,EAAWvB,IAC7B,CAEA,OAAOI,GAETR,UACE,MAAM8B,EAAkBlC,EAAMf,OAG9B,GAAIV,EAAAA,EAAM4D,SAASnC,GAEjB,OADA6B,QAAQO,IAAI,oBAAqBpC,EAAMxE,SAChCyE,QAAQC,OAAO,IAAI7E,EAAS,mBAAoB,EAAG,cAI5D,IAAK2E,EAAMY,SACT,OAAOX,QAAQC,OACb,IAAI7E,EACF2E,EAAMjB,QACF,mCACA,gCACJsC,EACA,kBAKN,MAAM,OAAE5F,EAAM,KAAE+E,GAASR,EAAMY,SAG/B,OAAQnF,GACN,KAAK,IAEHoG,QAAQ7B,MAAM,uCAEd,MACF,KAAK,IACH,OAAOC,QAAQC,OACb,IAAI7E,EACFmF,EAAKhF,SAAW,oDAChBC,EACA,cAGN,KAAK,IACH,OAAOwE,QAAQC,OACb,IAAI7E,EACFmF,EAAKhF,SAAW,qBAChBC,EACA,cAGN,KAAK,IACH,OAAOwE,QAAQC,OACb,IAAI7E,EACF,oBACAI,EACA,mBACA+E,EAAK6B,QAAU,CAAC,IAGtB,KAAK,IACH,OAAOpC,QAAQC,OACb,IAAI7E,EACFmF,EAAKhF,SAAW,4CAChBC,EACA,wBAGN,KAAK,IACL,KAAK,IACL,KAAK,IACL,KAAK,IAEH,GAAI0E,EAhKQ,IAgKqB+B,EAAgBI,OAAQ,CACvDnC,IACA,MAAMoC,EAjKI,IAiKkBC,KAAKC,IAAI,EAAGtC,EAAa,GAIrD,OAHA0B,QAAQO,IAAI,qBAAqBjC,WAAiCoC,OAElEL,EAAgBI,QAAS,EAClB,IAAIrC,QAASyC,GAClBC,WAAW,IAAMD,EAAQpE,EAAU4D,IAAmBK,GAE1D,CACA,OAAOtC,QAAQC,OACb,IAAI7E,EACFmF,EAAKhF,SAAW,uCAChBC,EACA,iBAGN,QACE,OAAOwE,QAAQC,OACb,IAAI7E,EACFmF,EAAKhF,SAAW,oBAChBC,EACA+E,EAAK9E,MAAQ,gBACb8E,OAoFZ,MAAMoC,EAAmDC,GACvDvG,EAAAA,GAAS,CACPkE,KAAMlE,EAAAA,GAAQuG,GACdC,KAAMxG,EAAAA,GAAS,CACbyG,MAAOzG,EAAAA,KACP0G,KAAM1G,EAAAA,KACN2G,MAAO3G,EAAAA,KACP4G,WAAY5G,EAAAA,SAKL6G,EAAqB,CAMhC,gBAAMC,GAA+E,IAApErC,EAAwBsC,UAAArF,OAAA,QAAAqD,IAAAgC,UAAA,GAAAA,UAAA,GAAG,CAAC,EAC3C,MAAM,KAAEL,EAAO,EAAC,MAAEC,EAAQ,GAAE,OAAEK,EAAM,OAAE7H,EAAM,KAAEoB,EAAI,WAAE0G,EAAU,OAAEC,EAAM,MAAEC,GAAU1C,EAalF,OAAOV,EACL,CACElB,OAAQ,MACRC,IAAK,kBAdW0B,EAAkB,CACpCkC,KAAMA,EAAKjD,WACXkD,MAAOA,EAAMlD,cACTuD,GAAU,CAAEA,aACZ7H,GAAU,CAAEA,aACZoB,GAAQ,CAAEA,WACV0G,GAAc,CAAEA,iBAChBC,GAAU,CAAEA,aACZC,GAAS,CAAEA,cAQfb,EAAwBvG,GAE5B,EAOA+D,iBAAsBsD,MAACC,GACdtD,EACL,CACElB,OAAQ,MACRC,IAAK,kBAAkBuE,KAEzBtH,GAWJ+D,mBAAwBwD,MACtBD,EACAlI,EACAoI,IAEOxD,EACL,CACElB,OAAQ,MACRC,IAAK,kBAAkBuE,WACvBnD,KAAM,CAAE/E,SAAQoI,WAElBxH,GASJ+D,aAAkB0D,MAACtD,GACVH,EACL,CACElB,OAAQ,OACRC,IAAK,iBACLoB,QAEFnE,GAUJ+D,aAAkB2D,MAACJ,EAAYnD,IACtBH,EACL,CACElB,OAAQ,MACRC,IAAK,kBAAkBuE,IACvBnD,QAEFnE,I,0CC7qBC,SAAS2H,EAAkB1L,GAKL,IALM,OACjCgF,EAAM,OACN2G,EAAM,QACNC,EAAO,eACPC,GACwB7L,EACxB,IAAKgF,EAAQ,OAAO,KAepB,OACE5E,EAAAA,EAAAA,KAACgB,EAAAA,GAAM,CAAC0K,KAAMH,EAAQI,aAAcH,EAAQvL,UAC1CyB,EAAAA,EAAAA,MAACD,EAAAA,GAAa,CAAC3B,UAAU,mBAAkBG,SAAA,EACzCD,EAAAA,EAAAA,KAAC4B,EAAAA,GAAY,CAAA3B,UACXD,EAAAA,EAAAA,KAAC8B,EAAAA,GAAW,CAAA7B,SAAC,sBAGfyB,EAAAA,EAAAA,MAAA,OAAK5B,UAAU,iBAAgBG,SAAA,EAC7ByB,EAAAA,EAAAA,MAAA,OAAK5B,UAAU,oCAAmCG,SAAA,EAChDD,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sBAAqBG,SAAE2E,EAAOxB,QAC5CpD,EAAAA,EAAAA,KAAC4L,EAAAA,EAAK,CAACC,QAvBK,CACpBC,OAAQ,UACRC,SAAU,YACVC,UAAW,eAoB2BpH,EAAO7B,QAAQ9C,SAC1C2E,EAAO7B,OAAOkJ,OAAO,GAAGC,cAAgBtH,EAAO7B,OAAOoJ,MAAM,SAIjEzK,EAAAA,EAAAA,MAAA,OAAK5B,UAAU,yBAAwBG,SAAA,EACrCyB,EAAAA,EAAAA,MAAA,OAAAzB,SAAA,EACED,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gCAA+BG,SAAC,WAC7CD,EAAAA,EAAAA,KAAA,KAAAC,SAAI2E,EAAOZ,WAEZY,EAAOX,QACNvC,EAAAA,EAAAA,MAAA,OAAAzB,SAAA,EACED,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gCAA+BG,SAAC,WAC7CD,EAAAA,EAAAA,KAAA,KAAAC,SAAI2E,EAAOX,YAGfvC,EAAAA,EAAAA,MAAA,OAAAzB,SAAA,EACED,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gCAA+BG,SAAC,kBAC7CD,EAAAA,EAAAA,KAAA,KAAAC,UAAImM,EAAAA,EAAAA,GAAO,IAAIjF,KAAKvC,EAAOP,WAAY,qBAEzC3C,EAAAA,EAAAA,MAAA,OAAAzB,SAAA,EACED,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gCAA+BG,SAAC,iBAC7CD,EAAAA,EAAAA,KAAA,KAAAC,SAAI2E,EAAOyH,YAAaD,EAAAA,EAAAA,GAAO,IAAIjF,KAAKvC,EAAOyH,YAAa,eAAiB,YAE/E3K,EAAAA,EAAAA,MAAA,OAAAzB,SAAA,EACED,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gCAA+BG,SAAC,wBAC7CD,EAAAA,EAAAA,KAAA,KAAAC,SAAI2E,EAAO0H,wBAEb5K,EAAAA,EAAAA,MAAA,OAAAzB,SAAA,EACED,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gCAA+BG,SAAC,uBAC7CD,EAAAA,EAAAA,KAAA,KAAAC,SAAI2E,EAAO2H,uBAEb7K,EAAAA,EAAAA,MAAA,OAAK5B,UAAU,aAAYG,SAAA,EACzBD,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gCAA+BG,SAAC,4BAC7CD,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wBAAuBG,UAnDxBkF,EAmDyCP,EAAO4H,sBAAwB,EAlDvF,IAAIC,KAAKC,aAAa,QAAS,CACpCC,MAAO,WACPtH,SAAU,QACT+G,OAAOjH,aAmDJzD,EAAAA,EAAAA,MAAA,OAAK5B,UAAU,gBAAeG,SAAA,EAC5BD,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2BAA0BG,SAAC,mBACzCD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,aAAYG,SACvB,CAAC,SAAU,WAAY,aAAuB2M,IAAK7J,IACnD/C,EAAAA,EAAAA,KAAC6M,EAAAA,EAAM,CAELhB,QAASjH,EAAO7B,SAAWA,EAAS,UAAY,UAChD+J,KAAK,KACLC,QAASA,IAAMtB,EAAe1I,GAC9BiK,SAAUpI,EAAO7B,SAAWA,EAAO9C,SAElC8C,EAAOkJ,OAAO,GAAGC,cAAgBnJ,EAAOoJ,MAAM,IAN1CpJ,iBA5DGoC,KA2E1B,CC5FO,SAAS8H,IAAe,IAADC,EAC5B,MAAOC,EAAYC,IAAiBC,EAAAA,EAAAA,UAAS,KACtC/C,EAAMgD,IAAWD,EAAAA,EAAAA,UAAS,IAC1BE,EAAgBC,IAAqBH,EAAAA,EAAAA,UAAgC,OACrEI,EAAeC,IAAoBL,EAAAA,EAAAA,WAAS,GAE7CM,GAAcC,EAAAA,EAAAA,OAEZC,OAAQC,IAAiBC,EAAAA,EAAAA,GAAY,CAC3CC,WAAYtG,UAAiE,IAA1D,GAAEuD,EAAE,OAAElI,GAA8CnD,QAC/D6K,EAAmBS,mBAAmBD,EAAIlI,IAElDkL,UAAWA,KACTN,EAAYO,kBAAkB,CAAEC,SAAU,CAAC,cAC3CC,EAAAA,EAAAA,IAAM,CACJC,MAAO,iBACPC,YAAa,kDAGjBC,QAASA,MACPH,EAAAA,EAAAA,IAAM,CACJC,MAAO,QACPC,YAAa,oDACbzC,QAAS,oBAoBP/D,KAAMI,EAAQ,UAAEsG,EAAS,MAAElH,EAAK,QAAEmH,IAAYC,EAAAA,EAAAA,GAAoC,CACxFP,SAAU,CAAC,UAAW,CAAE7D,OAAMC,MAvClB,GAuCyBK,OAAQuC,IAC7CwB,QAASA,IAAMlE,EAAmBC,WAAW,CAC3CJ,OACAC,MA1CU,GA2CVK,OAAQuC,EACRpK,YAAQ4F,MAKNiG,GAAUC,EAAAA,EAAAA,SAA0B,IAC3B,OAAR3G,QAAQ,IAARA,GAAAA,EAAUJ,KACRI,EAASJ,KAAK8E,IAAIhI,IAAM,IAC1BA,EACH0H,kBAAmB,EACnBC,iBAAkB,EAClBC,qBAAsB,KALI,GAO3B,CAACtE,IAGEsC,GAAqB,OAARtC,QAAQ,IAARA,GAAc,QAANgF,EAARhF,EAAUkC,YAAI,IAAA8C,OAAN,EAARA,EAAgB1C,aAAc,EAEjD,OAAIgE,GAEAxO,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCG,UACpDD,EAAAA,EAAAA,KAAC8O,EAAAA,EAAO,CAAChP,UAAU,2BAKrBwH,GAEA5F,EAAAA,EAAAA,MAAA,OAAK5B,UAAU,oBAAmBG,SAAA,EAChCD,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2BAA0BG,SAAC,4BACzCD,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6BAA4BG,SAAC,6BAC1CD,EAAAA,EAAAA,KAAC6M,EAAAA,EAAM,CAACE,QAASA,IAAM0B,IAAUxO,SAAC,cAMtCyB,EAAAA,EAAAA,MAAA,OAAK5B,UAAU,YAAWG,SAAA,EACxByB,EAAAA,EAAAA,MAAA,OAAK5B,UAAU,oCAAmCG,SAAA,EAChDD,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oCAAmCG,SAAC,wBAClDyB,EAAAA,EAAAA,MAACmL,EAAAA,EAAM,CAAA5M,SAAA,EACLD,EAAAA,EAAAA,KAACgC,EAAQ,CAAClC,UAAU,iBAAiB,oBAKzC4B,EAAAA,EAAAA,MAACqN,EAAAA,GAAI,CAAA9O,SAAA,EACHD,EAAAA,EAAAA,KAACgP,EAAAA,GAAU,CAAClP,UAAU,OAAMG,UAC1ByB,EAAAA,EAAAA,MAAA,OAAK5B,UAAU,oCAAmCG,SAAA,EAChDD,EAAAA,EAAAA,KAACiP,EAAAA,GAAS,CAAAhP,SAAC,iBACXyB,EAAAA,EAAAA,MAAA,OAAK5B,UAAU,gBAAeG,SAAA,EAC5BD,EAAAA,EAAAA,KAACkP,EAAAA,EAAM,CAACpP,UAAU,6DAClBE,EAAAA,EAAAA,KAACmP,EAAAA,EAAK,CACJC,KAAK,SACLC,YAAY,oBACZvP,UAAU,OACV4I,MAAOyE,EACPmC,SAAWC,GAAMnC,EAAcmC,EAAEC,OAAO9G,kBAKhDhH,EAAAA,EAAAA,MAAC+N,EAAAA,GAAW,CAAC3P,UAAU,MAAKG,SAAA,EAC1ByB,EAAAA,EAAAA,MAAChC,EAAAA,GAAK,CAAAO,SAAA,EACJD,EAAAA,EAAAA,KAACI,EAAAA,GAAW,CAAAH,UACVyB,EAAAA,EAAAA,MAACjB,EAAAA,GAAQ,CAAAR,SAAA,EACPD,EAAAA,EAAAA,KAACW,EAAAA,GAAS,CAAAV,SAAC,UACXD,EAAAA,EAAAA,KAACW,EAAAA,GAAS,CAAAV,SAAC,WACXD,EAAAA,EAAAA,KAACW,EAAAA,GAAS,CAAAV,SAAC,YACXD,EAAAA,EAAAA,KAACW,EAAAA,GAAS,CAAAV,SAAC,wBACXD,EAAAA,EAAAA,KAACW,EAAAA,GAAS,CAAAV,SAAC,qBACXD,EAAAA,EAAAA,KAACW,EAAAA,GAAS,CAAAV,SAAC,eACXD,EAAAA,EAAAA,KAACW,EAAAA,GAAS,CAACb,UAAU,YAAWG,SAAC,kBAGrCD,EAAAA,EAAAA,KAACM,EAAAA,GAAS,CAAAL,SACP2O,EAAQhC,IAAKhI,IACZlD,SAAAA,EAAAA,MAACjB,EAAAA,GAAQ,CAAAR,SAAA,EACPD,EAAAA,EAAAA,KAACa,EAAAA,GAAS,CAACf,UAAU,cAAaG,SAAE2E,EAAOxB,QAC3CpD,EAAAA,EAAAA,KAACa,EAAAA,GAAS,CAAAZ,SAAE2E,EAAOZ,SACnBhE,EAAAA,EAAAA,KAACa,EAAAA,GAAS,CAAAZ,UACRD,EAAAA,EAAAA,KAAC4L,EAAAA,EAAK,CAACC,QACLjH,EAAO7B,SAAWW,EAAagM,OAAS,UACxC9K,EAAO7B,SAAWW,EAAaiM,SAAW,YAAc,cACzD1P,SACE2E,EAAO7B,YAGZ/C,EAAAA,EAAAA,KAACa,EAAAA,GAAS,CAAAZ,SAAE2E,EAAO0H,qBACnBtM,EAAAA,EAAAA,KAACa,EAAAA,GAAS,CAAAZ,UA6DJkF,EA7DqBP,EAAO4H,sBAAwB,EA8DnE,IAAIC,KAAKC,aAAa,QAAS,CACpCC,MAAO,WACPtH,SAAU,QACT+G,OAAOjH,OAhEMnF,EAAAA,EAAAA,KAACa,EAAAA,GAAS,CAAAZ,UAAEmM,EAAAA,EAAAA,GAAO,IAAIjF,KAAKvC,EAAOP,WAAY,kBAC/CrE,EAAAA,EAAAA,KAACa,EAAAA,GAAS,CAAAZ,UACRyB,EAAAA,EAAAA,MAACmL,EAAAA,EAAM,CACLhB,QAAQ,QACRiB,KAAK,KACLC,QAASA,IApHFnI,KACzB4I,EAAkB5I,GAClB8I,GAAiB,IAkHgBkC,CAAkBhL,GAAQ3E,SAAA,EAEzCD,EAAAA,EAAAA,KAAC6P,EAAAA,EAAc,CAAC/P,UAAU,aAC1BE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,UAASG,SAAC,wBArBjB2E,EAAOf,KAyEtC,IAAwBsB,SA5CM,IAAnByJ,EAAQtJ,SACPtF,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yCAAwCG,SAAC,sBAK1DyB,EAAAA,EAAAA,MAAA,OAAK5B,UAAU,uDAAsDG,SAAA,EACnED,EAAAA,EAAAA,KAAC6M,EAAAA,EAAM,CACLhB,QAAQ,UACRiB,KAAK,KACLC,QAASA,IAAMO,EAAQwC,GAAKhG,KAAK/F,IAAI,EAAG+L,EAAI,IAC5C9C,SAAmB,IAAT1C,EAAWrK,SACtB,cAGDyB,EAAAA,EAAAA,MAAA,QAAM5B,UAAU,gCAA+BG,SAAA,CAAC,QACxCqK,EAAK,OAAKE,MAElBxK,EAAAA,EAAAA,KAAC6M,EAAAA,EAAM,CACLhB,QAAQ,UACRiB,KAAK,KACLC,QAASA,IAAMO,EAAQwC,GAAKA,EAAI,GAChC9C,SAAU1C,GAAQE,EAAWvK,SAC9B,kBAQNsN,IACCvN,EAAAA,EAAAA,KAACsL,EAAkB,CACjB1G,OAAQ2I,EACRhC,OAAQkC,EACRjC,QAASA,IAAMkC,GAAiB,GAChCjC,eA9JoB1I,IACtBwK,IACFO,EAAa,CACX7C,GAAIsC,EAAe1J,IACnBd,OAAQA,IAEV2K,GAAiB,SA6JvB,CAUA,S,iHC/NA,MAAMqB,EAAOpP,EAAAA,WAGX,CAAAC,EAA0BC,KAAG,IAA5B,UAAEC,KAAcC,GAAOH,EAAA,OACxBI,EAAAA,EAAAA,KAAA,OACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IACT,2DACAJ,MAEEC,MAGRgP,EAAK5O,YAAc,OAEnB,MAAM6O,EAAarP,EAAAA,WAGjB,CAAAU,EAA0BR,KAAG,IAA5B,UAAEC,KAAcC,GAAOM,EAAA,OACxBL,EAAAA,EAAAA,KAAA,OACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IAAG,gCAAiCJ,MAC3CC,MAGRiP,EAAW7O,YAAc,aAEzB,MAAM8O,EAAYtP,EAAAA,WAGhB,CAAAY,EAA0BV,KAAG,IAA5B,UAAEC,KAAcC,GAAOQ,EAAA,OACxBP,EAAAA,EAAAA,KAAA,MACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IACT,qDACAJ,MAEEC,MAGRkP,EAAU9O,YAAc,YAExB,MAAM4P,EAAkBpQ,EAAAA,WAGtB,CAAAa,EAA0BX,KAAG,IAA5B,UAAEC,KAAcC,GAAOS,EAAA,OACxBR,EAAAA,EAAAA,KAAA,KACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IAAG,gCAAiCJ,MAC3CC,MAGRgQ,EAAgB5P,YAAc,kBAE9B,MAAMsP,EAAc9P,EAAAA,WAGlB,CAAAe,EAA0Bb,KAAG,IAA5B,UAAEC,KAAcC,GAAOW,EAAA,OACxBV,EAAAA,EAAAA,KAAA,OAAKH,IAAKA,EAAKC,WAAWI,EAAAA,EAAAA,IAAG,WAAYJ,MAAgBC,MAE3D0P,EAAYtP,YAAc,cAE1B,MAAM6P,EAAarQ,EAAAA,WAGjB,CAAAiB,EAA0Bf,KAAG,IAA5B,UAAEC,KAAcC,GAAOa,EAAA,OACxBZ,EAAAA,EAAAA,KAAA,OACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IAAG,6BAA8BJ,MACxCC,MAGRiQ,EAAW7P,YAAc,Y,kCC/DnB,MAAA+O,GAASjN,E,QAAAA,GAAiB,SAAU,CACxC,CAAC,SAAU,CAAEG,GAAI,KAAMC,GAAI,KAAMC,EAAG,IAAKH,IAAK,WAC9C,CAAC,OAAQ,CAAED,EAAG,iBAAkBC,IAAK,Y,0DCCvC,IAAI8N,EAAQ,EA+BZ,MAAMC,EAAgB,IAAIC,IAEpBC,EAAoBC,IACxB,GAAIH,EAAcI,IAAID,GACpB,OAGF,MAAMpK,EAAUgE,WAAW,KACzBiG,EAAcK,OAAOF,GACrBG,EAAS,CACPpB,KAAM,eACNiB,QAASA,KAEV,KAEHH,EAAcO,IAAIJ,EAASpK,IAGhByK,EAAUA,CAACC,EAAcC,KACpC,OAAQA,EAAOxB,MACb,IAAK,YACH,MAAO,IACFuB,EACHE,OAAQ,CAACD,EAAOxC,SAAUuC,EAAME,QAAQ1E,MAAM,EAAG,IAGrD,IAAK,eACH,MAAO,IACFwE,EACHE,OAAQF,EAAME,OAAOjE,IAAKkE,GACxBA,EAAE7F,KAAO2F,EAAOxC,MAAMnD,GAAK,IAAK6F,KAAMF,EAAOxC,OAAU0C,IAI7D,IAAK,gBAAiB,CACpB,MAAM,QAAET,GAAYO,EAYpB,OARIP,EACFD,EAAiBC,GAEjBM,EAAME,OAAOpI,QAAS2F,IACpBgC,EAAiBhC,EAAMnD,MAIpB,IACF0F,EACHE,OAAQF,EAAME,OAAOjE,IAAKkE,GACxBA,EAAE7F,KAAOoF,QAAuB1H,IAAZ0H,EAChB,IACKS,EACHpF,MAAM,GAERoF,GAGV,CACA,IAAK,eACH,YAAuBnI,IAAnBiI,EAAOP,QACF,IACFM,EACHE,OAAQ,IAGL,IACFF,EACHE,OAAQF,EAAME,OAAOE,OAAQD,GAAMA,EAAE7F,KAAO2F,EAAOP,YAKrDW,EAA2C,GAEjD,IAAIC,EAAqB,CAAEJ,OAAQ,IAEnC,SAASL,EAASI,GAChBK,EAAcP,EAAQO,EAAaL,GACnCI,EAAUvI,QAASyI,IACjBA,EAASD,IAEb,CAIA,SAAS7C,EAAKxO,GAAuB,OAAjBG,GAAcH,EAChC,MAAMqL,GAnHNgF,GAASA,EAAQ,GAAKkB,OAAOC,iBACtBnB,EAAM5I,YAyHPgK,EAAUA,IAAMb,EAAS,CAAEpB,KAAM,gBAAiBiB,QAASpF,IAcjE,OAZAuF,EAAS,CACPpB,KAAM,YACNhB,MAAO,IACFrO,EACHkL,KACAS,MAAM,EACNC,aAAeD,IACRA,GAAM2F,QAKV,CACLpG,GAAIA,EACJoG,UACAC,OAtBcvR,GACdyQ,EAAS,CACPpB,KAAM,eACNhB,MAAO,IAAKrO,EAAOkL,QAqBzB,CAEA,SAASsG,IACP,MAAOZ,EAAOa,GAAY7R,EAAAA,SAAsBsR,GAYhD,OAVAtR,EAAAA,UAAgB,KACdqR,EAAUS,KAAKD,GACR,KACL,MAAME,EAAQV,EAAUW,QAAQH,GAC5BE,GAAS,GACXV,EAAUY,OAAOF,EAAO,KAG3B,CAACf,IAEG,IACFA,EACHvC,QACAiD,QAAUhB,GAAqBG,EAAS,CAAEpB,KAAM,gBAAiBiB,YAErE,C,kCCvKM,MAAAR,GAAiB5N,E,QAAAA,GAAiB,iBAAkB,CACxD,CAAC,SAAU,CAAEG,GAAI,KAAMC,GAAI,KAAMC,EAAG,IAAKH,IAAK,WAC9C,CAAC,SAAU,CAAEC,GAAI,KAAMC,GAAI,KAAMC,EAAG,IAAKH,IAAK,WAC9C,CAAC,SAAU,CAAEC,GAAI,IAAKC,GAAI,KAAMC,EAAG,IAAKH,IAAK,Y,0ECX/C,MAAM0P,GAAgBC,EAAAA,EAAAA,GACpB,yKACA,CACEC,SAAU,CACRlG,QAAS,CACPmG,QACE,4EACFC,UACE,kFACFC,YACE,wFACF3I,QACE,mEACF4I,QACE,qEACFC,QAAS,oBAGbC,gBAAiB,CACfxG,QAAS,aASf,SAASD,EAAKhM,GAAgD,IAA/C,UAAEE,EAAS,QAAE+L,KAAY9L,GAAmBH,EACzD,OACEI,EAAAA,EAAAA,KAAA,OAAKF,WAAWI,EAAAA,EAAAA,IAAG2R,EAAc,CAAEhG,YAAY/L,MAAgBC,GAEnE,C,sFC/BA,MAAMuS,GAAiBR,EAAAA,EAAAA,GACrB,yRACA,CACEC,SAAU,CACRlG,QAAS,CACPmG,QAAS,yDACTE,YACE,qEACFE,QACE,iFACFH,UACE,+DACFM,MAAO,+CACPC,KAAM,mDAER1F,KAAM,CACJkF,QAAS,iBACTS,GAAI,sBACJC,GAAI,uBACJC,KAAM,cAGVN,gBAAiB,CACfxG,QAAS,UACTiB,KAAM,aAWND,EAASlN,EAAAA,WACb,CAAAC,EAA0DC,KAAS,IAAlE,UAAEC,EAAS,QAAE+L,EAAO,KAAEiB,EAAI,QAAE8F,GAAU,KAAU7S,GAAOH,EACtD,MAAMiT,EAAOD,EAAUE,EAAAA,GAAO,SAC9B,OACE9S,EAAAA,EAAAA,KAAC6S,EAAI,CACH/S,WAAWI,EAAAA,EAAAA,IAAGoS,EAAe,CAAEzG,UAASiB,OAAMhN,eAC9CD,IAAKA,KACDE,MAKZ8M,EAAO1M,YAAc,Q,mEC9CrB,MAAMgP,EAAQxP,EAAAA,WACZ,CAAAC,EAAgCC,KAAS,IAAxC,UAAEC,EAAS,KAAEsP,KAASrP,GAAOH,EAC5B,OACEI,EAAAA,EAAAA,KAAA,SACEoP,KAAMA,EACNtP,WAAWI,EAAAA,EAAAA,IACT,+VACAJ,GAEFD,IAAKA,KACDE,MAKZoP,EAAMhP,YAAc,O", "sources": ["components/ui/table.tsx", "components/ui/dialog.tsx", "../node_modules/lucide-react/src/icons/user-plus.ts", "services/traderService.ts", "components/admin/TraderDetailsModal.tsx", "pages/admin/TradersPage.tsx", "components/ui/card.tsx", "../node_modules/lucide-react/src/icons/search.ts", "components/ui/use-toast.ts", "../node_modules/lucide-react/src/icons/more-horizontal.ts", "components/ui/badge.tsx", "components/ui/button.tsx", "components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst Table = React.forwardRef<\n  HTMLTableElement,\n  React.HTMLAttributes<HTMLTableElement>\n>(({ className, ...props }, ref) => (\n  <div className=\"w-full overflow-auto\">\n    <table\n      ref={ref}\n      className={cn(\"w-full caption-bottom text-sm\", className)}\n      {...props}\n    />\n  </div>\n))\nTable.displayName = \"Table\"\n\nconst TableHeader = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <thead ref={ref} className={cn(\"[&_tr]:border-b\", className)} {...props} />\n))\nTableHeader.displayName = \"TableHeader\"\n\nconst TableBody = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tbody\n    ref={ref}\n    className={cn(\"[&_tr:last-child]:border-0\", className)}\n    {...props}\n  />\n))\nTableBody.displayName = \"TableBody\"\n\nconst TableFooter = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tfoot\n    ref={ref}\n    className={cn(\"bg-primary font-medium text-primary-foreground\", className)}\n    {...props}\n  />\n))\nTableFooter.displayName = \"TableFooter\"\n\nconst TableRow = React.forwardRef<\n  HTMLTableRowElement,\n  React.HTMLAttributes<HTMLTableRowElement>\n>(({ className, ...props }, ref) => (\n  <tr\n    ref={ref}\n    className={cn(\n      \"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nTableRow.displayName = \"TableRow\"\n\nconst TableHead = React.forwardRef<\n  HTMLTableCellElement,\n  React.ThHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <th\n    ref={ref}\n    className={cn(\n      \"h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0\",\n      className\n    )}\n    {...props}\n  />\n))\nTableHead.displayName = \"TableHead\"\n\nconst TableCell = React.forwardRef<\n  HTMLTableCellElement,\n  React.TdHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <td\n    ref={ref}\n    className={cn(\"p-4 align-middle [&:has([role=checkbox])]:pr-0\", className)}\n    {...props}\n  />\n))\nTableCell.displayName = \"TableCell\"\n\nconst TableCaption = React.forwardRef<\n  HTMLTableCaptionElement,\n  React.HTMLAttributes<HTMLTableCaptionElement>\n>(({ className, ...props }, ref) => (\n  <caption\n    ref={ref}\n    className={cn(\"mt-4 text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nTableCaption.displayName = \"TableCaption\"\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n", "import * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { X } from \"lucide-react\"\nimport { cn } from \"../../lib/utils\"\n\nconst Dialog = DialogPrimitive.Root\n\nconst DialogTrigger = DialogPrimitive.Trigger\n\ninterface DialogPortalProps extends DialogPrimitive.DialogPortalProps {\n  className?: string;\n  children?: React.ReactNode;\n}\n\nconst DialogPortal = ({\n  className,\n  children,\n  ...props\n}: DialogPortalProps) => {\n  const { container, forceMount, ...restProps } = props;\n  const portalProps = { \n    container,\n    forceMount: forceMount as true | undefined \n  };\n  return (\n    <DialogPrimitive.Portal {...portalProps}>\n      <div className={cn(\n        \"fixed inset-0 z-50 flex items-start justify-center sm:items-center\",\n        className\n      )}>\n        {children}\n      </div>\n    </DialogPrimitive.Portal>\n  )\n}\nDialogPortal.displayName = DialogPrimitive.Portal.displayName\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/50 backdrop-blur-sm transition-all duration-100 data-[state=closed]:animate-out data-[state=closed]:fade-out data-[state=open]:fade-in\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed z-50 grid w-full gap-4 rounded-b-lg bg-white p-6 animate-in data-[state=open]:fade-in-90 data-[state=open]:slide-in-from-bottom-10 sm:max-w-lg sm:rounded-lg sm:zoom-in-90 data-[state=open]:sm:slide-in-from-bottom-0\",\n        \"dark:bg-gray-900\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-gray-100 dark:focus:ring-gray-400 dark:focus:ring-offset-gray-900 dark:data-[state=open]:bg-gray-800\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n))\nDialogContent.displayName = DialogPrimitive.Content.displayName\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold text-gray-900\",\n      \"dark:text-gray-50\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = DialogPrimitive.Title.displayName\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-gray-500\", \"dark:text-gray-400\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = DialogPrimitive.Description.displayName\n\nexport {\n  Dialog,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n}\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name UserPlus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMjF2LTJhNCA0IDAgMCAwLTQtNEg2YTQgNCAwIDAgMC00IDR2MiIgLz4KICA8Y2lyY2xlIGN4PSI5IiBjeT0iNyIgcj0iNCIgLz4KICA8bGluZSB4MT0iMTkiIHgyPSIxOSIgeTE9IjgiIHkyPSIxNCIgLz4KICA8bGluZSB4MT0iMjIiIHgyPSIxNiIgeTE9IjExIiB5Mj0iMTEiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/user-plus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst UserPlus = createLucideIcon('UserPlus', [\n  ['path', { d: 'M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2', key: '1yyitq' }],\n  ['circle', { cx: '9', cy: '7', r: '4', key: 'nufk8' }],\n  ['line', { x1: '19', x2: '19', y1: '8', y2: '14', key: '1bvyxn' }],\n  ['line', { x1: '22', x2: '16', y1: '11', y2: '11', key: '1shjgl' }],\n]);\n\nexport default UserPlus;\n", "import axios, { AxiosInstance, AxiosRequestConfig, CancelTokenSource, InternalAxiosRequestConfig } from 'axios';\nimport { API_BASE_URL } from '../config';\nimport { z } from 'zod';\n\n// Base API Response Schema\nexport const ApiResponseSchema = <T>(dataSchema: z.ZodType<T>) =>\n  z.object({\n    success: z.boolean(),\n    message: z.string().optional(),\n    data: dataSchema,\n    meta: z\n      .object({\n        total: z.number().optional(),\n        page: z.number().optional(),\n        limit: z.number().optional(),\n        totalPages: z.number().optional(),\n      })\n      .optional(),\n  });\n\nexport type ApiResponseType<T> = z.infer<ReturnType<typeof ApiResponseSchema<z.ZodType<T>>>>;\n\n// Common Types\nexport type PaginationParams = {\n  page?: number;\n  limit?: number;\n  sortBy?: string;\n  order?: 'asc' | 'desc';\n};\n\nexport type SearchParams = {\n  search?: string;\n  [key: string]: any;\n};\n\n// Error Types\nexport class ApiError extends Error {\n  status?: number;\n  code?: string;\n  details?: unknown;\n\n  constructor(message: string, status?: number, code?: string, details?: Record<string, unknown>) {\n    super(message);\n    this.name = 'ApiError';\n    this.status = status;\n    this.code = code;\n    this.details = details;\n    \n    // Set the prototype explicitly for TypeScript\n    Object.setPrototypeOf(this, ApiError.prototype);\n  }\n}\n\nexport type NetworkError = {\n  type: 'network';\n  message: string;\n  code?: string;\n};\n\nexport type ValidationError = {\n  type: 'validation';\n  message: string;\n  fields: Record<string, string[]>;\n};\n\ntype ErrorResponse = {\n  message: string;\n  code?: string;\n  errors?: Record<string, string[]>;\n};\n\n// Enums\nexport enum AssignmentStatus {\n  ACTIVE = 'active',\n  PENDING = 'pending',\n  COMPLETED = 'completed',\n  INACTIVE = 'inactive',\n}\n\nexport enum AssignmentType {\n  PERMANENT = 'permanent',\n  TEMPORARY = 'temporary',\n  PROJECT_BASED = 'project_based',\n}\n\nexport enum TraderStatus {\n  ACTIVE = 'active',\n  INACTIVE = 'inactive',\n  SUSPENDED = 'suspended',\n}\n\n// Domain Models\nexport interface User {\n  _id: string;\n  name: string;\n  email: string;\n  phone?: string;\n  status: TraderStatus;\n  role: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// Trader interface is now inferred from TraderSchema\n\nexport interface Merchant {\n  _id: string;\n  businessName: string;\n  email: string;\n  phone?: string;\n  address?: {\n    street: string;\n    city: string;\n    state: string;\n    country: string;\n    postalCode: string;\n  };\n  status: 'active' | 'inactive' | 'suspended';\n  metadata?: Record<string, unknown>;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Assignment {\n  _id: string;\n  trader: Pick<Trader, '_id' | 'name' | 'email'>;\n  merchant: Pick<Merchant, '_id' | 'businessName'>;\n  assignmentType: AssignmentType;\n  startDate: string;\n  endDate?: string;\n  status: AssignmentStatus;\n  collectionTarget?: {\n    amount: number;\n    currency: string;\n    period: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';\n  };\n  permissions: string[];\n  notes?: string;\n  metadata?: Record<string, unknown>;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface PerformanceMetrics {\n  totalCollections: number;\n  totalAmount: number;\n  currency: string;\n  averageCollectionTime: number; // in minutes\n  successRate: number; // percentage\n  metricsByPeriod: Array<{\n    period: string;\n    count: number;\n    amount: number;\n  }>;\n  topMerchants: Array<{\n    merchantId: string;\n    merchantName: string;\n    count: number;\n    amount: number;\n  }>;\n}\n\nexport interface DashboardStats {\n  totalTraders: number;\n  activeTraders: number;\n  totalMerchants: number;\n  activeAssignments: number;\n  pendingAssignments: number;\n  totalCollections: number;\n  totalAmount: number;\n  currency: string;\n  recentActivity: Array<{\n    id: string;\n    type: string;\n    description: string;\n    timestamp: string;\n    userId: string;\n    userName: string;\n  }>;\n}\n\n// Request/Response Types\nexport interface PaginatedResponse<T> {\n  data: T[];\n  meta: {\n    total: number;\n    page: number;\n    limit: number;\n    totalPages: number;\n  };\n}\n\nexport interface GetTradersParams extends PaginationParams, SearchParams {\n  status?: TraderStatus;\n  role?: string;\n  merchantId?: string;\n}\n\nexport interface GetAssignmentsParams extends PaginationParams, SearchParams {\n  status?: AssignmentStatus | AssignmentStatus[];\n  traderId?: string;\n  merchantId?: string;\n  startDate?: string;\n  endDate?: string;\n  assignmentType?: AssignmentType;\n}\n\nexport interface CreateTraderData {\n  name: string;\n  email: string;\n  phone?: string;\n  password: string;\n  role?: 'trader' | 'admin';\n  metadata?: Record<string, unknown>;\n}\n\nexport interface UpdateTraderData {\n  name?: string;\n  email?: string;\n  phone?: string;\n  status?: TraderStatus;\n  metadata?: Record<string, unknown>;\n}\n\nexport interface CreateAssignmentData {\n  traderId: string;\n  merchantId: string;\n  assignmentType: AssignmentType;\n  startDate: string;\n  endDate?: string;\n  status?: AssignmentStatus;\n  collectionTarget?: {\n    amount: number;\n    currency: string;\n    period: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';\n  };\n  permissions?: string[];\n  notes?: string;\n  metadata?: Record<string, unknown>;\n}\n\nexport interface UpdateAssignmentData extends Partial<CreateAssignmentData> {\n  status?: AssignmentStatus;\n  notes?: string;\n  metadata?: Record<string, unknown>;\n}\n\nexport interface UpdateAssignmentStatusData {\n  status: AssignmentStatus;\n  notes?: string;\n}\n\nexport interface BulkAssignTradersData {\n  traderIds: string[];\n  merchantIds: string[];\n  assignmentType: AssignmentType;\n  startDate: string;\n  endDate?: string;\n  permissions?: string[];\n  notes?: string;\n}\n\nexport interface GenerateReportParams {\n  startDate: string;\n  endDate: string;\n  format?: 'csv' | 'pdf' | 'xlsx';\n  traderId?: string;\n  merchantId?: string;\n  status?: AssignmentStatus | AssignmentStatus[];\n  assignmentType?: AssignmentType;\n}\n\n// Validation Schemas\n// Base user schema with common fields\nconst BaseUserSchema = z.object({\n  _id: z.string(),\n  name: z.string().min(2).max(100),\n  email: z.string().email(),\n  phone: z.string().optional(),\n  status: z.nativeEnum(TraderStatus),\n  role: z.string(),\n  metadata: z.record(z.string(), z.unknown()).optional(),\n  createdAt: z.string().datetime(),\n  updatedAt: z.string().datetime(),\n});\n\nexport const TraderSchema = BaseUserSchema.extend({\n  role: z.custom<'trader' | 'admin'>((val) => val === 'trader' || val === 'admin'),\n  lastLogin: z.string().datetime().optional(),\n});\n\n// Type for the trader schema\nexport type Trader = z.infer<typeof TraderSchema>;\n\nexport const AssignmentSchema = z.object({\n  _id: z.string(),\n  trader: z.object({\n    _id: z.string(),\n    name: z.string(),\n    email: z.string().email(),\n  }),\n  merchant: z.object({\n    _id: z.string(),\n    businessName: z.string(),\n  }),\n  assignmentType: z.nativeEnum(AssignmentType),\n  startDate: z.string().datetime(),\n  endDate: z.string().datetime().optional(),\n  status: z.nativeEnum(AssignmentStatus),\n  collectionTarget: z\n    .object({\n      amount: z.number().positive(),\n      currency: z.string().length(3),\n      period: z.custom<'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly'>(\n        (val) => ['daily', 'weekly', 'monthly', 'quarterly', 'yearly'].includes(String(val))\n      ),\n    })\n    .optional(),\n  permissions: z.array(z.string()),\n  notes: z.string().optional(),\n  metadata: z.record(z.string(), z.unknown()).optional(),\n  createdAt: z.string().datetime(),\n  updatedAt: z.string().datetime(),\n});\n\n// Extend AxiosRequestConfig to include our custom properties\ndeclare module 'axios' {\n  export interface AxiosRequestConfig {\n    responseSchema?: z.ZodType<unknown>;\n    _retry?: boolean;\n  }\n}\n\n// API Client Configuration\nconst DEFAULT_TIMEOUT = 10000; // 10 seconds\nconst MAX_RETRIES = 3;\nconst RETRY_DELAY = 1000; // 1 second\n\n// Request cancellation\nconst cancelTokenSources: Record<string, CancelTokenSource> = {};\n\n// Create a custom axios instance with proper typing\nconst createAxiosInstance = (): AxiosInstance => {\n  const instance = axios.create({\n    baseURL: `${API_BASE_URL}/api/v1/trader`, // This already includes /api/v1/trader\n    timeout: DEFAULT_TIMEOUT,\n    headers: {\n      'Content-Type': 'application/json',\n      'X-Requested-With': 'XMLHttpRequest',\n    },\n    withCredentials: true, // For CSRF protection\n  });\n\n  return instance;\n};\n\n// Create axios instance with default config\nconst traderApi = createAxiosInstance();\n\n// Request interceptor for auth token\ntraderApi.interceptors.request.use(\n  (config: InternalAxiosRequestConfig) => {\n    // Cancel previous request if it exists\n    const requestId = `${config.method}-${config.url}`;\n    if (cancelTokenSources[requestId]) {\n      cancelTokenSources[requestId].cancel('Request canceled - new request made');\n    }\n\n    // Create new cancel token\n    const source = axios.CancelToken.source();\n    cancelTokenSources[requestId] = source;\n    config.cancelToken = source.token;\n\n    // Add auth token if exists\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n\n    // Add request timestamp\n    config.headers['X-Request-Start'] = new Date().getTime().toString();\n\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor for handling errors and retries\nlet retryCount = 0;\n\ntraderApi.interceptors.response.use(\n  (response) => {\n    // Calculate request duration\n    const start = response.config.headers['X-Request-Start'];\n    if (start) {\n      const duration = new Date().getTime() - parseInt(start, 10);\n      console.debug(`API Request ${response.config.url} completed in ${duration}ms`);\n    }\n\n    // Clean up cancel token\n    const requestId = `${response.config.method}-${response.config.url}`;\n    if (cancelTokenSources[requestId]) {\n      delete cancelTokenSources[requestId];\n    }\n\n    // Validate response against schema if available\n    if (response.config.responseSchema) {\n      const validation = response.config.responseSchema.safeParse(response.data);\n      if (!validation.success) {\n        console.error('Response validation failed:', validation.error);\n        return Promise.reject(\n          new ApiError(\n            'Invalid response format',\n            response.status,\n            'INVALID_RESPONSE',\n            validation.error as unknown as Record<string, unknown>\n          )\n        );\n      }\n      response.data = validation.data;\n    }\n\n    return response;\n  },\n  async (error) => {\n    const originalRequest = error.config;\n\n    // Handle cancellation\n    if (axios.isCancel(error)) {\n      console.log('Request canceled:', error.message);\n      return Promise.reject(new ApiError('Request canceled', 0, 'CANCELLED'));\n    }\n\n    // Handle network errors\n    if (!error.response) {\n      return Promise.reject(\n        new ApiError(\n          error.request\n            ? 'No response received from server'\n            : 'Error setting up request',\n          undefined,\n          'NETWORK_ERROR'\n        )\n      );\n    }\n\n    const { status, data } = error.response;\n\n    // Handle specific error statuses\n    switch (status) {\n      case 401: // Unauthorized\n        // Handle token refresh here if needed\n        console.error('Unauthorized - redirecting to login');\n        // Redirect to login or refresh token\n        break;\n      case 403: // Forbidden\n        return Promise.reject(\n          new ApiError(\n            data.message || 'You do not have permission to perform this action',\n            status,\n            'FORBIDDEN'\n          )\n        );\n      case 404: // Not Found\n        return Promise.reject(\n          new ApiError(\n            data.message || 'Resource not found',\n            status,\n            'NOT_FOUND'\n          )\n        );\n      case 422: // Validation Error\n        return Promise.reject(\n          new ApiError(\n            'Validation failed',\n            status,\n            'VALIDATION_ERROR',\n            data.errors || {}\n          )\n        );\n      case 429: // Too Many Requests\n        return Promise.reject(\n          new ApiError(\n            data.message || 'Too many requests, please try again later',\n            status,\n            'RATE_LIMIT_EXCEEDED'\n          )\n        );\n      case 500: // Internal Server Error\n      case 502: // Bad Gateway\n      case 503: // Service Unavailable\n      case 504: // Gateway Timeout\n        // Retry logic for server errors\n        if (retryCount < MAX_RETRIES && !originalRequest._retry) {\n          retryCount++;\n          const delay = RETRY_DELAY * Math.pow(2, retryCount - 1);\n          console.log(`Retrying request (${retryCount}/${MAX_RETRIES}) in ${delay}ms`);\n          \n          originalRequest._retry = true;\n          return new Promise((resolve) =>\n            setTimeout(() => resolve(traderApi(originalRequest)), delay)\n          );\n        }\n        return Promise.reject(\n          new ApiError(\n            data.message || 'Server error, please try again later',\n            status,\n            'SERVER_ERROR'\n          )\n        );\n      default:\n        return Promise.reject(\n          new ApiError(\n            data.message || 'An error occurred',\n            status,\n            data.code || 'UNKNOWN_ERROR',\n            data\n          )\n        );\n    }\n  }\n);\n\n// Helper function to make API requests with proper typing\nexport async function apiRequest<T>(\n  config: AxiosRequestConfig,\n  schema?: z.ZodType<T>\n): Promise<T> {\n  try {\n    // Add response schema for validation if provided\n    const response = await traderApi({\n      ...config,\n      responseSchema: schema,\n    });\n    \n    return response.data.data;\n  } catch (error) {\n    if (error instanceof ApiError) {\n      throw error;\n    }\n    \n    // Handle Axios errors\n    if (axios.isAxiosError(error)) {\n      const status = error.response?.status || 0;\n      const data = error.response?.data || {};\n      \n      throw new ApiError(\n        data.message || error.message,\n        status,\n        data.code || 'API_ERROR',\n        data\n      );\n    }\n    \n    // Handle unknown errors\n    const errorDetails = error && typeof error === 'object' \n      ? { message: String(error) }\n      : { message: 'An unknown error occurred' };\n      \n    throw new ApiError(\n      'An unknown error occurred',\n      0,\n      'UNKNOWN_ERROR',\n      errorDetails\n    );\n  }\n}\n\n// API Response type for backward compatibility\nexport type ApiResponse<T> = {\n  data: T;\n  message?: string;\n  success: boolean;\n};\n\n// Helper function to cancel pending requests\nexport function cancelPendingRequests() {\n  Object.values(cancelTokenSources).forEach((source) => {\n    source.cancel('Operation canceled by user');\n  });\n}\n\n// Helper function to create query string from params\nfunction createQueryString(params: Record<string, string | number | boolean | (string | number | boolean)[] | undefined>): string {\n  const searchParams = new URLSearchParams();\n  \n  Object.entries(params).forEach(([key, value]) => {\n    if (value === undefined || value === null) return;\n    \n    if (Array.isArray(value)) {\n      value.forEach((item) => searchParams.append(key, String(item)));\n    } else {\n      searchParams.append(key, String(value));\n    }\n  });\n  \n  return searchParams.toString();\n}\n\n// Schema for paginated responses\nconst PaginatedResponseSchema = <T extends z.ZodTypeAny>(itemSchema: T) =>\n  z.object({\n    data: z.array(itemSchema),\n    meta: z.object({\n      total: z.number(),\n      page: z.number(),\n      limit: z.number(),\n      totalPages: z.number(),\n    }),\n  });\n\n// Admin-specific trader service\nexport const traderAdminService = {\n  /**\n   * Get all traders with pagination and filters\n   * @param params Query parameters for filtering and pagination\n   * @returns Paginated list of traders\n   */\n  async getTraders(params: GetTradersParams = {}): Promise<PaginatedResponse<Trader>> {\n    const { page = 1, limit = 10, search, status, role, merchantId, sortBy, order } = params;\n    \n    const queryParams = createQueryString({\n      page: page.toString(),\n      limit: limit.toString(),\n      ...(search && { search }),\n      ...(status && { status }),\n      ...(role && { role }),\n      ...(merchantId && { merchantId }),\n      ...(sortBy && { sortBy }),\n      ...(order && { order }),\n    });\n\n    return apiRequest<PaginatedResponse<Trader>>(\n      {\n        method: 'GET',\n        url: `/admin/traders?${queryParams}`,\n      },\n      PaginatedResponseSchema(TraderSchema)\n    );\n  },\n\n  /**\n   * Get trader details by ID\n   * @param id Trader ID\n   * @returns Trader details\n   */\n  async getTraderDetails(id: string): Promise<Trader> {\n    return apiRequest<Trader>(\n      {\n        method: 'GET',\n        url: `/admin/traders/${id}`,\n      },\n      TraderSchema\n    );\n  },\n\n  /**\n   * Update trader status\n   * @param id Trader ID\n   * @param status New status\n   * @param reason Optional reason for status change\n   * @returns Updated trader\n   */\n  async updateTraderStatus(\n    id: string, \n    status: TraderStatus, \n    reason?: string\n  ): Promise<Trader> {\n    return apiRequest<Trader>(\n      {\n        method: 'PUT',\n        url: `/admin/traders/${id}/status`,\n        data: { status, reason },\n      },\n      TraderSchema\n    );\n  },\n\n  /**\n   * Create a new trader\n   * @param data Trader data\n   * @returns Created trader\n   */\n  async createTrader(data: CreateTraderData): Promise<Trader> {\n    return apiRequest<Trader>(\n      {\n        method: 'POST',\n        url: '/admin/traders',\n        data,\n      },\n      TraderSchema\n    );\n  },\n\n  /**\n   * Update trader details\n   * @param id Trader ID\n   * @param data Data to update\n   * @returns Updated trader\n   */\n  async updateTrader(id: string, data: UpdateTraderData): Promise<Trader> {\n    return apiRequest<Trader>(\n      {\n        method: 'PUT',\n        url: `/admin/traders/${id}`,\n        data,\n      },\n      TraderSchema\n    );\n  },\n};\n\nexport const traderService = {\n  /**\n   * Get current trader's assignments\n   * @param status Optional status filter\n   * @returns List of assignments for the current trader\n   */\n  async getMyAssignments(status?: AssignmentStatus | AssignmentStatus[]): Promise<Assignment[]> {\n    const queryParams: Record<string, string | string[]> = {};\n    \n    if (status) {\n      queryParams.status = Array.isArray(status) ? status : [status];\n    }\n    \n    const params = createQueryString(queryParams);\n\n    return apiRequest<Assignment[]>(\n      {\n        method: 'GET',\n        url: `/traders/me/assignments${params ? `?${params}` : ''}`,\n        responseSchema: z.array(AssignmentSchema).optional()\n      },\n      AssignmentSchema.array()\n    );\n  },\n\n  /**\n   * Get assignment details by ID\n   * @param id Assignment ID\n   * @returns Assignment details\n   */\n  async getAssignment(id: string): Promise<Assignment> {\n    return apiRequest<Assignment>(\n      {\n        method: 'GET',\n        url: `/assignments/${id}`,\n      },\n      AssignmentSchema\n    );\n  },\n\n  /**\n   * Update assignment status\n   * @param id Assignment ID\n   * @param status New status\n   * @param notes Optional notes\n   * @returns Updated assignment\n   */\n  async updateAssignmentStatus(\n    id: string,\n    status: AssignmentStatus,\n    notes?: string\n  ): Promise<Assignment> {\n    return apiRequest<Assignment>(\n      {\n        method: 'PATCH',\n        url: `/assignments/${id}/status`,\n        data: { status, ...(notes && { notes }) },\n      },\n      AssignmentSchema\n    );\n  },\n\n  /**\n   * Get performance metrics for the current trader\n   * @param params Query parameters for filtering metrics\n   * @returns Performance metrics\n   */\n  async getPerformanceMetrics(params: {\n    startDate?: string;\n    endDate?: string;\n    merchantId?: string;\n  }): Promise<PerformanceMetrics> {\n    const queryParams = createQueryString({\n      ...(params.startDate && { startDate: params.startDate }),\n      ...(params.endDate && { endDate: params.endDate }),\n      ...(params.merchantId && { merchantId: params.merchantId }),\n    });\n\n    return apiRequest<PerformanceMetrics>(\n      {\n        method: 'GET',\n        url: `/performance${queryParams ? `?${queryParams}` : ''}`,\n      },\n      z.object({\n        totalCollections: z.number(),\n        totalAmount: z.number(),\n        currency: z.string(),\n        averageCollectionTime: z.number(),\n        successRate: z.number(),\n        metricsByPeriod: z.array(\n          z.object({\n            period: z.string(),\n            count: z.number(),\n            amount: z.number(),\n          })\n        ),\n        topMerchants: z.array(\n          z.object({\n            merchantId: z.string(),\n            merchantName: z.string(),\n            count: z.number(),\n            amount: z.number(),\n          })\n        ),\n      })\n    );\n  },\n\n  /**\n   * Get all assignments with pagination and filters (admin only)\n   * @param params Query parameters for filtering and pagination\n   * @returns Paginated list of assignments\n   */\n  async getAllAssignments(\n    params: GetAssignmentsParams = {}\n  ): Promise<PaginatedResponse<Assignment>> {\n    const queryParams: Record<string, string | string[]> = {\n      page: params.page?.toString() || '1',\n      limit: params.limit?.toString() || '10',\n    };\n\n    // Handle status separately to ensure correct typing\n    if (params.status) {\n      queryParams.status = Array.isArray(params.status) \n        ? params.status \n        : [params.status];\n    }\n\n    // Add other optional parameters\n    const optionalParams: Record<string, string> = {};\n    if (params.traderId) optionalParams.traderId = params.traderId;\n    if (params.merchantId) optionalParams.merchantId = params.merchantId;\n    if (params.startDate) optionalParams.startDate = params.startDate;\n    if (params.endDate) optionalParams.endDate = params.endDate;\n    if (params.assignmentType) optionalParams.assignmentType = params.assignmentType;\n    if (params.search) optionalParams.search = params.search;\n    if (params.sortBy) optionalParams.sortBy = params.sortBy;\n    if (params.order) optionalParams.order = params.order;\n\n    // Combine all parameters\n    const allParams = { ...queryParams, ...optionalParams };\n    const queryString = createQueryString(allParams);\n\n    return apiRequest<PaginatedResponse<Assignment>>(\n      {\n        method: 'GET',\n        url: `/admin/assignments${queryParams ? `?${queryParams}` : ''}`,\n      },\n      PaginatedResponseSchema(AssignmentSchema)\n    );\n  },\n\n  /**\n   * Get all merchants (admin only)\n   * @returns List of merchants\n   */\n  async getMerchants(): Promise<Merchant[]> {\n    return apiRequest<Merchant[]>(\n      {\n        method: 'GET',\n        url: '/admin/merchants',\n      },\n      z.array(\n        z.object({\n          _id: z.string(),\n          businessName: z.string(),\n          email: z.string().email(),\n          phone: z.string().optional(),\n          status: z.custom<'active' | 'inactive' | 'suspended'>(\n            (val) => ['active', 'inactive', 'suspended'].includes(String(val))\n          ),\n          metadata: z.record(z.string(), z.unknown()).optional(),\n          createdAt: z.string().datetime(),\n          updatedAt: z.string().datetime(),\n        })\n      )\n    );\n  },\n\n  /**\n   * Create a new assignment (admin only)\n   * @param data Assignment data\n   * @returns Created assignment\n   */\n  async createAssignment(data: CreateAssignmentData): Promise<Assignment> {\n    return apiRequest<Assignment>(\n      {\n        method: 'POST',\n        url: '/admin/assignments',\n        data,\n      },\n      AssignmentSchema\n    );\n  },\n\n  /**\n   * Update an assignment (admin only)\n   * @param id Assignment ID\n   * @param data Data to update\n   * @returns Updated assignment\n   */\n  async updateAssignment(\n    id: string,\n    data: UpdateAssignmentData\n  ): Promise<Assignment> {\n    return apiRequest<Assignment>(\n      {\n        method: 'PUT',\n        url: `/admin/assignments/${id}`,\n        data,\n      },\n      AssignmentSchema\n    );\n  },\n\n  /**\n   * Delete an assignment (admin only)\n   * @param id Assignment ID\n   * @returns Deletion result\n   */\n  async deleteAssignment(id: string): Promise<{ id: string }> {\n    return apiRequest<{ id: string }>(\n      {\n        method: 'DELETE',\n        url: `/admin/assignments/${id}`,\n      },\n      z.object({\n        id: z.string(),\n      })\n    );\n  },\n\n  /**\n   * Bulk assign traders to merchants (admin only)\n   * @param data Bulk assignment data\n   * @returns Bulk operation result\n   */\n  async bulkAssignTraders(\n    data: BulkAssignTradersData\n  ): Promise<{ success: boolean; message?: string }> {\n    return apiRequest(\n      {\n        method: 'POST',\n        url: '/admin/assignments/bulk',\n        data,\n      },\n      z.object({\n        success: z.boolean(),\n        message: z.string().optional(),\n      })\n    );\n  },\n\n  /**\n   * Generate an assignment report\n   * @param params Report parameters\n   * @returns Blob containing the report data\n   */\n  async generateAssignmentReport(params: GenerateReportParams): Promise<Blob> {\n    return apiRequest<Blob>({\n      method: 'GET',\n      url: '/api/assignments/report',\n      params: {\n        ...params,\n        format: params.format || 'pdf', // Default to PDF if not specified\n      },\n      responseType: 'blob',\n    });\n  },\n\n  /**\n   * Get dashboard stats (admin only)\n   * @returns Dashboard stats\n   */\n  async getDashboardStats(): Promise<DashboardStats> {\n    return apiRequest<DashboardStats>(\n      {\n        method: 'GET',\n        url: '/api/traders/dashboard/stats',\n        responseSchema: z.object({\n          totalTraders: z.number(),\n          activeTraders: z.number(),\n          totalMerchants: z.number(),\n          activeAssignments: z.number(),\n          pendingAssignments: z.number(),\n          totalCollections: z.number(),\n          totalAmount: z.number(),\n          currency: z.string(),\n          recentActivity: z.array(z.object({\n            id: z.string(),\n            type: z.string(),\n            description: z.string(),\n            timestamp: z.string(),\n            userId: z.string(),\n            userName: z.string(),\n          })),\n        }),\n      },\n    );\n  },\n};\n", "import { Dialog, DialogContent, DialogHeader, DialogTitle } from '../ui/dialog';\nimport { Button } from '../ui/button';\nimport { Badge } from '../ui/badge';\nimport { format } from 'date-fns';\n\ninterface TraderDetailsModalProps {\n  trader: {\n    _id: string;\n    name: string;\n    email: string;\n    phone?: string;\n    status: 'active' | 'inactive' | 'suspended';\n    lastActive?: string;\n    createdAt: string;\n    updatedAt: string;\n    assignedMerchants: number;\n    totalCollections: number;\n    totalAmountCollected: number;\n  };\n  isOpen: boolean;\n  onClose: () => void;\n  onStatusChange: (status: 'active' | 'inactive' | 'suspended') => void;\n}\n\nexport function TraderDetailsModal({ \n  trader, \n  isOpen, \n  onClose, \n  onStatusChange \n}: TraderDetailsModalProps) {\n  if (!trader) return null;\n\n  const statusVariant = {\n    active: 'default',\n    inactive: 'secondary',\n    suspended: 'destructive'\n  } as const;\n\n  const formatCurrency = (amount: number) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD',\n    }).format(amount);\n  };\n\n  return (\n    <Dialog open={isOpen} onOpenChange={onClose}>\n      <DialogContent className=\"sm:max-w-[500px]\">\n        <DialogHeader>\n          <DialogTitle>Trader Details</DialogTitle>\n        </DialogHeader>\n        \n        <div className=\"space-y-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <h3 className=\"text-lg font-medium\">{trader.name}</h3>\n            <Badge variant={statusVariant[trader.status]}>\n              {trader.status.charAt(0).toUpperCase() + trader.status.slice(1)}\n            </Badge>\n          </div>\n          \n          <div className=\"grid grid-cols-2 gap-4\">\n            <div>\n              <p className=\"text-sm text-muted-foreground\">Email</p>\n              <p>{trader.email}</p>\n            </div>\n            {trader.phone && (\n              <div>\n                <p className=\"text-sm text-muted-foreground\">Phone</p>\n                <p>{trader.phone}</p>\n              </div>\n            )}\n            <div>\n              <p className=\"text-sm text-muted-foreground\">Member Since</p>\n              <p>{format(new Date(trader.createdAt), 'MMM d, yyyy')}</p>\n            </div>\n            <div>\n              <p className=\"text-sm text-muted-foreground\">Last Active</p>\n              <p>{trader.lastActive ? format(new Date(trader.lastActive), 'MMM d, yyyy') : 'N/A'}</p>\n            </div>\n            <div>\n              <p className=\"text-sm text-muted-foreground\">Assigned Merchants</p>\n              <p>{trader.assignedMerchants}</p>\n            </div>\n            <div>\n              <p className=\"text-sm text-muted-foreground\">Total Collections</p>\n              <p>{trader.totalCollections}</p>\n            </div>\n            <div className=\"col-span-2\">\n              <p className=\"text-sm text-muted-foreground\">Total Amount Collected</p>\n              <p className=\"text-lg font-semibold\">{formatCurrency(trader.totalAmountCollected || 0)}</p>\n            </div>\n          </div>\n          \n          <div className=\"pt-4 border-t\">\n            <h4 className=\"text-sm font-medium mb-2\">Change Status</h4>\n            <div className=\"flex gap-2\">\n              {(['active', 'inactive', 'suspended'] as const).map((status) => (\n                <Button\n                  key={status}\n                  variant={trader.status === status ? 'default' : 'outline'}\n                  size=\"sm\"\n                  onClick={() => onStatusChange(status)}\n                  disabled={trader.status === status}\n                >\n                  {status.charAt(0).toUpperCase() + status.slice(1)}\n                </Button>\n              ))}\n            </div>\n          </div>\n        </div>\n      </DialogContent>\n    </Dialog>\n  );\n}\n", "import { useState, useMemo } from 'react';\nimport { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';\nimport { PaginatedResponse } from '../../services/traderService';\nimport { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';\nimport { Button } from '../../components/ui/button';\nimport { Input } from '../../components/ui/input';\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/ui/table';\nimport { Badge } from '../../components/ui/badge';\nimport { Loader2, Search, UserPlus, MoreHorizontal } from 'lucide-react';\nimport { traderAdminService, Trader, TraderStatus } from '../../services/traderService';\nimport { format } from 'date-fns';\nimport { toast } from '../../components/ui/use-toast';\nimport { TraderDetailsModal } from '../../components/admin/TraderDetailsModal';\n\ntype TraderListItem = Trader & {\n  assignedMerchants: number;\n  totalCollections: number;\n  totalAmountCollected: number;\n  lastActive?: string;\n};\n\nexport function TradersPage() {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [page, setPage] = useState(1);\n  const [selectedTrader, setSelectedTrader] = useState<TraderListItem | null>(null);\n  const [isDetailsOpen, setIsDetailsOpen] = useState(false);\n  const limit = 10;\n  const queryClient = useQueryClient();\n\n  const { mutate: updateStatus } = useMutation({\n    mutationFn: async ({ id, status }: { id: string; status: TraderStatus }) => {\n      await traderAdminService.updateTraderStatus(id, status);\n    },\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: ['traders'] });\n      toast({\n        title: 'Status updated',\n        description: 'Trader status has been updated successfully.'\n      });\n    },\n    onError: () => {\n      toast({\n        title: 'Error',\n        description: 'Failed to update trader status. Please try again.',\n        variant: 'destructive',\n      });\n    }\n  });\n\n  const handleViewDetails = (trader: TraderListItem) => {\n    setSelectedTrader(trader);\n    setIsDetailsOpen(true);\n  };\n\n  const handleStatusChange = (status: 'active' | 'inactive' | 'suspended') => {\n    if (selectedTrader) {\n      updateStatus({ \n        id: selectedTrader._id, \n        status: status as TraderStatus \n      });\n      setIsDetailsOpen(false);\n    }\n  };\n\n  const { data: response, isLoading, error, refetch } = useQuery<PaginatedResponse<Trader>>({\n    queryKey: ['traders', { page, limit, search: searchTerm }],\n    queryFn: () => traderAdminService.getTraders({ \n      page, \n      limit, \n      search: searchTerm,\n      status: undefined\n    })\n  });\n\n  // Transform the response data to include required fields\n  const traders = useMemo<TraderListItem[]>(() => {\n    if (!response?.data) return [];\n    return response.data.map(trader => ({\n      ...trader,\n      assignedMerchants: 0, // Default value, replace with actual data if available\n      totalCollections: 0,  // Default value, replace with actual data if available\n      totalAmountCollected: 0 // Default value, replace with actual data if available\n    }));\n  }, [response]);\n\n  // Calculate total pages for pagination\n  const totalPages = response?.meta?.totalPages || 1;\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <Loader2 className=\"w-8 h-8 animate-spin\" />\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"text-center py-10\">\n        <h3 className=\"text-lg font-medium mb-2\">Failed to load traders</h3>\n        <p className=\"text-muted-foreground mb-4\">Please try again later.</p>\n        <Button onClick={() => refetch()}>Retry</Button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-3xl font-bold tracking-tight\">Traders Management</h2>\n        <Button>\n          <UserPlus className=\"mr-2 h-4 w-4\" />\n          Add Trader\n        </Button>\n      </div>\n      \n      <Card>\n        <CardHeader className=\"pb-0\">\n          <div className=\"flex items-center justify-between\">\n            <CardTitle>All Traders</CardTitle>\n            <div className=\"relative w-64\">\n              <Search className=\"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground\" />\n              <Input\n                type=\"search\"\n                placeholder=\"Search traders...\"\n                className=\"pl-8\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n              />\n            </div>\n          </div>\n        </CardHeader>\n        <CardContent className=\"p-0\">\n          <Table>\n            <TableHeader>\n              <TableRow>\n                <TableHead>Name</TableHead>\n                <TableHead>Email</TableHead>\n                <TableHead>Status</TableHead>\n                <TableHead>Assigned Merchants</TableHead>\n                <TableHead>Total Collected</TableHead>\n                <TableHead>Joined On</TableHead>\n                <TableHead className=\"w-[100px]\">Actions</TableHead>\n              </TableRow>\n            </TableHeader>\n            <TableBody>\n              {traders.map((trader) => (\n                <TableRow key={trader._id}>\n                  <TableCell className=\"font-medium\">{trader.name}</TableCell>\n                  <TableCell>{trader.email}</TableCell>\n                  <TableCell>\n                    <Badge variant={\n                      trader.status === TraderStatus.ACTIVE ? 'default' : \n                      trader.status === TraderStatus.INACTIVE ? 'secondary' : 'destructive'\n                    }>\n                      {trader.status}\n                    </Badge>\n                  </TableCell>\n                  <TableCell>{trader.assignedMerchants}</TableCell>\n                  <TableCell>{formatCurrency(trader.totalAmountCollected || 0)}</TableCell>\n                  <TableCell>{format(new Date(trader.createdAt), 'MMM d, yyyy')}</TableCell>\n                  <TableCell>\n                    <Button \n                      variant=\"ghost\" \n                      size=\"sm\" \n                      onClick={() => handleViewDetails(trader)}\n                    >\n                      <MoreHorizontal className=\"h-4 w-4\" />\n                      <span className=\"sr-only\">View details</span>\n                    </Button>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n          \n          {traders.length === 0 && (\n            <div className=\"py-8 text-center text-muted-foreground\">\n              No traders found\n            </div>\n          )}\n          \n          <div className=\"flex items-center justify-end space-x-2 p-4 border-t\">\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => setPage(p => Math.max(1, p - 1))}\n              disabled={page === 1}\n            >\n              Previous\n            </Button>\n            <span className=\"text-sm text-muted-foreground\">\n              Page {page} of {totalPages}\n            </span>\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => setPage(p => p + 1)}\n              disabled={page >= totalPages}\n            >\n              Next\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Trader Details Modal */}\n      {selectedTrader && (\n        <TraderDetailsModal\n          trader={selectedTrader}\n          isOpen={isDetailsOpen}\n          onClose={() => setIsDetailsOpen(false)}\n          onStatusChange={handleStatusChange}\n        />\n      )}\n    </div>\n  );\n}\n\n// Helper function to format currency\nfunction formatCurrency(amount: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(amount);\n}\n\nexport default TradersPage;\n", "import * as React from \"react\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardDescription,\n  CardContent,\n}\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Search\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMSIgY3k9IjExIiByPSI4IiAvPgogIDxwYXRoIGQ9Im0yMSAyMS00LjMtNC4zIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/search\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Search = createLucideIcon('Search', [\n  ['circle', { cx: '11', cy: '11', r: '8', key: '4ej97u' }],\n  ['path', { d: 'm21 21-4.3-4.3', key: '1qie3q' }],\n]);\n\nexport default Search;\n", "import * as React from 'react';\nimport { ToastProps, ToastActionElement } from './toast';\n\ntype ToasterToast = ToastProps & {\n  id: string;\n  title?: React.ReactNode;\n  description?: React.ReactNode;\n};\n\nconst actionTypes = {\n  ADD_TOAST: 'ADD_TOAST',\n  UPDATE_TOAST: 'UPDATE_TOAST',\n  DISMISS_TOAST: 'DISMISS_TOAST',\n  REMOVE_TOAST: 'REMOVE_TOAST',\n} as const;\n\nlet count = 0;\n\nfunction genId() {\n  count = (count + 1) % Number.MAX_SAFE_INTEGER;\n  return count.toString();\n}\n\ntype ActionType = typeof actionTypes;\n\ntype Action =\n  | {\n      type: ActionType['ADD_TOAST'];\n      toast: ToasterToast;\n    }\n  | {\n      type: ActionType['UPDATE_TOAST'];\n      toast: Partial<ToasterToast>;\n    }\n  | {\n      type: ActionType['DISMISS_TOAST'];\n      toastId?: ToasterToast['id'];\n    }\n  | {\n      type: ActionType['REMOVE_TOAST'];\n      toastId?: ToasterToast['id'];\n    };\n\ninterface State {\n  toasts: ToasterToast[];\n}\n\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>();\n\nconst addToRemoveQueue = (toastId: string) => {\n  if (toastTimeouts.has(toastId)) {\n    return;\n  }\n\n  const timeout = setTimeout(() => {\n    toastTimeouts.delete(toastId);\n    dispatch({\n      type: 'REMOVE_TOAST',\n      toastId: toastId,\n    });\n  }, 1000);\n\n  toastTimeouts.set(toastId, timeout);\n};\n\nexport const reducer = (state: State, action: Action): State => {\n  switch (action.type) {\n    case 'ADD_TOAST':\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, 5),\n      };\n\n    case 'UPDATE_TOAST':\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\n        ),\n      };\n\n    case 'DISMISS_TOAST': {\n      const { toastId } = action;\n\n      // ! Side effects ! - This could be extracted into a dismissToast() action,\n      // but I'll keep it here for simplicity\n      if (toastId) {\n        addToRemoveQueue(toastId);\n      } else {\n        state.toasts.forEach((toast) => {\n          addToRemoveQueue(toast.id);\n        });\n      }\n\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                open: false,\n              }\n            : t\n        ),\n      };\n    }\n    case 'REMOVE_TOAST':\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        };\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\n      };\n  }\n};\n\nconst listeners: Array<(state: State) => void> = [];\n\nlet memoryState: State = { toasts: [] };\n\nfunction dispatch(action: Action) {\n  memoryState = reducer(memoryState, action);\n  listeners.forEach((listener) => {\n    listener(memoryState);\n  });\n}\n\ntype Toast = Omit<ToasterToast, 'id'>;\n\nfunction toast({ ...props }: Toast) {\n  const id = genId();\n\n  const update = (props: ToasterToast) =>\n    dispatch({\n      type: 'UPDATE_TOAST',\n      toast: { ...props, id },\n    });\n  const dismiss = () => dispatch({ type: 'DISMISS_TOAST', toastId: id });\n\n  dispatch({\n    type: 'ADD_TOAST',\n    toast: {\n      ...props,\n      id,\n      open: true,\n      onOpenChange: (open: boolean) => {\n        if (!open) dismiss();\n      },\n    },\n  });\n\n  return {\n    id: id,\n    dismiss,\n    update,\n  };\n}\n\nfunction useToast() {\n  const [state, setState] = React.useState<State>(memoryState);\n\n  React.useEffect(() => {\n    listeners.push(setState);\n    return () => {\n      const index = listeners.indexOf(setState);\n      if (index > -1) {\n        listeners.splice(index, 1);\n      }\n    };\n  }, [state]);\n\n  return {\n    ...state,\n    toast,\n    dismiss: (toastId?: string) => dispatch({ type: 'DISMISS_TOAST', toastId }),\n  };\n}\n\nexport { useToast, toast };\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name MoreHorizontal\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxIiAvPgogIDxjaXJjbGUgY3g9IjE5IiBjeT0iMTIiIHI9IjEiIC8+CiAgPGNpcmNsZSBjeD0iNSIgY3k9IjEyIiByPSIxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/more-horizontal\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MoreHorizontal = createLucideIcon('MoreHorizontal', [\n  ['circle', { cx: '12', cy: '12', r: '1', key: '41hilf' }],\n  ['circle', { cx: '19', cy: '12', r: '1', key: '1wjl8i' }],\n  ['circle', { cx: '5', cy: '12', r: '1', key: '1pcz8c' }],\n]);\n\nexport default MoreHorizontal;\n", "import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        success:\n          \"border-transparent bg-green-500 text-white hover:bg-green-500/80\",\n        warning:\n          \"border-transparent bg-yellow-500 text-white hover:bg-yellow-500/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n", "import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n", "import * as React from \"react\"\n\nimport { cn } from \"../../lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": ["Table", "React", "_ref", "ref", "className", "props", "_jsx", "children", "cn", "displayName", "TableHeader", "_ref2", "TableBody", "_ref3", "_ref4", "TableRow", "_ref5", "TableHead", "_ref6", "TableCell", "_ref7", "_ref8", "Dialog", "DialogPrimitive", "DialogTrigger", "DialogPortal", "container", "forceMount", "restProps", "portalProps", "DialogOverlay", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_jsxs", "X", "DialogHeader", "<PERSON><PERSON><PERSON><PERSON>er", "DialogTitle", "DialogDescription", "UserPlus", "createLucideIcon", "d", "key", "cx", "cy", "r", "x1", "x2", "y1", "y2", "ApiError", "Error", "constructor", "message", "status", "code", "details", "super", "this", "name", "Object", "setPrototypeOf", "prototype", "AssignmentStatus", "AssignmentType", "TraderStatus", "TraderSchema", "z", "_id", "min", "max", "email", "phone", "optional", "role", "metadata", "createdAt", "datetime", "updatedAt", "extend", "val", "lastLogin", "cancelTokenSources", "trader", "merchant", "businessName", "assignmentType", "startDate", "endDate", "collectionTarget", "amount", "positive", "currency", "length", "period", "includes", "String", "permissions", "notes", "traderApi", "axios", "create", "baseURL", "API_BASE_URL", "timeout", "headers", "withCredentials", "interceptors", "request", "use", "config", "requestId", "method", "url", "cancel", "source", "CancelToken", "cancelToken", "token", "localStorage", "getItem", "Authorization", "Date", "getTime", "toString", "error", "Promise", "reject", "retryCount", "async", "apiRequest", "schema", "responseSchema", "data", "isAxiosError", "_error$response", "_error$response2", "response", "errorDetails", "createQueryString", "params", "searchParams", "URLSearchParams", "entries", "for<PERSON>ach", "value", "undefined", "Array", "isArray", "item", "append", "start", "duration", "parseInt", "console", "debug", "validation", "safeParse", "success", "originalRequest", "isCancel", "log", "errors", "_retry", "delay", "Math", "pow", "resolve", "setTimeout", "PaginatedResponseSchema", "itemSchema", "meta", "total", "page", "limit", "totalPages", "traderAdminService", "getTraders", "arguments", "search", "merchantId", "sortBy", "order", "getTraderDetails", "id", "updateTraderStatus", "reason", "createTrader", "updateTrader", "TraderDetailsModal", "isOpen", "onClose", "onStatusChange", "open", "onOpenChange", "Badge", "variant", "active", "inactive", "suspended", "char<PERSON>t", "toUpperCase", "slice", "format", "lastActive", "assignedMerchants", "totalCollections", "totalAmountCollected", "Intl", "NumberFormat", "style", "map", "<PERSON><PERSON>", "size", "onClick", "disabled", "TradersPage", "_response$meta", "searchTerm", "setSearchTerm", "useState", "setPage", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedTrader", "isDetailsOpen", "setIsDetailsOpen", "queryClient", "useQueryClient", "mutate", "updateStatus", "useMutation", "mutationFn", "onSuccess", "invalidateQueries", "query<PERSON><PERSON>", "toast", "title", "description", "onError", "isLoading", "refetch", "useQuery", "queryFn", "traders", "useMemo", "Loader2", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "Search", "Input", "type", "placeholder", "onChange", "e", "target", "<PERSON><PERSON><PERSON><PERSON>", "ACTIVE", "INACTIVE", "handleViewDetails", "MoreHorizontal", "p", "CardDescription", "<PERSON><PERSON><PERSON>er", "count", "toastTimeouts", "Map", "addToRemoveQueue", "toastId", "has", "delete", "dispatch", "set", "reducer", "state", "action", "toasts", "t", "filter", "listeners", "memoryState", "listener", "Number", "MAX_SAFE_INTEGER", "dismiss", "update", "useToast", "setState", "push", "index", "indexOf", "splice", "badgeVariants", "cva", "variants", "default", "secondary", "destructive", "warning", "outline", "defaultVariants", "buttonVariants", "ghost", "link", "sm", "lg", "icon", "<PERSON><PERSON><PERSON><PERSON>", "Comp", "Slot"], "sourceRoot": ""}