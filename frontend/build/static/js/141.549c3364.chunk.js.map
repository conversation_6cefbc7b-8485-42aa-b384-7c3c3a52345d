{"version": 3, "file": "static/js/141.549c3364.chunk.js", "mappings": "kKACA,SAASA,EAAKC,GACV,OAAOA,CACX,CACA,SAASC,EAAkBC,EAAUC,QACd,IAAfA,IAAyBA,EAAaJ,GAC1C,IAAIK,EAAS,GACTC,GAAW,EA0Df,MAzDa,CACTC,KAAM,WACF,GAAID,EACA,MAAM,IAAIE,MAAM,oGAEpB,OAAIH,EAAOI,OACAJ,EAAOA,EAAOI,OAAS,GAE3BN,CACX,EACAO,UAAW,SAAUC,GACjB,IAAIC,EAAOR,EAAWO,EAAML,GAE5B,OADAD,EAAOQ,KAAKD,GACL,WACHP,EAASA,EAAOS,OAAO,SAAUC,GAAK,OAAOA,IAAMH,CAAM,EAC7D,CACJ,EACAI,iBAAkB,SAAUC,GAExB,IADAX,GAAW,EACJD,EAAOI,QAAQ,CAClB,IAAIS,EAAMb,EACVA,EAAS,GACTa,EAAIC,QAAQF,EAChB,CACAZ,EAAS,CACLQ,KAAM,SAAUE,GAAK,OAAOE,EAAGF,EAAI,EACnCD,OAAQ,WAAc,OAAOT,CAAQ,EAE7C,EACAe,aAAc,SAAUH,GACpBX,GAAW,EACX,IAAIe,EAAe,GACnB,GAAIhB,EAAOI,OAAQ,CACf,IAAIS,EAAMb,EACVA,EAAS,GACTa,EAAIC,QAAQF,GACZI,EAAehB,CACnB,CACA,IAAIiB,EAAe,WACf,IAAIJ,EAAMG,EACVA,EAAe,GACfH,EAAIC,QAAQF,EAChB,EACIM,EAAQ,WAAc,OAAOC,QAAQC,UAAUC,KAAKJ,EAAe,EACvEC,IACAlB,EAAS,CACLQ,KAAM,SAAUE,GACZM,EAAaR,KAAKE,GAClBQ,GACJ,EACAT,OAAQ,SAAUA,GAEd,OADAO,EAAeA,EAAaP,OAAOA,GAC5BT,CACX,EAER,EAGR,CAMO,SAASsB,EAAoBC,QAChB,IAAZA,IAAsBA,EAAU,CAAC,GACrC,IAAIC,EAAS3B,EAAkB,MAE/B,OADA2B,EAAOD,SAAUE,EAAAA,EAAAA,IAAS,CAAEC,OAAO,EAAMC,KAAK,GAASJ,GAChDC,CACX,C,8DC7EO,IAAII,EAAqB,4BACrBC,EAAqB,0BACrBC,EAAwB,0BAKxBC,EAAyB,gC,0DCLhCC,EAAU,SAAUC,GACpB,IAAIC,EAAUD,EAAGC,QAASC,GAAOC,EAAAA,EAAAA,IAAOH,EAAI,CAAC,YAC7C,IAAKC,EACD,MAAM,IAAI/B,MAAM,sEAEpB,IAAIkC,EAASH,EAAQhC,OACrB,IAAKmC,EACD,MAAM,IAAIlC,MAAM,4BAEpB,OAAOmC,EAAAA,cAAoBD,GAAQZ,EAAAA,EAAAA,IAAS,CAAC,EAAGU,GACpD,EAEO,SAASI,EAAcf,EAAQgB,GAElC,OADAhB,EAAOnB,UAAUmC,GACVR,CACX,CAJAA,EAAQS,iBAAkB,C,mCCb1B,IAAIC,EAAmB,SAAUC,GAC7B,MAAwB,qBAAbC,SACA,MAEQC,MAAMC,QAAQH,GAAkBA,EAAe,GAAKA,GACnDI,cAAcC,IACtC,EACIC,EAAa,IAAIC,QACjBC,EAAoB,IAAID,QACxBE,EAAY,CAAC,EACbC,EAAY,EACZC,EAAa,SAAUC,GACvB,OAAOA,IAASA,EAAKC,MAAQF,EAAWC,EAAKE,YACjD,EAwBIC,EAAyB,SAAUf,EAAgBc,EAAYE,EAAYC,GAC3E,IAAIC,EAxBa,SAAUC,EAAQD,GACnC,OAAOA,EACFE,IAAI,SAAUC,GACf,GAAIF,EAAOG,SAASD,GAChB,OAAOA,EAEX,IAAIE,EAAkBZ,EAAWU,GACjC,OAAIE,GAAmBJ,EAAOG,SAASC,GAC5BA,GAEXC,QAAQC,MAAM,cAAeJ,EAAQ,0BAA2BF,EAAQ,mBACjE,KACX,GACKrD,OAAO,SAAUC,GAAK,OAAO2D,QAAQ3D,EAAI,EAClD,CAUkB4D,CAAeb,EAAYZ,MAAMC,QAAQH,GAAkBA,EAAiB,CAACA,IACtFS,EAAUO,KACXP,EAAUO,GAAc,IAAIT,SAEhC,IAAIqB,EAAgBnB,EAAUO,GAC1Ba,EAAc,GACdC,EAAiB,IAAIC,IACrBC,EAAiB,IAAID,IAAIb,GACzBe,EAAO,SAAUC,GACZA,IAAMJ,EAAeK,IAAID,KAG9BJ,EAAeM,IAAIF,GACnBD,EAAKC,EAAGpB,YACZ,EACAI,EAAQ/C,QAAQ8D,GAChB,IAAII,EAAO,SAAUlB,GACZA,IAAUa,EAAeG,IAAIhB,IAGlCjB,MAAMoC,UAAUnE,QAAQoE,KAAKpB,EAAOqB,SAAU,SAAU5B,GACpD,GAAIkB,EAAeK,IAAIvB,GACnByB,EAAKzB,QAGL,IACI,IAAI6B,EAAO7B,EAAK8B,aAAazB,GACzB0B,EAAyB,OAATF,GAA0B,UAATA,EACjCG,GAAgBtC,EAAWuC,IAAIjC,IAAS,GAAK,EAC7CkC,GAAelB,EAAciB,IAAIjC,IAAS,GAAK,EACnDN,EAAWyC,IAAInC,EAAMgC,GACrBhB,EAAcmB,IAAInC,EAAMkC,GACxBjB,EAAYhE,KAAK+C,GACI,IAAjBgC,GAAsBD,GACtBnC,EAAkBuC,IAAInC,GAAM,GAEZ,IAAhBkC,GACAlC,EAAKoC,aAAahC,EAAY,QAE7B2B,GACD/B,EAAKoC,aAAa/B,EAAkB,OAE5C,CACA,MAAOgC,GACHzB,QAAQC,MAAM,kCAAmCb,EAAMqC,EAC3D,CAER,EACJ,EAIA,OAHAZ,EAAKvB,GACLgB,EAAeoB,QACfxC,IACO,WACHmB,EAAY1D,QAAQ,SAAUyC,GAC1B,IAAIgC,EAAetC,EAAWuC,IAAIjC,GAAQ,EACtCkC,EAAclB,EAAciB,IAAIjC,GAAQ,EAC5CN,EAAWyC,IAAInC,EAAMgC,GACrBhB,EAAcmB,IAAInC,EAAMkC,GACnBF,IACIpC,EAAkB2B,IAAIvB,IACvBA,EAAKuC,gBAAgBlC,GAEzBT,EAAkB4C,OAAOxC,IAExBkC,GACDlC,EAAKuC,gBAAgBnC,EAE7B,KACAN,IAGIJ,EAAa,IAAIC,QACjBD,EAAa,IAAIC,QACjBC,EAAoB,IAAID,QACxBE,EAAY,CAAC,EAErB,CACJ,EAQW4C,EAAa,SAAUrD,EAAgBc,EAAYE,QACvC,IAAfA,IAAyBA,EAAa,oBAC1C,IAAIE,EAAUhB,MAAMoD,KAAKpD,MAAMC,QAAQH,GAAkBA,EAAiB,CAACA,IACvEuD,EAAmBzC,GAAcf,EAAiBC,GACtD,OAAKuD,GAKLrC,EAAQrD,KAAK2F,MAAMtC,EAAShB,MAAMoD,KAAKC,EAAiBE,iBAAiB,yBAClE1C,EAAuBG,EAASqC,EAAkBvC,EAAY,gBAL1D,WAAc,OAAO,IAAM,CAM1C,C,qECtIW0C,EAAU,CACjBC,KAAM,EACNC,IAAK,EACLC,MAAO,EACPC,IAAK,GAELC,EAAQ,SAAUhG,GAAK,OAAOiG,SAASjG,GAAK,GAAI,KAAO,CAAG,EAQnDkG,EAAc,SAAUC,GAE/B,QADgB,IAAZA,IAAsBA,EAAU,UACd,qBAAXC,OACP,OAAOT,EAEX,IAAIU,EAZQ,SAAUF,GACtB,IAAIG,EAAKF,OAAOG,iBAAiBrE,SAASI,MACtCsD,EAAOU,EAAe,YAAZH,EAAwB,cAAgB,cAClDN,EAAMS,EAAe,YAAZH,EAAwB,aAAe,aAChDL,EAAQQ,EAAe,YAAZH,EAAwB,eAAiB,eACxD,MAAO,CAACH,EAAMJ,GAAOI,EAAMH,GAAMG,EAAMF,GAC3C,CAMkBU,CAAUL,GACpBM,EAAgBvE,SAASwE,gBAAgBC,YACzCC,EAAcR,OAAOS,WACzB,MAAO,CACHjB,KAAMS,EAAQ,GACdR,IAAKQ,EAAQ,GACbP,MAAOO,EAAQ,GACfN,IAAKe,KAAKC,IAAI,EAAGH,EAAcH,EAAgBJ,EAAQ,GAAKA,EAAQ,IAE5E,ECxBIW,GAAQC,EAAAA,EAAAA,MACDC,EAAgB,qBAIvBC,EAAY,SAAU5F,EAAI6F,EAAejB,EAASkB,GAClD,IAAIzB,EAAOrE,EAAGqE,KAAMC,EAAMtE,EAAGsE,IAAKC,EAAQvE,EAAGuE,MAAOC,EAAMxE,EAAGwE,IAE7D,YADgB,IAAZI,IAAsBA,EAAU,UAC7B,QAAQmB,OAAOlG,EAAAA,GAAuB,4BAA4BkG,OAAOD,EAAW,yBAAyBC,OAAOvB,EAAK,OAAOuB,OAAOD,EAAW,mBAAmBC,OAAOJ,EAAe,8BAA8BI,OAAOD,EAAW,8CAA8CC,OAAO,CACnSF,GAAiB,sBAAsBE,OAAOD,EAAW,KAC7C,WAAZlB,GACI,uBAAuBmB,OAAO1B,EAAM,0BAA0B0B,OAAOzB,EAAK,4BAA4ByB,OAAOxB,EAAO,kEAAkEwB,OAAOvB,EAAK,OAAOuB,OAAOD,EAAW,WACnN,YAAZlB,GAAyB,kBAAkBmB,OAAOvB,EAAK,OAAOuB,OAAOD,EAAW,MAE/EtH,OAAO4D,SACP4D,KAAK,IAAK,kBAAkBD,OAAOpG,EAAAA,GAAoB,mBAAmBoG,OAAOvB,EAAK,OAAOuB,OAAOD,EAAW,mBAAmBC,OAAOnG,EAAAA,GAAoB,0BAA0BmG,OAAOvB,EAAK,OAAOuB,OAAOD,EAAW,mBAAmBC,OAAOpG,EAAAA,GAAoB,MAAMoG,OAAOpG,EAAAA,GAAoB,qBAAqBoG,OAAOD,EAAW,mBAAmBC,OAAOnG,EAAAA,GAAoB,MAAMmG,OAAOnG,EAAAA,GAAoB,4BAA4BmG,OAAOD,EAAW,uBAAuBC,OAAOJ,EAAe,aAAaI,OAAOjG,EAAAA,GAAwB,MAAMiG,OAAOvB,EAAK,aACnkB,EACIyB,EAAuB,WACvB,IAAIC,EAAUxB,SAAS/D,SAASI,KAAKqC,aAAauC,IAAkB,IAAK,IACzE,OAAOQ,SAASD,GAAWA,EAAU,CACzC,EAkBWE,EAAkB,SAAUpG,GACnC,IAAIqG,EAAarG,EAAGqG,WAAYC,EAActG,EAAGsG,YAAaC,EAAKvG,EAAG4E,QAASA,OAAiB,IAAP2B,EAAgB,SAAWA,EAjBpHlG,EAAAA,UAAgB,WAEZ,OADAM,SAASI,KAAK2C,aAAaiC,GAAgBM,IAAyB,GAAGO,YAChE,WACH,IAAIC,EAAaR,IAAyB,EACtCQ,GAAc,EACd9F,SAASI,KAAK8C,gBAAgB8B,GAG9BhF,SAASI,KAAK2C,aAAaiC,EAAec,EAAWD,WAE7D,CACJ,EAAG,IAaH,IAAIhC,EAAMnE,EAAAA,QAAc,WAAc,OAAOsE,EAAYC,EAAU,EAAG,CAACA,IACvE,OAAOvE,EAAAA,cAAoBoF,EAAO,CAAEiB,OAAQd,EAAUpB,GAAM6B,EAAYzB,EAAU0B,EAA6B,GAAf,eACpG,C,qDCrBO,IAAI9G,EAAW,WAQpB,OAPAA,EAAWmH,OAAOC,QAAU,SAAkBC,GAC1C,IAAK,IAAIC,EAAGC,EAAI,EAAGC,EAAIC,UAAU9I,OAAQ4I,EAAIC,EAAGD,IAE5C,IAAK,IAAIG,KADTJ,EAAIG,UAAUF,GACOJ,OAAO3D,UAAUmE,eAAelE,KAAK6D,EAAGI,KAAIL,EAAEK,GAAKJ,EAAEI,IAE9E,OAAOL,CACX,EACOrH,EAAS0E,MAAMkD,KAAMH,UAC9B,EAEO,SAAS9G,EAAO2G,EAAGnD,GACxB,IAAIkD,EAAI,CAAC,EACT,IAAK,IAAIK,KAAKJ,EAAOH,OAAO3D,UAAUmE,eAAelE,KAAK6D,EAAGI,IAAMvD,EAAE0D,QAAQH,GAAK,IAC9EL,EAAEK,GAAKJ,EAAEI,IACb,GAAS,MAALJ,GAAqD,oBAAjCH,OAAOW,sBACtB,KAAIP,EAAI,EAAb,IAAgBG,EAAIP,OAAOW,sBAAsBR,GAAIC,EAAIG,EAAE/I,OAAQ4I,IAC3DpD,EAAE0D,QAAQH,EAAEH,IAAM,GAAKJ,OAAO3D,UAAUuE,qBAAqBtE,KAAK6D,EAAGI,EAAEH,MACvEF,EAAEK,EAAEH,IAAMD,EAAEI,EAAEH,IAF4B,CAItD,OAAOF,CACT,CAoG6BF,OAAOa,OA6D7B,SAASC,EAAcC,EAAI1D,EAAM2D,GACtC,GAAIA,GAA6B,IAArBV,UAAU9I,OAAc,IAAK,IAA4ByJ,EAAxBb,EAAI,EAAGc,EAAI7D,EAAK7F,OAAY4I,EAAIc,EAAGd,KACxEa,GAAQb,KAAK/C,IACR4D,IAAIA,EAAKhH,MAAMoC,UAAU8E,MAAM7E,KAAKe,EAAM,EAAG+C,IAClDa,EAAGb,GAAK/C,EAAK+C,IAGrB,OAAOW,EAAG3B,OAAO6B,GAAMhH,MAAMoC,UAAU8E,MAAM7E,KAAKe,GACpD,CAsCyB2C,OAAOa,OAoEkB,oBAApBO,iBAAiCA,e,gDC1TxD,SAASC,EAAUC,EAAKC,GAO3B,MANmB,oBAARD,EACPA,EAAIC,GAECD,IACLA,EAAIE,QAAUD,GAEXD,CACX,CClBA,IAAIG,EAA8C,qBAAXvD,OAAyBxE,EAAAA,gBAAwBA,EAAAA,UACpFgI,EAAgB,IAAIpH,QAejB,SAASqH,EAAaC,EAAMC,GAC/B,IAAIC,ECLD,SAAwBC,EAAcC,GACzC,IAAIV,GAAMW,EAAAA,EAAAA,UAAS,WAAc,MAAQ,CAErCV,MAAOQ,EAEPC,SAAUA,EAEVE,OAAQ,CACJ,WAAIV,GACA,OAAOF,EAAIC,KACf,EACA,WAAIC,CAAQD,GACR,IAAIY,EAAOb,EAAIC,MACXY,IAASZ,IACTD,EAAIC,MAAQA,EACZD,EAAIU,SAAST,EAAOY,GAE5B,GAEJ,GAAG,GAGP,OADAb,EAAIU,SAAWA,EACRV,EAAIY,MACf,CDlBsBE,CAAeP,GAAgB,KAAM,SAAUQ,GAC7D,OAAOT,EAAK1J,QAAQ,SAAUoJ,GAAO,OAAOD,EAAUC,EAAKe,EAAW,EAC1E,GAqBA,OAnBAZ,EAA0B,WACtB,IAAIa,EAAWZ,EAAc9E,IAAIkF,GACjC,GAAIQ,EAAU,CACV,IAAIC,EAAa,IAAIzG,IAAIwG,GACrBE,EAAa,IAAI1G,IAAI8F,GACrBa,EAAYX,EAAYN,QAC5Be,EAAWrK,QAAQ,SAAUoJ,GACpBkB,EAAWtG,IAAIoF,IAChBD,EAAUC,EAAK,KAEvB,GACAkB,EAAWtK,QAAQ,SAAUoJ,GACpBiB,EAAWrG,IAAIoF,IAChBD,EAAUC,EAAKmB,EAEvB,EACJ,CACAf,EAAc5E,IAAIgF,EAAaF,EACnC,EAAG,CAACA,IACGE,CACX,C,uCE5CIY,E,UCCJ,SAASC,IACL,IAAK3I,SACD,OAAO,KACX,IAAI4I,EAAM5I,SAAS6I,cAAc,SACjCD,EAAIE,KAAO,WACX,IAAIC,EDDAL,GAIOM,EAAAA,GCCX,OAHID,GACAH,EAAI7F,aAAa,QAASgG,GAEvBH,CACX,CAeO,IAAIK,EAAsB,WAC7B,IAAI1D,EAAU,EACV2D,EAAa,KACjB,MAAO,CACH/G,IAAK,SAAUgH,GAlBvB,IAAsBP,EAAKQ,EAmBA,GAAX7D,IACK2D,EAAaP,OApBPS,EAqBkBD,GArBvBP,EAqBWM,GAnBrBG,WAEJT,EAAIS,WAAWC,QAAUF,EAGzBR,EAAIW,YAAYvJ,SAASwJ,eAAeJ,IAGhD,SAAwBR,IACT5I,SAASyJ,MAAQzJ,SAAS0J,qBAAqB,QAAQ,IAC7DH,YAAYX,EACrB,CASoBe,CAAeT,IAGvB3D,GACJ,EACAqE,OAAQ,eACJrE,GACgB2D,IACZA,EAAWrI,YAAcqI,EAAWrI,WAAWgJ,YAAYX,GAC3DA,EAAa,KAErB,EAER,ECxCWnE,EAAiB,WACxB,IAAI+E,ECGwB,WAC5B,IAAIC,EAAQd,IACZ,OAAO,SAAUlD,EAAQiE,GACrBtK,EAAAA,UAAgB,WAEZ,OADAqK,EAAM5H,IAAI4D,GACH,WACHgE,EAAMH,QACV,CACJ,EAAG,CAAC7D,GAAUiE,GAClB,CACJ,CDbmBC,GAMf,OALY,SAAU5K,GAClB,IAAI0G,EAAS1G,EAAG0G,OAAQmE,EAAU7K,EAAG6K,QAErC,OADAJ,EAAS/D,EAAQmE,GACV,IACX,CAEJ,C", "sources": ["../node_modules/use-sidecar/dist/es2015/medium.js", "../node_modules/react-remove-scroll-bar/dist/es2015/constants.js", "../node_modules/use-sidecar/dist/es2015/exports.js", "../node_modules/aria-hidden/dist/es2015/index.js", "../node_modules/react-remove-scroll-bar/dist/es2015/utils.js", "../node_modules/react-remove-scroll-bar/dist/es2015/component.js", "../node_modules/tslib/tslib.es6.mjs", "../node_modules/use-callback-ref/dist/es2015/assignRef.js", "../node_modules/use-callback-ref/dist/es2015/useMergeRef.js", "../node_modules/use-callback-ref/dist/es2015/useRef.js", "../node_modules/get-nonce/dist/es2015/index.js", "../node_modules/react-style-singleton/dist/es2015/singleton.js", "../node_modules/react-style-singleton/dist/es2015/component.js", "../node_modules/react-style-singleton/dist/es2015/hook.js"], "sourcesContent": ["import { __assign } from \"tslib\";\nfunction ItoI(a) {\n    return a;\n}\nfunction innerCreateMedium(defaults, middleware) {\n    if (middleware === void 0) { middleware = ItoI; }\n    var buffer = [];\n    var assigned = false;\n    var medium = {\n        read: function () {\n            if (assigned) {\n                throw new Error('Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.');\n            }\n            if (buffer.length) {\n                return buffer[buffer.length - 1];\n            }\n            return defaults;\n        },\n        useMedium: function (data) {\n            var item = middleware(data, assigned);\n            buffer.push(item);\n            return function () {\n                buffer = buffer.filter(function (x) { return x !== item; });\n            };\n        },\n        assignSyncMedium: function (cb) {\n            assigned = true;\n            while (buffer.length) {\n                var cbs = buffer;\n                buffer = [];\n                cbs.forEach(cb);\n            }\n            buffer = {\n                push: function (x) { return cb(x); },\n                filter: function () { return buffer; },\n            };\n        },\n        assignMedium: function (cb) {\n            assigned = true;\n            var pendingQueue = [];\n            if (buffer.length) {\n                var cbs = buffer;\n                buffer = [];\n                cbs.forEach(cb);\n                pendingQueue = buffer;\n            }\n            var executeQueue = function () {\n                var cbs = pendingQueue;\n                pendingQueue = [];\n                cbs.forEach(cb);\n            };\n            var cycle = function () { return Promise.resolve().then(executeQueue); };\n            cycle();\n            buffer = {\n                push: function (x) {\n                    pendingQueue.push(x);\n                    cycle();\n                },\n                filter: function (filter) {\n                    pendingQueue = pendingQueue.filter(filter);\n                    return buffer;\n                },\n            };\n        },\n    };\n    return medium;\n}\nexport function createMedium(defaults, middleware) {\n    if (middleware === void 0) { middleware = ItoI; }\n    return innerCreateMedium(defaults, middleware);\n}\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function createSidecarMedium(options) {\n    if (options === void 0) { options = {}; }\n    var medium = innerCreateMedium(null);\n    medium.options = __assign({ async: true, ssr: false }, options);\n    return medium;\n}\n", "export var zeroRightClassName = 'right-scroll-bar-position';\nexport var fullWidthClassName = 'width-before-scroll-bar';\nexport var noScrollbarsClassName = 'with-scroll-bars-hidden';\n/**\n * Name of a CSS variable containing the amount of \"hidden\" scrollbar\n * ! might be undefined ! use will fallback!\n */\nexport var removedBarSizeVariable = '--removed-body-scroll-bar-size';\n", "import { __assign, __rest } from \"tslib\";\nimport * as React from 'react';\nvar SideCar = function (_a) {\n    var sideCar = _a.sideCar, rest = __rest(_a, [\"sideCar\"]);\n    if (!sideCar) {\n        throw new Error('Sidecar: please provide `sideCar` property to import the right car');\n    }\n    var Target = sideCar.read();\n    if (!Target) {\n        throw new Error('Sidecar medium not found');\n    }\n    return React.createElement(Target, __assign({}, rest));\n};\nSideCar.isSideCarExport = true;\nexport function exportSidecar(medium, exported) {\n    medium.useMedium(exported);\n    return SideCar;\n}\n", "var getDefaultParent = function (originalTarget) {\n    if (typeof document === 'undefined') {\n        return null;\n    }\n    var sampleTarget = Array.isArray(originalTarget) ? originalTarget[0] : originalTarget;\n    return sampleTarget.ownerDocument.body;\n};\nvar counterMap = new WeakMap();\nvar uncontrolledNodes = new WeakMap();\nvar markerMap = {};\nvar lockCount = 0;\nvar unwrapHost = function (node) {\n    return node && (node.host || unwrapHost(node.parentNode));\n};\nvar correctTargets = function (parent, targets) {\n    return targets\n        .map(function (target) {\n        if (parent.contains(target)) {\n            return target;\n        }\n        var correctedTarget = unwrapHost(target);\n        if (correctedTarget && parent.contains(correctedTarget)) {\n            return correctedTarget;\n        }\n        console.error('aria-hidden', target, 'in not contained inside', parent, '. Doing nothing');\n        return null;\n    })\n        .filter(function (x) { return Boolean(x); });\n};\n/**\n * Marks everything except given node(or nodes) as aria-hidden\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @param {String} [controlAttribute] - html Attribute to control\n * @return {Undo} undo command\n */\nvar applyAttributeToOthers = function (originalTarget, parentNode, markerName, controlAttribute) {\n    var targets = correctTargets(parentNode, Array.isArray(originalTarget) ? originalTarget : [originalTarget]);\n    if (!markerMap[markerName]) {\n        markerMap[markerName] = new WeakMap();\n    }\n    var markerCounter = markerMap[markerName];\n    var hiddenNodes = [];\n    var elementsToKeep = new Set();\n    var elementsToStop = new Set(targets);\n    var keep = function (el) {\n        if (!el || elementsToKeep.has(el)) {\n            return;\n        }\n        elementsToKeep.add(el);\n        keep(el.parentNode);\n    };\n    targets.forEach(keep);\n    var deep = function (parent) {\n        if (!parent || elementsToStop.has(parent)) {\n            return;\n        }\n        Array.prototype.forEach.call(parent.children, function (node) {\n            if (elementsToKeep.has(node)) {\n                deep(node);\n            }\n            else {\n                try {\n                    var attr = node.getAttribute(controlAttribute);\n                    var alreadyHidden = attr !== null && attr !== 'false';\n                    var counterValue = (counterMap.get(node) || 0) + 1;\n                    var markerValue = (markerCounter.get(node) || 0) + 1;\n                    counterMap.set(node, counterValue);\n                    markerCounter.set(node, markerValue);\n                    hiddenNodes.push(node);\n                    if (counterValue === 1 && alreadyHidden) {\n                        uncontrolledNodes.set(node, true);\n                    }\n                    if (markerValue === 1) {\n                        node.setAttribute(markerName, 'true');\n                    }\n                    if (!alreadyHidden) {\n                        node.setAttribute(controlAttribute, 'true');\n                    }\n                }\n                catch (e) {\n                    console.error('aria-hidden: cannot operate on ', node, e);\n                }\n            }\n        });\n    };\n    deep(parentNode);\n    elementsToKeep.clear();\n    lockCount++;\n    return function () {\n        hiddenNodes.forEach(function (node) {\n            var counterValue = counterMap.get(node) - 1;\n            var markerValue = markerCounter.get(node) - 1;\n            counterMap.set(node, counterValue);\n            markerCounter.set(node, markerValue);\n            if (!counterValue) {\n                if (!uncontrolledNodes.has(node)) {\n                    node.removeAttribute(controlAttribute);\n                }\n                uncontrolledNodes.delete(node);\n            }\n            if (!markerValue) {\n                node.removeAttribute(markerName);\n            }\n        });\n        lockCount--;\n        if (!lockCount) {\n            // clear\n            counterMap = new WeakMap();\n            counterMap = new WeakMap();\n            uncontrolledNodes = new WeakMap();\n            markerMap = {};\n        }\n    };\n};\n/**\n * Marks everything except given node(or nodes) as aria-hidden\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nexport var hideOthers = function (originalTarget, parentNode, markerName) {\n    if (markerName === void 0) { markerName = 'data-aria-hidden'; }\n    var targets = Array.from(Array.isArray(originalTarget) ? originalTarget : [originalTarget]);\n    var activeParentNode = parentNode || getDefaultParent(originalTarget);\n    if (!activeParentNode) {\n        return function () { return null; };\n    }\n    // we should not hide aria-live elements - https://github.com/theKashey/aria-hidden/issues/10\n    // and script elements, as they have no impact on accessibility.\n    targets.push.apply(targets, Array.from(activeParentNode.querySelectorAll('[aria-live], script')));\n    return applyAttributeToOthers(targets, activeParentNode, markerName, 'aria-hidden');\n};\n/**\n * Marks everything except given node(or nodes) as inert\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nexport var inertOthers = function (originalTarget, parentNode, markerName) {\n    if (markerName === void 0) { markerName = 'data-inert-ed'; }\n    var activeParentNode = parentNode || getDefaultParent(originalTarget);\n    if (!activeParentNode) {\n        return function () { return null; };\n    }\n    return applyAttributeToOthers(originalTarget, activeParentNode, markerName, 'inert');\n};\n/**\n * @returns if current browser supports inert\n */\nexport var supportsInert = function () {\n    return typeof HTMLElement !== 'undefined' && HTMLElement.prototype.hasOwnProperty('inert');\n};\n/**\n * Automatic function to \"suppress\" DOM elements - _hide_ or _inert_ in the best possible way\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nexport var suppressOthers = function (originalTarget, parentNode, markerName) {\n    if (markerName === void 0) { markerName = 'data-suppressed'; }\n    return (supportsInert() ? inertOthers : hideOthers)(originalTarget, parentNode, markerName);\n};\n", "export var zeroGap = {\n    left: 0,\n    top: 0,\n    right: 0,\n    gap: 0,\n};\nvar parse = function (x) { return parseInt(x || '', 10) || 0; };\nvar getOffset = function (gapMode) {\n    var cs = window.getComputedStyle(document.body);\n    var left = cs[gapMode === 'padding' ? 'paddingLeft' : 'marginLeft'];\n    var top = cs[gapMode === 'padding' ? 'paddingTop' : 'marginTop'];\n    var right = cs[gapMode === 'padding' ? 'paddingRight' : 'marginRight'];\n    return [parse(left), parse(top), parse(right)];\n};\nexport var getGapWidth = function (gapMode) {\n    if (gapMode === void 0) { gapMode = 'margin'; }\n    if (typeof window === 'undefined') {\n        return zeroGap;\n    }\n    var offsets = getOffset(gapMode);\n    var documentWidth = document.documentElement.clientWidth;\n    var windowWidth = window.innerWidth;\n    return {\n        left: offsets[0],\n        top: offsets[1],\n        right: offsets[2],\n        gap: Math.max(0, windowWidth - documentWidth + offsets[2] - offsets[0]),\n    };\n};\n", "import * as React from 'react';\nimport { styleSingleton } from 'react-style-singleton';\nimport { fullWidthClassName, zeroRightClassName, noScrollbarsClassName, removedBarSizeVariable } from './constants';\nimport { getGapWidth } from './utils';\nvar Style = styleSingleton();\nexport var lockAttribute = 'data-scroll-locked';\n// important tip - once we measure scrollBar width and remove them\n// we could not repeat this operation\n// thus we are using style-singleton - only the first \"yet correct\" style will be applied.\nvar getStyles = function (_a, allowRelative, gapMode, important) {\n    var left = _a.left, top = _a.top, right = _a.right, gap = _a.gap;\n    if (gapMode === void 0) { gapMode = 'margin'; }\n    return \"\\n  .\".concat(noScrollbarsClassName, \" {\\n   overflow: hidden \").concat(important, \";\\n   padding-right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  body[\").concat(lockAttribute, \"] {\\n    overflow: hidden \").concat(important, \";\\n    overscroll-behavior: contain;\\n    \").concat([\n        allowRelative && \"position: relative \".concat(important, \";\"),\n        gapMode === 'margin' &&\n            \"\\n    padding-left: \".concat(left, \"px;\\n    padding-top: \").concat(top, \"px;\\n    padding-right: \").concat(right, \"px;\\n    margin-left:0;\\n    margin-top:0;\\n    margin-right: \").concat(gap, \"px \").concat(important, \";\\n    \"),\n        gapMode === 'padding' && \"padding-right: \".concat(gap, \"px \").concat(important, \";\"),\n    ]\n        .filter(Boolean)\n        .join(''), \"\\n  }\\n  \\n  .\").concat(zeroRightClassName, \" {\\n    right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  \\n  .\").concat(fullWidthClassName, \" {\\n    margin-right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  \\n  .\").concat(zeroRightClassName, \" .\").concat(zeroRightClassName, \" {\\n    right: 0 \").concat(important, \";\\n  }\\n  \\n  .\").concat(fullWidthClassName, \" .\").concat(fullWidthClassName, \" {\\n    margin-right: 0 \").concat(important, \";\\n  }\\n  \\n  body[\").concat(lockAttribute, \"] {\\n    \").concat(removedBarSizeVariable, \": \").concat(gap, \"px;\\n  }\\n\");\n};\nvar getCurrentUseCounter = function () {\n    var counter = parseInt(document.body.getAttribute(lockAttribute) || '0', 10);\n    return isFinite(counter) ? counter : 0;\n};\nexport var useLockAttribute = function () {\n    React.useEffect(function () {\n        document.body.setAttribute(lockAttribute, (getCurrentUseCounter() + 1).toString());\n        return function () {\n            var newCounter = getCurrentUseCounter() - 1;\n            if (newCounter <= 0) {\n                document.body.removeAttribute(lockAttribute);\n            }\n            else {\n                document.body.setAttribute(lockAttribute, newCounter.toString());\n            }\n        };\n    }, []);\n};\n/**\n * Removes page scrollbar and blocks page scroll when mounted\n */\nexport var RemoveScrollBar = function (_a) {\n    var noRelative = _a.noRelative, noImportant = _a.noImportant, _b = _a.gapMode, gapMode = _b === void 0 ? 'margin' : _b;\n    useLockAttribute();\n    /*\n     gap will be measured on every component mount\n     however it will be used only by the \"first\" invocation\n     due to singleton nature of <Style\n     */\n    var gap = React.useMemo(function () { return getGapWidth(gapMode); }, [gapMode]);\n    return React.createElement(Style, { styles: getStyles(gap, !noRelative, gapMode, !noImportant ? '!important' : '') });\n};\n", "/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nexport function __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nexport var __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nexport function __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nexport function __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nexport function __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nexport function __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nexport function __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nexport function __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nexport function __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nexport function __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nexport function __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n  return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nexport function __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nexport function __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nexport function __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nexport function __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nexport function __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nexport function __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nexport function __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nexport function __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n  function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nexport function __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nexport function __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nexport function __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nvar ownKeys = function(o) {\n  ownKeys = Object.getOwnPropertyNames || function (o) {\n    var ar = [];\n    for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n    return ar;\n  };\n  return ownKeys(o);\n};\n\nexport function __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nexport function __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nexport function __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nexport function __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose, inner;\n    if (async) {\n      if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n      dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n      if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n      dispose = value[Symbol.dispose];\n      if (async) inner = dispose;\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nexport function __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  var r, s = 0;\n  function next() {\n    while (r = env.stack.pop()) {\n      try {\n        if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\n        if (r.dispose) {\n          var result = r.dispose.call(r.value);\n          if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n        }\n        else s |= 1;\n      }\n      catch (e) {\n        fail(e);\n      }\n    }\n    if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nexport function __rewriteRelativeImportExtension(path, preserveJsx) {\n  if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\n      return path.replace(/\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+?)?)\\.([cm]?)ts$/i, function (m, tsx, d, ext, cm) {\n          return tsx ? preserveJsx ? \".jsx\" : \".js\" : d && (!ext || !cm) ? m : (d + ext + \".\" + cm.toLowerCase() + \"js\");\n      });\n  }\n  return path;\n}\n\nexport default {\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __esDecorate,\n  __runInitializers,\n  __propKey,\n  __setFunctionName,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n  __rewriteRelativeImportExtension,\n};\n", "/**\n * Assigns a value for a given ref, no matter of the ref format\n * @param {RefObject} ref - a callback function or ref object\n * @param value - a new value\n *\n * @see https://github.com/theKashey/use-callback-ref#assignref\n * @example\n * const refObject = useRef();\n * const refFn = (ref) => {....}\n *\n * assignRef(refObject, \"refValue\");\n * assignRef(refFn, \"refValue\");\n */\nexport function assignRef(ref, value) {\n    if (typeof ref === 'function') {\n        ref(value);\n    }\n    else if (ref) {\n        ref.current = value;\n    }\n    return ref;\n}\n", "import * as React from 'react';\nimport { assignRef } from './assignRef';\nimport { useCallbackRef } from './useRef';\nvar useIsomorphicLayoutEffect = typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\nvar currentValues = new WeakMap();\n/**\n * Merges two or more refs together providing a single interface to set their value\n * @param {RefObject|Ref} refs\n * @returns {MutableRefObject} - a new ref, which translates all changes to {refs}\n *\n * @see {@link mergeRefs} a version without buit-in memoization\n * @see https://github.com/theKashey/use-callback-ref#usemergerefs\n * @example\n * const Component = React.forwardRef((props, ref) => {\n *   const ownRef = useRef();\n *   const domRef = useMergeRefs([ref, ownRef]); // 👈 merge together\n *   return <div ref={domRef}>...</div>\n * }\n */\nexport function useMergeRefs(refs, defaultValue) {\n    var callbackRef = useCallbackRef(defaultValue || null, function (newValue) {\n        return refs.forEach(function (ref) { return assignRef(ref, newValue); });\n    });\n    // handle refs changes - added or removed\n    useIsomorphicLayoutEffect(function () {\n        var oldValue = currentValues.get(callbackRef);\n        if (oldValue) {\n            var prevRefs_1 = new Set(oldValue);\n            var nextRefs_1 = new Set(refs);\n            var current_1 = callbackRef.current;\n            prevRefs_1.forEach(function (ref) {\n                if (!nextRefs_1.has(ref)) {\n                    assignRef(ref, null);\n                }\n            });\n            nextRefs_1.forEach(function (ref) {\n                if (!prevRefs_1.has(ref)) {\n                    assignRef(ref, current_1);\n                }\n            });\n        }\n        currentValues.set(callbackRef, refs);\n    }, [refs]);\n    return callbackRef;\n}\n", "import { useState } from 'react';\n/**\n * creates a MutableRef with ref change callback\n * @param initialValue - initial ref value\n * @param {Function} callback - a callback to run when value changes\n *\n * @example\n * const ref = useCallbackRef(0, (newValue, oldValue) => console.log(oldValue, '->', newValue);\n * ref.current = 1;\n * // prints 0 -> 1\n *\n * @see https://reactjs.org/docs/hooks-reference.html#useref\n * @see https://github.com/theKashey/use-callback-ref#usecallbackref---to-replace-reactuseref\n * @returns {MutableRefObject}\n */\nexport function useCallbackRef(initialValue, callback) {\n    var ref = useState(function () { return ({\n        // value\n        value: initialValue,\n        // last callback\n        callback: callback,\n        // \"memoized\" public interface\n        facade: {\n            get current() {\n                return ref.value;\n            },\n            set current(value) {\n                var last = ref.value;\n                if (last !== value) {\n                    ref.value = value;\n                    ref.callback(value, last);\n                }\n            },\n        },\n    }); })[0];\n    // update callback\n    ref.callback = callback;\n    return ref.facade;\n}\n", "var currentNonce;\nexport var setNonce = function (nonce) {\n    currentNonce = nonce;\n};\nexport var getNonce = function () {\n    if (currentNonce) {\n        return currentNonce;\n    }\n    if (typeof __webpack_nonce__ !== 'undefined') {\n        return __webpack_nonce__;\n    }\n    return undefined;\n};\n", "import { getNonce } from 'get-nonce';\nfunction makeStyleTag() {\n    if (!document)\n        return null;\n    var tag = document.createElement('style');\n    tag.type = 'text/css';\n    var nonce = getNonce();\n    if (nonce) {\n        tag.setAttribute('nonce', nonce);\n    }\n    return tag;\n}\nfunction injectStyles(tag, css) {\n    // @ts-ignore\n    if (tag.styleSheet) {\n        // @ts-ignore\n        tag.styleSheet.cssText = css;\n    }\n    else {\n        tag.appendChild(document.createTextNode(css));\n    }\n}\nfunction insertStyleTag(tag) {\n    var head = document.head || document.getElementsByTagName('head')[0];\n    head.appendChild(tag);\n}\nexport var stylesheetSingleton = function () {\n    var counter = 0;\n    var stylesheet = null;\n    return {\n        add: function (style) {\n            if (counter == 0) {\n                if ((stylesheet = makeStyleTag())) {\n                    injectStyles(stylesheet, style);\n                    insertStyleTag(stylesheet);\n                }\n            }\n            counter++;\n        },\n        remove: function () {\n            counter--;\n            if (!counter && stylesheet) {\n                stylesheet.parentNode && stylesheet.parentNode.removeChild(stylesheet);\n                stylesheet = null;\n            }\n        },\n    };\n};\n", "import { styleHook<PERSON>ingleton } from './hook';\n/**\n * create a Component to add styles on demand\n * - styles are added when first instance is mounted\n * - styles are removed when the last instance is unmounted\n * - changing styles in runtime does nothing unless dynamic is set. But with multiple components that can lead to the undefined behavior\n */\nexport var styleSingleton = function () {\n    var useStyle = styleHookSingleton();\n    var Sheet = function (_a) {\n        var styles = _a.styles, dynamic = _a.dynamic;\n        useStyle(styles, dynamic);\n        return null;\n    };\n    return Sheet;\n};\n", "import * as React from 'react';\nimport { stylesheetSingleton } from './singleton';\n/**\n * creates a hook to control style singleton\n * @see {@link styleSingleton} for a safer component version\n * @example\n * ```tsx\n * const useStyle = styleHookSingleton();\n * ///\n * useStyle('body { overflow: hidden}');\n */\nexport var styleHookSingleton = function () {\n    var sheet = stylesheetSingleton();\n    return function (styles, isDynamic) {\n        React.useEffect(function () {\n            sheet.add(styles);\n            return function () {\n                sheet.remove();\n            };\n        }, [styles && isDynamic]);\n    };\n};\n"], "names": ["ItoI", "a", "innerCreateMedium", "defaults", "middleware", "buffer", "assigned", "read", "Error", "length", "useMedium", "data", "item", "push", "filter", "x", "assignSyncMedium", "cb", "cbs", "for<PERSON>ach", "assignMedium", "pendingQueue", "executeQueue", "cycle", "Promise", "resolve", "then", "createSidecarMedium", "options", "medium", "__assign", "async", "ssr", "zeroRightClassName", "fullWidthClassName", "noScrollbarsClassName", "removedBarSizeVariable", "SideCar", "_a", "sideCar", "rest", "__rest", "Target", "React", "exportSidecar", "exported", "isSideCarExport", "getDefaultParent", "originalTarget", "document", "Array", "isArray", "ownerDocument", "body", "counterMap", "WeakMap", "uncontrolledNodes", "markerMap", "lockCount", "unwrapHost", "node", "host", "parentNode", "applyAttributeToOthers", "markerName", "controlAttribute", "targets", "parent", "map", "target", "contains", "<PERSON><PERSON><PERSON><PERSON>", "console", "error", "Boolean", "correctTargets", "markerCounter", "hiddenNodes", "elementsToKeep", "Set", "elementsToStop", "keep", "el", "has", "add", "deep", "prototype", "call", "children", "attr", "getAttribute", "alreadyHidden", "counterValue", "get", "markerValue", "set", "setAttribute", "e", "clear", "removeAttribute", "delete", "hideOthers", "from", "activeParentNode", "apply", "querySelectorAll", "zeroGap", "left", "top", "right", "gap", "parse", "parseInt", "getGapWidth", "gapMode", "window", "offsets", "cs", "getComputedStyle", "getOffset", "documentWidth", "documentElement", "clientWidth", "windowWidth", "innerWidth", "Math", "max", "Style", "styleSingleton", "lockAttribute", "getStyles", "allowRelative", "important", "concat", "join", "getCurrentUseCounter", "counter", "isFinite", "RemoveScrollBar", "noRelative", "noImportant", "_b", "toString", "newCounter", "styles", "Object", "assign", "t", "s", "i", "n", "arguments", "p", "hasOwnProperty", "this", "indexOf", "getOwnPropertySymbols", "propertyIsEnumerable", "create", "__spread<PERSON><PERSON>y", "to", "pack", "ar", "l", "slice", "SuppressedError", "assignRef", "ref", "value", "current", "useIsomorphicLayoutEffect", "currentV<PERSON>ues", "useMergeRefs", "refs", "defaultValue", "callback<PERSON><PERSON>", "initialValue", "callback", "useState", "facade", "last", "useCallbackRef", "newValue", "oldValue", "prevRefs_1", "nextRefs_1", "current_1", "currentNonce", "makeStyleTag", "tag", "createElement", "type", "nonce", "__webpack_nonce__", "stylesheetSingleton", "stylesheet", "style", "css", "styleSheet", "cssText", "append<PERSON><PERSON><PERSON>", "createTextNode", "head", "getElementsByTagName", "insertStyleTag", "remove", "<PERSON><PERSON><PERSON><PERSON>", "useStyle", "sheet", "isDynamic", "styleHookSingleton", "dynamic"], "sourceRoot": ""}