const ApiError = require('../utils/ApiError');
const httpStatus = require('http-status');
const logger = require('../utils/logger');

/**
 * Role-based access control middleware
 * @param {Array<String>} allowedRoles - Array of allowed roles
 * @returns {Function} Express middleware function
 */
const checkRole = (allowedRoles = []) => {
  if (!Array.isArray(allowedRoles)) {
    throw new Error('Allowed roles must be an array');
  }

  return (req, res, next) => {
    try {
      // Get user from request (assuming user is attached to request by auth middleware)
      const user = req.user;
      
      if (!user) {
        logger.warn('Unauthorized: No user found in request');
        throw new ApiError(httpStatus.UNAUTHORIZED, 'Unauthorized - Please authenticate');
      }

      // Check if user has one of the allowed roles
      if (!allowedRoles.includes(user.role)) {
        logger.warn(`Forbidden: User ${user.id} with role ${user.role} attempted to access ${req.originalUrl}`);
        throw new ApiError(httpStatus.FORBIDDEN, 'Forbidden - Insufficient permissions');
      }

      // If user has required role, continue to next middleware
      next();
    } catch (error) {
      next(error);
    }
  };
};

// Predefined roles for easier use
const roles = {
  admin: ['admin'],
  merchant: ['merchant', 'admin'],
  trader: ['trader', 'merchant', 'admin'],
  all: ['admin', 'merchant', 'trader']
};

// Middleware factory for specific roles
const requireRole = {
  admin: checkRole(roles.admin),
  merchant: checkRole(roles.merchant),
  trader: checkRole(roles.trader),
  any: checkRole(roles.all),
  custom: checkRole
};

module.exports = requireRole;
