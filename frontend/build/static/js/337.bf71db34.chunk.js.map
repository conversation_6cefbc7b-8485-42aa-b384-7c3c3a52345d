{"version": 3, "file": "static/js/337.bf71db34.chunk.js", "mappings": "sOAIA,MAAMA,EAAQC,EAAAA,WAGZ,CAAAC,EAA0BC,KAAG,IAA5B,UAAEC,KAAcC,GAAOH,EAAA,OACxBI,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uBAAsBG,UACnCD,EAAAA,EAAAA,KAAA,SACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IAAG,gCAAiCJ,MAC3CC,QAIVL,EAAMS,YAAc,QAEpB,MAAMC,EAAcT,EAAAA,WAGlB,CAAAU,EAA0BR,KAAG,IAA5B,UAAEC,KAAcC,GAAOM,EAAA,OACxBL,EAAAA,EAAAA,KAAA,SAAOH,IAAKA,EAAKC,WAAWI,EAAAA,EAAAA,IAAG,kBAAmBJ,MAAgBC,MAEpEK,EAAYD,YAAc,cAE1B,MAAMG,EAAYX,EAAAA,WAGhB,CAAAY,EAA0BV,KAAG,IAA5B,UAAEC,KAAcC,GAAOQ,EAAA,OACxBP,EAAAA,EAAAA,KAAA,SACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IAAG,6BAA8BJ,MACxCC,MAGRO,EAAUH,YAAc,YAEJR,EAAAA,WAGlB,CAAAa,EAA0BX,KAAG,IAA5B,UAAEC,KAAcC,GAAOS,EAAA,OACxBR,EAAAA,EAAAA,KAAA,SACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IAAG,iDAAkDJ,MAC5DC,MAGII,YAAc,cAE1B,MAAMM,EAAWd,EAAAA,WAGf,CAAAe,EAA0Bb,KAAG,IAA5B,UAAEC,KAAcC,GAAOW,EAAA,OACxBV,EAAAA,EAAAA,KAAA,MACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IACT,8EACAJ,MAEEC,MAGRU,EAASN,YAAc,WAEvB,MAAMQ,EAAYhB,EAAAA,WAGhB,CAAAiB,EAA0Bf,KAAG,IAA5B,UAAEC,KAAcC,GAAOa,EAAA,OACxBZ,EAAAA,EAAAA,KAAA,MACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IACT,mGACAJ,MAEEC,MAGRY,EAAUR,YAAc,YAExB,MAAMU,EAAYlB,EAAAA,WAGhB,CAAAmB,EAA0BjB,KAAG,IAA5B,UAAEC,KAAcC,GAAOe,EAAA,OACxBd,EAAAA,EAAAA,KAAA,MACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IAAG,iDAAkDJ,MAC5DC,MAGRc,EAAUV,YAAc,YAEHR,EAAAA,WAGnB,CAAAoB,EAA0BlB,KAAG,IAA5B,UAAEC,KAAcC,GAAOgB,EAAA,OACxBf,EAAAA,EAAAA,KAAA,WACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IAAG,qCAAsCJ,MAChDC,MAGKI,YAAc,c,6ICjG3B,MAAMa,EAASC,EAAAA,GAETC,EAAgBD,EAAAA,GAOhBE,EAAevB,IAIK,IAJJ,UACpBE,EAAS,SACTG,KACGF,GACeH,EAClB,MAAM,UAAEwB,EAAS,WAAEC,KAAeC,GAAcvB,EAC1CwB,EAAc,CAClBH,YACAC,WAAYA,GAEd,OACErB,EAAAA,EAAAA,KAACiB,EAAAA,GAAsB,IAAKM,EAAWtB,UACrCD,EAAAA,EAAAA,KAAA,OAAKF,WAAWI,EAAAA,EAAAA,IACd,qEACAJ,GACAG,SACCA,OAKTkB,EAAahB,YAAcc,EAAAA,GAAuBd,YAElD,MAAMqB,EAAgB7B,EAAAA,WAGpB,CAAAU,EAA0BR,KAAG,IAA5B,UAAEC,KAAcC,GAAOM,EAAA,OACxBL,EAAAA,EAAAA,KAACiB,EAAAA,GAAuB,CACtBpB,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IACT,qKACAJ,MAEEC,MAGRyB,EAAcrB,YAAcc,EAAAA,GAAwBd,YAEpD,MAAMsB,EAAgB9B,EAAAA,WAGpB,CAAAY,EAAoCV,KAAG,IAAtC,UAAEC,EAAS,SAAEG,KAAaF,GAAOQ,EAAA,OAClCmB,EAAAA,EAAAA,MAACP,EAAY,CAAAlB,SAAA,EACXD,EAAAA,EAAAA,KAACwB,EAAa,KACdE,EAAAA,EAAAA,MAACT,EAAAA,GAAuB,CACtBpB,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IACT,+NACA,mBACAJ,MAEEC,EAAKE,SAAA,CAERA,GACDyB,EAAAA,EAAAA,MAACT,EAAAA,GAAqB,CAACnB,UAAU,mTAAkTG,SAAA,EACjVD,EAAAA,EAAAA,KAAC2B,EAAAA,EAAC,CAAC7B,UAAU,aACbE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,UAASG,SAAC,qBAKlCwB,EAActB,YAAcc,EAAAA,GAAwBd,YAEpD,MAAMyB,EAAepB,IAAA,IAAC,UACpBV,KACGC,GACkCS,EAAA,OACrCR,EAAAA,EAAAA,KAAA,OACEF,WAAWI,EAAAA,EAAAA,IACT,mDACAJ,MAEEC,KAGR6B,EAAazB,YAAc,eAE3B,MAAM0B,EAAenB,IAAA,IAAC,UACpBZ,KACGC,GACkCW,EAAA,OACrCV,EAAAA,EAAAA,KAAA,OACEF,WAAWI,EAAAA,EAAAA,IACT,gEACAJ,MAEEC,KAGR8B,EAAa1B,YAAc,eAE3B,MAAM2B,EAAcnC,EAAAA,WAGlB,CAAAiB,EAA0Bf,KAAG,IAA5B,UAAEC,KAAcC,GAAOa,EAAA,OACxBZ,EAAAA,EAAAA,KAACiB,EAAAA,GAAqB,CACpBpB,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IACT,sCACA,oBACAJ,MAEEC,MAGR+B,EAAY3B,YAAcc,EAAAA,GAAsBd,YAEhD,MAAM4B,EAAoBpC,EAAAA,WAGxB,CAAAmB,EAA0BjB,KAAG,IAA5B,UAAEC,KAAcC,GAAOe,EAAA,OACxBd,EAAAA,EAAAA,KAACiB,EAAAA,GAA2B,CAC1BpB,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IAAG,wBAAyB,qBAAsBJ,MACzDC,MAGRgC,EAAkB5B,YAAcc,EAAAA,GAA4Bd,W,4EC9H5D,MAAM6B,GAAgBC,EAAAA,EAAAA,GACpB,8FAGIC,EAAQvC,EAAAA,WAGZ,CAAAC,EAA0BC,KAAG,IAA5B,UAAEC,KAAcC,GAAOH,EAAA,OACxBI,EAAAA,EAAAA,KAAA,SACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IAAG8B,IAAiBlC,MAC3BC,MAGRmC,EAAM/B,YAAc,O,iEChBpB,SAASgC,EAAQvC,GAGyB,IAHxB,UAChBE,KACGC,GACkCH,EACrC,OACEI,EAAAA,EAAAA,KAAA,OACEF,WAAWI,EAAAA,EAAAA,IAAG,wDAAyDJ,MACnEC,GAGV,C,kCCAM,MAAAqC,GAASC,E,QAAAA,GAAiB,SAAU,CACxC,CAAC,OAAQ,CAAEC,EAAG,8CAA+CC,IAAK,Y,6LCNzDC,GAAWC,EAAAA,EAAAA,IAAyB,CAC7CC,UAAW,WACXC,eAAgBC,EAAAA,EAChBC,wBAAyB,OACzBC,0BAA2B,CAAC,OAAQ,QACpCC,eAAgB,CAAC,CACfC,SAAU,QACVC,SAAUC,EAAAA,GACT,CACDF,SAAU,QACVC,SAAUE,EAAAA,IAEZC,cAAeA,EAAAA,K,4DCPX,MAAAC,GAAYhB,E,QAAAA,GAAiB,YAAa,CAC9C,CAAC,OAAQ,CAAEC,EAAG,4CAA6CC,IAAK,WAChE,CAAC,SAAU,CAAEe,GAAI,IAAKC,GAAI,IAAKC,EAAG,IAAKjB,IAAK,UAC5C,CAAC,WAAY,CAAEkB,OAAQ,mBAAoBlB,IAAK,a,+DCwBlD,MAGMmB,EAAiB,SAACC,GAAsD,IAAtCC,EAAgBC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,MACzD,OAAO,IAAIG,KAAKC,aAAa,QAAS,CACpCC,MAAO,WACPN,WACAO,sBAAuB,EACvBC,sBAAuB,IACtBC,OAAOV,EACZ,EAgbA,EAzZiCW,KAC/B,MAAM,KAAEC,EAAI,OAAEC,IAAWC,EAAAA,EAAAA,KACnBC,GAAWC,EAAAA,EAAAA,OACX,MAAEC,IAAUC,EAAAA,EAAAA,OAEXC,EAAWC,IAAgBC,EAAAA,EAAAA,WAAS,IACpCC,EAAOC,IAAYF,EAAAA,EAAAA,UAAyB,CACjDG,WAAY,EACZC,kBAAmB,EACnBC,aAAc,EACdC,gBAAiB,KAGZC,EAAoBC,IAAyBR,EAAAA,EAAAA,UAAwB,KACrES,EAAaC,IAAkBV,EAAAA,EAAAA,UAA4C,IAG5EW,EAAwB,CAC5B,CACEC,KAAM,cACNC,MAAOZ,EAAME,WAAWW,iBACxBC,KAAMC,EAAAA,EACNC,YAAa,yBACbC,OAAQ,SACRC,MAAO,MAET,CACEP,KAAM,qBACNC,MAAOZ,EAAMG,kBAAkBU,iBAC/BC,KAAMK,EAAAA,EACNH,YAAa,wBACbC,OAAQ,QACRC,MAAO,MAET,CACEP,KAAM,gBACNC,MAAOnC,EAAeuB,EAAMI,cAC5BU,KAAMM,EAAAA,EACNJ,YAAa,0BACbC,OAAQ,SACRC,MAAO,MAET,CACEP,KAAM,mBACNC,MAAOZ,EAAMK,gBAAgBgB,WAC7BP,KAAM1C,EACN4C,YAAa,6BACbC,OAAQ,QACRC,MAAO,OAKLI,EAAqBC,UACzB,IACEzB,GAAa,SAQP,IAAI0B,QAAQC,GAAWC,WAAWD,EAAS,MAGjDxB,EAAS,CACPC,WAAY,KACZC,kBAAmB,MACnBC,aAAc,UACdC,gBAAiB,KAGnBE,EAAsB,CACpB,CACEoB,GAAI,MAAQC,KAAKC,MAAsB,IAAhBD,KAAKE,UAC5BC,MAAM,IAAIC,MAAOC,cACjBC,SAAU,gBACVxD,OAAQ,MACRyD,OAAQ,YACRxD,SAAU,OAEZ,CACEgD,GAAI,MAAQC,KAAKC,MAAsB,IAAhBD,KAAKE,UAC5BC,KAAM,IAAIC,KAAKA,KAAKI,MAAQ,OAAUH,cACtCC,SAAU,mBACVxD,OAAQ,MACRyD,OAAQ,UACRxD,SAAU,OAEZ,CACEgD,GAAI,MAAQC,KAAKC,MAAsB,IAAhBD,KAAKE,UAC5BC,KAAM,IAAIC,KAAKA,KAAKI,MAAQ,QAAWH,cACvCC,SAAU,eACVxD,OAAQ,IACRyD,OAAQ,YACRxD,SAAU,SAId8B,EAAe,CACb,CAAEE,KAAM,MAAO0B,QAAS,KACxB,CAAE1B,KAAM,MAAO0B,QAAS,KACxB,CAAE1B,KAAM,MAAO0B,QAAS,KACxB,CAAE1B,KAAM,MAAO0B,QAAS,MACxB,CAAE1B,KAAM,MAAO0B,QAAS,MACxB,CAAE1B,KAAM,MAAO0B,QAAS,OAG5B,CAAE,MAAOC,GACPC,QAAQD,MAAM,iCAAkCA,GAChD3C,EAAM,CACJ6C,MAAO,QACPxB,YAAa,mDACbyB,QAAS,eAEb,CAAC,QACC3C,GAAa,EACf,IAGF4C,EAAAA,EAAAA,WAAU,KACRpB,IAGA,MAAMqB,EAAWC,YAAYtB,EAAoB,KAEjD,MAAO,IAAMuB,cAAcF,IAC1B,IAgBH,OACElG,EAAAA,EAAAA,MAAA,OAAK5B,UAAU,0BAAyBG,SAAA,EACtCD,EAAAA,EAAAA,KAAA,UAAQF,UAAU,kBAAiBG,UACjCyB,EAAAA,EAAAA,MAAA,OAAK5B,UAAU,gFAA+EG,SAAA,EAC5FyB,EAAAA,EAAAA,MAAA,OAAAzB,SAAA,EACED,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mCAAkCG,SAAC,qBACjDyB,EAAAA,EAAAA,MAAA,KAAG5B,UAAU,wBAAuBG,SAAA,CAAC,kBAAmB,OAAJsE,QAAI,IAAJA,OAAI,EAAJA,EAAMqB,OAAQ,eAEpElE,EAAAA,EAAAA,MAAA,OAAK5B,UAAU,8BAA6BG,SAAA,EAC1CyB,EAAAA,EAAAA,MAACqG,EAAAA,EAAM,CACLL,QAAQ,UACRM,KAAK,KACLC,QAAS1B,EACT2B,SAAUpD,EAAU7E,SAAA,EAEpBD,EAAAA,EAAAA,KAACmI,EAAAA,EAAS,CAACrI,UAAW,iBAAgBgF,EAAY,eAAiB,MAAQ,cAG7EpD,EAAAA,EAAAA,MAACqG,EAAAA,EAAM,CAACL,QAAQ,UAAUM,KAAK,KAAKC,QAhCzBzB,UACnB,UACQhC,IACNE,EAAS,SACX,CAAE,MAAO6C,GACPC,QAAQD,MAAM,gBAAiBA,GAC/B3C,EAAM,CACJ6C,MAAO,QACPxB,YAAa,uCACbyB,QAAS,eAEb,GAqBkEzH,SAAA,EACxDD,EAAAA,EAAAA,KAACoI,EAAAA,EAAM,CAACtI,UAAU,iBAAiB,qBAO3C4B,EAAAA,EAAAA,MAAA,QAAM5B,UAAU,yCAAwCG,SAAA,EAEtDD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gDAA+CG,SAC3D6E,EAECuD,MAAM,GAAGC,KAAK,GAAGC,IAAI,CAACC,EAAGC,KACvB/G,EAAAA,EAAAA,MAACgH,EAAAA,GAAI,CAAAzI,SAAA,EACHyB,EAAAA,EAAAA,MAACiH,EAAAA,GAAU,CAAC7I,UAAU,YAAWG,SAAA,EAC/BD,EAAAA,EAAAA,KAACmC,EAAAA,EAAQ,CAACrC,UAAU,cACpBE,EAAAA,EAAAA,KAACmC,EAAAA,EAAQ,CAACrC,UAAU,iBAEtBE,EAAAA,EAAAA,KAAC4I,EAAAA,GAAW,CAAA3I,UACVD,EAAAA,EAAAA,KAACmC,EAAAA,EAAQ,CAACrC,UAAU,iBANb,iBAAiB2I,MAY9B9C,EAAU4C,IAAKM,IACbnH,EAAAA,EAAAA,MAACgH,EAAAA,GAAI,CAAiB5I,UAAU,oCAAmCG,SAAA,EACjED,EAAAA,EAAAA,KAAC2I,EAAAA,GAAU,CAAC7I,UAAU,OAAMG,UAC1ByB,EAAAA,EAAAA,MAAA,OAAK5B,UAAU,oCAAmCG,SAAA,EAChDD,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4CAA2CG,SACrD4I,EAAKjD,QAER5F,EAAAA,EAAAA,KAAC6I,EAAK9C,KAAI,CAACjG,UAAU,wCAGzB4B,EAAAA,EAAAA,MAACkH,EAAAA,GAAW,CAAA3I,SAAA,EACVD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qBAAoBG,SAAE4I,EAAKhD,SAC1CnE,EAAAA,EAAAA,MAAA,KAAG5B,UAAU,qCAAoCG,SAAA,EAC/CD,EAAAA,EAAAA,KAAA,QAAMF,UAA0B,OAAf+I,EAAK1C,MAAiB,iBAAmB,eAAelG,SACtE4I,EAAK3C,SACA,IACP2C,EAAK5C,oBAfD4C,EAAKjD,UAuBtBlE,EAAAA,EAAAA,MAACoH,EAAAA,GAAI,CAACC,aAAa,WAAWjJ,UAAU,YAAWG,SAAA,EACjDyB,EAAAA,EAAAA,MAAA,OAAK5B,UAAU,oCAAmCG,SAAA,EAChDyB,EAAAA,EAAAA,MAACsH,EAAAA,GAAQ,CAAA/I,SAAA,EACPD,EAAAA,EAAAA,KAACiJ,EAAAA,GAAW,CAACpD,MAAM,WAAU5F,SAAC,cAC9BD,EAAAA,EAAAA,KAACiJ,EAAAA,GAAW,CAACpD,MAAM,eAAc5F,SAAC,kBAClCD,EAAAA,EAAAA,KAACiJ,EAAAA,GAAW,CAACpD,MAAM,UAAS5F,SAAC,aAC7ByB,EAAAA,EAAAA,MAACuH,EAAAA,GAAW,CAACpD,MAAM,WAAU5F,SAAA,EAC3BD,EAAAA,EAAAA,KAACoC,EAAAA,EAAM,CAACtC,UAAU,iBAAiB,kBAIvC4B,EAAAA,EAAAA,MAAA,OAAK5B,UAAU,gCAA+BG,SAAA,CAAC,kBAC9B,IAAIgH,MAAOiC,4BAI9BlJ,EAAAA,EAAAA,KAACmJ,EAAAA,GAAW,CAACtD,MAAM,WAAW/F,UAAU,YAAWG,UACjDyB,EAAAA,EAAAA,MAACgH,EAAAA,GAAI,CAAAzI,SAAA,EACHyB,EAAAA,EAAAA,MAACiH,EAAAA,GAAU,CAAA1I,SAAA,EACTD,EAAAA,EAAAA,KAACoJ,EAAAA,GAAS,CAAAnJ,SAAC,sBACXD,EAAAA,EAAAA,KAACqJ,EAAAA,GAAe,CAAApJ,SAAC,6CAInBD,EAAAA,EAAAA,KAAC4I,EAAAA,GAAW,CAAC9I,UAAU,OAAMG,SAC1B6E,GACC9E,EAAAA,EAAAA,KAAA,OAAKF,UAAU,6CAA4CG,UACzDD,EAAAA,EAAAA,KAACmI,EAAAA,EAAS,CAACrI,UAAU,kDAGvBE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWG,UACxBD,EAAAA,EAAAA,KAACsJ,EAAAA,EAAmB,CAACC,MAAM,OAAOC,OAAO,OAAMvJ,UAC7CyB,EAAAA,EAAAA,MAACc,EAAQ,CAACiH,KAAMhE,EAAYxF,SAAA,EAC1BD,EAAAA,EAAAA,KAAC0J,EAAAA,EAAa,CAACC,gBAAgB,MAAMC,OAAO,aAC5C5J,EAAAA,EAAAA,KAACkD,EAAAA,EAAK,CACJ2G,QAAQ,OACRC,KAAM,CAAExB,KAAM,WACdyB,UAAU,KAEZ/J,EAAAA,EAAAA,KAACmD,EAAAA,EAAK,CACJ2G,KAAM,CAAExB,KAAM,WACdyB,UAAU,EACVC,cAAgBnE,GAAU,IAAIA,EAAMC,sBAEtC9F,EAAAA,EAAAA,KAACiK,EAAAA,EAAO,CACNC,UAAYrE,GAAkB,CAAC,IAAIA,EAAMC,mBAAoB,WAC7DqE,eAAiBC,GAAU,UAAUA,OAEvCpK,EAAAA,EAAAA,KAACqK,EAAAA,EAAM,KACPrK,EAAAA,EAAAA,KAAC4C,EAAAA,EAAG,CACFiH,QAAQ,UACRjE,KAAK,kBACL0C,KAAK,UACLgC,OAAQ,CAAC,EAAG,EAAG,EAAG,oBAUlCtK,EAAAA,EAAAA,KAACmJ,EAAAA,GAAW,CAACtD,MAAM,eAAc5F,UAC/ByB,EAAAA,EAAAA,MAACgH,EAAAA,GAAI,CAAAzI,SAAA,EACHD,EAAAA,EAAAA,KAAC2I,EAAAA,GAAU,CAAA1I,UACTyB,EAAAA,EAAAA,MAAA,OAAK5B,UAAU,oCAAmCG,SAAA,EAChDyB,EAAAA,EAAAA,MAAA,OAAAzB,SAAA,EACED,EAAAA,EAAAA,KAACoJ,EAAAA,GAAS,CAAAnJ,SAAC,yBACXD,EAAAA,EAAAA,KAACqJ,EAAAA,GAAe,CAAApJ,SAAC,iDAInByB,EAAAA,EAAAA,MAACqG,EAAAA,EAAM,CAACL,QAAQ,UAAUM,KAAK,KAAKC,QAAS1B,EAAmBtG,SAAA,EAC9DD,EAAAA,EAAAA,KAACmI,EAAAA,EAAS,CAACrI,UAAW,iBAAgBgF,EAAY,eAAiB,MAAQ,mBAKjF9E,EAAAA,EAAAA,KAAC4I,EAAAA,GAAW,CAAA3I,SACT6E,GACC9E,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWG,SACvBoI,MAAM,GAAGC,KAAK,GAAGC,IAAI,CAACC,EAAGC,KACxBzI,EAAAA,EAAAA,KAACmC,EAAAA,EAAQ,CAA2BrC,UAAU,eAA/B,gBAAgB2I,SAInCzI,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oBAAmBG,UAChCyB,EAAAA,EAAAA,MAAChC,EAAAA,GAAK,CAAAO,SAAA,EACJD,EAAAA,EAAAA,KAACI,EAAAA,GAAW,CAAAH,UACVyB,EAAAA,EAAAA,MAACjB,EAAAA,GAAQ,CAAAR,SAAA,EACPD,EAAAA,EAAAA,KAACW,EAAAA,GAAS,CAAAV,SAAC,QACXD,EAAAA,EAAAA,KAACW,EAAAA,GAAS,CAAAV,SAAC,iBACXD,EAAAA,EAAAA,KAACW,EAAAA,GAAS,CAAAV,SAAC,cACXD,EAAAA,EAAAA,KAACW,EAAAA,GAAS,CAACb,UAAU,aAAYG,SAAC,YAClCD,EAAAA,EAAAA,KAACW,EAAAA,GAAS,CAAAV,SAAC,iBAGfD,EAAAA,EAAAA,KAACM,EAAAA,GAAS,CAAAL,SACPsF,EAAmBzB,OAAS,EAC3ByB,EAAmBgD,IAAKgC,IACtB7I,SAAAA,EAAAA,MAACjB,EAAAA,GAAQ,CAAcX,UAAU,mBAAkBG,SAAA,EACjDD,EAAAA,EAAAA,KAACa,EAAAA,GAAS,CAACf,UAAU,cAAaG,UAChCD,EAAAA,EAAAA,KAAA,QAAMF,UAAU,oBAAmBG,SAAEsK,EAAI3D,QAE3C5G,EAAAA,EAAAA,KAACa,EAAAA,GAAS,CAAAZ,UACRD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,UAASG,UAlVpCuK,EAmV0BD,EAAIvD,KAlVzC,IAAIC,KAAKuD,GAAY1E,eAAe,QAAS,CAClD2E,KAAM,UACNC,MAAO,QACPC,IAAK,UACLC,KAAM,UACNC,OAAQ,kBAgVkB7K,EAAAA,EAAAA,KAACa,EAAAA,GAAS,CAAAZ,UACRD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,cAAaG,SAAEsK,EAAIpD,cAEpCnH,EAAAA,EAAAA,KAACa,EAAAA,GAAS,CAACf,UAAU,yBAAwBG,SAC1CyD,EAAe6G,EAAI5G,OAAQ4G,EAAI3G,aAElC5D,EAAAA,EAAAA,KAACa,EAAAA,GAAS,CAAAZ,UACRD,EAAAA,EAAAA,KAAA,QACEF,UAAW,4EACM,cAAfyK,EAAInD,OACA,8BACe,YAAfmD,EAAInD,OACJ,gCACA,2BACHnH,SAEFsK,EAAInD,OAAO0D,OAAO,GAAGC,cAAgBR,EAAInD,OAAO4D,MAAM,SAzB9CT,EAAI3D,IA7U3B4D,SA4WMxK,EAAAA,EAAAA,KAACS,EAAAA,GAAQ,CAAAR,UACPD,EAAAA,EAAAA,KAACa,EAAAA,GAAS,CAACoK,QAAS,EAAGnL,UAAU,yCAAwCG,SAAC,2CAa5FD,EAAAA,EAAAA,KAACmJ,EAAAA,GAAW,CAACtD,MAAM,UAAS5F,UAC1ByB,EAAAA,EAAAA,MAACgH,EAAAA,GAAI,CAAAzI,SAAA,EACHyB,EAAAA,EAAAA,MAACiH,EAAAA,GAAU,CAAA1I,SAAA,EACTD,EAAAA,EAAAA,KAACoJ,EAAAA,GAAS,CAAAnJ,SAAC,uBACXD,EAAAA,EAAAA,KAACqJ,EAAAA,GAAe,CAAApJ,SAAC,6CAInBD,EAAAA,EAAAA,KAAC4I,EAAAA,GAAW,CAAA3I,UACVD,EAAAA,EAAAA,KAACkL,EAAAA,iBAAgB,YAKvBlL,EAAAA,EAAAA,KAACmJ,EAAAA,GAAW,CAACtD,MAAM,WAAU5F,UAC3ByB,EAAAA,EAAAA,MAACgH,EAAAA,GAAI,CAAAzI,SAAA,EACHyB,EAAAA,EAAAA,MAACiH,EAAAA,GAAU,CAAA1I,SAAA,EACTD,EAAAA,EAAAA,KAACoJ,EAAAA,GAAS,CAAAnJ,SAAC,uBACXD,EAAAA,EAAAA,KAACqJ,EAAAA,GAAe,CAAApJ,SAAC,qDAInByB,EAAAA,EAAAA,MAACkH,EAAAA,GAAW,CAAC9I,UAAU,YAAWG,SAAA,EAChCyB,EAAAA,EAAAA,MAAA,OAAK5B,UAAU,wBAAuBG,SAAA,EACpCD,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mBAAkBG,SAAC,+BACjCD,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qCAAoCG,SAAC,0DAGlDD,EAAAA,EAAAA,KAAC+H,EAAAA,EAAM,CAACL,QAAQ,UAAUM,KAAK,KAAI/H,SAAC,mBAKtCyB,EAAAA,EAAAA,MAAA,OAAK5B,UAAU,wBAAuBG,SAAA,EACpCD,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mBAAkBG,SAAC,wBACjCD,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qCAAoCG,SAAC,qCAGlDD,EAAAA,EAAAA,KAAC+H,EAAAA,EAAM,CAACL,QAAQ,UAAUM,KAAK,KAAI/H,SAAC,6C,kCCxchD,MAAAkI,GAAY9F,E,QAAAA,GAAiB,YAAa,CAC9C,CACE,OACA,CAAEC,EAAG,qDAAsDC,IAAK,WAElE,CAAC,OAAQ,CAAED,EAAG,aAAcC,IAAK,WACjC,CACE,OACA,CAAED,EAAG,sDAAuDC,IAAK,WAEnE,CAAC,OAAQ,CAAED,EAAG,YAAaC,IAAK,Y,yQCD5B4I,EAAc,UAGbC,EAAqBC,IAAqBC,EAAAA,EAAAA,GAAmBH,IAc7DI,EAAgBC,GAAoBJ,EAAwCD,GAU7EnK,EAAiCjB,IACrC,MAAM,cACJ0L,EAAA,SACAxL,EACAyL,KAAMC,EAAA,YACNC,EAAA,aACAC,EAAA,MACAC,GAAQ,GACN/L,EACEgM,EAAmBpM,EAAAA,OAA0B,MAC7CqM,EAAmBrM,EAAAA,OAA6B,OAC/C+L,EAAMO,IAAWC,EAAAA,EAAAA,GAAqB,CAC3CC,KAAMR,EACNS,YAAaR,IAAe,EAC5BS,SAAUR,EACVS,OAAQnB,IAGV,OACEoB,EAAAA,EAAAA,KAAChB,EAAA,CACCiB,MAAOf,EACPM,aACAC,aACAS,WAAWC,EAAAA,EAAAA,KACXC,SAASD,EAAAA,EAAAA,KACTE,eAAeF,EAAAA,EAAAA,KACfhB,OACAG,aAAcI,EACdY,aAAoBlN,EAAAA,YAAY,IAAMsM,EAASa,IAAcA,GAAW,CAACb,IACzEH,QAEC7L,cAKPe,EAAOb,YAAcgL,EAMrB,IAAM4B,EAAe,gBAMf7L,EAAsBvB,EAAAA,WAC1B,CAACI,EAAwCiN,KACvC,MAAM,cAAEvB,KAAkBwB,GAAiBlN,EACrCmN,EAAU1B,EAAiBuB,EAActB,GACzC0B,GAAqBC,EAAAA,EAAAA,GAAgBJ,EAAcE,EAAQnB,YACjE,OACEQ,EAAAA,EAAAA,KAACc,EAAAA,GAAUC,OAAV,CACCC,KAAK,SACL,gBAAc,SACd,gBAAeL,EAAQxB,KACvB,gBAAewB,EAAQT,UACvB,aAAYe,EAASN,EAAQxB,SACzBuB,EACJpN,IAAKsN,EACLlF,SAASwF,EAAAA,EAAAA,GAAqB1N,EAAMkI,QAASiF,EAAQL,kBAM7D3L,EAAcf,YAAc4M,EAM5B,IAAMW,EAAc,gBAGbC,EAAgBC,GAAoBxC,EAAwCsC,EAAa,CAC9FrM,gBAAY,IAiBRF,EAA6CpB,IACjD,MAAM,cAAE0L,EAAA,WAAepK,EAAA,SAAYpB,EAAA,UAAUmB,GAAcrB,EACrDmN,EAAU1B,EAAiBkC,EAAajC,GAC9C,OACEc,EAAAA,EAAAA,KAACoB,EAAA,CAAenB,MAAOf,EAAepK,aACnCpB,SAAMN,EAAAA,SAAS4I,IAAItI,EAAW4N,IAC7BtB,EAAAA,EAAAA,KAACuB,EAAAA,EAAA,CAASC,QAAS1M,GAAc6L,EAAQxB,KACvCzL,UAAAsM,EAAAA,EAAAA,KAACyB,EAAAA,EAAA,CAAgBC,SAAO,EAAC7M,YACtBnB,SAAA4N,UAQb1M,EAAahB,YAAcuN,EAM3B,IAAMQ,EAAe,gBAWf1M,EAAsB7B,EAAAA,WAC1B,CAACI,EAAwCiN,KACvC,MAAMmB,EAAgBP,EAAiBM,EAAcnO,EAAM0L,gBACrD,WAAEpK,EAAa8M,EAAc9M,cAAe+M,GAAiBrO,EAC7DmN,EAAU1B,EAAiB0C,EAAcnO,EAAM0L,eACrD,OAAOyB,EAAQpB,OACbS,EAAAA,EAAAA,KAACuB,EAAAA,EAAA,CAASC,QAAS1M,GAAc6L,EAAQxB,KACvCzL,UAAAsM,EAAAA,EAAAA,KAAC8B,EAAA,IAAsBD,EAAcvO,IAAKmN,MAE1C,OAIRxL,EAAcrB,YAAc+N,EAM5B,IAAMI,GAAOC,EAAAA,EAAAA,IAAW,8BAElBF,EAA0B1O,EAAAA,WAC9B,CAACI,EAA4CiN,KAC3C,MAAM,cAAEvB,KAAkB2C,GAAiBrO,EACrCmN,EAAU1B,EAAiB0C,EAAczC,GAC/C,OAGEc,EAAAA,EAAAA,KAACiC,EAAAA,EAAA,CAAaC,GAAIH,EAAMI,gBAAc,EAACC,OAAQ,CAACzB,EAAQlB,YACtD/L,UAAAsM,EAAAA,EAAAA,KAACc,EAAAA,GAAUuB,IAAV,CACC,aAAYpB,EAASN,EAAQxB,SACzB0C,EACJvO,IAAKmN,EAEL9I,MAAO,CAAE2K,cAAe,UAAWT,EAAalK,aAWpD4K,EAAe,gBAWfrN,EAAsB9B,EAAAA,WAC1B,CAACI,EAAwCiN,KACvC,MAAMmB,EAAgBP,EAAiBkB,EAAc/O,EAAM0L,gBACrD,WAAEpK,EAAa8M,EAAc9M,cAAe0N,GAAiBhP,EAC7DmN,EAAU1B,EAAiBsD,EAAc/O,EAAM0L,eACrD,OACEc,EAAAA,EAAAA,KAACuB,EAAAA,EAAA,CAASC,QAAS1M,GAAc6L,EAAQxB,KACtCzL,SAAAiN,EAAQpB,OACPS,EAAAA,EAAAA,KAACyC,EAAA,IAAuBD,EAAclP,IAAKmN,KAE3CT,EAAAA,EAAAA,KAAC0C,EAAA,IAA0BF,EAAclP,IAAKmN,QAOxDvL,EAActB,YAAc2O,EAQ5B,IAAME,EAA2BrP,EAAAA,WAC/B,CAACI,EAA4CiN,KAC3C,MAAME,EAAU1B,EAAiBsD,EAAc/O,EAAM0L,eAC/CO,EAAmBrM,EAAAA,OAAuB,MAC1CuP,GAAe9B,EAAAA,EAAAA,GAAgBJ,EAAcE,EAAQlB,WAAYA,GAQvE,OALMrM,EAAAA,UAAU,KACd,MAAMwP,EAAUnD,EAAWoD,QAC3B,GAAID,EAAS,OAAOE,EAAAA,EAAAA,IAAWF,IAC9B,KAGD5C,EAAAA,EAAAA,KAAC+C,EAAA,IACKvP,EACJF,IAAKqP,EAGLK,UAAWrC,EAAQxB,KACnB8D,6BAA2B,EAC3BC,kBAAkBhC,EAAAA,EAAAA,GAAqB1N,EAAM0P,iBAAmBC,IAC9DA,EAAMC,iBACNzC,EAAQnB,WAAWqD,SAASQ,UAE9BC,sBAAsBpC,EAAAA,EAAAA,GAAqB1N,EAAM8P,qBAAuBH,IACtE,MAAMI,EAAgBJ,EAAMK,OAAOD,cAC7BE,EAAyC,IAAzBF,EAAcxC,SAA0C,IAA1BwC,EAAcG,SACpB,IAAzBH,EAAcxC,QAAgB0C,IAIjCN,EAAMC,mBAI1BO,gBAAgBzC,EAAAA,EAAAA,GAAqB1N,EAAMmQ,eAAiBR,GAC1DA,EAAMC,sBASVV,EAA8BtP,EAAAA,WAClC,CAACI,EAA4CiN,KAC3C,MAAME,EAAU1B,EAAiBsD,EAAc/O,EAAM0L,eAC/C0E,EAAgCxQ,EAAAA,QAAO,GACvCyQ,EAAiCzQ,EAAAA,QAAO,GAE9C,OACE4M,EAAAA,EAAAA,KAAC+C,EAAA,IACKvP,EACJF,IAAKmN,EACLuC,WAAW,EACXC,6BAA6B,EAC7BC,iBAAmBC,IACjB3P,EAAM0P,mBAAmBC,GAEpBA,EAAMW,mBACJF,EAAwBf,SAASlC,EAAQnB,WAAWqD,SAASQ,QAElEF,EAAMC,kBAGRQ,EAAwBf,SAAU,EAClCgB,EAAyBhB,SAAU,GAErCkB,kBAAoBZ,IAClB3P,EAAMuQ,oBAAoBZ,GAErBA,EAAMW,mBACTF,EAAwBf,SAAU,EACM,gBAApCM,EAAMK,OAAOD,cAAcvC,OAC7B6C,EAAyBhB,SAAU,IAOvC,MAAMmB,EAASb,EAAMa,OACfC,EAAkBtD,EAAQnB,WAAWqD,SAASqB,SAASF,GACzDC,GAAiBd,EAAMC,iBAMa,YAApCD,EAAMK,OAAOD,cAAcvC,MAAsB6C,EAAyBhB,SAC5EM,EAAMC,sBAkCZL,EAA0B3P,EAAAA,WAC9B,CAACI,EAA4CiN,KAC3C,MAAM,cAAEvB,EAAA,UAAe8D,EAAA,gBAAWmB,EAAA,iBAAiBjB,KAAqBV,GAAiBhP,EACnFmN,EAAU1B,EAAiBsD,EAAcrD,GACzCO,EAAmBrM,EAAAA,OAAuB,MAC1CuP,GAAe9B,EAAAA,EAAAA,GAAgBJ,EAAchB,GAMnD,OAFA2E,EAAAA,EAAAA,OAGEC,EAAAA,EAAAA,MAAAC,EAAAA,SAAA,CACE5Q,SAAA,EAAAsM,EAAAA,EAAAA,KAACuE,EAAAA,EAAA,CACC7C,SAAO,EACP8C,MAAI,EACJC,QAASzB,EACT0B,iBAAkBP,EAClBQ,mBAAoBzB,EAEpBxP,UAAAsM,EAAAA,EAAAA,KAAC4E,EAAAA,GAAA,CACCC,KAAK,SACLxK,GAAIsG,EAAQT,UACZ,mBAAkBS,EAAQN,cAC1B,kBAAiBM,EAAQP,QACzB,aAAYa,EAASN,EAAQxB,SACzBqD,EACJlP,IAAKqP,EACLmC,UAAWA,IAAMnE,EAAQrB,cAAa,QAIxC+E,EAAAA,EAAAA,MAAAC,EAAAA,SAAA,CACE5Q,SAAA,EAAAsM,EAAAA,EAAAA,KAAC+E,EAAA,CAAa3E,QAASO,EAAQP,WAC/BJ,EAAAA,EAAAA,KAACgF,EAAA,CAAmBvF,aAAwBY,cAAeM,EAAQN,wBAYzE4E,EAAa,cAMb1P,EAAoBnC,EAAAA,WACxB,CAACI,EAAsCiN,KACrC,MAAM,cAAEvB,KAAkBgG,GAAe1R,EACnCmN,EAAU1B,EAAiBgG,EAAY/F,GAC7C,OAAOc,EAAAA,EAAAA,KAACc,EAAAA,GAAUqE,GAAV,CAAa9K,GAAIsG,EAAQP,WAAa8E,EAAY5R,IAAKmN,MAInElL,EAAY3B,YAAcqR,EAM1B,IAAMG,EAAmB,oBAMnB5P,EAA0BpC,EAAAA,WAC9B,CAACI,EAA4CiN,KAC3C,MAAM,cAAEvB,KAAkBmG,GAAqB7R,EACzCmN,EAAU1B,EAAiBmG,EAAkBlG,GACnD,OAAOc,EAAAA,EAAAA,KAACc,EAAAA,GAAUwE,EAAV,CAAYjL,GAAIsG,EAAQN,iBAAmBgF,EAAkB/R,IAAKmN,MAI9EjL,EAAkB5B,YAAcwR,EAMhC,IAAMG,EAAa,cAKbC,EAAoBpS,EAAAA,WACxB,CAACI,EAAsCiN,KACrC,MAAM,cAAEvB,KAAkBuG,GAAejS,EACnCmN,EAAU1B,EAAiBsG,EAAYrG,GAC7C,OACEc,EAAAA,EAAAA,KAACc,EAAAA,GAAUC,OAAV,CACCC,KAAK,YACDyE,EACJnS,IAAKmN,EACL/E,SAASwF,EAAAA,EAAAA,GAAqB1N,EAAMkI,QAAS,IAAMiF,EAAQrB,cAAa,QAUhF,SAAS2B,EAAS9B,GAChB,OAAOA,EAAO,OAAS,QACzB,CANAqG,EAAY5R,YAAc2R,EAQ1B,IAAMG,EAAqB,sBAEpBC,EAAiBC,IAAqBC,EAAAA,EAAAA,GAAcH,EAAoB,CAC7EI,YAAavD,EACbwD,UAAWd,EACXe,SAAU,WAKNjB,EAA4C1R,IAAiB,IAAhB,QAAE+M,GAAQ/M,EAC3D,MAAM4S,EAAsBL,EAAkBF,GAExCQ,EAAU,KAAKD,EAAoBH,8BAA8BG,EAAoBF,wGAEjEE,EAAoBF,gJAE4BE,EAAoBD,WAS9F,OAPM5S,EAAAA,UAAU,KACd,GAAIgN,EAAS,CACM+F,SAASC,eAAehG,IAC1BnF,QAAQD,MAAMkL,EAC/B,GACC,CAACA,EAAS9F,IAEN,MAUH4E,EAAwDlR,IAAmC,IAAlC,WAAE2L,EAAA,cAAYY,GAAcvM,EACzF,MACMoS,EAAU,6EADkBN,EARH,4BASwFE,gBAWvH,OATM1S,EAAAA,UAAU,KACd,MAAMiT,EAAgB5G,EAAWoD,SAASyD,aAAa,oBAEvD,GAAIjG,GAAiBgG,EAAe,CACXF,SAASC,eAAe/F,IAC1BpF,QAAQsL,KAAKL,EACpC,GACC,CAACA,EAASzG,EAAYY,IAElB,MAGHmG,EAAO/R,EACPgS,EAAU9R,EACV+R,GAAS9R,EACT+R,GAAU1R,EACV2R,GAAU1R,EACV2R,GAAQtR,EACRuR,GAActR,EACduR,GAAQvB,C,sICtiBd,MAAMwB,EAASC,EAAAA,GAITC,GAFcD,EAAAA,GAEAA,EAAAA,IAEdE,EAAgB/T,EAAAA,WAGpB,CAAAC,EAAoCC,KAAG,IAAtC,UAAEC,EAAS,SAAEG,KAAaF,GAAOH,EAAA,OAClC8B,EAAAA,EAAAA,MAAC8R,EAAAA,GAAuB,CACtB3T,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IACT,4RACAJ,MAEEC,EAAKE,SAAA,CAERA,GACDD,EAAAA,EAAAA,KAACwT,EAAAA,GAAoB,CAACvF,SAAO,EAAAhO,UAC3BD,EAAAA,EAAAA,KAAC2T,EAAAA,EAAW,CAAC7T,UAAU,8BAI7B4T,EAAcvT,YAAcqT,EAAAA,GAAwBrT,YAEpD,MAAMyT,EAAgBjU,EAAAA,WAGpB,CAAAU,EAAyDR,KAAG,IAA3D,UAAEC,EAAS,SAAEG,EAAQ,SAAE4T,EAAW,YAAa9T,GAAOM,EAAA,OACvDL,EAAAA,EAAAA,KAACwT,EAAAA,GAAsB,CAAAvT,UACrBD,EAAAA,EAAAA,KAACwT,EAAAA,GAAuB,CACtB3T,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IACT,6bACa,WAAb2T,GACE,kIACF/T,GAEF+T,SAAUA,KACN9T,EAAKE,UAETD,EAAAA,EAAAA,KAACwT,EAAAA,GAAwB,CACvB1T,WAAWI,EAAAA,EAAAA,IACT,MACa,WAAb2T,GACE,2FACF5T,SAEDA,UAKT2T,EAAczT,YAAcqT,EAAAA,GAAwBrT,YAEhCR,EAAAA,WAGlB,CAAAY,EAA0BV,KAAG,IAA5B,UAAEC,KAAcC,GAAOQ,EAAA,OACxBP,EAAAA,EAAAA,KAACwT,EAAAA,GAAqB,CACpB3T,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IAAG,yCAA0CJ,MACpDC,MAGII,YAAcqT,EAAAA,GAAsBrT,YAEhD,MAAM2T,EAAanU,EAAAA,WAGjB,CAAAa,EAAoCX,KAAG,IAAtC,UAAEC,EAAS,SAAEG,KAAaF,GAAOS,EAAA,OAClCkB,EAAAA,EAAAA,MAAC8R,EAAAA,GAAoB,CACnB3T,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IACT,4NACAJ,MAEEC,EAAKE,SAAA,EAETD,EAAAA,EAAAA,KAAA,QAAMF,UAAU,+DAA8DG,UAC5ED,EAAAA,EAAAA,KAACwT,EAAAA,GAA6B,CAAAvT,UAC5BD,EAAAA,EAAAA,KAAC+T,EAAAA,EAAK,CAACjU,UAAU,iBAIrBE,EAAAA,EAAAA,KAACwT,EAAAA,GAAwB,CAAAvT,SAAEA,SAG/B6T,EAAW3T,YAAcqT,EAAAA,GAAqBrT,YAEtBR,EAAAA,WAGtB,CAAAe,EAA0Bb,KAAG,IAA5B,UAAEC,KAAcC,GAAOW,EAAA,OACxBV,EAAAA,EAAAA,KAACwT,EAAAA,GAAyB,CACxB3T,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IAAG,2BAA4BJ,MACtCC,MAGQI,YAAcqT,EAAAA,GAA0BrT,W,kCC9FlD,MAAAiG,GAAa/D,E,QAAAA,GAAiB,aAAc,CAChD,CACE,OACA,CAAEkH,MAAO,KAAMC,OAAQ,KAAMwK,EAAG,IAAKC,EAAG,IAAKC,GAAI,IAAK3R,IAAK,WAE7D,CAAC,OAAQ,CAAE4R,GAAI,IAAKC,GAAI,KAAMC,GAAI,KAAMC,GAAI,KAAM/R,IAAK,Y,yGCbzD,MAAMuG,EAAOyL,EAAAA,GAEPvL,EAAWrJ,EAAAA,WAGf,CAAAC,EAA0BC,KAAG,IAA5B,UAAEC,KAAcC,GAAOH,EAAA,OACxBI,EAAAA,EAAAA,KAACuU,EAAAA,GAAkB,CACjB1U,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IACT,6FACAJ,MAEEC,MAGRiJ,EAAS7I,YAAcoU,EAAAA,GAAmBpU,YAE1C,MAAM8I,EAActJ,EAAAA,WAGlB,CAAAU,EAA0BR,KAAG,IAA5B,UAAEC,KAAcC,GAAOM,EAAA,OACxBL,EAAAA,EAAAA,KAACuU,EAAAA,GAAqB,CACpB1U,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IACT,sYACAJ,MAEEC,MAGRkJ,EAAY9I,YAAcoU,EAAAA,GAAsBpU,YAEhD,MAAMgJ,EAAcxJ,EAAAA,WAGlB,CAAAY,EAA0BV,KAAG,IAA5B,UAAEC,KAAcC,GAAOQ,EAAA,OACxBP,EAAAA,EAAAA,KAACuU,EAAAA,GAAqB,CACpB1U,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IACT,kIACAJ,MAEEC,MAGRoJ,EAAYhJ,YAAcoU,EAAAA,GAAsBpU,W,iHC9ChD,MAAMuI,EAAO/I,EAAAA,WAGX,CAAAC,EAA0BC,KAAG,IAA5B,UAAEC,KAAcC,GAAOH,EAAA,OACxBI,EAAAA,EAAAA,KAAA,OACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IACT,2DACAJ,MAEEC,MAGR2I,EAAKvI,YAAc,OAEnB,MAAMwI,EAAahJ,EAAAA,WAGjB,CAAAU,EAA0BR,KAAG,IAA5B,UAAEC,KAAcC,GAAOM,EAAA,OACxBL,EAAAA,EAAAA,KAAA,OACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IAAG,gCAAiCJ,MAC3CC,MAGR4I,EAAWxI,YAAc,aAEzB,MAAMiJ,EAAYzJ,EAAAA,WAGhB,CAAAY,EAA0BV,KAAG,IAA5B,UAAEC,KAAcC,GAAOQ,EAAA,OACxBP,EAAAA,EAAAA,KAAA,MACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IACT,qDACAJ,MAEEC,MAGRqJ,EAAUjJ,YAAc,YAExB,MAAMkJ,EAAkB1J,EAAAA,WAGtB,CAAAa,EAA0BX,KAAG,IAA5B,UAAEC,KAAcC,GAAOS,EAAA,OACxBR,EAAAA,EAAAA,KAAA,KACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IAAG,gCAAiCJ,MAC3CC,MAGRsJ,EAAgBlJ,YAAc,kBAE9B,MAAMyI,EAAcjJ,EAAAA,WAGlB,CAAAe,EAA0Bb,KAAG,IAA5B,UAAEC,KAAcC,GAAOW,EAAA,OACxBV,EAAAA,EAAAA,KAAA,OAAKH,IAAKA,EAAKC,WAAWI,EAAAA,EAAAA,IAAG,WAAYJ,MAAgBC,MAE3D6I,EAAYzI,YAAc,cAE1B,MAAMqU,EAAa7U,EAAAA,WAGjB,CAAAiB,EAA0Bf,KAAG,IAA5B,UAAEC,KAAcC,GAAOa,EAAA,OACxBZ,EAAAA,EAAAA,KAAA,OACEH,IAAKA,EACLC,WAAWI,EAAAA,EAAAA,IAAG,6BAA8BJ,MACxCC,MAGRyU,EAAWrU,YAAc,Y,0DC5DzB,IAAIsU,EAAQ,EA+BZ,MAAMC,EAAgB,IAAIC,IAEpBC,EAAoBC,IACxB,GAAIH,EAAcI,IAAID,GACpB,OAGF,MAAME,EAAUpO,WAAW,KACzB+N,EAAcM,OAAOH,GACrBI,EAAS,CACP1H,KAAM,eACNsH,QAASA,KAEV,KAEHH,EAAcQ,IAAIL,EAASE,IAGhBI,EAAUA,CAACC,EAAcC,KACpC,OAAQA,EAAO9H,MACb,IAAK,YACH,MAAO,IACF6H,EACHE,OAAQ,CAACD,EAAOzQ,SAAUwQ,EAAME,QAAQtK,MAAM,EAAG,IAGrD,IAAK,eACH,MAAO,IACFoK,EACHE,OAAQF,EAAME,OAAO/M,IAAKgN,GACxBA,EAAE3O,KAAOyO,EAAOzQ,MAAMgC,GAAK,IAAK2O,KAAMF,EAAOzQ,OAAU2Q,IAI7D,IAAK,gBAAiB,CACpB,MAAM,QAAEV,GAAYQ,EAYpB,OARIR,EACFD,EAAiBC,GAEjBO,EAAME,OAAOE,QAAS5Q,IACpBgQ,EAAiBhQ,EAAMgC,MAIpB,IACFwO,EACHE,OAAQF,EAAME,OAAO/M,IAAKgN,GACxBA,EAAE3O,KAAOiO,QAAuB9Q,IAAZ8Q,EAChB,IACKU,EACH7J,MAAM,GAER6J,GAGV,CACA,IAAK,eACH,YAAuBxR,IAAnBsR,EAAOR,QACF,IACFO,EACHE,OAAQ,IAGL,IACFF,EACHE,OAAQF,EAAME,OAAOG,OAAQF,GAAMA,EAAE3O,KAAOyO,EAAOR,YAKrDa,EAA2C,GAEjD,IAAIC,EAAqB,CAAEL,OAAQ,IAEnC,SAASL,EAASI,GAChBM,EAAcR,EAAQQ,EAAaN,GACnCK,EAAUF,QAASI,IACjBA,EAASD,IAEb,CAIA,SAAS/Q,EAAKhF,GAAuB,OAAjBG,GAAcH,EAChC,MAAMgH,GAnHN6N,GAASA,EAAQ,GAAKoB,OAAOC,iBACtBrB,EAAMnO,YAyHPyP,EAAUA,IAAMd,EAAS,CAAE1H,KAAM,gBAAiBsH,QAASjO,IAcjE,OAZAqO,EAAS,CACP1H,KAAM,YACN3I,MAAO,IACF7E,EACH6G,KACA8E,MAAM,EACNG,aAAeH,IACRA,GAAMqK,QAKV,CACLnP,GAAIA,EACJmP,UACAC,OAtBcjW,GACdkV,EAAS,CACP1H,KAAM,eACN3I,MAAO,IAAK7E,EAAO6G,QAqBzB,CAEA,SAAS/B,IACP,MAAOuQ,EAAOa,GAAYtW,EAAAA,SAAsBgW,GAYhD,OAVAhW,EAAAA,UAAgB,KACd+V,EAAUQ,KAAKD,GACR,KACL,MAAME,EAAQT,EAAUU,QAAQH,GAC5BE,GAAS,GACXT,EAAUW,OAAOF,EAAO,KAG3B,CAACf,IAEG,IACFA,EACHxQ,QACAmR,QAAUlB,GAAqBI,EAAS,CAAE1H,KAAM,gBAAiBsH,YAErE,C,kCCvKM,MAAA7O,GAAQ3D,E,QAAAA,GAAiB,QAAS,CACtC,CAAC,OAAQ,CAAEC,EAAG,4CAA6CC,IAAK,WAChE,CAAC,SAAU,CAAEe,GAAI,IAAKC,GAAI,IAAKC,EAAG,IAAKjB,IAAK,UAC5C,CAAC,OAAQ,CAAED,EAAG,6BAA8BC,IAAK,WACjD,CAAC,OAAQ,CAAED,EAAG,4BAA6BC,IAAK,Y,kCCJ5C,MAAA6F,GAAS/F,E,QAAAA,GAAiB,SAAU,CACxC,CAAC,OAAQ,CAAEC,EAAG,0CAA2CC,IAAK,WAC9D,CAAC,WAAY,CAAEkB,OAAQ,mBAAoBlB,IAAK,WAChD,CAAC,OAAQ,CAAE4R,GAAI,KAAMC,GAAI,IAAKC,GAAI,KAAMC,GAAI,KAAM/R,IAAK,Y,+LCyBzD,MAAM+T,EAAUC,4BAEHrL,EAA6BA,KACxC,MAAM,KAAE3G,EAAI,OAAEC,IAAWC,EAAAA,EAAAA,KACnB+R,EAAQC,aAAaC,QAAQ,UAC7B,MAAE9R,IAAUC,EAAAA,EAAAA,OACX8R,EAAaC,IAAkB5R,EAAAA,EAAAA,UAA6B,KAC5D6R,EAASC,IAAc9R,EAAAA,EAAAA,UAAyB,KAChD+R,EAAWC,IAAgBhS,EAAAA,EAAAA,UAAyB,KACpDiS,EAASC,IAAclS,EAAAA,EAAAA,WAAS,IAChCmS,EAAcC,IAAmBpS,EAAAA,EAAAA,WAAS,IAC1CqS,EAAUC,IAAetS,EAAAA,EAAAA,UAAS,CACvCuS,WAAY,GACZC,SAAU,GACVC,WAAWpT,EAAAA,EAAAA,GAAO,IAAI4C,KAAQ,cAC9ByQ,QAAS,GACTC,MAAO,KAGHC,EAAU,CACd,eAAgB,sBACZpB,EAAQ,CAAEqB,cAAe,UAAUrB,KAAY,CAAC,IAGtD7O,EAAAA,EAAAA,WAAU,KACRmQ,IACAC,KACC,IAEH,MAAMD,EAAYtR,UAChB,IACE,MAAOwR,SAAwBvR,QAAQwR,IAAI,CACzCC,EAAAA,EAAMC,IAAI,GAAG7B,uBAA8B,CAAEsB,cAE/ChB,EAAeoB,EAAevO,KAAKA,KACrC,CAAE,MAAOlC,GACPC,QAAQD,MAAM,uBAAwBA,GACtC3C,EAAM,CACJ6C,MAAO,QACPxB,YAAa,uBACbyB,QAAS,eAEb,CAAC,QACCwP,GAAW,EACb,GAGIa,EAAavR,UACjB,IACE,MAAO4R,EAAYC,SAAsB5R,QAAQwR,IAAI,CACnDC,EAAAA,EAAMC,IAAI,GAAG7B,4BAAmC,CAAEsB,YAClDM,EAAAA,EAAMC,IAAI,GAAG7B,8BAAqC,CAAEsB,cAEtDd,EAAWsB,EAAW3O,KAAKA,MAC3BuN,EAAaqB,EAAa5O,KAAKA,KACjC,CAAE,MAAOlC,GACPC,QAAQD,MAAM,wBAAyBA,GACvC3C,EAAM,CACJ6C,MAAO,QACPxB,YAAa,wBACbyB,QAAS,eAEb,GAiEI4Q,EAAe/T,GACdA,EACe,kBAATA,EAA0B,aAC7BA,EAAsBqB,MAASrB,EAAsBgU,OAAS,UAFpD,UAKdC,EAAmBjU,IACvB,IAAKA,EAAM,MAAO,UAClB,GAAoB,kBAATA,EAAmB,MAAO,aACrC,MAAMkU,EAAWlU,EACjB,OAAOkU,EAASC,cAAgBD,EAAS7S,MAAQ6S,EAASF,OAAS,WAGrE,OAAItB,GACKjX,EAAAA,EAAAA,KAAA,OAAAC,SAAK,gBAIZyB,EAAAA,EAAAA,MAAA,OAAK5B,UAAU,wBAAuBG,SAAA,EACpCyB,EAAAA,EAAAA,MAAA,OAAK5B,UAAU,yCAAwCG,SAAA,EACrDD,EAAAA,EAAAA,KAAA,MAAIF,UAAU,qBAAoBG,SAAC,uBACnCyB,EAAAA,EAAAA,MAACV,EAAAA,GAAM,CAAC0K,KAAMyL,EAActL,aAAcuL,EAAgBnX,SAAA,EACxDD,EAAAA,EAAAA,KAACkB,EAAAA,GAAa,CAAC+M,SAAO,EAAAhO,UACpBD,EAAAA,EAAAA,KAAC+H,EAAAA,EAAM,CAAA9H,SAAC,qBAEVyB,EAAAA,EAAAA,MAACD,EAAAA,GAAa,CAAAxB,SAAA,EACZD,EAAAA,EAAAA,KAAC4B,EAAAA,GAAY,CAAA3B,UACXD,EAAAA,EAAAA,KAAC8B,EAAAA,GAAW,CAAA7B,SAAC,iCAEfyB,EAAAA,EAAAA,MAAA,QAAMiX,SA3FKnS,UACnBoS,EAAEjJ,iBACF,UACQuI,EAAAA,EAAMW,KACV,GAAGvC,uBACH,IACKe,EACHI,UAAW,IAAIxQ,KAAKoQ,EAASI,WAAWvQ,cACxCwQ,QAASL,EAASK,QAAU,IAAIzQ,KAAKoQ,EAASK,SAASxQ,mBAAgBnD,GAEzE,CAAE6T,YAGJhT,EAAM,CACJ6C,MAAO,UACPxB,YAAa,iCAGfmR,GAAgB,GAChBE,EAAY,CACVC,WAAY,GACZC,SAAU,GACVC,WAAWpT,EAAAA,EAAAA,GAAO,IAAI4C,KAAQ,cAC9ByQ,QAAS,GACTC,MAAO,KAGTG,GACF,CAAE,MAAOvQ,GACPC,QAAQD,MAAM,6BAA8BA,GAC5C3C,EAAM,CACJ6C,MAAO,QACPxB,YAAa,0BACbyB,QAAS,eAEb,GAwDsC5H,UAAU,YAAWG,SAAA,EACjDyB,EAAAA,EAAAA,MAAA,OAAK5B,UAAU,YAAWG,SAAA,EACxBD,EAAAA,EAAAA,KAACkC,EAAAA,EAAK,CAAC4W,QAAQ,aAAY7Y,SAAC,cAC5ByB,EAAAA,EAAAA,MAAC6R,EAAAA,GAAM,CACL1N,MAAOwR,EAASE,WAChBwB,cAAgBlT,GAAUyR,EAAY,IAAKD,EAAUE,WAAY1R,IACjEmT,UAAQ,EAAA/Y,SAAA,EAERD,EAAAA,EAAAA,KAAC0T,EAAAA,GAAa,CAAAzT,UACZD,EAAAA,EAAAA,KAACyT,EAAAA,GAAW,CAACwF,YAAY,yBAE3BjZ,EAAAA,EAAAA,KAAC4T,EAAAA,GAAa,CAAA3T,SACX8W,EAAUxO,IAAKpB,IACdnH,EAAAA,EAAAA,KAAC8T,EAAAA,GAAU,CAAoBjO,MAAOsB,EAAS+R,KAAO,GAAGjZ,SACtDuY,EAAgBrR,IADFA,EAAS+R,eAQlCxX,EAAAA,EAAAA,MAAA,OAAK5B,UAAU,YAAWG,SAAA,EACxBD,EAAAA,EAAAA,KAACkC,EAAAA,EAAK,CAAC4W,QAAQ,WAAU7Y,SAAC,YAC1ByB,EAAAA,EAAAA,MAAC6R,EAAAA,GAAM,CACL1N,MAAOwR,EAASG,SAChBuB,cAAgBlT,GAAUyR,EAAY,IAAKD,EAAUG,SAAU3R,IAC/DmT,UAAQ,EAAA/Y,SAAA,EAERD,EAAAA,EAAAA,KAAC0T,EAAAA,GAAa,CAAAzT,UACZD,EAAAA,EAAAA,KAACyT,EAAAA,GAAW,CAACwF,YAAY,uBAE3BjZ,EAAAA,EAAAA,KAAC4T,EAAAA,GAAa,CAAA3T,SACX4W,EAAQtO,IAAK4Q,IACZnZ,EAAAA,EAAAA,KAAC8T,EAAAA,GAAU,CAAkBjO,MAAOsT,EAAOD,KAAO,GAAGjZ,SACjDkZ,EAAgBvT,MAASuT,EAAgBZ,OAAS,kBADrCY,EAAOD,eAQhCxX,EAAAA,EAAAA,MAAA,OAAK5B,UAAU,yBAAwBG,SAAA,EACrCyB,EAAAA,EAAAA,MAAA,OAAK5B,UAAU,YAAWG,SAAA,EACxBD,EAAAA,EAAAA,KAACkC,EAAAA,EAAK,CAAC4W,QAAQ,YAAW7Y,SAAC,gBAC3BD,EAAAA,EAAAA,KAACoZ,EAAAA,EAAK,CACJ7L,KAAK,OACL3G,GAAG,YACHf,MAAOwR,EAASI,UAChBpL,SAAWuM,GAAMtB,EAAY,IAAKD,EAAUI,UAAWmB,EAAErI,OAAO1K,QAChEmT,UAAQ,QAGZtX,EAAAA,EAAAA,MAAA,OAAK5B,UAAU,YAAWG,SAAA,EACxBD,EAAAA,EAAAA,KAACkC,EAAAA,EAAK,CAAC4W,QAAQ,UAAS7Y,SAAC,yBACzBD,EAAAA,EAAAA,KAACoZ,EAAAA,EAAK,CACJ7L,KAAK,OACL3G,GAAG,UACHf,MAAOwR,EAASK,QAChBrL,SAAWuM,GAAMtB,EAAY,IAAKD,EAAUK,QAASkB,EAAErI,OAAO1K,QAC9DwT,IAAKhC,EAASI,mBAKpB/V,EAAAA,EAAAA,MAAA,OAAK5B,UAAU,YAAWG,SAAA,EACxBD,EAAAA,EAAAA,KAACkC,EAAAA,EAAK,CAAC4W,QAAQ,QAAO7Y,SAAC,sBACvBD,EAAAA,EAAAA,KAACoZ,EAAAA,EAAK,CACJxS,GAAG,QACHf,MAAOwR,EAASM,MAChBtL,SAAWuM,GAAMtB,EAAY,IAAKD,EAAUM,MAAOiB,EAAErI,OAAO1K,QAC5DoT,YAAY,+CAIhBvX,EAAAA,EAAAA,MAAA,OAAK5B,UAAU,kCAAiCG,SAAA,EAC9CD,EAAAA,EAAAA,KAAC+H,EAAAA,EAAM,CACLwF,KAAK,SACL7F,QAAQ,UACRO,QAASA,IAAMmP,GAAgB,GAAOnX,SACvC,YAGDD,EAAAA,EAAAA,KAAC+H,EAAAA,EAAM,CAACwF,KAAK,SAAQtN,SAAC,kCAOhCD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oBAAmBG,UAChCyB,EAAAA,EAAAA,MAAChC,EAAAA,GAAK,CAAAO,SAAA,EACJD,EAAAA,EAAAA,KAACI,EAAAA,GAAW,CAAAH,UACVyB,EAAAA,EAAAA,MAACjB,EAAAA,GAAQ,CAAAR,SAAA,EACPD,EAAAA,EAAAA,KAACW,EAAAA,GAAS,CAAAV,SAAC,cACXD,EAAAA,EAAAA,KAACW,EAAAA,GAAS,CAAAV,SAAC,YACXD,EAAAA,EAAAA,KAACW,EAAAA,GAAS,CAAAV,SAAC,gBACXD,EAAAA,EAAAA,KAACW,EAAAA,GAAS,CAAAV,SAAC,cACXD,EAAAA,EAAAA,KAACW,EAAAA,GAAS,CAAAV,SAAC,YACXD,EAAAA,EAAAA,KAACW,EAAAA,GAAS,CAAAV,SAAC,kBAGfD,EAAAA,EAAAA,KAACM,EAAAA,GAAS,CAAAL,SACgB,IAAvB0W,EAAY7S,QACX9D,EAAAA,EAAAA,KAACS,EAAAA,GAAQ,CAAAR,UACPD,EAAAA,EAAAA,KAACa,EAAAA,GAAS,CAACoK,QAAS,EAAGnL,UAAU,mBAAkBG,SAAC,2BAKtD0W,EAAYpO,IAAK+Q,IACf5X,EAAAA,EAAAA,MAACjB,EAAAA,GAAQ,CAAAR,SAAA,EACPD,EAAAA,EAAAA,KAACa,EAAAA,GAAS,CAAAZ,SAAEqY,EAAYgB,EAAWnS,aACnCnH,EAAAA,EAAAA,KAACa,EAAAA,GAAS,CAAAZ,SAAEqY,EAAYgB,EAAWH,WACnCnZ,EAAAA,EAAAA,KAACa,EAAAA,GAAS,CAAAZ,UAAEoE,EAAAA,EAAAA,GAAO,IAAI4C,KAAKqS,EAAW7B,WAAY,kBACnDzX,EAAAA,EAAAA,KAACa,EAAAA,GAAS,CAAAZ,SACPqZ,EAAW5B,SAAUrT,EAAAA,EAAAA,GAAO,IAAI4C,KAAKqS,EAAW5B,SAAU,eAAiB,SAE9E1X,EAAAA,EAAAA,KAACa,EAAAA,GAAS,CAAAZ,UACRD,EAAAA,EAAAA,KAAA,QACEF,UAAW,mCACa,WAAtBwZ,EAAWlS,OACP,8BACsB,cAAtBkS,EAAWlS,OACX,4BACA,6BACHnH,SAEFqZ,EAAWlS,OAAO0D,OAAO,GAAGC,cAAgBuO,EAAWlS,OAAO4D,MAAM,QAGzEhL,EAAAA,EAAAA,KAACa,EAAAA,GAAS,CAAAZ,SACe,WAAtBqZ,EAAWlS,SACVpH,EAAAA,EAAAA,KAAC+H,EAAAA,EAAM,CACLL,QAAQ,UACRM,KAAK,KACLlI,UAAU,OACVmI,QAASA,IA7LAzB,OAAOI,EAAYQ,KAChD,UACQ8Q,EAAAA,EAAMqB,IACV,GAAGjD,wBAA8B1P,IACjC,CAAEQ,UACF,CAAEwQ,YAGJhT,EAAM,CACJ6C,MAAO,UACPxB,YAAa,oCAGf6R,GACF,CAAE,MAAOvQ,GACPC,QAAQD,MAAM,6BAA8BA,GAC5C3C,EAAM,CACJ6C,MAAO,QACPxB,YAAa,8BACbyB,QAAS,eAEb,GAwKmC8R,CAAuBF,EAAWJ,IAAK,aAAajZ,SACpE,sBA3BQqZ,EAAWJ,iBA0C1C,G,sFCvVA,MAAMO,GAAiBxX,EAAAA,EAAAA,GACrB,yRACA,CACEyX,SAAU,CACRhS,QAAS,CACPiS,QAAS,yDACTC,YACE,qEACFC,QACE,iFACFC,UACE,+DACFC,MAAO,+CACPC,KAAM,mDAERhS,KAAM,CACJ2R,QAAS,iBACTM,GAAI,sBACJC,GAAI,uBACJnU,KAAM,cAGVoU,gBAAiB,CACfzS,QAAS,UACTM,KAAM,aAWND,EAASpI,EAAAA,WACb,CAAAC,EAA0DC,KAAS,IAAlE,UAAEC,EAAS,QAAE4H,EAAO,KAAEM,EAAI,QAAEiG,GAAU,KAAUlO,GAAOH,EACtD,MAAMwa,EAAOnM,EAAUK,EAAAA,GAAO,SAC9B,OACEtO,EAAAA,EAAAA,KAACoa,EAAI,CACHta,WAAWI,EAAAA,EAAAA,IAAGuZ,EAAe,CAAE/R,UAASM,OAAMlI,eAC9CD,IAAKA,KACDE,MAKZgI,EAAO5H,YAAc,Q,mEC9CrB,MAAMiZ,EAAQzZ,EAAAA,WACZ,CAAAC,EAAgCC,KAAS,IAAxC,UAAEC,EAAS,KAAEyN,KAASxN,GAAOH,EAC5B,OACEI,EAAAA,EAAAA,KAAA,SACEuN,KAAMA,EACNzN,WAAWI,EAAAA,EAAAA,IACT,+VACAJ,GAEFD,IAAKA,KACDE,MAKZqZ,EAAMjZ,YAAc,O", "sources": ["components/ui/table.tsx", "components/ui/dialog.tsx", "components/ui/label.tsx", "components/ui/skeleton.tsx", "../node_modules/lucide-react/src/icons/shield.ts", "../node_modules/recharts/es6/chart/BarChart.js", "../node_modules/lucide-react/src/icons/user-check.ts", "pages/AdminDashboard.tsx", "../node_modules/lucide-react/src/icons/refresh-cw.ts", "../node_modules/@radix-ui/react-dialog/src/dialog.tsx", "components/ui/select.tsx", "../node_modules/lucide-react/src/icons/credit-card.ts", "components/ui/tabs.tsx", "components/ui/card.tsx", "components/ui/use-toast.ts", "../node_modules/lucide-react/src/icons/users.ts", "../node_modules/lucide-react/src/icons/log-out.ts", "components/TraderManagement.tsx", "components/ui/button.tsx", "components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst Table = React.forwardRef<\n  HTMLTableElement,\n  React.HTMLAttributes<HTMLTableElement>\n>(({ className, ...props }, ref) => (\n  <div className=\"w-full overflow-auto\">\n    <table\n      ref={ref}\n      className={cn(\"w-full caption-bottom text-sm\", className)}\n      {...props}\n    />\n  </div>\n))\nTable.displayName = \"Table\"\n\nconst TableHeader = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <thead ref={ref} className={cn(\"[&_tr]:border-b\", className)} {...props} />\n))\nTableHeader.displayName = \"TableHeader\"\n\nconst TableBody = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tbody\n    ref={ref}\n    className={cn(\"[&_tr:last-child]:border-0\", className)}\n    {...props}\n  />\n))\nTableBody.displayName = \"TableBody\"\n\nconst TableFooter = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tfoot\n    ref={ref}\n    className={cn(\"bg-primary font-medium text-primary-foreground\", className)}\n    {...props}\n  />\n))\nTableFooter.displayName = \"TableFooter\"\n\nconst TableRow = React.forwardRef<\n  HTMLTableRowElement,\n  React.HTMLAttributes<HTMLTableRowElement>\n>(({ className, ...props }, ref) => (\n  <tr\n    ref={ref}\n    className={cn(\n      \"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nTableRow.displayName = \"TableRow\"\n\nconst TableHead = React.forwardRef<\n  HTMLTableCellElement,\n  React.ThHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <th\n    ref={ref}\n    className={cn(\n      \"h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0\",\n      className\n    )}\n    {...props}\n  />\n))\nTableHead.displayName = \"TableHead\"\n\nconst TableCell = React.forwardRef<\n  HTMLTableCellElement,\n  React.TdHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <td\n    ref={ref}\n    className={cn(\"p-4 align-middle [&:has([role=checkbox])]:pr-0\", className)}\n    {...props}\n  />\n))\nTableCell.displayName = \"TableCell\"\n\nconst TableCaption = React.forwardRef<\n  HTMLTableCaptionElement,\n  React.HTMLAttributes<HTMLTableCaptionElement>\n>(({ className, ...props }, ref) => (\n  <caption\n    ref={ref}\n    className={cn(\"mt-4 text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nTableCaption.displayName = \"TableCaption\"\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n", "import * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { X } from \"lucide-react\"\nimport { cn } from \"../../lib/utils\"\n\nconst Dialog = DialogPrimitive.Root\n\nconst DialogTrigger = DialogPrimitive.Trigger\n\ninterface DialogPortalProps extends DialogPrimitive.DialogPortalProps {\n  className?: string;\n  children?: React.ReactNode;\n}\n\nconst DialogPortal = ({\n  className,\n  children,\n  ...props\n}: DialogPortalProps) => {\n  const { container, forceMount, ...restProps } = props;\n  const portalProps = { \n    container,\n    forceMount: forceMount as true | undefined \n  };\n  return (\n    <DialogPrimitive.Portal {...portalProps}>\n      <div className={cn(\n        \"fixed inset-0 z-50 flex items-start justify-center sm:items-center\",\n        className\n      )}>\n        {children}\n      </div>\n    </DialogPrimitive.Portal>\n  )\n}\nDialogPortal.displayName = DialogPrimitive.Portal.displayName\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/50 backdrop-blur-sm transition-all duration-100 data-[state=closed]:animate-out data-[state=closed]:fade-out data-[state=open]:fade-in\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed z-50 grid w-full gap-4 rounded-b-lg bg-white p-6 animate-in data-[state=open]:fade-in-90 data-[state=open]:slide-in-from-bottom-10 sm:max-w-lg sm:rounded-lg sm:zoom-in-90 data-[state=open]:sm:slide-in-from-bottom-0\",\n        \"dark:bg-gray-900\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-gray-100 dark:focus:ring-gray-400 dark:focus:ring-offset-gray-900 dark:data-[state=open]:bg-gray-800\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n))\nDialogContent.displayName = DialogPrimitive.Content.displayName\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold text-gray-900\",\n      \"dark:text-gray-50\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = DialogPrimitive.Title.displayName\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-gray-500\", \"dark:text-gray-400\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = DialogPrimitive.Description.displayName\n\nexport {\n  Dialog,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n}\n", "import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  HTMLLabelElement,\n  React.LabelHTMLAttributes<HTMLLabelElement> & VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <label\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = \"Label\"\n\nexport { Label }\n", "import * as React from \"react\"\nimport { cn } from \"../../lib/utils\"\n\nfunction Skeleton({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) {\n  return (\n    <div\n      className={cn(\"animate-pulse rounded-md bg-gray-100 dark:bg-gray-800\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Skeleton }\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Shield\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMjJzOC00IDgtMTBWNWwtOC0zLTggM3Y3YzAgNiA4IDEwIDggMTB6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/shield\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Shield = createLucideIcon('Shield', [\n  ['path', { d: 'M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z', key: '3xmgem' }],\n]);\n\nexport default Shield;\n", "/**\n * @fileOverview Bar Chart\n */\nimport { generateCategoricalChart } from './generateCategoricalChart';\nimport { Bar } from '../cartesian/Bar';\nimport { XAxis } from '../cartesian/XAxis';\nimport { YAxis } from '../cartesian/YAxis';\nimport { formatAxisMap } from '../util/CartesianUtils';\nexport var BarChart = generateCategoricalChart({\n  chartName: 'BarChart',\n  GraphicalChild: Bar,\n  defaultTooltipEventType: 'axis',\n  validateTooltipEventTypes: ['axis', 'item'],\n  axisComponents: [{\n    axisType: 'xAxis',\n    AxisComp: XAxis\n  }, {\n    axisType: 'yAxis',\n    AxisComp: YAxis\n  }],\n  formatAxisMap: formatAxisMap\n});", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name UserCheck\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMjF2LTJhNCA0IDAgMCAwLTQtNEg2YTQgNCAwIDAgMC00IDR2MiIgLz4KICA8Y2lyY2xlIGN4PSI5IiBjeT0iNyIgcj0iNCIgLz4KICA8cG9seWxpbmUgcG9pbnRzPSIxNiAxMSAxOCAxMyAyMiA5IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/user-check\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst UserCheck = createLucideIcon('UserCheck', [\n  ['path', { d: 'M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2', key: '1yyitq' }],\n  ['circle', { cx: '9', cy: '7', r: '4', key: 'nufk8' }],\n  ['polyline', { points: '16 11 18 13 22 9', key: '1pwet4' }],\n]);\n\nexport default UserCheck;\n", "import React, { useEffect, useState } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { format } from 'date-fns';\n\n// UI Components\nimport { Button } from '../components/ui/button';\nimport { Card, CardContent, CardHeader, CardTitle, CardDescription } from '../components/ui/card';\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../components/ui/table';\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';\nimport { Skeleton } from '../components/ui/skeleton';\n\n// Charts\nimport { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts';\n\n// Icons\nimport { Users, CreditCard, DollarSign, Activity, RefreshCw, LogOut, Shield, UserCheck } from 'lucide-react';\n\n// Components\nimport { TraderManagement } from '../components/TraderManagement';\nimport { useToast } from '../components/ui/use-toast';\n\n// Types\ninterface DashboardStats {\n  totalUsers: number;\n  totalTransactions: number;\n  totalRevenue: number;\n  activeMerchants: number;\n}\n\ninterface Transaction {\n  id: string;\n  date: string;\n  merchant: string;\n  amount: number;\n  status: 'completed' | 'pending' | 'failed';\n  currency: string;\n}\n\nconst API_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api/v1';\n\n// Format currency\nconst formatCurrency = (amount: number, currency: string = 'USD'): string => {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n    minimumFractionDigits: 2,\n    maximumFractionDigits: 2\n  }).format(amount);\n};\n\n// Format date\nconst formatDate = (dateString: string): string => {\n  return new Date(dateString).toLocaleString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit'\n  });\n};\n\n// Stats card type\ninterface StatCard {\n  name: string;\n  value: string;\n  icon: React.ComponentType<{ className?: string }>;\n  description: string;\n  change: string;\n  trend: 'up' | 'down';\n}\n\nconst AdminDashboard: React.FC = () => {\n  const { user, logout } = useAuth();\n  const navigate = useNavigate();\n  const { toast } = useToast();\n  \n  const [isLoading, setIsLoading] = useState(true);\n  const [stats, setStats] = useState<DashboardStats>({\n    totalUsers: 0,\n    totalTransactions: 0,\n    totalRevenue: 0,\n    activeMerchants: 0\n  });\n  \n  const [recentTransactions, setRecentTransactions] = useState<Transaction[]>([]);\n  const [revenueData, setRevenueData] = useState<{name: string; revenue: number}[]>([]);\n\n  // Stats cards data\n  const statCards: StatCard[] = [\n    {\n      name: 'Total Users',\n      value: stats.totalUsers.toLocaleString(),\n      icon: Users,\n      description: 'Total registered users',\n      change: '+12.5%',\n      trend: 'up'\n    },\n    {\n      name: 'Total Transactions',\n      value: stats.totalTransactions.toLocaleString(),\n      icon: CreditCard,\n      description: 'All-time transactions',\n      change: '+8.2%',\n      trend: 'up'\n    },\n    {\n      name: 'Total Revenue',\n      value: formatCurrency(stats.totalRevenue),\n      icon: DollarSign,\n      description: 'Total revenue generated',\n      change: '+15.3%',\n      trend: 'up'\n    },\n    {\n      name: 'Active Merchants',\n      value: stats.activeMerchants.toString(),\n      icon: UserCheck,\n      description: 'Currently active merchants',\n      change: '+4.1%',\n      trend: 'up'\n    }\n  ];\n\n  // Fetch dashboard data\n  const fetchDashboardData = async () => {\n    try {\n      setIsLoading(true);\n      \n      // In a real app, you would make actual API calls here\n      // const statsResponse = await axios.get(`${API_URL}/admin/dashboard/stats`);\n      // const transactionsResponse = await axios.get(`${API_URL}/admin/transactions/recent`);\n      // const revenueResponse = await axios.get(`${API_URL}/admin/revenue/monthly`);\n      \n      // Simulate API call with timeout\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      // Mock data - replace with actual API calls\n      setStats({\n        totalUsers: 1245,\n        totalTransactions: 12478,\n        totalRevenue: 124560.78,\n        activeMerchants: 92\n      });\n      \n      setRecentTransactions([\n        { \n          id: 'TXN' + Math.floor(Math.random() * 1000000),\n          date: new Date().toISOString(),\n          merchant: 'Test Merchant',\n          amount: 100.50,\n          status: 'completed',\n          currency: 'USD'\n        },\n        { \n          id: 'TXN' + Math.floor(Math.random() * 1000000),\n          date: new Date(Date.now() - 86400000).toISOString(),\n          merchant: 'Another Business',\n          amount: 75.25,\n          status: 'pending',\n          currency: 'USD'\n        },\n        { \n          id: 'TXN' + Math.floor(Math.random() * 1000000),\n          date: new Date(Date.now() - 172800000).toISOString(),\n          merchant: 'Online Store',\n          amount: 200.00,\n          status: 'completed',\n          currency: 'USD'\n        }\n      ]);\n      \n      setRevenueData([\n        { name: 'Jan', revenue: 4000 },\n        { name: 'Feb', revenue: 3000 },\n        { name: 'Mar', revenue: 5000 },\n        { name: 'Apr', revenue: 4780 },\n        { name: 'May', revenue: 3890 },\n        { name: 'Jun', revenue: 6390 },\n      ]);\n      \n    } catch (error) {\n      console.error('Error fetching dashboard data:', error);\n      toast({\n        title: 'Error',\n        description: 'Failed to load dashboard data. Please try again.',\n        variant: 'destructive',\n      });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchDashboardData();\n    \n    // Set up auto-refresh every 5 minutes\n    const interval = setInterval(fetchDashboardData, 5 * 60 * 1000);\n    \n    return () => clearInterval(interval);\n  }, []);\n  \n  const handleLogout = async () => {\n    try {\n      await logout();\n      navigate('/login');\n    } catch (error) {\n      console.error('Logout error:', error);\n      toast({\n        title: 'Error',\n        description: 'Failed to log out. Please try again.',\n        variant: 'destructive',\n      });\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <header className=\"bg-white shadow\">\n        <div className=\"max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8 flex justify-between items-center\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">Admin Dashboard</h1>\n            <p className=\"text-sm text-gray-500\">Welcome back, {user?.name || 'Admin'}</p>\n          </div>\n          <div className=\"flex items-center space-x-4\">\n            <Button \n              variant=\"outline\" \n              size=\"sm\" \n              onClick={fetchDashboardData}\n              disabled={isLoading}\n            >\n              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />\n              Refresh\n            </Button>\n            <Button variant=\"outline\" size=\"sm\" onClick={handleLogout}>\n              <LogOut className=\"h-4 w-4 mr-2\" />\n              Logout\n            </Button>\n          </div>\n        </div>\n      </header>\n\n      <main className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n        {/* Stats Grid */}\n        <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-8\">\n          {isLoading ? (\n            // Loading skeleton for stats\n            Array(4).fill(0).map((_, i) => (\n              <Card key={`stat-skeleton-${i}`}>\n                <CardHeader className=\"space-y-2\">\n                  <Skeleton className=\"h-4 w-24\" />\n                  <Skeleton className=\"h-6 w-16\" />\n                </CardHeader>\n                <CardContent>\n                  <Skeleton className=\"h-4 w-32\" />\n                </CardContent>\n              </Card>\n            ))\n          ) : (\n            // Actual stats cards\n            statCards.map((stat) => (\n              <Card key={stat.name} className=\"hover:shadow-md transition-shadow\">\n                <CardHeader className=\"pb-2\">\n                  <div className=\"flex justify-between items-center\">\n                    <p className=\"text-sm font-medium text-muted-foreground\">\n                      {stat.name}\n                    </p>\n                    <stat.icon className=\"h-4 w-4 text-muted-foreground\" />\n                  </div>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"text-2xl font-bold\">{stat.value}</div>\n                  <p className=\"text-xs text-muted-foreground mt-1\">\n                    <span className={stat.trend === 'up' ? 'text-green-500' : 'text-red-500'}>\n                      {stat.change}\n                    </span>{' '}\n                    {stat.description}\n                  </p>\n                </CardContent>\n              </Card>\n            ))\n          )}\n        </div>\n\n        <Tabs defaultValue=\"overview\" className=\"space-y-4\">\n          <div className=\"flex justify-between items-center\">\n            <TabsList>\n              <TabsTrigger value=\"overview\">Overview</TabsTrigger>\n              <TabsTrigger value=\"transactions\">Transactions</TabsTrigger>\n              <TabsTrigger value=\"traders\">Traders</TabsTrigger>\n              <TabsTrigger value=\"security\">\n                <Shield className=\"h-4 w-4 mr-2\" />\n                Security\n              </TabsTrigger>\n            </TabsList>\n            <div className=\"text-sm text-muted-foreground\">\n              Last updated: {new Date().toLocaleTimeString()}\n            </div>\n          </div>\n\n          <TabsContent value=\"overview\" className=\"space-y-4\">\n            <Card>\n              <CardHeader>\n                <CardTitle>Revenue Overview</CardTitle>\n                <CardDescription>\n                  Monthly revenue trends and analytics\n                </CardDescription>\n              </CardHeader>\n              <CardContent className=\"pl-2\">\n                {isLoading ? (\n                  <div className=\"h-[300px] flex items-center justify-center\">\n                    <RefreshCw className=\"h-8 w-8 animate-spin text-muted-foreground\" />\n                  </div>\n                ) : (\n                  <div className=\"h-[300px]\">\n                    <ResponsiveContainer width=\"100%\" height=\"100%\">\n                      <BarChart data={revenueData}>\n                        <CartesianGrid strokeDasharray=\"3 3\" stroke=\"#f0f0f0\" />\n                        <XAxis \n                          dataKey=\"name\" \n                          tick={{ fill: '#6b7280' }}\n                          axisLine={false}\n                        />\n                        <YAxis \n                          tick={{ fill: '#6b7280' }}\n                          axisLine={false}\n                          tickFormatter={(value) => `$${value.toLocaleString()}`}\n                        />\n                        <Tooltip \n                          formatter={(value: number) => [`$${value.toLocaleString()}`, 'Revenue']}\n                          labelFormatter={(label) => `Month: ${label}`}\n                        />\n                        <Legend />\n                        <Bar \n                          dataKey=\"revenue\" \n                          name=\"Monthly Revenue\"\n                          fill=\"#6366f1\" \n                          radius={[4, 4, 0, 0]}\n                        />\n                      </BarChart>\n                    </ResponsiveContainer>\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n          </TabsContent>\n\n          <TabsContent value=\"transactions\">\n            <Card>\n              <CardHeader>\n                <div className=\"flex justify-between items-center\">\n                  <div>\n                    <CardTitle>Recent Transactions</CardTitle>\n                    <CardDescription>\n                      Latest transactions across all merchants\n                    </CardDescription>\n                  </div>\n                  <Button variant=\"outline\" size=\"sm\" onClick={fetchDashboardData}>\n                    <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />\n                    Refresh\n                  </Button>\n                </div>\n              </CardHeader>\n              <CardContent>\n                {isLoading ? (\n                  <div className=\"space-y-4\">\n                    {Array(5).fill(0).map((_, i) => (\n                      <Skeleton key={`txn-skeleton-${i}`} className=\"h-12 w-full\" />\n                    ))}\n                  </div>\n                ) : (\n                  <div className=\"rounded-md border\">\n                    <Table>\n                      <TableHeader>\n                        <TableRow>\n                          <TableHead>ID</TableHead>\n                          <TableHead>Date & Time</TableHead>\n                          <TableHead>Merchant</TableHead>\n                          <TableHead className=\"text-right\">Amount</TableHead>\n                          <TableHead>Status</TableHead>\n                        </TableRow>\n                      </TableHeader>\n                      <TableBody>\n                        {recentTransactions.length > 0 ? (\n                          recentTransactions.map((txn) => (\n                            <TableRow key={txn.id} className=\"hover:bg-gray-50\">\n                              <TableCell className=\"font-medium\">\n                                <span className=\"font-mono text-xs\">{txn.id}</span>\n                              </TableCell>\n                              <TableCell>\n                                <div className=\"text-sm\">\n                                  {formatDate(txn.date)}\n                                </div>\n                              </TableCell>\n                              <TableCell>\n                                <div className=\"font-medium\">{txn.merchant}</div>\n                              </TableCell>\n                              <TableCell className=\"text-right font-medium\">\n                                {formatCurrency(txn.amount, txn.currency)}\n                              </TableCell>\n                              <TableCell>\n                                <span \n                                  className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                                    txn.status === 'completed' \n                                      ? 'bg-green-100 text-green-800' \n                                      : txn.status === 'pending'\n                                      ? 'bg-yellow-100 text-yellow-800'\n                                      : 'bg-red-100 text-red-800'\n                                  }`}\n                                >\n                                  {txn.status.charAt(0).toUpperCase() + txn.status.slice(1)}\n                                </span>\n                              </TableCell>\n                            </TableRow>\n                          ))\n                        ) : (\n                          <TableRow>\n                            <TableCell colSpan={5} className=\"text-center py-8 text-muted-foreground\">\n                              No transactions found\n                            </TableCell>\n                          </TableRow>\n                        )}\n                      </TableBody>\n                    </Table>\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n          </TabsContent>\n\n          <TabsContent value=\"traders\">\n            <Card>\n              <CardHeader>\n                <CardTitle>Trader Management</CardTitle>\n                <CardDescription>\n                  Manage traders and their assignments\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <TraderManagement />\n              </CardContent>\n            </Card>\n          </TabsContent>\n\n          <TabsContent value=\"security\">\n            <Card>\n              <CardHeader>\n                <CardTitle>Security Settings</CardTitle>\n                <CardDescription>\n                  Manage security settings and access controls\n                </CardDescription>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div className=\"p-4 border rounded-lg\">\n                  <h3 className=\"font-medium mb-2\">Two-Factor Authentication</h3>\n                  <p className=\"text-sm text-muted-foreground mb-4\">\n                    Add an extra layer of security to your admin account\n                  </p>\n                  <Button variant=\"outline\" size=\"sm\">\n                    Enable 2FA\n                  </Button>\n                </div>\n                \n                <div className=\"p-4 border rounded-lg\">\n                  <h3 className=\"font-medium mb-2\">Session Management</h3>\n                  <p className=\"text-sm text-muted-foreground mb-4\">\n                    View and manage active sessions\n                  </p>\n                  <Button variant=\"outline\" size=\"sm\">\n                    View Active Sessions\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          </TabsContent>\n        </Tabs>\n      </main>\n    </div>\n  )\n}\n\nexport default AdminDashboard\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name RefreshCw\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAxMmE5IDkgMCAwIDEgOS05IDkuNzUgOS43NSAwIDAgMSA2Ljc0IDIuNzRMMjEgOCIgLz4KICA8cGF0aCBkPSJNMjEgM3Y1aC01IiAvPgogIDxwYXRoIGQ9Ik0yMSAxMmE5IDkgMCAwIDEtOSA5IDkuNzUgOS43NSAwIDAgMS02Ljc0LTIuNzRMMyAxNiIgLz4KICA8cGF0aCBkPSJNOCAxNkgzdjUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/refresh-cw\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst RefreshCw = createLucideIcon('RefreshCw', [\n  [\n    'path',\n    { d: 'M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8', key: 'v9h5vc' },\n  ],\n  ['path', { d: 'M21 3v5h-5', key: '1q7to0' }],\n  [\n    'path',\n    { d: 'M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16', key: '3uifl3' },\n  ],\n  ['path', { d: 'M8 16H3v5', key: '1cv678' }],\n]);\n\nexport default RefreshCw;\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContext, createContextScope } from '@radix-ui/react-context';\nimport { useId } from '@radix-ui/react-id';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { FocusScope } from '@radix-ui/react-focus-scope';\nimport { Portal as PortalPrimitive } from '@radix-ui/react-portal';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useFocusGuards } from '@radix-ui/react-focus-guards';\nimport { RemoveScroll } from 'react-remove-scroll';\nimport { hideOthers } from 'aria-hidden';\nimport { createSlot } from '@radix-ui/react-slot';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Dialog\n * -----------------------------------------------------------------------------------------------*/\n\nconst DIALOG_NAME = 'Dialog';\n\ntype ScopedProps<P> = P & { __scopeDialog?: Scope };\nconst [createDialogContext, createDialogScope] = createContextScope(DIALOG_NAME);\n\ntype DialogContextValue = {\n  triggerRef: React.RefObject<HTMLButtonElement | null>;\n  contentRef: React.RefObject<DialogContentElement | null>;\n  contentId: string;\n  titleId: string;\n  descriptionId: string;\n  open: boolean;\n  onOpenChange(open: boolean): void;\n  onOpenToggle(): void;\n  modal: boolean;\n};\n\nconst [DialogProvider, useDialogContext] = createDialogContext<DialogContextValue>(DIALOG_NAME);\n\ninterface DialogProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n  modal?: boolean;\n}\n\nconst Dialog: React.FC<DialogProps> = (props: ScopedProps<DialogProps>) => {\n  const {\n    __scopeDialog,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    modal = true,\n  } = props;\n  const triggerRef = React.useRef<HTMLButtonElement>(null);\n  const contentRef = React.useRef<DialogContentElement>(null);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: DIALOG_NAME,\n  });\n\n  return (\n    <DialogProvider\n      scope={__scopeDialog}\n      triggerRef={triggerRef}\n      contentRef={contentRef}\n      contentId={useId()}\n      titleId={useId()}\n      descriptionId={useId()}\n      open={open}\n      onOpenChange={setOpen}\n      onOpenToggle={React.useCallback(() => setOpen((prevOpen) => !prevOpen), [setOpen])}\n      modal={modal}\n    >\n      {children}\n    </DialogProvider>\n  );\n};\n\nDialog.displayName = DIALOG_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'DialogTrigger';\n\ntype DialogTriggerElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface DialogTriggerProps extends PrimitiveButtonProps {}\n\nconst DialogTrigger = React.forwardRef<DialogTriggerElement, DialogTriggerProps>(\n  (props: ScopedProps<DialogTriggerProps>, forwardedRef) => {\n    const { __scopeDialog, ...triggerProps } = props;\n    const context = useDialogContext(TRIGGER_NAME, __scopeDialog);\n    const composedTriggerRef = useComposedRefs(forwardedRef, context.triggerRef);\n    return (\n      <Primitive.button\n        type=\"button\"\n        aria-haspopup=\"dialog\"\n        aria-expanded={context.open}\n        aria-controls={context.contentId}\n        data-state={getState(context.open)}\n        {...triggerProps}\n        ref={composedTriggerRef}\n        onClick={composeEventHandlers(props.onClick, context.onOpenToggle)}\n      />\n    );\n  }\n);\n\nDialogTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'DialogPortal';\n\ntype PortalContextValue = { forceMount?: true };\nconst [PortalProvider, usePortalContext] = createDialogContext<PortalContextValue>(PORTAL_NAME, {\n  forceMount: undefined,\n});\n\ntype PortalProps = React.ComponentPropsWithoutRef<typeof PortalPrimitive>;\ninterface DialogPortalProps {\n  children?: React.ReactNode;\n  /**\n   * Specify a container element to portal the content into.\n   */\n  container?: PortalProps['container'];\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst DialogPortal: React.FC<DialogPortalProps> = (props: ScopedProps<DialogPortalProps>) => {\n  const { __scopeDialog, forceMount, children, container } = props;\n  const context = useDialogContext(PORTAL_NAME, __scopeDialog);\n  return (\n    <PortalProvider scope={__scopeDialog} forceMount={forceMount}>\n      {React.Children.map(children, (child) => (\n        <Presence present={forceMount || context.open}>\n          <PortalPrimitive asChild container={container}>\n            {child}\n          </PortalPrimitive>\n        </Presence>\n      ))}\n    </PortalProvider>\n  );\n};\n\nDialogPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogOverlay\n * -----------------------------------------------------------------------------------------------*/\n\nconst OVERLAY_NAME = 'DialogOverlay';\n\ntype DialogOverlayElement = DialogOverlayImplElement;\ninterface DialogOverlayProps extends DialogOverlayImplProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst DialogOverlay = React.forwardRef<DialogOverlayElement, DialogOverlayProps>(\n  (props: ScopedProps<DialogOverlayProps>, forwardedRef) => {\n    const portalContext = usePortalContext(OVERLAY_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, props.__scopeDialog);\n    return context.modal ? (\n      <Presence present={forceMount || context.open}>\n        <DialogOverlayImpl {...overlayProps} ref={forwardedRef} />\n      </Presence>\n    ) : null;\n  }\n);\n\nDialogOverlay.displayName = OVERLAY_NAME;\n\ntype DialogOverlayImplElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface DialogOverlayImplProps extends PrimitiveDivProps {}\n\nconst Slot = createSlot('DialogOverlay.RemoveScroll');\n\nconst DialogOverlayImpl = React.forwardRef<DialogOverlayImplElement, DialogOverlayImplProps>(\n  (props: ScopedProps<DialogOverlayImplProps>, forwardedRef) => {\n    const { __scopeDialog, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, __scopeDialog);\n    return (\n      // Make sure `Content` is scrollable even when it doesn't live inside `RemoveScroll`\n      // ie. when `Overlay` and `Content` are siblings\n      <RemoveScroll as={Slot} allowPinchZoom shards={[context.contentRef]}>\n        <Primitive.div\n          data-state={getState(context.open)}\n          {...overlayProps}\n          ref={forwardedRef}\n          // We re-enable pointer-events prevented by `Dialog.Content` to allow scrolling the overlay.\n          style={{ pointerEvents: 'auto', ...overlayProps.style }}\n        />\n      </RemoveScroll>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * DialogContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'DialogContent';\n\ntype DialogContentElement = DialogContentTypeElement;\ninterface DialogContentProps extends DialogContentTypeProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst DialogContent = React.forwardRef<DialogContentElement, DialogContentProps>(\n  (props: ScopedProps<DialogContentProps>, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    return (\n      <Presence present={forceMount || context.open}>\n        {context.modal ? (\n          <DialogContentModal {...contentProps} ref={forwardedRef} />\n        ) : (\n          <DialogContentNonModal {...contentProps} ref={forwardedRef} />\n        )}\n      </Presence>\n    );\n  }\n);\n\nDialogContent.displayName = CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype DialogContentTypeElement = DialogContentImplElement;\ninterface DialogContentTypeProps\n  extends Omit<DialogContentImplProps, 'trapFocus' | 'disableOutsidePointerEvents'> {}\n\nconst DialogContentModal = React.forwardRef<DialogContentTypeElement, DialogContentTypeProps>(\n  (props: ScopedProps<DialogContentTypeProps>, forwardedRef) => {\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const contentRef = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, context.contentRef, contentRef);\n\n    // aria-hide everything except the content (better supported equivalent to setting aria-modal)\n    React.useEffect(() => {\n      const content = contentRef.current;\n      if (content) return hideOthers(content);\n    }, []);\n\n    return (\n      <DialogContentImpl\n        {...props}\n        ref={composedRefs}\n        // we make sure focus isn't trapped once `DialogContent` has been closed\n        // (closed !== unmounted when animating out)\n        trapFocus={context.open}\n        disableOutsidePointerEvents\n        onCloseAutoFocus={composeEventHandlers(props.onCloseAutoFocus, (event) => {\n          event.preventDefault();\n          context.triggerRef.current?.focus();\n        })}\n        onPointerDownOutside={composeEventHandlers(props.onPointerDownOutside, (event) => {\n          const originalEvent = event.detail.originalEvent;\n          const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n          const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n\n          // If the event is a right-click, we shouldn't close because\n          // it is effectively as if we right-clicked the `Overlay`.\n          if (isRightClick) event.preventDefault();\n        })}\n        // When focus is trapped, a `focusout` event may still happen.\n        // We make sure we don't trigger our `onDismiss` in such case.\n        onFocusOutside={composeEventHandlers(props.onFocusOutside, (event) =>\n          event.preventDefault()\n        )}\n      />\n    );\n  }\n);\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst DialogContentNonModal = React.forwardRef<DialogContentTypeElement, DialogContentTypeProps>(\n  (props: ScopedProps<DialogContentTypeProps>, forwardedRef) => {\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const hasInteractedOutsideRef = React.useRef(false);\n    const hasPointerDownOutsideRef = React.useRef(false);\n\n    return (\n      <DialogContentImpl\n        {...props}\n        ref={forwardedRef}\n        trapFocus={false}\n        disableOutsidePointerEvents={false}\n        onCloseAutoFocus={(event) => {\n          props.onCloseAutoFocus?.(event);\n\n          if (!event.defaultPrevented) {\n            if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n            // Always prevent auto focus because we either focus manually or want user agent focus\n            event.preventDefault();\n          }\n\n          hasInteractedOutsideRef.current = false;\n          hasPointerDownOutsideRef.current = false;\n        }}\n        onInteractOutside={(event) => {\n          props.onInteractOutside?.(event);\n\n          if (!event.defaultPrevented) {\n            hasInteractedOutsideRef.current = true;\n            if (event.detail.originalEvent.type === 'pointerdown') {\n              hasPointerDownOutsideRef.current = true;\n            }\n          }\n\n          // Prevent dismissing when clicking the trigger.\n          // As the trigger is already setup to close, without doing so would\n          // cause it to close and immediately open.\n          const target = event.target as HTMLElement;\n          const targetIsTrigger = context.triggerRef.current?.contains(target);\n          if (targetIsTrigger) event.preventDefault();\n\n          // On Safari if the trigger is inside a container with tabIndex={0}, when clicked\n          // we will get the pointer down outside event on the trigger, but then a subsequent\n          // focus outside event on the container, we ignore any focus outside event when we've\n          // already had a pointer down outside event.\n          if (event.detail.originalEvent.type === 'focusin' && hasPointerDownOutsideRef.current) {\n            event.preventDefault();\n          }\n        }}\n      />\n    );\n  }\n);\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype DialogContentImplElement = React.ComponentRef<typeof DismissableLayer>;\ntype DismissableLayerProps = React.ComponentPropsWithoutRef<typeof DismissableLayer>;\ntype FocusScopeProps = React.ComponentPropsWithoutRef<typeof FocusScope>;\ninterface DialogContentImplProps extends Omit<DismissableLayerProps, 'onDismiss'> {\n  /**\n   * When `true`, focus cannot escape the `Content` via keyboard,\n   * pointer, or a programmatic focus.\n   * @defaultValue false\n   */\n  trapFocus?: FocusScopeProps['trapped'];\n\n  /**\n   * Event handler called when auto-focusing on open.\n   * Can be prevented.\n   */\n  onOpenAutoFocus?: FocusScopeProps['onMountAutoFocus'];\n\n  /**\n   * Event handler called when auto-focusing on close.\n   * Can be prevented.\n   */\n  onCloseAutoFocus?: FocusScopeProps['onUnmountAutoFocus'];\n}\n\nconst DialogContentImpl = React.forwardRef<DialogContentImplElement, DialogContentImplProps>(\n  (props: ScopedProps<DialogContentImplProps>, forwardedRef) => {\n    const { __scopeDialog, trapFocus, onOpenAutoFocus, onCloseAutoFocus, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, __scopeDialog);\n    const contentRef = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, contentRef);\n\n    // Make sure the whole tree has focus guards as our `Dialog` will be\n    // the last element in the DOM (because of the `Portal`)\n    useFocusGuards();\n\n    return (\n      <>\n        <FocusScope\n          asChild\n          loop\n          trapped={trapFocus}\n          onMountAutoFocus={onOpenAutoFocus}\n          onUnmountAutoFocus={onCloseAutoFocus}\n        >\n          <DismissableLayer\n            role=\"dialog\"\n            id={context.contentId}\n            aria-describedby={context.descriptionId}\n            aria-labelledby={context.titleId}\n            data-state={getState(context.open)}\n            {...contentProps}\n            ref={composedRefs}\n            onDismiss={() => context.onOpenChange(false)}\n          />\n        </FocusScope>\n        {process.env.NODE_ENV !== 'production' && (\n          <>\n            <TitleWarning titleId={context.titleId} />\n            <DescriptionWarning contentRef={contentRef} descriptionId={context.descriptionId} />\n          </>\n        )}\n      </>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * DialogTitle\n * -----------------------------------------------------------------------------------------------*/\n\nconst TITLE_NAME = 'DialogTitle';\n\ntype DialogTitleElement = React.ComponentRef<typeof Primitive.h2>;\ntype PrimitiveHeading2Props = React.ComponentPropsWithoutRef<typeof Primitive.h2>;\ninterface DialogTitleProps extends PrimitiveHeading2Props {}\n\nconst DialogTitle = React.forwardRef<DialogTitleElement, DialogTitleProps>(\n  (props: ScopedProps<DialogTitleProps>, forwardedRef) => {\n    const { __scopeDialog, ...titleProps } = props;\n    const context = useDialogContext(TITLE_NAME, __scopeDialog);\n    return <Primitive.h2 id={context.titleId} {...titleProps} ref={forwardedRef} />;\n  }\n);\n\nDialogTitle.displayName = TITLE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogDescription\n * -----------------------------------------------------------------------------------------------*/\n\nconst DESCRIPTION_NAME = 'DialogDescription';\n\ntype DialogDescriptionElement = React.ComponentRef<typeof Primitive.p>;\ntype PrimitiveParagraphProps = React.ComponentPropsWithoutRef<typeof Primitive.p>;\ninterface DialogDescriptionProps extends PrimitiveParagraphProps {}\n\nconst DialogDescription = React.forwardRef<DialogDescriptionElement, DialogDescriptionProps>(\n  (props: ScopedProps<DialogDescriptionProps>, forwardedRef) => {\n    const { __scopeDialog, ...descriptionProps } = props;\n    const context = useDialogContext(DESCRIPTION_NAME, __scopeDialog);\n    return <Primitive.p id={context.descriptionId} {...descriptionProps} ref={forwardedRef} />;\n  }\n);\n\nDialogDescription.displayName = DESCRIPTION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogClose\n * -----------------------------------------------------------------------------------------------*/\n\nconst CLOSE_NAME = 'DialogClose';\n\ntype DialogCloseElement = React.ComponentRef<typeof Primitive.button>;\ninterface DialogCloseProps extends PrimitiveButtonProps {}\n\nconst DialogClose = React.forwardRef<DialogCloseElement, DialogCloseProps>(\n  (props: ScopedProps<DialogCloseProps>, forwardedRef) => {\n    const { __scopeDialog, ...closeProps } = props;\n    const context = useDialogContext(CLOSE_NAME, __scopeDialog);\n    return (\n      <Primitive.button\n        type=\"button\"\n        {...closeProps}\n        ref={forwardedRef}\n        onClick={composeEventHandlers(props.onClick, () => context.onOpenChange(false))}\n      />\n    );\n  }\n);\n\nDialogClose.displayName = CLOSE_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getState(open: boolean) {\n  return open ? 'open' : 'closed';\n}\n\nconst TITLE_WARNING_NAME = 'DialogTitleWarning';\n\nconst [WarningProvider, useWarningContext] = createContext(TITLE_WARNING_NAME, {\n  contentName: CONTENT_NAME,\n  titleName: TITLE_NAME,\n  docsSlug: 'dialog',\n});\n\ntype TitleWarningProps = { titleId?: string };\n\nconst TitleWarning: React.FC<TitleWarningProps> = ({ titleId }) => {\n  const titleWarningContext = useWarningContext(TITLE_WARNING_NAME);\n\n  const MESSAGE = `\\`${titleWarningContext.contentName}\\` requires a \\`${titleWarningContext.titleName}\\` for the component to be accessible for screen reader users.\n\nIf you want to hide the \\`${titleWarningContext.titleName}\\`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/${titleWarningContext.docsSlug}`;\n\n  React.useEffect(() => {\n    if (titleId) {\n      const hasTitle = document.getElementById(titleId);\n      if (!hasTitle) console.error(MESSAGE);\n    }\n  }, [MESSAGE, titleId]);\n\n  return null;\n};\n\nconst DESCRIPTION_WARNING_NAME = 'DialogDescriptionWarning';\n\ntype DescriptionWarningProps = {\n  contentRef: React.RefObject<DialogContentElement | null>;\n  descriptionId?: string;\n};\n\nconst DescriptionWarning: React.FC<DescriptionWarningProps> = ({ contentRef, descriptionId }) => {\n  const descriptionWarningContext = useWarningContext(DESCRIPTION_WARNING_NAME);\n  const MESSAGE = `Warning: Missing \\`Description\\` or \\`aria-describedby={undefined}\\` for {${descriptionWarningContext.contentName}}.`;\n\n  React.useEffect(() => {\n    const describedById = contentRef.current?.getAttribute('aria-describedby');\n    // if we have an id and the user hasn't set aria-describedby={undefined}\n    if (descriptionId && describedById) {\n      const hasDescription = document.getElementById(descriptionId);\n      if (!hasDescription) console.warn(MESSAGE);\n    }\n  }, [MESSAGE, contentRef, descriptionId]);\n\n  return null;\n};\n\nconst Root = Dialog;\nconst Trigger = DialogTrigger;\nconst Portal = DialogPortal;\nconst Overlay = DialogOverlay;\nconst Content = DialogContent;\nconst Title = DialogTitle;\nconst Description = DialogDescription;\nconst Close = DialogClose;\n\nexport {\n  createDialogScope,\n  //\n  Dialog,\n  DialogTrigger,\n  DialogPortal,\n  DialogOverlay,\n  DialogContent,\n  DialogTitle,\n  DialogDescription,\n  DialogClose,\n  //\n  Root,\n  Trigger,\n  Portal,\n  Overlay,\n  Content,\n  Title,\n  Description,\n  Close,\n  //\n  WarningProvider,\n};\nexport type {\n  DialogProps,\n  DialogTriggerProps,\n  DialogPortalProps,\n  DialogOverlayProps,\n  DialogContentProps,\n  DialogTitleProps,\n  DialogDescriptionProps,\n  DialogCloseProps,\n};\n", "import * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown } from \"lucide-react\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n}\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name CreditCard\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iMTQiIHg9IjIiIHk9IjUiIHJ4PSIyIiAvPgogIDxsaW5lIHgxPSIyIiB4Mj0iMjIiIHkxPSIxMCIgeTI9IjEwIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/credit-card\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CreditCard = createLucideIcon('CreditCard', [\n  [\n    'rect',\n    { width: '20', height: '14', x: '2', y: '5', rx: '2', key: 'ynyp8z' },\n  ],\n  ['line', { x1: '2', x2: '22', y1: '10', y2: '10', key: '1b3vmo' }],\n]);\n\nexport default CreditCard;\n", "import * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n", "import * as React from \"react\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardDescription,\n  CardContent,\n}\n", "import * as React from 'react';\nimport { ToastProps, ToastActionElement } from './toast';\n\ntype ToasterToast = ToastProps & {\n  id: string;\n  title?: React.ReactNode;\n  description?: React.ReactNode;\n};\n\nconst actionTypes = {\n  ADD_TOAST: 'ADD_TOAST',\n  UPDATE_TOAST: 'UPDATE_TOAST',\n  DISMISS_TOAST: 'DISMISS_TOAST',\n  REMOVE_TOAST: 'REMOVE_TOAST',\n} as const;\n\nlet count = 0;\n\nfunction genId() {\n  count = (count + 1) % Number.MAX_SAFE_INTEGER;\n  return count.toString();\n}\n\ntype ActionType = typeof actionTypes;\n\ntype Action =\n  | {\n      type: ActionType['ADD_TOAST'];\n      toast: ToasterToast;\n    }\n  | {\n      type: ActionType['UPDATE_TOAST'];\n      toast: Partial<ToasterToast>;\n    }\n  | {\n      type: ActionType['DISMISS_TOAST'];\n      toastId?: ToasterToast['id'];\n    }\n  | {\n      type: ActionType['REMOVE_TOAST'];\n      toastId?: ToasterToast['id'];\n    };\n\ninterface State {\n  toasts: ToasterToast[];\n}\n\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>();\n\nconst addToRemoveQueue = (toastId: string) => {\n  if (toastTimeouts.has(toastId)) {\n    return;\n  }\n\n  const timeout = setTimeout(() => {\n    toastTimeouts.delete(toastId);\n    dispatch({\n      type: 'REMOVE_TOAST',\n      toastId: toastId,\n    });\n  }, 1000);\n\n  toastTimeouts.set(toastId, timeout);\n};\n\nexport const reducer = (state: State, action: Action): State => {\n  switch (action.type) {\n    case 'ADD_TOAST':\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, 5),\n      };\n\n    case 'UPDATE_TOAST':\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\n        ),\n      };\n\n    case 'DISMISS_TOAST': {\n      const { toastId } = action;\n\n      // ! Side effects ! - This could be extracted into a dismissToast() action,\n      // but I'll keep it here for simplicity\n      if (toastId) {\n        addToRemoveQueue(toastId);\n      } else {\n        state.toasts.forEach((toast) => {\n          addToRemoveQueue(toast.id);\n        });\n      }\n\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                open: false,\n              }\n            : t\n        ),\n      };\n    }\n    case 'REMOVE_TOAST':\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        };\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\n      };\n  }\n};\n\nconst listeners: Array<(state: State) => void> = [];\n\nlet memoryState: State = { toasts: [] };\n\nfunction dispatch(action: Action) {\n  memoryState = reducer(memoryState, action);\n  listeners.forEach((listener) => {\n    listener(memoryState);\n  });\n}\n\ntype Toast = Omit<ToasterToast, 'id'>;\n\nfunction toast({ ...props }: Toast) {\n  const id = genId();\n\n  const update = (props: ToasterToast) =>\n    dispatch({\n      type: 'UPDATE_TOAST',\n      toast: { ...props, id },\n    });\n  const dismiss = () => dispatch({ type: 'DISMISS_TOAST', toastId: id });\n\n  dispatch({\n    type: 'ADD_TOAST',\n    toast: {\n      ...props,\n      id,\n      open: true,\n      onOpenChange: (open: boolean) => {\n        if (!open) dismiss();\n      },\n    },\n  });\n\n  return {\n    id: id,\n    dismiss,\n    update,\n  };\n}\n\nfunction useToast() {\n  const [state, setState] = React.useState<State>(memoryState);\n\n  React.useEffect(() => {\n    listeners.push(setState);\n    return () => {\n      const index = listeners.indexOf(setState);\n      if (index > -1) {\n        listeners.splice(index, 1);\n      }\n    };\n  }, [state]);\n\n  return {\n    ...state,\n    toast,\n    dismiss: (toastId?: string) => dispatch({ type: 'DISMISS_TOAST', toastId }),\n  };\n}\n\nexport { useToast, toast };\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Users\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMjF2LTJhNCA0IDAgMCAwLTQtNEg2YTQgNCAwIDAgMC00IDR2MiIgLz4KICA8Y2lyY2xlIGN4PSI5IiBjeT0iNyIgcj0iNCIgLz4KICA8cGF0aCBkPSJNMjIgMjF2LTJhNCA0IDAgMCAwLTMtMy44NyIgLz4KICA8cGF0aCBkPSJNMTYgMy4xM2E0IDQgMCAwIDEgMCA3Ljc1IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/users\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Users = createLucideIcon('Users', [\n  ['path', { d: 'M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2', key: '1yyitq' }],\n  ['circle', { cx: '9', cy: '7', r: '4', key: 'nufk8' }],\n  ['path', { d: 'M22 21v-2a4 4 0 0 0-3-3.87', key: 'kshegd' }],\n  ['path', { d: 'M16 3.13a4 4 0 0 1 0 7.75', key: '1da9ce' }],\n]);\n\nexport default Users;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name LogOut\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOSAyMUg1YTIgMiAwIDAgMS0yLTJWNWEyIDIgMCAwIDEgMi0yaDQiIC8+CiAgPHBvbHlsaW5lIHBvaW50cz0iMTYgMTcgMjEgMTIgMTYgNyIgLz4KICA8bGluZSB4MT0iMjEiIHgyPSI5IiB5MT0iMTIiIHkyPSIxMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/log-out\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst LogOut = createLucideIcon('LogOut', [\n  ['path', { d: 'M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4', key: '1uf3rs' }],\n  ['polyline', { points: '16 17 21 12 16 7', key: '1gabdz' }],\n  ['line', { x1: '21', x2: '9', y1: '12', y2: '12', key: '1uyos4' }],\n]);\n\nexport default LogOut;\n", "import React, { useState, useEffect } from 'react';\nimport { useAuth, type User, type BaseUser } from '../contexts/AuthContext';\nimport axios from 'axios';\nimport { format } from 'date-fns';\nimport {\n  Table,\n  TableBody,\n  TableCell,\n  TableHead,\n  TableHeader,\n  TableRow,\n} from './ui/table';\nimport { Button } from './ui/button';\nimport { Input } from './ui/input';\nimport { Label } from './ui/label';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';\nimport { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog';\nimport { useToast } from './ui/use-toast';\n\n// Extended user type that includes all properties from BaseUser\ninterface ExtendedUser extends Omit<BaseUser, 'role'> {\n  _id: string;\n  role: User['role'];\n  businessName?: string;\n  name: string;\n  email: string;\n}\n\ninterface TraderAssignment {\n  _id: string;\n  merchant: ExtendedUser | string;\n  trader: ExtendedUser | string;\n  startDate: string;\n  endDate?: string;\n  status: 'active' | 'inactive' | 'completed' | 'pending';\n  notes?: string;\n  assignedBy: User | string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nconst API_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api/v1';\n\nexport const TraderManagement: React.FC = () => {\n  const { user, logout } = useAuth();\n  const token = localStorage.getItem('token');\n  const { toast } = useToast();\n  const [assignments, setAssignments] = useState<TraderAssignment[]>([]);\n  const [traders, setTraders] = useState<ExtendedUser[]>([]);\n  const [merchants, setMerchants] = useState<ExtendedUser[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [isDialogOpen, setIsDialogOpen] = useState(false);\n  const [formData, setFormData] = useState({\n    merchantId: '',\n    traderId: '',\n    startDate: format(new Date(), 'yyyy-MM-dd'),\n    endDate: '',\n    notes: '',\n  });\n\n  const headers = {\n    'Content-Type': 'application/json',\n    ...(token ? { Authorization: `Bearer ${token}` } : {}),\n  };\n\n  useEffect(() => {\n    fetchData();\n    fetchUsers();\n  }, []);\n\n  const fetchData = async () => {\n    try {\n      const [assignmentsRes] = await Promise.all([\n        axios.get(`${API_URL}/trader/assignments`, { headers }),\n      ]);\n      setAssignments(assignmentsRes.data.data);\n    } catch (error) {\n      console.error('Error fetching data:', error);\n      toast({\n        title: 'Error',\n        description: 'Failed to fetch data',\n        variant: 'destructive',\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchUsers = async () => {\n    try {\n      const [tradersRes, merchantsRes] = await Promise.all([\n        axios.get(`${API_URL}/admin/users?role=trader`, { headers }),\n        axios.get(`${API_URL}/admin/users?role=merchant`, { headers }),\n      ]);\n      setTraders(tradersRes.data.data);\n      setMerchants(merchantsRes.data.data);\n    } catch (error) {\n      console.error('Error fetching users:', error);\n      toast({\n        title: 'Error',\n        description: 'Failed to fetch users',\n        variant: 'destructive',\n      });\n    }\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    try {\n      await axios.post(\n        `${API_URL}/trader/assignments`,\n        {\n          ...formData,\n          startDate: new Date(formData.startDate).toISOString(),\n          endDate: formData.endDate ? new Date(formData.endDate).toISOString() : undefined,\n        },\n        { headers }\n      );\n      \n      toast({\n        title: 'Success',\n        description: 'Trader assigned successfully',\n      });\n      \n      setIsDialogOpen(false);\n      setFormData({\n        merchantId: '',\n        traderId: '',\n        startDate: format(new Date(), 'yyyy-MM-dd'),\n        endDate: '',\n        notes: '',\n      });\n      \n      fetchData();\n    } catch (error) {\n      console.error('Error creating assignment:', error);\n      toast({\n        title: 'Error',\n        description: 'Failed to assign trader',\n        variant: 'destructive',\n      });\n    }\n  };\n\n  const updateAssignmentStatus = async (id: string, status: string) => {\n    try {\n      await axios.put(\n        `${API_URL}/trader/assignments/${id}`,\n        { status },\n        { headers }\n      );\n      \n      toast({\n        title: 'Success',\n        description: 'Assignment updated successfully',\n      });\n      \n      fetchData();\n    } catch (error) {\n      console.error('Error updating assignment:', error);\n      toast({\n        title: 'Error',\n        description: 'Failed to update assignment',\n        variant: 'destructive',\n      });\n    }\n  };\n\n  const getUserName = (user: ExtendedUser | string | undefined) => {\n    if (!user) return 'Unknown';\n    if (typeof user === 'string') return 'Loading...';\n    return (user as ExtendedUser).name || (user as ExtendedUser).email || 'Unknown';\n  };\n\n  const getMerchantName = (user: ExtendedUser | string | undefined) => {\n    if (!user) return 'Unknown';\n    if (typeof user === 'string') return 'Loading...';\n    const userData = user as ExtendedUser;\n    return userData.businessName || userData.name || userData.email || 'Unknown';\n  };\n\n  if (loading) {\n    return <div>Loading...</div>;\n  }\n\n  return (\n    <div className=\"container mx-auto p-6\">\n      <div className=\"flex justify-between items-center mb-6\">\n        <h1 className=\"text-2xl font-bold\">Trader Management</h1>\n        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>\n          <DialogTrigger asChild>\n            <Button>Assign Trader</Button>\n          </DialogTrigger>\n          <DialogContent>\n            <DialogHeader>\n              <DialogTitle>Assign Trader to Merchant</DialogTitle>\n            </DialogHeader>\n            <form onSubmit={handleSubmit} className=\"space-y-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"merchantId\">Merchant</Label>\n                <Select\n                  value={formData.merchantId}\n                  onValueChange={(value) => setFormData({ ...formData, merchantId: value })}\n                  required\n                >\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Select a merchant\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {merchants.map((merchant) => (\n                      <SelectItem key={merchant._id} value={merchant._id || ''}>\n                        {getMerchantName(merchant)}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"traderId\">Trader</Label>\n                <Select\n                  value={formData.traderId}\n                  onValueChange={(value) => setFormData({ ...formData, traderId: value })}\n                  required\n                >\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Select a trader\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {traders.map((trader) => (\n                      <SelectItem key={trader._id} value={trader._id || ''}>\n                        {(trader as User).name || (trader as User).email || 'Unknown Trader'}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"startDate\">Start Date</Label>\n                  <Input\n                    type=\"date\"\n                    id=\"startDate\"\n                    value={formData.startDate}\n                    onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}\n                    required\n                  />\n                </div>\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"endDate\">End Date (Optional)</Label>\n                  <Input\n                    type=\"date\"\n                    id=\"endDate\"\n                    value={formData.endDate}\n                    onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}\n                    min={formData.startDate}\n                  />\n                </div>\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"notes\">Notes (Optional)</Label>\n                <Input\n                  id=\"notes\"\n                  value={formData.notes}\n                  onChange={(e) => setFormData({ ...formData, notes: e.target.value })}\n                  placeholder=\"Additional notes about this assignment\"\n                />\n              </div>\n\n              <div className=\"flex justify-end space-x-2 pt-4\">\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  onClick={() => setIsDialogOpen(false)}\n                >\n                  Cancel\n                </Button>\n                <Button type=\"submit\">Assign Trader</Button>\n              </div>\n            </form>\n          </DialogContent>\n        </Dialog>\n      </div>\n\n      <div className=\"rounded-md border\">\n        <Table>\n          <TableHeader>\n            <TableRow>\n              <TableHead>Merchant</TableHead>\n              <TableHead>Trader</TableHead>\n              <TableHead>Start Date</TableHead>\n              <TableHead>End Date</TableHead>\n              <TableHead>Status</TableHead>\n              <TableHead>Actions</TableHead>\n            </TableRow>\n          </TableHeader>\n          <TableBody>\n            {assignments.length === 0 ? (\n              <TableRow>\n                <TableCell colSpan={6} className=\"text-center py-4\">\n                  No assignments found\n                </TableCell>\n              </TableRow>\n            ) : (\n              assignments.map((assignment) => (\n                <TableRow key={assignment._id}>\n                  <TableCell>{getUserName(assignment.merchant)}</TableCell>\n                  <TableCell>{getUserName(assignment.trader)}</TableCell>\n                  <TableCell>{format(new Date(assignment.startDate), 'MMM d, yyyy')}</TableCell>\n                  <TableCell>\n                    {assignment.endDate ? format(new Date(assignment.endDate), 'MMM d, yyyy') : 'N/A'}\n                  </TableCell>\n                  <TableCell>\n                    <span\n                      className={`px-2 py-1 rounded-full text-xs ${\n                        assignment.status === 'active'\n                          ? 'bg-green-100 text-green-800'\n                          : assignment.status === 'completed'\n                          ? 'bg-blue-100 text-blue-800'\n                          : 'bg-gray-100 text-gray-800'\n                      }`}\n                    >\n                      {assignment.status.charAt(0).toUpperCase() + assignment.status.slice(1)}\n                    </span>\n                  </TableCell>\n                  <TableCell>\n                    {assignment.status === 'active' && (\n                      <Button\n                        variant=\"outline\"\n                        size=\"sm\"\n                        className=\"mr-2\"\n                        onClick={() => updateAssignmentStatus(assignment._id, 'completed')}\n                      >\n                        Mark Complete\n                      </Button>\n                    )}\n                  </TableCell>\n                </TableRow>\n              ))\n            )}\n          </TableBody>\n        </Table>\n      </div>\n    </div>\n  );\n};\n\nexport default TraderManagement;\n", "import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n", "import * as React from \"react\"\n\nimport { cn } from \"../../lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": ["Table", "React", "_ref", "ref", "className", "props", "_jsx", "children", "cn", "displayName", "TableHeader", "_ref2", "TableBody", "_ref3", "_ref4", "TableRow", "_ref5", "TableHead", "_ref6", "TableCell", "_ref7", "_ref8", "Dialog", "DialogPrimitive", "DialogTrigger", "DialogPortal", "container", "forceMount", "restProps", "portalProps", "DialogOverlay", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_jsxs", "X", "DialogHeader", "<PERSON><PERSON><PERSON><PERSON>er", "DialogTitle", "DialogDescription", "labelVariants", "cva", "Label", "Skeleton", "Shield", "createLucideIcon", "d", "key", "<PERSON><PERSON><PERSON>", "generateCategoricalChart", "chartName", "GraphicalChild", "Bar", "defaultTooltipEventType", "validateTooltipEventTypes", "axisComponents", "axisType", "AxisComp", "XAxis", "YA<PERSON>s", "formatAxisMap", "UserCheck", "cx", "cy", "r", "points", "formatCurrency", "amount", "currency", "arguments", "length", "undefined", "Intl", "NumberFormat", "style", "minimumFractionDigits", "maximumFractionDigits", "format", "AdminDashboard", "user", "logout", "useAuth", "navigate", "useNavigate", "toast", "useToast", "isLoading", "setIsLoading", "useState", "stats", "setStats", "totalUsers", "totalTransactions", "totalRevenue", "activeMerchants", "recentTransactions", "setRecentTransactions", "revenueData", "setRevenueData", "statCards", "name", "value", "toLocaleString", "icon", "Users", "description", "change", "trend", "CreditCard", "DollarSign", "toString", "fetchDashboardData", "async", "Promise", "resolve", "setTimeout", "id", "Math", "floor", "random", "date", "Date", "toISOString", "merchant", "status", "now", "revenue", "error", "console", "title", "variant", "useEffect", "interval", "setInterval", "clearInterval", "<PERSON><PERSON>", "size", "onClick", "disabled", "RefreshCw", "LogOut", "Array", "fill", "map", "_", "i", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "stat", "Tabs", "defaultValue", "TabsList", "TabsTrigger", "toLocaleTimeString", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CardTitle", "CardDescription", "ResponsiveContainer", "width", "height", "data", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stroke", "dataKey", "tick", "axisLine", "tick<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "formatter", "labelFormatter", "label", "Legend", "radius", "txn", "dateString", "year", "month", "day", "hour", "minute", "char<PERSON>t", "toUpperCase", "slice", "colSpan", "TraderManagement", "DIALOG_NAME", "createDialogContext", "createDialogScope", "createContextScope", "Dialog<PERSON><PERSON>", "useDialogContext", "__scopeDialog", "open", "openProp", "defaultOpen", "onOpenChange", "modal", "triggerRef", "contentRef", "<PERSON><PERSON><PERSON>", "useControllableState", "prop", "defaultProp", "onChange", "caller", "jsx", "scope", "contentId", "useId", "titleId", "descriptionId", "onOpenToggle", "prevOpen", "TRIGGER_NAME", "forwardedRef", "triggerProps", "context", "composedTriggerRef", "useComposedRefs", "Primitive", "button", "type", "getState", "composeEventHandlers", "PORTAL_NAME", "PortalProvider", "usePortalContext", "child", "Presence", "present", "PortalPrimitive", "<PERSON><PERSON><PERSON><PERSON>", "OVERLAY_NAME", "portalContext", "overlayProps", "DialogOverlayImpl", "Slot", "createSlot", "RemoveScroll", "as", "allowPinchZoom", "shards", "div", "pointerEvents", "CONTENT_NAME", "contentProps", "DialogContentModal", "DialogContentNonModal", "composedRefs", "content", "current", "hideOthers", "DialogContentImpl", "trapFocus", "disableOutsidePointerEvents", "onCloseAutoFocus", "event", "preventDefault", "focus", "onPointerDownOutside", "originalEvent", "detail", "ctrlLeftClick", "ctrl<PERSON>ey", "onFocusOutside", "hasInteractedOutsideRef", "hasPointerDownOutsideRef", "defaultPrevented", "onInteractOutside", "target", "targetIsTrigger", "contains", "onOpenAutoFocus", "useFocusGuards", "jsxs", "Fragment", "FocusScope", "loop", "trapped", "onMountAutoFocus", "onUnmountAutoFocus", "Dismissa<PERSON><PERSON><PERSON><PERSON>", "role", "on<PERSON><PERSON><PERSON>", "TitleWarning", "DescriptionWarning", "TITLE_NAME", "titleProps", "h2", "DESCRIPTION_NAME", "descriptionProps", "p", "CLOSE_NAME", "DialogClose", "closeProps", "TITLE_WARNING_NAME", "WarningProvider", "useWarningContext", "createContext", "contentName", "<PERSON><PERSON><PERSON>", "docs<PERSON>lug", "titleWarningContext", "MESSAGE", "document", "getElementById", "describedById", "getAttribute", "warn", "Root", "<PERSON><PERSON>", "Portal", "Overlay", "Content", "Title", "Description", "Close", "Select", "SelectPrimitive", "SelectValue", "SelectTrigger", "ChevronDown", "SelectContent", "position", "SelectItem", "Check", "x", "y", "rx", "x1", "x2", "y1", "y2", "TabsPrimitive", "<PERSON><PERSON><PERSON>er", "count", "toastTimeouts", "Map", "addToRemoveQueue", "toastId", "has", "timeout", "delete", "dispatch", "set", "reducer", "state", "action", "toasts", "t", "for<PERSON>ach", "filter", "listeners", "memoryState", "listener", "Number", "MAX_SAFE_INTEGER", "dismiss", "update", "setState", "push", "index", "indexOf", "splice", "API_URL", "process", "token", "localStorage", "getItem", "assignments", "setAssignments", "traders", "setTraders", "merchants", "setMerchants", "loading", "setLoading", "isDialogOpen", "setIsDialogOpen", "formData", "setFormData", "merchantId", "traderId", "startDate", "endDate", "notes", "headers", "Authorization", "fetchData", "fetchUsers", "assignmentsRes", "all", "axios", "get", "tradersRes", "merchantsRes", "getUserName", "email", "getMerchantName", "userData", "businessName", "onSubmit", "e", "post", "htmlFor", "onValueChange", "required", "placeholder", "_id", "trader", "Input", "min", "assignment", "put", "updateAssignmentStatus", "buttonVariants", "variants", "default", "destructive", "outline", "secondary", "ghost", "link", "sm", "lg", "defaultVariants", "Comp"], "sourceRoot": ""}