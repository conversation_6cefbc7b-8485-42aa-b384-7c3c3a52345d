const mongoose = require('mongoose');

const ReserveActivitySchema = new mongoose.Schema(
  {
    // Reference to the reserve strategy
    strategy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'ReserveStrategy',
      required: true,
      index: true
    },
    
    // Type of activity (create, update, delete, status_change, rule_update, etc.)
    type: {
      type: String,
      required: true,
      enum: [
        'create',
        'update',
        'delete',
        'status_change',
        'rule_update',
        'rule_add',
        'rule_remove',
        'auto_replenish',
        'notification',
        'other'
      ]
    },
    
    // Human-readable description of the activity
    description: {
      type: String,
      required: true
    },
    
    // Current status of the activity (pending, approved, flagged, etc.)
    status: {
      type: String,
      required: true,
      enum: ['pending', 'approved', 'flagged', 'rejected'],
      default: 'pending',
      index: true
    },
    
    // User who performed the activity
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    
    // User who reviewed the activity (if any)
    reviewedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    
    // When the activity was reviewed
    reviewedAt: {
      type: Date
    },
    
    // Notes from the reviewer
    reviewNote: {
      type: String
    },
    
    // Additional details about the activity (stored as JSON)
    details: {
      type: mongoose.Schema.Types.Mixed,
      default: {}
    },
    
    // IP address of the user who performed the activity
    ipAddress: {
      type: String
    },
    
    // User agent information
    userAgent: {
      type: String
    },
    
    // Metadata for the activity
    metadata: {
      type: Map,
      of: String
    }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Indexes for common queries
ReserveActivitySchema.index({ createdAt: -1 });
ReserveActivitySchema.index({ strategy: 1, createdAt: -1 });
ReserveActivitySchema.index({ type: 1, status: 1 });
ReserveActivitySchema.index({ user: 1, createdAt: -1 });

// Virtual for activity age (time since creation)
ReserveActivitySchema.virtual('age').get(function() {
  return Date.now() - this.createdAt.getTime();
});

// Pre-save hook to add audit info
ReserveActivitySchema.pre('save', function(next) {
  // Set IP address and user agent if not already set
  if (this.isNew) {
    // This would typically be set from the request object in the controller
    // this.ipAddress = req.ip;
    // this.userAgent = req.get('User-Agent');
  }
  
  next();
});

// Static method to log a new activity
ReserveActivitySchema.statics.logActivity = async function(activityData) {
  try {
    const activity = new this(activityData);
    await activity.save();
    return activity;
  } catch (error) {
    console.error('Error logging activity:', error);
    throw error;
  }
};

// Method to approve an activity
ReserveActivitySchema.methods.approve = async function(userId, note = '') {
  if (this.status !== 'pending') {
    throw new Error('Only pending activities can be approved');
  }
  
  this.status = 'approved';
  this.reviewedBy = userId;
  this.reviewedAt = new Date();
  this.reviewNote = note;
  
  return this.save();
};

// Method to flag an activity for review
ReserveActivitySchema.methods.flag = async function(userId, note = '') {
  if (this.status !== 'pending') {
    throw new Error('Only pending activities can be flagged');
  }
  
  this.status = 'flagged';
  this.reviewedBy = userId;
  this.reviewedAt = new Date();
  this.reviewNote = note;
  
  return this.save();
};

// Method to get a summary of the activity
ReserveActivitySchema.methods.getSummary = function() {
  return {
    id: this._id,
    type: this.type,
    description: this.description,
    status: this.status,
    createdAt: this.createdAt,
    user: this.user,
    strategy: this.strategy
  };
};

// Create and export the model
const ReserveActivity = mongoose.model('ReserveActivity', ReserveActivitySchema);

module.exports = ReserveActivity;
