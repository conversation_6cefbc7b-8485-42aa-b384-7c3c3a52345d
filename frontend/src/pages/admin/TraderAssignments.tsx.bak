import React, { useState, useEffect, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { format } from 'date-fns';
import { Plus, Search, Filter, Edit, Trash2, User, Briefcase, Calendar as CalendarIcon, Clock } from 'lucide-react';

// UI Components
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Badge } from '../../components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '../../components/ui/dialog';
import { Label } from '../../components/ui/label';
import { Textarea } from '../../components/ui/textarea';
import { Checkbox } from '../../components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';

// Services
import { traderService } from '../../services/traderService';

// Hooks
import { useToast } from '../../components/ui/use-toast';

// Define ApiResponse type
interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

// Skeleton component for loading states
const Skeleton = ({ className = '' }: { className?: string }) => (
  <div className={`animate-pulse bg-gray-200 rounded ${className}`} />
);

// Helper function to safely access data from API responses
const getData = <T,>(response: ApiResponse<T> | T[] | undefined): T[] => {
  if (!response) return [];
  if (Array.isArray(response)) return response;
  return response.data ? (Array.isArray(response.data) ? response.data : [response.data]) : [];
};

// Define types for our data
type Trader = {
  _id: string;
  name: string;
  email: string;
  phone?: string;
  status: 'active' | 'inactive' | 'suspended';
  createdAt: string;
  updatedAt: string;
};

type Merchant = {
  _id: string;
  businessName: string;
  businessType: string;
  email: string;
  phone: string;
  status: 'active' | 'inactive' | 'suspended';
  createdAt: string;
  updatedAt: string;
};

type Assignment = {
  _id: string;
  trader: Trader | string;
  merchant: Merchant | string;
  assignmentType: 'permanent' | 'temporary' | 'project_based';
  startDate: string;
  endDate?: string;
  status: 'active' | 'pending' | 'completed' | 'inactive';
  collectionTarget?: {
    amount: number;
    period: 'daily' | 'weekly' | 'monthly' | 'quarterly';
  };
  permissions: string[];
  notes?: string;
  createdAt: string;
  updatedAt: string;
  traderName?: string;
  merchantName?: string;
};

type LocalAssignment = Omit<Assignment, '_id' | 'createdAt' | 'updatedAt'> & {
  _id?: string;
  createdAt?: string;
  updatedAt?: string;
};

// Using direct import for DollarSign since the icons module is having issues
const DollarSign = (props: React.SVGProps<SVGSVGElement>) => (
  <svg 
    xmlns="http://www.w3.org/2000/svg" 
    width="24" 
    height="24" 
    viewBox="0 0 24 24" 
    fill="none" 
    stroke="currentColor" 
    strokeWidth="2" 
    strokeLinecap="round" 
    strokeLinejoin="round" 
    className="h-4 w-4"
    {...props}
  >
    <line x1="12" y1="2" x2="12" y2="22"></line>
    <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
  </svg>
);

// Helper function to render status badges
const renderStatusBadge = (status: string) => {
  switch (status) {
    case 'active':
      return <Badge className="bg-green-100 text-green-800">Active</Badge>;
    case 'pending':
      return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
    case 'completed':
      return <Badge className="bg-blue-100 text-blue-800">Completed</Badge>;
    case 'inactive':
      return <Badge variant="outline">Inactive</Badge>;
    default:
      return <Badge variant="outline">{status}</Badge>;
  }
};

const TraderAssignments = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  // State
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | Assignment['status']>('all');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedAssignment, setSelectedAssignment] = useState<Assignment | null>(null);
  
  // Form state
  type FormData = {
    traderId: string;
    merchantId: string;
    assignmentType: 'permanent' | 'temporary' | 'project_based';
    startDate: string;
    endDate?: string;
    status: 'active' | 'pending' | 'completed' | 'inactive';
    collectionTarget?: {
      amount: number;
      period: 'daily' | 'weekly' | 'monthly' | 'quarterly';
    };
    permissions: string[];
    notes?: string;
  };
  
  // Form state
  const [formData, setFormData] = useState<FormData>({
    traderId: '',
    merchantId: '',
    assignmentType: 'temporary',
    startDate: new Date().toISOString().split('T')[0],
    status: 'pending',
    permissions: [],
    notes: '',
  });

  // Update form data when selected assignment changes
  useEffect(() => {
    if (selectedAssignment) {
      const traderId = selectedAssignment.trader && typeof selectedAssignment.trader === 'object' ? selectedAssignment.trader._id : '';
      const merchantId = selectedAssignment.merchant && typeof selectedAssignment.merchant === 'object' ? selectedAssignment.merchant._id : '';
      
      setFormData({
        traderId,
        merchantId,
        assignmentType: selectedAssignment.assignmentType,
        startDate: selectedAssignment.startDate,
        endDate: selectedAssignment.endDate || '',
        status: selectedAssignment.status,
        collectionTarget: selectedAssignment.collectionTarget,
        permissions: [...(selectedAssignment.permissions || [])],
        notes: selectedAssignment.notes || '',
      });
    } else {
      setFormData({
        traderId: '',
        merchantId: '',
        assignmentType: 'temporary',
        startDate: new Date().toISOString().split('T')[0],
        status: 'pending',
        permissions: [],
      });
    }
  }, [selectedAssignment]);

  // Fetch all trader assignments with proper typing
  const { data: assignmentsResponse, isLoading, error } = useQuery({
    queryKey: ['allTraderAssignments'],
    queryFn: () => traderService.getAllAssignments() as Promise<ApiResponse<Assignment[]>>,
  });
  
  const assignments = getData(assignmentsResponse);
  
  // Fetch traders and merchants for the form with proper typing
  const { data: tradersResponse } = useQuery({
    queryKey: ['traders'],
    queryFn: () => traderService.getTraders() as Promise<ApiResponse<Trader[]>>,
  });
  
  const traders = getData(tradersResponse);
  
  const { data: merchantsResponse } = useQuery({
    queryKey: ['merchants'],
    queryFn: () => traderService.getMerchants() as Promise<ApiResponse<Merchant[]>>,
  });
  
  const merchants = getData(merchantsResponse);
  
  // Handle form input changes
  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;
    
    setFormData(prev => {
      if (name.startsWith('collectionTarget.')) {
        const field = name.split('.')[1] as 'amount' | 'period';
        return {
          ...prev,
          collectionTarget: {
            ...(prev.collectionTarget || { amount: 0, period: 'monthly' }),
            [field]: type === 'number' ? parseFloat(value) || 0 : value
          }
        };
      }
      
      if (type === 'checkbox') {
        const { checked } = e.target as HTMLInputElement;
        if (name === 'permissions') {
          const permission = value;
          return {
            ...prev,
            permissions: checked
              ? [...prev.permissions, permission]
              : prev.permissions.filter(p => p !== permission)
          };
        }
        return { ...prev, [name]: checked };
      }
      
      if (type === 'number') {
        return { ...prev, [name]: parseFloat(value) || 0 };
      }
      
      return { ...prev, [name]: value };
    });
  }, []);
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const trader = traders.find((t: Trader) => t._id === formData.traderId);
      const merchant = merchants.find((m: Merchant) => m._id === formData.merchantId);
      
      if (!trader || !merchant) {
        throw new Error('Trader or merchant not found');
      }
      
      const assignmentData = {
        ...formData,
        trader: trader._id,
        merchant: merchant._id,
        traderName: trader.name,
        merchantName: merchant.businessName,
        // Ensure we don't send undefined values
        endDate: formData.endDate || undefined,
        notes: formData.notes || undefined,
        collectionTarget: formData.collectionTarget?.amount ? formData.collectionTarget : undefined
      };
      
      if (selectedAssignment) {
        await traderService.updateAssignment(selectedAssignment._id, assignmentData);
      } else {
        await traderService.createAssignment(assignmentData);
      }
      
      // Reset form and close dialog
      setFormData({
        traderId: '',
        merchantId: '',
        assignmentType: 'temporary',
        startDate: new Date().toISOString().split('T')[0],
        status: 'pending',
        permissions: [],
        notes: '',
        collectionTarget: undefined
      });
      
      // Show success message
      toast({
        title: 'Success',
        description: `Assignment ${selectedAssignment ? 'updated' : 'created'} successfully`,
      });
      
      // Close dialog and refresh data
      setIsDialogOpen(false);
      setSelectedAssignment(null);
      queryClient.invalidateQueries({ queryKey: ['allTraderAssignments'] });
    } catch (error) {
      console.error('Error saving assignment:', error);
      toast({
        title: 'Error',
        description: (error as Error).message || 'Failed to save assignment',
        variant: 'destructive',
      });
    }
  };

  // Filter assignments based on search and status
  const filteredAssignments = (Array.isArray(assignments) ? assignments : []).filter((assignment: Assignment) => {
    const traderName = assignment.trader && typeof assignment.trader === 'object' 
      ? assignment.trader.name 
      : typeof assignment.trader === 'string' 
        ? '' 
        : '';
    
    const merchantName = assignment.merchant && typeof assignment.merchant === 'object' 
      ? assignment.merchant.businessName 
      : typeof assignment.merchant === 'string' 
        ? '' 
        : '';
    
    const searchLower = searchTerm.toLowerCase();
    const matchesSearch = 
      (traderName || '').toLowerCase().includes(searchLower) ||
      (merchantName || '').toLowerCase().includes(searchLower);
    
    const matchesStatus = statusFilter === 'all' || assignment.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  // Group assignments by status
  const activeAssignments = filteredAssignments.filter((a) => a.status === 'active');
  const pendingAssignments = filteredAssignments.filter((a) => a.status === 'pending');
  const completedAssignments = filteredAssignments.filter((a) => a.status === 'completed');
  const inactiveAssignments = filteredAssignments.filter((a) => a.status === 'inactive');

  // Helper function to render status badges
  const renderStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">Active</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case 'completed':
        return <Badge className="bg-blue-100 text-blue-800">Completed</Badge>;
      case 'inactive':
        return <Badge variant="outline">Inactive</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    setFormData({
      traderId,
      merchantId,
      assignmentType: selectedAssignment.assignmentType,
      startDate: selectedAssignment.startDate,
      endDate: selectedAssignment.endDate || '',
      status: selectedAssignment.status,
      collectionTarget: selectedAssignment.collectionTarget,
      permissions: [...(selectedAssignment.permissions || [])],
      notes: selectedAssignment.notes || '',
    });
  } else {
    setFormData({
      traderId: '',
      merchantId: '',
      assignmentType: 'temporary',
      startDate: new Date().toISOString().split('T')[0],
      status: 'pending',
      permissions: [],
    });
  }
}, [selectedAssignment]);

// Fetch all trader assignments with proper typing
const { data: assignmentsResponse, isLoading, error } = useQuery({
  queryKey: ['allTraderAssignments'],
  queryFn: () => traderService.getAllAssignments() as Promise<ApiResponse<Assignment[]>>,
});
  
const assignments = getData(assignmentsResponse);
  
// Fetch traders and merchants for the form with proper typing
const { data: tradersResponse } = useQuery({
  queryKey: ['traders'],
  queryFn: () => traderService.getTraders() as Promise<ApiResponse<Trader[]>>,
});
  
const traders = getData(tradersResponse);
  
const { data: merchantsResponse } = useQuery({
  queryKey: ['merchants'],
  queryFn: () => traderService.getMerchants() as Promise<ApiResponse<Merchant[]>>,
});
  
const merchants = getData(merchantsResponse);
  
// Handle form input changes
const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
  const { name, value, type } = e.target as HTMLInputElement;
  
  setFormData(prev => {
    if (name.startsWith('collectionTarget.')) {
      const field = name.split('.')[1] as 'amount' | 'period';
      return {
        });
      }
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;
    
    setFormData((prev: FormData) => ({
      ...prev,
      [name]: type === 'number' ? Number(value) : value,
    }));
  };

  const handleNestedInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target as HTMLInputElement;
    const [parent, child] = name.split('.');
    
    if (parent === 'collectionTarget') {
      setFormData(prev => ({
        ...prev,
        collectionTarget: {
          ...(prev.collectionTarget || { amount: 0, period: 'monthly' }),
          [child]: child === 'amount' ? Number(value) : value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  const handlePermissionChange = (permission: string, isChecked: boolean) => {
    setFormData(prev => {
      const permissions = new Set(prev.permissions);
      if (isChecked) {
        permissions.add(permission);
      } else {
        permissions.delete(permission);
      }
      return { ...prev, permissions: Array.from(permissions) };
    });
  };

  const onSuccess = () => {
    queryClient.invalidateQueries({ queryKey: ['allTraderAssignments'] });
    toast({
      title: 'Success',
      description: `Assignment ${selectedAssignment ? 'updated' : 'created'} successfully`,
    });
    setIsDialogOpen(false);
    setSelectedAssignment(null);
  };

  const onError = (error: Error) => {
    toast({
      title: 'Error',
      description: error.message || 'Failed to save assignment',
      variant: 'destructive',
    });
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Skeleton className="h-10 w-48" />
          <Skeleton className="h-10 w-32" />
        </div>
        <Skeleton className="h-12 w-full" />
        {[1, 2, 3, 4, 5].map((i) => (
          <Skeleton key={i} className="h-16 w-full" />
        ))}
      </div>
    );
  }

  if (error) {
    return <div>Error loading assignments</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Trader Assignments</h1>
          <p className="text-muted-foreground">
            Manage trader assignments and collections
          </p>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => setSelectedAssignment(null)}>
              <Plus className="mr-2 h-4 w-4" /> New Assignment
            </Button>
          </DialogTrigger>
          <AssignmentForm 
            assignment={selectedAssignment} 
            onSuccess={onSuccess} 
            onError={onError} 
          />
        </Dialog>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div className="relative w-full md:w-96">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search traders or merchants..."
                className="w-full pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          <Tabs defaultValue="all">
            <TabsList className="mb-4">
              <TabsTrigger value="all">All ({filteredAssignments.length})</TabsTrigger>
              <TabsTrigger value="active">Active ({activeAssignments.length})</TabsTrigger>
              <TabsTrigger value="pending">Pending ({pendingAssignments.length})</TabsTrigger>
              <TabsTrigger value="completed">Completed ({completedAssignments.length})</TabsTrigger>
            </TabsList>
            
            <TabsContent value="all">
              <AssignmentTable 
                assignments={filteredAssignments} 
                onEdit={handleEditAssignment}
                onDelete={handleDeleteAssignment}
              />
            </TabsContent>
            <TabsContent value="active">
              <AssignmentTable 
                assignments={activeAssignments} 
                onEdit={handleEditAssignment}
                onDelete={handleDeleteAssignment}
              />
            </TabsContent>
            <TabsContent value="pending">
              <AssignmentTable 
                assignments={pendingAssignments} 
                onEdit={handleEditAssignment}
                onDelete={handleDeleteAssignment}
              />
            </TabsContent>
            <TabsContent value="completed">
              <AssignmentTable 
                assignments={completedAssignments} 
                onEdit={handleEditAssignment}
                onDelete={handleDeleteAssignment}
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}

function AssignmentTable({ assignments, onEdit, onDelete }: any) {
  if (assignments.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        <p>No assignments found</p>
      </div>
    );
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Trader</TableHead>
            <TableHead>Merchant</TableHead>
            <TableHead>Type</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Period</TableHead>
            <TableHead>Collection Target</TableHead>
            <TableHead className="w-[100px]">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {assignments.map((assignment: any) => (
            <TableRow key={assignment._id}>
              <TableCell className="font-medium">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <span>{assignment.trader?.name || 'N/A'}</span>
                </div>
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-2">
                  <Briefcase className="h-4 w-4 text-muted-foreground" />
                  <span>{assignment.merchant?.businessName || 'N/A'}</span>
                </div>
              </TableCell>
              <TableCell>
                <Badge variant="outline">{assignment.assignmentType}</Badge>
              </TableCell>
              <TableCell>{renderStatusBadge(assignment.status)}</TableCell>
              <TableCell>
                <div className="flex items-center gap-2">
                  <CalendarIcon className="h-4 w-4 text-muted-foreground" />
                  <span>
                    {format(new Date(assignment.startDate), 'MMM d, yyyy')} -{" "}
                    {assignment.endDate
                      ? format(new Date(assignment.endDate), 'MMM d, yyyy')
                      : 'Ongoing'}
                  </span>
                </div>
              </TableCell>
              <TableCell>
                {assignment.collectionTarget ? (
                  <div className="flex items-center gap-1">
                    <DollarSign className="h-4 w-4 text-green-500" />
                    <span>
                      ${assignment.collectionTarget.amount} / {assignment.collectionTarget.period}
                    </span>
                  </div>
                ) : (
                  'Not set'
                )}
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => onEdit(assignment)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => onDelete(assignment._id)}
                    className="text-red-500 hover:text-red-600"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}

interface AssignmentFormProps {
  assignment: Assignment | null;
  onSuccess: () => void;
  onError: (error: Error) => void;
}

const AssignmentForm = ({ assignment, onSuccess, onError }: AssignmentFormProps) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  const [formData, setFormData] = useState({
    traderId: assignment?.trader?._id || '',
    merchantId: assignment?.merchant?._id || '',
    assignmentType: assignment?.assignmentType || 'permanent',
    startDate: assignment?.startDate ? format(new Date(assignment.startDate), 'yyyy-MM-dd') : format(new Date(), 'yyyy-MM-dd'),
    endDate: assignment?.endDate ? format(new Date(assignment.endDate), 'yyyy-MM-dd') : '',
    collectionTarget: {
      amount: assignment?.collectionTarget?.amount || '',
      period: assignment?.collectionTarget?.period || 'monthly',
    },
    permissions: assignment?.permissions || [],
    notes: assignment?.notes || '',
  });

  const { data: traders = [] } = useQuery({
    queryKey: ['traders'],
    queryFn: () => traderService.getTraders(),
  });

  const { data: merchants = [] } = useQuery({
    queryKey: ['merchants'],
    queryFn: () => traderService.getMerchants(),
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;
    
    setFormData((prev: any) => ({
      ...prev,
      [name]: type === 'number' ? Number(value) : value,
    }));
  };

  const handleNestedInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target as HTMLInputElement;
    const [parent, child] = name.split('.');
    
    if (parent === 'collectionTarget') {
      setFormData(prev => ({
        ...prev,
        collectionTarget: {
          ...(prev.collectionTarget || { amount: 0, period: 'monthly' }),
          [child]: child === 'amount' ? Number(value) : value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  const handlePermissionChange = (permission: string, isChecked: boolean) => {
    setFormData(prev => {
      const permissions = new Set(prev.permissions);
      if (isChecked) {
        permissions.add(permission);
      } else {
        permissions.delete(permission);
      }
      return { ...prev, permissions: Array.from(permissions) };
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const assignmentData = {
        ...formData,
        trader: traders.find(t => t._id === formData.traderId),
        merchant: merchants.find(m => m._id === formData.merchantId)
      } as unknown as LocalAssignment;
      
      if (selectedAssignment) {
        await traderService.updateAssignment(selectedAssignment._id, assignmentData);
      } else {
        await traderService.createAssignment(assignmentData);
      }
      
      // Reset form and close dialog
      setFormData({
        traderId: '',
        merchantId: '',
        assignmentType: 'temporary',
        startDate: new Date().toISOString().split('T')[0],
        status: 'pending',
        permissions: [],
      });
      
      toast({
        title: 'Success',
        description: `Assignment ${selectedAssignment ? 'updated' : 'created'} successfully`,
      });
      
      setIsDialogOpen(false);
      setSelectedAssignment(null);
      queryClient.invalidateQueries({ queryKey: ['allTraderAssignments'] });
    } catch (error) {
      console.error('Error saving assignment:', error);
      toast({
        title: 'Error',
        description: (error as Error).message || 'Failed to save assignment',
        variant: 'destructive',
      });
    }
  };

  return (
    <DialogContent className="max-w-3xl">
      <DialogHeader>
        <DialogTitle>
          {assignment ? 'Edit Assignment' : 'Create New Assignment'}
        </DialogTitle>
      </DialogHeader>
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <label className="text-sm font-medium">Trader</label>
            <select
              name="traderId"
              value={formData.traderId}
              onChange={handleChange}
              className="w-full p-2 border rounded-md"
              required
            >
              <option value="">Select Trader</option>
              {traders.data?.map((trader: any) => (
                <option key={trader._id} value={trader._id}>
                  {trader.name} ({trader.email})
                </option>
              ))}
            </select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Merchant</label>
            <select
              name="merchantId"
              value={formData.merchantId}
              onChange={handleChange}
              className="w-full p-2 border rounded-md"
              required
            >
              <option value="">Select Merchant</option>
              {merchants.data?.map((merchant: any) => (
                <option key={merchant._id} value={merchant._id}>
                  {merchant.businessName}
                </option>
              ))}
            </select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Assignment Type</label>
            <select
              name="assignmentType"
              value={formData.assignmentType}
              onChange={handleChange}
              className="w-full p-2 border rounded-md"
            >
              <option value="permanent">Permanent</option>
              <option value="temporary">Temporary</option>
              <option value="project_based">Project Based</option>
            </select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Start Date</label>
            <input
              type="date"
              name="startDate"
              value={formData.startDate}
              onChange={handleChange}
              className="w-full p-2 border rounded-md"
              required
            />
          </div>

          {formData.assignmentType === 'temporary' && (
            <div className="space-y-2">
              <label className="text-sm font-medium">End Date</label>
              <input
                type="date"
                name="endDate"
                value={formData.endDate}
                onChange={handleChange}
                className="w-full p-2 border rounded-md"
                required={formData.assignmentType === 'temporary'}
                min={formData.startDate}
              />
            </div>
          )}

          <div className="space-y-2">
            <label className="text-sm font-medium">Collection Target Amount</label>
            <div className="flex gap-2">
              <span className="inline-flex items-center px-3 border rounded-l-md bg-gray-50 text-gray-500">
                $
              </span>
              <input
                type="number"
                name="collectionTarget.amount"
                value={formData.collectionTarget.amount}
                onChange={handleChange}
                className="flex-1 p-2 border rounded-r-md"
                min="0"
                step="0.01"
                placeholder="0.00"
              />
            </div>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Collection Period</label>
            <select
              name="collectionTarget.period"
              value={formData.collectionTarget.period}
              onChange={handleChange}
              className="w-full p-2 border rounded-md"
            >
              <option value="daily">Daily</option>
              <option value="weekly">Weekly</option>
              <option value="monthly">Monthly</option>
              <option value="quarterly">Quarterly</option>
            </select>
          </div>
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Permissions</label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {['view_transactions', 'process_payments', 'generate_reports', 'contact_customers'].map((permission) => (
              <label key={permission} className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={formData.permissions.includes(permission)}
                  onChange={(e) => handlePermissionChange(permission, e.target.checked)}
                  className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                />
                <span className="text-sm">
                  {permission.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                </span>
              </label>
            ))}
          </div>
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Notes</label>
          <textarea
            name="notes"
            value={formData.notes}
            onChange={handleChange}
            rows={3}
            className="w-full p-2 border rounded-md"
            placeholder="Additional notes about this assignment..."
          />
        </div>

        <div className="flex justify-end gap-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => document.getElementById('assignment-dialog-close')?.click()}
          >
            Cancel
          </Button>
          <Button type="submit">
            {assignment ? 'Update Assignment' : 'Create Assignment'}
          </Button>
        </div>
      </form>
    </DialogContent>
  );
}

export default TraderAssignments;
