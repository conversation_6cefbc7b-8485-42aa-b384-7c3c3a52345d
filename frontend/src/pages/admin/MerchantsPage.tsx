import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '../../components/ui/dropdown-menu';
import { Badge } from '../../components/ui/badge';
import { useToast } from '../../components/ui/use-toast';
import {
  MoreHorizontal,
  Search,
  Loader2,
  Plus,
  Filter,
  Users,
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  Eye,
  RefreshCw
} from 'lucide-react';
import MerchantForm from '../../components/admin/MerchantForm';
import ConfirmDialog from '../../components/admin/ConfirmDialog';
import {
  merchantAdminService,
  Merchant,
  MerchantFormData
} from '../../services/merchantAdminService';

export default function MerchantsPage() {
  const { toast } = useToast();

  // State management
  const [merchants, setMerchants] = useState<Merchant[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalMerchants, setTotalMerchants] = useState(0);

  // Modal states
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isApproveDialogOpen, setIsApproveDialogOpen] = useState(false);
  const [isRejectDialogOpen, setIsRejectDialogOpen] = useState(false);
  const [selectedMerchant, setSelectedMerchant] = useState<Merchant | null>(null);

  // Loading states
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isApproving, setIsApproving] = useState(false);

  // Fetch merchants data
  const fetchMerchants = async () => {
    try {
      setLoading(true);
      const response = await merchantAdminService.getMerchants({
        page: currentPage,
        limit: 10,
        status: statusFilter === 'all' ? undefined : statusFilter,
        search: searchTerm,
      });

      setMerchants(response.data);
      setTotalPages(response.totalPages);
      setTotalMerchants(response.total);
    } catch (error) {
      console.error('Error fetching merchants:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch merchants. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMerchants();
  }, [currentPage, statusFilter]);

  // Handle search with debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      if (currentPage === 1) {
        fetchMerchants();
      } else {
        setCurrentPage(1);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // CRUD Operations
  const handleCreateMerchant = async (formData: MerchantFormData) => {
    try {
      setIsSubmitting(true);
      await merchantAdminService.createMerchant(formData);
      toast({
        title: 'Success',
        description: 'Merchant created successfully',
      });
      fetchMerchants();
      setIsFormOpen(false);
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to create merchant',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUpdateMerchant = async (formData: MerchantFormData) => {
    if (!selectedMerchant) return;

    try {
      setIsSubmitting(true);
      await merchantAdminService.updateMerchant(selectedMerchant._id, formData);
      toast({
        title: 'Success',
        description: 'Merchant updated successfully',
      });
      fetchMerchants();
      setIsFormOpen(false);
      setSelectedMerchant(null);
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update merchant',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteMerchant = async () => {
    if (!selectedMerchant) return;

    try {
      setIsDeleting(true);
      await merchantAdminService.deleteMerchant(selectedMerchant._id);
      toast({
        title: 'Success',
        description: 'Merchant deleted successfully',
      });
      fetchMerchants();
      setIsDeleteDialogOpen(false);
      setSelectedMerchant(null);
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete merchant',
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const handleApproveMerchant = async () => {
    if (!selectedMerchant) return;

    try {
      setIsApproving(true);
      await merchantAdminService.approveMerchant(selectedMerchant._id);
      toast({
        title: 'Success',
        description: 'Merchant approved successfully',
      });
      fetchMerchants();
      setIsApproveDialogOpen(false);
      setSelectedMerchant(null);
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to approve merchant',
        variant: 'destructive',
      });
    } finally {
      setIsApproving(false);
    }
  };

  const handleRejectMerchant = async () => {
    if (!selectedMerchant) return;

    try {
      setIsApproving(true);
      await merchantAdminService.rejectMerchant(selectedMerchant._id, 'Rejected by admin');
      toast({
        title: 'Success',
        description: 'Merchant rejected successfully',
      });
      fetchMerchants();
      setIsRejectDialogOpen(false);
      setSelectedMerchant(null);
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to reject merchant',
        variant: 'destructive',
      });
    } finally {
      setIsApproving(false);
    }
  };

  // Utility functions
  const openEditForm = (merchant: Merchant) => {
    setSelectedMerchant(merchant);
    setIsFormOpen(true);
  };

  const openDeleteDialog = (merchant: Merchant) => {
    setSelectedMerchant(merchant);
    setIsDeleteDialogOpen(true);
  };

  const openApproveDialog = (merchant: Merchant) => {
    setSelectedMerchant(merchant);
    setIsApproveDialogOpen(true);
  };

  const openRejectDialog = (merchant: Merchant) => {
    setSelectedMerchant(merchant);
    setIsRejectDialogOpen(true);
  };

  const getStatusBadge = (merchant: Merchant) => {
    if (merchant.verificationStatus === 'verified') {
      return <Badge variant="default" className="bg-green-100 text-green-800">Verified</Badge>;
    } else if (merchant.verificationStatus === 'rejected') {
      return <Badge variant="destructive">Rejected</Badge>;
    } else {
      return <Badge variant="secondary">Pending</Badge>;
    }
  };

  const getActiveBadge = (isActive: boolean) => {
    return isActive
      ? <Badge variant="default">Active</Badge>
      : <Badge variant="outline">Inactive</Badge>;
  };

  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading merchants...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">Merchants Management</h1>
            <p className="text-muted-foreground">
              Manage and monitor merchant accounts
            </p>
          </div>
          <div className="flex gap-2">
            <Button onClick={() => setIsFormOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Merchant
            </Button>
            <Button variant="outline" onClick={fetchMerchants}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Merchants</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalMerchants}</div>
              <p className="text-xs text-muted-foreground">
                Total merchant accounts
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active</CardTitle>
              <Badge variant="default" className="text-xs">Live</Badge>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {merchants.filter(m => m.status === 'active').length}
              </div>
              <p className="text-xs text-muted-foreground">
                Currently active
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Verified</CardTitle>
              <Badge variant="default" className="text-xs">✓</Badge>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {merchants.filter(m => m.verificationStatus === 'verified').length}
              </div>
              <p className="text-xs text-muted-foreground">
                Verified accounts
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending</CardTitle>
              <Badge variant="secondary" className="text-xs">Review</Badge>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {merchants.filter(m => m.verificationStatus === 'pending').length}
              </div>
              <p className="text-xs text-muted-foreground">
                Awaiting approval
              </p>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Merchants List</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2 mb-4">
              <div className="relative flex-1">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search merchants..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
              <Button variant="outline">
                <Filter className="mr-2 h-4 w-4" />
                Filter
              </Button>
            </div>

            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Business</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Verified</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8">
                        <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                        <p className="mt-2">Loading merchants...</p>
                      </TableCell>
                    </TableRow>
                  ) : merchants.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8">
                        No merchants found
                      </TableCell>
                    </TableRow>
                  ) : (
                    merchants.map((merchant) => (
                      <TableRow key={merchant._id}>
                        <TableCell className="font-medium">{merchant.name}</TableCell>
                        <TableCell>{merchant.email}</TableCell>
                        <TableCell>{merchant.businessName || 'N/A'}</TableCell>
                        <TableCell>{merchant.businessType || 'N/A'}</TableCell>
                        <TableCell>{getActiveBadge(merchant.isActive)}</TableCell>
                        <TableCell>{getStatusBadge(merchant)}</TableCell>
                        <TableCell>
                          {new Date(merchant.createdAt).toLocaleDateString()}
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => openEditForm(merchant)}>
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => openEditForm(merchant)}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              {merchant.verificationStatus === 'pending' && (
                                <>
                                  <DropdownMenuItem onClick={() => openApproveDialog(merchant)}>
                                    <CheckCircle className="mr-2 h-4 w-4" />
                                    Approve
                                  </DropdownMenuItem>
                                  <DropdownMenuItem onClick={() => openRejectDialog(merchant)}>
                                    <XCircle className="mr-2 h-4 w-4" />
                                    Reject
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                </>
                              )}
                              <DropdownMenuItem
                                onClick={() => openDeleteDialog(merchant)}
                                className="text-red-600"
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between">
            <p className="text-sm text-muted-foreground">
              Showing {merchants.length} of {totalMerchants} merchants
            </p>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
              >
                Previous
              </Button>
              <span className="text-sm">
                Page {currentPage} of {totalPages}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Modals */}
      <MerchantForm
        isOpen={isFormOpen}
        onClose={() => {
          setIsFormOpen(false);
          setSelectedMerchant(null);
        }}
        onSubmit={selectedMerchant ? handleUpdateMerchant : handleCreateMerchant}
        merchant={selectedMerchant}
        isLoading={isSubmitting}
      />

      <ConfirmDialog
        isOpen={isDeleteDialogOpen}
        onClose={() => {
          setIsDeleteDialogOpen(false);
          setSelectedMerchant(null);
        }}
        onConfirm={handleDeleteMerchant}
        title="Delete Merchant"
        description={`Are you sure you want to delete ${selectedMerchant?.name}? This action cannot be undone.`}
        confirmText="Delete"
        variant="danger"
        isLoading={isDeleting}
      />

      <ConfirmDialog
        isOpen={isApproveDialogOpen}
        onClose={() => {
          setIsApproveDialogOpen(false);
          setSelectedMerchant(null);
        }}
        onConfirm={handleApproveMerchant}
        title="Approve Merchant"
        description={`Are you sure you want to approve ${selectedMerchant?.name}? They will be able to start processing payments.`}
        confirmText="Approve"
        variant="success"
        isLoading={isApproving}
      />

      <ConfirmDialog
        isOpen={isRejectDialogOpen}
        onClose={() => {
          setIsRejectDialogOpen(false);
          setSelectedMerchant(null);
        }}
        onConfirm={handleRejectMerchant}
        title="Reject Merchant"
        description={`Are you sure you want to reject ${selectedMerchant?.name}? They will need to reapply for verification.`}
        confirmText="Reject"
        variant="warning"
        isLoading={isApproving}
      />
    </div>
  );
}