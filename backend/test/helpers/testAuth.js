const request = require('supertest');
const mongoose = require('mongoose');
const User = require('../../models/User');
const bcrypt = require('bcryptjs');

/**
 * Create a test user and log them in
 * @param {Object} app - Express app instance
 * @param {Object} userData - User data (optional)
 * @returns {Promise<Object>} - Object containing user data and authenticated agent
 */
const createAndLoginUser = async (app, userData = {}) => {
  const agent = request.agent(app);
  
  // Create test user
  const password = userData.password || 'test1234';
  const hashedPassword = await bcrypt.hash(password, 10);
  
  const user = await User.create({
    name: userData.name || 'Test User',
    email: userData.email || '<EMAIL>',
    password: hashedPassword,
    role: userData.role || 'merchant',
    isActive: userData.isActive !== undefined ? userData.isActive : true,
    isVerified: userData.isVerified !== undefined ? userData.isVerified : true
  });
  
  // Log in the user
  await agent
    .post('/api/auth/login')
    .send({
      email: user.email,
      password: password
    });
  
  return { user, agent };
};

/**
 * Create an admin user and log them in
 * @param {Object} app - Express app instance
 * @returns {Promise<Object>} - Object containing admin user data and authenticated agent
 */
const loginAsAdmin = async (app) => {
  return createAndLoginUser(app, {
    name: 'Admin User',
    email: '<EMAIL>',
    role: 'admin'
  });
};

module.exports = {
  createAndLoginUser,
  loginAsAdmin
};
