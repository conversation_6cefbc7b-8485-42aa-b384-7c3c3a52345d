import React, { useState, useEffect } from "react"
import { useAuth } from "../contexts/AuthContext"
import { usePayment } from "../contexts/PaymentContext"
import { useNavigate } from "react-router-dom"
import axios from "axios"
import { format } from "date-fns"

// UI Components
import { But<PERSON> } from "../components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "../components/ui/card"
import { Input } from "../components/ui/input"
import { Label } from "../components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "../components/ui/table"
import { Badge } from "../components/ui/badge"
import { Skeleton } from "../components/ui/skeleton"
import { useToast } from "../components/ui/use-toast"

// Icons
import { 
  DollarSign, 
  CreditCard, 
  TrendingUp, 
  Users, 
  Copy, 
  RefreshCw, 
  CheckCircle, 
  Clock, 
  AlertCircle,
  ArrowUpRight,
  ArrowDownRight,
  ExternalLink,
  Settings,
  Key,
  Globe
} from "lucide-react"

// Charts
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, Legend } from "recharts"

// Types
interface DashboardStats {
  totalRevenue: number;
  totalTransactions: number;
  successRate: number;
  pendingPayouts: number;
  monthlyRevenue: { month: string; revenue: number }[];
  recentTransactions: Transaction[];
}

interface Transaction {
  id: string;
  amount: number;
  currency: string;
  status: 'completed' | 'pending' | 'failed';
  description: string;
  createdAt: string;
  customerEmail?: string;
  paymentMethod?: string;
}

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api/v1';

// Custom Tabs component with TypeScript types
const TabContext = React.createContext<{
  activeTab: string;
  setActiveTab: (value: string) => void;
} | null>(null);

const Tabs: React.FC<{ 
  value: string;
  onValueChange: (value: string) => void;
  children: React.ReactNode;
  className?: string;
}> = ({ value, onValueChange, children, className = '' }) => {
  return (
    <TabContext.Provider value={{ activeTab: value, setActiveTab: onValueChange }}>
      <div className={className}>{children}</div>
    </TabContext.Provider>
  );
};

const TabsList: React.FC<{ 
  children: React.ReactNode;
  className?: string;
}> = ({ children, className = '' }) => {
  return (
    <div className={`flex space-x-2 ${className}`}>
      {children}
    </div>
  );
};

const TabsTrigger: React.FC<{ 
  value: string;
  children: React.ReactNode;
  className?: string;
}> = ({ value, children, className = '' }) => {
  const context = React.useContext(TabContext);
  if (!context) {
    throw new Error('TabsTrigger must be used within a Tabs component');
  }
  
  const { activeTab, setActiveTab } = context;
  const isActive = activeTab === value;
  
  return (
    <button
      type="button"
      className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
        isActive
          ? 'bg-primary text-primary-foreground'
          : 'text-muted-foreground hover:text-foreground'
      } ${className}`}
      onClick={() => setActiveTab(value)}
    >
      {children}
    </button>
  );
};

const TabsContent: React.FC<{ 
  value: string;
  children: React.ReactNode;
  className?: string;
}> = ({ value, children, className = '' }) => {
  const context = React.useContext(TabContext);
  if (!context) {
    throw new Error('TabsContent must be used within a Tabs component');
  }
  
  const { activeTab } = context;
  
  if (activeTab !== value) {
    return null;
  }
  
  return (
    <div className={`mt-4 ${className}`}>
      {children}
    </div>
  );
};

const MerchantDashboard: React.FC = () => {
  // Hooks and state
  const { user: currentUser } = useAuth();
  const { processPayment } = usePayment();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  // State for dashboard data
  const [stats, setStats] = useState<DashboardStats>({
    totalRevenue: 0,
    totalTransactions: 0,
    successRate: 0,
    pendingPayouts: 0,
    monthlyRevenue: [],
    recentTransactions: []
  });
  
  const [apiKey, setApiKey] = useState("");
  const [webhookUrl, setWebhookUrl] = useState("");
  const [isGeneratingKey, setIsGeneratingKey] = useState(false);
  const [isUpdatingWebhook, setIsUpdatingWebhook] = useState(false);
  const [newWebhookUrl, setNewWebhookUrl] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("overview");

  // Format currency
  const formatCurrency = (amount: number, currency: string = 'USD'): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string): string => {
    return format(new Date(dateString), 'MMM dd, yyyy HH:mm');
  };

  // Chart data for the revenue overview
  const chartData = [
    { name: "Jan", revenue: 4000, transactions: 240 },
    { name: "Feb", revenue: 3000, transactions: 139 },
    { name: "Mar", revenue: 2000, transactions: 980 },
    { name: "Apr", revenue: 2780, transactions: 390 },
    { name: "May", revenue: 1890, transactions: 480 },
    { name: "Jun", revenue: 2390, transactions: 380 },
  ];

  // Generate API key function
  const generateApiKey = async () => {
    try {
      setIsGeneratingKey(true);
      // In a real app, this would call your API
      const newApiKey = `sk_test_${Math.random().toString(36).substring(2, 27)}`;
      setApiKey(newApiKey);
      toast({
        title: "API Key Generated",
        description: "Your new API key has been generated successfully.",
      });
    } catch (error) {
      console.error("Error generating API key:", error);
      toast({
        title: "Error",
        description: "Failed to generate API key. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingKey(false);
    }
  };

  // Update webhook URL
  const updateWebhookUrl = async () => {
    if (!newWebhookUrl) return;
    
    try {
      setIsUpdatingWebhook(true);
      // In a real app, this would call your API
      // await axios.put(`${API_URL}/merchant/webhook`, { url: newWebhookUrl });
      
      setWebhookUrl(newWebhookUrl);
      toast({
        title: 'Success',
        description: 'Webhook URL updated successfully.',
      });
    } catch (error) {
      console.error('Error updating webhook URL:', error);
      toast({
        title: 'Error',
        description: 'Failed to update webhook URL. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsUpdatingWebhook(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setIsLoading(true);
        // In a real app, this would call your API
        // const response = await axios.get(`${API_URL}/dashboard`);
        // setStats(response.data);
        
        // Mock data for now
        setStats({
          totalRevenue: 12500,
          totalTransactions: 342,
          successRate: 98.5,
          pendingPayouts: 4500,
          monthlyRevenue: [
            { month: 'Jan', revenue: 3200 },
            { month: 'Feb', revenue: 2800 },
            { month: 'Mar', revenue: 4100 },
            { month: 'Apr', revenue: 3800 },
            { month: 'May', revenue: 5200 },
            { month: 'Jun', revenue: 4800 },
          ],
          recentTransactions: [
            {
              id: 'txn_' + Math.random().toString(36).substr(2, 9),
              amount: 125.99,
              currency: 'USD',
              status: 'completed',
              description: 'Premium Subscription',
              customerEmail: '<EMAIL>',
              paymentMethod: 'visa',
              createdAt: new Date().toISOString()
            },
          ]
        });
      } catch (error) {
        console.error("Error fetching dashboard data:", error);
        toast({
          title: "Error",
          description: "Failed to load dashboard data. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  // Initialize component with data
  useEffect(() => {
    setApiKey('sk_test_' + Math.random().toString(36).substr(2, 43));
    setWebhookUrl('https://api.yourdomain.com/webhooks/payment');
    setNewWebhookUrl('https://api.yourdomain.com/webhooks/payment');
  }, []);

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">Merchant Dashboard</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.totalRevenue)}</div>
            <p className="text-xs text-muted-foreground">+20.1% from last month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Transactions</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalTransactions}</div>
            <p className="text-xs text-muted-foreground">+19% from last month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.successRate}%</div>
            <p className="text-xs text-muted-foreground">+2% from last month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Payouts</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.pendingPayouts)}</div>
            <p className="text-xs text-muted-foreground">Will be processed in 2 days</p>
          </CardContent>
        </Card>
      </div>
      
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="transactions">Transactions</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Revenue Overview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={chartData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Line type="monotone" dataKey="revenue" stroke="#8884d8" />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Recent Transactions</CardTitle>
                <CardDescription>You had {stats.recentTransactions.length} transactions this month.</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {stats.recentTransactions.length > 0 ? (
                    stats.recentTransactions.slice(0, 5).map((transaction) => (
                      <div key={transaction.id} className="flex items-center justify-between">
                        <div className="space-y-1">
                          <p className="text-sm font-medium leading-none">
                            {transaction.description}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {formatDate(transaction.createdAt)}
                          </p>
                        </div>
                        <div className="flex items-center">
                          <span className="font-medium">
                            {formatCurrency(transaction.amount, transaction.currency)}
                          </span>
                          {transaction.status === 'completed' ? (
                            <CheckCircle className="h-4 w-4 text-green-500 ml-2" />
                          ) : transaction.status === 'pending' ? (
                            <Clock className="h-4 w-4 text-yellow-500 ml-2" />
                          ) : (
                            <AlertCircle className="h-4 w-4 text-red-500 ml-2" />
                          )}
                        </div>
                      </div>
                    ))
                  ) : (
                    <p className="text-sm text-muted-foreground">No recent transactions</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="transactions">
          <Card>
            <CardHeader>
              <CardTitle>Transaction History</CardTitle>
              <CardDescription>View and manage your transaction history</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>ID</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {stats.recentTransactions && stats.recentTransactions.length > 0 ? (
                    stats.recentTransactions.map((transaction) => (
                      <TableRow key={transaction.id}>
                        <TableCell className="font-medium">{transaction.id.substring(0, 8)}...</TableCell>
                        <TableCell>{transaction.description}</TableCell>
                        <TableCell>{formatDate(transaction.createdAt)}</TableCell>
                        <TableCell>{formatCurrency(transaction.amount, transaction.currency)}</TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              transaction.status === 'completed'
                                ? 'default'
                                : transaction.status === 'pending'
                                ? 'secondary'
                                : 'destructive'
                            }
                          >
                            {transaction.status}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-4 text-muted-foreground">
                        No transactions found
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="settings">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Key className="h-5 w-5 mr-2" />
                  API Keys
                </CardTitle>
                <CardDescription>Manage your API keys for integration</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="api-key">Your API Key</Label>
                  <div className="flex space-x-2">
                    <Input
                      id="api-key"
                      value={apiKey}
                      readOnly
                      placeholder="Generate an API key to get started"
                    />
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => {
                        navigator.clipboard.writeText(apiKey);
                        toast({
                          title: 'Copied!',
                          description: 'API key copied to clipboard.',
                        });
                      }}
                      disabled={!apiKey}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <Button
                  onClick={async () => {
                    try {
                      setIsGeneratingKey(true);
                      // Generate API key logic here
                      const newKey = `sk_test_${Math.random().toString(36).substring(2, 15)}`;
                      setApiKey(newKey);
                      toast({
                        title: 'Success',
                        description: 'New API key generated successfully.',
                      });
                    } catch (error) {
                      console.error('Error generating API key:', error);
                      toast({
                        title: 'Error',
                        description: 'Failed to generate API key. Please try again.',
                        variant: 'destructive',
                      });
                    } finally {
                      setIsGeneratingKey(false);
                    }
                  }}
                  disabled={isGeneratingKey}
                >
                  {isGeneratingKey ? (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    'Generate New API Key'
                  )}
                </Button>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Globe className="h-5 w-5 mr-2" />
                  Webhook Settings
                </CardTitle>
                <CardDescription>Configure your webhook endpoint</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="webhook-url">Webhook URL</Label>
                  <div className="flex space-x-2">
                    <Input
                      id="webhook-url"
                      value={newWebhookUrl || webhookUrl}
                      onChange={(e) => setNewWebhookUrl(e.target.value)}
                      placeholder="https://example.com/webhook"
                    />
                    <Button
                      onClick={async () => {
                        try {
                          setIsUpdatingWebhook(true);
                          // Save webhook URL logic here
                          setWebhookUrl(newWebhookUrl);
                          toast({
                            title: 'Success',
                            description: 'Webhook URL updated successfully.',
                          });
                        } catch (error) {
                          console.error('Error updating webhook URL:', error);
                          toast({
                            title: 'Error',
                            description: 'Failed to update webhook URL. Please try again.',
                            variant: 'destructive',
                          });
                        } finally {
                          setIsUpdatingWebhook(false);
                        }
                      }}
                      disabled={isUpdatingWebhook || !newWebhookUrl}
                    >
                      {isUpdatingWebhook ? (
                        <>
                          <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        'Save'
                      )}
                    </Button>
                  </div>
                </div>
                <div className="text-sm text-muted-foreground">
                  <p>We'll send POST requests to this URL for the following events:</p>
                  <ul className="list-disc pl-5 mt-2 space-y-1">
                    <li>payment.succeeded</li>
                    <li>payment.failed</li>
                    <li>payment.refunded</li>
                    <li>subscription.created</li>
                    <li>subscription.updated</li>
                    <li>subscription.cancelled</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default MerchantDashboard;
