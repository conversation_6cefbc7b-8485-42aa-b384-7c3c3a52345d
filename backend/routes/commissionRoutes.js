const express = require('express');
const router = express.Router();
const commissionController = require('../controllers/commissionController');
// Authentication middleware removed

/**
 * @swagger
 * /api/commissions:
 *   post:
 *     tags: [Commissions]
 *     summary: Create a new commission structure
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Commission'
 *     responses:
 *       201:
 *         description: Commission structure created successfully
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - requires admin or finance role
 */
router.post('/', commissionController.createCommissionStructure);

/**
 * @swagger
 * /api/commissions:
 *   get:
 *     tags: [Commissions]
 *     summary: Get all commission structures
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of commission structures
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Commission'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - requires admin or finance role
 */
router.get('/', commissionController.getCommissionStructures);

/**
 * @swagger
 * /api/commissions/{id}:
 *   get:
 *     tags: [Commissions]
 *     summary: Get a single commission structure
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Commission structure ID
 *     responses:
 *       200:
 *         description: Commission structure data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Commission'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - requires admin or finance role
 *       404:
 *         description: Commission structure not found
 */
router.get('/:id', commissionController.getCommissionStructure);

/**
 * @swagger
 * /api/commissions/{id}:
 *   put:
 *     tags: [Commissions]
 *     summary: Update a commission structure
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Commission structure ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Commission'
 *     responses:
 *       200:
 *         description: Commission structure updated successfully
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - requires admin or finance role
 *       404:
 *         description: Commission structure not found
 */
router.put('/:id', commissionController.updateCommissionStructure);

/**
 * @swagger
 * /api/commissions/{id}:
 *   delete:
 *     tags: [Commissions]
 *     summary: Delete a commission structure
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Commission structure ID
 *     responses:
 *       200:
 *         description: Commission structure deleted successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - requires admin or finance role
 *       404:
 *         description: Commission structure not found
 */
router.delete('/:id', commissionController.deleteCommissionStructure);

/**
 * @swagger
 * /api/commissions/calculate:
 *   post:
 *     tags: [Commissions]
 *     summary: Calculate commission for a transaction
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [amount, currency, transactionType]
 *             properties:
 *               amount:
 *                 type: number
 *                 description: Transaction amount
 *               currency:
 *                 type: string
 *                 description: Transaction currency code (e.g., USD, EUR)
 *               transactionType:
 *                 type: string
 *                 description: Type of transaction (e.g., 'card_payment', 'bank_transfer')
 *     responses:
 *       200:
 *         description: Commission calculated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 amount:
 *                   type: number
 *                   description: Original transaction amount
 *                 commission:
 *                   type: number
 *                   description: Calculated commission amount
 *                 total:
 *                   type: number
 *                   description: Total amount (original + commission)
 *                 currency:
 *                   type: string
 *                   description: Transaction currency
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - requires admin or finance role
 */
router.post('/calculate', commissionController.calculateCommission);

module.exports = router;
