import React, { useState } from 'react';
import { But<PERSON> } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Save, 
  X, 
  Calendar,
  Type,
  Hash,
  Mail,
  Phone,
  Link,
  ToggleLeft,
  List
} from 'lucide-react';
import { CustomField } from '../../services/merchantAdminService';

interface CustomFieldsManagerProps {
  customFields: CustomField[];
  onAddField: (field: Omit<CustomField, 'addedBy' | 'addedAt'>) => Promise<void>;
  onUpdateField: (fieldName: string, fieldValue: any, changeReason?: string) => Promise<void>;
  onDeleteField: (fieldName: string, changeReason?: string) => Promise<void>;
  isLoading?: boolean;
}

const fieldTypes = [
  { value: 'text', label: 'Text', icon: Type },
  { value: 'number', label: 'Number', icon: Hash },
  { value: 'email', label: 'Email', icon: Mail },
  { value: 'phone', label: 'Phone', icon: Phone },
  { value: 'url', label: 'URL', icon: Link },
  { value: 'date', label: 'Date', icon: Calendar },
  { value: 'boolean', label: 'Yes/No', icon: ToggleLeft },
  { value: 'select', label: 'Select', icon: List },
];

const CustomFieldsManager: React.FC<CustomFieldsManagerProps> = ({
  customFields,
  onAddField,
  onUpdateField,
  onDeleteField,
  isLoading = false,
}) => {
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [editingField, setEditingField] = useState<string | null>(null);
  const [editValue, setEditValue] = useState<any>('');
  const [changeReason, setChangeReason] = useState('');

  // New field form state
  const [newField, setNewField] = useState<Omit<CustomField, 'addedBy' | 'addedAt'>>({
    fieldName: '',
    fieldType: 'text',
    fieldValue: '',
    isRequired: false,
    options: [],
  });

  const handleAddField = async () => {
    try {
      await onAddField(newField);
      setIsAddDialogOpen(false);
      setNewField({
        fieldName: '',
        fieldType: 'text',
        fieldValue: '',
        isRequired: false,
        options: [],
      });
    } catch (error) {
      console.error('Error adding field:', error);
    }
  };

  const handleUpdateField = async (fieldName: string) => {
    try {
      await onUpdateField(fieldName, editValue, changeReason);
      setEditingField(null);
      setEditValue('');
      setChangeReason('');
    } catch (error) {
      console.error('Error updating field:', error);
    }
  };

  const handleDeleteField = async (fieldName: string) => {
    if (window.confirm(`Are you sure you want to delete the field "${fieldName}"?`)) {
      try {
        await onDeleteField(fieldName, changeReason);
      } catch (error) {
        console.error('Error deleting field:', error);
      }
    }
  };

  const startEditing = (field: CustomField) => {
    setEditingField(field.fieldName);
    setEditValue(field.fieldValue);
  };

  const cancelEditing = () => {
    setEditingField(null);
    setEditValue('');
    setChangeReason('');
  };

  const getFieldIcon = (fieldType: string) => {
    const fieldTypeConfig = fieldTypes.find(ft => ft.value === fieldType);
    const IconComponent = fieldTypeConfig?.icon || Type;
    return <IconComponent className="h-4 w-4" />;
  };

  const renderFieldValue = (field: CustomField) => {
    if (editingField === field.fieldName) {
      return (
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            {field.fieldType === 'boolean' ? (
              <Select value={editValue.toString()} onValueChange={(value) => setEditValue(value === 'true')}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="true">Yes</SelectItem>
                  <SelectItem value="false">No</SelectItem>
                </SelectContent>
              </Select>
            ) : field.fieldType === 'select' ? (
              <Select value={editValue} onValueChange={setEditValue}>
                <SelectTrigger className="w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {field.options?.map((option) => (
                    <SelectItem key={option} value={option}>
                      {option}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            ) : (
              <Input
                type={field.fieldType === 'number' ? 'number' : field.fieldType === 'date' ? 'date' : 'text'}
                value={editValue}
                onChange={(e) => setEditValue(e.target.value)}
                className="w-48"
              />
            )}
            <Button size="sm" onClick={() => handleUpdateField(field.fieldName)}>
              <Save className="h-3 w-3" />
            </Button>
            <Button size="sm" variant="outline" onClick={cancelEditing}>
              <X className="h-3 w-3" />
            </Button>
          </div>
          <Input
            placeholder="Reason for change (optional)"
            value={changeReason}
            onChange={(e) => setChangeReason(e.target.value)}
            className="w-full text-xs"
          />
        </div>
      );
    }

    return (
      <div className="flex items-center space-x-2">
        <span className="font-mono text-sm">
          {field.fieldType === 'boolean' 
            ? (field.fieldValue ? 'Yes' : 'No')
            : field.fieldValue?.toString() || 'N/A'
          }
        </span>
        <Button size="sm" variant="ghost" onClick={() => startEditing(field)}>
          <Edit className="h-3 w-3" />
        </Button>
        <Button 
          size="sm" 
          variant="ghost" 
          onClick={() => handleDeleteField(field.fieldName)}
          className="text-red-600 hover:text-red-700"
        >
          <Trash2 className="h-3 w-3" />
        </Button>
      </div>
    );
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <List className="h-5 w-5" />
              Custom Fields
            </CardTitle>
            <CardDescription>
              Add custom fields to store additional merchant information
            </CardDescription>
          </div>
          <Button onClick={() => setIsAddDialogOpen(true)} disabled={isLoading}>
            <Plus className="mr-2 h-4 w-4" />
            Add Field
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {customFields.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No custom fields added yet. Click "Add Field" to create one.
          </div>
        ) : (
          <div className="space-y-4">
            {customFields.map((field) => (
              <div key={field.fieldName} className="border rounded-lg p-4">
                <div className="flex items-start justify-between">
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      {getFieldIcon(field.fieldType)}
                      <span className="font-medium">{field.fieldName}</span>
                      <Badge variant="outline" className="text-xs">
                        {field.fieldType}
                      </Badge>
                      {field.isRequired && (
                        <Badge variant="destructive" className="text-xs">
                          Required
                        </Badge>
                      )}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Value: {renderFieldValue(field)}
                    </div>
                    {field.addedAt && (
                      <div className="text-xs text-muted-foreground">
                        Added on {new Date(field.addedAt).toLocaleDateString()}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>

      {/* Add Field Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Custom Field</DialogTitle>
            <DialogDescription>
              Create a new custom field to store additional merchant information
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="fieldName">Field Name</Label>
              <Input
                id="fieldName"
                value={newField.fieldName}
                onChange={(e) => setNewField(prev => ({ ...prev, fieldName: e.target.value }))}
                placeholder="Enter field name"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="fieldType">Field Type</Label>
              <Select
                value={newField.fieldType}
                onValueChange={(value: any) => setNewField(prev => ({ ...prev, fieldType: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {fieldTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      <div className="flex items-center space-x-2">
                        <type.icon className="h-4 w-4" />
                        <span>{type.label}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            {newField.fieldType === 'select' && (
              <div className="space-y-2">
                <Label htmlFor="options">Options (comma-separated)</Label>
                <Textarea
                  id="options"
                  value={newField.options?.join(', ') || ''}
                  onChange={(e) => setNewField(prev => ({ 
                    ...prev, 
                    options: e.target.value.split(',').map(opt => opt.trim()).filter(Boolean)
                  }))}
                  placeholder="Option 1, Option 2, Option 3"
                />
              </div>
            )}
            <div className="space-y-2">
              <Label htmlFor="fieldValue">Initial Value</Label>
              {newField.fieldType === 'boolean' ? (
                <Select
                  value={newField.fieldValue?.toString() || 'false'}
                  onValueChange={(value) => setNewField(prev => ({ ...prev, fieldValue: value === 'true' }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="true">Yes</SelectItem>
                    <SelectItem value="false">No</SelectItem>
                  </SelectContent>
                </Select>
              ) : newField.fieldType === 'select' ? (
                <Select
                  value={newField.fieldValue}
                  onValueChange={(value) => setNewField(prev => ({ ...prev, fieldValue: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select an option" />
                  </SelectTrigger>
                  <SelectContent>
                    {newField.options?.map((option) => (
                      <SelectItem key={option} value={option}>
                        {option}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ) : (
                <Input
                  id="fieldValue"
                  type={newField.fieldType === 'number' ? 'number' : newField.fieldType === 'date' ? 'date' : 'text'}
                  value={newField.fieldValue}
                  onChange={(e) => setNewField(prev => ({ ...prev, fieldValue: e.target.value }))}
                  placeholder="Enter initial value"
                />
              )}
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddField} disabled={!newField.fieldName || isLoading}>
              Add Field
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export default CustomFieldsManager;
