export interface BusinessAddress {
  street: string
  city: string
  state: string
  country: string
  postalCode: string
}

export interface BankAccount {
  accountHolderName: string
  accountNumber: string
  bankName: string
  ifscCode: string
  accountType: 'savings' | 'current'
}

export interface KYCData {
  documentType: 'aadhaar' | 'pan' | 'passport' | 'driving_license'
  documentNumber: string
  documentFrontUrl?: string
  documentBackUrl?: string
  verified: boolean
  verifiedAt?: string
}

export interface MerchantProfile {
  id: string
  businessName: string
  businessType: string
  businessRegistrationNumber: string
  businessAddress: BusinessAddress
  contactPersonName: string
  contactEmail: string
  contactPhone: string
  website?: string
  bankAccount: BankAccount
  kyc: KYCData
  status: 'draft' | 'pending' | 'verified' | 'rejected' | 'suspended'
  rejectionReason?: string
  createdAt: string
  updatedAt: string
}

export interface CreateMerchantDto {
  [key: string]: unknown;
  businessName: string
  businessType: string
  businessRegistrationNumber: string
  businessAddress: BusinessAddress
  contactPersonName: string
  contactEmail: string
  contactPhone: string
  website?: string
  bankAccount: Omit<BankAccount, 'verified' | 'verifiedAt'>
  kyc: {
    documentType: KYCData['documentType']
    documentNumber: string
    documentFrontFile: File
    documentBackFile?: File
  }
}

export interface UpdateMerchantDto extends Partial<CreateMerchantDto> {}

export interface MerchantListItem {
  id: string;
  businessName: string;
  contactEmail: string;
  contactPhone: string;
  status: 'draft' | 'pending' | 'verified' | 'rejected' | 'suspended';
  createdAt: string;
  updatedAt: string;
}

export interface MerchantListResponse {
  data: MerchantListItem[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface MerchantStatusUpdate {
  status: 'pending' | 'verified' | 'rejected' | 'suspended';
  reason?: string;
}

