{"version": 3, "file": "static/js/604.14d0e974.chunk.js", "mappings": ";2KAOA,MAAMA,EAAe,CAAC,SAAU,WAEzB,MAAMC,UAAsBC,EAAAA,EAMjCC,WAAAA,GACEC,QACAC,KAAKC,MAASC,IAGZ,IAAKC,EAAAA,IAAYC,OAAOC,iBAAkB,CACxC,MAAMC,EAAWA,IAAMJ,IAMvB,OAJAP,EAAaY,QAASC,IACpBJ,OAAOC,iBAAiBG,EAAOF,GAAU,KAGpC,KAELX,EAAaY,QAASC,IACpBJ,OAAOK,oBAAoBD,EAAOF,KAGvC,EAIJ,CAESI,WAAAA,GACHV,KAAKW,SACRX,KAAKY,iBAAiBZ,KAAKC,MAE9B,CAESY,aAAAA,GACkB,IAAAC,EAArBd,KAAKe,iBACR,OAAAD,EAAAd,KAAKW,UAALG,EAAAE,KAAAhB,MACAA,KAAKW,aAAUM,EAElB,CAEDL,gBAAAA,CAAiBX,GAAsB,IAAAiB,EACrClB,KAAKC,MAAQA,EACb,OAAAiB,EAAAlB,KAAKW,UAALO,EAAAF,KAAAhB,MACAA,KAAKW,QAAUV,EAAOkB,IACE,mBAAXA,EACTnB,KAAKoB,UAAUD,GAEfnB,KAAKE,YAGV,CAEDkB,SAAAA,CAAUD,GACQnB,KAAKmB,SAAWA,IAG9BnB,KAAKmB,OAASA,EACdnB,KAAKE,WAER,CAEDA,QAAAA,GACEF,KAAKqB,UAAUd,QAAQe,IAAkB,IAAjB,SAAEhB,GAAHgB,EACrBhB,KAEH,CAEDiB,QAAAA,GACE,MAA2B,mBAAhBvB,KAAKmB,OACPnB,KAAKmB,OAIS,qBAAdK,WACqB,qBAArBA,UAAUC,QAKZD,UAAUC,MAClB,EAGU,MAAAC,EAAgB,IAAI9B,C,kCC7F1B,MAAMC,EAGXC,WAAAA,GACEE,KAAKqB,UAAY,IAAIM,IACrB3B,KAAK4B,UAAY5B,KAAK4B,UAAUC,KAAK7B,KACtC,CAED4B,SAAAA,CAAUtB,GACR,MAAMwB,EAAW,CAAExB,YAKnB,OAJAN,KAAKqB,UAAUU,IAAID,GAEnB9B,KAAKU,cAEE,KACLV,KAAKqB,UAAUW,OAAOF,GACtB9B,KAAKa,gBAER,CAEDE,YAAAA,GACE,OAAOf,KAAKqB,UAAUY,KAAO,CAC9B,CAESvB,WAAAA,GAAoB,CAIpBG,aAAAA,GAAsB,E,iBCnBlC,IAAIqB,EAAQC,EAAQ,MAIpB,IAAIC,EAAW,oBAAsBC,OAAOC,GAAKD,OAAOC,GAHxD,SAAYC,EAAGC,GACb,OAAQD,IAAMC,IAAM,IAAMD,GAAK,EAAIA,IAAM,EAAIC,IAAQD,IAAMA,GAAKC,IAAMA,CACxE,EAEEC,EAAWP,EAAMO,SACjBC,EAAYR,EAAMQ,UAClBC,EAAkBT,EAAMS,gBACxBC,EAAgBV,EAAMU,cA0BxB,SAASC,EAAuBC,GAC9B,IAAIC,EAAoBD,EAAKE,YAC7BF,EAAOA,EAAKG,MACZ,IACE,IAAIC,EAAYH,IAChB,OAAQX,EAASU,EAAMI,EACzB,CAAE,MAAOC,GACP,OAAO,CACT,CACF,CAIA,IAAIC,EACF,qBAAuBhD,QACvB,qBAAuBA,OAAOiD,UAC9B,qBAAuBjD,OAAOiD,SAASC,cANzC,SAAgC1B,EAAWoB,GACzC,OAAOA,GACT,EArCA,SAAgCpB,EAAWoB,GACzC,IAAIC,EAAQD,IACVO,EAAYd,EAAS,CAAEK,KAAM,CAAEG,MAAOA,EAAOD,YAAaA,KAC1DF,EAAOS,EAAU,GAAGT,KACpBU,EAAcD,EAAU,GAmB1B,OAlBAZ,EACE,WACEG,EAAKG,MAAQA,EACbH,EAAKE,YAAcA,EACnBH,EAAuBC,IAASU,EAAY,CAAEV,KAAMA,GACtD,EACA,CAAClB,EAAWqB,EAAOD,IAErBN,EACE,WAEE,OADAG,EAAuBC,IAASU,EAAY,CAAEV,KAAMA,IAC7ClB,EAAU,WACfiB,EAAuBC,IAASU,EAAY,CAAEV,KAAMA,GACtD,EACF,EACA,CAAClB,IAEHgB,EAAcK,GACPA,CACT,EAoBAQ,EAAQC,0BACN,IAAWxB,EAAMwB,qBAAuBxB,EAAMwB,qBAAuBN,C,0DC1DhE,MAAMO,UAAqB9D,EAAAA,EAMhCC,WAAAA,GACEC,QACAC,KAAKC,MAAS2D,IAGZ,IAAKzD,EAAAA,IAAYC,OAAOC,iBAAkB,CACxC,MAAMC,EAAWA,IAAMsD,IAKvB,OAHAxD,OAAOC,iBAAiB,mBAAoBC,GAAU,GACtDF,OAAOC,iBAAiB,QAASC,GAAU,GAEpC,KAELF,OAAOK,oBAAoB,mBAAoBH,GAC/CF,OAAOK,oBAAoB,QAASH,GAEvC,EAGJ,CAESI,WAAAA,GACHV,KAAKW,SACRX,KAAKY,iBAAiBZ,KAAKC,MAE9B,CAESY,aAAAA,GACkB,IAAAC,EAArBd,KAAKe,iBACR,OAAAD,EAAAd,KAAKW,UAALG,EAAAE,KAAAhB,MACAA,KAAKW,aAAUM,EAElB,CAEDL,gBAAAA,CAAiBX,GAAsB,IAAAiB,EACrClB,KAAKC,MAAQA,EACb,OAAAiB,EAAAlB,KAAKW,UAALO,EAAAF,KAAAhB,MACAA,KAAKW,QAAUV,EAAO4D,IACG,mBAAZA,EACT7D,KAAK8D,WAAWD,GAEhB7D,KAAK4D,WAGV,CAEDE,UAAAA,CAAWD,GACO7D,KAAK6D,UAAYA,IAE/B7D,KAAK6D,QAAUA,EACf7D,KAAK4D,UAER,CAEDA,OAAAA,GACE5D,KAAKqB,UAAUd,QAAQe,IAAkB,IAAjB,SAAEhB,GAAHgB,EACrBhB,KAEH,CAEDyD,SAAAA,GACE,MAA4B,mBAAjB/D,KAAK6D,QACP7D,KAAK6D,QAIU,qBAAbR,UAIJ,MAACpC,EAAW,UAAW,aAAa+C,SACzCX,SAASY,gBAEZ,EAGU,MAAAC,EAAe,IAAIP,C,iBCzFzB,SAASQ,EACdC,EACAC,GAGA,MAAiC,oBAAtBD,EACFA,KAAqBC,KAGrBD,CACV,C,wGC8BM,MAAME,UAMHzE,EAAAA,EA8BRC,WAAAA,CACEyE,EACAC,GAQAzE,QAEAC,KAAKuE,OAASA,EACdvE,KAAKwE,QAAUA,EACfxE,KAAKyE,aAAe,IAAI9C,IACxB3B,KAAK0E,YAAc,KACnB1E,KAAK2E,cACL3E,KAAK4E,WAAWJ,EACjB,CAESG,WAAAA,GACR3E,KAAK6E,OAAS7E,KAAK6E,OAAOhD,KAAK7B,MAC/BA,KAAK8E,QAAU9E,KAAK8E,QAAQjD,KAAK7B,KAClC,CAESU,WAAAA,GACoB,IAAxBV,KAAKqB,UAAUY,OACjBjC,KAAK+E,aAAaC,YAAYhF,MAE1BiF,EAAmBjF,KAAK+E,aAAc/E,KAAKwE,UAC7CxE,KAAKkF,eAGPlF,KAAKmF,eAER,CAEStE,aAAAA,GACHb,KAAKe,gBACRf,KAAKoF,SAER,CAEDC,sBAAAA,GACE,OAAOC,EACLtF,KAAK+E,aACL/E,KAAKwE,QACLxE,KAAKwE,QAAQe,mBAEhB,CAEDC,wBAAAA,GACE,OAAOF,EACLtF,KAAK+E,aACL/E,KAAKwE,QACLxE,KAAKwE,QAAQiB,qBAEhB,CAEDL,OAAAA,GACEpF,KAAKqB,UAAY,IAAIM,IACrB3B,KAAK0F,oBACL1F,KAAK2F,uBACL3F,KAAK+E,aAAaa,eAAe5F,KAClC,CAED4E,UAAAA,CACEJ,EAOAqB,GAEA,MAAMC,EAAc9F,KAAKwE,QACnBuB,EAAY/F,KAAK+E,aAuBvB,GArBA/E,KAAKwE,QAAUxE,KAAKuE,OAAOyB,oBAAoBxB,IAa1CyB,EAAAA,EAAAA,IAAoBH,EAAa9F,KAAKwE,UACzCxE,KAAKuE,OAAO2B,gBAAgBC,OAAO,CACjCC,KAAM,yBACNC,MAAOrG,KAAK+E,aACZuB,SAAUtG,OAKoB,qBAAzBA,KAAKwE,QAAQ+B,SACY,mBAAzBvG,KAAKwE,QAAQ+B,QAEpB,MAAM,IAAIC,MAAM,oCAIbxG,KAAKwE,QAAQiC,WAChBzG,KAAKwE,QAAQiC,SAAWX,EAAYW,UAGtCzG,KAAK0G,cAEL,MAAMC,EAAU3G,KAAKe,eAInB4F,GACAC,EACE5G,KAAK+E,aACLgB,EACA/F,KAAKwE,QACLsB,IAGF9F,KAAKkF,eAIPlF,KAAK6G,aAAahB,IAIhBc,GACC3G,KAAK+E,eAAiBgB,GACrB/F,KAAKwE,QAAQ+B,UAAYT,EAAYS,SACrCvG,KAAKwE,QAAQsC,YAAchB,EAAYgB,WAEzC9G,KAAK+G,qBAGP,MAAMC,EAAsBhH,KAAKiH,0BAI/BN,GACC3G,KAAK+E,eAAiBgB,GACrB/F,KAAKwE,QAAQ+B,UAAYT,EAAYS,SACrCS,IAAwBhH,KAAKkH,wBAE/BlH,KAAKmH,sBAAsBH,EAE9B,CAEDI,mBAAAA,CACE5C,GAQA,MAAM6B,EAAQrG,KAAKuE,OAAO2B,gBAAgBmB,MAAMrH,KAAKuE,OAAQC,GAEvD8C,EAAStH,KAAKuH,aAAalB,EAAO7B,GAuBxC,OAqhBJ,SAOE8B,EACAkB,EACAhD,GAcA,GAAIA,EAAQiD,iBACV,OAAO,EAKT,QAAgCxG,IAA5BuD,EAAQkD,gBAIV,OAAOF,EAAiBG,kBAK1B,KAAK1B,EAAAA,EAAAA,IAAoBK,EAASsB,mBAAoBJ,GACpD,OAAO,EAIT,OAAO,CACR,CAtlBOK,CAAsC7H,KAAMsH,EAAQ9C,KAiBtDxE,KAAK8H,cAAgBR,EACrBtH,KAAK+H,qBAAuB/H,KAAKwE,QACjCxE,KAAKgI,mBAAqBhI,KAAK+E,aAAakD,OAEvCX,CACR,CAEDM,gBAAAA,GACE,OAAO5H,KAAK8H,aACb,CAEDI,WAAAA,CACEZ,GAEA,MAAMa,EAAgB,CAAC,EAavB,OAXA9F,OAAO+F,KAAKd,GAAQ/G,QAAS8H,IAC3BhG,OAAOiG,eAAeH,EAAeE,EAAK,CACxCE,cAAc,EACdC,YAAY,EACZC,IAAKA,KACHzI,KAAKyE,aAAa1C,IAAIsG,GACff,EAAOe,QAKbF,CACR,CAEDO,eAAAA,GACE,OAAO1I,KAAK+E,YACb,CAEDF,MAAAA,GACE7E,KAAKuE,OAAO2B,gBAAgBrB,OAAO7E,KAAK+E,aACzC,CAEDD,OAAAA,GAKE,IALiB,YACjB6D,KACGnE,GAFcoE,UAAAC,OAAA,QAAA5H,IAAA2H,UAAA,GAAAA,UAAA,GAGiC,CAAC,EAGnD,OAAO5I,KAAK8I,MAAM,IACbtE,EACHuE,KAAM,CAAEJ,gBAEX,CAEDK,eAAAA,CACExE,GAQA,MAAMyE,EAAmBjJ,KAAKuE,OAAOyB,oBAAoBxB,GAEnD6B,EAAQrG,KAAKuE,OAChB2B,gBACAmB,MAAMrH,KAAKuE,OAAQ0E,GAGtB,OAFA5C,EAAM6C,sBAAuB,EAEtB7C,EAAMyC,QAAQK,KAAK,IAAMnJ,KAAKuH,aAAalB,EAAO4C,GAC1D,CAESH,KAAAA,CACRM,GAC6C,IAAAC,EAC7C,OAAOrJ,KAAKkF,aAAa,IACpBkE,EACHE,cAA6C,OAA9BD,EAAAD,EAAaE,gBAAiBD,IAC5CF,KAAK,KACNnJ,KAAK6G,eACE7G,KAAK8H,eAEf,CAEO5C,YAAAA,CACNkE,GAGApJ,KAAK0G,cAGL,IAAI6C,EAA2CvJ,KAAK+E,aAAa+D,MAC/D9I,KAAKwE,QACL4E,GAOF,OAJI,MAACA,GAAAA,EAAcI,eACjBD,EAAUA,EAAQE,MAAMC,EAAAA,KAGnBH,CACR,CAEOxC,kBAAAA,GAGN,GAFA/G,KAAK0F,oBAGHvF,EAAAA,IACAH,KAAK8H,cAAc6B,WAClBC,EAAAA,EAAAA,IAAe5J,KAAKwE,QAAQsC,WAE7B,OAGF,MAOM+C,GAPOC,EAAAA,EAAAA,IACX9J,KAAK8H,cAAciC,cACnB/J,KAAKwE,QAAQsC,WAKQ,EAEvB9G,KAAKgK,eAAiBC,WAAW,KAC1BjK,KAAK8H,cAAc6B,SACtB3J,KAAK6G,gBAENgD,EACJ,CAEO5C,sBAAAA,GAAyB,IAAAiD,EAC/B,MAA+C,oBAAjClK,KAAKwE,QAAQ2F,gBACvBnK,KAAKwE,QAAQ2F,gBAAgBnK,KAAK8H,cAAcsC,KAAMpK,KAAK+E,cADxD,OAEHmF,EAAAlK,KAAKwE,QAAQ2F,kBAFVD,CAGR,CAEO/C,qBAAAA,CAAsBkD,GAC5BrK,KAAK2F,uBAEL3F,KAAKkH,uBAAyBmD,GAG5BlK,EAAAA,KACyB,IAAzBH,KAAKwE,QAAQ+B,UACZqD,EAAAA,EAAAA,IAAe5J,KAAKkH,yBACW,IAAhClH,KAAKkH,yBAKPlH,KAAKsK,kBAAoBC,YAAY,MAEjCvK,KAAKwE,QAAQgG,6BACbtG,EAAAA,EAAaH,cAEb/D,KAAKkF,gBAENlF,KAAKkH,wBACT,CAEO/B,YAAAA,GACNnF,KAAK+G,qBACL/G,KAAKmH,sBAAsBnH,KAAKiH,yBACjC,CAEOvB,iBAAAA,GACF1F,KAAKgK,iBACPS,aAAazK,KAAKgK,gBAClBhK,KAAKgK,oBAAiB/I,EAEzB,CAEO0E,oBAAAA,GACF3F,KAAKsK,oBACPI,cAAc1K,KAAKsK,mBACnBtK,KAAKsK,uBAAoBrJ,EAE5B,CAESsG,YAAAA,CACRlB,EACA7B,GAQA,MAAMuB,EAAY/F,KAAK+E,aACjBe,EAAc9F,KAAKwE,QACnBmG,EAAa3K,KAAK8H,cAGlB8C,EAAkB5K,KAAKgI,mBACvB6C,EAAoB7K,KAAK+H,qBACzB+C,EAAczE,IAAUN,EACxBgF,EAAoBD,EACtBzE,EAAM4B,MACNjI,KAAKgL,yBACHC,EAAkBH,EACpB9K,KAAK8H,cACL9H,KAAKkL,qBAEH,MAAEjD,GAAU5B,EAClB,IAGI+D,GAHA,cAAEL,EAAF,MAAiB5G,EAAjB,eAAwBgI,EAAxB,YAAwCC,EAAxC,OAAqDC,GAAWpD,EAChEqD,GAAiB,EACjB3D,GAAoB,EAIxB,GAAInD,EAAQ+G,mBAAoB,CAC9B,MAAM5E,EAAU3G,KAAKe,eAEfyK,GAAgB7E,GAAW1B,EAAmBoB,EAAO7B,GAErDiH,EACJ9E,GAAWC,EAAsBP,EAAON,EAAWvB,EAASsB,IAE1D0F,GAAgBC,KAClBL,GAAcM,EAAAA,EAAAA,IAASrF,EAAM7B,QAAQmH,aACjC,WACA,SACC5B,IACHsB,EAAS,YAGsB,gBAA/B7G,EAAQ+G,qBACVH,EAAc,OAEjB,CAGD,GACE5G,EAAQiD,mBACPQ,EAAM8B,eADP,MAEAkB,GAAAA,EAAiBW,WACN,UAAXP,EAEAjB,EAAOa,EAAgBb,KACvBL,EAAgBkB,EAAgBlB,cAChCsB,EAASJ,EAAgBI,OACzBC,GAAiB,OAGd,GAAI9G,EAAQqH,QAAgC,qBAAf5D,EAAMmC,KAEtC,GACEO,GACA1C,EAAMmC,QAAS,MAAAQ,OAAA,EAAAA,EAAiBR,OAChC5F,EAAQqH,SAAW7L,KAAK8L,SAExB1B,EAAOpK,KAAK+L,kBAEZ,IACE/L,KAAK8L,SAAWtH,EAAQqH,OACxBzB,EAAO5F,EAAQqH,OAAO5D,EAAMmC,MAC5BA,GAAO4B,EAAAA,EAAAA,IAAY,MAAArB,OAAA,EAAAA,EAAYP,KAAMA,EAAM5F,GAC3CxE,KAAK+L,aAAe3B,EACpBpK,KAAK0E,YAAc,I,CACnB,MAAOA,GACHuH,EAGJjM,KAAK0E,YAAcA,CACpB,MAKH0F,EAAOnC,EAAMmC,KAIf,GACqC,qBAA5B5F,EAAQkD,iBACC,qBAAT0C,GACI,YAAXiB,EACA,CACA,IAAI3D,EAGJ,GACE,MAAAiD,GAAAA,EAAYhD,mBACZnD,EAAQkD,mBAAR,MAA4BmD,OAA5B,EAA4BA,EAAmBnD,iBAE/CA,EAAkBiD,EAAWP,UAM7B,GAJA1C,EACqC,oBAA5BlD,EAAQkD,gBACVlD,EAAQkD,kBACTlD,EAAQkD,gBACVlD,EAAQqH,QAAqC,qBAApBnE,EAC3B,IACEA,EAAkBlD,EAAQqH,OAAOnE,GACjC1H,KAAK0E,YAAc,I,CACnB,MAAOA,GACHuH,EAGJjM,KAAK0E,YAAcA,CACpB,CAI0B,qBAApBgD,IACT2D,EAAS,UACTjB,GAAO4B,EAAAA,EAAAA,IAAY,MAAArB,OAAA,EAAAA,EAAYP,KAAM1C,EAAiBlD,GACtDmD,GAAoB,EAEvB,CAEG3H,KAAK0E,cACPvB,EAAQnD,KAAK0E,YACb0F,EAAOpK,KAAK+L,aACZZ,EAAiBe,KAAKC,MACtBd,EAAS,SAGX,MAAMe,EAA6B,aAAhBhB,EACbiB,EAAuB,YAAXhB,EACZiB,EAAqB,UAAXjB,EAgChB,MA9BuD,CACrDA,SACAD,cACAiB,YACAT,UAAsB,YAAXP,EACXiB,UACAC,iBAAkBF,GAAaD,EAC/BhC,OACAL,gBACA5G,QACAgI,iBACAqB,aAAcvE,EAAMwE,kBACpBC,cAAezE,EAAM0E,mBACrBC,iBAAkB3E,EAAM2E,iBACxBC,UAAW5E,EAAM6E,gBAAkB,GAAK7E,EAAM2E,iBAAmB,EACjEG,oBACE9E,EAAM6E,gBAAkB/B,EAAkB+B,iBAC1C7E,EAAM2E,iBAAmB7B,EAAkB6B,iBAC7CR,aACAY,aAAcZ,IAAeC,EAC7BY,eAAgBX,GAAmC,IAAxBrE,EAAM8B,cACjCmD,SAA0B,WAAhB9B,EACVzD,oBACA2D,iBACA6B,eAAgBb,GAAmC,IAAxBrE,EAAM8B,cACjCJ,QAASA,EAAQtD,EAAO7B,GACxBM,QAAS9E,KAAK8E,QACdD,OAAQ7E,KAAK6E,OAIhB,CAEDgC,YAAAA,CAAahB,GACX,MAAM8E,EAAa3K,KAAK8H,cAIlBsF,EAAapN,KAAKuH,aAAavH,KAAK+E,aAAc/E,KAAKwE,SAK7D,GAJAxE,KAAKgI,mBAAqBhI,KAAK+E,aAAakD,MAC5CjI,KAAK+H,qBAAuB/H,KAAKwE,SAG7ByB,EAAAA,EAAAA,IAAoBmH,EAAYzC,GAClC,OAGF3K,KAAK8H,cAAgBsF,EAGrB,MAAMC,EAAsC,CAAEC,OAAO,IAmCpB,KAA7B,MAAAzH,OAAA,EAAAA,EAAexE,YAjCWkM,MAC5B,IAAK5C,EACH,OAAO,EAGT,MAAM,oBAAE6C,GAAwBxN,KAAKwE,QAC/BiJ,EAC2B,oBAAxBD,EACHA,IACAA,EAEN,GAC+B,QAA7BC,IACEA,IAA6BzN,KAAKyE,aAAaxC,KAEjD,OAAO,EAGT,MAAMyL,EAAgB,IAAI/L,IAAJ,MACpB8L,EAAAA,EAA4BzN,KAAKyE,cAOnC,OAJIzE,KAAKwE,QAAQmJ,kBACfD,EAAc3L,IAAI,SAGbM,OAAO+F,KAAKpI,KAAK8H,eAAe8F,KAAMvF,IAC3C,MAAMwF,EAAWxF,EAEjB,OADgBrI,KAAK8H,cAAc+F,KAAclD,EAAWkD,IAC1CH,EAAcI,IAAID,MAIEN,KACxCF,EAAqBhM,WAAY,GAGnCrB,KAAKmG,OAAO,IAAKkH,KAAyBxH,GAC3C,CAEOa,WAAAA,GACN,MAAML,EAAQrG,KAAKuE,OAAO2B,gBAAgBmB,MAAMrH,KAAKuE,OAAQvE,KAAKwE,SAElE,GAAI6B,IAAUrG,KAAK+E,aACjB,OAGF,MAAMgB,EAAY/F,KAAK+E,aAGvB/E,KAAK+E,aAAesB,EACpBrG,KAAKgL,yBAA2B3E,EAAM4B,MACtCjI,KAAKkL,oBAAsBlL,KAAK8H,cAE5B9H,KAAKe,iBACE,MAATgF,GAAAA,EAAWH,eAAe5F,MAC1BqG,EAAMrB,YAAYhF,MAErB,CAED+N,aAAAA,CAAcC,GACZ,MAAMnI,EAA+B,CAAC,EAElB,YAAhBmI,EAAO5H,KACTP,EAAcoI,WAAaD,EAAOE,OACT,UAAhBF,EAAO5H,OAAqB+H,EAAAA,EAAAA,IAAiBH,EAAO7K,SAC7D0C,EAAcuI,SAAU,GAG1BpO,KAAK6G,aAAahB,GAEd7F,KAAKe,gBACPf,KAAKmF,cAER,CAEOgB,MAAAA,CAAON,GACbwI,EAAAA,EAAcC,MAAM,KAEW,IAAAC,EAAAC,EAAAC,EAAAC,EAA7B,GAAI7I,EAAcoI,UAChB,OAAAM,GAAAC,EAAAxO,KAAKwE,SAAQyJ,YAAbM,EAAAvN,KAAAwN,EAAyBxO,KAAK8H,cAAcsC,MAC5C,OAAKqE,GAAAC,EAAA,KAAAlK,SAAQmK,YAAbF,EAAAzN,KAAA0N,EAAyB1O,KAAK8H,cAAcsC,KAAO,WAC9C,GAAIvE,EAAcuI,QAAS,KAAAQ,EAAAC,EAAAC,EAAAC,EAChC,OAAAH,GAAAC,EAAA7O,KAAKwE,SAAQ4J,UAAbQ,EAAA5N,KAAA6N,EAAuB7O,KAAK8H,cAAc3E,OAC1C,OAAK2L,GAAAC,EAAA,KAAAvK,SAAQmK,YAAbG,EAAA9N,KAAA+N,OAAyB9N,EAAWjB,KAAK8H,cAAc3E,MACxD,CAGG0C,EAAcxE,WAChBrB,KAAKqB,UAAUd,QAAQe,IAAkB,IAAjB,SAAEhB,GAAHgB,EACrBhB,EAASN,KAAK8H,iBAKdjC,EAAcyH,OAChBtN,KAAKuE,OAAO2B,gBAAgBC,OAAO,CACjCE,MAAOrG,KAAK+E,aACZqB,KAAM,4BAIb,EAcH,SAASnB,EACPoB,EACA7B,GAEA,OAfF,SACE6B,EACA7B,GAEA,OACsB,IAApBA,EAAQ+B,UACPF,EAAM4B,MAAM8B,iBACY,UAAvB1D,EAAM4B,MAAMoD,SAA+C,IAAzB7G,EAAQwK,aAE/C,CAOGC,CAAkB5I,EAAO7B,IACxB6B,EAAM4B,MAAM8B,cAAgB,GAC3BzE,EAAce,EAAO7B,EAASA,EAAQ0K,eAE3C,CAED,SAAS5J,EACPe,EACA7B,EACA2K,GAIA,IAAwB,IAApB3K,EAAQ+B,QAAmB,CAC7B,MAAMtD,EAAyB,oBAAVkM,EAAuBA,EAAM9I,GAAS8I,EAE3D,MAAiB,WAAVlM,IAAiC,IAAVA,GAAmB0G,EAAQtD,EAAO7B,EACjE,CACD,OAAO,CACR,CAED,SAASoC,EACPP,EACAN,EACAvB,EACAsB,GAEA,OACsB,IAApBtB,EAAQ+B,UACPF,IAAUN,IAAqC,IAAxBD,EAAYS,YAClC/B,EAAQ4K,UAAmC,UAAvB/I,EAAM4B,MAAMoD,SAClC1B,EAAQtD,EAAO7B,EAElB,CAED,SAASmF,EACPtD,EACA7B,GAEA,OAAO6B,EAAMgJ,cAAc7K,EAAQsC,UACpC,C,wBC/wBD,SAAAwI,I,gBAGIC,WAAAA,KACEC,GAAA,GAEFC,MAAAA,KACED,GAAA,GAEFA,QAAAA,IACEA,EAGL,CAED,MAAAE,EAAAxN,EAAAA,cAAAoN,KAIOK,EAAAA,IAAAzN,EAAAA,WAAAwN,G,cC3BP,MAAAE,EAAA1N,EAAAA,eAAA,GAEO2N,EAAAA,IAAA3N,EAAAA,WAAA0N,GACMA,EAAAE,S,+BCsBXtL,EAAA4K,UAAA5K,EAAAmJ,oBAEEoC,EAAAP,Y,qBAMSQ,EAAAD,I,iBAITA,EAAAR,c,MAIGU,EAAA3O,IAML,IANK,O,gDAUL+E,GAJA/E,E,uEC5CW4O,EACXjH,IAEIA,EAAiBmG,UAGuB,kBAA/BnG,EAAiBnC,YAC1BmC,EAAiBnC,UAAY,MAUtBqJ,EAAgBA,CAC3BlH,EAGA3B,EACA8I,KACG,MAAAnH,OAAA,EAAAA,EAAkBmG,WAXEiB,EACvB/I,EACA8I,IACG9I,EAAO+E,WAAa/E,EAAO8E,aAAegE,EAQZC,CAAU/I,EAAQ8I,GAExCpH,EAAkBA,CAO7BC,EAOA3C,EACAyJ,IAEAzJ,EACG0C,gBAAgBC,GAChBE,KAAK7H,IAAc,IAAb,KAAE8I,GAAH9I,EACJ,MAAA2H,EAAiBgF,WAAjBhF,EAAiBgF,UAAY7D,GAC7B,MAAAnB,EAAiB0F,WAAjB1F,EAAiB0F,UAAYvE,EAAM,QAEpCX,MAAOtG,IACN4M,EAAmBR,aACnB,MAAAtG,EAAiBmF,SAAjBnF,EAAiBmF,QAAUjL,GAC3B,MAAA8F,EAAiB0F,WAAjB1F,EAAiB0F,eAAY1N,EAAWkC,KCiFvC,SAAAmN,EAAAC,EAAAC,EAAAC,GAaL,OCtIK,SAAAjM,EAAAkM,G,mSA8CLC,EAAA1H,EAAA8G,G,KAIA,MAAAzJ,GAAApE,EAAAA,SAAA,QAAAwO,EAAAE,EAAA3H,IAQA3B,EAAAhB,EAAAc,oBAAA6B,G,IAEAvF,EAAAA,EAAAA,GAAAxB,EAAAA,YAAA2O,IAGM,MAAAC,EAAAV,EAAA,OAAA9J,EAAA1E,UAAAyM,EAAAA,EAAA0C,WAAAF,IAQA,OAFAvK,EAAAO,eAEAiK,GACD,CAAAxK,EAAA8J,IAAA,IAAA9J,EAAAsB,mBAAA,IAAAtB,EAAAsB,oB,iBAUHtB,EAAA1B,WAAAqE,EAAA,CAAwC5H,WAAA,KACzC,CAAA4H,EAAA3C,I,SAIC,MAAA0C,EAAAC,EAAA3C,EAAAyJ,GAIF,GAAAE,EAAA,C,4GAYA,OAAAhH,EAAAuE,oBAAAlG,EAAAhB,EAAA4B,YAAAZ,EAGD,CD4BC0J,E,eAAA1M,EACD,C,sFE9GD,SAAS2M,EAAkBzE,GACzB,OAAO0E,KAAKC,IAAI,IAAO,GAAK3E,EAAc,IAC3C,CAEM,SAASd,EAASC,GACvB,MAAqC,YAA7B,MAAAA,EAAAA,EAAe,WACnBjK,EAAAA,EAAcH,UAEnB,CAEM,MAAM6P,EAGXtR,WAAAA,CAAY0E,GACVxE,KAAKqR,OAAS,MAAA7M,OAAA,EAAAA,EAAS6M,OACvBrR,KAAKsR,OAAS,MAAA9M,OAAA,EAAAA,EAAS8M,MACxB,EAGI,SAASnD,EAAiBlL,GAC/B,OAAOA,aAAiBmO,CACzB,CAEM,SAASG,EACdC,GAEA,IAGIC,EACAC,EACAC,EALAC,GAAmB,EACnBpF,EAAe,EACfqF,GAAa,EAKjB,MAAMtI,EAAU,IAAIuI,QAAe,CAACC,EAAcC,KAChDN,EAAiBK,EACjBJ,EAAgBK,IAkBZC,EAAcA,KACjB/N,EAAAA,EAAaH,aACU,WAAvByN,EAAO7F,cAA6BjK,EAAAA,EAAcH,WAE/C2Q,EAAWjP,IACV4O,IACHA,GAAa,EACb,MAAAL,EAAOvD,WAAPuD,EAAOvD,UAAYhL,GACT,MAAVwO,GAAAA,IACAC,EAAezO,KAIbkP,EAAUlP,IACT4O,IACHA,GAAa,EACb,MAAAL,EAAOpD,SAAPoD,EAAOpD,QAAUnL,GACP,MAAVwO,GAAAA,IACAE,EAAc1O,KAIZmP,EAAQA,IACL,IAAIN,QAASO,IAClBZ,EAAcxO,IACZ,MAAMqP,EAAcT,IAAeI,IAInC,OAHIK,GACFD,EAAgBpP,GAEXqP,GAET,MAAAd,EAAOe,SAAPf,EAAOe,YACNpJ,KAAK,KACNsI,OAAaxQ,EACR4Q,GACH,MAAAL,EAAOgB,YAAPhB,EAAOgB,eAMPC,EAAMA,KAEV,GAAIZ,EACF,OAGF,IAAIa,EAGJ,IACEA,EAAiBlB,EAAOmB,I,CACxB,MAAOxP,GACPuP,EAAiBZ,QAAQK,OAAOhP,EACjC,CAED2O,QAAQI,QAAQQ,GACbvJ,KAAK+I,GACLzI,MAAOtG,IAAU,IAAAyP,EAAAC,EAEhB,GAAIhB,EACF,OAIF,MAAMiB,EAAK,OAAGF,EAAApB,EAAOsB,OAAVF,EAAmB,EACxBG,EAAU,OAAGF,EAAArB,EAAOuB,YAAVF,EAAwB5B,EAClC+B,EACkB,oBAAfD,EACHA,EAAWvG,EAAcrJ,GACzB4P,EACAE,GACM,IAAVH,GACkB,kBAAVA,GAAsBtG,EAAesG,GAC3B,oBAAVA,GAAwBA,EAAMtG,EAAcrJ,IAElDyO,GAAqBqB,GAMzBzG,IAGA,MAAAgF,EAAO0B,QAAP1B,EAAO0B,OAAS1G,EAAcrJ,IAG9BgQ,EAAAA,EAAAA,IAAMH,GAEH7J,KAAK,KACJ,GAAI8I,IACF,OAAOG,MAIVjJ,KAAK,KACAyI,EACFO,EAAOhP,GAEPsP,OAtBJN,EAAOhP,MAmCf,OANIuI,EAAS8F,EAAO7F,aAClB8G,IAEAL,IAAQjJ,KAAKsJ,GAGR,CACLlJ,UACA6J,OAlIcC,IACTxB,IACHM,EAAO,IAAIf,EAAeiC,IAE1B,MAAA7B,EAAO8B,OAAP9B,EAAO8B,UA+HTC,SAAUA,KACS,MAAG9B,OAAH,EAAGA,KACClI,EAAUuI,QAAQI,UAEzCsB,YAhIkBA,KAClB5B,GAAmB,GAgInB6B,cA7HoBA,KACpB7B,GAAmB,GA8HtB,C,4LCpJM,MAAMzR,EAA6B,qBAAXC,QAA0B,SAAUA,OAE5D,SAASsJ,IAEf,CAEM,SAASgK,EACdC,EACAC,GAEA,MAA0B,oBAAZD,EACTA,EAAgDC,GACjDD,CACL,CAEM,SAAS/J,EAAe3G,GAC7B,MAAwB,kBAAVA,GAAsBA,GAAS,GAAKA,IAAU4Q,GAC7D,CAYM,SAAS/J,EAAegK,EAAmBhN,GAChD,OAAOoK,KAAK6C,IAAID,GAAahN,GAAa,GAAKoF,KAAKC,MAAO,EAC5D,CAEM,SAAS6H,EAIdzD,EACAC,EACAC,GAEA,OAAKwD,EAAW1D,GAII,oBAATC,EACF,IAAKC,EAAMhK,SAAU8J,EAAM2D,QAAS1D,GAGtC,IAAKA,EAAM/J,SAAU8J,GAPnBA,CAQV,CAEM,SAAS4D,EAGd5D,EACAC,EACAC,GAEA,OAAIwD,EAAW1D,GACO,oBAATC,EACF,IAAKC,EAAM2D,YAAa7D,EAAM8D,WAAY7D,GAE5C,IAAKA,EAAM4D,YAAa7D,GAGb,oBAATA,EACF,IAAKC,EAAM6D,WAAY9D,GAGzB,IAAKA,EACb,CAEM,SAAS+D,EAId/D,EACAC,EACAC,GAEA,OACEwD,EAAW1D,GAAQ,CAAC,IAAKC,EAAM/J,SAAU8J,GAAQE,GAAQ,CAACF,GAAQ,CAAC,EAAGC,EAEzE,CAiBM,SAAS+D,EACdC,EACAnO,GAEA,MAAM,KACJD,EAAO,MADH,MAEJqO,EAFI,YAGJrJ,EAHI,UAIJsJ,EAJI,SAKJjO,EALI,MAMJkO,GACEH,EAEJ,GAAIP,EAAWxN,GACb,GAAIgO,GACF,GAAIpO,EAAMuO,YAAcC,EAAsBpO,EAAUJ,EAAM7B,SAC5D,OAAO,OAEJ,IAAKsQ,EAAgBzO,EAAMI,SAAUA,GAC1C,OAAO,EAIX,GAAa,QAATL,EAAgB,CAClB,MAAM2O,EAAW1O,EAAM0O,WACvB,GAAa,WAAT3O,IAAsB2O,EACxB,OAAO,EAET,GAAa,aAAT3O,GAAuB2O,EACzB,OAAO,CAEV,CAED,OAAqB,mBAAVJ,GAAuBtO,EAAMsD,YAAcgL,MAK7B,qBAAhBvJ,GACPA,IAAgB/E,EAAM4B,MAAMmD,gBAK1BsJ,IAAcA,EAAUrO,IAK7B,CAEM,SAAS2O,EACdR,EACAS,GAEA,MAAM,MAAER,EAAF,SAASS,EAAT,UAAmBR,EAAnB,YAA8BN,GAAgBI,EACpD,GAAIP,EAAWG,GAAc,CAC3B,IAAKa,EAASzQ,QAAQ4P,YACpB,OAAO,EAET,GAAIK,GACF,GACEU,EAAaF,EAASzQ,QAAQ4P,eAAiBe,EAAaf,GAE5D,OAAO,OAEJ,IAAKU,EAAgBG,EAASzQ,QAAQ4P,YAAaA,GACxD,OAAO,CAEV,CAED,OACsB,mBAAbc,GACoB,YAA1BD,EAAShN,MAAMoD,SAA0B6J,MAKxCR,IAAcA,EAAUO,GAK7B,CAEM,SAASJ,EACdpO,EACAjC,GAGA,QADsB,MAAPA,OAAA,EAAAA,EAAS4Q,iBAAkBD,GAC5B1O,EACf,CAMM,SAAS0O,EAAa1O,GAC3B,OAAO4O,KAAKC,UAAU7O,EAAU,CAAC8O,EAAGC,IAClCC,EAAcD,GACVnT,OAAO+F,KAAKoN,GACTE,OACAC,OAAO,CAACrO,EAAQe,KACff,EAAOe,GAAOmN,EAAInN,GACXf,GACN,CAAC,GACNkO,EAEP,CAKM,SAASV,EAAgBc,EAAaC,GAC3C,OAAOC,EAAiBF,EAAGC,EAC5B,CAKM,SAASC,EAAiBF,EAAQC,GACvC,OAAID,IAAMC,UAICD,WAAaC,OAIpBD,IAAKC,GAAkB,kBAAND,GAA+B,kBAANC,KACpCxT,OAAO+F,KAAKyN,GAAGjI,KAAMvF,IAASyN,EAAiBF,EAAEvN,GAAMwN,EAAExN,KAIpE,CAQM,SAAS0N,EAAiBH,EAAQC,GACvC,GAAID,IAAMC,EACR,OAAOD,EAGT,MAAMI,EAAQC,EAAaL,IAAMK,EAAaJ,GAE9C,GAAIG,GAAUP,EAAcG,IAAMH,EAAcI,GAAK,CACnD,MAAMK,EAAQF,EAAQJ,EAAE/M,OAASxG,OAAO+F,KAAKwN,GAAG/M,OAC1CsN,EAASH,EAAQH,EAAIxT,OAAO+F,KAAKyN,GACjCO,EAAQD,EAAOtN,OACfwN,EAAYL,EAAQ,GAAK,CAAC,EAEhC,IAAIM,EAAa,EAEjB,IAAK,IAAIC,EAAI,EAAGA,EAAIH,EAAOG,IAAK,CAC9B,MAAMlO,EAAM2N,EAAQO,EAAIJ,EAAOI,GAC/BF,EAAKhO,GAAO0N,EAAiBH,EAAEvN,GAAMwN,EAAExN,IACnCgO,EAAKhO,KAASuN,EAAEvN,IAClBiO,GAEH,CAED,OAAOJ,IAAUE,GAASE,IAAeJ,EAAQN,EAAIS,CACtD,CAED,OAAOR,CACR,CAKM,SAAS5P,EAAuB2P,EAAMC,GAC3C,GAAKD,IAAMC,GAAOA,IAAMD,EACtB,OAAO,EAGT,IAAK,MAAMvN,KAAOuN,EAChB,GAAIA,EAAEvN,KAASwN,EAAExN,GACf,OAAO,EAIX,OAAO,CACR,CAEM,SAAS4N,EAAahT,GAC3B,OAAOuT,MAAMC,QAAQxT,IAAUA,EAAM4F,SAAWxG,OAAO+F,KAAKnF,GAAO4F,MACpE,CAGM,SAAS4M,EAAciB,GAC5B,IAAKC,EAAmBD,GACtB,OAAO,EAIT,MAAME,EAAOF,EAAE5W,YACf,GAAoB,qBAAT8W,EACT,OAAO,EAIT,MAAMC,EAAOD,EAAKE,UAClB,QAAKH,EAAmBE,MAKnBA,EAAKE,eAAe,gBAM1B,CAED,SAASJ,EAAmBD,GAC1B,MAA6C,oBAAtCrU,OAAOyU,UAAUE,SAAShW,KAAK0V,EACvC,CAEM,SAASzC,EAAWhR,GACzB,OAAOuT,MAAMC,QAAQxT,EACtB,CAMM,SAASkQ,EAAMtJ,GACpB,OAAO,IAAIiI,QAASI,IAClBjI,WAAWiI,EAASrI,IAEvB,CAMM,SAASoN,EAAkBC,GAChC/D,EAAM,GAAGhK,KAAK+N,EACf,CAEM,SAASC,IACd,GAA+B,oBAApBC,gBACT,OAAO,IAAIA,eAGd,CAEM,SAASpL,EAGdqL,EAA6BjN,EAAa5F,GAE1C,aAAIA,EAAQ8S,aAAR9S,EAAQ8S,YAAcD,EAAUjN,GAC3BiN,EACuC,oBAA9B7S,EAAQ+S,kBACjB/S,EAAQ+S,kBAAkBF,EAAUjN,IACJ,IAA9B5F,EAAQ+S,kBAEVxB,EAAiBsB,EAAUjN,GAE7BA,CACR,C,kCCjbM,MAAA1G,E,QAAA8T,oB,iBCDLC,EAAOhU,QAAU,EAAjBgU,K,iDCSK,MAAAC,EAAAxV,EAAAA,mBAAAjB,GAGP0W,EAAAzV,EAAAA,eAAA,GASA,SAAA0V,EAAAC,EAAAC,GAIE,OAAAD,IAGAC,GAAA,qBAAA1X,QACEA,OAAA2X,0B,kEAOFL,EACD,CAEM,MAAAM,EAAA,WAAwB,IAAxB,QAA0BH,GAAFjP,UAAAC,OAAA,QAAA5H,IAAA2H,UAAA,GAAAA,UAAA,MAC7B,MAAAgI,EAAA1O,EAAAA,WAAA0V,EAAAC,EAAA3V,EAAAA,WAAAyV,K,MAKE,MAAM,IAANnR,MAAA,0DAGF,OAAAoK,CACD,C,gDC+CY,MAAAvC,EAvFN,WACL,IAAI4J,EAA0B,GAC1BC,EAAe,EACfC,EAA4BjB,IAC9BA,KAEEkB,EAAsClB,IACxCA,KAGF,MAcMmB,EAAYnB,IACZgB,EACFD,EAAMK,KAAKpB,IAEXD,EAAAA,EAAAA,IAAkB,KAChBkB,EAASjB,MAkBTqB,EAAQA,KACZ,MAAMC,EAAgBP,EACtBA,EAAQ,GACJO,EAAc3P,SAChBoO,EAAAA,EAAAA,IAAkB,KAChBmB,EAAc,KACZI,EAAcjY,QAAS2W,IACrBiB,EAASjB,UAuBnB,MAAO,CACL5I,MApEgB4I,IAChB,IAAI5P,EACJ4Q,IACA,IACE5Q,EAAS4P,GACV,CAFD,QAGEgB,IACKA,GACHK,GAEH,CACD,OAAOjR,GA0DPyJ,WAzCAmG,GAEO,WAAa,QAAAuB,EAAA7P,UAAAC,OAAT6P,EAAJ,IAAAlC,MAAAiC,GAAAE,EAAA,EAAAA,EAAAF,EAAAE,IAAID,EAAJC,GAAA/P,UAAA+P,GACLN,EAAS,KACPnB,KAAYwB,I,EAsChBL,WACAO,kBAhByBjG,IACzBwF,EAAWxF,GAgBXkG,uBAT8BlG,IAC9ByF,EAAgBzF,GAUnB,CAG4BmG,E", "sources": ["../node_modules/@tanstack/query-core/src/onlineManager.ts", "../node_modules/@tanstack/query-core/src/subscribable.ts", "../node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.production.js", "../node_modules/@tanstack/query-core/src/focusManager.ts", "../node_modules/@tanstack/react-query/src/utils.ts", "../node_modules/@tanstack/query-core/src/queryObserver.ts", "../node_modules/@tanstack/react-query/src/QueryErrorResetBoundary.tsx", "../node_modules/@tanstack/react-query/src/isRestoring.tsx", "../node_modules/@tanstack/react-query/src/errorBoundaryUtils.ts", "../node_modules/@tanstack/react-query/src/suspense.ts", "../node_modules/@tanstack/react-query/src/useQuery.ts", "../node_modules/@tanstack/react-query/src/useBaseQuery.ts", "../node_modules/@tanstack/query-core/src/retryer.ts", "../node_modules/@tanstack/query-core/src/utils.ts", "../node_modules/@tanstack/react-query/src/useSyncExternalStore.ts", "../node_modules/use-sync-external-store/shim/index.js", "../node_modules/@tanstack/react-query/src/QueryClientProvider.tsx", "../node_modules/@tanstack/query-core/src/notifyManager.ts"], "sourcesContent": ["import { Subscribable } from './subscribable'\nimport { isServer } from './utils'\n\ntype SetupFn = (\n  setOnline: (online?: boolean) => void,\n) => (() => void) | undefined\n\nconst onlineEvents = ['online', 'offline'] as const\n\nexport class OnlineManager extends Subscribable {\n  private online?: boolean\n  private cleanup?: () => void\n\n  private setup: SetupFn\n\n  constructor() {\n    super()\n    this.setup = (onOnline) => {\n      // addEventListener does not exist in React Native, but window does\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n      if (!isServer && window.addEventListener) {\n        const listener = () => onOnline()\n        // Listen to online\n        onlineEvents.forEach((event) => {\n          window.addEventListener(event, listener, false)\n        })\n\n        return () => {\n          // Be sure to unsubscribe if a new handler is set\n          onlineEvents.forEach((event) => {\n            window.removeEventListener(event, listener)\n          })\n        }\n      }\n\n      return\n    }\n  }\n\n  protected onSubscribe(): void {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup)\n    }\n  }\n\n  protected onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.cleanup?.()\n      this.cleanup = undefined\n    }\n  }\n\n  setEventListener(setup: SetupFn): void {\n    this.setup = setup\n    this.cleanup?.()\n    this.cleanup = setup((online?: boolean) => {\n      if (typeof online === 'boolean') {\n        this.setOnline(online)\n      } else {\n        this.onOnline()\n      }\n    })\n  }\n\n  setOnline(online?: boolean): void {\n    const changed = this.online !== online\n\n    if (changed) {\n      this.online = online\n      this.onOnline()\n    }\n  }\n\n  onOnline(): void {\n    this.listeners.forEach(({ listener }) => {\n      listener()\n    })\n  }\n\n  isOnline(): boolean {\n    if (typeof this.online === 'boolean') {\n      return this.online\n    }\n\n    if (\n      typeof navigator === 'undefined' ||\n      typeof navigator.onLine === 'undefined'\n    ) {\n      return true\n    }\n\n    return navigator.onLine\n  }\n}\n\nexport const onlineManager = new OnlineManager()\n", "type Listener = () => void\n\nexport class Subscribable<TListener extends Function = Listener> {\n  protected listeners: Set<{ listener: TListener }>\n\n  constructor() {\n    this.listeners = new Set()\n    this.subscribe = this.subscribe.bind(this)\n  }\n\n  subscribe(listener: TListener): () => void {\n    const identity = { listener }\n    this.listeners.add(identity)\n\n    this.onSubscribe()\n\n    return () => {\n      this.listeners.delete(identity)\n      this.onUnsubscribe()\n    }\n  }\n\n  hasListeners(): boolean {\n    return this.listeners.size > 0\n  }\n\n  protected onSubscribe(): void {\n    // Do nothing\n  }\n\n  protected onUnsubscribe(): void {\n    // Do nothing\n  }\n}\n", "/**\n * @license React\n * use-sync-external-store-shim.production.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\nvar React = require(\"react\");\nfunction is(x, y) {\n  return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n}\nvar objectIs = \"function\" === typeof Object.is ? Object.is : is,\n  useState = React.useState,\n  useEffect = React.useEffect,\n  useLayoutEffect = React.useLayoutEffect,\n  useDebugValue = React.useDebugValue;\nfunction useSyncExternalStore$2(subscribe, getSnapshot) {\n  var value = getSnapshot(),\n    _useState = useState({ inst: { value: value, getSnapshot: getSnapshot } }),\n    inst = _useState[0].inst,\n    forceUpdate = _useState[1];\n  useLayoutEffect(\n    function () {\n      inst.value = value;\n      inst.getSnapshot = getSnapshot;\n      checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n    },\n    [subscribe, value, getSnapshot]\n  );\n  useEffect(\n    function () {\n      checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n      return subscribe(function () {\n        checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n      });\n    },\n    [subscribe]\n  );\n  useDebugValue(value);\n  return value;\n}\nfunction checkIfSnapshotChanged(inst) {\n  var latestGetSnapshot = inst.getSnapshot;\n  inst = inst.value;\n  try {\n    var nextValue = latestGetSnapshot();\n    return !objectIs(inst, nextValue);\n  } catch (error) {\n    return !0;\n  }\n}\nfunction useSyncExternalStore$1(subscribe, getSnapshot) {\n  return getSnapshot();\n}\nvar shim =\n  \"undefined\" === typeof window ||\n  \"undefined\" === typeof window.document ||\n  \"undefined\" === typeof window.document.createElement\n    ? useSyncExternalStore$1\n    : useSyncExternalStore$2;\nexports.useSyncExternalStore =\n  void 0 !== React.useSyncExternalStore ? React.useSyncExternalStore : shim;\n", "import { Subscribable } from './subscribable'\nimport { isServer } from './utils'\n\ntype SetupFn = (\n  setFocused: (focused?: boolean) => void,\n) => (() => void) | undefined\n\nexport class FocusManager extends Subscribable {\n  private focused?: boolean\n  private cleanup?: () => void\n\n  private setup: SetupFn\n\n  constructor() {\n    super()\n    this.setup = (onFocus) => {\n      // addEventListener does not exist in React Native, but window does\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n      if (!isServer && window.addEventListener) {\n        const listener = () => onFocus()\n        // Listen to visibillitychange and focus\n        window.addEventListener('visibilitychange', listener, false)\n        window.addEventListener('focus', listener, false)\n\n        return () => {\n          // Be sure to unsubscribe if a new handler is set\n          window.removeEventListener('visibilitychange', listener)\n          window.removeEventListener('focus', listener)\n        }\n      }\n      return\n    }\n  }\n\n  protected onSubscribe(): void {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup)\n    }\n  }\n\n  protected onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.cleanup?.()\n      this.cleanup = undefined\n    }\n  }\n\n  setEventListener(setup: SetupFn): void {\n    this.setup = setup\n    this.cleanup?.()\n    this.cleanup = setup((focused) => {\n      if (typeof focused === 'boolean') {\n        this.setFocused(focused)\n      } else {\n        this.onFocus()\n      }\n    })\n  }\n\n  setFocused(focused?: boolean): void {\n    const changed = this.focused !== focused\n    if (changed) {\n      this.focused = focused\n      this.onFocus()\n    }\n  }\n\n  onFocus(): void {\n    this.listeners.forEach(({ listener }) => {\n      listener()\n    })\n  }\n\n  isFocused(): boolean {\n    if (typeof this.focused === 'boolean') {\n      return this.focused\n    }\n\n    // document global can be unavailable in react native\n    if (typeof document === 'undefined') {\n      return true\n    }\n\n    return [undefined, 'visible', 'prerender'].includes(\n      document.visibilityState,\n    )\n  }\n}\n\nexport const focusManager = new FocusManager()\n", "export function shouldThrowError<T extends (...args: any[]) => boolean>(\n  _useErrorBoundary: boolean | T | undefined,\n  params: Parameters<T>,\n): boolean {\n  // Allow useErrorBoundary function to override throwing behavior on a per-error basis\n  if (typeof _useErrorBoundary === 'function') {\n    return _useErrorBoundary(...params)\n  }\n\n  return !!_useErrorBoundary\n}\n", "import {\n  isServer,\n  isValidTimeout,\n  noop,\n  replaceData,\n  shallowEqualObjects,\n  timeUntilStale,\n} from './utils'\nimport { notifyManager } from './notifyManager'\nimport { focusManager } from './focusManager'\nimport { Subscribable } from './subscribable'\nimport { canFetch, isCancelledError } from './retryer'\nimport type {\n  PlaceholderDataFunction,\n  QueryKey,\n  QueryObserverBaseResult,\n  QueryObserverOptions,\n  QueryObserverResult,\n  QueryOptions,\n  RefetchOptions,\n} from './types'\nimport type { Action, FetchOptions, Query, QueryState } from './query'\nimport type { QueryClient } from './queryClient'\nimport type { DefaultedQueryObserverOptions, RefetchPageFilters } from './types'\n\ntype QueryObserverListener<TData, TError> = (\n  result: QueryObserverResult<TData, TError>,\n) => void\n\nexport interface NotifyOptions {\n  cache?: boolean\n  listeners?: boolean\n  onError?: boolean\n  onSuccess?: boolean\n}\n\nexport interface ObserverFetchOptions extends FetchOptions {\n  throwOnError?: boolean\n}\n\nexport class QueryObserver<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> extends Subscribable<QueryObserverListener<TData, TError>> {\n  options: QueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >\n\n  private client: QueryClient\n  private currentQuery!: Query<TQueryFnData, TError, TQueryData, TQueryKey>\n  private currentQueryInitialState!: QueryState<TQueryData, TError>\n  private currentResult!: QueryObserverResult<TData, TError>\n  private currentResultState?: QueryState<TQueryData, TError>\n  private currentResultOptions?: QueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >\n  private previousQueryResult?: QueryObserverResult<TData, TError>\n  private selectError: TError | null\n  private selectFn?: (data: TQueryData) => TData\n  private selectResult?: TData\n  private staleTimeoutId?: ReturnType<typeof setTimeout>\n  private refetchIntervalId?: ReturnType<typeof setInterval>\n  private currentRefetchInterval?: number | false\n  private trackedProps!: Set<keyof QueryObserverResult>\n\n  constructor(\n    client: QueryClient,\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ) {\n    super()\n\n    this.client = client\n    this.options = options\n    this.trackedProps = new Set()\n    this.selectError = null\n    this.bindMethods()\n    this.setOptions(options)\n  }\n\n  protected bindMethods(): void {\n    this.remove = this.remove.bind(this)\n    this.refetch = this.refetch.bind(this)\n  }\n\n  protected onSubscribe(): void {\n    if (this.listeners.size === 1) {\n      this.currentQuery.addObserver(this)\n\n      if (shouldFetchOnMount(this.currentQuery, this.options)) {\n        this.executeFetch()\n      }\n\n      this.updateTimers()\n    }\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.hasListeners()) {\n      this.destroy()\n    }\n  }\n\n  shouldFetchOnReconnect(): boolean {\n    return shouldFetchOn(\n      this.currentQuery,\n      this.options,\n      this.options.refetchOnReconnect,\n    )\n  }\n\n  shouldFetchOnWindowFocus(): boolean {\n    return shouldFetchOn(\n      this.currentQuery,\n      this.options,\n      this.options.refetchOnWindowFocus,\n    )\n  }\n\n  destroy(): void {\n    this.listeners = new Set()\n    this.clearStaleTimeout()\n    this.clearRefetchInterval()\n    this.currentQuery.removeObserver(this)\n  }\n\n  setOptions(\n    options?: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n    notifyOptions?: NotifyOptions,\n  ): void {\n    const prevOptions = this.options\n    const prevQuery = this.currentQuery\n\n    this.options = this.client.defaultQueryOptions(options)\n\n    if (\n      process.env.NODE_ENV !== 'production' &&\n      typeof options?.isDataEqual !== 'undefined'\n    ) {\n      this.client\n        .getLogger()\n        .error(\n          `The isDataEqual option has been deprecated and will be removed in the next major version. You can achieve the same functionality by passing a function as the structuralSharing option`,\n        )\n    }\n\n    if (!shallowEqualObjects(prevOptions, this.options)) {\n      this.client.getQueryCache().notify({\n        type: 'observerOptionsUpdated',\n        query: this.currentQuery,\n        observer: this,\n      })\n    }\n\n    if (\n      typeof this.options.enabled !== 'undefined' &&\n      typeof this.options.enabled !== 'boolean'\n    ) {\n      throw new Error('Expected enabled to be a boolean')\n    }\n\n    // Keep previous query key if the user does not supply one\n    if (!this.options.queryKey) {\n      this.options.queryKey = prevOptions.queryKey\n    }\n\n    this.updateQuery()\n\n    const mounted = this.hasListeners()\n\n    // Fetch if there are subscribers\n    if (\n      mounted &&\n      shouldFetchOptionally(\n        this.currentQuery,\n        prevQuery,\n        this.options,\n        prevOptions,\n      )\n    ) {\n      this.executeFetch()\n    }\n\n    // Update result\n    this.updateResult(notifyOptions)\n\n    // Update stale interval if needed\n    if (\n      mounted &&\n      (this.currentQuery !== prevQuery ||\n        this.options.enabled !== prevOptions.enabled ||\n        this.options.staleTime !== prevOptions.staleTime)\n    ) {\n      this.updateStaleTimeout()\n    }\n\n    const nextRefetchInterval = this.computeRefetchInterval()\n\n    // Update refetch interval if needed\n    if (\n      mounted &&\n      (this.currentQuery !== prevQuery ||\n        this.options.enabled !== prevOptions.enabled ||\n        nextRefetchInterval !== this.currentRefetchInterval)\n    ) {\n      this.updateRefetchInterval(nextRefetchInterval)\n    }\n  }\n\n  getOptimisticResult(\n    options: DefaultedQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): QueryObserverResult<TData, TError> {\n    const query = this.client.getQueryCache().build(this.client, options)\n\n    const result = this.createResult(query, options)\n\n    if (shouldAssignObserverCurrentProperties(this, result, options)) {\n      // this assigns the optimistic result to the current Observer\n      // because if the query function changes, useQuery will be performing\n      // an effect where it would fetch again.\n      // When the fetch finishes, we perform a deep data cloning in order\n      // to reuse objects references. This deep data clone is performed against\n      // the `observer.currentResult.data` property\n      // When QueryKey changes, we refresh the query and get new `optimistic`\n      // result, while we leave the `observer.currentResult`, so when new data\n      // arrives, it finds the old `observer.currentResult` which is related\n      // to the old QueryKey. Which means that currentResult and selectData are\n      // out of sync already.\n      // To solve this, we move the cursor of the currentResult everytime\n      // an observer reads an optimistic value.\n\n      // When keeping the previous data, the result doesn't change until new\n      // data arrives.\n      this.currentResult = result\n      this.currentResultOptions = this.options\n      this.currentResultState = this.currentQuery.state\n    }\n    return result\n  }\n\n  getCurrentResult(): QueryObserverResult<TData, TError> {\n    return this.currentResult\n  }\n\n  trackResult(\n    result: QueryObserverResult<TData, TError>,\n  ): QueryObserverResult<TData, TError> {\n    const trackedResult = {} as QueryObserverResult<TData, TError>\n\n    Object.keys(result).forEach((key) => {\n      Object.defineProperty(trackedResult, key, {\n        configurable: false,\n        enumerable: true,\n        get: () => {\n          this.trackedProps.add(key as keyof QueryObserverResult)\n          return result[key as keyof QueryObserverResult]\n        },\n      })\n    })\n\n    return trackedResult\n  }\n\n  getCurrentQuery(): Query<TQueryFnData, TError, TQueryData, TQueryKey> {\n    return this.currentQuery\n  }\n\n  remove(): void {\n    this.client.getQueryCache().remove(this.currentQuery)\n  }\n\n  refetch<TPageData>({\n    refetchPage,\n    ...options\n  }: RefetchOptions & RefetchPageFilters<TPageData> = {}): Promise<\n    QueryObserverResult<TData, TError>\n  > {\n    return this.fetch({\n      ...options,\n      meta: { refetchPage },\n    })\n  }\n\n  fetchOptimistic(\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): Promise<QueryObserverResult<TData, TError>> {\n    const defaultedOptions = this.client.defaultQueryOptions(options)\n\n    const query = this.client\n      .getQueryCache()\n      .build(this.client, defaultedOptions)\n    query.isFetchingOptimistic = true\n\n    return query.fetch().then(() => this.createResult(query, defaultedOptions))\n  }\n\n  protected fetch(\n    fetchOptions: ObserverFetchOptions,\n  ): Promise<QueryObserverResult<TData, TError>> {\n    return this.executeFetch({\n      ...fetchOptions,\n      cancelRefetch: fetchOptions.cancelRefetch ?? true,\n    }).then(() => {\n      this.updateResult()\n      return this.currentResult\n    })\n  }\n\n  private executeFetch(\n    fetchOptions?: ObserverFetchOptions,\n  ): Promise<TQueryData | undefined> {\n    // Make sure we reference the latest query as the current one might have been removed\n    this.updateQuery()\n\n    // Fetch\n    let promise: Promise<TQueryData | undefined> = this.currentQuery.fetch(\n      this.options as QueryOptions<TQueryFnData, TError, TQueryData, TQueryKey>,\n      fetchOptions,\n    )\n\n    if (!fetchOptions?.throwOnError) {\n      promise = promise.catch(noop)\n    }\n\n    return promise\n  }\n\n  private updateStaleTimeout(): void {\n    this.clearStaleTimeout()\n\n    if (\n      isServer ||\n      this.currentResult.isStale ||\n      !isValidTimeout(this.options.staleTime)\n    ) {\n      return\n    }\n\n    const time = timeUntilStale(\n      this.currentResult.dataUpdatedAt,\n      this.options.staleTime,\n    )\n\n    // The timeout is sometimes triggered 1 ms before the stale time expiration.\n    // To mitigate this issue we always add 1 ms to the timeout.\n    const timeout = time + 1\n\n    this.staleTimeoutId = setTimeout(() => {\n      if (!this.currentResult.isStale) {\n        this.updateResult()\n      }\n    }, timeout)\n  }\n\n  private computeRefetchInterval() {\n    return typeof this.options.refetchInterval === 'function'\n      ? this.options.refetchInterval(this.currentResult.data, this.currentQuery)\n      : this.options.refetchInterval ?? false\n  }\n\n  private updateRefetchInterval(nextInterval: number | false): void {\n    this.clearRefetchInterval()\n\n    this.currentRefetchInterval = nextInterval\n\n    if (\n      isServer ||\n      this.options.enabled === false ||\n      !isValidTimeout(this.currentRefetchInterval) ||\n      this.currentRefetchInterval === 0\n    ) {\n      return\n    }\n\n    this.refetchIntervalId = setInterval(() => {\n      if (\n        this.options.refetchIntervalInBackground ||\n        focusManager.isFocused()\n      ) {\n        this.executeFetch()\n      }\n    }, this.currentRefetchInterval)\n  }\n\n  private updateTimers(): void {\n    this.updateStaleTimeout()\n    this.updateRefetchInterval(this.computeRefetchInterval())\n  }\n\n  private clearStaleTimeout(): void {\n    if (this.staleTimeoutId) {\n      clearTimeout(this.staleTimeoutId)\n      this.staleTimeoutId = undefined\n    }\n  }\n\n  private clearRefetchInterval(): void {\n    if (this.refetchIntervalId) {\n      clearInterval(this.refetchIntervalId)\n      this.refetchIntervalId = undefined\n    }\n  }\n\n  protected createResult(\n    query: Query<TQueryFnData, TError, TQueryData, TQueryKey>,\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): QueryObserverResult<TData, TError> {\n    const prevQuery = this.currentQuery\n    const prevOptions = this.options\n    const prevResult = this.currentResult as\n      | QueryObserverResult<TData, TError>\n      | undefined\n    const prevResultState = this.currentResultState\n    const prevResultOptions = this.currentResultOptions\n    const queryChange = query !== prevQuery\n    const queryInitialState = queryChange\n      ? query.state\n      : this.currentQueryInitialState\n    const prevQueryResult = queryChange\n      ? this.currentResult\n      : this.previousQueryResult\n\n    const { state } = query\n    let { dataUpdatedAt, error, errorUpdatedAt, fetchStatus, status } = state\n    let isPreviousData = false\n    let isPlaceholderData = false\n    let data: TData | undefined\n\n    // Optimistically set result in fetching state if needed\n    if (options._optimisticResults) {\n      const mounted = this.hasListeners()\n\n      const fetchOnMount = !mounted && shouldFetchOnMount(query, options)\n\n      const fetchOptionally =\n        mounted && shouldFetchOptionally(query, prevQuery, options, prevOptions)\n\n      if (fetchOnMount || fetchOptionally) {\n        fetchStatus = canFetch(query.options.networkMode)\n          ? 'fetching'\n          : 'paused'\n        if (!dataUpdatedAt) {\n          status = 'loading'\n        }\n      }\n      if (options._optimisticResults === 'isRestoring') {\n        fetchStatus = 'idle'\n      }\n    }\n\n    // Keep previous data if needed\n    if (\n      options.keepPreviousData &&\n      !state.dataUpdatedAt &&\n      prevQueryResult?.isSuccess &&\n      status !== 'error'\n    ) {\n      data = prevQueryResult.data\n      dataUpdatedAt = prevQueryResult.dataUpdatedAt\n      status = prevQueryResult.status\n      isPreviousData = true\n    }\n    // Select data if needed\n    else if (options.select && typeof state.data !== 'undefined') {\n      // Memoize select result\n      if (\n        prevResult &&\n        state.data === prevResultState?.data &&\n        options.select === this.selectFn\n      ) {\n        data = this.selectResult\n      } else {\n        try {\n          this.selectFn = options.select\n          data = options.select(state.data)\n          data = replaceData(prevResult?.data, data, options)\n          this.selectResult = data\n          this.selectError = null\n        } catch (selectError) {\n          if (process.env.NODE_ENV !== 'production') {\n            this.client.getLogger().error(selectError)\n          }\n          this.selectError = selectError as TError\n        }\n      }\n    }\n    // Use query data\n    else {\n      data = state.data as unknown as TData\n    }\n\n    // Show placeholder data if needed\n    if (\n      typeof options.placeholderData !== 'undefined' &&\n      typeof data === 'undefined' &&\n      status === 'loading'\n    ) {\n      let placeholderData\n\n      // Memoize placeholder data\n      if (\n        prevResult?.isPlaceholderData &&\n        options.placeholderData === prevResultOptions?.placeholderData\n      ) {\n        placeholderData = prevResult.data\n      } else {\n        placeholderData =\n          typeof options.placeholderData === 'function'\n            ? (options.placeholderData as PlaceholderDataFunction<TQueryData>)()\n            : options.placeholderData\n        if (options.select && typeof placeholderData !== 'undefined') {\n          try {\n            placeholderData = options.select(placeholderData)\n            this.selectError = null\n          } catch (selectError) {\n            if (process.env.NODE_ENV !== 'production') {\n              this.client.getLogger().error(selectError)\n            }\n            this.selectError = selectError as TError\n          }\n        }\n      }\n\n      if (typeof placeholderData !== 'undefined') {\n        status = 'success'\n        data = replaceData(prevResult?.data, placeholderData, options) as TData\n        isPlaceholderData = true\n      }\n    }\n\n    if (this.selectError) {\n      error = this.selectError as any\n      data = this.selectResult\n      errorUpdatedAt = Date.now()\n      status = 'error'\n    }\n\n    const isFetching = fetchStatus === 'fetching'\n    const isLoading = status === 'loading'\n    const isError = status === 'error'\n\n    const result: QueryObserverBaseResult<TData, TError> = {\n      status,\n      fetchStatus,\n      isLoading,\n      isSuccess: status === 'success',\n      isError,\n      isInitialLoading: isLoading && isFetching,\n      data,\n      dataUpdatedAt,\n      error,\n      errorUpdatedAt,\n      failureCount: state.fetchFailureCount,\n      failureReason: state.fetchFailureReason,\n      errorUpdateCount: state.errorUpdateCount,\n      isFetched: state.dataUpdateCount > 0 || state.errorUpdateCount > 0,\n      isFetchedAfterMount:\n        state.dataUpdateCount > queryInitialState.dataUpdateCount ||\n        state.errorUpdateCount > queryInitialState.errorUpdateCount,\n      isFetching,\n      isRefetching: isFetching && !isLoading,\n      isLoadingError: isError && state.dataUpdatedAt === 0,\n      isPaused: fetchStatus === 'paused',\n      isPlaceholderData,\n      isPreviousData,\n      isRefetchError: isError && state.dataUpdatedAt !== 0,\n      isStale: isStale(query, options),\n      refetch: this.refetch,\n      remove: this.remove,\n    }\n\n    return result as QueryObserverResult<TData, TError>\n  }\n\n  updateResult(notifyOptions?: NotifyOptions): void {\n    const prevResult = this.currentResult as\n      | QueryObserverResult<TData, TError>\n      | undefined\n\n    const nextResult = this.createResult(this.currentQuery, this.options)\n    this.currentResultState = this.currentQuery.state\n    this.currentResultOptions = this.options\n\n    // Only notify and update result if something has changed\n    if (shallowEqualObjects(nextResult, prevResult)) {\n      return\n    }\n\n    this.currentResult = nextResult\n\n    // Determine which callbacks to trigger\n    const defaultNotifyOptions: NotifyOptions = { cache: true }\n\n    const shouldNotifyListeners = (): boolean => {\n      if (!prevResult) {\n        return true\n      }\n\n      const { notifyOnChangeProps } = this.options\n      const notifyOnChangePropsValue =\n        typeof notifyOnChangeProps === 'function'\n          ? notifyOnChangeProps()\n          : notifyOnChangeProps\n\n      if (\n        notifyOnChangePropsValue === 'all' ||\n        (!notifyOnChangePropsValue && !this.trackedProps.size)\n      ) {\n        return true\n      }\n\n      const includedProps = new Set(\n        notifyOnChangePropsValue ?? this.trackedProps,\n      )\n\n      if (this.options.useErrorBoundary) {\n        includedProps.add('error')\n      }\n\n      return Object.keys(this.currentResult).some((key) => {\n        const typedKey = key as keyof QueryObserverResult\n        const changed = this.currentResult[typedKey] !== prevResult[typedKey]\n        return changed && includedProps.has(typedKey)\n      })\n    }\n\n    if (notifyOptions?.listeners !== false && shouldNotifyListeners()) {\n      defaultNotifyOptions.listeners = true\n    }\n\n    this.notify({ ...defaultNotifyOptions, ...notifyOptions })\n  }\n\n  private updateQuery(): void {\n    const query = this.client.getQueryCache().build(this.client, this.options)\n\n    if (query === this.currentQuery) {\n      return\n    }\n\n    const prevQuery = this.currentQuery as\n      | Query<TQueryFnData, TError, TQueryData, TQueryKey>\n      | undefined\n    this.currentQuery = query\n    this.currentQueryInitialState = query.state\n    this.previousQueryResult = this.currentResult\n\n    if (this.hasListeners()) {\n      prevQuery?.removeObserver(this)\n      query.addObserver(this)\n    }\n  }\n\n  onQueryUpdate(action: Action<TData, TError>): void {\n    const notifyOptions: NotifyOptions = {}\n\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = !action.manual\n    } else if (action.type === 'error' && !isCancelledError(action.error)) {\n      notifyOptions.onError = true\n    }\n\n    this.updateResult(notifyOptions)\n\n    if (this.hasListeners()) {\n      this.updateTimers()\n    }\n  }\n\n  private notify(notifyOptions: NotifyOptions): void {\n    notifyManager.batch(() => {\n      // First trigger the configuration callbacks\n      if (notifyOptions.onSuccess) {\n        this.options.onSuccess?.(this.currentResult.data!)\n        this.options.onSettled?.(this.currentResult.data!, null)\n      } else if (notifyOptions.onError) {\n        this.options.onError?.(this.currentResult.error!)\n        this.options.onSettled?.(undefined, this.currentResult.error!)\n      }\n\n      // Then trigger the listeners\n      if (notifyOptions.listeners) {\n        this.listeners.forEach(({ listener }) => {\n          listener(this.currentResult)\n        })\n      }\n\n      // Then the cache listeners\n      if (notifyOptions.cache) {\n        this.client.getQueryCache().notify({\n          query: this.currentQuery,\n          type: 'observerResultsUpdated',\n        })\n      }\n    })\n  }\n}\n\nfunction shouldLoadOnMount(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any>,\n): boolean {\n  return (\n    options.enabled !== false &&\n    !query.state.dataUpdatedAt &&\n    !(query.state.status === 'error' && options.retryOnMount === false)\n  )\n}\n\nfunction shouldFetchOnMount(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return (\n    shouldLoadOnMount(query, options) ||\n    (query.state.dataUpdatedAt > 0 &&\n      shouldFetchOn(query, options, options.refetchOnMount))\n  )\n}\n\nfunction shouldFetchOn(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n  field: typeof options['refetchOnMount'] &\n    typeof options['refetchOnWindowFocus'] &\n    typeof options['refetchOnReconnect'],\n) {\n  if (options.enabled !== false) {\n    const value = typeof field === 'function' ? field(query) : field\n\n    return value === 'always' || (value !== false && isStale(query, options))\n  }\n  return false\n}\n\nfunction shouldFetchOptionally(\n  query: Query<any, any, any, any>,\n  prevQuery: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n  prevOptions: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return (\n    options.enabled !== false &&\n    (query !== prevQuery || prevOptions.enabled === false) &&\n    (!options.suspense || query.state.status !== 'error') &&\n    isStale(query, options)\n  )\n}\n\nfunction isStale(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return query.isStaleByTime(options.staleTime)\n}\n\n// this function would decide if we will update the observer's 'current'\n// properties after an optimistic reading via getOptimisticResult\nfunction shouldAssignObserverCurrentProperties<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  observer: QueryObserver<TQueryFnData, TError, TData, TQueryData, TQueryKey>,\n  optimisticResult: QueryObserverResult<TData, TError>,\n  options: DefaultedQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >,\n) {\n  // it is important to keep this condition like this for three reasons:\n  // 1. It will get removed in the v5\n  // 2. it reads: don't update the properties if we want to keep the previous\n  // data.\n  // 3. The opposite condition (!options.keepPreviousData) would fallthrough\n  // and will result in a bad decision\n  if (options.keepPreviousData) {\n    return false\n  }\n\n  // this means we want to put some placeholder data when pending and queryKey\n  // changed.\n  if (options.placeholderData !== undefined) {\n    // re-assign properties only if current data is placeholder data\n    // which means that data did not arrive yet, so, if there is some cached data\n    // we need to \"prepare\" to receive it\n    return optimisticResult.isPlaceholderData\n  }\n\n  // if the newly created result isn't what the observer is holding as current,\n  // then we'll need to update the properties as well\n  if (!shallowEqualObjects(observer.getCurrentResult(), optimisticResult)) {\n    return true\n  }\n\n  // basically, just keep previous properties if nothing changed\n  return false\n}\n", "'use client'\nimport * as React from 'react'\n\n// CONTEXT\n\nexport interface QueryErrorResetBoundaryValue {\n  clearReset: () => void\n  isReset: () => boolean\n  reset: () => void\n}\n\nfunction createValue(): QueryErrorResetBoundaryValue {\n  let isReset = false\n  return {\n    clearReset: () => {\n      isReset = false\n    },\n    reset: () => {\n      isReset = true\n    },\n    isReset: () => {\n      return isReset\n    },\n  }\n}\n\nconst QueryErrorResetBoundaryContext = React.createContext(createValue())\n\n// HOOK\n\nexport const useQueryErrorResetBoundary = () =>\n  React.useContext(QueryErrorResetBoundaryContext)\n\n// COMPONENT\n\nexport interface QueryErrorResetBoundaryProps {\n  children:\n    | ((value: QueryErrorResetBoundaryValue) => React.ReactNode)\n    | React.ReactNode\n}\n\nexport const QueryErrorResetBoundary = ({\n  children,\n}: QueryErrorResetBoundaryProps) => {\n  const [value] = React.useState(() => createValue())\n  return (\n    <QueryErrorResetBoundaryContext.Provider value={value}>\n      {typeof children === 'function'\n        ? (children as Function)(value)\n        : children}\n    </QueryErrorResetBoundaryContext.Provider>\n  )\n}\n", "'use client'\nimport * as React from 'react'\n\nconst IsRestoringContext = React.createContext(false)\n\nexport const useIsRestoring = () => React.useContext(IsRestoringContext)\nexport const IsRestoringProvider = IsRestoringContext.Provider\n", "'use client'\nimport * as React from 'react'\nimport { shouldThrowError } from './utils'\nimport type {\n  DefaultedQueryObserverOptions,\n  Query,\n  QueryKey,\n  QueryObserverResult,\n  UseErrorBoundary,\n} from '@tanstack/query-core'\nimport type { QueryErrorResetBoundaryValue } from './QueryErrorResetBoundary'\n\nexport const ensurePreventErrorBoundaryRetry = <\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryData,\n  TQueryKey extends QueryKey,\n>(\n  options: DefaultedQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >,\n  errorResetBoundary: QueryErrorResetBoundaryValue,\n) => {\n  if (options.suspense || options.useErrorBoundary) {\n    // Prevent retrying failed query if the error boundary has not been reset yet\n    if (!errorResetBoundary.isReset()) {\n      options.retryOnMount = false\n    }\n  }\n}\n\nexport const useClearResetErrorBoundary = (\n  errorResetBoundary: QueryErrorResetBoundaryValue,\n) => {\n  React.useEffect(() => {\n    errorResetBoundary.clearReset()\n  }, [errorResetBoundary])\n}\n\nexport const getHasError = <\n  TData,\n  TError,\n  TQueryFnData,\n  TQueryData,\n  TQueryKey extends QueryKey,\n>({\n  result,\n  errorResetBoundary,\n  useErrorBoundary,\n  query,\n}: {\n  result: QueryObserverResult<TData, TError>\n  errorResetBoundary: QueryErrorResetBoundaryValue\n  useErrorBoundary: UseErrorBoundary<\n    TQueryFnData,\n    TError,\n    TQueryData,\n    TQueryKey\n  >\n  query: Query<TQueryFnData, TError, TQueryData, TQueryKey>\n}) => {\n  return (\n    result.isError &&\n    !errorResetBoundary.isReset() &&\n    !result.isFetching &&\n    shouldThrowError(useErrorBoundary, [result.error, query])\n  )\n}\n", "import type { DefaultedQueryObserverOptions } from '@tanstack/query-core'\nimport type { QueryObserver } from '@tanstack/query-core'\nimport type { QueryErrorResetBoundaryValue } from './QueryErrorResetBoundary'\nimport type { QueryObserverResult } from '@tanstack/query-core'\nimport type { QueryKey } from '@tanstack/query-core'\n\nexport const ensureStaleTime = (\n  defaultedOptions: DefaultedQueryObserverOptions<any, any, any, any, any>,\n) => {\n  if (defaultedOptions.suspense) {\n    // Always set stale time when using suspense to prevent\n    // fetching again when directly mounting after suspending\n    if (typeof defaultedOptions.staleTime !== 'number') {\n      defaultedOptions.staleTime = 1000\n    }\n  }\n}\n\nexport const willFetch = (\n  result: QueryObserverResult<any, any>,\n  isRestoring: boolean,\n) => result.isLoading && result.isFetching && !isRestoring\n\nexport const shouldSuspend = (\n  defaultedOptions:\n    | DefaultedQueryObserverOptions<any, any, any, any, any>\n    | undefined,\n  result: QueryObserverResult<any, any>,\n  isRestoring: boolean,\n) => defaultedOptions?.suspense && willFetch(result, isRestoring)\n\nexport const fetchOptimistic = <\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryData,\n  TQueryKey extends QueryKey,\n>(\n  defaultedOptions: DefaultedQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >,\n  observer: QueryObserver<TQueryFnData, TError, TData, TQueryData, TQueryKey>,\n  errorResetBoundary: QueryErrorResetBoundaryValue,\n) =>\n  observer\n    .fetchOptimistic(defaultedOptions)\n    .then(({ data }) => {\n      defaultedOptions.onSuccess?.(data as TData)\n      defaultedOptions.onSettled?.(data, null)\n    })\n    .catch((error) => {\n      errorResetBoundary.clearReset()\n      defaultedOptions.onError?.(error)\n      defaultedOptions.onSettled?.(undefined, error)\n    })\n", "'use client'\nimport { QueryObserver, parseQueryArgs } from '@tanstack/query-core'\nimport { useBaseQuery } from './useBaseQuery'\nimport type {\n  InitialDataFunction,\n  NonUndefinedGuard,\n  OmitKeyof,\n  QueryFunction,\n  QueryKey,\n} from '@tanstack/query-core'\nimport type {\n  DefinedUseQueryResult,\n  UseQueryOptions,\n  UseQueryResult,\n} from './types'\n\n// HOOK\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: OmitKeyof<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'initialData'\n  > & {\n    initialData:\n      | NonUndefinedGuard<TQueryFnData>\n      | (() => NonUndefinedGuard<TQueryFnData>)\n  },\n): DefinedUseQueryResult<TData, TError>\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: OmitKeyof<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'initialData'\n  > & {\n    initialData?:\n      | undefined\n      | InitialDataFunction<NonUndefinedGuard<TQueryFnData>>\n      | NonUndefinedGuard<TQueryFnData>\n  },\n): UseQueryResult<TData, TError>\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n): UseQueryResult<TData, TError>\n\n/** @deprecated */\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  options?: OmitKeyof<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey' | 'initialData'\n  >,\n): UseQueryResult<TData, TError>\n/** @deprecated */\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  options?: OmitKeyof<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey' | 'initialData'\n  > & { initialData: TQueryFnData | (() => TQueryFnData) },\n): DefinedUseQueryResult<TData, TError>\n/** @deprecated */\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  options?: OmitKeyof<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey'\n  >,\n): UseQueryResult<TData, TError>\n/** @deprecated */\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n  options?: OmitKeyof<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey' | 'queryFn' | 'initialData'\n  > & { initialData?: () => undefined },\n): UseQueryResult<TData, TError>\n/** @deprecated */\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n  options?: OmitKeyof<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey' | 'queryFn' | 'initialData'\n  > & { initialData: TQueryFnData | (() => TQueryFnData) },\n): DefinedUseQueryResult<TData, TError>\n/** @deprecated */\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n  options?: OmitKeyof<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey' | 'queryFn'\n  >,\n): UseQueryResult<TData, TError>\nexport function useQuery<\n  TQueryFnData,\n  TError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  arg1: TQueryKey | UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  arg2?:\n    | QueryFunction<TQueryFnData, TQueryKey>\n    | UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  arg3?: UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n): UseQueryResult<TData, TError> {\n  const parsedOptions = parseQueryArgs(arg1, arg2, arg3)\n  return useBaseQuery(parsedOptions, QueryObserver)\n}\n", "'use client'\nimport * as React from 'react'\n\nimport { notifyManager } from '@tanstack/query-core'\nimport { useSyncExternalStore } from './useSyncExternalStore'\nimport { useQueryErrorResetBoundary } from './QueryErrorResetBoundary'\nimport { useQueryClient } from './QueryClientProvider'\nimport { useIsRestoring } from './isRestoring'\nimport {\n  ensurePreventErrorBoundaryRetry,\n  getHasError,\n  useClearResetErrorBoundary,\n} from './errorBoundaryUtils'\nimport { ensureStaleTime, fetchOptimistic, shouldSuspend } from './suspense'\nimport type { QueryKey, QueryObserver } from '@tanstack/query-core'\nimport type { UseBaseQueryOptions } from './types'\n\nexport function useBaseQuery<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryData,\n  TQueryKey extends QueryKey,\n>(\n  options: UseBaseQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryK<PERSON>\n  >,\n  Observer: typeof QueryObserver,\n) {\n  const queryClient = useQueryClient({ context: options.context })\n  const isRestoring = useIsRestoring()\n  const errorResetBoundary = useQueryErrorResetBoundary()\n  const defaultedOptions = queryClient.defaultQueryOptions(options)\n\n  // Make sure results are optimistically set in fetching state before subscribing or updating options\n  defaultedOptions._optimisticResults = isRestoring\n    ? 'isRestoring'\n    : 'optimistic'\n\n  // Include callbacks in batch renders\n  if (defaultedOptions.onError) {\n    defaultedOptions.onError = notifyManager.batchCalls(\n      defaultedOptions.onError,\n    )\n  }\n\n  if (defaultedOptions.onSuccess) {\n    defaultedOptions.onSuccess = notifyManager.batchCalls(\n      defaultedOptions.onSuccess,\n    )\n  }\n\n  if (defaultedOptions.onSettled) {\n    defaultedOptions.onSettled = notifyManager.batchCalls(\n      defaultedOptions.onSettled,\n    )\n  }\n\n  ensureStaleTime(defaultedOptions)\n  ensurePreventErrorBoundaryRetry(defaultedOptions, errorResetBoundary)\n\n  useClearResetErrorBoundary(errorResetBoundary)\n\n  const [observer] = React.useState(\n    () =>\n      new Observer<TQueryFnData, TError, TData, TQueryData, TQueryKey>(\n        queryClient,\n        defaultedOptions,\n      ),\n  )\n\n  const result = observer.getOptimisticResult(defaultedOptions)\n\n  useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) => {\n        const unsubscribe = isRestoring\n          ? () => undefined\n          : observer.subscribe(notifyManager.batchCalls(onStoreChange))\n\n        // Update result to make sure we did not miss any query updates\n        // between creating the observer and subscribing to it.\n        observer.updateResult()\n\n        return unsubscribe\n      },\n      [observer, isRestoring],\n    ),\n    () => observer.getCurrentResult(),\n    () => observer.getCurrentResult(),\n  )\n\n  React.useEffect(() => {\n    // Do not notify on updates because of changes in the options because\n    // these changes should already be reflected in the optimistic result.\n    observer.setOptions(defaultedOptions, { listeners: false })\n  }, [defaultedOptions, observer])\n\n  // Handle suspense\n  if (shouldSuspend(defaultedOptions, result, isRestoring)) {\n    throw fetchOptimistic(defaultedOptions, observer, errorResetBoundary)\n  }\n\n  // Handle error boundary\n  if (\n    getHasError({\n      result,\n      errorResetBoundary,\n      useErrorBoundary: defaultedOptions.useErrorBoundary,\n      query: observer.getCurrentQuery(),\n    })\n  ) {\n    throw result.error\n  }\n\n  // Handle result property usage tracking\n  return !defaultedOptions.notifyOnChangeProps\n    ? observer.trackResult(result)\n    : result\n}\n", "import { focusManager } from './focusManager'\nimport { onlineManager } from './onlineManager'\nimport { sleep } from './utils'\nimport type { CancelOptions, NetworkMode } from './types'\n\n// TYPES\n\ninterface RetryerConfig<TData = unknown, TError = unknown> {\n  fn: () => TData | Promise<TData>\n  abort?: () => void\n  onError?: (error: TError) => void\n  onSuccess?: (data: TData) => void\n  onFail?: (failureCount: number, error: TError) => void\n  onPause?: () => void\n  onContinue?: () => void\n  retry?: RetryValue<TError>\n  retryDelay?: RetryDelayValue<TError>\n  networkMode: NetworkMode | undefined\n}\n\nexport interface Retryer<TData = unknown> {\n  promise: Promise<TData>\n  cancel: (cancelOptions?: CancelOptions) => void\n  continue: () => Promise<unknown>\n  cancelRetry: () => void\n  continueRetry: () => void\n}\n\nexport type RetryValue<TError> = boolean | number | ShouldRetryFunction<TError>\n\ntype ShouldRetryFunction<TError> = (\n  failureCount: number,\n  error: TError,\n) => boolean\n\nexport type RetryDelayValue<TError> = number | RetryDelayFunction<TError>\n\ntype RetryDelayFunction<TError = unknown> = (\n  failureCount: number,\n  error: TError,\n) => number\n\nfunction defaultRetryDelay(failureCount: number) {\n  return Math.min(1000 * 2 ** failureCount, 30000)\n}\n\nexport function canFetch(networkMode: NetworkMode | undefined): boolean {\n  return (networkMode ?? 'online') === 'online'\n    ? onlineManager.isOnline()\n    : true\n}\n\nexport class CancelledError {\n  revert?: boolean\n  silent?: boolean\n  constructor(options?: CancelOptions) {\n    this.revert = options?.revert\n    this.silent = options?.silent\n  }\n}\n\nexport function isCancelledError(value: any): value is CancelledError {\n  return value instanceof CancelledError\n}\n\nexport function createRetryer<TData = unknown, TError = unknown>(\n  config: RetryerConfig<TData, TError>,\n): Retryer<TData> {\n  let isRetryCancelled = false\n  let failureCount = 0\n  let isResolved = false\n  let continueFn: ((value?: unknown) => boolean) | undefined\n  let promiseResolve: (data: TData) => void\n  let promiseReject: (error: TError) => void\n\n  const promise = new Promise<TData>((outerResolve, outerReject) => {\n    promiseResolve = outerResolve\n    promiseReject = outerReject\n  })\n\n  const cancel = (cancelOptions?: CancelOptions): void => {\n    if (!isResolved) {\n      reject(new CancelledError(cancelOptions))\n\n      config.abort?.()\n    }\n  }\n  const cancelRetry = () => {\n    isRetryCancelled = true\n  }\n\n  const continueRetry = () => {\n    isRetryCancelled = false\n  }\n\n  const shouldPause = () =>\n    !focusManager.isFocused() ||\n    (config.networkMode !== 'always' && !onlineManager.isOnline())\n\n  const resolve = (value: any) => {\n    if (!isResolved) {\n      isResolved = true\n      config.onSuccess?.(value)\n      continueFn?.()\n      promiseResolve(value)\n    }\n  }\n\n  const reject = (value: any) => {\n    if (!isResolved) {\n      isResolved = true\n      config.onError?.(value)\n      continueFn?.()\n      promiseReject(value)\n    }\n  }\n\n  const pause = () => {\n    return new Promise((continueResolve) => {\n      continueFn = (value) => {\n        const canContinue = isResolved || !shouldPause()\n        if (canContinue) {\n          continueResolve(value)\n        }\n        return canContinue\n      }\n      config.onPause?.()\n    }).then(() => {\n      continueFn = undefined\n      if (!isResolved) {\n        config.onContinue?.()\n      }\n    })\n  }\n\n  // Create loop function\n  const run = () => {\n    // Do nothing if already resolved\n    if (isResolved) {\n      return\n    }\n\n    let promiseOrValue: any\n\n    // Execute query\n    try {\n      promiseOrValue = config.fn()\n    } catch (error) {\n      promiseOrValue = Promise.reject(error)\n    }\n\n    Promise.resolve(promiseOrValue)\n      .then(resolve)\n      .catch((error) => {\n        // Stop if the fetch is already resolved\n        if (isResolved) {\n          return\n        }\n\n        // Do we need to retry the request?\n        const retry = config.retry ?? 3\n        const retryDelay = config.retryDelay ?? defaultRetryDelay\n        const delay =\n          typeof retryDelay === 'function'\n            ? retryDelay(failureCount, error)\n            : retryDelay\n        const shouldRetry =\n          retry === true ||\n          (typeof retry === 'number' && failureCount < retry) ||\n          (typeof retry === 'function' && retry(failureCount, error))\n\n        if (isRetryCancelled || !shouldRetry) {\n          // We are done if the query does not need to be retried\n          reject(error)\n          return\n        }\n\n        failureCount++\n\n        // Notify on fail\n        config.onFail?.(failureCount, error)\n\n        // Delay\n        sleep(delay)\n          // Pause if the document is not visible or when the device is offline\n          .then(() => {\n            if (shouldPause()) {\n              return pause()\n            }\n            return\n          })\n          .then(() => {\n            if (isRetryCancelled) {\n              reject(error)\n            } else {\n              run()\n            }\n          })\n      })\n  }\n\n  // Start loop\n  if (canFetch(config.networkMode)) {\n    run()\n  } else {\n    pause().then(run)\n  }\n\n  return {\n    promise,\n    cancel,\n    continue: () => {\n      const didContinue = continueFn?.()\n      return didContinue ? promise : Promise.resolve()\n    },\n    cancelRetry,\n    continueRetry,\n  }\n}\n", "import type { Mutation } from './mutation'\nimport type { Query } from './query'\nimport type {\n  FetchStatus,\n  MutationFunction,\n  MutationKey,\n  MutationOptions,\n  QueryFunction,\n  QueryKey,\n  QueryOptions,\n} from './types'\n\n// TYPES\n\nexport interface QueryFilters {\n  /**\n   * Filter to active queries, inactive queries or all queries\n   */\n  type?: QueryTypeFilter\n  /**\n   * Match query key exactly\n   */\n  exact?: boolean\n  /**\n   * Include queries matching this predicate function\n   */\n  predicate?: (query: Query) => boolean\n  /**\n   * Include queries matching this query key\n   */\n  queryKey?: QueryKey\n  /**\n   * Include or exclude stale queries\n   */\n  stale?: boolean\n  /**\n   * Include queries matching their fetchStatus\n   */\n  fetchStatus?: FetchStatus\n}\n\nexport interface MutationFilters {\n  /**\n   * Match mutation key exactly\n   */\n  exact?: boolean\n  /**\n   * Include mutations matching this predicate function\n   */\n  predicate?: (mutation: Mutation<any, any, any>) => boolean\n  /**\n   * Include mutations matching this mutation key\n   */\n  mutationKey?: Mutation<PERSON>ey\n  /**\n   * Include or exclude fetching mutations\n   */\n  fetching?: boolean\n}\n\nexport type DataUpdateFunction<TInput, TOutput> = (input: TInput) => TOutput\n\nexport type Updater<TInput, TOutput> =\n  | TOutput\n  | DataUpdateFunction<TInput, TOutput>\n\nexport type QueryTypeFilter = 'all' | 'active' | 'inactive'\n\n// UTILS\n\nexport const isServer = typeof window === 'undefined' || 'Deno' in window\n\nexport function noop(): undefined {\n  return undefined\n}\n\nexport function functionalUpdate<TInput, TOutput>(\n  updater: Updater<TInput, TOutput>,\n  input: TInput,\n): TOutput {\n  return typeof updater === 'function'\n    ? (updater as DataUpdateFunction<TInput, TOutput>)(input)\n    : updater\n}\n\nexport function isValidTimeout(value: unknown): value is number {\n  return typeof value === 'number' && value >= 0 && value !== Infinity\n}\n\nexport function difference<T>(array1: T[], array2: T[]): T[] {\n  return array1.filter((x) => !array2.includes(x))\n}\n\nexport function replaceAt<T>(array: T[], index: number, value: T): T[] {\n  const copy = array.slice(0)\n  copy[index] = value\n  return copy\n}\n\nexport function timeUntilStale(updatedAt: number, staleTime?: number): number {\n  return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0)\n}\n\nexport function parseQueryArgs<\n  TOptions extends QueryOptions<any, any, any, TQueryKey>,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  arg1: TQueryKey | TOptions,\n  arg2?: QueryFunction<any, TQueryKey> | TOptions,\n  arg3?: TOptions,\n): TOptions {\n  if (!isQueryKey(arg1)) {\n    return arg1 as TOptions\n  }\n\n  if (typeof arg2 === 'function') {\n    return { ...arg3, queryKey: arg1, queryFn: arg2 } as TOptions\n  }\n\n  return { ...arg2, queryKey: arg1 } as TOptions\n}\n\nexport function parseMutationArgs<\n  TOptions extends MutationOptions<any, any, any, any>,\n>(\n  arg1: MutationKey | MutationFunction<any, any> | TOptions,\n  arg2?: MutationFunction<any, any> | TOptions,\n  arg3?: TOptions,\n): TOptions {\n  if (isQueryKey(arg1)) {\n    if (typeof arg2 === 'function') {\n      return { ...arg3, mutationKey: arg1, mutationFn: arg2 } as TOptions\n    }\n    return { ...arg2, mutationKey: arg1 } as TOptions\n  }\n\n  if (typeof arg1 === 'function') {\n    return { ...arg2, mutationFn: arg1 } as TOptions\n  }\n\n  return { ...arg1 } as TOptions\n}\n\nexport function parseFilterArgs<\n  TFilters extends QueryFilters,\n  TOptions = unknown,\n>(\n  arg1?: QueryKey | TFilters,\n  arg2?: TFilters | TOptions,\n  arg3?: TOptions,\n): [TFilters, TOptions | undefined] {\n  return (\n    isQueryKey(arg1) ? [{ ...arg2, queryKey: arg1 }, arg3] : [arg1 || {}, arg2]\n  ) as [TFilters, TOptions]\n}\n\nexport function parseMutationFilterArgs<\n  TFilters extends MutationFilters,\n  TOptions = unknown,\n>(\n  arg1?: QueryKey | TFilters,\n  arg2?: TFilters | TOptions,\n  arg3?: TOptions,\n): [TFilters, TOptions | undefined] {\n  return (\n    isQueryKey(arg1)\n      ? [{ ...arg2, mutationKey: arg1 }, arg3]\n      : [arg1 || {}, arg2]\n  ) as [TFilters, TOptions]\n}\n\nexport function matchQuery(\n  filters: QueryFilters,\n  query: Query<any, any, any, any>,\n): boolean {\n  const {\n    type = 'all',\n    exact,\n    fetchStatus,\n    predicate,\n    queryKey,\n    stale,\n  } = filters\n\n  if (isQueryKey(queryKey)) {\n    if (exact) {\n      if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n        return false\n      }\n    } else if (!partialMatchKey(query.queryKey, queryKey)) {\n      return false\n    }\n  }\n\n  if (type !== 'all') {\n    const isActive = query.isActive()\n    if (type === 'active' && !isActive) {\n      return false\n    }\n    if (type === 'inactive' && isActive) {\n      return false\n    }\n  }\n\n  if (typeof stale === 'boolean' && query.isStale() !== stale) {\n    return false\n  }\n\n  if (\n    typeof fetchStatus !== 'undefined' &&\n    fetchStatus !== query.state.fetchStatus\n  ) {\n    return false\n  }\n\n  if (predicate && !predicate(query)) {\n    return false\n  }\n\n  return true\n}\n\nexport function matchMutation(\n  filters: MutationFilters,\n  mutation: Mutation<any, any>,\n): boolean {\n  const { exact, fetching, predicate, mutationKey } = filters\n  if (isQueryKey(mutationKey)) {\n    if (!mutation.options.mutationKey) {\n      return false\n    }\n    if (exact) {\n      if (\n        hashQueryKey(mutation.options.mutationKey) !== hashQueryKey(mutationKey)\n      ) {\n        return false\n      }\n    } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n      return false\n    }\n  }\n\n  if (\n    typeof fetching === 'boolean' &&\n    (mutation.state.status === 'loading') !== fetching\n  ) {\n    return false\n  }\n\n  if (predicate && !predicate(mutation)) {\n    return false\n  }\n\n  return true\n}\n\nexport function hashQueryKeyByOptions<TQueryKey extends QueryKey = QueryKey>(\n  queryKey: TQueryKey,\n  options?: QueryOptions<any, any, any, TQueryKey>,\n): string {\n  const hashFn = options?.queryKeyHashFn || hashQueryKey\n  return hashFn(queryKey)\n}\n\n/**\n * Default query keys hash function.\n * Hashes the value into a stable hash.\n */\nexport function hashQueryKey(queryKey: QueryKey): string {\n  return JSON.stringify(queryKey, (_, val) =>\n    isPlainObject(val)\n      ? Object.keys(val)\n          .sort()\n          .reduce((result, key) => {\n            result[key] = val[key]\n            return result\n          }, {} as any)\n      : val,\n  )\n}\n\n/**\n * Checks if key `b` partially matches with key `a`.\n */\nexport function partialMatchKey(a: QueryKey, b: QueryKey): boolean {\n  return partialDeepEqual(a, b)\n}\n\n/**\n * Checks if `b` partially matches with `a`.\n */\nexport function partialDeepEqual(a: any, b: any): boolean {\n  if (a === b) {\n    return true\n  }\n\n  if (typeof a !== typeof b) {\n    return false\n  }\n\n  if (a && b && typeof a === 'object' && typeof b === 'object') {\n    return !Object.keys(b).some((key) => !partialDeepEqual(a[key], b[key]))\n  }\n\n  return false\n}\n\n/**\n * This function returns `a` if `b` is deeply equal.\n * If not, it will replace any deeply equal children of `b` with those of `a`.\n * This can be used for structural sharing between JSON values for example.\n */\nexport function replaceEqualDeep<T>(a: unknown, b: T): T\nexport function replaceEqualDeep(a: any, b: any): any {\n  if (a === b) {\n    return a\n  }\n\n  const array = isPlainArray(a) && isPlainArray(b)\n\n  if (array || (isPlainObject(a) && isPlainObject(b))) {\n    const aSize = array ? a.length : Object.keys(a).length\n    const bItems = array ? b : Object.keys(b)\n    const bSize = bItems.length\n    const copy: any = array ? [] : {}\n\n    let equalItems = 0\n\n    for (let i = 0; i < bSize; i++) {\n      const key = array ? i : bItems[i]\n      copy[key] = replaceEqualDeep(a[key], b[key])\n      if (copy[key] === a[key]) {\n        equalItems++\n      }\n    }\n\n    return aSize === bSize && equalItems === aSize ? a : copy\n  }\n\n  return b\n}\n\n/**\n * Shallow compare objects. Only works with objects that always have the same properties.\n */\nexport function shallowEqualObjects<T>(a: T, b: T): boolean {\n  if ((a && !b) || (b && !a)) {\n    return false\n  }\n\n  for (const key in a) {\n    if (a[key] !== b[key]) {\n      return false\n    }\n  }\n\n  return true\n}\n\nexport function isPlainArray(value: unknown) {\n  return Array.isArray(value) && value.length === Object.keys(value).length\n}\n\n// Copied from: https://github.com/jonschlinkert/is-plain-object\nexport function isPlainObject(o: any): o is Object {\n  if (!hasObjectPrototype(o)) {\n    return false\n  }\n\n  // If has modified constructor\n  const ctor = o.constructor\n  if (typeof ctor === 'undefined') {\n    return true\n  }\n\n  // If has modified prototype\n  const prot = ctor.prototype\n  if (!hasObjectPrototype(prot)) {\n    return false\n  }\n\n  // If constructor does not have an Object-specific method\n  if (!prot.hasOwnProperty('isPrototypeOf')) {\n    return false\n  }\n\n  // Most likely a plain Object\n  return true\n}\n\nfunction hasObjectPrototype(o: any): boolean {\n  return Object.prototype.toString.call(o) === '[object Object]'\n}\n\nexport function isQueryKey(value: unknown): value is QueryKey {\n  return Array.isArray(value)\n}\n\nexport function isError(value: any): value is Error {\n  return value instanceof Error\n}\n\nexport function sleep(timeout: number): Promise<void> {\n  return new Promise((resolve) => {\n    setTimeout(resolve, timeout)\n  })\n}\n\n/**\n * Schedules a microtask.\n * This can be useful to schedule state updates after rendering.\n */\nexport function scheduleMicrotask(callback: () => void) {\n  sleep(0).then(callback)\n}\n\nexport function getAbortController(): AbortController | undefined {\n  if (typeof AbortController === 'function') {\n    return new AbortController()\n  }\n  return\n}\n\nexport function replaceData<\n  TData,\n  TOptions extends QueryOptions<any, any, any, any>,\n>(prevData: TData | undefined, data: TData, options: TOptions): TData {\n  // Use prev data if an isDataEqual function is defined and returns `true`\n  if (options.isDataEqual?.(prevData, data)) {\n    return prevData as TData\n  } else if (typeof options.structuralSharing === 'function') {\n    return options.structuralSharing(prevData, data)\n  } else if (options.structuralSharing !== false) {\n    // Structurally share data between prev and new data if needed\n    return replaceEqualDeep(prevData, data)\n  }\n  return data\n}\n", "'use client'\n// Temporary workaround due to an issue with react-native uSES - https://github.com/TanStack/query/pull/3601\nimport { useSyncExternalStore as uSES } from 'use-sync-external-store/shim/index.js'\n\nexport const useSyncExternalStore = uSES\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim.production.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim.development.js');\n}\n", "'use client'\nimport * as React from 'react'\n\nimport type { QueryClient } from '@tanstack/query-core'\nimport type { ContextOptions } from './types'\n\ndeclare global {\n  interface Window {\n    ReactQueryClientContext?: React.Context<QueryClient | undefined>\n  }\n}\n\nexport const defaultContext = React.createContext<QueryClient | undefined>(\n  undefined,\n)\nconst QueryClientSharingContext = React.createContext<boolean>(false)\n\n// If we are given a context, we will use it.\n// Otherwise, if contextSharing is on, we share the first and at least one\n// instance of the context across the window\n// to ensure that if React Query is used across\n// different bundles or microfrontends they will\n// all use the same **instance** of context, regardless\n// of module scoping.\nfunction getQueryClientContext(\n  context: React.Context<QueryClient | undefined> | undefined,\n  contextSharing: boolean,\n) {\n  if (context) {\n    return context\n  }\n  if (contextSharing && typeof window !== 'undefined') {\n    if (!window.ReactQueryClientContext) {\n      window.ReactQueryClientContext = defaultContext\n    }\n\n    return window.ReactQueryClientContext\n  }\n\n  return defaultContext\n}\n\nexport const useQueryClient = ({ context }: ContextOptions = {}) => {\n  const queryClient = React.useContext(\n    getQueryClientContext(context, React.useContext(QueryClientSharingContext)),\n  )\n\n  if (!queryClient) {\n    throw new Error('No QueryClient set, use QueryClientProvider to set one')\n  }\n\n  return queryClient\n}\n\ntype QueryClientProviderPropsBase = {\n  client: QueryClient\n  children?: React.ReactNode\n}\ntype QueryClientProviderPropsWithContext = ContextOptions & {\n  contextSharing?: never\n} & QueryClientProviderPropsBase\ntype QueryClientProviderPropsWithContextSharing = {\n  context?: never\n  contextSharing?: boolean\n} & QueryClientProviderPropsBase\n\nexport type QueryClientProviderProps =\n  | QueryClientProviderPropsWithContext\n  | QueryClientProviderPropsWithContextSharing\n\nexport const QueryClientProvider = ({\n  client,\n  children,\n  context,\n  contextSharing = false,\n}: QueryClientProviderProps): JSX.Element => {\n  React.useEffect(() => {\n    client.mount()\n    return () => {\n      client.unmount()\n    }\n  }, [client])\n\n  if (process.env.NODE_ENV !== 'production' && contextSharing) {\n    client\n      .getLogger()\n      .error(\n        `The contextSharing option has been deprecated and will be removed in the next major version`,\n      )\n  }\n\n  const Context = getQueryClientContext(context, contextSharing)\n\n  return (\n    <QueryClientSharingContext.Provider value={!context && contextSharing}>\n      <Context.Provider value={client}>{children}</Context.Provider>\n    </QueryClientSharingContext.Provider>\n  )\n}\n", "import { scheduleMicrotask } from './utils'\n\n// TYPES\n\ntype NotifyCallback = () => void\n\ntype NotifyFunction = (callback: () => void) => void\n\ntype BatchNotifyFunction = (callback: () => void) => void\n\ntype BatchCallsCallback<T extends unknown[]> = (...args: T) => void\n\nexport function createNotifyManager() {\n  let queue: NotifyCallback[] = []\n  let transactions = 0\n  let notifyFn: NotifyFunction = (callback) => {\n    callback()\n  }\n  let batchNotifyFn: BatchNotifyFunction = (callback: () => void) => {\n    callback()\n  }\n\n  const batch = <T>(callback: () => T): T => {\n    let result\n    transactions++\n    try {\n      result = callback()\n    } finally {\n      transactions--\n      if (!transactions) {\n        flush()\n      }\n    }\n    return result\n  }\n\n  const schedule = (callback: NotifyCallback): void => {\n    if (transactions) {\n      queue.push(callback)\n    } else {\n      scheduleMicrotask(() => {\n        notifyFn(callback)\n      })\n    }\n  }\n\n  /**\n   * All calls to the wrapped function will be batched.\n   */\n  const batchCalls = <T extends unknown[]>(\n    callback: BatchCallsCallback<T>,\n  ): BatchCallsCallback<T> => {\n    return (...args) => {\n      schedule(() => {\n        callback(...args)\n      })\n    }\n  }\n\n  const flush = (): void => {\n    const originalQueue = queue\n    queue = []\n    if (originalQueue.length) {\n      scheduleMicrotask(() => {\n        batchNotifyFn(() => {\n          originalQueue.forEach((callback) => {\n            notifyFn(callback)\n          })\n        })\n      })\n    }\n  }\n\n  /**\n   * Use this method to set a custom notify function.\n   * This can be used to for example wrap notifications with `React.act` while running tests.\n   */\n  const setNotifyFunction = (fn: NotifyFunction) => {\n    notifyFn = fn\n  }\n\n  /**\n   * Use this method to set a custom function to batch notifications together into a single tick.\n   * By default React Query will use the batch function provided by ReactDOM or React Native.\n   */\n  const setBatchNotifyFunction = (fn: BatchNotifyFunction) => {\n    batchNotifyFn = fn\n  }\n\n  return {\n    batch,\n    batchCalls,\n    schedule,\n    setNotifyFunction,\n    setBatchNotifyFunction,\n  } as const\n}\n\n// SINGLETON\nexport const notifyManager = createNotifyManager()\n"], "names": ["onlineEvents", "OnlineManager", "Subscribable", "constructor", "super", "this", "setup", "onOnline", "isServer", "window", "addEventListener", "listener", "for<PERSON>ach", "event", "removeEventListener", "onSubscribe", "cleanup", "setEventListener", "onUnsubscribe", "_this$cleanup", "hasListeners", "call", "undefined", "_this$cleanup2", "online", "setOnline", "listeners", "_ref", "isOnline", "navigator", "onLine", "onlineManager", "Set", "subscribe", "bind", "identity", "add", "delete", "size", "React", "require", "objectIs", "Object", "is", "x", "y", "useState", "useEffect", "useLayoutEffect", "useDebugValue", "checkIfSnapshotChanged", "inst", "latestGetSnapshot", "getSnapshot", "value", "nextValue", "error", "shim", "document", "createElement", "_useState", "forceUpdate", "exports", "useSyncExternalStore", "FocusManager", "onFocus", "focused", "setFocused", "isFocused", "includes", "visibilityState", "focusManager", "shouldThrowError", "_useErrorBoundary", "params", "QueryObserver", "client", "options", "trackedProps", "selectError", "bindMethods", "setOptions", "remove", "refetch", "<PERSON><PERSON><PERSON><PERSON>", "addObserver", "shouldFetchOnMount", "executeFetch", "updateTimers", "destroy", "shouldFetchOnReconnect", "shouldFetchOn", "refetchOnReconnect", "shouldFetchOnWindowFocus", "refetchOnWindowFocus", "clearStaleTimeout", "clearRefetchInterval", "removeObserver", "notifyOptions", "prevOptions", "prev<PERSON><PERSON><PERSON>", "defaultQueryOptions", "shallowEqualObjects", "get<PERSON><PERSON><PERSON><PERSON>ache", "notify", "type", "query", "observer", "enabled", "Error", "query<PERSON><PERSON>", "updateQuery", "mounted", "shouldFetchOptionally", "updateResult", "staleTime", "updateStaleTimeout", "nextRefetchInterval", "computeRefetchInterval", "currentRefetchInterval", "updateRefetchInterval", "getOptimisticResult", "build", "result", "createResult", "optimisticResult", "keepPreviousData", "placeholderData", "isPlaceholderData", "getCurrentResult", "shouldAssignObserverCurrentProperties", "currentResult", "currentResultOptions", "currentResultState", "state", "trackResult", "trackedResult", "keys", "key", "defineProperty", "configurable", "enumerable", "get", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "refetchPage", "arguments", "length", "fetch", "meta", "fetchOptimistic", "defaultedOptions", "isFetchingOptimistic", "then", "fetchOptions", "_fetchOptions$cancelR", "cancelRefetch", "promise", "throwOnError", "catch", "noop", "isStale", "isValidTimeout", "timeout", "timeUntilStale", "dataUpdatedAt", "staleTimeoutId", "setTimeout", "_this$options$refetch", "refetchInterval", "data", "nextInterval", "refetchIntervalId", "setInterval", "refetchIntervalInBackground", "clearTimeout", "clearInterval", "prevResult", "prevResultState", "prevResultOptions", "query<PERSON>hange", "queryInitialState", "currentQueryInitialState", "prevQueryResult", "previousQueryResult", "errorUpdatedAt", "fetchStatus", "status", "isPreviousData", "_optimisticResults", "fetchOnMount", "fetchOptionally", "canFetch", "networkMode", "isSuccess", "select", "selectFn", "selectResult", "replaceData", "process", "Date", "now", "isFetching", "isLoading", "isError", "isInitialLoading", "failureCount", "fetchFailureCount", "failureReason", "fetchFailureReason", "errorUpdateCount", "isFetched", "dataUpdateCount", "isFetchedAfterMount", "isRefetching", "isLoadingError", "isPaused", "isRefetchError", "nextResult", "defaultNotifyOptions", "cache", "shouldNotifyListeners", "notifyOnChangeProps", "notifyOnChangePropsValue", "includedProps", "useErrorBoundary", "some", "<PERSON><PERSON><PERSON>", "has", "onQueryUpdate", "action", "onSuccess", "manual", "isCancelledError", "onError", "notify<PERSON><PERSON>ger", "batch", "_this$options$onSucce", "_this$options", "_this$options$onSettl", "_this$options2", "onSettled", "_this$options$onError", "_this$options3", "_this$options$onSettl2", "_this$options4", "retryOnMount", "shouldLoadOnMount", "refetchOnMount", "field", "suspense", "isStaleByTime", "createValue", "clear<PERSON><PERSON>t", "isReset", "reset", "QueryErrorResetBoundaryContext", "useQueryErrorResetBoundary", "IsRestoringContext", "useIsRestoring", "Provider", "errorResetBoundary", "useClearResetErrorBoundary", "getHasError", "ensureStaleTime", "shouldSuspend", "isRestoring", "<PERSON><PERSON><PERSON><PERSON>", "useQuery", "arg1", "arg2", "arg3", "Observer", "ensurePreventErrorBoundaryRetry", "queryClient", "onStoreChange", "unsubscribe", "batchCalls", "useBaseQuery", "defaultRetryDelay", "Math", "min", "CancelledError", "revert", "silent", "createRetryer", "config", "continueFn", "promiseResolve", "promiseReject", "isRetryCancelled", "isResolved", "Promise", "outerResolve", "outerReject", "shouldP<PERSON>e", "resolve", "reject", "pause", "continueResolve", "canContinue", "onPause", "onContinue", "run", "promiseOrValue", "fn", "_config$retry", "_config$retryDelay", "retry", "retry<PERSON><PERSON><PERSON>", "delay", "shouldRetry", "onFail", "sleep", "cancel", "cancelOptions", "abort", "continue", "cancelRetry", "continueRetry", "functionalUpdate", "updater", "input", "Infinity", "updatedAt", "max", "parse<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "is<PERSON>uery<PERSON>ey", "queryFn", "parseMutationArgs", "<PERSON><PERSON><PERSON>", "mutationFn", "parseFilter<PERSON><PERSON>s", "matchQuery", "filters", "exact", "predicate", "stale", "queryHash", "hashQueryKeyByOptions", "partialMatchKey", "isActive", "matchMutation", "mutation", "fetching", "hashQuery<PERSON>ey", "queryKeyHashFn", "JSON", "stringify", "_", "val", "isPlainObject", "sort", "reduce", "a", "b", "partialDeepEqual", "replaceEqualDeep", "array", "is<PERSON><PERSON>A<PERSON>y", "aSize", "bItems", "bSize", "copy", "equalItems", "i", "Array", "isArray", "o", "hasObjectPrototype", "ctor", "prot", "prototype", "hasOwnProperty", "toString", "scheduleMicrotask", "callback", "getAbortController", "AbortController", "prevData", "isDataEqual", "structuralSharing", "useSyncExternalStore$1", "module", "defaultContext", "QueryClientSharingContext", "getQueryClientContext", "context", "contextSharing", "ReactQueryClientContext", "useQueryClient", "queue", "transactions", "notifyFn", "batchNotifyFn", "schedule", "push", "flush", "originalQueue", "_len", "args", "_key", "setNotifyFunction", "setBatchNotifyFunction", "createNotifyManager"], "sourceRoot": ""}