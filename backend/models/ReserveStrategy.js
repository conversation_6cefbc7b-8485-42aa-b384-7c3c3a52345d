const mongoose = require('mongoose');

const reserveRuleSchema = new mongoose.Schema({
  // Rule type: percentage, fixed_amount, tiered
  type: {
    type: String,
    required: true,
    enum: ['percentage', 'fixed_amount', 'tiered'],
    default: 'percentage'
  },
  
  // For percentage type
  percentage: {
    type: Number,
    min: 0,
    max: 100,
    required: function() {
      return this.type === 'percentage';
    }
  },
  
  // For fixed amount type
  fixedAmount: {
    type: Number,
    min: 0,
    required: function() {
      return this.type === 'fixed_amount';
    }
  },
  
  // For tiered type
  tiers: [{
    minAmount: { type: Number, required: true },
    maxAmount: { type: Number },
    percentage: { type: Number, min: 0, max: 100 },
    fixedAmount: { type: Number, min: 0 }
  }],
  
  // Minimum reserve amount to maintain
  minReserve: {
    type: Number,
    min: 0,
    default: 0
  },
  
  // Maximum reserve amount cap
  maxReserve: {
    type: Number,
    min: 0
  },
  
  // Priority of this rule (lower number = higher priority)
  priority: {
    type: Number,
    default: 0
  },
  
  // Whether this rule is active
  isActive: {
    type: Boolean,
    default: true
  },
  
  // Description of the rule
  description: {
    type: String,
    trim: true
  },
  
  // Tags for categorization
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }]
}, { _id: false, timestamps: false });

const reserveStrategySchema = new mongoose.Schema({
  // Strategy name
  name: {
    type: String,
    required: true,
    trim: true,
    unique: true
  },
  
  // Strategy description
  description: {
    type: String,
    trim: true
  },
  
  // Rules for this strategy
  rules: [reserveRuleSchema],
  
  // Whether this is the default strategy
  isDefault: {
    type: Boolean,
    default: false
  },
  
  // Status of the strategy
  status: {
    type: String,
    enum: ['active', 'inactive', 'draft'],
    default: 'draft'
  },
  
  // Effective date range
  effectiveFrom: {
    type: Date,
    default: Date.now
  },
  effectiveTo: {
    type: Date
  },
  
  // Audit trail
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  // Metadata
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  
  // Additional configuration
  config: {
    // Auto-replenish settings
    autoReplenish: {
      enabled: {
        type: Boolean,
        default: false
      },
      threshold: {
        type: Number,
        min: 0,
        default: 0
      },
      amount: {
        type: Number,
        min: 0
      },
      sourceAccount: {
        type: String
      }
    },
    
    // Notification settings
    notifications: {
      lowBalance: {
        enabled: {
          type: Boolean,
          default: true
        },
        threshold: {
          type: Number,
          min: 0,
          default: 0
        },
        recipients: [{
          type: String,
          trim: true
        }]
      },
      
      // Add more notification types as needed
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
reserveStrategySchema.index({ name: 1 }, { unique: true });
reserveStrategySchema.index({ status: 1 });
reserveStrategySchema.index({ isDefault: 1 });
reserveStrategySchema.index({ 'config.autoReplenish.enabled': 1 });

// Pre-save hook to ensure only one default strategy
reserveStrategySchema.pre('save', async function(next) {
  if (this.isDefault) {
    try {
      await this.constructor.updateMany(
        { _id: { $ne: this._id }, isDefault: true },
        { $set: { isDefault: false } }
      );
    } catch (err) {
      return next(err);
    }
  }
  next();
});

/**
 * Calculate the reserve amount based on the transaction amount and strategy rules
 * @param {Number} amount - The transaction amount
 * @param {Object} options - Additional options
 * @returns {Object} - Reserve details
 */
reserveStrategySchema.methods.calculateReserve = function(amount, options = {}) {
  // Default result
  const result = {
    reserveAmount: 0,
    ruleApplied: null,
    message: 'No applicable reserve rule found'
  };
  
  // If strategy is not active, return zero reserve
  if (this.status !== 'active') {
    result.message = 'Reserve strategy is not active';
    return result;
  }
  
  // Check effective date range
  const now = options.effectiveDate || new Date();
  if (this.effectiveFrom && new Date(this.effectiveFrom) > now) {
    result.message = 'Reserve strategy is not yet effective';
    return result;
  }
  
  if (this.effectiveTo && new Date(this.effectiveTo) < now) {
    result.message = 'Reserve strategy has expired';
    return result;
  }
  
  // Sort rules by priority (ascending) and process them
  const sortedRules = [...this.rules]
    .filter(rule => rule.isActive)
    .sort((a, b) => a.priority - b.priority);
  
  // Find the first applicable rule
  for (const rule of sortedRules) {
    let reserveAmount = 0;
    let ruleApplied = false;
    
    switch (rule.type) {
      case 'percentage':
        reserveAmount = (amount * rule.percentage) / 100;
        ruleApplied = true;
        break;
        
      case 'fixed_amount':
        reserveAmount = rule.fixedAmount;
        ruleApplied = true;
        break;
        
      case 'tiered':
        if (rule.tiers && rule.tiers.length > 0) {
          // Sort tiers by minAmount ascending
          const sortedTiers = [...rule.tiers].sort((a, b) => a.minAmount - b.minAmount);
          
          // Find the first matching tier
          for (const tier of sortedTiers) {
            if (amount >= tier.minAmount && (!tier.maxAmount || amount <= tier.maxAmount)) {
              if (tier.percentage !== undefined) {
                reserveAmount = (amount * tier.percentage) / 100;
              } else if (tier.fixedAmount !== undefined) {
                reserveAmount = tier.fixedAmount;
              }
              ruleApplied = true;
              break;
            }
          }
        }
        break;
    }
    
    // If we found a matching rule, apply minimum/maximum reserve constraints
    if (ruleApplied) {
      // Apply minimum reserve
      if (rule.minReserve !== undefined && reserveAmount < rule.minReserve) {
        reserveAmount = rule.minReserve;
      }
      
      // Apply maximum reserve
      if (rule.maxReserve !== undefined && reserveAmount > rule.maxReserve) {
        reserveAmount = rule.maxReserve;
      }
      
      // Update result and break the loop
      result.reserveAmount = reserveAmount;
      result.ruleApplied = {
        type: rule.type,
        percentage: rule.percentage,
        fixedAmount: rule.fixedAmount,
        minReserve: rule.minReserve,
        maxReserve: rule.maxReserve,
        description: rule.description
      };
      result.message = 'Reserve calculated successfully';
      break;
    }
  }
  
  return result;
};

/**
 * Check if the reserve amount is below the threshold
 * @param {Number} currentReserve - Current reserve amount
 * @returns {Object} - Status and details
 */
reserveStrategySchema.methods.checkReserveStatus = function(currentReserve) {
  const result = {
    isBelowThreshold: false,
    threshold: null,
    currentReserve: currentReserve || 0,
    message: 'Reserve is above threshold'
  };
  
  // Find the first rule with a threshold
  const ruleWithThreshold = this.rules
    .filter(rule => rule.isActive && rule.minReserve !== undefined)
    .sort((a, b) => a.priority - b.priority)[0];
  
  if (ruleWithThreshold && ruleWithThreshold.minReserve !== undefined) {
    result.threshold = ruleWithThreshold.minReserve;
    result.isBelowThreshold = currentReserve < ruleWithThreshold.minReserve;
    
    if (result.isBelowThreshold) {
      result.message = `Reserve is below threshold of ${ruleWithThreshold.minReserve}`;
    }
  }
  
  return result;
};

module.exports = mongoose.model('ReserveStrategy', reserveStrategySchema);
