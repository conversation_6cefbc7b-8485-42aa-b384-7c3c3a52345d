import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  Chip,
  Divider,
  Grid,
  IconButton,
  LinearProgress,
  Paper,
  Tab,
  Tabs,
  Typography,
  useTheme,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  Star as DefaultIcon,
  StarBorder as NotDefaultIcon,
  Timeline as TimelineIcon,
  Info as InfoIcon,
  Settings as SettingsIcon,
  Description as DescriptionIcon,
} from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import { format } from 'date-fns';
import PageHeader from '../../../components/layout/PageHeader';
import api from '../../../services/api';
import ReserveRuleList from '../components/ReserveRuleList';
import ReserveStrategyHistory from '../components/ReserveStrategyHistory';
import ReserveStrategySummary from '../components/ReserveStrategySummary';

// Tab panel component
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`strategy-tabpanel-${index}`}
      aria-labelledby={`strategy-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

// Tab props for accessibility
function a11yProps(index) {
  return {
    id: `strategy-tab-${index}`,
    'aria-controls': `strategy-tabpanel-${index}`,
  };
}

const ReserveStrategyDetailPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  const theme = useTheme();
  
  // State
  const [strategy, setStrategy] = useState(null);
  const [loading, setLoading] = useState(true);
  const [tabValue, setTabValue] = useState(0);
  const [deleting, setDeleting] = useState(false);
  const [settingDefault, setSettingDefault] = useState(false);
  
  // Fetch strategy details
  const fetchStrategy = async () => {
    try {
      setLoading(true);
      const response = await api.get(`/reserve-strategies/${id}`);
      setStrategy(response.data);
    } catch (error) {
      console.error('Error fetching reserve strategy:', error);
      enqueueSnackbar('Failed to load reserve strategy', { variant: 'error' });
      navigate('/reserve-strategies');
    } finally {
      setLoading(false);
    }
  };
  
  // Initial load
  useEffect(() => {
    if (id) {
      fetchStrategy();
    }
  }, [id]);
  
  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };
  
  // Handle set as default
  const handleSetAsDefault = async () => {
    if (!strategy) return;
    
    try {
      setSettingDefault(true);
      await api.patch(`/reserve-strategies/${strategy._id}/set-default`);
      enqueueSnackbar('Default reserve strategy updated', { variant: 'success' });
      fetchStrategy(); // Refresh to update the default status
    } catch (error) {
      console.error('Error setting default strategy:', error);
      enqueueSnackbar(
        error.response?.data?.message || 'Failed to set default strategy',
        { variant: 'error' }
      );
    } finally {
      setSettingDefault(false);
    }
  };
  
  // Handle delete
  const handleDelete = async () => {
    if (!strategy) return;
    
    if (!window.confirm(`Are you sure you want to delete "${strategy.name}"?`)) {
      return;
    }
    
    try {
      setDeleting(true);
      await api.delete(`/reserve-strategies/${strategy._id}`);
      enqueueSnackbar('Reserve strategy deleted', { variant: 'success' });
      navigate('/reserve-strategies');
    } catch (error) {
      console.error('Error deleting strategy:', error);
      enqueueSnackbar(
        error.response?.data?.message || 'Failed to delete strategy',
        { variant: 'error' }
      );
    } finally {
      setDeleting(false);
    }
  };
  
  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return format(new Date(dateString), 'MMM d, yyyy');
  };
  
  // Loading state
  if (loading && !strategy) {
    return (
      <Box sx={{ p: 3 }}>
        <LinearProgress />
      </Box>
    );
  }
  
  // Not found state
  if (!loading && !strategy) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h6" color="textSecondary" gutterBottom>
          Reserve strategy not found
        </Typography>
        <Button
          variant="outlined"
          color="primary"
          onClick={() => navigate('/reserve-strategies')}
          startIcon={<ArrowBackIcon />}
        >
          Back to Strategies
        </Button>
      </Box>
    );
  }
  
  return (
    <Box>
      <PageHeader
        title={strategy.name}
        subtitle={strategy.description || 'Reserve Strategy Details'}
        breadcrumbs={[
          { label: 'Reserve Strategies', to: '/reserve-strategies' },
          { label: strategy.name },
        ]}
        action={
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="outlined"
              startIcon={<ArrowBackIcon />}
              onClick={() => navigate('/reserve-strategies')}
              disabled={deleting || settingDefault}
            >
              Back
            </Button>
            
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={fetchStrategy}
              disabled={loading || deleting || settingDefault}
            >
              Refresh
            </Button>
            
            <Button
              variant="outlined"
              color="primary"
              startIcon={
                strategy.isDefault ? <DefaultIcon /> : <NotDefaultIcon />
              }
              onClick={handleSetAsDefault}
              disabled={strategy.isDefault || settingDefault || deleting}
              sx={{ minWidth: 150 }}
            >
              {strategy.isDefault ? 'Default' : 'Set as Default'}
            </Button>
            
            <Button
              variant="contained"
              color="primary"
              startIcon={<EditIcon />}
              onClick={() => navigate(`/reserve-strategies/${id}/edit`)}
              disabled={deleting || settingDefault}
            >
              Edit
            </Button>
          </Box>
        }
      />
      
      {/* Status and Metadata */}
      <Box sx={{ mb: 3 }}>
        <Paper variant="outlined" sx={{ p: 2 }}>
          <Grid container spacing={2}>
            <Grid item xs={12} md={4}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <InfoIcon color="action" sx={{ mr: 1 }} />
                <Typography variant="subtitle2" color="textSecondary">
                  Status
                </Typography>
              </Box>
              <Box>
                <Chip
                  label={strategy.status.charAt(0).toUpperCase() + strategy.status.slice(1)}
                  color={
                    strategy.status === 'active' 
                      ? 'success' 
                      : strategy.status === 'inactive' 
                        ? 'error' 
                        : 'warning'
                  }
                  variant="outlined"
                  size="small"
                />
                {strategy.isDefault && (
                  <Chip
                    label="Default"
                    color="primary"
                    size="small"
                    icon={<DefaultIcon fontSize="small" />}
                    sx={{ ml: 1 }}
                  />
                )}
              </Box>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <TimelineIcon color="action" sx={{ mr: 1 }} />
                <Typography variant="subtitle2" color="textSecondary">
                  Effective Period
                </Typography>
              </Box>
              <Typography variant="body2">
                {strategy.effectiveFrom 
                  ? `${formatDate(strategy.effectiveFrom)} - ${strategy.effectiveTo ? formatDate(strategy.effectiveTo) : 'No end date'}`
                  : 'Not specified'}
              </Typography>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <DescriptionIcon color="action" sx={{ mr: 1 }} />
                <Typography variant="subtitle2" color="textSecondary">
                  Created
                </Typography>
              </Box>
              <Typography variant="body2">
                {formatDate(strategy.createdAt)} by {strategy.createdBy?.name || 'System'}
              </Typography>
              {strategy.updatedAt && (
                <Typography variant="caption" color="textSecondary">
                  Last updated: {formatDate(strategy.updatedAt)}
                </Typography>
              )}
            </Grid>
            
            {strategy.tags && strategy.tags.length > 0 && (
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <SettingsIcon color="action" sx={{ mr: 1 }} />
                  <Typography variant="subtitle2" color="textSecondary">
                    Tags
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {strategy.tags.map((tag) => (
                    <Chip 
                      key={tag} 
                      label={tag} 
                      size="small" 
                      variant="outlined"
                    />
                  ))}
                </Box>
              </Grid>
            )}
          </Grid>
        </Paper>
      </Box>
      
      {/* Tabs */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs 
            value={tabValue} 
            onChange={handleTabChange}
            aria-label="strategy tabs"
            variant="scrollable"
            scrollButtons="auto"
          >
            <Tab label="Overview" {...a11yProps(0)} />
            <Tab label="Rules" {...a11yProps(1)} />
            <Tab label="History" {...a11yProps(2)} />
            <Tab label="Settings" {...a11yProps(3)} />
          </Tabs>
        </Box>
        
        {/* Tab Content */}
        <TabPanel value={tabValue} index={0}>
          <ReserveStrategySummary strategy={strategy} />
        </TabPanel>
        
        <TabPanel value={tabValue} index={1}>
          <ReserveRuleList 
            rules={strategy.rules || []} 
            readOnly={true}
          />
        </TabPanel>
        
        <TabPanel value={tabValue} index={2}>
          <ReserveStrategyHistory strategyId={strategy._id} />
        </TabPanel>
        
        <TabPanel value={tabValue} index={3}>
          <Box sx={{ maxWidth: 800 }}>
            <Typography variant="h6" gutterBottom>
              Auto-Replenish Settings
            </Typography>
            
            {strategy.config?.autoReplenish?.enabled ? (
              <Box>
                <Typography variant="body1" gutterBottom>
                  Auto-replenish is <strong>enabled</strong> for this strategy.
                </Typography>
                <Box sx={{ mt: 2, pl: 2 }}>
                  <Typography variant="body2">
                    <strong>Threshold:</strong> ${strategy.config.autoReplenish.threshold}
                  </Typography>
                  <Typography variant="body2">
                    <strong>Replenish Amount:</strong> ${strategy.config.autoReplenish.amount}
                  </Typography>
                  <Typography variant="body2">
                    <strong>Source Account:</strong> {strategy.config.autoReplenish.sourceAccount || 'Not specified'}
                  </Typography>
                </Box>
              </Box>
            ) : (
              <Typography variant="body1">
                Auto-replenish is <strong>disabled</strong> for this strategy.
              </Typography>
            )}
            
            <Divider sx={{ my: 3 }} />
            
            <Typography variant="h6" gutterBottom>
              Notification Settings
            </Typography>
            
            {strategy.config?.notifications?.lowBalance?.enabled ? (
              <Box>
                <Typography variant="body1" gutterBottom>
                  Low balance notifications are <strong>enabled</strong>.
                </Typography>
                <Box sx={{ mt: 2, pl: 2 }}>
                  <Typography variant="body2">
                    <strong>Threshold:</strong> ${strategy.config.notifications.lowBalance.threshold}
                  </Typography>
                  {strategy.config.notifications.lowBalance.recipients?.length > 0 ? (
                    <Box sx={{ mt: 1 }}>
                      <Typography variant="body2">
                        <strong>Recipients:</strong>
                      </Typography>
                      <Box component="ul" sx={{ pl: 2, mt: 0.5, mb: 0 }}>
                        {strategy.config.notifications.lowBalance.recipients.map((email, idx) => (
                          <li key={idx}>
                            <Typography variant="body2">{email}</Typography>
                          </li>
                        ))}
                      </Box>
                    </Box>
                  ) : (
                    <Typography variant="body2">
                      <strong>Recipients:</strong> None configured
                    </Typography>
                  )}
                </Box>
              </Box>
            ) : (
              <Typography variant="body1">
                Low balance notifications are <strong>disabled</strong>.
              </Typography>
            )}
            
            <Divider sx={{ my: 3 }} />
            
            <Box>
              <Typography variant="h6" gutterBottom>
                Danger Zone
              </Typography>
              <Paper variant="outlined" sx={{ p: 2, bgcolor: 'rgba(244, 67, 54, 0.05)' }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Box>
                    <Typography variant="subtitle1" gutterBottom>
                      Delete this reserve strategy
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Once you delete a reserve strategy, there is no going back. Please be certain.
                    </Typography>
                  </Box>
                  <Button
                    variant="outlined"
                    color="error"
                    startIcon={<DeleteIcon />}
                    onClick={handleDelete}
                    disabled={deleting || settingDefault}
                  >
                    {deleting ? 'Deleting...' : 'Delete Strategy'}
                  </Button>
                </Box>
              </Paper>
            </Box>
          </Box>
        </TabPanel>
      </Card>
    </Box>
  );
};

export default ReserveStrategyDetailPage;
