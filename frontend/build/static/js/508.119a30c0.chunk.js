"use strict";(self.webpackChunkpayment_gateway_frontend=self.webpackChunkpayment_gateway_frontend||[]).push([[508],{382:(e,t,a)=>{a.d(t,{A0:()=>i,BF:()=>d,Hj:()=>l,XI:()=>o,nA:()=>u,nd:()=>c});var s=a(5043),r=a(3009),n=a(579);const o=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("div",{className:"w-full overflow-auto",children:(0,n.jsx)("table",{ref:t,className:(0,r.cn)("w-full caption-bottom text-sm",a),...s})})});o.displayName="Table";const i=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("thead",{ref:t,className:(0,r.cn)("[&_tr]:border-b",a),...s})});i.displayName="TableHeader";const d=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("tbody",{ref:t,className:(0,r.cn)("[&_tr:last-child]:border-0",a),...s})});d.displayName="TableBody";s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("tfoot",{ref:t,className:(0,r.cn)("bg-primary font-medium text-primary-foreground",a),...s})}).displayName="TableFooter";const l=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("tr",{ref:t,className:(0,r.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",a),...s})});l.displayName="TableRow";const c=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("th",{ref:t,className:(0,r.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",a),...s})});c.displayName="TableHead";const u=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("td",{ref:t,className:(0,r.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",a),...s})});u.displayName="TableCell";s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("caption",{ref:t,className:(0,r.cn)("mt-4 text-sm text-muted-foreground",a),...s})}).displayName="TableCaption"},492:(e,t,a)=>{a.d(t,{Cf:()=>f,Es:()=>p,L3:()=>g,c7:()=>m,lG:()=>d,rr:()=>h,zM:()=>l});var s=a(5043),r=a(5179),n=a(1172),o=a(3009),i=a(579);const d=r.bL,l=r.l9,c=e=>{let{className:t,children:a,...s}=e;const{container:n,forceMount:d,...l}=s,c={container:n,forceMount:d};return(0,i.jsx)(r.ZL,{...c,children:(0,i.jsx)("div",{className:(0,o.cn)("fixed inset-0 z-50 flex items-start justify-center sm:items-center",t),children:a})})};c.displayName=r.ZL.displayName;const u=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,i.jsx)(r.hJ,{ref:t,className:(0,o.cn)("fixed inset-0 z-50 bg-black/50 backdrop-blur-sm transition-all duration-100 data-[state=closed]:animate-out data-[state=closed]:fade-out data-[state=open]:fade-in",a),...s})});u.displayName=r.hJ.displayName;const f=s.forwardRef((e,t)=>{let{className:a,children:s,...d}=e;return(0,i.jsxs)(c,{children:[(0,i.jsx)(u,{}),(0,i.jsxs)(r.UC,{ref:t,className:(0,o.cn)("fixed z-50 grid w-full gap-4 rounded-b-lg bg-white p-6 animate-in data-[state=open]:fade-in-90 data-[state=open]:slide-in-from-bottom-10 sm:max-w-lg sm:rounded-lg sm:zoom-in-90 data-[state=open]:sm:slide-in-from-bottom-0","dark:bg-gray-900",a),...d,children:[s,(0,i.jsxs)(r.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-gray-100 dark:focus:ring-gray-400 dark:focus:ring-offset-gray-900 dark:data-[state=open]:bg-gray-800",children:[(0,i.jsx)(n.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});f.displayName=r.UC.displayName;const m=e=>{let{className:t,...a}=e;return(0,i.jsx)("div",{className:(0,o.cn)("flex flex-col space-y-2 text-center sm:text-left",t),...a})};m.displayName="DialogHeader";const p=e=>{let{className:t,...a}=e;return(0,i.jsx)("div",{className:(0,o.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...a})};p.displayName="DialogFooter";const g=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,i.jsx)(r.hE,{ref:t,className:(0,o.cn)("text-lg font-semibold text-gray-900","dark:text-gray-50",a),...s})});g.displayName=r.hE.displayName;const h=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,i.jsx)(r.VY,{ref:t,className:(0,o.cn)("text-sm text-gray-500","dark:text-gray-400",a),...s})});h.displayName=r.VY.displayName},2248:(e,t,a)=>{a.d(t,{J:()=>d});var s=a(5043),r=a(917),n=a(3009),o=a(579);const i=(0,r.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,o.jsx)("label",{ref:t,className:(0,n.cn)(i(),a),...s})});d.displayName="Label"},5179:(e,t,a)=>{a.d(t,{UC:()=>ae,VY:()=>re,ZL:()=>ee,bL:()=>K,bm:()=>ne,hE:()=>se,hJ:()=>te,l9:()=>Q});var s=a(5043),r=a(858),n=a(2814),o=a(1862),i=a(4490),d=a(3642),l=a(1184),c=a(276),u=a(3321),f=a(2894),m=a(7920),p=a(6590),g=a(4064),h=a(5754),x=a(6851),y=a(579),b="Dialog",[j,N]=(0,o.A)(b),[v,w]=j(b),D=e=>{const{__scopeDialog:t,children:a,open:r,defaultOpen:n,onOpenChange:o,modal:l=!0}=e,c=s.useRef(null),u=s.useRef(null),[f,m]=(0,d.i)({prop:r,defaultProp:n??!1,onChange:o,caller:b});return(0,y.jsx)(v,{scope:t,triggerRef:c,contentRef:u,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:f,onOpenChange:m,onOpenToggle:s.useCallback(()=>m(e=>!e),[m]),modal:l,children:a})};D.displayName=b;var A="DialogTrigger",C=s.forwardRef((e,t)=>{const{__scopeDialog:a,...s}=e,o=w(A,a),i=(0,n.s)(t,o.triggerRef);return(0,y.jsx)(m.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":G(o.open),...s,ref:i,onClick:(0,r.m)(e.onClick,o.onOpenToggle)})});C.displayName=A;var R="DialogPortal",[_,I]=j(R,{forceMount:void 0}),T=e=>{const{__scopeDialog:t,forceMount:a,children:r,container:n}=e,o=w(R,t);return(0,y.jsx)(_,{scope:t,forceMount:a,children:s.Children.map(r,e=>(0,y.jsx)(f.C,{present:a||o.open,children:(0,y.jsx)(u.Z,{asChild:!0,container:n,children:e})}))})};T.displayName=R;var S="DialogOverlay",E=s.forwardRef((e,t)=>{const a=I(S,e.__scopeDialog),{forceMount:s=a.forceMount,...r}=e,n=w(S,e.__scopeDialog);return n.modal?(0,y.jsx)(f.C,{present:s||n.open,children:(0,y.jsx)(O,{...r,ref:t})}):null});E.displayName=S;var M=(0,x.TL)("DialogOverlay.RemoveScroll"),O=s.forwardRef((e,t)=>{const{__scopeDialog:a,...s}=e,r=w(S,a);return(0,y.jsx)(g.A,{as:M,allowPinchZoom:!0,shards:[r.contentRef],children:(0,y.jsx)(m.sG.div,{"data-state":G(r.open),...s,ref:t,style:{pointerEvents:"auto",...s.style}})})}),k="DialogContent",F=s.forwardRef((e,t)=>{const a=I(k,e.__scopeDialog),{forceMount:s=a.forceMount,...r}=e,n=w(k,e.__scopeDialog);return(0,y.jsx)(f.C,{present:s||n.open,children:n.modal?(0,y.jsx)($,{...r,ref:t}):(0,y.jsx)(U,{...r,ref:t})})});F.displayName=k;var $=s.forwardRef((e,t)=>{const a=w(k,e.__scopeDialog),o=s.useRef(null),i=(0,n.s)(t,a.contentRef,o);return s.useEffect(()=>{const e=o.current;if(e)return(0,h.Eq)(e)},[]),(0,y.jsx)(z,{...e,ref:i,trapFocus:a.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,r.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),a.triggerRef.current?.focus()}),onPointerDownOutside:(0,r.m)(e.onPointerDownOutside,e=>{const t=e.detail.originalEvent,a=0===t.button&&!0===t.ctrlKey;(2===t.button||a)&&e.preventDefault()}),onFocusOutside:(0,r.m)(e.onFocusOutside,e=>e.preventDefault())})}),U=s.forwardRef((e,t)=>{const a=w(k,e.__scopeDialog),r=s.useRef(!1),n=s.useRef(!1);return(0,y.jsx)(z,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(r.current||a.triggerRef.current?.focus(),t.preventDefault()),r.current=!1,n.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(r.current=!0,"pointerdown"===t.detail.originalEvent.type&&(n.current=!0));const s=t.target,o=a.triggerRef.current?.contains(s);o&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&n.current&&t.preventDefault()}})}),z=s.forwardRef((e,t)=>{const{__scopeDialog:a,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:i,...d}=e,u=w(k,a),f=s.useRef(null),m=(0,n.s)(t,f);return(0,p.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(c.n,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:i,children:(0,y.jsx)(l.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":G(u.open),...d,ref:m,onDismiss:()=>u.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(X,{titleId:u.titleId}),(0,y.jsx)(Y,{contentRef:f,descriptionId:u.descriptionId})]})]})}),L="DialogTitle",P=s.forwardRef((e,t)=>{const{__scopeDialog:a,...s}=e,r=w(L,a);return(0,y.jsx)(m.sG.h2,{id:r.titleId,...s,ref:t})});P.displayName=L;var q="DialogDescription",J=s.forwardRef((e,t)=>{const{__scopeDialog:a,...s}=e,r=w(q,a);return(0,y.jsx)(m.sG.p,{id:r.descriptionId,...s,ref:t})});J.displayName=q;var B="DialogClose",V=s.forwardRef((e,t)=>{const{__scopeDialog:a,...s}=e,n=w(B,a);return(0,y.jsx)(m.sG.button,{type:"button",...s,ref:t,onClick:(0,r.m)(e.onClick,()=>n.onOpenChange(!1))})});function G(e){return e?"open":"closed"}V.displayName=B;var H="DialogTitleWarning",[Z,W]=(0,o.q)(H,{contentName:k,titleName:L,docsSlug:"dialog"}),X=e=>{let{titleId:t}=e;const a=W(H),r=`\`${a.contentName}\` requires a \`${a.titleName}\` for the component to be accessible for screen reader users.\n\nIf you want to hide the \`${a.titleName}\`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/${a.docsSlug}`;return s.useEffect(()=>{if(t){document.getElementById(t)||console.error(r)}},[r,t]),null},Y=e=>{let{contentRef:t,descriptionId:a}=e;const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${W("DialogDescriptionWarning").contentName}}.`;return s.useEffect(()=>{const e=t.current?.getAttribute("aria-describedby");if(a&&e){document.getElementById(a)||console.warn(r)}},[r,t,a]),null},K=D,Q=C,ee=T,te=E,ae=F,se=P,re=J,ne=V},5320:(e,t,a)=>{a.d(t,{bq:()=>u,eb:()=>m,gC:()=>f,l6:()=>l,yv:()=>c});var s=a(5043),r=a(2500),n=a(1024),o=a(1285),i=a(3009),d=a(579);const l=r.bL,c=(r.YJ,r.WT),u=s.forwardRef((e,t)=>{let{className:a,children:s,...n}=e;return(0,d.jsxs)(r.l9,{ref:t,className:(0,i.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),...n,children:[s,(0,d.jsx)(r.In,{asChild:!0,children:(0,d.jsx)(o.A,{className:"h-4 w-4 opacity-50"})})]})});u.displayName=r.l9.displayName;const f=s.forwardRef((e,t)=>{let{className:a,children:s,position:n="popper",...o}=e;return(0,d.jsx)(r.ZL,{children:(0,d.jsx)(r.UC,{ref:t,className:(0,i.cn)("relative z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:n,...o,children:(0,d.jsx)(r.LM,{className:(0,i.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s})})})});f.displayName=r.UC.displayName;s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,d.jsx)(r.JU,{ref:t,className:(0,i.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...s})}).displayName=r.JU.displayName;const m=s.forwardRef((e,t)=>{let{className:a,children:s,...o}=e;return(0,d.jsxs)(r.q7,{ref:t,className:(0,i.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...o,children:[(0,d.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,d.jsx)(r.VF,{children:(0,d.jsx)(n.A,{className:"h-4 w-4"})})}),(0,d.jsx)(r.p4,{children:s})]})});m.displayName=r.q7.displayName;s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,d.jsx)(r.wv,{ref:t,className:(0,i.cn)("-mx-1 my-1 h-px bg-muted",a),...s})}).displayName=r.wv.displayName},6879:(e,t,a)=>{a.d(t,{dj:()=>f,oR:()=>u});var s=a(5043);let r=0;const n=new Map,o=e=>{if(n.has(e))return;const t=setTimeout(()=>{n.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e3);n.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,5)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{const{toastId:a}=t;return a?o(a):e.toasts.forEach(e=>{o(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,open:!1}:e)}}case"REMOVE_TOAST":return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},d=[];let l={toasts:[]};function c(e){l=i(l,e),d.forEach(e=>{e(l)})}function u(e){let{...t}=e;const a=(r=(r+1)%Number.MAX_SAFE_INTEGER,r.toString()),s=()=>c({type:"DISMISS_TOAST",toastId:a});return c({type:"ADD_TOAST",toast:{...t,id:a,open:!0,onOpenChange:e=>{e||s()}}}),{id:a,dismiss:s,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:a}})}}function f(){const[e,t]=s.useState(l);return s.useEffect(()=>(d.push(t),()=>{const e=d.indexOf(t);e>-1&&d.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},9508:(e,t,a)=>{a.r(t),a.d(t,{TraderManagement:()=>h,default:()=>x});var s=a(5043),r=a(9066),n=a(6213),o=a(7842),i=a(382),d=a(9772),l=a(9954),c=a(2248),u=a(5320),f=a(492),m=a(6879),p=a(579);const g="http://localhost:5001/api",h=()=>{const{user:e,logout:t}=(0,r.A)(),a=localStorage.getItem("token"),{toast:h}=(0,m.dj)(),[x,y]=(0,s.useState)([]),[b,j]=(0,s.useState)([]),[N,v]=(0,s.useState)([]),[w,D]=(0,s.useState)(!0),[A,C]=(0,s.useState)(!1),[R,_]=(0,s.useState)({merchantId:"",traderId:"",startDate:(0,o.A)(new Date,"yyyy-MM-dd"),endDate:"",notes:""}),I={"Content-Type":"application/json",...a?{Authorization:`Bearer ${a}`}:{}};(0,s.useEffect)(()=>{T(),S()},[]);const T=async()=>{try{const[e]=await Promise.all([n.A.get(`${g}/trader/assignments`,{headers:I})]);y(e.data.data)}catch(e){console.error("Error fetching data:",e),h({title:"Error",description:"Failed to fetch data",variant:"destructive"})}finally{D(!1)}},S=async()=>{try{const[e,t]=await Promise.all([n.A.get(`${g}/admin/users?role=trader`,{headers:I}),n.A.get(`${g}/admin/users?role=merchant`,{headers:I})]);j(e.data.data),v(t.data.data)}catch(e){console.error("Error fetching users:",e),h({title:"Error",description:"Failed to fetch users",variant:"destructive"})}},E=e=>e?"string"===typeof e?"Loading...":e.name||e.email||"Unknown":"Unknown",M=e=>{if(!e)return"Unknown";if("string"===typeof e)return"Loading...";const t=e;return t.businessName||t.name||t.email||"Unknown"};return w?(0,p.jsx)("div",{children:"Loading..."}):(0,p.jsxs)("div",{className:"container mx-auto p-6",children:[(0,p.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,p.jsx)("h1",{className:"text-2xl font-bold",children:"Trader Management"}),(0,p.jsxs)(f.lG,{open:A,onOpenChange:C,children:[(0,p.jsx)(f.zM,{asChild:!0,children:(0,p.jsx)(d.$,{children:"Assign Trader"})}),(0,p.jsxs)(f.Cf,{children:[(0,p.jsx)(f.c7,{children:(0,p.jsx)(f.L3,{children:"Assign Trader to Merchant"})}),(0,p.jsxs)("form",{onSubmit:async e=>{e.preventDefault();try{await n.A.post(`${g}/trader/assignments`,{...R,startDate:new Date(R.startDate).toISOString(),endDate:R.endDate?new Date(R.endDate).toISOString():void 0},{headers:I}),h({title:"Success",description:"Trader assigned successfully"}),C(!1),_({merchantId:"",traderId:"",startDate:(0,o.A)(new Date,"yyyy-MM-dd"),endDate:"",notes:""}),T()}catch(t){console.error("Error creating assignment:",t),h({title:"Error",description:"Failed to assign trader",variant:"destructive"})}},className:"space-y-4",children:[(0,p.jsxs)("div",{className:"space-y-2",children:[(0,p.jsx)(c.J,{htmlFor:"merchantId",children:"Merchant"}),(0,p.jsxs)(u.l6,{value:R.merchantId,onValueChange:e=>_({...R,merchantId:e}),required:!0,children:[(0,p.jsx)(u.bq,{children:(0,p.jsx)(u.yv,{placeholder:"Select a merchant"})}),(0,p.jsx)(u.gC,{children:N.map(e=>(0,p.jsx)(u.eb,{value:e._id||"",children:M(e)},e._id))})]})]}),(0,p.jsxs)("div",{className:"space-y-2",children:[(0,p.jsx)(c.J,{htmlFor:"traderId",children:"Trader"}),(0,p.jsxs)(u.l6,{value:R.traderId,onValueChange:e=>_({...R,traderId:e}),required:!0,children:[(0,p.jsx)(u.bq,{children:(0,p.jsx)(u.yv,{placeholder:"Select a trader"})}),(0,p.jsx)(u.gC,{children:b.map(e=>(0,p.jsx)(u.eb,{value:e._id||"",children:e.name||e.email||"Unknown Trader"},e._id))})]})]}),(0,p.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,p.jsxs)("div",{className:"space-y-2",children:[(0,p.jsx)(c.J,{htmlFor:"startDate",children:"Start Date"}),(0,p.jsx)(l.p,{type:"date",id:"startDate",value:R.startDate,onChange:e=>_({...R,startDate:e.target.value}),required:!0})]}),(0,p.jsxs)("div",{className:"space-y-2",children:[(0,p.jsx)(c.J,{htmlFor:"endDate",children:"End Date (Optional)"}),(0,p.jsx)(l.p,{type:"date",id:"endDate",value:R.endDate,onChange:e=>_({...R,endDate:e.target.value}),min:R.startDate})]})]}),(0,p.jsxs)("div",{className:"space-y-2",children:[(0,p.jsx)(c.J,{htmlFor:"notes",children:"Notes (Optional)"}),(0,p.jsx)(l.p,{id:"notes",value:R.notes,onChange:e=>_({...R,notes:e.target.value}),placeholder:"Additional notes about this assignment"})]}),(0,p.jsxs)("div",{className:"flex justify-end space-x-2 pt-4",children:[(0,p.jsx)(d.$,{type:"button",variant:"outline",onClick:()=>C(!1),children:"Cancel"}),(0,p.jsx)(d.$,{type:"submit",children:"Assign Trader"})]})]})]})]})]}),(0,p.jsx)("div",{className:"rounded-md border",children:(0,p.jsxs)(i.XI,{children:[(0,p.jsx)(i.A0,{children:(0,p.jsxs)(i.Hj,{children:[(0,p.jsx)(i.nd,{children:"Merchant"}),(0,p.jsx)(i.nd,{children:"Trader"}),(0,p.jsx)(i.nd,{children:"Start Date"}),(0,p.jsx)(i.nd,{children:"End Date"}),(0,p.jsx)(i.nd,{children:"Status"}),(0,p.jsx)(i.nd,{children:"Actions"})]})}),(0,p.jsx)(i.BF,{children:0===x.length?(0,p.jsx)(i.Hj,{children:(0,p.jsx)(i.nA,{colSpan:6,className:"text-center py-4",children:"No assignments found"})}):x.map(e=>(0,p.jsxs)(i.Hj,{children:[(0,p.jsx)(i.nA,{children:E(e.merchant)}),(0,p.jsx)(i.nA,{children:E(e.trader)}),(0,p.jsx)(i.nA,{children:(0,o.A)(new Date(e.startDate),"MMM d, yyyy")}),(0,p.jsx)(i.nA,{children:e.endDate?(0,o.A)(new Date(e.endDate),"MMM d, yyyy"):"N/A"}),(0,p.jsx)(i.nA,{children:(0,p.jsx)("span",{className:"px-2 py-1 rounded-full text-xs "+("active"===e.status?"bg-green-100 text-green-800":"completed"===e.status?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-800"),children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})}),(0,p.jsx)(i.nA,{children:"active"===e.status&&(0,p.jsx)(d.$,{variant:"outline",size:"sm",className:"mr-2",onClick:()=>(async(e,t)=>{try{await n.A.put(`${g}/trader/assignments/${e}`,{status:t},{headers:I}),h({title:"Success",description:"Assignment updated successfully"}),T()}catch(a){console.error("Error updating assignment:",a),h({title:"Error",description:"Failed to update assignment",variant:"destructive"})}})(e._id,"completed"),children:"Mark Complete"})})]},e._id))})]})})]})},x=h},9772:(e,t,a)=>{a.d(t,{$:()=>l});var s=a(5043),r=a(6851),n=a(917),o=a(3009),i=a(579);const d=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=s.forwardRef((e,t)=>{let{className:a,variant:s,size:n,asChild:l=!1,...c}=e;const u=l?r.DX:"button";return(0,i.jsx)(u,{className:(0,o.cn)(d({variant:s,size:n,className:a})),ref:t,...c})});l.displayName="Button"},9954:(e,t,a)=>{a.d(t,{p:()=>o});var s=a(5043),r=a(3009),n=a(579);const o=s.forwardRef((e,t)=>{let{className:a,type:s,...o}=e;return(0,n.jsx)("input",{type:s,className:(0,r.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:t,...o})});o.displayName="Input"}}]);
//# sourceMappingURL=508.119a30c0.chunk.js.map