{"version": 3, "file": "static/js/293.39f79040.chunk.js", "mappings": "4PASe,SAASA,IACtB,MAAM,KAAEC,IAASC,EAAAA,EAAAA,KAEjB,OAAKD,GAIHE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,qBAAoBC,SAAC,sBACnCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wBAAuBC,SAAC,qDAKvCF,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACC,aAAa,UAAUJ,UAAU,YAAWC,SAAA,EAChDF,EAAAA,EAAAA,MAACM,EAAAA,GAAQ,CAAAJ,SAAA,EACPC,EAAAA,EAAAA,KAACI,EAAAA,GAAW,CAACC,MAAM,UAASN,SAAC,aAC7BC,EAAAA,EAAAA,KAACI,EAAAA,GAAW,CAACC,MAAM,WAAUN,SAAC,cAC9BC,EAAAA,EAAAA,KAACI,EAAAA,GAAW,CAACC,MAAM,WAAUN,SAAC,cAC9BC,EAAAA,EAAAA,KAACI,EAAAA,GAAW,CAACC,MAAM,MAAKN,SAAC,wBAG3BC,EAAAA,EAAAA,KAACM,EAAAA,GAAW,CAACD,MAAM,UAAUP,UAAU,YAAWC,UAChDF,EAAAA,EAAAA,MAACU,EAAAA,GAAI,CAAAR,SAAA,EACHC,EAAAA,EAAAA,KAACQ,EAAAA,GAAU,CAAAT,UACTC,EAAAA,EAAAA,KAACS,EAAAA,GAAS,CAAAV,SAAC,2BAEbC,EAAAA,EAAAA,KAACU,EAAAA,GAAW,CAAAX,UACVC,EAAAA,EAAAA,KAACW,EAAAA,EAAW,CAAChB,KAAMA,YAKzBK,EAAAA,EAAAA,KAACM,EAAAA,GAAW,CAACD,MAAM,WAAUN,UAC3BF,EAAAA,EAAAA,MAACU,EAAAA,GAAI,CAAAR,SAAA,EACHC,EAAAA,EAAAA,KAACQ,EAAAA,GAAU,CAAAT,UACTC,EAAAA,EAAAA,KAACS,EAAAA,GAAS,CAAAV,SAAC,uBAEbC,EAAAA,EAAAA,KAACU,EAAAA,GAAW,CAAAX,UACVC,EAAAA,EAAAA,KAACY,EAAAA,EAAkB,YAKzBZ,EAAAA,EAAAA,KAACM,EAAAA,GAAW,CAACD,MAAM,WAAUN,UAC3BF,EAAAA,EAAAA,MAACU,EAAAA,GAAI,CAAAR,SAAA,EACHC,EAAAA,EAAAA,KAACQ,EAAAA,GAAU,CAAAT,UACTC,EAAAA,EAAAA,KAACS,EAAAA,GAAS,CAAAV,SAAC,gBAEbC,EAAAA,EAAAA,KAACU,EAAAA,GAAW,CAAAX,UACVC,EAAAA,EAAAA,KAACa,EAAAA,EAAO,YAKdb,EAAAA,EAAAA,KAACM,EAAAA,GAAW,CAACD,MAAM,MAAKN,UACtBF,EAAAA,EAAAA,MAACU,EAAAA,GAAI,CAAAR,SAAA,EACHC,EAAAA,EAAAA,KAACQ,EAAAA,GAAU,CAAAT,UACTC,EAAAA,EAAAA,KAACS,EAAAA,GAAS,CAAAV,SAAC,iCAEbC,EAAAA,EAAAA,KAACU,EAAAA,GAAW,CAAAX,UACVC,EAAAA,EAAAA,KAACc,EAAAA,EAAa,kBA1DjBd,EAAAA,EAAAA,KAAA,OAAAD,SAAK,wBAiEhB,C", "sources": ["pages/merchant/Profile.tsx"], "sourcesContent": ["import React from 'react';\nimport { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from '../../components/ui/card';\nimport { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '../../components/ui/tabs';\nimport { ProfileForm } from '../../components/profile/ProfileForm';\nimport { PasswordChangeForm } from '../../components/profile/PasswordChangeForm';\nimport { TwoFactorAuth } from '../../components/profile/TwoFactorAuth';\nimport { ApiKeys } from '../../components/profile/ApiKeys';\nimport { useAuth } from '../../contexts/AuthContext';\n\nexport default function ProfilePage() {\n  const { user } = useAuth();\n  \n  if (!user) {\n    return <div>Loading user data...</div>;\n  }\n  return (\n    <div className=\"space-y-6\">\n      <div>\n        <h1 className=\"text-3xl font-bold\">Profile Settings</h1>\n        <p className=\"text-muted-foreground\">\n          Manage your account settings and preferences\n        </p>\n      </div>\n\n      <Tabs defaultValue=\"profile\" className=\"space-y-6\">\n        <TabsList>\n          <TabsTrigger value=\"profile\">Profile</TabsTrigger>\n          <TabsTrigger value=\"security\">Security</TabsTrigger>\n          <TabsTrigger value=\"api-keys\">API Keys</TabsTrigger>\n          <TabsTrigger value=\"2fa\">Two-Factor Auth</TabsTrigger>\n        </TabsList>\n\n        <TabsContent value=\"profile\" className=\"space-y-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Profile Information</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <ProfileForm user={user} />\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"security\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Change Password</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <PasswordChangeForm />\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"api-keys\">\n          <Card>\n            <CardHeader>\n              <CardTitle>API Keys</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <ApiKeys />\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"2fa\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Two-Factor Authentication</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <TwoFactorAuth />\n            </CardContent>\n          </Card>\n        </TabsContent>\n      </Tabs>\n    </div>\n  );\n}\n"], "names": ["ProfilePage", "user", "useAuth", "_jsxs", "className", "children", "_jsx", "Tabs", "defaultValue", "TabsList", "TabsTrigger", "value", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "<PERSON><PERSON><PERSON><PERSON>", "ProfileForm", "PasswordChangeForm", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TwoFactorAuth"], "sourceRoot": ""}