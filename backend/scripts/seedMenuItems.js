const mongoose = require('mongoose');
const Menu = require('../models/Menu');
const config = require('../config/config');

const menuItems = [
  // Common Items
  {
    menuId: 'dashboard',
    label: 'Dashboard',
    to: '/dashboard',
    icon: 'Dashboard',
    role: 'all',
    order: 1,
    description: 'Main dashboard overview'
  },

  // Merchant Items
  {
    menuId: 'merchant-transactions',
    label: 'Transactions',
    to: '/merchant/transactions',
    icon: 'Receipt',
    role: 'merchant',
    order: 2,
    description: 'View and manage transactions'
  },
  {
    menuId: 'merchant-collections',
    label: 'Collections',
    to: '/merchant/collections',
    icon: 'Payment',
    role: 'merchant',
    order: 3,
    description: 'Manage payment collections'
  },
  {
    menuId: 'merchant-billing',
    label: 'Billing',
    to: '/merchant/billing',
    icon: 'CreditCard',
    role: 'merchant',
    order: 4,
    description: 'Billing and invoices'
  },
  {
    menuId: 'merchant-traders',
    label: 'Traders',
    to: '/merchant/traders',
    icon: 'Users',
    role: 'merchant',
    order: 5,
    description: 'Manage traders'
  },
  {
    menuId: 'merchant-settings',
    label: 'Settings',
    to: '/merchant/settings',
    icon: 'Settings',
    role: 'merchant',
    order: 6,
    description: 'Account and application settings'
  },

  // Admin Items
  {
    menuId: 'admin-merchants',
    label: 'Merchants',
    to: '/admin/merchants',
    icon: 'Store',
    role: 'admin',
    order: 2,
    description: 'Manage merchants'
  },
  {
    menuId: 'admin-traders',
    label: 'Traders',
    to: '/admin/traders',
    icon: 'Users',
    role: 'admin',
    order: 3,
    description: 'Manage traders'
  },
  {
    menuId: 'admin-payments',
    label: 'Payments',
    to: '/admin/payments',
    icon: 'CreditCard',
    role: 'admin',
    order: 4,
    description: 'Manage payments'
  },
  {
    menuId: 'admin-reports',
    label: 'Reports',
    to: '/admin/reports',
    icon: 'FileText',
    role: 'admin',
    order: 5,
    description: 'View system reports'
  },
  {
    menuId: 'admin-settings',
    label: 'Settings',
    to: '/admin/settings',
    icon: 'Settings',
    role: 'admin',
    order: 6,
    description: 'System settings'
  },

  // Trader Items (if any)
  // ...
];

async function seedMenuItems() {
  try {
    // Connect to MongoDB
    await mongoose.connect(config.mongo.uri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    console.log('Connected to MongoDB');

    // Clear existing menu items
    await Menu.deleteMany({});
    console.log('Cleared existing menu items');

    // Insert new menu items
    const createdMenus = await Menu.insertMany(menuItems);
    console.log(`Successfully seeded ${createdMenus.length} menu items`);

    process.exit(0);
  } catch (error) {
    console.error('Error seeding menu items:', error);
    process.exit(1);
  }
}

seedMenuItems();
