const mongoose = require('mongoose');
const crypto = require('crypto');
const { v4: uuidv4 } = require('uuid');
const { ApiError } = require('../utils/ApiError');
const logger = require('../utils/logger');

const apiKeySchema = new mongoose.Schema(
  {
    merchant: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Merchant',
      required: [true, 'Merchant ID is required'],
      index: true,
    },
    name: {
      type: String,
      required: [true, 'API key name is required'],
      trim: true,
      maxlength: [100, 'API key name cannot be more than 100 characters'],
    },
    key: {
      type: String,
      required: true,
      unique: true,
      index: true,
      default: () => `pk_${crypto.randomBytes(16).toString('hex')}`,
    },
    secret: {
      type: String,
      required: true,
      select: false,
      default: () => `sk_${crypto.randomBytes(32).toString('hex')}`,
    },
    description: {
      type: String,
      trim: true,
      maxlength: [500, 'Description cannot be more than 500 characters'],
    },
    permissions: {
      type: [String],
      default: ['read'],
      enum: {
        values: [
          'read',
          'write',
          'payments:read',
          'payments:create',
          'refunds:process',
          'webhooks:manage',
          'api_keys:manage',
        ],
        message: 'Invalid permission type',
      },
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    lastUsedAt: {
      type: Date,
    },
    expiresAt: {
      type: Date,
    },
    ipWhitelist: [
      {
        type: String,
        trim: true,
      },
    ],
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
  },
  {
    timestamps: true,
    toJSON: {
      virtuals: true,
      transform: function (doc, ret) {
        delete ret.__v;
        delete ret.id;
        delete ret.secret; // Never include the secret in JSON output
        return ret;
      },
    },
    toObject: {
      virtuals: true,
      transform: function (doc, ret) {
        delete ret.__v;
        delete ret.id;
        delete ret.secret; // Never include the secret in object output
        return ret;
      },
    },
  }
);

// Indexes
apiKeySchema.index({ key: 1, isActive: 1 });
apiKeySchema.index({ merchant: 1, isActive: 1 });
apiKeySchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

/**
 * Check if API key has specific permissions
 * @param {...string} requiredPermissions - Required permissions
 * @returns {boolean} True if key has all required permissions
 */
apiKeySchema.methods.hasPermissions = function(...requiredPermissions) {
  if (!requiredPermissions.length) return true;
  return requiredPermissions.every(permission => 
    this.permissions.includes(permission) ||
    this.permissions.includes('write') ||
    this.permissions.includes('admin')
  );
};

/**
 * Check if API key is expired
 * @returns {boolean} True if key is expired
 */
apiKeySchema.methods.isExpired = function() {
  return this.expiresAt && new Date() > this.expiresAt;
};

/**
 * Update last used timestamp
 */
apiKeySchema.methods.updateLastUsed = async function() {
  this.lastUsedAt = new Date();
  return this.save();
};

/**
 * Revoke API key
 */
apiKeySchema.methods.revoke = async function(userId) {
  this.isActive = false;
  this.updatedBy = userId;
  return this.save();
};

/**
 * Find active API key by key value
 * @param {string} key - API key
 * @returns {Promise<ApiKey>} API key document
 */
apiKeySchema.statics.findByKey = async function(key) {
  return this.findOne({ key, isActive: true });
};

/**
 * Generate a new API key for a merchant
 * @param {Object} options - Key generation options
 * @param {string} options.merchantId - Merchant ID
 * @param {string} options.name - Key name
 * @param {string} options.description - Key description
 * @param {string[]} options.permissions - Key permissions
 * @param {string[]} options.ipWhitelist - Allowed IP addresses
 * @param {Date} options.expiresAt - Expiration date
 * @param {string} userId - User ID creating the key
 * @returns {Promise<Object>} Generated key data
 */
apiKeySchema.statics.generateKey = async function({
  merchantId,
  name,
  description = '',
  permissions = ['read'],
  ipWhitelist = [],
  expiresAt = null,
}, userId) {
  const keyData = {
    merchant: merchantId,
    name,
    description,
    permissions,
    ipWhitelist,
    expiresAt,
    createdBy: userId,
    updatedBy: userId,
  };

  const apiKey = new this(keyData);
  await apiKey.save();

  // Return the key with the secret (only time it's available)
  return {
    id: apiKey._id,
    key: apiKey.key,
    secret: apiKey.secret, // Only shown once
    name: apiKey.name,
    description: apiKey.description,
    permissions: apiKey.permissions,
    ipWhitelist: apiKey.ipWhitelist,
    expiresAt: apiKey.expiresAt,
    createdAt: apiKey.createdAt,
  };
};

/**
 * Verify API key and secret
 * @param {string} key - API key
 * @param {string} secret - API secret
 * @returns {Promise<Object>} API key document if valid
 * @throws {ApiError} If key is invalid
 */
apiKeySchema.statics.verify = async function(key, secret) {
  if (!key || !secret) {
    throw new ApiError(401, 'API key and secret are required');
  }

  const apiKey = await this.findOne({ key }).select('+secret');
  
  if (!apiKey || !apiKey.isActive) {
    throw new ApiError(401, 'Invalid API key');
  }

  if (apiKey.isExpired()) {
    apiKey.isActive = false;
    await apiKey.save();
    throw new ApiError(401, 'API key has expired');
  }

  // Verify secret
  const isMatch = secret === apiKey.secret;
  if (!isMatch) {
    throw new ApiError(401, 'Invalid API secret');
  }

  // Update last used timestamp
  await apiKey.updateLastUsed();

  // Return key without secret
  const keyObj = apiKey.toObject();
  delete keyObj.secret;
  return keyObj;
};

const ApiKey = mongoose.model('ApiKey', apiKeySchema);

module.exports = ApiKey;
