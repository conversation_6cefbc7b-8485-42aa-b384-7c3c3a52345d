"use strict";(self.webpackChunkpayment_gateway_frontend=self.webpackChunkpayment_gateway_frontend||[]).push([[975],{1024:(t,e,n)=>{n.d(e,{A:()=>o});const o=(0,n(3797).A)("Check",[["polyline",{points:"20 6 9 17 4 12",key:"10jjfj"}]])},6178:(t,e,n)=>{n.d(e,{BN:()=>m,ER:()=>p,Ej:()=>y,UE:()=>x,UU:()=>g,cY:()=>h,jD:()=>w,we:()=>u});var o=n(6667),r=n(5043),i=n(7950),l="undefined"!==typeof document?r.useLayoutEffect:function(){};function c(t,e){if(t===e)return!0;if(typeof t!==typeof e)return!1;if("function"===typeof t&&t.toString()===e.toString())return!0;let n,o,r;if(t&&e&&"object"===typeof t){if(Array.isArray(t)){if(n=t.length,n!==e.length)return!1;for(o=n;0!==o--;)if(!c(t[o],e[o]))return!1;return!0}if(r=Object.keys(t),n=r.length,n!==Object.keys(e).length)return!1;for(o=n;0!==o--;)if(!{}.hasOwnProperty.call(e,r[o]))return!1;for(o=n;0!==o--;){const n=r[o];if(("_owner"!==n||!t.$$typeof)&&!c(t[n],e[n]))return!1}return!0}return t!==t&&e!==e}function s(t){if("undefined"===typeof window)return 1;return(t.ownerDocument.defaultView||window).devicePixelRatio||1}function a(t,e){const n=s(t);return Math.round(e*n)/n}function f(t){const e=r.useRef(t);return l(()=>{e.current=t}),e}function u(t){void 0===t&&(t={});const{placement:e="bottom",strategy:n="absolute",middleware:u=[],platform:d,elements:{reference:h,floating:m}={},transform:p=!0,whileElementsMounted:g,open:y}=t,[w,x]=r.useState({x:0,y:0,strategy:n,placement:e,middlewareData:{},isPositioned:!1}),[v,b]=r.useState(u);c(v,u)||b(u);const[R,A]=r.useState(null),[E,L]=r.useState(null),T=r.useCallback(t=>{t!==C.current&&(C.current=t,A(t))},[]),D=r.useCallback(t=>{t!==P.current&&(P.current=t,L(t))},[]),S=h||R,O=m||E,C=r.useRef(null),P=r.useRef(null),k=r.useRef(w),F=null!=g,H=f(g),B=f(d),j=f(y),M=r.useCallback(()=>{if(!C.current||!P.current)return;const t={placement:e,strategy:n,middleware:v};B.current&&(t.platform=B.current),(0,o.rD)(C.current,P.current,t).then(t=>{const e={...t,isPositioned:!1!==j.current};W.current&&!c(k.current,e)&&(k.current=e,i.flushSync(()=>{x(e)}))})},[v,e,n,B,j]);l(()=>{!1===y&&k.current.isPositioned&&(k.current.isPositioned=!1,x(t=>({...t,isPositioned:!1})))},[y]);const W=r.useRef(!1);l(()=>(W.current=!0,()=>{W.current=!1}),[]),l(()=>{if(S&&(C.current=S),O&&(P.current=O),S&&O){if(H.current)return H.current(S,O,M);M()}},[S,O,M,H,F]);const U=r.useMemo(()=>({reference:C,floating:P,setReference:T,setFloating:D}),[T,D]),V=r.useMemo(()=>({reference:S,floating:O}),[S,O]),N=r.useMemo(()=>{const t={position:n,left:0,top:0};if(!V.floating)return t;const e=a(V.floating,w.x),o=a(V.floating,w.y);return p?{...t,transform:"translate("+e+"px, "+o+"px)",...s(V.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:e,top:o}},[n,p,V.floating,w.x,w.y]);return r.useMemo(()=>({...w,update:M,refs:U,elements:V,floatingStyles:N}),[w,M,U,V,N])}const d=t=>({name:"arrow",options:t,fn(e){const{element:n,padding:r}="function"===typeof t?t(e):t;return n&&(i=n,{}.hasOwnProperty.call(i,"current"))?null!=n.current?(0,o.UE)({element:n.current,padding:r}).fn(e):{}:n?(0,o.UE)({element:n,padding:r}).fn(e):{};var i}}),h=(t,e)=>({...(0,o.cY)(t),options:[t,e]}),m=(t,e)=>({...(0,o.BN)(t),options:[t,e]}),p=(t,e)=>({...(0,o.ER)(t),options:[t,e]}),g=(t,e)=>({...(0,o.UU)(t),options:[t,e]}),y=(t,e)=>({...(0,o.Ej)(t),options:[t,e]}),w=(t,e)=>({...(0,o.jD)(t),options:[t,e]}),x=(t,e)=>({...d(t),options:[t,e]})},6667:(t,e,n)=>{n.d(e,{UE:()=>Ct,ll:()=>Et,rD:()=>kt,UU:()=>Dt,jD:()=>Ot,ER:()=>Pt,cY:()=>Lt,BN:()=>Tt,Ej:()=>St});const o=["top","right","bottom","left"],r=Math.min,i=Math.max,l=Math.round,c=Math.floor,s=t=>({x:t,y:t}),a={left:"right",right:"left",bottom:"top",top:"bottom"},f={start:"end",end:"start"};function u(t,e,n){return i(t,r(e,n))}function d(t,e){return"function"===typeof t?t(e):t}function h(t){return t.split("-")[0]}function m(t){return t.split("-")[1]}function p(t){return"x"===t?"y":"x"}function g(t){return"y"===t?"height":"width"}const y=new Set(["top","bottom"]);function w(t){return y.has(h(t))?"y":"x"}function x(t){return p(w(t))}function v(t){return t.replace(/start|end/g,t=>f[t])}const b=["left","right"],R=["right","left"],A=["top","bottom"],E=["bottom","top"];function L(t,e,n,o){const r=m(t);let i=function(t,e,n){switch(t){case"top":case"bottom":return n?e?R:b:e?b:R;case"left":case"right":return e?A:E;default:return[]}}(h(t),"start"===n,o);return r&&(i=i.map(t=>t+"-"+r),e&&(i=i.concat(i.map(v)))),i}function T(t){return t.replace(/left|right|bottom|top/g,t=>a[t])}function D(t){return"number"!==typeof t?function(t){return{top:0,right:0,bottom:0,left:0,...t}}(t):{top:t,right:t,bottom:t,left:t}}function S(t){const{x:e,y:n,width:o,height:r}=t;return{width:o,height:r,top:n,left:e,right:e+o,bottom:n+r,x:e,y:n}}function O(t,e,n){let{reference:o,floating:r}=t;const i=w(e),l=x(e),c=g(l),s=h(e),a="y"===i,f=o.x+o.width/2-r.width/2,u=o.y+o.height/2-r.height/2,d=o[c]/2-r[c]/2;let p;switch(s){case"top":p={x:f,y:o.y-r.height};break;case"bottom":p={x:f,y:o.y+o.height};break;case"right":p={x:o.x+o.width,y:u};break;case"left":p={x:o.x-r.width,y:u};break;default:p={x:o.x,y:o.y}}switch(m(e)){case"start":p[l]-=d*(n&&a?-1:1);break;case"end":p[l]+=d*(n&&a?-1:1)}return p}async function C(t,e){var n;void 0===e&&(e={});const{x:o,y:r,platform:i,rects:l,elements:c,strategy:s}=t,{boundary:a="clippingAncestors",rootBoundary:f="viewport",elementContext:u="floating",altBoundary:h=!1,padding:m=0}=d(e,t),p=D(m),g=c[h?"floating"===u?"reference":"floating":u],y=S(await i.getClippingRect({element:null==(n=await(null==i.isElement?void 0:i.isElement(g)))||n?g:g.contextElement||await(null==i.getDocumentElement?void 0:i.getDocumentElement(c.floating)),boundary:a,rootBoundary:f,strategy:s})),w="floating"===u?{x:o,y:r,width:l.floating.width,height:l.floating.height}:l.reference,x=await(null==i.getOffsetParent?void 0:i.getOffsetParent(c.floating)),v=await(null==i.isElement?void 0:i.isElement(x))&&await(null==i.getScale?void 0:i.getScale(x))||{x:1,y:1},b=S(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:c,rect:w,offsetParent:x,strategy:s}):w);return{top:(y.top-b.top+p.top)/v.y,bottom:(b.bottom-y.bottom+p.bottom)/v.y,left:(y.left-b.left+p.left)/v.x,right:(b.right-y.right+p.right)/v.x}}function P(t,e){return{top:t.top-e.height,right:t.right-e.width,bottom:t.bottom-e.height,left:t.left-e.width}}function k(t){return o.some(e=>t[e]>=0)}const F=new Set(["left","top"]);function H(){return"undefined"!==typeof window}function B(t){return W(t)?(t.nodeName||"").toLowerCase():"#document"}function j(t){var e;return(null==t||null==(e=t.ownerDocument)?void 0:e.defaultView)||window}function M(t){var e;return null==(e=(W(t)?t.ownerDocument:t.document)||window.document)?void 0:e.documentElement}function W(t){return!!H()&&(t instanceof Node||t instanceof j(t).Node)}function U(t){return!!H()&&(t instanceof Element||t instanceof j(t).Element)}function V(t){return!!H()&&(t instanceof HTMLElement||t instanceof j(t).HTMLElement)}function N(t){return!(!H()||"undefined"===typeof ShadowRoot)&&(t instanceof ShadowRoot||t instanceof j(t).ShadowRoot)}const z=new Set(["inline","contents"]);function _(t){const{overflow:e,overflowX:n,overflowY:o,display:r}=et(t);return/auto|scroll|overlay|hidden|clip/.test(e+o+n)&&!z.has(r)}const Y=new Set(["table","td","th"]);function $(t){return Y.has(B(t))}const I=[":popover-open",":modal"];function q(t){return I.some(e=>{try{return t.matches(e)}catch(n){return!1}})}const X=["transform","translate","scale","rotate","perspective"],G=["transform","translate","scale","rotate","perspective","filter"],J=["paint","layout","strict","content"];function K(t){const e=Q(),n=U(t)?et(t):t;return X.some(t=>!!n[t]&&"none"!==n[t])||!!n.containerType&&"normal"!==n.containerType||!e&&!!n.backdropFilter&&"none"!==n.backdropFilter||!e&&!!n.filter&&"none"!==n.filter||G.some(t=>(n.willChange||"").includes(t))||J.some(t=>(n.contain||"").includes(t))}function Q(){return!("undefined"===typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}const Z=new Set(["html","body","#document"]);function tt(t){return Z.has(B(t))}function et(t){return j(t).getComputedStyle(t)}function nt(t){return U(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function ot(t){if("html"===B(t))return t;const e=t.assignedSlot||t.parentNode||N(t)&&t.host||M(t);return N(e)?e.host:e}function rt(t){const e=ot(t);return tt(e)?t.ownerDocument?t.ownerDocument.body:t.body:V(e)&&_(e)?e:rt(e)}function it(t,e,n){var o;void 0===e&&(e=[]),void 0===n&&(n=!0);const r=rt(t),i=r===(null==(o=t.ownerDocument)?void 0:o.body),l=j(r);if(i){const t=lt(l);return e.concat(l,l.visualViewport||[],_(r)?r:[],t&&n?it(t):[])}return e.concat(r,it(r,[],n))}function lt(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function ct(t){const e=et(t);let n=parseFloat(e.width)||0,o=parseFloat(e.height)||0;const r=V(t),i=r?t.offsetWidth:n,c=r?t.offsetHeight:o,s=l(n)!==i||l(o)!==c;return s&&(n=i,o=c),{width:n,height:o,$:s}}function st(t){return U(t)?t:t.contextElement}function at(t){const e=st(t);if(!V(e))return s(1);const n=e.getBoundingClientRect(),{width:o,height:r,$:i}=ct(e);let c=(i?l(n.width):n.width)/o,a=(i?l(n.height):n.height)/r;return c&&Number.isFinite(c)||(c=1),a&&Number.isFinite(a)||(a=1),{x:c,y:a}}const ft=s(0);function ut(t){const e=j(t);return Q()&&e.visualViewport?{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}:ft}function dt(t,e,n,o){void 0===e&&(e=!1),void 0===n&&(n=!1);const r=t.getBoundingClientRect(),i=st(t);let l=s(1);e&&(o?U(o)&&(l=at(o)):l=at(t));const c=function(t,e,n){return void 0===e&&(e=!1),!(!n||e&&n!==j(t))&&e}(i,n,o)?ut(i):s(0);let a=(r.left+c.x)/l.x,f=(r.top+c.y)/l.y,u=r.width/l.x,d=r.height/l.y;if(i){const t=j(i),e=o&&U(o)?j(o):o;let n=t,r=lt(n);for(;r&&o&&e!==n;){const t=at(r),e=r.getBoundingClientRect(),o=et(r),i=e.left+(r.clientLeft+parseFloat(o.paddingLeft))*t.x,l=e.top+(r.clientTop+parseFloat(o.paddingTop))*t.y;a*=t.x,f*=t.y,u*=t.x,d*=t.y,a+=i,f+=l,n=j(r),r=lt(n)}}return S({width:u,height:d,x:a,y:f})}function ht(t,e){const n=nt(t).scrollLeft;return e?e.left+n:dt(M(t)).left+n}function mt(t,e,n){void 0===n&&(n=!1);const o=t.getBoundingClientRect();return{x:o.left+e.scrollLeft-(n?0:ht(t,o)),y:o.top+e.scrollTop}}const pt=new Set(["absolute","fixed"]);function gt(t,e,n){let o;if("viewport"===e)o=function(t,e){const n=j(t),o=M(t),r=n.visualViewport;let i=o.clientWidth,l=o.clientHeight,c=0,s=0;if(r){i=r.width,l=r.height;const t=Q();(!t||t&&"fixed"===e)&&(c=r.offsetLeft,s=r.offsetTop)}return{width:i,height:l,x:c,y:s}}(t,n);else if("document"===e)o=function(t){const e=M(t),n=nt(t),o=t.ownerDocument.body,r=i(e.scrollWidth,e.clientWidth,o.scrollWidth,o.clientWidth),l=i(e.scrollHeight,e.clientHeight,o.scrollHeight,o.clientHeight);let c=-n.scrollLeft+ht(t);const s=-n.scrollTop;return"rtl"===et(o).direction&&(c+=i(e.clientWidth,o.clientWidth)-r),{width:r,height:l,x:c,y:s}}(M(t));else if(U(e))o=function(t,e){const n=dt(t,!0,"fixed"===e),o=n.top+t.clientTop,r=n.left+t.clientLeft,i=V(t)?at(t):s(1);return{width:t.clientWidth*i.x,height:t.clientHeight*i.y,x:r*i.x,y:o*i.y}}(e,n);else{const n=ut(t);o={x:e.x-n.x,y:e.y-n.y,width:e.width,height:e.height}}return S(o)}function yt(t,e){const n=ot(t);return!(n===e||!U(n)||tt(n))&&("fixed"===et(n).position||yt(n,e))}function wt(t,e,n){const o=V(e),r=M(e),i="fixed"===n,l=dt(t,!0,i,e);let c={scrollLeft:0,scrollTop:0};const a=s(0);function f(){a.x=ht(r)}if(o||!o&&!i)if(("body"!==B(e)||_(r))&&(c=nt(e)),o){const t=dt(e,!0,i,e);a.x=t.x+e.clientLeft,a.y=t.y+e.clientTop}else r&&f();i&&!o&&r&&f();const u=!r||o||i?s(0):mt(r,c);return{x:l.left+c.scrollLeft-a.x-u.x,y:l.top+c.scrollTop-a.y-u.y,width:l.width,height:l.height}}function xt(t){return"static"===et(t).position}function vt(t,e){if(!V(t)||"fixed"===et(t).position)return null;if(e)return e(t);let n=t.offsetParent;return M(t)===n&&(n=n.ownerDocument.body),n}function bt(t,e){const n=j(t);if(q(t))return n;if(!V(t)){let e=ot(t);for(;e&&!tt(e);){if(U(e)&&!xt(e))return e;e=ot(e)}return n}let o=vt(t,e);for(;o&&$(o)&&xt(o);)o=vt(o,e);return o&&tt(o)&&xt(o)&&!K(o)?n:o||function(t){let e=ot(t);for(;V(e)&&!tt(e);){if(K(e))return e;if(q(e))return null;e=ot(e)}return null}(t)||n}const Rt={convertOffsetParentRelativeRectToViewportRelativeRect:function(t){let{elements:e,rect:n,offsetParent:o,strategy:r}=t;const i="fixed"===r,l=M(o),c=!!e&&q(e.floating);if(o===l||c&&i)return n;let a={scrollLeft:0,scrollTop:0},f=s(1);const u=s(0),d=V(o);if((d||!d&&!i)&&(("body"!==B(o)||_(l))&&(a=nt(o)),V(o))){const t=dt(o);f=at(o),u.x=t.x+o.clientLeft,u.y=t.y+o.clientTop}const h=!l||d||i?s(0):mt(l,a,!0);return{width:n.width*f.x,height:n.height*f.y,x:n.x*f.x-a.scrollLeft*f.x+u.x+h.x,y:n.y*f.y-a.scrollTop*f.y+u.y+h.y}},getDocumentElement:M,getClippingRect:function(t){let{element:e,boundary:n,rootBoundary:o,strategy:l}=t;const c=[..."clippingAncestors"===n?q(e)?[]:function(t,e){const n=e.get(t);if(n)return n;let o=it(t,[],!1).filter(t=>U(t)&&"body"!==B(t)),r=null;const i="fixed"===et(t).position;let l=i?ot(t):t;for(;U(l)&&!tt(l);){const e=et(l),n=K(l);n||"fixed"!==e.position||(r=null),(i?!n&&!r:!n&&"static"===e.position&&r&&pt.has(r.position)||_(l)&&!n&&yt(t,l))?o=o.filter(t=>t!==l):r=e,l=ot(l)}return e.set(t,o),o}(e,this._c):[].concat(n),o],s=c[0],a=c.reduce((t,n)=>{const o=gt(e,n,l);return t.top=i(o.top,t.top),t.right=r(o.right,t.right),t.bottom=r(o.bottom,t.bottom),t.left=i(o.left,t.left),t},gt(e,s,l));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:bt,getElementRects:async function(t){const e=this.getOffsetParent||bt,n=this.getDimensions,o=await n(t.floating);return{reference:wt(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:o.width,height:o.height}}},getClientRects:function(t){return Array.from(t.getClientRects())},getDimensions:function(t){const{width:e,height:n}=ct(t);return{width:e,height:n}},getScale:at,isElement:U,isRTL:function(t){return"rtl"===et(t).direction}};function At(t,e){return t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height}function Et(t,e,n,o){void 0===o&&(o={});const{ancestorScroll:l=!0,ancestorResize:s=!0,elementResize:a="function"===typeof ResizeObserver,layoutShift:f="function"===typeof IntersectionObserver,animationFrame:u=!1}=o,d=st(t),h=l||s?[...d?it(d):[],...it(e)]:[];h.forEach(t=>{l&&t.addEventListener("scroll",n,{passive:!0}),s&&t.addEventListener("resize",n)});const m=d&&f?function(t,e){let n,o=null;const l=M(t);function s(){var t;clearTimeout(n),null==(t=o)||t.disconnect(),o=null}return function a(f,u){void 0===f&&(f=!1),void 0===u&&(u=1),s();const d=t.getBoundingClientRect(),{left:h,top:m,width:p,height:g}=d;if(f||e(),!p||!g)return;const y={rootMargin:-c(m)+"px "+-c(l.clientWidth-(h+p))+"px "+-c(l.clientHeight-(m+g))+"px "+-c(h)+"px",threshold:i(0,r(1,u))||1};let w=!0;function x(e){const o=e[0].intersectionRatio;if(o!==u){if(!w)return a();o?a(!1,o):n=setTimeout(()=>{a(!1,1e-7)},1e3)}1!==o||At(d,t.getBoundingClientRect())||a(),w=!1}try{o=new IntersectionObserver(x,{...y,root:l.ownerDocument})}catch(v){o=new IntersectionObserver(x,y)}o.observe(t)}(!0),s}(d,n):null;let p,g=-1,y=null;a&&(y=new ResizeObserver(t=>{let[o]=t;o&&o.target===d&&y&&(y.unobserve(e),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var t;null==(t=y)||t.observe(e)})),n()}),d&&!u&&y.observe(d),y.observe(e));let w=u?dt(t):null;return u&&function e(){const o=dt(t);w&&!At(w,o)&&n();w=o,p=requestAnimationFrame(e)}(),n(),()=>{var t;h.forEach(t=>{l&&t.removeEventListener("scroll",n),s&&t.removeEventListener("resize",n)}),null==m||m(),null==(t=y)||t.disconnect(),y=null,u&&cancelAnimationFrame(p)}}const Lt=function(t){return void 0===t&&(t=0),{name:"offset",options:t,async fn(e){var n,o;const{x:r,y:i,placement:l,middlewareData:c}=e,s=await async function(t,e){const{placement:n,platform:o,elements:r}=t,i=await(null==o.isRTL?void 0:o.isRTL(r.floating)),l=h(n),c=m(n),s="y"===w(n),a=F.has(l)?-1:1,f=i&&s?-1:1,u=d(e,t);let{mainAxis:p,crossAxis:g,alignmentAxis:y}="number"===typeof u?{mainAxis:u,crossAxis:0,alignmentAxis:null}:{mainAxis:u.mainAxis||0,crossAxis:u.crossAxis||0,alignmentAxis:u.alignmentAxis};return c&&"number"===typeof y&&(g="end"===c?-1*y:y),s?{x:g*f,y:p*a}:{x:p*a,y:g*f}}(e,t);return l===(null==(n=c.offset)?void 0:n.placement)&&null!=(o=c.arrow)&&o.alignmentOffset?{}:{x:r+s.x,y:i+s.y,data:{...s,placement:l}}}}},Tt=function(t){return void 0===t&&(t={}),{name:"shift",options:t,async fn(e){const{x:n,y:o,placement:r}=e,{mainAxis:i=!0,crossAxis:l=!1,limiter:c={fn:t=>{let{x:e,y:n}=t;return{x:e,y:n}}},...s}=d(t,e),a={x:n,y:o},f=await C(e,s),m=w(h(r)),g=p(m);let y=a[g],x=a[m];if(i){const t="y"===g?"bottom":"right";y=u(y+f["y"===g?"top":"left"],y,y-f[t])}if(l){const t="y"===m?"bottom":"right";x=u(x+f["y"===m?"top":"left"],x,x-f[t])}const v=c.fn({...e,[g]:y,[m]:x});return{...v,data:{x:v.x-n,y:v.y-o,enabled:{[g]:i,[m]:l}}}}}},Dt=function(t){return void 0===t&&(t={}),{name:"flip",options:t,async fn(e){var n,o;const{placement:r,middlewareData:i,rects:l,initialPlacement:c,platform:s,elements:a}=e,{mainAxis:f=!0,crossAxis:u=!0,fallbackPlacements:p,fallbackStrategy:y="bestFit",fallbackAxisSideDirection:b="none",flipAlignment:R=!0,...A}=d(t,e);if(null!=(n=i.arrow)&&n.alignmentOffset)return{};const E=h(r),D=w(c),S=h(c)===c,O=await(null==s.isRTL?void 0:s.isRTL(a.floating)),P=p||(S||!R?[T(c)]:function(t){const e=T(t);return[v(t),e,v(e)]}(c)),k="none"!==b;!p&&k&&P.push(...L(c,R,b,O));const F=[c,...P],H=await C(e,A),B=[];let j=(null==(o=i.flip)?void 0:o.overflows)||[];if(f&&B.push(H[E]),u){const t=function(t,e,n){void 0===n&&(n=!1);const o=m(t),r=x(t),i=g(r);let l="x"===r?o===(n?"end":"start")?"right":"left":"start"===o?"bottom":"top";return e.reference[i]>e.floating[i]&&(l=T(l)),[l,T(l)]}(r,l,O);B.push(H[t[0]],H[t[1]])}if(j=[...j,{placement:r,overflows:B}],!B.every(t=>t<=0)){var M,W;const t=((null==(M=i.flip)?void 0:M.index)||0)+1,e=F[t];if(e){if(!("alignment"===u&&D!==w(e))||j.every(t=>t.overflows[0]>0&&w(t.placement)===D))return{data:{index:t,overflows:j},reset:{placement:e}}}let n=null==(W=j.filter(t=>t.overflows[0]<=0).sort((t,e)=>t.overflows[1]-e.overflows[1])[0])?void 0:W.placement;if(!n)switch(y){case"bestFit":{var U;const t=null==(U=j.filter(t=>{if(k){const e=w(t.placement);return e===D||"y"===e}return!0}).map(t=>[t.placement,t.overflows.filter(t=>t>0).reduce((t,e)=>t+e,0)]).sort((t,e)=>t[1]-e[1])[0])?void 0:U[0];t&&(n=t);break}case"initialPlacement":n=c}if(r!==n)return{reset:{placement:n}}}return{}}}},St=function(t){return void 0===t&&(t={}),{name:"size",options:t,async fn(e){var n,o;const{placement:l,rects:c,platform:s,elements:a}=e,{apply:f=()=>{},...u}=d(t,e),p=await C(e,u),g=h(l),y=m(l),x="y"===w(l),{width:v,height:b}=c.floating;let R,A;"top"===g||"bottom"===g?(R=g,A=y===(await(null==s.isRTL?void 0:s.isRTL(a.floating))?"start":"end")?"left":"right"):(A=g,R="end"===y?"top":"bottom");const E=b-p.top-p.bottom,L=v-p.left-p.right,T=r(b-p[R],E),D=r(v-p[A],L),S=!e.middlewareData.shift;let O=T,P=D;if(null!=(n=e.middlewareData.shift)&&n.enabled.x&&(P=L),null!=(o=e.middlewareData.shift)&&o.enabled.y&&(O=E),S&&!y){const t=i(p.left,0),e=i(p.right,0),n=i(p.top,0),o=i(p.bottom,0);x?P=v-2*(0!==t||0!==e?t+e:i(p.left,p.right)):O=b-2*(0!==n||0!==o?n+o:i(p.top,p.bottom))}await f({...e,availableWidth:P,availableHeight:O});const k=await s.getDimensions(a.floating);return v!==k.width||b!==k.height?{reset:{rects:!0}}:{}}}},Ot=function(t){return void 0===t&&(t={}),{name:"hide",options:t,async fn(e){const{rects:n}=e,{strategy:o="referenceHidden",...r}=d(t,e);switch(o){case"referenceHidden":{const t=P(await C(e,{...r,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:t,referenceHidden:k(t)}}}case"escaped":{const t=P(await C(e,{...r,altBoundary:!0}),n.floating);return{data:{escapedOffsets:t,escaped:k(t)}}}default:return{}}}}},Ct=t=>({name:"arrow",options:t,async fn(e){const{x:n,y:o,placement:i,rects:l,platform:c,elements:s,middlewareData:a}=e,{element:f,padding:h=0}=d(t,e)||{};if(null==f)return{};const p=D(h),y={x:n,y:o},w=x(i),v=g(w),b=await c.getDimensions(f),R="y"===w,A=R?"top":"left",E=R?"bottom":"right",L=R?"clientHeight":"clientWidth",T=l.reference[v]+l.reference[w]-y[w]-l.floating[v],S=y[w]-l.reference[w],O=await(null==c.getOffsetParent?void 0:c.getOffsetParent(f));let C=O?O[L]:0;C&&await(null==c.isElement?void 0:c.isElement(O))||(C=s.floating[L]||l.floating[v]);const P=T/2-S/2,k=C/2-b[v]/2-1,F=r(p[A],k),H=r(p[E],k),B=F,j=C-b[v]-H,M=C/2-b[v]/2+P,W=u(B,M,j),U=!a.arrow&&null!=m(i)&&M!==W&&l.reference[v]/2-(M<B?F:H)-b[v]/2<0,V=U?M<B?M-B:M-j:0;return{[w]:y[w]+V,data:{[w]:W,centerOffset:M-W-V,...U&&{alignmentOffset:V}},reset:U}}}),Pt=function(t){return void 0===t&&(t={}),{options:t,fn(e){const{x:n,y:o,placement:r,rects:i,middlewareData:l}=e,{offset:c=0,mainAxis:s=!0,crossAxis:a=!0}=d(t,e),f={x:n,y:o},u=w(r),m=p(u);let g=f[m],y=f[u];const x=d(c,e),v="number"===typeof x?{mainAxis:x,crossAxis:0}:{mainAxis:0,crossAxis:0,...x};if(s){const t="y"===m?"height":"width",e=i.reference[m]-i.floating[t]+v.mainAxis,n=i.reference[m]+i.reference[t]-v.mainAxis;g<e?g=e:g>n&&(g=n)}if(a){var b,R;const t="y"===m?"width":"height",e=F.has(h(r)),n=i.reference[u]-i.floating[t]+(e&&(null==(b=l.offset)?void 0:b[u])||0)+(e?0:v.crossAxis),o=i.reference[u]+i.reference[t]+(e?0:(null==(R=l.offset)?void 0:R[u])||0)-(e?v.crossAxis:0);y<n?y=n:y>o&&(y=o)}return{[m]:g,[u]:y}}}},kt=(t,e,n)=>{const o=new Map,r={platform:Rt,...n},i={...r.platform,_c:o};return(async(t,e,n)=>{const{placement:o="bottom",strategy:r="absolute",middleware:i=[],platform:l}=n,c=i.filter(Boolean),s=await(null==l.isRTL?void 0:l.isRTL(e));let a=await l.getElementRects({reference:t,floating:e,strategy:r}),{x:f,y:u}=O(a,o,s),d=o,h={},m=0;for(let p=0;p<c.length;p++){const{name:n,fn:i}=c[p],{x:g,y:y,data:w,reset:x}=await i({x:f,y:u,initialPlacement:o,placement:d,strategy:r,middlewareData:h,rects:a,platform:l,elements:{reference:t,floating:e}});f=null!=g?g:f,u=null!=y?y:u,h={...h,[n]:{...h[n],...w}},x&&m<=50&&(m++,"object"===typeof x&&(x.placement&&(d=x.placement),x.rects&&(a=!0===x.rects?await l.getElementRects({reference:t,floating:e,strategy:r}):x.rects),({x:f,y:u}=O(a,d,s))),p=-1)}return{x:f,y:u,placement:d,strategy:r,middlewareData:h}})(t,e,{...r,platform:i})}}}]);
//# sourceMappingURL=975.47a9de52.chunk.js.map