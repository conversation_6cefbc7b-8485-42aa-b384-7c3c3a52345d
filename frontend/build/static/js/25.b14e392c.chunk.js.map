{"version": 3, "file": "static/js/25.b14e392c.chunk.js", "mappings": "oPAaM,MAAAA,GAAOC,EAAAA,EAAAA,GAAiB,OAAQ,CACpC,CAAC,WAAY,CAAEC,OAAQ,mBAAoBC,IAAK,WAChD,CAAC,WAAY,CAAED,OAAQ,gBAAiBC,IAAK,a,sDCFzC,MAAAC,GAAeH,EAAAA,EAAAA,GAAiB,eAAgB,CACpD,CACE,OACA,CACEI,EAAG,2DACHF,IAAK,WAGT,CAAC,WAAY,CAAED,OAAQ,iBAAkBC,IAAK,WAC9C,CAAC,OAAQ,CAAEG,GAAI,KAAMC,GAAI,KAAMC,GAAI,KAAMC,GAAI,IAAKN,IAAK,aCTnDO,GAAWT,EAAAA,EAAAA,GAAiB,WAAY,CAC5C,CACE,OACA,CACEI,EAAG,wjBACHF,IAAK,WAGT,CAAC,SAAU,CAAEQ,GAAI,KAAMC,GAAI,KAAMC,EAAG,IAAKV,IAAK,a,uBCXhD,MAoNA,EApN4BW,KAC1B,MAAOC,EAAQC,IAAaC,EAAAA,EAAAA,UAAS,iCAC9BC,EAAYC,IAAiBF,EAAAA,EAAAA,WAAS,IACtCG,EAAYC,IAAiBJ,EAAAA,EAAAA,UAAS,uDACtCK,EAAQC,IAAaN,EAAAA,EAAAA,WAAS,IAC9BO,EAAgBC,IAAqBR,EAAAA,EAAAA,WAAS,GAE/CS,EAAcC,IAClBC,UAAUC,UAAUC,UAAUH,GAC9BJ,GAAU,GACVQ,WAAW,IAAMR,GAAU,GAAQ,MAYrC,OACES,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oCAAmCC,SAAC,0BAClDC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wBAAuBC,SAAC,yEAKvCF,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACC,aAAa,MAAMJ,UAAU,YAAWC,SAAA,EAC5CF,EAAAA,EAAAA,MAACM,EAAAA,GAAQ,CAAAJ,SAAA,EACPF,EAAAA,EAAAA,MAACO,EAAAA,GAAW,CAACC,MAAM,MAAKN,SAAA,EACtBC,EAAAA,EAAAA,KAACnC,EAAI,CAACiC,UAAU,iBAAiB,eAGnCD,EAAAA,EAAAA,MAACO,EAAAA,GAAW,CAACC,MAAM,WAAUN,SAAA,EAC3BC,EAAAA,EAAAA,KAACM,EAAAA,EAAI,CAACR,UAAU,iBAAiB,eAGnCD,EAAAA,EAAAA,MAACO,EAAAA,GAAW,CAACC,MAAM,WAAUN,SAAA,EAC3BC,EAAAA,EAAAA,KAACO,EAAAA,EAAI,CAACT,UAAU,iBAAiB,kBAKrCE,EAAAA,EAAAA,KAACQ,EAAAA,GAAW,CAACH,MAAM,MAAMP,UAAU,YAAWC,UAC5CF,EAAAA,EAAAA,MAACY,EAAAA,GAAI,CAAAV,SAAA,EACHF,EAAAA,EAAAA,MAACa,EAAAA,GAAU,CAAAX,SAAA,EACTC,EAAAA,EAAAA,KAACW,EAAAA,GAAS,CAAAZ,SAAC,cACXC,EAAAA,EAAAA,KAACY,EAAAA,GAAe,CAAAb,SAAC,2GAInBF,EAAAA,EAAAA,MAACgB,EAAAA,GAAW,CAACf,UAAU,YAAWC,SAAA,EAChCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8CAA6CC,SAAA,EAC1DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,SAAQC,SAAA,EACrBC,EAAAA,EAAAA,KAACc,EAAAA,EAAK,CAACC,QAAQ,UAAShB,SAAC,oBACzBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mCAAkCC,SAAA,EAC/CC,EAAAA,EAAAA,KAACgB,EAAAA,EAAK,CACJC,GAAG,UACHZ,MAAOzB,EACPsC,UAAQ,EACRpB,UAAU,eAEZE,EAAAA,EAAAA,KAACmB,EAAAA,EAAM,CACLC,QAAQ,UACRC,KAAK,OACLC,QAASA,IAAM/B,EAAWX,GAC1B2C,SAAUpC,EAAOY,SAEhBZ,GAASa,EAAAA,EAAAA,KAACwB,EAAAA,EAAK,CAAC1B,UAAU,aAAeE,EAAAA,EAAAA,KAACyB,EAAAA,EAAI,CAAC3B,UAAU,qBAIhEE,EAAAA,EAAAA,KAACmB,EAAAA,EAAM,CACLC,QAAQ,UACRE,QAjESI,KACvBpC,GAAkB,GAElBM,WAAW,KACTf,EAAU,MAAM8C,KAAKC,SAASC,SAAS,IAAIC,UAAU,EAAG,OACxDxC,GAAkB,IACjB,MA4DWiC,SAAUlC,EACVS,UAAU,OAAMC,SAEfV,GACCQ,EAAAA,EAAAA,MAAAkC,EAAAA,SAAA,CAAAhC,SAAA,EACEC,EAAAA,EAAAA,KAACgC,EAAAA,EAAS,CAAClC,UAAU,8BAA8B,qBAIrD,uBAKND,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAACiC,EAAAA,EAAM,CACLhB,GAAG,YACHiB,QAASnD,EACToD,gBAAiBnD,KAEnBgB,EAAAA,EAAAA,KAACc,EAAAA,EAAK,CAACC,QAAQ,YAAWhB,SACvBhB,EAAa,YAAc,eAE9BiB,EAAAA,EAAAA,KAACoC,EAAAA,EAAK,CAAChB,QAASrC,EAAa,cAAgB,YAAae,UAAU,OAAMC,SACvEhB,EAAa,4BAA8B,0CAIhDc,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0BAAyBC,SAAA,EACtCC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mBAAkBC,SAAC,uBACjCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qCAAoCC,SAAC,mFAGlDF,EAAAA,EAAAA,MAACsB,EAAAA,EAAM,CAACC,QAAQ,UAAUC,KAAK,KAAItB,SAAA,EACjCC,EAAAA,EAAAA,KAAC/B,EAAY,CAAC6B,UAAU,iBAAiB,wCAQnDE,EAAAA,EAAAA,KAACQ,EAAAA,GAAW,CAACH,MAAM,WAAWP,UAAU,YAAWC,UACjDF,EAAAA,EAAAA,MAACY,EAAAA,GAAI,CAAAV,SAAA,EACHF,EAAAA,EAAAA,MAACa,EAAAA,GAAU,CAAAX,SAAA,EACTC,EAAAA,EAAAA,KAACW,EAAAA,GAAS,CAAAZ,SAAC,2BACXC,EAAAA,EAAAA,KAACY,EAAAA,GAAe,CAAAb,SAAC,gFAInBC,EAAAA,EAAAA,KAACa,EAAAA,GAAW,CAAAd,UACVF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAACc,EAAAA,EAAK,CAACC,QAAQ,cAAahB,SAAC,iBAC7BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mCAAkCC,SAAA,EAC/CC,EAAAA,EAAAA,KAACgB,EAAAA,EAAK,CACJC,GAAG,cACHZ,MAAOpB,EACPoD,SAAWC,GAAMpD,EAAcoD,EAAEC,OAAOlC,OACxCmC,YAAY,gDAEd3C,EAAAA,EAAAA,MAACsB,EAAAA,EAAM,CAACC,QAAQ,UAAUE,QAASA,IAAM/B,EAAWN,GAAYc,SAAA,EAC9DC,EAAAA,EAAAA,KAACyB,EAAAA,EAAI,CAAC3B,UAAU,iBACfX,EAAS,UAAY,iBAK5BU,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0BAAyBC,SAAA,EACtCC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mBAAkBC,SAAC,oBACjCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qCAAoCC,SAAC,4EAGlDF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,yDAAwDC,SAAA,EACpEF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EAAIC,EAAAA,EAAAA,KAAA,QAAAD,SAAM,sBAAwB,kDAClCF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EAAIC,EAAAA,EAAAA,KAAA,QAAAD,SAAM,mBAAqB,8BAC/BF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EAAIC,EAAAA,EAAAA,KAAA,QAAAD,SAAM,qBAAuB,oCACjCF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EAAIC,EAAAA,EAAAA,KAAA,QAAAD,SAAM,2BAA6B,mDAQnDC,EAAAA,EAAAA,KAACQ,EAAAA,GAAW,CAACH,MAAM,WAAWP,UAAU,YAAWC,UACjDF,EAAAA,EAAAA,MAACY,EAAAA,GAAI,CAAAV,SAAA,EACHF,EAAAA,EAAAA,MAACa,EAAAA,GAAU,CAAAX,SAAA,EACTC,EAAAA,EAAAA,KAACW,EAAAA,GAAS,CAAAZ,SAAC,uBACXC,EAAAA,EAAAA,KAACY,EAAAA,GAAe,CAAAb,SAAC,0DAInBF,EAAAA,EAAAA,MAACgB,EAAAA,GAAW,CAACf,UAAU,YAAWC,SAAA,EAChCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,cAAaC,SAAC,+BAC5BC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gCAA+BC,SAAC,uDAI/CF,EAAAA,EAAAA,MAACsB,EAAAA,EAAM,CAACC,QAAQ,UAASrB,SAAA,EACvBC,EAAAA,EAAAA,KAACzB,EAAQ,CAACuB,UAAU,iBAAiB,uBAKzCD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gBAAeC,SAAA,EAC5BC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mBAAkBC,SAAC,qBACjCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qCAAoCC,SAAC,yEAGlDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAACgB,EAAAA,EAAK,CACJwB,YAAY,cACZ1C,UAAU,cAEZE,EAAAA,EAAAA,KAACmB,EAAAA,EAAM,CAACC,QAAQ,UAASrB,SAAC,eAE5BC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gCAA+BC,SAAC,yG,kCCpMzD,MAAAiC,GAAYlE,E,QAAAA,GAAiB,YAAa,CAC9C,CACE,OACA,CAAEI,EAAG,qDAAsDF,IAAK,WAElE,CAAC,OAAQ,CAAEE,EAAG,aAAcF,IAAK,WACjC,CACE,OACA,CAAEE,EAAG,sDAAuDF,IAAK,WAEnE,CAAC,OAAQ,CAAEE,EAAG,YAAaF,IAAK,Y,kCCV5B,MAAAyD,GAAO3D,E,QAAAA,GAAiB,OAAQ,CACpC,CACE,OACA,CACE2E,MAAO,KACPC,OAAQ,KACRC,EAAG,IACHC,EAAG,IACHC,GAAI,IACJC,GAAI,IACJ9E,IAAK,WAGT,CACE,OACA,CACEE,EAAG,0DACHF,IAAK,Y,kCCjBL,MAAAuC,GAAOzC,E,QAAAA,GAAiB,OAAQ,CACpC,CACE,OACA,CACE2E,MAAO,KACPC,OAAQ,KACRC,EAAG,IACHC,EAAG,KACHC,GAAI,IACJC,GAAI,IACJ9E,IAAK,WAGT,CAAC,OAAQ,CAAEE,EAAG,2BAA4BF,IAAK,Y,mECnBjD,MAAMgD,EAAQ+B,EAAAA,WACZ,CAAAC,EAAgCC,KAAS,IAAxC,UAAEnD,EAAS,KAAEoD,KAASC,GAAOH,EAC5B,OACEhD,EAAAA,EAAAA,KAAA,SACEkD,KAAMA,EACNpD,WAAWsD,EAAAA,EAAAA,IACT,+VACAtD,GAEFmD,IAAKA,KACDE,MAKZnC,EAAMqC,YAAc,O", "sources": ["../node_modules/lucide-react/src/icons/code.ts", "../node_modules/lucide-react/src/icons/external-link.ts", "../node_modules/lucide-react/src/icons/settings.ts", "pages/merchant/IntegrationSettings.tsx", "../node_modules/lucide-react/src/icons/refresh-cw.ts", "../node_modules/lucide-react/src/icons/copy.ts", "../node_modules/lucide-react/src/icons/lock.ts", "components/ui/input.tsx"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Code\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWxpbmUgcG9pbnRzPSIxNiAxOCAyMiAxMiAxNiA2IiAvPgogIDxwb2x5bGluZSBwb2ludHM9IjggNiAyIDEyIDggMTgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/code\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Code = createLucideIcon('Code', [\n  ['polyline', { points: '16 18 22 12 16 6', key: 'z7tu5w' }],\n  ['polyline', { points: '8 6 2 12 8 18', key: '1eg1df' }],\n]);\n\nexport default Code;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ExternalLink\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTggMTN2NmEyIDIgMCAwIDEtMiAySDVhMiAyIDAgMCAxLTItMlY4YTIgMiAwIDAgMSAyLTJoNiIgLz4KICA8cG9seWxpbmUgcG9pbnRzPSIxNSAzIDIxIDMgMjEgOSIgLz4KICA8bGluZSB4MT0iMTAiIHgyPSIyMSIgeTE9IjE0IiB5Mj0iMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/external-link\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ExternalLink = createLucideIcon('ExternalLink', [\n  [\n    'path',\n    {\n      d: 'M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6',\n      key: 'a6xqqp',\n    },\n  ],\n  ['polyline', { points: '15 3 21 3 21 9', key: 'mznyad' }],\n  ['line', { x1: '10', x2: '21', y1: '14', y2: '3', key: '18c3s4' }],\n]);\n\nexport default ExternalLink;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Settings\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIuMjIgMmgtLjQ0YTIgMiAwIDAgMC0yIDJ2LjE4YTIgMiAwIDAgMS0xIDEuNzNsLS40My4yNWEyIDIgMCAwIDEtMiAwbC0uMTUtLjA4YTIgMiAwIDAgMC0yLjczLjczbC0uMjIuMzhhMiAyIDAgMCAwIC43MyAyLjczbC4xNS4xYTIgMiAwIDAgMSAxIDEuNzJ2LjUxYTIgMiAwIDAgMS0xIDEuNzRsLS4xNS4wOWEyIDIgMCAwIDAtLjczIDIuNzNsLjIyLjM4YTIgMiAwIDAgMCAyLjczLjczbC4xNS0uMDhhMiAyIDAgMCAxIDIgMGwuNDMuMjVhMiAyIDAgMCAxIDEgMS43M1YyMGEyIDIgMCAwIDAgMiAyaC40NGEyIDIgMCAwIDAgMi0ydi0uMThhMiAyIDAgMCAxIDEtMS43M2wuNDMtLjI1YTIgMiAwIDAgMSAyIDBsLjE1LjA4YTIgMiAwIDAgMCAyLjczLS43M2wuMjItLjM5YTIgMiAwIDAgMC0uNzMtMi43M2wtLjE1LS4wOGEyIDIgMCAwIDEtMS0xLjc0di0uNWEyIDIgMCAwIDEgMS0xLjc0bC4xNS0uMDlhMiAyIDAgMCAwIC43My0yLjczbC0uMjItLjM4YTIgMiAwIDAgMC0yLjczLS43M2wtLjE1LjA4YTIgMiAwIDAgMS0yIDBsLS40My0uMjVhMiAyIDAgMCAxLTEtMS43M1Y0YTIgMiAwIDAgMC0yLTJ6IiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTIiIHI9IjMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/settings\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Settings = createLucideIcon('Settings', [\n  [\n    'path',\n    {\n      d: 'M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z',\n      key: '1qme2f',\n    },\n  ],\n  ['circle', { cx: '12', cy: '12', r: '3', key: '1v7zrd' }],\n]);\n\nexport default Settings;\n", "import React, { useState } from 'react';\nimport { Card, CardContent, CardHeader, CardTitle, CardDescription } from '../../components/ui/card';\nimport { Button } from '../../components/ui/button';\nimport { Input } from '../../components/ui/input';\nimport { Label } from '../../components/ui/label';\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs';\nimport { Switch } from '../../components/ui/switch';\nimport { Copy, Check, RefreshCw, ExternalLink, Code, Settings, Bell, Lock } from 'lucide-react';\nimport { Badge } from '../../components/ui/badge';\n\nconst IntegrationSettings = () => {\n  const [apiKey, setApiKey] = useState('sk_test_51N1lZ2SJv8rX9vX7...');\n  const [isLiveMode, setIsLiveMode] = useState(false);\n  const [webhookUrl, setWebhookUrl] = useState('https://your-merchant-url.com/api/webhooks/payment');\n  const [copied, setCopied] = useState(false);\n  const [isRegenerating, setIsRegenerating] = useState(false);\n\n  const handleCopy = (text: string) => {\n    navigator.clipboard.writeText(text);\n    setCopied(true);\n    setTimeout(() => setCopied(false), 2000);\n  };\n\n  const regenerateApiKey = () => {\n    setIsRegenerating(true);\n    // Simulate API call\n    setTimeout(() => {\n      setApiKey(`sk_${Math.random().toString(36).substring(2, 42)}`);\n      setIsRegenerating(false);\n    }, 1000);\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div>\n        <h2 className=\"text-2xl font-bold tracking-tight\">Integration Settings</h2>\n        <p className=\"text-muted-foreground\">\n          Configure how your application connects with our payment gateway\n        </p>\n      </div>\n\n      <Tabs defaultValue=\"api\" className=\"space-y-4\">\n        <TabsList>\n          <TabsTrigger value=\"api\">\n            <Code className=\"mr-2 h-4 w-4\" />\n            API Keys\n          </TabsTrigger>\n          <TabsTrigger value=\"webhooks\">\n            <Bell className=\"mr-2 h-4 w-4\" />\n            Webhooks\n          </TabsTrigger>\n          <TabsTrigger value=\"security\">\n            <Lock className=\"mr-2 h-4 w-4\" />\n            Security\n          </TabsTrigger>\n        </TabsList>\n\n        <TabsContent value=\"api\" className=\"space-y-4\">\n          <Card>\n            <CardHeader>\n              <CardTitle>API Keys</CardTitle>\n              <CardDescription>\n                Your API keys are used to authenticate requests to our API. Keep them secret and never share them.\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-6\">\n              <div className=\"flex items-center justify-between space-x-4\">\n                <div className=\"flex-1\">\n                  <Label htmlFor=\"api-key\">Secret API Key</Label>\n                  <div className=\"flex items-center space-x-2 mt-1\">\n                    <Input\n                      id=\"api-key\"\n                      value={apiKey}\n                      readOnly\n                      className=\"font-mono\"\n                    />\n                    <Button\n                      variant=\"outline\"\n                      size=\"icon\"\n                      onClick={() => handleCopy(apiKey)}\n                      disabled={copied}\n                    >\n                      {copied ? <Check className=\"h-4 w-4\" /> : <Copy className=\"h-4 w-4\" />}\n                    </Button>\n                  </div>\n                </div>\n                <Button\n                  variant=\"outline\"\n                  onClick={regenerateApiKey}\n                  disabled={isRegenerating}\n                  className=\"mt-6\"\n                >\n                  {isRegenerating ? (\n                    <>\n                      <RefreshCw className=\"mr-2 h-4 w-4 animate-spin\" />\n                      Regenerating...\n                    </>\n                  ) : (\n                    'Regenerate Key'\n                  )}\n                </Button>\n              </div>\n\n              <div className=\"flex items-center space-x-2\">\n                <Switch\n                  id=\"live-mode\"\n                  checked={isLiveMode}\n                  onCheckedChange={setIsLiveMode}\n                />\n                <Label htmlFor=\"live-mode\">\n                  {isLiveMode ? 'Live Mode' : 'Test Mode'}\n                </Label>\n                <Badge variant={isLiveMode ? 'destructive' : 'secondary'} className=\"ml-2\">\n                  {isLiveMode ? 'Charges will be processed' : 'No real charges will be processed'}\n                </Badge>\n              </div>\n\n              <div className=\"rounded-md bg-muted p-4\">\n                <h4 className=\"font-medium mb-2\">API Documentation</h4>\n                <p className=\"text-sm text-muted-foreground mb-4\">\n                  Check out our API documentation for detailed integration guides and examples.\n                </p>\n                <Button variant=\"outline\" size=\"sm\">\n                  <ExternalLink className=\"mr-2 h-4 w-4\" />\n                  View API Documentation\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"webhooks\" className=\"space-y-4\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Webhook Configuration</CardTitle>\n              <CardDescription>\n                Receive real-time updates about payment events directly to your server.\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                <div>\n                  <Label htmlFor=\"webhook-url\">Webhook URL</Label>\n                  <div className=\"flex items-center space-x-2 mt-1\">\n                    <Input\n                      id=\"webhook-url\"\n                      value={webhookUrl}\n                      onChange={(e) => setWebhookUrl(e.target.value)}\n                      placeholder=\"https://your-merchant-url.com/api/webhooks\"\n                    />\n                    <Button variant=\"outline\" onClick={() => handleCopy(webhookUrl)}>\n                      <Copy className=\"mr-2 h-4 w-4\" />\n                      {copied ? 'Copied!' : 'Copy'}\n                    </Button>\n                  </div>\n                </div>\n\n                <div className=\"rounded-md bg-muted p-4\">\n                  <h4 className=\"font-medium mb-2\">Webhook Events</h4>\n                  <p className=\"text-sm text-muted-foreground mb-2\">\n                    We'll send POST requests to your webhook URL for the following events:\n                  </p>\n                  <ul className=\"list-disc pl-5 space-y-1 text-sm text-muted-foreground\">\n                    <li><code>payment.succeeded</code> - When a payment is successfully completed</li>\n                    <li><code>payment.failed</code> - When a payment fails</li>\n                    <li><code>payment.refunded</code> - When a payment is refunded</li>\n                    <li><code>charge.dispute.created</code> - When a dispute is created</li>\n                  </ul>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"security\" className=\"space-y-4\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Security Settings</CardTitle>\n              <CardDescription>\n                Manage your account security and access controls.\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <h4 className=\"font-medium\">Two-Factor Authentication</h4>\n                  <p className=\"text-sm text-muted-foreground\">\n                    Add an extra layer of security to your account\n                  </p>\n                </div>\n                <Button variant=\"outline\">\n                  <Settings className=\"mr-2 h-4 w-4\" />\n                  Configure 2FA\n                </Button>\n              </div>\n\n              <div className=\"border-t pt-4\">\n                <h4 className=\"font-medium mb-2\">IP Whitelisting</h4>\n                <p className=\"text-sm text-muted-foreground mb-4\">\n                  Restrict API access to specific IP addresses for enhanced security.\n                </p>\n                <div className=\"space-y-2\">\n                  <div className=\"flex items-center space-x-2\">\n                    <Input\n                      placeholder=\"***********\"\n                      className=\"max-w-xs\"\n                    />\n                    <Button variant=\"outline\">Add IP</Button>\n                  </div>\n                  <p className=\"text-xs text-muted-foreground\">\n                    Leave empty to allow access from any IP address (not recommended for production)\n                  </p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n      </Tabs>\n    </div>\n  );\n};\n\nexport default IntegrationSettings;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name RefreshCw\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAxMmE5IDkgMCAwIDEgOS05IDkuNzUgOS43NSAwIDAgMSA2Ljc0IDIuNzRMMjEgOCIgLz4KICA8cGF0aCBkPSJNMjEgM3Y1aC01IiAvPgogIDxwYXRoIGQ9Ik0yMSAxMmE5IDkgMCAwIDEtOSA5IDkuNzUgOS43NSAwIDAgMS02Ljc0LTIuNzRMMyAxNiIgLz4KICA8cGF0aCBkPSJNOCAxNkgzdjUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/refresh-cw\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst RefreshCw = createLucideIcon('RefreshCw', [\n  [\n    'path',\n    { d: 'M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8', key: 'v9h5vc' },\n  ],\n  ['path', { d: 'M21 3v5h-5', key: '1q7to0' }],\n  [\n    'path',\n    { d: 'M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16', key: '3uifl3' },\n  ],\n  ['path', { d: 'M8 16H3v5', key: '1cv678' }],\n]);\n\nexport default RefreshCw;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Copy\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHg9IjgiIHk9IjgiIHJ4PSIyIiByeT0iMiIgLz4KICA8cGF0aCBkPSJNNCAxNmMtMS4xIDAtMi0uOS0yLTJWNGMwLTEuMS45LTIgMi0yaDEwYzEuMSAwIDIgLjkgMiAyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/copy\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Copy = createLucideIcon('Copy', [\n  [\n    'rect',\n    {\n      width: '14',\n      height: '14',\n      x: '8',\n      y: '8',\n      rx: '2',\n      ry: '2',\n      key: '17jyea',\n    },\n  ],\n  [\n    'path',\n    {\n      d: 'M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2',\n      key: 'zix9uf',\n    },\n  ],\n]);\n\nexport default Copy;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Lock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTEiIHg9IjMiIHk9IjExIiByeD0iMiIgcnk9IjIiIC8+CiAgPHBhdGggZD0iTTcgMTFWN2E1IDUgMCAwIDEgMTAgMHY0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/lock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Lock = createLucideIcon('Lock', [\n  [\n    'rect',\n    {\n      width: '18',\n      height: '11',\n      x: '3',\n      y: '11',\n      rx: '2',\n      ry: '2',\n      key: '1w4ew1',\n    },\n  ],\n  ['path', { d: 'M7 11V7a5 5 0 0 1 10 0v4', key: 'fwvmzm' }],\n]);\n\nexport default Lock;\n", "import * as React from \"react\"\n\nimport { cn } from \"../../lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": ["Code", "createLucideIcon", "points", "key", "ExternalLink", "d", "x1", "x2", "y1", "y2", "Settings", "cx", "cy", "r", "IntegrationSettings", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useState", "isLiveMode", "setIsLiveMode", "webhookUrl", "setWebhookUrl", "copied", "setCopied", "isRegenerating", "setIsRegenerating", "handleCopy", "text", "navigator", "clipboard", "writeText", "setTimeout", "_jsxs", "className", "children", "_jsx", "Tabs", "defaultValue", "TabsList", "TabsTrigger", "value", "Bell", "Lock", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "CardDescription", "<PERSON><PERSON><PERSON><PERSON>", "Label", "htmlFor", "Input", "id", "readOnly", "<PERSON><PERSON>", "variant", "size", "onClick", "disabled", "Check", "Copy", "regenerateApiKey", "Math", "random", "toString", "substring", "_Fragment", "RefreshCw", "Switch", "checked", "onCheckedChange", "Badge", "onChange", "e", "target", "placeholder", "width", "height", "x", "y", "rx", "ry", "React", "_ref", "ref", "type", "props", "cn", "displayName"], "sourceRoot": ""}