import axios, { AxiosInstance, AxiosRequestConfig, CancelTokenSource, InternalAxiosRequestConfig } from 'axios';
import { API_BASE_URL } from '../config';
import { z } from 'zod';

// Base API Response Schema
export const ApiResponseSchema = <T>(dataSchema: z.ZodType<T>) =>
  z.object({
    success: z.boolean(),
    message: z.string().optional(),
    data: dataSchema,
    meta: z
      .object({
        total: z.number().optional(),
        page: z.number().optional(),
        limit: z.number().optional(),
        totalPages: z.number().optional(),
      })
      .optional(),
  });

export type ApiResponseType<T> = z.infer<ReturnType<typeof ApiResponseSchema<z.ZodType<T>>>>;

// Common Types
export type PaginationParams = {
  page?: number;
  limit?: number;
  sortBy?: string;
  order?: 'asc' | 'desc';
};

export type SearchParams = {
  search?: string;
  [key: string]: any;
};

// Error Types
export class ApiError extends Error {
  status?: number;
  code?: string;
  details?: unknown;

  constructor(message: string, status?: number, code?: string, details?: Record<string, unknown>) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.code = code;
    this.details = details;
    
    // Set the prototype explicitly for TypeScript
    Object.setPrototypeOf(this, ApiError.prototype);
  }
}

export type NetworkError = {
  type: 'network';
  message: string;
  code?: string;
};

export type ValidationError = {
  type: 'validation';
  message: string;
  fields: Record<string, string[]>;
};

type ErrorResponse = {
  message: string;
  code?: string;
  errors?: Record<string, string[]>;
};

// Enums
export enum AssignmentStatus {
  ACTIVE = 'active',
  PENDING = 'pending',
  COMPLETED = 'completed',
  INACTIVE = 'inactive',
}

export enum AssignmentType {
  PERMANENT = 'permanent',
  TEMPORARY = 'temporary',
  PROJECT_BASED = 'project_based',
}

export enum TraderStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
}

// Domain Models
export interface User {
  _id: string;
  name: string;
  email: string;
  phone?: string;
  status: TraderStatus;
  role: string;
  createdAt: string;
  updatedAt: string;
}

// Trader interface is now inferred from TraderSchema

export interface Merchant {
  _id: string;
  businessName: string;
  email: string;
  phone?: string;
  address?: {
    street: string;
    city: string;
    state: string;
    country: string;
    postalCode: string;
  };
  status: 'active' | 'inactive' | 'suspended';
  metadata?: Record<string, unknown>;
  createdAt: string;
  updatedAt: string;
}

export interface Assignment {
  _id: string;
  trader: Pick<Trader, '_id' | 'name' | 'email'>;
  merchant: Pick<Merchant, '_id' | 'businessName'>;
  assignmentType: AssignmentType;
  startDate: string;
  endDate?: string;
  status: AssignmentStatus;
  collectionTarget?: {
    amount: number;
    currency: string;
    period: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  };
  permissions: string[];
  notes?: string;
  metadata?: Record<string, unknown>;
  createdAt: string;
  updatedAt: string;
}

export interface PerformanceMetrics {
  totalCollections: number;
  totalAmount: number;
  currency: string;
  averageCollectionTime: number; // in minutes
  successRate: number; // percentage
  metricsByPeriod: Array<{
    period: string;
    count: number;
    amount: number;
  }>;
  topMerchants: Array<{
    merchantId: string;
    merchantName: string;
    count: number;
    amount: number;
  }>;
}

export interface DashboardStats {
  totalTraders: number;
  activeTraders: number;
  totalMerchants: number;
  activeAssignments: number;
  pendingAssignments: number;
  totalCollections: number;
  totalAmount: number;
  currency: string;
  recentActivity: Array<{
    id: string;
    type: string;
    description: string;
    timestamp: string;
    userId: string;
    userName: string;
  }>;
}

// Request/Response Types
export interface PaginatedResponse<T> {
  data: T[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export interface GetTradersParams extends PaginationParams, SearchParams {
  status?: TraderStatus;
  role?: string;
  merchantId?: string;
}

export interface GetAssignmentsParams extends PaginationParams, SearchParams {
  status?: AssignmentStatus | AssignmentStatus[];
  traderId?: string;
  merchantId?: string;
  startDate?: string;
  endDate?: string;
  assignmentType?: AssignmentType;
}

export interface CreateTraderData {
  name: string;
  email: string;
  phone?: string;
  password: string;
  role?: 'trader' | 'admin';
  metadata?: Record<string, unknown>;
}

export interface UpdateTraderData {
  name?: string;
  email?: string;
  phone?: string;
  status?: TraderStatus;
  metadata?: Record<string, unknown>;
}

export interface CreateAssignmentData {
  traderId: string;
  merchantId: string;
  assignmentType: AssignmentType;
  startDate: string;
  endDate?: string;
  status?: AssignmentStatus;
  collectionTarget?: {
    amount: number;
    currency: string;
    period: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  };
  permissions?: string[];
  notes?: string;
  metadata?: Record<string, unknown>;
}

export interface UpdateAssignmentData extends Partial<CreateAssignmentData> {
  status?: AssignmentStatus;
  notes?: string;
  metadata?: Record<string, unknown>;
}

export interface UpdateAssignmentStatusData {
  status: AssignmentStatus;
  notes?: string;
}

export interface BulkAssignTradersData {
  traderIds: string[];
  merchantIds: string[];
  assignmentType: AssignmentType;
  startDate: string;
  endDate?: string;
  permissions?: string[];
  notes?: string;
}

export interface GenerateReportParams {
  startDate: string;
  endDate: string;
  format?: 'csv' | 'pdf' | 'xlsx';
  traderId?: string;
  merchantId?: string;
  status?: AssignmentStatus | AssignmentStatus[];
  assignmentType?: AssignmentType;
}

// Validation Schemas
// Base user schema with common fields
const BaseUserSchema = z.object({
  _id: z.string(),
  name: z.string().min(2).max(100),
  email: z.string().email(),
  phone: z.string().optional(),
  status: z.nativeEnum(TraderStatus),
  role: z.string(),
  metadata: z.record(z.string(), z.unknown()).optional(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

export const TraderSchema = BaseUserSchema.extend({
  role: z.custom<'trader' | 'admin'>((val) => val === 'trader' || val === 'admin'),
  lastLogin: z.string().datetime().optional(),
});

// Type for the trader schema
export type Trader = z.infer<typeof TraderSchema>;

export const AssignmentSchema = z.object({
  _id: z.string(),
  trader: z.object({
    _id: z.string(),
    name: z.string(),
    email: z.string().email(),
  }),
  merchant: z.object({
    _id: z.string(),
    businessName: z.string(),
  }),
  assignmentType: z.nativeEnum(AssignmentType),
  startDate: z.string().datetime(),
  endDate: z.string().datetime().optional(),
  status: z.nativeEnum(AssignmentStatus),
  collectionTarget: z
    .object({
      amount: z.number().positive(),
      currency: z.string().length(3),
      period: z.custom<'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly'>(
        (val) => ['daily', 'weekly', 'monthly', 'quarterly', 'yearly'].includes(String(val))
      ),
    })
    .optional(),
  permissions: z.array(z.string()),
  notes: z.string().optional(),
  metadata: z.record(z.string(), z.unknown()).optional(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

// Extend AxiosRequestConfig to include our custom properties
declare module 'axios' {
  export interface AxiosRequestConfig {
    responseSchema?: z.ZodType<unknown>;
    _retry?: boolean;
  }
}

// API Client Configuration
const DEFAULT_TIMEOUT = 10000; // 10 seconds
const MAX_RETRIES = 3;
const RETRY_DELAY = 1000; // 1 second

// Request cancellation
const cancelTokenSources: Record<string, CancelTokenSource> = {};

// Create a custom axios instance with proper typing
const createAxiosInstance = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: `${API_BASE_URL}/api/v1/trader`,
    timeout: DEFAULT_TIMEOUT,
    headers: {
      'Content-Type': 'application/json',
      'X-Requested-With': 'XMLHttpRequest',
    },
    withCredentials: true, // For CSRF protection
  });

  return instance;
};

// Create axios instance with default config
const traderApi = createAxiosInstance();

// Request interceptor for auth token
traderApi.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // Cancel previous request if it exists
    const requestId = `${config.method}-${config.url}`;
    if (cancelTokenSources[requestId]) {
      cancelTokenSources[requestId].cancel('Request canceled - new request made');
    }

    // Create new cancel token
    const source = axios.CancelToken.source();
    cancelTokenSources[requestId] = source;
    config.cancelToken = source.token;

    // Add auth token if exists
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Add request timestamp
    config.headers['X-Request-Start'] = new Date().getTime().toString();

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for handling errors and retries
let retryCount = 0;

traderApi.interceptors.response.use(
  (response) => {
    // Calculate request duration
    const start = response.config.headers['X-Request-Start'];
    if (start) {
      const duration = new Date().getTime() - parseInt(start, 10);
      console.debug(`API Request ${response.config.url} completed in ${duration}ms`);
    }

    // Clean up cancel token
    const requestId = `${response.config.method}-${response.config.url}`;
    if (cancelTokenSources[requestId]) {
      delete cancelTokenSources[requestId];
    }

    // Validate response against schema if available
    if (response.config.responseSchema) {
      const validation = response.config.responseSchema.safeParse(response.data);
      if (!validation.success) {
        console.error('Response validation failed:', validation.error);
        return Promise.reject(
          new ApiError(
            'Invalid response format',
            response.status,
            'INVALID_RESPONSE',
            validation.error as unknown as Record<string, unknown>
          )
        );
      }
      response.data = validation.data;
    }

    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // Handle cancellation
    if (axios.isCancel(error)) {
      console.log('Request canceled:', error.message);
      return Promise.reject(new ApiError('Request canceled', 0, 'CANCELLED'));
    }

    // Handle network errors
    if (!error.response) {
      return Promise.reject(
        new ApiError(
          error.request
            ? 'No response received from server'
            : 'Error setting up request',
          undefined,
          'NETWORK_ERROR'
        )
      );
    }

    const { status, data } = error.response;

    // Handle specific error statuses
    switch (status) {
      case 401: // Unauthorized
        // Handle token refresh here if needed
        console.error('Unauthorized - redirecting to login');
        // Redirect to login or refresh token
        break;
      case 403: // Forbidden
        return Promise.reject(
          new ApiError(
            data.message || 'You do not have permission to perform this action',
            status,
            'FORBIDDEN'
          )
        );
      case 404: // Not Found
        return Promise.reject(
          new ApiError(
            data.message || 'Resource not found',
            status,
            'NOT_FOUND'
          )
        );
      case 422: // Validation Error
        return Promise.reject(
          new ApiError(
            'Validation failed',
            status,
            'VALIDATION_ERROR',
            data.errors || {}
          )
        );
      case 429: // Too Many Requests
        return Promise.reject(
          new ApiError(
            data.message || 'Too many requests, please try again later',
            status,
            'RATE_LIMIT_EXCEEDED'
          )
        );
      case 500: // Internal Server Error
      case 502: // Bad Gateway
      case 503: // Service Unavailable
      case 504: // Gateway Timeout
        // Retry logic for server errors
        if (retryCount < MAX_RETRIES && !originalRequest._retry) {
          retryCount++;
          const delay = RETRY_DELAY * Math.pow(2, retryCount - 1);
          console.log(`Retrying request (${retryCount}/${MAX_RETRIES}) in ${delay}ms`);
          
          originalRequest._retry = true;
          return new Promise((resolve) =>
            setTimeout(() => resolve(traderApi(originalRequest)), delay)
          );
        }
        return Promise.reject(
          new ApiError(
            data.message || 'Server error, please try again later',
            status,
            'SERVER_ERROR'
          )
        );
      default:
        return Promise.reject(
          new ApiError(
            data.message || 'An error occurred',
            status,
            data.code || 'UNKNOWN_ERROR',
            data
          )
        );
    }
  }
);

// Helper function to make API requests with proper typing
export async function apiRequest<T>(
  config: AxiosRequestConfig,
  schema?: z.ZodType<T>
): Promise<T> {
  try {
    // Add response schema for validation if provided
    const response = await traderApi({
      ...config,
      responseSchema: schema,
    });
    
    return response.data.data;
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    
    // Handle Axios errors
    if (axios.isAxiosError(error)) {
      const status = error.response?.status || 0;
      const data = error.response?.data || {};
      
      throw new ApiError(
        data.message || error.message,
        status,
        data.code || 'API_ERROR',
        data
      );
    }
    
    // Handle unknown errors
    throw new ApiError(
      'An unknown error occurred',
      0,
      'UNKNOWN_ERROR',
      error
    );
  }
}

// API Response type for backward compatibility
export type ApiResponse<T> = {
  data: T;
  message?: string;
  success: boolean;
};

// Helper function to cancel pending requests
export function cancelPendingRequests() {
  Object.values(cancelTokenSources).forEach((source) => {
    source.cancel('Operation canceled by user');
  });
}

// Helper function to create query string from params
function createQueryString(params: Record<string, string | number | boolean | (string | number | boolean)[] | undefined>): string {
  const searchParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value === undefined || value === null) return;
    
    if (Array.isArray(value)) {
      value.forEach((item) => searchParams.append(key, String(item)));
    } else {
      searchParams.append(key, String(value));
    }
  });
  
  return searchParams.toString();
}

// Schema for paginated responses
const PaginatedResponseSchema = <T extends z.ZodTypeAny>(itemSchema: T) =>
  z.object({
    data: z.array(itemSchema),
    meta: z.object({
      total: z.number(),
      page: z.number(),
      limit: z.number(),
      totalPages: z.number(),
    }),
  });

// Admin-specific trader service
export const traderAdminService = {
  /**
   * Get all traders with pagination and filters
   * @param params Query parameters for filtering and pagination
   * @returns Paginated list of traders
   */
  async getTraders(params: GetTradersParams = {}): Promise<PaginatedResponse<Trader>> {
    const { page = 1, limit = 10, search, status, role, merchantId, sortBy, order } = params;
    
    const queryParams = createQueryString({
      page: page.toString(),
      limit: limit.toString(),
      ...(search && { search }),
      ...(status && { status }),
      ...(role && { role }),
      ...(merchantId && { merchantId }),
      ...(sortBy && { sortBy }),
      ...(order && { order }),
    });

    return apiRequest<PaginatedResponse<Trader>>(
      {
        method: 'GET',
        url: `/admin/traders?${queryParams}`,
      },
      PaginatedResponseSchema(TraderSchema)
    );
  },

  /**
   * Get trader details by ID
   * @param id Trader ID
   * @returns Trader details
   */
  async getTraderDetails(id: string): Promise<Trader> {
    return apiRequest<Trader>(
      {
        method: 'GET',
        url: `/admin/traders/${id}`,
      },
      TraderSchema
    );
  },

  /**
   * Update trader status
   * @param id Trader ID
   * @param status New status
   * @param reason Optional reason for status change
   * @returns Updated trader
   */
  async updateTraderStatus(
    id: string, 
    status: TraderStatus, 
    reason?: string
  ): Promise<Trader> {
    return apiRequest<Trader>(
      {
        method: 'PUT',
        url: `/admin/traders/${id}/status`,
        data: { status, reason },
      },
      TraderSchema
    );
  },

  /**
   * Create a new trader
   * @param data Trader data
   * @returns Created trader
   */
  async createTrader(data: CreateTraderData): Promise<Trader> {
    return apiRequest<Trader>(
      {
        method: 'POST',
        url: '/admin/traders',
        data,
      },
      TraderSchema
    );
  },

  /**
   * Update trader details
   * @param id Trader ID
   * @param data Data to update
   * @returns Updated trader
   */
  async updateTrader(id: string, data: UpdateTraderData): Promise<Trader> {
    return apiRequest<Trader>(
      {
        method: 'PUT',
        url: `/admin/traders/${id}`,
        data,
      },
      TraderSchema
    );
  },
};

export const traderService = {
  /**
   * Get current trader's assignments
   * @param status Optional status filter
   * @returns List of assignments for the current trader
   */
  async getMyAssignments(status?: AssignmentStatus | AssignmentStatus[]): Promise<Assignment[]> {
    const queryParams: Record<string, string | string[]> = {};
    
    if (status) {
      queryParams.status = Array.isArray(status) ? status : [status];
    }
    
    const params = createQueryString(queryParams);

    return apiRequest<Assignment[]>(
      {
        method: 'GET',
        url: `/traders/me/assignments${params ? `?${params}` : ''}`,
        responseSchema: z.array(AssignmentSchema).optional()
      },
      AssignmentSchema.array()
    );
  },

  /**
   * Get assignment details by ID
   * @param id Assignment ID
   * @returns Assignment details
   */
  async getAssignment(id: string): Promise<Assignment> {
    return apiRequest<Assignment>(
      {
        method: 'GET',
        url: `/assignments/${id}`,
      },
      AssignmentSchema
    );
  },

  /**
   * Update assignment status
   * @param id Assignment ID
   * @param status New status
   * @param notes Optional notes
   * @returns Updated assignment
   */
  async updateAssignmentStatus(
    id: string,
    status: AssignmentStatus,
    notes?: string
  ): Promise<Assignment> {
    return apiRequest<Assignment>(
      {
        method: 'PATCH',
        url: `/assignments/${id}/status`,
        data: { status, ...(notes && { notes }) },
      },
      AssignmentSchema
    );
  },

  /**
   * Get performance metrics for the current trader
   * @param params Query parameters for filtering metrics
   * @returns Performance metrics
   */
  async getPerformanceMetrics(params: {
    startDate?: string;
    endDate?: string;
    merchantId?: string;
  }): Promise<PerformanceMetrics> {
    const queryParams = createQueryString({
      ...(params.startDate && { startDate: params.startDate }),
      ...(params.endDate && { endDate: params.endDate }),
      ...(params.merchantId && { merchantId: params.merchantId }),
    });

    return apiRequest<PerformanceMetrics>(
      {
        method: 'GET',
        url: `/performance${queryParams ? `?${queryParams}` : ''}`,
      },
      z.object({
        totalCollections: z.number(),
        totalAmount: z.number(),
        currency: z.string(),
        averageCollectionTime: z.number(),
        successRate: z.number(),
        metricsByPeriod: z.array(
          z.object({
            period: z.string(),
            count: z.number(),
            amount: z.number(),
          })
        ),
        topMerchants: z.array(
          z.object({
            merchantId: z.string(),
            merchantName: z.string(),
            count: z.number(),
            amount: z.number(),
          })
        ),
      })
    );
  },

  /**
   * Get all assignments with pagination and filters (admin only)
   * @param params Query parameters for filtering and pagination
   * @returns Paginated list of assignments
   */
  async getAllAssignments(
    params: GetAssignmentsParams = {}
  ): Promise<PaginatedResponse<Assignment>> {
    const queryParams: Record<string, string | string[]> = {
      page: params.page?.toString() || '1',
      limit: params.limit?.toString() || '10',
    };

    // Handle status separately to ensure correct typing
    if (params.status) {
      queryParams.status = Array.isArray(params.status) 
        ? params.status 
        : [params.status];
    }

    // Add other optional parameters
    const optionalParams: Record<string, string> = {};
    if (params.traderId) optionalParams.traderId = params.traderId;
    if (params.merchantId) optionalParams.merchantId = params.merchantId;
    if (params.startDate) optionalParams.startDate = params.startDate;
    if (params.endDate) optionalParams.endDate = params.endDate;
    if (params.assignmentType) optionalParams.assignmentType = params.assignmentType;
    if (params.search) optionalParams.search = params.search;
    if (params.sortBy) optionalParams.sortBy = params.sortBy;
    if (params.order) optionalParams.order = params.order;

    // Combine all parameters
    const allParams = { ...queryParams, ...optionalParams };
    const queryString = createQueryString(allParams);

    return apiRequest<PaginatedResponse<Assignment>>(
      {
        method: 'GET',
        url: `/admin/assignments${queryParams ? `?${queryParams}` : ''}`,
      },
      PaginatedResponseSchema(AssignmentSchema)
    );
  },

  /**
   * Get all merchants (admin only)
   * @returns List of merchants
   */
  async getMerchants(): Promise<Merchant[]> {
    return apiRequest<Merchant[]>(
      {
        method: 'GET',
        url: '/admin/merchants',
      },
      z.array(
        z.object({
          _id: z.string(),
          businessName: z.string(),
          email: z.string().email(),
          phone: z.string().optional(),
          status: z.custom<'active' | 'inactive' | 'suspended'>(
            (val) => ['active', 'inactive', 'suspended'].includes(String(val))
          ),
          metadata: z.record(z.string(), z.unknown()).optional(),
          createdAt: z.string().datetime(),
          updatedAt: z.string().datetime(),
        })
      )
    );
  },

  /**
   * Create a new assignment (admin only)
   * @param data Assignment data
   * @returns Created assignment
   */
  async createAssignment(data: CreateAssignmentData): Promise<Assignment> {
    return apiRequest<Assignment>(
      {
        method: 'POST',
        url: '/admin/assignments',
        data,
      },
      AssignmentSchema
    );
  },

  /**
   * Update an assignment (admin only)
   * @param id Assignment ID
   * @param data Data to update
   * @returns Updated assignment
   */
  async updateAssignment(
    id: string,
    data: UpdateAssignmentData
  ): Promise<Assignment> {
    return apiRequest<Assignment>(
      {
        method: 'PUT',
        url: `/admin/assignments/${id}`,
        data,
      },
      AssignmentSchema
    );
  },

  /**
   * Delete an assignment (admin only)
   * @param id Assignment ID
   * @returns Deletion result
   */
  async deleteAssignment(id: string): Promise<{ id: string }> {
    return apiRequest<{ id: string }>(
      {
        method: 'DELETE',
        url: `/admin/assignments/${id}`,
      },
      z.object({
        id: z.string(),
      })
    );
  },

  /**
   * Bulk assign traders to merchants (admin only)
   * @param data Bulk assignment data
   * @returns Bulk operation result
   */
  async bulkAssignTraders(
    data: BulkAssignTradersData
  ): Promise<{ success: boolean; message?: string }> {
    return apiRequest(
      {
        method: 'POST',
        url: '/admin/assignments/bulk',
        data,
      },
      z.object({
        success: z.boolean(),
        message: z.string().optional(),
      })
    );
  },

  /**
   * Generate an assignment report (admin only)
   * @param params Report parameters
   */
  async getDashboardStats(): Promise<DashboardStats> {
    return apiRequest<DashboardStats>(
      {
        method: 'GET',
        url: '/dashboard/stats',
      },
      z.object({
        totalTraders: z.number(),
        activeTraders: z.number(),
        totalMerchants: z.number(),
        activeAssignments: z.number(),
        pendingAssignments: z.number(),
        totalCollections: z.number(),
        totalAmount: z.number(),
        currency: z.string(),
        recentActivity: z.array(
          z.object({
            id: z.string(),
            type: z.string(),
            description: z.string(),
            timestamp: z.string(),
            userId: z.string(),
            userName: z.string(),
          })
        ),
      })
    );
  },
};
