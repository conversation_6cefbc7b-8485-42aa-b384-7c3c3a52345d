const mongoose = require('mongoose');
require('dotenv').config();
const Menu = require('./models/Menu');

async function migrateMenus() {
  try {
    console.log('🔄 MIGRATING MENU COLLECTIONS TO UNIFIED MENU SYSTEM...\n');
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/payment-gateway');
    
    // Clear existing menus
    await Menu.deleteMany({});
    console.log('🗑️  Cleared existing unified menus\n');
    
    // Define comprehensive menu structure based on collections
    const menuStructure = [
      // ADMIN MENUS
      {
        menuId: 'admin-dashboard',
        label: 'Dashboard',
        to: '/admin',
        icon: 'LayoutDashboard',
        role: 'admin',
        order: 1,
        description: 'Admin dashboard with overview and analytics',
        metadata: { category: 'overview', isNew: false }
      },
      {
        menuId: 'admin-users',
        label: 'User Management',
        to: '/admin/users',
        icon: 'Users',
        role: 'admin',
        order: 2,
        description: 'Manage all system users',
        metadata: { category: 'management' }
      },
      {
        menuId: 'admin-traders',
        label: 'Traders',
        to: '/admin/traders',
        icon: 'UserCheck',
        role: 'admin',
        order: 3,
        description: 'Manage trader accounts and profiles',
        metadata: { category: 'management' }
      },
      {
        menuId: 'admin-merchants',
        label: 'Merchants',
        to: '/admin/merchants',
        icon: 'Store',
        role: 'admin',
        order: 4,
        description: 'Manage merchant accounts and businesses',
        metadata: { category: 'management' }
      },
      {
        menuId: 'admin-assignments',
        label: 'Trader Assignments',
        to: '/admin/trader-assignments',
        icon: 'UserPlus',
        role: 'admin',
        order: 5,
        description: 'Manage trader-merchant assignments',
        metadata: { category: 'operations' }
      },
      {
        menuId: 'admin-transactions',
        label: 'Transactions',
        to: '/admin/transactions',
        icon: 'CreditCard',
        role: 'admin',
        order: 6,
        description: 'Monitor all payment transactions',
        metadata: { category: 'financial' }
      },
      {
        menuId: 'admin-settlements',
        label: 'Settlements',
        to: '/admin/settlements',
        icon: 'DollarSign',
        role: 'admin',
        order: 7,
        description: 'Manage payment settlements',
        metadata: { category: 'financial' }
      },
      {
        menuId: 'admin-reconciliations',
        label: 'Reconciliations',
        to: '/admin/reconciliations',
        icon: 'Calculator',
        role: 'admin',
        order: 8,
        description: 'Financial reconciliation management',
        metadata: { category: 'financial' }
      },
      {
        menuId: 'admin-webhooks',
        label: 'Webhooks',
        to: '/admin/webhooks',
        icon: 'Webhook',
        role: 'admin',
        order: 9,
        description: 'Webhook configuration and monitoring',
        metadata: { category: 'integration' }
      },
      {
        menuId: 'admin-reports',
        label: 'Reports',
        to: '/admin/reports',
        icon: 'FileText',
        role: 'admin',
        order: 10,
        description: 'Generate and view system reports',
        metadata: { category: 'analytics' }
      },
      {
        menuId: 'admin-settings',
        label: 'Settings',
        to: '/admin/settings',
        icon: 'Settings',
        role: 'admin',
        order: 11,
        description: 'System configuration and settings',
        metadata: { category: 'system' }
      },

      // MERCHANT MENUS
      {
        menuId: 'merchant-dashboard',
        label: 'Dashboard',
        to: '/merchant',
        icon: 'LayoutDashboard',
        role: 'merchant',
        order: 1,
        description: 'Merchant dashboard overview',
        metadata: { category: 'overview' }
      },
      {
        menuId: 'merchant-transactions',
        label: 'My Transactions',
        to: '/merchant/transactions',
        icon: 'CreditCard',
        role: 'merchant',
        order: 2,
        description: 'View your payment transactions',
        metadata: { category: 'financial' }
      },
      {
        menuId: 'merchant-settlements',
        label: 'Settlements',
        to: '/merchant/settlements',
        icon: 'DollarSign',
        role: 'merchant',
        order: 3,
        description: 'View your settlement history',
        metadata: { category: 'financial' }
      },
      {
        menuId: 'merchant-profile',
        label: 'Business Profile',
        to: '/merchant/profile',
        icon: 'Building',
        role: 'merchant',
        order: 4,
        description: 'Manage your business information',
        metadata: { category: 'management' }
      },
      {
        menuId: 'merchant-webhooks',
        label: 'Webhooks',
        to: '/merchant/webhooks',
        icon: 'Webhook',
        role: 'merchant',
        order: 5,
        description: 'Configure payment webhooks',
        metadata: { category: 'integration' }
      },
      {
        menuId: 'merchant-reports',
        label: 'Reports',
        to: '/merchant/reports',
        icon: 'FileText',
        role: 'merchant',
        order: 6,
        description: 'View business reports',
        metadata: { category: 'analytics' }
      },

      // TRADER MENUS
      {
        menuId: 'trader-dashboard',
        label: 'Dashboard',
        to: '/trader',
        icon: 'LayoutDashboard',
        role: 'trader',
        order: 1,
        description: 'Trader dashboard overview',
        metadata: { category: 'overview' }
      },
      {
        menuId: 'trader-assignments',
        label: 'My Assignments',
        to: '/trader/assignments',
        icon: 'UserCheck',
        role: 'trader',
        order: 2,
        description: 'View your merchant assignments',
        metadata: { category: 'operations' }
      },
      {
        menuId: 'trader-collections',
        label: 'Collections',
        to: '/trader/collections',
        icon: 'Target',
        role: 'trader',
        order: 3,
        description: 'Manage collection activities',
        metadata: { category: 'operations' }
      },
      {
        menuId: 'trader-performance',
        label: 'Performance',
        to: '/trader/performance',
        icon: 'TrendingUp',
        role: 'trader',
        order: 4,
        description: 'View performance metrics',
        metadata: { category: 'analytics' }
      },
      {
        menuId: 'trader-profile',
        label: 'Profile',
        to: '/trader/profile',
        icon: 'User',
        role: 'trader',
        order: 5,
        description: 'Manage your profile',
        metadata: { category: 'management' }
      }
    ];

    // Insert new unified menus
    const createdMenus = await Menu.insertMany(menuStructure);
    console.log(`✅ Created ${createdMenus.length} unified menus\n`);

    // Show menu summary by role
    const menusByRole = await Menu.aggregate([
      { $group: { _id: '$role', count: { $sum: 1 }, menus: { $push: '$label' } } }
    ]);

    console.log('📋 MENU SUMMARY BY ROLE:');
    console.log('='.repeat(50));
    menusByRole.forEach(role => {
      console.log(`\n${role._id.toUpperCase()} (${role.count} menus):`);
      role.menus.forEach((menu, index) => {
        console.log(`  ${index + 1}. ${menu}`);
      });
    });

    console.log('\n✅ Menu migration completed successfully!');
    console.log('\n🔗 API Endpoints:');
    console.log('  GET /api/menus - Get all menus');
    console.log('  GET /api/menus/role/:role - Get menus by role');
    console.log('  POST /api/menus - Create new menu');
    console.log('  PUT /api/menus/:id - Update menu');
    console.log('  DELETE /api/menus/:id - Delete menu');

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
  } finally {
    await mongoose.connection.close();
  }
}

migrateMenus();
