// Real API service for trader assignments using the payment-gateway database
const API_BASE_URL = 'http://localhost:5000/api';

export interface RealTraderAssignment {
  _id: string;
  traderId: {
    _id: string;
    name: string;
    email: string;
    role: string;
    phone?: string;
  };
  merchantId: {
    _id: string;
    name: string;
    email: string;
    businessName: string;
    role: string;
    phone?: string;
  };
  assignedBy: {
    _id: string;
    name: string;
    email: string;
    role: string;
  };
  assignmentType: 'permanent' | 'temporary' | 'project_based';
  startDate: string;
  endDate?: string;
  status: 'active' | 'inactive' | 'suspended' | 'completed';
  collectionTarget: {
    amount: number;
    period: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  };
  permissions: string[];
  performanceMetrics?: {
    totalCollected: number;
    successfulTransactions: number;
    failedTransactions: number;
    averageCollectionTime?: number;
    customerSatisfactionRating?: number;
  };
  notes?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface RealUser {
  _id: string;
  name: string;
  email: string;
  role: 'admin' | 'merchant' | 'trader';
  businessName?: string;
  phone?: string;
  isActive: boolean;
  createdAt: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  meta?: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
  message?: string;
}

export interface AssignmentFilters {
  page?: number;
  limit?: number;
  status?: string;
  search?: string;
}

class RealTraderAssignmentService {
  private async fetchApi<T>(endpoint: string, options?: RequestInit): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        headers: {
          'Content-Type': 'application/json',
          ...options?.headers,
        },
        ...options,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  // Get all trader assignments
  async getTraderAssignments(filters: AssignmentFilters = {}): Promise<ApiResponse<RealTraderAssignment[]>> {
    const params = new URLSearchParams();
    
    if (filters.page) params.append('page', filters.page.toString());
    if (filters.limit) params.append('limit', filters.limit.toString());
    if (filters.status && filters.status !== 'all') params.append('status', filters.status);
    if (filters.search) params.append('search', filters.search);

    const queryString = params.toString();
    const endpoint = `/trader-assignments${queryString ? `?${queryString}` : ''}`;
    
    return this.fetchApi<RealTraderAssignment[]>(endpoint);
  }

  // Get trader assignment by ID
  async getTraderAssignment(id: string): Promise<ApiResponse<RealTraderAssignment>> {
    return this.fetchApi<RealTraderAssignment>(`/trader-assignments/${id}`);
  }

  // Create new trader assignment
  async createTraderAssignment(data: Partial<RealTraderAssignment>): Promise<ApiResponse<RealTraderAssignment>> {
    return this.fetchApi<RealTraderAssignment>('/trader-assignments', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Update trader assignment
  async updateTraderAssignment(id: string, data: Partial<RealTraderAssignment>): Promise<ApiResponse<RealTraderAssignment>> {
    return this.fetchApi<RealTraderAssignment>(`/trader-assignments/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  // Delete trader assignment
  async deleteTraderAssignment(id: string): Promise<ApiResponse<void>> {
    return this.fetchApi<void>(`/trader-assignments/${id}`, {
      method: 'DELETE',
    });
  }

  // Get all users (traders and merchants)
  async getUsers(role?: 'trader' | 'merchant'): Promise<ApiResponse<RealUser[]>> {
    const endpoint = role ? `/users/role/${role}` : '/users';
    return this.fetchApi<RealUser[]>(endpoint);
  }

  // Get traders
  async getTraders(): Promise<ApiResponse<RealUser[]>> {
    return this.getUsers('trader');
  }

  // Get merchants
  async getMerchants(): Promise<ApiResponse<RealUser[]>> {
    return this.getUsers('merchant');
  }

  // Get assignments by trader ID
  async getAssignmentsByTrader(traderId: string): Promise<ApiResponse<RealTraderAssignment[]>> {
    return this.fetchApi<RealTraderAssignment[]>(`/trader-assignments/trader/${traderId}`);
  }

  // Get assignments by merchant ID
  async getAssignmentsByMerchant(merchantId: string): Promise<ApiResponse<RealTraderAssignment[]>> {
    return this.fetchApi<RealTraderAssignment[]>(`/trader-assignments/merchant/${merchantId}`);
  }
}

// Create and export a singleton instance
export const realTraderAssignmentService = new RealTraderAssignmentService();

// Mock data fallback for when API is not available
export const mockTraderAssignments: RealTraderAssignment[] = [
  {
    _id: 'mock1',
    traderId: {
      _id: 'trader1',
      name: 'John Trader',
      email: '<EMAIL>',
      role: 'trader',
      phone: '+1234567890'
    },
    merchantId: {
      _id: 'merchant1',
      name: 'Alice Merchant',
      email: '<EMAIL>',
      businessName: 'Tech Store Inc',
      role: 'merchant',
      phone: '+1234567891'
    },
    assignedBy: {
      _id: 'admin1',
      name: 'Admin User',
      email: '<EMAIL>',
      role: 'admin'
    },
    assignmentType: 'permanent',
    startDate: new Date().toISOString(),
    status: 'active',
    collectionTarget: {
      amount: 10000,
      period: 'monthly'
    },
    permissions: ['view_transactions', 'process_payments'],
    performanceMetrics: {
      totalCollected: 8500,
      successfulTransactions: 45,
      failedTransactions: 3,
      averageCollectionTime: 24,
      customerSatisfactionRating: 4.5
    },
    notes: 'Primary trader for tech store operations',
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

export const mockUsers: RealUser[] = [
  {
    _id: 'trader1',
    name: 'John Trader',
    email: '<EMAIL>',
    role: 'trader',
    phone: '+1234567890',
    isActive: true,
    createdAt: new Date().toISOString()
  },
  {
    _id: 'merchant1',
    name: 'Alice Merchant',
    email: '<EMAIL>',
    role: 'merchant',
    businessName: 'Tech Store Inc',
    phone: '+1234567891',
    isActive: true,
    createdAt: new Date().toISOString()
  }
];
