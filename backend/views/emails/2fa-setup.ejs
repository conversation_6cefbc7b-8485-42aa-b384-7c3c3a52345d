<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width">
  <title>Two-Factor Authentication Setup</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      text-align: center;
      padding: 20px 0;
      border-bottom: 1px solid #eee;
      margin-bottom: 30px;
    }
    .logo {
      max-width: 150px;
      height: auto;
    }
    .content {
      padding: 0 20px 20px;
    }
    .code {
      font-family: monospace;
      font-size: 28px;
      letter-spacing: 4px;
      padding: 15px 25px;
      background-color: #f5f5f5;
      border-radius: 4px;
      display: inline-block;
      margin: 15px 0;
      text-align: center;
      border: 1px dashed #ccc;
    }
    .steps {
      margin: 25px 0;
      padding-left: 20px;
    }
    .steps li {
      margin-bottom: 15px;
    }
    .recovery-codes {
      background-color: #f9f9f9;
      border: 1px solid #eee;
      border-radius: 4px;
      padding: 15px;
      margin: 20px 0;
      font-family: monospace;
      line-height: 2;
    }
    .warning {
      background-color: #fff3cd;
      border-left: 4px solid #ffc107;
      padding: 12px 15px;
      margin: 20px 0;
    }
    .footer {
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #eee;
      font-size: 12px;
      color: #777;
      text-align: center;
    }
  </style>
</head>
<body>
  <div class="header">
    <img src="<%= baseUrl %>/logo.png" alt="Company Logo" class="logo">
  </div>
  
  <div class="content">
    <h2>Hello <%= name %>,</h2>
    
    <p>You've successfully enabled Two-Factor Authentication (2FA) for your account. This adds an extra layer of security to protect your account.</p>
    
    <h3>Your 2FA Secret Key</h3>
    <div class="code"><%= secret %></div>
    
    <div class="warning">
      <strong>Important:</strong> Save this secret key in a secure location. You'll need it to set up your authenticator app.
    </div>
    
    <h3>How to set up your authenticator app:</h3>
    <ol class="steps">
      <li>Install an authenticator app like Google Authenticator, Authy, or Microsoft Authenticator on your device.</li>
      <li>Open the app and select "Scan QR code" or "Enter setup key".</li>
      <li>If scanning a QR code, use the one shown in your account settings. If entering manually, use the secret key above.</li>
      <li>Enter the 6-digit code generated by the app to complete the setup.</li>
    </ol>
    
    <h3>Recovery Codes</h3>
    <p>Please save these recovery codes in a secure location. You can use them to access your account if you lose access to your authenticator app.</p>
    
    <div class="recovery-codes">
      <% recoveryCodes.forEach(function(code) { %>
        <div><%= code %></div>
      <% }); %>
    </div>
    
    <div class="warning">
      <strong>Important:</strong> These recovery codes are only shown once. Make sure to save them in a secure location.
    </div>
    
    <p>If you did not enable 2FA, please secure your account immediately by changing your password and contacting support.</p>
    
    <p>Best regards,<br>The Payment Gateway Security Team</p>
  </div>
  
  <div class="footer">
    <p>© <%= new Date().getFullYear() %> Payment Gateway. All rights reserved.</p>
    <p>This is an automated message, please do not reply to this email.</p>
  </div>
</body>
</html>
