import { useState, useEffect } from 'react';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { useToast } from '../ui/use-toast';
import { 
  useSetupTwoFactor, 
  useVerifyTwoFactor, 
  useDisableTwoFactor,
  type TwoFactorSetupResponse,
  type TwoFactorVerifyResponse,
  type TwoFactorDisableResponse
} from '../../hooks/useTwoFactorAuth';
import { Loader2, CheckCircle2, AlertCircle, QrCode } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '../ui/dialog';

interface TwoFactorAuthProps {
  twoFactorEnabled?: boolean;
}

export function TwoFactorAuth({ twoFactorEnabled = false }: TwoFactorAuthProps) {
  const { toast } = useToast();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [verificationCode, setVerificationCode] = useState('');
  const [qrCodeData, setQrCodeData] = useState('');
  const [recoveryCodes, setRecoveryCodes] = useState<string[]>([]);
  const [showRecoveryCodes, setShowRecoveryCodes] = useState(false);

  // Setup mutations with proper types
  const {
    mutate: setupTwoFactor,
    isPending: isSettingUp,
    isSuccess: isSetupSuccess,
    data: setupData,
    isError: isSetupError,
    error: setupError
  } = useSetupTwoFactor();
  
  const {
    mutate: verifyTwoFactor,
    isPending: isVerifying,
    isSuccess: isVerifySuccess,
    data: verifyData,
    isError: isVerifyError,
    error: verifyError
  } = useVerifyTwoFactor();
  
  const {
    mutate: disableTwoFactor,
    isPending: isDisabling,
    isSuccess: isDisableSuccess,
    isError: isDisableError,
    error: disableError
  } = useDisableTwoFactor();
  
  // Handle setup success/error with useEffect
  useEffect(() => {
    if (isSetupSuccess && setupData) {
      setQrCodeData(setupData.qrCodeData);
      setIsDialogOpen(true);
    }
  }, [isSetupSuccess, setupData]);
  
  // Handle verify success/error with useEffect
  useEffect(() => {
    if (isVerifySuccess && verifyData) {
      setRecoveryCodes(verifyData.recoveryCodes);
      setShowRecoveryCodes(true);
      toast({
        title: 'Success',
        description: 'Two-factor authentication has been enabled successfully.',
      });
    }
  }, [isVerifySuccess, verifyData, toast]);
  
  // Handle errors with useEffect
  useEffect(() => {
    if (isSetupError && setupError) {
      toast({
        title: 'Error',
        description: setupError.message || 'Failed to set up two-factor authentication',
        variant: 'destructive',
      });
    }
    
    if (isVerifyError && verifyError) {
      toast({
        title: 'Error',
        description: verifyError.message || 'Failed to verify two-factor authentication code',
        variant: 'destructive',
      });
    }
    
    if (isDisableError && disableError) {
      toast({
        title: 'Error',
        description: disableError.message || 'Failed to disable two-factor authentication',
        variant: 'destructive',
      });
    }
  }, [isSetupError, setupError, isVerifyError, verifyError, isDisableError, disableError, toast]);
  
  // Handle disable success
  useEffect(() => {
    if (isDisableSuccess) {
      toast({
        title: 'Success',
        description: 'Two-factor authentication has been disabled successfully.',
      });
      setIsDialogOpen(false);
      setVerificationCode('');
    }
  }, [isDisableSuccess, toast]);

  const handleDisable2FA = () => {
    if (!verificationCode.trim()) {
      toast({
        title: 'Error',
        description: 'Please enter a verification code',
        variant: 'destructive',
      });
      return;
    }
    
    disableTwoFactor(
      { code: verificationCode },
      {
        onSuccess: () => {
          setVerificationCode('');
          setQrCodeData('');
          setRecoveryCodes([]);
          setShowRecoveryCodes(false);
        },
        onError: (error: Error) => {
          toast({
            title: 'Error',
            description: error.message || 'Failed to disable two-factor authentication',
            variant: 'destructive',
          });
        },
      }
    );
  };

  const handleCopyRecoveryCodes = () => {
    navigator.clipboard.writeText(recoveryCodes.join('\n'));
    toast({
      title: 'Copied',
      description: 'Recovery codes copied to clipboard',
    });
  };

  const handleDownloadRecoveryCodes = () => {
    const element = document.createElement('a');
    const file = new Blob([recoveryCodes.join('\n')], { type: 'text/plain' });
    element.href = URL.createObjectURL(file);
    element.download = 'recovery-codes.txt';
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  };

  const handleSetup2FA = () => {
    setupTwoFactor(undefined, {
      onSuccess: (data: TwoFactorSetupResponse) => {
        setQrCodeData(data.qrCodeData);
        setIsDialogOpen(true);
      },
      onError: (error: Error) => {
        toast({
          title: 'Error',
          description: error.message || 'Failed to set up two-factor authentication',
          variant: 'destructive',
        });
      },
    });
  };
  
  const handleVerifyCode = () => {
    if (!verificationCode.trim()) {
      toast({
        title: 'Error',
        description: 'Please enter a verification code',
        variant: 'destructive',
      });
      return;
    }
    
    verifyTwoFactor(
      { code: verificationCode },
      {
        onSuccess: (data: TwoFactorVerifyResponse) => {
          setRecoveryCodes(data.recoveryCodes);
          setShowRecoveryCodes(true);
          toast({
            title: 'Success',
            description: 'Two-factor authentication has been enabled successfully.',
          });
        },
        onError: (error: Error) => {
          toast({
            title: 'Error',
            description: error.message || 'Failed to verify two-factor authentication code',
            variant: 'destructive',
          });
        },
      }
    );
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-base font-medium">Two-Factor Authentication</h3>
          <p className="text-sm text-muted-foreground">
            {twoFactorEnabled
              ? 'Two-factor authentication is enabled.'
              : 'Add an extra layer of security to your account.'}
          </p>
        </div>
        {twoFactorEnabled ? (
          <Button
            variant="destructive"
            onClick={() => setIsDialogOpen(true)}
          >
            Disable 2FA
          </Button>
        ) : (
          <Button
            onClick={handleSetup2FA}
            disabled={isSettingUp}
          >
            {isSettingUp ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Setting up...
              </>
            ) : (
              'Enable 2FA'
            )}
          </Button>
        )}
      </div>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>{twoFactorEnabled ? 'Disable two-factor authentication' : 'Set up two-factor authentication'}</DialogTitle>
            <DialogDescription>
              {twoFactorEnabled ? 'Enter your verification code to disable two-factor authentication.' : 'Scan the QR code with your authenticator app and enter the verification code.'}
            </DialogDescription>
          </DialogHeader>
          
          {!twoFactorEnabled && qrCodeData && !showRecoveryCodes && (
            <div className="flex flex-col items-center space-y-4">
              <div className="p-4 bg-white rounded-md">
                <img src={qrCodeData} alt="QR Code" className="w-48 h-48" />
              </div>
              <p className="text-sm text-muted-foreground">
                Scan this QR code with your authenticator app
              </p>
            </div>
          )}
          
          {showRecoveryCodes ? (
            <div className="space-y-4">
              <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-md">
                <div className="flex">
                  <AlertCircle className="h-5 w-5 text-yellow-500 mr-2 mt-0.5 flex-shrink-0" />
                  <p className="text-sm text-yellow-700 dark:text-yellow-300">
                    Please save these recovery codes in a safe place. You can use them to recover access to your account if you lose your device.
                  </p>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-2">
                {recoveryCodes.map((code, index) => (
                  <div key={index} className="font-mono text-sm p-2 bg-muted rounded">
                    {code}
                  </div>
                ))}
              </div>
              
              <div className="flex space-x-2">
                <Button variant="outline" onClick={handleCopyRecoveryCodes}>
                  Copy Codes
                </Button>
                <Button variant="outline" onClick={handleDownloadRecoveryCodes}>
                  Download
                </Button>
              </div>
              
              <Button className="w-full" onClick={() => {
                setShowRecoveryCodes(false);
                setIsDialogOpen(false);
              }}>
                I've saved my recovery codes
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="verification-code">
                  {twoFactorEnabled ? 'Enter verification code to disable 2FA' : 'Enter verification code from your authenticator app'}
                </Label>
                <Input
                  id="verification-code"
                  value={verificationCode}
                  onChange={(e) => setVerificationCode(e.target.value)}
                  placeholder="123456"
                  className="text-center text-xl font-mono tracking-widest"
                  autoComplete="one-time-code"
                />
              </div>
              
              <Button 
                className="w-full" 
                onClick={twoFactorEnabled ? handleDisable2FA : handleVerifyCode}
                disabled={twoFactorEnabled ? isDisabling : isVerifying}
              >
                {twoFactorEnabled ? (
                  isDisabling ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Disabling...
                    </>
                  ) : (
                    'Disable 2FA'
                  )
                ) : isVerifying ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Verifying...
                  </>
                ) : (
                  'Verify and Enable 2FA'
                )}
              </Button>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
