const fs = require('fs');
const path = require('path');

const envPath = path.join(__dirname, '../.env');

// Read the existing .env file
let envContent = '';
if (fs.existsSync(envPath)) {
  envContent = fs.readFileSync(envPath, 'utf8');
}

// Add API_VERSION if it doesn't exist
if (!envContent.includes('API_VERSION=')) {
  envContent += '\n# API Version\nAPI_VERSION=v1\n';
  fs.writeFileSync(envPath, envContent, 'utf8');
  console.log('✅ Added API_VERSION to .env file');
} else {
  console.log('ℹ️ API_VERSION already exists in .env file');
}
