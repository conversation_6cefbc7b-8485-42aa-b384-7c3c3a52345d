const mongoose = require('mongoose');
const logger = require('../utils/logger');

// Enable Mongoose debug mode in development
if (process.env.NODE_ENV === 'development') {
  mongoose.set('debug', (collectionName, method, query, doc) => {
    logger.debug(`Mongoose: ${collectionName}.${method}`, { query, doc });
  });
}

/**
 * Establishes a connection to MongoDB with retry logic
 * @returns {Promise<mongoose.Connection>} Mongoose connection instance
 */
const connectDB = async () => {
  // Configuration options for MongoDB connection
  const dbOptions = {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    maxPoolSize: process.env.MONGODB_POOL_SIZE || 100,
    serverSelectionTimeoutMS: 30000, // 30 seconds
    socketTimeoutMS: 45000, // 45 seconds
    connectTimeoutMS: 30000, // 30 seconds
    retryWrites: true,
    w: 'majority',
    dbName: process.env.MONGODB_DB_NAME || 'payment-gateway',
    autoIndex: process.env.NODE_ENV !== 'production', // Disable autoIndex in production
  };

  // For development, always use localhost
  let mongoUri = 'mongodb://localhost:27017/payment-gateway';
  
  // Only use environment variable in production
  if (process.env.NODE_ENV === 'production') {
    if (!process.env.MONGODB_URI) {
      throw new Error('MONGODB_URI environment variable is required in production');
    }
    mongoUri = process.env.MONGODB_URI;
  } else if (process.env.NODE_ENV === 'test') {
    // Use in-memory database for testing if available
    mongoUri = process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/payment-gateway-test';
  }
  
  // Log connection details
  logger.info(`Attempting to connect to MongoDB at: ${mongoUri}`);
  logger.info(`Database options: ${JSON.stringify(dbOptions, null, 2)}`);
  
  try {
    // Connect to MongoDB
    const conn = await mongoose.connect(mongoUri, dbOptions);
    
    // Log detailed connection information
    const db = conn.connection;
    logger.info(`MongoDB Connected to host: ${db.host}`);
    logger.info(`MongoDB Database name: ${db.name}`);
    logger.info(`MongoDB Connection state: ${db.readyState === 1 ? 'Connected' : 'Disconnected'}`);
    logger.info(`MongoDB Connection port: ${db.port}`);
    logger.info(`MongoDB Connection user: ${db.user || 'none'}`);
    
    // Verify database exists and is accessible
    try {
      const adminDb = db.db.admin();
      const dbs = await adminDb.listDatabases();
      logger.info(`Available databases: ${dbs.databases.map(d => d.name).join(', ')}`);
      
      // Check if our database exists
      const dbExists = dbs.databases.some(d => d.name === 'payment-gateway');
      if (!dbExists) {
        logger.warn('Database "payment-gateway" does not exist. It will be created when the first document is inserted.');
      }
      
      // Create the database by inserting a document if it doesn't exist
      if (!dbExists) {
        logger.info('Creating database by inserting a test document...');
        try {
          await db.db.collection('testCollection').insertOne({ createdAt: new Date() });
          logger.info('Test document inserted successfully');
          await db.db.collection('testCollection').deleteMany({});
          logger.info('Test document cleaned up');
        } catch (err) {
          logger.error(`Error creating test document: ${err.message}`);
        }
      }
    } catch (err) {
      logger.error(`Error verifying databases: ${err.message}`);
    }
    
    // List all collections in the current database
    try {
      const collections = await db.db.listCollections().toArray();
      logger.info(`Available collections in ${db.name}: ${collections.map(c => c.name).join(', ') || 'None'}`);
      
      // If no collections, create the necessary ones
      if (collections.length === 0) {
        logger.info('No collections found. Creating required collections...');
        try {
          // Create users collection with indexes
          await db.db.createCollection('users');
          await db.db.collection('users').createIndex({ email: 1 }, { unique: true });
          await db.db.collection('users').createIndex({ apiKey: 1 }, { unique: true, sparse: true });
          logger.info('Created users collection with indexes');
          
          // Create transactions collection with indexes
          await db.db.createCollection('transactions');
          await db.db.collection('transactions').createIndex({ userId: 1 });
          await db.db.collection('transactions').createIndex({ status: 1 });
          await db.db.collection('transactions').createIndex({ createdAt: -1 });
          logger.info('Created transactions collection with indexes');
          
          // Refresh collections list
          const updatedCollections = await db.db.listCollections().toArray();
          logger.info(`Updated collections: ${updatedCollections.map(c => c.name).join(', ')}`);
        } catch (err) {
          logger.error(`Error creating collections: ${err.message}`);
        }
      }
      
      // Log collection stats
      const updatedCollections = await db.db.listCollections().toArray();
      for (const coll of updatedCollections) {
        try {
          const stats = await db.db.collection(coll.name).stats();
          logger.info(`Collection ${coll.name} stats: ${JSON.stringify({
            count: stats.count,
            size: stats.size,
            storageSize: stats.storageSize,
            indexSizes: stats.indexSizes
          }, null, 2)}`);
        } catch (err) {
          logger.warn(`Could not get stats for collection ${coll.name}: ${err.message}`);
        }
      }
    } catch (err) {
      logger.error(`Error managing collections: ${err.message}`);
    }
    
    // Connection events with more detailed logging
    mongoose.connection.on('connected', () => {
      logger.info('MongoDB connected successfully');
      logger.debug(`MongoDB connection details: ${JSON.stringify({
        host: db.host,
        port: db.port,
        name: db.name,
        user: db.user,
        readyState: db.readyState,
        _hasOpened: db._hasOpened
      }, null, 2)}`);
    });
    
    mongoose.connection.on('error', (err) => {
      logger.error(`MongoDB connection error: ${err.message}`);
      logger.error(`Error stack: ${err.stack}`);
      logger.error(`Connection state: ${mongoose.connection.readyState}`);
    });
    
    mongoose.connection.on('disconnected', () => {
      logger.warn('MongoDB disconnected');
    });
    
    // Log all queries in development
    if (process.env.NODE_ENV === 'development') {
      mongoose.set('debug', true);
    }
    
    return conn;
    
  } catch (error) {
    logger.error(`MongoDB connection error: ${error.message}`);
    throw error;
  }
};

// Graceful shutdown handler
const gracefulShutdown = async (signal) => {
  logger.info(`${signal} signal received: closing MongoDB connection`);
  try {
    await mongoose.connection.close();
    logger.info('MongoDB connection closed');
    process.exit(0);
  } catch (err) {
    logger.error('Error during MongoDB shutdown:', err);
    process.exit(1);
  }
};

// Handle different shutdown signals
process.on('SIGINT', () => gracefulShutdown('SIGINT'));
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));

module.exports = {
  connectDB,
  mongoose
};
