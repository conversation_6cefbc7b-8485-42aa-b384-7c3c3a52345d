const mongoose = require('mongoose');
const Settlement = require('../models/Settlement');
const { NotFoundError, ForbiddenError } = require('../utils/errors');
const logger = require('../utils/logger');

/**
 * Middleware to check if the current user has access to a settlement
 * Attaches the settlement to req.settlement if found and authorized
 */
const checkSettlementAccess = async (req, res, next) => {
  try {
    const { id } = req.params;
    const user = req.user;
    
    // Validate if the ID is a valid MongoDB ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      logger.warn(`Invalid settlement ID format: ${id}`, { userId: user?.id });
      const error = new Error('Invalid settlement ID format');
      error.statusCode = 400;
      throw error;
    }
    
    // Find the settlement
    const settlement = await Settlement.findById(id)
      .populate('fromParty', 'user')
      .populate('toParty', 'user');
    
    if (!settlement) {
      logger.warn(`Settlement not found: ${id}`, { userId: user.id });
      throw new NotFoundError('Settlement not found');
    }
    
    // Check if user is admin or finance - they have access to all settlements
    if (user.role === 'admin' || user.role === 'finance') {
      req.settlement = settlement;
      return next();
    }
    
    // For merchants/traders, check if they are a party to the settlement
    console.log('Middleware - User ID:', user.id, 'Type:', typeof user.id);
    console.log('Middleware - Settlement fromParty:', settlement.fromParty ? {
      _id: settlement.fromParty._id.toString(),
      user: settlement.fromParty.user ? settlement.fromParty.user.toString() : 'no user field'
    } : 'no fromParty');
    console.log('Middleware - Settlement toParty:', settlement.toParty ? {
      _id: settlement.toParty._id.toString(),
      user: settlement.toParty.user ? settlement.toParty.user.toString() : 'no user field'
    } : 'no toParty');
    
    // Convert all IDs to strings for comparison to ensure type consistency
    const userId = user.id.toString();
    const fromPartyId = settlement.fromParty?._id?.toString();
    const toPartyId = settlement.toParty?._id?.toString();
    const fromPartyUserId = settlement.fromParty?.user?.toString();
    const toPartyUserId = settlement.toParty?.user?.toString();
    
    const isFromParty = fromPartyId === userId || fromPartyUserId === userId;
    const isToParty = toPartyId === userId || toPartyUserId === userId;
    
    console.log('Middleware - isFromParty:', isFromParty, 'isToParty:', isToParty);
    
    if (!isFromParty && !isToParty) {
      logger.warn(`Unauthorized access attempt to settlement ${id} by user ${user.id} (${user.role})`);
      throw new ForbiddenError('You do not have permission to access this settlement');
    }
    
    // User is authorized, attach settlement to request
    req.settlement = settlement;
    next();
  } catch (error) {
    next(error);
  }
};

module.exports = checkSettlementAccess;

module.exports = checkSettlementAccess;
