/**
 * Format a number as currency
 * @param {number} amount - The amount to format
 * @param {string} currency - The currency code (default: 'USD')
 * @param {number} decimals - Number of decimal places (default: 2)
 * @returns {string} Formatted currency string
 */
export const formatCurrency = (amount, currency = 'USD', decimals = 2) => {
  if (amount === null || amount === undefined) return 'N/A';
  
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(amount);
};

/**
 * Format a date string or timestamp to a readable format
 * @param {string|Date|number} date - The date to format
 * @param {Object} options - Formatting options
 * @param {boolean} options.includeTime - Whether to include time in the output
 * @returns {string} Formatted date string
 */
export const formatDate = (date, options = {}) => {
  if (!date) return 'N/A';
  
  const { includeTime = true } = options;
  const dateObj = new Date(date);
  
  if (isNaN(dateObj.getTime())) return 'Invalid Date';
  
  const dateFormat = new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    ...(includeTime && {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    })
  });
  
  return dateFormat.format(dateObj);
};

/**
 * Format a number with commas as thousand separators
 * @param {number|string} num - The number to format
 * @param {number} decimals - Number of decimal places to show
 * @returns {string} Formatted number string
 */
export const formatNumber = (num, decimals = 2) => {
  if (num === null || num === undefined) return 'N/A';
  
  const number = typeof num === 'string' ? parseFloat(num) : num;
  
  if (isNaN(number)) return 'Invalid Number';
  
  return number.toLocaleString('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: decimals,
  });
};

/**
 * Format a file size in bytes to a human-readable format
 * @param {number} bytes - File size in bytes
 * @param {number} decimals - Number of decimal places
 * @returns {string} Formatted file size string
 */
export const formatFileSize = (bytes, decimals = 2) => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(dm))} ${sizes[i]}`;
};

/**
 * Format a phone number to a standard format
 * @param {string} phone - The phone number to format
 * @returns {string} Formatted phone number
 */
export const formatPhoneNumber = (phone) => {
  if (!phone) return '';
  
  // Remove all non-digit characters
  const cleaned = ('' + phone).replace(/\D/g, '');
  
  // Check if the number looks like a US phone number
  const match = cleaned.match(/^(\d{1,3})?(\d{3})(\d{3})(\d{4})$/);
  
  if (match) {
    // Format as US phone number: (XXX) XXX-XXXX
    return `(${match[2]}) ${match[3]}-${match[4]}`;
  }
  
  // Return the original if it doesn't match expected format
  return phone;
};

/**
 * Truncate text to a specified length and add ellipsis
 * @param {string} text - The text to truncate
 * @param {number} maxLength - Maximum length before truncation
 * @returns {string} Truncated text with ellipsis if needed
 */
export const truncateText = (text, maxLength = 100) => {
  if (!text) return '';
  if (text.length <= maxLength) return text;
  
  return `${text.substring(0, maxLength)}...`;
};

/**
 * Format a credit card number by adding spaces for better readability
 * @param {string} cardNumber - The credit card number
 * @returns {string} Formatted credit card number
 */
export const formatCreditCardNumber = (cardNumber) => {
  if (!cardNumber) return '';
  
  // Remove all non-digit characters
  const cleaned = ('' + cardNumber).replace(/\D/g, '');
  
  // Add a space after every 4 digits
  return cleaned.replace(/(\d{4})(?=\d)/g, '$1 ');
};

/**
 * Mask sensitive information like credit card numbers or emails
 * @param {string} value - The value to mask
 * @param {Object} options - Masking options
 * @param {string} options.type - Type of masking ('email', 'card', 'ssn', 'phone')
 * @returns {string} Masked string
 */
export const maskSensitiveInfo = (value, options = {}) => {
  if (!value) return '';
  
  const { type = 'default' } = options;
  const str = String(value);
  
  switch (type) {
    case 'email': {
      const [username, domain] = str.split('@');
      if (!domain) return str;
      
      const maskedUsername = username.length > 2 
        ? `${username[0]}${'*'.repeat(3)}${username.slice(-1)}`
        : '*'.repeat(username.length);
      
      const [domainName, tld] = domain.split('.');
      const maskedDomain = domainName.length > 2
        ? `${domainName[0]}${'*'.repeat(2)}${domainName.slice(-1)}`
        : '*'.repeat(domainName.length);
      
      return `${maskedUsername}@${maskedDomain}${tld ? '.' + tld : ''}`;
    }
    
    case 'card': {
      const cleaned = str.replace(/\D/g, '');
      if (cleaned.length < 12) return cleaned;
      
      const lastFour = cleaned.slice(-4);
      return `**** **** **** ${lastFour}`;
    }
    
    case 'ssn': {
      const cleaned = str.replace(/\D/g, '');
      if (cleaned.length !== 9) return str;
      
      return `***-**-${cleaned.slice(-4)}`;
    }
    
    case 'phone': {
      const cleaned = str.replace(/\D/g, '');
      if (cleaned.length < 10) return str;
      
      const lastFour = cleaned.slice(-4);
      return `(***) ***-${lastFour}`;
    }
    
    default:
      return str.length > 8 
        ? `${str.substring(0, 2)}${'*'.repeat(Math.max(0, str.length - 6))}${str.slice(-4)}`
        : '*'.repeat(str.length);
  }
};

export default {
  formatCurrency,
  formatDate,
  formatNumber,
  formatFileSize,
  formatPhoneNumber,
  truncateText,
  formatCreditCardNumber,
  maskSensitiveInfo,
};
