const { body, query, param } = require('express-validator');
const { isValidObjectId } = require('mongoose').Types;

// Common validation rules
const commonRules = {
  id: param('id')
    .trim()
    .notEmpty().withMessage('ID is required')
    .custom((value) => {
      if (!isValidObjectId(value)) {
        throw new Error('Invalid ID format');
      }
      return true;
    }),
  
  pagination: [
    query('page')
      .optional()
      .isInt({ min: 1 }).withMessage('Page must be a positive integer')
      .toInt(),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100')
      .toInt(),
  ],
};

// Validation rules for settlement endpoints
const settlementValidationRules = {
  // GET /settlements
  getSettlements: [
    ...commonRules.pagination,
    query('status')
      .optional()
      .isIn(['pending', 'processing', 'completed', 'failed'])
      .withMessage('Invalid status value'),
    query('type')
      .optional()
      .isIn(['trader_payout', 'merchant_payout', 'internal'])
      .withMessage('Invalid settlement type'),
    query('fromParty')
      .optional()
      .custom((value) => {
        if (!isValidObjectId(value)) {
          throw new Error('Invalid fromParty ID format');
        }
        return true;
      }),
    query('toParty')
      .optional()
      .custom((value) => {
        if (!isValidObjectId(value)) {
          throw new Error('Invalid toParty ID format');
        }
        return true;
      }),
    query('startDate')
      .optional()
      .isISO8601()
      .withMessage('Invalid start date format. Use ISO 8601 format (e.g., 2023-01-01T00:00:00Z)'),
    query('endDate')
      .optional()
      .isISO8601()
      .withMessage('Invalid end date format. Use ISO 8601 format (e.g., 2023-01-31T23:59:59Z)')
      .custom((value, { req }) => {
        if (req.query.startDate && new Date(value) < new Date(req.query.startDate)) {
          throw new Error('End date must be after start date');
        }
        return true;
      }),
  ],

  // GET /settlements/:id
  getSettlement: [
    commonRules.id,
  ],

  // POST /settlements
  createSettlement: [
    body('type')
      .isIn(['trader_payout', 'merchant_payout', 'internal'])
      .withMessage('Invalid settlement type'),
    body('fromParty')
      .notEmpty().withMessage('fromParty is required')
      .custom((value) => {
        if (!isValidObjectId(value)) {
          throw new Error('Invalid fromParty ID format');
        }
        return true;
      }),
    body('toParty')
      .notEmpty().withMessage('toParty is required')
      .custom((value) => {
        if (!isValidObjectId(value)) {
          throw new Error('Invalid toParty ID format');
        }
        return true;
      })
      .custom((value, { req }) => {
        if (value === req.body.fromParty) {
          throw new Error('fromParty and toParty cannot be the same');
        }
        return true;
      }),
    body('periodStart')
      .notEmpty().withMessage('periodStart is required')
      .isISO8601()
      .withMessage('Invalid periodStart format. Use ISO 8601 format (e.g., 2023-01-01T00:00:00Z)'),
    body('periodEnd')
      .notEmpty().withMessage('periodEnd is required')
      .isISO8601()
      .withMessage('Invalid periodEnd format. Use ISO 8601 format (e.g., 2023-01-31T23:59:59Z)')
      .custom((value, { req }) => {
        if (new Date(value) <= new Date(req.body.periodStart)) {
          throw new Error('periodEnd must be after periodStart');
        }
        return true;
      }),
    body('paymentMethod')
      .optional()
      .isIn(['bank_transfer', 'wallet', 'card'])
      .withMessage('Invalid payment method'),
    body('notes')
      .optional()
      .isString()
      .isLength({ max: 1000 })
      .withMessage('Notes must be a string with maximum 1000 characters'),
  ],

  // POST /settlements/:id/process
  processSettlement: [
    commonRules.id,
  ],
};

module.exports = settlementValidationRules;
