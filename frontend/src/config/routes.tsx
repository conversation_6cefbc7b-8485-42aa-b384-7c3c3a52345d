import { lazy, Suspense, ReactNode } from 'react';
import { Navigate, RouteObject } from 'react-router-dom';
import LoadingSpinner from '../components/ui/LoadingSpinner';
import DashboardLayout from '../layouts/DashboardLayout';
import { merchantRoutes } from './merchantRoutes';

// Lazy load components for better performance
const AdminDashboard = lazy(() => import('../pages/admin/AdminPagePlaceholder'));
const PaymentPage = lazy(() => import('../pages/PaymentPage'));

// Admin pages
const MerchantsPage = lazy(() => import('../pages/admin/MerchantsPage'));
const TradersPage = lazy(() => import('../pages/admin/TradersPage'));
const PaymentsPage = lazy(() => import('../pages/admin/PaymentsPage'));
const ReportsPage = lazy(() => import('../pages/admin/ReportsPage'));
const SettingsPage = lazy(() => import('../pages/admin/SettingsPage'));

// Loading component for Suspense fallback
const Loading = () => (
  <div className="flex items-center justify-center min-h-screen">
    <LoadingSpinner size="lg" />
  </div>
);

// Error boundary component for route components
const RouteErrorBoundary = ({ children }: { children: ReactNode }) => {
  return (
    <>
      {children}
    </>
  );
};

// Route configuration
export const routes: RouteObject[] = [
  // Root path redirects to admin dashboard
  {
    path: '/',
    element: <Navigate to="/admin" replace />,
  },
  // Payment page
  {
    path: '/payment/:merchantId',
    element: (
      <Suspense fallback={<Loading />}>
        <RouteErrorBoundary>
          <PaymentPage />
        </RouteErrorBoundary>
      </Suspense>
    ),
  },
  
  // Merchant routes
  ...merchantRoutes,

  // Admin routes
  {
    path: '/admin',
    element: (
      <DashboardLayout title="Admin Dashboard">
        <Suspense fallback={<Loading />}>
          <RouteErrorBoundary>
            <AdminDashboard title="Admin Dashboard" />
          </RouteErrorBoundary>
        </Suspense>
      </DashboardLayout>
    ),
  },
  {
    path: '/admin/merchants',
    element: (
      <DashboardLayout title="Merchants Management">
        <Suspense fallback={<Loading />}>
          <RouteErrorBoundary>
            <MerchantsPage />
          </RouteErrorBoundary>
        </Suspense>
      </DashboardLayout>
    ),
  },
  {
    path: '/admin/traders',
    element: (
      <DashboardLayout title="Traders Management">
        <Suspense fallback={<Loading />}>
          <RouteErrorBoundary>
            <TradersPage />
          </RouteErrorBoundary>
        </Suspense>
      </DashboardLayout>
    ),
  },
  {
    path: '/admin/payments',
    element: (
      <DashboardLayout title="Payments Overview">
        <Suspense fallback={<Loading />}>
          <RouteErrorBoundary>
            <PaymentsPage />
          </RouteErrorBoundary>
        </Suspense>
      </DashboardLayout>
    ),
  },
  {
    path: '/admin/reports',
    element: (
      <DashboardLayout title="Reports & Analytics">
        <Suspense fallback={<Loading />}>
          <RouteErrorBoundary>
            <ReportsPage />
          </RouteErrorBoundary>
        </Suspense>
      </DashboardLayout>
    ),
  },
  {
    path: '/admin/settings',
    element: (
      <DashboardLayout title="System Settings">
        <Suspense fallback={<Loading />}>
          <RouteErrorBoundary>
            <SettingsPage />
          </RouteErrorBoundary>
        </Suspense>
      </DashboardLayout>
    ),
  },

  // Trader routes
  {
    path: '/trader',
    element: (
      <DashboardLayout title="Trader Dashboard">
        <Suspense fallback={<Loading />}>
          <RouteErrorBoundary>
            <AdminDashboard title="Trader Dashboard" />
          </RouteErrorBoundary>
        </Suspense>
      </DashboardLayout>
    ),
  },

  // Catch-all route - redirect to admin
  {
    path: '*',
    element: <Navigate to="/admin" replace />,
  },
];
