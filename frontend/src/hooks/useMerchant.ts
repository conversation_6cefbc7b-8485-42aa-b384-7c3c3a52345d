import { useQuery } from '@tanstack/react-query';
import { Merchant } from '../types/trader';

/**
 * Hook to fetch merchants list
 */
export const useGetMerchants = () => {
  return useQuery({
    queryKey: ['merchants'],
    queryFn: async (): Promise<Merchant[]> => {
      // In a real app, this would be an API call
      return [];
    },
  });
};

/**
 * Hook to fetch merchant details by ID
 */
export const useGetMerchant = (id?: string) => {
  return useQuery({
    queryKey: ['merchant', id],
    queryFn: async (): Promise<Merchant | null> => {
      if (!id) return null;
      // In a real app, this would be an API call
      return null;
    },
    enabled: !!id,
  });
};
