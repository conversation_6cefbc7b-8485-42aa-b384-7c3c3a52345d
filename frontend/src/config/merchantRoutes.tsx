import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import DashboardLayout from '../layouts/DashboardLayout';
// Authentication removed
import LoadingSpinner from '../components/ui/LoadingSpinner';
import lazyWithRetry from '../utils/lazyWithRetry';

// Loading component for Suspense fallback
const Loading = () => (
  <div className="flex items-center justify-center min-h-screen">
    <LoadingSpinner size="lg" />
  </div>
);

// Lazy load merchant components
const DashboardPage = lazyWithRetry(() => import('../pages/merchant/DashboardPage'));
const TransactionsPage = lazyWithRetry(() => import('../pages/merchant/TransactionsPage'));
const CollectionsPage = lazyWithRetry(() => import('../pages/merchant/CollectionsPage'));
const BillingPage = lazyWithRetry(() => import('../pages/merchant/Billing'));
const TradersPage = lazyWithRetry(() => import('../pages/merchant/MerchantTraders'));
const SettingsPage = lazyWithRetry(() => import('../pages/merchant/IntegrationSettings'));
const ProfilePage = lazyWithRetry(() => import('../pages/merchant/ProfilePage'));
const SupportPage = lazyWithRetry(() => import('../pages/merchant/Support'));
const NotificationsPage = lazyWithRetry(() => import('../pages/merchant/Notifications'));

// Merchant route configuration
export const merchantRoutes: RouteObject[] = [
  {
    path: '/merchant',
    element: (
      <DashboardLayout title="Merchant Dashboard">
        <DashboardPage />
      </DashboardLayout>
    ),
  },
  {
    path: '/merchant/dashboard',
    element: (
      <DashboardLayout title="Dashboard">
        <DashboardPage />
      </DashboardLayout>
    ),
  },
  {
    path: '/merchant/transactions',
    element: (
      <DashboardLayout title="Transactions">
        <TransactionsPage />
      </DashboardLayout>
    ),
  },
  {
    path: '/merchant/collections',
    element: (
      <DashboardLayout title="Collections">
        <CollectionsPage />
      </DashboardLayout>
    ),
  },
  {
    path: '/merchant/billing',
    element: (
      <DashboardLayout title="Billing">
        <BillingPage />
      </DashboardLayout>
    ),
  },
  {
    path: '/merchant/traders',
    element: (
      <DashboardLayout title="Traders">
        <TradersPage />
      </DashboardLayout>
    ),
  },
  {
    path: '/merchant/settings',
    element: (
      <DashboardLayout title="Settings">
        <SettingsPage />
      </DashboardLayout>
    ),
  },
  {
    path: '/merchant/profile',
    element: (
      <DashboardLayout title="Profile">
        <ProfilePage />
      </DashboardLayout>
    ),
  },
  {
    path: '/merchant/support',
    element: (
      <DashboardLayout title="Support">
        <SupportPage />
      </DashboardLayout>
    ),
  },
  {
    path: '/merchant/notifications',
    element: (
      <DashboardLayout title="Notifications">
        <NotificationsPage />
      </DashboardLayout>
    ),
  },
];

// Export all page components for easier imports
export {
  DashboardPage,
  TransactionsPage,
  CollectionsPage,
  BillingPage,
  TradersPage,
  SettingsPage,
  ProfilePage,
  SupportPage,
  NotificationsPage,
};
