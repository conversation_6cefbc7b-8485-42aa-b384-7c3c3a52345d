"use strict";(self.webpackChunkpayment_gateway_frontend=self.webpackChunkpayment_gateway_frontend||[]).push([[523],{519:(e,t,s)=>{s.d(t,{Xd:()=>w,yk:()=>g,nC:()=>N,xG:()=>j});var a=s(5604),r=(s(9781),s(2836),s(6213));const n="auth_token",o=()=>"undefined"===typeof window?null:localStorage.getItem(n);var i=s(6879);const c={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"http://localhost:5001/api",REACT_APP_APP_NAME:"PayGateway",REACT_APP_APP_URL:"http://localhost:3000",REACT_APP_GOOGLE_ANALYTICS_ID:"",REACT_APP_GOOGLE_MAPS_API_KEY:"",REACT_APP_NODE_ENV:"development"}.NEXT_PUBLIC_API_URL||"http://localhost:5000/api";const d=new class{constructor(){this.client=void 0,this.client=r.A.create({baseURL:c,headers:{"Content-Type":"application/json"},withCredentials:!0}),this.setupInterceptors()}setupInterceptors(){this.client.interceptors.request.use(e=>{const t=o();return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,e=>{var t,s;const a=null===(t=e.response)||void 0===t?void 0:t.status,r=null===(s=e.response)||void 0===s?void 0:s.data;return 401===a&&("undefined"!==typeof window&&localStorage.removeItem(n),window.location.href="/login"),null!==r&&void 0!==r&&r.message?(0,i.oR)({title:"Error",description:r.message,variant:"destructive"}):e.message&&(0,i.oR)({title:"Error",description:e.message,variant:"destructive"}),Promise.reject(e)})}async get(e,t){return(await this.client.get(e,t)).data}async post(e,t,s){return(await this.client.post(e,t,s)).data}async put(e,t,s){return(await this.client.put(e,t,s)).data}async delete(e,t){return(await this.client.delete(e,t)).data}};var l=s(6379);const u=e=>{const t=e instanceof Error?e.message:"An error occurred while fetching data";(0,i.oR)({title:"Error",description:t,variant:"destructive"})},m={defaultOptions:{queries:{staleTime:3e5,retry:1,refetchOnWindowFocus:!1},mutations:{onError:e=>{u(e)}}}},h=(new l.E(m),function(){return{...arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},useErrorBoundary:e=>(u(e),!0)}}),f=function(){return{...arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},keepPreviousData:!0,refetchOnWindowFocus:!1}},p="/merchants/me",x="/merchants/me/stats",v="/merchants/me/collections",y="/merchants/me/transactions",g=e=>(0,a.I)({queryKey:["merchant"],queryFn:async()=>(await d.get(p)).data,...h(),...e}),N=e=>(0,a.I)({queryKey:["merchant","stats"],queryFn:async()=>(await d.get(x)).data,...h(),...e}),w=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;return(0,a.I)({queryKey:["merchant","collections",e],queryFn:async()=>(await d.get(v,{params:e})).data,...h(),...t})},j=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;return(0,a.I)({queryKey:["merchant","transactions",e],queryFn:async()=>(await d.get(y,{params:e})).data,...f(),...h(),...t})}},2417:(e,t,s)=>{s.d(t,{E:()=>n});s(5043);var a=s(3009),r=s(579);function n(e){let{className:t,...s}=e;return(0,r.jsx)("div",{className:(0,a.cn)("animate-pulse rounded-md bg-gray-100 dark:bg-gray-800",t),...s})}},5722:(e,t,s)=>{s.d(t,{A:()=>a});const a=(0,s(3797).A)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},6523:(e,t,s)=>{s.r(t),s.d(t,{default:()=>f});var a=s(5043),r=s(9066),n=s(9772),o=s(6742),i=s(2417),c=s(519),d=s(5722),l=s(9120),u=s(4068),m=s(8112),h=s(579);function f(){var e,t;const{user:s}=(0,r.A)(),[f,p]=(0,a.useState)("month"),{data:x,isLoading:v}=(0,c.yk)(),{data:y,isLoading:g,refetch:N}=(0,c.nC)();return v||g?(0,h.jsx)("div",{className:"p-6 space-y-6",children:(0,h.jsx)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[1,2,3,4].map(e=>(0,h.jsxs)(o.Zp,{children:[(0,h.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,h.jsx)(i.E,{className:"h-4 w-24"}),(0,h.jsx)(i.E,{className:"h-4 w-4"})]}),(0,h.jsxs)(o.Wu,{children:[(0,h.jsx)(i.E,{className:"h-8 w-32 mb-1"}),(0,h.jsx)(i.E,{className:"h-3 w-24"})]})]},e))})}):(0,h.jsxs)("div",{className:"p-6 space-y-6",children:[(0,h.jsxs)("div",{className:"flex justify-between items-center",children:[(0,h.jsxs)("div",{children:[(0,h.jsx)("h1",{className:"text-2xl font-bold",children:"Dashboard"}),(0,h.jsxs)("p",{className:"text-muted-foreground",children:["Welcome back, ",(null===x||void 0===x?void 0:x.businessName)||"Merchant","!"]})]}),(0,h.jsx)(n.$,{variant:"outline",size:"icon",onClick:async()=>{await N()},children:(0,h.jsx)(u.A,{className:"h-4 w-4"})})]}),(0,h.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,h.jsxs)(o.Zp,{children:[(0,h.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,h.jsx)(o.ZB,{className:"text-sm font-medium",children:"Total Revenue"}),(0,h.jsx)(l.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,h.jsxs)(o.Wu,{children:[(0,h.jsxs)("div",{className:"text-2xl font-bold",children:["$",(null===y||void 0===y||null===(e=y.totalRevenue)||void 0===e?void 0:e.toLocaleString("en-US",{minimumFractionDigits:2}))||"0.00"]}),(0,h.jsxs)("p",{className:"text-xs text-muted-foreground",children:["+20.1% from last ",f]})]})]}),(0,h.jsxs)(o.Zp,{children:[(0,h.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,h.jsx)(o.ZB,{className:"text-sm font-medium",children:"Transactions"}),(0,h.jsx)(d.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,h.jsxs)(o.Wu,{children:[(0,h.jsx)("div",{className:"text-2xl font-bold",children:(null===y||void 0===y||null===(t=y.totalTransactions)||void 0===t?void 0:t.toLocaleString())||"0"}),(0,h.jsxs)("p",{className:"text-xs text-muted-foreground",children:["+12.5% from last ",f]})]})]}),(0,h.jsxs)(o.Zp,{children:[(0,h.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,h.jsx)(o.ZB,{className:"text-sm font-medium",children:"Active Traders"}),(0,h.jsx)(m.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,h.jsxs)(o.Wu,{children:[(0,h.jsx)("div",{className:"text-2xl font-bold",children:(null===y||void 0===y?void 0:y.activeTraders)||"0"}),(0,h.jsxs)("p",{className:"text-xs text-muted-foreground",children:["+2 from last ",f]})]})]})]})]})}},6742:(e,t,s)=>{s.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>c,Zp:()=>o,aR:()=>i,wL:()=>u});var a=s(5043),r=s(3009),n=s(579);const o=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,r.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...a})});o.displayName="Card";const i=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,r.cn)("flex flex-col space-y-1.5 p-6",s),...a})});i.displayName="CardHeader";const c=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,n.jsx)("h3",{ref:t,className:(0,r.cn)("text-2xl font-semibold leading-none tracking-tight",s),...a})});c.displayName="CardTitle";const d=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,n.jsx)("p",{ref:t,className:(0,r.cn)("text-sm text-muted-foreground",s),...a})});d.displayName="CardDescription";const l=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,r.cn)("p-6 pt-0",s),...a})});l.displayName="CardContent";const u=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,r.cn)("flex items-center p-6 pt-0",s),...a})});u.displayName="CardFooter"},6879:(e,t,s)=>{s.d(t,{dj:()=>m,oR:()=>u});var a=s(5043);let r=0;const n=new Map,o=e=>{if(n.has(e))return;const t=setTimeout(()=>{n.delete(e),l({type:"REMOVE_TOAST",toastId:e})},1e3);n.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,5)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{const{toastId:s}=t;return s?o(s):e.toasts.forEach(e=>{o(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},c=[];let d={toasts:[]};function l(e){d=i(d,e),c.forEach(e=>{e(d)})}function u(e){let{...t}=e;const s=(r=(r+1)%Number.MAX_SAFE_INTEGER,r.toString()),a=()=>l({type:"DISMISS_TOAST",toastId:s});return l({type:"ADD_TOAST",toast:{...t,id:s,open:!0,onOpenChange:e=>{e||a()}}}),{id:s,dismiss:a,update:e=>l({type:"UPDATE_TOAST",toast:{...e,id:s}})}}function m(){const[e,t]=a.useState(d);return a.useEffect(()=>(c.push(t),()=>{const e=c.indexOf(t);e>-1&&c.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>l({type:"DISMISS_TOAST",toastId:e})}}},8112:(e,t,s)=>{s.d(t,{A:()=>a});const a=(0,s(3797).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},9120:(e,t,s)=>{s.d(t,{A:()=>a});const a=(0,s(3797).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},9772:(e,t,s)=>{s.d(t,{$:()=>d});var a=s(5043),r=s(6851),n=s(917),o=s(3009),i=s(579);const c=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,t)=>{let{className:s,variant:a,size:n,asChild:d=!1,...l}=e;const u=d?r.DX:"button";return(0,i.jsx)(u,{className:(0,o.cn)(c({variant:a,size:n,className:s})),ref:t,...l})});d.displayName="Button"}}]);
//# sourceMappingURL=523.932b8bdb.chunk.js.map