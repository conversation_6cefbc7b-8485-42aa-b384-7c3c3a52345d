const SettlementService = require('../services/settlementService');
const { SuccessResponse, ErrorResponse } = require('../utils/apiResponse');
// Simple async handler replacement
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};
const { NotFoundError, ForbiddenError, ValidationError } = require('../utils/errors');
const logger = require('../utils/logger');

/**
 * @desc    Create a new settlement
 * @route   POST /api/v1/settlements
 * @access  Private/Admin
 */
exports.createSettlement = asyncHandler(async (req, res, next) => {
  const { type, fromParty, toParty, periodStart, periodEnd, notes, paymentMethod } = req.body;
  
  const settlement = await SettlementService.createSettlement({
    type,
    fromParty,
    toParty,
    periodStart: new Date(periodStart),
    periodEnd: new Date(periodEnd),
    createdBy: req.user.id,
    notes,
    paymentMethod
  });

  return new SuccessResponse(
    201,
    'Settlement created successfully',
    settlement
  ).send(res);
});

/**
 * @desc    Process a settlement
 * @route   POST /api/v1/settlements/:id/process
 * @access  Private/Admin
 */
exports.processSettlement = asyncHandler(async (req, res, next) => {
  const { id } = req.params;
  
  const settlement = await SettlementService.processSettlement(id, req.user.id);
  
  return new SuccessResponse(
    200,
    'Settlement processed successfully',
    settlement
  ).send(res);
});

/**
 * @desc    Get all settlements
 * @route   GET /api/v1/settlements
 * @access  Private/Admin
 */
exports.getSettlements = asyncHandler(async (req, res, next) => {
  const { 
    page = 1, 
    limit = 10, 
    status, 
    type, 
    fromParty, 
    toParty, 
    startDate, 
    endDate 
  } = req.query;

  const result = await SettlementService.getSettlements({
    page: parseInt(page, 10),
    limit: Math.min(parseInt(limit, 10), 100),
    status,
    type,
    fromParty,
    toParty,
    startDate,
    endDate
  });

  return new SuccessResponse(
    200,
    'Settlements retrieved successfully',
    {
      settlements: result.docs,
      pagination: {
        total: result.totalDocs,
        pages: result.totalPages,
        page: result.page,
        limit: result.limit,
        hasNext: result.hasNextPage,
        hasPrev: result.hasPrevPage
      }
    }
  ).send(res);
});

/**
 * @desc    Middleware to check settlement access
 * @access  Private
 */
exports.checkSettlementAccess = asyncHandler(async (req, res, next) => {
  const { id } = req.params;
  const { user } = req;
  
  try {
    const settlement = await SettlementService.getSettlementForAccessCheck(id);
    
    if (!settlement) {
      logger.warn('Settlement not found', { settlementId: id, userId: user.id });
      throw new NotFoundError('Settlement not found');
    }
    
    const hasAccess = await SettlementService.checkUserAccessToSettlement(user, settlement);
    
    if (!hasAccess) {
      logger.warn('Unauthorized access attempt to settlement', {
        settlementId: id,
        userId: user.id,
        userRole: user.role
      });
      throw new ForbiddenError('You do not have permission to access this settlement');
    }
    
    req.settlement = settlement;
    next();
  } catch (error) {
    logger.error('Error checking settlement access', {
      error: error.message,
      settlementId: id,
      userId: user.id,
      stack: error.stack
    });
    next(error);
  }
});

/**
 * @desc    Get single settlement
 * @route   GET /api/v1/settlements/:id
 * @access  Private/Admin,Finance
 */
exports.getSettlement = asyncHandler(async (req, res, next) => {
  try {
    const settlement = await SettlementService.getSettlementById(req.params.id);
    
    if (!settlement) {
      logger.warn('Settlement not found after access check', {
        settlementId: req.params.id,
        userId: req.user.id
      });
      throw new NotFoundError('Settlement not found');
    }
    
    logger.info('Settlement retrieved', {
      settlementId: settlement._id,
      userId: req.user.id,
      role: req.user.role
    });
    
    return new SuccessResponse(
      200,
      'Settlement retrieved successfully',
      settlement
    ).send(res);
  } catch (error) {
    logger.error('Error retrieving settlement', {
      error: error.message,
      settlementId: req.params.id,
      userId: req.user.id,
      stack: error.stack
    });
    next(error);
  }
});

/**
 * @desc    Create a commission structure
 * @route   POST /api/v1/commissions
 * @access  Private/Admin
 */
exports.createCommissionStructure = asyncHandler(async (req, res, next) => {
  const {
    name,
    description,
    fromType,
    toType,
    transactionType,
    isTiered = false,
    percentage,
    fixedAmount,
    tiers = [],
    effectiveFrom,
    effectiveTo,
    fromParty,
    toParty,
    minTransactionAmount,
    maxTransactionAmount,
    tags = []
  } = req.body;

  const commissionStructure = new (require('../models/CommissionStructure'))({
    name,
    description,
    fromType,
    toType,
    transactionType,
    isTiered,
    percentage,
    fixedAmount,
    tiers,
    effectiveFrom: effectiveFrom ? new Date(effectiveFrom) : undefined,
    effectiveTo: effectiveTo ? new Date(effectiveTo) : undefined,
    fromParty,
    toParty,
    minTransactionAmount,
    maxTransactionAmount,
    tags,
    createdBy: req.user.id
  });

  await commissionStructure.save();

  return new SuccessResponse(
    201,
    'Commission structure created successfully',
    commissionStructure
  ).send(res);
});

/**
 * @desc    Get all commission structures
 * @route   GET /api/v1/commissions
 * @access  Private/Admin
 */
exports.getCommissionStructures = asyncHandler(async (req, res, next) => {
  const { 
    page = 1, 
    limit = 10, 
    fromType, 
    toType, 
    transactionType,
    isActive,
    search
  } = req.query;

  const query = {};
  
  if (fromType) query.fromType = fromType;
  if (toType) query.toType = toType;
  if (transactionType) query.transactionType = transactionType;
  if (isActive !== undefined) query.isActive = isActive === 'true';
  
  if (search) {
    query.$or = [
      { name: { $regex: search, $options: 'i' } },
      { description: { $regex: search, $options: 'i' } }
    ];
  }

  const options = {
    page: parseInt(page, 10),
    limit: Math.min(parseInt(limit, 10), 100),
    sort: { createdAt: -1 },
    populate: [
      { path: 'fromParty', select: 'name email' },
      { path: 'toParty', select: 'name email' },
      { path: 'createdBy', select: 'name email' }
    ]
  };

  const result = await require('../models/CommissionStructure').paginate(query, options);

  return new SuccessResponse(
    200,
    'Commission structures retrieved successfully',
    {
      commissionStructures: result.docs,
      pagination: {
        total: result.totalDocs,
        pages: result.totalPages,
        page: result.page,
        limit: result.limit,
        hasNext: result.hasNextPage,
        hasPrev: result.hasPrevPage
      }
    }
  ).send(res);
});

/**
 * @desc    Calculate commission
 * @route   POST /api/v1/commissions/calculate
 * @access  Private/Admin
 */
exports.calculateCommission = asyncHandler(async (req, res, next) => {
  const { fromParty, toParty, fromType, toType, transactionType, amount } = req.body;
  
  const commission = await SettlementService.calculateCommission({
    fromParty,
    toParty,
    fromType: fromType || 'trader', // Default to trader if not specified
    toType: toType || 'merchant',   // Default to merchant if not specified
    transactionType: transactionType || 'payout', // Default to payout if not specified
    amount: parseFloat(amount)
  });
  
  return new SuccessResponse(
    200,
    'Commission calculated successfully',
    {
      amount: parseFloat(amount),
      commission: commission.amount,
      rate: commission.rate,
      structure: commission.structure,
      netAmount: parseFloat(amount) - commission.amount
    }
  ).send(res);
});
