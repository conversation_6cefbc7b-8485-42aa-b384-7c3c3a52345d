const logger = require('./logger');

/**
 * Default pagination configuration
 */
const DEFAULT_PAGINATION = {
  page: 1,
  pageSize: 10,
  maxPageSize: 100,
};

/**
 * Parses pagination parameters from the request query
 * @param {Object} query - The request query object
 * @param {Object} options - Options for pagination
 * @param {number} [options.defaultPage=1] - Default page number
 * @param {number} [options.defaultPageSize=10] - Default page size
 * @param {number} [options.maxPageSize=100] - Maximum allowed page size
 * @returns {Object} An object with page, pageSize, skip, and limit
 */
const getPaginationParams = (query, options = {}) => {
  const {
    defaultPage = DEFAULT_PAGINATION.page,
    defaultPageSize = DEFAULT_PAGINATION.pageSize,
    maxPageSize = DEFAULT_PAGINATION.maxPageSize,
  } = options;

  // Parse page and pageSize from query params
  let page = parseInt(query.page, 10) || defaultPage;
  let pageSize = parseInt(query.pageSize, 10) || defaultPageSize;

  // Ensure page is at least 1
  page = Math.max(1, page);
  
  // Ensure pageSize is within bounds
  pageSize = Math.max(1, Math.min(pageSize, maxPageSize));

  // Calculate skip and limit for database queries
  const skip = (page - 1) * pageSize;
  const limit = pageSize;

  return {
    page,
    pageSize,
    skip,
    limit,
  };
};

/**
 * Creates a paginated response object
 * @param {Array} items - Array of items for the current page
 * @param {number} totalItems - Total number of items across all pages
 * @param {Object} pagination - Pagination parameters
 * @param {number} pagination.page - Current page number
 * @param {number} pagination.pageSize - Number of items per page
 * @param {Object} options - Additional options
 * @param {string} [options.resourceName='items'] - Name of the resource being paginated
 * @returns {Object} A paginated response object
 */
const createPaginationResponse = (
  items,
  totalItems,
  { page, pageSize },
  options = {}
) => {
  const { resourceName = 'items' } = options;
  
  // Calculate total pages
  const totalPages = Math.ceil(totalItems / pageSize) || 1;
  
  // Ensure page is within bounds
  const currentPage = Math.min(Math.max(1, page), totalPages || 1);
  
  // Calculate next and previous page numbers
  const hasNextPage = currentPage < totalPages;
  const hasPreviousPage = currentPage > 1;
  
  // Build pagination links
  const baseUrl = process.env.API_BASE_URL || '';
  const path = options.path || '';
  const queryParams = new URLSearchParams();
  
  // Copy all query parameters except page
  if (options.query) {
    Object.entries(options.query).forEach(([key, value]) => {
      if (key !== 'page' && value !== undefined && value !== '') {
        queryParams.set(key, value);
      }
    });
  }
  
  // Helper function to generate pagination URL
  const createPageUrl = (pageNum) => {
    const params = new URLSearchParams(queryParams);
    params.set('page', pageNum);
    params.set('pageSize', pageSize);
    return `${baseUrl}${path}?${params.toString()}`;
  };
  
  // Build response
  const response = {
    success: true,
    [resourceName]: items,
    pagination: {
      totalItems,
      totalPages,
      page: currentPage,
      pageSize,
      hasNextPage,
      hasPreviousPage,
    },
    links: {
      first: createPageUrl(1),
      last: createPageUrl(totalPages),
      current: createPageUrl(currentPage),
    },
  };
  
  // Add next and previous links if they exist
  if (hasNextPage) {
    response.links.next = createPageUrl(currentPage + 1);
  }
  
  if (hasPreviousPage) {
    response.links.previous = createPageUrl(currentPage - 1);
  }
  
  // Add additional metadata if provided
  if (options.meta) {
    response.meta = options.meta;
  }
  
  return response;
};

/**
 * Middleware to handle pagination parameters
 * @param {Object} options - Pagination options
 * @returns {Function} Express middleware function
 */
const paginationMiddleware = (options = {}) => {
  return (req, res, next) => {
    try {
      const pagination = getPaginationParams(req.query, options);
      
      // Add pagination to request object
      req.pagination = pagination;
      
      // Add pagination metadata to response locals
      res.locals.pagination = {
        page: pagination.page,
        pageSize: pagination.pageSize,
        path: req.path,
        query: req.query,
      };
      
      next();
    } catch (error) {
      logger.error('Pagination middleware error:', {
        error: error.message,
        stack: error.stack,
        query: req.query,
      });
      next(error);
    }
  };
};

/**
 * Creates a paginated response using the default response format
 * @param {Object} res - Express response object
 * @param {Array} items - Array of items for the current page
 * @param {number} totalItems - Total number of items across all pages
 * @param {Object} [options] - Additional options
 * @returns {Object} The Express response
 */
const sendPaginatedResponse = (res, items, totalItems, options = {}) => {
  try {
    const pagination = res.locals.pagination || {
      page: 1,
      pageSize: 10,
      path: res.req?.path,
      query: res.req?.query || {},
    };
    
    const response = createPaginationResponse(
      items,
      totalItems,
      pagination,
      {
        ...options,
        path: pagination.path,
        query: pagination.query,
      }
    );
    
    return res.status(200).json(response);
  } catch (error) {
    logger.error('Error creating paginated response:', {
      error: error.message,
      stack: error.stack,
    });
    
    // Fallback to a simple response if pagination fails
    return res.status(200).json({
      success: true,
      items,
      total: totalItems,
      page: 1,
      pageSize: items.length,
      totalPages: 1,
    });
  }
};

/**
 * Creates a cursor-based pagination response
 * @param {Array} items - Array of items for the current page
 * @param {Object} options - Pagination options
 * @param {string|number} [options.cursor] - The cursor for the next page
 * @param {number} [options.limit=10] - Number of items per page
 * @param {boolean} [options.hasMore=false] - Whether there are more items
 * @param {Function} [options.getCursor] - Function to get cursor from an item
 * @returns {Object} A cursor-based pagination response
 */
const createCursorPaginationResponse = (
  items,
  {
    cursor,
    limit = 10,
    hasMore = false,
    getCursor = (item) => item?.id?.toString() || null,
  } = {}
) => {
  if (!items || !Array.isArray(items)) {
    throw new Error('Items must be an array');
  }
  
  // If no cursor is provided, use the first item's cursor
  if (!cursor && items.length > 0) {
    cursor = getCursor(items[0]);
  }
  
  // Get the next cursor from the last item if available
  const nextCursor = items.length > 0 ? getCursor(items[items.length - 1]) : null;
  
  return {
    success: true,
    items,
    pagination: {
      cursor: nextCursor,
      limit,
      hasMore: hasMore || (items.length >= limit && nextCursor !== cursor),
    },
  };
};

module.exports = {
  DEFAULT_PAGINATION,
  getPaginationParams,
  createPaginationResponse,
  paginationMiddleware,
  sendPaginatedResponse,
  createCursorPaginationResponse,
};
