const User = require('../models/User');
const { v4: uuidv4 } = require('uuid');

// Create new trader
exports.createTrader = async (req, res) => {
  try {
    const { name, email, phone, address } = req.body;
    const trader = new User({
      name,
      email,
      phone,
      address,
      role: 'trader',
      status: 'pending',
      traderId: uuidv4()
    });
    await trader.save();
    res.status(201).json(trader);
  } catch (error) {
    console.error('Error creating trader:', error);
    res.status(500).json({ message: 'Error creating trader' });
  }
};

// Update trader
exports.updateTrader = async (req, res) => {
  try {
    const trader = await User.findByIdAndUpdate(
      req.params.id,
      { $set: req.body },
      { new: true, runValidators: true }
    );
    if (!trader) return res.status(404).json({ message: 'Trader not found' });
    res.json(trader);
  } catch (error) {
    console.error('Error updating trader:', error);
    res.status(500).json({ message: 'Error updating trader' });
  }
};

// Delete trader
exports.deleteTrader = async (req, res) => {
  try {
    const trader = await User.findByIdAndDelete(req.params.id);
    if (!trader) return res.status(404).json({ message: 'Trader not found' });
    res.json({ message: 'Trader deleted successfully' });
  } catch (error) {
    console.error('Error deleting trader:', error);
    res.status(500).json({ message: 'Error deleting trader' });
  }
};

// Get trader profile
exports.getTraderProfile = async (req, res) => {
  try {
    const trader = await User.findById(req.params.id);
    if (!trader) return res.status(404).json({ message: 'Trader not found' });
    res.json(trader);
  } catch (error) {
    console.error('Error fetching trader profile:', error);
    res.status(500).json({ message: 'Error fetching trader profile' });
  }
};

// Get all traders (already in admin.js, but can add here if needed)