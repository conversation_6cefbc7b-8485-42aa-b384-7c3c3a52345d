import React, { useState } from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON>, 
  StepLabel, 
  Button, 
  Typography, 
  Box, 
  Paper, 
  Grid, 
  TextField, 
  FormControl, 
  InputLabel, 
  Select, 
  MenuItem, 
  FormControlLabel, 
  Checkbox, 
  Divider, 
  useTheme, 
  useMediaQuery,
  CircularProgress,
  Alert,
  IconButton,
  InputAdornment
} from '@mui/material';
import { 
  AccountCircle as AccountIcon, 
  Business as BusinessIcon, 
  LocationOn as LocationIcon, 
  Payment as PaymentIcon, 
  CheckCircle as CheckCircleIcon,
  Visibility,
  VisibilityOff,
  ArrowBack as ArrowBackIcon,
  ArrowForward as ArrowForwardIcon
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { formatPhoneNumber, maskSensitiveInfo } from '../../utils/formatters';

// Styled components
const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(4),
  borderRadius: theme.shape.borderRadius * 2,
  boxShadow: theme.shadows[3],
  maxWidth: 800,
  margin: '0 auto',
  [theme.breakpoints.down('sm')]: {
    padding: theme.spacing(2),
    boxShadow: 'none',
  },
}));

const StepIconRoot = styled('div')(({ theme, ownerState }) => ({
  display: 'flex',
  height: 22,
  alignItems: 'center',
  '& $StepIcon': {
    width: 24,
    height: 24,
    display: 'flex',
    borderRadius: '50%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.palette.grey[300],
    color: theme.palette.common.white,
    ...(ownerState.active && {
      backgroundColor: theme.palette.primary.main,
    }),
    ...(ownerState.completed && {
      backgroundColor: theme.palette.success.main,
    }),
  },
}));

function StepIcon(props) {
  const { active, completed, icon } = props;

  return (
    <StepIconRoot ownerState={{ active, completed }}>
      {completed ? (
        <CheckCircleIcon className="StepIcon-completedIcon" />
      ) : (
        <div className="StepIcon-circle">{icon}</div>
      )}
    </StepIconRoot>
  );
}

// Form validation schemas
const personalInfoSchema = Yup.object().shape({
  firstName: Yup.string().required('First name is required'),
  lastName: Yup.string().required('Last name is required'),
  email: Yup.string().email('Invalid email').required('Email is required'),
  phone: Yup.string()
    .required('Phone number is required')
    .matches(
      /^\+?[1-9]\d{1,14}$/,
      'Please enter a valid phone number with country code'
    ),
  password: Yup.string()
    .min(8, 'Password must be at least 8 characters')
    .required('Password is required'),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref('password'), null], 'Passwords must match')
    .required('Please confirm your password'),
});

const businessInfoSchema = Yup.object().shape({
  businessName: Yup.string().required('Business name is required'),
  businessType: Yup.string().required('Business type is required'),
  registrationNumber: Yup.string().required('Registration number is required'),
  taxId: Yup.string().required('Tax ID is required'),
  website: Yup.string().url('Please enter a valid URL'),
});

const addressInfoSchema = Yup.object().shape({
  street: Yup.string().required('Street address is required'),
  city: Yup.string().required('City is required'),
  state: Yup.string().required('State/Province is required'),
  postalCode: Yup.string().required('Postal code is required'),
  country: Yup.string().required('Country is required'),
});

const paymentInfoSchema = Yup.object().shape({
  accountHolderName: Yup.string().required('Account holder name is required'),
  accountNumber: Yup.string().required('Account number is required'),
  routingNumber: Yup.string().required('Routing number is required'),
  currency: Yup.string().required('Currency is required'),
  termsAccepted: Yup.boolean()
    .oneOf([true], 'You must accept the terms and conditions')
    .required('You must accept the terms and conditions'),
});

// Steps configuration
const steps = [
  { label: 'Personal Info', icon: <AccountIcon /> },
  { label: 'Business Info', icon: <BusinessIcon /> },
  { label: 'Address', icon: <LocationIcon /> },
  { label: 'Payment', icon: <PaymentIcon /> },
];

const businessTypes = [
  'Sole Proprietorship',
  'Partnership',
  'Corporation',
  'Limited Liability Company',
  'Nonprofit Organization',
  'Other',
];

const countries = [
  'United States',
  'Canada',
  'United Kingdom',
  'Australia',
  'Germany',
  'France',
  'Japan',
  'Other',
];

const currencies = [
  { code: 'USD', name: 'US Dollar' },
  { code: 'EUR', name: 'Euro' },
  { code: 'GBP', name: 'British Pound' },
  { code: 'JPY', name: 'Japanese Yen' },
  { code: 'CAD', name: 'Canadian Dollar' },
  { code: 'AUD', name: 'Australian Dollar' },
];

const OnboardingWizard = ({ onSubmit, loading, error }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [activeStep, setActiveStep] = useState(0);
  const [showPassword, setShowPassword] = useState(false);
  
  // Formik form
  const formik = useFormik({
    initialValues: {
      // Personal Info
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      password: '',
      confirmPassword: '',
      
      // Business Info
      businessName: '',
      businessType: '',
      registrationNumber: '',
      taxId: '',
      website: '',
      
      // Address
      street: '',
      city: '',
      state: '',
      postalCode: '',
      country: '',
      
      // Payment
      accountHolderName: '',
      accountNumber: '',
      routingNumber: '',
      currency: 'USD',
      termsAccepted: false,
    },
    validationSchema: personalInfoSchema, // Initial validation schema
    onSubmit: (values) => {
      if (onSubmit) {
        onSubmit(values);
      }
    },
  });

  // Get current validation schema based on active step
  const getCurrentSchema = () => {
    switch (activeStep) {
      case 0:
        return personalInfoSchema;
      case 1:
        return businessInfoSchema;
      case 2:
        return addressInfoSchema;
      case 3:
        return paymentInfoSchema;
      default:
        return Yup.object();
    }
  };

  // Handle next button click
  const handleNext = async () => {
    const schema = getCurrentSchema();
    const isValid = await schema.isValid(formik.values);
    
    if (isValid) {
      setActiveStep((prevStep) => prevStep + 1);
    } else {
      // Manually trigger validation
      formik.validateForm().then((errors) => {
        formik.setTouched({
          ...Object.keys(formik.values).reduce((acc, key) => ({
            ...acc,
            [key]: true,
          })),
        });
      });
    }
  };

  // Handle back button click
  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  // Handle phone number formatting
  const handlePhoneChange = (e) => {
    const formatted = formatPhoneNumber(e.target.value);
    formik.setFieldValue('phone', formatted);
  };

  // Toggle password visibility
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  // Render step content based on active step
  const renderStepContent = (step) => {
    switch (step) {
      case 0:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                id="firstName"
                name="firstName"
                label="First Name"
                value={formik.values.firstName}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.firstName && Boolean(formik.errors.firstName)}
                helperText={formik.touched.firstName && formik.errors.firstName}
                variant="outlined"
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                id="lastName"
                name="lastName"
                label="Last Name"
                value={formik.values.lastName}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.lastName && Boolean(formik.errors.lastName)}
                helperText={formik.touched.lastName && formik.errors.lastName}
                variant="outlined"
                margin="normal"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                id="email"
                name="email"
                label="Email Address"
                type="email"
                value={formik.values.email}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.email && Boolean(formik.errors.email)}
                helperText={formik.touched.email && formik.errors.email}
                variant="outlined"
                margin="normal"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                id="phone"
                name="phone"
                label="Phone Number"
                value={formik.values.phone}
                onChange={handlePhoneChange}
                onBlur={formik.handleBlur}
                error={formik.touched.phone && Boolean(formik.errors.phone)}
                helperText={formik.touched.phone && formik.errors.phone}
                variant="outlined"
                margin="normal"
                placeholder="+****************"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                id="password"
                name="password"
                label="Password"
                type={showPassword ? 'text' : 'password'}
                value={formik.values.password}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.password && Boolean(formik.errors.password)}
                helperText={formik.touched.password && formik.errors.password}
                variant="outlined"
                margin="normal"
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        aria-label="toggle password visibility"
                        onClick={togglePasswordVisibility}
                        edge="end"
                      >
                        {showPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                id="confirmPassword"
                name="confirmPassword"
                label="Confirm Password"
                type={showPassword ? 'text' : 'password'}
                value={formik.values.confirmPassword}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.confirmPassword && Boolean(formik.errors.confirmPassword)}
                helperText={formik.touched.confirmPassword && formik.errors.confirmPassword}
                variant="outlined"
                margin="normal"
              />
            </Grid>
          </Grid>
        );
      case 1:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                id="businessName"
                name="businessName"
                label="Business Name"
                value={formik.values.businessName}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.businessName && Boolean(formik.errors.businessName)}
                helperText={formik.touched.businessName && formik.errors.businessName}
                variant="outlined"
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl
                fullWidth
                variant="outlined"
                margin="normal"
                error={formik.touched.businessType && Boolean(formik.errors.businessType)}
              >
                <InputLabel id="business-type-label">Business Type</InputLabel>
                <Select
                  labelId="business-type-label"
                  id="businessType"
                  name="businessType"
                  value={formik.values.businessType}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  label="Business Type"
                >
                  {businessTypes.map((type) => (
                    <MenuItem key={type} value={type}>
                      {type}
                    </MenuItem>
                  ))}
                </Select>
                {formik.touched.businessType && formik.errors.businessType && (
                  <Typography color="error" variant="caption">
                    {formik.errors.businessType}
                  </Typography>
                )}
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                id="registrationNumber"
                name="registrationNumber"
                label="Registration Number"
                value={formik.values.registrationNumber}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.registrationNumber && Boolean(formik.errors.registrationNumber)}
                helperText={formik.touched.registrationNumber && formik.errors.registrationNumber}
                variant="outlined"
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                id="taxId"
                name="taxId"
                label="Tax ID (EIN/SSN)"
                value={formik.values.taxId}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.taxId && Boolean(formik.errors.taxId)}
                helperText={formik.touched.taxId && formik.errors.taxId}
                variant="outlined"
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                id="website"
                name="website"
                label="Website (Optional)"
                value={formik.values.website}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.website && Boolean(formik.errors.website)}
                helperText={formik.touched.website && formik.errors.website}
                variant="outlined"
                margin="normal"
                placeholder="https://example.com"
              />
            </Grid>
          </Grid>
        );
      case 2:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                id="street"
                name="street"
                label="Street Address"
                value={formik.values.street}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.street && Boolean(formik.errors.street)}
                helperText={formik.touched.street && formik.errors.street}
                variant="outlined"
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                id="city"
                name="city"
                label="City"
                value={formik.values.city}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.city && Boolean(formik.errors.city)}
                helperText={formik.touched.city && formik.errors.city}
                variant="outlined"
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                id="state"
                name="state"
                label="State/Province"
                value={formik.values.state}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.state && Boolean(formik.errors.state)}
                helperText={formik.touched.state && formik.errors.state}
                variant="outlined"
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                id="postalCode"
                name="postalCode"
                label="Postal Code"
                value={formik.values.postalCode}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.postalCode && Boolean(formik.errors.postalCode)}
                helperText={formik.touched.postalCode && formik.errors.postalCode}
                variant="outlined"
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl
                fullWidth
                variant="outlined"
                margin="normal"
                error={formik.touched.country && Boolean(formik.errors.country)}
              >
                <InputLabel id="country-label">Country</InputLabel>
                <Select
                  labelId="country-label"
                  id="country"
                  name="country"
                  value={formik.values.country}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  label="Country"
                >
                  {countries.map((country) => (
                    <MenuItem key={country} value={country}>
                      {country}
                    </MenuItem>
                  ))}
                </Select>
                {formik.touched.country && formik.errors.country && (
                  <Typography color="error" variant="caption">
                    {formik.errors.country}
                  </Typography>
                )}
              </FormControl>
            </Grid>
          </Grid>
        );
      case 3:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Alert severity="info" sx={{ mb: 3 }}>
                Your payment information is securely encrypted and stored in compliance with PCI DSS standards.
              </Alert>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                id="accountHolderName"
                name="accountHolderName"
                label="Account Holder Name"
                value={formik.values.accountHolderName}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.accountHolderName && Boolean(formik.errors.accountHolderName)}
                helperText={formik.touched.accountHolderName && formik.errors.accountHolderName}
                variant="outlined"
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                id="accountNumber"
                name="accountNumber"
                label="Account Number"
                value={formik.values.accountNumber}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.accountNumber && Boolean(formik.errors.accountNumber)}
                helperText={formik.touched.accountNumber && formik.errors.accountNumber}
                variant="outlined"
                margin="normal"
                type="password"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                id="routingNumber"
                name="routingNumber"
                label="Routing Number"
                value={formik.values.routingNumber}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.routingNumber && Boolean(formik.errors.routingNumber)}
                helperText={formik.touched.routingNumber && formik.errors.routingNumber}
                variant="outlined"
                margin="normal"
                type="password"
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl
                fullWidth
                variant="outlined"
                margin="normal"
                error={formik.touched.currency && Boolean(formik.errors.currency)}
              >
                <InputLabel id="currency-label">Currency</InputLabel>
                <Select
                  labelId="currency-label"
                  id="currency"
                  name="currency"
                  value={formik.values.currency}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  label="Currency"
                >
                  {currencies.map((currency) => (
                    <MenuItem key={currency.code} value={currency.code}>
                      {currency.name} ({currency.code})
                    </MenuItem>
                  ))}
                </Select>
                {formik.touched.currency && formik.errors.currency && (
                  <Typography color="error" variant="caption">
                    {formik.errors.currency}
                  </Typography>
                )}
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <FormControl
                error={formik.touched.termsAccepted && Boolean(formik.errors.termsAccepted)}
                component="fieldset"
                variant="standard"
              >
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={formik.values.termsAccepted}
                      onChange={formik.handleChange}
                      name="termsAccepted"
                      color="primary"
                    />
                  }
                  label={
                    <Typography variant="body2">
                      I agree to the{' '}
                      <a href="/terms" target="_blank" rel="noopener noreferrer">
                        Terms of Service
                      </a>{' '}
                      and{' '}
                      <a href="/privacy" target="_blank" rel="noopener noreferrer">
                        Privacy Policy
                      </a>
                    </Typography>
                  }
                />
                {formik.touched.termsAccepted && formik.errors.termsAccepted && (
                  <Typography color="error" variant="caption">
                    {formik.errors.termsAccepted}
                  </Typography>
                )}
              </FormControl>
            </Grid>
          </Grid>
        );
      default:
        return 'Unknown step';
    }
  };

  return (
    <StyledPaper elevation={isMobile ? 0 : 3}>
      <Typography component="h1" variant="h4" align="center" gutterBottom>
        Create Your Account
      </Typography>
      <Typography variant="subtitle1" align="center" color="textSecondary" paragraph>
        Complete the following steps to set up your account
      </Typography>
      
      {/* Stepper */}
      <Box sx={{ width: '100%', my: 4 }}>
        <Stepper 
          activeStep={activeStep} 
          alternativeLabel
          orientation={isMobile ? 'vertical' : 'horizontal'}
          sx={{
            '& .MuiStepLabel-iconContainer': {
              paddingRight: isMobile ? 2 : 0,
            },
          }}
        >
          {steps.map((step, index) => (
            <Step key={step.label}>
              <StepLabel StepIconComponent={StepIcon}>
                {!isMobile && step.label}
              </StepLabel>
            </Step>
          ))}
        </Stepper>
      </Box>
      
      {/* Error message */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}
      
      {/* Form content */}
      <Box component="form" onSubmit={formik.handleSubmit} sx={{ mt: 2 }}>
        {renderStepContent(activeStep)}
        
        {/* Navigation buttons */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
          <Button
            onClick={handleBack}
            disabled={activeStep === 0 || loading}
            startIcon={<ArrowBackIcon />}
            sx={{ minWidth: 120 }}
          >
            Back
          </Button>
          
          {activeStep === steps.length - 1 ? (
            <Button
              type="submit"
              variant="contained"
              color="primary"
              disabled={loading}
              endIcon={loading ? <CircularProgress size={24} /> : <ArrowForwardIcon />}
              sx={{ minWidth: 150 }}
            >
              {loading ? 'Submitting...' : 'Submit'}
            </Button>
          ) : (
            <Button
              variant="contained"
              color="primary"
              onClick={handleNext}
              endIcon={<ArrowForwardIcon />}
              disabled={loading}
              sx={{ minWidth: 120 }}
            >
              Next
            </Button>
          )}
        </Box>
      </Box>
      
      {/* Progress indicator */}
      {loading && (
        <Box sx={{ width: '100%', mt: 2 }}>
          <LinearProgress />
        </Box>
      )}
      
      {/* Debug info (remove in production) */}
      {process.env.NODE_ENV === 'development' && (
        <Box mt={4} p={2} bgcolor="background.default" borderRadius={1}>
          <Typography variant="caption" color="textSecondary">
            Form Values: {JSON.stringify(formik.values, null, 2)}
          </Typography>
        </Box>
      )}
    </StyledPaper>
  );
};

export default OnboardingWizard;
