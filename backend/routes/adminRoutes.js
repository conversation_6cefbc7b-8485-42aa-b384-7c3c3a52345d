const express = require('express');
const router = express.Router();
const User = require('../models/User');

// Get dashboard statistics
router.get('/stats', async (req, res) => {
  try {
    // Get active merchants count
    const activeMerchants = await User.countDocuments({ 
      role: 'merchant',
      isActive: true 
    });

    // Get active traders count
    const activeTraders = await User.countDocuments({ 
      role: 'trader',
      isActive: true 
    });

    res.json({
      success: true,
      data: {
        activeMerchants,
        activeTraders
      }
    });
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch dashboard statistics'
    });
  }
});

module.exports = router;
