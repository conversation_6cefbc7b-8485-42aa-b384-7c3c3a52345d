{"version": 3, "file": "static/js/693.fe43241e.chunk.js", "mappings": "wMAKMA,EAAqB,8BACrBC,EAAuB,gCACvBC,EAAgB,CAAEC,SAAS,EAAOC,YAAY,GAwC9CC,EAAmBC,EAAAA,WAA+C,CAACC,EAAOC,KAC9E,MAAM,KACJC,GAAO,EAAK,QACZC,GAAU,EACVC,iBAAkBC,EAClBC,mBAAoBC,KACjBC,GACDR,GACGS,EAAWC,GAAsBX,EAAAA,SAA6B,MAC/DK,GAAmBO,EAAAA,EAAAA,GAAeN,GAClCC,GAAqBK,EAAAA,EAAAA,GAAeJ,GACpCK,EAA8Bb,EAAAA,OAA2B,MACzDc,GAAeC,EAAAA,EAAAA,GAAgBb,EAAec,GAASL,EAAaK,IAEpEC,EAAmBjB,EAAAA,OAAO,CAC9BkB,QAAQ,EACRC,KAAAA,GACEC,KAAKF,QAAS,CAChB,EACAG,MAAAA,GACED,KAAKF,QAAS,CAChB,IACCI,QAGGtB,EAAAA,UAAU,KACd,GAAII,EAAS,CACX,IAASmB,EAAT,SAAuBC,GACrB,GAAIP,EAAWC,SAAWR,EAAW,OACrC,MAAMe,EAASD,EAAMC,OACjBf,EAAUgB,SAASD,GACrBZ,EAAsBS,QAAUG,EAEhCE,EAAMd,EAAsBS,QAAS,CAAEM,QAAQ,GAEnD,EAESC,EAAT,SAAwBL,GACtB,GAAIP,EAAWC,SAAWR,EAAW,OACrC,MAAMoB,EAAgBN,EAAMM,cAYN,OAAlBA,IAICpB,EAAUgB,SAASI,IACtBH,EAAMd,EAAsBS,QAAS,CAAEM,QAAQ,IAEnD,EAKSG,EAAT,SAAyBC,GAEvB,GADuBC,SAASC,gBACTD,SAASE,KAChC,IAAK,MAAMC,KAAYJ,EACjBI,EAASC,aAAaC,OAAS,GAAGX,EAAMjB,EAEhD,EAEAuB,SAASM,iBAAiB,UAAWhB,GACrCU,SAASM,iBAAiB,WAAYV,GACtC,MAAMW,EAAmB,IAAIC,iBAAiBV,GAG9C,OAFIrB,GAAW8B,EAAiBE,QAAQhC,EAAW,CAAEiC,WAAW,EAAMC,SAAS,IAExE,KACLX,SAASY,oBAAoB,UAAWtB,GACxCU,SAASY,oBAAoB,WAAYhB,GACzCW,EAAiBM,aAErB,GACC,CAAC1C,EAASM,EAAWO,EAAWC,SAE7BlB,EAAAA,UAAU,KACd,GAAIU,EAAW,CACbqC,EAAiBC,IAAI/B,GACrB,MAAMgC,EAA2BhB,SAASC,cAG1C,IAF4BxB,EAAUgB,SAASuB,GAErB,CACxB,MAAMC,EAAa,IAAIC,YAAYzD,EAAoBE,GACvDc,EAAU6B,iBAAiB7C,EAAoBW,GAC/CK,EAAU0C,cAAcF,GACnBA,EAAWG,oBA4ExB,SAAoBC,GAAoD,IAAzB,OAAE1B,GAAS,GAAM2B,UAAAjB,OAAA,QAAAkB,IAAAD,UAAA,GAAAA,UAAA,GAAI,CAAC,EACnE,MAAMN,EAA2BhB,SAASC,cAC1C,IAAK,MAAMuB,KAAaH,EAEtB,GADA3B,EAAM8B,EAAW,CAAE7B,WACfK,SAASC,gBAAkBe,EAA0B,MAE7D,CAjFUS,EAsMWC,EAtMYC,EAAsBlD,GAuM9CiD,EAAME,OAAQC,GAA0B,MAAjBA,EAAKC,UAvM+B,CAAEnC,QAAQ,IAChEK,SAASC,gBAAkBe,GAC7BtB,EAAMjB,GAGZ,CAEA,MAAO,KACLA,EAAUmC,oBAAoBnD,EAAoBW,GAKlD2D,WAAW,KACT,MAAMC,EAAe,IAAId,YAAYxD,EAAsBC,GAC3Dc,EAAU6B,iBAAiB5C,EAAsBY,GACjDG,EAAU0C,cAAca,GACnBA,EAAaZ,kBAChB1B,EAAMsB,GAA4BhB,SAASE,KAAM,CAAEP,QAAQ,IAG7DlB,EAAUmC,oBAAoBlD,EAAsBY,GAEpDwC,EAAiBmB,OAAOjD,IACvB,GAEP,CA4KJ,IAAqB0C,GA3KhB,CAACjD,EAAWL,EAAkBE,EAAoBU,IAGrD,MAAMkD,EAAsBnE,EAAAA,YACzBwB,IACC,IAAKrB,IAASC,EAAS,OACvB,GAAIa,EAAWC,OAAQ,OAEvB,MAAMkD,EAAyB,QAAd5C,EAAM6C,MAAkB7C,EAAM8C,SAAW9C,EAAM+C,UAAY/C,EAAMgD,QAC5EC,EAAiBxC,SAASC,cAEhC,GAAIkC,GAAYK,EAAgB,CAC9B,MAAMC,EAAYlD,EAAMmD,eACjBC,EAAOC,GA8CtB,SAA0BnE,GACxB,MAAM4C,EAAaM,EAAsBlD,GACnCkE,EAAQE,EAAYxB,EAAY5C,GAChCmE,EAAOC,EAAYxB,EAAWyB,UAAWrE,GAC/C,MAAO,CAACkE,EAAOC,EACjB,CAnD8BG,CAAiBN,GACLE,GAASC,EAMpCrD,EAAMyD,UAAYR,IAAmBI,EAG/BrD,EAAMyD,UAAYR,IAAmBG,IAC9CpD,EAAM0D,iBACF/E,GAAMwB,EAAMkD,EAAM,CAAEjD,QAAQ,MAJhCJ,EAAM0D,iBACF/E,GAAMwB,EAAMiD,EAAO,CAAEhD,QAAQ,KAJ/B6C,IAAmBC,GAAWlD,EAAM0D,gBAU5C,GAEF,CAAC/E,EAAMC,EAASa,EAAWC,SAG7B,OACEiE,EAAAA,EAAAA,KAACC,EAAAA,GAAUC,IAAV,CAAcC,UAAW,KAAO7E,EAAY8E,IAAKzE,EAAc0E,UAAWrB,MA0C/E,SAASP,EAAsBlD,GAC7B,MAAM+E,EAAuB,GACvBC,EAASzD,SAAS0D,iBAAiBjF,EAAWkF,WAAWC,aAAc,CAC3EC,WAAa9E,IACX,MAAM+E,EAAiC,UAAjB/E,EAAK+C,SAAqC,WAAd/C,EAAKgF,KACvD,OAAIhF,EAAKiF,UAAYjF,EAAKkF,QAAUH,EAAsBH,WAAWO,YAI9DnF,EAAKsE,UAAY,EAAIM,WAAWQ,cAAgBR,WAAWO,eAGtE,KAAOT,EAAOW,YAAYZ,EAAMa,KAAKZ,EAAOa,aAG5C,OAAOd,CACT,CAMA,SAASX,EAAY0B,EAAyB9F,GAC5C,IAAK,MAAM+F,KAAWD,EAEpB,IAAKE,EAASD,EAAS,CAAEE,KAAMjG,IAAc,OAAO+F,CAExD,CAEA,SAASC,EAAS1F,EAAA4F,GAAqD,IAAlC,KAAED,GAAKC,EAC1C,GAA0C,WAAtCC,iBAAiB7F,GAAM8F,WAAyB,OAAO,EAC3D,KAAO9F,GAAM,CAEX,QAAa,IAAT2F,GAAsB3F,IAAS2F,EAAM,OAAO,EAChD,GAAuC,SAAnCE,iBAAiB7F,GAAM+F,QAAoB,OAAO,EACtD/F,EAAOA,EAAKgG,aACd,CACA,OAAO,CACT,CAMA,SAASrF,EAAM8E,GAA2D,IAAzB,OAAE7E,GAAS,GAAM2B,UAAAjB,OAAA,QAAAkB,IAAAD,UAAA,GAAAA,UAAA,GAAI,CAAC,EAErE,GAAIkD,GAAWA,EAAQ9E,MAAO,CAC5B,MAAMsB,EAA2BhB,SAASC,cAE1CuE,EAAQ9E,MAAM,CAAEsF,eAAe,IAE3BR,IAAYxD,GAXpB,SAA2BwD,GACzB,OAAOA,aAAmBS,kBAAoB,WAAYT,CAC5D,CASgDU,CAAkBV,IAAY7E,GACxE6E,EAAQ7E,QACZ,CACF,CA5FA7B,EAAWqH,YAhMc,aAmSzB,IAAMrE,EAEN,WAEE,IAAIsE,EAAyB,GAE7B,MAAO,CACLrE,GAAAA,CAAI/B,GAEF,MAAMqG,EAAmBD,EAAM,GAC3BpG,IAAeqG,GACjBA,GAAkBnG,QAGpBkG,EAAQE,EAAYF,EAAOpG,GAC3BoG,EAAMG,QAAQvG,EAChB,EAEAiD,MAAAA,CAAOjD,GACLoG,EAAQE,EAAYF,EAAOpG,GAC3BoG,EAAM,IAAIhG,QACZ,EAEJ,CAvByBoG,GAyBzB,SAASF,EAAeG,EAAY5D,GAClC,MAAM6D,EAAe,IAAID,GACnBE,EAAQD,EAAaE,QAAQ/D,GAInC,OAHe,IAAX8D,GACFD,EAAaG,OAAOF,EAAO,GAEtBD,CACT,C,8ECjVWI,GAAYC,E,QAAAA,KCInBC,EAAU,WAEd,EAIIC,EAAelI,EAAAA,WAAiB,SAAUC,EAAOkI,GACjD,IAAI5C,EAAMvF,EAAAA,OAAa,MACnBoI,EAAKpI,EAAAA,SAAe,CACpBqI,gBAAiBJ,EACjBK,eAAgBL,EAChBM,mBAAoBN,IACpBO,EAAYJ,EAAG,GAAIK,EAAeL,EAAG,GACrCM,EAAezI,EAAMyI,aAAcC,EAAW1I,EAAM0I,SAAUC,EAAY3I,EAAM2I,UAAWC,EAAkB5I,EAAM4I,gBAAiBC,EAAU7I,EAAM6I,QAASC,EAAS9I,EAAM8I,OAAQC,EAAU/I,EAAM+I,QAASC,EAAahJ,EAAMgJ,WAAYC,EAAcjJ,EAAMiJ,YAAaC,EAAQlJ,EAAMkJ,MAAOC,EAAiBnJ,EAAMmJ,eAAgBC,EAAKpJ,EAAMqJ,GAAIC,OAAmB,IAAPF,EAAgB,MAAQA,EAAIG,EAAUvJ,EAAMuJ,QAASC,GAAOC,EAAAA,EAAAA,IAAOzJ,EAAO,CAAC,eAAgB,WAAY,YAAa,kBAAmB,UAAW,SAAU,UAAW,aAAc,cAAe,QAAS,iBAAkB,KAAM,YACzkB0J,EAAUX,EACVY,GAAeC,EAAAA,EAAAA,GAAa,CAACtE,EAAK4C,IAClC2B,GAAiBC,EAAAA,EAAAA,KAASA,EAAAA,EAAAA,IAAS,CAAC,EAAGN,GAAOjB,GAClD,OAAQxI,EAAAA,cAAoBA,EAAAA,SAAgB,KACxC8I,GAAY9I,EAAAA,cAAoB2J,EAAS,CAAEX,QAASjB,EAAWc,gBAAiBA,EAAiBE,OAAQA,EAAQE,WAAYA,EAAYC,YAAaA,EAAaC,MAAOA,EAAOV,aAAcA,EAAcW,iBAAkBA,EAAgBY,QAASzE,EAAKiE,QAASA,IACtQd,EAAgB1I,EAAAA,aAAmBA,EAAAA,SAAeiK,KAAKtB,IAAWoB,EAAAA,EAAAA,KAASA,EAAAA,EAAAA,IAAS,CAAC,EAAGD,GAAiB,CAAEvE,IAAKqE,KAAqB5J,EAAAA,cAAoBuJ,GAAWQ,EAAAA,EAAAA,IAAS,CAAC,EAAGD,EAAgB,CAAElB,UAAWA,EAAWrD,IAAKqE,IAAiBjB,GACvP,GACAT,EAAagC,aAAe,CACxBpB,SAAS,EACTD,iBAAiB,EACjBM,OAAO,GAEXjB,EAAaiC,WAAa,CACtBC,UAAWC,EAAAA,GACXC,UAAWC,EAAAA,I,kCCjCXC,GAAmB,EACvB,GAAsB,qBAAXC,OACP,IACI,IAAIC,EAAUC,OAAOC,eAAe,CAAC,EAAG,UAAW,CAC/CC,IAAK,WAED,OADAL,GAAmB,GACZ,CACX,IAGJC,OAAOlI,iBAAiB,OAAQmI,EAASA,GAEzCD,OAAO5H,oBAAoB,OAAQ6H,EAASA,EAChD,CACA,MAAOI,GACHN,GAAmB,CACvB,CAEG,IAAIO,IAAaP,GAAmB,CAAEQ,SAAS,GCdlDC,EAAuB,SAAUjK,EAAMkK,GACvC,KAAMlK,aAAgBmK,SAClB,OAAO,EAEX,IAAIC,EAASX,OAAO5D,iBAAiB7F,GACrC,MAEqB,WAArBoK,EAAOF,MAEDE,EAAOC,YAAcD,EAAOE,YAbX,SAAUtK,GAEjC,MAAwB,aAAjBA,EAAK+C,OAChB,CAUoDwH,CAAqBvK,IAA8B,YAArBoK,EAAOF,GACzF,EAGWM,EAA0B,SAAUC,EAAMzK,GACjD,IAAI0K,EAAgB1K,EAAK0K,cACrBpK,EAAUN,EACd,EAAG,CAMC,GAJ0B,qBAAf2K,YAA8BrK,aAAmBqK,aACxDrK,EAAUA,EAAQsK,MAEHC,EAAuBJ,EAAMnK,GAC9B,CACd,IAAI8G,EAAK0D,EAAmBL,EAAMnK,GAClC,GAD2D8G,EAAG,GAAmBA,EAAG,GAEhF,OAAO,CAEf,CACA9G,EAAUA,EAAQyK,UACtB,OAASzK,GAAWA,IAAYoK,EAAcvJ,MAC9C,OAAO,CACX,EAiBI0J,EAAyB,SAAUJ,EAAMzK,GACzC,MAAgB,MAATyK,EAtCmB,SAAUzK,GAAQ,OAAOiK,EAAqBjK,EAAM,YAAc,CAsCtEgL,CAAwBhL,GArCpB,SAAUA,GAAQ,OAAOiK,EAAqBjK,EAAM,YAAc,CAqCtCiL,CAAwBjL,EAClF,EACI8K,EAAqB,SAAUL,EAAMzK,GACrC,MAAgB,MAATyK,EAlBA,EAFyBrD,EAoBUpH,GAnBvBkL,UAA0B9D,EAAG+D,aAA6B/D,EAAGgE,cAO1D,SAAUhE,GAEhC,MAAO,CADUA,EAAGiE,WAA0BjE,EAAGkE,YAA2BlE,EAAGmE,YAMnF,CAKsDC,CAAoBxL,GApBhD,IAAUoH,CAqBpC,ECnDWqE,EAAa,SAAUjL,GAC9B,MAAO,mBAAoBA,EAAQ,CAACA,EAAMkL,eAAe,GAAGC,QAASnL,EAAMkL,eAAe,GAAGE,SAAW,CAAC,EAAG,EAChH,EACWC,EAAa,SAAUrL,GAAS,MAAO,CAACA,EAAMsL,OAAQtL,EAAMuL,OAAS,EAC5EC,EAAa,SAAUzH,GACvB,OAAOA,GAAO,YAAaA,EAAMA,EAAIjE,QAAUiE,CACnD,EAEI0H,EAAgB,SAAUC,GAAM,MAAO,4BAA4BC,OAAOD,EAAI,qDAAqDC,OAAOD,EAAI,4BAA8B,EAC5KE,EAAY,EACZC,EAAY,GAkIhB,SAASC,EAAyBtM,GAE9B,IADA,IAAIuM,EAAe,KACH,OAATvM,GACCA,aAAgB2K,aAChB4B,EAAevM,EAAK4K,KACpB5K,EAAOA,EAAK4K,MAEhB5K,EAAOA,EAAK+K,WAEhB,OAAOwB,CACX,CCzJA,SAAeC,EAAAA,EAAAA,GAAczF,EDctB,SAA6B9H,GAChC,IAAIwN,EAAqBzN,EAAAA,OAAa,IAClC0N,EAAgB1N,EAAAA,OAAa,CAAC,EAAG,IACjC2N,EAAa3N,EAAAA,SACbkN,EAAKlN,EAAAA,SAAeoN,KAAa,GACjCQ,EAAQ5N,EAAAA,SAAe6N,EAAAA,IAAgB,GACvCC,EAAY9N,EAAAA,OAAaC,GAC7BD,EAAAA,UAAgB,WACZ8N,EAAUxM,QAAUrB,CACxB,EAAG,CAACA,IACJD,EAAAA,UAAgB,WACZ,GAAIC,EAAMkJ,MAAO,CACblH,SAASE,KAAK4L,UAAU/K,IAAI,uBAAuBmK,OAAOD,IAC1D,IAAIc,GAAUC,EAAAA,EAAAA,IAAc,CAAChO,EAAM+J,QAAQ1I,UAAWrB,EAAM8I,QAAU,IAAImF,IAAIlB,IAAa,GAAMnJ,OAAOsK,SAExG,OADAH,EAAQI,QAAQ,SAAUC,GAAM,OAAOA,EAAGN,UAAU/K,IAAI,uBAAuBmK,OAAOD,GAAM,GACrF,WACHjL,SAASE,KAAK4L,UAAU7J,OAAO,uBAAuBiJ,OAAOD,IAC7Dc,EAAQI,QAAQ,SAAUC,GAAM,OAAOA,EAAGN,UAAU7J,OAAO,uBAAuBiJ,OAAOD,GAAM,EACnG,CACJ,CAEJ,EAAG,CAACjN,EAAMkJ,MAAOlJ,EAAM+J,QAAQ1I,QAASrB,EAAM8I,SAC9C,IAAIuF,EAAoBtO,EAAAA,YAAkB,SAAUwB,EAAO+M,GACvD,GAAK,YAAa/M,GAAkC,IAAzBA,EAAMgN,QAAQlM,QAAiC,UAAfd,EAAMwE,MAAoBxE,EAAM+C,QACvF,OAAQuJ,EAAUxM,QAAQ8H,eAE9B,IAIIqF,EAJAC,EAAQjC,EAAWjL,GACnBmN,EAAajB,EAAcpM,QAC3BwL,EAAS,WAAYtL,EAAQA,EAAMsL,OAAS6B,EAAW,GAAKD,EAAM,GAClE3B,EAAS,WAAYvL,EAAQA,EAAMuL,OAAS4B,EAAW,GAAKD,EAAM,GAElEjN,EAASD,EAAMC,OACfmN,EAAgBC,KAAKC,IAAIhC,GAAU+B,KAAKC,IAAI/B,GAAU,IAAM,IAEhE,GAAI,YAAavL,GAA2B,MAAlBoN,GAAyC,UAAhBnN,EAAOuE,KACtD,OAAO,EAEX,IAAI+I,EAA+BvD,EAAwBoD,EAAenN,GAC1E,IAAKsN,EACD,OAAO,EAUX,GARIA,EACAN,EAAcG,GAGdH,EAAgC,MAAlBG,EAAwB,IAAM,IAC5CG,EAA+BvD,EAAwBoD,EAAenN,KAGrEsN,EACD,OAAO,EAKX,IAHKpB,EAAWrM,SAAW,mBAAoBE,IAAUsL,GAAUC,KAC/DY,EAAWrM,QAAUmN,IAEpBA,EACD,OAAO,EAEX,IAAIO,EAAgBrB,EAAWrM,SAAWmN,EAC1C,ODVkB,SAAUhD,EAAMwD,EAAWzN,EAAO0N,EAAaC,GACrE,IAAIC,EATiB,SAAU3D,EAAM4D,GAMrC,MAAgB,MAAT5D,GAA8B,QAAd4D,GAAuB,EAAI,CACtD,CAE0BC,CAAmB7D,EAAMhB,OAAO5D,iBAAiBoI,GAAWI,WAC9EE,EAAQH,EAAkBF,EAE1BzN,EAASD,EAAMC,OACf+N,EAAeP,EAAUvN,SAASD,GAClCgO,GAAqB,EACrBC,EAAkBH,EAAQ,EAC1BI,EAAkB,EAClBC,EAAqB,EACzB,EAAG,CACC,IAAKnO,EACD,MAEJ,IAAI2G,EAAK0D,EAAmBL,EAAMhK,GAASoO,EAAWzH,EAAG,GACrD0H,EADoE1H,EAAG,GAAeA,EAAG,GACnDgH,EAAkBS,GACxDA,GAAYC,IACRjE,EAAuBJ,EAAMhK,KAC7BkO,GAAmBG,EACnBF,GAAsBC,GAG9B,IAAIE,EAAWtO,EAAOsK,WAGtBtK,EAAUsO,GAAYA,EAASC,WAAaC,KAAKC,uBAAyBH,EAASnE,KAAOmE,CAC9F,QAEEP,GAAgB/N,IAAWQ,SAASE,MAEjCqN,IAAiBP,EAAUvN,SAASD,IAAWwN,IAAcxN,IAUlE,OARIiO,IACEP,GAAgBN,KAAKC,IAAIa,GAAmB,IAAQR,GAAgBI,EAAQI,KAGxED,IACJP,GAAgBN,KAAKC,IAAIc,GAAsB,IAAQT,IAAiBI,EAAQK,MAHlFH,GAAqB,GAMlBA,CACX,CC/BeU,CAAanB,EAAeT,EAAQ/M,EAAyB,MAAlBwN,EAAwBlC,EAASC,GAAQ,EAC/F,EAAG,IACCqD,EAAgBpQ,EAAAA,YAAkB,SAAUqQ,GAC5C,IAAI7O,EAAQ6O,EACZ,GAAKhD,EAAU/K,QAAU+K,EAAUA,EAAU/K,OAAS,KAAOsL,EAA7D,CAIA,IAAI2B,EAAQ,WAAY/N,EAAQqL,EAAWrL,GAASiL,EAAWjL,GAC3D8O,EAAc7C,EAAmBnM,QAAQuC,OAAO,SAAU0M,GAAK,OAAOA,EAAEC,OAAShP,EAAMwE,OAASuK,EAAE9O,SAAWD,EAAMC,QAAUD,EAAMC,SAAW8O,EAAEhD,gBAxE/HkD,EAwE6JF,EAAEhB,MAxE5JmB,EAwEmKnB,EAxEvJkB,EAAE,KAAOC,EAAE,IAAMD,EAAE,KAAOC,EAAE,IAArD,IAAUD,EAAGC,CAwE2K,GAAG,GAEtM,GAAIJ,GAAeA,EAAYK,OACvBnP,EAAM1B,YACN0B,EAAM0D,sBAKd,IAAKoL,EAAa,CACd,IAAIM,GAAc9C,EAAUxM,QAAQyH,QAAU,IACzCmF,IAAIlB,GACJnJ,OAAOsK,SACPtK,OAAO,SAAU7C,GAAQ,OAAOA,EAAKU,SAASF,EAAMC,OAAS,IACjDmP,EAAWtO,OAAS,EAAIgM,EAAkB9M,EAAOoP,EAAW,KAAO9C,EAAUxM,QAAQ4H,cAE9F1H,EAAM1B,YACN0B,EAAM0D,gBAGlB,CAtBA,CAuBJ,EAAG,IACC2L,EAAe7Q,EAAAA,YAAkB,SAAUwQ,EAAMjB,EAAO9N,EAAQkP,GAChE,IAAInP,EAAQ,CAAEgP,KAAMA,EAAMjB,MAAOA,EAAO9N,OAAQA,EAAQkP,OAAQA,EAAQpD,aAAcD,EAAyB7L,IAC/GgM,EAAmBnM,QAAQgF,KAAK9E,GAChCwC,WAAW,WACPyJ,EAAmBnM,QAAUmM,EAAmBnM,QAAQuC,OAAO,SAAU0M,GAAK,OAAOA,IAAM/O,CAAO,EACtG,EAAG,EACP,EAAG,IACCsP,EAAmB9Q,EAAAA,YAAkB,SAAUwB,GAC/CkM,EAAcpM,QAAUmL,EAAWjL,GACnCmM,EAAWrM,aAAUkC,CACzB,EAAG,IACCuN,EAAc/Q,EAAAA,YAAkB,SAAUwB,GAC1CqP,EAAarP,EAAMwE,KAAM6G,EAAWrL,GAAQA,EAAMC,OAAQ6M,EAAkB9M,EAAOvB,EAAM+J,QAAQ1I,SACrG,EAAG,IACC0P,EAAkBhR,EAAAA,YAAkB,SAAUwB,GAC9CqP,EAAarP,EAAMwE,KAAMyG,EAAWjL,GAAQA,EAAMC,OAAQ6M,EAAkB9M,EAAOvB,EAAM+J,QAAQ1I,SACrG,EAAG,IACHtB,EAAAA,UAAgB,WAUZ,OATAqN,EAAU/G,KAAKsH,GACf3N,EAAMwI,aAAa,CACfJ,gBAAiB0I,EACjBzI,eAAgByI,EAChBxI,mBAAoByI,IAExB/O,SAASM,iBAAiB,QAAS6N,EAAerF,GAClD9I,SAASM,iBAAiB,YAAa6N,EAAerF,GACtD9I,SAASM,iBAAiB,aAAcuO,EAAkB/F,GACnD,WACHsC,EAAYA,EAAUxJ,OAAO,SAAUoN,GAAQ,OAAOA,IAASrD,CAAO,GACtE3L,SAASY,oBAAoB,QAASuN,EAAerF,GACrD9I,SAASY,oBAAoB,YAAauN,EAAerF,GACzD9I,SAASY,oBAAoB,aAAciO,EAAkB/F,EACjE,CACJ,EAAG,IACH,IAAIlC,EAAkB5I,EAAM4I,gBAAiBM,EAAQlJ,EAAMkJ,MAC3D,OAAQnJ,EAAAA,cAAoBA,EAAAA,SAAgB,KACxCmJ,EAAQnJ,EAAAA,cAAoB4N,EAAO,CAAExC,OAAQ6B,EAAcC,KAAS,KACpErE,EAAkB7I,EAAAA,cAAoBkR,EAAAA,GAAiB,CAAEjI,WAAYhJ,EAAMgJ,WAAYO,QAASvJ,EAAMuJ,UAAa,KAC3H,GE7IA,IAAI2H,EAAoBnR,EAAAA,WAAiB,SAAUC,EAAOsF,GAAO,OAAQvF,EAAAA,cAAoBkI,GAAc6B,EAAAA,EAAAA,IAAS,CAAC,EAAG9J,EAAO,CAAEsF,IAAKA,EAAKyD,QAASW,IAAc,GAClKwH,EAAkBhH,WAAajC,EAAaiC,WAC5C,S,+DCFMiH,GAAcpR,IAAAA,EAAAA,EAAAA,EAAAA,EAAAA,KAAc,UAAUqR,OAAOC,aAAe,MAAO,GACrEC,EAAQ,EAEZ,SAASC,EAAMC,GACb,MAAOvE,EAAIwE,GAAe1R,EAAAA,SAA6BoR,KAKvD,OAHAO,EAAAA,EAAAA,GAAgB,KACTF,GAAiBC,EAAOE,GAAYA,GAAWC,OAAON,OAC1D,CAACE,IACGA,IAAoBvE,EAAK,SAASA,IAAO,GAClD,C,iDCXIqE,EAAQ,EAWZ,SAASO,IACD9R,EAAAA,UAAU,KACd,MAAM+R,EAAa9P,SAAS+P,iBAAiB,4BAK7C,OAJA/P,SAASE,KAAK8P,sBAAsB,aAAcF,EAAW,IAAMG,KACnEjQ,SAASE,KAAK8P,sBAAsB,YAAaF,EAAW,IAAMG,KAClEX,IAEO,KACS,IAAVA,GACFtP,SAAS+P,iBAAiB,4BAA4B5D,QAASpN,GAASA,EAAKkD,UAE/EqN,MAED,GACL,CAEA,SAASW,IACP,MAAMzL,EAAUxE,SAASkQ,cAAc,QAOvC,OANA1L,EAAQ2L,aAAa,yBAA0B,IAC/C3L,EAAQnB,SAAW,EACnBmB,EAAQ4L,MAAMC,QAAU,OACxB7L,EAAQ4L,MAAME,QAAU,IACxB9L,EAAQ4L,MAAMxC,SAAW,QACzBpJ,EAAQ4L,MAAMG,cAAgB,OACvB/L,CACT,C", "sources": ["../node_modules/@radix-ui/react-focus-scope/src/focus-scope.tsx", "../node_modules/react-remove-scroll/dist/es2015/medium.js", "../node_modules/react-remove-scroll/dist/es2015/UI.js", "../node_modules/react-remove-scroll/dist/es2015/aggresiveCapture.js", "../node_modules/react-remove-scroll/dist/es2015/handleScroll.js", "../node_modules/react-remove-scroll/dist/es2015/SideEffect.js", "../node_modules/react-remove-scroll/dist/es2015/sidecar.js", "../node_modules/react-remove-scroll/dist/es2015/Combination.js", "../node_modules/@radix-ui/react-id/src/id.tsx", "../node_modules/@radix-ui/react-focus-guards/src/focus-guards.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\n\nconst AUTOFOCUS_ON_MOUNT = 'focusScope.autoFocusOnMount';\nconst AUTOFOCUS_ON_UNMOUNT = 'focusScope.autoFocusOnUnmount';\nconst EVENT_OPTIONS = { bubbles: false, cancelable: true };\n\ntype FocusableTarget = HTMLElement | { focus(): void };\n\n/* -------------------------------------------------------------------------------------------------\n * FocusScope\n * -----------------------------------------------------------------------------------------------*/\n\nconst FOCUS_SCOPE_NAME = 'FocusScope';\n\ntype FocusScopeElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface FocusScopeProps extends PrimitiveDivProps {\n  /**\n   * When `true`, tabbing from last item will focus first tabbable\n   * and shift+tab from first item will focus last tababble.\n   * @defaultValue false\n   */\n  loop?: boolean;\n\n  /**\n   * When `true`, focus cannot escape the focus scope via keyboard,\n   * pointer, or a programmatic focus.\n   * @defaultValue false\n   */\n  trapped?: boolean;\n\n  /**\n   * Event handler called when auto-focusing on mount.\n   * Can be prevented.\n   */\n  onMountAutoFocus?: (event: Event) => void;\n\n  /**\n   * Event handler called when auto-focusing on unmount.\n   * Can be prevented.\n   */\n  onUnmountAutoFocus?: (event: Event) => void;\n}\n\nconst FocusScope = React.forwardRef<FocusScopeElement, FocusScopeProps>((props, forwardedRef) => {\n  const {\n    loop = false,\n    trapped = false,\n    onMountAutoFocus: onMountAutoFocusProp,\n    onUnmountAutoFocus: onUnmountAutoFocusProp,\n    ...scopeProps\n  } = props;\n  const [container, setContainer] = React.useState<HTMLElement | null>(null);\n  const onMountAutoFocus = useCallbackRef(onMountAutoFocusProp);\n  const onUnmountAutoFocus = useCallbackRef(onUnmountAutoFocusProp);\n  const lastFocusedElementRef = React.useRef<HTMLElement | null>(null);\n  const composedRefs = useComposedRefs(forwardedRef, (node) => setContainer(node));\n\n  const focusScope = React.useRef({\n    paused: false,\n    pause() {\n      this.paused = true;\n    },\n    resume() {\n      this.paused = false;\n    },\n  }).current;\n\n  // Takes care of trapping focus if focus is moved outside programmatically for example\n  React.useEffect(() => {\n    if (trapped) {\n      function handleFocusIn(event: FocusEvent) {\n        if (focusScope.paused || !container) return;\n        const target = event.target as HTMLElement | null;\n        if (container.contains(target)) {\n          lastFocusedElementRef.current = target;\n        } else {\n          focus(lastFocusedElementRef.current, { select: true });\n        }\n      }\n\n      function handleFocusOut(event: FocusEvent) {\n        if (focusScope.paused || !container) return;\n        const relatedTarget = event.relatedTarget as HTMLElement | null;\n\n        // A `focusout` event with a `null` `relatedTarget` will happen in at least two cases:\n        //\n        // 1. When the user switches app/tabs/windows/the browser itself loses focus.\n        // 2. In Google Chrome, when the focused element is removed from the DOM.\n        //\n        // We let the browser do its thing here because:\n        //\n        // 1. The browser already keeps a memory of what's focused for when the page gets refocused.\n        // 2. In Google Chrome, if we try to focus the deleted focused element (as per below), it\n        //    throws the CPU to 100%, so we avoid doing anything for this reason here too.\n        if (relatedTarget === null) return;\n\n        // If the focus has moved to an actual legitimate element (`relatedTarget !== null`)\n        // that is outside the container, we move focus to the last valid focused element inside.\n        if (!container.contains(relatedTarget)) {\n          focus(lastFocusedElementRef.current, { select: true });\n        }\n      }\n\n      // When the focused element gets removed from the DOM, browsers move focus\n      // back to the document.body. In this case, we move focus to the container\n      // to keep focus trapped correctly.\n      function handleMutations(mutations: MutationRecord[]) {\n        const focusedElement = document.activeElement as HTMLElement | null;\n        if (focusedElement !== document.body) return;\n        for (const mutation of mutations) {\n          if (mutation.removedNodes.length > 0) focus(container);\n        }\n      }\n\n      document.addEventListener('focusin', handleFocusIn);\n      document.addEventListener('focusout', handleFocusOut);\n      const mutationObserver = new MutationObserver(handleMutations);\n      if (container) mutationObserver.observe(container, { childList: true, subtree: true });\n\n      return () => {\n        document.removeEventListener('focusin', handleFocusIn);\n        document.removeEventListener('focusout', handleFocusOut);\n        mutationObserver.disconnect();\n      };\n    }\n  }, [trapped, container, focusScope.paused]);\n\n  React.useEffect(() => {\n    if (container) {\n      focusScopesStack.add(focusScope);\n      const previouslyFocusedElement = document.activeElement as HTMLElement | null;\n      const hasFocusedCandidate = container.contains(previouslyFocusedElement);\n\n      if (!hasFocusedCandidate) {\n        const mountEvent = new CustomEvent(AUTOFOCUS_ON_MOUNT, EVENT_OPTIONS);\n        container.addEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n        container.dispatchEvent(mountEvent);\n        if (!mountEvent.defaultPrevented) {\n          focusFirst(removeLinks(getTabbableCandidates(container)), { select: true });\n          if (document.activeElement === previouslyFocusedElement) {\n            focus(container);\n          }\n        }\n      }\n\n      return () => {\n        container.removeEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n\n        // We hit a react bug (fixed in v17) with focusing in unmount.\n        // We need to delay the focus a little to get around it for now.\n        // See: https://github.com/facebook/react/issues/17894\n        setTimeout(() => {\n          const unmountEvent = new CustomEvent(AUTOFOCUS_ON_UNMOUNT, EVENT_OPTIONS);\n          container.addEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n          container.dispatchEvent(unmountEvent);\n          if (!unmountEvent.defaultPrevented) {\n            focus(previouslyFocusedElement ?? document.body, { select: true });\n          }\n          // we need to remove the listener after we `dispatchEvent`\n          container.removeEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n\n          focusScopesStack.remove(focusScope);\n        }, 0);\n      };\n    }\n  }, [container, onMountAutoFocus, onUnmountAutoFocus, focusScope]);\n\n  // Takes care of looping focus (when tabbing whilst at the edges)\n  const handleKeyDown = React.useCallback(\n    (event: React.KeyboardEvent) => {\n      if (!loop && !trapped) return;\n      if (focusScope.paused) return;\n\n      const isTabKey = event.key === 'Tab' && !event.altKey && !event.ctrlKey && !event.metaKey;\n      const focusedElement = document.activeElement as HTMLElement | null;\n\n      if (isTabKey && focusedElement) {\n        const container = event.currentTarget as HTMLElement;\n        const [first, last] = getTabbableEdges(container);\n        const hasTabbableElementsInside = first && last;\n\n        // we can only wrap focus if we have tabbable edges\n        if (!hasTabbableElementsInside) {\n          if (focusedElement === container) event.preventDefault();\n        } else {\n          if (!event.shiftKey && focusedElement === last) {\n            event.preventDefault();\n            if (loop) focus(first, { select: true });\n          } else if (event.shiftKey && focusedElement === first) {\n            event.preventDefault();\n            if (loop) focus(last, { select: true });\n          }\n        }\n      }\n    },\n    [loop, trapped, focusScope.paused]\n  );\n\n  return (\n    <Primitive.div tabIndex={-1} {...scopeProps} ref={composedRefs} onKeyDown={handleKeyDown} />\n  );\n});\n\nFocusScope.displayName = FOCUS_SCOPE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * Utils\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * Attempts focusing the first element in a list of candidates.\n * Stops when focus has actually moved.\n */\nfunction focusFirst(candidates: HTMLElement[], { select = false } = {}) {\n  const previouslyFocusedElement = document.activeElement;\n  for (const candidate of candidates) {\n    focus(candidate, { select });\n    if (document.activeElement !== previouslyFocusedElement) return;\n  }\n}\n\n/**\n * Returns the first and last tabbable elements inside a container.\n */\nfunction getTabbableEdges(container: HTMLElement) {\n  const candidates = getTabbableCandidates(container);\n  const first = findVisible(candidates, container);\n  const last = findVisible(candidates.reverse(), container);\n  return [first, last] as const;\n}\n\n/**\n * Returns a list of potential tabbable candidates.\n *\n * NOTE: This is only a close approximation. For example it doesn't take into account cases like when\n * elements are not visible. This cannot be worked out easily by just reading a property, but rather\n * necessitate runtime knowledge (computed styles, etc). We deal with these cases separately.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/TreeWalker\n * Credit: https://github.com/discord/focus-layers/blob/master/src/util/wrapFocus.tsx#L1\n */\nfunction getTabbableCandidates(container: HTMLElement) {\n  const nodes: HTMLElement[] = [];\n  const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n    acceptNode: (node: any) => {\n      const isHiddenInput = node.tagName === 'INPUT' && node.type === 'hidden';\n      if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n      // `.tabIndex` is not the same as the `tabindex` attribute. It works on the\n      // runtime's understanding of tabbability, so this automatically accounts\n      // for any kind of element that could be tabbed to.\n      return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n    },\n  });\n  while (walker.nextNode()) nodes.push(walker.currentNode as HTMLElement);\n  // we do not take into account the order of nodes with positive `tabIndex` as it\n  // hinders accessibility to have tab order different from visual order.\n  return nodes;\n}\n\n/**\n * Returns the first visible element in a list.\n * NOTE: Only checks visibility up to the `container`.\n */\nfunction findVisible(elements: HTMLElement[], container: HTMLElement) {\n  for (const element of elements) {\n    // we stop checking if it's hidden at the `container` level (excluding)\n    if (!isHidden(element, { upTo: container })) return element;\n  }\n}\n\nfunction isHidden(node: HTMLElement, { upTo }: { upTo?: HTMLElement }) {\n  if (getComputedStyle(node).visibility === 'hidden') return true;\n  while (node) {\n    // we stop at `upTo` (excluding it)\n    if (upTo !== undefined && node === upTo) return false;\n    if (getComputedStyle(node).display === 'none') return true;\n    node = node.parentElement as HTMLElement;\n  }\n  return false;\n}\n\nfunction isSelectableInput(element: any): element is FocusableTarget & { select: () => void } {\n  return element instanceof HTMLInputElement && 'select' in element;\n}\n\nfunction focus(element?: FocusableTarget | null, { select = false } = {}) {\n  // only focus if that element is focusable\n  if (element && element.focus) {\n    const previouslyFocusedElement = document.activeElement;\n    // NOTE: we prevent scrolling on focus, to minimize jarring transitions for users\n    element.focus({ preventScroll: true });\n    // only select if its not the same element, it supports selection and we need to select\n    if (element !== previouslyFocusedElement && isSelectableInput(element) && select)\n      element.select();\n  }\n}\n\n/* -------------------------------------------------------------------------------------------------\n * FocusScope stack\n * -----------------------------------------------------------------------------------------------*/\n\ntype FocusScopeAPI = { paused: boolean; pause(): void; resume(): void };\nconst focusScopesStack = createFocusScopesStack();\n\nfunction createFocusScopesStack() {\n  /** A stack of focus scopes, with the active one at the top */\n  let stack: FocusScopeAPI[] = [];\n\n  return {\n    add(focusScope: FocusScopeAPI) {\n      // pause the currently active focus scope (at the top of the stack)\n      const activeFocusScope = stack[0];\n      if (focusScope !== activeFocusScope) {\n        activeFocusScope?.pause();\n      }\n      // remove in case it already exists (because we'll re-add it at the top of the stack)\n      stack = arrayRemove(stack, focusScope);\n      stack.unshift(focusScope);\n    },\n\n    remove(focusScope: FocusScopeAPI) {\n      stack = arrayRemove(stack, focusScope);\n      stack[0]?.resume();\n    },\n  };\n}\n\nfunction arrayRemove<T>(array: T[], item: T) {\n  const updatedArray = [...array];\n  const index = updatedArray.indexOf(item);\n  if (index !== -1) {\n    updatedArray.splice(index, 1);\n  }\n  return updatedArray;\n}\n\nfunction removeLinks(items: HTMLElement[]) {\n  return items.filter((item) => item.tagName !== 'A');\n}\n\nconst Root = FocusScope;\n\nexport {\n  FocusScope,\n  //\n  Root,\n};\nexport type { FocusScopeProps };\n", "import { createSidecarMedium } from 'use-sidecar';\nexport var effectCar = createSidecarMedium();\n", "import { __assign, __rest } from \"tslib\";\nimport * as React from 'react';\nimport { fullWidthClassName, zeroRightClassName } from 'react-remove-scroll-bar/constants';\nimport { useMergeRefs } from 'use-callback-ref';\nimport { effectCar } from './medium';\nvar nothing = function () {\n    return;\n};\n/**\n * Removes scrollbar from the page and contain the scroll within the Lock\n */\nvar RemoveScroll = React.forwardRef(function (props, parentRef) {\n    var ref = React.useRef(null);\n    var _a = React.useState({\n        onScrollCapture: nothing,\n        onWheelCapture: nothing,\n        onTouchMoveCapture: nothing,\n    }), callbacks = _a[0], setCallbacks = _a[1];\n    var forwardProps = props.forwardProps, children = props.children, className = props.className, removeScrollBar = props.removeScrollBar, enabled = props.enabled, shards = props.shards, sideCar = props.sideCar, noRelative = props.noRelative, noIsolation = props.noIsolation, inert = props.inert, allowPinchZoom = props.allowPinchZoom, _b = props.as, Container = _b === void 0 ? 'div' : _b, gapMode = props.gapMode, rest = __rest(props, [\"forwardProps\", \"children\", \"className\", \"removeScrollBar\", \"enabled\", \"shards\", \"sideCar\", \"noRelative\", \"noIsolation\", \"inert\", \"allowPinchZoom\", \"as\", \"gapMode\"]);\n    var SideCar = sideCar;\n    var containerRef = useMergeRefs([ref, parentRef]);\n    var containerProps = __assign(__assign({}, rest), callbacks);\n    return (React.createElement(React.Fragment, null,\n        enabled && (React.createElement(SideCar, { sideCar: effectCar, removeScrollBar: removeScrollBar, shards: shards, noRelative: noRelative, noIsolation: noIsolation, inert: inert, setCallbacks: setCallbacks, allowPinchZoom: !!allowPinchZoom, lockRef: ref, gapMode: gapMode })),\n        forwardProps ? (React.cloneElement(React.Children.only(children), __assign(__assign({}, containerProps), { ref: containerRef }))) : (React.createElement(Container, __assign({}, containerProps, { className: className, ref: containerRef }), children))));\n});\nRemoveScroll.defaultProps = {\n    enabled: true,\n    removeScrollBar: true,\n    inert: false,\n};\nRemoveScroll.classNames = {\n    fullWidth: fullWidthClassName,\n    zeroRight: zeroRightClassName,\n};\nexport { RemoveScroll };\n", "var passiveSupported = false;\nif (typeof window !== 'undefined') {\n    try {\n        var options = Object.defineProperty({}, 'passive', {\n            get: function () {\n                passiveSupported = true;\n                return true;\n            },\n        });\n        // @ts-ignore\n        window.addEventListener('test', options, options);\n        // @ts-ignore\n        window.removeEventListener('test', options, options);\n    }\n    catch (err) {\n        passiveSupported = false;\n    }\n}\nexport var nonPassive = passiveSupported ? { passive: false } : false;\n", "var alwaysContainsScroll = function (node) {\n    // textarea will always _contain_ scroll inside self. It only can be hidden\n    return node.tagName === 'TEXTAREA';\n};\nvar elementCanBeScrolled = function (node, overflow) {\n    if (!(node instanceof Element)) {\n        return false;\n    }\n    var styles = window.getComputedStyle(node);\n    return (\n    // not-not-scrollable\n    styles[overflow] !== 'hidden' &&\n        // contains scroll inside self\n        !(styles.overflowY === styles.overflowX && !alwaysContainsScroll(node) && styles[overflow] === 'visible'));\n};\nvar elementCouldBeVScrolled = function (node) { return elementCanBeScrolled(node, 'overflowY'); };\nvar elementCouldBeHScrolled = function (node) { return elementCanBeScrolled(node, 'overflowX'); };\nexport var locationCouldBeScrolled = function (axis, node) {\n    var ownerDocument = node.ownerDocument;\n    var current = node;\n    do {\n        // Skip over shadow root\n        if (typeof ShadowRoot !== 'undefined' && current instanceof ShadowRoot) {\n            current = current.host;\n        }\n        var isScrollable = elementCouldBeScrolled(axis, current);\n        if (isScrollable) {\n            var _a = getScrollVariables(axis, current), scrollHeight = _a[1], clientHeight = _a[2];\n            if (scrollHeight > clientHeight) {\n                return true;\n            }\n        }\n        current = current.parentNode;\n    } while (current && current !== ownerDocument.body);\n    return false;\n};\nvar getVScrollVariables = function (_a) {\n    var scrollTop = _a.scrollTop, scrollHeight = _a.scrollHeight, clientHeight = _a.clientHeight;\n    return [\n        scrollTop,\n        scrollHeight,\n        clientHeight,\n    ];\n};\nvar getHScrollVariables = function (_a) {\n    var scrollLeft = _a.scrollLeft, scrollWidth = _a.scrollWidth, clientWidth = _a.clientWidth;\n    return [\n        scrollLeft,\n        scrollWidth,\n        clientWidth,\n    ];\n};\nvar elementCouldBeScrolled = function (axis, node) {\n    return axis === 'v' ? elementCouldBeVScrolled(node) : elementCouldBeHScrolled(node);\n};\nvar getScrollVariables = function (axis, node) {\n    return axis === 'v' ? getVScrollVariables(node) : getHScrollVariables(node);\n};\nvar getDirectionFactor = function (axis, direction) {\n    /**\n     * If the element's direction is rtl (right-to-left), then scrollLeft is 0 when the scrollbar is at its rightmost position,\n     * and then increasingly negative as you scroll towards the end of the content.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/Element/scrollLeft\n     */\n    return axis === 'h' && direction === 'rtl' ? -1 : 1;\n};\nexport var handleScroll = function (axis, endTarget, event, sourceDelta, noOverscroll) {\n    var directionFactor = getDirectionFactor(axis, window.getComputedStyle(endTarget).direction);\n    var delta = directionFactor * sourceDelta;\n    // find scrollable target\n    var target = event.target;\n    var targetInLock = endTarget.contains(target);\n    var shouldCancelScroll = false;\n    var isDeltaPositive = delta > 0;\n    var availableScroll = 0;\n    var availableScrollTop = 0;\n    do {\n        if (!target) {\n            break;\n        }\n        var _a = getScrollVariables(axis, target), position = _a[0], scroll_1 = _a[1], capacity = _a[2];\n        var elementScroll = scroll_1 - capacity - directionFactor * position;\n        if (position || elementScroll) {\n            if (elementCouldBeScrolled(axis, target)) {\n                availableScroll += elementScroll;\n                availableScrollTop += position;\n            }\n        }\n        var parent_1 = target.parentNode;\n        // we will \"bubble\" from ShadowDom in case we are, or just to the parent in normal case\n        // this is the same logic used in focus-lock\n        target = (parent_1 && parent_1.nodeType === Node.DOCUMENT_FRAGMENT_NODE ? parent_1.host : parent_1);\n    } while (\n    // portaled content\n    (!targetInLock && target !== document.body) ||\n        // self content\n        (targetInLock && (endTarget.contains(target) || endTarget === target)));\n    // handle epsilon around 0 (non standard zoom levels)\n    if (isDeltaPositive &&\n        ((noOverscroll && Math.abs(availableScroll) < 1) || (!noOverscroll && delta > availableScroll))) {\n        shouldCancelScroll = true;\n    }\n    else if (!isDeltaPositive &&\n        ((noOverscroll && Math.abs(availableScrollTop) < 1) || (!noOverscroll && -delta > availableScrollTop))) {\n        shouldCancelScroll = true;\n    }\n    return shouldCancelScroll;\n};\n", "import { __spreadArray } from \"tslib\";\nimport * as React from 'react';\nimport { RemoveScrollBar } from 'react-remove-scroll-bar';\nimport { styleSingleton } from 'react-style-singleton';\nimport { nonPassive } from './aggresiveCapture';\nimport { handleScroll, locationCouldBeScrolled } from './handleScroll';\nexport var getTouchXY = function (event) {\n    return 'changedTouches' in event ? [event.changedTouches[0].clientX, event.changedTouches[0].clientY] : [0, 0];\n};\nexport var getDeltaXY = function (event) { return [event.deltaX, event.deltaY]; };\nvar extractRef = function (ref) {\n    return ref && 'current' in ref ? ref.current : ref;\n};\nvar deltaCompare = function (x, y) { return x[0] === y[0] && x[1] === y[1]; };\nvar generateStyle = function (id) { return \"\\n  .block-interactivity-\".concat(id, \" {pointer-events: none;}\\n  .allow-interactivity-\").concat(id, \" {pointer-events: all;}\\n\"); };\nvar idCounter = 0;\nvar lockStack = [];\nexport function RemoveScrollSideCar(props) {\n    var shouldPreventQueue = React.useRef([]);\n    var touchStartRef = React.useRef([0, 0]);\n    var activeAxis = React.useRef();\n    var id = React.useState(idCounter++)[0];\n    var Style = React.useState(styleSingleton)[0];\n    var lastProps = React.useRef(props);\n    React.useEffect(function () {\n        lastProps.current = props;\n    }, [props]);\n    React.useEffect(function () {\n        if (props.inert) {\n            document.body.classList.add(\"block-interactivity-\".concat(id));\n            var allow_1 = __spreadArray([props.lockRef.current], (props.shards || []).map(extractRef), true).filter(Boolean);\n            allow_1.forEach(function (el) { return el.classList.add(\"allow-interactivity-\".concat(id)); });\n            return function () {\n                document.body.classList.remove(\"block-interactivity-\".concat(id));\n                allow_1.forEach(function (el) { return el.classList.remove(\"allow-interactivity-\".concat(id)); });\n            };\n        }\n        return;\n    }, [props.inert, props.lockRef.current, props.shards]);\n    var shouldCancelEvent = React.useCallback(function (event, parent) {\n        if (('touches' in event && event.touches.length === 2) || (event.type === 'wheel' && event.ctrlKey)) {\n            return !lastProps.current.allowPinchZoom;\n        }\n        var touch = getTouchXY(event);\n        var touchStart = touchStartRef.current;\n        var deltaX = 'deltaX' in event ? event.deltaX : touchStart[0] - touch[0];\n        var deltaY = 'deltaY' in event ? event.deltaY : touchStart[1] - touch[1];\n        var currentAxis;\n        var target = event.target;\n        var moveDirection = Math.abs(deltaX) > Math.abs(deltaY) ? 'h' : 'v';\n        // allow horizontal touch move on Range inputs. They will not cause any scroll\n        if ('touches' in event && moveDirection === 'h' && target.type === 'range') {\n            return false;\n        }\n        var canBeScrolledInMainDirection = locationCouldBeScrolled(moveDirection, target);\n        if (!canBeScrolledInMainDirection) {\n            return true;\n        }\n        if (canBeScrolledInMainDirection) {\n            currentAxis = moveDirection;\n        }\n        else {\n            currentAxis = moveDirection === 'v' ? 'h' : 'v';\n            canBeScrolledInMainDirection = locationCouldBeScrolled(moveDirection, target);\n            // other axis might be not scrollable\n        }\n        if (!canBeScrolledInMainDirection) {\n            return false;\n        }\n        if (!activeAxis.current && 'changedTouches' in event && (deltaX || deltaY)) {\n            activeAxis.current = currentAxis;\n        }\n        if (!currentAxis) {\n            return true;\n        }\n        var cancelingAxis = activeAxis.current || currentAxis;\n        return handleScroll(cancelingAxis, parent, event, cancelingAxis === 'h' ? deltaX : deltaY, true);\n    }, []);\n    var shouldPrevent = React.useCallback(function (_event) {\n        var event = _event;\n        if (!lockStack.length || lockStack[lockStack.length - 1] !== Style) {\n            // not the last active\n            return;\n        }\n        var delta = 'deltaY' in event ? getDeltaXY(event) : getTouchXY(event);\n        var sourceEvent = shouldPreventQueue.current.filter(function (e) { return e.name === event.type && (e.target === event.target || event.target === e.shadowParent) && deltaCompare(e.delta, delta); })[0];\n        // self event, and should be canceled\n        if (sourceEvent && sourceEvent.should) {\n            if (event.cancelable) {\n                event.preventDefault();\n            }\n            return;\n        }\n        // outside or shard event\n        if (!sourceEvent) {\n            var shardNodes = (lastProps.current.shards || [])\n                .map(extractRef)\n                .filter(Boolean)\n                .filter(function (node) { return node.contains(event.target); });\n            var shouldStop = shardNodes.length > 0 ? shouldCancelEvent(event, shardNodes[0]) : !lastProps.current.noIsolation;\n            if (shouldStop) {\n                if (event.cancelable) {\n                    event.preventDefault();\n                }\n            }\n        }\n    }, []);\n    var shouldCancel = React.useCallback(function (name, delta, target, should) {\n        var event = { name: name, delta: delta, target: target, should: should, shadowParent: getOutermostShadowParent(target) };\n        shouldPreventQueue.current.push(event);\n        setTimeout(function () {\n            shouldPreventQueue.current = shouldPreventQueue.current.filter(function (e) { return e !== event; });\n        }, 1);\n    }, []);\n    var scrollTouchStart = React.useCallback(function (event) {\n        touchStartRef.current = getTouchXY(event);\n        activeAxis.current = undefined;\n    }, []);\n    var scrollWheel = React.useCallback(function (event) {\n        shouldCancel(event.type, getDeltaXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n    }, []);\n    var scrollTouchMove = React.useCallback(function (event) {\n        shouldCancel(event.type, getTouchXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n    }, []);\n    React.useEffect(function () {\n        lockStack.push(Style);\n        props.setCallbacks({\n            onScrollCapture: scrollWheel,\n            onWheelCapture: scrollWheel,\n            onTouchMoveCapture: scrollTouchMove,\n        });\n        document.addEventListener('wheel', shouldPrevent, nonPassive);\n        document.addEventListener('touchmove', shouldPrevent, nonPassive);\n        document.addEventListener('touchstart', scrollTouchStart, nonPassive);\n        return function () {\n            lockStack = lockStack.filter(function (inst) { return inst !== Style; });\n            document.removeEventListener('wheel', shouldPrevent, nonPassive);\n            document.removeEventListener('touchmove', shouldPrevent, nonPassive);\n            document.removeEventListener('touchstart', scrollTouchStart, nonPassive);\n        };\n    }, []);\n    var removeScrollBar = props.removeScrollBar, inert = props.inert;\n    return (React.createElement(React.Fragment, null,\n        inert ? React.createElement(Style, { styles: generateStyle(id) }) : null,\n        removeScrollBar ? React.createElement(RemoveScrollBar, { noRelative: props.noRelative, gapMode: props.gapMode }) : null));\n}\nfunction getOutermostShadowParent(node) {\n    var shadowParent = null;\n    while (node !== null) {\n        if (node instanceof ShadowRoot) {\n            shadowParent = node.host;\n            node = node.host;\n        }\n        node = node.parentNode;\n    }\n    return shadowParent;\n}\n", "import { exportSidecar } from 'use-sidecar';\nimport { RemoveScrollSideCar } from './SideEffect';\nimport { effectCar } from './medium';\nexport default exportSidecar(effectCar, RemoveScrollSideCar);\n", "import { __assign } from \"tslib\";\nimport * as React from 'react';\nimport { RemoveScroll } from './UI';\nimport SideCar from './sidecar';\nvar ReactRemoveScroll = React.forwardRef(function (props, ref) { return (React.createElement(RemoveScroll, __assign({}, props, { ref: ref, sideCar: SideCar }))); });\nReactRemoveScroll.classNames = RemoveScroll.classNames;\nexport default ReactRemoveScroll;\n", "import * as React from 'react';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\n\n// We spaces with `.trim().toString()` to prevent bundlers from trying to `import { useId } from 'react';`\nconst useReactId = (React as any)[' useId '.trim().toString()] || (() => undefined);\nlet count = 0;\n\nfunction useId(deterministicId?: string): string {\n  const [id, setId] = React.useState<string | undefined>(useReactId());\n  // React versions older than 18 will have client-side ids only.\n  useLayoutEffect(() => {\n    if (!deterministicId) setId((reactId) => reactId ?? String(count++));\n  }, [deterministicId]);\n  return deterministicId || (id ? `radix-${id}` : '');\n}\n\nexport { useId };\n", "import * as React from 'react';\n\n/** Number of components which have requested interest to have focus guards */\nlet count = 0;\n\nfunction FocusGuards(props: any) {\n  useFocusGuards();\n  return props.children;\n}\n\n/**\n * Injects a pair of focus guards at the edges of the whole DOM tree\n * to ensure `focusin` & `focusout` events can be caught consistently.\n */\nfunction useFocusGuards() {\n  React.useEffect(() => {\n    const edgeGuards = document.querySelectorAll('[data-radix-focus-guard]');\n    document.body.insertAdjacentElement('afterbegin', edgeGuards[0] ?? createFocusGuard());\n    document.body.insertAdjacentElement('beforeend', edgeGuards[1] ?? createFocusGuard());\n    count++;\n\n    return () => {\n      if (count === 1) {\n        document.querySelectorAll('[data-radix-focus-guard]').forEach((node) => node.remove());\n      }\n      count--;\n    };\n  }, []);\n}\n\nfunction createFocusGuard() {\n  const element = document.createElement('span');\n  element.setAttribute('data-radix-focus-guard', '');\n  element.tabIndex = 0;\n  element.style.outline = 'none';\n  element.style.opacity = '0';\n  element.style.position = 'fixed';\n  element.style.pointerEvents = 'none';\n  return element;\n}\n\nconst Root = FocusGuards;\n\nexport {\n  FocusGuards,\n  //\n  Root,\n  //\n  useFocusGuards,\n};\n"], "names": ["AUTOFOCUS_ON_MOUNT", "AUTOFOCUS_ON_UNMOUNT", "EVENT_OPTIONS", "bubbles", "cancelable", "FocusScope", "React", "props", "forwardedRef", "loop", "trapped", "onMountAutoFocus", "onMountAutoFocusProp", "onUnmountAutoFocus", "onUnmountAutoFocusProp", "scopeProps", "container", "<PERSON><PERSON><PERSON><PERSON>", "useCallbackRef", "lastFocusedElementRef", "composedRefs", "useComposedRefs", "node", "focusScope", "paused", "pause", "this", "resume", "current", "handleFocusIn2", "event", "target", "contains", "focus", "select", "handleFocusOut2", "relatedTarget", "handleMutations2", "mutations", "document", "activeElement", "body", "mutation", "removedNodes", "length", "addEventListener", "mutationObserver", "MutationObserver", "observe", "childList", "subtree", "removeEventListener", "disconnect", "focusScopesStack", "add", "previouslyFocusedElement", "mountEvent", "CustomEvent", "dispatchEvent", "defaultPrevented", "candidates", "arguments", "undefined", "candidate", "focusFirst", "items", "getTabbableCandidates", "filter", "item", "tagName", "setTimeout", "unmountEvent", "remove", "handleKeyDown", "isTabKey", "key", "altKey", "ctrl<PERSON>ey", "metaKey", "focusedElement", "container2", "currentTarget", "first", "last", "findVisible", "reverse", "getTabbableEdges", "shift<PERSON>ey", "preventDefault", "jsx", "Primitive", "div", "tabIndex", "ref", "onKeyDown", "nodes", "walker", "createTreeWalker", "Node<PERSON><PERSON><PERSON>", "SHOW_ELEMENT", "acceptNode", "isHiddenInput", "type", "disabled", "hidden", "FILTER_SKIP", "FILTER_ACCEPT", "nextNode", "push", "currentNode", "elements", "element", "isHidden", "upTo", "_ref", "getComputedStyle", "visibility", "display", "parentElement", "preventScroll", "HTMLInputElement", "isSelectableInput", "displayName", "stack", "activeFocusScope", "arrayRemove", "unshift", "createFocusScopesStack", "array", "updatedArray", "index", "indexOf", "splice", "effectCar", "createSidecarMedium", "nothing", "RemoveScroll", "parentRef", "_a", "onScrollCapture", "onWheelCapture", "onTouchMoveCapture", "callbacks", "setCallbacks", "forwardProps", "children", "className", "removeScrollBar", "enabled", "shards", "sideCar", "noRelative", "noIsolation", "inert", "allowPinchZoom", "_b", "as", "Container", "gapMode", "rest", "__rest", "SideCar", "containerRef", "useMergeRefs", "containerProps", "__assign", "lockRef", "only", "defaultProps", "classNames", "fullWidth", "fullWidthClassName", "zeroRight", "zeroRightClassName", "passiveSupported", "window", "options", "Object", "defineProperty", "get", "err", "nonPassive", "passive", "elementCanBeScrolled", "overflow", "Element", "styles", "overflowY", "overflowX", "alwaysContainsScroll", "locationCouldBeScrolled", "axis", "ownerDocument", "ShadowRoot", "host", "elementCouldBeScrolled", "getScrollVariables", "parentNode", "elementCouldBeVScrolled", "elementCouldBeHScrolled", "scrollTop", "scrollHeight", "clientHeight", "scrollLeft", "scrollWidth", "clientWidth", "getHScrollVariables", "getTouchXY", "changedTouches", "clientX", "clientY", "getDeltaXY", "deltaX", "deltaY", "extractRef", "generateStyle", "id", "concat", "idCounter", "lockStack", "getOutermostShadowParent", "shadowParent", "exportSidecar", "shouldPreventQueue", "touchStartRef", "activeAxis", "Style", "styleSingleton", "lastProps", "classList", "allow_1", "__spread<PERSON><PERSON>y", "map", "Boolean", "for<PERSON>ach", "el", "shouldCancelEvent", "parent", "touches", "currentAxis", "touch", "touchStart", "moveDirection", "Math", "abs", "canBeScrolledInMainDirection", "cancelingAxis", "end<PERSON>ar<PERSON>", "sourceDelta", "noOverscroll", "directionFactor", "direction", "getDirectionFactor", "delta", "targetInLock", "shouldCancelScroll", "isDeltaPositive", "availableScroll", "availableScrollTop", "position", "elementScroll", "parent_1", "nodeType", "Node", "DOCUMENT_FRAGMENT_NODE", "handleScroll", "shouldPrevent", "_event", "sourceEvent", "e", "name", "x", "y", "should", "shardNodes", "shouldCancel", "scrollTouchStart", "scrollWheel", "scrollTouchMove", "inst", "RemoveScrollBar", "ReactRemoveScroll", "useReactId", "trim", "toString", "count", "useId", "deterministicId", "setId", "useLayoutEffect", "reactId", "String", "useFocusGuards", "edgeGuards", "querySelectorAll", "insertAdjacentElement", "createFocusGuard", "createElement", "setAttribute", "style", "outline", "opacity", "pointerEvents"], "sourceRoot": ""}