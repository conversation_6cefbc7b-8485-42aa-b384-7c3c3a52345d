"use strict";(self.webpackChunkpayment_gateway_frontend=self.webpackChunkpayment_gateway_frontend||[]).push([[825],{1285:(e,t,n)=>{n.d(t,{A:()=>r});const r=(0,n(3797).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},2500:(e,t,n)=>{n.d(t,{UC:()=>Xt,YJ:()=>Jt,In:()=>qt,q7:()=>Qt,VF:()=>tn,p4:()=>en,JU:()=>Gt,ZL:()=>Yt,bL:()=>zt,wv:()=>nn,l9:()=>Ut,WT:()=>jt,LM:()=>Zt});var r=n(8168),o=n(5043),l=n.t(o,2),i=n(7950);function a(e,t){let[n,r]=t;return Math.min(r,Math.max(n,e))}function c(e,t){let{checkForDefaultPrevented:n=!0}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return function(r){if(null===e||void 0===e||e(r),!1===n||!r.defaultPrevented)return null===t||void 0===t?void 0:t(r)}}function s(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=[];const r=()=>{const t=n.map(e=>(0,o.createContext)(e));return function(n){const r=(null===n||void 0===n?void 0:n[e])||t;return(0,o.useMemo)(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){const l=(0,o.createContext)(r),i=n.length;function a(t){const{scope:n,children:r,...a}=t,c=(null===n||void 0===n?void 0:n[e][i])||l,s=(0,o.useMemo)(()=>a,Object.values(a));return(0,o.createElement)(c.Provider,{value:s},r)}return n=[...n,r],a.displayName=t+"Provider",[a,function(n,a){const c=(null===a||void 0===a?void 0:a[e][i])||l,s=(0,o.useContext)(c);if(s)return s;if(void 0!==r)return r;throw new Error(`\`${n}\` must be used within \`${t}\``)}]},u(r,...t)]}function u(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];const r=t[0];if(1===t.length)return r;const l=()=>{const e=t.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(t){const n=e.reduce((e,n)=>{let{useScope:r,scopeName:o}=n;return{...e,...r(t)[`__scope${o}`]}},{});return(0,o.useMemo)(()=>({[`__scope${r.scopeName}`]:n}),[n])}};return l.scopeName=r.scopeName,l}function d(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return e=>t.forEach(t=>function(e,t){"function"===typeof e?e(t):null!==e&&void 0!==e&&(e.current=t)}(t,e))}function f(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,o.useCallback)(d(...t),t)}const v=(0,o.forwardRef)((e,t)=>{const{children:n,...l}=e,i=o.Children.toArray(n),a=i.find(h);if(a){const e=a.props.children,n=i.map(t=>t===a?o.Children.count(e)>1?o.Children.only(null):(0,o.isValidElement)(e)?e.props.children:null:t);return(0,o.createElement)(p,(0,r.A)({},l,{ref:t}),(0,o.isValidElement)(e)?(0,o.cloneElement)(e,void 0,n):null)}return(0,o.createElement)(p,(0,r.A)({},l,{ref:t}),n)});v.displayName="Slot";const p=(0,o.forwardRef)((e,t)=>{const{children:n,...r}=e;return(0,o.isValidElement)(n)?(0,o.cloneElement)(n,{...g(r,n.props),ref:t?d(t,n.ref):n.ref}):o.Children.count(n)>1?o.Children.only(null):null});p.displayName="SlotClone";const m=e=>{let{children:t}=e;return(0,o.createElement)(o.Fragment,null,t)};function h(e){return(0,o.isValidElement)(e)&&e.type===m}function g(e,t){const n={...t};for(const r in t){const o=e[r],l=t[r];/^on[A-Z]/.test(r)?o&&l?n[r]=function(){l(...arguments),o(...arguments)}:o&&(n[r]=o):"style"===r?n[r]={...o,...l}:"className"===r&&(n[r]=[o,l].filter(Boolean).join(" "))}return{...e,...n}}const w=(0,o.createContext)(void 0);const y=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{const n=(0,o.forwardRef)((e,n)=>{const{asChild:l,...i}=e,a=l?v:t;return(0,o.useEffect)(()=>{window[Symbol.for("radix-ui")]=!0},[]),(0,o.createElement)(a,(0,r.A)({},i,{ref:n}))});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function E(e){const t=(0,o.useRef)(e);return(0,o.useEffect)(()=>{t.current=e}),(0,o.useMemo)(()=>function(){for(var e,n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return null===(e=t.current)||void 0===e?void 0:e.call(t,...r)},[])}const b="dismissableLayer.update",C="dismissableLayer.pointerDownOutside",x="dismissableLayer.focusOutside";let S;const R=(0,o.createContext)({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),P=(0,o.forwardRef)((e,t)=>{var n;const{disableOutsidePointerEvents:l=!1,onEscapeKeyDown:i,onPointerDownOutside:a,onFocusOutside:s,onInteractOutside:u,onDismiss:d,...v}=e,p=(0,o.useContext)(R),[m,h]=(0,o.useState)(null),g=null!==(n=null===m||void 0===m?void 0:m.ownerDocument)&&void 0!==n?n:null===globalThis||void 0===globalThis?void 0:globalThis.document,[,w]=(0,o.useState)({}),P=f(t,e=>h(e)),k=Array.from(p.layers),[L]=[...p.layersWithOutsidePointerEventsDisabled].slice(-1),D=k.indexOf(L),N=m?k.indexOf(m):-1,_=p.layersWithOutsidePointerEventsDisabled.size>0,O=N>=D,I=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===globalThis||void 0===globalThis?void 0:globalThis.document;const n=E(e),r=(0,o.useRef)(!1),l=(0,o.useRef)(()=>{});return(0,o.useEffect)(()=>{const e=e=>{if(e.target&&!r.current){const o={originalEvent:e};function i(){T(C,n,o,{discrete:!0})}"touch"===e.pointerType?(t.removeEventListener("click",l.current),l.current=i,t.addEventListener("click",l.current,{once:!0})):i()}r.current=!1},o=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(o),t.removeEventListener("pointerdown",e),t.removeEventListener("click",l.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{const t=e.target,n=[...p.branches].some(e=>e.contains(t));O&&!n&&(null===a||void 0===a||a(e),null===u||void 0===u||u(e),e.defaultPrevented||null===d||void 0===d||d())},g),M=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===globalThis||void 0===globalThis?void 0:globalThis.document;const n=E(e),r=(0,o.useRef)(!1);return(0,o.useEffect)(()=>{const e=e=>{if(e.target&&!r.current){T(x,n,{originalEvent:e},{discrete:!1})}};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{const t=e.target;[...p.branches].some(e=>e.contains(t))||(null===s||void 0===s||s(e),null===u||void 0===u||u(e),e.defaultPrevented||null===d||void 0===d||d())},g);return function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===globalThis||void 0===globalThis?void 0:globalThis.document;const n=E(e);(0,o.useEffect)(()=>{const e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e),()=>t.removeEventListener("keydown",e)},[n,t])}(e=>{N===p.layers.size-1&&(null===i||void 0===i||i(e),!e.defaultPrevented&&d&&(e.preventDefault(),d()))},g),(0,o.useEffect)(()=>{if(m)return l&&(0===p.layersWithOutsidePointerEventsDisabled.size&&(S=g.body.style.pointerEvents,g.body.style.pointerEvents="none"),p.layersWithOutsidePointerEventsDisabled.add(m)),p.layers.add(m),A(),()=>{l&&1===p.layersWithOutsidePointerEventsDisabled.size&&(g.body.style.pointerEvents=S)}},[m,g,l,p]),(0,o.useEffect)(()=>()=>{m&&(p.layers.delete(m),p.layersWithOutsidePointerEventsDisabled.delete(m),A())},[m,p]),(0,o.useEffect)(()=>{const e=()=>w({});return document.addEventListener(b,e),()=>document.removeEventListener(b,e)},[]),(0,o.createElement)(y.div,(0,r.A)({},v,{ref:P,style:{pointerEvents:_?O?"auto":"none":void 0,...e.style},onFocusCapture:c(e.onFocusCapture,M.onFocusCapture),onBlurCapture:c(e.onBlurCapture,M.onBlurCapture),onPointerDownCapture:c(e.onPointerDownCapture,I.onPointerDownCapture)}))});function A(){const e=new CustomEvent(b);document.dispatchEvent(e)}function T(e,t,n,r){let{discrete:o}=r;const l=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&l.addEventListener(e,t,{once:!0}),o?function(e,t){e&&(0,i.flushSync)(()=>e.dispatchEvent(t))}(l,a):l.dispatchEvent(a)}let k=0;function L(){(0,o.useEffect)(()=>{var e,t;const n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=n[0])&&void 0!==e?e:D()),document.body.insertAdjacentElement("beforeend",null!==(t=n[1])&&void 0!==t?t:D()),k++,()=>{1===k&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),k--}},[])}function D(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.cssText="outline: none; opacity: 0; position: fixed; pointer-events: none",e}const N="focusScope.autoFocusOnMount",_="focusScope.autoFocusOnUnmount",O={bubbles:!1,cancelable:!0},I=(0,o.forwardRef)((e,t)=>{const{loop:n=!1,trapped:l=!1,onMountAutoFocus:i,onUnmountAutoFocus:a,...c}=e,[s,u]=(0,o.useState)(null),d=E(i),v=E(a),p=(0,o.useRef)(null),m=f(t,e=>u(e)),h=(0,o.useRef)({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;(0,o.useEffect)(()=>{if(l){function e(e){if(h.paused||!s)return;const t=e.target;s.contains(t)?p.current=t:B(p.current,{select:!0})}function t(e){if(h.paused||!s)return;const t=e.relatedTarget;null!==t&&(s.contains(t)||B(p.current,{select:!0}))}function n(e){const t=document.activeElement;for(const n of e)n.removedNodes.length>0&&(null!==s&&void 0!==s&&s.contains(t)||B(s))}document.addEventListener("focusin",e),document.addEventListener("focusout",t);const r=new MutationObserver(n);return s&&r.observe(s,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[l,s,h.paused]),(0,o.useEffect)(()=>{if(s){H.add(h);const t=document.activeElement;if(!s.contains(t)){const n=new CustomEvent(N,O);s.addEventListener(N,d),s.dispatchEvent(n),n.defaultPrevented||(!function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n=document.activeElement;for(const r of e)if(B(r,{select:t}),document.activeElement!==n)return}((e=M(s),e.filter(e=>"A"!==e.tagName)),{select:!0}),document.activeElement===t&&B(s))}return()=>{s.removeEventListener(N,d),setTimeout(()=>{const e=new CustomEvent(_,O);s.addEventListener(_,v),s.dispatchEvent(e),e.defaultPrevented||B(null!==t&&void 0!==t?t:document.body,{select:!0}),s.removeEventListener(_,v),H.remove(h)},0)}}var e},[s,d,v,h]);const g=(0,o.useCallback)(e=>{if(!n&&!l)return;if(h.paused)return;const t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){const t=e.currentTarget,[o,l]=function(e){const t=M(e),n=W(t,e),r=W(t.reverse(),e);return[n,r]}(t);o&&l?e.shiftKey||r!==l?e.shiftKey&&r===o&&(e.preventDefault(),n&&B(l,{select:!0})):(e.preventDefault(),n&&B(o,{select:!0})):r===t&&e.preventDefault()}},[n,l,h.paused]);return(0,o.createElement)(y.div,(0,r.A)({tabIndex:-1},c,{ref:m,onKeyDown:g}))});function M(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{const t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function W(e,t){for(const n of e)if(!F(n,{upTo:t}))return n}function F(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e;){if(void 0!==n&&e===n)return!1;if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}function B(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&function(e){return e instanceof HTMLInputElement&&"select"in e}(e)&&t&&e.select()}}const H=function(){let e=[];return{add(t){const n=e[0];t!==n&&(null===n||void 0===n||n.pause()),e=V(e,t),e.unshift(t)},remove(t){var n;e=V(e,t),null===(n=e[0])||void 0===n||n.resume()}}}();function V(e,t){const n=[...e],r=n.indexOf(t);return-1!==r&&n.splice(r,1),n}const K=Boolean(null===globalThis||void 0===globalThis?void 0:globalThis.document)?o.useLayoutEffect:()=>{},$=l["useId".toString()]||(()=>{});let z=0;function U(e){const[t,n]=o.useState($());return K(()=>{e||n(e=>null!==e&&void 0!==e?e:String(z++))},[e]),e||(t?`radix-${t}`:"")}var j=n(6178),q=n(6667);const Y="Popper",[X,Z]=s(Y),[J,G]=X(Y),Q=e=>{const{__scopePopper:t,children:n}=e,[r,l]=(0,o.useState)(null);return(0,o.createElement)(J,{scope:t,anchor:r,onAnchorChange:l},n)},ee="PopperAnchor",te=(0,o.forwardRef)((e,t)=>{const{__scopePopper:n,virtualRef:l,...i}=e,a=G(ee,n),c=(0,o.useRef)(null),s=f(t,c);return(0,o.useEffect)(()=>{a.onAnchorChange((null===l||void 0===l?void 0:l.current)||c.current)}),l?null:(0,o.createElement)(y.div,(0,r.A)({},i,{ref:s}))}),ne="PopperContent",[re,oe]=X(ne),le=(0,o.forwardRef)((e,t)=>{var n,l,i,a,c,s,u,d;const{__scopePopper:v,side:p="bottom",sideOffset:m=0,align:h="center",alignOffset:g=0,arrowPadding:w=0,collisionBoundary:b=[],collisionPadding:C=0,sticky:x="partial",hideWhenDetached:S=!1,avoidCollisions:R=!0,onPlaced:P,...A}=e,T=G(ne,v),[k,L]=(0,o.useState)(null),D=f(t,e=>L(e)),[N,_]=(0,o.useState)(null),O=function(e){const[t,n]=(0,o.useState)(void 0);return K(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const t=new ResizeObserver(t=>{if(!Array.isArray(t))return;if(!t.length)return;const r=t[0];let o,l;if("borderBoxSize"in r){const e=r.borderBoxSize,t=Array.isArray(e)?e[0]:e;o=t.inlineSize,l=t.blockSize}else o=e.offsetWidth,l=e.offsetHeight;n({width:o,height:l})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(N),I=null!==(n=null===O||void 0===O?void 0:O.width)&&void 0!==n?n:0,M=null!==(l=null===O||void 0===O?void 0:O.height)&&void 0!==l?l:0,W=p+("center"!==h?"-"+h:""),F="number"===typeof C?C:{top:0,right:0,bottom:0,left:0,...C},B=Array.isArray(b)?b:[b],H=B.length>0,V={padding:F,boundary:B.filter(ie),altBoundary:H},{refs:$,floatingStyles:z,placement:U,isPositioned:Y,middlewareData:X}=(0,j.we)({strategy:"fixed",placement:W,whileElementsMounted:q.ll,elements:{reference:T.anchor},middleware:[(0,j.cY)({mainAxis:m+M,alignmentAxis:g}),R&&(0,j.BN)({mainAxis:!0,crossAxis:!1,limiter:"partial"===x?(0,j.ER)():void 0,...V}),R&&(0,j.UU)({...V}),(0,j.Ej)({...V,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e;const{width:l,height:i}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width",`${r}px`),a.setProperty("--radix-popper-available-height",`${o}px`),a.setProperty("--radix-popper-anchor-width",`${l}px`),a.setProperty("--radix-popper-anchor-height",`${i}px`)}}),N&&(0,j.UE)({element:N,padding:w}),ae({arrowWidth:I,arrowHeight:M}),S&&(0,j.jD)({strategy:"referenceHidden"})]}),[Z,J]=ce(U),Q=E(P);K(()=>{Y&&(null===Q||void 0===Q||Q())},[Y,Q]);const ee=null===(i=X.arrow)||void 0===i?void 0:i.x,te=null===(a=X.arrow)||void 0===a?void 0:a.y,oe=0!==(null===(c=X.arrow)||void 0===c?void 0:c.centerOffset),[le,se]=(0,o.useState)();return K(()=>{k&&se(window.getComputedStyle(k).zIndex)},[k]),(0,o.createElement)("div",{ref:$.setFloating,"data-radix-popper-content-wrapper":"",style:{...z,transform:Y?z.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:le,"--radix-popper-transform-origin":[null===(s=X.transformOrigin)||void 0===s?void 0:s.x,null===(u=X.transformOrigin)||void 0===u?void 0:u.y].join(" ")},dir:e.dir},(0,o.createElement)(re,{scope:v,placedSide:Z,onArrowChange:_,arrowX:ee,arrowY:te,shouldHideArrow:oe},(0,o.createElement)(y.div,(0,r.A)({"data-side":Z,"data-align":J},A,{ref:D,style:{...A.style,animation:Y?void 0:"none",opacity:null!==(d=X.hide)&&void 0!==d&&d.referenceHidden?0:void 0}}))))});function ie(e){return null!==e}const ae=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,l,i;const{placement:a,rects:c,middlewareData:s}=t,u=0!==(null===(n=s.arrow)||void 0===n?void 0:n.centerOffset),d=u?0:e.arrowWidth,f=u?0:e.arrowHeight,[v,p]=ce(a),m={start:"0%",center:"50%",end:"100%"}[p],h=(null!==(r=null===(o=s.arrow)||void 0===o?void 0:o.x)&&void 0!==r?r:0)+d/2,g=(null!==(l=null===(i=s.arrow)||void 0===i?void 0:i.y)&&void 0!==l?l:0)+f/2;let w="",y="";return"bottom"===v?(w=u?m:`${h}px`,y=-f+"px"):"top"===v?(w=u?m:`${h}px`,y=`${c.floating.height+f}px`):"right"===v?(w=-f+"px",y=u?m:`${g}px`):"left"===v&&(w=`${c.floating.width+f}px`,y=u?m:`${g}px`),{data:{x:w,y:y}}}});function ce(e){const[t,n="center"]=e.split("-");return[t,n]}const se=Q,ue=te,de=le,fe=(0,o.forwardRef)((e,t)=>{var n;const{container:l=(null===globalThis||void 0===globalThis||null===(n=globalThis.document)||void 0===n?void 0:n.body),...a}=e;return l?i.createPortal((0,o.createElement)(y.div,(0,r.A)({},a,{ref:t})),l):null});function ve(e){let{prop:t,defaultProp:n,onChange:r=()=>{}}=e;const[l,i]=function(e){let{defaultProp:t,onChange:n}=e;const r=(0,o.useState)(t),[l]=r,i=(0,o.useRef)(l),a=E(n);return(0,o.useEffect)(()=>{i.current!==l&&(a(l),i.current=l)},[l,i,a]),r}({defaultProp:n,onChange:r}),a=void 0!==t,c=a?t:l,s=E(r);return[c,(0,o.useCallback)(e=>{if(a){const n="function"===typeof e?e(t):e;n!==t&&s(n)}else i(e)},[a,t,i,s])]}function pe(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return e=>t.forEach(t=>function(e,t){"function"===typeof e?e(t):null!==e&&void 0!==e&&(e.current=t)}(t,e))}const me=(0,o.forwardRef)((e,t)=>{const{children:n,...l}=e,i=o.Children.toArray(n),a=i.find(we);if(a){const e=a.props.children,n=i.map(t=>t===a?o.Children.count(e)>1?o.Children.only(null):(0,o.isValidElement)(e)?e.props.children:null:t);return(0,o.createElement)(he,(0,r.A)({},l,{ref:t}),(0,o.isValidElement)(e)?(0,o.cloneElement)(e,void 0,n):null)}return(0,o.createElement)(he,(0,r.A)({},l,{ref:t}),n)});me.displayName="Slot";const he=(0,o.forwardRef)((e,t)=>{const{children:n,...r}=e;return(0,o.isValidElement)(n)?(0,o.cloneElement)(n,{...ye(r,n.props),ref:t?pe(t,n.ref):n.ref}):o.Children.count(n)>1?o.Children.only(null):null});he.displayName="SlotClone";const ge=e=>{let{children:t}=e;return(0,o.createElement)(o.Fragment,null,t)};function we(e){return(0,o.isValidElement)(e)&&e.type===ge}function ye(e,t){const n={...t};for(const r in t){const o=e[r],l=t[r];/^on[A-Z]/.test(r)?o&&l?n[r]=function(){l(...arguments),o(...arguments)}:o&&(n[r]=o):"style"===r?n[r]={...o,...l}:"className"===r&&(n[r]=[o,l].filter(Boolean).join(" "))}return{...e,...n}}const Ee=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{const n=(0,o.forwardRef)((e,n)=>{const{asChild:l,...i}=e,a=l?me:t;return(0,o.useEffect)(()=>{window[Symbol.for("radix-ui")]=!0},[]),(0,o.createElement)(a,(0,r.A)({},i,{ref:n}))});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});const be=(0,o.forwardRef)((e,t)=>(0,o.createElement)(Ee.span,(0,r.A)({},e,{ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}})));var Ce=n(5754),xe=n(6326),Se=n(3259),Re=n(6934),Pe=(0,n(1872).f)(),Ae=function(){},Te=o.forwardRef(function(e,t){var n=o.useRef(null),r=o.useState({onScrollCapture:Ae,onWheelCapture:Ae,onTouchMoveCapture:Ae}),l=r[0],i=r[1],a=e.forwardProps,c=e.children,s=e.className,u=e.removeScrollBar,d=e.enabled,f=e.shards,v=e.sideCar,p=e.noIsolation,m=e.inert,h=e.allowPinchZoom,g=e.as,w=void 0===g?"div":g,y=(0,xe.Tt)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as"]),E=v,b=(0,Re.S)([n,t]),C=(0,xe.Cl)((0,xe.Cl)({},y),l);return o.createElement(o.Fragment,null,d&&o.createElement(E,{sideCar:Pe,removeScrollBar:u,shards:f,noIsolation:p,inert:m,setCallbacks:i,allowPinchZoom:!!h,lockRef:n}),a?o.cloneElement(o.Children.only(c),(0,xe.Cl)((0,xe.Cl)({},C),{ref:b})):o.createElement(w,(0,xe.Cl)({},C,{className:s,ref:b}),c))});Te.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},Te.classNames={fullWidth:Se.pN,zeroRight:Se.Mi};var ke=n(4560),Le=n(5857),De=n(7513),Ne=!1;if("undefined"!==typeof window)try{var _e=Object.defineProperty({},"passive",{get:function(){return Ne=!0,!0}});window.addEventListener("test",_e,_e),window.removeEventListener("test",_e,_e)}catch(rn){Ne=!1}var Oe=!!Ne&&{passive:!1},Ie=function(e,t){var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&!function(e){return"TEXTAREA"===e.tagName}(e)&&"visible"===n[t])},Me=function(e,t){var n=t;do{if("undefined"!==typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),We(e,n)){var r=Fe(e,n);if(r[1]>r[2])return!0}n=n.parentNode}while(n&&n!==document.body);return!1},We=function(e,t){return"v"===e?function(e){return Ie(e,"overflowY")}(t):function(e){return Ie(e,"overflowX")}(t)},Fe=function(e,t){return"v"===e?[(n=t).scrollTop,n.scrollHeight,n.clientHeight]:function(e){return[e.scrollLeft,e.scrollWidth,e.clientWidth]}(t);var n},Be=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},He=function(e){return[e.deltaX,e.deltaY]},Ve=function(e){return e&&"current"in e?e.current:e},Ke=function(e){return"\n  .block-interactivity-".concat(e," {pointer-events: none;}\n  .allow-interactivity-").concat(e," {pointer-events: all;}\n")},$e=0,ze=[];const Ue=(0,ke.m)(Pe,function(e){var t=o.useRef([]),n=o.useRef([0,0]),r=o.useRef(),l=o.useState($e++)[0],i=o.useState(function(){return(0,De.T0)()})[0],a=o.useRef(e);o.useEffect(function(){a.current=e},[e]),o.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(l));var t=(0,xe.fX)([e.lockRef.current],(e.shards||[]).map(Ve),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(l))}),function(){document.body.classList.remove("block-interactivity-".concat(l)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(l))})}}},[e.inert,e.lockRef.current,e.shards]);var c=o.useCallback(function(e,t){if("touches"in e&&2===e.touches.length)return!a.current.allowPinchZoom;var o,l=Be(e),i=n.current,c="deltaX"in e?e.deltaX:i[0]-l[0],s="deltaY"in e?e.deltaY:i[1]-l[1],u=e.target,d=Math.abs(c)>Math.abs(s)?"h":"v";if("touches"in e&&"h"===d&&"range"===u.type)return!1;var f=Me(d,u);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=Me(d,u)),!f)return!1;if(!r.current&&"changedTouches"in e&&(c||s)&&(r.current=o),!o)return!0;var v=r.current||o;return function(e,t,n,r,o){var l=function(e,t){return"h"===e&&"rtl"===t?-1:1}(e,window.getComputedStyle(t).direction),i=l*r,a=n.target,c=t.contains(a),s=!1,u=i>0,d=0,f=0;do{var v=Fe(e,a),p=v[0],m=v[1]-v[2]-l*p;(p||m)&&We(e,a)&&(d+=m,f+=p),a=a.parentNode}while(!c&&a!==document.body||c&&(t.contains(a)||t===a));return(u&&(o&&0===d||!o&&i>d)||!u&&(o&&0===f||!o&&-i>f))&&(s=!0),s}(v,t,e,"h"===v?c:s,!0)},[]),s=o.useCallback(function(e){var n=e;if(ze.length&&ze[ze.length-1]===i){var r="deltaY"in n?He(n):Be(n),o=t.current.filter(function(e){return e.name===n.type&&e.target===n.target&&(t=e.delta,o=r,t[0]===o[0]&&t[1]===o[1]);var t,o})[0];if(o&&o.should)n.cancelable&&n.preventDefault();else if(!o){var l=(a.current.shards||[]).map(Ve).filter(Boolean).filter(function(e){return e.contains(n.target)});(l.length>0?c(n,l[0]):!a.current.noIsolation)&&n.cancelable&&n.preventDefault()}}},[]),u=o.useCallback(function(e,n,r,o){var l={name:e,delta:n,target:r,should:o};t.current.push(l),setTimeout(function(){t.current=t.current.filter(function(e){return e!==l})},1)},[]),d=o.useCallback(function(e){n.current=Be(e),r.current=void 0},[]),f=o.useCallback(function(t){u(t.type,He(t),t.target,c(t,e.lockRef.current))},[]),v=o.useCallback(function(t){u(t.type,Be(t),t.target,c(t,e.lockRef.current))},[]);o.useEffect(function(){return ze.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:v}),document.addEventListener("wheel",s,Oe),document.addEventListener("touchmove",s,Oe),document.addEventListener("touchstart",d,Oe),function(){ze=ze.filter(function(e){return e!==i}),document.removeEventListener("wheel",s,Oe),document.removeEventListener("touchmove",s,Oe),document.removeEventListener("touchstart",d,Oe)}},[]);var p=e.removeScrollBar,m=e.inert;return o.createElement(o.Fragment,null,m?o.createElement(i,{styles:Ke(l)}):null,p?o.createElement(Le.jp,{gapMode:"margin"}):null)});var je=o.forwardRef(function(e,t){return o.createElement(Te,(0,xe.Cl)({},e,{ref:t,sideCar:Ue}))});je.classNames=Te.classNames;const qe=je,Ye=[" ","Enter","ArrowUp","ArrowDown"],Xe=[" ","Enter"],Ze="Select",[Je,Ge,Qe]=function(e){const t=e+"CollectionProvider",[n,r]=s(t),[l,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),a=e=>{const{scope:t,children:n}=e,r=o.useRef(null),i=o.useRef(new Map).current;return o.createElement(l,{scope:t,itemMap:i,collectionRef:r},n)},c=e+"CollectionSlot",u=o.forwardRef((e,t)=>{const{scope:n,children:r}=e,l=f(t,i(c,n).collectionRef);return o.createElement(v,{ref:l},r)}),d=e+"CollectionItemSlot",p="data-radix-collection-item",m=o.forwardRef((e,t)=>{const{scope:n,children:r,...l}=e,a=o.useRef(null),c=f(t,a),s=i(d,n);return o.useEffect(()=>(s.itemMap.set(a,{ref:a,...l}),()=>{s.itemMap.delete(a)})),o.createElement(v,{[p]:"",ref:c},r)});return[{Provider:a,Slot:u,ItemSlot:m},function(t){const n=i(e+"CollectionConsumer",t);return o.useCallback(()=>{const e=n.collectionRef.current;if(!e)return[];const t=Array.from(e.querySelectorAll(`[${p}]`));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}(Ze),[et,tt]=s(Ze,[Qe,Z]),nt=Z(),[rt,ot]=et(Ze),[lt,it]=et(Ze),at=e=>{const{__scopeSelect:t,children:n,open:r,defaultOpen:l,onOpenChange:i,value:a,defaultValue:c,onValueChange:s,dir:u,name:d,autoComplete:f,disabled:v,required:p}=e,m=nt(t),[h,g]=(0,o.useState)(null),[y,E]=(0,o.useState)(null),[b,C]=(0,o.useState)(!1),x=function(e){const t=(0,o.useContext)(w);return e||t||"ltr"}(u),[S=!1,R]=ve({prop:r,defaultProp:l,onChange:i}),[P,A]=ve({prop:a,defaultProp:c,onChange:s}),T=(0,o.useRef)(null),k=!h||Boolean(h.closest("form")),[L,D]=(0,o.useState)(new Set),N=Array.from(L).map(e=>e.props.value).join(";");return(0,o.createElement)(se,m,(0,o.createElement)(rt,{required:p,scope:t,trigger:h,onTriggerChange:g,valueNode:y,onValueNodeChange:E,valueNodeHasChildren:b,onValueNodeHasChildrenChange:C,contentId:U(),value:P,onValueChange:A,open:S,onOpenChange:R,dir:x,triggerPointerDownPosRef:T,disabled:v},(0,o.createElement)(Je.Provider,{scope:t},(0,o.createElement)(lt,{scope:e.__scopeSelect,onNativeOptionAdd:(0,o.useCallback)(e=>{D(t=>new Set(t).add(e))},[]),onNativeOptionRemove:(0,o.useCallback)(e=>{D(t=>{const n=new Set(t);return n.delete(e),n})},[])},n)),k?(0,o.createElement)(Vt,{key:N,"aria-hidden":!0,required:p,tabIndex:-1,name:d,autoComplete:f,value:P,onChange:e=>A(e.target.value),disabled:v},void 0===P?(0,o.createElement)("option",{value:""}):null,Array.from(L)):null))},ct="SelectTrigger",st=(0,o.forwardRef)((e,t)=>{const{__scopeSelect:n,disabled:l=!1,...i}=e,a=nt(n),s=ot(ct,n),u=s.disabled||l,d=f(t,s.onTriggerChange),v=Ge(n),[p,m,h]=Kt(e=>{const t=v().filter(e=>!e.disabled),n=t.find(e=>e.value===s.value),r=$t(t,e,n);void 0!==r&&s.onValueChange(r.value)}),g=()=>{u||(s.onOpenChange(!0),h())};return(0,o.createElement)(ue,(0,r.A)({asChild:!0},a),(0,o.createElement)(y.button,(0,r.A)({type:"button",role:"combobox","aria-controls":s.contentId,"aria-expanded":s.open,"aria-required":s.required,"aria-autocomplete":"none",dir:s.dir,"data-state":s.open?"open":"closed",disabled:u,"data-disabled":u?"":void 0,"data-placeholder":void 0===s.value?"":void 0},i,{ref:d,onClick:c(i.onClick,e=>{e.currentTarget.focus()}),onPointerDown:c(i.onPointerDown,e=>{const t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&(g(),s.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)},e.preventDefault())}),onKeyDown:c(i.onKeyDown,e=>{const t=""!==p.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),t&&" "===e.key||Ye.includes(e.key)&&(g(),e.preventDefault())})})))}),ut="SelectValue",dt=(0,o.forwardRef)((e,t)=>{const{__scopeSelect:n,className:l,style:i,children:a,placeholder:c,...s}=e,u=ot(ut,n),{onValueNodeHasChildrenChange:d}=u,v=void 0!==a,p=f(t,u.onValueNodeChange);return K(()=>{d(v)},[d,v]),(0,o.createElement)(y.span,(0,r.A)({},s,{ref:p,style:{pointerEvents:"none"}}),void 0===u.value&&void 0!==c?c:a)}),ft=(0,o.forwardRef)((e,t)=>{const{__scopeSelect:n,children:l,...i}=e;return(0,o.createElement)(y.span,(0,r.A)({"aria-hidden":!0},i,{ref:t}),l||"\u25bc")}),vt=e=>(0,o.createElement)(fe,(0,r.A)({asChild:!0},e)),pt="SelectContent",mt=(0,o.forwardRef)((e,t)=>{const n=ot(pt,e.__scopeSelect),[l,a]=(0,o.useState)();if(K(()=>{a(new DocumentFragment)},[]),!n.open){const t=l;return t?(0,i.createPortal)((0,o.createElement)(gt,{scope:e.__scopeSelect},(0,o.createElement)(Je.Slot,{scope:e.__scopeSelect},(0,o.createElement)("div",null,e.children))),t):null}return(0,o.createElement)(yt,(0,r.A)({},e,{ref:t}))}),ht=10,[gt,wt]=et(pt),yt=(0,o.forwardRef)((e,t)=>{const{__scopeSelect:n,position:l="item-aligned",onCloseAutoFocus:i,onEscapeKeyDown:a,onPointerDownOutside:s,side:u,sideOffset:d,align:p,alignOffset:m,arrowPadding:h,collisionBoundary:g,collisionPadding:w,sticky:y,hideWhenDetached:E,avoidCollisions:b,...C}=e,x=ot(pt,n),[S,R]=(0,o.useState)(null),[A,T]=(0,o.useState)(null),k=f(t,e=>R(e)),[D,N]=(0,o.useState)(null),[_,O]=(0,o.useState)(null),M=Ge(n),[W,F]=(0,o.useState)(!1),B=(0,o.useRef)(!1);(0,o.useEffect)(()=>{if(S)return(0,Ce.Eq)(S)},[S]),L();const H=(0,o.useCallback)(e=>{const[t,...n]=M().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(const l of e){if(l===o)return;if(null===l||void 0===l||l.scrollIntoView({block:"nearest"}),l===t&&A&&(A.scrollTop=0),l===r&&A&&(A.scrollTop=A.scrollHeight),null===l||void 0===l||l.focus(),document.activeElement!==o)return}},[M,A]),V=(0,o.useCallback)(()=>H([D,S]),[H,D,S]);(0,o.useEffect)(()=>{W&&V()},[W,V]);const{onOpenChange:K,triggerPointerDownPosRef:$}=x;(0,o.useEffect)(()=>{if(S){let e={x:0,y:0};const t=t=>{var n,r,o,l;e={x:Math.abs(Math.round(t.pageX)-(null!==(n=null===(r=$.current)||void 0===r?void 0:r.x)&&void 0!==n?n:0)),y:Math.abs(Math.round(t.pageY)-(null!==(o=null===(l=$.current)||void 0===l?void 0:l.y)&&void 0!==o?o:0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():S.contains(n.target)||K(!1),document.removeEventListener("pointermove",t),$.current=null};return null!==$.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[S,K,$]),(0,o.useEffect)(()=>{const e=()=>K(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[K]);const[z,U]=Kt(e=>{const t=M().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=$t(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),j=(0,o.useCallback)((e,t,n)=>{const r=!B.current&&!n;(void 0!==x.value&&x.value===t||r)&&(N(e),r&&(B.current=!0))},[x.value]),q=(0,o.useCallback)(()=>null===S||void 0===S?void 0:S.focus(),[S]),Y=(0,o.useCallback)((e,t,n)=>{const r=!B.current&&!n;(void 0!==x.value&&x.value===t||r)&&O(e)},[x.value]),X="popper"===l?bt:Et,Z=X===bt?{side:u,sideOffset:d,align:p,alignOffset:m,arrowPadding:h,collisionBoundary:g,collisionPadding:w,sticky:y,hideWhenDetached:E,avoidCollisions:b}:{};return(0,o.createElement)(gt,{scope:n,content:S,viewport:A,onViewportChange:T,itemRefCallback:j,selectedItem:D,onItemLeave:q,itemTextRefCallback:Y,focusSelectedItem:V,selectedItemText:_,position:l,isPositioned:W,searchRef:z},(0,o.createElement)(qe,{as:v,allowPinchZoom:!0},(0,o.createElement)(I,{asChild:!0,trapped:x.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:c(i,e=>{var t;null===(t=x.trigger)||void 0===t||t.focus({preventScroll:!0}),e.preventDefault()})},(0,o.createElement)(P,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>x.onOpenChange(!1)},(0,o.createElement)(X,(0,r.A)({role:"listbox",id:x.contentId,"data-state":x.open?"open":"closed",dir:x.dir,onContextMenu:e=>e.preventDefault()},C,Z,{onPlaced:()=>F(!0),ref:k,style:{display:"flex",flexDirection:"column",outline:"none",...C.style},onKeyDown:c(C.onKeyDown,e=>{const t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||U(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=M().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){const n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>H(t)),e.preventDefault()}})}))))))}),Et=(0,o.forwardRef)((e,t)=>{const{__scopeSelect:n,onPlaced:l,...i}=e,c=ot(pt,n),s=wt(pt,n),[u,d]=(0,o.useState)(null),[v,p]=(0,o.useState)(null),m=f(t,e=>p(e)),h=Ge(n),g=(0,o.useRef)(!1),w=(0,o.useRef)(!0),{viewport:E,selectedItem:b,selectedItemText:C,focusSelectedItem:x}=s,S=(0,o.useCallback)(()=>{if(c.trigger&&c.valueNode&&u&&v&&E&&b&&C){const e=c.trigger.getBoundingClientRect(),t=v.getBoundingClientRect(),n=c.valueNode.getBoundingClientRect(),r=C.getBoundingClientRect();if("rtl"!==c.dir){const o=r.left-t.left,l=n.left-o,i=e.left-l,c=e.width+i,s=Math.max(c,t.width),d=window.innerWidth-ht,f=a(l,[ht,d-s]);u.style.minWidth=c+"px",u.style.left=f+"px"}else{const o=t.right-r.right,l=window.innerWidth-n.right-o,i=window.innerWidth-e.right-l,c=e.width+i,s=Math.max(c,t.width),d=window.innerWidth-ht,f=a(l,[ht,d-s]);u.style.minWidth=c+"px",u.style.right=f+"px"}const o=h(),i=window.innerHeight-2*ht,s=E.scrollHeight,d=window.getComputedStyle(v),f=parseInt(d.borderTopWidth,10),p=parseInt(d.paddingTop,10),m=parseInt(d.borderBottomWidth,10),w=f+p+s+parseInt(d.paddingBottom,10)+m,y=Math.min(5*b.offsetHeight,w),x=window.getComputedStyle(E),S=parseInt(x.paddingTop,10),R=parseInt(x.paddingBottom,10),P=e.top+e.height/2-ht,A=i-P,T=b.offsetHeight/2,k=f+p+(b.offsetTop+T),L=w-k;if(k<=P){const e=b===o[o.length-1].ref.current;u.style.bottom="0px";const t=v.clientHeight-E.offsetTop-E.offsetHeight,n=k+Math.max(A,T+(e?R:0)+t+m);u.style.height=n+"px"}else{const e=b===o[0].ref.current;u.style.top="0px";const t=Math.max(P,f+E.offsetTop+(e?S:0)+T)+L;u.style.height=t+"px",E.scrollTop=k-P+E.offsetTop}u.style.margin=`${ht}px 0`,u.style.minHeight=y+"px",u.style.maxHeight=i+"px",null===l||void 0===l||l(),requestAnimationFrame(()=>g.current=!0)}},[h,c.trigger,c.valueNode,u,v,E,b,C,c.dir,l]);K(()=>S(),[S]);const[R,P]=(0,o.useState)();K(()=>{v&&P(window.getComputedStyle(v).zIndex)},[v]);const A=(0,o.useCallback)(e=>{e&&!0===w.current&&(S(),null===x||void 0===x||x(),w.current=!1)},[S,x]);return(0,o.createElement)(Ct,{scope:n,contentWrapper:u,shouldExpandOnScrollRef:g,onScrollButtonChange:A},(0,o.createElement)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:R}},(0,o.createElement)(y.div,(0,r.A)({},i,{ref:m,style:{boxSizing:"border-box",maxHeight:"100%",...i.style}}))))}),bt=(0,o.forwardRef)((e,t)=>{const{__scopeSelect:n,align:l="start",collisionPadding:i=ht,...a}=e,c=nt(n);return(0,o.createElement)(de,(0,r.A)({},c,a,{ref:t,align:l,collisionPadding:i,style:{boxSizing:"border-box",...a.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}}))}),[Ct,xt]=et(pt,{}),St="SelectViewport",Rt=(0,o.forwardRef)((e,t)=>{const{__scopeSelect:n,...l}=e,i=wt(St,n),a=xt(St,n),s=f(t,i.onViewportChange),u=(0,o.useRef)(0);return(0,o.createElement)(o.Fragment,null,(0,o.createElement)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"}}),(0,o.createElement)(Je.Slot,{scope:n},(0,o.createElement)(y.div,(0,r.A)({"data-radix-select-viewport":"",role:"presentation"},l,{ref:s,style:{position:"relative",flex:1,overflow:"auto",...l.style},onScroll:c(l.onScroll,e=>{const t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=a;if(null!==r&&void 0!==r&&r.current&&n){const e=Math.abs(u.current-t.scrollTop);if(e>0){const r=window.innerHeight-2*ht,o=parseFloat(n.style.minHeight),l=parseFloat(n.style.height),i=Math.max(o,l);if(i<r){const o=i+e,l=Math.min(r,o),a=o-l;n.style.height=l+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}u.current=t.scrollTop})}))))}),Pt="SelectGroup",[At,Tt]=et(Pt),kt=(0,o.forwardRef)((e,t)=>{const{__scopeSelect:n,...l}=e,i=U();return(0,o.createElement)(At,{scope:n,id:i},(0,o.createElement)(y.div,(0,r.A)({role:"group","aria-labelledby":i},l,{ref:t})))}),Lt="SelectLabel",Dt=(0,o.forwardRef)((e,t)=>{const{__scopeSelect:n,...l}=e,i=Tt(Lt,n);return(0,o.createElement)(y.div,(0,r.A)({id:i.id},l,{ref:t}))}),Nt="SelectItem",[_t,Ot]=et(Nt),It=(0,o.forwardRef)((e,t)=>{const{__scopeSelect:n,value:l,disabled:i=!1,textValue:a,...s}=e,u=ot(Nt,n),d=wt(Nt,n),v=u.value===l,[p,m]=(0,o.useState)(null!==a&&void 0!==a?a:""),[h,g]=(0,o.useState)(!1),w=f(t,e=>{var t;return null===(t=d.itemRefCallback)||void 0===t?void 0:t.call(d,e,l,i)}),E=U(),b=()=>{i||(u.onValueChange(l),u.onOpenChange(!1))};return(0,o.createElement)(_t,{scope:n,value:l,disabled:i,textId:E,isSelected:v,onItemTextChange:(0,o.useCallback)(e=>{m(t=>{var n;return t||(null!==(n=null===e||void 0===e?void 0:e.textContent)&&void 0!==n?n:"").trim()})},[])},(0,o.createElement)(Je.ItemSlot,{scope:n,value:l,disabled:i,textValue:p},(0,o.createElement)(y.div,(0,r.A)({role:"option","aria-labelledby":E,"data-highlighted":h?"":void 0,"aria-selected":v&&h,"data-state":v?"checked":"unchecked","aria-disabled":i||void 0,"data-disabled":i?"":void 0,tabIndex:i?void 0:-1},s,{ref:w,onFocus:c(s.onFocus,()=>g(!0)),onBlur:c(s.onBlur,()=>g(!1)),onPointerUp:c(s.onPointerUp,b),onPointerMove:c(s.onPointerMove,e=>{var t;i?null===(t=d.onItemLeave)||void 0===t||t.call(d):e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:c(s.onPointerLeave,e=>{var t;e.currentTarget===document.activeElement&&(null===(t=d.onItemLeave)||void 0===t||t.call(d))}),onKeyDown:c(s.onKeyDown,e=>{var t;""!==(null===(t=d.searchRef)||void 0===t?void 0:t.current)&&" "===e.key||(Xe.includes(e.key)&&b()," "===e.key&&e.preventDefault())})}))))}),Mt="SelectItemText",Wt=(0,o.forwardRef)((e,t)=>{const{__scopeSelect:n,className:l,style:a,...c}=e,s=ot(Mt,n),u=wt(Mt,n),d=Ot(Mt,n),v=it(Mt,n),[p,m]=(0,o.useState)(null),h=f(t,e=>m(e),d.onItemTextChange,e=>{var t;return null===(t=u.itemTextRefCallback)||void 0===t?void 0:t.call(u,e,d.value,d.disabled)}),g=null===p||void 0===p?void 0:p.textContent,w=(0,o.useMemo)(()=>(0,o.createElement)("option",{key:d.value,value:d.value,disabled:d.disabled},g),[d.disabled,d.value,g]),{onNativeOptionAdd:E,onNativeOptionRemove:b}=v;return K(()=>(E(w),()=>b(w)),[E,b,w]),(0,o.createElement)(o.Fragment,null,(0,o.createElement)(y.span,(0,r.A)({id:d.textId},c,{ref:h})),d.isSelected&&s.valueNode&&!s.valueNodeHasChildren?(0,i.createPortal)(c.children,s.valueNode):null)}),Ft="SelectItemIndicator",Bt=(0,o.forwardRef)((e,t)=>{const{__scopeSelect:n,...l}=e;return Ot(Ft,n).isSelected?(0,o.createElement)(y.span,(0,r.A)({"aria-hidden":!0},l,{ref:t})):null}),Ht=(0,o.forwardRef)((e,t)=>{const{__scopeSelect:n,...l}=e;return(0,o.createElement)(y.div,(0,r.A)({"aria-hidden":!0},l,{ref:t}))}),Vt=(0,o.forwardRef)((e,t)=>{const{value:n,...l}=e,i=(0,o.useRef)(null),a=f(t,i),c=function(e){const t=(0,o.useRef)({value:e,previous:e});return(0,o.useMemo)(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(n);return(0,o.useEffect)(()=>{const e=i.current,t=window.HTMLSelectElement.prototype,r=Object.getOwnPropertyDescriptor(t,"value").set;if(c!==n&&r){const t=new Event("change",{bubbles:!0});r.call(e,n),e.dispatchEvent(t)}},[c,n]),(0,o.createElement)(be,{asChild:!0},(0,o.createElement)("select",(0,r.A)({},l,{ref:a,defaultValue:n})))});function Kt(e){const t=E(e),n=(0,o.useRef)(""),r=(0,o.useRef)(0),l=(0,o.useCallback)(e=>{const o=n.current+e;t(o),function e(t){n.current=t,window.clearTimeout(r.current),""!==t&&(r.current=window.setTimeout(()=>e(""),1e3))}(o)},[t]),i=(0,o.useCallback)(()=>{n.current="",window.clearTimeout(r.current)},[]);return(0,o.useEffect)(()=>()=>window.clearTimeout(r.current),[]),[n,l,i]}function $t(e,t,n){const r=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,o=n?e.indexOf(n):-1;let l=(i=e,a=Math.max(o,0),i.map((e,t)=>i[(a+t)%i.length]));var i,a;1===r.length&&(l=l.filter(e=>e!==n));const c=l.find(e=>e.textValue.toLowerCase().startsWith(r.toLowerCase()));return c!==n?c:void 0}Vt.displayName="BubbleSelect";const zt=at,Ut=st,jt=dt,qt=ft,Yt=vt,Xt=mt,Zt=Rt,Jt=kt,Gt=Dt,Qt=It,en=Wt,tn=Bt,nn=Ht}}]);
//# sourceMappingURL=825.7e4f6110.chunk.js.map