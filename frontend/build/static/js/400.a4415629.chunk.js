"use strict";(self.webpackChunkpayment_gateway_frontend=self.webpackChunkpayment_gateway_frontend||[]).push([[400],{126:(e,s,t)=>{t.d(s,{C5:()=>m,MJ:()=>c,eI:()=>l,lR:()=>d,lV:()=>i});var a=t(5043),r=t(7660),n=t(3009),o=t(579);const i=r.bL;a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,o.jsx)(r.D0,{ref:s,className:(0,n.cn)("space-y-2",t),...a})}).displayName="FormField";const l=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,o.jsx)("div",{ref:s,className:(0,n.cn)("space-y-2",t),...a})});l.displayName="FormItem";const d=a.forwardRef((e,s)=>{let{className:t,required:a,children:i,...l}=e;return(0,o.jsx)(r.JU,{ref:s,className:(0,n.cn)("block text-sm font-medium text-gray-700 dark:text-gray-300",a&&"after:content-['*'] after:ml-0.5 after:text-red-500",t),...l,children:i})});d.displayName="FormLabel";const c=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,o.jsx)(r.Ec,{ref:s,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),...a})});c.displayName="FormControl";a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,o.jsx)("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",t),...a})}).displayName="FormDescription";const m=a.forwardRef((e,s)=>{let{className:t,children:a,...i}=e;return(0,o.jsx)(r.QB,{ref:s,className:(0,n.cn)("text-sm font-medium text-destructive",t),...i,children:a})});m.displayName="FormMessage"},382:(e,s,t)=>{t.d(s,{A0:()=>i,BF:()=>l,Hj:()=>d,XI:()=>o,nA:()=>m,nd:()=>c});var a=t(5043),r=t(3009),n=t(579);const o=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,n.jsx)("div",{className:"w-full overflow-auto",children:(0,n.jsx)("table",{ref:s,className:(0,r.cn)("w-full caption-bottom text-sm",t),...a})})});o.displayName="Table";const i=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,n.jsx)("thead",{ref:s,className:(0,r.cn)("[&_tr]:border-b",t),...a})});i.displayName="TableHeader";const l=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,n.jsx)("tbody",{ref:s,className:(0,r.cn)("[&_tr:last-child]:border-0",t),...a})});l.displayName="TableBody";a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,n.jsx)("tfoot",{ref:s,className:(0,r.cn)("bg-primary font-medium text-primary-foreground",t),...a})}).displayName="TableFooter";const d=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,n.jsx)("tr",{ref:s,className:(0,r.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",t),...a})});d.displayName="TableRow";const c=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,n.jsx)("th",{ref:s,className:(0,r.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",t),...a})});c.displayName="TableHead";const m=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,n.jsx)("td",{ref:s,className:(0,r.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",t),...a})});m.displayName="TableCell";a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,n.jsx)("caption",{ref:s,className:(0,r.cn)("mt-4 text-sm text-muted-foreground",t),...a})}).displayName="TableCaption"},492:(e,s,t)=>{t.d(s,{Cf:()=>u,Es:()=>p,L3:()=>x,c7:()=>f,lG:()=>l,rr:()=>h,zM:()=>d});var a=t(5043),r=t(5179),n=t(1172),o=t(3009),i=t(579);const l=r.bL,d=r.l9,c=e=>{let{className:s,children:t,...a}=e;const{container:n,forceMount:l,...d}=a,c={container:n,forceMount:l};return(0,i.jsx)(r.ZL,{...c,children:(0,i.jsx)("div",{className:(0,o.cn)("fixed inset-0 z-50 flex items-start justify-center sm:items-center",s),children:t})})};c.displayName=r.ZL.displayName;const m=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,i.jsx)(r.hJ,{ref:s,className:(0,o.cn)("fixed inset-0 z-50 bg-black/50 backdrop-blur-sm transition-all duration-100 data-[state=closed]:animate-out data-[state=closed]:fade-out data-[state=open]:fade-in",t),...a})});m.displayName=r.hJ.displayName;const u=a.forwardRef((e,s)=>{let{className:t,children:a,...l}=e;return(0,i.jsxs)(c,{children:[(0,i.jsx)(m,{}),(0,i.jsxs)(r.UC,{ref:s,className:(0,o.cn)("fixed z-50 grid w-full gap-4 rounded-b-lg bg-white p-6 animate-in data-[state=open]:fade-in-90 data-[state=open]:slide-in-from-bottom-10 sm:max-w-lg sm:rounded-lg sm:zoom-in-90 data-[state=open]:sm:slide-in-from-bottom-0","dark:bg-gray-900",t),...l,children:[a,(0,i.jsxs)(r.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-gray-100 dark:focus:ring-gray-400 dark:focus:ring-offset-gray-900 dark:data-[state=open]:bg-gray-800",children:[(0,i.jsx)(n.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});u.displayName=r.UC.displayName;const f=e=>{let{className:s,...t}=e;return(0,i.jsx)("div",{className:(0,o.cn)("flex flex-col space-y-2 text-center sm:text-left",s),...t})};f.displayName="DialogHeader";const p=e=>{let{className:s,...t}=e;return(0,i.jsx)("div",{className:(0,o.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",s),...t})};p.displayName="DialogFooter";const x=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,i.jsx)(r.hE,{ref:s,className:(0,o.cn)("text-lg font-semibold text-gray-900","dark:text-gray-50",t),...a})});x.displayName=r.hE.displayName;const h=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,i.jsx)(r.VY,{ref:s,className:(0,o.cn)("text-sm text-gray-500","dark:text-gray-400",t),...a})});h.displayName=r.VY.displayName},2036:(e,s,t)=>{t.d(s,{k:()=>j});var a=t(5043),r=t(9772),n=t(9954),o=t(2248),i=t(6879),l=t(9781),d=t(2836),c=t(9066);const m=()=>{const{user:e}=(0,c.A)(),s=(0,l.jE)();return(0,d.n)({mutationFn:async()=>new Promise(e=>{setTimeout(()=>{e({qrCodeData:"data:image/svg+xml;base64,mock-qr-code-data",secret:"MOCK_SECRET_KEY"})},500)}),onSuccess:()=>{s.invalidateQueries({queryKey:["user","2fa"]})}})},u=()=>{const e=(0,l.jE)();return(0,d.n)({mutationFn:async e=>{let{code:s}=e;if("123456"!==s)throw new Error("Invalid verification code");return new Promise(e=>{setTimeout(()=>{e({recoveryCodes:["mock1","mock2","mock3","mock4","mock5"],success:!0})},500)})},onSuccess:()=>{e.invalidateQueries({queryKey:["user","2fa"]})}})},f=()=>{const e=(0,l.jE)(),{user:s}=(0,c.A)();return(0,d.n)({mutationFn:async e=>{let{code:s}=e;if("123456"!==s)throw new Error("Invalid verification code");return new Promise(e=>{setTimeout(()=>{e({success:!0,message:"Two-factor authentication has been disabled."})},500)})},onSuccess:()=>{e.invalidateQueries({queryKey:["user","2fa"]})}})};var p=t(7772),x=t(987),h=t(492),y=t(579);function j(e){let{twoFactorEnabled:s=!1}=e;const{toast:t}=(0,i.dj)(),[l,d]=(0,a.useState)(!1),[c,j]=(0,a.useState)(""),[N,g]=(0,a.useState)(""),[b,w]=(0,a.useState)([]),[v,k]=(0,a.useState)(!1),{mutate:A,isPending:C,isSuccess:S,data:E,isError:P,error:R}=m(),{mutate:T,isPending:F,isSuccess:I,data:D,isError:M,error:O}=u(),{mutate:_,isPending:z,isSuccess:L,isError:U,error:Y}=f();(0,a.useEffect)(()=>{S&&E&&(g(E.qrCodeData),d(!0))},[S,E]),(0,a.useEffect)(()=>{I&&D&&(w(D.recoveryCodes),k(!0),t({title:"Success",description:"Two-factor authentication has been enabled successfully."}))},[I,D,t]),(0,a.useEffect)(()=>{P&&R&&t({title:"Error",description:R.message||"Failed to set up two-factor authentication",variant:"destructive"}),M&&O&&t({title:"Error",description:O.message||"Failed to verify two-factor authentication code",variant:"destructive"}),U&&Y&&t({title:"Error",description:Y.message||"Failed to disable two-factor authentication",variant:"destructive"})},[P,R,M,O,U,Y,t]),(0,a.useEffect)(()=>{L&&(t({title:"Success",description:"Two-factor authentication has been disabled successfully."}),d(!1),j(""))},[L,t]);return(0,y.jsxs)("div",{className:"space-y-4",children:[(0,y.jsxs)("div",{className:"flex items-center justify-between",children:[(0,y.jsxs)("div",{children:[(0,y.jsx)("h3",{className:"text-base font-medium",children:"Two-Factor Authentication"}),(0,y.jsx)("p",{className:"text-sm text-muted-foreground",children:s?"Two-factor authentication is enabled.":"Add an extra layer of security to your account."})]}),s?(0,y.jsx)(r.$,{variant:"destructive",onClick:()=>d(!0),children:"Disable 2FA"}):(0,y.jsx)(r.$,{onClick:()=>{A(void 0,{onSuccess:e=>{g(e.qrCodeData),d(!0)},onError:e=>{t({title:"Error",description:e.message||"Failed to set up two-factor authentication",variant:"destructive"})}})},disabled:C,children:C?(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(p.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Setting up..."]}):"Enable 2FA"})]}),(0,y.jsx)(h.lG,{open:l,onOpenChange:d,children:(0,y.jsxs)(h.Cf,{className:"sm:max-w-[425px]",children:[(0,y.jsxs)(h.c7,{children:[(0,y.jsx)(h.L3,{children:s?"Disable two-factor authentication":"Set up two-factor authentication"}),(0,y.jsx)(h.rr,{children:s?"Enter your verification code to disable two-factor authentication.":"Scan the QR code with your authenticator app and enter the verification code."})]}),!s&&N&&!v&&(0,y.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,y.jsx)("div",{className:"p-4 bg-white rounded-md",children:(0,y.jsx)("img",{src:N,alt:"QR Code",className:"w-48 h-48"})}),(0,y.jsx)("p",{className:"text-sm text-muted-foreground",children:"Scan this QR code with your authenticator app"})]}),v?(0,y.jsxs)("div",{className:"space-y-4",children:[(0,y.jsx)("div",{className:"bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-md",children:(0,y.jsxs)("div",{className:"flex",children:[(0,y.jsx)(x.A,{className:"h-5 w-5 text-yellow-500 mr-2 mt-0.5 flex-shrink-0"}),(0,y.jsx)("p",{className:"text-sm text-yellow-700 dark:text-yellow-300",children:"Please save these recovery codes in a safe place. You can use them to recover access to your account if you lose your device."})]})}),(0,y.jsx)("div",{className:"grid grid-cols-2 gap-2",children:b.map((e,s)=>(0,y.jsx)("div",{className:"font-mono text-sm p-2 bg-muted rounded",children:e},s))}),(0,y.jsxs)("div",{className:"flex space-x-2",children:[(0,y.jsx)(r.$,{variant:"outline",onClick:()=>{navigator.clipboard.writeText(b.join("\n")),t({title:"Copied",description:"Recovery codes copied to clipboard"})},children:"Copy Codes"}),(0,y.jsx)(r.$,{variant:"outline",onClick:()=>{const e=document.createElement("a"),s=new Blob([b.join("\n")],{type:"text/plain"});e.href=URL.createObjectURL(s),e.download="recovery-codes.txt",document.body.appendChild(e),e.click(),document.body.removeChild(e)},children:"Download"})]}),(0,y.jsx)(r.$,{className:"w-full",onClick:()=>{k(!1),d(!1)},children:"I've saved my recovery codes"})]}):(0,y.jsxs)("div",{className:"space-y-4",children:[(0,y.jsxs)("div",{className:"space-y-2",children:[(0,y.jsx)(o.J,{htmlFor:"verification-code",children:s?"Enter verification code to disable 2FA":"Enter verification code from your authenticator app"}),(0,y.jsx)(n.p,{id:"verification-code",value:c,onChange:e=>j(e.target.value),placeholder:"123456",className:"text-center text-xl font-mono tracking-widest",autoComplete:"one-time-code"})]}),(0,y.jsx)(r.$,{className:"w-full",onClick:s?()=>{c.trim()?_({code:c},{onSuccess:()=>{j(""),g(""),w([]),k(!1)},onError:e=>{t({title:"Error",description:e.message||"Failed to disable two-factor authentication",variant:"destructive"})}}):t({title:"Error",description:"Please enter a verification code",variant:"destructive"})}:()=>{c.trim()?T({code:c},{onSuccess:e=>{w(e.recoveryCodes),k(!0),t({title:"Success",description:"Two-factor authentication has been enabled successfully."})},onError:e=>{t({title:"Error",description:e.message||"Failed to verify two-factor authentication code",variant:"destructive"})}}):t({title:"Error",description:"Please enter a verification code",variant:"destructive"})},disabled:s?z:F,children:s?z?(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(p.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Disabling..."]}):"Disable 2FA":F?(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(p.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Verifying..."]}):"Verify and Enable 2FA"})]})]})})]})}},2246:(e,s,t)=>{t.d(s,{G:()=>f});var a=t(5043),r=t(9772),n=t(126),o=t(9954),i=t(6879),l=t(8021),d=t(5923),c=t(8775),m=t(7772),u=t(579);function f(){const{toast:e}=(0,i.dj)(),[s,t]=(0,a.useState)(!1),[f,p]=(0,a.useState)(!1),[x,h]=(0,a.useState)(!1),[y,j]=(0,a.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),[N,g]=(0,a.useState)(!1),{mutate:b}=(0,l.tk)({onSuccess:()=>{e({title:"Success",description:"Your password has been updated successfully."}),j({currentPassword:"",newPassword:"",confirmPassword:""})},onError:s=>{const t=s instanceof Error?s.message:"Failed to update password";e({title:"Error",description:t,variant:"destructive"})}}),w=e=>{const{name:s,value:t}=e.target;j(e=>({...e,[s]:t}))};return(0,u.jsxs)(n.lV,{onSubmit:async s=>{s.preventDefault(),y.newPassword===y.confirmPassword?y.newPassword.length<8?e({title:"Error",description:"Password must be at least 8 characters",variant:"destructive"}):/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^\da-zA-Z]).{8,}$/.test(y.newPassword)?(g(!0),b({currentPassword:y.currentPassword,newPassword:y.newPassword}),g(!1)):e({title:"Error",description:"Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character",variant:"destructive"}):e({title:"Error",description:"Passwords do not match",variant:"destructive"})},className:"space-y-6",children:[(0,u.jsxs)("div",{className:"space-y-4",children:[(0,u.jsxs)(n.eI,{children:[(0,u.jsx)(n.lR,{children:"Current Password"}),(0,u.jsxs)("div",{className:"relative",children:[(0,u.jsx)(n.MJ,{children:(0,u.jsx)(o.p,{type:s?"text":"password",name:"currentPassword",value:y.currentPassword,onChange:w,placeholder:"Enter current password",required:!0})}),(0,u.jsx)("button",{type:"button",className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700",onClick:()=>t(!s),children:s?(0,u.jsx)(d.A,{className:"h-4 w-4"}):(0,u.jsx)(c.A,{className:"h-4 w-4"})})]}),(0,u.jsx)(n.C5,{})]}),(0,u.jsxs)(n.eI,{children:[(0,u.jsx)(n.lR,{children:"New Password"}),(0,u.jsxs)("div",{className:"relative",children:[(0,u.jsx)(n.MJ,{children:(0,u.jsx)(o.p,{type:f?"text":"password",name:"newPassword",value:y.newPassword,onChange:w,placeholder:"Enter new password",required:!0})}),(0,u.jsx)("button",{type:"button",className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700",onClick:()=>p(!f),children:f?(0,u.jsx)(d.A,{className:"h-4 w-4"}):(0,u.jsx)(c.A,{className:"h-4 w-4"})})]}),(0,u.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"Must be at least 8 characters and include uppercase, lowercase, number, and special character."}),(0,u.jsx)(n.C5,{})]}),(0,u.jsxs)(n.eI,{children:[(0,u.jsx)(n.lR,{children:"Confirm New Password"}),(0,u.jsxs)("div",{className:"relative",children:[(0,u.jsx)(n.MJ,{children:(0,u.jsx)(o.p,{type:x?"text":"password",name:"confirmPassword",value:y.confirmPassword,onChange:w,placeholder:"Confirm new password",required:!0})}),(0,u.jsx)("button",{type:"button",className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700",onClick:()=>h(!x),children:x?(0,u.jsx)(d.A,{className:"h-4 w-4"}):(0,u.jsx)(c.A,{className:"h-4 w-4"})})]}),(0,u.jsx)(n.C5,{})]})]}),(0,u.jsxs)(r.$,{type:"submit",disabled:N,children:[N&&(0,u.jsx)(m.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Update Password"]})]})}},2248:(e,s,t)=>{t.d(s,{J:()=>l});var a=t(5043),r=t(917),n=t(3009),o=t(579);const i=(0,r.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,o.jsx)("label",{ref:s,className:(0,n.cn)(i(),t),...a})});l.displayName="Label"},3647:(e,s,t)=>{t.d(s,{SQ:()=>u,_2:()=>f,rI:()=>c,ty:()=>m});var a=t(5043),r=t(1624),n=t(1024),o=t(8432),i=t(3992),l=t(3009),d=t(579);const c=r.bL,m=r.l9;r.YJ,r.ZL,r.Pb,r.z6;a.forwardRef((e,s)=>{let{className:t,inset:a,children:n,...i}=e;return(0,d.jsxs)(r.ZP,{ref:s,className:(0,l.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",a&&"pl-8",t),...i,children:[n,(0,d.jsx)(o.A,{className:"ml-auto h-4 w-4"})]})}).displayName=r.ZP.displayName;a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,d.jsx)(r.G5,{ref:s,className:(0,l.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...a})}).displayName=r.G5.displayName;const u=a.forwardRef((e,s)=>{let{className:t,sideOffset:a=4,...n}=e;return(0,d.jsx)(r.ZL,{children:(0,d.jsx)(r.UC,{ref:s,sideOffset:a,className:(0,l.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...n})})});u.displayName=r.UC.displayName;const f=a.forwardRef((e,s)=>{let{className:t,inset:a,...n}=e;return(0,d.jsx)(r.q7,{ref:s,className:(0,l.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a&&"pl-8",t),...n})});f.displayName=r.q7.displayName;a.forwardRef((e,s)=>{let{className:t,children:a,checked:o,...i}=e;return(0,d.jsxs)(r.H_,{ref:s,className:(0,l.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),checked:o,...i,children:[(0,d.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,d.jsx)(r.VF,{children:(0,d.jsx)(n.A,{className:"h-4 w-4"})})}),a]})}).displayName=r.H_.displayName;a.forwardRef((e,s)=>{let{className:t,children:a,...n}=e;return(0,d.jsxs)(r.hN,{ref:s,className:(0,l.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...n,children:[(0,d.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,d.jsx)(r.VF,{children:(0,d.jsx)(i.A,{className:"h-2 w-2 fill-current"})})}),a]})}).displayName=r.hN.displayName;a.forwardRef((e,s)=>{let{className:t,inset:a,...n}=e;return(0,d.jsx)(r.JU,{ref:s,className:(0,l.cn)("px-2 py-1.5 text-sm font-semibold",a&&"pl-8",t),...n})}).displayName=r.JU.displayName;a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,d.jsx)(r.wv,{ref:s,className:(0,l.cn)("-mx-1 my-1 h-px bg-muted",t),...a})}).displayName=r.wv.displayName},4199:(e,s,t)=>{t.d(s,{x:()=>A});var a=t(5043),r=t(5604),n=t(9781),o=t(2836),i=t(9772),l=t(382),d=t(9954),c=t(2248),m=t(6879),u=t(492),f=t(3647),p=t(987),x=t(8064),h=t(5979),y=t(7772),j=t(1526),N=t(2380),g=t(6339),b=t(579);const w=async()=>{try{const e=await fetch("/api/keys");if(!e.ok)throw new Error("Failed to fetch API keys");return await e.json()}catch(e){throw console.error("Error fetching API keys:",e),e}},v=async e=>{try{const s=await fetch("/api/keys",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e})});if(!s.ok)throw new Error("Failed to create API key");return await s.json()}catch(s){throw console.error("Error creating API key:",s),s}},k=async e=>{try{if(!(await fetch(`/api/keys/${e}`,{method:"DELETE"})).ok)throw new Error("Failed to delete API key");return{success:!0}}catch(s){throw console.error("Error deleting API key:",s),s}};function A(){const{toast:e}=(0,m.dj)(),s=(0,n.jE)(),[t,A]=(0,a.useState)(!1),[C,S]=(0,a.useState)(""),[E,P]=(0,a.useState)(null),[R,T]=(0,a.useState)(null),{data:F,isLoading:I}=(0,r.I)({queryKey:["apiKeys"],queryFn:w}),D=(0,o.n)({mutationFn:e=>v(e),onSuccess:e=>{P(e),s.invalidateQueries({queryKey:["apiKeys"]}),S("")},onError:()=>{e({title:"Error",description:"Failed to create API key",variant:"destructive"})}}),M=(0,o.n)({mutationFn:e=>k(e),onSuccess:()=>{s.invalidateQueries({queryKey:["apiKeys"]}),e({title:"Success",description:"API key deleted successfully"})},onError:()=>{e({title:"Error",description:"Failed to delete API key",variant:"destructive"})}}),O=(s,t)=>{navigator.clipboard.writeText(s),T(t),e({title:"Copied",description:"API key copied to clipboard"}),setTimeout(()=>T(null),2e3)},_=e=>e?new Date(e).toLocaleString():"Never";return I?(0,b.jsx)("div",{children:"Loading API keys..."}):(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)("div",{className:"flex justify-between items-center",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("h3",{className:"text-lg font-medium",children:"API Keys"}),(0,b.jsx)("p",{className:"text-sm text-muted-foreground",children:"Manage your API keys for programmatic access to your account."})]}),(0,b.jsxs)(u.lG,{open:t,onOpenChange:A,children:[(0,b.jsx)(u.zM,{asChild:!0,children:(0,b.jsxs)(i.$,{children:[(0,b.jsx)(N.A,{className:"mr-2 h-4 w-4"}),"Create New Key"]})}),(0,b.jsxs)(u.Cf,{children:[(0,b.jsxs)(u.c7,{children:[(0,b.jsx)(u.L3,{children:"Create New API Key"}),(0,b.jsx)(u.rr,{children:"Create a new API key to authenticate requests to our API."})]}),(0,b.jsxs)("div",{className:"space-y-4",children:[(0,b.jsxs)("div",{className:"space-y-2",children:[(0,b.jsx)(c.J,{htmlFor:"key-name",children:"Key Name"}),(0,b.jsx)(d.p,{id:"key-name",placeholder:"e.g., Production, Development",value:C,onChange:e=>S(e.target.value)}),(0,b.jsx)("p",{className:"text-xs text-muted-foreground",children:"Give this key a name to identify it later."})]}),E&&(0,b.jsx)("div",{className:"rounded-md bg-yellow-50 p-4",children:(0,b.jsxs)("div",{className:"flex",children:[(0,b.jsx)("div",{className:"flex-shrink-0",children:(0,b.jsx)(p.A,{className:"h-5 w-5 text-yellow-400","aria-hidden":"true"})}),(0,b.jsxs)("div",{className:"ml-3",children:[(0,b.jsx)("h3",{className:"text-sm font-medium text-yellow-800",children:"Your new API key"}),(0,b.jsxs)("div",{className:"mt-2 text-sm text-yellow-700",children:[(0,b.jsx)("p",{children:"Please copy your new API key now. You won't be able to see it again!"}),(0,b.jsxs)("div",{className:"mt-2 flex rounded-md shadow-sm",children:[(0,b.jsx)("div",{className:"relative flex flex-grow items-stretch focus-within:z-10",children:(0,b.jsx)("input",{type:"text",className:"block w-full rounded-none rounded-l-md border-0 py-1.5 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",value:E.key,readOnly:!0})}),(0,b.jsxs)("button",{type:"button",className:"relative -ml-px inline-flex items-center gap-x-1.5 rounded-r-md px-3 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50",onClick:()=>O(E.key,E.id),children:[(0,b.jsx)(h.A,{className:"h-4 w-4 text-gray-400","aria-hidden":"true"}),"Copy"]})]})]})]})]})})]}),(0,b.jsxs)(u.Es,{children:[(0,b.jsx)(i.$,{variant:"outline",onClick:()=>{A(!1),P(null)},children:"Close"}),E?null:(0,b.jsx)(i.$,{type:"button",onClick:()=>{C.trim()?D.mutate(C):e({title:"Error",description:"Please enter a name for the API key",variant:"destructive"})},disabled:D.isPending,children:D.isPending?(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(y.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Creating..."]}):"Create Key"})]})]})]})]}),(0,b.jsx)("div",{className:"rounded-md border",children:(0,b.jsxs)(l.XI,{children:[(0,b.jsx)(l.A0,{children:(0,b.jsxs)(l.Hj,{children:[(0,b.jsx)(l.nd,{children:"Name"}),(0,b.jsx)(l.nd,{children:"Key"}),(0,b.jsx)(l.nd,{children:"Created"}),(0,b.jsx)(l.nd,{children:"Last Used"}),(0,b.jsx)(l.nd,{className:"w-10"})]})}),(0,b.jsx)(l.BF,{children:null!==F&&void 0!==F&&F.length?F.map(e=>(0,b.jsxs)(l.Hj,{children:[(0,b.jsx)(l.nA,{className:"font-medium",children:e.name}),(0,b.jsx)(l.nA,{children:(0,b.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,b.jsxs)("span",{className:"font-mono text-sm",children:[e.key.substring(0,8),"...",e.key.substring(e.key.length-4)]}),(0,b.jsx)("button",{type:"button",className:"text-muted-foreground hover:text-foreground",onClick:()=>O(e.key,e.id),children:R===e.id?(0,b.jsx)(x.A,{className:"h-4 w-4 text-green-500"}):(0,b.jsx)(h.A,{className:"h-4 w-4"})})]})}),(0,b.jsx)(l.nA,{children:_(e.createdAt)}),(0,b.jsx)(l.nA,{children:_(e.lastUsed)}),(0,b.jsx)(l.nA,{children:(0,b.jsxs)(f.rI,{children:[(0,b.jsx)(f.ty,{asChild:!0,children:(0,b.jsxs)(i.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:[(0,b.jsx)(j.A,{className:"h-4 w-4"}),(0,b.jsx)("span",{className:"sr-only",children:"Actions"})]})}),(0,b.jsx)(f.SQ,{align:"end",children:(0,b.jsxs)(f._2,{className:"text-red-600",onClick:()=>M.mutate(e.id),disabled:M.variables===e.id,children:[M.variables===e.id?(0,b.jsx)(y.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,b.jsx)(g.A,{className:"mr-2 h-4 w-4"}),"Delete"]})})]})})]},e.id)):(0,b.jsx)(l.Hj,{children:(0,b.jsx)(l.nA,{colSpan:5,className:"h-24 text-center",children:"No API keys found."})})})]})})]})}},6736:(e,s,t)=>{t.d(s,{Xi:()=>d,av:()=>c,j7:()=>l,tU:()=>i});var a=t(5043),r=t(7127),n=t(3009),o=t(579);const i=r.bL,l=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,o.jsx)(r.B8,{ref:s,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),...a})});l.displayName=r.B8.displayName;const d=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,o.jsx)(r.l9,{ref:s,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",t),...a})});d.displayName=r.l9.displayName;const c=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,o.jsx)(r.UC,{ref:s,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),...a})});c.displayName=r.UC.displayName},6742:(e,s,t)=>{t.d(s,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>o,aR:()=>i,wL:()=>m});var a=t(5043),r=t(3009),n=t(579);const o=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,n.jsx)("div",{ref:s,className:(0,r.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...a})});o.displayName="Card";const i=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,n.jsx)("div",{ref:s,className:(0,r.cn)("flex flex-col space-y-1.5 p-6",t),...a})});i.displayName="CardHeader";const l=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,n.jsx)("h3",{ref:s,className:(0,r.cn)("text-2xl font-semibold leading-none tracking-tight",t),...a})});l.displayName="CardTitle";const d=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,n.jsx)("p",{ref:s,className:(0,r.cn)("text-sm text-muted-foreground",t),...a})});d.displayName="CardDescription";const c=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,n.jsx)("div",{ref:s,className:(0,r.cn)("p-6 pt-0",t),...a})});c.displayName="CardContent";const m=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,n.jsx)("div",{ref:s,className:(0,r.cn)("flex items-center p-6 pt-0",t),...a})});m.displayName="CardFooter"},6879:(e,s,t)=>{t.d(s,{dj:()=>u,oR:()=>m});var a=t(5043);let r=0;const n=new Map,o=e=>{if(n.has(e))return;const s=setTimeout(()=>{n.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e3);n.set(e,s)},i=(e,s)=>{switch(s.type){case"ADD_TOAST":return{...e,toasts:[s.toast,...e.toasts].slice(0,5)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===s.toast.id?{...e,...s.toast}:e)};case"DISMISS_TOAST":{const{toastId:t}=s;return t?o(t):e.toasts.forEach(e=>{o(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===t||void 0===t?{...e,open:!1}:e)}}case"REMOVE_TOAST":return void 0===s.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==s.toastId)}}},l=[];let d={toasts:[]};function c(e){d=i(d,e),l.forEach(e=>{e(d)})}function m(e){let{...s}=e;const t=(r=(r+1)%Number.MAX_SAFE_INTEGER,r.toString()),a=()=>c({type:"DISMISS_TOAST",toastId:t});return c({type:"ADD_TOAST",toast:{...s,id:t,open:!0,onOpenChange:e=>{e||a()}}}),{id:t,dismiss:a,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function u(){const[e,s]=a.useState(d);return a.useEffect(()=>(l.push(s),()=>{const e=l.indexOf(s);e>-1&&l.splice(e,1)}),[e]),{...e,toast:m,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},7298:(e,s,t)=>{t.d(s,{g:()=>x});var a=t(4858),r=t(1850),n=t(880),o=t(9772),i=t(126),l=t(9954),d=t(6879),c=t(8021),m=t(7772),u=t(579);const f=n.Ik({name:n.Yj().min(2,"Name must be at least 2 characters"),email:n.Yj().email("Invalid email address"),businessName:n.Yj().optional(),businessType:n.Yj().optional(),businessAddress:n.Ik({street:n.Yj().optional(),city:n.Yj().optional(),state:n.Yj().optional(),country:n.Yj().optional(),postalCode:n.Yj().optional()}).optional()}),p=e=>{let{name:s,label:t,render:r,...n}=e;const{control:o}=(0,a.xW)();return(0,u.jsx)(a.xI,{name:s,control:o,render:e=>{let{field:s}=e;return(0,u.jsxs)(i.eI,{children:[(0,u.jsx)(i.lR,{children:t}),(0,u.jsx)(i.MJ,{children:r(s)}),(0,u.jsx)(i.C5,{})]})}})};function x(e){let{user:s,onSuccess:t}=e;const{toast:n}=(0,d.dj)(),{mutate:x,isPending:h}=(0,c.Sn)({onSuccess:()=>{n({title:"Success",description:"Your profile has been updated successfully."}),null===t||void 0===t||t()},onError:e=>{const s=e instanceof Error?e.message:"Failed to update profile";n({title:"Error",description:s,variant:"destructive"})}}),y=(0,a.mN)({resolver:(0,r.u)(f),defaultValues:{name:s.name,email:s.email,businessName:"merchant"===s.role?s.businessName:"",businessType:"",businessAddress:{street:"",city:"",state:"",country:"",postalCode:""}}});return(0,u.jsx)(i.lV,{...y,children:(0,u.jsxs)("form",{onSubmit:y.handleSubmit(e=>{x(e)}),className:"space-y-6",children:[(0,u.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,u.jsx)(p,{name:"name",label:"Full Name",render:e=>(0,u.jsx)(l.p,{placeholder:"John Doe",...e})}),(0,u.jsx)(p,{name:"email",label:"Email",render:e=>(0,u.jsx)(l.p,{type:"email",placeholder:"<EMAIL>",disabled:!0,...e})}),"merchant"===s.role&&(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)(p,{name:"businessName",label:"Business Name",render:e=>(0,u.jsx)(l.p,{placeholder:"Acme Inc.",...e})}),(0,u.jsx)(p,{name:"businessType",label:"Business Type",render:e=>(0,u.jsx)(l.p,{placeholder:"Retail",...e})}),(0,u.jsxs)("div",{className:"col-span-2 space-y-4",children:[(0,u.jsx)("h4",{className:"text-sm font-medium",children:"Business Address"}),(0,u.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,u.jsx)("div",{className:"md:col-span-2",children:(0,u.jsx)(p,{name:"businessAddress.street",label:"Street Address",render:e=>(0,u.jsx)(l.p,{placeholder:"123 Main St",...e})})}),(0,u.jsx)(p,{name:"businessAddress.city",label:"City",render:e=>(0,u.jsx)(l.p,{placeholder:"New York",...e})}),(0,u.jsx)(p,{name:"businessAddress.state",label:"State/Province",render:e=>(0,u.jsx)(l.p,{placeholder:"NY",...e})}),(0,u.jsx)(p,{name:"businessAddress.postalCode",label:"Postal Code",render:e=>(0,u.jsx)(l.p,{placeholder:"10001",...e})}),(0,u.jsx)(p,{name:"businessAddress.country",label:"Country",render:e=>(0,u.jsx)(l.p,{placeholder:"United States",...e})})]})]})]})]}),(0,u.jsx)("div",{className:"flex justify-end",children:(0,u.jsxs)(o.$,{type:"submit",disabled:h,children:[h&&(0,u.jsx)(m.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Update Profile"]})})]})})}},8021:(e,s,t)=>{t.d(s,{Sn:()=>i,tk:()=>l});var a=t(9781),r=t(2836);const n=async function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const t=await fetch(`/api${e}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(!t.ok)throw new Error("Network response was not ok");return t.json()},o=async function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const t=await fetch(`/api${e}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(!t.ok)throw new Error("Network response was not ok");return t.json()},i=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const s=(0,a.jE)();return(0,r.n)({mutationFn:async e=>(await o("/users/me",e)).data,onSuccess:(t,a,r)=>{s.setQueryData(["currentUser"],t),e.onSuccess&&e.onSuccess(t,a,r)},onError:e.onError,onSettled:e.onSettled,onMutate:e.onMutate})},l=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return(0,r.n)({mutationFn:async e=>(await n("/auth/change-password",e)).data,onSuccess:e.onSuccess,onError:e.onError,onSettled:e.onSettled,onMutate:e.onMutate})}},9772:(e,s,t)=>{t.d(s,{$:()=>d});var a=t(5043),r=t(6851),n=t(917),o=t(3009),i=t(579);const l=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,s)=>{let{className:t,variant:a,size:n,asChild:d=!1,...c}=e;const m=d?r.DX:"button";return(0,i.jsx)(m,{className:(0,o.cn)(l({variant:a,size:n,className:t})),ref:s,...c})});d.displayName="Button"},9954:(e,s,t)=>{t.d(s,{p:()=>o});var a=t(5043),r=t(3009),n=t(579);const o=a.forwardRef((e,s)=>{let{className:t,type:a,...o}=e;return(0,n.jsx)("input",{type:a,className:(0,r.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...o})});o.displayName="Input"}}]);
//# sourceMappingURL=400.a4415629.chunk.js.map