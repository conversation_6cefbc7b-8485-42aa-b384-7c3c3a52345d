import React from 'react';
import { 
  Box, 
  Grid, 
  Paper, 
  Typography, 
  Button, 
  Card, 
  CardContent, 
  CardActions,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Avatar,
  LinearProgress,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  ArrowUpward as ArrowUpwardIcon,
  ArrowDownward as ArrowDownwardIcon,
  AccountBalanceWallet as WalletIcon,
  Receipt as ReceiptIcon,
  Payment as PaymentIcon,
  Add as AddIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { useNavigate } from 'react-router-dom';
import { formatCurrency } from '../../utils/formatters';
import DashboardLayout from '../../layouts/DashboardLayout';

// Styled components
const StatCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  transition: 'transform 0.2s, box-shadow 0.2s',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: theme.shadows[8],
  },
}));

const StatCardContent = styled(CardContent)(({ theme }) => ({
  flexGrow: 1,
  padding: theme.spacing(3),
}));

const StatValue = styled(Typography)(({ theme }) => ({
  fontSize: '2rem',
  fontWeight: 700,
  margin: theme.spacing(1, 0),
}));

const StatChange = styled(Typography, {
  shouldForwardProp: (prop) => prop !== 'positive',
})(({ theme, positive }) => ({
  display: 'flex',
  alignItems: 'center',
  color: positive ? theme.palette.success.main : theme.palette.error.main,
  fontWeight: 500,
}));

const QuickActionButton = styled(Button)(({ theme }) => ({
  textTransform: 'none',
  padding: theme.spacing(1.5, 2),
  justifyContent: 'flex-start',
  '& .MuiButton-startIcon': {
    marginRight: theme.spacing(1.5),
  },
}));

// Mock data - replace with actual API calls
const stats = {
  balance: 12540.75,
  pending: 3250.20,
  income: 45890.50,
  expenses: 33349.75,
  change: 12.5,
};

const recentTransactions = [
  { id: 1, name: 'Payment from John Doe', amount: 1250.75, type: 'credit', date: '2023-06-15T10:30:00Z', status: 'completed' },
  { id: 2, name: 'Grocery Store', amount: 156.20, type: 'debit', date: '2023-06-14T15:45:00Z', status: 'completed' },
  { id: 3, name: 'Monthly Subscription', amount: 29.99, type: 'debit', date: '2023-06-10T08:15:00Z', status: 'completed' },
  { id: 4, name: 'Freelance Work', amount: 850.00, type: 'credit', date: '2023-06-08T14:20:00Z', status: 'completed' },
  { id: 5, name: 'Restaurant Bill', amount: 78.40, type: 'debit', date: '2023-06-05T19:30:00Z', status: 'completed' },
];

const quickActions = [
  { id: 1, label: 'Send Money', icon: <ArrowUpwardIcon />, path: '/send-money' },
  { id: 2, label: 'Request Payment', icon: <ArrowDownwardIcon />, path: '/request-payment' },
  { id: 3, label: 'Pay Bills', icon: <PaymentIcon />, path: '/pay-bills' },
  { id: 4, label: 'Add Money', icon: <AddIcon />, path: '/add-money' },
];

const DashboardPage = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const navigate = useNavigate();

  const handleQuickAction = (path) => {
    navigate(path);
  };

  const renderStatCard = (title, value, icon, change, isCurrency = true, positiveIsGood = true) => {
    const isPositive = change >= 0;
    const color = isPositive === positiveIsGood ? 'success.main' : 'error.main';
    
    return (
      <StatCard elevation={3}>
        <StatCardContent>
          <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
            <Typography color="textSecondary" variant="subtitle2">
              {title}
            </Typography>
            <Box
              sx={{
                p: 1,
                borderRadius: '50%',
                bgcolor: `${color}15`,
                color: color,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              {React.cloneElement(icon, { fontSize: 'small' })}
            </Box>
          </Box>
          <StatValue variant="h4">
            {isCurrency ? formatCurrency(value) : value}
          </StatValue>
          <StatChange positive={isPositive}>
            {isPositive ? <TrendingUpIcon fontSize="small" /> : <TrendingDownIcon fontSize="small" />}
            <Box component="span" ml={0.5}>
              {Math.abs(change)}% {isPositive ? 'increase' : 'decrease'} from last month
            </Box>
          </StatChange>
        </StatCardContent>
      </StatCard>
    );
  };

  return (
    <DashboardLayout title="Dashboard">
      {/* Stats Grid */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          {renderStatCard('Available Balance', stats.balance, <WalletIcon />, stats.change, true, true)}
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          {renderStatCard('Pending Balance', stats.pending, <ReceiptIcon />, -2.5, true, false)}
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          {renderStatCard('Total Income', stats.income, <ArrowDownwardIcon />, 8.2, true, true)}
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          {renderStatCard('Total Expenses', stats.expenses, <ArrowUpwardIcon />, -4.7, true, false)}
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Quick Actions */}
        <Grid item xs={12} md={4}>
          <Paper elevation={3} sx={{ p: 3, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Quick Actions
            </Typography>
            <Grid container spacing={2}>
              {quickActions.map((action) => (
                <Grid item xs={6} key={action.id}>
                  <QuickActionButton
                    fullWidth
                    variant="outlined"
                    startIcon={action.icon}
                    onClick={() => handleQuickAction(action.path)}
                  >
                    {action.label}
                  </QuickActionButton>
                </Grid>
              ))}
            </Grid>
            
            <Box mt={4}>
              <Typography variant="subtitle2" gutterBottom>
                Account Limit
              </Typography>
              <Box display="flex" justifyContent="space-between" mb={1}>
                <Typography variant="body2" color="textSecondary">
                  $12,450 / $50,000
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  24.9%
                </Typography>
              </Box>
              <LinearProgress variant="determinate" value={24.9} color="primary" />
            </Box>
          </Paper>
        </Grid>

        {/* Recent Transactions */}
        <Grid item xs={12} md={8}>
          <Paper elevation={3} sx={{ p: 3 }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
              <Typography variant="h6">Recent Transactions</Typography>
              <Button color="primary" onClick={() => navigate('/transactions')}>
                View All
              </Button>
            </Box>
            
            <List disablePadding>
              {recentTransactions.map((transaction, index) => (
                <React.Fragment key={transaction.id}>
                  <ListItem 
                    button 
                    onClick={() => navigate(`/transactions/${transaction.id}`)}
                    sx={{
                      '&:hover': {
                        backgroundColor: 'action.hover',
                        borderRadius: 1,
                      },
                      px: 2,
                      py: 1.5,
                    }}
                  >
                    <ListItemIcon>
                      <Avatar
                        sx={{
                          bgcolor: transaction.type === 'credit' ? 'success.light' : 'error.light',
                          color: transaction.type === 'credit' ? 'success.contrastText' : 'error.contrastText',
                        }}
                      >
                        {transaction.type === 'credit' ? <ArrowDownwardIcon /> : <ArrowUpwardIcon />}
                      </Avatar>
                    </ListItemIcon>
                    <ListItemText
                      primary={transaction.name}
                      primaryTypographyProps={{
                        variant: 'subtitle2',
                        color: 'text.primary',
                      }}
                      secondary={new Date(transaction.date).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric',
                      })}
                      secondaryTypographyProps={{
                        variant: 'caption',
                        color: 'text.secondary',
                      }}
                    />
                    <Box textAlign="right">
                      <Typography
                        variant="subtitle2"
                        color={transaction.type === 'credit' ? 'success.main' : 'error.main'}
                      >
                        {transaction.type === 'credit' ? '+' : '-'} {formatCurrency(transaction.amount)}
                      </Typography>
                      <Typography variant="caption" color="textSecondary">
                        {transaction.status}
                      </Typography>
                    </Box>
                  </ListItem>
                  {index < recentTransactions.length - 1 && <Divider variant="inset" component="li" />}
                </React.Fragment>
              ))}
            </List>
          </Paper>
        </Grid>
      </Grid>
    </DashboardLayout>
  );
};

export default DashboardPage;
