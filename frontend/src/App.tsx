import React from 'react';
import { BrowserRouter as Router, useRoutes } from 'react-router-dom';
import { QueryClientProvider } from '@tanstack/react-query';
import { ErrorBoundary } from './components/ErrorBoundary';
import { queryClient } from './lib/react-query';
import { routes } from './config/routes';
import './App.css';

// Main App component with routing
const AppRoutes = () => {
  const element = useRoutes(routes);
  return element;
};

// Error Boundary for the entire app
const AppErrorBoundary = ({ children }: { children: React.ReactNode }) => (
  <ErrorBoundary 
    fallback={
      <div className="flex items-center justify-center min-h-screen bg-gray-100">
        <div className="p-8 bg-white rounded-lg shadow-md">
          <h2 className="text-2xl font-bold text-red-600 mb-4">Something went wrong</h2>
          <p className="text-gray-700">Please refresh the page or try again later.</p>
        </div>
      </div>
    }
  >
    {children}
  </ErrorBoundary>
);

function App() {
  return (
    <React.StrictMode>
      <QueryClientProvider client={queryClient}>
        <AppErrorBoundary>
          <Router>
            <div className="min-h-screen bg-gray-50">
              <AppErrorBoundary>
                <AppRoutes />
              </AppErrorBoundary>
            </div>
          </Router>
        </AppErrorBoundary>
      </QueryClientProvider>
    </React.StrictMode>
  );
}

export default App;
