const Joi = require('joi');
const { role } = require('../config/roles');

const createMenuItem = {
  body: Joi.object().keys({
    menuId: Joi.string().required(),
    label: Joi.string().required(),
    to: Joi.string().required(),
    icon: Joi.string().default('Menu'),
    role: Joi.string().valid(...Object.values(role), 'all').required(),
    parentId: Joi.string().allow(null, ''),
    order: Joi.number().default(0),
    isActive: Joi.boolean().default(true),
    permissions: Joi.array().items(Joi.string()),
    description: Joi.string().allow(''),
    badge: Joi.object({
      text: Joi.string().required(),
      color: Joi.string().valid('red', 'blue', 'green', 'yellow', 'purple', 'gray')
    }),
    metadata: Joi.object({
      category: Joi.string(),
      tags: Joi.array().items(Joi.string()),
      isNew: Joi.boolean(),
      isBeta: Joi.boolean()
    })
  })
};

const updateMenuItem = {
  params: Joi.object().keys({
    menuId: Joi.string().required()
  }),
  body: Joi.object().keys({
    label: Joi.string(),
    to: Joi.string(),
    icon: Joi.string(),
    role: Joi.string().valid(...Object.values(role), 'all'),
    parentId: Joi.string().allow(null, ''),
    order: Joi.number(),
    isActive: Joi.boolean(),
    permissions: Joi.array().items(Joi.string()),
    description: Joi.string().allow(''),
    badge: Joi.object({
      text: Joi.string().required(),
      color: Joi.string().valid('red', 'blue', 'green', 'yellow', 'purple', 'gray')
    }),
    metadata: Joi.object({
      category: Joi.string(),
      tags: Joi.array().items(Joi.string()),
      isNew: Joi.boolean(),
      isBeta: Joi.boolean()
    })
  }).min(1)
};

const getMenuByRole = {
  params: Joi.object().keys({
    role: Joi.string().valid(...Object.values(role), 'all').required()
  })
};

const getMenuItem = {
  params: Joi.object().keys({
    menuId: Joi.string().required()
  })
};

const deleteMenuItem = {
  params: Joi.object().keys({
    menuId: Joi.string().required()
  })
};

module.exports = {
  createMenuItem,
  updateMenuItem,
  getMenuByRole,
  getMenuItem,
  deleteMenuItem
};
