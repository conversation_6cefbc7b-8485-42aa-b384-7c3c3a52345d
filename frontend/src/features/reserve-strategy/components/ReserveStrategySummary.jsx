import React from 'react';
import {
  Box,
  Card,
  CardContent,
  Grid,
  LinearProgress,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  useTheme,
} from '@mui/material';
import {
  AccountBalance as AccountBalanceIcon,
  AttachMoney as AttachMoneyIcon,
  Timeline as TimelineIcon,
  Info as InfoIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';

const StatCard = ({ title, value, icon, color = 'primary', subValue, loading = false }) => {
  const theme = useTheme();
  
  const getIcon = () => {
    switch (icon) {
      case 'balance':
        return <AccountBalanceIcon />;
      case 'money':
        return <AttachMoneyIcon />;
      case 'timeline':
        return <TimelineIcon />;
      case 'info':
        return <InfoIcon />;
      case 'warning':
        return <WarningIcon />;
      case 'success':
        return <CheckCircleIcon />;
      default:
        return null;
    }
  };
  
  return (
    <Paper 
      elevation={0} 
      sx={{ 
        p: 2, 
        height: '100%',
        border: `1px solid ${theme.palette.divider}`,
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      {loading && (
        <LinearProgress 
          sx={{ 
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: 4,
          }} 
        />
      )}
      
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
        <Box 
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: 40,
            height: 40,
            borderRadius: '50%',
            bgcolor: `${color}.light`,
            color: `${color}.dark`,
            mr: 2,
          }}
        >
          {getIcon()}
        </Box>
        <Box>
          <Typography variant="body2" color="textSecondary" noWrap>
            {title}
          </Typography>
          <Typography variant="h6" noWrap>
            {value}
          </Typography>
          {subValue && (
            <Typography variant="caption" color="textSecondary">
              {subValue}
            </Typography>
          )}
        </Box>
      </Box>
    </Paper>
  );
};

const ReserveStrategySummary = ({ strategy }) => {
  const theme = useTheme();
  
  if (!strategy) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="body1" color="textSecondary">
          No strategy data available
        </Typography>
      </Box>
    );
  }
  
  // Calculate total reserve percentage
  const calculateTotalReservePercentage = () => {
    if (!strategy.rules || strategy.rules.length === 0) return 0;
    
    // For non-tiered rules, sum the percentages
    const nonTieredRules = strategy.rules.filter(rule => rule.type !== 'tiered' && rule.percentage);
    const totalPercentage = nonTieredRules.reduce((sum, rule) => sum + parseFloat(rule.percentage || 0), 0);
    
    // For tiered rules, use the highest percentage from the first tier
    const tieredRules = strategy.rules.filter(rule => rule.type === 'tiered' && rule.tiers && rule.tiers.length > 0);
    const highestTierPercentage = tieredRules.length > 0 
      ? Math.max(...tieredRules.map(rule => parseFloat(rule.tiers[0]?.percentage || 0)))
      : 0;
    
    return totalPercentage + highestTierPercentage;
  };
  
  // Count active rules
  const activeRulesCount = strategy.rules?.filter(rule => rule.isActive).length || 0;
  const totalRulesCount = strategy.rules?.length || 0;
  
  // Get status info
  const getStatusInfo = () => {
    const now = new Date();
    const effectiveFrom = strategy.effectiveFrom ? new Date(strategy.effectiveFrom) : null;
    const effectiveTo = strategy.effectiveTo ? new Date(strategy.effectiveTo) : null;
    
    if (strategy.status === 'draft') {
      return {
        status: 'Draft',
        color: 'warning',
        description: 'This strategy is in draft mode and not yet active',
      };
    }
    
    if (strategy.status === 'inactive') {
      return {
        status: 'Inactive',
        color: 'error',
        description: 'This strategy is currently inactive',
      };
    }
    
    // Active status with date checks
    if (effectiveFrom && now < effectiveFrom) {
      return {
        status: 'Scheduled',
        color: 'info',
        description: `Will become active on ${format(effectiveFrom, 'MMM d, yyyy')}`,
      };
    }
    
    if (effectiveTo && now > effectiveTo) {
      return {
        status: 'Expired',
        color: 'error',
        description: `Expired on ${format(effectiveTo, 'MMM d, yyyy')}`,
      };
    }
    
    return {
      status: 'Active',
      color: 'success',
      description: effectiveTo 
        ? `Active until ${format(effectiveTo, 'MMM d, yyyy')}`
        : 'Currently active with no end date',
    };
  };
  
  const statusInfo = getStatusInfo();
  const totalReservePercentage = calculateTotalReservePercentage();
  
  // Get auto-replenish status
  const getAutoReplenishStatus = () => {
    if (!strategy.config?.autoReplenish?.enabled) {
      return {
        status: 'Disabled',
        color: 'text.disabled',
        icon: 'info',
        description: 'Auto-replenish is not configured for this strategy',
      };
    }
    
    return {
      status: 'Enabled',
      color: 'success.main',
      icon: 'success',
      description: `Replenishes $${strategy.config.autoReplenish.amount} when below $${strategy.config.autoReplenish.threshold}`,
    };
  };
  
  const autoReplenishStatus = getAutoReplenishStatus();
  
  return (
    <Box>
      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard 
            title="Status"
            value={statusInfo.status}
            subValue={statusInfo.description}
            icon="info"
            color={statusInfo.color}
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard 
            title="Total Reserve"
            value={`${totalReservePercentage.toFixed(2)}%`}
            subValue={`${activeRulesCount} of ${totalRulesCount} rules active`}
            icon="money"
            color="primary"
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard 
            title="Auto-Replenish"
            value={autoReplenishStatus.status}
            subValue={autoReplenishStatus.description}
            icon={autoReplenishStatus.icon}
            color={autoReplenishStatus.status === 'Enabled' ? 'success' : 'text'}
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard 
            title="Effective Period"
            value={
              strategy.effectiveFrom 
                ? format(new Date(strategy.effectiveFrom), 'MMM d, yyyy')
                : 'No start date'
            }
            subValue={
              strategy.effectiveTo 
                ? `to ${format(new Date(strategy.effectiveTo), 'MMM d, yyyy')}`
                : 'No end date'
            }
            icon="timeline"
            color="info"
          />
        </Grid>
      </Grid>
      
      {/* Rules Summary */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Reserve Rules
          </Typography>
          
          {(!strategy.rules || strategy.rules.length === 0) ? (
            <Box sx={{ p: 3, textAlign: 'center', border: `1px dashed ${theme.palette.divider}`, borderRadius: 1 }}>
              <Typography variant="body1" color="textSecondary">
                No reserve rules defined for this strategy
              </Typography>
            </Box>
          ) : (
            <TableContainer component={Paper} variant="outlined">
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Description</TableCell>
                    <TableCell>Type</TableCell>
                    <TableCell>Reserve</TableCell>
                    <TableCell>Min/Max</TableCell>
                    <TableCell>Priority</TableCell>
                    <TableCell>Status</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {strategy.rules.map((rule, index) => (
                    <TableRow key={index} hover>
                      <TableCell>
                        <Typography variant="body2" fontWeight={500}>
                          {rule.description || `Rule ${index + 1}`}
                        </Typography>
                        {rule.type === 'tiered' && rule.tiers && (
                          <Typography variant="caption" color="textSecondary">
                            {rule.tiers.length} tiers defined
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell>
                        <Box sx={{ textTransform: 'capitalize' }}>
                          {rule.type.replace('_', ' ')}
                        </Box>
                      </TableCell>
                      <TableCell>
                        {rule.type === 'percentage' && `${rule.percentage}%`}
                        {rule.type === 'fixed_amount' && `$${rule.fixedAmount}`}
                        {rule.type === 'tiered' && (
                          <Box>
                            {rule.tiers && rule.tiers.length > 0 ? (
                              <Box>
                                <Box>First: {rule.tiers[0].percentage}%</Box>
                                <Box>Last: {rule.tiers[rule.tiers.length - 1].percentage}%</Box>
                              </Box>
                            ) : (
                              'No tiers'
                            )}
                          </Box>
                        )}
                      </TableCell>
                      <TableCell>
                        {rule.minReserve || rule.maxReserve ? (
                          <>
                            <div>Min: ${rule.minReserve || '0'}</div>
                            <div>Max: ${rule.maxReserve || '∞'}</div>
                          </>
                        ) : (
                          'No limits'
                        )}
                      </TableCell>
                      <TableCell>{rule.priority}</TableCell>
                      <TableCell>
                        <Box 
                          sx={{
                            display: 'inline-flex',
                            alignItems: 'center',
                            px: 1,
                            py: 0.5,
                            borderRadius: 1,
                            bgcolor: rule.isActive ? 'success.light' : 'action.disabledBackground',
                            color: rule.isActive ? 'success.dark' : 'text.secondary',
                          }}
                        >
                          {rule.isActive ? 'Active' : 'Inactive'}
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>
      
      {/* Auto-Replenish Details */}
      {strategy.config?.autoReplenish?.enabled && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Auto-Replenish Settings
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                    Threshold Amount
                  </Typography>
                  <Typography variant="body1">
                    ${strategy.config.autoReplenish.threshold}
                  </Typography>
                  <Typography variant="caption" color="textSecondary">
                    When the reserve balance falls below this amount, auto-replenish will be triggered
                  </Typography>
                </Box>
                
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                    Replenish Amount
                  </Typography>
                  <Typography variant="body1">
                    ${strategy.config.autoReplenish.amount}
                  </Typography>
                  <Typography variant="caption" color="textSecondary">
                    The amount that will be transferred to the reserve when triggered
                  </Typography>
                </Box>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                    Source Account
                  </Typography>
                  <Typography variant="body1">
                    {strategy.config.autoReplenish.sourceAccount || 'Not specified'}
                  </Typography>
                  <Typography variant="caption" color="textSecondary">
                    The account that funds will be transferred from
                  </Typography>
                </Box>
                
                <Box>
                  <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                    Last Replenishment
                  </Typography>
                  <Typography variant="body1">
                    {strategy.lastReplenishedAt 
                      ? format(new Date(strategy.lastReplenishedAt), 'MMM d, yyyy h:mm a') 
                      : 'Never'}
                  </Typography>
                  <Typography variant="caption" color="textSecondary">
                    Last time the reserve was automatically replenished
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}
      
      {/* Notification Settings */}
      {strategy.config?.notifications?.lowBalance?.enabled && (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Notification Settings
            </Typography>
            
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                Low Balance Alerts
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                      Threshold
                    </Typography>
                    <Typography variant="body1">
                      ${strategy.config.notifications.lowBalance.threshold}
                    </Typography>
                    <Typography variant="caption" color="textSecondary">
                      Notifications will be sent when the reserve balance falls below this amount
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Box>
                    <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                      Recipients
                    </Typography>
                    {strategy.config.notifications.lowBalance.recipients?.length > 0 ? (
                      <Box component="ul" sx={{ pl: 2, m: 0 }}>
                        {strategy.config.notifications.lowBalance.recipients.map((email, idx) => (
                          <li key={idx}>
                            <Typography variant="body2">{email}</Typography>
                          </li>
                        ))}
                      </Box>
                    ) : (
                      <Typography variant="body2" color="textSecondary">
                        No recipients configured
                      </Typography>
                    )}
                    <Typography variant="caption" color="textSecondary">
                      Email addresses that will receive low balance alerts
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </Box>
          </CardContent>
        </Card>
      )}
    </Box>
  );
};

export default ReserveStrategySummary;
