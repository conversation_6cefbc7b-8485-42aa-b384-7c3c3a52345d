"use strict";(self.webpackChunkpayment_gateway_frontend=self.webpackChunkpayment_gateway_frontend||[]).push([[839],{1144:(t,e,n)=>{n.d(e,{A:()=>h});var a={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};const r=function(t,e,n){var r,o=a[t];return r="string"===typeof o?o:1===e?o.one:o.other.replace("{{count}}",e.toString()),null!==n&&void 0!==n&&n.addSuffix?n.comparison&&n.comparison>0?"in "+r:r+" ago":r};function o(t){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=e.width?String(e.width):t.defaultWidth;return t.formats[n]||t.formats[t.defaultWidth]}}var i={date:o({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:o({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:o({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})};var u={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function s(t){return function(e,n){var a;if("formatting"===(null!==n&&void 0!==n&&n.context?String(n.context):"standalone")&&t.formattingValues){var r=t.defaultFormattingWidth||t.defaultWidth,o=null!==n&&void 0!==n&&n.width?String(n.width):r;a=t.formattingValues[o]||t.formattingValues[r]}else{var i=t.defaultWidth,u=null!==n&&void 0!==n&&n.width?String(n.width):t.defaultWidth;a=t.values[u]||t.values[i]}return a[t.argumentCallback?t.argumentCallback(e):e]}}function d(t){return function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=n.width,r=a&&t.matchPatterns[a]||t.matchPatterns[t.defaultMatchWidth],o=e.match(r);if(!o)return null;var i,u=o[0],s=a&&t.parsePatterns[a]||t.parsePatterns[t.defaultParseWidth],d=Array.isArray(s)?function(t,e){for(var n=0;n<t.length;n++)if(e(t[n]))return n;return}(s,function(t){return t.test(u)}):function(t,e){for(var n in t)if(t.hasOwnProperty(n)&&e(t[n]))return n;return}(s,function(t){return t.test(u)});return i=t.valueCallback?t.valueCallback(d):d,{value:i=n.valueCallback?n.valueCallback(i):i,rest:e.slice(u.length)}}}var l;const h={code:"en-US",formatDistance:r,formatLong:i,formatRelative:function(t,e,n,a){return u[t]},localize:{ordinalNumber:function(t,e){var n=Number(t),a=n%100;if(a>20||a<10)switch(a%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},era:s({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:s({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:function(t){return t-1}}),month:s({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:s({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:s({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:(l={matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:function(t){return parseInt(t,10)}},function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.match(l.matchPattern);if(!n)return null;var a=n[0],r=t.match(l.parsePattern);if(!r)return null;var o=l.valueCallback?l.valueCallback(r[0]):r[0];return{value:o=e.valueCallback?e.valueCallback(o):o,rest:t.slice(a.length)}}),era:d({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:d({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:function(t){return t+1}}),month:d({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:d({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:d({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}}},2239:(t,e,n)=>{n.d(e,{q:()=>r});var a={};function r(){return a}},2284:(t,e,n)=>{function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}n.d(e,{A:()=>a})},3666:(t,e,n)=>{function a(t,e){if(e.length<t)throw new TypeError(t+" argument"+(t>1?"s":"")+" required, but only "+e.length+" present")}n.d(e,{A:()=>a})},4967:(t,e,n)=>{function a(t){var e=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return e.setUTCFullYear(t.getFullYear()),t.getTime()-e.getTime()}n.d(e,{A:()=>a})},6260:(t,e,n)=>{n.d(e,{A:()=>o});var a=n(2284),r=n(3666);function o(t){(0,r.A)(1,arguments);var e=Object.prototype.toString.call(t);return t instanceof Date||"object"===(0,a.A)(t)&&"[object Date]"===e?new Date(t.getTime()):"number"===typeof t||"[object Number]"===e?new Date(t):("string"!==typeof t&&"[object String]"!==e||"undefined"===typeof console||(console.warn("Starting with v2.0.0-beta.1 date-fns doesn't accept strings as date arguments. Please use `parseISO` to parse strings. See: https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#string-arguments"),console.warn((new Error).stack)),new Date(NaN))}},6474:(t,e,n)=>{n.d(e,{A:()=>a});const a=(0,n(3797).A)("LogIn",[["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}],["polyline",{points:"10 17 15 12 10 7",key:"1ail0h"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}]])},8522:(t,e,n)=>{n.d(e,{A:()=>a});const a=(0,n(3797).A)("ShieldAlert",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z",key:"3xmgem"}],["path",{d:"M12 8v4",key:"1got3b"}],["path",{d:"M12 16h.01",key:"1drbdi"}]])},9445:(t,e,n)=>{n.d(e,{A:()=>a});const a=(0,n(3797).A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},9693:(t,e,n)=>{n.d(e,{A:()=>w});var a=n(2239),r=n(6260),o=n(3666);function i(t,e){(0,o.A)(2,arguments);var n=(0,r.A)(t),a=(0,r.A)(e),i=n.getTime()-a.getTime();return i<0?-1:i>0?1:i}function u(t){(0,o.A)(1,arguments);var e=(0,r.A)(t);return function(t){(0,o.A)(1,arguments);var e=(0,r.A)(t);return e.setHours(23,59,59,999),e}(e).getTime()===function(t){(0,o.A)(1,arguments);var e=(0,r.A)(t),n=e.getMonth();return e.setFullYear(e.getFullYear(),n+1,0),e.setHours(23,59,59,999),e}(e).getTime()}function s(t,e){(0,o.A)(2,arguments);var n,a=(0,r.A)(t),s=(0,r.A)(e),d=i(a,s),l=Math.abs(function(t,e){(0,o.A)(2,arguments);var n=(0,r.A)(t),a=(0,r.A)(e);return 12*(n.getFullYear()-a.getFullYear())+(n.getMonth()-a.getMonth())}(a,s));if(l<1)n=0;else{1===a.getMonth()&&a.getDate()>27&&a.setDate(30),a.setMonth(a.getMonth()-d*l);var h=i(a,s)===-d;u((0,r.A)(t))&&1===l&&1===i(t,s)&&(h=!1),n=d*(l-Number(h))}return 0===n?0:n}var d={ceil:Math.ceil,round:Math.round,floor:Math.floor,trunc:function(t){return t<0?Math.ceil(t):Math.floor(t)}},l="trunc";function h(t,e,n){(0,o.A)(2,arguments);var a,i=function(t,e){return(0,o.A)(2,arguments),(0,r.A)(t).getTime()-(0,r.A)(e).getTime()}(t,e)/1e3;return((a=null===n||void 0===n?void 0:n.roundingMethod)?d[a]:d[l])(i)}var m=n(1144);function f(t,e){if(null==t)throw new TypeError("assign requires that input parameter not be null or undefined");for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}var c=n(4967),g=1440,v=2520,y=43200,b=86400;function p(t,e,n){var u,d;(0,o.A)(2,arguments);var l=(0,a.q)(),p=null!==(u=null!==(d=null===n||void 0===n?void 0:n.locale)&&void 0!==d?d:l.locale)&&void 0!==u?u:m.A;if(!p.formatDistance)throw new RangeError("locale must contain formatDistance property");var w=i(t,e);if(isNaN(w))throw new RangeError("Invalid time value");var M,A,D=f(f({},n),{addSuffix:Boolean(null===n||void 0===n?void 0:n.addSuffix),comparison:w});w>0?(M=(0,r.A)(e),A=(0,r.A)(t)):(M=(0,r.A)(t),A=(0,r.A)(e));var S,k=h(A,M),W=((0,c.A)(A)-(0,c.A)(M))/1e3,P=Math.round((k-W)/60);if(P<2)return null!==n&&void 0!==n&&n.includeSeconds?k<5?p.formatDistance("lessThanXSeconds",5,D):k<10?p.formatDistance("lessThanXSeconds",10,D):k<20?p.formatDistance("lessThanXSeconds",20,D):k<40?p.formatDistance("halfAMinute",0,D):k<60?p.formatDistance("lessThanXMinutes",1,D):p.formatDistance("xMinutes",1,D):0===P?p.formatDistance("lessThanXMinutes",1,D):p.formatDistance("xMinutes",P,D);if(P<45)return p.formatDistance("xMinutes",P,D);if(P<90)return p.formatDistance("aboutXHours",1,D);if(P<g){var T=Math.round(P/60);return p.formatDistance("aboutXHours",T,D)}if(P<v)return p.formatDistance("xDays",1,D);if(P<y){var x=Math.round(P/g);return p.formatDistance("xDays",x,D)}if(P<b)return S=Math.round(P/y),p.formatDistance("aboutXMonths",S,D);if((S=s(A,M))<12){var C=Math.round(P/y);return p.formatDistance("xMonths",C,D)}var j=S%12,X=Math.floor(S/12);return j<3?p.formatDistance("aboutXYears",X,D):j<9?p.formatDistance("overXYears",X,D):p.formatDistance("almostXYears",X+1,D)}function w(t,e){return(0,o.A)(1,arguments),p(t,Date.now(),e)}}}]);
//# sourceMappingURL=839.a4ee6f1c.chunk.js.map