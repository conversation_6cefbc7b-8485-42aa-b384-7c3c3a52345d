const { MongoClient, ObjectId } = require('mongodb');
const { faker } = require('@faker-js/faker');
const crypto = require('crypto');
const axios = require('axios');
const { expect } = require('chai');
// Use the correct path for the backend application
const app = require('../backend/app');
const { connectDB } = require('../backend/config/db'); // Updated to use the correct db.js file

// Test configuration
const TEST_PORT = 3001;
const BASE_URL = `http://localhost:${TEST_PORT}`;
const MONGODB_URI = 'mongodb://localhost:27017/payment-gateway-test';

// Test data
let testMerchant;
let testWebhook;
let server;

// Helper function to generate a webhook signature
function generateSignature(secret, payload, timestamp) {
  const hmac = crypto.createHmac('sha256', secret);
  const signature = hmac.update(`${timestamp}.${JSON.stringify(payload)}`).digest('hex');
  return `t=${timestamp},v1=${signature}`;
}

describe('Webhook Integration Tests', function() {
  this.timeout(10000); // Increase timeout for async tests

  before(async () => {
    // Start the test server
    server = app.listen(TEST_PORT, () => {
      console.log(`Test server running on port ${TEST_PORT}`);
    });

    // Set up test database
    const client = new MongoClient(MONGODB_URI);
    await client.connect();
    const db = client.db();

    // Create test merchant
    testMerchant = {
      _id: new ObjectId(),
      merchantId: `test_merchant_${Date.now()}`,
      businessName: 'Test Merchant',
      email: `test.${Date.now()}@example.com`,
      status: 'active',
      role: 'merchant',
      password: 'testpassword',
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    // Generate auth token for the test merchant
    const jwt = require('jsonwebtoken');
    testMerchant.token = jwt.sign(
      { userId: testMerchant._id, role: 'merchant' },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '1h' }
    );

    // Create test webhook
    testWebhook = {
      webhookId: `wh_${crypto.randomBytes(8).toString('hex')}`,
      merchantId: testMerchant._id,
      url: 'https://webhook.site/test-webhook',
      secret: 'test_secret_key',
      events: ['payment.succeeded', 'payment.failed'],
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Insert test data
    await db.collection('merchants').insertOne(testMerchant);
    await db.collection('webhooks').insertOne(testWebhook);

    // Close the connection
    await client.close();
  });

  after(async () => {
    // Clean up test data
    const client = new MongoClient(MONGODB_URI);
    await client.connect();
    const db = client.db();

    await db.collection('merchants').deleteOne({ _id: testMerchant._id });
    await db.collection('webhooks').deleteOne({ webhookId: testWebhook.webhookId });
    await db.collection('webhook_events').deleteMany({});
    await db.collection('webhooklogs').deleteMany({});

    await client.close();
    
    // Close the test server
    if (server) {
      server.close();
    }
  });

  describe('Webhook Processing', () => {
    it('should process a valid webhook event', async () => {
      const event = {
        type: 'payment.succeeded',
        data: {
          id: `evt_${crypto.randomBytes(8).toString('hex')}`,
          amount: 1000,
          currency: 'INR',
          status: 'succeeded',
          customer: {
            email: '<EMAIL>',
            name: 'Test Customer'
          },
          metadata: {
            orderId: 'order_123',
            reference: 'ref_456'
          }
        }
      };

      const timestamp = Math.floor(Date.now() / 1000);
      const signature = generateSignature(testWebhook.secret, event, timestamp);

      const response = await axios.post(
        `${BASE_URL}/api/webhooks/${testWebhook.webhookId}`,
        event,
        {
          headers: {
            'Content-Type': 'application/json',
            'X-Webhook-Signature': signature
          }
        }
      );

      expect(response.status).to.equal(202);
      expect(response.data).to.have.property('success', true);
      expect(response.data).to.have.property('eventId');
      expect(response.data).to.have.property('status', 'received');
    });

    it('should reject webhook with invalid signature', async () => {
      const event = {
        type: 'payment.failed',
        data: {
          id: `evt_${crypto.randomBytes(8).toString('hex')}`,
          amount: 500,
          currency: 'INR',
          status: 'failed',
          failure_code: 'insufficient_funds',
          failure_message: 'Insufficient funds'
        }
      };

      // Generate signature with wrong secret
      const timestamp = Math.floor(Date.now() / 1000);
      const signature = generateSignature('wrong_secret_key', event, timestamp);

      try {
        await axios.post(
          `${BASE_URL}/api/webhooks/${testWebhook.webhookId}`,
          event,
          {
            headers: {
              'Content-Type': 'application/json',
              'X-Webhook-Signature': signature
            }
          }
        );
        throw new Error('Should have thrown an error');
      } catch (error) {
        expect(error.response.status).to.equal(401);
        expect(error.response.data).to.have.property('success', false);
        expect(error.response.data.error).to.have.property('code', 'WEBHOOK_ERROR');
      }
    });
  });

  describe('Webhook Event Management', () => {
    let testEvent;

    before(async () => {
      // Create a test event
      const db = await getDatabase();
      testEvent = {
        eventId: `evt_${crypto.randomBytes(8).toString('hex')}`,
        webhookId: testWebhook.webhookId,
        merchantId: testMerchant._id,
        type: 'payment.succeeded',
        payload: {
          id: `py_${crypto.randomBytes(8).toString('hex')}`,
          amount: 2000,
          currency: 'INR',
          status: 'succeeded'
        },
        status: 'delivered',
        receivedAt: new Date(),
        deliveredAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      await db.collection('webhook_events').insertOne(testEvent);
    });

    it('should list webhook events', async () => {
      const response = await axios.get(
        `${BASE_URL}/api/webhooks/events`,
        {
          headers: {
            'Authorization': `Bearer ${testMerchant.token}`
          }
        }
      );

      expect(response.status).to.equal(200);
      expect(response.data).to.have.property('success', true);
      expect(response.data.data).to.be.an('array');
      expect(response.data.pagination).to.have.property('total');
    });

    it('should get webhook event details', async () => {
      const response = await axios.get(
        `${BASE_URL}/api/merchant/webhooks/events/${testEvent.eventId}`,
        {
          headers: {
            'Authorization': `Bearer test_api_key`,
            'X-Merchant-ID': testMerchant.merchantId
          }
        }
      );

      expect(response.status).to.equal(200);
      expect(response.data).to.have.property('success', true);
      expect(response.data.data).to.have.property('eventId', testEvent.eventId);
      expect(response.data.data).to.have.property('type', testEvent.type);
    });
  });
});
