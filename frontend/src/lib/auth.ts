const TOKEN_KEY = 'auth_token';

export const getToken = (): string | null => {
  if (typeof window === 'undefined') return null;
  return localStorage.getItem(TOKEN_KEY);
};

export const setToken = (token: string): void => {
  if (typeof window !== 'undefined') {
    localStorage.setItem(TOKEN_KEY, token);
  }
};

export const clearToken = (): void => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem(TOKEN_KEY);
  }
};

export const isAuthenticated = (): boolean => {
  return !!getToken();
};

export const getUserRole = (): string | null => {
  if (typeof window === 'undefined') return null;
  const token = getToken();
  if (!token) return null;
  
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const payload = JSON.parse(window.atob(base64));
    return payload.role || null;
  } catch (error) {
    console.error('Error decoding token:', error);
    return null;
  }
};

export const checkAuth = (): boolean => {
  const isAuth = isAuthenticated();
  if (!isAuth) {
    window.location.href = '/login';
    return false;
  }
  return true;
};

export const checkRole = (allowedRoles: string[]): boolean => {
  const role = getUserRole();
  if (!role || !allowedRoles.includes(role)) {
    window.location.href = '/unauthorized';
    return false;
  }
  return true;
};
