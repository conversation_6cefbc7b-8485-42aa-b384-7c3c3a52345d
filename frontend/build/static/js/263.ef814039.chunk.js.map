{"version": 3, "file": "static/js/263.ef814039.chunk.js", "mappings": "mLAGA,SAASA,EAAQC,GAGyB,IAHxB,UAChBC,KACGC,GACkCF,EACrC,OACEG,EAAAA,EAAAA,KAAA,OACEF,WAAWG,EAAAA,EAAAA,IAAG,wDAAyDH,MACnEC,GAGV,C,iFCVA,MAAMG,E,QAAqBC,EAAMC,OAAO,CACtCC,QAASC,4BACTC,QAAS,CACP,eAAgB,oBAElBC,iBAAiB,IAInBN,EAAIO,aAAaC,QAAQC,IACtBC,IACC,MAAMC,EAAQC,aAAaC,QAAQ,SAInC,OAHIF,IACFD,EAAOL,QAAQS,cAAgB,UAAUH,KAEpCD,GAERK,GACQC,QAAQC,OAAOF,IAK1Bf,EAAIO,aAAaW,SAAST,IACvBS,GAAaA,EACbH,IACKA,EAAMG,UAEsB,MAA1BH,EAAMG,SAASC,SAEjBP,aAAaQ,WAAW,SACxBC,OAAOC,SAASC,KAAO,UAIpBP,QAAQC,OAAOF,KAKnB,MAAMS,EACNC,CAAcC,EAAahB,IAC9BV,EAAIyB,IAAOC,EAAKhB,GCfPiB,EAAiBC,gBACLJ,EAAkC,cACzCK,KAAKA,K,wJCjBvB,MAAMC,EAAoBF,SAEjB,CACL,CACEG,GAAI,IACJC,OAAQ,QACRC,YAAa,sDACbC,WAAW,IAAIC,MAAOC,cACtBjB,OAAQ,UACRkB,GAAI,cACJC,UAAW,uHAEb,CACEP,GAAI,IACJC,OAAQ,kBACRC,YAAa,mBACbC,UAAW,IAAIC,KAAKA,KAAKI,MAAQ,MAAoBH,cACrDjB,OAAQ,WAEV,CACEY,GAAI,IACJC,OAAQ,gBACRC,YAAa,4CACbC,UAAW,IAAIC,KAAKA,KAAKI,MAAQ,OAAqBH,cACtDjB,OAAQ,SACRkB,GAAI,YAEN,CACEN,GAAI,IACJC,OAAQ,cACRC,YAAa,oCACbC,UAAW,IAAIC,KAAKA,KAAKI,MAAQ,QAAyBH,cAC1DjB,OAAQ,YAORqB,EAAiBR,IACrB,OAAQA,GACN,IAAK,QACH,OAAOlC,EAAAA,EAAAA,KAAC2C,EAAAA,EAAK,CAAC7C,UAAU,YAC1B,IAAK,SACH,OAAOE,EAAAA,EAAAA,KAAC4C,EAAAA,EAAM,CAAC9C,UAAU,YAC3B,IAAK,kBACL,IAAK,cACH,OAAOE,EAAAA,EAAAA,KAAC6C,EAAAA,EAAW,CAAC/C,UAAU,YAChC,QACE,OAAOE,EAAAA,EAAAA,KAAC8C,EAAAA,EAAW,CAAChD,UAAU,cAI7B,SAASiD,IACd,MAAQhB,KAAMiB,EAAY,UAAEC,EAAS,MAAEhC,IAAUiC,EAAAA,EAAAA,GAAwB,CACvEC,SAAU,CAAC,gBACXC,QAASpB,IAGLqB,EAAOL,GAAgB,GA0B7B,OAAIC,GAEAjD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCwD,UACpDtD,EAAAA,EAAAA,KAACuD,EAAAA,EAAO,CAACzD,UAAU,iDAKrBmB,GAEAjB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2BAA0BwD,UACvCE,EAAAA,EAAAA,MAAA,OAAK1D,UAAU,OAAMwD,SAAA,EACnBtD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gBAAewD,UAC5BtD,EAAAA,EAAAA,KAAC8C,EAAAA,EAAW,CAAChD,UAAU,uBAAuB,cAAY,YAE5D0D,EAAAA,EAAAA,MAAA,OAAK1D,UAAU,OAAMwD,SAAA,EACnBtD,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mCAAkCwD,SAAC,kCAGjDtD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,4BAA2BwD,UACxCtD,EAAAA,EAAAA,KAAA,KAAAsD,SAAG,sFAQK,IAAhBD,EAAKI,QAELzD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oBAAmBwD,UAChCtD,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wBAAuBwD,SAAC,+BAMzCtD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWwD,UACxBtD,EAAAA,EAAAA,KAAA,MAAI0D,KAAK,OAAO5D,UAAU,QAAOwD,SAC9BD,EAAKM,IAAI,CAACC,EAAkBC,KAC3B7D,SAAAA,EAAAA,KAAA,MAAAsD,UACEE,EAAAA,EAAAA,MAAA,OAAK1D,UAAU,gBAAewD,SAAA,CAC3BO,IAAWR,EAAKI,OAAS,GACxBzD,EAAAA,EAAAA,KAAA,QACEF,UAAU,wDACV,cAAY,SAEZ,MACJ0D,EAAAA,EAAAA,MAAA,OAAK1D,UAAU,0BAAyBwD,SAAA,EACtCtD,EAAAA,EAAAA,KAAA,OAAAsD,UACEtD,EAAAA,EAAAA,KAAA,QACEF,UAAW,4EACM,YAAf8D,EAAIvC,OAAuB,8BACZ,WAAfuC,EAAIvC,OAAsB,0BAC1B,iCACCiC,SAEFZ,EAAckB,EAAI1B,aAGvBsB,EAAAA,EAAAA,MAAA,OAAK1D,UAAU,uDAAsDwD,SAAA,EACnEE,EAAAA,EAAAA,MAAA,OAAAF,SAAA,EACEE,EAAAA,EAAAA,MAAA,KAAG1D,UAAU,wBAAuBwD,SAAA,CACjCM,EAAIzB,YACJyB,EAAIrB,KACHiB,EAAAA,EAAAA,MAAA,QAAM1D,UAAU,qCAAoCwD,SAAA,CAAC,IACjDM,EAAIrB,GAAG,UAIdqB,EAAIpB,YACHxC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qCAAoCwD,SAC9CM,EAAIpB,gBAIXgB,EAAAA,EAAAA,MAAA,OAAK1D,UAAU,6DAA4DwD,SAAA,EACzEtD,EAAAA,EAAAA,KAAA,QAAM8D,SAAUF,EAAIxB,UAAUkB,UAC3BS,EAAAA,EAAAA,GAAoB,IAAI1B,KAAKuB,EAAIxB,WAAY,CAAE4B,WAAW,OAE7DhE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,OAAMwD,UACnBE,EAAAA,EAAAA,MAAA,QACE1D,UAAW,4EACM,YAAf8D,EAAIvC,OAAuB,8BACZ,WAAfuC,EAAIvC,OAAsB,0BAC1B,iCACCiC,SAAA,EAjGFjC,EAmGcuC,EAAIvC,OAjGhC,YADCA,GAEGrB,EAAAA,EAAAA,KAACiE,EAAAA,EAAY,CAACnE,UAAU,aAIxBE,EAAAA,EAAAA,KAAC8C,EAAAA,EAAW,CAAChD,UAAU,cA6FdE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,kBAAiBwD,SAC9BM,EAAIvC,2BAlDZuC,EAAI3B,IAnDEZ,WAkHzB,CC9Le,SAAS6C,IACtB,MAAQC,KAAMC,IAAgBC,EAAAA,EAAAA,MACvBC,EAAWC,IAAgBC,EAAAA,EAAAA,UAAS,YAEnCzC,KAAMoC,EAAI,UAAElB,EAAS,MAAEhC,EAAK,QAAEwD,IAAYvB,EAAAA,EAAAA,GAAS,CACzDC,SAAU,CAAC,eACXC,QAASvB,EACT6C,UAAWN,IAUb,OAPAO,EAAAA,EAAAA,WAAU,KAEU,YAAdL,GACFG,KAED,CAACH,EAAWG,IAEXxB,GAEAO,EAAAA,EAAAA,MAAA,OAAK1D,UAAU,mCAAkCwD,SAAA,EAC/CtD,EAAAA,EAAAA,KAACJ,EAAAA,EAAQ,CAACE,UAAU,oBACpBE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,aAAYwD,UACzBtD,EAAAA,EAAAA,KAACJ,EAAAA,EAAQ,CAACE,UAAU,qBAMxBmB,GAEAjB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yBAAwBwD,UACrCtD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8DAA6DwD,SAAC,sDAO9Ea,GAWHX,EAAAA,EAAAA,MAAA,OAAK1D,UAAU,mCAAkCwD,SAAA,EAC/CE,EAAAA,EAAAA,MAAA,OAAK1D,UAAU,YAAWwD,SAAA,EACxBtD,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oCAAmCwD,SAAC,sBAClDtD,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wBAAuBwD,SAAC,qDAKvCE,EAAAA,EAAAA,MAACoB,EAAAA,GAAI,CACHC,aAAa,UACbC,MAAOR,EACPS,cAAeR,EACfzE,UAAU,YAAWwD,SAAA,EAErBE,EAAAA,EAAAA,MAACwB,EAAAA,GAAQ,CAAClF,UAAU,kDAAiDwD,SAAA,EACnEtD,EAAAA,EAAAA,KAACiF,EAAAA,GAAW,CAACH,MAAM,UAASxB,SAAC,aAC7BtD,EAAAA,EAAAA,KAACiF,EAAAA,GAAW,CAACH,MAAM,WAAUxB,SAAC,cAC9BtD,EAAAA,EAAAA,KAACiF,EAAAA,GAAW,CAACH,MAAM,WAAUxB,SAAC,cAC9BtD,EAAAA,EAAAA,KAACiF,EAAAA,GAAW,CAACH,MAAM,WAAUxB,SAAC,iBAGhCtD,EAAAA,EAAAA,KAACkF,EAAAA,GAAW,CAACJ,MAAM,UAASxB,UAC1BE,EAAAA,EAAAA,MAAC2B,EAAAA,GAAI,CAAA7B,SAAA,EACHE,EAAAA,EAAAA,MAAC4B,EAAAA,GAAU,CAAA9B,SAAA,EACTtD,EAAAA,EAAAA,KAACqF,EAAAA,GAAS,CAAA/B,SAAC,yBACXtD,EAAAA,EAAAA,KAACsF,EAAAA,GAAe,CAAAhC,SAAC,qEAInBtD,EAAAA,EAAAA,KAACuF,EAAAA,GAAW,CAAAjC,UACVtD,EAAAA,EAAAA,KAACwF,EAAAA,EAAW,CACVrB,KAAMA,EACNsB,UAAWA,IAAMhB,cAMzBzE,EAAAA,EAAAA,KAACkF,EAAAA,GAAW,CAACJ,MAAM,WAAUxB,UAC3BE,EAAAA,EAAAA,MAAA,OAAK1D,UAAU,YAAWwD,SAAA,EACxBE,EAAAA,EAAAA,MAAC2B,EAAAA,GAAI,CAAA7B,SAAA,EACHE,EAAAA,EAAAA,MAAC4B,EAAAA,GAAU,CAAA9B,SAAA,EACTtD,EAAAA,EAAAA,KAACqF,EAAAA,GAAS,CAAA/B,SAAC,qBACXtD,EAAAA,EAAAA,KAACsF,EAAAA,GAAe,CAAAhC,SAAC,6EAInBtD,EAAAA,EAAAA,KAACuF,EAAAA,GAAW,CAAAjC,UACVtD,EAAAA,EAAAA,KAAC0F,EAAAA,EAAkB,UAIvBlC,EAAAA,EAAAA,MAAC2B,EAAAA,GAAI,CAAA7B,SAAA,EACHE,EAAAA,EAAAA,MAAC4B,EAAAA,GAAU,CAAA9B,SAAA,EACTtD,EAAAA,EAAAA,KAACqF,EAAAA,GAAS,CAAA/B,SAAC,+BACXtD,EAAAA,EAAAA,KAACsF,EAAAA,GAAe,CAAAhC,SAAC,wFAInBtD,EAAAA,EAAAA,KAACuF,EAAAA,GAAW,CAAAjC,UACVtD,EAAAA,EAAAA,KAAC2F,EAAAA,EAAa,CAACC,iBAAkBzB,EAAKyB,8BAM9C5F,EAAAA,EAAAA,KAACkF,EAAAA,GAAW,CAACJ,MAAM,WAAUxB,UAC3BE,EAAAA,EAAAA,MAAC2B,EAAAA,GAAI,CAAA7B,SAAA,EACHE,EAAAA,EAAAA,MAAC4B,EAAAA,GAAU,CAAA9B,SAAA,EACTtD,EAAAA,EAAAA,KAACqF,EAAAA,GAAS,CAAA/B,SAAC,cACXtD,EAAAA,EAAAA,KAACsF,EAAAA,GAAe,CAAAhC,SAAC,sEAInBtD,EAAAA,EAAAA,KAACuF,EAAAA,GAAW,CAAAjC,UACVtD,EAAAA,EAAAA,KAAC6F,EAAAA,EAAO,YAKd7F,EAAAA,EAAAA,KAACkF,EAAAA,GAAW,CAACJ,MAAM,WAAUxB,UAC3BE,EAAAA,EAAAA,MAAC2B,EAAAA,GAAI,CAAA7B,SAAA,EACHE,EAAAA,EAAAA,MAAC4B,EAAAA,GAAU,CAAA9B,SAAA,EACTtD,EAAAA,EAAAA,KAACqF,EAAAA,GAAS,CAAA/B,SAAC,kBACXtD,EAAAA,EAAAA,KAACsF,EAAAA,GAAe,CAAAhC,SAAC,8CAInBtD,EAAAA,EAAAA,KAACuF,EAAAA,GAAW,CAAAjC,UACVtD,EAAAA,EAAAA,KAAC+C,EAAW,kBAlGpB/C,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yBAAwBwD,UACrCtD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oEAAmEwD,SAAC,yCAwG3F,C", "sources": ["components/ui/skeleton.tsx", "utils/api.ts", "services/userService.ts", "components/profile/ActivityLog.tsx", "pages/Profile.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"../../lib/utils\"\n\nfunction Skeleton({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) {\n  return (\n    <div\n      className={cn(\"animate-pulse rounded-md bg-gray-100 dark:bg-gray-800\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Skeleton }\n", "import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';\n\n// Create an axios instance with default config\nconst api: AxiosInstance = axios.create({\n  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api/v1',\n  headers: {\n    'Content-Type': 'application/json',\n  },\n  withCredentials: true,\n});\n\n// Request interceptor to add auth token to requests\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor to handle common errors\napi.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    if (error.response) {\n      // Handle specific status codes\n      if (error.response.status === 401) {\n        // Handle unauthorized access (e.g., redirect to login)\n        localStorage.removeItem('token');\n        window.location.href = '/login';\n      }\n      // You can add more specific error handling here\n    }\n    return Promise.reject(error);\n  }\n);\n\n// Helper function to make API requests with proper typing\nexport const apiRequest = {\n  get: <T = unknown>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> => \n    api.get<T>(url, config),\n  \n  post: <T = unknown>(\n    url: string, \n    data?: unknown, \n    config?: AxiosRequestConfig\n  ): Promise<AxiosResponse<T>> => \n    api.post<T>(url, data, config),\n  \n  put: <T = unknown>(\n    url: string, \n    data?: unknown, \n    config?: AxiosRequestConfig\n  ): Promise<AxiosResponse<T>> => \n    api.put<T>(url, data, config),\n  \n  delete: <T = unknown>(\n    url: string, \n    config?: AxiosRequestConfig\n  ): Promise<AxiosResponse<T>> => \n    api.delete<T>(url, config),\n  \n  patch: <T = unknown>(\n    url: string, \n    data?: unknown, \n    config?: AxiosRequestConfig\n  ): Promise<AxiosResponse<T>> => \n    api.patch<T>(url, data, config),\n};\n\nexport default api;\n", "import { apiRequest } from '../utils/api';\nimport { User } from '../contexts/AuthContext';\n\ninterface ActivityLog {\n  id: string;\n  action: string;\n  timestamp: string;\n  ipAddress: string;\n  userAgent: string;\n  metadata?: Record<string, unknown>;\n}\n\ninterface PaginationMeta {\n  total: number;\n  page: number;\n  limit: number;\n  totalPages: number;\n}\n\ninterface ApiResponse<T> {\n  success: boolean;\n  data: T;\n  message?: string;\n  error?: string;\n  code?: number;\n}\n\n/**\n * Fetches the current user's profile data\n */\nexport const getCurrentUser = async (): Promise<User> => {\n  const response = await apiRequest.get<ApiResponse<User>>('/users/me');\n  return response.data.data;\n};\n\n/**\n * Updates the current user's profile\n */\nexport const updateProfile = async (data: Partial<User>): Promise<User> => {\n  const response = await apiRequest.patch<ApiResponse<User>>('/users/me', data);\n  return response.data.data;\n};\n\n/**\n * Changes the user's password\n */\nexport const changePassword = async (data: {\n  currentPassword: string;\n  newPassword: string;\n}): Promise<{ success: boolean; message: string }> => {\n  const response = await apiRequest.post<ApiResponse<{ success: boolean; message: string }>>(\n    '/auth/change-password',\n    data\n  );\n  return response.data.data;\n};\n\n/**\n * Enables or disables two-factor authentication\n */\nexport const toggleTwoFactorAuth = async (enabled: boolean): Promise<{ success: boolean; message: string }> => {\n  const response = await apiRequest.post<ApiResponse<{ success: boolean; message: string }>>(\n    '/auth/two-factor',\n    { enabled }\n  );\n  return response.data.data;\n};\n\n/**\n * Generates a new API key\n */\nexport const generateApiKey = async (name: string): Promise<{ key: string }> => {\n  const response = await apiRequest.post<ApiResponse<{ key: string }>>('/api-keys', { name });\n  return response.data.data;\n};\n\n/**\n * Deletes an API key\n */\nexport const deleteApiKey = async (id: string): Promise<{ success: boolean }> => {\n  const response = await apiRequest.delete<ApiResponse<{ success: boolean }>>(`/api-keys/${id}`);\n  return response.data.data;\n};\n\n/**\n * Fetches the user's activity log\n */\nexport const getActivityLog = async (params: {\n  page?: number;\n  limit?: number;\n} = {}): Promise<{ data: ActivityLog[]; pagination: PaginationMeta }> => {\n  const response = await apiRequest.get<ApiResponse<{ data: ActivityLog[]; pagination: PaginationMeta }>>(\n    '/users/activity',\n    { params }\n  );\n  return response.data.data;\n};\n", "import { useQuery } from '@tanstack/react-query';\nimport { formatDistanceToNow } from 'date-fns';\nimport { Loader2, AlertCircle, CheckCircle2, ShieldAlert, LogIn, LogOut } from 'lucide-react';\n\ntype ActivityLog = {\n  id: string;\n  action: string;\n  description: string;\n  timestamp: string;\n  status: 'success' | 'failed' | 'warning';\n  ip?: string;\n  userAgent?: string;\n};\n\n// Mock API service function - replace with actual implementation\nconst fetchActivityLogs = async (): Promise<ActivityLog[]> => {\n  // Replace with actual API call\n  return [\n    {\n      id: '1',\n      action: 'login',\n      description: 'Successful login from *********** (Chrome, Windows)',\n      timestamp: new Date().toISOString(),\n      status: 'success' as const,\n      ip: '***********',\n      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',\n    },\n    {\n      id: '2',\n      action: 'password_change',\n      description: 'Password changed',\n      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),\n      status: 'success' as const,\n    },\n    {\n      id: '3',\n      action: 'login_attempt',\n      description: 'Failed login <NAME_EMAIL>',\n      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(),\n      status: 'failed' as const,\n      ip: '********',\n    },\n    {\n      id: '4',\n      action: '2fa_enabled',\n      description: 'Two-factor authentication enabled',\n      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2).toISOString(),\n      status: 'success' as const,\n    },\n  ];\n};\n\n\n\nconst getActionIcon = (action: string) => {\n  switch (action) {\n    case 'login':\n      return <LogIn className=\"h-4 w-4\" />;\n    case 'logout':\n      return <LogOut className=\"h-4 w-4\" />;\n    case 'password_change':\n    case '2fa_enabled':\n      return <ShieldAlert className=\"h-4 w-4\" />;\n    default:\n      return <AlertCircle className=\"h-4 w-4\" />;\n  }\n};\n\nexport function ActivityLog() {\n  const { data: activityLogs, isLoading, error } = useQuery<ActivityLog[]>({\n    queryKey: ['activityLogs'],\n    queryFn: fetchActivityLogs,\n  });\n  \n  const logs = activityLogs || [];\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'success':\n        return 'bg-green-100 text-green-800';\n      case 'failed':\n        return 'bg-red-100 text-red-800';\n      case 'warning':\n        return 'bg-yellow-100 text-yellow-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'success':\n        return <CheckCircle2 className=\"h-4 w-4\" />;\n      case 'failed':\n        return <AlertCircle className=\"h-4 w-4\" />;\n      default:\n        return <AlertCircle className=\"h-4 w-4\" />;\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"flex justify-center items-center h-32\">\n        <Loader2 className=\"h-6 w-6 animate-spin text-muted-foreground\" />\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"rounded-md bg-red-50 p-4\">\n        <div className=\"flex\">\n          <div className=\"flex-shrink-0\">\n            <AlertCircle className=\"h-5 w-5 text-red-400\" aria-hidden=\"true\" />\n          </div>\n          <div className=\"ml-3\">\n            <h3 className=\"text-sm font-medium text-red-800\">\n              Failed to load activity logs\n            </h3>\n            <div className=\"mt-2 text-sm text-red-700\">\n              <p>Unable to load your activity logs at this time. Please try again later.</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  if (logs.length === 0) {\n    return (\n      <div className=\"text-center py-12\">\n        <p className=\"text-muted-foreground\">No activity logs found.</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"flow-root\">\n      <ul role=\"list\" className=\"-mb-8\">\n        {logs.map((log: ActivityLog, logIdx: number) => (\n          <li key={log.id}>\n            <div className=\"relative pb-8\">\n              {logIdx !== logs.length - 1 ? (\n                <span\n                  className=\"absolute left-4 top-4 -ml-px h-full w-0.5 bg-gray-200\"\n                  aria-hidden=\"true\"\n                />\n              ) : null}\n              <div className=\"relative flex space-x-3\">\n                <div>\n                  <span\n                    className={`h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white ${\n                      log.status === 'success' ? 'bg-green-100 text-green-700' :\n                      log.status === 'failed' ? 'bg-red-100 text-red-700' :\n                      'bg-yellow-100 text-yellow-700'\n                    }`}\n                  >\n                    {getActionIcon(log.action)}\n                  </span>\n                </div>\n                <div className=\"flex min-w-0 flex-1 justify-between space-x-4 pt-1.5\">\n                  <div>\n                    <p className=\"text-sm text-gray-800\">\n                      {log.description}\n                      {log.ip && (\n                        <span className=\"text-xs text-muted-foreground ml-1\">\n                          ({log.ip})\n                        </span>\n                      )}\n                    </p>\n                    {log.userAgent && (\n                      <p className=\"text-xs text-muted-foreground mt-1\">\n                        {log.userAgent}\n                      </p>\n                    )}\n                  </div>\n                  <div className=\"whitespace-nowrap text-right text-sm text-muted-foreground\">\n                    <time dateTime={log.timestamp}>\n                      {formatDistanceToNow(new Date(log.timestamp), { addSuffix: true })}\n                    </time>\n                    <div className=\"mt-1\">\n                      <span\n                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                          log.status === 'success' ? 'bg-green-100 text-green-800' :\n                          log.status === 'failed' ? 'bg-red-100 text-red-800' :\n                          'bg-yellow-100 text-yellow-800'\n                        }`}\n                      >\n                        {getStatusIcon(log.status)}\n                        <span className=\"ml-1 capitalize\">\n                          {log.status}\n                        </span>\n                      </span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </li>\n        ))}\n      </ul>\n    </div>\n  );\n}\n", "import { useState, useEffect } from 'react';\nimport { useQuery } from '@tanstack/react-query';\nimport { useAuth } from '../hooks/useAuth';\nimport { getCurrentUser } from '../services/userService';\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';\nimport { Skeleton } from '../components/ui/skeleton';\nimport { ProfileForm } from '../components/profile/ProfileForm';\nimport { PasswordChangeForm } from '../components/profile/PasswordChangeForm';\nimport { TwoFactorAuth } from '../components/profile/TwoFactorAuth';\nimport { ApiKeys } from '../components/profile/ApiKeys';\nimport { ActivityLog } from '../components/profile/ActivityLog';\n\nexport default function ProfilePage() {\n  const { user: currentUser } = useAuth();\n  const [activeTab, setActiveTab] = useState('profile');\n\n  const { data: user, isLoading, error, refetch } = useQuery({\n    queryKey: ['currentUser'],\n    queryFn: getCurrentUser,\n    enabled: !!currentUser,\n  });\n\n  useEffect(() => {\n    // Refetch user data when the tab changes to ensure we have fresh data\n    if (activeTab !== 'profile') {\n      refetch();\n    }\n  }, [activeTab, refetch]);\n\n  if (isLoading) {\n    return (\n      <div className=\"container mx-auto py-8 space-y-8\">\n        <Skeleton className=\"h-10 w-64 mb-6\" />\n        <div className=\"grid gap-6\">\n          <Skeleton className=\"h-96 w-full\" />\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"container mx-auto py-8\">\n        <div className=\"rounded-lg border border-red-200 bg-red-50 p-4 text-red-700\">\n          Failed to load profile. Please try again later.\n        </div>\n      </div>\n    );\n  }\n\n  if (!user) {\n    return (\n      <div className=\"container mx-auto py-8\">\n        <div className=\"rounded-lg border border-amber-200 bg-amber-50 p-4 text-amber-700\">\n          Please log in to view your profile.\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container mx-auto py-8 space-y-8\">\n      <div className=\"space-y-2\">\n        <h1 className=\"text-3xl font-bold tracking-tight\">Profile Settings</h1>\n        <p className=\"text-muted-foreground\">\n          Manage your account settings and preferences\n        </p>\n      </div>\n\n      <Tabs \n        defaultValue=\"profile\" \n        value={activeTab} \n        onValueChange={setActiveTab}\n        className=\"space-y-6\"\n      >\n        <TabsList className=\"grid w-full grid-cols-2 md:grid-cols-4 lg:w-1/2\">\n          <TabsTrigger value=\"profile\">Profile</TabsTrigger>\n          <TabsTrigger value=\"security\">Security</TabsTrigger>\n          <TabsTrigger value=\"api-keys\">API Keys</TabsTrigger>\n          <TabsTrigger value=\"activity\">Activity</TabsTrigger>\n        </TabsList>\n\n        <TabsContent value=\"profile\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Profile Information</CardTitle>\n              <CardDescription>\n                Update your account's profile information and email address.\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <ProfileForm \n                user={user} \n                onSuccess={() => refetch()} \n              />\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"security\">\n          <div className=\"space-y-6\">\n            <Card>\n              <CardHeader>\n                <CardTitle>Change Password</CardTitle>\n                <CardDescription>\n                  Ensure your account is using a long, random password to stay secure.\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <PasswordChangeForm />\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader>\n                <CardTitle>Two-Factor Authentication</CardTitle>\n                <CardDescription>\n                  Add an extra layer of security to your account using two-factor authentication.\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <TwoFactorAuth twoFactorEnabled={user.twoFactorEnabled} />\n              </CardContent>\n            </Card>\n          </div>\n        </TabsContent>\n\n        <TabsContent value=\"api-keys\">\n          <Card>\n            <CardHeader>\n              <CardTitle>API Keys</CardTitle>\n              <CardDescription>\n                Manage your API keys for programmatic access to your account.\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <ApiKeys />\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"activity\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Activity Log</CardTitle>\n              <CardDescription>\n                View recent activity on your account.\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <ActivityLog />\n            </CardContent>\n          </Card>\n        </TabsContent>\n      </Tabs>\n    </div>\n  );\n}\n"], "names": ["Skeleton", "_ref", "className", "props", "_jsx", "cn", "api", "axios", "create", "baseURL", "process", "headers", "withCredentials", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "status", "removeItem", "window", "location", "href", "apiRequest", "get", "url", "getCurrentUser", "async", "data", "fetchActivityLogs", "id", "action", "description", "timestamp", "Date", "toISOString", "ip", "userAgent", "now", "getActionIcon", "LogIn", "LogOut", "<PERSON><PERSON><PERSON><PERSON>", "AlertCircle", "ActivityLog", "activityLogs", "isLoading", "useQuery", "query<PERSON><PERSON>", "queryFn", "logs", "children", "Loader2", "_jsxs", "length", "role", "map", "log", "logIdx", "dateTime", "formatDistanceToNow", "addSuffix", "CheckCircle2", "ProfilePage", "user", "currentUser", "useAuth", "activeTab", "setActiveTab", "useState", "refetch", "enabled", "useEffect", "Tabs", "defaultValue", "value", "onValueChange", "TabsList", "TabsTrigger", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "CardDescription", "<PERSON><PERSON><PERSON><PERSON>", "ProfileForm", "onSuccess", "PasswordChangeForm", "TwoFactorAuth", "twoFactorEnabled", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sourceRoot": ""}