#!/bin/bash

# Initialize test environment

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}🚀 Setting up Payment Gateway Test Environment...${NC}\n"

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo -e "❌ ${RED}Node.js is not installed. Please install Node.js v14 or higher and try again.${NC}"
    exit 1
fi

# Check npm version
NPM_VERSION=$(npm -v)
if [ "$(printf '%s\n' "7.0.0" "$NPM_VERSION" | sort -V | head -n1)" = "7.0.0" ]; then
    echo -e "✓ Using npm v${NPM_VERSION}"
else
    echo -e "⚠️  ${YELLOW}You're using an older version of npm (v${NPM_VERSION}). Consider upgrading to npm 7 or higher.${NC}"
fi

# Install dependencies
echo -e "\n📦 Installing dependencies..."
npm install

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo -e "\n🔧 Creating .env file..."
    cat > .env <<EOL
# Server Configuration
PORT=3001
NODE_ENV=test
JWT_SECRET=your_jwt_secret_key_here

# Database Configuration (if needed)
# MONGO_URI=mongodb://localhost:27017/payment-gateway-test

# Logging
LOG_LEVEL=debug

# API Configuration
API_PREFIX=/api
EOL
    echo -e "✓ .env file created"
fi

# Create logs directory if it doesn't exist
mkdir -p logs

echo -e "\n${GREEN}✅ Test environment setup complete!${NC}"
echo -e "\nTo start the mock server, run: ${YELLOW}npm run start-mock${NC}"
echo -e "To run tests, use: ${YELLOW}npm test${NC}\n"

exit 0
