const mongoose = require('mongoose');

const businessAddressSchema = new mongoose.Schema({
  street: String,
  city: String,
  state: String,
  country: { type: String, default: 'India' },
  postalCode: String
});

const bankAccountSchema = new mongoose.Schema({
  accountHolderName: String,
  accountNumber: String,
  bankName: String,
  ifscCode: String,
  accountType: { type: String, enum: ['savings', 'current'] }
});

const kycSchema = new mongoose.Schema({
  documentType: {
    type: String,
    enum: ['aadhaar', 'pan', 'passport', 'driving_license'],
    required: true
  },
  documentNumber: { type: String, required: true },
  documentFrontUrl: String,
  documentBackUrl: String,
  verified: { type: Boolean, default: false },
  verifiedAt: Date,
  rejectionReason: String
});

// Custom field schema for dynamic fields
const customFieldSchema = new mongoose.Schema({
  fieldName: { type: String, required: true },
  fieldType: {
    type: String,
    enum: ['text', 'number', 'email', 'phone', 'url', 'date', 'boolean', 'select'],
    default: 'text'
  },
  fieldValue: mongoose.Schema.Types.Mixed,
  isRequired: { type: Boolean, default: false },
  options: [String], // For select type fields
  addedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  addedAt: { type: Date, default: Date.now }
});

// Change log schema for tracking modifications
const changeLogSchema = new mongoose.Schema({
  field: { type: String, required: true },
  oldValue: mongoose.Schema.Types.Mixed,
  newValue: mongoose.Schema.Types.Mixed,
  changedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  changedAt: { type: Date, default: Date.now },
  reason: String, // Optional reason for the change
  changeType: {
    type: String,
    enum: ['create', 'update', 'delete', 'approve', 'reject', 'suspend', 'activate'],
    default: 'update'
  }
});

const merchantSchema = new mongoose.Schema({
  userId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User',
    required: true,
    unique: true
  },
  businessName: { type: String, required: true },
  businessType: { type: String, required: true },
  businessRegistrationNumber: { type: String, required: true },
  businessAddress: { type: businessAddressSchema, required: true },
  contactPersonName: { type: String, required: true },
  contactEmail: { type: String, required: true },
  contactPhone: { type: String, required: true },
  website: String,
  bankAccount: { type: bankAccountSchema, required: true },
  kyc: { type: kycSchema, required: true },
  status: {
    type: String,
    enum: ['draft', 'pending', 'verified', 'rejected', 'suspended'],
    default: 'draft'
  },
  rejectionReason: String,
  isActive: { type: Boolean, default: false },

  // Custom fields added by admins
  customFields: [customFieldSchema],

  // Change tracking
  changeLog: [changeLogSchema],

  // Additional metadata
  verificationStatus: {
    type: String,
    enum: ['pending', 'verified', 'rejected'],
    default: 'pending'
  },
  verifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  verifiedAt: Date,
  lastModifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, { timestamps: true });

module.exports = mongoose.model('Merchant', merchantSchema);
