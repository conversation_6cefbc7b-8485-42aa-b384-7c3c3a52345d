"use client"

import type React from "react"
import { create<PERSON>ontext, useContext, useState, useEffect, type ReactNode } from "react"
import axios from "axios"
import { useToast } from "../hooks/use-toast"
import { ToastAction } from "../components/ui/toast"
import { API_BASE_URL } from "../config"

// Base user properties that are common to all user types
export interface BaseUser {
  id: string
  _id?: string
  email: string
  name: string
  isVerified: boolean
  twoFactorEnabled: boolean
  avatar?: string
  lastLogin?: string
}

// Role-specific properties
export interface AdminUser extends BaseUser {
  role: "admin"
  permissions: string[]
}

export interface Merchant<PERSON>ser extends BaseUser {
  role: "merchant"
  merchantId: string
  businessName: string
  balance: number
  permissions?: string[]
}

export interface TraderUser extends BaseUser {
  role: "trader"
  traderId: string
  level: number
  totalTrades: number
  merchantId?: string
  permissions?: string[]
}

// Union type for all possible user types
export type User = AdminUser | MerchantUser | TraderUser;

interface RegisterData {
  email: string
  password: string
  name: string
  // Add other registration fields as needed
}

interface LoginResponse {
  requires2FA?: boolean;
  userId?: string;
  success?: boolean;
  error?: string;
}

interface AuthContextType {
  user: User | null
  login: (email: string, password: string) => Promise<LoginResponse>
  register: (userData: RegisterData) => Promise<void>
  logout: () => void
  loading: boolean
  error: string | null
  clearError: () => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = () => {
  const context = useContext(AuthContext)
  
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}

interface AuthProviderProps {
  children: ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { toast } = useToast()
  
  const clearError = () => setError(null)

  useEffect(() => {
    // Check for existing session
    const checkAuth = async () => {
      try {
        // Try to fetch the current user from the server
        const response = await axios.get(`${API_BASE_URL}/auth/me`, {
          withCredentials: true, // Important for sending cookies
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          }
        });
        
        if (response.data && response.data.user) {
          setUser(response.data.user);
        }
      } catch (error) {
        // Session is invalid or doesn't exist
        console.error('Session check failed:', error);
        // Clear any existing auth data
        delete axios.defaults.headers.common['Authorization'];
        setUser(null);
      } finally {
        setLoading(false);
      }
    };
    
    checkAuth();
  }, [])

  const fetchUser = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/auth/me`, {
        withCredentials: true,
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });
      
      if (response.data && response.data.user) {
        setUser(response.data.user);
      }
    } catch (error) {
      console.error('Failed to fetch user:', error);
      // Clear user state on error
      setUser(null);
      throw error; // Re-throw to allow error handling in components
    } finally {
      setLoading(false);
    }
  }

  // Mock data for demo users
  const mockUsers: Record<string, User> = {
    '<EMAIL>': {
      id: '1',
      email: '<EMAIL>',
      role: 'admin',
      name: 'Alex Johnson',
      isVerified: true,
      twoFactorEnabled: false,
      avatar: 'https://i.pravatar.cc/150?img=1',
      lastLogin: new Date().toISOString(),
      // Admin specific
      permissions: [
        'view_dashboard',
        'manage_users',
        'view_transactions',
        'manage_settings',
        'view_reports'
      ]
    } as AdminUser,
    '<EMAIL>': {
      id: '2',
      email: '<EMAIL>',
      role: 'merchant',
      name: 'Sarah Williams',
      isVerified: true,
      twoFactorEnabled: false,
      avatar: 'https://i.pravatar.cc/150?img=22',
      lastLogin: new Date().toISOString(),
      // Merchant specific
      merchantId: 'merchant_123',
      businessName: 'Premium Goods Inc.',
      balance: 15432.76,
      // Base user properties
      permissions: []
    } as MerchantUser,
    '<EMAIL>': {
      id: '3',
      email: '<EMAIL>',
      role: 'trader',
      name: 'Michael Chen',
      isVerified: true,
      twoFactorEnabled: false,
      avatar: 'https://i.pravatar.cc/150?img=32',
      lastLogin: new Date().toISOString(),
      // Trader specific
      traderId: 'trader_456',
      level: 3,
      totalTrades: 127,
      // Base user properties
      permissions: []
    } as TraderUser
  };

  const login = async (email: string, password: string): Promise<LoginResponse> => {
    setLoading(true);
    setError(null);
    
    try {
      // Create Basic Auth token for the login request
      const authHeader = `Basic ${btoa(`${email}:${password}`)}`;
      
      // Call login API with Basic Auth
      const response = await axios.post("/api/auth/login", 
        { email, password },
        { 
          headers: { 'Authorization': authHeader },
          withCredentials: true // Important for session cookies
        }
      );
      
      if (response.data.requires2FA) {
        // Handle 2FA case if needed
        toast({
          title: "2FA Required",
          description: "Please complete two-factor authentication.",
        });
        return { 
          requires2FA: true, 
          userId: response.data.userId 
        };
      }
      
      // Set auth header for subsequent requests
      axios.defaults.headers.common['Authorization'] = authHeader;
      
      // Fetch user data after successful login
      const userResponse = await axios.get("/api/auth/me");
      if (userResponse.data?.user) {
        const userData = userResponse.data.user;
        
        // Update last login time
        const updatedUser = {
          ...userData,
          lastLogin: new Date().toISOString()
        };
        
        // Update user state
        setUser(updatedUser);
        
        // Show success message
        toast({
          title: "Login successful",
          description: `Welcome back, ${updatedUser.name || 'User'}!`,
        });
        
        return { success: true };
      }
      // If we get here, login failed
      throw new Error('Invalid email or password');
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Login failed. Please try again.';
      setError(message);
      toast?.({
        title: 'Login Failed',
        description: message,
        variant: 'destructive',
      });
      throw error;
    } finally {
      setLoading(false);
    }
  }

  const register = async (userData: RegisterData): Promise<void> => {
    setLoading(true);
    setError(null);
    
    try {
      // Input validation
      if (!userData.email || !userData.password || !userData.name) {
        throw new Error('All fields are required');
      }
      
      if (userData.password.length < 6) {
        throw new Error('Password must be at least 6 characters long');
      }
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Create a new trader user by default
      const newUser: TraderUser = {
        id: `user_${Date.now()}`,
        email: userData.email,
        name: userData.name,
        role: 'trader',
        isVerified: false,
        twoFactorEnabled: false,
        traderId: `trader_${Date.now()}`,
        level: 1,
        totalTrades: 0,
        permissions: [],
        merchantId: '',
        lastLogin: new Date().toISOString()
      };
      
      // Store user data in session storage for persistence
      const token = `mock-jwt-token-${newUser.id}`;
      sessionStorage.setItem('token', token);
      sessionStorage.setItem('user', JSON.stringify(newUser));
      
      // Set auth header for API calls
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      
      // Update state
      setUser(newUser);
      setError(null);
      
      // Show success message
      toast?.({
        title: 'Registration Successful',
        description: 'Your account has been created!',
      });
      
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Registration failed. Please try again.';
      setError(message);
      toast?.({
        title: 'Registration Failed',
        description: message,
        variant: 'destructive',
      });
      throw error;
    } finally {
      setLoading(false);
    }
  }

  const logout = (): void => {
    // Clear all auth-related data
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    delete axios.defaults.headers.common['Authorization'];
    
    // Reset user state
    setUser(null);
    
    // Notify user
    if (toast) {
      toast({
        title: 'Logged out',
        description: 'You have been successfully logged out.',
      });
    }
  }

  const value = {
    user,
    login,
    register,
    logout,
    loading,
    error,
    clearError
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}
