const rateLimit = require('express-rate-limit');
const RedisStore = require('rate-limit-redis');
const { createClient } = require('redis');
const logger = require('../utils/logger');

// Initialize Redis client for distributed rate limiting
let redisClient;

if (process.env.REDIS_URL) {
  (async () => {
    try {
      redisClient = createClient({
        url: process.env.REDIS_URL,
        socket: {
          reconnectStrategy: (retries) => {
            if (retries > 10) {
              logger.error('Too many attempts to reconnect to Redis');
              return new Error('Too many retries to connect to Redis');
            }
            // Exponential backoff
            return Math.min(retries * 100, 60000);
          },
        },
      });

      redisClient.on('error', (err) => {
        logger.error('Redis client error:', err);
      });

      await redisClient.connect();
      logger.info('Connected to Redis for rate limiting sensitive endpoints');
    } catch (error) {
      logger.error('Failed to connect to Redis for rate limiting:', error);
    }
  })();
}

/**
 * Create a rate limiter for sensitive endpoints
 * @param {Object} options - Rate limiter options
 * @returns {Object} Rate limiter middleware
 */
const createSensitiveEndpointLimiter = (options = {}) => {
  const {
    windowMs = 15 * 60 * 1000, // 15 minutes
    max = 5,                    // Limit each IP to 5 requests per windowMs
    message = 'Too many attempts, please try again later.',
    keyGenerator = (req) => {
      // Default key generator uses IP + user ID (if authenticated) + endpoint
      const identifier = req.user ? `${req.user.id}:${req.ip}` : req.ip;
      return `${identifier}:${req.method}:${req.originalUrl}`;
    },
    skip = (req) => {
      // Skip rate limiting for certain IPs or users
      const whitelist = (process.env.RATE_LIMIT_WHITELIST || '').split(',');
      return whitelist.includes(req.ip) || 
             (req.user && req.user.role === 'admin');
    },
    handler = (req, res) => {
      logger.warn('Rate limit exceeded', {
        ip: req.ip,
        method: req.method,
        url: req.originalUrl,
        user: req.user ? req.user.id : 'anonymous'
      });
      
      res.status(429).json({
        success: false,
        message,
        retryAfter: Math.ceil(windowMs / 1000)
      });
    }
  } = options;

  // Use Redis store if available, otherwise use in-memory store
  const store = redisClient
    ? new RedisStore({
        client: redisClient,
        prefix: 'sensitive_endpoint_rl:',
        // Expire rate limit counters 2x the window to ensure cleanup
        expiry: Math.ceil(windowMs / 1000) * 2
      })
    : undefined;

  return rateLimit({
    windowMs,
    max,
    message,
    keyGenerator,
    skip,
    handler,
    store,
    standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders: false,  // Disable the `X-RateLimit-*` headers
    // Trust proxy if behind a reverse proxy (e.g., Heroku, AWS ELB, etc.)
    trustProxy: process.env.TRUST_PROXY === 'true'
  });
};

// Rate limiters for sensitive endpoints
const sensitiveEndpoints = {
  // Login attempts
  login: createSensitiveEndpointLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5,                   // 5 attempts per windowMs
    message: 'Too many login attempts, please try again later.'
  }),

  // Password reset requests
  passwordReset: createSensitiveEndpointLimiter({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 3,                   // 3 attempts per hour
    message: 'Too many password reset attempts, please try again later.'
  }),

  // Email verification
  emailVerification: createSensitiveEndpointLimiter({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 3,                   // 3 attempts per hour
    message: 'Too many verification attempts, please try again later.'
  }),

  // 2FA verification
  twoFactorAuth: createSensitiveEndpointLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5,                   // 5 attempts per windowMs
    message: 'Too many 2FA attempts, please try again later.'
  }),

  // Payment processing
  payment: createSensitiveEndpointLimiter({
    windowMs: 5 * 60 * 1000,  // 5 minutes
    max: 10,                  // 10 attempts per windowMs
    message: 'Too many payment attempts, please try again later.'
  }),

  // API key generation
  apiKeyGeneration: createSensitiveEndpointLimiter({
    windowMs: 24 * 60 * 60 * 1000, // 24 hours
    max: 3,                        // 3 attempts per day
    message: 'Too many API key generation attempts, please try again later.'
  }),

  // Account creation
  accountCreation: createSensitiveEndpointLimiter({
    windowMs: 24 * 60 * 60 * 1000, // 24 hours
    max: 10,                       // 10 accounts per day per IP
    message: 'Too many account creation attempts, please try again later.'
  })
};

module.exports = {
  createSensitiveEndpointLimiter,
  sensitiveEndpoints
};
