{"version": 3, "file": "static/js/523.932b8bdb.chunk.js", "mappings": "yNAAA,MAAMA,EAAY,aAELC,EAAWA,IACA,qBAAXC,OAA+B,KACnCC,aAAaC,QAAQJ,G,cCA9B,MAAMK,EAAeC,CAAAA,SAAAA,aAAAA,WAAAA,GAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,kBAAAA,4BAAAA,mBAAAA,aAAAA,kBAAAA,wBAAAA,8BAAAA,GAAAA,8BAAAA,GAAAA,mBAAAA,eAAYC,qBAAuB,4BAwFjD,MAAMC,EAAY,IAtFzB,MAGEC,WAAAA,GAAe,KAFPC,YAAM,EAGZC,KAAKD,OAASE,EAAAA,EAAMC,OAAO,CACzBC,QAAST,EACTU,QAAS,CACP,eAAgB,oBAElBC,iBAAiB,IAGnBL,KAAKM,mBACP,CAEQA,iBAAAA,GAENN,KAAKD,OAAOQ,aAAaC,QAAQC,IAC9BC,IACC,MAAMC,EAAQrB,IAId,OAHIqB,IACFD,EAAON,QAAQQ,cAAgB,UAAUD,KAEpCD,GAERG,GACQC,QAAQC,OAAOF,IAK1Bb,KAAKD,OAAOQ,aAAaS,SAASP,IAC/BO,GAA4BA,EAC5BH,IAAuB,IAADI,EAAAC,EACrB,MAAMC,EAAuB,QAAjBF,EAAGJ,EAAMG,gBAAQ,IAAAC,OAAA,EAAdA,EAAgBE,OACzBC,EAAqB,QAAjBF,EAAGL,EAAMG,gBAAQ,IAAAE,OAAA,EAAdA,EAAgBE,KAuB7B,OApBe,MAAXD,ID9BY,qBAAX5B,QACTC,aAAa6B,WAAWhC,GC+BlBE,OAAO+B,SAASC,KAAO,UAIjB,OAAJH,QAAI,IAAJA,GAAAA,EAAMI,SACRC,EAAAA,EAAAA,IAAM,CACJC,MAAO,QACPC,YAAaP,EAAKI,QAClBI,QAAS,gBAEFf,EAAMW,UACfC,EAAAA,EAAAA,IAAM,CACJC,MAAO,QACPC,YAAad,EAAMW,QACnBI,QAAS,gBAINd,QAAQC,OAAOF,IAG5B,CAEA,SAAMgB,CAAOC,EAAapB,GAExB,aADuBV,KAAKD,OAAO8B,IAAOC,EAAKpB,IAC/BU,IAClB,CAEA,UAAMW,CAAQD,EAAaV,EAAYV,GAErC,aADuBV,KAAKD,OAAOgC,KAAQD,EAAKV,EAAMV,IACtCU,IAClB,CAEA,SAAMY,CAAOF,EAAaV,EAAYV,GAEpC,aADuBV,KAAKD,OAAOiC,IAAOF,EAAKV,EAAMV,IACrCU,IAClB,CAEA,YAAMa,CAAUH,EAAapB,GAE3B,aADuBV,KAAKD,OAAOkC,OAAUH,EAAKpB,IAClCU,IAClB,G,cCpFF,MAAMc,EAAerB,IACnB,MAAMsB,EAAetB,aAAiBuB,MAClCvB,EAAMW,QACN,yCAEJC,EAAAA,EAAAA,IAAM,CACJC,MAAO,QACPC,YAAaQ,EACbP,QAAS,iBAKPS,EAAiC,CACrCC,eAAgB,CACdC,QAAS,CACPC,UAAW,IACXC,MAAO,EACPC,sBAAsB,GAExBC,UAAW,CACTC,QAAU/B,IACRqB,EAAYrB,OASPgC,GAHc,IAAIC,EAAAA,EAAYT,GAWtB,WAAgB,MAAM,IAAXU,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EAEhCG,iBAAmBrC,IACjBqB,EAAYrB,IACL,GAEV,GAdUgC,EAiBA,WAAgB,MAAM,IAAXE,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EAExBI,kBAAkB,EAClBT,sBAAsB,EACvB,EChBGU,EACM,gBADNA,EAEG,sBAFHA,EAGS,4BAHTA,EAIU,6BAsBHC,EAAeC,IACnBC,EAAAA,EAAAA,GAA8C,CACnDC,SAAU,CAAC,YACXC,QAASC,gBACgB7D,EAAUgC,IAA2BuB,IAC5ChC,QAEfyB,OACAS,IAIMK,EAAoBL,IACxBC,EAAAA,EAAAA,GAAwD,CAC7DC,SAAU,CAAC,WAAY,SACvBC,QAASC,gBACgB7D,EAAUgC,IAAgCuB,IACjDhC,QAEfyB,OACAS,IA4BMM,EAAiB,WAAiM,IAAhMC,EAA4Bd,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EAAGO,EAAuJP,UAAAC,OAAA,EAAAD,UAAA,QAAAE,EACvN,OAAOM,EAAAA,EAAAA,GAAyG,CAC9GC,SAAU,CAAC,WAAY,cAAeK,GACtCJ,QAASC,gBACgB7D,EAAUgC,IAA4CuB,EAAuB,CAAES,YACtFzC,QAEfyB,OACAS,GAEP,EAcaQ,EAAkB,WAAiM,IAAhMD,EAA2Bd,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EAAGO,EAAwJP,UAAAC,OAAA,EAAAD,UAAA,QAAAE,EACxN,OAAOM,EAAAA,EAAAA,GAA0G,CAC/GC,SAAU,CAAC,WAAY,eAAgBK,GACvCJ,QAASC,gBACgB7D,EAAUgC,IAA6CuB,EAAwB,CAAES,YACxFzC,QAEfyB,OACAA,OACAS,GAEP,C,iECjJA,SAASS,EAAQC,GAGyB,IAHxB,UAChBC,KACGC,GACkCF,EACrC,OACEG,EAAAA,EAAAA,KAAA,OACEF,WAAWG,EAAAA,EAAAA,IAAG,wDAAyDH,MACnEC,GAGV,C,kCCAM,MAAAG,GAAaC,E,QAAAA,GAAiB,aAAc,CAChD,CACE,OACA,CAAEC,MAAO,KAAMC,OAAQ,KAAMC,EAAG,IAAKC,EAAG,IAAKC,GAAI,IAAKC,IAAK,WAE7D,CAAC,OAAQ,CAAEC,GAAI,IAAKC,GAAI,KAAMC,GAAI,KAAMC,GAAI,KAAMJ,IAAK,Y,+JCR1C,SAASK,IAAiB,IAADC,EAAAC,EACtC,MAAM,KAAEC,IAASC,EAAAA,EAAAA,MACVC,EAAWC,IAAgBC,EAAAA,EAAAA,UAAS,UAGnCpE,KAAMqE,EAAUC,UAAWC,IAAsBtC,EAAAA,EAAAA,OACjDjC,KAAMwE,EAAOF,UAAWG,EAAc,QAAEC,IAAYnC,EAAAA,EAAAA,MAM5D,OAAIgC,GAAqBE,GAErB1B,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gBAAe8B,UAC5B5B,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2CAA0C8B,SACtD,CAAC,EAAG,EAAG,EAAG,GAAGC,IAAKC,IACjBC,EAAAA,EAAAA,MAACC,EAAAA,GAAI,CAAAJ,SAAA,EACHG,EAAAA,EAAAA,MAACE,EAAAA,GAAU,CAACnC,UAAU,4DAA2D8B,SAAA,EAC/E5B,EAAAA,EAAAA,KAACJ,EAAAA,EAAQ,CAACE,UAAU,cACpBE,EAAAA,EAAAA,KAACJ,EAAAA,EAAQ,CAACE,UAAU,gBAEtBiC,EAAAA,EAAAA,MAACG,EAAAA,GAAW,CAAAN,SAAA,EACV5B,EAAAA,EAAAA,KAACJ,EAAAA,EAAQ,CAACE,UAAU,mBACpBE,EAAAA,EAAAA,KAACJ,EAAAA,EAAQ,CAACE,UAAU,kBAPbgC,SAiBnBC,EAAAA,EAAAA,MAAA,OAAKjC,UAAU,gBAAe8B,SAAA,EAC5BG,EAAAA,EAAAA,MAAA,OAAKjC,UAAU,oCAAmC8B,SAAA,EAChDG,EAAAA,EAAAA,MAAA,OAAAH,SAAA,EACE5B,EAAAA,EAAAA,KAAA,MAAIF,UAAU,qBAAoB8B,SAAC,eACnCG,EAAAA,EAAAA,MAAA,KAAGjC,UAAU,wBAAuB8B,SAAA,CAAC,kBACZ,OAARN,QAAQ,IAARA,OAAQ,EAARA,EAAUa,eAAgB,WAAW,WAGxDnC,EAAAA,EAAAA,KAACoC,EAAAA,EAAM,CAAC3E,QAAQ,UAAU4E,KAAK,OAAOC,QAlCtB/C,gBACdoC,KAiC2DC,UAC3D5B,EAAAA,EAAAA,KAACuC,EAAAA,EAAS,CAACzC,UAAU,kBAIzBiC,EAAAA,EAAAA,MAAA,OAAKjC,UAAU,2CAA0C8B,SAAA,EACvDG,EAAAA,EAAAA,MAACC,EAAAA,GAAI,CAAAJ,SAAA,EACHG,EAAAA,EAAAA,MAACE,EAAAA,GAAU,CAACnC,UAAU,4DAA2D8B,SAAA,EAC/E5B,EAAAA,EAAAA,KAACwC,EAAAA,GAAS,CAAC1C,UAAU,sBAAqB8B,SAAC,mBAC3C5B,EAAAA,EAAAA,KAACyC,EAAAA,EAAU,CAAC3C,UAAU,sCAExBiC,EAAAA,EAAAA,MAACG,EAAAA,GAAW,CAAAN,SAAA,EACVG,EAAAA,EAAAA,MAAA,OAAKjC,UAAU,qBAAoB8B,SAAA,CAAC,KAC3B,OAALH,QAAK,IAALA,GAAmB,QAAdV,EAALU,EAAOiB,oBAAY,IAAA3B,OAAd,EAALA,EAAqB4B,eAAe,QAAS,CAAEC,sBAAuB,MAAQ,WAElFb,EAAAA,EAAAA,MAAA,KAAGjC,UAAU,gCAA+B8B,SAAA,CAAC,oBAAkBT,YAInEY,EAAAA,EAAAA,MAACC,EAAAA,GAAI,CAAAJ,SAAA,EACHG,EAAAA,EAAAA,MAACE,EAAAA,GAAU,CAACnC,UAAU,4DAA2D8B,SAAA,EAC/E5B,EAAAA,EAAAA,KAACwC,EAAAA,GAAS,CAAC1C,UAAU,sBAAqB8B,SAAC,kBAC3C5B,EAAAA,EAAAA,KAACE,EAAAA,EAAU,CAACJ,UAAU,sCAExBiC,EAAAA,EAAAA,MAACG,EAAAA,GAAW,CAAAN,SAAA,EACV5B,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qBAAoB8B,UAAO,OAALH,QAAK,IAALA,GAAwB,QAAnBT,EAALS,EAAOoB,yBAAiB,IAAA7B,OAAnB,EAALA,EAA0B2B,mBAAoB,OACnFZ,EAAAA,EAAAA,MAAA,KAAGjC,UAAU,gCAA+B8B,SAAA,CAAC,oBAAkBT,YAInEY,EAAAA,EAAAA,MAACC,EAAAA,GAAI,CAAAJ,SAAA,EACHG,EAAAA,EAAAA,MAACE,EAAAA,GAAU,CAACnC,UAAU,4DAA2D8B,SAAA,EAC/E5B,EAAAA,EAAAA,KAACwC,EAAAA,GAAS,CAAC1C,UAAU,sBAAqB8B,SAAC,oBAC3C5B,EAAAA,EAAAA,KAAC8C,EAAAA,EAAK,CAAChD,UAAU,sCAEnBiC,EAAAA,EAAAA,MAACG,EAAAA,GAAW,CAAAN,SAAA,EACV5B,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qBAAoB8B,UAAO,OAALH,QAAK,IAALA,OAAK,EAALA,EAAOsB,gBAAiB,OAC7DhB,EAAAA,EAAAA,MAAA,KAAGjC,UAAU,gCAA+B8B,SAAA,CAAC,gBAAcT,gBAMvE,C,iHC3FA,MAAMa,EAAOgB,EAAAA,WAGX,CAAAnD,EAA0BoD,KAAG,IAA5B,UAAEnD,KAAcC,GAAOF,EAAA,OACxBG,EAAAA,EAAAA,KAAA,OACEiD,IAAKA,EACLnD,WAAWG,EAAAA,EAAAA,IACT,2DACAH,MAEEC,MAGRiC,EAAKkB,YAAc,OAEnB,MAAMjB,EAAae,EAAAA,WAGjB,CAAAG,EAA0BF,KAAG,IAA5B,UAAEnD,KAAcC,GAAOoD,EAAA,OACxBnD,EAAAA,EAAAA,KAAA,OACEiD,IAAKA,EACLnD,WAAWG,EAAAA,EAAAA,IAAG,gCAAiCH,MAC3CC,MAGRkC,EAAWiB,YAAc,aAEzB,MAAMV,EAAYQ,EAAAA,WAGhB,CAAAI,EAA0BH,KAAG,IAA5B,UAAEnD,KAAcC,GAAOqD,EAAA,OACxBpD,EAAAA,EAAAA,KAAA,MACEiD,IAAKA,EACLnD,WAAWG,EAAAA,EAAAA,IACT,qDACAH,MAEEC,MAGRyC,EAAUU,YAAc,YAExB,MAAMG,EAAkBL,EAAAA,WAGtB,CAAAM,EAA0BL,KAAG,IAA5B,UAAEnD,KAAcC,GAAOuD,EAAA,OACxBtD,EAAAA,EAAAA,KAAA,KACEiD,IAAKA,EACLnD,WAAWG,EAAAA,EAAAA,IAAG,gCAAiCH,MAC3CC,MAGRsD,EAAgBH,YAAc,kBAE9B,MAAMhB,EAAcc,EAAAA,WAGlB,CAAAO,EAA0BN,KAAG,IAA5B,UAAEnD,KAAcC,GAAOwD,EAAA,OACxBvD,EAAAA,EAAAA,KAAA,OAAKiD,IAAKA,EAAKnD,WAAWG,EAAAA,EAAAA,IAAG,WAAYH,MAAgBC,MAE3DmC,EAAYgB,YAAc,cAE1B,MAAMM,EAAaR,EAAAA,WAGjB,CAAAS,EAA0BR,KAAG,IAA5B,UAAEnD,KAAcC,GAAO0D,EAAA,OACxBzD,EAAAA,EAAAA,KAAA,OACEiD,IAAKA,EACLnD,WAAWG,EAAAA,EAAAA,IAAG,6BAA8BH,MACxCC,MAGRyD,EAAWN,YAAc,Y,0DC5DzB,IAAIQ,EAAQ,EA+BZ,MAAMC,EAAgB,IAAIC,IAEpBC,EAAoBC,IACxB,GAAIH,EAAcI,IAAID,GACpB,OAGF,MAAME,EAAUC,WAAW,KACzBN,EAAc7F,OAAOgG,GACrBI,EAAS,CACPC,KAAM,eACNL,QAASA,KAEV,KAEHH,EAAcS,IAAIN,EAASE,IAGhBK,EAAUA,CAACC,EAAcC,KACpC,OAAQA,EAAOJ,MACb,IAAK,YACH,MAAO,IACFG,EACHE,OAAQ,CAACD,EAAOjH,SAAUgH,EAAME,QAAQC,MAAM,EAAG,IAGrD,IAAK,eACH,MAAO,IACFH,EACHE,OAAQF,EAAME,OAAO3C,IAAK6C,GACxBA,EAAEC,KAAOJ,EAAOjH,MAAMqH,GAAK,IAAKD,KAAMH,EAAOjH,OAAUoH,IAI7D,IAAK,gBAAiB,CACpB,MAAM,QAAEZ,GAAYS,EAYpB,OARIT,EACFD,EAAiBC,GAEjBQ,EAAME,OAAOI,QAAStH,IACpBuG,EAAiBvG,EAAMqH,MAIpB,IACFL,EACHE,OAAQF,EAAME,OAAO3C,IAAK6C,GACxBA,EAAEC,KAAOb,QAAuBhF,IAAZgF,EAChB,IACKY,EACHG,MAAM,GAERH,GAGV,CACA,IAAK,eACH,YAAuB5F,IAAnByF,EAAOT,QACF,IACFQ,EACHE,OAAQ,IAGL,IACFF,EACHE,OAAQF,EAAME,OAAOM,OAAQJ,GAAMA,EAAEC,KAAOJ,EAAOT,YAKrDiB,EAA2C,GAEjD,IAAIC,EAAqB,CAAER,OAAQ,IAEnC,SAASN,EAASK,GAChBS,EAAcX,EAAQW,EAAaT,GACnCQ,EAAUH,QAASK,IACjBA,EAASD,IAEb,CAIA,SAAS1H,EAAKuC,GAAuB,OAAjBE,GAAcF,EAChC,MAAM8E,GAnHNjB,GAASA,EAAQ,GAAKwB,OAAOC,iBACtBzB,EAAM0B,YAyHPC,EAAUA,IAAMnB,EAAS,CAAEC,KAAM,gBAAiBL,QAASa,IAcjE,OAZAT,EAAS,CACPC,KAAM,YACN7G,MAAO,IACFyC,EACH4E,KACAE,MAAM,EACNS,aAAeT,IACRA,GAAMQ,QAKV,CACLV,GAAIA,EACJU,UACAE,OAtBcxF,GACdmE,EAAS,CACPC,KAAM,eACN7G,MAAO,IAAKyC,EAAO4E,QAqBzB,CAEA,SAASa,IACP,MAAOlB,EAAOmB,GAAYzC,EAAAA,SAAsBgC,GAYhD,OAVAhC,EAAAA,UAAgB,KACd+B,EAAUW,KAAKD,GACR,KACL,MAAME,EAAQZ,EAAUa,QAAQH,GAC5BE,GAAS,GACXZ,EAAUc,OAAOF,EAAO,KAG3B,CAACrB,IAEG,IACFA,EACHhH,QACA+H,QAAUvB,GAAqBI,EAAS,CAAEC,KAAM,gBAAiBL,YAErE,C,kCCvKM,MAAAhB,GAAQ3C,E,QAAAA,GAAiB,QAAS,CACtC,CAAC,OAAQ,CAAE2F,EAAG,4CAA6CrF,IAAK,WAChE,CAAC,SAAU,CAAEsF,GAAI,IAAKC,GAAI,IAAKC,EAAG,IAAKxF,IAAK,UAC5C,CAAC,OAAQ,CAAEqF,EAAG,6BAA8BrF,IAAK,WACjD,CAAC,OAAQ,CAAEqF,EAAG,4BAA6BrF,IAAK,Y,kCCJ5C,MAAAgC,GAAatC,E,QAAAA,GAAiB,aAAc,CAChD,CAAC,OAAQ,CAAEO,GAAI,KAAMC,GAAI,KAAMC,GAAI,IAAKC,GAAI,KAAMJ,IAAK,WACvD,CACE,OACA,CAAEqF,EAAG,oDAAqDrF,IAAK,Y,sFCXnE,MAAMyF,GAAiBC,EAAAA,EAAAA,GACrB,yRACA,CACEC,SAAU,CACR3I,QAAS,CACP4I,QAAS,yDACTC,YACE,qEACFC,QACE,iFACFC,UACE,+DACFC,MAAO,+CACPC,KAAM,mDAERrE,KAAM,CACJgE,QAAS,iBACTM,GAAI,sBACJC,GAAI,uBACJC,KAAM,cAGVC,gBAAiB,CACfrJ,QAAS,UACT4E,KAAM,aAWND,EAASY,EAAAA,WACb,CAAAnD,EAA0DoD,KAAS,IAAlE,UAAEnD,EAAS,QAAErC,EAAO,KAAE4E,EAAI,QAAE0E,GAAU,KAAUhH,GAAOF,EACtD,MAAMmH,EAAOD,EAAUE,EAAAA,GAAO,SAC9B,OACEjH,EAAAA,EAAAA,KAACgH,EAAI,CACHlH,WAAWG,EAAAA,EAAAA,IAAGiG,EAAe,CAAEzI,UAAS4E,OAAMvC,eAC9CmD,IAAKA,KACDlD,MAKZqC,EAAOc,YAAc,Q", "sources": ["lib/auth.ts", "lib/api-client.ts", "lib/react-query.ts", "services/api/merchant.ts", "components/ui/skeleton.tsx", "../node_modules/lucide-react/src/icons/credit-card.ts", "pages/merchant/DashboardPage.tsx", "components/ui/card.tsx", "components/ui/use-toast.ts", "../node_modules/lucide-react/src/icons/users.ts", "../node_modules/lucide-react/src/icons/dollar-sign.ts", "components/ui/button.tsx"], "sourcesContent": ["const TOKEN_KEY = 'auth_token';\n\nexport const getToken = (): string | null => {\n  if (typeof window === 'undefined') return null;\n  return localStorage.getItem(TOKEN_KEY);\n};\n\nexport const setToken = (token: string): void => {\n  if (typeof window !== 'undefined') {\n    localStorage.setItem(TOKEN_KEY, token);\n  }\n};\n\nexport const clearToken = (): void => {\n  if (typeof window !== 'undefined') {\n    localStorage.removeItem(TOKEN_KEY);\n  }\n};\n\nexport const isAuthenticated = (): boolean => {\n  return !!getToken();\n};\n\nexport const getUserRole = (): string | null => {\n  if (typeof window === 'undefined') return null;\n  const token = getToken();\n  if (!token) return null;\n  \n  try {\n    const base64Url = token.split('.')[1];\n    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n    const payload = JSON.parse(window.atob(base64));\n    return payload.role || null;\n  } catch (error) {\n    console.error('Error decoding token:', error);\n    return null;\n  }\n};\n\nexport const checkAuth = (): boolean => {\n  const isAuth = isAuthenticated();\n  if (!isAuth) {\n    window.location.href = '/login';\n    return false;\n  }\n  return true;\n};\n\nexport const checkRole = (allowedRoles: string[]): boolean => {\n  const role = getUserRole();\n  if (!role || !allowedRoles.includes(role)) {\n    window.location.href = '/unauthorized';\n    return false;\n  }\n  return true;\n};\n", "import axios, { AxiosError, AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';\nimport { getToken, clearToken } from './auth';\nimport { toast } from '../components/ui/use-toast';\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';\n\nclass ApiClient {\n  private client: AxiosInstance;\n\n  constructor() {\n    this.client = axios.create({\n      baseURL: API_BASE_URL,\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      withCredentials: true,\n    });\n\n    this.setupInterceptors();\n  }\n\n  private setupInterceptors() {\n    // Request interceptor\n    this.client.interceptors.request.use(\n      (config) => {\n        const token = getToken();\n        if (token) {\n          config.headers.Authorization = `Bearer ${token}`;\n        }\n        return config;\n      },\n      (error) => {\n        return Promise.reject(error);\n      }\n    );\n\n    // Response interceptor\n    this.client.interceptors.response.use(\n      (response: AxiosResponse) => response,\n      (error: AxiosError) => {\n        const status = error.response?.status;\n        const data = error.response?.data as { message?: string };\n        \n        // Handle 401 Unauthorized\n        if (status === 401) {\n          clearToken();\n          window.location.href = '/login';\n        }\n\n        // Show error toast\n        if (data?.message) {\n          toast({\n            title: 'Error',\n            description: data.message,\n            variant: 'destructive',\n          });\n        } else if (error.message) {\n          toast({\n            title: 'Error',\n            description: error.message,\n            variant: 'destructive',\n          });\n        }\n\n        return Promise.reject(error);\n      }\n    );\n  }\n\n  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {\n    const response = await this.client.get<T>(url, config);\n    return response.data;\n  }\n\n  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {\n    const response = await this.client.post<T>(url, data, config);\n    return response.data;\n  }\n\n  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {\n    const response = await this.client.put<T>(url, data, config);\n    return response.data;\n  }\n\n  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {\n    const response = await this.client.delete<T>(url, config);\n    return response.data;\n  }\n\n  // Add other HTTP methods as needed\n}\n\nexport const apiClient = new ApiClient();\n", "import { QueryClient, QueryClientConfig } from '@tanstack/react-query';\nimport { toast } from '../components/ui/use-toast';\n\nconst handleError = (error: unknown) => {\n  const errorMessage = error instanceof Error \n    ? error.message \n    : 'An error occurred while fetching data';\n  \n  toast({\n    title: 'Error',\n    description: errorMessage,\n    variant: 'destructive',\n  });\n};\n\n// Create a type-safe query client configuration\nconst queryConfig: QueryClientConfig = {\n  defaultOptions: {\n    queries: {\n      staleTime: 5 * 60 * 1000, // 5 minutes\n      retry: 1,\n      refetchOnWindowFocus: false,\n    },\n    mutations: {\n      onError: (error: unknown) => {\n        handleError(error);\n      },\n    },\n  },\n};\n\nexport const queryClient = new QueryClient(queryConfig);\n\n// Custom hooks for common query options\nexport const queryOptions = {\n  defaultOptions: (options = {}) => ({\n    ...options,\n    refetchOnWindowFocus: false,\n    retry: 1,\n  }),\n  \n  // Standard error handling for queries\n  withErrorHandling: <T>(options = {}) => ({\n    ...options,\n    useErrorBoundary: (error: unknown) => {\n      handleError(error);\n      return true;\n    },\n  } as const),\n\n  // Helper for paginated queries\n  paginated: <T>(options = {}) => ({\n    ...options,\n    keepPreviousData: true,\n    refetchOnWindowFocus: false,\n  } as const),\n};\n", "import { useQuery, useMutation, useQueryClient, UseQueryOptions } from '@tanstack/react-query';\nimport { apiClient } from '../../lib/api-client';\nimport { queryOptions } from '../../lib/react-query';\nimport { AxiosResponse } from 'axios';\n\n// Helper type to extract the data type from ApiResponse\ntype ApiResponseData<T> = T extends { data: infer U } ? U : never;\n\nexport interface Merchant {\n  id: string;\n  businessName: string;\n  email: string;\n  phone: string;\n  status: 'active' | 'pending' | 'suspended';\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface MerchantStats {\n  totalRevenue: number;\n  totalTransactions: number;\n  activeTraders: number;\n  pendingPayouts: number;\n}\n\nexport interface Collection {\n  id: string;\n  amount: number;\n  status: 'pending' | 'completed' | 'failed';\n  trader: {\n    id: string;\n    name: string;\n    email: string;\n  };\n  reference: string;\n  createdAt: string;\n}\n\n// API Endpoints\nconst ENDPOINTS = {\n  merchant: '/merchants/me',\n  stats: '/merchants/me/stats',\n  collections: '/merchants/me/collections',\n  transactions: '/merchants/me/transactions',\n  traders: '/merchants/me/traders',\n  settings: '/merchants/me/settings',\n};\n\n// Base response type for API calls\ninterface ApiResponse<T> {\n  data: T;\n  message?: string;\n  success: boolean;\n}\n\n// Type for paginated responses\ninterface PaginatedData<T> {\n  data: T[];\n  total: number;\n  page: number;\n  limit: number;\n  totalPages: number;\n}\n\n// Merchant Queries\nexport const useMerchant = (options?: Omit<UseQueryOptions<Merchant, Error, Merchant, string[]>, 'queryKey' | 'queryFn'>) => {\n  return useQuery<Merchant, Error, Merchant, string[]>({\n    queryKey: ['merchant'],\n    queryFn: async (): Promise<Merchant> => {\n      const response = await apiClient.get<ApiResponse<Merchant>>(ENDPOINTS.merchant);\n      return response.data;\n    },\n    ...queryOptions.withErrorHandling(),\n    ...options,\n  });\n};\n\nexport const useMerchantStats = (options?: Omit<UseQueryOptions<MerchantStats, Error, MerchantStats, string[]>, 'queryKey' | 'queryFn'>) => {\n  return useQuery<MerchantStats, Error, MerchantStats, string[]>({\n    queryKey: ['merchant', 'stats'],\n    queryFn: async (): Promise<MerchantStats> => {\n      const response = await apiClient.get<ApiResponse<MerchantStats>>(ENDPOINTS.stats);\n      return response.data;\n    },\n    ...queryOptions.withErrorHandling(),\n    ...options,\n  });\n};\n\n// Collections\nexport interface GetCollectionsParams {\n  status?: 'pending' | 'completed' | 'failed';\n  search?: string;\n  sortBy?: string;\n  sortOrder?: 'asc' | 'desc';\n  startDate?: string;\n  endDate?: string;\n  page?: number;\n  limit?: number;\n  traderId?: string;\n  reference?: string;\n  minAmount?: number;\n  maxAmount?: number;\n}\n\ninterface PaginatedResponse<T> {\n  data: T[];\n  total: number;\n  page: number;\n  limit: number;\n  totalPages: number;\n}\n\nexport const useCollections = (params: GetCollectionsParams = {}, options?: Omit<UseQueryOptions<PaginatedData<Collection>, Error, PaginatedData<Collection>, (string | GetCollectionsParams)[]>, 'queryKey' | 'queryFn'>) => {\n  return useQuery<PaginatedData<Collection>, Error, PaginatedData<Collection>, (string | GetCollectionsParams)[]>({\n    queryKey: ['merchant', 'collections', params],\n    queryFn: async (): Promise<PaginatedData<Collection>> => {\n      const response = await apiClient.get<ApiResponse<PaginatedData<Collection>>>(ENDPOINTS.collections, { params });\n      return response.data;\n    },\n    ...queryOptions.withErrorHandling(),\n    ...options,\n  });\n};\n\n// Transactions\nexport interface Transaction {\n  id: string;\n  amount: number;\n  status: 'pending' | 'completed' | 'failed' | 'refunded';\n  type: string;\n  reference: string;\n  description: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport const useTransactions = (params: Record<string, any> = {}, options?: Omit<UseQueryOptions<PaginatedData<Transaction>, Error, PaginatedData<Transaction>, (string | Record<string, any>)[]>, 'queryKey' | 'queryFn'>) => {\n  return useQuery<PaginatedData<Transaction>, Error, PaginatedData<Transaction>, (string | Record<string, any>)[]>({\n    queryKey: ['merchant', 'transactions', params],\n    queryFn: async (): Promise<PaginatedData<Transaction>> => {\n      const response = await apiClient.get<ApiResponse<PaginatedData<Transaction>>>(ENDPOINTS.transactions, { params });\n      return response.data;\n    },\n    ...queryOptions.paginated(),\n    ...queryOptions.withErrorHandling(),\n    ...options,\n  });\n};\n\n// Traders\nexport interface Trader {\n  id: string;\n  name: string;\n  email: string;\n  phone: string;\n  status: 'active' | 'inactive' | 'suspended';\n  lastActive: string;\n  createdAt: string;\n}\n\nexport const useTraders = (options?: Omit<UseQueryOptions<Trader[], Error, Trader[], string[]>, 'queryKey' | 'queryFn'>) => {\n  return useQuery<Trader[], Error, Trader[], string[]>({\n    queryKey: ['merchant', 'traders'],\n    queryFn: async (): Promise<Trader[]> => {\n      const response = await apiClient.get<ApiResponse<Trader[]>>(ENDPOINTS.traders);\n      return response.data;\n    },\n    ...queryOptions.withErrorHandling(),\n    ...options,\n  });\n};\n\n// Settings\nexport interface MerchantSettings {\n  id: string;\n  businessName: string;\n  email: string;\n  phone: string;\n  address: string;\n  city: string;\n  country: string;\n  postalCode: string;\n  taxId: string;\n  website: string;\n  logoUrl: string;\n  currency: string;\n  timezone: string;\n  notificationPreferences: {\n    email: boolean;\n    sms: boolean;\n    push: boolean;\n  };\n  security: {\n    twoFactorEnabled: boolean;\n    lastPasswordChange: string;\n  };\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport const useMerchantSettings = (options?: Omit<UseQueryOptions<MerchantSettings, Error, MerchantSettings, string[]>, 'queryKey' | 'queryFn'>) => {\n  return useQuery<MerchantSettings, Error, MerchantSettings, string[]>({\n    queryKey: ['merchant', 'settings'],\n    queryFn: async (): Promise<MerchantSettings> => {\n      const response = await apiClient.get<ApiResponse<MerchantSettings>>(ENDPOINTS.settings);\n      return response.data;\n    },\n    ...queryOptions.withErrorHandling(),\n    ...options,\n  });\n};\n\nexport const useUpdateMerchantSettings = () => {\n  const queryClient = useQueryClient();\n  \n  return useMutation<MerchantSettings, Error, Partial<MerchantSettings>>({\n    mutationFn: async (data): Promise<MerchantSettings> => {\n      const response = await apiClient.put<ApiResponse<MerchantSettings>>(ENDPOINTS.settings, data);\n      return response.data;\n    },\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: ['merchant', 'settings'] });\n    },\n    ...queryOptions.withErrorHandling(),\n  });\n};\n\n// API Hooks Export\nconst merchantApi = {\n  useMerchant,\n  useMerchantStats,\n  useCollections,\n  useTransactions,\n  useTraders,\n  useMerchantSettings,\n  useUpdateMerchantSettings,\n};\n\nexport default merchantApi;\n", "import * as React from \"react\"\nimport { cn } from \"../../lib/utils\"\n\nfunction Skeleton({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) {\n  return (\n    <div\n      className={cn(\"animate-pulse rounded-md bg-gray-100 dark:bg-gray-800\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Skeleton }\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name CreditCard\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iMTQiIHg9IjIiIHk9IjUiIHJ4PSIyIiAvPgogIDxsaW5lIHgxPSIyIiB4Mj0iMjIiIHkxPSIxMCIgeTI9IjEwIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/credit-card\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CreditCard = createLucideIcon('CreditCard', [\n  [\n    'rect',\n    { width: '20', height: '14', x: '2', y: '5', rx: '2', key: 'ynyp8z' },\n  ],\n  ['line', { x1: '2', x2: '22', y1: '10', y2: '10', key: '1b3vmo' }],\n]);\n\nexport default CreditCard;\n", "\"use client\";\n\nimport { useState } from \"react\";\nimport { useAuth } from \"../../contexts/AuthContext\";\nimport { Button } from \"../../components/ui/button\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"../../components/ui/card\";\nimport { Skeleton } from \"../../components/ui/skeleton\";\nimport { useMerchant, useMerchantStats } from \"../../services/api/merchant\";\nimport { DollarSign, CreditCard, Users, RefreshCw } from \"lucide-react\";\n\nexport default function DashboardPage() {\n  const { user } = useAuth();\n  const [timeRange, setTimeRange] = useState(\"month\");\n  \n  // Fetch data\n  const { data: merchant, isLoading: isLoadingMerchant } = useMerchant();\n  const { data: stats, isLoading: isLoadingStats, refetch } = useMerchantStats();\n\n  const handleRefresh = async () => {\n    await refetch();\n  };\n\n  if (isLoadingMerchant || isLoadingStats) {\n    return (\n      <div className=\"p-6 space-y-6\">\n        <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\">\n          {[1, 2, 3, 4].map((i) => (\n            <Card key={i}>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <Skeleton className=\"h-4 w-24\" />\n                <Skeleton className=\"h-4 w-4\" />\n              </CardHeader>\n              <CardContent>\n                <Skeleton className=\"h-8 w-32 mb-1\" />\n                <Skeleton className=\"h-3 w-24\" />\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"p-6 space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold\">Dashboard</h1>\n          <p className=\"text-muted-foreground\">\n            Welcome back, {merchant?.businessName || 'Merchant'}!\n          </p>\n        </div>\n        <Button variant=\"outline\" size=\"icon\" onClick={handleRefresh}>\n          <RefreshCw className=\"h-4 w-4\" />\n        </Button>\n      </div>\n\n      <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\">\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Total Revenue</CardTitle>\n            <DollarSign className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">\n              ${stats?.totalRevenue?.toLocaleString('en-US', { minimumFractionDigits: 2 }) || '0.00'}\n            </div>\n            <p className=\"text-xs text-muted-foreground\">+20.1% from last {timeRange}</p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Transactions</CardTitle>\n            <CreditCard className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">{stats?.totalTransactions?.toLocaleString() || '0'}</div>\n            <p className=\"text-xs text-muted-foreground\">+12.5% from last {timeRange}</p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Active Traders</CardTitle>\n            <Users className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">{stats?.activeTraders || '0'}</div>\n            <p className=\"text-xs text-muted-foreground\">+2 from last {timeRange}</p>\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  );\n}\n", "import * as React from \"react\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardDescription,\n  CardContent,\n}\n", "import * as React from 'react';\nimport { ToastProps, ToastActionElement } from './toast';\n\ntype ToasterToast = ToastProps & {\n  id: string;\n  title?: React.ReactNode;\n  description?: React.ReactNode;\n};\n\nconst actionTypes = {\n  ADD_TOAST: 'ADD_TOAST',\n  UPDATE_TOAST: 'UPDATE_TOAST',\n  DISMISS_TOAST: 'DISMISS_TOAST',\n  REMOVE_TOAST: 'REMOVE_TOAST',\n} as const;\n\nlet count = 0;\n\nfunction genId() {\n  count = (count + 1) % Number.MAX_SAFE_INTEGER;\n  return count.toString();\n}\n\ntype ActionType = typeof actionTypes;\n\ntype Action =\n  | {\n      type: ActionType['ADD_TOAST'];\n      toast: ToasterToast;\n    }\n  | {\n      type: ActionType['UPDATE_TOAST'];\n      toast: Partial<ToasterToast>;\n    }\n  | {\n      type: ActionType['DISMISS_TOAST'];\n      toastId?: ToasterToast['id'];\n    }\n  | {\n      type: ActionType['REMOVE_TOAST'];\n      toastId?: ToasterToast['id'];\n    };\n\ninterface State {\n  toasts: ToasterToast[];\n}\n\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>();\n\nconst addToRemoveQueue = (toastId: string) => {\n  if (toastTimeouts.has(toastId)) {\n    return;\n  }\n\n  const timeout = setTimeout(() => {\n    toastTimeouts.delete(toastId);\n    dispatch({\n      type: 'REMOVE_TOAST',\n      toastId: toastId,\n    });\n  }, 1000);\n\n  toastTimeouts.set(toastId, timeout);\n};\n\nexport const reducer = (state: State, action: Action): State => {\n  switch (action.type) {\n    case 'ADD_TOAST':\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, 5),\n      };\n\n    case 'UPDATE_TOAST':\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\n        ),\n      };\n\n    case 'DISMISS_TOAST': {\n      const { toastId } = action;\n\n      // ! Side effects ! - This could be extracted into a dismissToast() action,\n      // but I'll keep it here for simplicity\n      if (toastId) {\n        addToRemoveQueue(toastId);\n      } else {\n        state.toasts.forEach((toast) => {\n          addToRemoveQueue(toast.id);\n        });\n      }\n\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                open: false,\n              }\n            : t\n        ),\n      };\n    }\n    case 'REMOVE_TOAST':\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        };\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\n      };\n  }\n};\n\nconst listeners: Array<(state: State) => void> = [];\n\nlet memoryState: State = { toasts: [] };\n\nfunction dispatch(action: Action) {\n  memoryState = reducer(memoryState, action);\n  listeners.forEach((listener) => {\n    listener(memoryState);\n  });\n}\n\ntype Toast = Omit<ToasterToast, 'id'>;\n\nfunction toast({ ...props }: Toast) {\n  const id = genId();\n\n  const update = (props: ToasterToast) =>\n    dispatch({\n      type: 'UPDATE_TOAST',\n      toast: { ...props, id },\n    });\n  const dismiss = () => dispatch({ type: 'DISMISS_TOAST', toastId: id });\n\n  dispatch({\n    type: 'ADD_TOAST',\n    toast: {\n      ...props,\n      id,\n      open: true,\n      onOpenChange: (open: boolean) => {\n        if (!open) dismiss();\n      },\n    },\n  });\n\n  return {\n    id: id,\n    dismiss,\n    update,\n  };\n}\n\nfunction useToast() {\n  const [state, setState] = React.useState<State>(memoryState);\n\n  React.useEffect(() => {\n    listeners.push(setState);\n    return () => {\n      const index = listeners.indexOf(setState);\n      if (index > -1) {\n        listeners.splice(index, 1);\n      }\n    };\n  }, [state]);\n\n  return {\n    ...state,\n    toast,\n    dismiss: (toastId?: string) => dispatch({ type: 'DISMISS_TOAST', toastId }),\n  };\n}\n\nexport { useToast, toast };\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Users\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMjF2LTJhNCA0IDAgMCAwLTQtNEg2YTQgNCAwIDAgMC00IDR2MiIgLz4KICA8Y2lyY2xlIGN4PSI5IiBjeT0iNyIgcj0iNCIgLz4KICA8cGF0aCBkPSJNMjIgMjF2LTJhNCA0IDAgMCAwLTMtMy44NyIgLz4KICA8cGF0aCBkPSJNMTYgMy4xM2E0IDQgMCAwIDEgMCA3Ljc1IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/users\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Users = createLucideIcon('Users', [\n  ['path', { d: 'M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2', key: '1yyitq' }],\n  ['circle', { cx: '9', cy: '7', r: '4', key: 'nufk8' }],\n  ['path', { d: 'M22 21v-2a4 4 0 0 0-3-3.87', key: 'kshegd' }],\n  ['path', { d: 'M16 3.13a4 4 0 0 1 0 7.75', key: '1da9ce' }],\n]);\n\nexport default Users;\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name DollarSign\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjIiIHkyPSIyMiIgLz4KICA8cGF0aCBkPSJNMTcgNUg5LjVhMy41IDMuNSAwIDAgMCAwIDdoNWEzLjUgMy41IDAgMCAxIDAgN0g2IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/dollar-sign\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst DollarSign = createLucideIcon('DollarSign', [\n  ['line', { x1: '12', x2: '12', y1: '2', y2: '22', key: '7eqyqh' }],\n  [\n    'path',\n    { d: 'M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6', key: '1b0p4s' },\n  ],\n]);\n\nexport default DollarSign;\n", "import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": ["TOKEN_KEY", "getToken", "window", "localStorage", "getItem", "API_BASE_URL", "process", "NEXT_PUBLIC_API_URL", "apiClient", "constructor", "client", "this", "axios", "create", "baseURL", "headers", "withCredentials", "setupInterceptors", "interceptors", "request", "use", "config", "token", "Authorization", "error", "Promise", "reject", "response", "_error$response", "_error$response2", "status", "data", "removeItem", "location", "href", "message", "toast", "title", "description", "variant", "get", "url", "post", "put", "delete", "handleError", "errorMessage", "Error", "queryConfig", "defaultOptions", "queries", "staleTime", "retry", "refetchOnWindowFocus", "mutations", "onError", "queryOptions", "QueryClient", "arguments", "length", "undefined", "useErrorBoundary", "keepPreviousData", "ENDPOINTS", "useMerchant", "options", "useQuery", "query<PERSON><PERSON>", "queryFn", "async", "useMerchantStats", "useCollections", "params", "useTransactions", "Skeleton", "_ref", "className", "props", "_jsx", "cn", "CreditCard", "createLucideIcon", "width", "height", "x", "y", "rx", "key", "x1", "x2", "y1", "y2", "DashboardPage", "_stats$totalRevenue", "_stats$totalTransacti", "user", "useAuth", "timeRange", "setTimeRange", "useState", "merchant", "isLoading", "isLoadingMerchant", "stats", "isLoadingStats", "refetch", "children", "map", "i", "_jsxs", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "businessName", "<PERSON><PERSON>", "size", "onClick", "RefreshCw", "CardTitle", "DollarSign", "totalRevenue", "toLocaleString", "minimumFractionDigits", "totalTransactions", "Users", "activeTraders", "React", "ref", "displayName", "_ref2", "_ref3", "CardDescription", "_ref4", "_ref5", "<PERSON><PERSON><PERSON>er", "_ref6", "count", "toastTimeouts", "Map", "addToRemoveQueue", "toastId", "has", "timeout", "setTimeout", "dispatch", "type", "set", "reducer", "state", "action", "toasts", "slice", "t", "id", "for<PERSON>ach", "open", "filter", "listeners", "memoryState", "listener", "Number", "MAX_SAFE_INTEGER", "toString", "dismiss", "onOpenChange", "update", "useToast", "setState", "push", "index", "indexOf", "splice", "d", "cx", "cy", "r", "buttonVariants", "cva", "variants", "default", "destructive", "outline", "secondary", "ghost", "link", "sm", "lg", "icon", "defaultVariants", "<PERSON><PERSON><PERSON><PERSON>", "Comp", "Slot"], "sourceRoot": ""}