"use strict";(self.webpackChunkpayment_gateway_frontend=self.webpackChunkpayment_gateway_frontend||[]).push([[72],{382:(e,t,r)=>{r.d(t,{A0:()=>o,BF:()=>l,Hj:()=>c,XI:()=>s,nA:()=>u,nd:()=>d});var n=r(5043),a=r(3009),i=r(579);const s=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,i.jsx)("div",{className:"w-full overflow-auto",children:(0,i.jsx)("table",{ref:t,className:(0,a.cn)("w-full caption-bottom text-sm",r),...n})})});s.displayName="Table";const o=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,i.jsx)("thead",{ref:t,className:(0,a.cn)("[&_tr]:border-b",r),...n})});o.displayName="TableHeader";const l=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,i.jsx)("tbody",{ref:t,className:(0,a.cn)("[&_tr:last-child]:border-0",r),...n})});l.displayName="TableBody";n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,i.jsx)("tfoot",{ref:t,className:(0,a.cn)("bg-primary font-medium text-primary-foreground",r),...n})}).displayName="TableFooter";const c=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,i.jsx)("tr",{ref:t,className:(0,a.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",r),...n})});c.displayName="TableRow";const d=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,i.jsx)("th",{ref:t,className:(0,a.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",r),...n})});d.displayName="TableHead";const u=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,i.jsx)("td",{ref:t,className:(0,a.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",r),...n})});u.displayName="TableCell";n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,i.jsx)("caption",{ref:t,className:(0,a.cn)("mt-4 text-sm text-muted-foreground",r),...n})}).displayName="TableCaption"},4490:(e,t,r)=>{var n;r.d(t,{B:()=>l});var a=r(5043),i=r(503),s=(n||(n=r.t(a,2)))[" useId ".trim().toString()]||(()=>{}),o=0;function l(e){const[t,r]=a.useState(s());return(0,i.N)(()=>{e||r(e=>e??String(o++))},[e]),e||(t?`radix-${t}`:"")}},5595:(e,t,r)=>{r.r(t),r.d(t,{default:()=>ie});var n=r(5043),a=r(9066),i=r(3216),s=(r(7842),r(9772)),o=r(6742),l=r(6736),c=r(382),d=r(6879),u=r(9120),p=r(3797);const m=(0,p.A)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]]),f=(0,p.A)("BarChart2",[["line",{x1:"18",x2:"18",y1:"20",y2:"10",key:"1xfpm4"}],["line",{x1:"12",x2:"12",y1:"20",y2:"4",key:"be30l9"}],["line",{x1:"6",x2:"6",y1:"20",y2:"14",key:"1r4le6"}]]),h=(0,p.A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]);var y=r(108),x=r(8420),v=r(1744),g=r(1629),b=r.n(g),j=r(9686),N=r.n(j),A=r(9853),w=r.n(A),S=r(8387),O=r(8471),T=r(8892),k=r(1639),D=r(1519),P=r(8813),E=r(6307),C=r(240),R=r(6015),I=r(202),B=["type","layout","connectNulls","ref"],_=["key"];function L(e){return L="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},L(e)}function F(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}function W(){return W=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},W.apply(this,arguments)}function Z(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function U(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Z(Object(r),!0).forEach(function(t){J(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Z(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function M(e){return function(e){if(Array.isArray(e))return V(e)}(e)||function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"===typeof e)return V(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return V(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function V(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function H(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,G(n.key),n)}}function z(e,t,r){return t=$(t),function(e,t){if(t&&("object"===L(t)||"function"===typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,X()?Reflect.construct(t,r||[],$(e).constructor):t.apply(e,r))}function X(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(X=function(){return!!e})()}function $(e){return $=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},$(e)}function K(e,t){return K=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},K(e,t)}function J(e,t,r){return(t=G(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function G(e){var t=function(e,t){if("object"!=L(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=L(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==L(t)?t:t+""}var q=function(e){function t(){var e;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);for(var r=arguments.length,n=new Array(r),a=0;a<r;a++)n[a]=arguments[a];return J(e=z(this,t,[].concat(n)),"state",{isAnimationFinished:!0,totalLength:0}),J(e,"generateSimpleStrokeDasharray",function(e,t){return"".concat(t,"px ").concat(e-t,"px")}),J(e,"getStrokeDasharray",function(r,n,a){var i=a.reduce(function(e,t){return e+t});if(!i)return e.generateSimpleStrokeDasharray(n,r);for(var s=Math.floor(r/i),o=r%i,l=n-r,c=[],d=0,u=0;d<a.length;u+=a[d],++d)if(u+a[d]>o){c=[].concat(M(a.slice(0,d)),[o-u]);break}var p=c.length%2===0?[0,l]:[l];return[].concat(M(t.repeat(a,s)),M(c),p).map(function(e){return"".concat(e,"px")}).join(", ")}),J(e,"id",(0,E.NF)("recharts-line-")),J(e,"pathRef",function(t){e.mainCurve=t}),J(e,"handleAnimationEnd",function(){e.setState({isAnimationFinished:!0}),e.props.onAnimationEnd&&e.props.onAnimationEnd()}),J(e,"handleAnimationStart",function(){e.setState({isAnimationFinished:!1}),e.props.onAnimationStart&&e.props.onAnimationStart()}),e}return function(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&K(e,t)}(t,e),r=t,i=[{key:"getDerivedStateFromProps",value:function(e,t){return e.animationId!==t.prevAnimationId?{prevAnimationId:e.animationId,curPoints:e.points,prevPoints:t.curPoints}:e.points!==t.curPoints?{curPoints:e.points}:null}},{key:"repeat",value:function(e,t){for(var r=e.length%2!==0?[].concat(M(e),[0]):e,n=[],a=0;a<t;++a)n=[].concat(M(n),M(r));return n}},{key:"renderDotItem",value:function(e,t){var r;if(n.isValidElement(e))r=n.cloneElement(e,t);else if(b()(e))r=e(t);else{var a=t.key,i=F(t,_),s=(0,S.A)("recharts-line-dot","boolean"!==typeof e?e.className:"");r=n.createElement(T.c,W({key:a},i,{className:s}))}return r}}],(a=[{key:"componentDidMount",value:function(){if(this.props.isAnimationActive){var e=this.getTotalLength();this.setState({totalLength:e})}}},{key:"componentDidUpdate",value:function(){if(this.props.isAnimationActive){var e=this.getTotalLength();e!==this.state.totalLength&&this.setState({totalLength:e})}}},{key:"getTotalLength",value:function(){var e=this.mainCurve;try{return e&&e.getTotalLength&&e.getTotalLength()||0}catch(t){return 0}}},{key:"renderErrorBar",value:function(e,t){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,a=r.points,i=r.xAxis,s=r.yAxis,o=r.layout,l=r.children,c=(0,C.aS)(l,P.u);if(!c)return null;var d=function(e,t){return{x:e.x,y:e.y,value:e.value,errorVal:(0,I.kr)(e.payload,t)}},u={clipPath:e?"url(#clipPath-".concat(t,")"):null};return n.createElement(k.W,u,c.map(function(e){return n.cloneElement(e,{key:"bar-".concat(e.props.dataKey),data:a,xAxis:i,yAxis:s,layout:o,dataPointFormatter:d})}))}},{key:"renderDots",value:function(e,r,a){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var i=this.props,s=i.dot,o=i.points,l=i.dataKey,c=(0,C.J9)(this.props,!1),d=(0,C.J9)(s,!0),u=o.map(function(e,r){var n=U(U(U({key:"dot-".concat(r),r:3},c),d),{},{index:r,cx:e.x,cy:e.y,value:e.value,dataKey:l,payload:e.payload,points:o});return t.renderDotItem(s,n)}),p={clipPath:e?"url(#clipPath-".concat(r?"":"dots-").concat(a,")"):null};return n.createElement(k.W,W({className:"recharts-line-dots",key:"dots"},p),u)}},{key:"renderCurveStatically",value:function(e,t,r,a){var i=this.props,s=i.type,o=i.layout,l=i.connectNulls,c=(i.ref,F(i,B)),d=U(U(U({},(0,C.J9)(c,!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:t?"url(#clipPath-".concat(r,")"):null,points:e},a),{},{type:s,layout:o,connectNulls:l});return n.createElement(O.I,W({},d,{pathRef:this.pathRef}))}},{key:"renderCurveWithAnimation",value:function(e,t){var r=this,a=this.props,i=a.points,s=a.strokeDasharray,o=a.isAnimationActive,l=a.animationBegin,c=a.animationDuration,d=a.animationEasing,u=a.animationId,p=a.animateNewValues,m=a.width,f=a.height,h=this.state,y=h.prevPoints,x=h.totalLength;return n.createElement(v.Ay,{begin:l,duration:c,isActive:o,easing:d,from:{t:0},to:{t:1},key:"line-".concat(u),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(n){var a=n.t;if(y){var o=y.length/i.length,l=i.map(function(e,t){var r=Math.floor(t*o);if(y[r]){var n=y[r],i=(0,E.Dj)(n.x,e.x),s=(0,E.Dj)(n.y,e.y);return U(U({},e),{},{x:i(a),y:s(a)})}if(p){var l=(0,E.Dj)(2*m,e.x),c=(0,E.Dj)(f/2,e.y);return U(U({},e),{},{x:l(a),y:c(a)})}return U(U({},e),{},{x:e.x,y:e.y})});return r.renderCurveStatically(l,e,t)}var c,d=(0,E.Dj)(0,x)(a);if(s){var u="".concat(s).split(/[,\s]+/gim).map(function(e){return parseFloat(e)});c=r.getStrokeDasharray(d,x,u)}else c=r.generateSimpleStrokeDasharray(x,d);return r.renderCurveStatically(i,e,t,{strokeDasharray:c})})}},{key:"renderCurve",value:function(e,t){var r=this.props,n=r.points,a=r.isAnimationActive,i=this.state,s=i.prevPoints,o=i.totalLength;return a&&n&&n.length&&(!s&&o>0||!w()(s,n))?this.renderCurveWithAnimation(e,t):this.renderCurveStatically(n,e,t)}},{key:"render",value:function(){var e,t=this.props,r=t.hide,a=t.dot,i=t.points,s=t.className,o=t.xAxis,l=t.yAxis,c=t.top,d=t.left,u=t.width,p=t.height,m=t.isAnimationActive,f=t.id;if(r||!i||!i.length)return null;var h=this.state.isAnimationFinished,y=1===i.length,x=(0,S.A)("recharts-line",s),v=o&&o.allowDataOverflow,g=l&&l.allowDataOverflow,b=v||g,j=N()(f)?this.id:f,A=null!==(e=(0,C.J9)(a,!1))&&void 0!==e?e:{r:3,strokeWidth:2},w=A.r,O=void 0===w?3:w,T=A.strokeWidth,P=void 0===T?2:T,E=((0,C.sT)(a)?a:{}).clipDot,R=void 0===E||E,I=2*O+P;return n.createElement(k.W,{className:x},v||g?n.createElement("defs",null,n.createElement("clipPath",{id:"clipPath-".concat(j)},n.createElement("rect",{x:v?d:d-u/2,y:g?c:c-p/2,width:v?u:2*u,height:g?p:2*p})),!R&&n.createElement("clipPath",{id:"clipPath-dots-".concat(j)},n.createElement("rect",{x:d-I/2,y:c-I/2,width:u+I,height:p+I}))):null,!y&&this.renderCurve(b,j),this.renderErrorBar(b,j),(y||a)&&this.renderDots(b,R,j),(!m||h)&&D.Z.renderCallByParent(this.props,i))}}])&&H(r.prototype,a),i&&H(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,a,i}(n.PureComponent);J(q,"displayName","Line"),J(q,"defaultProps",{xAxisId:0,yAxisId:0,connectNulls:!1,activeDot:!0,dot:!0,legendType:"line",stroke:"#3182bd",strokeWidth:1,fill:"#fff",points:[],isAnimationActive:!R.m.isSsr,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",hide:!1,label:!1}),J(q,"getComposedData",function(e){var t=e.props,r=e.xAxis,n=e.yAxis,a=e.xAxisTicks,i=e.yAxisTicks,s=e.dataKey,o=e.bandSize,l=e.displayedData,c=e.offset,d=t.layout;return U({points:l.map(function(e,t){var l=(0,I.kr)(e,s);return"horizontal"===d?{x:(0,I.nb)({axis:r,ticks:a,bandSize:o,entry:e,index:t}),y:N()(l)?null:n.scale(l),value:l,payload:e}:{x:N()(l)?null:r.scale(l),y:(0,I.nb)({axis:n,ticks:i,bandSize:o,entry:e,index:t}),value:l,payload:e}}),layout:d},c)});var Q=r(2185),Y=r(6026),ee=r(3831),te=(0,x.gu)({chartName:"LineChart",GraphicalChild:q,axisComponents:[{axisType:"xAxis",AxisComp:Q.W},{axisType:"yAxis",AxisComp:Y.h}],formatAxisMap:ee.pr}),re=r(7734),ne=r(6150),ae=r(579);const ie=()=>{const{user:e,logout:t}=(0,a.A)(),{toast:r}=(0,d.dj)(),p=(0,i.Zp)(),[x,v]=(0,n.useState)(!0),[g,b]=(0,n.useState)("overview"),[j,N]=(0,n.useState)("24h"),[A,w]=(0,n.useState)(!0),S={totalVolume:12345.67,totalTrades:1234,activePairs:24,dailyVolume:1234.56,dailyChange:-2.3,pnl:1234.56,pnlPercent:12.5,winRate:67.8,avgTradeSize:456.78},O=[{name:"Total Volume",value:`$${S.totalVolume.toLocaleString(void 0,{minimumFractionDigits:2,maximumFractionDigits:2})}`,change:S.dailyChange,icon:u.A},{name:"Total Trades",value:S.totalTrades.toLocaleString(),icon:m},{name:"Active Pairs",value:S.activePairs,icon:f},{name:"Daily P&L",value:`$${S.pnl.toLocaleString(void 0,{minimumFractionDigits:2,maximumFractionDigits:2})}`,change:S.pnlPercent,icon:h}],T=[{id:"TRD123",pair:"BTC/USD",type:"buy",price:42123.45,amount:.5,total:21061.73,fee:25,status:"completed",date:(new Date).toISOString(),time:"10:23:45"},{id:"TRD124",pair:"ETH/USD",type:"sell",price:2345.67,amount:2.1,total:4925.91,fee:12.31,status:"completed",date:(new Date).toISOString(),time:"09:45:12"},{id:"TRD125",pair:"SOL/USD",type:"buy",price:123.45,amount:50,total:6172.5,fee:15.43,status:"pending",date:(new Date).toISOString(),time:"08:12:33"}];return(0,ae.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,ae.jsx)("header",{className:"bg-white shadow",children:(0,ae.jsxs)("div",{className:"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8 flex justify-between items-center",children:[(0,ae.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Trader Dashboard"}),(0,ae.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,ae.jsxs)("div",{className:"text-right",children:[(0,ae.jsx)("p",{className:"text-sm text-gray-500",children:"Connected as"}),(0,ae.jsx)("p",{className:"font-medium",children:null===e||void 0===e?void 0:e.email})]}),(0,ae.jsx)(s.$,{onClick:()=>{t(),p("/login")},variant:"outline",children:"Logout"})]})]})}),(0,ae.jsxs)("main",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:[(0,ae.jsx)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-8",children:O.map(e=>{const t=e.icon;return(0,ae.jsxs)(o.Zp,{children:[(0,ae.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,ae.jsx)(o.ZB,{className:"text-sm font-medium",children:e.name}),(0,ae.jsx)(t,{className:"h-4 w-4 text-muted-foreground"})]}),(0,ae.jsxs)(o.Wu,{children:[(0,ae.jsx)("div",{className:"text-2xl font-bold",children:e.value}),void 0!==e.change&&(0,ae.jsxs)("p",{className:"text-xs "+(e.change>=0?"text-green-500":"text-red-500"),children:[e.change>=0?"+":"",e.change,"% from yesterday"]})]})]},e.name)})}),(0,ae.jsxs)("div",{className:"grid gap-4 md:grid-cols-3",children:[(0,ae.jsx)("div",{className:"md:col-span-2",children:(0,ae.jsxs)(o.Zp,{children:[(0,ae.jsx)(o.aR,{children:(0,ae.jsx)(o.ZB,{children:"BTC/USD Price"})}),(0,ae.jsx)(o.Wu,{className:"pl-2",children:(0,ae.jsx)("div",{className:"h-[300px]",children:(0,ae.jsx)(y.u,{width:"100%",height:"100%",children:(0,ae.jsxs)(te,{data:[{name:"9:00",price:4e4},{name:"10:00",price:40500},{name:"11:00",price:39800},{name:"12:00",price:41e3},{name:"13:00",price:41500},{name:"14:00",price:41200},{name:"15:00",price:41800}],children:[(0,ae.jsx)(re.d,{strokeDasharray:"3 3"}),(0,ae.jsx)(Q.W,{dataKey:"name"}),(0,ae.jsx)(Y.h,{domain:["auto","auto"]}),(0,ae.jsx)(ne.m,{}),(0,ae.jsx)(q,{type:"monotone",dataKey:"price",stroke:"#8884d8",strokeWidth:2,dot:!1,activeDot:{r:6}})]})})})})]})}),(0,ae.jsx)("div",{children:(0,ae.jsxs)(o.Zp,{children:[(0,ae.jsx)(o.aR,{children:(0,ae.jsx)(o.ZB,{children:"Recent Trades"})}),(0,ae.jsx)(o.Wu,{children:(0,ae.jsxs)(c.XI,{children:[(0,ae.jsx)(c.A0,{children:(0,ae.jsxs)(c.Hj,{children:[(0,ae.jsx)(c.nd,{children:"Pair"}),(0,ae.jsx)(c.nd,{children:"Type"}),(0,ae.jsx)(c.nd,{children:"Price"}),(0,ae.jsx)(c.nd,{children:"Amount"}),(0,ae.jsx)(c.nd,{children:"Total"})]})}),(0,ae.jsx)(c.BF,{children:T.map(e=>(0,ae.jsxs)(c.Hj,{children:[(0,ae.jsx)(c.nA,{className:"font-medium",children:e.pair}),(0,ae.jsx)(c.nA,{className:"buy"===e.type.toLowerCase()?"text-green-500":"text-red-500",children:e.type.charAt(0).toUpperCase()+e.type.slice(1)}),(0,ae.jsx)(c.nA,{children:e.price}),(0,ae.jsx)(c.nA,{children:e.amount}),(0,ae.jsx)(c.nA,{children:e.total})]},e.id))})]})})]})})]}),(0,ae.jsx)("div",{className:"mt-8 ",children:(0,ae.jsxs)(l.tU,{defaultValue:"open-orders",className:"space-y-4",children:[(0,ae.jsxs)(l.j7,{children:[(0,ae.jsx)(l.Xi,{value:"open-orders",children:"Open Orders"}),(0,ae.jsx)(l.Xi,{value:"order-history",children:"Order History"}),(0,ae.jsx)(l.Xi,{value:"positions",children:"Positions"})]}),(0,ae.jsx)(l.av,{value:"open-orders",children:(0,ae.jsxs)(o.Zp,{children:[(0,ae.jsx)(o.aR,{children:(0,ae.jsx)(o.ZB,{children:"Open Orders"})}),(0,ae.jsx)(o.Wu,{children:(0,ae.jsx)("p",{className:"text-gray-500 text-center py-8",children:"No open orders"})})]})}),(0,ae.jsx)(l.av,{value:"order-history",children:(0,ae.jsxs)(o.Zp,{children:[(0,ae.jsx)(o.aR,{children:(0,ae.jsx)(o.ZB,{children:"Order History"})}),(0,ae.jsx)(o.Wu,{children:(0,ae.jsx)("p",{className:"text-gray-500 text-center py-8",children:"No order history available"})})]})}),(0,ae.jsx)(l.av,{value:"positions",children:(0,ae.jsxs)(o.Zp,{children:[(0,ae.jsx)(o.aR,{children:(0,ae.jsx)(o.ZB,{children:"Positions"})}),(0,ae.jsx)(o.Wu,{children:(0,ae.jsx)("p",{className:"text-gray-500 text-center py-8",children:"No open positions"})})]})})]})})]})]})}},6736:(e,t,r)=>{r.d(t,{Xi:()=>c,av:()=>d,j7:()=>l,tU:()=>o});var n=r(5043),a=r(7127),i=r(3009),s=r(579);const o=a.bL,l=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)(a.B8,{ref:t,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",r),...n})});l.displayName=a.B8.displayName;const c=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)(a.l9,{ref:t,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",r),...n})});c.displayName=a.l9.displayName;const d=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)(a.UC,{ref:t,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",r),...n})});d.displayName=a.UC.displayName},6742:(e,t,r)=>{r.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>l,Zp:()=>s,aR:()=>o,wL:()=>u});var n=r(5043),a=r(3009),i=r(579);const s=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,i.jsx)("div",{ref:t,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...n})});s.displayName="Card";const o=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,i.jsx)("div",{ref:t,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",r),...n})});o.displayName="CardHeader";const l=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,i.jsx)("h3",{ref:t,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",r),...n})});l.displayName="CardTitle";const c=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,i.jsx)("p",{ref:t,className:(0,a.cn)("text-sm text-muted-foreground",r),...n})});c.displayName="CardDescription";const d=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,i.jsx)("div",{ref:t,className:(0,a.cn)("p-6 pt-0",r),...n})});d.displayName="CardContent";const u=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,i.jsx)("div",{ref:t,className:(0,a.cn)("flex items-center p-6 pt-0",r),...n})});u.displayName="CardFooter"},6879:(e,t,r)=>{r.d(t,{dj:()=>p,oR:()=>u});var n=r(5043);let a=0;const i=new Map,s=e=>{if(i.has(e))return;const t=setTimeout(()=>{i.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e3);i.set(e,t)},o=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,5)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{const{toastId:r}=t;return r?s(r):e.toasts.forEach(e=>{s(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[];let c={toasts:[]};function d(e){c=o(c,e),l.forEach(e=>{e(c)})}function u(e){let{...t}=e;const r=(a=(a+1)%Number.MAX_SAFE_INTEGER,a.toString()),n=()=>d({type:"DISMISS_TOAST",toastId:r});return d({type:"ADD_TOAST",toast:{...t,id:r,open:!0,onOpenChange:e=>{e||n()}}}),{id:r,dismiss:n,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:r}})}}function p(){const[e,t]=n.useState(c);return n.useEffect(()=>(l.push(t),()=>{const e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}},9772:(e,t,r)=>{r.d(t,{$:()=>c});var n=r(5043),a=r(6851),i=r(917),s=r(3009),o=r(579);const l=(0,i.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=n.forwardRef((e,t)=>{let{className:r,variant:n,size:i,asChild:c=!1,...d}=e;const u=c?a.DX:"button";return(0,o.jsx)(u,{className:(0,s.cn)(l({variant:n,size:i,className:r})),ref:t,...d})});c.displayName="Button"}}]);
//# sourceMappingURL=72.4d3e6b52.chunk.js.map