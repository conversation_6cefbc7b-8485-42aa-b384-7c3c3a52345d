[{"_id": "6881cde138f0c102b1675cf8", "transactionId": "txn_30f7e1f5b948ba83758cb169ca6b2a12", "merchantId": "6881cde038f0c102b1675cef", "traderId": "6881cde138f0c102b1675cf6", "amount": 99.99, "currency": "USD", "status": "completed", "paymentMethod": "card", "paymentDetails": {"cardLast4": "4242", "cardBrand": "Visa"}, "customerInfo": {"email": "<EMAIL>", "name": "Customer One", "billingAddress": {"street": "123 Main St", "city": "New York", "state": "NY", "zipCode": "10001", "country": "US"}}, "fees": {"processingFee": 3.2, "platformFee": 0.5}, "webhookDelivered": false, "webhookAttempts": 0, "refundAmount": 0, "disputeStatus": "none", "riskScore": 0, "settlementDate": "2025-07-26T06:08:33.952Z", "notes": [], "createdAt": "2025-07-24T06:08:33.957Z", "updatedAt": "2025-07-24T06:08:33.957Z", "netAmount": 96.28999999999999, "__v": 0}, {"_id": "6881cde138f0c102b1675cfa", "transactionId": "txn_7582d6474b6777b307629abee7b79c30", "merchantId": "6881cde138f0c102b1675cf4", "amount": 249.99, "currency": "USD", "status": "completed", "paymentMethod": "card", "paymentDetails": {"cardLast4": "1234", "cardBrand": "Mastercard"}, "customerInfo": {"email": "<EMAIL>", "name": "Customer Two", "billingAddress": {"street": "456 Oak Ave", "city": "Los Angeles", "state": "CA", "zipCode": "90210", "country": "US"}}, "fees": {"processingFee": 7.55, "platformFee": 1.25}, "webhookDelivered": false, "webhookAttempts": 0, "refundAmount": 0, "disputeStatus": "none", "riskScore": 15, "settlementDate": "2025-07-26T06:08:33.952Z", "notes": [], "createdAt": "2025-07-24T06:08:33.961Z", "updatedAt": "2025-07-24T06:08:33.961Z", "netAmount": 241.19, "__v": 0}, {"_id": "6881cde138f0c102b1675cfc", "transactionId": "txn_03c5dfab9234744253b7019e53af7940", "merchantId": "6881cde038f0c102b1675cef", "amount": 49.99, "currency": "USD", "status": "failed", "paymentMethod": "card", "paymentDetails": {"cardLast4": "0000", "cardBrand": "Visa"}, "customerInfo": {"email": "<EMAIL>", "name": "Customer Three"}, "fees": {"processingFee": 1.75, "platformFee": 0.25}, "webhookDelivered": false, "webhookAttempts": 0, "refundAmount": 0, "disputeStatus": "none", "riskScore": 25, "notes": [], "createdAt": "2025-07-24T06:08:33.964Z", "updatedAt": "2025-07-24T06:08:33.964Z", "netAmount": 47.99, "__v": 0}, {"_id": "68835ac1ea354209faabe0b9", "transactionId": "txn_57a6d25cac3318e30ee47d0945c2ab8d", "merchantId": "68835abfea354209faabe0b3", "traderId": "68835ac0ea354209faabe0b7", "amount": 99.99, "currency": "USD", "status": "completed", "paymentMethod": "card", "paymentDetails": {"cardLast4": "4242", "cardBrand": "Visa"}, "customerInfo": {"email": "<EMAIL>", "name": "Customer One", "billingAddress": {"street": "123 Main St", "city": "New York", "state": "NY", "zipCode": "10001", "country": "US"}}, "fees": {"processingFee": 3.2, "platformFee": 0.5}, "webhookDelivered": false, "webhookAttempts": 0, "refundAmount": 0, "disputeStatus": "none", "riskScore": 0, "settlementDate": "2025-07-27T10:21:53.746Z", "notes": [], "createdAt": "2025-07-25T10:21:53.756Z", "updatedAt": "2025-07-25T10:21:53.756Z", "netAmount": 96.28999999999999, "__v": 0}, {"_id": "68835ac1ea354209faabe0bb", "transactionId": "txn_70baa55dec0641e04339f30c634ebae3", "merchantId": "68835ac0ea354209faabe0b5", "amount": 249.99, "currency": "USD", "status": "completed", "paymentMethod": "card", "paymentDetails": {"cardLast4": "1234", "cardBrand": "Mastercard"}, "customerInfo": {"email": "<EMAIL>", "name": "Customer Two", "billingAddress": {"street": "456 Oak Ave", "city": "Los Angeles", "state": "CA", "zipCode": "90210", "country": "US"}}, "fees": {"processingFee": 7.55, "platformFee": 1.25}, "webhookDelivered": false, "webhookAttempts": 0, "refundAmount": 0, "disputeStatus": "none", "riskScore": 15, "settlementDate": "2025-07-27T10:21:53.746Z", "notes": [], "createdAt": "2025-07-25T10:21:53.778Z", "updatedAt": "2025-07-25T10:21:53.778Z", "netAmount": 241.19, "__v": 0}, {"_id": "68835ac1ea354209faabe0bd", "transactionId": "txn_0b3f4b0eaa81f07ff0a9d181300e8702", "merchantId": "68835abfea354209faabe0b3", "amount": 49.99, "currency": "USD", "status": "failed", "paymentMethod": "card", "paymentDetails": {"cardLast4": "0000", "cardBrand": "Visa"}, "customerInfo": {"email": "<EMAIL>", "name": "Customer Three"}, "fees": {"processingFee": 1.75, "platformFee": 0.25}, "webhookDelivered": false, "webhookAttempts": 0, "refundAmount": 0, "disputeStatus": "none", "riskScore": 25, "notes": [], "createdAt": "2025-07-25T10:21:53.784Z", "updatedAt": "2025-07-25T10:21:53.784Z", "netAmount": 47.99, "__v": 0}]