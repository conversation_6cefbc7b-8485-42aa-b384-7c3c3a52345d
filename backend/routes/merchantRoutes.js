const express = require('express');
const router = express.Router();
const { protect, authorize } = require('../middleware/auth');
const upload = require('../middleware/upload');
const {
  createOrUpdateMerchant,
  submitForVerification,
  getMerchantProfile,
  getAllMerchants,
  updateMerchantStatus
} = require('../controllers/merchantController');

// Merchant routes
router.route('/')
  .post(
    protect,
    upload.fields([
      { name: 'documentFront', maxCount: 1 },
      { name: 'documentBack', maxCount: 1 }
    ]),
    createOrUpdateMerchant
  )
  .get(protect, getMerchantProfile);

router.post('/submit-verification', protect, submitForVerification);

// Admin routes
router.get('/admin/merchants', 
  protect, 
  authorize('admin'),
  getAllMerchants
);

router.put('/admin/merchants/:id/status',
  protect,
  authorize('admin'),
  updateMerchantStatus
);

module.exports = router;
