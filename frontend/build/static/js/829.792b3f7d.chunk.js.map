{"version": 3, "file": "static/js/829.792b3f7d.chunk.js", "mappings": "8LASO,SAASA,EAAoBC,GAGL,IAHM,MACnCC,EAAK,YACLC,EAAc,+DAC<PERSON>F,EAC1B,MAAMG,GAAWC,EAAAA,EAAAA,MAEjB,OACEC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yBAAwBC,UACrCC,EAAAA,EAAAA,MAACC,EAAAA,GAAI,CAAAF,SAAA,EACHF,EAAAA,EAAAA,KAACK,EAAAA,GAAU,CAAAH,UACTF,EAAAA,EAAAA,KAACM,EAAAA,GAAS,CAACL,UAAU,qBAAoBC,SAAEN,OAE7CO,EAAAA,EAAAA,MAACI,EAAAA,GAAW,CAACN,UAAU,YAAWC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wBAAuBC,SAAEL,KACtCM,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBC,SAAA,EAC7BF,EAAAA,EAAAA,KAACQ,EAAAA,EAAM,CAACC,QAASA,IAAMX,GAAU,GAAIY,QAAQ,UAASR,SAAC,aAGvDF,EAAAA,EAAAA,KAACQ,EAAAA,EAAM,CAACC,QAASA,IAAMX,EAAS,UAAUI,SAAC,kCAQvD,C,wFCjCO,SAASS,IACd,OACEX,EAAAA,EAAAA,KAACN,EAAAA,EAAoB,CACnBE,MAAM,kBACNC,YAAY,oFAGlB,CAEA,S,iHCPA,MAAMO,EAAOQ,EAAAA,WAGX,CAAAjB,EAA0BkB,KAAG,IAA5B,UAAEZ,KAAca,GAAOnB,EAAA,OACxBK,EAAAA,EAAAA,KAAA,OACEa,IAAKA,EACLZ,WAAWc,EAAAA,EAAAA,IACT,2DACAd,MAEEa,MAGRV,EAAKY,YAAc,OAEnB,MAAMX,EAAaO,EAAAA,WAGjB,CAAAK,EAA0BJ,KAAG,IAA5B,UAAEZ,KAAca,GAAOG,EAAA,OACxBjB,EAAAA,EAAAA,KAAA,OACEa,IAAKA,EACLZ,WAAWc,EAAAA,EAAAA,IAAG,gCAAiCd,MAC3Ca,MAGRT,EAAWW,YAAc,aAEzB,MAAMV,EAAYM,EAAAA,WAGhB,CAAAM,EAA0BL,KAAG,IAA5B,UAAEZ,KAAca,GAAOI,EAAA,OACxBlB,EAAAA,EAAAA,KAAA,MACEa,IAAKA,EACLZ,WAAWc,EAAAA,EAAAA,IACT,qDACAd,MAEEa,MAGRR,EAAUU,YAAc,YAExB,MAAMG,EAAkBP,EAAAA,WAGtB,CAAAQ,EAA0BP,KAAG,IAA5B,UAAEZ,KAAca,GAAOM,EAAA,OACxBpB,EAAAA,EAAAA,KAAA,KACEa,IAAKA,EACLZ,WAAWc,EAAAA,EAAAA,IAAG,gCAAiCd,MAC3Ca,MAGRK,EAAgBH,YAAc,kBAE9B,MAAMT,EAAcK,EAAAA,WAGlB,CAAAS,EAA0BR,KAAG,IAA5B,UAAEZ,KAAca,GAAOO,EAAA,OACxBrB,EAAAA,EAAAA,KAAA,OAAKa,IAAKA,EAAKZ,WAAWc,EAAAA,EAAAA,IAAG,WAAYd,MAAgBa,MAE3DP,EAAYS,YAAc,cAE1B,MAAMM,EAAaV,EAAAA,WAGjB,CAAAW,EAA0BV,KAAG,IAA5B,UAAEZ,KAAca,GAAOS,EAAA,OACxBvB,EAAAA,EAAAA,KAAA,OACEa,IAAKA,EACLZ,WAAWc,EAAAA,EAAAA,IAAG,6BAA8Bd,MACxCa,MAGRQ,EAAWN,YAAc,Y,sFCtEzB,MAAMQ,GAAiBC,EAAAA,EAAAA,GACrB,yRACA,CACEC,SAAU,CACRhB,QAAS,CACPiB,QAAS,yDACTC,YACE,qEACFC,QACE,iFACFC,UACE,+DACFC,MAAO,+CACPC,KAAM,mDAERC,KAAM,CACJN,QAAS,iBACTO,GAAI,sBACJC,GAAI,uBACJC,KAAM,cAGVC,gBAAiB,CACf3B,QAAS,UACTuB,KAAM,aAWNzB,EAASI,EAAAA,WACb,CAAAjB,EAA0DkB,KAAS,IAAlE,UAAEZ,EAAS,QAAES,EAAO,KAAEuB,EAAI,QAAEK,GAAU,KAAUxB,GAAOnB,EACtD,MAAM4C,EAAOD,EAAUE,EAAAA,GAAO,SAC9B,OACExC,EAAAA,EAAAA,KAACuC,EAAI,CACHtC,WAAWc,EAAAA,EAAAA,IAAGS,EAAe,CAAEd,UAASuB,OAAMhC,eAC9CY,IAAKA,KACDC,MAKZN,EAAOQ,YAAc,Q", "sources": ["pages/admin/AdminPagePlaceholder.tsx", "pages/admin/SettingsPage.tsx", "components/ui/card.tsx", "components/ui/button.tsx"], "sourcesContent": ["import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from \"../../components/ui/card\";\nimport { Button } from \"../../components/ui/button\";\nimport { useNavigate } from \"react-router-dom\";\n\ninterface AdminPagePlaceholderProps {\n  title: string;\n  description?: string;\n}\n\nexport function AdminPagePlaceholder({ \n  title, \n  description = \"This page is under construction and will be available soon.\" \n}: AdminPagePlaceholderProps) {\n  const navigate = useNavigate();\n  \n  return (\n    <div className=\"container mx-auto py-8\">\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"text-2xl font-bold\">{title}</CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <p className=\"text-muted-foreground\">{description}</p>\n          <div className=\"flex space-x-4\">\n            <Button onClick={() => navigate(-1)} variant=\"outline\">\n              Go Back\n            </Button>\n            <Button onClick={() => navigate('/admin')}>\n              Return to Dashboard\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n\nexport default AdminPagePlaceholder;\n", "import { AdminPagePlaceholder } from './AdminPagePlaceholder';\n\nexport function SettingsPage() {\n  return (\n    <AdminPagePlaceholder \n      title=\"System Settings\"\n      description=\"Configure system settings, manage integrations, and update platform preferences.\"\n    />\n  );\n}\n\nexport default SettingsPage;\n", "import * as React from \"react\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardDescription,\n  CardContent,\n}\n", "import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": ["AdminPagePlaceholder", "_ref", "title", "description", "navigate", "useNavigate", "_jsx", "className", "children", "_jsxs", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "onClick", "variant", "SettingsPage", "React", "ref", "props", "cn", "displayName", "_ref2", "_ref3", "CardDescription", "_ref4", "_ref5", "<PERSON><PERSON><PERSON>er", "_ref6", "buttonVariants", "cva", "variants", "default", "destructive", "outline", "secondary", "ghost", "link", "size", "sm", "lg", "icon", "defaultVariants", "<PERSON><PERSON><PERSON><PERSON>", "Comp", "Slot"], "sourceRoot": ""}