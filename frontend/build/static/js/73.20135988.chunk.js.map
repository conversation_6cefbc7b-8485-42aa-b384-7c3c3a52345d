{"version": 3, "file": "static/js/73.20135988.chunk.js", "mappings": "qMAoFO,MAAMA,UAKHC,EAAAA,EAWRC,WAAAA,CAAYC,GACVC,QAEAC,KAAKC,eAAiBH,EAAOG,eAC7BD,KAAKE,WAAaJ,EAAOI,WACzBF,KAAKG,cAAgBL,EAAOK,cAC5BH,KAAKI,OAASN,EAAOM,QAAUC,EAAAA,EAC/BL,KAAKM,UAAY,GACjBN,KAAKO,MAAQT,EAAOS,OAASC,IAE7BR,KAAKS,WAAWX,EAAOY,SACvBV,KAAKW,YACN,CAEDF,UAAAA,CACEC,GAEAV,KAAKU,QAAU,IAAKV,KAAKC,kBAAmBS,GAE5CV,KAAKY,gBAAgBZ,KAAKU,QAAQG,UACnC,CAEO,QAAJC,GACF,OAAOd,KAAKU,QAAQI,IACrB,CAEDC,QAAAA,CAASR,GACPP,KAAKgB,SAAS,CAAEC,KAAM,WAAYV,SACnC,CAEDW,WAAAA,CAAYC,GACLnB,KAAKM,UAAUc,SAASD,KAC3BnB,KAAKM,UAAUe,KAAKF,GAGpBnB,KAAKsB,iBAELtB,KAAKG,cAAcoB,OAAO,CACxBN,KAAM,gBACNO,SAAUxB,KACVmB,aAGL,CAEDM,cAAAA,CAAeN,GACbnB,KAAKM,UAAYN,KAAKM,UAAUoB,OAAQC,GAAMA,IAAMR,GAEpDnB,KAAKW,aAELX,KAAKG,cAAcoB,OAAO,CACxBN,KAAM,kBACNO,SAAUxB,KACVmB,YAEH,CAESS,cAAAA,GACH5B,KAAKM,UAAUuB,SACQ,YAAtB7B,KAAKO,MAAMuB,OACb9B,KAAKW,aAELX,KAAKG,cAAc4B,OAAO/B,MAG/B,CAEDgC,WAA6B,IAAAC,EAAAC,EAC3B,cAAOD,EAAA,OAAAC,EAAAlC,KAAKmC,cAAL,EAAAD,EAAcF,YAArBC,EAAmCjC,KAAKoC,SACzC,CAEY,aAAPA,GACJ,MAAMC,EAAkBA,KAAM,IAAAC,EAsB5B,OArBAtC,KAAKmC,SAAUI,EAAAA,EAAAA,IAAc,CAC3BC,GAAIA,IACGxC,KAAKU,QAAQ+B,WAGXzC,KAAKU,QAAQ+B,WAAWzC,KAAKO,MAAMmC,WAFjCC,QAAQC,OAAO,uBAI1BC,OAAQA,CAACC,EAAcC,KACrB/C,KAAKgB,SAAS,CAAEC,KAAM,SAAU6B,eAAcC,WAEhDC,QAASA,KACPhD,KAAKgB,SAAS,CAAEC,KAAM,WAExBgC,WAAYA,KACVjD,KAAKgB,SAAS,CAAEC,KAAM,cAExBiC,MAAK,OAAAZ,EAAEtC,KAAKU,QAAQwC,OAAfZ,EAAwB,EAC7Ba,WAAYnD,KAAKU,QAAQyC,WACzBC,YAAapD,KAAKU,QAAQ0C,cAGrBpD,KAAKmC,QAAQkB,SAGhBC,EAAiC,YAAtBtD,KAAKO,MAAMuB,OAC5B,IAAI,IAAAyB,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EACF,IAAKR,EAAU,KAAAS,EAAAC,EAAAC,EAAAC,EACblE,KAAKgB,SAAS,CAAEC,KAAM,UAAWyB,UAAW1C,KAAKU,QAAQgC,kBAEzD,OAAAqB,GAAWC,EAAA,KAAA7D,cAAcL,QAAOqE,eAAhC,EAAMJ,EACJK,KAAAJ,EAAAhE,KAAKO,MAAMmC,UACX1C,OAEF,MAAMqE,QAAgB,OAAAJ,GAAAC,EAAAlE,KAAKU,SAAQyD,eAAb,EAAAF,EAAAG,KAAAF,EAAwBlE,KAAKO,MAAMmC,YACrD2B,IAAYrE,KAAKO,MAAM8D,SACzBrE,KAAKgB,SAAS,CACZC,KAAM,UACNoD,UACA3B,UAAW1C,KAAKO,MAAMmC,WAG3B,CACD,MAAM4B,QAAajC,IAiCnB,aA9BA,OAAMkB,GAAAC,EAAAxD,KAAKG,cAAcL,QAAOyE,gBAAhC,EAAMhB,EAAAa,KAAAZ,EACJc,EACAtE,KAAKO,MAAMmC,UACX1C,KAAKO,MAAM8D,QACXrE,aAGI,OAANyD,GAAMC,EAAA1D,KAAKU,SAAQ6D,gBAAb,EAAAd,EAAAW,KAAAV,EACJY,EACAtE,KAAKO,MAAMmC,UACX1C,KAAKO,MAAM8D,gBAIb,OAAMV,GAAAC,EAAA5D,KAAKG,cAAcL,QAAO0E,gBAAhC,EAAMb,EACJS,KAAAR,EAAAU,EACA,KACAtE,KAAKO,MAAMmC,UACX1C,KAAKO,MAAM8D,QACXrE,aAGI,OAAN6D,GAAMC,EAAA9D,KAAKU,SAAQ8D,gBAAb,EAAAX,EAAAO,KAAAN,EACJQ,EACA,KACAtE,KAAKO,MAAMmC,UACX1C,KAAKO,MAAM8D,UAGbrE,KAAKgB,SAAS,CAAEC,KAAM,UAAWqD,SAC1BA,C,CACP,MAAOvB,GACP,IAAI,IAAA0B,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAkCF,YAhCA,OAAMP,GAAAC,EAAA1E,KAAKG,cAAcL,QAAOmF,cAAhC,EAAMR,EAAAL,KAAAM,EACJ3B,EACA/C,KAAKO,MAAMmC,UACX1C,KAAKO,MAAM8D,QACXrE,aAOI,OAAN2E,GAAMC,EAAA5E,KAAKU,SAAQuE,cAAb,EAAAN,EAAAP,KAAAQ,EACJ7B,EACA/C,KAAKO,MAAMmC,UACX1C,KAAKO,MAAM8D,gBAIb,OAAMQ,GAAAC,EAAA9E,KAAKG,cAAcL,QAAO0E,gBAAhC,EAAMK,EACJT,KAAAU,OAAAI,EACAnC,EACA/C,KAAKO,MAAMmC,UACX1C,KAAKO,MAAM8D,QACXrE,aAGI,OAAN+E,GAAMC,EAAAhF,KAAKU,SAAQ8D,gBAAb,EAAAO,EAAAX,KAAAY,OACJE,EACAnC,EACA/C,KAAKO,MAAMmC,UACX1C,KAAKO,MAAM8D,UAEPtB,CACP,CAnCD,QAoCE/C,KAAKgB,SAAS,CAAEC,KAAM,QAAS8B,MAAOA,GACvC,CACF,CACF,CAEO/B,QAAAA,CAASmE,GA4DfnF,KAAKO,MA1DHA,KAEA,OAAQ4E,EAAOlE,MACb,IAAK,SACH,MAAO,IACFV,EACHuC,aAAcqC,EAAOrC,aACrBsC,cAAeD,EAAOpC,OAE1B,IAAK,QACH,MAAO,IACFxC,EACH8E,UAAU,GAEd,IAAK,WACH,MAAO,IACF9E,EACH8E,UAAU,GAEd,IAAK,UACH,MAAO,IACF9E,EACH8D,QAASc,EAAOd,QAChBC,UAAMY,EACNpC,aAAc,EACdsC,cAAe,KACfrC,MAAO,KACPsC,WAAWC,EAAAA,EAAAA,IAAStF,KAAKU,QAAQ0C,aACjCtB,OAAQ,UACRY,UAAWyC,EAAOzC,WAEtB,IAAK,UACH,MAAO,IACFnC,EACH+D,KAAMa,EAAOb,KACbxB,aAAc,EACdsC,cAAe,KACfrC,MAAO,KACPjB,OAAQ,UACRuD,UAAU,GAEd,IAAK,QACH,MAAO,IACF9E,EACH+D,UAAMY,EACNnC,MAAOoC,EAAOpC,MACdD,aAAcvC,EAAMuC,aAAe,EACnCsC,cAAeD,EAAOpC,MACtBsC,UAAU,EACVvD,OAAQ,SAEZ,IAAK,WACH,MAAO,IACFvB,KACA4E,EAAO5E,SAILgF,CAAQvF,KAAKO,OAE1BiF,EAAAA,EAAcC,MAAM,KAClBzF,KAAKM,UAAUoF,QAASvE,IACtBA,EAASwE,iBAAiBR,KAE5BnF,KAAKG,cAAcoB,OAAO,CACxBC,SAAUxB,KACViB,KAAM,UACNkE,YAGL,EAGI,SAAS3E,IAMd,MAAO,CACL6D,aAASa,EACTZ,UAAMY,EACNnC,MAAO,KACPD,aAAc,EACdsC,cAAe,KACfC,UAAU,EACVvD,OAAQ,OACRY,eAAWwC,EAEd,C,iCCtXM,MAAM7E,EAAwBuF,O,uFCmB9B,MAAMC,UAKHC,EAAAA,EAeRjG,WAAAA,CACEkG,EACArF,GAEAX,QAEAC,KAAK+F,OAASA,EACd/F,KAAKS,WAAWC,GAChBV,KAAKgG,cACLhG,KAAKiG,cACN,CAESD,WAAAA,GACRhG,KAAKkG,OAASlG,KAAKkG,OAAOC,KAAKnG,MAC/BA,KAAKoG,MAAQpG,KAAKoG,MAAMD,KAAKnG,KAC9B,CAEDS,UAAAA,CACEC,GACA,IAAA2F,EACA,MAAMC,EAActG,KAAKU,QACzBV,KAAKU,QAAUV,KAAK+F,OAAOQ,uBAAuB7F,IAC7C8F,EAAAA,EAAAA,IAAoBF,EAAatG,KAAKU,UACzCV,KAAK+F,OAAOU,mBAAmBlF,OAAO,CACpCN,KAAM,yBACNO,SAAUxB,KAAK0G,gBACfvF,SAAUnB,OAGd,OAAAqG,EAAArG,KAAK0G,kBAALL,EAAsB5F,WAAWT,KAAKU,QACvC,CAESiG,aAAAA,GACkB,IAAAC,EAArB5G,KAAK6G,iBACR,OAAAD,EAAA5G,KAAK0G,kBAALE,EAAsBnF,eAAezB,MAExC,CAED2F,gBAAAA,CAAiBR,GACfnF,KAAKiG,eAGL,MAAMa,EAA+B,CACnCC,WAAW,GAGO,YAAhB5B,EAAOlE,KACT6F,EAAcvC,WAAY,EACD,UAAhBY,EAAOlE,OAChB6F,EAAc7B,SAAU,GAG1BjF,KAAKuB,OAAOuF,EACb,CAEDE,gBAAAA,GAME,OAAOhH,KAAKiH,aACb,CAEDb,KAAAA,GACEpG,KAAK0G,qBAAkBxB,EACvBlF,KAAKiG,eACLjG,KAAKuB,OAAO,CAAEwF,WAAW,GAC1B,CAEDb,MAAAA,CACExD,EACAhC,GAgBA,OAdAV,KAAKkH,cAAgBxG,EAEjBV,KAAK0G,iBACP1G,KAAK0G,gBAAgBjF,eAAezB,MAGtCA,KAAK0G,gBAAkB1G,KAAK+F,OAAOU,mBAAmBU,MAAMnH,KAAK+F,OAAQ,IACpE/F,KAAKU,QACRgC,UACuB,qBAAdA,EAA4BA,EAAY1C,KAAKU,QAAQgC,YAGhE1C,KAAK0G,gBAAgBxF,YAAYlB,MAE1BA,KAAK0G,gBAAgBtE,SAC7B,CAEO6D,YAAAA,GACN,MAAM1F,EAAQP,KAAK0G,gBACf1G,KAAK0G,gBAAgBnG,OACrBC,EAAAA,EAAAA,KAEE4G,EAA6B,YAAjB7G,EAAMuB,OAClBuF,EAKF,IACC9G,EACH6G,YACAE,UAAWF,EACXG,UAA4B,YAAjBhH,EAAMuB,OACjB0F,QAA0B,UAAjBjH,EAAMuB,OACf2F,OAAyB,SAAjBlH,EAAMuB,OACdoE,OAAQlG,KAAKkG,OACbE,MAAOpG,KAAKoG,OAGdpG,KAAKiH,cAAgBI,CAMtB,CAEO9F,MAAAA,CAAOb,GACb8E,EAAAA,EAAcC,MAAM,KAGO,IAAAiC,EAAAC,EAAAC,EAAAC,EADzB,GAAI7H,KAAKkH,eAAiBlH,KAAK6G,eAC7B,GAAInG,EAAQ6D,UAER,OADFmD,GAAAC,EAAA3H,KAAKkH,eAAc3C,YACjBmD,EAAAtD,KAAAuD,EAAA3H,KAAKiH,cAAc3C,KACnBtE,KAAKiH,cAAcvE,UACnB1C,KAAKiH,cAAc5C,SAErB,OAAAuD,GAAAC,EAAA7H,KAAKkH,eAAc1C,YAAnBoD,EAAAxD,KAAAyD,EACE7H,KAAKiH,cAAc3C,KACnB,KACAtE,KAAKiH,cAAcvE,UACnB1C,KAAKiH,cAAc5C,cAEhB,GAAI3D,EAAQuE,QAAS,KAAA6C,EAAAC,EAAAC,EAAAC,EAExB,OADFH,GAAAC,EAAA/H,KAAKkH,eAAcjC,UACjB6C,EAAA1D,KAAA2D,EAAA/H,KAAKiH,cAAclE,MACnB/C,KAAKiH,cAAcvE,UACnB1C,KAAKiH,cAAc5C,SAErB,OAAA2D,GAAAC,EAAAjI,KAAKkH,eAAc1C,YAAnBwD,EAAA5D,KAAA6D,OACE/C,EACAlF,KAAKiH,cAAclE,MACnB/C,KAAKiH,cAAcvE,UACnB1C,KAAKiH,cAAc5C,QAEtB,CAIC3D,EAAQqG,WACV/G,KAAK+G,UAAUrB,QAAQwC,IAAkB,IAAjB,SAAEC,GAAHD,EACrBC,EAASnI,KAAKiH,kBAIrB,E,kCC7II,SAAAmB,EAAAC,EAAAC,EAAAC,G,yDAkBLpH,GAAAqH,EAAAA,SAAA,QAAA3C,EAAA4C,EAAA/H,I,kCAUC,CAAAS,EAAAT,IAED,MAAA2G,GAAAqB,EAAAA,EAAAA,GAAAF,EAAAA,YAAAG,GAAAxH,EAAAyH,UAAApD,EAAAA,EAAAqD,WAAAF,IAAA,CAAAxH,IAAA,IAAAA,EAAA6F,mBAAA,IAAA7F,EAAA6F,oB,gDAeG,CAAA7F,IAIH,GAAAkG,EAAAtE,QAAA+F,EAAAA,EAAAA,GAAA3H,EAAAT,QAAAqI,iBAAA,CAAA1B,EAAAtE,Q,wDAQD,CAGD,SAAAiG,IAAA,C,kCChHM,MAAAC,GAAYC,E,QAAAA,GAAiB,YAAa,CAC9C,CACE,OACA,CAAEC,EAAG,qDAAsDC,IAAK,WAElE,CAAC,OAAQ,CAAED,EAAG,aAAcC,IAAK,WACjC,CACE,OACA,CAAED,EAAG,sDAAuDC,IAAK,WAEnE,CAAC,OAAQ,CAAED,EAAG,YAAaC,IAAK,Y,uFCqH3B,MAAMC,UAKHzJ,EAAAA,EAiBRC,WAAAA,CAAYC,GACVC,QAEAC,KAAKsJ,qBAAsB,EAC3BtJ,KAAKC,eAAiBH,EAAOG,eAC7BD,KAAKS,WAAWX,EAAOY,SACvBV,KAAKM,UAAY,GACjBN,KAAKuJ,MAAQzJ,EAAOyJ,MACpBvJ,KAAKI,OAASN,EAAOM,QAAUC,EAAAA,EAC/BL,KAAKwJ,SAAW1J,EAAO0J,SACvBxJ,KAAKyJ,UAAY3J,EAAO2J,UACxBzJ,KAAK0J,aAAe5J,EAAOS,OA0a/B,SAMEG,GAEA,MAAM4D,EAC2B,oBAAxB5D,EAAQiJ,YACVjJ,EAAQiJ,cACTjJ,EAAQiJ,YAERC,EAA0B,qBAATtF,EAEjBuF,EAAuBD,EACe,oBAAjClJ,EAAQmJ,qBACZnJ,EAAQmJ,uBACTnJ,EAAQmJ,qBACV,EAEJ,MAAO,CACLvF,OACAwF,gBAAiB,EACjBC,cAAeH,EAAU,MAAAC,EAAAA,EAAwBG,KAAKC,MAAQ,EAC9DlH,MAAO,KACPmH,iBAAkB,EAClBC,eAAgB,EAChBC,kBAAmB,EACnBC,mBAAoB,KACpBC,UAAW,KACXC,eAAe,EACfzI,OAAQ8H,EAAU,UAAY,UAC9BY,YAAa,OAEhB,CA7cuChK,CAAgBR,KAAKU,SACzDV,KAAKO,MAAQP,KAAK0J,aAClB1J,KAAKW,YACN,CAEO,QAAJG,GACF,OAAOd,KAAKU,QAAQI,IACrB,CAEOL,UAAAA,CACNC,GAEAV,KAAKU,QAAU,IAAKV,KAAKC,kBAAmBS,GAE5CV,KAAKY,gBAAgBZ,KAAKU,QAAQG,UACnC,CAESe,cAAAA,GACH5B,KAAKM,UAAUuB,QAAqC,SAA3B7B,KAAKO,MAAMiK,aACvCxK,KAAKuJ,MAAMxH,OAAO/B,KAErB,CAEDyK,OAAAA,CACEC,EACAhK,GAEA,MAAM4D,GAAOqG,EAAAA,EAAAA,IAAY3K,KAAKO,MAAM+D,KAAMoG,EAAS1K,KAAKU,SAUxD,OAPAV,KAAKgB,SAAS,CACZsD,OACArD,KAAM,UACN8I,cAAe,MAAArJ,OAAA,EAAAA,EAASkK,UACxBC,OAAQ,MAAAnK,OAAA,EAAAA,EAASmK,SAGZvG,CACR,CAEDvD,QAAAA,CACER,EACAuK,GAEA9K,KAAKgB,SAAS,CAAEC,KAAM,WAAYV,QAAOuK,mBAC1C,CAEDC,MAAAA,CAAOrK,GAAwC,IAAAwB,EAC7C,MAAMmB,EAAUrD,KAAKqD,QAErB,OADA,OAAAnB,EAAAlC,KAAKmC,UAALD,EAAc6I,OAAOrK,GACd2C,EAAUA,EAAQ2H,KAAKhC,EAAAA,IAAMiC,MAAMjC,EAAAA,IAAQrG,QAAQuI,SAC3D,CAEDC,OAAAA,GACEpL,MAAMoL,UAENnL,KAAK+K,OAAO,CAAEK,QAAQ,GACvB,CAEDhF,KAAAA,GACEpG,KAAKmL,UACLnL,KAAKe,SAASf,KAAK0J,aACpB,CAED2B,QAAAA,GACE,OAAOrL,KAAKM,UAAUgL,KAAMnK,IAA0C,IAA7BA,EAAST,QAAQ6K,QAC3D,CAEDC,UAAAA,GACE,OAAOxL,KAAKyL,oBAAsB,IAAMzL,KAAKqL,UAC9C,CAEDK,OAAAA,GACE,OACE1L,KAAKO,MAAMgK,gBACVvK,KAAKO,MAAMwJ,eACZ/J,KAAKM,UAAUgL,KAAMnK,GAAaA,EAAS6F,mBAAmB0E,QAEjE,CAEDC,aAAAA,GAAsC,IAAxBC,EAASC,UAAAhK,OAAA,QAAAqD,IAAA2G,UAAA,GAAAA,UAAA,GAAG,EACxB,OACE7L,KAAKO,MAAMgK,gBACVvK,KAAKO,MAAMwJ,iBACX+B,EAAAA,EAAAA,IAAe9L,KAAKO,MAAMwJ,cAAe6B,EAE7C,CAEDG,OAAAA,GAAgB,IAAAC,EACd,MAAM7K,EAAWnB,KAAKM,UAAU2L,KAAMtK,GAAMA,EAAEuK,4BAE1C/K,GACFA,EAASgL,QAAQ,CAAEC,eAAe,IAIpC,OAAKJ,EAAA,KAAA7J,UAAL6J,EAAchK,UACf,CAEDqK,QAAAA,GAAiB,IAAAC,EACf,MAAMnL,EAAWnB,KAAKM,UAAU2L,KAAMtK,GAAMA,EAAE4K,0BAE1CpL,GACFA,EAASgL,QAAQ,CAAEC,eAAe,IAIpC,OAAKE,EAAA,KAAAnK,UAALmK,EAActK,UACf,CAEDd,WAAAA,CAAYC,GACLnB,KAAKM,UAAUc,SAASD,KAC3BnB,KAAKM,UAAUe,KAAKF,GAGpBnB,KAAKsB,iBAELtB,KAAKuJ,MAAMhI,OAAO,CAAEN,KAAM,gBAAiBuL,MAAOxM,KAAMmB,aAE3D,CAEDM,cAAAA,CAAeN,GACTnB,KAAKM,UAAUc,SAASD,KAC1BnB,KAAKM,UAAYN,KAAKM,UAAUoB,OAAQC,GAAMA,IAAMR,GAE/CnB,KAAKM,UAAUuB,SAGd7B,KAAKmC,UACHnC,KAAKsJ,oBACPtJ,KAAKmC,QAAQ4I,OAAO,CAAE0B,QAAQ,IAE9BzM,KAAKmC,QAAQuK,eAIjB1M,KAAKW,cAGPX,KAAKuJ,MAAMhI,OAAO,CAAEN,KAAM,kBAAmBuL,MAAOxM,KAAMmB,aAE7D,CAEDsK,iBAAAA,GACE,OAAOzL,KAAKM,UAAUuB,MACvB,CAED8K,UAAAA,GACO3M,KAAKO,MAAMgK,eACdvK,KAAKgB,SAAS,CAAEC,KAAM,cAEzB,CAED2L,KAAAA,CACElM,EACAmM,GACgB,IAAAC,EAAAC,EAChB,GAA+B,SAA3B/M,KAAKO,MAAMiK,YACb,GAAIxK,KAAKO,MAAMwJ,eAAiB,MAAA8C,GAAAA,EAAcT,cAE5CpM,KAAK+K,OAAO,CAAEK,QAAQ,SACjB,GAAIpL,KAAKqD,QAAS,KAAA2J,EAIvB,OAFA,OAAAA,EAAAhN,KAAKmC,UAAL6K,EAAcC,gBAEPjN,KAAKqD,OACb,CAUH,GANI3C,GACFV,KAAKS,WAAWC,IAKbV,KAAKU,QAAQwM,QAAS,CACzB,MAAM/L,EAAWnB,KAAKM,UAAU2L,KAAMtK,GAAMA,EAAEjB,QAAQwM,SAClD/L,GACFnB,KAAKS,WAAWU,EAAST,QAE5B,CAUD,MAAMyM,GAAkBC,EAAAA,EAAAA,MAGlBC,EAAkD,CACtD7D,SAAUxJ,KAAKwJ,SACf8D,eAAWpI,EACXpE,KAAMd,KAAKc,MAMPyM,EAAqBC,IACzBC,OAAOC,eAAeF,EAAQ,SAAU,CACtCG,YAAY,EACZC,IAAKA,KACH,GAAIT,EAEF,OADAnN,KAAKsJ,qBAAsB,EACpB6D,EAAgBU,WAO/BN,EAAkBF,GAGlB,MAWMhJ,EAAgE,CACpEwI,eACAnM,QAASV,KAAKU,QACd8I,SAAUxJ,KAAKwJ,SACfjJ,MAAOP,KAAKO,MACZuN,QAhBcA,IACT9N,KAAKU,QAAQwM,SAKlBlN,KAAKsJ,qBAAsB,EACpBtJ,KAAKU,QAAQwM,QAAQG,IALnB1K,QAAQC,OAAR,iCAC4B5C,KAAKU,QAAQ+I,UADhD,MA4BF,IAAAsE,GAXFR,EAAkBlJ,GAElB,OAAKyI,EAAA,KAAApM,QAAQsN,WAAblB,EAAuBmB,QAAQ5J,GAG/BrE,KAAKkO,YAAclO,KAAKO,MAIK,SAA3BP,KAAKO,MAAMiK,aACXxK,KAAKO,MAAM+J,aAAX,OAAAyC,EAAyB1I,EAAQwI,mBAAjC,EAAyBE,EAAsBjM,QAE/Cd,KAAKgB,SAAS,CAAEC,KAAM,QAASH,KAAI,OAAEiN,EAAA1J,EAAQwI,mBAAV,EAAEkB,EAAsBjN,OAG7D,MAAMmE,EAAWlC,IASe,IAAAoL,EAAAC,EAAAC,EAAAC,IAPxBC,EAAAA,EAAAA,IAAiBxL,IAAUA,EAAMqI,QACrCpL,KAAKgB,SAAS,CACZC,KAAM,QACN8B,MAAOA,KAINwL,EAAAA,EAAAA,IAAiBxL,MAEQ,OAAvBoL,GAAAC,EAAA,KAAA7E,MAAMzJ,QAAOmF,UAAUkJ,EAAA/J,KAAAgK,EAAArL,EAAO/C,MACnC,OAAAqO,GAAAC,EAAAtO,KAAKuJ,MAAMzJ,QAAO0E,YAAlB6J,EAAAjK,KAAAkK,EACEtO,KAAKO,MAAM+D,KACXvB,EACA/C,OAQCA,KAAKwO,sBAERxO,KAAKW,aAEPX,KAAKwO,sBAAuB,GAmD9B,OA/CAxO,KAAKmC,SAAUI,EAAAA,EAAAA,IAAc,CAC3BC,GAAI6B,EAAQyJ,QACZW,MAAK,MAAEtB,OAAF,EAAEA,EAAiBsB,MAAMtI,KAAKgH,GACnC5I,UAAYD,IAAS,IAAAoK,EAAAC,EAAAC,EAAAC,EACC,qBAATvK,GAUXtE,KAAKyK,QAAQnG,GAGiB,OAAzBoK,GAAAC,EAAA,KAAApF,MAAMzJ,QAAOyE,YAAYmK,EAAAtK,KAAAuK,EAAArK,EAAMtE,MACpC,OAAA4O,GAAAC,EAAA7O,KAAKuJ,MAAMzJ,QAAO0E,YAAlBoK,EAAAxK,KAAAyK,EACEvK,EACAtE,KAAKO,MAAMwC,MACX/C,MAGGA,KAAKwO,sBAERxO,KAAKW,aAEPX,KAAKwO,sBAAuB,GAlB1BvJ,EAAQ,IAAI6J,MAAS9O,KAAKyJ,UAAlB,wBAoBZxE,UACApC,OAAQA,CAACC,EAAcC,KACrB/C,KAAKgB,SAAS,CAAEC,KAAM,SAAU6B,eAAcC,WAEhDC,QAASA,KACPhD,KAAKgB,SAAS,CAAEC,KAAM,WAExBgC,WAAYA,KACVjD,KAAKgB,SAAS,CAAEC,KAAM,cAExBiC,MAAOmB,EAAQ3D,QAAQwC,MACvBC,WAAYkB,EAAQ3D,QAAQyC,WAC5BC,YAAaiB,EAAQ3D,QAAQ0C,cAG/BpD,KAAKqD,QAAUrD,KAAKmC,QAAQkB,QAErBrD,KAAKqD,OACb,CAEOrC,QAAAA,CAASmE,GAgFfnF,KAAKO,MA9EHA,KAC8B,IAAAwO,EAAAC,EAC9B,OAAQ7J,EAAOlE,MACb,IAAK,SACH,MAAO,IACFV,EACH6J,kBAAmBjF,EAAOrC,aAC1BuH,mBAAoBlF,EAAOpC,OAE/B,IAAK,QACH,MAAO,IACFxC,EACHiK,YAAa,UAEjB,IAAK,WACH,MAAO,IACFjK,EACHiK,YAAa,YAEjB,IAAK,QACH,MAAO,IACFjK,EACH6J,kBAAmB,EACnBC,mBAAoB,KACpBC,UAAS,OAAEyE,EAAA5J,EAAOrE,MAATiO,EAAiB,KAC1BvE,aAAalF,EAAAA,EAAAA,IAAStF,KAAKU,QAAQ0C,aAC/B,WACA,aACC7C,EAAMwJ,eAAiB,CAC1BhH,MAAO,KACPjB,OAAQ,YAGd,IAAK,UACH,MAAO,IACFvB,EACH+D,KAAMa,EAAOb,KACbwF,gBAAiBvJ,EAAMuJ,gBAAkB,EACzCC,cAAa,OAAAiF,EAAE7J,EAAO4E,eAATiF,EAA0BhF,KAAKC,MAC5ClH,MAAO,KACPwH,eAAe,EACfzI,OAAQ,cACHqD,EAAO0F,QAAU,CACpBL,YAAa,OACbJ,kBAAmB,EACnBC,mBAAoB,OAG1B,IAAK,QACH,MAAMtH,EAAQoC,EAAOpC,MAErB,OAAIwL,EAAAA,EAAAA,IAAiBxL,IAAUA,EAAM0J,QAAUzM,KAAKkO,YAC3C,IAAKlO,KAAKkO,YAAa1D,YAAa,QAGtC,IACFjK,EACHwC,MAAOA,EACPmH,iBAAkB3J,EAAM2J,iBAAmB,EAC3CC,eAAgBH,KAAKC,MACrBG,kBAAmB7J,EAAM6J,kBAAoB,EAC7CC,mBAAoBtH,EACpByH,YAAa,OACb1I,OAAQ,SAEZ,IAAK,aACH,MAAO,IACFvB,EACHgK,eAAe,GAEnB,IAAK,WACH,MAAO,IACFhK,KACA4E,EAAO5E,SAKLgF,CAAQvF,KAAKO,OAE1BiF,EAAAA,EAAcC,MAAM,KAClBzF,KAAKM,UAAUoF,QAASvE,IACtBA,EAAS8N,cAAc9J,KAGzBnF,KAAKuJ,MAAMhI,OAAO,CAAEiL,MAAOxM,KAAMiB,KAAM,UAAWkE,YAErD,E,cCtgBI,MAAM+J,UAAmBpJ,EAAAA,EAM9BjG,WAAAA,CAAYC,GACVC,QACAC,KAAKF,OAASA,GAAU,CAAC,EACzBE,KAAKmP,QAAU,GACfnP,KAAKoP,WAAa,CAAC,CACpB,CAEDjI,KAAAA,CACEpB,EACArF,EACAH,GAC+C,IAAA8O,EAC/C,MAAM7F,EAAW9I,EAAQ8I,SACnBC,EACiB,OAArB4F,EAAA3O,EAAQ+I,WAAa4F,GAAAC,EAAAA,EAAAA,IAAsB9F,EAAU9I,GACvD,IAAI8L,EAAQxM,KAAK4N,IAA4CnE,GAe7D,OAbK+C,IACHA,EAAQ,IAAInD,EAAM,CAChBE,MAAOvJ,KACPI,OAAQ2F,EAAOwJ,YACf/F,WACAC,YACA/I,QAASqF,EAAOyJ,oBAAoB9O,GACpCH,QACAN,eAAgB8F,EAAO0J,iBAAiBjG,KAE1CxJ,KAAK0P,IAAIlD,IAGJA,CACR,CAEDkD,GAAAA,CAAIlD,GACGxM,KAAKoP,WAAW5C,EAAM/C,aACzBzJ,KAAKoP,WAAW5C,EAAM/C,WAAa+C,EACnCxM,KAAKmP,QAAQ9N,KAAKmL,GAClBxM,KAAKuB,OAAO,CACVN,KAAM,QACNuL,UAGL,CAEDzK,MAAAA,CAAOyK,GACL,MAAMmD,EAAa3P,KAAKoP,WAAW5C,EAAM/C,WAErCkG,IACFnD,EAAMrB,UAENnL,KAAKmP,QAAUnP,KAAKmP,QAAQzN,OAAQC,GAAMA,IAAM6K,GAE5CmD,IAAenD,UACVxM,KAAKoP,WAAW5C,EAAM/C,WAG/BzJ,KAAKuB,OAAO,CAAEN,KAAM,UAAWuL,UAElC,CAEDoD,KAAAA,GACEpK,EAAAA,EAAcC,MAAM,KAClBzF,KAAKmP,QAAQzJ,QAAS8G,IACpBxM,KAAK+B,OAAOyK,MAGjB,CAEDoB,GAAAA,CAMEnE,GAEA,OAAOzJ,KAAKoP,WAAW3F,EACxB,CAEDoG,MAAAA,GACE,OAAO7P,KAAKmP,OACb,CAeDlD,IAAAA,CACE5D,EACAC,GAEA,MAAOwH,IAAWC,EAAAA,EAAAA,IAAgB1H,EAAMC,GAMxC,MAJ6B,qBAAlBwH,EAAQE,QACjBF,EAAQE,OAAQ,GAGXhQ,KAAKmP,QAAQlD,KAAMO,IAAUyD,EAAAA,EAAAA,IAAWH,EAAStD,GACzD,CAoBD0D,OAAAA,CACE7H,EACAC,GAEA,MAAOwH,IAAWC,EAAAA,EAAAA,IAAgB1H,EAAMC,GACxC,OAAOmF,OAAO0C,KAAKL,GAASjO,OAAS,EACjC7B,KAAKmP,QAAQzN,OAAQ8K,IAAUyD,EAAAA,EAAAA,IAAWH,EAAStD,IACnDxM,KAAKmP,OACV,CAED5N,MAAAA,CAAO6O,GACL5K,EAAAA,EAAcC,MAAM,KAClBzF,KAAK+G,UAAUrB,QAAQwC,IAAkB,IAAjB,SAAEC,GAAHD,EACrBC,EAASiI,MAGd,CAEDrE,OAAAA,GACEvG,EAAAA,EAAcC,MAAM,KAClBzF,KAAKmP,QAAQzJ,QAAS8G,IACpBA,EAAMT,aAGX,CAEDM,QAAAA,GACE7G,EAAAA,EAAcC,MAAM,KAClBzF,KAAKmP,QAAQzJ,QAAS8G,IACpBA,EAAMH,cAGX,E,aChKI,MAAMgE,UAAsBvK,EAAAA,EAOjCjG,WAAAA,CAAYC,GACVC,QACAC,KAAKF,OAASA,GAAU,CAAC,EACzBE,KAAKsQ,UAAY,GACjBtQ,KAAKE,WAAa,CACnB,CAEDiH,KAAAA,CACEpB,EACArF,EACAH,GAEA,MAAMiB,EAAW,IAAI7B,EAAAA,EAAS,CAC5BQ,cAAeH,KACfI,OAAQ2F,EAAOwJ,YACfrP,aAAcF,KAAKE,WACnBQ,QAASqF,EAAOQ,uBAAuB7F,GACvCH,QACAN,eAAgBS,EAAQ6P,YACpBxK,EAAOyK,oBAAoB9P,EAAQ6P,kBACnCrL,IAKN,OAFAlF,KAAK0P,IAAIlO,GAEFA,CACR,CAEDkO,GAAAA,CAAIlO,GACFxB,KAAKsQ,UAAUjP,KAAKG,GACpBxB,KAAKuB,OAAO,CAAEN,KAAM,QAASO,YAC9B,CAEDO,MAAAA,CAAOP,GACLxB,KAAKsQ,UAAYtQ,KAAKsQ,UAAU5O,OAAQC,GAAMA,IAAMH,GACpDxB,KAAKuB,OAAO,CAAEN,KAAM,UAAWO,YAChC,CAEDoO,KAAAA,GACEpK,EAAAA,EAAcC,MAAM,KAClBzF,KAAKsQ,UAAU5K,QAASlE,IACtBxB,KAAK+B,OAAOP,MAGjB,CAEDqO,MAAAA,GACE,OAAO7P,KAAKsQ,SACb,CAEDrE,IAAAA,CACE6D,GAMA,MAJ6B,qBAAlBA,EAAQE,QACjBF,EAAQE,OAAQ,GAGXhQ,KAAKsQ,UAAUrE,KAAMzK,IAAaiP,EAAAA,EAAAA,IAAcX,EAAStO,GACjE,CAED0O,OAAAA,CAAQJ,GACN,OAAO9P,KAAKsQ,UAAU5O,OAAQF,IAAaiP,EAAAA,EAAAA,IAAcX,EAAStO,GACnE,CAEDD,MAAAA,CAAO6O,GACL5K,EAAAA,EAAcC,MAAM,KAClBzF,KAAK+G,UAAUrB,QAAQwC,IAAkB,IAAjB,SAAEC,GAAHD,EACrBC,EAASiI,MAGd,CAEDM,qBAAAA,GAA0C,IAAAC,EAgBxC,OAfA3Q,KAAK4Q,UAAW,OAACD,EAAA3Q,KAAK4Q,UAAND,EAAkBhO,QAAQuI,WACvCF,KAAK,KACJ,MAAM6F,EAAkB7Q,KAAKsQ,UAAU5O,OAAQC,GAAMA,EAAEpB,MAAM8E,UAC7D,OAAOG,EAAAA,EAAcC,MAAM,IACzBoL,EAAgBC,OACd,CAACzN,EAAS7B,IACR6B,EAAQ2H,KAAK,IAAMxJ,EAASQ,WAAWiJ,MAAMjC,EAAAA,KAC/CrG,QAAQuI,cAIbF,KAAK,KACJhL,KAAK4Q,cAAW1L,IAGblF,KAAK4Q,QACb,E,uBC1KI,SAASG,IAKd,MAAO,CACL9C,QAAU5J,IACRA,EAAQyJ,QAAU,KAAM,IAAAf,EAAAgB,EAAAiD,EAAAC,EAAAC,EAAAC,EACtB,MAAMC,EAA2D,OAC/DrE,EAAA1I,EAAQwI,eAAR,OAD+DkB,EAC/DhB,EAAsBjM,WADyC,EAC/DiN,EAA4BqD,YACxBC,EAAS,OAAGL,EAAA3M,EAAQwI,eAAR,OAAHoE,EAAGD,EAAsBlQ,WAAzB,EAAGmQ,EAA4BI,UACxC/D,EAAY,MAAA+D,OAAA,EAAAA,EAAW/D,UACvBgE,EAA8C,aAAhB,MAATD,OAAA,EAAAA,EAAWE,WAChCC,EAAkD,cAAhB,MAATH,OAAA,EAAAA,EAAWE,WACpCE,GAAW,OAAAP,EAAA7M,EAAQ9D,MAAM+D,WAAd,EAAA4M,EAAoBQ,QAAS,GACxCC,GAAgB,OAAAR,EAAA9M,EAAQ9D,MAAM+D,WAAd,EAAA6M,EAAoBS,aAAc,GACxD,IAAIC,EAAgBF,EAChBG,GAAY,EAEhB,MAiBM5E,EACJ7I,EAAQ3D,QAAQwM,SAAhB,KAEEvK,QAAQC,OAAR,iCACmCyB,EAAQ3D,QAAQ+I,UADnD,MAIEsI,EAAgBA,CACpBL,EACAM,EACAC,EACAC,KAEAL,EAAgBK,EACZ,CAACF,KAAUH,GACX,IAAIA,EAAeG,GAChBE,EAAW,CAACD,KAASP,GAAS,IAAIA,EAAOO,IAI5CE,EAAYA,CAChBT,EACA7G,EACAmH,EACAE,KAEA,GAAIJ,EACF,OAAOnP,QAAQC,OAAO,aAGxB,GAAqB,qBAAVoP,IAA0BnH,GAAU6G,EAAM7P,OACnD,OAAOc,QAAQuI,QAAQwG,GAGzB,MAAMrE,EAAuC,CAC3C7D,SAAUnF,EAAQmF,SAClB8D,UAAW0E,EACXlR,KAAMuD,EAAQ3D,QAAQI,MAtDC0M,QAyDPH,EAxDlBI,OAAOC,eAAeF,EAAQ,SAAU,CACtCG,YAAY,EACZC,IAAKA,KAAM,IAAAwE,EAGFC,EAKP,OAPI,OAAJD,EAAI/N,EAAQwJ,SAARuE,EAAgBE,QAClBR,GAAY,EAEZ,OAAAO,EAAAhO,EAAQwJ,SAARwE,EAAgBE,iBAAiB,QAAS,KACxCT,GAAY,IAGTzN,EAAQwJ,UAgDnB,MAAM2E,EAAgBtF,EAAQG,GAM9B,OAJgB1K,QAAQuI,QAAQsH,GAAexH,KAAMiH,GACnDF,EAAcL,EAAOM,EAAOC,EAAMC,KAMtC,IAAI7O,EAGJ,GAAKoO,EAAS5P,OAKT,GAAIyP,EAAoB,CAC3B,MAAMzG,EAA8B,qBAAdyC,EAChB0E,EAAQnH,EACVyC,EACAmF,EAAiBpO,EAAQ3D,QAAS+Q,GACtCpO,EAAU8O,EAAUV,EAAU5G,EAAQmH,EACvC,MAGI,GAAIR,EAAwB,CAC/B,MAAM3G,EAA8B,qBAAdyC,EAChB0E,EAAQnH,EACVyC,EACAoF,EAAqBrO,EAAQ3D,QAAS+Q,GAC1CpO,EAAU8O,EAAUV,EAAU5G,EAAQmH,GAAO,EAC9C,KAGI,CACHH,EAAgB,GAEhB,MAAMhH,EAAqD,qBAArCxG,EAAQ3D,QAAQ+R,iBAQtCpP,GALE+N,IAAeK,EAAS,IACpBL,EAAYK,EAAS,GAAI,EAAGA,GAK9BU,EAAU,GAAItH,EAAQ8G,EAAc,IACpChP,QAAQuI,QAAQ6G,EAAc,GAAIJ,EAAc,GAAIF,EAAS,KAGjE,IAAK,IAAIkB,EAAI,EAAGA,EAAIlB,EAAS5P,OAAQ8Q,IACnCtP,EAAUA,EAAQ2H,KAAM0G,IAMtB,IAJEN,IAAeK,EAASkB,IACpBvB,EAAYK,EAASkB,GAAIA,EAAGlB,GAGT,CACvB,MAAMO,EAAQnH,EACV8G,EAAcgB,GACdF,EAAiBpO,EAAQ3D,QAASgR,GACtC,OAAOS,EAAUT,EAAO7G,EAAQmH,EACjC,CACD,OAAOrP,QAAQuI,QACb6G,EAAcL,EAAOC,EAAcgB,GAAIlB,EAASkB,MAIvD,MAxDCtP,EAAU8O,EAAU,IA+DtB,OALqB9O,EAAQ2H,KAAM0G,IAAD,CAChCA,QACAE,WAAYC,OAOrB,CAEM,SAASY,EACd/R,EACAgR,GAEA,aAAOhR,EAAQ+R,sBAAf,EAAO/R,EAAQ+R,iBAAmBf,EAAMA,EAAM7P,OAAS,GAAI6P,EAC5D,CAEM,SAASgB,EACdhS,EACAgR,GAEA,aAAOhR,EAAQgS,0BAAf,EAAOhS,EAAQgS,qBAAuBhB,EAAM,GAAIA,EACjD,CC3HM,MAAMkB,EAWX/S,WAAAA,GAA4C,IAAhCC,EAAyB+L,UAAAhK,OAAA,QAAAqD,IAAA2G,UAAA,GAAAA,UAAA,GAAG,CAAC,EACvC7L,KAAK6S,WAAa/S,EAAO+S,YAAc,IAAI3D,EAC3ClP,KAAKG,cAAgBL,EAAOK,eAAiB,IAAIkQ,EACjDrQ,KAAKI,OAASN,EAAOM,QAAUC,EAAAA,EAC/BL,KAAKC,eAAiBH,EAAOG,gBAAkB,CAAC,EAChDD,KAAK8S,cAAgB,GACrB9S,KAAK+S,iBAAmB,GACxB/S,KAAKgT,WAAa,CAOnB,CAEDC,KAAAA,GACEjT,KAAKgT,aACmB,IAApBhT,KAAKgT,aAEThT,KAAKkT,iBAAmBC,EAAAA,EAAavK,UAAU,KACzCuK,EAAAA,EAAaC,cACfpT,KAAK0Q,wBACL1Q,KAAK6S,WAAW9G,aAGpB/L,KAAKqT,kBAAoBC,EAAAA,EAAc1K,UAAU,KAC3C0K,EAAAA,EAAcC,aAChBvT,KAAK0Q,wBACL1Q,KAAK6S,WAAWxG,cAGrB,CAEDmH,OAAAA,GAAgB,IAAAC,EAAAC,EACd1T,KAAKgT,aACmB,IAApBhT,KAAKgT,aAET,OAAAS,EAAAzT,KAAKkT,mBAALO,EAAArP,KAAApE,MACAA,KAAKkT,sBAAmBhO,EAExB,OAAAwO,EAAA1T,KAAKqT,oBAALK,EAAAtP,KAAApE,MACAA,KAAKqT,uBAAoBnO,EAC1B,CAaDyO,UAAAA,CAAWtL,EAAgCC,GACzC,MAAOwH,IAAWC,EAAAA,EAAAA,IAAgB1H,EAAMC,GAExC,OADAwH,EAAQtF,YAAc,WACfxK,KAAK6S,WAAW3C,QAAQJ,GAASjO,MACzC,CAED+R,UAAAA,CAAW9D,GACT,OAAO9P,KAAKG,cAAc+P,QAAQ,IAAKJ,EAAS+D,UAAU,IAAQhS,MACnE,CAeDiS,YAAAA,CACEtK,EACAsG,GAC0B,IAAAiE,EAC1B,cAAAA,EAAO/T,KAAK6S,WAAW5G,KAAmBzC,EAAUsG,SAApD,EAAOiE,EAAuDxT,MAAM+D,IACrE,CA+CD0P,eAAAA,CAME3L,EAMAC,EAGAC,GAEA,MAAM0L,GAAgBC,EAAAA,EAAAA,IAAe7L,EAAMC,EAAMC,GAC3C4L,EAAanU,KAAK8T,aAAoBG,EAAczK,UAE1D,OAAO2K,EACHxR,QAAQuI,QAAQiJ,GAChBnU,KAAKoU,WAAWH,EACrB,CAcDI,cAAAA,CACEC,GAEA,OAAOtU,KAAKuU,gBACTrE,QAAQoE,GACRE,IAAIC,IAAyB,IAAxB,SAAEjL,EAAF,MAAYjJ,GAAbkU,EAEH,MAAO,CAACjL,EADKjJ,EAAM+D,OAGxB,CAEDoQ,YAAAA,CACElL,EACAmL,EACAjU,GAEA,MAAM8L,EAAQxM,KAAK6S,WAAW5G,KAAmBzC,GAC3CoL,EAAQ,MAAGpI,OAAH,EAAGA,EAAOjM,MAAM+D,KACxBA,GAAOuQ,EAAAA,EAAAA,IAAiBF,EAASC,GAEvC,GAAoB,qBAATtQ,EACT,OAGF,MAAM2P,GAAgBC,EAAAA,EAAAA,IAAe1K,GAC/BsL,EAAmB9U,KAAKwP,oBAAoByE,GAClD,OAAOjU,KAAK6S,WACT1L,MAAMnH,KAAM8U,GACZrK,QAAQnG,EAAM,IAAK5D,EAASmK,QAAQ,GACxC,CAkBDkK,cAAAA,CACET,EACAK,EACAjU,GAEA,OAAO8E,EAAAA,EAAcC,MAAM,IACzBzF,KAAKuU,gBACFrE,QAAQoE,GACRE,IAAIQ,IAAA,IAAC,SAAExL,GAAHwL,EAAA,MAAkB,CACrBxL,EACAxJ,KAAK0U,aAA2BlL,EAAUmL,EAASjU,MAG1D,CAEDuU,aAAAA,CACEzL,EAIAsG,GAC8C,IAAAoF,EAC9C,OAAO,OAAAA,EAAAlV,KAAK6S,WAAW5G,KAA2BzC,EAAUsG,SAArD,EAAAoF,EAA+D3U,KACvE,CAaD4U,aAAAA,CACE9M,EACAC,GAEA,MAAOwH,IAAWC,EAAAA,EAAAA,IAAgB1H,EAAMC,GAClCuK,EAAa7S,KAAK6S,WACxBrN,EAAAA,EAAcC,MAAM,KAClBoN,EAAW3C,QAAQJ,GAASpK,QAAS8G,IACnCqG,EAAW9Q,OAAOyK,MAGvB,CAiBD4I,YAAAA,CACE/M,EACAC,EACAC,GAEA,MAAOuH,EAASpP,IAAWqP,EAAAA,EAAAA,IAAgB1H,EAAMC,EAAMC,GACjDsK,EAAa7S,KAAK6S,WAElBwC,EAAsC,CAC1CpU,KAAM,YACH6O,GAGL,OAAOtK,EAAAA,EAAcC,MAAM,KACzBoN,EAAW3C,QAAQJ,GAASpK,QAAS8G,IACnCA,EAAMpG,UAEDpG,KAAKsV,eAAeD,EAAgB3U,IAE9C,CAcD6U,aAAAA,CACElN,EACAC,EACAC,GAEA,MAAOuH,EAAS0F,EAAgB,CAAC,IAAKzF,EAAAA,EAAAA,IAAgB1H,EAAMC,EAAMC,GAE9B,qBAAzBiN,EAAc/I,SACvB+I,EAAc/I,QAAS,GAGzB,MAAMgJ,EAAWjQ,EAAAA,EAAcC,MAAM,IACnCzF,KAAK6S,WACF3C,QAAQJ,GACR0E,IAAKhI,GAAUA,EAAMzB,OAAOyK,KAGjC,OAAO7S,QAAQ+S,IAAID,GAAUzK,KAAKhC,EAAAA,IAAMiC,MAAMjC,EAAAA,GAC/C,CAiBD2M,iBAAAA,CACEtN,EACAC,EACAC,GAEA,MAAOuH,EAASpP,IAAWqP,EAAAA,EAAAA,IAAgB1H,EAAMC,EAAMC,GAEvD,OAAO/C,EAAAA,EAAcC,MAAM,KAAM,IAAAyC,EAAA0N,EAK/B,GAJA5V,KAAK6S,WAAW3C,QAAQJ,GAASpK,QAAS8G,IACxCA,EAAMG,eAGoB,SAAxBmD,EAAQ+F,YACV,OAAOlT,QAAQuI,UAEjB,MAAMmK,EAAsC,IACvCvF,EACH7O,KAA6C,OAAzCiH,EAAA,OAAA0N,EAAE9F,EAAQ+F,aAAVD,EAAyB9F,EAAQ7O,MAAQiH,EAAA,UAE/C,OAAOlI,KAAKsV,eAAeD,EAAgB3U,IAE9C,CAiBD4U,cAAAA,CACEjN,EACAC,EACAC,GAEA,MAAOuH,EAASpP,IAAWqP,EAAAA,EAAAA,IAAgB1H,EAAMC,EAAMC,GAEjDkN,EAAWjQ,EAAAA,EAAcC,MAAM,IACnCzF,KAAK6S,WACF3C,QAAQJ,GACRpO,OAAQ8K,IAAWA,EAAMhB,cACzBgJ,IAAKhI,IAAD,IAAAsJ,EAAA,OACHtJ,EAAMI,WAAM1H,EAAW,IAClBxE,EACH0L,cAAa,OAAA0J,EAAA,MAAEpV,OAAF,EAAEA,EAAS0L,gBAAX0J,EACbhV,KAAM,CAAEsQ,YAAatB,EAAQsB,kBAKrC,IAAI/N,EAAUV,QAAQ+S,IAAID,GAAUzK,KAAKhC,EAAAA,IAMzC,OAJI,MAACtI,GAAAA,EAASqV,eACZ1S,EAAUA,EAAQ4H,MAAMjC,EAAAA,KAGnB3F,CACR,CA4CD+Q,UAAAA,CAME/L,EACAC,EAMAC,GAKA,MAAM0L,GAAgBC,EAAAA,EAAAA,IAAe7L,EAAMC,EAAMC,GAC3CuM,EAAmB9U,KAAKwP,oBAAoByE,GAGZ,qBAA3Ba,EAAiB5R,QAC1B4R,EAAiB5R,OAAQ,GAG3B,MAAMsJ,EAAQxM,KAAK6S,WAAW1L,MAAMnH,KAAM8U,GAE1C,OAAOtI,EAAMb,cAAcmJ,EAAiBlJ,WACxCY,EAAMI,MAAMkI,GACZnS,QAAQuI,QAAQsB,EAAMjM,MAAM+D,KACjC,CA4CD0R,aAAAA,CAME3N,EACAC,EAMAC,GAKA,OAAOvI,KAAKoU,WAAW/L,EAAaC,EAAaC,GAC9CyC,KAAKhC,EAAAA,IACLiC,MAAMjC,EAAAA,GACV,CA4CDiN,kBAAAA,CAME5N,EAGAC,EAMAC,GAKA,MAAM0L,GAAgBC,EAAAA,EAAAA,IAAe7L,EAAMC,EAAMC,GAMjD,OALA0L,EAAcjG,SAAW+C,IAKlB/Q,KAAKoU,WAAWH,EACxB,CA4CDiC,qBAAAA,CAME7N,EAGAC,EAMAC,GAKA,OAAOvI,KAAKiW,mBAAmB5N,EAAaC,EAAaC,GACtDyC,KAAKhC,EAAAA,IACLiC,MAAMjC,EAAAA,GACV,CAED0H,qBAAAA,GACE,OAAO1Q,KAAKG,cAAcuQ,uBAC3B,CAED6D,aAAAA,GACE,OAAOvU,KAAK6S,UACb,CAEDpM,gBAAAA,GACE,OAAOzG,KAAKG,aACb,CAEDoP,SAAAA,GACE,OAAOvP,KAAKI,MACb,CAED+V,iBAAAA,GACE,OAAOnW,KAAKC,cACb,CAEDmW,iBAAAA,CAAkB1V,GAChBV,KAAKC,eAAiBS,CACvB,CAED2V,gBAAAA,CACE7M,EACA9I,GAEA,MAAM2G,EAASrH,KAAK8S,cAAc7G,KAC/BtK,IAAM2U,EAAAA,EAAAA,IAAa9M,MAAc8M,EAAAA,EAAAA,IAAa3U,EAAE6H,WAE/CnC,EACFA,EAAOpH,eAAiBS,EAExBV,KAAK8S,cAAczR,KAAK,CAAEmI,WAAUvJ,eAAgBS,GAEvD,CAED+O,gBAAAA,CACEjG,GAEA,IAAKA,EACH,OAIF,MAAM+M,EAAwBvW,KAAK8S,cAAc7G,KAAMtK,IACrD6U,EAAAA,EAAAA,IAAgBhN,EAAU7H,EAAE6H,WAmB9B,aAAO+M,OAAP,EAAOA,EAAuBtW,cAC/B,CAEDwW,mBAAAA,CACElG,EACA7P,GAEA,MAAM2G,EAASrH,KAAK+S,iBAAiB9G,KAClCtK,IAAM2U,EAAAA,EAAAA,IAAa/F,MAAiB+F,EAAAA,EAAAA,IAAa3U,EAAE4O,cAElDlJ,EACFA,EAAOpH,eAAiBS,EAExBV,KAAK+S,iBAAiB1R,KAAK,CAAEkP,cAAatQ,eAAgBS,GAE7D,CAED8P,mBAAAA,CACED,GAEA,IAAKA,EACH,OAIF,MAAMgG,EAAwBvW,KAAK+S,iBAAiB9G,KAAMtK,IACxD6U,EAAAA,EAAAA,IAAgBjG,EAAa5O,EAAE4O,cAmBjC,aAAOgG,OAAP,EAAOA,EAAuBtW,cAC/B,CAEDuP,mBAAAA,CAOE9O,GAgBA,SAAIA,GAAAA,EAASgW,WACX,OAAOhW,EAST,MAAMoU,EAAmB,IACpB9U,KAAKC,eAAekP,WACpBnP,KAAKyP,iBAAL,MAAsB/O,OAAtB,EAAsBA,EAAS8I,aAC/B9I,EACHgW,YAAY,GAmBd,OAhBK5B,EAAiBrL,WAAaqL,EAAiBtL,WAClDsL,EAAiBrL,WAAY6F,EAAAA,EAAAA,IAC3BwF,EAAiBtL,SACjBsL,IAK+C,qBAAxCA,EAAiB6B,qBAC1B7B,EAAiB6B,mBACkB,WAAjC7B,EAAiB1R,aAE4B,qBAAtC0R,EAAiB/L,mBAC1B+L,EAAiB/L,mBAAqB+L,EAAiB8B,UAGlD9B,CAOR,CAEDvO,sBAAAA,CACE7F,GAEA,aAAIA,GAAAA,EAASgW,WACJhW,EAEF,IACFV,KAAKC,eAAeqQ,aACpBtQ,KAAKwQ,oBAAL,MAAyB9P,OAAzB,EAAyBA,EAAS6P,gBAClC7P,EACHgW,YAAY,EAEf,CAED9G,KAAAA,GACE5P,KAAK6S,WAAWjD,QAChB5P,KAAKG,cAAcyP,OACpB,E,gDC37BI,MAAehQ,EAIpBuL,OAAAA,GACEnL,KAAKsB,gBACN,CAESX,UAAAA,GACRX,KAAKsB,kBAEDuV,EAAAA,EAAAA,IAAe7W,KAAKa,aACtBb,KAAK8W,UAAYC,WAAW,KAC1B/W,KAAK4B,kBACJ5B,KAAKa,WAEX,CAESD,eAAAA,CAAgBoW,GAExBhX,KAAKa,UAAYoW,KAAKC,IACpBlX,KAAKa,WAAa,EAClB,MAAAmW,EAAAA,EAAiBG,EAAAA,GAAWC,IAAW,IAE1C,CAES9V,cAAAA,GACJtB,KAAK8W,YACPO,aAAarX,KAAK8W,WAClB9W,KAAK8W,eAAY5R,EAEpB,E", "sources": ["../node_modules/@tanstack/query-core/src/mutation.ts", "../node_modules/@tanstack/query-core/src/logger.ts", "../node_modules/@tanstack/query-core/src/mutationObserver.ts", "../node_modules/@tanstack/react-query/src/useMutation.ts", "../node_modules/lucide-react/src/icons/refresh-cw.ts", "../node_modules/@tanstack/query-core/src/query.ts", "../node_modules/@tanstack/query-core/src/queryCache.ts", "../node_modules/@tanstack/query-core/src/mutationCache.ts", "../node_modules/@tanstack/query-core/src/infiniteQueryBehavior.ts", "../node_modules/@tanstack/query-core/src/queryClient.ts", "../node_modules/@tanstack/query-core/src/removable.ts"], "sourcesContent": ["import { defaultLogger } from './logger'\nimport { notify<PERSON><PERSON><PERSON> } from './notifyManager'\nimport { Removable } from './removable'\nimport { canFetch, createRetry<PERSON> } from './retryer'\nimport type { MutationMeta, MutationOptions, MutationStatus } from './types'\nimport type { MutationCache } from './mutationCache'\nimport type { MutationObserver } from './mutationObserver'\nimport type { Logger } from './logger'\nimport type { <PERSON><PERSON><PERSON> } from './retryer'\n\n// TYPES\n\ninterface MutationConfig<TData, TError, TVariables, TContext> {\n  mutationId: number\n  mutationCache: MutationCache\n  options: MutationOptions<TData, TError, TVariables, TContext>\n  logger?: Logger\n  defaultOptions?: MutationOptions<TData, TError, TVariables, TContext>\n  state?: MutationState<TData, TError, TVariables, TContext>\n  meta?: MutationMeta\n}\n\nexport interface MutationState<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n> {\n  context: TContext | undefined\n  data: TData | undefined\n  error: TError | null\n  failureCount: number\n  failureReason: TError | null\n  isPaused: boolean\n  status: MutationStatus\n  variables: TVariables | undefined\n}\n\ninterface FailedAction<TError> {\n  type: 'failed'\n  failureCount: number\n  error: TError | null\n}\n\ninterface LoadingAction<TVariables, TContext> {\n  type: 'loading'\n  variables?: TVariables\n  context?: TContext\n}\n\ninterface SuccessAction<TData> {\n  type: 'success'\n  data: TData\n}\n\ninterface ErrorAction<TError> {\n  type: 'error'\n  error: TError\n}\n\ninterface PauseAction {\n  type: 'pause'\n}\n\ninterface ContinueAction {\n  type: 'continue'\n}\n\ninterface SetStateAction<TData, TError, TVariables, TContext> {\n  type: 'setState'\n  state: MutationState<TData, TError, TVariables, TContext>\n}\n\nexport type Action<TData, TError, TVariables, TContext> =\n  | ContinueAction\n  | ErrorAction<TError>\n  | FailedAction<TError>\n  | LoadingAction<TVariables, TContext>\n  | PauseAction\n  | SetStateAction<TData, TError, TVariables, TContext>\n  | SuccessAction<TData>\n\n// CLASS\n\nexport class Mutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n> extends Removable {\n  state: MutationState<TData, TError, TVariables, TContext>\n  options!: MutationOptions<TData, TError, TVariables, TContext>\n  mutationId: number\n\n  private observers: MutationObserver<TData, TError, TVariables, TContext>[]\n  private defaultOptions?: MutationOptions<TData, TError, TVariables, TContext>\n  private mutationCache: MutationCache\n  private logger: Logger\n  private retryer?: Retryer<TData>\n\n  constructor(config: MutationConfig<TData, TError, TVariables, TContext>) {\n    super()\n\n    this.defaultOptions = config.defaultOptions\n    this.mutationId = config.mutationId\n    this.mutationCache = config.mutationCache\n    this.logger = config.logger || defaultLogger\n    this.observers = []\n    this.state = config.state || getDefaultState()\n\n    this.setOptions(config.options)\n    this.scheduleGc()\n  }\n\n  setOptions(\n    options?: MutationOptions<TData, TError, TVariables, TContext>,\n  ): void {\n    this.options = { ...this.defaultOptions, ...options }\n\n    this.updateCacheTime(this.options.cacheTime)\n  }\n\n  get meta(): MutationMeta | undefined {\n    return this.options.meta\n  }\n\n  setState(state: MutationState<TData, TError, TVariables, TContext>): void {\n    this.dispatch({ type: 'setState', state })\n  }\n\n  addObserver(observer: MutationObserver<any, any, any, any>): void {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer)\n\n      // Stop the mutation from being garbage collected\n      this.clearGcTimeout()\n\n      this.mutationCache.notify({\n        type: 'observerAdded',\n        mutation: this,\n        observer,\n      })\n    }\n  }\n\n  removeObserver(observer: MutationObserver<any, any, any, any>): void {\n    this.observers = this.observers.filter((x) => x !== observer)\n\n    this.scheduleGc()\n\n    this.mutationCache.notify({\n      type: 'observerRemoved',\n      mutation: this,\n      observer,\n    })\n  }\n\n  protected optionalRemove() {\n    if (!this.observers.length) {\n      if (this.state.status === 'loading') {\n        this.scheduleGc()\n      } else {\n        this.mutationCache.remove(this)\n      }\n    }\n  }\n\n  continue(): Promise<unknown> {\n    return this.retryer?.continue() ?? this.execute()\n  }\n\n  async execute(): Promise<TData> {\n    const executeMutation = () => {\n      this.retryer = createRetryer({\n        fn: () => {\n          if (!this.options.mutationFn) {\n            return Promise.reject('No mutationFn found')\n          }\n          return this.options.mutationFn(this.state.variables!)\n        },\n        onFail: (failureCount, error) => {\n          this.dispatch({ type: 'failed', failureCount, error })\n        },\n        onPause: () => {\n          this.dispatch({ type: 'pause' })\n        },\n        onContinue: () => {\n          this.dispatch({ type: 'continue' })\n        },\n        retry: this.options.retry ?? 0,\n        retryDelay: this.options.retryDelay,\n        networkMode: this.options.networkMode,\n      })\n\n      return this.retryer.promise\n    }\n\n    const restored = this.state.status === 'loading'\n    try {\n      if (!restored) {\n        this.dispatch({ type: 'loading', variables: this.options.variables! })\n        // Notify cache callback\n        await this.mutationCache.config.onMutate?.(\n          this.state.variables,\n          this as Mutation<unknown, unknown, unknown, unknown>,\n        )\n        const context = await this.options.onMutate?.(this.state.variables!)\n        if (context !== this.state.context) {\n          this.dispatch({\n            type: 'loading',\n            context,\n            variables: this.state.variables,\n          })\n        }\n      }\n      const data = await executeMutation()\n\n      // Notify cache callback\n      await this.mutationCache.config.onSuccess?.(\n        data,\n        this.state.variables,\n        this.state.context,\n        this as Mutation<unknown, unknown, unknown, unknown>,\n      )\n\n      await this.options.onSuccess?.(\n        data,\n        this.state.variables!,\n        this.state.context!,\n      )\n\n      // Notify cache callback\n      await this.mutationCache.config.onSettled?.(\n        data,\n        null,\n        this.state.variables,\n        this.state.context,\n        this as Mutation<unknown, unknown, unknown, unknown>,\n      )\n\n      await this.options.onSettled?.(\n        data,\n        null,\n        this.state.variables!,\n        this.state.context,\n      )\n\n      this.dispatch({ type: 'success', data })\n      return data\n    } catch (error) {\n      try {\n        // Notify cache callback\n        await this.mutationCache.config.onError?.(\n          error,\n          this.state.variables,\n          this.state.context,\n          this as Mutation<unknown, unknown, unknown, unknown>,\n        )\n\n        if (process.env.NODE_ENV !== 'production') {\n          this.logger.error(error)\n        }\n\n        await this.options.onError?.(\n          error as TError,\n          this.state.variables!,\n          this.state.context,\n        )\n\n        // Notify cache callback\n        await this.mutationCache.config.onSettled?.(\n          undefined,\n          error,\n          this.state.variables,\n          this.state.context,\n          this as Mutation<unknown, unknown, unknown, unknown>,\n        )\n\n        await this.options.onSettled?.(\n          undefined,\n          error as TError,\n          this.state.variables!,\n          this.state.context,\n        )\n        throw error\n      } finally {\n        this.dispatch({ type: 'error', error: error as TError })\n      }\n    }\n  }\n\n  private dispatch(action: Action<TData, TError, TVariables, TContext>): void {\n    const reducer = (\n      state: MutationState<TData, TError, TVariables, TContext>,\n    ): MutationState<TData, TError, TVariables, TContext> => {\n      switch (action.type) {\n        case 'failed':\n          return {\n            ...state,\n            failureCount: action.failureCount,\n            failureReason: action.error,\n          }\n        case 'pause':\n          return {\n            ...state,\n            isPaused: true,\n          }\n        case 'continue':\n          return {\n            ...state,\n            isPaused: false,\n          }\n        case 'loading':\n          return {\n            ...state,\n            context: action.context,\n            data: undefined,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            isPaused: !canFetch(this.options.networkMode),\n            status: 'loading',\n            variables: action.variables,\n          }\n        case 'success':\n          return {\n            ...state,\n            data: action.data,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            status: 'success',\n            isPaused: false,\n          }\n        case 'error':\n          return {\n            ...state,\n            data: undefined,\n            error: action.error,\n            failureCount: state.failureCount + 1,\n            failureReason: action.error,\n            isPaused: false,\n            status: 'error',\n          }\n        case 'setState':\n          return {\n            ...state,\n            ...action.state,\n          }\n      }\n    }\n    this.state = reducer(this.state)\n\n    notifyManager.batch(() => {\n      this.observers.forEach((observer) => {\n        observer.onMutationUpdate(action)\n      })\n      this.mutationCache.notify({\n        mutation: this,\n        type: 'updated',\n        action,\n      })\n    })\n  }\n}\n\nexport function getDefaultState<\n  TData,\n  TError,\n  TVariables,\n  TContext,\n>(): MutationState<TData, TError, TVariables, TContext> {\n  return {\n    context: undefined,\n    data: undefined,\n    error: null,\n    failureCount: 0,\n    failureReason: null,\n    isPaused: false,\n    status: 'idle',\n    variables: undefined,\n  }\n}\n", "export interface Logger {\n  log: LogFunction\n  warn: LogFunction\n  error: LogFunction\n}\n\ntype LogFunction = (...args: any[]) => void\n\nexport const defaultLogger: Logger = console\n", "import { getDefaultState } from './mutation'\nimport { notifyManager } from './notifyManager'\nimport { Subscribable } from './subscribable'\nimport { shallowEqualObjects } from './utils'\nimport type { QueryClient } from './queryClient'\nimport type {\n  MutateOptions,\n  MutationObserverBaseResult,\n  MutationObserverOptions,\n  MutationObserverResult,\n} from './types'\nimport type { Action, Mutation } from './mutation'\n\n// TYPES\n\ntype MutationObserverListener<TData, TError, TVariables, TContext> = (\n  result: MutationObserverResult<TData, TError, TVariables, TContext>,\n) => void\n\ninterface NotifyOptions {\n  listeners?: boolean\n  onError?: boolean\n  onSuccess?: boolean\n}\n\n// CLASS\n\nexport class MutationObserver<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n> extends Subscribable<\n  MutationObserverListener<TData, TError, TVariables, TContext>\n> {\n  options!: MutationObserverOptions<TData, TError, TVariables, TContext>\n\n  private client: QueryClient\n  private currentResult!: MutationObserverResult<\n    TData,\n    TError,\n    TVariables,\n    TContext\n  >\n  private currentMutation?: Mutation<TData, TError, TVariables, TContext>\n  private mutateOptions?: MutateOptions<TData, TError, TVariables, TContext>\n\n  constructor(\n    client: QueryClient,\n    options: MutationObserverOptions<TData, TError, TVariables, TContext>,\n  ) {\n    super()\n\n    this.client = client\n    this.setOptions(options)\n    this.bindMethods()\n    this.updateResult()\n  }\n\n  protected bindMethods(): void {\n    this.mutate = this.mutate.bind(this)\n    this.reset = this.reset.bind(this)\n  }\n\n  setOptions(\n    options?: MutationObserverOptions<TData, TError, TVariables, TContext>,\n  ) {\n    const prevOptions = this.options\n    this.options = this.client.defaultMutationOptions(options)\n    if (!shallowEqualObjects(prevOptions, this.options)) {\n      this.client.getMutationCache().notify({\n        type: 'observerOptionsUpdated',\n        mutation: this.currentMutation,\n        observer: this,\n      })\n    }\n    this.currentMutation?.setOptions(this.options)\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.hasListeners()) {\n      this.currentMutation?.removeObserver(this)\n    }\n  }\n\n  onMutationUpdate(action: Action<TData, TError, TVariables, TContext>): void {\n    this.updateResult()\n\n    // Determine which callbacks to trigger\n    const notifyOptions: NotifyOptions = {\n      listeners: true,\n    }\n\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = true\n    } else if (action.type === 'error') {\n      notifyOptions.onError = true\n    }\n\n    this.notify(notifyOptions)\n  }\n\n  getCurrentResult(): MutationObserverResult<\n    TData,\n    TError,\n    TVariables,\n    TContext\n  > {\n    return this.currentResult\n  }\n\n  reset(): void {\n    this.currentMutation = undefined\n    this.updateResult()\n    this.notify({ listeners: true })\n  }\n\n  mutate(\n    variables?: TVariables,\n    options?: MutateOptions<TData, TError, TVariables, TContext>,\n  ): Promise<TData> {\n    this.mutateOptions = options\n\n    if (this.currentMutation) {\n      this.currentMutation.removeObserver(this)\n    }\n\n    this.currentMutation = this.client.getMutationCache().build(this.client, {\n      ...this.options,\n      variables:\n        typeof variables !== 'undefined' ? variables : this.options.variables,\n    })\n\n    this.currentMutation.addObserver(this)\n\n    return this.currentMutation.execute()\n  }\n\n  private updateResult(): void {\n    const state = this.currentMutation\n      ? this.currentMutation.state\n      : getDefaultState<TData, TError, TVariables, TContext>()\n\n    const isLoading = state.status === 'loading'\n    const result: MutationObserverBaseResult<\n      TData,\n      TError,\n      TVariables,\n      TContext\n    > = {\n      ...state,\n      isLoading,\n      isPending: isLoading,\n      isSuccess: state.status === 'success',\n      isError: state.status === 'error',\n      isIdle: state.status === 'idle',\n      mutate: this.mutate,\n      reset: this.reset,\n    }\n\n    this.currentResult = result as MutationObserverResult<\n      TData,\n      TError,\n      TVariables,\n      TContext\n    >\n  }\n\n  private notify(options: NotifyOptions) {\n    notifyManager.batch(() => {\n      // First trigger the mutate callbacks\n      if (this.mutateOptions && this.hasListeners()) {\n        if (options.onSuccess) {\n          this.mutateOptions.onSuccess?.(\n            this.currentResult.data!,\n            this.currentResult.variables!,\n            this.currentResult.context!,\n          )\n          this.mutateOptions.onSettled?.(\n            this.currentResult.data!,\n            null,\n            this.currentResult.variables!,\n            this.currentResult.context,\n          )\n        } else if (options.onError) {\n          this.mutateOptions.onError?.(\n            this.currentResult.error!,\n            this.currentResult.variables!,\n            this.currentResult.context,\n          )\n          this.mutateOptions.onSettled?.(\n            undefined,\n            this.currentResult.error,\n            this.currentResult.variables!,\n            this.currentResult.context,\n          )\n        }\n      }\n\n      // Then trigger the listeners\n      if (options.listeners) {\n        this.listeners.forEach(({ listener }) => {\n          listener(this.currentResult)\n        })\n      }\n    })\n  }\n}\n", "'use client'\nimport * as React from 'react'\nimport {\n  MutationObserver,\n  notifyManager,\n  parseMutationArgs,\n} from '@tanstack/query-core'\nimport { useSyncExternalStore } from './useSyncExternalStore'\n\nimport { useQueryClient } from './QueryClientProvider'\nimport { shouldThrowError } from './utils'\nimport type { MutationFunction, MutationKey } from '@tanstack/query-core'\nimport type {\n  UseMutateFunction,\n  UseMutationOptions,\n  UseMutationResult,\n} from './types'\n\n// HOOK\n\nexport function useMutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n>(\n  options: UseMutationOptions<TData, TError, TVariables, TContext>,\n): UseMutationResult<TData, TError, TVariables, TContext>\nexport function useMutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n>(\n  mutationFn: MutationFunction<TData, TVariables>,\n  options?: Omit<\n    UseMutationOptions<TData, TError, TVariables, TContext>,\n    'mutationFn'\n  >,\n): UseMutationResult<TData, TError, TVariables, TContext>\nexport function useMutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n>(\n  mutationKey: MutationKey,\n  options?: Omit<\n    UseMutationOptions<TData, TError, TVariables, TContext>,\n    'mutationKey'\n  >,\n): UseMutationResult<TData, TError, TVariables, TContext>\nexport function useMutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n>(\n  mutationKey: MutationKey,\n  mutationFn?: MutationFunction<TData, TVariables>,\n  options?: Omit<\n    UseMutationOptions<TData, TError, TVariables, TContext>,\n    'mutationKey' | 'mutationFn'\n  >,\n): UseMutationResult<TData, TError, TVariables, TContext>\nexport function useMutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n>(\n  arg1:\n    | MutationKey\n    | MutationFunction<TData, TVariables>\n    | UseMutationOptions<TData, TError, TVariables, TContext>,\n  arg2?:\n    | MutationFunction<TData, TVariables>\n    | UseMutationOptions<TData, TError, TVariables, TContext>,\n  arg3?: UseMutationOptions<TData, TError, TVariables, TContext>,\n): UseMutationResult<TData, TError, TVariables, TContext> {\n  const options = parseMutationArgs(arg1, arg2, arg3)\n  const queryClient = useQueryClient({ context: options.context })\n\n  const [observer] = React.useState(\n    () =>\n      new MutationObserver<TData, TError, TVariables, TContext>(\n        queryClient,\n        options,\n      ),\n  )\n\n  React.useEffect(() => {\n    observer.setOptions(options)\n  }, [observer, options])\n\n  const result = useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) =>\n        observer.subscribe(notifyManager.batchCalls(onStoreChange)),\n      [observer],\n    ),\n    () => observer.getCurrentResult(),\n    () => observer.getCurrentResult(),\n  )\n\n  const mutate = React.useCallback<\n    UseMutateFunction<TData, TError, TVariables, TContext>\n  >(\n    (variables, mutateOptions) => {\n      observer.mutate(variables, mutateOptions).catch(noop)\n    },\n    [observer],\n  )\n\n  if (\n    result.error &&\n    shouldThrowError(observer.options.useErrorBoundary, [result.error])\n  ) {\n    throw result.error\n  }\n\n  return { ...result, mutate, mutateAsync: result.mutate }\n}\n\n// eslint-disable-next-line @typescript-eslint/no-empty-function\nfunction noop() {}\n", "import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name RefreshCw\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAxMmE5IDkgMCAwIDEgOS05IDkuNzUgOS43NSAwIDAgMSA2Ljc0IDIuNzRMMjEgOCIgLz4KICA8cGF0aCBkPSJNMjEgM3Y1aC01IiAvPgogIDxwYXRoIGQ9Ik0yMSAxMmE5IDkgMCAwIDEtOSA5IDkuNzUgOS43NSAwIDAgMS02Ljc0LTIuNzRMMyAxNiIgLz4KICA8cGF0aCBkPSJNOCAxNkgzdjUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/refresh-cw\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst RefreshCw = createLucideIcon('RefreshCw', [\n  [\n    'path',\n    { d: 'M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8', key: 'v9h5vc' },\n  ],\n  ['path', { d: 'M21 3v5h-5', key: '1q7to0' }],\n  [\n    'path',\n    { d: 'M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16', key: '3uifl3' },\n  ],\n  ['path', { d: 'M8 16H3v5', key: '1cv678' }],\n]);\n\nexport default RefreshCw;\n", "import { getAbortController, noop, replaceData, timeUntilStale } from './utils'\nimport { defaultLogger } from './logger'\nimport { notifyManager } from './notifyManager'\nimport { canFetch, createRetryer, isCancelledError } from './retryer'\nimport { Removable } from './removable'\nimport type {\n  CancelOptions,\n  FetchStatus,\n  InitialDataFunction,\n  QueryFunctionContext,\n  QueryKey,\n  QueryMeta,\n  QueryOptions,\n  QueryStatus,\n  SetDataOptions,\n} from './types'\nimport type { QueryCache } from './queryCache'\nimport type { QueryObserver } from './queryObserver'\nimport type { Logger } from './logger'\nimport type { Retryer } from './retryer'\n\n// TYPES\n\ninterface QueryConfig<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  cache: QueryCache\n  queryKey: TQueryKey\n  queryHash: string\n  logger?: Logger\n  options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  defaultOptions?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  state?: QueryState<TData, TError>\n}\n\nexport interface QueryState<TData = unknown, TError = unknown> {\n  data: TData | undefined\n  dataUpdateCount: number\n  dataUpdatedAt: number\n  error: TError | null\n  errorUpdateCount: number\n  errorUpdatedAt: number\n  fetchFailureCount: number\n  fetchFailureReason: TError | null\n  fetchMeta: any\n  isInvalidated: boolean\n  status: QueryStatus\n  fetchStatus: FetchStatus\n}\n\nexport interface FetchContext<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  fetchFn: () => unknown | Promise<unknown>\n  fetchOptions?: FetchOptions\n  signal?: AbortSignal\n  options: QueryOptions<TQueryFnData, TError, TData, any>\n  queryKey: TQueryKey\n  state: QueryState<TData, TError>\n}\n\nexport interface QueryBehavior<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  onFetch: (\n    context: FetchContext<TQueryFnData, TError, TData, TQueryKey>,\n  ) => void\n}\n\nexport interface FetchOptions {\n  cancelRefetch?: boolean\n  meta?: any\n}\n\ninterface FailedAction<TError> {\n  type: 'failed'\n  failureCount: number\n  error: TError\n}\n\ninterface FetchAction {\n  type: 'fetch'\n  meta?: any\n}\n\ninterface SuccessAction<TData> {\n  data: TData | undefined\n  type: 'success'\n  dataUpdatedAt?: number\n  manual?: boolean\n}\n\ninterface ErrorAction<TError> {\n  type: 'error'\n  error: TError\n}\n\ninterface InvalidateAction {\n  type: 'invalidate'\n}\n\ninterface PauseAction {\n  type: 'pause'\n}\n\ninterface ContinueAction {\n  type: 'continue'\n}\n\ninterface SetStateAction<TData, TError> {\n  type: 'setState'\n  state: Partial<QueryState<TData, TError>>\n  setStateOptions?: SetStateOptions\n}\n\nexport type Action<TData, TError> =\n  | ContinueAction\n  | ErrorAction<TError>\n  | FailedAction<TError>\n  | FetchAction\n  | InvalidateAction\n  | PauseAction\n  | SetStateAction<TData, TError>\n  | SuccessAction<TData>\n\nexport interface SetStateOptions {\n  meta?: any\n}\n\n// CLASS\n\nexport class Query<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> extends Removable {\n  queryKey: TQueryKey\n  queryHash: string\n  options!: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  initialState: QueryState<TData, TError>\n  revertState?: QueryState<TData, TError>\n  state: QueryState<TData, TError>\n  isFetchingOptimistic?: boolean\n\n  private cache: QueryCache\n  private logger: Logger\n  private promise?: Promise<TData>\n  private retryer?: Retryer<TData>\n  private observers: QueryObserver<any, any, any, any, any>[]\n  private defaultOptions?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  private abortSignalConsumed: boolean\n\n  constructor(config: QueryConfig<TQueryFnData, TError, TData, TQueryKey>) {\n    super()\n\n    this.abortSignalConsumed = false\n    this.defaultOptions = config.defaultOptions\n    this.setOptions(config.options)\n    this.observers = []\n    this.cache = config.cache\n    this.logger = config.logger || defaultLogger\n    this.queryKey = config.queryKey\n    this.queryHash = config.queryHash\n    this.initialState = config.state || getDefaultState(this.options)\n    this.state = this.initialState\n    this.scheduleGc()\n  }\n\n  get meta(): QueryMeta | undefined {\n    return this.options.meta\n  }\n\n  private setOptions(\n    options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): void {\n    this.options = { ...this.defaultOptions, ...options }\n\n    this.updateCacheTime(this.options.cacheTime)\n  }\n\n  protected optionalRemove() {\n    if (!this.observers.length && this.state.fetchStatus === 'idle') {\n      this.cache.remove(this)\n    }\n  }\n\n  setData(\n    newData: TData,\n    options?: SetDataOptions & { manual: boolean },\n  ): TData {\n    const data = replaceData(this.state.data, newData, this.options)\n\n    // Set data and mark it as cached\n    this.dispatch({\n      data,\n      type: 'success',\n      dataUpdatedAt: options?.updatedAt,\n      manual: options?.manual,\n    })\n\n    return data\n  }\n\n  setState(\n    state: Partial<QueryState<TData, TError>>,\n    setStateOptions?: SetStateOptions,\n  ): void {\n    this.dispatch({ type: 'setState', state, setStateOptions })\n  }\n\n  cancel(options?: CancelOptions): Promise<void> {\n    const promise = this.promise\n    this.retryer?.cancel(options)\n    return promise ? promise.then(noop).catch(noop) : Promise.resolve()\n  }\n\n  destroy(): void {\n    super.destroy()\n\n    this.cancel({ silent: true })\n  }\n\n  reset(): void {\n    this.destroy()\n    this.setState(this.initialState)\n  }\n\n  isActive(): boolean {\n    return this.observers.some((observer) => observer.options.enabled !== false)\n  }\n\n  isDisabled(): boolean {\n    return this.getObserversCount() > 0 && !this.isActive()\n  }\n\n  isStale(): boolean {\n    return (\n      this.state.isInvalidated ||\n      !this.state.dataUpdatedAt ||\n      this.observers.some((observer) => observer.getCurrentResult().isStale)\n    )\n  }\n\n  isStaleByTime(staleTime = 0): boolean {\n    return (\n      this.state.isInvalidated ||\n      !this.state.dataUpdatedAt ||\n      !timeUntilStale(this.state.dataUpdatedAt, staleTime)\n    )\n  }\n\n  onFocus(): void {\n    const observer = this.observers.find((x) => x.shouldFetchOnWindowFocus())\n\n    if (observer) {\n      observer.refetch({ cancelRefetch: false })\n    }\n\n    // Continue fetch if currently paused\n    this.retryer?.continue()\n  }\n\n  onOnline(): void {\n    const observer = this.observers.find((x) => x.shouldFetchOnReconnect())\n\n    if (observer) {\n      observer.refetch({ cancelRefetch: false })\n    }\n\n    // Continue fetch if currently paused\n    this.retryer?.continue()\n  }\n\n  addObserver(observer: QueryObserver<any, any, any, any, any>): void {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer)\n\n      // Stop the query from being garbage collected\n      this.clearGcTimeout()\n\n      this.cache.notify({ type: 'observerAdded', query: this, observer })\n    }\n  }\n\n  removeObserver(observer: QueryObserver<any, any, any, any, any>): void {\n    if (this.observers.includes(observer)) {\n      this.observers = this.observers.filter((x) => x !== observer)\n\n      if (!this.observers.length) {\n        // If the transport layer does not support cancellation\n        // we'll let the query continue so the result can be cached\n        if (this.retryer) {\n          if (this.abortSignalConsumed) {\n            this.retryer.cancel({ revert: true })\n          } else {\n            this.retryer.cancelRetry()\n          }\n        }\n\n        this.scheduleGc()\n      }\n\n      this.cache.notify({ type: 'observerRemoved', query: this, observer })\n    }\n  }\n\n  getObserversCount(): number {\n    return this.observers.length\n  }\n\n  invalidate(): void {\n    if (!this.state.isInvalidated) {\n      this.dispatch({ type: 'invalidate' })\n    }\n  }\n\n  fetch(\n    options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    fetchOptions?: FetchOptions,\n  ): Promise<TData> {\n    if (this.state.fetchStatus !== 'idle') {\n      if (this.state.dataUpdatedAt && fetchOptions?.cancelRefetch) {\n        // Silently cancel current fetch if the user wants to cancel refetches\n        this.cancel({ silent: true })\n      } else if (this.promise) {\n        // make sure that retries that were potentially cancelled due to unmounts can continue\n        this.retryer?.continueRetry()\n        // Return current promise if we are already fetching\n        return this.promise\n      }\n    }\n\n    // Update config if passed, otherwise the config from the last execution is used\n    if (options) {\n      this.setOptions(options)\n    }\n\n    // Use the options from the first observer with a query function if no function is found.\n    // This can happen when the query is hydrated or created with setQueryData.\n    if (!this.options.queryFn) {\n      const observer = this.observers.find((x) => x.options.queryFn)\n      if (observer) {\n        this.setOptions(observer.options)\n      }\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (!Array.isArray(this.options.queryKey)) {\n        this.logger.error(\n          `As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']`,\n        )\n      }\n    }\n\n    const abortController = getAbortController()\n\n    // Create query function context\n    const queryFnContext: QueryFunctionContext<TQueryKey> = {\n      queryKey: this.queryKey,\n      pageParam: undefined,\n      meta: this.meta,\n    }\n\n    // Adds an enumerable signal property to the object that\n    // which sets abortSignalConsumed to true when the signal\n    // is read.\n    const addSignalProperty = (object: unknown) => {\n      Object.defineProperty(object, 'signal', {\n        enumerable: true,\n        get: () => {\n          if (abortController) {\n            this.abortSignalConsumed = true\n            return abortController.signal\n          }\n          return undefined\n        },\n      })\n    }\n\n    addSignalProperty(queryFnContext)\n\n    // Create fetch function\n    const fetchFn = () => {\n      if (!this.options.queryFn) {\n        return Promise.reject(\n          `Missing queryFn for queryKey '${this.options.queryHash}'`,\n        )\n      }\n      this.abortSignalConsumed = false\n      return this.options.queryFn(queryFnContext)\n    }\n\n    // Trigger behavior hook\n    const context: FetchContext<TQueryFnData, TError, TData, TQueryKey> = {\n      fetchOptions,\n      options: this.options,\n      queryKey: this.queryKey,\n      state: this.state,\n      fetchFn,\n    }\n\n    addSignalProperty(context)\n\n    this.options.behavior?.onFetch(context)\n\n    // Store state in case the current fetch needs to be reverted\n    this.revertState = this.state\n\n    // Set to fetching state if not already in it\n    if (\n      this.state.fetchStatus === 'idle' ||\n      this.state.fetchMeta !== context.fetchOptions?.meta\n    ) {\n      this.dispatch({ type: 'fetch', meta: context.fetchOptions?.meta })\n    }\n\n    const onError = (error: TError | { silent?: boolean }) => {\n      // Optimistically update state if needed\n      if (!(isCancelledError(error) && error.silent)) {\n        this.dispatch({\n          type: 'error',\n          error: error as TError,\n        })\n      }\n\n      if (!isCancelledError(error)) {\n        // Notify cache callback\n        this.cache.config.onError?.(error, this as Query<any, any, any, any>)\n        this.cache.config.onSettled?.(\n          this.state.data,\n          error,\n          this as Query<any, any, any, any>,\n        )\n\n        if (process.env.NODE_ENV !== 'production') {\n          this.logger.error(error)\n        }\n      }\n\n      if (!this.isFetchingOptimistic) {\n        // Schedule query gc after fetching\n        this.scheduleGc()\n      }\n      this.isFetchingOptimistic = false\n    }\n\n    // Try to fetch the data\n    this.retryer = createRetryer({\n      fn: context.fetchFn as () => TData,\n      abort: abortController?.abort.bind(abortController),\n      onSuccess: (data) => {\n        if (typeof data === 'undefined') {\n          if (process.env.NODE_ENV !== 'production') {\n            this.logger.error(\n              `Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: ${this.queryHash}`,\n            )\n          }\n          onError(new Error(`${this.queryHash} data is undefined`) as any)\n          return\n        }\n\n        this.setData(data as TData)\n\n        // Notify cache callback\n        this.cache.config.onSuccess?.(data, this as Query<any, any, any, any>)\n        this.cache.config.onSettled?.(\n          data,\n          this.state.error,\n          this as Query<any, any, any, any>,\n        )\n\n        if (!this.isFetchingOptimistic) {\n          // Schedule query gc after fetching\n          this.scheduleGc()\n        }\n        this.isFetchingOptimistic = false\n      },\n      onError,\n      onFail: (failureCount, error) => {\n        this.dispatch({ type: 'failed', failureCount, error })\n      },\n      onPause: () => {\n        this.dispatch({ type: 'pause' })\n      },\n      onContinue: () => {\n        this.dispatch({ type: 'continue' })\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay,\n      networkMode: context.options.networkMode,\n    })\n\n    this.promise = this.retryer.promise\n\n    return this.promise\n  }\n\n  private dispatch(action: Action<TData, TError>): void {\n    const reducer = (\n      state: QueryState<TData, TError>,\n    ): QueryState<TData, TError> => {\n      switch (action.type) {\n        case 'failed':\n          return {\n            ...state,\n            fetchFailureCount: action.failureCount,\n            fetchFailureReason: action.error,\n          }\n        case 'pause':\n          return {\n            ...state,\n            fetchStatus: 'paused',\n          }\n        case 'continue':\n          return {\n            ...state,\n            fetchStatus: 'fetching',\n          }\n        case 'fetch':\n          return {\n            ...state,\n            fetchFailureCount: 0,\n            fetchFailureReason: null,\n            fetchMeta: action.meta ?? null,\n            fetchStatus: canFetch(this.options.networkMode)\n              ? 'fetching'\n              : 'paused',\n            ...(!state.dataUpdatedAt && {\n              error: null,\n              status: 'loading',\n            }),\n          }\n        case 'success':\n          return {\n            ...state,\n            data: action.data,\n            dataUpdateCount: state.dataUpdateCount + 1,\n            dataUpdatedAt: action.dataUpdatedAt ?? Date.now(),\n            error: null,\n            isInvalidated: false,\n            status: 'success',\n            ...(!action.manual && {\n              fetchStatus: 'idle',\n              fetchFailureCount: 0,\n              fetchFailureReason: null,\n            }),\n          }\n        case 'error':\n          const error = action.error as unknown\n\n          if (isCancelledError(error) && error.revert && this.revertState) {\n            return { ...this.revertState, fetchStatus: 'idle' }\n          }\n\n          return {\n            ...state,\n            error: error as TError,\n            errorUpdateCount: state.errorUpdateCount + 1,\n            errorUpdatedAt: Date.now(),\n            fetchFailureCount: state.fetchFailureCount + 1,\n            fetchFailureReason: error as TError,\n            fetchStatus: 'idle',\n            status: 'error',\n          }\n        case 'invalidate':\n          return {\n            ...state,\n            isInvalidated: true,\n          }\n        case 'setState':\n          return {\n            ...state,\n            ...action.state,\n          }\n      }\n    }\n\n    this.state = reducer(this.state)\n\n    notifyManager.batch(() => {\n      this.observers.forEach((observer) => {\n        observer.onQueryUpdate(action)\n      })\n\n      this.cache.notify({ query: this, type: 'updated', action })\n    })\n  }\n}\n\nfunction getDefaultState<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey,\n>(\n  options: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n): QueryState<TData, TError> {\n  const data =\n    typeof options.initialData === 'function'\n      ? (options.initialData as InitialDataFunction<TData>)()\n      : options.initialData\n\n  const hasData = typeof data !== 'undefined'\n\n  const initialDataUpdatedAt = hasData\n    ? typeof options.initialDataUpdatedAt === 'function'\n      ? (options.initialDataUpdatedAt as () => number | undefined)()\n      : options.initialDataUpdatedAt\n    : 0\n\n  return {\n    data,\n    dataUpdateCount: 0,\n    dataUpdatedAt: hasData ? initialDataUpdatedAt ?? Date.now() : 0,\n    error: null,\n    errorUpdateCount: 0,\n    errorUpdatedAt: 0,\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchMeta: null,\n    isInvalidated: false,\n    status: hasData ? 'success' : 'loading',\n    fetchStatus: 'idle',\n  }\n}\n", "import { hashQueryKeyByOptions, matchQuery, parseFilterArgs } from './utils'\nimport { Query } from './query'\nimport { notifyManager } from './notifyManager'\nimport { Subscribable } from './subscribable'\nimport type { QueryFilters } from './utils'\nimport type { Action, QueryState } from './query'\nimport type { NotifyEvent, OmitKeyof, QueryKey, QueryOptions } from './types'\nimport type { QueryClient } from './queryClient'\nimport type { QueryObserver } from './queryObserver'\n\n// TYPES\n\ninterface QueryCacheConfig {\n  onError?: (error: unknown, query: Query<unknown, unknown, unknown>) => void\n  onSuccess?: (data: unknown, query: Query<unknown, unknown, unknown>) => void\n  onSettled?: (\n    data: unknown | undefined,\n    error: unknown | null,\n    query: Query<unknown, unknown, unknown>,\n  ) => void\n}\n\ninterface QueryHashMap {\n  [hash: string]: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryAdded extends NotifyEvent {\n  type: 'added'\n  query: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryRemoved extends NotifyEvent {\n  type: 'removed'\n  query: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryUpdated extends NotifyEvent {\n  type: 'updated'\n  query: Query<any, any, any, any>\n  action: Action<any, any>\n}\n\ninterface NotifyEventQueryObserverAdded extends NotifyEvent {\n  type: 'observerAdded'\n  query: Query<any, any, any, any>\n  observer: QueryObserver<any, any, any, any, any>\n}\n\ninterface NotifyEventQueryObserverRemoved extends NotifyEvent {\n  type: 'observerRemoved'\n  query: Query<any, any, any, any>\n  observer: QueryObserver<any, any, any, any, any>\n}\n\ninterface NotifyEventQueryObserverResultsUpdated extends NotifyEvent {\n  type: 'observerResultsUpdated'\n  query: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryObserverOptionsUpdated extends NotifyEvent {\n  type: 'observerOptionsUpdated'\n  query: Query<any, any, any, any>\n  observer: QueryObserver<any, any, any, any, any>\n}\n\nexport type QueryCacheNotifyEvent =\n  | NotifyEventQueryAdded\n  | NotifyEventQueryRemoved\n  | NotifyEventQueryUpdated\n  | NotifyEventQueryObserverAdded\n  | NotifyEventQueryObserverRemoved\n  | NotifyEventQueryObserverResultsUpdated\n  | NotifyEventQueryObserverOptionsUpdated\n\ntype QueryCacheListener = (event: QueryCacheNotifyEvent) => void\n\n// CLASS\n\nexport class QueryCache extends Subscribable<QueryCacheListener> {\n  config: QueryCacheConfig\n\n  private queries: Query<any, any, any, any>[]\n  private queriesMap: QueryHashMap\n\n  constructor(config?: QueryCacheConfig) {\n    super()\n    this.config = config || {}\n    this.queries = []\n    this.queriesMap = {}\n  }\n\n  build<TQueryFnData, TError, TData, TQueryKey extends QueryKey>(\n    client: QueryClient,\n    options: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    state?: QueryState<TData, TError>,\n  ): Query<TQueryFnData, TError, TData, TQueryKey> {\n    const queryKey = options.queryKey!\n    const queryHash =\n      options.queryHash ?? hashQueryKeyByOptions(queryKey, options)\n    let query = this.get<TQueryFnData, TError, TData, TQueryKey>(queryHash)\n\n    if (!query) {\n      query = new Query({\n        cache: this,\n        logger: client.getLogger(),\n        queryKey,\n        queryHash,\n        options: client.defaultQueryOptions(options),\n        state,\n        defaultOptions: client.getQueryDefaults(queryKey),\n      })\n      this.add(query)\n    }\n\n    return query\n  }\n\n  add(query: Query<any, any, any, any>): void {\n    if (!this.queriesMap[query.queryHash]) {\n      this.queriesMap[query.queryHash] = query\n      this.queries.push(query)\n      this.notify({\n        type: 'added',\n        query,\n      })\n    }\n  }\n\n  remove(query: Query<any, any, any, any>): void {\n    const queryInMap = this.queriesMap[query.queryHash]\n\n    if (queryInMap) {\n      query.destroy()\n\n      this.queries = this.queries.filter((x) => x !== query)\n\n      if (queryInMap === query) {\n        delete this.queriesMap[query.queryHash]\n      }\n\n      this.notify({ type: 'removed', query })\n    }\n  }\n\n  clear(): void {\n    notifyManager.batch(() => {\n      this.queries.forEach((query) => {\n        this.remove(query)\n      })\n    })\n  }\n\n  get<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryHash: string,\n  ): Query<TQueryFnData, TError, TData, TQueryKey> | undefined {\n    return this.queriesMap[queryHash]\n  }\n\n  getAll(): Query[] {\n    return this.queries\n  }\n\n  find<TQueryFnData = unknown, TError = unknown, TData = TQueryFnData>(\n    filters: QueryFilters,\n  ): Query<TQueryFnData, TError, TData> | undefined\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  find<TQueryFnData = unknown, TError = unknown, TData = TQueryFnData>(\n    queryKey: QueryKey,\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): Query<TQueryFnData, TError, TData> | undefined\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  find<TQueryFnData = unknown, TError = unknown, TData = TQueryFnData>(\n    arg1: QueryKey | QueryFilters,\n    arg2?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): Query<TQueryFnData, TError, TData> | undefined {\n    const [filters] = parseFilterArgs(arg1, arg2)\n\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true\n    }\n\n    return this.queries.find((query) => matchQuery(filters, query))\n  }\n\n  findAll(filters?: QueryFilters): Query[]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  findAll(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): Query[]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  findAll(\n    arg1?: QueryKey | QueryFilters,\n    arg2?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): Query[]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  findAll(\n    arg1?: QueryKey | QueryFilters,\n    arg2?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): Query[] {\n    const [filters] = parseFilterArgs(arg1, arg2)\n    return Object.keys(filters).length > 0\n      ? this.queries.filter((query) => matchQuery(filters, query))\n      : this.queries\n  }\n\n  notify(event: QueryCacheNotifyEvent) {\n    notifyManager.batch(() => {\n      this.listeners.forEach(({ listener }) => {\n        listener(event)\n      })\n    })\n  }\n\n  onFocus(): void {\n    notifyManager.batch(() => {\n      this.queries.forEach((query) => {\n        query.onFocus()\n      })\n    })\n  }\n\n  onOnline(): void {\n    notifyManager.batch(() => {\n      this.queries.forEach((query) => {\n        query.onOnline()\n      })\n    })\n  }\n}\n", "import { notify<PERSON><PERSON><PERSON> } from './notifyManager'\nimport { Mutation } from './mutation'\nimport { matchMutation, noop } from './utils'\nimport { Subscribable } from './subscribable'\nimport type { MutationObserver } from './mutationObserver'\nimport type { MutationOptions, NotifyEvent } from './types'\nimport type { QueryClient } from './queryClient'\nimport type { Action, MutationState } from './mutation'\nimport type { MutationFilters } from './utils'\n\n// TYPES\n\ninterface MutationCacheConfig {\n  onError?: (\n    error: unknown,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n  onSuccess?: (\n    data: unknown,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n  onMutate?: (\n    variables: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n  onSettled?: (\n    data: unknown | undefined,\n    error: unknown | null,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n}\n\ninterface NotifyEventMutationAdded extends NotifyEvent {\n  type: 'added'\n  mutation: Mutation<any, any, any, any>\n}\ninterface NotifyEventMutationRemoved extends NotifyEvent {\n  type: 'removed'\n  mutation: Mutation<any, any, any, any>\n}\n\ninterface NotifyEventMutationObserverAdded extends NotifyEvent {\n  type: 'observerAdded'\n  mutation: Mutation<any, any, any, any>\n  observer: MutationObserver<any, any, any>\n}\n\ninterface NotifyEventMutationObserverRemoved extends NotifyEvent {\n  type: 'observerRemoved'\n  mutation: Mutation<any, any, any, any>\n  observer: MutationObserver<any, any, any>\n}\n\ninterface NotifyEventMutationObserverOptionsUpdated extends NotifyEvent {\n  type: 'observerOptionsUpdated'\n  mutation?: Mutation<any, any, any, any>\n  observer: MutationObserver<any, any, any, any>\n}\n\ninterface NotifyEventMutationUpdated extends NotifyEvent {\n  type: 'updated'\n  mutation: Mutation<any, any, any, any>\n  action: Action<any, any, any, any>\n}\n\ntype MutationCacheNotifyEvent =\n  | NotifyEventMutationAdded\n  | NotifyEventMutationRemoved\n  | NotifyEventMutationObserverAdded\n  | NotifyEventMutationObserverRemoved\n  | NotifyEventMutationObserverOptionsUpdated\n  | NotifyEventMutationUpdated\n\ntype MutationCacheListener = (event: MutationCacheNotifyEvent) => void\n\n// CLASS\n\nexport class MutationCache extends Subscribable<MutationCacheListener> {\n  config: MutationCacheConfig\n\n  private mutations: Mutation<any, any, any, any>[]\n  private mutationId: number\n  private resuming: Promise<unknown> | undefined\n\n  constructor(config?: MutationCacheConfig) {\n    super()\n    this.config = config || {}\n    this.mutations = []\n    this.mutationId = 0\n  }\n\n  build<TData, TError, TVariables, TContext>(\n    client: QueryClient,\n    options: MutationOptions<TData, TError, TVariables, TContext>,\n    state?: MutationState<TData, TError, TVariables, TContext>,\n  ): Mutation<TData, TError, TVariables, TContext> {\n    const mutation = new Mutation({\n      mutationCache: this,\n      logger: client.getLogger(),\n      mutationId: ++this.mutationId,\n      options: client.defaultMutationOptions(options),\n      state,\n      defaultOptions: options.mutationKey\n        ? client.getMutationDefaults(options.mutationKey)\n        : undefined,\n    })\n\n    this.add(mutation)\n\n    return mutation\n  }\n\n  add(mutation: Mutation<any, any, any, any>): void {\n    this.mutations.push(mutation)\n    this.notify({ type: 'added', mutation })\n  }\n\n  remove(mutation: Mutation<any, any, any, any>): void {\n    this.mutations = this.mutations.filter((x) => x !== mutation)\n    this.notify({ type: 'removed', mutation })\n  }\n\n  clear(): void {\n    notifyManager.batch(() => {\n      this.mutations.forEach((mutation) => {\n        this.remove(mutation)\n      })\n    })\n  }\n\n  getAll(): Mutation[] {\n    return this.mutations\n  }\n\n  find<TData = unknown, TError = unknown, TVariables = any, TContext = unknown>(\n    filters: MutationFilters,\n  ): Mutation<TData, TError, TVariables, TContext> | undefined {\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true\n    }\n\n    return this.mutations.find((mutation) => matchMutation(filters, mutation))\n  }\n\n  findAll(filters: MutationFilters): Mutation[] {\n    return this.mutations.filter((mutation) => matchMutation(filters, mutation))\n  }\n\n  notify(event: MutationCacheNotifyEvent) {\n    notifyManager.batch(() => {\n      this.listeners.forEach(({ listener }) => {\n        listener(event)\n      })\n    })\n  }\n\n  resumePausedMutations(): Promise<unknown> {\n    this.resuming = (this.resuming ?? Promise.resolve())\n      .then(() => {\n        const pausedMutations = this.mutations.filter((x) => x.state.isPaused)\n        return notifyManager.batch(() =>\n          pausedMutations.reduce(\n            (promise, mutation) =>\n              promise.then(() => mutation.continue().catch(noop)),\n            Promise.resolve() as Promise<unknown>,\n          ),\n        )\n      })\n      .then(() => {\n        this.resuming = undefined\n      })\n\n    return this.resuming\n  }\n}\n", "import type { QueryBehavior } from './query'\n\nimport type {\n  InfiniteData,\n  QueryFunctionContext,\n  QueryOptions,\n  RefetchQueryFilters,\n} from './types'\n\nexport function infiniteQueryBehavior<\n  TQueryFnData,\n  TError,\n  TData,\n>(): QueryBehavior<TQueryFnData, TError, InfiniteData<TData>> {\n  return {\n    onFetch: (context) => {\n      context.fetchFn = () => {\n        const refetchPage: RefetchQueryFilters['refetchPage'] | undefined =\n          context.fetchOptions?.meta?.refetchPage\n        const fetchMore = context.fetchOptions?.meta?.fetchMore\n        const pageParam = fetchMore?.pageParam\n        const isFetchingNextPage = fetchMore?.direction === 'forward'\n        const isFetchingPreviousPage = fetchMore?.direction === 'backward'\n        const oldPages = context.state.data?.pages || []\n        const oldPageParams = context.state.data?.pageParams || []\n        let newPageParams = oldPageParams\n        let cancelled = false\n\n        const addSignalProperty = (object: unknown) => {\n          Object.defineProperty(object, 'signal', {\n            enumerable: true,\n            get: () => {\n              if (context.signal?.aborted) {\n                cancelled = true\n              } else {\n                context.signal?.addEventListener('abort', () => {\n                  cancelled = true\n                })\n              }\n              return context.signal\n            },\n          })\n        }\n\n        // Get query function\n        const queryFn =\n          context.options.queryFn ||\n          (() =>\n            Promise.reject(\n              `Missing queryFn for queryKey '${context.options.queryHash}'`,\n            ))\n\n        const buildNewPages = (\n          pages: unknown[],\n          param: unknown,\n          page: unknown,\n          previous?: boolean,\n        ) => {\n          newPageParams = previous\n            ? [param, ...newPageParams]\n            : [...newPageParams, param]\n          return previous ? [page, ...pages] : [...pages, page]\n        }\n\n        // Create function to fetch a page\n        const fetchPage = (\n          pages: unknown[],\n          manual?: boolean,\n          param?: unknown,\n          previous?: boolean,\n        ): Promise<unknown[]> => {\n          if (cancelled) {\n            return Promise.reject('Cancelled')\n          }\n\n          if (typeof param === 'undefined' && !manual && pages.length) {\n            return Promise.resolve(pages)\n          }\n\n          const queryFnContext: QueryFunctionContext = {\n            queryKey: context.queryKey,\n            pageParam: param,\n            meta: context.options.meta,\n          }\n\n          addSignalProperty(queryFnContext)\n\n          const queryFnResult = queryFn(queryFnContext)\n\n          const promise = Promise.resolve(queryFnResult).then((page) =>\n            buildNewPages(pages, param, page, previous),\n          )\n\n          return promise\n        }\n\n        let promise: Promise<unknown[]>\n\n        // Fetch first page?\n        if (!oldPages.length) {\n          promise = fetchPage([])\n        }\n\n        // Fetch next page?\n        else if (isFetchingNextPage) {\n          const manual = typeof pageParam !== 'undefined'\n          const param = manual\n            ? pageParam\n            : getNextPageParam(context.options, oldPages)\n          promise = fetchPage(oldPages, manual, param)\n        }\n\n        // Fetch previous page?\n        else if (isFetchingPreviousPage) {\n          const manual = typeof pageParam !== 'undefined'\n          const param = manual\n            ? pageParam\n            : getPreviousPageParam(context.options, oldPages)\n          promise = fetchPage(oldPages, manual, param, true)\n        }\n\n        // Refetch pages\n        else {\n          newPageParams = []\n\n          const manual = typeof context.options.getNextPageParam === 'undefined'\n\n          const shouldFetchFirstPage =\n            refetchPage && oldPages[0]\n              ? refetchPage(oldPages[0], 0, oldPages)\n              : true\n\n          // Fetch first page\n          promise = shouldFetchFirstPage\n            ? fetchPage([], manual, oldPageParams[0])\n            : Promise.resolve(buildNewPages([], oldPageParams[0], oldPages[0]))\n\n          // Fetch remaining pages\n          for (let i = 1; i < oldPages.length; i++) {\n            promise = promise.then((pages) => {\n              const shouldFetchNextPage =\n                refetchPage && oldPages[i]\n                  ? refetchPage(oldPages[i], i, oldPages)\n                  : true\n\n              if (shouldFetchNextPage) {\n                const param = manual\n                  ? oldPageParams[i]\n                  : getNextPageParam(context.options, pages)\n                return fetchPage(pages, manual, param)\n              }\n              return Promise.resolve(\n                buildNewPages(pages, oldPageParams[i], oldPages[i]),\n              )\n            })\n          }\n        }\n\n        const finalPromise = promise.then((pages) => ({\n          pages,\n          pageParams: newPageParams,\n        }))\n\n        return finalPromise\n      }\n    },\n  }\n}\n\nexport function getNextPageParam(\n  options: QueryOptions<any, any>,\n  pages: unknown[],\n): unknown | undefined {\n  return options.getNextPageParam?.(pages[pages.length - 1], pages)\n}\n\nexport function getPreviousPageParam(\n  options: QueryOptions<any, any>,\n  pages: unknown[],\n): unknown | undefined {\n  return options.getPreviousPageParam?.(pages[0], pages)\n}\n\n/**\n * Checks if there is a next page.\n * Returns `undefined` if it cannot be determined.\n */\nexport function hasNextPage(\n  options: QueryOptions<any, any, any, any>,\n  pages?: unknown,\n): boolean | undefined {\n  if (options.getNextPageParam && Array.isArray(pages)) {\n    const nextPageParam = getNextPageParam(options, pages)\n    return (\n      typeof nextPageParam !== 'undefined' &&\n      nextPageParam !== null &&\n      nextPageParam !== false\n    )\n  }\n  return\n}\n\n/**\n * Checks if there is a previous page.\n * Returns `undefined` if it cannot be determined.\n */\nexport function hasPreviousPage(\n  options: QueryOptions<any, any, any, any>,\n  pages?: unknown,\n): boolean | undefined {\n  if (options.getPreviousPageParam && Array.isArray(pages)) {\n    const previousPageParam = getPreviousPageParam(options, pages)\n    return (\n      typeof previousPageParam !== 'undefined' &&\n      previousPageParam !== null &&\n      previousPageParam !== false\n    )\n  }\n  return\n}\n", "import {\n  functionalUpdate,\n  hashQueryKey,\n  hashQueryKeyByOptions,\n  noop,\n  parseFilterArgs,\n  parseQueryArgs,\n  partialMatchKey,\n} from './utils'\nimport { QueryCache } from './queryCache'\nimport { MutationCache } from './mutationCache'\nimport { focusManager } from './focusManager'\nimport { onlineManager } from './onlineManager'\nimport { notifyManager } from './notifyManager'\nimport { infiniteQueryBehavior } from './infiniteQueryBehavior'\nimport { defaultLogger } from './logger'\nimport type { OmitKeyof } from '@tanstack/query-core'\nimport type { CancelOptions, DefaultedQueryObserverOptions } from './types'\nimport type { Logger } from './logger'\nimport type { QueryState } from './query'\nimport type {\n  DefaultOptions,\n  FetchInfiniteQueryOptions,\n  FetchQueryOptions,\n  InfiniteData,\n  InvalidateOptions,\n  InvalidateQueryFilters,\n  MutationKey,\n  MutationObserverOptions,\n  MutationOptions,\n  QueryClientConfig,\n  QueryFunction,\n  QueryKey,\n  QueryObserverOptions,\n  QueryOptions,\n  RefetchOptions,\n  RefetchQueryFilters,\n  ResetOptions,\n  ResetQueryFilters,\n  SetDataOptions,\n  WithRequired,\n} from './types'\nimport type { MutationFilters, QueryFilters, Updater } from './utils'\n\n// TYPES\n\ninterface QueryDefaults {\n  queryKey: QueryKey\n  defaultOptions: QueryOptions<any, any, any>\n}\n\ninterface MutationDefaults {\n  mutationKey: MutationKey\n  defaultOptions: MutationOptions<any, any, any, any>\n}\n\n// CLASS\n\nexport class QueryClient {\n  private queryCache: QueryCache\n  private mutationCache: MutationCache\n  private logger: Logger\n  private defaultOptions: DefaultOptions\n  private queryDefaults: QueryDefaults[]\n  private mutationDefaults: MutationDefaults[]\n  private mountCount: number\n  private unsubscribeFocus?: () => void\n  private unsubscribeOnline?: () => void\n\n  constructor(config: QueryClientConfig = {}) {\n    this.queryCache = config.queryCache || new QueryCache()\n    this.mutationCache = config.mutationCache || new MutationCache()\n    this.logger = config.logger || defaultLogger\n    this.defaultOptions = config.defaultOptions || {}\n    this.queryDefaults = []\n    this.mutationDefaults = []\n    this.mountCount = 0\n\n    if (process.env.NODE_ENV !== 'production' && config.logger) {\n      this.logger.error(\n        `Passing a custom logger has been deprecated and will be removed in the next major version.`,\n      )\n    }\n  }\n\n  mount(): void {\n    this.mountCount++\n    if (this.mountCount !== 1) return\n\n    this.unsubscribeFocus = focusManager.subscribe(() => {\n      if (focusManager.isFocused()) {\n        this.resumePausedMutations()\n        this.queryCache.onFocus()\n      }\n    })\n    this.unsubscribeOnline = onlineManager.subscribe(() => {\n      if (onlineManager.isOnline()) {\n        this.resumePausedMutations()\n        this.queryCache.onOnline()\n      }\n    })\n  }\n\n  unmount(): void {\n    this.mountCount--\n    if (this.mountCount !== 0) return\n\n    this.unsubscribeFocus?.()\n    this.unsubscribeFocus = undefined\n\n    this.unsubscribeOnline?.()\n    this.unsubscribeOnline = undefined\n  }\n\n  isFetching(filters?: QueryFilters): number\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  isFetching(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): number\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  isFetching(arg1?: QueryKey | QueryFilters, arg2?: QueryFilters): number {\n    const [filters] = parseFilterArgs(arg1, arg2)\n    filters.fetchStatus = 'fetching'\n    return this.queryCache.findAll(filters).length\n  }\n\n  isMutating(filters?: MutationFilters): number {\n    return this.mutationCache.findAll({ ...filters, fetching: true }).length\n  }\n\n  getQueryData<TQueryFnData = unknown>(\n    queryKey: QueryKey,\n  ): TQueryFnData | undefined\n  /**\n   * @deprecated This method will accept only queryKey in the next major version.\n   */\n  getQueryData<TQueryFnData = unknown>(\n    queryKey: QueryKey,\n    filters: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): TQueryFnData | undefined\n  /**\n   * @deprecated This method will accept only queryKey in the next major version.\n   */\n  getQueryData<TQueryFnData = unknown>(\n    queryKey: QueryKey,\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): TQueryFnData | undefined {\n    return this.queryCache.find<TQueryFnData>(queryKey, filters)?.state.data\n  }\n\n  ensureQueryData<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: WithRequired<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n  ): Promise<TData>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  ensureQueryData<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    options?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n  ): Promise<TData>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  ensureQueryData<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<TData>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  ensureQueryData<\n    TQueryFnData,\n    TError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    arg1:\n      | TQueryKey\n      | WithRequired<\n          FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n          'queryKey'\n        >,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg3?: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<TData> {\n    const parsedOptions = parseQueryArgs(arg1, arg2, arg3)\n    const cachedData = this.getQueryData<TData>(parsedOptions.queryKey!)\n\n    return cachedData\n      ? Promise.resolve(cachedData)\n      : this.fetchQuery(parsedOptions)\n  }\n\n  getQueriesData<TQueryFnData = unknown>(\n    filters: QueryFilters,\n  ): [QueryKey, TQueryFnData | undefined][]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  getQueriesData<TQueryFnData = unknown>(\n    queryKey: QueryKey,\n  ): [QueryKey, TQueryFnData | undefined][]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  getQueriesData<TQueryFnData = unknown>(\n    queryKeyOrFilters: QueryKey | QueryFilters,\n  ): [QueryKey, TQueryFnData | undefined][] {\n    return this.getQueryCache()\n      .findAll(queryKeyOrFilters)\n      .map(({ queryKey, state }) => {\n        const data = state.data as TQueryFnData | undefined\n        return [queryKey, data]\n      })\n  }\n\n  setQueryData<TQueryFnData>(\n    queryKey: QueryKey,\n    updater: Updater<TQueryFnData | undefined, TQueryFnData | undefined>,\n    options?: SetDataOptions,\n  ): TQueryFnData | undefined {\n    const query = this.queryCache.find<TQueryFnData>(queryKey)\n    const prevData = query?.state.data\n    const data = functionalUpdate(updater, prevData)\n\n    if (typeof data === 'undefined') {\n      return undefined\n    }\n\n    const parsedOptions = parseQueryArgs(queryKey)\n    const defaultedOptions = this.defaultQueryOptions(parsedOptions)\n    return this.queryCache\n      .build(this, defaultedOptions)\n      .setData(data, { ...options, manual: true })\n  }\n\n  setQueriesData<TQueryFnData>(\n    filters: QueryFilters,\n    updater: Updater<TQueryFnData | undefined, TQueryFnData | undefined>,\n    options?: SetDataOptions,\n  ): [QueryKey, TQueryFnData | undefined][]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  setQueriesData<TQueryFnData>(\n    queryKey: QueryKey,\n    updater: Updater<TQueryFnData | undefined, TQueryFnData | undefined>,\n    options?: SetDataOptions,\n  ): [QueryKey, TQueryFnData | undefined][]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  setQueriesData<TQueryFnData>(\n    queryKeyOrFilters: QueryKey | QueryFilters,\n    updater: Updater<TQueryFnData | undefined, TQueryFnData | undefined>,\n    options?: SetDataOptions,\n  ): [QueryKey, TQueryFnData | undefined][] {\n    return notifyManager.batch(() =>\n      this.getQueryCache()\n        .findAll(queryKeyOrFilters)\n        .map(({ queryKey }) => [\n          queryKey,\n          this.setQueryData<TQueryFnData>(queryKey, updater, options),\n        ]),\n    )\n  }\n\n  getQueryState<TQueryFnData = unknown, TError = undefined>(\n    queryKey: QueryKey,\n    /**\n     * @deprecated This filters will be removed in the next major version.\n     */\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): QueryState<TQueryFnData, TError> | undefined {\n    return this.queryCache.find<TQueryFnData, TError>(queryKey, filters)?.state\n  }\n\n  removeQueries(filters?: QueryFilters): void\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  removeQueries(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): void\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  removeQueries(\n    arg1?: QueryKey | QueryFilters,\n    arg2?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): void {\n    const [filters] = parseFilterArgs(arg1, arg2)\n    const queryCache = this.queryCache\n    notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        queryCache.remove(query)\n      })\n    })\n  }\n\n  resetQueries<TPageData = unknown>(\n    filters?: ResetQueryFilters<TPageData>,\n    options?: ResetOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  resetQueries<TPageData = unknown>(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<ResetQueryFilters<TPageData>, 'queryKey'>,\n    options?: ResetOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  resetQueries(\n    arg1?: QueryKey | ResetQueryFilters,\n    arg2?: OmitKeyof<ResetQueryFilters, 'queryKey'> | ResetOptions,\n    arg3?: ResetOptions,\n  ): Promise<void> {\n    const [filters, options] = parseFilterArgs(arg1, arg2, arg3)\n    const queryCache = this.queryCache\n\n    const refetchFilters: RefetchQueryFilters = {\n      type: 'active',\n      ...filters,\n    }\n\n    return notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        query.reset()\n      })\n      return this.refetchQueries(refetchFilters, options)\n    })\n  }\n\n  cancelQueries(filters?: QueryFilters, options?: CancelOptions): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  cancelQueries(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n    options?: CancelOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  cancelQueries(\n    arg1?: QueryKey | QueryFilters,\n    arg2?: OmitKeyof<QueryFilters, 'queryKey'> | CancelOptions,\n    arg3?: CancelOptions,\n  ): Promise<void> {\n    const [filters, cancelOptions = {}] = parseFilterArgs(arg1, arg2, arg3)\n\n    if (typeof cancelOptions.revert === 'undefined') {\n      cancelOptions.revert = true\n    }\n\n    const promises = notifyManager.batch(() =>\n      this.queryCache\n        .findAll(filters)\n        .map((query) => query.cancel(cancelOptions)),\n    )\n\n    return Promise.all(promises).then(noop).catch(noop)\n  }\n\n  invalidateQueries<TPageData = unknown>(\n    filters?: InvalidateQueryFilters<TPageData>,\n    options?: InvalidateOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  invalidateQueries<TPageData = unknown>(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<InvalidateQueryFilters<TPageData>, 'queryKey'>,\n    options?: InvalidateOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  invalidateQueries(\n    arg1?: QueryKey | InvalidateQueryFilters,\n    arg2?: OmitKeyof<InvalidateQueryFilters, 'queryKey'> | InvalidateOptions,\n    arg3?: InvalidateOptions,\n  ): Promise<void> {\n    const [filters, options] = parseFilterArgs(arg1, arg2, arg3)\n\n    return notifyManager.batch(() => {\n      this.queryCache.findAll(filters).forEach((query) => {\n        query.invalidate()\n      })\n\n      if (filters.refetchType === 'none') {\n        return Promise.resolve()\n      }\n      const refetchFilters: RefetchQueryFilters = {\n        ...filters,\n        type: filters.refetchType ?? filters.type ?? 'active',\n      }\n      return this.refetchQueries(refetchFilters, options)\n    })\n  }\n\n  refetchQueries<TPageData = unknown>(\n    filters?: RefetchQueryFilters<TPageData>,\n    options?: RefetchOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  refetchQueries<TPageData = unknown>(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<RefetchQueryFilters<TPageData>, 'queryKey'>,\n    options?: RefetchOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  refetchQueries(\n    arg1?: QueryKey | RefetchQueryFilters,\n    arg2?: OmitKeyof<RefetchQueryFilters, 'queryKey'> | RefetchOptions,\n    arg3?: RefetchOptions,\n  ): Promise<void> {\n    const [filters, options] = parseFilterArgs(arg1, arg2, arg3)\n\n    const promises = notifyManager.batch(() =>\n      this.queryCache\n        .findAll(filters)\n        .filter((query) => !query.isDisabled())\n        .map((query) =>\n          query.fetch(undefined, {\n            ...options,\n            cancelRefetch: options?.cancelRefetch ?? true,\n            meta: { refetchPage: filters.refetchPage },\n          }),\n        ),\n    )\n\n    let promise = Promise.all(promises).then(noop)\n\n    if (!options?.throwOnError) {\n      promise = promise.catch(noop)\n    }\n\n    return promise\n  }\n\n  fetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<TData>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    options?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n  ): Promise<TData>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<TData>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchQuery<\n    TQueryFnData,\n    TError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    arg1: TQueryKey | FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | OmitKeyof<\n          FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n          'queryKey'\n        >,\n    arg3?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<TData> {\n    const parsedOptions = parseQueryArgs(arg1, arg2, arg3)\n    const defaultedOptions = this.defaultQueryOptions(parsedOptions)\n\n    // https://github.com/tannerlinsley/react-query/issues/652\n    if (typeof defaultedOptions.retry === 'undefined') {\n      defaultedOptions.retry = false\n    }\n\n    const query = this.queryCache.build(this, defaultedOptions)\n\n    return query.isStaleByTime(defaultedOptions.staleTime)\n      ? query.fetch(defaultedOptions)\n      : Promise.resolve(query.state.data as TData)\n  }\n\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    options?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    arg1: TQueryKey | FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | OmitKeyof<\n          FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n          'queryKey'\n        >,\n    arg3?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<void> {\n    return this.fetchQuery(arg1 as any, arg2 as any, arg3)\n      .then(noop)\n      .catch(noop)\n  }\n\n  fetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<InfiniteData<TData>>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    options?: OmitKeyof<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n  ): Promise<InfiniteData<TData>>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: OmitKeyof<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<InfiniteData<TData>>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchInfiniteQuery<\n    TQueryFnData,\n    TError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    arg1:\n      | TQueryKey\n      | FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | OmitKeyof<\n          FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n          'queryKey'\n        >,\n    arg3?: OmitKeyof<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<InfiniteData<TData>> {\n    const parsedOptions = parseQueryArgs(arg1, arg2, arg3)\n    parsedOptions.behavior = infiniteQueryBehavior<\n      TQueryFnData,\n      TError,\n      TData\n    >()\n    return this.fetchQuery(parsedOptions)\n  }\n\n  prefetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    options?: OmitKeyof<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: OmitKeyof<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchInfiniteQuery<\n    TQueryFnData,\n    TError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    arg1:\n      | TQueryKey\n      | FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | OmitKeyof<\n          FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n          'queryKey'\n        >,\n    arg3?: OmitKeyof<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<void> {\n    return this.fetchInfiniteQuery(arg1 as any, arg2 as any, arg3)\n      .then(noop)\n      .catch(noop)\n  }\n\n  resumePausedMutations(): Promise<unknown> {\n    return this.mutationCache.resumePausedMutations()\n  }\n\n  getQueryCache(): QueryCache {\n    return this.queryCache\n  }\n\n  getMutationCache(): MutationCache {\n    return this.mutationCache\n  }\n\n  getLogger(): Logger {\n    return this.logger\n  }\n\n  getDefaultOptions(): DefaultOptions {\n    return this.defaultOptions\n  }\n\n  setDefaultOptions(options: DefaultOptions): void {\n    this.defaultOptions = options\n  }\n\n  setQueryDefaults(\n    queryKey: QueryKey,\n    options: QueryObserverOptions<unknown, any, any, any>,\n  ): void {\n    const result = this.queryDefaults.find(\n      (x) => hashQueryKey(queryKey) === hashQueryKey(x.queryKey),\n    )\n    if (result) {\n      result.defaultOptions = options\n    } else {\n      this.queryDefaults.push({ queryKey, defaultOptions: options })\n    }\n  }\n\n  getQueryDefaults(\n    queryKey?: QueryKey,\n  ): QueryObserverOptions<any, any, any, any, any> | undefined {\n    if (!queryKey) {\n      return undefined\n    }\n\n    // Get the first matching defaults\n    const firstMatchingDefaults = this.queryDefaults.find((x) =>\n      partialMatchKey(queryKey, x.queryKey),\n    )\n\n    // Additional checks and error in dev mode\n    if (process.env.NODE_ENV !== 'production') {\n      // Retrieve all matching defaults for the given key\n      const matchingDefaults = this.queryDefaults.filter((x) =>\n        partialMatchKey(queryKey, x.queryKey),\n      )\n      // It is ok not having defaults, but it is error prone to have more than 1 default for a given key\n      if (matchingDefaults.length > 1) {\n        this.logger.error(\n          `[QueryClient] Several query defaults match with key '${JSON.stringify(\n            queryKey,\n          )}'. The first matching query defaults are used. Please check how query defaults are registered. Order does matter here. cf. https://react-query.tanstack.com/reference/QueryClient#queryclientsetquerydefaults.`,\n        )\n      }\n    }\n\n    return firstMatchingDefaults?.defaultOptions\n  }\n\n  setMutationDefaults(\n    mutationKey: MutationKey,\n    options: MutationObserverOptions<any, any, any, any>,\n  ): void {\n    const result = this.mutationDefaults.find(\n      (x) => hashQueryKey(mutationKey) === hashQueryKey(x.mutationKey),\n    )\n    if (result) {\n      result.defaultOptions = options\n    } else {\n      this.mutationDefaults.push({ mutationKey, defaultOptions: options })\n    }\n  }\n\n  getMutationDefaults(\n    mutationKey?: MutationKey,\n  ): MutationObserverOptions<any, any, any, any> | undefined {\n    if (!mutationKey) {\n      return undefined\n    }\n\n    // Get the first matching defaults\n    const firstMatchingDefaults = this.mutationDefaults.find((x) =>\n      partialMatchKey(mutationKey, x.mutationKey),\n    )\n\n    // Additional checks and error in dev mode\n    if (process.env.NODE_ENV !== 'production') {\n      // Retrieve all matching defaults for the given key\n      const matchingDefaults = this.mutationDefaults.filter((x) =>\n        partialMatchKey(mutationKey, x.mutationKey),\n      )\n      // It is ok not having defaults, but it is error prone to have more than 1 default for a given key\n      if (matchingDefaults.length > 1) {\n        this.logger.error(\n          `[QueryClient] Several mutation defaults match with key '${JSON.stringify(\n            mutationKey,\n          )}'. The first matching mutation defaults are used. Please check how mutation defaults are registered. Order does matter here. cf. https://react-query.tanstack.com/reference/QueryClient#queryclientsetmutationdefaults.`,\n        )\n      }\n    }\n\n    return firstMatchingDefaults?.defaultOptions\n  }\n\n  defaultQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey extends QueryKey,\n  >(\n    options?:\n      | QueryObserverOptions<TQueryFnData, TError, TData, TQueryData, TQueryKey>\n      | DefaultedQueryObserverOptions<\n          TQueryFnData,\n          TError,\n          TData,\n          TQueryData,\n          TQueryKey\n        >,\n  ): DefaultedQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  > {\n    if (options?._defaulted) {\n      return options as DefaultedQueryObserverOptions<\n        TQueryFnData,\n        TError,\n        TData,\n        TQueryData,\n        TQueryKey\n      >\n    }\n\n    const defaultedOptions = {\n      ...this.defaultOptions.queries,\n      ...this.getQueryDefaults(options?.queryKey),\n      ...options,\n      _defaulted: true,\n    }\n\n    if (!defaultedOptions.queryHash && defaultedOptions.queryKey) {\n      defaultedOptions.queryHash = hashQueryKeyByOptions(\n        defaultedOptions.queryKey,\n        defaultedOptions,\n      )\n    }\n\n    // dependent default values\n    if (typeof defaultedOptions.refetchOnReconnect === 'undefined') {\n      defaultedOptions.refetchOnReconnect =\n        defaultedOptions.networkMode !== 'always'\n    }\n    if (typeof defaultedOptions.useErrorBoundary === 'undefined') {\n      defaultedOptions.useErrorBoundary = !!defaultedOptions.suspense\n    }\n\n    return defaultedOptions as DefaultedQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >\n  }\n\n  defaultMutationOptions<T extends MutationOptions<any, any, any, any>>(\n    options?: T,\n  ): T {\n    if (options?._defaulted) {\n      return options\n    }\n    return {\n      ...this.defaultOptions.mutations,\n      ...this.getMutationDefaults(options?.mutationKey),\n      ...options,\n      _defaulted: true,\n    } as T\n  }\n\n  clear(): void {\n    this.queryCache.clear()\n    this.mutationCache.clear()\n  }\n}\n", "import { isServer, isValidTimeout } from './utils'\n\nexport abstract class Removable {\n  cacheTime!: number\n  private gcTimeout?: ReturnType<typeof setTimeout>\n\n  destroy(): void {\n    this.clearGcTimeout()\n  }\n\n  protected scheduleGc(): void {\n    this.clearGcTimeout()\n\n    if (isValidTimeout(this.cacheTime)) {\n      this.gcTimeout = setTimeout(() => {\n        this.optionalRemove()\n      }, this.cacheTime)\n    }\n  }\n\n  protected updateCacheTime(newCacheTime: number | undefined): void {\n    // Default to 5 minutes (Infinity for server-side) if no cache time is set\n    this.cacheTime = Math.max(\n      this.cacheTime || 0,\n      newCacheTime ?? (isServer ? Infinity : 5 * 60 * 1000),\n    )\n  }\n\n  protected clearGcTimeout() {\n    if (this.gcTimeout) {\n      clearTimeout(this.gcTimeout)\n      this.gcTimeout = undefined\n    }\n  }\n\n  protected abstract optionalRemove(): void\n}\n"], "names": ["Mutation", "Removable", "constructor", "config", "super", "this", "defaultOptions", "mutationId", "mutationCache", "logger", "defaultLogger", "observers", "state", "getDefaultState", "setOptions", "options", "scheduleGc", "updateCacheTime", "cacheTime", "meta", "setState", "dispatch", "type", "addObserver", "observer", "includes", "push", "clearGcTimeout", "notify", "mutation", "removeObserver", "filter", "x", "optionalRemove", "length", "status", "remove", "continue", "_this$retryer$continu", "_this$retryer", "retryer", "execute", "executeMutation", "_this$options$retry", "createRetryer", "fn", "mutationFn", "variables", "Promise", "reject", "onFail", "failureCount", "error", "onPause", "onContinue", "retry", "retry<PERSON><PERSON><PERSON>", "networkMode", "promise", "restored", "_this$mutationCache$c3", "_this$mutationCache$c4", "_this$options$onSucce", "_this$options2", "_this$mutationCache$c5", "_this$mutationCache$c6", "_this$options$onSettl", "_this$options3", "_this$mutationCache$c", "_this$mutationCache$c2", "_this$options$onMutat", "_this$options", "onMutate", "call", "context", "data", "onSuccess", "onSettled", "_this$mutationCache$c7", "_this$mutationCache$c8", "_this$options$onError", "_this$options4", "_this$mutationCache$c9", "_this$mutationCache$c10", "_this$options$onSettl2", "_this$options5", "onError", "undefined", "action", "failureReason", "isPaused", "canFetch", "reducer", "notify<PERSON><PERSON>ger", "batch", "for<PERSON>ach", "onMutationUpdate", "console", "MutationObserver", "Subscribable", "client", "bindMethods", "updateResult", "mutate", "bind", "reset", "_this$currentMutation", "prevOptions", "defaultMutationOptions", "shallowEqualObjects", "getMutationCache", "currentMutation", "onUnsubscribe", "_this$currentMutation2", "hasListeners", "notifyOptions", "listeners", "getCurrentResult", "currentResult", "mutateOptions", "build", "isLoading", "result", "isPending", "isSuccess", "isError", "isIdle", "_this$mutateOptions$o", "_this$mutateOptions", "_this$mutateOptions$o2", "_this$mutateOptions2", "_this$mutateOptions$o3", "_this$mutateOptions3", "_this$mutateOptions$o4", "_this$mutateOptions4", "_ref", "listener", "useMutation", "arg1", "arg2", "arg3", "React", "queryClient", "useSyncExternalStore", "onStoreChange", "subscribe", "batchCalls", "shouldThrowError", "useErrorBoundary", "noop", "RefreshCw", "createLucideIcon", "d", "key", "Query", "abortSignalConsumed", "cache", "query<PERSON><PERSON>", "queryHash", "initialState", "initialData", "hasData", "initialDataUpdatedAt", "dataUpdateCount", "dataUpdatedAt", "Date", "now", "errorUpdateCount", "errorUpdatedAt", "fetchFailureCount", "fetchFailureReason", "fetchMeta", "isInvalidated", "fetchStatus", "setData", "newData", "replaceData", "updatedAt", "manual", "setStateOptions", "cancel", "then", "catch", "resolve", "destroy", "silent", "isActive", "some", "enabled", "isDisabled", "getObserversCount", "isStale", "isStaleByTime", "staleTime", "arguments", "timeUntilStale", "onFocus", "_this$retryer2", "find", "shouldFetchOnWindowFocus", "refetch", "cancelRefetch", "onOnline", "_this$retryer3", "shouldFetchOnReconnect", "query", "revert", "cancelRetry", "invalidate", "fetch", "fetchOptions", "_this$options$behavio", "_context$fetchOptions", "_this$retryer4", "continueRetry", "queryFn", "abortController", "getAbortController", "queryFnContext", "pageParam", "addSignalProperty", "object", "Object", "defineProperty", "enumerable", "get", "signal", "fetchFn", "_context$fetchOptions2", "behavior", "onFetch", "revertState", "_this$cache$config$on", "_this$cache$config", "_this$cache$config$on2", "_this$cache$config2", "isCancelledError", "isFetchingOptimistic", "abort", "_this$cache$config$on3", "_this$cache$config3", "_this$cache$config$on4", "_this$cache$config4", "Error", "_action$meta", "_action$dataUpdatedAt", "onQueryUpdate", "Query<PERSON>ache", "queries", "queriesMap", "_options$queryHash", "hashQueryKeyByOptions", "<PERSON><PERSON><PERSON><PERSON>", "defaultQueryOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "add", "queryInMap", "clear", "getAll", "filters", "parseFilter<PERSON><PERSON>s", "exact", "matchQuery", "findAll", "keys", "event", "MutationCache", "mutations", "<PERSON><PERSON><PERSON>", "getMutationDefaults", "matchMutation", "resumePausedMutations", "_this$resuming", "resuming", "pausedMutations", "reduce", "infiniteQueryBehavior", "_context$fetchOptions3", "_context$fetchOptions4", "_context$state$data", "_context$state$data2", "refetchPage", "fetchMore", "isFetchingNextPage", "direction", "isFetchingPreviousPage", "oldPages", "pages", "oldPageParams", "pageParams", "newPageParams", "cancelled", "buildNewPages", "param", "page", "previous", "fetchPage", "_context$signal", "_context$signal2", "aborted", "addEventListener", "queryFnResult", "getNextPageParam", "getPreviousPageParam", "i", "QueryClient", "queryCache", "queryDefaults", "mutationDefaults", "mountCount", "mount", "unsubscribeFocus", "focusManager", "isFocused", "unsubscribeOnline", "onlineManager", "isOnline", "unmount", "_this$unsubscribeFocu", "_this$unsubscribeOnli", "isFetching", "isMutating", "fetching", "getQueryData", "_this$queryCache$find", "ensureQueryData", "parsedOptions", "parse<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cachedData", "<PERSON><PERSON><PERSON><PERSON>", "getQueriesData", "query<PERSON>eyOrFilters", "get<PERSON><PERSON><PERSON><PERSON>ache", "map", "_ref2", "setQueryData", "updater", "prevData", "functionalUpdate", "defaultedOptions", "setQueriesData", "_ref3", "getQueryState", "_this$queryCache$find2", "removeQueries", "resetQueries", "refetchFilters", "refetchQueries", "cancelQueries", "cancelOptions", "promises", "all", "invalidateQueries", "_filters$refetchType", "refetchType", "_options$cancelRefetc", "throwOnError", "prefetch<PERSON><PERSON>y", "fetchInfiniteQuery", "prefetchInfiniteQuery", "getDefaultOptions", "setDefaultOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hashQuery<PERSON>ey", "firstMatchingDefaults", "partialMatchKey", "setMutationDefaults", "_defaulted", "refetchOnReconnect", "suspense", "isValidTimeout", "gcTimeout", "setTimeout", "newCacheTime", "Math", "max", "isServer", "Infinity", "clearTimeout"], "sourceRoot": ""}