const ApiError = require('../utils/ApiError');
const logger = require('../utils/logger');

/**
 * Error handler middleware for Express
 * @param {Error} err - Error object
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const errorHandler = (err, req, res, next) => {
  let error = { ...err };
  error.message = err.message;
  error.stack = process.env.NODE_ENV === 'development' ? err.stack : undefined;
  
  // Log the error
  logger.error({
    message: err.message,
    statusCode: err.statusCode || 500,
    stack: err.stack,
    path: req.path,
    method: req.method,
    ip: req.ip,
    user: req.user ? req.user.id : 'anonymous',
  });

  // Handle specific error types
  switch (err.name) {
    // Mongoose errors
    case 'CastError':
      error = new ApiError(400, `Invalid ${err.path}: ${err.value}`);
      break;
    
    case 'ValidationError':
      const messages = Object.values(err.errors).map(val => val.message);
      error = new ApiError(400, `Validation Error: ${messages.join('. ')}`);
      break;
    
    case 'MongoError':
      // Handle duplicate key error (code 11000)
      if (err.code === 11000) {
        const field = Object.keys(err.keyValue)[0];
        error = new ApiError(400, `Duplicate field value: ${field}. Please use another value.`);
      } else {
        error = new ApiError(500, 'Database error occurred');
      }
      break;
    
    // JWT errors
    case 'JsonWebTokenError':
      error = new ApiError(401, 'Invalid token. Please log in again.');
      break;
    
    case 'TokenExpiredError':
      error = new ApiError(401, 'Your token has expired. Please log in again.');
      break;
    
    // Rate limiter errors
    case 'RateLimitError':
      error = new ApiError(429, 'Too many requests, please try again later.');
      break;
    
    // Default to 500 if not a known error
    default:
      // If it's our custom ApiError, use its status code and message
      if (err instanceof ApiError) {
        error = err;
      } else {
        // For unhandled errors, log the full error in development
        if (process.env.NODE_ENV === 'development') {
          console.error('Unhandled error:', err);
        }
        error = new ApiError(500, 'Internal Server Error');
      }
  }

  // Send error response
  res.status(error.statusCode).json({
    success: false,
    message: error.message,
    ...(process.env.NODE_ENV === 'development' && { stack: error.stack }),
    ...(error.errors && { errors: error.errors }),
  });
};

/**
 * 404 Not Found handler
 */
const notFound = (req, res, next) => {
  next(new ApiError(404, `Not Found - ${req.originalUrl}`));
};

/**
 * Async handler to wrap async/await route handlers
 * @param {Function} fn - Async route handler function
 * @returns {Function} Wrapped route handler with error handling
 */
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

module.exports = {
  errorHandler,
  notFound,
  asyncHandler,
};
