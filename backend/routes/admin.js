// Admin Routes
const express = require('express');
const router = express.Router();
const MerchantProfile = require('../models/MerchantProfile');
const TraderAssignment = require('../models/TraderAssignment');
const User = require('../models/User');
const Transaction = require('../models/Transaction');
const merchantController = require('../controllers/merchantController');
const traderController = require('../controllers/traderController');
// Authentication middleware removed
const logger = require('../utils/logger');

// Audit log middleware
const auditLog = (req, res, next) => {
  logger.info(`Admin action: ${req.method} ${req.originalUrl}`, {
    user: req.user?.id || 'anonymous',
    ip: req.ip,
    userAgent: req.get('user-agent'),
    timestamp: new Date().toISOString()
  });
  next();
};

// Apply audit logging to all admin routes
router.use(auditLog);

// Get dashboard analytics
router.get('/analytics', async (req, res) => {
  try {
    const [
      totalMerchants,
      activeMerchants,
      pendingMerchants,
      activeTraders,
      totalTransactions,
      monthlyVolume,
      successfulTransactions
    ] = await Promise.all([
      MerchantProfile.countDocuments(),
      MerchantProfile.countDocuments({ verificationStatus: 'verified' }),
      MerchantProfile.countDocuments({ verificationStatus: 'pending' }),
      User.countDocuments({ role: 'trader', status: 'active' }),
      Transaction.countDocuments(),
      Transaction.aggregate([
        {
          $match: {
            createdAt: { $gte: new Date(new Date().setMonth(new Date().getMonth() - 1)) }
          }
        },
        { $group: { _id: null, total: { $sum: '$amount' } } }
      ]),
      Transaction.countDocuments({ status: 'completed' })
    ]);

    const successRate = totalTransactions > 0 ? 
      ((successfulTransactions / totalTransactions) * 100).toFixed(2) : 0;

    // Get volume trend for last 6 months
    const volumeTrend = await Transaction.aggregate([
      {
        $match: {
          createdAt: { $gte: new Date(new Date().setMonth(new Date().getMonth() - 6)) }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' }
          },
          volume: { $sum: '$amount' },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1 } }
    ]);

    // Get payment methods distribution
    const paymentMethods = await Transaction.aggregate([
      {
        $group: {
          _id: '$paymentMethod',
          value: { $sum: 1 },
          name: { $first: '$paymentMethod' }
        }
      }
    ]);

    res.json({
      totalMerchants,
      activeMerchants,
      pendingMerchants,
      activeTraders,
      totalVolume: monthlyVolume[0]?.total || 0,
      successRate: parseFloat(successRate),
      failedTransactions: totalTransactions - successfulTransactions,
      volumeTrend: volumeTrend.map(item => ({
        month: `${item._id.year}-${item._id.month.toString().padStart(2, '0')}`,
        volume: item.volume
      })),
      paymentMethods
    });
  } catch (error) {
    console.error('Error fetching analytics:', error);
    res.status(500).json({ error: 'Failed to fetch analytics' });
  }
});

// Get all merchants for admin review
router.get('/merchants', async (req, res) => {
  try {
    const { status, page = 1, limit = 10 } = req.query;
    const filter = status ? { verificationStatus: status } : {};
    
    const merchants = await MerchantProfile.find(filter)
      .populate('userId', 'email name')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await MerchantProfile.countDocuments(filter);

    res.json({
      merchants,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (error) {
    console.error('Error fetching merchants:', error);
    res.status(500).json({ error: 'Failed to fetch merchants' });
  }
});

// Approve merchant
router.post('/merchants/:id/approve', async (req, res) => {
  try {
    const merchant = await MerchantProfile.findByIdAndUpdate(
      req.params.id,
      { 
        verificationStatus: 'verified',
        verifiedAt: new Date(),
        verifiedBy: req.user.id
      },
      { new: true }
    );

    if (!merchant) {
      return res.status(404).json({ error: 'Merchant not found' });
    }

    // Send approval notification
    // await sendApprovalNotification(merchant);

    res.json({ message: 'Merchant approved successfully', merchant });
  } catch (error) {
    console.error('Error approving merchant:', error);
    res.status(500).json({ error: 'Failed to approve merchant' });
  }
});

// Reject merchant
router.post('/merchants/:id/reject', async (req, res) => {
  try {
    const { reason } = req.body;
    
    const merchant = await MerchantProfile.findByIdAndUpdate(
      req.params.id,
      { 
        verificationStatus: 'rejected',
        rejectionReason: reason,
        rejectedAt: new Date(),
        rejectedBy: req.user.id
      },
      { new: true }
    );

    if (!merchant) {
      return res.status(404).json({ error: 'Merchant not found' });
    }

    res.json({ message: 'Merchant rejected', merchant });
  } catch (error) {
    console.error('Error rejecting merchant:', error);
    res.status(500).json({ error: 'Failed to reject merchant' });
  }
});

// Get all traders
router.get('/traders', async (req, res) => {
  try {
    const { status, page = 1, limit = 10 } = req.query;
    const filter = { role: 'trader' };
    if (status) filter.status = status;

    const traders = await User.find(filter)
      .select('-password')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    // Get assignment counts for each trader
    const tradersWithAssignments = await Promise.all(
      traders.map(async (trader) => {
        const assignmentCount = await TraderAssignment.countDocuments({
          traderId: trader._id,
          status: 'active'
        });
        return {
          ...trader.toObject(),
          assignedMerchants: assignmentCount
        };
      })
    );

    const total = await User.countDocuments(filter);

    res.json({
      traders: tradersWithAssignments,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (error) {
    console.error('Error fetching traders:', error);
    res.status(500).json({ error: 'Failed to fetch traders' });
  }
});

// Approve trader
router.post('/traders/:id/approve', async (req, res) => {
  try {
    const trader = await User.findByIdAndUpdate(
      req.params.id,
      { 
        status: 'active',
        approvedAt: new Date(),
        approvedBy: req.user.id
      },
      { new: true }
    ).select('-password');

    if (!trader) {
      return res.status(404).json({ error: 'Trader not found' });
    }

    res.json({ message: 'Trader approved successfully', trader });
  } catch (error) {
    console.error('Error approving trader:', error);
    res.status(500).json({ error: 'Failed to approve trader' });
  }
});

// Get recent transactions
router.get('/transactions', async (req, res) => {
  try {
    const { page = 1, limit = 20, status, merchantId } = req.query;
    const filter = {};
    if (status) filter.status = status;
    if (merchantId) filter.merchantId = merchantId;

    const transactions = await Transaction.find(filter)
      .populate('merchantId', 'businessName')
      .populate('traderId', 'name')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Transaction.countDocuments(filter);

    res.json({
      transactions,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (error) {
    console.error('Error fetching transactions:', error);
    res.status(500).json({ error: 'Failed to fetch transactions' });
  }
});

// Merchant CRUD
router.post('/merchants', merchantController.createMerchantProfile);
router.put('/merchants/:id', merchantController.updateMerchantProfile);
router.delete('/merchants/:id', merchantController.deleteMerchant);
router.get('/merchants/:id', merchantController.getMerchantProfile);

// Custom fields management
router.post('/merchants/:id/custom-fields', merchantController.addCustomField);
router.put('/merchants/:id/custom-fields', merchantController.updateCustomField);
router.delete('/merchants/:id/custom-fields', merchantController.deleteCustomField);

// Change history
router.get('/merchants/:id/change-history', merchantController.getMerchantChangeHistory);

// Trader CRUD
router.post('/traders', traderController.createTrader);
router.put('/traders/:id', traderController.updateTrader);
router.delete('/traders/:id', traderController.deleteTrader);
router.get('/traders/:id', traderController.getTraderProfile);

// Assign trader to merchant
router.post('/assign-trader', async (req, res) => {
  try {
    const { traderId, merchantId, startDate, endDate, permissions } = req.body;

    // Check if assignment already exists
    const existingAssignment = await TraderAssignment.findOne({
      traderId,
      merchantId,
      status: 'active'
    });

    if (existingAssignment) {
      return res.status(400).json({ error: 'Trader already assigned to this merchant' });
    }

    const assignment = new TraderAssignment({
      traderId,
      merchantId,
      assignedBy: req.user.id,
      startDate: new Date(startDate),
      endDate: endDate ? new Date(endDate) : null,
      permissions: permissions || ['view_transactions', 'collect_payments'],
      status: 'active'
    });

    await assignment.save();

    res.status(201).json({ message: 'Trader assigned successfully', assignment });
  } catch (error) {
    console.error('Error assigning trader:', error);
    res.status(500).json({ error: 'Failed to assign trader' });
  }
});

module.exports = router;