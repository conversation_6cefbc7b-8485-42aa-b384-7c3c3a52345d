// API configuration
export const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

// Default pagination settings
export const DEFAULT_PAGE_SIZE = 10;
export const DEFAULT_PAGE = 1;

// Date formats
export const DATE_FORMAT = 'yyyy-MM-dd';
export const DATE_TIME_FORMAT = 'yyyy-MM-dd HH:mm:ss';

// Roles
export const ROLES = {
  ADMIN: 'admin',
  TRADER: 'trader',  
  MERCHANT: 'merchant'
};

// Assignment types
export const ASSIGNMENT_TYPES = [
  { value: 'permanent', label: 'Permanent' },
  { value: 'temporary', label: 'Temporary' },
  { value: 'project_based', label: 'Project Based' }
];

// Collection periods
export const COLLECTION_PERIODS = [
  { value: 'daily', label: 'Daily' },
  { value: 'weekly', label: 'Weekly' },
  { value: 'monthly', label: 'Monthly' },
  { value: 'quarterly', label: 'Quarterly' }
];

// Permissions
export const PERMISSIONS = [
  { id: 'view_transactions', label: 'View Transactions' },
  { id: 'process_payments', label: 'Process Payments' },
  { id: 'generate_reports', label: 'Generate Reports' },
  { id: 'contact_customers', label: 'Contact Customers' },
  { id: 'manage_inventory', label: 'Manage Inventory' },
  { id: 'process_refunds', label: 'Process Refunds' }
];

// Status options
export const STATUS_OPTIONS = [
  { value: 'active', label: 'Active' },
  { value: 'pending', label: 'Pending' },
  { value: 'completed', label: 'Completed' },
  { value: 'inactive', label: 'Inactive' },
  { value: 'cancelled', label: 'Cancelled' }
];

// Default pagination settings for tables
export const TABLE_CONFIG = {
  pageSize: 10,
  pageSizeOptions: [5, 10, 20, 50, 100]
};

// Toast default duration (in ms)
export const TOAST_DURATION = 5000;
