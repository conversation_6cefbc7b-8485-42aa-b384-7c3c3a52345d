import React from 'react';
import { 
  Box, 
  TextField, 
  FormControl, 
  InputLabel, 
  Select, 
  MenuItem, 
  IconButton, 
  FormControlLabel, 
  Switch, 
  Typography,
  Grid,
  Collapse,
  Paper,
  Divider,
  Card,
  CardContent,
  CardHeader
} from '@mui/material';
import { 
  Delete as DeleteIcon, 
  ExpandMore as ExpandMoreIcon, 
  ExpandLess as ExpandLessIcon,
  Add as AddIcon
} from '@mui/icons-material';

const ReserveRuleForm = ({ 
  rule, 
  index, 
  expanded, 
  onToggleExpand, 
  onRemove, 
  onChange, 
  onAddTier, 
  onRemoveTier,
  errors = {}
}) => {
  const handleChange = (field, value) => {
    const updatedRule = { ...rule, [field]: value };
    onChange(index, updatedRule);
  };

  const handleTierChange = (tierIndex, field, value) => {
    const updatedTiers = [...rule.tiers];
    updatedTiers[tierIndex] = { ...updatedTiers[tierIndex], [field]: value };
    
    // If updating minAmount, update previous tier's maxAmount
    if (field === 'minAmount' && tierIndex > 0) {
      updatedTiers[tierIndex - 1] = { 
        ...updatedTiers[tierIndex - 1], 
        maxAmount: value 
      };
    }
    
    // If updating maxAmount, update next tier's minAmount
    if (field === 'maxAmount' && tierIndex < rule.tiers.length - 1) {
      updatedTiers[tierIndex + 1] = { 
        ...updatedTiers[tierIndex + 1], 
        minAmount: value || 0 
      };
    }
    
    onChange(index, { ...rule, tiers: updatedTiers });
  };

  const handleRuleTypeChange = (type) => {
    let updatedRule = { ...rule, type };
    
    // Reset values that don't apply to the new type
    if (type === 'percentage') {
      updatedRule = { ...updatedRule, percentage: 10, fixedAmount: undefined, tiers: [] };
    } else if (type === 'fixed_amount') {
      updatedRule = { ...updatedRule, fixedAmount: 0, percentage: undefined, tiers: [] };
    } else if (type === 'tiered') {
      updatedRule = { 
        ...updatedRule, 
        percentage: undefined, 
        fixedAmount: undefined,
        tiers: [{
          minAmount: 0,
          maxAmount: null,
          percentage: 10,
          fixedAmount: 0
        }]
      };
    }
    
    onChange(index, updatedRule);
  };

  const renderTieredRuleForm = () => (
    <Box sx={{ mt: 2, pl: 2, borderLeft: '2px solid', borderColor: 'divider' }}>
      <Typography variant="subtitle2" gutterBottom>
        Tiers
      </Typography>
      
      {rule.tiers.map((tier, tierIndex) => (
        <Card key={tierIndex} variant="outlined" sx={{ mb: 2 }}>
          <CardHeader
            title={`Tier ${tierIndex + 1}`}
            action={
              <IconButton 
                size="small" 
                onClick={() => onRemoveTier(index, tierIndex)}
                disabled={rule.tiers.length <= 1}
              >
                <DeleteIcon fontSize="small" />
              </IconButton>
            }
            sx={{ 
              bgcolor: 'action.hover',
              py: 1,
              '& .MuiCardHeader-content': {
                flex: '1 1 auto'
              },
              '& .MuiCardHeader-action': {
                m: 0,
                alignSelf: 'center'
              }
            }}
          />
          <CardContent>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="Min Amount"
                  type="number"
                  value={tier.minAmount}
                  onChange={(e) => handleTierChange(tierIndex, 'minAmount', parseFloat(e.target.value) || 0)}
                  InputProps={{
                    startAdornment: <span style={{ marginRight: 8 }}>$</span>,
                  }}
                  error={Boolean(errors.tiers?.[tierIndex]?.minAmount)}
                  helperText={errors.tiers?.[tierIndex]?.minAmount}
                />
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="Max Amount"
                  type="number"
                  value={tier.maxAmount || ''}
                  onChange={(e) => 
                    handleTierChange(
                      tierIndex, 
                      'maxAmount', 
                      e.target.value === '' ? null : parseFloat(e.target.value)
                    )
                  }
                  InputProps={{
                    startAdornment: <span style={{ marginRight: 8 }}>$</span>,
                  }}
                  placeholder="No max"
                  error={Boolean(errors.tiers?.[tierIndex]?.maxAmount)}
                  helperText={errors.tiers?.[tierIndex]?.maxAmount}
                />
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="Percentage"
                  type="number"
                  value={tier.percentage || ''}
                  onChange={(e) => 
                    handleTierChange(
                      tierIndex, 
                      'percentage', 
                      e.target.value === '' ? undefined : parseFloat(e.target.value)
                    )
                  }
                  InputProps={{
                    endAdornment: <span>%</span>,
                  }}
                  error={Boolean(errors.tiers?.[tierIndex]?.percentage)}
                  helperText={errors.tiers?.[tierIndex]?.percentage}
                />
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="Fixed Amount"
                  type="number"
                  value={tier.fixedAmount || ''}
                  onChange={(e) => 
                    handleTierChange(
                      tierIndex, 
                      'fixedAmount', 
                      e.target.value === '' ? undefined : parseFloat(e.target.value)
                    )
                  }
                  InputProps={{
                    startAdornment: <span style={{ marginRight: 8 }}>$</span>,
                  }}
                  error={Boolean(errors.tiers?.[tierIndex]?.fixedAmount)}
                  helperText={errors.tiers?.[tierIndex]?.fixedAmount}
                />
              </Grid>
            </Grid>
            
            {tierIndex === rule.tiers.length - 1 && (
              <Box mt={2}>
                <Button
                  size="small"
                  startIcon={<AddIcon />}
                  onClick={() => onAddTier(index)}
                >
                  Add Tier
                </Button>
              </Box>
            )}
          </CardContent>
        </Card>
      ))}
      
      {errors.tiers && typeof errors.tiers === 'string' && (
        <FormHelperText error>{errors.tiers}</FormHelperText>
      )}
    </Box>
  );

  return (
    <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Typography variant="subtitle1">
          Rule {index + 1}
        </Typography>
        <Box>
          <IconButton size="small" onClick={() => onToggleExpand(index)}>
            {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </IconButton>
          <IconButton 
            size="small" 
            color="error"
            onClick={() => onRemove(index)}
            disabled={onRemove === undefined}
          >
            <DeleteIcon fontSize="small" />
          </IconButton>
        </Box>
      </Box>
      
      <Collapse in={expanded}>
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth margin="normal">
              <InputLabel>Rule Type</InputLabel>
              <Select
                value={rule.type}
                onChange={(e) => handleRuleTypeChange(e.target.value)}
                label="Rule Type"
                error={Boolean(errors.type)}
              >
                <MenuItem value="percentage">Percentage</MenuItem>
                <MenuItem value="fixed_amount">Fixed Amount</MenuItem>
                <MenuItem value="tiered">Tiered</MenuItem>
              </Select>
              {errors.type && <FormHelperText error>{errors.type}</FormHelperText>}
            </FormControl>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              margin="normal"
              label="Description"
              value={rule.description || ''}
              onChange={(e) => handleChange('description', e.target.value)}
              error={Boolean(errors.description)}
              helperText={errors.description}
            />
          </Grid>
          
          {rule.type === 'percentage' && (
            <Grid item xs={12} sm={6} md={4}>
              <TextField
                fullWidth
                margin="normal"
                label="Percentage"
                type="number"
                value={rule.percentage || ''}
                onChange={(e) => handleChange('percentage', parseFloat(e.target.value) || 0)}
                InputProps={{
                  endAdornment: <span>%</span>,
                }}
                error={Boolean(errors.percentage)}
                helperText={errors.percentage}
              />
            </Grid>
          )}
          
          {rule.type === 'fixed_amount' && (
            <Grid item xs={12} sm={6} md={4}>
              <TextField
                fullWidth
                margin="normal"
                label="Fixed Amount"
                type="number"
                value={rule.fixedAmount || ''}
                onChange={(e) => handleChange('fixedAmount', parseFloat(e.target.value) || 0)}
                InputProps={{
                  startAdornment: <span style={{ marginRight: 8 }}>$</span>,
                }}
                error={Boolean(errors.fixedAmount)}
                helperText={errors.fixedAmount}
              />
            </Grid>
          )}
          
          {rule.type === 'tiered' && renderTieredRuleForm()}
          
          <Grid item xs={12} sm={6} md={4}>
            <TextField
              fullWidth
              margin="normal"
              label="Minimum Reserve"
              type="number"
              value={rule.minReserve || ''}
              onChange={(e) => 
                handleChange('minReserve', e.target.value === '' ? null : parseFloat(e.target.value))
              }
              InputProps={{
                startAdornment: <span style={{ marginRight: 8 }}>$</span>,
              }}
              placeholder="No minimum"
              error={Boolean(errors.minReserve)}
              helperText={errors.minReserve}
            />
          </Grid>
          
          <Grid item xs={12} sm={6} md={4}>
            <TextField
              fullWidth
              margin="normal"
              label="Maximum Reserve"
              type="number"
              value={rule.maxReserve || ''}
              onChange={(e) => 
                handleChange('maxReserve', e.target.value === '' ? null : parseFloat(e.target.value))
              }
              InputProps={{
                startAdornment: <span style={{ marginRight: 8 }}>$</span>,
              }}
              placeholder="No maximum"
              error={Boolean(errors.maxReserve)}
              helperText={errors.maxReserve}
            />
          </Grid>
          
          <Grid item xs={12} sm={6} md={4}>
            <TextField
              fullWidth
              margin="normal"
              label="Priority"
              type="number"
              value={rule.priority || 0}
              onChange={(e) => handleChange('priority', parseInt(e.target.value, 10) || 0)}
              error={Boolean(errors.priority)}
              helperText={errors.priority || "Lower numbers have higher priority"}
            />
          </Grid>
          
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Switch
                  checked={rule.isActive !== false}
                  onChange={(e) => handleChange('isActive', e.target.checked)}
                  color="primary"
                />
              }
              label="Rule is active"
            />
          </Grid>
        </Grid>
      </Collapse>
    </Paper>
  );
};

export default ReserveRuleForm;
