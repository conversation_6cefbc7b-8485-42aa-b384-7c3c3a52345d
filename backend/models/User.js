const mongoose = require("mongoose");

const userSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    email: {
      type: String,
      required: true,
      unique: true,
      lowercase: true,
      trim: true,
      match: [/^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/, 'Please enter a valid email'],
    },
    role: {
      type: String,
      enum: ["admin", "merchant", "trader"],
      default: "merchant",
    },

    businessName: String,
    businessType: String,
    businessAddress: {
      street: String,
      city: String,
      state: String,
      zipCode: String,
      country: String,
    },
    phone: String,
    website: String,
    webhookUrl: String,
    isActive: {
      type: Boolean,
      default: true,
    },

  },
  {
    timestamps: true,
  },
)

// Index for performance
userSchema.index({ email: 1 })

const User = mongoose.model("User", userSchema)

module.exports = User


