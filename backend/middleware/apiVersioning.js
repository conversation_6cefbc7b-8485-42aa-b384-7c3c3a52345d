const semver = require('semver');
const { ApiError } = require('../utils/ApiError');

/**
 * Middleware to handle API versioning
 * Extracts the API version from the Accept header or query parameter
 * and attaches it to the request object
 */
const apiVersioning = (req, res, next) => {
  // Default to v1 if no version is specified
  let version = '1.0.0';
  
  // Check Accept header for version (e.g., application/vnd.payment-gateway.v1+json)
  const acceptHeader = req.headers['accept'] || '';
  const versionMatch = acceptHeader.match(/vnd\.payment-gateway\.v(\d+)/i);
  
  if (versionMatch && versionMatch[1]) {
    version = `${versionMatch[1]}.0.0`;
  } else {
    // Check for version in query parameter (e.g., ?v=1.0)
    const queryVersion = req.query.v || req.query.apiVersion;
    if (queryVersion) {
      version = semver.valid(semver.coerce(queryVersion)) || version;
    }
  }
  
  // Attach version to request object
  req.apiVersion = version;
  
  // Set response headers
  res.set('X-API-Version', version);
  res.set('X-API-Deprecated', isDeprecated(version) ? 'true' : 'false');
  
  // If the API version is deprecated, add a warning header
  if (isDeprecated(version)) {
    res.set('Warning', `299 - "Deprecated API: Version ${version} is deprecated and will be removed soon. Please upgrade to the latest version.`);
  }
  
  next();
};

/**
 * Check if an API version is deprecated
 * @param {string} version - The version to check
 * @returns {boolean} True if the version is deprecated
 */
function isDeprecated(version) {
  const currentVersion = '1.0.0';
  const deprecatedVersions = []; // Add deprecated versions here when needed
  
  return deprecatedVersions.some(deprecatedVersion => 
    semver.satisfies(version, deprecatedVersion)
  );
}

/**
 * Middleware to ensure the requested API version is supported
 */
const checkApiVersion = (req, res, next) => {
  const supportedVersions = ['1.0.0']; // Add new versions when they're released
  const requestedVersion = req.apiVersion;
  
  // Check if the requested version is supported
  const isSupported = supportedVersions.some(supportedVersion => 
    semver.satisfies(requestedVersion, `^${supportedVersion}`)
  );
  
  if (!isSupported) {
    return next(new ApiError(400, `Unsupported API version: ${requestedVersion}. Supported versions: ${supportedVersions.join(', ')}`));
  }
  
  next();
};

/**
 * Middleware to handle version-specific routes
 * @param {Object} versions - Object mapping version ranges to route handlers
 * @returns {Function} Middleware function
 */
const versionedRoute = (versions) => {
  return (req, res, next) => {
    const version = req.apiVersion;
    
    // Find the latest matching version
    let handler = null;
    for (const [versionRange, routeHandler] of Object.entries(versions)) {
      if (semver.satisfies(version, versionRange)) {
        handler = routeHandler;
        // Don't break to find the latest version that matches
      }
    }
    
    if (!handler) {
      return next(new ApiError(400, `No route handler found for API version ${version}`));
    }
    
    // Execute the route handler
    return handler(req, res, next);
  };
};

module.exports = {
  apiVersioning,
  checkApiVersion,
  versionedRoute,
  isDeprecated
};
