import { useState } from 'react';
import { useQuery, UseQueryResult } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../../components/ui/dropdown-menu';
import { Badge } from '../../components/ui/badge';
import { MoreHorizontal, Search, Loader2 } from 'lucide-react';
import { merchantAdminService } from '../../services/merchantService';
import { MerchantListResponse, MerchantListItem, MerchantStatusUpdate } from '../../types/merchant';
import { toast } from '../../components/ui/use-toast';
import { format } from 'date-fns';

const statusVariant: Record<string, 'default' | 'secondary' | 'destructive' | 'outline'> = {
  verified: 'default',
  pending: 'secondary',
  rejected: 'destructive',
  suspended: 'outline',
  draft: 'outline',
};

export function MerchantsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [pageInput, setPageInput] = useState('');

  const { 
    data: merchantsData, 
    isLoading, 
    error, 
    refetch 
  } = useQuery<MerchantListResponse>({
    queryKey: ['merchants', { page, limit, search: searchTerm }],
    queryFn: () => merchantAdminService.getAllMerchants({ page, limit, search: searchTerm })
  });

  const handleStatusUpdate = async (id: string, status: MerchantStatusUpdate) => {
    try {
      await merchantAdminService.updateMerchantStatus(id, status.status, status.reason);
      toast({
        title: 'Status updated',
        description: 'Merchant status has been updated successfully.'
      });
      refetch();
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update merchant status. Please try again.',
        variant: 'destructive',
      });
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="w-8 h-8 animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-10">
        <h3 className="text-lg font-medium mb-2">Failed to load merchants</h3>
        <p className="text-muted-foreground mb-4">Please try again later.</p>
        <Button onClick={() => refetch()}>Retry</Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Merchants Management</h2>
      </div>
      
      <Card>
        <CardHeader className="pb-0">
          <div className="flex items-center justify-between">
            <CardTitle>All Merchants</CardTitle>
            <div className="relative w-64">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search merchants..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Business Name</TableHead>
                <TableHead>Contact Email</TableHead>
                <TableHead>Phone</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Created At</TableHead>
                <TableHead className="w-[100px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {merchantsData?.data.map((merchant: MerchantListItem) => (
                <TableRow key={merchant.id}>
                  <TableCell className="font-medium">{merchant.businessName}</TableCell>
                  <TableCell>{merchant.contactEmail}</TableCell>
                  <TableCell>{merchant.contactPhone}</TableCell>
                  <TableCell>
                    <Badge variant={statusVariant[merchant.status]}>
                      {merchant.status.charAt(0).toUpperCase() + merchant.status.slice(1)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {format(new Date(merchant.createdAt), 'MMM d, yyyy')}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() => handleStatusUpdate(merchant.id, { status: 'verified' })}
                          disabled={merchant.status === 'verified'}
                        >
                          Mark as Verified
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleStatusUpdate(merchant.id, { status: 'suspended' })}
                          disabled={merchant.status === 'suspended'}
                        >
                          Suspend Account
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleStatusUpdate(merchant.id, { status: 'rejected', reason: 'Business verification failed' })}
                          disabled={merchant.status === 'rejected'}
                          className="text-destructive"
                        >
                          Reject
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          
          {merchantsData?.data.length === 0 && (
            <div className="py-8 text-center text-muted-foreground">
              No merchants found
            </div>
          )}
          
          <div className="flex flex-col sm:flex-row items-center justify-between p-4 border-t gap-4">
            <div className="text-sm text-muted-foreground">
              Showing <span className="font-medium">{merchantsData?.data?.length || 0}</span> of{' '}
              <span className="font-medium">{merchantsData?.total || 0}</span> merchants
            </div>
            
            <div className="flex items-center space-x-2">
              <span className="text-sm text-muted-foreground">Rows per page:</span>
              <select
                value={limit}
                onChange={(e) => {
                  setLimit(Number(e.target.value));
                  setPage(1); // Reset to first page when changing page size
                }}
                className="h-8 w-16 rounded-md border border-input bg-background px-2 py-1 text-sm"
                disabled={isLoading}
              >
                {[5, 10, 20, 50].map((size) => (
                  <option key={size} value={size}>
                    {size}
                  </option>
                ))}
              </select>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage(1)}
                disabled={page === 1 || isLoading}
              >
                First
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage(p => Math.max(1, p - 1))}
                disabled={page === 1 || isLoading}
              >
                Previous
              </Button>
              
              <div className="flex items-center space-x-1">
                <span className="text-sm text-muted-foreground">Page</span>
                <input
                  type="number"
                  min={1}
                  max={merchantsData?.totalPages || 1}
                  value={pageInput || page}
                  onChange={(e) => setPageInput(e.target.value)}
                  onBlur={(e) => {
                    const newPage = Math.min(
                      Math.max(1, Number(e.target.value)),
                      merchantsData?.totalPages || 1
                    );
                    setPage(newPage);
                    setPageInput('');
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.currentTarget.blur();
                    }
                  }}
                  className="w-12 h-8 text-center border rounded-md"
                  disabled={isLoading}
                />
                <span className="text-sm text-muted-foreground">
                  of {merchantsData?.totalPages || 1}
                </span>
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage(p => Math.min(p + 1, merchantsData?.totalPages || 1))}
                disabled={page >= (merchantsData?.totalPages || 1) || isLoading}
              >
                Next
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage(merchantsData?.totalPages || 1)}
                disabled={page >= (merchantsData?.totalPages || 1) || isLoading}
              >
                Last
              </Button>
              
              {isLoading && (
                <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default MerchantsPage;
