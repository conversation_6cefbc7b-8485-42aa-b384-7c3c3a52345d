const dotenv = require('dotenv');
const path = require('path');

// Load environment variables from backend/.env first, then fall back to root .env
dotenv.config({ path: path.resolve(__dirname, '../.env') });
dotenv.config({ path: path.resolve(__dirname, '../../.env'), override: true });

// List of required environment variables
const requiredEnvVars = [
  'NODE_ENV',
  'PORT',
  'FRONTEND_URL',
  'API_URL',
  'COOKIE_SECRET',
  'ADMIN_EMAIL',
  'ADMIN_PASSWORD',
  'JWT_SECRET',
  'JWT_TEMP_SECRET',
  'JWT_EXPIRES_IN',
  'JWT_REFRESH_EXPIRES_IN',
  'MONGODB_URI',
  'MONGODB_TEST_URI',
  'EMAIL_HOST',
  'EMAIL_PORT',
  'EMAIL_USER',
  'EMAIL_PASS',
  'EMAIL_FROM'
];

// Check for missing required environment variables
const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingVars.length > 0) {
  console.error('❌ Missing required environment variables:');
  missingVars.forEach(varName => console.error(`- ${varName}`));
  console.log('\n💡 Run `npm run setup` to generate a .env file with default values.');
  process.exit(1);
}

// Configuration object
const config = {
  // Application
  env: process.env.NODE_ENV || 'development',
  port: process.env.PORT || 3000,
  isProduction: process.env.NODE_ENV === 'production',
  isDevelopment: process.env.NODE_ENV === 'development',
  isTest: process.env.NODE_ENV === 'test',
  
  // URLs
  frontendUrl: process.env.FRONTEND_URL,
  apiUrl: process.env.API_URL,
  
  // JWT
  jwt: {
    secret: process.env.JWT_SECRET,
    tempSecret: process.env.JWT_TEMP_SECRET,
    expiresIn: process.env.JWT_EXPIRES_IN || '1d',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
    issuer: process.env.JWT_ISSUER || 'payment-gateway',
    enableBypass: process.env.ENABLE_JWT_BYPASS === 'true'
  },
  
  // Database
  database: {
    uri: process.env.MONGODB_URI,
    testUri: process.env.MONGODB_TEST_URI,
    options: {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      autoIndex: true,
    }
  },
  
  // Email
  email: {
    host: process.env.EMAIL_HOST,
    port: process.env.EMAIL_PORT,
    secure: process.env.EMAIL_SECURE === 'true',
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
    from: process.env.EMAIL_FROM
  },
  
  // Security
  security: {
    cookieSecret: process.env.COOKIE_SECRET,
    rateLimit: {
      windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10), // 15 minutes
      max: parseInt(process.env.RATE_LIMIT_MAX || '100', 10) // 100 requests per window
    }
  },
  
  // Features
  features: {
    enable2FA: process.env.ENABLE_2FA === 'true',
    enableSignup: process.env.ENABLE_SIGNUP !== 'false',
    enablePasswordReset: process.env.ENABLE_PASSWORD_RESET !== 'false',
    enableRateLimiting: process.env.ENABLE_RATE_LIMITING !== 'false'
  },
  
  // Admin
  admin: {
    email: process.env.ADMIN_EMAIL,
    password: process.env.ADMIN_PASSWORD
  },
  
  // CORS
  cors: {
    origin: process.env.CORS_ORIGIN ? process.env.CORS_ORIGIN.split(',') : '*',
    methods: process.env.CORS_METHODS ? process.env.CORS_METHODS.split(',') : ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
    credentials: process.env.CORS_CREDENTIALS === 'true'
  }
};

module.exports = config;
