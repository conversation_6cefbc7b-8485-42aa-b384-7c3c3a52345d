import { useState, useMemo } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { PaginatedResponse } from '../../services/traderService';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/ui/table';
import { Badge } from '../../components/ui/badge';
import { Loader2, Search, UserPlus, MoreHorizontal } from 'lucide-react';
import { traderAdminService, Trader, TraderStatus } from '../../services/traderService';
import { format } from 'date-fns';
import { toast } from '../../components/ui/use-toast';
import { TraderDetailsModal } from '../../components/admin/TraderDetailsModal';

type TraderListItem = Trader & {
  assignedMerchants: number;
  totalCollections: number;
  totalAmountCollected: number;
  lastActive?: string;
};

export function TradersPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(1);
  const [selectedTrader, setSelectedTrader] = useState<TraderListItem | null>(null);
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  const limit = 10;
  const queryClient = useQueryClient();

  const { mutate: updateStatus } = useMutation({
    mutationFn: async ({ id, status }: { id: string; status: TraderStatus }) => {
      await traderAdminService.updateTraderStatus(id, status);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['traders'] });
      toast({
        title: 'Status updated',
        description: 'Trader status has been updated successfully.'
      });
    },
    onError: () => {
      toast({
        title: 'Error',
        description: 'Failed to update trader status. Please try again.',
        variant: 'destructive',
      });
    }
  });

  const handleViewDetails = (trader: TraderListItem) => {
    setSelectedTrader(trader);
    setIsDetailsOpen(true);
  };

  const handleStatusChange = (status: 'active' | 'inactive' | 'suspended') => {
    if (selectedTrader) {
      updateStatus({ 
        id: selectedTrader._id, 
        status: status as TraderStatus 
      });
      setIsDetailsOpen(false);
    }
  };

  const { data: response, isLoading, error, refetch } = useQuery<PaginatedResponse<Trader>>({
    queryKey: ['traders', { page, limit, search: searchTerm }],
    queryFn: () => traderAdminService.getTraders({ 
      page, 
      limit, 
      search: searchTerm,
      status: undefined
    })
  });

  // Transform the response data to include required fields
  const traders = useMemo<TraderListItem[]>(() => {
    if (!response?.data) return [];
    return response.data.map(trader => ({
      ...trader,
      assignedMerchants: 0, // Default value, replace with actual data if available
      totalCollections: 0,  // Default value, replace with actual data if available
      totalAmountCollected: 0 // Default value, replace with actual data if available
    }));
  }, [response]);

  // Calculate total pages for pagination
  const totalPages = response?.meta?.totalPages || 1;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="w-8 h-8 animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-10">
        <h3 className="text-lg font-medium mb-2">Failed to load traders</h3>
        <p className="text-muted-foreground mb-4">Please try again later.</p>
        <Button onClick={() => refetch()}>Retry</Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Traders Management</h2>
        <Button>
          <UserPlus className="mr-2 h-4 w-4" />
          Add Trader
        </Button>
      </div>
      
      <Card>
        <CardHeader className="pb-0">
          <div className="flex items-center justify-between">
            <CardTitle>All Traders</CardTitle>
            <div className="relative w-64">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search traders..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Assigned Merchants</TableHead>
                <TableHead>Total Collected</TableHead>
                <TableHead>Joined On</TableHead>
                <TableHead className="w-[100px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {traders.map((trader) => (
                <TableRow key={trader._id}>
                  <TableCell className="font-medium">{trader.name}</TableCell>
                  <TableCell>{trader.email}</TableCell>
                  <TableCell>
                    <Badge variant={
                      trader.status === TraderStatus.ACTIVE ? 'default' : 
                      trader.status === TraderStatus.INACTIVE ? 'secondary' : 'destructive'
                    }>
                      {trader.status}
                    </Badge>
                  </TableCell>
                  <TableCell>{trader.assignedMerchants}</TableCell>
                  <TableCell>{formatCurrency(trader.totalAmountCollected || 0)}</TableCell>
                  <TableCell>{format(new Date(trader.createdAt), 'MMM d, yyyy')}</TableCell>
                  <TableCell>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={() => handleViewDetails(trader)}
                    >
                      <MoreHorizontal className="h-4 w-4" />
                      <span className="sr-only">View details</span>
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          
          {traders.length === 0 && (
            <div className="py-8 text-center text-muted-foreground">
              No traders found
            </div>
          )}
          
          <div className="flex items-center justify-end space-x-2 p-4 border-t">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(p => Math.max(1, p - 1))}
              disabled={page === 1}
            >
              Previous
            </Button>
            <span className="text-sm text-muted-foreground">
              Page {page} of {totalPages}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(p => p + 1)}
              disabled={page >= totalPages}
            >
              Next
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Trader Details Modal */}
      {selectedTrader && (
        <TraderDetailsModal
          trader={selectedTrader}
          isOpen={isDetailsOpen}
          onClose={() => setIsDetailsOpen(false)}
          onStatusChange={handleStatusChange}
        />
      )}
    </div>
  );
}

// Helper function to format currency
function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount);
}

export default TradersPage;
