import React, { useState } from 'react';
import { 
  Container, 
  Box, 
  Typography, 
  Paper, 
  useTheme, 
  useMediaQuery,
  Button,
  Grid,
  Link,
  Alert,
  CircularProgress
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { useNavigate } from 'react-router-dom';
import { Lock as LockIcon, ArrowBack as ArrowBackIcon } from '@mui/icons-material';
import OnboardingWizard from '../../components/onboarding/OnboardingWizard';

// Styled components
const StyledContainer = styled(Container)(({ theme }) => ({
  paddingTop: theme.spacing(8),
  paddingBottom: theme.spacing(8),
  minHeight: '100vh',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'center',
}));

const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(4),
  borderRadius: theme.shape.borderRadius * 2,
  boxShadow: theme.shadows[3],
  [theme.breakpoints.down('sm')]: {
    padding: theme.spacing(3, 2),
    boxShadow: 'none',
  },
}));

const LogoContainer = styled(Box)({
  textAlign: 'center',
  marginBottom: 24,
});

const OnboardingPage = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  // Handle form submission
  const handleSubmit = async (values) => {
    setLoading(true);
    setError('');
    
    try {
      // Simulate API call
      console.log('Submitting form with values:', values);
      
      // In a real app, you would make an API call here
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // For demo purposes, we'll just show a success message
      setSuccess(true);
    } catch (err) {
      console.error('Error submitting form:', err);
      setError(err.message || 'An error occurred while submitting the form. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle back to home
  const handleBackToHome = () => {
    navigate('/');
  };

  // Handle login navigation
  const handleLogin = () => {
    navigate('/login');
  };

  if (success) {
    return (
      <StyledContainer component="main" maxWidth="md">
        <StyledPaper elevation={isMobile ? 0 : 3}>
          <Box textAlign="center" py={6}>
            <Box
              sx={{
                width: 80,
                height: 80,
                borderRadius: '50%',
                backgroundColor: 'success.light',
                display: 'inline-flex',
                alignItems: 'center',
                justifyContent: 'center',
                mb: 3,
              }}
            >
              <LockIcon sx={{ color: 'success.contrastText', fontSize: 40 }} />
            </Box>
            
            <Typography component="h1" variant="h4" gutterBottom>
              Account Created Successfully!
            </Typography>
            
            <Typography variant="body1" color="textSecondary" paragraph>
              Thank you for signing up with our platform. Your account is being reviewed and will be activated shortly.
            </Typography>
            
            <Typography variant="body2" color="textSecondary" paragraph>
              We've sent a confirmation email to your registered email address. Please verify your email to complete the registration process.
            </Typography>
            
            <Box mt={4} display="flex" flexDirection={isMobile ? 'column' : 'row'} gap={2} justifyContent="center">
              <Button
                variant="contained"
                color="primary"
                onClick={handleLogin}
                size="large"
                sx={{ minWidth: 200 }}
              >
                Go to Login
              </Button>
              
              <Button
                variant="outlined"
                color="primary"
                onClick={handleBackToHome}
                size="large"
                sx={{ minWidth: 200 }}
              >
                Back to Home
              </Button>
            </Box>
            
            <Box mt={4}>
              <Typography variant="body2" color="textSecondary">
                Didn't receive the email?{' '}
                <Link href="#" color="primary" onClick={(e) => { e.preventDefault(); /* Handle resend */ }}>
                  Resend confirmation email
                </Link>
              </Typography>
            </Box>
          </Box>
        </StyledPaper>
      </StyledContainer>
    );
  }

  return (
    <StyledContainer component="main" maxWidth="md">
      <Button
        startIcon={<ArrowBackIcon />}
        onClick={handleBackToHome}
        sx={{ mb: 2, alignSelf: 'flex-start' }}
      >
        Back to Home
      </Button>
      
      <LogoContainer>
        <Box
          component="img"
          src="/logo.png"
          alt="Logo"
          sx={{ 
            height: 60,
            width: 'auto',
            mb: 2,
            filter: theme.palette.mode === 'dark' ? 'brightness(0) invert(1)' : 'none',
          }}
          onError={(e) => {
            e.target.style.display = 'none';
            e.target.parentNode.querySelector('h1').style.display = 'block';
          }}
        />
        <Typography component="h1" variant="h4" sx={{ display: 'none' }}>
          Payment Gateway
        </Typography>
        <Typography variant="subtitle1" color="textSecondary">
          Create your merchant/trader account
        </Typography>
      </LogoContainer>
      
      <StyledPaper elevation={isMobile ? 0 : 3}>
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}
        
        <OnboardingWizard 
          onSubmit={handleSubmit} 
          loading={loading} 
          error={error}
        />
        
        <Box mt={4} textAlign="center">
          <Typography variant="body2" color="textSecondary">
            Already have an account?{' '}
            <Link href="/login" onClick={(e) => { e.preventDefault(); handleLogin(); }}>
              Sign in
            </Link>
          </Typography>
        </Box>
        
        <Box mt={4}>
          <Typography variant="caption" color="textSecondary" component="div">
            By creating an account, you agree to our{' '}
            <Link href="/terms" target="_blank" rel="noopener noreferrer">
              Terms of Service
            </Link>{' '}
            and{' '}
            <Link href="/privacy" target="_blank" rel="noopener noreferrer">
              Privacy Policy
            </Link>.
          </Typography>
        </Box>
      </StyledPaper>
      
      {/* Loading overlay */}
      {loading && (
        <Box
          sx={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 9999,
          }}
        >
          <Paper
            sx={{
              p: 4,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              minWidth: 300,
            }}
          >
            <CircularProgress size={40} thickness={4} sx={{ mb: 2 }} />
            <Typography variant="subtitle1" color="common.white">
              Creating your account...
            </Typography>
          </Paper>
        </Box>
      )}
    </StyledContainer>
  );
};

export default OnboardingPage;
