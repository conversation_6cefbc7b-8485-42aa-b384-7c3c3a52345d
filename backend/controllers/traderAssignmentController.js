const TraderAssignment = require('../models/TraderAssignment');
const User = require('../models/User');

// Create a new trader-merchant assignment
exports.createAssignment = async (req, res) => {
  try {
    const { traderId, merchantId, assignmentType, endDate, collectionTarget, permissions } = req.body;
    
    // Check if trader and merchant exist and have correct roles
    const [trader, merchant] = await Promise.all([
      User.findOne({ _id: traderId, role: 'trader' }),
      User.findOne({ _id: merchantId, role: 'merchant' })
    ]);

    if (!trader || !merchant) {
      return res.status(404).json({ message: 'Trader or merchant not found or invalid role' });
    }

    // Check for existing active assignment
    const existingAssignment = await TraderAssignment.findOne({
      traderId,
      merchantId,
      status: 'active'
    });

    if (existingAssignment) {
      return res.status(400).json({ message: 'An active assignment already exists for this trader and merchant' });
    }

    const assignment = new TraderAssignment({
      traderId,
      merchantId,
      assignedBy: req.user.id,
      assignmentType,
      startDate: new Date(),
      endDate: endDate || null,
      collectionTarget,
      permissions: permissions || ['view_transactions', 'process_payments']
    });

    await assignment.save();
    res.status(201).json(assignment);
  } catch (error) {
    console.error('Error creating assignment:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Get all assignments with optional filters
exports.getAssignments = async (req, res) => {
  try {
    const { traderId, merchantId, status } = req.query;
    const filter = {};
    
    if (traderId) filter.traderId = traderId;
    if (merchantId) filter.merchantId = merchantId;
    if (status) filter.status = status;

    const assignments = await TraderAssignment.find(filter)
      .populate('traderId', 'name email phone')
      .populate('merchantId', 'businessName email phone')
      .populate('assignedBy', 'name email');

    res.json(assignments);
  } catch (error) {
    console.error('Error fetching assignments:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Update an assignment
exports.updateAssignment = async (req, res) => {
  try {
    const { id } = req.params;
    const { status, endDate, collectionTarget, permissions } = req.body;

    const assignment = await TraderAssignment.findById(id);
    if (!assignment) {
      return res.status(404).json({ message: 'Assignment not found' });
    }

    // Only allow updating certain fields
    if (status) assignment.status = status;
    if (endDate) assignment.endDate = endDate;
    if (collectionTarget) assignment.collectionTarget = collectionTarget;
    if (permissions) assignment.permissions = permissions;

    await assignment.save();
    res.json(assignment);
  } catch (error) {
    console.error('Error updating assignment:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Delete an assignment
exports.deleteAssignment = async (req, res) => {
  try {
    const { id } = req.params;
    await TraderAssignment.findByIdAndDelete(id);
    res.json({ message: 'Assignment deleted successfully' });
  } catch (error) {
    console.error('Error deleting assignment:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Get assignments for current trader
exports.getMyAssignments = async (req, res) => {
  try {
    if (req.user.role !== 'trader') {
      return res.status(403).json({ message: 'Access denied' });
    }

    const assignments = await TraderAssignment.find({ traderId: req.user.id, status: 'active' })
      .populate('merchantId', 'businessName address phone email')
      .select('-traderId -assignedBy -__v');

    res.json(assignments);
  } catch (error) {
    console.error('Error fetching trader assignments:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};
