{"name": "payment-gateway-backend", "version": "1.0.0", "description": "Payment Gateway Backend API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "cross-env NODE_ENV=test jest --config jest.config.js", "test:security": "cross-env NODE_ENV=test jest --config jest.config.js --testPathPattern=test/__tests__/security", "test:watch": "cross-env NODE_ENV=test jest --watch --config jest.config.js", "test:coverage": "cross-env NODE_ENV=test jest --coverage --config jest.config.js", "seed": "node scripts/seed.js", "setup": "node scripts/setup-env.js"}, "dependencies": {"axios": "^1.5.0", "bcryptjs": "^2.4.3", "compression": "^1.8.1", "connect-mongo": "^5.1.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "ejs": "^3.1.10", "express": "^4.18.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^6.10.0", "express-session": "^1.18.2", "express-validator": "^7.0.1", "helmet": "^7.2.0", "hpp": "^0.2.3", "moment": "^2.30.1", "mongoose": "^7.5.0", "morgan": "^1.10.1", "nodemailer": "^6.9.4", "qrcode": "^1.5.4", "rate-limiter-flexible": "^7.1.1", "socket.io": "^4.8.1", "speakeasy": "^2.0.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^11.1.0", "winston": "^3.17.0", "xss-clean": "^0.1.4", "yamljs": "^0.3.0"}, "devDependencies": {"chalk": "^4.1.2", "cross-env": "^10.0.0", "dotenv-cli": "^9.0.0", "jest": "^29.6.4", "mongodb-memory-server": "^9.1.6", "nodemon": "^3.0.1", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}