const mongoose = require('mongoose');

const settlementSchema = new mongoose.Schema({
  settlementId: { 
    type: String, 
    required: true, 
    unique: true 
  },
  type: { 
    type: String, 
    enum: ['trader-trader', 'trader-merchant', 'trader-agent', 'internal'],
    required: true 
  },
  fromParty: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  toParty: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  periodStart: { 
    type: Date, 
    required: true 
  },
  periodEnd: { 
    type: Date, 
    required: true 
  },
  transactions: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Transaction'
  }],
  totalAmount: { 
    type: Number, 
    required: true 
  },
  fees: {
    processing: { type: Number, default: 0 },
    platform: { type: Number, default: 0 },
    commission: { type: Number, default: 0 }
  },
  commissionRates: {
    payin: { type: Number, default: 0 },
    payout: { type: Number, default: 0 },
    internal: { type: Number, default: 0 }
  },
  settlementAmount: { 
    type: Number, 
    required: true 
  },
  status: {
    type: String,
    enum: ['pending', 'processing', 'completed', 'failed', 'disputed'],
    default: 'pending'
  },
  createdBy: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User' 
  },
  processedAt: Date,
  notes: String,
  // Additional fields for tracking
  referenceNumber: String,
  paymentMethod: {
    type: String,
    enum: ['bank_transfer', 'wallet', 'internal_balance', 'other'],
    default: 'bank_transfer'
  },
  metadata: mongoose.Schema.Types.Mixed
}, { 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
settlementSchema.index({ fromParty: 1, status: 1 });
settlementSchema.index({ toParty: 1, status: 1 });
settlementSchema.index({ periodStart: 1, periodEnd: 1 });
settlementSchema.index({ type: 1, status: 1 });

// Virtual for settlement duration in days
settlementSchema.virtual('durationInDays').get(function() {
  return Math.ceil((this.periodEnd - this.periodStart) / (1000 * 60 * 60 * 24));
});

// Pre-save hook to generate settlement ID
settlementSchema.pre('save', async function(next) {
  if (!this.settlementId) {
    const count = await this.constructor.countDocuments();
    this.settlementId = `STL-${Date.now()}-${(count + 1).toString().padStart(6, '0')}`;
  }
  next();
});

// Static method to get settlement summary
settlementSchema.statics.getSummary = async function(query = {}) {
  return this.aggregate([
    { $match: query },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        totalAmount: { $sum: '$totalAmount' },
        totalSettlement: { $sum: '$settlementAmount' },
        totalFees: { 
          $sum: { 
            $add: [
              { $ifNull: ['$fees.processing', 0] },
              { $ifNull: ['$fees.platform', 0] },
              { $ifNull: ['$fees.commission', 0] }
            ]
          }
        }
      }
    }
  ]);
};

module.exports = mongoose.model('Settlement', settlementSchema);
