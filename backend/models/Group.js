const mongoose = require('mongoose');

const groupSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    unique: true,
    maxlength: 50
  },
  description: {
    type: String,
    trim: true,
    maxlength: 500
  },
  type: {
    type: String,
    enum: ['system', 'custom'],
    default: 'custom'
  },
  members: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    role: {
      type: String,
      enum: ['member', 'admin', 'owner'],
      default: 'member'
    },
    joinedAt: {
      type: Date,
      default: Date.now
    }
  }],
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  isActive: {
    type: Boolean,
    default: true
  },
  metadata: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
groupSchema.index({ name: 1 }, { unique: true });
groupSchema.index({ type: 1 });
groupSchema.index({ 'members.user': 1 });
groupSchema.index({ tags: 1 });

// Virtual for member count
groupSchema.virtual('memberCount').get(function() {
  return this.members ? this.members.length : 0;
});

// Static method to find groups by user ID
groupSchema.statics.findByUserId = function(userId) {
  return this.find({ 'members.user': userId });
};

// Method to add member to group
groupSchema.methods.addMember = function(userId, role = 'member', addedBy) {
  if (!this.members.some(member => member.user.toString() === userId.toString())) {
    this.members.push({
      user: userId,
      role,
      joinedAt: new Date()
    });
    this.updatedBy = addedBy || this.updatedBy;
    return this.save();
  }
  return Promise.resolve(this);
};

// Method to remove member from group
groupSchema.methods.removeMember = function(userId, updatedBy) {
  const initialLength = this.members.length;
  this.members = this.members.filter(member => member.user.toString() !== userId.toString());
  
  if (this.members.length !== initialLength) {
    this.updatedBy = updatedBy || this.updatedBy;
    return this.save();
  }
  return Promise.resolve(this);
};

// Pre-save hook to ensure at least one owner
groupSchema.pre('save', async function(next) {
  if (this.isModified('members')) {
    const owners = this.members.filter(member => member.role === 'owner');
    if (owners.length === 0 && this.members.length > 0) {
      // Automatically make the first member an owner if no owners exist
      this.members[0].role = 'owner';
    }
  }
  next();
});

// Pre-remove hook to clean up references
// groupSchema.pre('remove', async function(next) {
//   // Remove group references from users
//   await mongoose.model('User').updateMany(
//     { _id: { $in: this.members.map(m => m.user) } },
//     { $pull: { groups: this._id } }
//   );
//   next();
// });

module.exports = mongoose.model('Group', groupSchema);
