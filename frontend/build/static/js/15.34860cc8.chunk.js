"use strict";(self.webpackChunkpayment_gateway_frontend=self.webpackChunkpayment_gateway_frontend||[]).push([[15],{656:(e,a,r)=>{r.d(a,{u:()=>i});var t=r(6742),s=r(9772),n=r(3216),o=r(579);function i(e){let{title:a,description:r="This page is under construction and will be available soon."}=e;const i=(0,n.Zp)();return(0,o.jsx)("div",{className:"container mx-auto py-8",children:(0,o.jsxs)(t.Zp,{children:[(0,o.jsx)(t.aR,{children:(0,o.jsx)(t.ZB,{className:"text-2xl font-bold",children:a})}),(0,o.jsxs)(t.<PERSON>,{className:"space-y-4",children:[(0,o.jsx)("p",{className:"text-muted-foreground",children:r}),(0,o.jsxs)("div",{className:"flex space-x-4",children:[(0,o.jsx)(s.$,{onClick:()=>i(-1),variant:"outline",children:"Go Back"}),(0,o.jsx)(s.$,{onClick:()=>i("/admin"),children:"Return to Dashboard"})]})]})]})})}},3015:(e,a,r)=>{r.r(a),r.d(a,{PaymentsPage:()=>n,default:()=>o});var t=r(656),s=r(579);function n(){return(0,s.jsx)(t.u,{title:"Payments Overview",description:"View and manage all payment transactions across the platform."})}const o=n},6742:(e,a,r)=>{r.d(a,{BT:()=>c,Wu:()=>l,ZB:()=>d,Zp:()=>o,aR:()=>i,wL:()=>u});var t=r(5043),s=r(3009),n=r(579);const o=t.forwardRef((e,a)=>{let{className:r,...t}=e;return(0,n.jsx)("div",{ref:a,className:(0,s.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...t})});o.displayName="Card";const i=t.forwardRef((e,a)=>{let{className:r,...t}=e;return(0,n.jsx)("div",{ref:a,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",r),...t})});i.displayName="CardHeader";const d=t.forwardRef((e,a)=>{let{className:r,...t}=e;return(0,n.jsx)("h3",{ref:a,className:(0,s.cn)("text-2xl font-semibold leading-none tracking-tight",r),...t})});d.displayName="CardTitle";const c=t.forwardRef((e,a)=>{let{className:r,...t}=e;return(0,n.jsx)("p",{ref:a,className:(0,s.cn)("text-sm text-muted-foreground",r),...t})});c.displayName="CardDescription";const l=t.forwardRef((e,a)=>{let{className:r,...t}=e;return(0,n.jsx)("div",{ref:a,className:(0,s.cn)("p-6 pt-0",r),...t})});l.displayName="CardContent";const u=t.forwardRef((e,a)=>{let{className:r,...t}=e;return(0,n.jsx)("div",{ref:a,className:(0,s.cn)("flex items-center p-6 pt-0",r),...t})});u.displayName="CardFooter"},9772:(e,a,r)=>{r.d(a,{$:()=>c});var t=r(5043),s=r(6851),n=r(917),o=r(3009),i=r(579);const d=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=t.forwardRef((e,a)=>{let{className:r,variant:t,size:n,asChild:c=!1,...l}=e;const u=c?s.DX:"button";return(0,i.jsx)(u,{className:(0,o.cn)(d({variant:t,size:n,className:r})),ref:a,...l})});c.displayName="Button"}}]);
//# sourceMappingURL=15.34860cc8.chunk.js.map