# Trader Management API Documentation

This document outlines the API endpoints for managing traders and their assignments in the Payment Gateway system.

## Base URL
```
/api/v1/trader
```

## Authentication
All endpoints require authentication using a valid JWT token. Include the token in the `Authorization` header:
```
Authorization: Bearer <your-jwt-token>
```

## Endpoints

### 1. Get Trader Assignments
Retrieve a list of trader assignments with optional filtering.

**Endpoint:** `GET /api/v1/trader/assignments`

**Query Parameters:**
- `merchantId` (optional): Filter by merchant ID
- `traderId` (optional): Filter by trader ID
- `status` (optional): Filter by assignment status

**Response:**
```json
{
  "success": true,
  "count": 2,
  "data": [
    {
      "_id": "60d5ec9b58f9b12a3c7e8d1a",
      "merchant": {
        "_id": "60d5ec9b58f9b12a3c7e8d1b",
        "businessName": "Merchant One",
        "email": "<EMAIL>"
      },
      "trader": {
        "_id": "60d5ec9b58f9b12a3c7e8d1c",
        "name": "John Trader",
        "email": "<EMAIL>"
      },
      "startDate": "2023-01-01T00:00:00.000Z",
      "endDate": "2023-12-31T23:59:59.999Z",
      "status": "active",
      "notes": "Primary trader for Merchant One"
    }
  ]
}
```

### 2. Create Trader Assignment
Assign a trader to a merchant.

**Endpoint:** `POST /api/v1/trader/assignments`

**Request Body:**
```json
{
  "merchantId": "60d5ec9b58f9b12a3c7e8d1b",
  "traderId": "60d5ec9b58f9b12a3c7e8d1c",
  "startDate": "2023-01-01T00:00:00.000Z",
  "endDate": "2023-12-31T23:59:59.999Z",
  "notes": "Primary trader assignment"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "_id": "60d5ec9b58f9b12a3c7e8d1a",
    "merchant": "60d5ec9b58f9b12a3c7e8d1b",
    "trader": "60d5ec9b58f9b12a3c7e8d1c",
    "startDate": "2023-01-01T00:00:00.000Z",
    "endDate": "2023-12-31T23:59:59.999Z",
    "status": "active",
    "notes": "Primary trader assignment",
    "assignedBy": "60d5ec9b58f9b12a3c7e8d1d",
    "createdAt": "2023-01-01T12:00:00.000Z",
    "updatedAt": "2023-01-01T12:00:00.000Z"
  }
}
```

### 3. Update Trader Assignment
Update an existing trader assignment.

**Endpoint:** `PUT /api/v1/trader/assignments/:id`

**Request Body:**
```json
{
  "status": "completed",
  "endDate": "2023-06-30T23:59:59.999Z",
  "notes": "Assignment completed early"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "_id": "60d5ec9b58f9b12a3c7e8d1a",
    "merchant": "60d5ec9b58f9b12a3c7e8d1b",
    "trader": "60d5ec9b58f9b12a3c7e8d1c",
    "startDate": "2023-01-01T00:00:00.000Z",
    "endDate": "2023-06-30T23:59:59.999Z",
    "status": "completed",
    "notes": "Assignment completed early",
    "assignedBy": "60d5ec9b58f9b12a3c7e8d1d",
    "createdAt": "2023-01-01T12:00:00.000Z",
    "updatedAt": "2023-03-15T14:30:00.000Z"
  }
}
```

### 4. Get Trader Performance
Get performance metrics for traders.

**Endpoint:** `GET /api/v1/trader/performance`

**Query Parameters:**
- `traderId` (optional): Filter by specific trader
- `startDate` (optional): Start date for filtering
- `endDate` (optional): End date for filtering

**Response:**
```json
{
  "success": true,
  "count": 1,
  "data": [
    {
      "trader": {
        "id": "60d5ec9b58f9b12a3c7e8d1c",
        "name": "John Trader",
        "email": "<EMAIL>"
      },
      "totalAssignments": 5,
      "totalTransactions": 1245,
      "totalAmount": 125000.75,
      "successfulTransactions": 1200,
      "failedTransactions": 45,
      "successRate": 96.39
    }
  ]
}
```

## Error Responses

### 400 Bad Request
```json
{
  "success": false,
  "error": "Invalid trader ID"
}
```

### 401 Unauthorized
```json
{
  "success": false,
  "error": "Not authorized to access this route"
}
```

### 403 Forbidden
```json
{
  "success": false,
  "error": "Not authorized to update this assignment"
}
```

### 404 Not Found
```json
{
  "success": false,
  "error": "No assignment found with id of 60d5ec9b58f9b12a3c7e8d1a"
}
```

## Rate Limiting
All endpoints are rate limited to 100 requests per 15 minutes per IP address.
