"use strict";(self.webpackChunkpayment_gateway_frontend=self.webpackChunkpayment_gateway_frontend||[]).push([[836],{382:(e,t,a)=>{a.d(t,{A0:()=>o,BF:()=>i,Hj:()=>l,XI:()=>d,nA:()=>m,nd:()=>c});var s=a(5043),r=a(3009),n=a(579);const d=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("div",{className:"w-full overflow-auto",children:(0,n.jsx)("table",{ref:t,className:(0,r.cn)("w-full caption-bottom text-sm",a),...s})})});d.displayName="Table";const o=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("thead",{ref:t,className:(0,r.cn)("[&_tr]:border-b",a),...s})});o.displayName="TableHeader";const i=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("tbody",{ref:t,className:(0,r.cn)("[&_tr:last-child]:border-0",a),...s})});i.displayName="TableBody";s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("tfoot",{ref:t,className:(0,r.cn)("bg-primary font-medium text-primary-foreground",a),...s})}).displayName="TableFooter";const l=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("tr",{ref:t,className:(0,r.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",a),...s})});l.displayName="TableRow";const c=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("th",{ref:t,className:(0,r.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",a),...s})});c.displayName="TableHead";const m=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("td",{ref:t,className:(0,r.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",a),...s})});m.displayName="TableCell";s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("caption",{ref:t,className:(0,r.cn)("mt-4 text-sm text-muted-foreground",a),...s})}).displayName="TableCaption"},3647:(e,t,a)=>{a.d(t,{SQ:()=>u,_2:()=>f,rI:()=>c,ty:()=>m});var s=a(5043),r=a(1624),n=a(1024),d=a(8432),o=a(3992),i=a(3009),l=a(579);const c=r.bL,m=r.l9;r.YJ,r.ZL,r.Pb,r.z6;s.forwardRef((e,t)=>{let{className:a,inset:s,children:n,...o}=e;return(0,l.jsxs)(r.ZP,{ref:t,className:(0,i.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",s&&"pl-8",a),...o,children:[n,(0,l.jsx)(d.A,{className:"ml-auto h-4 w-4"})]})}).displayName=r.ZP.displayName;s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,l.jsx)(r.G5,{ref:t,className:(0,i.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...s})}).displayName=r.G5.displayName;const u=s.forwardRef((e,t)=>{let{className:a,sideOffset:s=4,...n}=e;return(0,l.jsx)(r.ZL,{children:(0,l.jsx)(r.UC,{ref:t,sideOffset:s,className:(0,i.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...n})})});u.displayName=r.UC.displayName;const f=s.forwardRef((e,t)=>{let{className:a,inset:s,...n}=e;return(0,l.jsx)(r.q7,{ref:t,className:(0,i.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s&&"pl-8",a),...n})});f.displayName=r.q7.displayName;s.forwardRef((e,t)=>{let{className:a,children:s,checked:d,...o}=e;return(0,l.jsxs)(r.H_,{ref:t,className:(0,i.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),checked:d,...o,children:[(0,l.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,l.jsx)(r.VF,{children:(0,l.jsx)(n.A,{className:"h-4 w-4"})})}),s]})}).displayName=r.H_.displayName;s.forwardRef((e,t)=>{let{className:a,children:s,...n}=e;return(0,l.jsxs)(r.hN,{ref:t,className:(0,i.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...n,children:[(0,l.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,l.jsx)(r.VF,{children:(0,l.jsx)(o.A,{className:"h-2 w-2 fill-current"})})}),s]})}).displayName=r.hN.displayName;s.forwardRef((e,t)=>{let{className:a,inset:s,...n}=e;return(0,l.jsx)(r.JU,{ref:t,className:(0,i.cn)("px-2 py-1.5 text-sm font-semibold",s&&"pl-8",a),...n})}).displayName=r.JU.displayName;s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,l.jsx)(r.wv,{ref:t,className:(0,i.cn)("-mx-1 my-1 h-px bg-muted",a),...s})}).displayName=r.wv.displayName},6742:(e,t,a)=>{a.d(t,{BT:()=>l,Wu:()=>c,ZB:()=>i,Zp:()=>d,aR:()=>o,wL:()=>m});var s=a(5043),r=a(3009),n=a(579);const d=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,r.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...s})});d.displayName="Card";const o=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,r.cn)("flex flex-col space-y-1.5 p-6",a),...s})});o.displayName="CardHeader";const i=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("h3",{ref:t,className:(0,r.cn)("text-2xl font-semibold leading-none tracking-tight",a),...s})});i.displayName="CardTitle";const l=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("p",{ref:t,className:(0,r.cn)("text-sm text-muted-foreground",a),...s})});l.displayName="CardDescription";const c=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,r.cn)("p-6 pt-0",a),...s})});c.displayName="CardContent";const m=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,r.cn)("flex items-center p-6 pt-0",a),...s})});m.displayName="CardFooter"},6836:(e,t,a)=>{a.r(t),a.d(t,{MerchantsPage:()=>v,default:()=>j});var s=a(5043),r=a(5604),n=a(6742),d=a(9772),o=a(9954),i=a(382),l=a(3647),c=a(8567),m=a(7772),u=a(6875),f=a(7892),p=a(6213);const h=`${a(4558).JR}/merchants`,x={async getAllMerchants(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{page:t=1,limit:a=10,search:s,status:r,sortBy:n,order:d}=e,o=new URLSearchParams({page:t.toString(),limit:a.toString(),...s&&{search:s},...r&&{status:r},...n&&{sortBy:n},...d&&{order:d}});return(await p.A.get(`${h}/admin/merchants?${o}`)).data},updateMerchantStatus:async(e,t,a)=>(await p.A.patch(`${h}/admin/merchants/${e}/status`,{status:t,reason:a},{headers:{"Content-Type":"application/json"}})).data,getMerchantDetails:async e=>(await p.A.get(`${h}/admin/merchants/${e}`)).data};var g=a(6879),y=a(7842),N=a(579);const b={verified:"default",pending:"secondary",rejected:"destructive",suspended:"outline",draft:"outline"};function v(){const[e,t]=(0,s.useState)(""),[a,p]=(0,s.useState)(1),{data:h,isLoading:v,error:j,refetch:w}=(0,r.I)({queryKey:["merchants",{page:a,limit:10,search:e}],queryFn:()=>x.getAllMerchants({page:a,limit:10,search:e})}),A=async(e,t)=>{try{await x.updateMerchantStatus(e,t.status,t.reason),(0,g.oR)({title:"Status updated",description:"Merchant status has been updated successfully."}),w()}catch(j){(0,g.oR)({title:"Error",description:"Failed to update merchant status. Please try again.",variant:"destructive"})}};return v?(0,N.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,N.jsx)(m.A,{className:"w-8 h-8 animate-spin"})}):j?(0,N.jsxs)("div",{className:"text-center py-10",children:[(0,N.jsx)("h3",{className:"text-lg font-medium mb-2",children:"Failed to load merchants"}),(0,N.jsx)("p",{className:"text-muted-foreground mb-4",children:"Please try again later."}),(0,N.jsx)(d.$,{onClick:()=>w(),children:"Retry"})]}):(0,N.jsxs)("div",{className:"space-y-6",children:[(0,N.jsx)("div",{className:"flex items-center justify-between",children:(0,N.jsx)("h2",{className:"text-3xl font-bold tracking-tight",children:"Merchants Management"})}),(0,N.jsxs)(n.Zp,{children:[(0,N.jsx)(n.aR,{className:"pb-0",children:(0,N.jsxs)("div",{className:"flex items-center justify-between",children:[(0,N.jsx)(n.ZB,{children:"All Merchants"}),(0,N.jsxs)("div",{className:"relative w-64",children:[(0,N.jsx)(u.A,{className:"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground"}),(0,N.jsx)(o.p,{type:"search",placeholder:"Search merchants...",className:"pl-8",value:e,onChange:e=>t(e.target.value)})]})]})}),(0,N.jsxs)(n.Wu,{className:"p-0",children:[(0,N.jsxs)(i.XI,{children:[(0,N.jsx)(i.A0,{children:(0,N.jsxs)(i.Hj,{children:[(0,N.jsx)(i.nd,{children:"Business Name"}),(0,N.jsx)(i.nd,{children:"Contact Email"}),(0,N.jsx)(i.nd,{children:"Phone"}),(0,N.jsx)(i.nd,{children:"Status"}),(0,N.jsx)(i.nd,{children:"Created At"}),(0,N.jsx)(i.nd,{className:"w-[100px]",children:"Actions"})]})}),(0,N.jsx)(i.BF,{children:null===h||void 0===h?void 0:h.data.map(e=>(0,N.jsxs)(i.Hj,{children:[(0,N.jsx)(i.nA,{className:"font-medium",children:e.businessName}),(0,N.jsx)(i.nA,{children:e.contactEmail}),(0,N.jsx)(i.nA,{children:e.contactPhone}),(0,N.jsx)(i.nA,{children:(0,N.jsx)(c.E,{variant:b[e.status],children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})}),(0,N.jsx)(i.nA,{children:(0,y.A)(new Date(e.createdAt),"MMM d, yyyy")}),(0,N.jsx)(i.nA,{children:(0,N.jsxs)(l.rI,{children:[(0,N.jsx)(l.ty,{asChild:!0,children:(0,N.jsxs)(d.$,{variant:"ghost",className:"h-8 w-8 p-0",children:[(0,N.jsx)("span",{className:"sr-only",children:"Open menu"}),(0,N.jsx)(f.A,{className:"h-4 w-4"})]})}),(0,N.jsxs)(l.SQ,{align:"end",children:[(0,N.jsx)(l._2,{onClick:()=>A(e.id,{status:"verified"}),disabled:"verified"===e.status,children:"Mark as Verified"}),(0,N.jsx)(l._2,{onClick:()=>A(e.id,{status:"suspended"}),disabled:"suspended"===e.status,children:"Suspend Account"}),(0,N.jsx)(l._2,{onClick:()=>A(e.id,{status:"rejected",reason:"Business verification failed"}),disabled:"rejected"===e.status,className:"text-destructive",children:"Reject"})]})]})})]},e.id))})]}),0===(null===h||void 0===h?void 0:h.data.length)&&(0,N.jsx)("div",{className:"py-8 text-center text-muted-foreground",children:"No merchants found"}),(0,N.jsxs)("div",{className:"flex items-center justify-end space-x-2 p-4 border-t",children:[(0,N.jsx)(d.$,{variant:"outline",size:"sm",onClick:()=>p(e=>Math.max(1,e-1)),disabled:1===a,children:"Previous"}),(0,N.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Page ",a," of ",(null===h||void 0===h?void 0:h.totalPages)||1]}),(0,N.jsx)(d.$,{variant:"outline",size:"sm",onClick:()=>p(e=>e+1),disabled:a>=((null===h||void 0===h?void 0:h.totalPages)||1),children:"Next"})]})]})]})]})}const j=v},6875:(e,t,a)=>{a.d(t,{A:()=>s});const s=(0,a(3797).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},6879:(e,t,a)=>{a.d(t,{dj:()=>u,oR:()=>m});var s=a(5043);let r=0;const n=new Map,d=e=>{if(n.has(e))return;const t=setTimeout(()=>{n.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e3);n.set(e,t)},o=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,5)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{const{toastId:a}=t;return a?d(a):e.toasts.forEach(e=>{d(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,open:!1}:e)}}case"REMOVE_TOAST":return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},i=[];let l={toasts:[]};function c(e){l=o(l,e),i.forEach(e=>{e(l)})}function m(e){let{...t}=e;const a=(r=(r+1)%Number.MAX_SAFE_INTEGER,r.toString()),s=()=>c({type:"DISMISS_TOAST",toastId:a});return c({type:"ADD_TOAST",toast:{...t,id:a,open:!0,onOpenChange:e=>{e||s()}}}),{id:a,dismiss:s,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:a}})}}function u(){const[e,t]=s.useState(l);return s.useEffect(()=>(i.push(t),()=>{const e=i.indexOf(t);e>-1&&i.splice(e,1)}),[e]),{...e,toast:m,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},7772:(e,t,a)=>{a.d(t,{A:()=>s});const s=(0,a(3797).A)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},7892:(e,t,a)=>{a.d(t,{A:()=>s});const s=(0,a(3797).A)("MoreHorizontal",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},8567:(e,t,a)=>{a.d(t,{E:()=>o});a(5043);var s=a(917),r=a(3009),n=a(579);const d=(0,s.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",success:"border-transparent bg-green-500 text-white hover:bg-green-500/80",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-500/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:a,...s}=e;return(0,n.jsx)("div",{className:(0,r.cn)(d({variant:a}),t),...s})}},9772:(e,t,a)=>{a.d(t,{$:()=>l});var s=a(5043),r=a(6851),n=a(917),d=a(3009),o=a(579);const i=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=s.forwardRef((e,t)=>{let{className:a,variant:s,size:n,asChild:l=!1,...c}=e;const m=l?r.DX:"button";return(0,o.jsx)(m,{className:(0,d.cn)(i({variant:s,size:n,className:a})),ref:t,...c})});l.displayName="Button"},9954:(e,t,a)=>{a.d(t,{p:()=>d});var s=a(5043),r=a(3009),n=a(579);const d=s.forwardRef((e,t)=>{let{className:a,type:s,...d}=e;return(0,n.jsx)("input",{type:s,className:(0,r.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:t,...d})});d.displayName="Input"}}]);
//# sourceMappingURL=836.a71a4994.chunk.js.map