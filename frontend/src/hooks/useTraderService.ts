import { useQuery, useMutation, useQueryClient, UseQueryOptions, UseMutationOptions } from '@tanstack/react-query';
import {
  traderService,
  traderAdminService,
  Trader,
  Assignment,
  Merchant,
  PerformanceMetrics,
  DashboardStats,
  GetTradersParams,
  GetAssignmentsParams,
  CreateTraderData,
  UpdateTraderData,
  CreateAssignmentData,
  UpdateAssignmentData,
  BulkAssignTradersData,
  GenerateReportParams,
  PaginatedResponse,
  AssignmentStatus,
  TraderStatus,
} from '../services/traderService';

// Query keys
const queryKeys = {
  // Admin trader queries
  traders: (params: GetTradersParams = {}) => ['traders', params],
  trader: (id: string) => ['trader', id],
  
  // Assignment queries
  myAssignments: (status?: AssignmentStatus | AssignmentStatus[]) => ['assignments', 'me', { status }],
  assignment: (id: string) => ['assignment', id],
  allAssignments: (params: GetAssignmentsParams = {}) => ['assignments', 'all', params],
  
  // Merchant queries
  merchants: ['merchants'] as const,
  
  // Performance queries
  performance: (params: { startDate?: string; endDate?: string; merchantId?: string } = {}) => 
    ['performance', params],
  
  // Dashboard queries
  dashboardStats: ['dashboard', 'stats'] as const,
} as const;

// Helper type to handle readonly arrays in query keys
type Mutable<T> = {
  -readonly [P in keyof T]: T[P] extends readonly (infer U)[] ? Mutable<U>[] : Mutable<T[P]>;
};

type QueryConfig<T, TQueryKey extends readonly unknown[] = readonly unknown[]> = Omit<UseQueryOptions<T, Error, T, TQueryKey>, 'queryKey' | 'queryFn'>;

// Admin Trader Hooks
export const useTraders = (params: GetTradersParams = {}, options?: QueryConfig<PaginatedResponse<Trader>, ReturnType<typeof queryKeys.traders>>) => {
  return useQuery({
    queryKey: queryKeys.traders(params),
    queryFn: () => traderAdminService.getTraders(params),
    ...options,
  });
};

export const useTrader = (id: string, options?: QueryConfig<Trader, ReturnType<typeof queryKeys.trader>>) => {
  return useQuery({
    queryKey: queryKeys.trader(id),
    queryFn: () => traderAdminService.getTraderDetails(id),
    enabled: !!id,
    ...options,
  });
};

export const useCreateTrader = (options?: UseMutationOptions<Trader, Error, CreateTraderData>) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: CreateTraderData) => traderAdminService.createTrader(data),
    onSuccess: (data, variables, context) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.traders() });
      options?.onSuccess?.(data, variables, context);
    },
    ...options,
  });
};

export const useUpdateTrader = (id: string, options?: UseMutationOptions<Trader, Error, UpdateTraderData>) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: UpdateTraderData) => traderAdminService.updateTrader(id, data),
    onSuccess: (data, variables, context) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.traders() });
      queryClient.invalidateQueries({ queryKey: queryKeys.trader(id) });
      options?.onSuccess?.(data, variables, context);
    },
    ...options,
  });
};

export const useUpdateTraderStatus = (id: string, options?: UseMutationOptions<Trader, Error, { status: TraderStatus; reason?: string }>) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ status, reason }) => traderAdminService.updateTraderStatus(id, status, reason),
    onSuccess: (data, variables, context) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.traders() });
      queryClient.invalidateQueries({ queryKey: queryKeys.trader(id) });
      options?.onSuccess?.(data, variables, context);
    },
    ...options,
  });
};

// Assignment Hooks
export const useMyAssignments = (status?: AssignmentStatus | AssignmentStatus[], options?: QueryConfig<Assignment[], ReturnType<typeof queryKeys.myAssignments>>) => {
  return useQuery({
    queryKey: queryKeys.myAssignments(status),
    queryFn: () => traderService.getMyAssignments(status),
    ...options,
  });
};

export const useAssignment = (id: string, options?: QueryConfig<Assignment, ReturnType<typeof queryKeys.assignment>>) => {
  return useQuery({
    queryKey: queryKeys.assignment(id),
    queryFn: () => traderService.getAssignment(id),
    enabled: !!id,
    ...options,
  });
};

export const useAllAssignments = (params: GetAssignmentsParams = {}, options?: QueryConfig<PaginatedResponse<Assignment>, ReturnType<typeof queryKeys.allAssignments>>) => {
  return useQuery({
    queryKey: queryKeys.allAssignments(params),
    queryFn: () => traderService.getAllAssignments(params),
    ...options,
  });
};

export const useCreateAssignment = (options?: UseMutationOptions<Assignment, Error, CreateAssignmentData>) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: CreateAssignmentData) => traderService.createAssignment(data),
    onSuccess: (data, variables, context) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.allAssignments() });
      queryClient.invalidateQueries({ queryKey: queryKeys.myAssignments() });
      options?.onSuccess?.(data, variables, context);
    },
    ...options,
  });
};

export const useUpdateAssignment = (id: string, options?: UseMutationOptions<Assignment, Error, UpdateAssignmentData>) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: UpdateAssignmentData) => traderService.updateAssignment(id, data),
    onSuccess: (data, variables, context) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.allAssignments() });
      queryClient.invalidateQueries({ queryKey: queryKeys.myAssignments() });
      queryClient.invalidateQueries({ queryKey: queryKeys.assignment(id) });
      options?.onSuccess?.(data, variables, context);
    },
    ...options,
  });
};

export const useUpdateAssignmentStatus = (id: string, options?: UseMutationOptions<Assignment, Error, { status: AssignmentStatus; notes?: string }>) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ status, notes }) => traderService.updateAssignmentStatus(id, status, notes),
    onSuccess: (data, variables, context) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.allAssignments() });
      queryClient.invalidateQueries({ queryKey: queryKeys.myAssignments() });
      queryClient.invalidateQueries({ queryKey: queryKeys.assignment(id) });
      options?.onSuccess?.(data, variables, context);
    },
    ...options,
  });
};

export const useDeleteAssignment = (options?: UseMutationOptions<{ id: string }, Error, string>) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => traderService.deleteAssignment(id),
    onSuccess: (data, variables, context) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.allAssignments() });
      queryClient.invalidateQueries({ queryKey: queryKeys.myAssignments() });
      queryClient.removeQueries({ queryKey: queryKeys.assignment(variables) });
      options?.onSuccess?.(data, variables, context);
    },
    ...options,
  });
};

export const useBulkAssignTraders = (options?: UseMutationOptions<{ success: boolean; message?: string }, Error, BulkAssignTradersData>) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: BulkAssignTradersData) => traderService.bulkAssignTraders(data),
    onSuccess: (data, variables, context) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.allAssignments() });
      queryClient.invalidateQueries({ queryKey: queryKeys.myAssignments() });
      options?.onSuccess?.(data, variables, context);
    },
    ...options,
  });
};

// Merchant Hooks
export const useMerchants = (options?: QueryConfig<Merchant[], readonly ["merchants"]>) => {
  return useQuery({
    queryKey: queryKeys.merchants,
    queryFn: () => traderService.getMerchants(),
    ...options,
  });
};

// Performance Hooks
export const usePerformanceMetrics = (
  params: { startDate?: string; endDate?: string; merchantId?: string } = {},
  options?: QueryConfig<PerformanceMetrics, ReturnType<typeof queryKeys.performance>>
) => {
  return useQuery({
    queryKey: queryKeys.performance(params),
    queryFn: () => traderService.getPerformanceMetrics(params),
    ...options,
  });
};

// Dashboard Hooks
export const useDashboardStats = (options?: QueryConfig<DashboardStats, readonly ["dashboard", "stats"]>) => {
  return useQuery({
    queryKey: queryKeys.dashboardStats,
    queryFn: () => traderService.getDashboardStats(),
    // Consider a shorter stale time for dashboard data
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

// Report Generation
export const useGenerateAssignmentReport = (options?: UseMutationOptions<Blob, Error, GenerateReportParams>) => {
  return useMutation({
    mutationFn: (params: GenerateReportParams) => traderService.generateAssignmentReport(params),
    ...options,
  });
};
