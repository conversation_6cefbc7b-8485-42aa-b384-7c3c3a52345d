import { useQuery } from '@tanstack/react-query';
import { formatDistanceToNow } from 'date-fns';
import { Loader2, AlertCircle, CheckCircle2, ShieldAlert, LogIn, LogOut } from 'lucide-react';

type ActivityLog = {
  id: string;
  action: string;
  description: string;
  timestamp: string;
  status: 'success' | 'failed' | 'warning';
  ip?: string;
  userAgent?: string;
};

// Mock API service function - replace with actual implementation
const fetchActivityLogs = async (): Promise<ActivityLog[]> => {
  // Replace with actual API call
  return [
    {
      id: '1',
      action: 'login',
      description: 'Successful login from *********** (Chrome, Windows)',
      timestamp: new Date().toISOString(),
      status: 'success' as const,
      ip: '***********',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    },
    {
      id: '2',
      action: 'password_change',
      description: 'Password changed',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
      status: 'success' as const,
    },
    {
      id: '3',
      action: 'login_attempt',
      description: 'Failed login <NAME_EMAIL>',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(),
      status: 'failed' as const,
      ip: '********',
    },
    {
      id: '4',
      action: '2fa_enabled',
      description: 'Two-factor authentication enabled',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2).toISOString(),
      status: 'success' as const,
    },
  ];
};



const getActionIcon = (action: string) => {
  switch (action) {
    case 'login':
      return <LogIn className="h-4 w-4" />;
    case 'logout':
      return <LogOut className="h-4 w-4" />;
    case 'password_change':
    case '2fa_enabled':
      return <ShieldAlert className="h-4 w-4" />;
    default:
      return <AlertCircle className="h-4 w-4" />;
  }
};

export function ActivityLog() {
  const { data: activityLogs, isLoading, error } = useQuery<ActivityLog[]>({
    queryKey: ['activityLogs'],
    queryFn: fetchActivityLogs,
  });
  
  const logs = activityLogs || [];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle2 className="h-4 w-4" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-32">
        <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-md bg-red-50 p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <AlertCircle className="h-5 w-5 text-red-400" aria-hidden="true" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">
              Failed to load activity logs
            </h3>
            <div className="mt-2 text-sm text-red-700">
              <p>Unable to load your activity logs at this time. Please try again later.</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (logs.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-muted-foreground">No activity logs found.</p>
      </div>
    );
  }

  return (
    <div className="flow-root">
      <ul role="list" className="-mb-8">
        {logs.map((log: ActivityLog, logIdx: number) => (
          <li key={log.id}>
            <div className="relative pb-8">
              {logIdx !== logs.length - 1 ? (
                <span
                  className="absolute left-4 top-4 -ml-px h-full w-0.5 bg-gray-200"
                  aria-hidden="true"
                />
              ) : null}
              <div className="relative flex space-x-3">
                <div>
                  <span
                    className={`h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white ${
                      log.status === 'success' ? 'bg-green-100 text-green-700' :
                      log.status === 'failed' ? 'bg-red-100 text-red-700' :
                      'bg-yellow-100 text-yellow-700'
                    }`}
                  >
                    {getActionIcon(log.action)}
                  </span>
                </div>
                <div className="flex min-w-0 flex-1 justify-between space-x-4 pt-1.5">
                  <div>
                    <p className="text-sm text-gray-800">
                      {log.description}
                      {log.ip && (
                        <span className="text-xs text-muted-foreground ml-1">
                          ({log.ip})
                        </span>
                      )}
                    </p>
                    {log.userAgent && (
                      <p className="text-xs text-muted-foreground mt-1">
                        {log.userAgent}
                      </p>
                    )}
                  </div>
                  <div className="whitespace-nowrap text-right text-sm text-muted-foreground">
                    <time dateTime={log.timestamp}>
                      {formatDistanceToNow(new Date(log.timestamp), { addSuffix: true })}
                    </time>
                    <div className="mt-1">
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          log.status === 'success' ? 'bg-green-100 text-green-800' :
                          log.status === 'failed' ? 'bg-red-100 text-red-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}
                      >
                        {getStatusIcon(log.status)}
                        <span className="ml-1 capitalize">
                          {log.status}
                        </span>
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </li>
        ))}
      </ul>
    </div>
  );
}
