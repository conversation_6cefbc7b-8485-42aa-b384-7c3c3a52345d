{"name": "payment-gateway-tests", "version": "1.0.0", "description": "Test suite for Payment Gateway API", "main": "test-api.js", "scripts": {"test": "node test-api.js", "start-mock": "node mock-server.js", "test:ci": "npm test -- --ci"}, "dependencies": {"axios": "^1.6.2", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "chalk": "^4.1.2", "cors": "^2.8.5", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "uuid": "^9.0.0"}, "engines": {"node": ">=14.0.0"}}