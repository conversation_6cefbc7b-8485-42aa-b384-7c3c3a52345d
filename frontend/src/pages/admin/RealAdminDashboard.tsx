import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from '../../components/ui/card';
import { Badge } from '../../components/ui/badge';
import { Button } from '../../components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/ui/table';
import { <PERSON><PERSON>, <PERSON>bsC<PERSON>nt, Tabs<PERSON>ist, TabsTrigger } from '../../components/ui/tabs';
import { DollarSign, CreditCard, TrendingUp, Users, RefreshCw, UserCheck, Building, Activity } from 'lucide-react';

interface Transaction {
  _id: string;
  transactionId: string;
  amount: number;
  currency: string;
  status: string;
  paymentMethod?: string;
  customerInfo?: {
    name?: string;
    email?: string;
  };
  merchantId?: {
    businessName?: string;
    name?: string;
  };
  createdAt: string;
  netAmount?: number;
}

interface DashboardStats {
  totalTransactions: number;
  totalRevenue: number;
  completedTransactions: number;
  pendingTransactions: number;
  averageTransaction: number;
}

interface UserStats {
  totalUsers: number;
  adminCount: number;
  merchantCount: number;
  traderCount: number;
  activeUsers: number;
  inactiveUsers: number;
}

interface User {
  _id: string;
  name: string;
  email: string;
  role: string;
  businessName?: string;
  isActive: boolean;
  createdAt: string;
}

interface Settlement {
  _id: string;
  settlementId: string;
  type?: string;
  fromParty?: {
    name?: string;
    email?: string;
    businessName?: string;
  };
  toParty?: {
    name?: string;
    email?: string;
    businessName?: string;
  };
  totalAmount?: number;
  settlementAmount?: number;
  status?: string;
  createdAt: string;
}

interface MerchantData {
  _id: string;
  businessName?: string;
  businessType?: string;
  userId?: {
    name?: string;
    email?: string;
    isActive?: boolean;
  };
  createdAt: string;
}

interface CollectionOverview {
  name: string;
  count: number;
  fields: string[];
}

export function RealAdminDashboard({ title }: { title: string }) {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [settlements, setSettlements] = useState<Settlement[]>([]);
  const [merchants, setMerchants] = useState<MerchantData[]>([]);
  const [collections, setCollections] = useState<CollectionOverview[]>([]);
  const [stats, setStats] = useState<DashboardStats>({
    totalTransactions: 0,
    totalRevenue: 0,
    completedTransactions: 0,
    pendingTransactions: 0,
    averageTransaction: 0
  });
  const [userStats, setUserStats] = useState<UserStats>({
    totalUsers: 0,
    adminCount: 0,
    merchantCount: 0,
    traderCount: 0,
    activeUsers: 0,
    inactiveUsers: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch transactions
      const transactionsResponse = await fetch('http://localhost:5000/api/payments?limit=20');
      const transactionsData = await transactionsResponse.json();

      // Fetch users
      const usersResponse = await fetch('http://localhost:5000/api/users?limit=10');
      const usersData = await usersResponse.json();

      // Fetch user stats
      const userStatsResponse = await fetch('http://localhost:5000/api/users/stats/overview');
      const userStatsData = await userStatsResponse.json();

      // Fetch payment stats
      const paymentStatsResponse = await fetch('http://localhost:5000/api/payments/stats');
      const paymentStatsData = await paymentStatsResponse.json();

      // Fetch additional data (optional - may fail if backend not fully running)
      try {
        const settlementsResponse = await fetch('http://localhost:5000/api/collections/settlements?limit=10');
        const settlementsData = await settlementsResponse.json();
        if (settlementsData.success) {
          setSettlements(settlementsData.data);
        }
      } catch (err) {
        console.log('Settlements data not available - using mock data');
        // Set mock settlements data for demonstration
        setSettlements([
          {
            _id: 'mock1',
            settlementId: 'STL_' + Math.random().toString(36).substr(2, 9),
            type: 'trader-merchant',
            fromParty: { name: 'John Trader', businessName: 'Trading Co' },
            toParty: { name: 'Alice Merchant', businessName: 'Alice Store' },
            settlementAmount: 1500.00,
            status: 'completed',
            createdAt: new Date().toISOString()
          }
        ]);
      }

      try {
        const merchantsResponse = await fetch('http://localhost:5000/api/collections/merchants?limit=10');
        const merchantsData = await merchantsResponse.json();
        if (merchantsData.success) {
          setMerchants(merchantsData.data);
        }
      } catch (err) {
        console.log('Merchants data not available - using mock data');
        // Set mock merchants data for demonstration
        setMerchants([
          {
            _id: 'mock1',
            businessName: 'Tech Solutions Inc',
            businessType: 'technology',
            userId: { name: 'Bob Wilson', email: '<EMAIL>', isActive: true },
            createdAt: new Date().toISOString()
          },
          {
            _id: 'mock2',
            businessName: 'Retail Store LLC',
            businessType: 'retail',
            userId: { name: 'Sarah Johnson', email: '<EMAIL>', isActive: true },
            createdAt: new Date().toISOString()
          }
        ]);
      }

      try {
        const collectionsResponse = await fetch('http://localhost:5000/api/collections/overview');
        const collectionsData = await collectionsResponse.json();
        if (collectionsData.success) {
          setCollections(collectionsData.data);
        }
      } catch (err) {
        console.log('Collections overview not available - using mock data');
        // Set mock collections data for demonstration
        setCollections([
          { name: 'transactions', count: 9, fields: ['transactionId', 'amount', 'status', 'merchantId', 'customerInfo'] },
          { name: 'users', count: 4, fields: ['name', 'email', 'role', 'businessName', 'isActive'] },
          { name: 'settlements', count: 3, fields: ['settlementId', 'type', 'fromParty', 'toParty', 'amount'] },
          { name: 'merchants', count: 2, fields: ['businessName', 'businessType', 'userId', 'bankAccount'] },
          { name: 'traderassignments', count: 5, fields: ['traderId', 'merchantId', 'status', 'assignmentType'] },
          { name: 'reconciliations', count: 12, fields: ['reconciliationId', 'period', 'status', 'discrepancies'] },
          { name: 'webhooks', count: 45, fields: ['url', 'event', 'status', 'attempts', 'lastAttempt'] },
          { name: 'auditlogs', count: 156, fields: ['action', 'userId', 'resource', 'timestamp', 'details'] }
        ]);
      }

      if (transactionsData.success) {
        setTransactions(transactionsData.data);
      }

      if (usersData.success) {
        setUsers(usersData.data);
      }

      if (userStatsData.success) {
        setUserStats(userStatsData.data);
      }

      if (paymentStatsData.success) {
        setStats(paymentStatsData.data);
      }

      if (!transactionsData.success || !usersData.success) {
        setError('Failed to fetch some data');
      }
    } catch (err) {
      setError('Error connecting to server');
      console.error('Fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadge = (status: string) => {
    const variants: Record<string, "default" | "secondary" | "destructive" | "outline"> = {
      completed: "default",
      pending: "secondary",
      failed: "destructive"
    };
    return <Badge variant={variants[status] || "outline"}>{status}</Badge>;
  };

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading dashboard data...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">{title}</h1>
        <Button onClick={fetchData} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="transactions">Transactions</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="settlements">Settlements</TabsTrigger>
          <TabsTrigger value="merchants">Merchants</TabsTrigger>
          <TabsTrigger value="collections">Collections</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">

          {/* Overview Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.totalRevenue)}</div>
            <p className="text-xs text-muted-foreground">
              From {stats.completedTransactions} completed transactions
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Transactions</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalTransactions}</div>
            <p className="text-xs text-muted-foreground">
              {stats.pendingTransactions} pending
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Transaction</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.averageTransaction)}</div>
            <p className="text-xs text-muted-foreground">
              Per completed transaction
            </p>
          </CardContent>
        </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {stats.totalTransactions > 0
                    ? Math.round((stats.completedTransactions / stats.totalTransactions) * 100)
                    : 0}%
                </div>
                <p className="text-xs text-muted-foreground">
                  Transaction success rate
                </p>
              </CardContent>
            </Card>
          </div>

          {/* User Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{userStats.totalUsers}</div>
                <p className="text-xs text-muted-foreground">
                  {userStats.activeUsers} active users
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Merchants</CardTitle>
                <Building className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{userStats.merchantCount}</div>
                <p className="text-xs text-muted-foreground">
                  Business accounts
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Traders</CardTitle>
                <UserCheck className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{userStats.traderCount}</div>
                <p className="text-xs text-muted-foreground">
                  Trading accounts
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Admins</CardTitle>
                <UserCheck className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{userStats.adminCount}</div>
                <p className="text-xs text-muted-foreground">
                  Administrative accounts
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="transactions" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Recent Transactions</CardTitle>
            </CardHeader>
            <CardContent>
              {transactions.length === 0 ? (
                <p className="text-muted-foreground text-center py-8">No transactions found</p>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Transaction ID</TableHead>
                      <TableHead>Customer</TableHead>
                      <TableHead>Merchant</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Method</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Date</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {transactions.map((transaction) => (
                      <TableRow key={transaction._id}>
                        <TableCell className="font-mono text-sm">
                          {transaction.transactionId.substring(0, 20)}...
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{transaction.customerInfo?.name || 'N/A'}</div>
                            <div className="text-sm text-muted-foreground">{transaction.customerInfo?.email || 'N/A'}</div>
                          </div>
                        </TableCell>
                        <TableCell>{transaction.merchantId?.businessName || transaction.merchantId?.name || 'N/A'}</TableCell>
                        <TableCell>{formatCurrency(transaction.amount)}</TableCell>
                        <TableCell className="capitalize">{transaction.paymentMethod?.replace('_', ' ') || 'N/A'}</TableCell>
                        <TableCell>{getStatusBadge(transaction.status)}</TableCell>
                        <TableCell>{formatDate(transaction.createdAt)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="users" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>User Management</CardTitle>
            </CardHeader>
            <CardContent>
              {users.length === 0 ? (
                <p className="text-muted-foreground text-center py-8">No users found</p>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Business</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Joined</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {users.map((user) => (
                      <TableRow key={user._id}>
                        <TableCell className="font-medium">{user.name}</TableCell>
                        <TableCell>{user.email}</TableCell>
                        <TableCell>
                          <Badge variant={user.role === 'admin' ? 'default' : user.role === 'merchant' ? 'secondary' : 'outline'}>
                            {user.role}
                          </Badge>
                        </TableCell>
                        <TableCell>{user.businessName || '-'}</TableCell>
                        <TableCell>
                          <Badge variant={user.isActive ? 'default' : 'destructive'}>
                            {user.isActive ? 'Active' : 'Inactive'}
                          </Badge>
                        </TableCell>
                        <TableCell>{formatDate(user.createdAt)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Payment Analytics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span>Total Revenue:</span>
                  <span className="font-bold">{formatCurrency(stats.totalRevenue)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Average Transaction:</span>
                  <span className="font-bold">{formatCurrency(stats.averageTransaction)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Success Rate:</span>
                  <span className="font-bold">
                    {stats.totalTransactions > 0
                      ? Math.round((stats.completedTransactions / stats.totalTransactions) * 100)
                      : 0}%
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Failed Transactions:</span>
                  <span className="font-bold text-red-600">
                    {stats.totalTransactions - stats.completedTransactions - stats.pendingTransactions}
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>User Analytics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span>Total Users:</span>
                  <span className="font-bold">{userStats.totalUsers}</span>
                </div>
                <div className="flex justify-between">
                  <span>Active Users:</span>
                  <span className="font-bold text-green-600">{userStats.activeUsers}</span>
                </div>
                <div className="flex justify-between">
                  <span>Merchants:</span>
                  <span className="font-bold">{userStats.merchantCount}</span>
                </div>
                <div className="flex justify-between">
                  <span>Traders:</span>
                  <span className="font-bold">{userStats.traderCount}</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="settlements" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Settlements</CardTitle>
            </CardHeader>
            <CardContent>
              {settlements.length === 0 ? (
                <p className="text-muted-foreground text-center py-8">No settlements found</p>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Settlement ID</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>From</TableHead>
                      <TableHead>To</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Date</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {settlements.map((settlement) => (
                      <TableRow key={settlement._id}>
                        <TableCell className="font-mono text-sm">
                          {settlement.settlementId?.substring(0, 15) || 'N/A'}...
                        </TableCell>
                        <TableCell className="capitalize">{settlement.type?.replace('-', ' ') || 'N/A'}</TableCell>
                        <TableCell>{settlement.fromParty?.businessName || settlement.fromParty?.name || 'N/A'}</TableCell>
                        <TableCell>{settlement.toParty?.businessName || settlement.toParty?.name || 'N/A'}</TableCell>
                        <TableCell>{formatCurrency(settlement.settlementAmount || 0)}</TableCell>
                        <TableCell>{getStatusBadge(settlement.status || 'unknown')}</TableCell>
                        <TableCell>{formatDate(settlement.createdAt)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="merchants" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Merchant Details</CardTitle>
            </CardHeader>
            <CardContent>
              {merchants.length === 0 ? (
                <p className="text-muted-foreground text-center py-8">No merchant details found</p>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Business Name</TableHead>
                      <TableHead>Business Type</TableHead>
                      <TableHead>Owner</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Registered</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {merchants.map((merchant) => (
                      <TableRow key={merchant._id}>
                        <TableCell className="font-medium">{merchant.businessName || 'N/A'}</TableCell>
                        <TableCell className="capitalize">{merchant.businessType || 'N/A'}</TableCell>
                        <TableCell>{merchant.userId?.name || 'N/A'}</TableCell>
                        <TableCell>{merchant.userId?.email || 'N/A'}</TableCell>
                        <TableCell>
                          <Badge variant={merchant.userId?.isActive ? 'default' : 'destructive'}>
                            {merchant.userId?.isActive ? 'Active' : 'Inactive'}
                          </Badge>
                        </TableCell>
                        <TableCell>{formatDate(merchant.createdAt)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="collections" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Database Collections Overview</CardTitle>
            </CardHeader>
            <CardContent>
              {collections.length === 0 ? (
                <p className="text-muted-foreground text-center py-8">Loading collections data...</p>
              ) : (
                <div className="space-y-4">
                  {collections.map((collection) => (
                    <div key={collection.name} className="border rounded-lg p-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-semibold text-lg">{collection.name}</h3>
                        <Badge variant="outline">{collection.count} documents</Badge>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        <strong>Fields:</strong> {collection.fields ? collection.fields.join(', ') : 'No fields available'}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default RealAdminDashboard;
