const express = require('express');
const mongoose = require('mongoose');
const path = require('path');
const { createServer } = require('http');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const mongoSanitize = require('express-mongo-sanitize');
const xss = require('xss-clean');
const hpp = require('hpp');
const cookieParser = require('cookie-parser');
const morgan = require('morgan');
const session = require('express-session');
const MongoStore = require('connect-mongo');

// Import config
const { setupSocket } = require('./config/socket');
const { setupSwagger } = require('./config/swagger');
const { securityHeaders, preventNoSqlInjection } = require('./middleware/security');
const { errorHandler } = require('./middleware/errorHandler');
const logger = require('./utils/logger');

// Load environment variables
const envPath = process.env.NODE_ENV === 'test' 
  ? path.join(__dirname, '../.env.test')
  : path.join(__dirname, '../.env');
require('dotenv').config({ path: envPath });

// Ensure required environment variables are set
const requiredEnvVars = [
  'NODE_ENV',
  'MONGODB_URI',
  'COOKIE_SECRET',
  'FRONTEND_URL',
  'API_VERSION'
];

// Check for missing environment variables
const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
if (missingVars.length > 0) {
  const errorMsg = `Missing required environment variables: ${missingVars.join(', ')}`;
  logger.error(errorMsg);
  
  // Don't exit during tests - let the test framework handle the failure
  if (process.env.NODE_ENV !== 'test') {
    process.exit(1);
  } else {
    console.warn('Test environment - continuing despite missing vars:', errorMsg);
  }
}

// Import API routes
const authRoutes = require('./routes/auth');
const settlementRoutes = require('./routes/settlementRoutes');
const reserveStrategyRoutes = require('./routes/reserveStrategyRoutes');
const commissionRoutes = require('./routes/commissionRoutes');
const adminRoutes = require('./routes/admin');

/**
 * Create and configure the Express application
 * @returns {Object} Object containing the Express app and HTTP server
 */
const createApp = () => {
  // Initialize express app
  const app = express();
  const server = createServer(app);

  // ====================================
  // Security Middleware
  // ====================================
  
  // Set security HTTP headers
  app.use(helmet());
  
  // Enable CORS with security configuration
  const corsOptions = {
    origin: (origin, callback) => {
      const allowedOrigins = process.env.FRONTEND_URL 
        ? process.env.FRONTEND_URL.split(',').map(url => url.trim())
        : ['http://localhost:3000'];
      
      // Allow requests with no origin (like mobile apps or curl requests)
      if (!origin) return callback(null, true);
      
      if (allowedOrigins.indexOf(origin) === -1) {
        const msg = `CORS policy does not allow access from: ${origin}`;
        logger.warn(msg);
        return callback(new Error(msg), false);
      }
      return callback(null, true);
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    exposedHeaders: ['Set-Cookie', 'Date', 'ETag'],
    maxAge: 600 // 10 minutes
  };
  app.use(cors(corsOptions));
  
  // Body parser, reading data from body into req.body
  app.use(express.json({ limit: '10kb' }));
  app.use(express.urlencoded({ extended: true, limit: '10kb' }));
  
  // Cookie parser
  app.use(cookieParser(process.env.COOKIE_SECRET, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: 24 * 60 * 60 * 1000 // 24 hours
  }));
  
  // Session configuration
  const sessionConfig = {
    secret: process.env.SESSION_SECRET || process.env.COOKIE_SECRET || 'your-secret-key',
    resave: false,
    saveUninitialized: false,
    store: MongoStore.create({
      mongoUrl: process.env.MONGODB_URI,
      ttl: 7 * 24 * 60 * 60, // 7 days
      autoRemove: 'interval',
      autoRemoveInterval: 60 // Remove expired sessions every 60 minutes
    }),
    cookie: {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
    }
  };
  
  if (app.get('env') === 'production') {
    app.set('trust proxy', 1); // Trust first proxy
    sessionConfig.cookie.secure = true; // Serve secure cookies
  }
  
  app.use(session(sessionConfig));
  
  // Data sanitization against NoSQL query injection
  app.use(mongoSanitize());
  
  // Data sanitization against XSS
  app.use(xss());
  
  // Prevent parameter pollution
  app.use(hpp({
    whitelist: [
      'duration', 'ratingsQuantity', 'ratingsAverage', 'maxGroupSize', 'difficulty', 'price'
    ]
  }));
  
  // Prevent NoSQL injection
  app.use(preventNoSqlInjection);
  
  // Security headers (additional)
  app.use(securityHeaders);
  
  // ====================================
  // Development Logging
  // ====================================
  if (process.env.NODE_ENV === 'development') {
    app.use(morgan('dev', {
      skip: (req) => req.originalUrl.startsWith('/health')
    }));
  }
  
  // ====================================
  // API Routes
  // ====================================
  
  // Health check endpoint
  app.get('/health', (req, res) => {
    try {
      const status = {
        status: 'OK',
        version: process.env.npm_package_version,
        node: process.version,
        environment: process.env.NODE_ENV,
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        database: mongoose.connection.readyState === 1 ? 'connected' : 'disconnected'
      };
      
      return res.status(200).json(status);
    } catch (error) {
      logger.error('Health check failed:', error);
      return res.status(500).json({
        status: 'error',
        message: 'Health check failed',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  });

  // API Documentation
  setupSwagger(app);
  
  // Mount API routes
  if (process.env.NODE_ENV === 'development') {
    // In development, use the development auth middleware that bypasses login
    const { auth: devAuth } = require('./middleware/devAuth');
    
    // Apply dev auth to all routes except public auth endpoints
    app.use((req, res, next) => {
      // Skip auth for public routes
      if (req.path === '/api/auth/login' || 
          req.path === '/api/auth/register' ||
          req.path.startsWith('/api/auth/verify-email') ||
          req.path === '/api/auth/forgot-password' ||
          req.path.startsWith('/api/auth/reset-password')) {
        return next();
      }
      // Use dev auth for all other routes
      devAuth(req, res, next);
    });
    
    // Mount auth routes after applying dev auth
    app.use('/api/auth', authRoutes);
  } else {
    // In production, use the regular auth middleware
    app.use('/api/auth', authRoutes);
  }
  
  app.use('/api/admin', adminRoutes);
  app.use('/api/settlements', settlementRoutes);
  app.use('/api/reserve-strategies', reserveStrategyRoutes);
  app.use('/api/commissions', commissionRoutes);
  
  // Health check endpoint
  app.get('/api/health', (req, res) => {
    res.status(200).json({
      status: 'ok',
      timestamp: new Date().toISOString()
    });
  });
  
  // Handle 404 for undefined routes
  app.use('/api', (req, res) => {
    res.status(404).json({
      success: false,
      error: {
        code: 'NOT_FOUND',
        message: 'The requested resource was not found',
        details: {
          method: req.method,
          path: req.originalUrl,
          timestamp: new Date().toISOString()
        }
      }
    });
  });
  
  // Serve static files in production
  if (process.env.NODE_ENV === 'production') {
    app.use(express.static(path.join(__dirname, '../frontend/build')));
    
    // Handle React routing, return all requests to React app
    app.get('*', (req, res) => {
      res.sendFile(path.join(__dirname, '../frontend/build/index.html'));
    });
  } else {
    // In development, redirect to API docs
    app.get('/', (req, res) => {
      res.redirect('/api-docs');
    });
  }
  
  // ====================================
  // Error Handling
  // ====================================
  
  // Handle 404 - Must be after all other routes
  app.all('*', (req, res, next) => {
    next(new ApiError(`Can't find ${req.originalUrl} on this server!`, 404));
  });
  
  // Global error handler
  app.use(errorHandler);
  
  // ====================================
  // WebSocket Setup
  // ====================================
  setupSocket(server);
  
  return { app, server };
};

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  // In production, you might want to restart the server here
  if (process.env.NODE_ENV === 'production') {
    process.exit(1);
  }
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  // In production, you might want to restart the server here
  if (process.env.NODE_ENV === 'production') {
    process.exit(1);
  }
});

// Export the createApp function and connectDB
const db = require('./config/db');

// For testing purposes, create and export the app and server directly
const { app, server } = createApp();

// Export for testing
module.exports = {
  app,
  server,
  createApp,
  connectDB: db.connectDB
};
