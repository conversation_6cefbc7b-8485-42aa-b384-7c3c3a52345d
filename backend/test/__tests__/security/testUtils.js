/**
 * Creates a mock Express request object
 */
const createMockRequest = (overrides = {}) => {
  const req = {
    params: {},
    query: {},
    body: {},
    headers: {},
    user: null,
    ...overrides
  };
  return req;
};

/**
 * Creates a mock Express response object with Jest spies
 */
const createMockResponse = () => {
  const res = {};
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  res.send = jest.fn().mockReturnValue(res);
  return res;
};

/**
 * Creates a mock Express next function
 */
const createNextFunction = () => {
  return jest.fn();
};

/**
 * Generates a JWT token for testing
 */
const generateTestToken = (user) => {
  // This is a simplified version - in a real app, use your actual JWT implementation
  return `test-token-${user._id}-${Date.now()}`;
};

/**
 * Waits for a specified number of milliseconds
 */
const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));

module.exports = {
  createMockRequest,
  createMockResponse,
  createNextFunction,
  generateTestToken,
  wait
};
