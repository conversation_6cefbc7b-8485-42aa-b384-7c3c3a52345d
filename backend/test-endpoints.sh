#!/bin/bash

# Payment Gateway API Endpoint Testing Script
# Base URL for all API calls
BASE_URL="http://localhost:5000/api"

echo "🧪 PAYMENT GATEWAY API ENDPOINT TESTING"
echo "========================================"
echo "Base URL: $BASE_URL"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to test endpoint
test_endpoint() {
    local method=$1
    local endpoint=$2
    local description=$3
    local data=$4
    
    echo -e "${BLUE}Testing:${NC} $method $endpoint"
    echo -e "${YELLOW}Description:${NC} $description"
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "\n%{http_code}" "$BASE_URL$endpoint")
    elif [ "$method" = "POST" ]; then
        response=$(curl -s -w "\n%{http_code}" -X POST -H "Content-Type: application/json" -d "$data" "$BASE_URL$endpoint")
    elif [ "$method" = "PUT" ]; then
        response=$(curl -s -w "\n%{http_code}" -X PUT -H "Content-Type: application/json" -d "$data" "$BASE_URL$endpoint")
    elif [ "$method" = "DELETE" ]; then
        response=$(curl -s -w "\n%{http_code}" -X DELETE "$BASE_URL$endpoint")
    fi
    
    # Extract HTTP status code (last line)
    http_code=$(echo "$response" | tail -n1)
    # Extract response body (all but last line)
    body=$(echo "$response" | head -n -1)
    
    if [ "$http_code" -ge 200 ] && [ "$http_code" -lt 300 ]; then
        echo -e "${GREEN}✅ SUCCESS${NC} (HTTP $http_code)"
        echo "$body" | jq '.' 2>/dev/null || echo "$body"
    elif [ "$http_code" -ge 400 ] && [ "$http_code" -lt 500 ]; then
        echo -e "${YELLOW}⚠️  CLIENT ERROR${NC} (HTTP $http_code)"
        echo "$body" | jq '.' 2>/dev/null || echo "$body"
    elif [ "$http_code" -ge 500 ]; then
        echo -e "${RED}❌ SERVER ERROR${NC} (HTTP $http_code)"
        echo "$body"
    else
        echo -e "${RED}❌ CONNECTION ERROR${NC}"
        echo "Could not connect to server"
    fi
    
    echo ""
    echo "----------------------------------------"
    echo ""
}

echo "🏥 HEALTH CHECK"
echo "==============="
test_endpoint "GET" "/health" "Server health check"

echo "📋 MENU ENDPOINTS"
echo "=================="
test_endpoint "GET" "/menus" "Get all menus"
test_endpoint "GET" "/menus/role/admin" "Get admin menus"
test_endpoint "GET" "/menus/role/merchant" "Get merchant menus"
test_endpoint "GET" "/menus/role/trader" "Get trader menus"

echo "👥 USER ENDPOINTS"
echo "=================="
test_endpoint "GET" "/users" "Get all users"
test_endpoint "GET" "/users/role/admin" "Get admin users"
test_endpoint "GET" "/users/role/merchant" "Get merchant users"
test_endpoint "GET" "/users/role/trader" "Get trader users"

echo "📋 TRADER ASSIGNMENT ENDPOINTS"
echo "==============================="
test_endpoint "GET" "/trader-assignments" "Get all trader assignments"
test_endpoint "GET" "/trader-assignments?page=1&limit=5" "Get trader assignments with pagination"

echo "💳 TRANSACTION ENDPOINTS"
echo "========================="
test_endpoint "GET" "/transactions" "Get all transactions"
test_endpoint "GET" "/transactions?status=completed" "Get completed transactions"

echo "💰 SETTLEMENT ENDPOINTS"
echo "========================"
test_endpoint "GET" "/settlements" "Get all settlements"

echo "🏪 MERCHANT ENDPOINTS"
echo "====================="
test_endpoint "GET" "/merchants" "Get all merchants"

echo "📊 COLLECTIONS ENDPOINTS"
echo "========================="
test_endpoint "GET" "/collections/overview" "Get collections overview"
test_endpoint "GET" "/collections/settlements" "Get settlements via collections API"
test_endpoint "GET" "/collections/merchants" "Get merchants via collections API"
test_endpoint "GET" "/collections/trader-assignments" "Get trader assignments via collections API"
test_endpoint "GET" "/collections/reconciliations" "Get reconciliations"
test_endpoint "GET" "/collections/reserve-activities" "Get reserve activities"
test_endpoint "GET" "/collections/commission-structures" "Get commission structures"

echo "🔧 ADMIN ENDPOINTS"
echo "=================="
test_endpoint "GET" "/admin/stats" "Get admin statistics"

echo "💼 COMMISSION ENDPOINTS"
echo "======================="
test_endpoint "GET" "/commissions" "Get commission structures"

echo "🏦 RESERVE STRATEGY ENDPOINTS"
echo "============================="
test_endpoint "GET" "/reserve-strategies" "Get reserve strategies"

echo ""
echo "🎯 ENDPOINT TESTING COMPLETED"
echo "=============================="
echo "Check the results above for any failed endpoints."
echo "Green ✅ = Working, Yellow ⚠️ = Client Error, Red ❌ = Server Error"
