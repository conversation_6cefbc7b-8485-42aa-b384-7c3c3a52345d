const mongoose = require("mongoose")
const bcrypt = require("bcryptjs")
const User = require("../models/User")
const Transaction = require("../models/Transaction")
require("dotenv").config()

async function seedDatabase() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || "mongodb://localhost:27017/paymentgateway")

    // Clear existing data
    await User.deleteMany({})
    await Transaction.deleteMany({})

    // Create admin user
    const adminUser = new User({
      name: "Admin User",
      email: "<EMAIL>",
      password: "Admin@123", // Will be hashed by pre-save hook
      role: "admin",
      isVerified: true,
      isActive: true,
      isEmailVerified: true,
      emailVerificationToken: undefined,
      emailVerificationExpires: undefined,
    })
    await adminUser.save()
    console.log('Admin user created:', adminUser.email)

    // Create sample merchants
    const merchant1 = new User({
      name: "<PERSON>",
      email: "<EMAIL>",
      password: "Merchant@123", // Will be hashed by pre-save hook
      role: "merchant",
      businessName: "Tech Solutions Inc",
      businessType: "Technology",
      phone: "+1234567890",
      website: "https://techsolutions.com",
      isVerified: true,
      isActive: true,
      isEmailVerified: true,
      emailVerificationToken: undefined,
      emailVerificationExpires: undefined,
    })
    merchant1.generateApiKey()
    await merchant1.save()

    const merchant2 = new User({
      name: "Jane Smith",
      email: "<EMAIL>",
      password: "Merchant@456", // Will be hashed by pre-save hook
      role: "merchant",
      businessName: "Fashion Store",
      businessType: "Retail",
      phone: "+1234567891",
      website: "https://fashionstore.com",
      isVerified: true,
      isActive: true,
      isEmailVerified: true,
      emailVerificationToken: undefined,
      emailVerificationExpires: undefined,
    })
    merchant2.generateApiKey()
    await merchant2.save()

    // Create sample trader
    const trader1 = new User({
      name: "Bob Wilson",
      email: "<EMAIL>",
      password: "Trader@123", // Will be hashed by pre-save hook
      role: "trader",
      isVerified: true,
      isActive: true,
      isEmailVerified: true,
      emailVerificationToken: undefined,
      emailVerificationExpires: undefined,
    })
    await trader1.save()

    // Create sample transactions
    const sampleTransactions = [
      {
        transactionId: Transaction.generateTransactionId(),
        merchantId: merchant1._id,
        traderId: trader1._id,
        amount: 99.99,
        currency: "USD",
        status: "completed",
        paymentMethod: "card",
        paymentDetails: {
          cardLast4: "4242",
          cardBrand: "Visa",
        },
        customerInfo: {
          email: "<EMAIL>",
          name: "Customer One",
          billingAddress: {
            street: "123 Main St",
            city: "New York",
            state: "NY",
            zipCode: "10001",
            country: "US",
          },
        },
        fees: {
          processingFee: 3.2,
          platformFee: 0.5,
        },
        riskScore: 25,
        settlementDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
      },
      {
        transactionId: Transaction.generateTransactionId(),
        merchantId: merchant2._id,
        amount: 249.99,
        currency: "USD",
        status: "completed",
        paymentMethod: "card",
        paymentDetails: {
          cardLast4: "1234",
          cardBrand: "Mastercard",
        },
        customerInfo: {
          email: "<EMAIL>",
          name: "Customer Two",
          billingAddress: {
            street: "456 Oak Ave",
            city: "Los Angeles",
            state: "CA",
            zipCode: "90210",
            country: "US",
          },
        },
        fees: {
          processingFee: 7.55,
          platformFee: 1.25,
        },
        riskScore: 15,
        settlementDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
      },
      {
        transactionId: Transaction.generateTransactionId(),
        merchantId: merchant1._id,
        amount: 49.99,
        currency: "USD",
        status: "failed",
        paymentMethod: "card",
        paymentDetails: {
          cardLast4: "0000",
          cardBrand: "Visa",
        },
        customerInfo: {
          email: "<EMAIL>",
          name: "Customer Three",
        },
        fees: {
          processingFee: 1.75,
          platformFee: 0.25,
        },
        riskScore: 85,
      },
    ]

    for (const transactionData of sampleTransactions) {
      const transaction = new Transaction(transactionData)
      transaction.calculateRiskScore()
      await transaction.save()
    }

    console.log("Database seeded successfully!")
    console.log("Admin credentials: <EMAIL> / admin123")
    console.log("Merchant 1 credentials: <EMAIL> / merchant123")
    console.log("Merchant 2 credentials: <EMAIL> / merchant123")
    console.log("Trader credentials: <EMAIL> / trader123")

    process.exit(0)
  } catch (error) {
    console.error("Seeding error:", error)
    process.exit(1)
  }
}

seedDatabase()
