import { useParams, useNavigate } from 'react-router-dom';
import { format, parseISO } from 'date-fns';
import { ArrowLeft, Edit, Trash2 } from 'lucide-react';
import { Button } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '../ui/card';
import { Badge } from '../ui/badge';
import { useTraderAssignments, useDeleteTraderAssignment } from '../../hooks/useTraderAssignments';
import { useToast } from '../ui/use-toast';
import { TraderAssignment, User, Merchant } from '../../types/trader';

type Status = 'active' | 'inactive' | 'suspended' | 'completed';

const statusVariant: Record<Status, 'default' | 'secondary' | 'destructive' | 'outline'> = {
  active: 'default',
  inactive: 'outline',
  suspended: 'destructive',
  completed: 'secondary',
};

export function TraderAssignmentDetail() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { assignments, isLoading, error } = useTraderAssignments({ traderId: id });
  const assignment = assignments?.[0];
  
  // Helper function to safely get user name
  const getUserName = (user: User | string | undefined): string => {
    if (!user) return 'Unknown';
    if (typeof user === 'string') return user;
    return user.name || user.email || 'Unknown';
  };
  
  // Helper to safely format dates
  const formatDate = (dateString: string | Date | undefined): string => {
    if (!dateString) return 'N/A';
    try {
      const date = typeof dateString === 'string' ? parseISO(dateString) : dateString;
      return format(date, 'PPpp');
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Invalid date';
    }
  };
  const deleteAssignment = useDeleteTraderAssignment();

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this assignment?') && id) {
      try {
        await deleteAssignment.mutateAsync(id);
        toast({
          title: 'Success',
          description: 'Assignment deleted successfully',
        });
        navigate('/admin/trader-assignments');
      } catch (err) {
        const error = err as Error & { response?: { data?: { message?: string } } };
        toast({
          title: 'Error',
          description: error.response?.data?.message || 'Failed to delete assignment',
          variant: 'destructive',
        });
      }
    }
  };

  if (isLoading) return <div>Loading assignment details...</div>;
  if (error) return <div>Error loading assignment: {error instanceof Error ? error.message : 'Unknown error'}</div>;
  if (!assignment) return <div>Assignment not found</div>;
  if (!assignment) return <div>Assignment not found</div>;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button
          variant="ghost"
          onClick={() => navigate('/admin/trader-assignments')}
          className="px-0"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Assignments
        </Button>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={() => navigate(`/admin/trader-assignments/${id}/edit`)}
          >
            <Edit className="mr-2 h-4 w-4" />
            Edit
          </Button>
          <Button variant="destructive" onClick={handleDelete}>
            <Trash2 className="mr-2 h-4 w-4" />
            Delete
          </Button>
        </div>
      </div>

      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <div className="flex justify-between items-start">
              <div>
                <CardTitle>Assignment Details</CardTitle>
                <div className="mt-2 flex items-center space-x-2">
                  <Badge variant={statusVariant[assignment.status as Status]} className="capitalize">
                    {assignment.status}
                  </Badge>
                  <span className="text-sm text-muted-foreground">
                    Created on {assignment.createdAt ? formatDate(assignment.createdAt) : 'N/A'}
                  </span>
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6 md:grid-cols-2">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Trader Information</h3>
                <div className="space-y-2">
                  <p className="font-medium">{getUserName(assignment.trader)}</p>
                  <p className="text-sm text-muted-foreground">
                    {typeof assignment.trader !== 'string' ? assignment.trader?.email : 'N/A'}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {typeof assignment.trader !== 'string' ? assignment.trader?.phone || 'N/A' : 'N/A'}
                  </p>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Merchant Information</h3>
                <div className="space-y-2">
                  <p className="font-medium">
                    {typeof assignment.merchant !== 'string' ? 
                      (assignment.merchant as Merchant)?.businessName || 'N/A' : 
                      'N/A'}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {typeof assignment.merchant !== 'string' ? 
                      (assignment.merchant as User)?.email || 'N/A' : 
                      'N/A'}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {typeof assignment.merchant !== 'string' ? 
                      (assignment.merchant as User)?.phone || 'N/A' : 
                      'N/A'}
                  </p>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Assignment Details</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-muted-foreground">Type</span>
                    <span className="capitalize">{assignment.assignmentType.replace('_', ' ')}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-muted-foreground">Start Date</span>
                    <span>{formatDate(assignment.startDate)}</span>
                  </div>
                  {assignment.endDate && (
                    <div className="flex justify-between">
                      <span className="text-sm font-medium text-muted-foreground">End Date</span>
                      <span>{formatDate(assignment.endDate)}</span>
                    </div>
                  )}
                  {assignment.assignedBy && (
                    <div className="flex justify-between">
                      <span className="text-sm font-medium text-muted-foreground">Assigned By</span>
                      <span>
                        {typeof assignment.assignedBy === 'string' 
                          ? assignment.assignedBy 
                          : assignment.assignedBy.name || 'N/A'}
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {assignment.collectionTarget && (
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Collection Target</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm font-medium text-muted-foreground">Amount</span>
                      <span>${assignment.collectionTarget.amount}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm font-medium text-muted-foreground">Period</span>
                      <span className="capitalize">{assignment.collectionTarget.period}</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {assignment.permissions && assignment.permissions.length > 0 && (
          <Card>
            <CardHeader>
              <h3 className="text-lg font-medium">Permissions</h3>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {assignment.permissions.map((permission) => (
                  <Badge key={permission} variant="outline" className="capitalize">
                    {permission.replace('_', ' ')}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {assignment.performanceMetrics && (
          <Card>
            <CardHeader>
              <h3 className="text-lg font-medium">Performance Metrics</h3>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <div className="rounded-lg border p-4">
                  <p className="text-sm font-medium text-muted-foreground">Total Collected</p>
                  <p className="text-2xl font-bold">
                    ${assignment.performanceMetrics.totalCollected?.toLocaleString() || '0'}
                  </p>
                </div>
                <div className="rounded-lg border p-4">
                  <p className="text-sm font-medium text-muted-foreground">Successful Transactions</p>
                  <p className="text-2xl font-bold text-green-600">
                    {assignment.performanceMetrics.successfulTransactions || '0'}
                  </p>
                </div>
                <div className="rounded-lg border p-4">
                  <p className="text-sm font-medium text-muted-foreground">Pending Transactions</p>
                  <p className="text-2xl font-bold text-yellow-600">
                    {assignment.performanceMetrics.pendingTransactions || '0'}
                  </p>
                </div>
                <div className="rounded-lg border p-4">
                  <p className="text-sm font-medium text-muted-foreground">Failed Transactions</p>
                  <p className="text-2xl font-bold text-red-600">
                    {assignment.performanceMetrics.failedTransactions || '0'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
