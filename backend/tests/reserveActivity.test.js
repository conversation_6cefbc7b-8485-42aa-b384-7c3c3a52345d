const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const ReserveActivity = require('../models/ReserveActivity');
const User = require('../models/User');
const ReserveStrategy = require('../models/ReserveStrategy');

// Test data
let mongoServer;
let testUser;
let testStrategy;

// Set up in-memory database before tests run
beforeAll(async () => {
  mongoServer = await MongoMemoryServer.create();
  const uri = mongoServer.getUri();
  await mongoose.connect(uri);
  
  // Create a test user
  testUser = await User.create({
    name: 'Test User',
    email: '<EMAIL>',
    password: 'password123',
    roles: ['admin']
  });
  
  // Create a test strategy
  testStrategy = await ReserveStrategy.create({
    name: 'Test Strategy',
    description: 'Test Description',
    rules: [{
      name: 'Test Rule',
      type: 'percentage',
      percentage: 10,
      isActive: true
    }],
    createdBy: testUser._id
  });
});

// Clean up after each test
afterEach(async () => {
  await ReserveActivity.deleteMany({});
});

// Disconnect and close server after all tests
afterAll(async () => {
  await mongoose.connection.dropDatabase();
  await mongoose.connection.close();
  await mongoServer.stop();
});

describe('ReserveActivity Model', () => {
  it('should create and save an activity successfully', async () => {
    const activityData = {
      strategy: testStrategy._id,
      type: 'create',
      description: 'Test activity',
      user: testUser._id,
      details: { test: 'data' }
    };
    
    const activity = new ReserveActivity(activityData);
    const savedActivity = await activity.save();
    
    expect(savedActivity._id).toBeDefined();
    expect(savedActivity.type).toBe(activityData.type);
    expect(savedActivity.description).toBe(activityData.description);
    expect(savedActivity.status).toBe('pending');
    expect(savedActivity.user.toString()).toBe(testUser._id.toString());
    expect(savedActivity.strategy.toString()).toBe(testStrategy._id.toString());
  });
  
  it('should require required fields', async () => {
    const activity = new ReserveActivity({});
    
    let err;
    try {
      await activity.validate();
    } catch (error) {
      err = error;
    }
    
    expect(err).toBeDefined();
    expect(err.errors.strategy).toBeDefined();
    expect(err.errors.type).toBeDefined();
    expect(err.errors.description).toBeDefined();
    expect(err.errors.user).toBeDefined();
  });
  
  it('should validate activity type enum', async () => {
    const activity = new ReserveActivity({
      strategy: testStrategy._id,
      type: 'invalid_type',
      description: 'Test',
      user: testUser._id
    });
    
    let err;
    try {
      await activity.validate();
    } catch (error) {
      err = error;
    }
    
    expect(err).toBeDefined();
    expect(err.errors.type).toBeDefined();
  });
  
  it('should set default status to pending', async () => {
    const activity = new ReserveActivity({
      strategy: testStrategy._id,
      type: 'create',
      description: 'Test',
      user: testUser._id
    });
    
    expect(activity.status).toBe('pending');
  });
  
  it('should update status and review info when approving', async () => {
    const activity = await ReserveActivity.create({
      strategy: testStrategy._id,
      type: 'create',
      description: 'Test',
      user: testUser._id,
      status: 'pending'
    });
    
    const reviewer = await User.create({
      name: 'Reviewer',
      email: '<EMAIL>',
      password: 'password123',
      roles: ['admin']
    });
    
    await activity.approve(reviewer._id, 'Looks good');
    
    expect(activity.status).toBe('approved');
    expect(activity.reviewedBy.toString()).toBe(reviewer._id.toString());
    expect(activity.reviewNote).toBe('Looks good');
    expect(activity.reviewedAt).toBeDefined();
  });
  
  it('should update status and review info when flagging', async () => {
    const activity = await ReserveActivity.create({
      strategy: testStrategy._id,
      type: 'create',
      description: 'Test',
      user: testUser._id,
      status: 'pending'
    });
    
    const reviewer = await User.create({
      name: 'Reviewer',
      email: '<EMAIL>',
      password: 'password123',
      roles: ['admin']
    });
    
    await activity.flag(reviewer._id, 'Needs more info');
    
    expect(activity.status).toBe('flagged');
    expect(activity.reviewedBy.toString()).toBe(reviewer._id.toString());
    expect(activity.reviewNote).toBe('Needs more info');
    expect(activity.reviewedAt).toBeDefined();
  });
  
  it('should not allow approving a non-pending activity', async () => {
    const activity = await ReserveActivity.create({
      strategy: testStrategy._id,
      type: 'create',
      description: 'Test',
      user: testUser._id,
      status: 'approved',
      reviewedBy: testUser._id,
      reviewedAt: new Date()
    });
    
    await expect(activity.approve(testUser._id, 'Test'))
      .rejects
      .toThrow('Only pending activities can be approved');
  });
  
  it('should not allow flagging a non-pending activity', async () => {
    const activity = await ReserveActivity.create({
      strategy: testStrategy._id,
      type: 'create',
      description: 'Test',
      user: testUser._id,
      status: 'approved',
      reviewedBy: testUser._id,
      reviewedAt: new Date()
    });
    
    await expect(activity.flag(testUser._id, 'Test'))
      .rejects
      .toThrow('Only pending activities can be flagged');
  });
  
  it('should return a summary of the activity', async () => {
    const activity = await ReserveActivity.create({
      strategy: testStrategy._id,
      type: 'create',
      description: 'Test',
      user: testUser._id,
      details: { test: 'data' }
    });
    
    const summary = activity.getSummary();
    
    expect(summary).toMatchObject({
      type: 'create',
      description: 'Test',
      status: 'pending'
    });
    expect(summary.id).toBeDefined();
    expect(summary.createdAt).toBeDefined();
    expect(summary.user.toString()).toBe(testUser._id.toString());
    expect(summary.strategy.toString()).toBe(testStrategy._id.toString());
  });
  
  it('should log an activity using the static method', async () => {
    const activityData = {
      strategy: testStrategy._id,
      type: 'create',
      description: 'Test',
      user: testUser._id,
      details: { test: 'data' }
    };
    
    const activity = await ReserveActivity.logActivity(activityData);
    
    expect(activity._id).toBeDefined();
    expect(activity.type).toBe('create');
    expect(activity.status).toBe('pending');
  });
});
