"use strict";(self.webpackChunkpayment_gateway_frontend=self.webpackChunkpayment_gateway_frontend||[]).push([[73],{116:(t,e,s)=>{s.d(e,{$:()=>u,s:()=>o});var i=s(685),r=s(9939),a=s(7264),n=s(7988);class o extends a.k{constructor(t){super(),this.defaultOptions=t.defaultOptions,this.mutationId=t.mutationId,this.mutationCache=t.mutationCache,this.logger=t.logger||i.U,this.observers=[],this.state=t.state||u(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options={...this.defaultOptions,...t},this.updateCacheTime(this.options.cacheTime)}get meta(){return this.options.meta}setState(t){this.dispatch({type:"setState",state:t})}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),this.mutationCache.notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){this.observers=this.observers.filter(e=>e!==t),this.scheduleGc(),this.mutationCache.notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){this.observers.length||("loading"===this.state.status?this.scheduleGc():this.mutationCache.remove(this))}continue(){var t,e;return null!=(t=null==(e=this.retryer)?void 0:e.continue())?t:this.execute()}async execute(){const t=()=>{var t;return this.retryer=(0,n.II)({fn:()=>this.options.mutationFn?this.options.mutationFn(this.state.variables):Promise.reject("No mutationFn found"),onFail:(t,e)=>{this.dispatch({type:"failed",failureCount:t,error:e})},onPause:()=>{this.dispatch({type:"pause"})},onContinue:()=>{this.dispatch({type:"continue"})},retry:null!=(t=this.options.retry)?t:0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode}),this.retryer.promise},e="loading"===this.state.status;try{var s,i,r,a,o,u,h,l;if(!e){var c,d,f,p;this.dispatch({type:"loading",variables:this.options.variables}),await(null==(c=(d=this.mutationCache.config).onMutate)?void 0:c.call(d,this.state.variables,this));const t=await(null==(f=(p=this.options).onMutate)?void 0:f.call(p,this.state.variables));t!==this.state.context&&this.dispatch({type:"loading",context:t,variables:this.state.variables})}const n=await t();return await(null==(s=(i=this.mutationCache.config).onSuccess)?void 0:s.call(i,n,this.state.variables,this.state.context,this)),await(null==(r=(a=this.options).onSuccess)?void 0:r.call(a,n,this.state.variables,this.state.context)),await(null==(o=(u=this.mutationCache.config).onSettled)?void 0:o.call(u,n,null,this.state.variables,this.state.context,this)),await(null==(h=(l=this.options).onSettled)?void 0:h.call(l,n,null,this.state.variables,this.state.context)),this.dispatch({type:"success",data:n}),n}catch(M){try{var y,v,m,g,b,C,q,O;throw await(null==(y=(v=this.mutationCache.config).onError)?void 0:y.call(v,M,this.state.variables,this.state.context,this)),await(null==(m=(g=this.options).onError)?void 0:m.call(g,M,this.state.variables,this.state.context)),await(null==(b=(C=this.mutationCache.config).onSettled)?void 0:b.call(C,void 0,M,this.state.variables,this.state.context,this)),await(null==(q=(O=this.options).onSettled)?void 0:q.call(O,void 0,M,this.state.variables,this.state.context)),M}finally{this.dispatch({type:"error",error:M})}}}dispatch(t){this.state=(e=>{switch(t.type){case"failed":return{...e,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...e,isPaused:!0};case"continue":return{...e,isPaused:!1};case"loading":return{...e,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:!(0,n.v_)(this.options.networkMode),status:"loading",variables:t.variables};case"success":return{...e,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...e,data:void 0,error:t.error,failureCount:e.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"};case"setState":return{...e,...t.state}}})(this.state),r.j.batch(()=>{this.observers.forEach(e=>{e.onMutationUpdate(t)}),this.mutationCache.notify({mutation:this,type:"updated",action:t})})}}function u(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0}}},685:(t,e,s)=>{s.d(e,{U:()=>i});const i=console},2836:(t,e,s)=>{s.d(e,{n:()=>d});var i=s(5043),r=s(8664),a=s(116),n=s(9939),o=s(2078);class u extends o.Q{constructor(t,e){super(),this.client=t,this.setOptions(e),this.bindMethods(),this.updateResult()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(t){var e;const s=this.options;this.options=this.client.defaultMutationOptions(t),(0,r.f8)(s,this.options)||this.client.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.currentMutation,observer:this}),null==(e=this.currentMutation)||e.setOptions(this.options)}onUnsubscribe(){var t;this.hasListeners()||(null==(t=this.currentMutation)||t.removeObserver(this))}onMutationUpdate(t){this.updateResult();const e={listeners:!0};"success"===t.type?e.onSuccess=!0:"error"===t.type&&(e.onError=!0),this.notify(e)}getCurrentResult(){return this.currentResult}reset(){this.currentMutation=void 0,this.updateResult(),this.notify({listeners:!0})}mutate(t,e){return this.mutateOptions=e,this.currentMutation&&this.currentMutation.removeObserver(this),this.currentMutation=this.client.getMutationCache().build(this.client,{...this.options,variables:"undefined"!==typeof t?t:this.options.variables}),this.currentMutation.addObserver(this),this.currentMutation.execute()}updateResult(){const t=this.currentMutation?this.currentMutation.state:(0,a.$)(),e="loading"===t.status,s={...t,isLoading:e,isPending:e,isSuccess:"success"===t.status,isError:"error"===t.status,isIdle:"idle"===t.status,mutate:this.mutate,reset:this.reset};this.currentResult=s}notify(t){n.j.batch(()=>{var e,s,i,r;if(this.mutateOptions&&this.hasListeners())if(t.onSuccess)null==(e=(s=this.mutateOptions).onSuccess)||e.call(s,this.currentResult.data,this.currentResult.variables,this.currentResult.context),null==(i=(r=this.mutateOptions).onSettled)||i.call(r,this.currentResult.data,null,this.currentResult.variables,this.currentResult.context);else if(t.onError){var a,n,o,u;null==(a=(n=this.mutateOptions).onError)||a.call(n,this.currentResult.error,this.currentResult.variables,this.currentResult.context),null==(o=(u=this.mutateOptions).onSettled)||o.call(u,void 0,this.currentResult.error,this.currentResult.variables,this.currentResult.context)}t.listeners&&this.listeners.forEach(t=>{let{listener:e}=t;e(this.currentResult)})})}}var h=s(8873),l=s(9781),c=s(4084);function d(t,e,s){const a=(0,r.GR)(t,e,s),o=(0,l.jE)({context:a.context}),[d]=i.useState(()=>new u(o,a));i.useEffect(()=>{d.setOptions(a)},[d,a]);const p=(0,h.r)(i.useCallback(t=>d.subscribe(n.j.batchCalls(t)),[d]),()=>d.getCurrentResult(),()=>d.getCurrentResult()),y=i.useCallback((t,e)=>{d.mutate(t,e).catch(f)},[d]);if(p.error&&(0,c.G)(d.options.useErrorBoundary,[p.error]))throw p.error;return{...p,mutate:y,mutateAsync:p.mutate}}function f(){}},4068:(t,e,s)=>{s.d(e,{A:()=>i});const i=(0,s(3797).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},6379:(t,e,s)=>{s.d(e,{E:()=>g});var i=s(8664),r=s(685),a=s(9939),n=s(7988),o=s(7264);class u extends o.k{constructor(t){super(),this.abortSignalConsumed=!1,this.defaultOptions=t.defaultOptions,this.setOptions(t.options),this.observers=[],this.cache=t.cache,this.logger=t.logger||r.U,this.queryKey=t.queryKey,this.queryHash=t.queryHash,this.initialState=t.state||function(t){const e="function"===typeof t.initialData?t.initialData():t.initialData,s="undefined"!==typeof e,i=s?"function"===typeof t.initialDataUpdatedAt?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0;return{data:e,dataUpdateCount:0,dataUpdatedAt:s?null!=i?i:Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"loading",fetchStatus:"idle"}}(this.options),this.state=this.initialState,this.scheduleGc()}get meta(){return this.options.meta}setOptions(t){this.options={...this.defaultOptions,...t},this.updateCacheTime(this.options.cacheTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.cache.remove(this)}setData(t,e){const s=(0,i.pl)(this.state.data,t,this.options);return this.dispatch({data:s,type:"success",dataUpdatedAt:null==e?void 0:e.updatedAt,manual:null==e?void 0:e.manual}),s}setState(t,e){this.dispatch({type:"setState",state:t,setStateOptions:e})}cancel(t){var e;const s=this.promise;return null==(e=this.retryer)||e.cancel(t),s?s.then(i.lQ).catch(i.lQ):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.initialState)}isActive(){return this.observers.some(t=>!1!==t.options.enabled)}isDisabled(){return this.getObserversCount()>0&&!this.isActive()}isStale(){return this.state.isInvalidated||!this.state.dataUpdatedAt||this.observers.some(t=>t.getCurrentResult().isStale)}isStaleByTime(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return this.state.isInvalidated||!this.state.dataUpdatedAt||!(0,i.j3)(this.state.dataUpdatedAt,t)}onFocus(){var t;const e=this.observers.find(t=>t.shouldFetchOnWindowFocus());e&&e.refetch({cancelRefetch:!1}),null==(t=this.retryer)||t.continue()}onOnline(){var t;const e=this.observers.find(t=>t.shouldFetchOnReconnect());e&&e.refetch({cancelRefetch:!1}),null==(t=this.retryer)||t.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),this.cache.notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(e=>e!==t),this.observers.length||(this.retryer&&(this.abortSignalConsumed?this.retryer.cancel({revert:!0}):this.retryer.cancelRetry()),this.scheduleGc()),this.cache.notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.dispatch({type:"invalidate"})}fetch(t,e){var s,r;if("idle"!==this.state.fetchStatus)if(this.state.dataUpdatedAt&&null!=e&&e.cancelRefetch)this.cancel({silent:!0});else if(this.promise){var a;return null==(a=this.retryer)||a.continueRetry(),this.promise}if(t&&this.setOptions(t),!this.options.queryFn){const t=this.observers.find(t=>t.options.queryFn);t&&this.setOptions(t.options)}const o=(0,i.jY)(),u={queryKey:this.queryKey,pageParam:void 0,meta:this.meta},h=t=>{Object.defineProperty(t,"signal",{enumerable:!0,get:()=>{if(o)return this.abortSignalConsumed=!0,o.signal}})};h(u);const l={fetchOptions:e,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:()=>this.options.queryFn?(this.abortSignalConsumed=!1,this.options.queryFn(u)):Promise.reject("Missing queryFn for queryKey '"+this.options.queryHash+"'")};var c;(h(l),null==(s=this.options.behavior)||s.onFetch(l),this.revertState=this.state,"idle"===this.state.fetchStatus||this.state.fetchMeta!==(null==(r=l.fetchOptions)?void 0:r.meta))&&this.dispatch({type:"fetch",meta:null==(c=l.fetchOptions)?void 0:c.meta});const d=t=>{var e,s,i,r;((0,n.wm)(t)&&t.silent||this.dispatch({type:"error",error:t}),(0,n.wm)(t))||(null==(e=(s=this.cache.config).onError)||e.call(s,t,this),null==(i=(r=this.cache.config).onSettled)||i.call(r,this.state.data,t,this));this.isFetchingOptimistic||this.scheduleGc(),this.isFetchingOptimistic=!1};return this.retryer=(0,n.II)({fn:l.fetchFn,abort:null==o?void 0:o.abort.bind(o),onSuccess:t=>{var e,s,i,r;"undefined"!==typeof t?(this.setData(t),null==(e=(s=this.cache.config).onSuccess)||e.call(s,t,this),null==(i=(r=this.cache.config).onSettled)||i.call(r,t,this.state.error,this),this.isFetchingOptimistic||this.scheduleGc(),this.isFetchingOptimistic=!1):d(new Error(this.queryHash+" data is undefined"))},onError:d,onFail:(t,e)=>{this.dispatch({type:"failed",failureCount:t,error:e})},onPause:()=>{this.dispatch({type:"pause"})},onContinue:()=>{this.dispatch({type:"continue"})},retry:l.options.retry,retryDelay:l.options.retryDelay,networkMode:l.options.networkMode}),this.promise=this.retryer.promise,this.promise}dispatch(t){this.state=(e=>{var s,i;switch(t.type){case"failed":return{...e,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...e,fetchStatus:"paused"};case"continue":return{...e,fetchStatus:"fetching"};case"fetch":return{...e,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null!=(s=t.meta)?s:null,fetchStatus:(0,n.v_)(this.options.networkMode)?"fetching":"paused",...!e.dataUpdatedAt&&{error:null,status:"loading"}};case"success":return{...e,data:t.data,dataUpdateCount:e.dataUpdateCount+1,dataUpdatedAt:null!=(i=t.dataUpdatedAt)?i:Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const r=t.error;return(0,n.wm)(r)&&r.revert&&this.revertState?{...this.revertState,fetchStatus:"idle"}:{...e,error:r,errorUpdateCount:e.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:e.fetchFailureCount+1,fetchFailureReason:r,fetchStatus:"idle",status:"error"};case"invalidate":return{...e,isInvalidated:!0};case"setState":return{...e,...t.state}}})(this.state),a.j.batch(()=>{this.observers.forEach(e=>{e.onQueryUpdate(t)}),this.cache.notify({query:this,type:"updated",action:t})})}}var h=s(2078);class l extends h.Q{constructor(t){super(),this.config=t||{},this.queries=[],this.queriesMap={}}build(t,e,s){var r;const a=e.queryKey,n=null!=(r=e.queryHash)?r:(0,i.F$)(a,e);let o=this.get(n);return o||(o=new u({cache:this,logger:t.getLogger(),queryKey:a,queryHash:n,options:t.defaultQueryOptions(e),state:s,defaultOptions:t.getQueryDefaults(a)}),this.add(o)),o}add(t){this.queriesMap[t.queryHash]||(this.queriesMap[t.queryHash]=t,this.queries.push(t),this.notify({type:"added",query:t}))}remove(t){const e=this.queriesMap[t.queryHash];e&&(t.destroy(),this.queries=this.queries.filter(e=>e!==t),e===t&&delete this.queriesMap[t.queryHash],this.notify({type:"removed",query:t}))}clear(){a.j.batch(()=>{this.queries.forEach(t=>{this.remove(t)})})}get(t){return this.queriesMap[t]}getAll(){return this.queries}find(t,e){const[s]=(0,i.b_)(t,e);return"undefined"===typeof s.exact&&(s.exact=!0),this.queries.find(t=>(0,i.MK)(s,t))}findAll(t,e){const[s]=(0,i.b_)(t,e);return Object.keys(s).length>0?this.queries.filter(t=>(0,i.MK)(s,t)):this.queries}notify(t){a.j.batch(()=>{this.listeners.forEach(e=>{let{listener:s}=e;s(t)})})}onFocus(){a.j.batch(()=>{this.queries.forEach(t=>{t.onFocus()})})}onOnline(){a.j.batch(()=>{this.queries.forEach(t=>{t.onOnline()})})}}var c=s(116);class d extends h.Q{constructor(t){super(),this.config=t||{},this.mutations=[],this.mutationId=0}build(t,e,s){const i=new c.s({mutationCache:this,logger:t.getLogger(),mutationId:++this.mutationId,options:t.defaultMutationOptions(e),state:s,defaultOptions:e.mutationKey?t.getMutationDefaults(e.mutationKey):void 0});return this.add(i),i}add(t){this.mutations.push(t),this.notify({type:"added",mutation:t})}remove(t){this.mutations=this.mutations.filter(e=>e!==t),this.notify({type:"removed",mutation:t})}clear(){a.j.batch(()=>{this.mutations.forEach(t=>{this.remove(t)})})}getAll(){return this.mutations}find(t){return"undefined"===typeof t.exact&&(t.exact=!0),this.mutations.find(e=>(0,i.nJ)(t,e))}findAll(t){return this.mutations.filter(e=>(0,i.nJ)(t,e))}notify(t){a.j.batch(()=>{this.listeners.forEach(e=>{let{listener:s}=e;s(t)})})}resumePausedMutations(){var t;return this.resuming=(null!=(t=this.resuming)?t:Promise.resolve()).then(()=>{const t=this.mutations.filter(t=>t.state.isPaused);return a.j.batch(()=>t.reduce((t,e)=>t.then(()=>e.continue().catch(i.lQ)),Promise.resolve()))}).then(()=>{this.resuming=void 0}),this.resuming}}var f=s(2540),p=s(585);function y(){return{onFetch:t=>{t.fetchFn=()=>{var e,s,i,r,a,n;const o=null==(e=t.fetchOptions)||null==(s=e.meta)?void 0:s.refetchPage,u=null==(i=t.fetchOptions)||null==(r=i.meta)?void 0:r.fetchMore,h=null==u?void 0:u.pageParam,l="forward"===(null==u?void 0:u.direction),c="backward"===(null==u?void 0:u.direction),d=(null==(a=t.state.data)?void 0:a.pages)||[],f=(null==(n=t.state.data)?void 0:n.pageParams)||[];let p=f,y=!1;const g=t.options.queryFn||(()=>Promise.reject("Missing queryFn for queryKey '"+t.options.queryHash+"'")),b=(t,e,s,i)=>(p=i?[e,...p]:[...p,e],i?[s,...t]:[...t,s]),C=(e,s,i,r)=>{if(y)return Promise.reject("Cancelled");if("undefined"===typeof i&&!s&&e.length)return Promise.resolve(e);const a={queryKey:t.queryKey,pageParam:i,meta:t.options.meta};var n;n=a,Object.defineProperty(n,"signal",{enumerable:!0,get:()=>{var e,s;return null!=(e=t.signal)&&e.aborted?y=!0:null==(s=t.signal)||s.addEventListener("abort",()=>{y=!0}),t.signal}});const o=g(a);return Promise.resolve(o).then(t=>b(e,i,t,r))};let q;if(d.length)if(l){const e="undefined"!==typeof h,s=e?h:v(t.options,d);q=C(d,e,s)}else if(c){const e="undefined"!==typeof h,s=e?h:m(t.options,d);q=C(d,e,s,!0)}else{p=[];const e="undefined"===typeof t.options.getNextPageParam;q=!o||!d[0]||o(d[0],0,d)?C([],e,f[0]):Promise.resolve(b([],f[0],d[0]));for(let s=1;s<d.length;s++)q=q.then(i=>{if(!o||!d[s]||o(d[s],s,d)){const r=e?f[s]:v(t.options,i);return C(i,e,r)}return Promise.resolve(b(i,f[s],d[s]))})}else q=C([]);return q.then(t=>({pages:t,pageParams:p}))}}}}function v(t,e){return null==t.getNextPageParam?void 0:t.getNextPageParam(e[e.length-1],e)}function m(t,e){return null==t.getPreviousPageParam?void 0:t.getPreviousPageParam(e[0],e)}class g{constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.queryCache=t.queryCache||new l,this.mutationCache=t.mutationCache||new d,this.logger=t.logger||r.U,this.defaultOptions=t.defaultOptions||{},this.queryDefaults=[],this.mutationDefaults=[],this.mountCount=0}mount(){this.mountCount++,1===this.mountCount&&(this.unsubscribeFocus=f.m.subscribe(()=>{f.m.isFocused()&&(this.resumePausedMutations(),this.queryCache.onFocus())}),this.unsubscribeOnline=p.t.subscribe(()=>{p.t.isOnline()&&(this.resumePausedMutations(),this.queryCache.onOnline())}))}unmount(){var t,e;this.mountCount--,0===this.mountCount&&(null==(t=this.unsubscribeFocus)||t.call(this),this.unsubscribeFocus=void 0,null==(e=this.unsubscribeOnline)||e.call(this),this.unsubscribeOnline=void 0)}isFetching(t,e){const[s]=(0,i.b_)(t,e);return s.fetchStatus="fetching",this.queryCache.findAll(s).length}isMutating(t){return this.mutationCache.findAll({...t,fetching:!0}).length}getQueryData(t,e){var s;return null==(s=this.queryCache.find(t,e))?void 0:s.state.data}ensureQueryData(t,e,s){const r=(0,i.vh)(t,e,s),a=this.getQueryData(r.queryKey);return a?Promise.resolve(a):this.fetchQuery(r)}getQueriesData(t){return this.getQueryCache().findAll(t).map(t=>{let{queryKey:e,state:s}=t;return[e,s.data]})}setQueryData(t,e,s){const r=this.queryCache.find(t),a=null==r?void 0:r.state.data,n=(0,i.Zw)(e,a);if("undefined"===typeof n)return;const o=(0,i.vh)(t),u=this.defaultQueryOptions(o);return this.queryCache.build(this,u).setData(n,{...s,manual:!0})}setQueriesData(t,e,s){return a.j.batch(()=>this.getQueryCache().findAll(t).map(t=>{let{queryKey:i}=t;return[i,this.setQueryData(i,e,s)]}))}getQueryState(t,e){var s;return null==(s=this.queryCache.find(t,e))?void 0:s.state}removeQueries(t,e){const[s]=(0,i.b_)(t,e),r=this.queryCache;a.j.batch(()=>{r.findAll(s).forEach(t=>{r.remove(t)})})}resetQueries(t,e,s){const[r,n]=(0,i.b_)(t,e,s),o=this.queryCache,u={type:"active",...r};return a.j.batch(()=>(o.findAll(r).forEach(t=>{t.reset()}),this.refetchQueries(u,n)))}cancelQueries(t,e,s){const[r,n={}]=(0,i.b_)(t,e,s);"undefined"===typeof n.revert&&(n.revert=!0);const o=a.j.batch(()=>this.queryCache.findAll(r).map(t=>t.cancel(n)));return Promise.all(o).then(i.lQ).catch(i.lQ)}invalidateQueries(t,e,s){const[r,n]=(0,i.b_)(t,e,s);return a.j.batch(()=>{var t,e;if(this.queryCache.findAll(r).forEach(t=>{t.invalidate()}),"none"===r.refetchType)return Promise.resolve();const s={...r,type:null!=(t=null!=(e=r.refetchType)?e:r.type)?t:"active"};return this.refetchQueries(s,n)})}refetchQueries(t,e,s){const[r,n]=(0,i.b_)(t,e,s),o=a.j.batch(()=>this.queryCache.findAll(r).filter(t=>!t.isDisabled()).map(t=>{var e;return t.fetch(void 0,{...n,cancelRefetch:null==(e=null==n?void 0:n.cancelRefetch)||e,meta:{refetchPage:r.refetchPage}})}));let u=Promise.all(o).then(i.lQ);return null!=n&&n.throwOnError||(u=u.catch(i.lQ)),u}fetchQuery(t,e,s){const r=(0,i.vh)(t,e,s),a=this.defaultQueryOptions(r);"undefined"===typeof a.retry&&(a.retry=!1);const n=this.queryCache.build(this,a);return n.isStaleByTime(a.staleTime)?n.fetch(a):Promise.resolve(n.state.data)}prefetchQuery(t,e,s){return this.fetchQuery(t,e,s).then(i.lQ).catch(i.lQ)}fetchInfiniteQuery(t,e,s){const r=(0,i.vh)(t,e,s);return r.behavior=y(),this.fetchQuery(r)}prefetchInfiniteQuery(t,e,s){return this.fetchInfiniteQuery(t,e,s).then(i.lQ).catch(i.lQ)}resumePausedMutations(){return this.mutationCache.resumePausedMutations()}getQueryCache(){return this.queryCache}getMutationCache(){return this.mutationCache}getLogger(){return this.logger}getDefaultOptions(){return this.defaultOptions}setDefaultOptions(t){this.defaultOptions=t}setQueryDefaults(t,e){const s=this.queryDefaults.find(e=>(0,i.Od)(t)===(0,i.Od)(e.queryKey));s?s.defaultOptions=e:this.queryDefaults.push({queryKey:t,defaultOptions:e})}getQueryDefaults(t){if(!t)return;const e=this.queryDefaults.find(e=>(0,i.Cp)(t,e.queryKey));return null==e?void 0:e.defaultOptions}setMutationDefaults(t,e){const s=this.mutationDefaults.find(e=>(0,i.Od)(t)===(0,i.Od)(e.mutationKey));s?s.defaultOptions=e:this.mutationDefaults.push({mutationKey:t,defaultOptions:e})}getMutationDefaults(t){if(!t)return;const e=this.mutationDefaults.find(e=>(0,i.Cp)(t,e.mutationKey));return null==e?void 0:e.defaultOptions}defaultQueryOptions(t){if(null!=t&&t._defaulted)return t;const e={...this.defaultOptions.queries,...this.getQueryDefaults(null==t?void 0:t.queryKey),...t,_defaulted:!0};return!e.queryHash&&e.queryKey&&(e.queryHash=(0,i.F$)(e.queryKey,e)),"undefined"===typeof e.refetchOnReconnect&&(e.refetchOnReconnect="always"!==e.networkMode),"undefined"===typeof e.useErrorBoundary&&(e.useErrorBoundary=!!e.suspense),e}defaultMutationOptions(t){return null!=t&&t._defaulted?t:{...this.defaultOptions.mutations,...this.getMutationDefaults(null==t?void 0:t.mutationKey),...t,_defaulted:!0}}clear(){this.queryCache.clear(),this.mutationCache.clear()}}},7264:(t,e,s)=>{s.d(e,{k:()=>r});var i=s(8664);class r{destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),(0,i.gn)(this.cacheTime)&&(this.gcTimeout=setTimeout(()=>{this.optionalRemove()},this.cacheTime))}updateCacheTime(t){this.cacheTime=Math.max(this.cacheTime||0,null!=t?t:i.S$?1/0:3e5)}clearGcTimeout(){this.gcTimeout&&(clearTimeout(this.gcTimeout),this.gcTimeout=void 0)}}}}]);
//# sourceMappingURL=73.20135988.chunk.js.map