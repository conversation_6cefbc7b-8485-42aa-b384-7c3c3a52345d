import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Label } from '../../components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs';
import { Switch } from '../../components/ui/switch';
import { Copy, Check, RefreshCw, ExternalLink, Code, Settings, Bell, Lock } from 'lucide-react';
import { Badge } from '../../components/ui/badge';

const IntegrationSettings = () => {
  const [apiKey, setApiKey] = useState('sk_test_51N1lZ2SJv8rX9vX7...');
  const [isLiveMode, setIsLiveMode] = useState(false);
  const [webhookUrl, setWebhookUrl] = useState('https://your-merchant-url.com/api/webhooks/payment');
  const [copied, setCopied] = useState(false);
  const [isRegenerating, setIsRegenerating] = useState(false);

  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const regenerateApiKey = () => {
    setIsRegenerating(true);
    // Simulate API call
    setTimeout(() => {
      setApiKey(`sk_${Math.random().toString(36).substring(2, 42)}`);
      setIsRegenerating(false);
    }, 1000);
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">Integration Settings</h2>
        <p className="text-muted-foreground">
          Configure how your application connects with our payment gateway
        </p>
      </div>

      <Tabs defaultValue="api" className="space-y-4">
        <TabsList>
          <TabsTrigger value="api">
            <Code className="mr-2 h-4 w-4" />
            API Keys
          </TabsTrigger>
          <TabsTrigger value="webhooks">
            <Bell className="mr-2 h-4 w-4" />
            Webhooks
          </TabsTrigger>
          <TabsTrigger value="security">
            <Lock className="mr-2 h-4 w-4" />
            Security
          </TabsTrigger>
        </TabsList>

        <TabsContent value="api" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>API Keys</CardTitle>
              <CardDescription>
                Your API keys are used to authenticate requests to our API. Keep them secret and never share them.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between space-x-4">
                <div className="flex-1">
                  <Label htmlFor="api-key">Secret API Key</Label>
                  <div className="flex items-center space-x-2 mt-1">
                    <Input
                      id="api-key"
                      value={apiKey}
                      readOnly
                      className="font-mono"
                    />
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => handleCopy(apiKey)}
                      disabled={copied}
                    >
                      {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
                <Button
                  variant="outline"
                  onClick={regenerateApiKey}
                  disabled={isRegenerating}
                  className="mt-6"
                >
                  {isRegenerating ? (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                      Regenerating...
                    </>
                  ) : (
                    'Regenerate Key'
                  )}
                </Button>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="live-mode"
                  checked={isLiveMode}
                  onCheckedChange={setIsLiveMode}
                />
                <Label htmlFor="live-mode">
                  {isLiveMode ? 'Live Mode' : 'Test Mode'}
                </Label>
                <Badge variant={isLiveMode ? 'destructive' : 'secondary'} className="ml-2">
                  {isLiveMode ? 'Charges will be processed' : 'No real charges will be processed'}
                </Badge>
              </div>

              <div className="rounded-md bg-muted p-4">
                <h4 className="font-medium mb-2">API Documentation</h4>
                <p className="text-sm text-muted-foreground mb-4">
                  Check out our API documentation for detailed integration guides and examples.
                </p>
                <Button variant="outline" size="sm">
                  <ExternalLink className="mr-2 h-4 w-4" />
                  View API Documentation
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="webhooks" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Webhook Configuration</CardTitle>
              <CardDescription>
                Receive real-time updates about payment events directly to your server.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="webhook-url">Webhook URL</Label>
                  <div className="flex items-center space-x-2 mt-1">
                    <Input
                      id="webhook-url"
                      value={webhookUrl}
                      onChange={(e) => setWebhookUrl(e.target.value)}
                      placeholder="https://your-merchant-url.com/api/webhooks"
                    />
                    <Button variant="outline" onClick={() => handleCopy(webhookUrl)}>
                      <Copy className="mr-2 h-4 w-4" />
                      {copied ? 'Copied!' : 'Copy'}
                    </Button>
                  </div>
                </div>

                <div className="rounded-md bg-muted p-4">
                  <h4 className="font-medium mb-2">Webhook Events</h4>
                  <p className="text-sm text-muted-foreground mb-2">
                    We'll send POST requests to your webhook URL for the following events:
                  </p>
                  <ul className="list-disc pl-5 space-y-1 text-sm text-muted-foreground">
                    <li><code>payment.succeeded</code> - When a payment is successfully completed</li>
                    <li><code>payment.failed</code> - When a payment fails</li>
                    <li><code>payment.refunded</code> - When a payment is refunded</li>
                    <li><code>charge.dispute.created</code> - When a dispute is created</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Security Settings</CardTitle>
              <CardDescription>
                Manage your account security and access controls.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">Two-Factor Authentication</h4>
                  <p className="text-sm text-muted-foreground">
                    Add an extra layer of security to your account
                  </p>
                </div>
                <Button variant="outline">
                  <Settings className="mr-2 h-4 w-4" />
                  Configure 2FA
                </Button>
              </div>

              <div className="border-t pt-4">
                <h4 className="font-medium mb-2">IP Whitelisting</h4>
                <p className="text-sm text-muted-foreground mb-4">
                  Restrict API access to specific IP addresses for enhanced security.
                </p>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Input
                      placeholder="***********"
                      className="max-w-xs"
                    />
                    <Button variant="outline">Add IP</Button>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Leave empty to allow access from any IP address (not recommended for production)
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default IntegrationSettings;
