const { validationResult } = require('express-validator');
const ApiError = require('../utils/ApiError');

/**
 * Middleware to format API responses consistently
 */
const responseFormatter = (req, res, next) => {
  // Success response method
  res.success = (data, message = 'Success', statusCode = 200) => {
    const response = {
      success: true,
      message,
      data
    };
    
    // Add pagination metadata if available
    if (data && data.docs) {
      const { docs, ...pagination } = data;
      response.data = docs;
      response.pagination = pagination;
    }
    
    return res.status(statusCode).json(response);
  };
  
  // Error response method
  res.error = (message, statusCode = 400, errors = null) => {
    const response = {
      success: false,
      message,
      ...(errors && { errors })
    };
    
    return res.status(statusCode).json(response);
  };
  
  // Pagination response method
  res.paginate = (result, message = 'Success') => {
    const { docs, ...pagination } = result;
    
    return res.status(200).json({
      success: true,
      message,
      data: docs,
      pagination: {
        total: pagination.totalDocs || 0,
        limit: pagination.limit || 10,
        page: pagination.page || 1,
        pages: pagination.totalPages || 1,
        ...(pagination.hasPrevPage && { prevPage: pagination.prevPage }),
        ...(pagination.hasNextPage && { nextPage: pagination.nextPage })
      }
    });
  };
  
  // Validation error response method
  res.validationError = (errors) => {
    const formattedErrors = {};
    
    if (Array.isArray(errors)) {
      errors.forEach(error => {
        if (error.param && !formattedErrors[error.param]) {
          formattedErrors[error.param] = error.msg;
        }
      });
    } else if (typeof errors === 'object' && errors !== null) {
      Object.assign(formattedErrors, errors);
    }
    
    return res.status(422).json({
      success: false,
      message: 'Validation failed',
      errors: formattedErrors
    });
  };
  
  // Not found response method
  res.notFound = (message = 'Resource not found') => {
    return res.status(404).json({
      success: false,
      message
    });
  };
  
  // Unauthorized response method
  res.unauthorized = (message = 'Unauthorized') => {
    return res.status(401).json({
      success: false,
      message
    });
  };
  
  // Forbidden response method
  res.forbidden = (message = 'Forbidden') => {
    return res.status(403).json({
      success: false,
      message
    });
  };
  
  // Handle validation errors from express-validator
  const handleValidationErrors = (req, res, next) => {
    const errors = validationResult(req);
    
    if (!errors.isEmpty()) {
      return res.validationError(errors.array());
    }
    
    next();
  };
  
  // Attach to res object for use in routes
  res.handleValidationErrors = handleValidationErrors;
  
  next();
};

module.exports = responseFormatter;
