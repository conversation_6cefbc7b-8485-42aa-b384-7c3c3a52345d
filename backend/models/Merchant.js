const mongoose = require('mongoose');

const businessAddressSchema = new mongoose.Schema({
  street: String,
  city: String,
  state: String,
  country: { type: String, default: 'India' },
  postalCode: String
});

const bankAccountSchema = new mongoose.Schema({
  accountHolderName: String,
  accountNumber: String,
  bankName: String,
  ifscCode: String,
  accountType: { type: String, enum: ['savings', 'current'] }
});

const kycSchema = new mongoose.Schema({
  documentType: { 
    type: String, 
    enum: ['aadhaar', 'pan', 'passport', 'driving_license'],
    required: true
  },
  documentNumber: { type: String, required: true },
  documentFrontUrl: String,
  documentBackUrl: String,
  verified: { type: Boolean, default: false },
  verifiedAt: Date,
  rejectionReason: String
});

const merchantSchema = new mongoose.Schema({
  userId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User',
    required: true,
    unique: true
  },
  businessName: { type: String, required: true },
  businessType: { type: String, required: true },
  businessRegistrationNumber: { type: String, required: true },
  businessAddress: { type: businessAddressSchema, required: true },
  contactPersonName: { type: String, required: true },
  contactEmail: { type: String, required: true },
  contactPhone: { type: String, required: true },
  website: String,
  bankAccount: { type: bankAccountSchema, required: true },
  kyc: { type: kycSchema, required: true },
  status: { 
    type: String, 
    enum: ['draft', 'pending', 'verified', 'rejected', 'suspended'],
    default: 'draft'
  },
  rejectionReason: String,
  isActive: { type: Boolean, default: false }
}, { timestamps: true });

module.exports = mongoose.model('Merchant', merchantSchema);
