"use strict";(self.webpackChunkpayment_gateway_frontend=self.webpackChunkpayment_gateway_frontend||[]).push([[263],{2417:(e,s,a)=>{a.d(s,{E:()=>i});a(5043);var t=a(3009),r=a(579);function i(e){let{className:s,...a}=e;return(0,r.jsx)("div",{className:(0,t.cn)("animate-pulse rounded-md bg-gray-100 dark:bg-gray-800",s),...a})}},7263:(e,s,a)=>{a.r(s),a.d(s,{default:()=>P});var t=a(5043),r=a(5604),i=a(9066);const n=a(6213).A.create({baseURL:"http://localhost:5001/api",headers:{"Content-Type":"application/json"},withCredentials:!0});n.interceptors.request.use(e=>{const s=localStorage.getItem("token");return s&&(e.headers.Authorization=`Bearer ${s}`),e},e=>Promise.reject(e)),n.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(localStorage.removeItem("token"),window.location.href="/login"),Promise.reject(e)));const l=(e,s)=>n.get(e,s),c=async()=>(await l("/users/me")).data.data;var d=a(6736),o=a(6742),x=a(2417),u=a(7298),m=a(2246),h=a(2036),j=a(4199),p=a(9693),g=a(6474),f=a(9445),y=a(8522),v=a(987),w=a(8064),N=a(7772),b=a(579);const A=async()=>[{id:"1",action:"login",description:"Successful login from *********** (Chrome, Windows)",timestamp:(new Date).toISOString(),status:"success",ip:"***********",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"},{id:"2",action:"password_change",description:"Password changed",timestamp:new Date(Date.now()-72e5).toISOString(),status:"success"},{id:"3",action:"login_attempt",description:"Failed login <NAME_EMAIL>",timestamp:new Date(Date.now()-864e5).toISOString(),status:"failed",ip:"********"},{id:"4",action:"2fa_enabled",description:"Two-factor authentication enabled",timestamp:new Date(Date.now()-1728e5).toISOString(),status:"success"}],S=e=>{switch(e){case"login":return(0,b.jsx)(g.A,{className:"h-4 w-4"});case"logout":return(0,b.jsx)(f.A,{className:"h-4 w-4"});case"password_change":case"2fa_enabled":return(0,b.jsx)(y.A,{className:"h-4 w-4"});default:return(0,b.jsx)(v.A,{className:"h-4 w-4"})}};function k(){const{data:e,isLoading:s,error:a}=(0,r.I)({queryKey:["activityLogs"],queryFn:A}),t=e||[];return s?(0,b.jsx)("div",{className:"flex justify-center items-center h-32",children:(0,b.jsx)(N.A,{className:"h-6 w-6 animate-spin text-muted-foreground"})}):a?(0,b.jsx)("div",{className:"rounded-md bg-red-50 p-4",children:(0,b.jsxs)("div",{className:"flex",children:[(0,b.jsx)("div",{className:"flex-shrink-0",children:(0,b.jsx)(v.A,{className:"h-5 w-5 text-red-400","aria-hidden":"true"})}),(0,b.jsxs)("div",{className:"ml-3",children:[(0,b.jsx)("h3",{className:"text-sm font-medium text-red-800",children:"Failed to load activity logs"}),(0,b.jsx)("div",{className:"mt-2 text-sm text-red-700",children:(0,b.jsx)("p",{children:"Unable to load your activity logs at this time. Please try again later."})})]})]})}):0===t.length?(0,b.jsx)("div",{className:"text-center py-12",children:(0,b.jsx)("p",{className:"text-muted-foreground",children:"No activity logs found."})}):(0,b.jsx)("div",{className:"flow-root",children:(0,b.jsx)("ul",{role:"list",className:"-mb-8",children:t.map((e,s)=>{return(0,b.jsx)("li",{children:(0,b.jsxs)("div",{className:"relative pb-8",children:[s!==t.length-1?(0,b.jsx)("span",{className:"absolute left-4 top-4 -ml-px h-full w-0.5 bg-gray-200","aria-hidden":"true"}):null,(0,b.jsxs)("div",{className:"relative flex space-x-3",children:[(0,b.jsx)("div",{children:(0,b.jsx)("span",{className:"h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white "+("success"===e.status?"bg-green-100 text-green-700":"failed"===e.status?"bg-red-100 text-red-700":"bg-yellow-100 text-yellow-700"),children:S(e.action)})}),(0,b.jsxs)("div",{className:"flex min-w-0 flex-1 justify-between space-x-4 pt-1.5",children:[(0,b.jsxs)("div",{children:[(0,b.jsxs)("p",{className:"text-sm text-gray-800",children:[e.description,e.ip&&(0,b.jsxs)("span",{className:"text-xs text-muted-foreground ml-1",children:["(",e.ip,")"]})]}),e.userAgent&&(0,b.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:e.userAgent})]}),(0,b.jsxs)("div",{className:"whitespace-nowrap text-right text-sm text-muted-foreground",children:[(0,b.jsx)("time",{dateTime:e.timestamp,children:(0,p.A)(new Date(e.timestamp),{addSuffix:!0})}),(0,b.jsx)("div",{className:"mt-1",children:(0,b.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium "+("success"===e.status?"bg-green-100 text-green-800":"failed"===e.status?"bg-red-100 text-red-800":"bg-yellow-100 text-yellow-800"),children:[(a=e.status,"success"===a?(0,b.jsx)(w.A,{className:"h-4 w-4"}):(0,b.jsx)(v.A,{className:"h-4 w-4"})),(0,b.jsx)("span",{className:"ml-1 capitalize",children:e.status})]})})]})]})]})]})},e.id);var a})})})}function P(){const{user:e}=(0,i.A)(),[s,a]=(0,t.useState)("profile"),{data:n,isLoading:l,error:p,refetch:g}=(0,r.I)({queryKey:["currentUser"],queryFn:c,enabled:!!e});return(0,t.useEffect)(()=>{"profile"!==s&&g()},[s,g]),l?(0,b.jsxs)("div",{className:"container mx-auto py-8 space-y-8",children:[(0,b.jsx)(x.E,{className:"h-10 w-64 mb-6"}),(0,b.jsx)("div",{className:"grid gap-6",children:(0,b.jsx)(x.E,{className:"h-96 w-full"})})]}):p?(0,b.jsx)("div",{className:"container mx-auto py-8",children:(0,b.jsx)("div",{className:"rounded-lg border border-red-200 bg-red-50 p-4 text-red-700",children:"Failed to load profile. Please try again later."})}):n?(0,b.jsxs)("div",{className:"container mx-auto py-8 space-y-8",children:[(0,b.jsxs)("div",{className:"space-y-2",children:[(0,b.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Profile Settings"}),(0,b.jsx)("p",{className:"text-muted-foreground",children:"Manage your account settings and preferences"})]}),(0,b.jsxs)(d.tU,{defaultValue:"profile",value:s,onValueChange:a,className:"space-y-6",children:[(0,b.jsxs)(d.j7,{className:"grid w-full grid-cols-2 md:grid-cols-4 lg:w-1/2",children:[(0,b.jsx)(d.Xi,{value:"profile",children:"Profile"}),(0,b.jsx)(d.Xi,{value:"security",children:"Security"}),(0,b.jsx)(d.Xi,{value:"api-keys",children:"API Keys"}),(0,b.jsx)(d.Xi,{value:"activity",children:"Activity"})]}),(0,b.jsx)(d.av,{value:"profile",children:(0,b.jsxs)(o.Zp,{children:[(0,b.jsxs)(o.aR,{children:[(0,b.jsx)(o.ZB,{children:"Profile Information"}),(0,b.jsx)(o.BT,{children:"Update your account's profile information and email address."})]}),(0,b.jsx)(o.Wu,{children:(0,b.jsx)(u.g,{user:n,onSuccess:()=>g()})})]})}),(0,b.jsx)(d.av,{value:"security",children:(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)(o.Zp,{children:[(0,b.jsxs)(o.aR,{children:[(0,b.jsx)(o.ZB,{children:"Change Password"}),(0,b.jsx)(o.BT,{children:"Ensure your account is using a long, random password to stay secure."})]}),(0,b.jsx)(o.Wu,{children:(0,b.jsx)(m.G,{})})]}),(0,b.jsxs)(o.Zp,{children:[(0,b.jsxs)(o.aR,{children:[(0,b.jsx)(o.ZB,{children:"Two-Factor Authentication"}),(0,b.jsx)(o.BT,{children:"Add an extra layer of security to your account using two-factor authentication."})]}),(0,b.jsx)(o.Wu,{children:(0,b.jsx)(h.k,{twoFactorEnabled:n.twoFactorEnabled})})]})]})}),(0,b.jsx)(d.av,{value:"api-keys",children:(0,b.jsxs)(o.Zp,{children:[(0,b.jsxs)(o.aR,{children:[(0,b.jsx)(o.ZB,{children:"API Keys"}),(0,b.jsx)(o.BT,{children:"Manage your API keys for programmatic access to your account."})]}),(0,b.jsx)(o.Wu,{children:(0,b.jsx)(j.x,{})})]})}),(0,b.jsx)(d.av,{value:"activity",children:(0,b.jsxs)(o.Zp,{children:[(0,b.jsxs)(o.aR,{children:[(0,b.jsx)(o.ZB,{children:"Activity Log"}),(0,b.jsx)(o.BT,{children:"View recent activity on your account."})]}),(0,b.jsx)(o.Wu,{children:(0,b.jsx)(k,{})})]})})]})]}):(0,b.jsx)("div",{className:"container mx-auto py-8",children:(0,b.jsx)("div",{className:"rounded-lg border border-amber-200 bg-amber-50 p-4 text-amber-700",children:"Please log in to view your profile."})})}}}]);
//# sourceMappingURL=263.ef814039.chunk.js.map