const logger = require('../utils/logger');

/**
 * Middleware to enforce HTTPS in production
 * Redirects HTTP requests to HTTPS
 */
const enforceHTTPS = (req, res, next) => {
  // Skip in development or if already using HTTPS
  if (process.env.NODE_ENV === 'development' || req.secure) {
    return next();
  }

  // For Heroku, check the x-forwarded-proto header
  if (req.headers['x-forwarded-proto'] !== 'https') {
    const httpsUrl = ['https://', req.get('Host'), req.originalUrl].join('');
    logger.info(`Redirecting to HTTPS: ${httpsUrl}`);
    return res.redirect(301, httpsUrl);
  }

  next();
};

module.exports = enforceHTTPS;
