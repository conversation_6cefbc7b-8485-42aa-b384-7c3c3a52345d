import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-toastify';
import { 
  getAssignments, 
  getMyAssignments, 
  createAssignment as createAssignment<PERSON>pi,
  updateAssignment as updateAssignment<PERSON><PERSON>,
  deleteAssignment as deleteAssignment<PERSON>pi,
  updateAssignmentStatus as updateAssignmentStatusApi,
  getAssignmentMetrics as getAssignmentMetricsApi,
  getAvailableTraders,
  getAvailableMerchants
} from '../services/traderAssignmentService';
import { 
  AssignmentFilters, 
  AssignmentStatus, 
  TraderAssignment, 
  AssignmentFormData 
} from '../types/trader';
import { useAuth } from './useAuth';

export const useTraderAssignments = (filters: AssignmentFilters = {}) => {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  // Fetch all trader assignments (admin only)
  const {
    data: assignmentsData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['traderAssignments', filters],
    queryFn: () => getAssignments(filters),
    enabled: !!user && user.role === 'admin',
  });

  // Fetch assignments for current trader
  const {
    data: myAssignmentsData,
    isLoading: isLoadingMyAssignments,
    error: myAssignmentsError,
    refetch: refetchMyAssignments,
  } = useQuery({
    queryKey: ['myTraderAssignments', user?._id, filters],
    queryFn: () => getMyAssignments(filters),
    enabled: !!user && user.role === 'trader',
  });

  // Fetch available traders for assignment (admin only)
  const {
    data: availableTraders = [],
    isLoading: isLoadingTraders,
    refetch: refetchTraders,
  } = useQuery({
    queryKey: ['availableTraders'],
    queryFn: getAvailableTraders,
    enabled: !!user && user.role === 'admin',
  });

  // Fetch available merchants for assignment
  const {
    data: availableMerchants = [],
    isLoading: isLoadingMerchants,
    refetch: refetchMerchants,
  } = useQuery({
    queryKey: ['availableMerchants'],
    queryFn: () => getAvailableMerchants(),
    enabled: !!user,
  });

  // Create a new assignment
  const createAssignment = useMutation({
    mutationFn: (data: AssignmentFormData) => createAssignmentApi(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['traderAssignments'] });
      queryClient.invalidateQueries({ queryKey: ['availableMerchants'] });
      toast.success('Assignment created successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to create assignment');
    },
  });

  // Update an assignment
  const updateAssignment = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<AssignmentFormData> }) => 
      updateAssignmentApi(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['traderAssignments'] });
      queryClient.invalidateQueries({ queryKey: ['myTraderAssignments'] });
      toast.success('Assignment updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to update assignment');
    },
  });

  // Delete an assignment
  const deleteAssignment = useMutation({
    mutationFn: (id: string) => deleteAssignmentApi(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['traderAssignments'] });
      queryClient.invalidateQueries({ queryKey: ['myTraderAssignments'] });
      queryClient.invalidateQueries({ queryKey: ['availableMerchants'] });
      toast.success('Assignment deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to delete assignment');
    },
  });

  // Update assignment status
  const updateAssignmentStatus = useMutation({
    mutationFn: ({ id, status }: { id: string; status: AssignmentStatus }) =>
      updateAssignmentStatusApi(id, status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['traderAssignments'] });
      queryClient.invalidateQueries({ queryKey: ['myTraderAssignments'] });
      toast.success('Assignment status updated');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 'Failed to update assignment status'
      );
    },
  });

  // Get assignment metrics
  const useAssignmentMetrics = (assignmentId: string) => {
    return useQuery({
      queryKey: ['assignmentMetrics', assignmentId],
      queryFn: () => getAssignmentMetricsApi(assignmentId),
      enabled: !!assignmentId,
    });
  };

  return {
    // Assignments data
    assignments: assignmentsData?.data || [],
    pagination: assignmentsData?.pagination,
    myAssignments: myAssignmentsData?.data || [],
    myAssignmentsPagination: myAssignmentsData?.pagination,
    
    // Available options
    availableTraders,
    availableMerchants,
    
    // Loading states
    isLoading,
    isLoadingMyAssignments,
    isLoadingTraders,
    isLoadingMerchants,
    
    // Errors
    error,
    myAssignmentsError,
    
    // Refetch functions
    refetch,
    refetchMyAssignments,
    refetchTraders,
    refetchMerchants,
    
    // Mutations
    createAssignment: createAssignment.mutateAsync,
    isCreating: createAssignment.isPending,
    
    updateAssignment: updateAssignment.mutateAsync,
    isUpdating: updateAssignment.isPending,
    
    deleteAssignment: deleteAssignment.mutateAsync,
    isDeleting: deleteAssignment.isPending,
    
    updateAssignmentStatus: updateAssignmentStatus.mutateAsync,
    isUpdatingStatus: updateAssignmentStatus.isPending,
    
    // Queries
    useAssignmentMetrics,
  };
};

// Get assignments for current trader
export const useMyTraderAssignments = () => {
  return useQuery({
    queryKey: ['myTraderAssignments'],
    queryFn: () => getMyAssignments({}),
  });
};

// Create a new assignment
export const useCreateTraderAssignment = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: AssignmentFormData) => createAssignmentApi(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['traderAssignments'] });
      queryClient.invalidateQueries({ queryKey: ['myTraderAssignments'] });
    },
  });
};

// Update an assignment
export const useUpdateTraderAssignment = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<AssignmentFormData> }) => 
      updateAssignmentApi(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['traderAssignments'] });
      queryClient.invalidateQueries({ queryKey: ['myTraderAssignments'] });
    },
  });
};

// Delete an assignment
export const useDeleteTraderAssignment = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => deleteAssignmentApi(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['traderAssignments'] });
      queryClient.invalidateQueries({ queryKey: ['myTraderAssignments'] });
    },
  });
};
