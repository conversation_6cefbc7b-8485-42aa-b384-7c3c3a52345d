const Menu = require('../models/Menu');
const ApiError = require('../utils/ApiError');
const httpStatus = require('http-status');

/**
 * Get menu items by role
 * @param {string} role - User role (admin, merchant, trader)
 * @returns {Promise<Array>}
 */
const getMenuByRole = async (role) => {
  try {
    const menuItems = await Menu.find({
      $or: [
        { role: 'all' },
        { role: role }
      ],
      isActive: true
    })
    .sort({ order: 1, label: 1 })
    .lean();

    // Organize menu items into parent-child hierarchy
    const menuMap = {};
    const result = [];

    // First pass: create a map of all menu items
    menuItems.forEach(item => {
      menuMap[item.menuId] = { ...item, submenus: [] };
    });

    // Second pass: build the hierarchy
    menuItems.forEach(item => {
      if (item.parentId && menuMap[item.parentId]) {
        menuMap[item.parentId].submenus.push(menuMap[item.menuId]);
      } else if (!item.parentId) {
        result.push(menuMap[item.menuId]);
      }
    });

    return result;
  } catch (error) {
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error fetching menu items');
  }
};

/**
 * Get all menu items (admin only)
 * @returns {Promise<Array>}
 */
const getAllMenuItems = async () => {
  return Menu.find().sort({ role: 1, order: 1 });
};

/**
 * Create a new menu item
 * @param {Object} menuItemData
 * @returns {Promise<Object>}
 */
const createMenuItem = async (menuItemData) => {
  if (await Menu.isMenuIdTaken(menuItemData.menuId)) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Menu ID already taken');
  }
  return Menu.create(menuItemData);
};

/**
 * Update a menu item
 * @param {string} menuId
 * @param {Object} updateBody
 * @returns {Promise<Object>}
 */
const updateMenuItem = async (menuId, updateBody) => {
  const menuItem = await Menu.findOne({ menuId });
  if (!menuItem) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Menu item not found');
  }
  if (updateBody.menuId && (await Menu.isMenuIdTaken(updateBody.menuId, menuId))) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Menu ID already taken');
  }
  Object.assign(menuItem, updateBody);
  await menuItem.save();
  return menuItem;
};

/**
 * Delete a menu item
 * @param {string} menuId
 * @returns {Promise<void>}
 */
const deleteMenuItem = async (menuId) => {
  const menuItem = await Menu.findOne({ menuId });
  if (!menuItem) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Menu item not found');
  }
  
  // Check if this menu has submenus
  const hasSubmenus = await Menu.exists({ parentId: menuId });
  if (hasSubmenus) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      'Cannot delete menu item with submenus. Delete submenus first.'
    );
  }
  
  await menuItem.remove();
};

module.exports = {
  getMenuByRole,
  getAllMenuItems,
  createMenuItem,
  updateMenuItem,
  deleteMenuItem,
};
