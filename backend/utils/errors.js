const ApiError = require('./ApiError');

/**
 * Error class for 404 Not Found errors
 */
class NotFoundError extends ApiError {
  constructor(message = 'Resource not found', details = {}) {
    super(message, 404, 'NOT_FOUND', details);
  }
}

/**
 * Error class for 403 Forbidden errors
 */
class ForbiddenError extends ApiError {
  constructor(message = 'Forbidden', details = {}) {
    super(message, 403, 'FORBIDDEN', details);
  }
}

/**
 * Error class for 401 Unauthorized errors
 */
class UnauthorizedError extends ApiError {
  constructor(message = 'Unauthorized', details = {}) {
    super(message, 401, 'UNAUTHORIZED', details);
  }
}

/**
 * Error class for 400 Bad Request errors
 */
class BadRequestError extends ApiError {
  constructor(message = 'Bad Request', details = {}) {
    super(message, 400, 'BAD_REQUEST', details);
  }
}

/**
 * Error class for 409 Conflict errors
 */
class ConflictError extends ApiError {
  constructor(message = 'Conflict', details = {}) {
    super(message, 409, 'CONFLICT', details);
  }
}

/**
 * Error class for 422 Unprocessable Entity errors
 */
class ValidationError extends ApiError {
  constructor(message = 'Validation Error', details = {}) {
    super(message, 422, 'VALIDATION_ERROR', details);
  }
}

module.exports = {
  NotFoundError,
  ForbiddenError,
  UnauthorizedError,
  BadRequestError,
  ConflictError,
  ValidationError,
  ApiError
};
