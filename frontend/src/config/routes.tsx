import { lazy, Suspense, ReactNode } from 'react';
import { Navigate, RouteObject } from 'react-router-dom';
import ProtectedRoute from '../components/ProtectedRoute';
import LoadingSpinner from '../components/ui/LoadingSpinner';
import DashboardLayout from '../layouts/DashboardLayout';
import { merchantRoutes } from './merchantRoutes';

// Lazy load components for better performance
const Login = lazy(() => import('../pages/Login'));
const Register = lazy(() => import('../pages/Register'));
const AdminDashboard = lazy(() => import('../pages/AdminDashboard'));
const TraderDashboard = lazy(() => import('../pages/TraderDashboard'));
const PaymentPage = lazy(() => import('../pages/PaymentPage'));
const TraderManagement = lazy(() => import('../components/TraderManagement'));
const Profile = lazy(() => import('../pages/Profile'));

// Admin pages
const MerchantsPage = lazy(() => import('../pages/admin/MerchantsPage'));
const TradersPage = lazy(() => import('../pages/admin/TradersPage'));
const PaymentsPage = lazy(() => import('../pages/admin/PaymentsPage'));
const ReportsPage = lazy(() => import('../pages/admin/ReportsPage'));
const SettingsPage = lazy(() => import('../pages/admin/SettingsPage'));

// Loading component for Suspense fallback
const Loading = () => (
  <div className="flex items-center justify-center min-h-screen">
    <LoadingSpinner size="lg" />
  </div>
);

// Error boundary component for route components
const RouteErrorBoundary = ({ children }: { children: ReactNode }) => {
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-4">Something went wrong</h2>
        <button
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          onClick={() => window.location.reload()}
        >
          Reload Page
        </button>
      </div>
    </div>
  );
};

// Route configuration
export const routes: RouteObject[] = [
  // Root path redirects to login
  {
    path: '/',
    element: <Navigate to="/login" replace />,
  },
  // Public routes
  {
    path: '/login',
    element: (
      <Suspense fallback={<Loading />}>
        <RouteErrorBoundary>
          <Login />
        </RouteErrorBoundary>
      </Suspense>
    ),
  },
  {
    path: '/register',
    element: (
      <Suspense fallback={<Loading />}>
        <RouteErrorBoundary>
          <Register />
        </RouteErrorBoundary>
      </Suspense>
    ),
  },
  {
    path: '/payment/:merchantId',
    element: (
      <Suspense fallback={<Loading />}>
        <RouteErrorBoundary>
          <PaymentPage />
        </RouteErrorBoundary>
      </Suspense>
    ),
  },
  
  // Merchant routes
  ...merchantRoutes,

  // Admin routes
  {
    path: '/admin',
    element: (
      <ProtectedRoute role="admin">
        <DashboardLayout title="Admin Dashboard">
          <Suspense fallback={<Loading />}>
            <RouteErrorBoundary>
              <AdminDashboard />
            </RouteErrorBoundary>
          </Suspense>
        </DashboardLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: '/admin/merchants',
    element: (
      <ProtectedRoute role="admin">
        <DashboardLayout title="Merchants Management">
          <Suspense fallback={<Loading />}>
            <RouteErrorBoundary>
              <MerchantsPage />
            </RouteErrorBoundary>
          </Suspense>
        </DashboardLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: '/admin/traders',
    element: (
      <ProtectedRoute role="admin">
        <DashboardLayout title="Traders Management">
          <Suspense fallback={<Loading />}>
            <RouteErrorBoundary>
              <TradersPage />
            </RouteErrorBoundary>
          </Suspense>
        </DashboardLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: '/admin/payments',
    element: (
      <ProtectedRoute role="admin">
        <DashboardLayout title="Payments Overview">
          <Suspense fallback={<Loading />}>
            <RouteErrorBoundary>
              <PaymentsPage />
            </RouteErrorBoundary>
          </Suspense>
        </DashboardLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: '/admin/reports',
    element: (
      <ProtectedRoute role="admin">
        <DashboardLayout title="Reports & Analytics">
          <Suspense fallback={<Loading />}>
            <RouteErrorBoundary>
              <ReportsPage />
            </RouteErrorBoundary>
          </Suspense>
        </DashboardLayout>
      </ProtectedRoute>
    ),
  },
  {
    path: '/admin/settings',
    element: (
      <ProtectedRoute role="admin">
        <DashboardLayout title="System Settings">
          <Suspense fallback={<Loading />}>
            <RouteErrorBoundary>
              <SettingsPage />
            </RouteErrorBoundary>
          </Suspense>
        </DashboardLayout>
      </ProtectedRoute>
    ),
  },
  
  // Trader routes
  {
    path: '/trader',
    element: (
      <ProtectedRoute role="trader">
        <DashboardLayout title="Trader Dashboard">
          <Suspense fallback={<Loading />}>
            <RouteErrorBoundary>
              <TraderDashboard />
            </RouteErrorBoundary>
          </Suspense>
        </DashboardLayout>
      </ProtectedRoute>
    ),
  },
  
  // Catch-all route - redirect to login
  {
    path: '*',
    element: <Navigate to="/login" replace />,
  },
];
