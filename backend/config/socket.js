const { Server } = require('socket.io');
const logger = require('../utils/logger');

// Map to store user ID to socket ID mappings
const userSockets = new Map();
let io = null;

/**
 * Initialize Socket.IO with the HTTP server
 * @param {http.Server} server - HTTP server instance
 */
const setupSocket = (server) => {
  io = new Server(server, {
    cors: {
      origin: process.env.FRONTEND_URL ? process.env.FRONTEND_URL.split(',') : 'http://localhost:3000',
      methods: ['GET', 'POST'],
      credentials: true,
    },
    pingTimeout: 30000, // 30 seconds
    pingInterval: 25000, // 25 seconds
    maxHttpBufferSize: 1e8, // 100MB max payload size
  });

  // Authentication middleware for Socket.IO
  io.use((socket, next) => {
    try {
      const token = socket.handshake.auth?.token || socket.handshake.query?.token;
      
      if (!token) {
        logger.warn('Socket connection attempt without token');
        return next(new Error('Authentication error: No token provided'));
      }

      // Verify JWT token (you'll need to implement this)
      const decoded = require('jsonwebtoken').verify(
        token,
        process.env.JWT_SECRET || 'your_jwt_secret'
      );

      // Attach user info to the socket
      socket.user = {
        id: decoded.userId,
        email: decoded.email,
        role: decoded.role,
      };

      // Track the user's socket
      userSockets.set(decoded.userId, socket.id);
      
      next();
    } catch (error) {
      logger.error('Socket authentication error:', error);
      next(new Error('Authentication error: Invalid token'));
    }
  });

  // Handle new connections
  io.on('connection', (socket) => {
    const { id, user } = socket;
    logger.info(`Socket connected: ${id} (User: ${user?.id || 'unknown'})`);

    // Join rooms based on user role
    if (user) {
      // Join user's personal room
      socket.join(`user_${user.id}`);
      
      // Join role-based room
      socket.join(`role_${user.role}`);
      
      // Join admin room if admin
      if (user.role === 'admin') {
        socket.join('admin');
      }
    }

    // Handle disconnect
    socket.on('disconnect', (reason) => {
      logger.info(`Socket disconnected: ${id} (Reason: ${reason})`);
      
      // Clean up user socket mapping
      if (user) {
        userSockets.delete(user.id);
      }
    });

    // Handle errors
    socket.on('error', (error) => {
      logger.error(`Socket error (${id}):`, error);
    });
  });

  // Handle global errors
  io.on('error', (error) => {
    logger.error('Socket.IO server error:', error);
  });

  logger.info('Socket.IO server initialized');
  return io;
};

/**
 * Get the Socket.IO server instance
 * @returns {Server} Socket.IO server instance
 */
const getIO = () => {
  if (!io) {
    throw new Error('Socket.IO not initialized. Call setupSocket first.');
  }
  return io;
};

/**
 * Emit an event to a specific user
 * @param {string} userId - User ID to send the event to
 * @param {string} event - Event name
 * @param {*} data - Data to send
 */
const emitToUser = (userId, event, data) => {
  try {
    const socketId = userSockets.get(userId);
    if (socketId) {
      io.to(socketId).emit(event, data);
    } else {
      logger.warn(`User ${userId} is not connected to Socket.IO`);
    }
  } catch (error) {
    logger.error('Error emitting to user:', error);
  }
};

/**
 * Emit an event to all users in a room
 * @param {string} room - Room name
 * @param {string} event - Event name
 * @param {*} data - Data to send
 */
const emitToRoom = (room, event, data) => {
  try {
    io.to(room).emit(event, data);
  } catch (error) {
    logger.error(`Error emitting to room ${room}:`, error);
  }
};

/**
 * Emit an event to all connected clients
 * @param {string} event - Event name
 * @param {*} data - Data to send
 */
const broadcast = (event, data) => {
  try {
    io.emit(event, data);
  } catch (error) {
    logger.error('Error broadcasting event:', error);
  }
};

module.exports = {
  setupSocket,
  getIO,
  emitToUser,
  emitToRoom,
  broadcast,
  userSockets,
};
