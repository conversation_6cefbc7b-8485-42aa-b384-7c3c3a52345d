{"timestamp": "2025-07-25T16:32:21.823Z", "database": "payment-gateway", "collections": {"reconciliations": {"documentCount": 0, "sampleDocuments": [], "indexes": [{"name": "_id_", "key": {"_id": 1}, "unique": false, "sparse": false, "partialFilterExpression": null}, {"name": "reconciliationId_unique", "key": {"reconciliationId": 1}, "unique": true, "sparse": false, "partialFilterExpression": null}, {"name": "merchantId_index", "key": {"merchantId": 1}, "unique": false, "sparse": false, "partialFilterExpression": null}, {"name": "status_index", "key": {"status": 1}, "unique": false, "sparse": false, "partialFilterExpression": null}, {"name": "date_range_index", "key": {"startDate": -1, "endDate": -1}, "unique": false, "sparse": false, "partialFilterExpression": null}]}, "payments": {"documentCount": 0, "sampleDocuments": [], "indexes": [{"name": "_id_", "key": {"_id": 1}, "unique": false, "sparse": false, "partialFilterExpression": null}, {"name": "paymentId_unique", "key": {"paymentId": 1}, "unique": true, "sparse": false, "partialFilterExpression": null}, {"name": "merchantId_index", "key": {"merchantId": 1}, "unique": false, "sparse": false, "partialFilterExpression": null}, {"name": "transactionId_index", "key": {"transactionId": 1}, "unique": false, "sparse": false, "partialFilterExpression": null}, {"name": "status_index", "key": {"status": 1}, "unique": false, "sparse": false, "partialFilterExpression": null}, {"name": "createdAt_desc", "key": {"createdAt": -1}, "unique": false, "sparse": false, "partialFilterExpression": null}]}, "adminmenus": {"documentCount": 15, "sampleDocuments": [{"_id": "687e37c32ab16b2c1aa10131", "to": "/admin/dashboard", "label": "Dashboard", "__v": 0}, {"_id": "687e37c32ab16b2c1aa10132", "to": "/admin/analytics", "label": "Analytics", "__v": 0}, {"_id": "687e37c32ab16b2c1aa10133", "to": "/admin/merchants", "label": "Merchant Management", "__v": 0}, {"_id": "687e37c32ab16b2c1aa10134", "to": "/admin/merchants/:merchantId", "label": "Merchant Details", "__v": 0}, {"_id": "687e37c32ab16b2c1aa10135", "to": "/admin/traders", "label": "Trader Management", "__v": 0}], "indexes": [{"name": "_id_", "key": {"_id": 1}, "unique": false, "sparse": false, "partialFilterExpression": null}, {"name": "path_unique", "key": {"to": 1}, "unique": true, "sparse": true, "partialFilterExpression": null}]}, "transactions": {"documentCount": 6, "sampleDocuments": [{"_id": "6881cde138f0c102b1675cf8", "transactionId": "txn_30f7e1f5b948ba83758cb169ca6b2a12", "merchantId": "6881cde038f0c102b1675cef", "traderId": "6881cde138f0c102b1675cf6", "amount": 99.99, "currency": "USD", "status": "completed", "paymentMethod": "card", "paymentDetails": {"cardLast4": "4242", "cardBrand": "Visa"}, "customerInfo": {"email": "<EMAIL>", "name": "Customer One", "billingAddress": {"street": "123 Main St", "city": "New York", "state": "NY", "zipCode": "10001", "country": "US"}}, "fees": {"processingFee": 3.2, "platformFee": 0.5}, "webhookDelivered": false, "webhookAttempts": 0, "refundAmount": 0, "disputeStatus": "none", "riskScore": 0, "settlementDate": "2025-07-26T06:08:33.952Z", "notes": [], "createdAt": "2025-07-24T06:08:33.957Z", "updatedAt": "2025-07-24T06:08:33.957Z", "netAmount": 96.28999999999999, "__v": 0}, {"_id": "6881cde138f0c102b1675cfa", "transactionId": "txn_7582d6474b6777b307629abee7b79c30", "merchantId": "6881cde138f0c102b1675cf4", "amount": 249.99, "currency": "USD", "status": "completed", "paymentMethod": "card", "paymentDetails": {"cardLast4": "1234", "cardBrand": "Mastercard"}, "customerInfo": {"email": "<EMAIL>", "name": "Customer Two", "billingAddress": {"street": "456 Oak Ave", "city": "Los Angeles", "state": "CA", "zipCode": "90210", "country": "US"}}, "fees": {"processingFee": 7.55, "platformFee": 1.25}, "webhookDelivered": false, "webhookAttempts": 0, "refundAmount": 0, "disputeStatus": "none", "riskScore": 15, "settlementDate": "2025-07-26T06:08:33.952Z", "notes": [], "createdAt": "2025-07-24T06:08:33.961Z", "updatedAt": "2025-07-24T06:08:33.961Z", "netAmount": 241.19, "__v": 0}, {"_id": "6881cde138f0c102b1675cfc", "transactionId": "txn_03c5dfab9234744253b7019e53af7940", "merchantId": "6881cde038f0c102b1675cef", "amount": 49.99, "currency": "USD", "status": "failed", "paymentMethod": "card", "paymentDetails": {"cardLast4": "0000", "cardBrand": "Visa"}, "customerInfo": {"email": "<EMAIL>", "name": "Customer Three"}, "fees": {"processingFee": 1.75, "platformFee": 0.25}, "webhookDelivered": false, "webhookAttempts": 0, "refundAmount": 0, "disputeStatus": "none", "riskScore": 25, "notes": [], "createdAt": "2025-07-24T06:08:33.964Z", "updatedAt": "2025-07-24T06:08:33.964Z", "netAmount": 47.99, "__v": 0}, {"_id": "68835ac1ea354209faabe0b9", "transactionId": "txn_57a6d25cac3318e30ee47d0945c2ab8d", "merchantId": "68835abfea354209faabe0b3", "traderId": "68835ac0ea354209faabe0b7", "amount": 99.99, "currency": "USD", "status": "completed", "paymentMethod": "card", "paymentDetails": {"cardLast4": "4242", "cardBrand": "Visa"}, "customerInfo": {"email": "<EMAIL>", "name": "Customer One", "billingAddress": {"street": "123 Main St", "city": "New York", "state": "NY", "zipCode": "10001", "country": "US"}}, "fees": {"processingFee": 3.2, "platformFee": 0.5}, "webhookDelivered": false, "webhookAttempts": 0, "refundAmount": 0, "disputeStatus": "none", "riskScore": 0, "settlementDate": "2025-07-27T10:21:53.746Z", "notes": [], "createdAt": "2025-07-25T10:21:53.756Z", "updatedAt": "2025-07-25T10:21:53.756Z", "netAmount": 96.28999999999999, "__v": 0}, {"_id": "68835ac1ea354209faabe0bb", "transactionId": "txn_70baa55dec0641e04339f30c634ebae3", "merchantId": "68835ac0ea354209faabe0b5", "amount": 249.99, "currency": "USD", "status": "completed", "paymentMethod": "card", "paymentDetails": {"cardLast4": "1234", "cardBrand": "Mastercard"}, "customerInfo": {"email": "<EMAIL>", "name": "Customer Two", "billingAddress": {"street": "456 Oak Ave", "city": "Los Angeles", "state": "CA", "zipCode": "90210", "country": "US"}}, "fees": {"processingFee": 7.55, "platformFee": 1.25}, "webhookDelivered": false, "webhookAttempts": 0, "refundAmount": 0, "disputeStatus": "none", "riskScore": 15, "settlementDate": "2025-07-27T10:21:53.746Z", "notes": [], "createdAt": "2025-07-25T10:21:53.778Z", "updatedAt": "2025-07-25T10:21:53.778Z", "netAmount": 241.19, "__v": 0}], "indexes": [{"name": "_id_", "key": {"_id": 1}, "unique": false, "sparse": false, "partialFilterExpression": null}, {"name": "merchantId_1_createdAt_-1", "key": {"merchantId": 1, "createdAt": -1}, "unique": false, "sparse": false, "partialFilterExpression": null}, {"name": "customerInfo.email_1", "key": {"customerInfo.email": 1}, "unique": false, "sparse": false, "partialFilterExpression": null}, {"name": "merchantId_index", "key": {"merchantId": 1}, "unique": false, "sparse": false, "partialFilterExpression": null}, {"name": "traderId_index", "key": {"traderId": 1}, "unique": false, "sparse": false, "partialFilterExpression": null}, {"name": "createdAt_desc", "key": {"createdAt": -1}, "unique": false, "sparse": false, "partialFilterExpression": null}, {"name": "customerEmail_index", "key": {"customerInfo.email": 1}, "unique": false, "sparse": true, "partialFilterExpression": null}, {"name": "transactionId_unique", "key": {"transactionId": 1}, "unique": true, "sparse": false, "partialFilterExpression": null}, {"name": "status_index", "key": {"status": 1}, "unique": false, "sparse": false, "partialFilterExpression": null}]}, "webhooks": {"documentCount": 0, "sampleDocuments": [], "indexes": [{"name": "_id_", "key": {"_id": 1}, "unique": false, "sparse": false, "partialFilterExpression": null}, {"name": "webhookId_unique", "key": {"webhookId": 1}, "unique": true, "sparse": false, "partialFilterExpression": null}, {"name": "merchantId_index", "key": {"merchantId": 1}, "unique": false, "sparse": false, "partialFilterExpression": null}, {"name": "eventType_index", "key": {"eventType": 1}, "unique": false, "sparse": false, "partialFilterExpression": null}, {"name": "isActive_index", "key": {"isActive": 1}, "unique": false, "sparse": false, "partialFilterExpression": null}]}, "users": {"documentCount": 9, "sampleDocuments": [{"_id": "6881cde038f0c102b1675ce8", "name": "Admin User", "email": "<EMAIL>", "password": "$2a$12$ZY0X0WZlMNNlqvYoAEwfVu1x7vgbcB9WTbXcsb1kG..Kp1SIbgOQu", "role": "admin", "isVerified": true, "twoFactorEnabled": false, "security": {"passwordHistory": [], "loginHistory": [{"timestamp": "2025-07-25T10:08:09.916Z", "success": true, "_id": "688357896d5b185909af84e8"}, {"timestamp": "2025-07-25T10:11:25.110Z", "success": true, "_id": "6883584d6d5b185909af84ec"}, {"timestamp": "2025-07-25T10:12:43.251Z", "success": true, "_id": "6883589b6d5b185909af84f1"}, {"timestamp": "2025-07-25T10:24:19.013Z", "success": true, "_id": "68835b536d5b185909af84f7"}, {"timestamp": "2025-07-25T10:38:07.589Z", "success": true, "_id": "68835e8f6501eda695a663c4"}, {"timestamp": "2025-07-25T10:38:47.765Z", "success": true, "_id": "68835eb76501eda695a663cc"}, {"timestamp": "2025-07-25T11:09:34.353Z", "success": true, "_id": "688365ee6501eda695a663db"}]}, "isActive": true, "loginAttempts": 0, "twoFactorRecoveryCodes": [], "sessions": [], "createdAt": "2025-07-24T06:08:32.200Z", "updatedAt": "2025-07-25T11:09:34.364Z", "__v": 7, "lastActive": "2025-07-25T11:09:34.357Z", "lastLogin": "2025-07-25T11:09:34.356Z", "userId": "6883a71c0dd872be81f0e6c8"}, {"_id": "6881cde038f0c102b1675cef", "name": "<PERSON>", "email": "<EMAIL>", "password": "$2a$12$kIlXpX8Zmxk2vfYZyVBPyu8Duh6bZ5kHnpfsj3v4Vke96ktv/eE9O", "role": "merchant", "isVerified": true, "twoFactorEnabled": false, "security": {"passwordHistory": [], "loginHistory": [{"timestamp": "2025-07-25T10:40:02.093Z", "success": true, "_id": "68835f026501eda695a663cf"}]}, "businessName": "Tech Solutions Inc", "businessType": "Technology", "phone": "+1234567890", "website": "https://techsolutions.com", "isActive": true, "loginAttempts": 0, "twoFactorRecoveryCodes": [], "sessions": [], "apiKey": "pk_7b4333b4f81b4c773a5aacdfe0e728e175c99988d8a7b25ce408dcac2ea79596", "createdAt": "2025-07-24T06:08:32.512Z", "updatedAt": "2025-07-25T10:40:02.096Z", "__v": 1, "lastActive": "2025-07-25T10:40:02.095Z", "lastLogin": "2025-07-25T10:40:02.094Z", "userId": "6883a71c0dd872be81f0e6c9"}, {"_id": "6881cde138f0c102b1675cf4", "name": "<PERSON>", "email": "<EMAIL>", "password": "$2a$12$mjEHvmECPOSeXBjh.FGCnOzfKxcVgObaThOrAvnXBX0QW0kGjmYw.", "role": "merchant", "isVerified": true, "twoFactorEnabled": false, "security": {"passwordHistory": [], "loginHistory": []}, "businessName": "Fashion Store", "businessType": "Retail", "phone": "+1234567891", "website": "https://fashionstore.com", "isActive": true, "loginAttempts": 0, "twoFactorRecoveryCodes": [], "sessions": [], "apiKey": "pk_fa9a64ec7137420cfd3759b8707827389dc2a0f1ab7e46955ca4b8cdf01762b6", "createdAt": "2025-07-24T06:08:33.079Z", "updatedAt": "2025-07-24T06:08:33.079Z", "__v": 0, "userId": "6883a71c0dd872be81f0e6ca"}, {"_id": "6881cde138f0c102b1675cf6", "name": "<PERSON>", "email": "<EMAIL>", "password": "$2a$12$N1baRBLaFD5z8nPC3uD9jOSvaw6omtnmZhBvzTolwT8giUFVrE6VC", "role": "trader", "isVerified": true, "twoFactorEnabled": false, "security": {"passwordHistory": [], "loginHistory": [{"timestamp": "2025-07-25T10:42:05.964Z", "success": true, "_id": "68835f7d6501eda695a663d2"}]}, "isActive": true, "loginAttempts": 0, "twoFactorRecoveryCodes": [], "sessions": [], "createdAt": "2025-07-24T06:08:33.598Z", "updatedAt": "2025-07-25T10:42:05.965Z", "__v": 1, "lastActive": "2025-07-25T10:42:05.964Z", "lastLogin": "2025-07-25T10:42:05.964Z", "userId": "6883a71c0dd872be81f0e6cb"}, {"_id": "68837690fac47de02f732371", "name": "Test User", "email": "<EMAIL>", "password": "$2a$12$ll8vWHyurML7RRTvJGf9KupLQbKp3GCQ5UUY3ImNP2SjNr3gMBPsy", "role": "merchant", "isVerified": false, "twoFactorEnabled": false, "security": {"passwordHistory": [], "loginHistory": [{"timestamp": "2025-07-25T12:23:11.574Z", "success": true, "_id": "6883772ffac47de02f732374"}]}, "businessName": "Test Business", "phone": "1234567890", "isActive": true, "loginAttempts": 0, "twoFactorRecoveryCodes": [], "sessions": [], "createdAt": "2025-07-25T12:20:32.430Z", "updatedAt": "2025-07-25T12:23:11.578Z", "__v": 1, "lastActive": "2025-07-25T12:23:11.577Z", "lastLogin": "2025-07-25T12:23:11.577Z", "userId": "6883a71c0dd872be81f0e6cc"}], "indexes": [{"name": "_id_", "key": {"_id": 1}, "unique": false, "sparse": false, "partialFilterExpression": null}, {"name": "apiKey_1", "key": {"apiKey": 1}, "unique": false, "sparse": false, "partialFilterExpression": null}, {"name": "userId_unique", "key": {"userId": 1}, "unique": true, "sparse": false, "partialFilterExpression": null}, {"name": "role_index", "key": {"role": 1}, "unique": false, "sparse": false, "partialFilterExpression": null}, {"name": "email_unique", "key": {"email": 1}, "unique": true, "sparse": false, "partialFilterExpression": null}]}, "merchants": {"documentCount": 3, "sampleDocuments": [{"_id": "686eacedb7192975f7e7e9a4", "merchantId": "686eacedb7192975f7e7e9a3", "userId": "686eacedb7192975f7e7e9a2", "businessInfo": {"businessName": "Test Business", "businessType": "retail", "website": "https://testbusiness.com"}, "bankDetails": {"accountNumber": "************", "bankName": "HDFC Bank", "ifscCode": "HDFC0001234"}, "kycDocuments": [{"documentType": "business_license", "documentUrl": "https://example.com/business_license.jpg", "verificationStatus": "verified", "_id": "686eacedb7192975f7e7e9a5", "uploadedAt": "2025-07-09T17:54:53.678Z"}, {"documentType": "tax_certificate", "documentUrl": "https://example.com/tax_certificate.jpg", "verificationStatus": "verified", "_id": "686eacedb7192975f7e7e9a6", "uploadedAt": "2025-07-09T17:54:53.679Z"}], "paymentSettings": {"acceptedMethods": [], "currencies": [], "commissionRate": 2.5, "settlementCycle": "weekly"}, "isActive": false, "createdAt": "2025-07-09T17:54:53.680Z", "updatedAt": "2025-07-09T17:54:53.680Z", "__v": 0}, {"_id": "6871cc0c9984e8f18a4fd200", "merchantId": "6871cc0c9984e8f18a4fd1ff", "userId": "6871cc0c9984e8f18a4fd1fe", "businessInfo": {"businessName": "Test Business", "businessType": "retail", "website": "https://testbusiness.com"}, "bankDetails": {"accountNumber": "************", "bankName": "HDFC Bank", "ifscCode": "HDFC0001234"}, "kycDocuments": [{"documentType": "business_license", "documentUrl": "https://example.com/business_license.jpg", "verificationStatus": "verified", "_id": "6871cc0c9984e8f18a4fd201", "uploadedAt": "2025-07-12T02:44:28.420Z"}, {"documentType": "tax_certificate", "documentUrl": "https://example.com/tax_certificate.jpg", "verificationStatus": "verified", "_id": "6871cc0c9984e8f18a4fd202", "uploadedAt": "2025-07-12T02:44:28.421Z"}], "paymentSettings": {"acceptedMethods": [], "currencies": [], "commissionRate": 2.5, "settlementCycle": "weekly"}, "isActive": false, "createdAt": "2025-07-12T02:44:28.422Z", "updatedAt": "2025-07-12T02:44:28.422Z", "__v": 0}, {"_id": "6871cc7014321211ef6aee6a", "merchantId": "6871cc7014321211ef6aee69", "userId": "6871cc7014321211ef6aee68", "businessInfo": {"businessName": "Test Business", "businessType": "retail", "website": "https://testbusiness.com"}, "bankDetails": {"accountNumber": "************", "bankName": "HDFC Bank", "ifscCode": "HDFC0001234"}, "kycDocuments": [{"documentType": "business_license", "documentUrl": "https://example.com/business_license.jpg", "verificationStatus": "verified", "_id": "6871cc7014321211ef6aee6b", "uploadedAt": "2025-07-12T02:46:08.486Z"}, {"documentType": "tax_certificate", "documentUrl": "https://example.com/tax_certificate.jpg", "verificationStatus": "verified", "_id": "6871cc7014321211ef6aee6c", "uploadedAt": "2025-07-12T02:46:08.486Z"}], "paymentSettings": {"acceptedMethods": [], "currencies": [], "commissionRate": 2.5, "settlementCycle": "weekly"}, "isActive": false, "createdAt": "2025-07-12T02:46:08.487Z", "updatedAt": "2025-07-12T02:46:08.487Z", "__v": 0}], "indexes": [{"name": "_id_", "key": {"_id": 1}, "unique": false, "sparse": false, "partialFilterExpression": null}, {"name": "userId_index", "key": {"userId": 1}, "unique": false, "sparse": false, "partialFilterExpression": null}, {"name": "merchantId_unique", "key": {"merchantId": 1}, "unique": true, "sparse": false, "partialFilterExpression": null}]}, "reports": {"documentCount": 0, "sampleDocuments": [], "indexes": [{"name": "_id_", "key": {"_id": 1}, "unique": false, "sparse": false, "partialFilterExpression": null}, {"name": "reportId_unique", "key": {"reportId": 1}, "unique": true, "sparse": false, "partialFilterExpression": null}, {"name": "type_index", "key": {"type": 1}, "unique": false, "sparse": false, "partialFilterExpression": null}, {"name": "createdAt_desc", "key": {"createdAt": -1}, "unique": false, "sparse": false, "partialFilterExpression": null}]}, "settlements": {"documentCount": 0, "sampleDocuments": [], "indexes": [{"name": "_id_", "key": {"_id": 1}, "unique": false, "sparse": false, "partialFilterExpression": null}, {"name": "settlementId_unique", "key": {"settlementId": 1}, "unique": true, "sparse": false, "partialFilterExpression": null}, {"name": "merchantId_index", "key": {"merchantId": 1}, "unique": false, "sparse": false, "partialFilterExpression": null}, {"name": "status_index", "key": {"status": 1}, "unique": false, "sparse": false, "partialFilterExpression": null}, {"name": "settlementDate_desc", "key": {"settlementDate": -1}, "unique": false, "sparse": false, "partialFilterExpression": null}]}, "webhooklogs": {"documentCount": 0, "sampleDocuments": [], "indexes": [{"name": "_id_", "key": {"_id": 1}, "unique": false, "sparse": false, "partialFilterExpression": null}, {"name": "webhookLogId_unique", "key": {"webhookLogId": 1}, "unique": true, "sparse": false, "partialFilterExpression": null}, {"name": "webhookId_index", "key": {"webhookId": 1}, "unique": false, "sparse": false, "partialFilterExpression": null}, {"name": "status_index", "key": {"status": 1}, "unique": false, "sparse": false, "partialFilterExpression": null}, {"name": "createdAt_desc", "key": {"createdAt": -1}, "unique": false, "sparse": false, "partialFilterExpression": null}]}, "merchantmenus": {"documentCount": 10, "sampleDocuments": [{"_id": "687e37bdd45b9ac1ffd7bcff", "to": "/merchant/dashboard", "label": "Dashboard", "__v": 0}, {"_id": "687e37bdd45b9ac1ffd7bd00", "to": "/merchant/transactions", "label": "Transaction History", "__v": 0}, {"_id": "687e37bdd45b9ac1ffd7bd01", "to": "/merchant/collections", "label": "Collections", "__v": 0}, {"_id": "687e37bdd45b9ac1ffd7bd02", "to": "/merchant/profile", "label": "Profile Settings", "__v": 0}, {"_id": "687e37bdd45b9ac1ffd7bd03", "to": "/merchant/billing", "label": "Billing and Settlements", "__v": 0}], "indexes": [{"name": "_id_", "key": {"_id": 1}, "unique": false, "sparse": false, "partialFilterExpression": null}, {"name": "path_unique", "key": {"to": 1}, "unique": true, "sparse": true, "partialFilterExpression": null}]}, "tradermenus": {"documentCount": 8, "sampleDocuments": [{"_id": "687e37c1dac9deef2b570e16", "to": "/trader/dashboard", "label": "Dashboard", "__v": 0}, {"_id": "687e37c1dac9deef2b570e17", "to": "/trader/transactions", "label": "Transaction History", "__v": 0}, {"_id": "687e37c1dac9deef2b570e18", "to": "/trader/collections", "label": "Collections", "__v": 0}, {"_id": "687e37c1dac9deef2b570e19", "to": "/trader/merchants", "label": "Assigned Merchants", "__v": 0}, {"_id": "687e37c1dac9deef2b570e1a", "to": "/trader/payment", "label": "Payment Page", "__v": 0}], "indexes": [{"name": "_id_", "key": {"_id": 1}, "unique": false, "sparse": false, "partialFilterExpression": null}, {"name": "path_unique", "key": {"to": 1}, "unique": true, "sparse": true, "partialFilterExpression": null}]}, "traders": {"documentCount": 0, "sampleDocuments": [], "indexes": [{"name": "_id_", "key": {"_id": 1}, "unique": false, "sparse": false, "partialFilterExpression": null}, {"name": "traderId_unique", "key": {"traderId": 1}, "unique": true, "sparse": false, "partialFilterExpression": null}, {"name": "userId_unique", "key": {"userId": 1}, "unique": true, "sparse": false, "partialFilterExpression": null}, {"name": "email_unique", "key": {"email": 1}, "unique": true, "sparse": false, "partialFilterExpression": null}]}}}