const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

async function testAPI() {
  console.log('🧪 Testing Payment Gateway API Endpoints\n');
  
  try {
    // Test 1: Payment Stats
    console.log('1️⃣ Testing Payment Stats...');
    const paymentStats = await axios.get(`${BASE_URL}/payments/stats`);
    console.log('✅ Payment Stats:', paymentStats.data.data);
    
    // Test 2: Recent Payments
    console.log('\n2️⃣ Testing Recent Payments...');
    const payments = await axios.get(`${BASE_URL}/payments?limit=3`);
    console.log('✅ Recent Payments Count:', payments.data.count);
    console.log('📋 Sample Transaction:', {
      id: payments.data.data[0]?.transactionId?.substring(0, 15) + '...',
      amount: payments.data.data[0]?.amount,
      status: payments.data.data[0]?.status,
      merchant: payments.data.data[0]?.merchantId?.businessName
    });
    
    // Test 3: User Stats
    console.log('\n3️⃣ Testing User Stats...');
    const userStats = await axios.get(`${BASE_URL}/users/stats/overview`);
    console.log('✅ User Stats:', userStats.data.data);
    
    // Test 4: Users List
    console.log('\n4️⃣ Testing Users List...');
    const users = await axios.get(`${BASE_URL}/users?limit=3`);
    console.log('✅ Users Count:', users.data.count);
    console.log('👥 Sample Users:');
    users.data.data.forEach(user => {
      console.log(`   - ${user.name} (${user.role}) - ${user.email}`);
    });
    
    // Test 5: Merchants Only
    console.log('\n5️⃣ Testing Merchants Only...');
    const merchants = await axios.get(`${BASE_URL}/users/role/merchant`);
    console.log('✅ Merchants Count:', merchants.data.count);
    merchants.data.data.forEach(merchant => {
      console.log(`   - ${merchant.businessName || merchant.name} - ${merchant.email}`);
    });
    
    // Test 6: Completed Transactions
    console.log('\n6️⃣ Testing Completed Transactions...');
    const completedTx = await axios.get(`${BASE_URL}/payments/status/completed`);
    console.log('✅ Completed Transactions:', completedTx.data.count);
    
    console.log('\n🎉 All API endpoints are working correctly!');
    console.log('\n📊 Summary:');
    console.log(`   • Total Transactions: ${paymentStats.data.data.totalTransactions}`);
    console.log(`   • Total Revenue: $${paymentStats.data.data.totalRevenue.toFixed(2)}`);
    console.log(`   • Success Rate: ${paymentStats.data.data.successRate.toFixed(1)}%`);
    console.log(`   • Total Users: ${userStats.data.data.totalUsers}`);
    console.log(`   • Active Merchants: ${merchants.data.count}`);
    
  } catch (error) {
    console.error('❌ API Test Failed:', error.message);
    if (error.response) {
      console.error('Response:', error.response.data);
    }
  }
}

testAPI();
