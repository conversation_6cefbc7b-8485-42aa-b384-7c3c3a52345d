import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { 
  Box, 
  Button, 
  TextField, 
  FormControl, 
  InputLabel, 
  Select, 
  MenuItem, 
  Typography, 
  Paper, 
  Grid, 
  Divider, 
  FormControlLabel, 
  Switch,
  Card,
  CardContent,
  CardHeader,
  FormHelperText,
  Chip
} from '@mui/material';
import { 
  Save as SaveIcon, 
  Cancel as CancelIcon, 
  Add as AddIcon
} from '@mui/icons-material';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useSnackbar } from 'notistack';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import api from '../../../services/api';
import PageHeader from '../../../components/layout/PageHeader';
import ReserveRuleForm from './ReserveRuleForm';

// Validation schema
const validationSchema = Yup.object({
  name: Yup.string().required('Name is required'),
  description: Yup.string(),
  status: Yup.string().oneOf(['active', 'inactive', 'draft']).required('Status is required'),
  isDefault: Yup.boolean(),
  effectiveFrom: Yup.date().nullable(),
  effectiveTo: Yup.date()
    .nullable()
    .when('effectiveFrom', (effectiveFrom) => 
      effectiveFrom 
        ? Yup.date().min(effectiveFrom, 'End date must be after start date') 
        : Yup.date()
    ),
  rules: Yup.array().of(
    Yup.object({
      type: Yup.string().oneOf(['percentage', 'fixed_amount', 'tiered']).required('Type is required'),
      percentage: Yup.number().when('type', {
        is: 'percentage',
        then: Yup.number()
          .min(0, 'Percentage must be at least 0')
          .max(100, 'Percentage cannot exceed 100')
          .required('Percentage is required')
      }),
      fixedAmount: Yup.number().when('type', {
        is: 'fixed_amount',
        then: Yup.number()
          .min(0, 'Amount must be at least 0')
          .required('Amount is required')
      }),
      minReserve: Yup.number().min(0, 'Minimum reserve must be at least 0'),
      maxReserve: Yup.number().min(0, 'Maximum reserve must be at least 0'),
      priority: Yup.number().integer('Priority must be an integer').min(0, 'Priority must be at least 0'),
      isActive: Yup.boolean(),
      description: Yup.string(),
      tiers: Yup.array().when('type', {
        is: 'tiered',
        then: Yup.array().of(
          Yup.object({
            minAmount: Yup.number().required('Min amount is required').min(0, 'Must be at least 0'),
            maxAmount: Yup.number().nullable(),
            percentage: Yup.number()
              .min(0, 'Percentage must be at least 0')
              .max(100, 'Percentage cannot exceed 100'),
            fixedAmount: Yup.number().min(0, 'Amount must be at least 0')
          }).test(
            'tier-validation',
            'Either percentage or fixed amount is required',
            (value) => value.percentage !== undefined || value.fixedAmount !== undefined
          )
        ).min(1, 'At least one tier is required')
      })
    })
  ).min(1, 'At least one rule is required'),
  config: Yup.object({
    autoReplenish: Yup.object({
      enabled: Yup.boolean(),
      threshold: Yup.number().when('enabled', {
        is: true,
        then: Yup.number().min(0, 'Threshold must be at least 0').required('Threshold is required')
      }),
      amount: Yup.number().when('enabled', {
        is: true,
        then: Yup.number().min(0, 'Amount must be at least 0').required('Amount is required')
      }),
      sourceAccount: Yup.string().when('enabled', {
        is: true,
        then: Yup.string().required('Source account is required')
      })
    })
  })
});

const ReserveStrategyForm = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  const [loading, setLoading] = useState(!!id);
  const [expandedRule, setExpandedRule] = useState(0);
  const [sourceAccounts, setSourceAccounts] = useState([]);

  const formik = useFormik({
    initialValues: {
      name: '',
      description: '',
      status: 'draft',
      isDefault: false,
      effectiveFrom: null,
      effectiveTo: null,
      rules: [{
        type: 'percentage',
        percentage: 10,
        minReserve: 0,
        maxReserve: null,
        priority: 0,
        isActive: true,
        description: '',
        tiers: []
      }],
      tags: [],
      config: {
        autoReplenish: {
          enabled: false,
          threshold: 0,
          amount: 0,
          sourceAccount: ''
        },
        notifications: {
          lowBalance: {
            enabled: true,
            threshold: 0,
            recipients: []
          }
        }
      }
    },
    validationSchema,
    onSubmit: async (values) => {
      try {
        setLoading(true);
        
        // Format dates
        const payload = {
          ...values,
          effectiveFrom: values.effectiveFrom ? new Date(values.effectiveFrom).toISOString() : null,
          effectiveTo: values.effectiveTo ? new Date(values.effectiveTo).toISOString() : null,
          rules: values.rules.map(rule => ({
            ...rule,
            // Remove empty strings from tiers
            tiers: rule.tiers?.map(tier => ({
              ...tier,
              maxAmount: tier.maxAmount || null
            })) || []
          }))
        };

        if (id) {
          // Update existing strategy
          await api.put(`/reserve-strategies/${id}`, payload);
          enqueueSnackbar('Reserve strategy updated successfully', { variant: 'success' });
        } else {
          // Create new strategy
          await api.post('/reserve-strategies', payload);
          enqueueSnackbar('Reserve strategy created successfully', { variant: 'success' });
        }
        
        navigate('/reserve-strategies');
      } catch (error) {
        console.error('Error saving reserve strategy:', error);
        enqueueSnackbar(
          error.response?.data?.message || 'Failed to save reserve strategy', 
          { variant: 'error' }
        );
      } finally {
        setLoading(false);
      }
    }
  });

  // Load strategy data if editing
  useEffect(() => {
    if (!id) return;

    const fetchStrategy = async () => {
      try {
        const response = await api.get(`/reserve-strategies/${id}`);
        const strategy = response.data;
        
        // Format dates for the date picker
        formik.setValues({
          ...strategy,
          effectiveFrom: strategy.effectiveFrom ? new Date(strategy.effectiveFrom) : null,
          effectiveTo: strategy.effectiveTo ? new Date(strategy.effectiveTo) : null
        });
      } catch (error) {
        console.error('Error fetching reserve strategy:', error);
        enqueueSnackbar('Failed to load reserve strategy', { variant: 'error' });
        navigate('/reserve-strategies');
      } finally {
        setLoading(false);
      }
    };

    fetchStrategy();
  }, [id]);

  // Load source accounts for auto-replenish
  useEffect(() => {
    const fetchSourceAccounts = async () => {
      try {
        // Replace with actual API call to get source accounts
        // const response = await api.get('/accounts?type=source');
        // setSourceAccounts(response.data);
        
        // Mock data for now
        setSourceAccounts([
          { id: 'acc_1', name: 'Main Operating Account' },
          { id: 'acc_2', name: 'Reserve Fund' },
          { id: 'acc_3', name: 'Backup Fund' }
        ]);
      } catch (error) {
        console.error('Error fetching source accounts:', error);
      }
    };

    fetchSourceAccounts();
  }, []);

  // Rule management functions
  const handleAddRule = () => {
    const newRule = {
      type: 'percentage',
      percentage: 10,
      minReserve: 0,
      maxReserve: null,
      priority: formik.values.rules.length,
      isActive: true,
      description: '',
      tiers: []
    };
    
    formik.setFieldValue('rules', [...formik.values.rules, newRule]);
    setExpandedRule(formik.values.rules.length);
  };

  const handleRemoveRule = (index) => {
    if (formik.values.rules.length <= 1) {
      enqueueSnackbar('At least one rule is required', { variant: 'warning' });
      return;
    }
    
    const newRules = [...formik.values.rules];
    newRules.splice(index, 1);
    formik.setFieldValue('rules', newRules);
    
    if (expandedRule >= index) {
      setExpandedRule(Math.max(0, expandedRule - 1));
    }
  };

  const handleUpdateRule = (index, updatedRule) => {
    const newRules = [...formik.values.rules];
    newRules[index] = updatedRule;
    formik.setFieldValue('rules', newRules);
  };

  const handleAddTier = (ruleIndex) => {
    const newRules = [...formik.values.rules];
    const rule = { ...newRules[ruleIndex] };
    
    if (!rule.tiers) {
      rule.tiers = [];
    }
    
    const lastTier = rule.tiers[rule.tiers.length - 1];
    const minAmount = lastTier ? (lastTier.maxAmount || 0) : 0;
    
    rule.tiers = [
      ...rule.tiers,
      {
        minAmount,
        maxAmount: null,
        percentage: 10,
        fixedAmount: 0
      }
    ];
    
    newRules[ruleIndex] = rule;
    formik.setFieldValue('rules', newRules);
  };

  const handleRemoveTier = (ruleIndex, tierIndex) => {
    const newRules = [...formik.values.rules];
    const rule = { ...newRules[ruleIndex] };
    
    if (!rule.tiers || rule.tiers.length <= 1) {
      enqueueSnackbar('At least one tier is required', { variant: 'warning' });
      return;
    }
    
    rule.tiers = rule.tiers.filter((_, i) => i !== tierIndex);
    newRules[ruleIndex] = rule;
    formik.setFieldValue('rules', newRules);
  };

  const handleToggleExpand = (index) => {
    setExpandedRule(expandedRule === index ? -1 : index);
  };

  // Handle tag input
  const handleTagAdd = (tag) => {
    if (tag && !formik.values.tags.includes(tag)) {
      formik.setFieldValue('tags', [...formik.values.tags, tag]);
    }
  };

  const handleTagDelete = (tagToDelete) => {
    formik.setFieldValue(
      'tags',
      formik.values.tags.filter((tag) => tag !== tagToDelete)
    );
  };

  // Handle notification recipients
  const handleAddRecipient = (recipient) => {
    if (recipient && !formik.values.config.notifications.lowBalance.recipients.includes(recipient)) {
      const updatedConfig = { ...formik.values.config };
      updatedConfig.notifications.lowBalance.recipients = [
        ...updatedConfig.notifications.lowBalance.recipients,
        recipient
      ];
      formik.setFieldValue('config', updatedConfig);
    }
  };

  const handleRemoveRecipient = (recipientToRemove) => {
    const updatedConfig = { ...formik.values.config };
    updatedConfig.notifications.lowBalance.recipients = 
      updatedConfig.notifications.lowBalance.recipients.filter(
        (recipient) => recipient !== recipientToRemove
      );
    formik.setFieldValue('config', updatedConfig);
  };

  // Render the form UI
  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <form onSubmit={formik.handleSubmit}>
        <PageHeader 
          title={id ? 'Edit Reserve Strategy' : 'Create Reserve Strategy'}
          subtitle={id ? 'Update the reserve strategy details' : 'Define a new reserve strategy'}
          action={
            <>
              <Button
                variant="outlined"
                color="secondary"
                startIcon={<CancelIcon />}
                onClick={() => navigate('/reserve-strategies')}
                sx={{ mr: 1 }}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="contained"
                color="primary"
                startIcon={<SaveIcon />}
                disabled={loading || !formik.isValid}
              >
                {loading ? 'Saving...' : 'Save Strategy'}
              </Button>
            </>
          }
        />

        <Grid container spacing={3}>
          {/* Basic Information Section */}
          <Grid item xs={12} md={8}>
            <Paper sx={{ p: 3, mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Basic Information
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Strategy Name"
                    name="name"
                    value={formik.values.name}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.name && Boolean(formik.errors.name)}
                    helperText={formik.touched.name && formik.errors.name}
                    margin="normal"
                    disabled={loading}
                  />
                </Grid>
                
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    multiline
                    rows={3}
                    label="Description"
                    name="description"
                    value={formik.values.description}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.description && Boolean(formik.errors.description)}
                    helperText={formik.touched.description && formik.errors.description}
                    margin="normal"
                    disabled={loading}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6} md={4}>
                  <FormControl fullWidth margin="normal">
                    <InputLabel>Status</InputLabel>
                    <Select
                      name="status"
                      value={formik.values.status}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={formik.touched.status && Boolean(formik.errors.status)}
                      label="Status"
                      disabled={loading}
                    >
                      <MenuItem value="draft">Draft</MenuItem>
                      <MenuItem value="active">Active</MenuItem>
                      <MenuItem value="inactive">Inactive</MenuItem>
                    </Select>
                    {formik.touched.status && formik.errors.status && (
                      <FormHelperText error>{formik.errors.status}</FormHelperText>
                    )}
                  </FormControl>
                </Grid>
                
                <Grid item xs={12} sm={6} md={4}>
                  <FormControlLabel
                    control={
                      <Switch
                        name="isDefault"
                        checked={formik.values.isDefault}
                        onChange={formik.handleChange}
                        color="primary"
                        disabled={loading}
                      />
                    }
                    label="Default Strategy"
                    sx={{ mt: 2 }}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6} md={4}>
                  <DatePicker
                    label="Effective From"
                    value={formik.values.effectiveFrom}
                    onChange={(date) => formik.setFieldValue('effectiveFrom', date)}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        fullWidth
                        margin="normal"
                        error={
                          formik.touched.effectiveFrom &&
                          Boolean(formik.errors.effectiveFrom)
                        }
                        helperText={
                          formik.touched.effectiveFrom && formik.errors.effectiveFrom
                        }
                        disabled={loading}
                      />
                    )}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6} md={4}>
                  <DatePicker
                    label="Effective To"
                    value={formik.values.effectiveTo}
                    onChange={(date) => formik.setFieldValue('effectiveTo', date)}
                    minDate={formik.values.effectiveFrom}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        fullWidth
                        margin="normal"
                        error={
                          formik.touched.effectiveTo &&
                          Boolean(formik.errors.effectiveTo)
                        }
                        helperText={
                          formik.touched.effectiveTo && formik.errors.effectiveTo
                        }
                        disabled={loading}
                      />
                    )}
                  />
                </Grid>
                
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Tags"
                    margin="normal"
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        handleTagAdd(e.target.value);
                        e.target.value = '';
                      }
                    }}
                    disabled={loading}
                    helperText="Press Enter to add a tag"
                  />
                  <Box sx={{ mt: 1, display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {formik.values.tags.map((tag) => (
                      <Chip
                        key={tag}
                        label={tag}
                        onDelete={() => handleTagDelete(tag)}
                        size="small"
                      />
                    ))}
                  </Box>
                </Grid>
              </Grid>
            </Paper>
            
            {/* Rules Section */}
            <Paper sx={{ p: 3, mb: 3 }}>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h6">Reserve Rules</Typography>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<AddIcon />}
                  onClick={handleAddRule}
                  disabled={loading}
                >
                  Add Rule
                </Button>
              </Box>
              
              {formik.values.rules.map((rule, index) => (
                <ReserveRuleForm
                  key={index}
                  rule={rule}
                  index={index}
                  expanded={expandedRule === index}
                  onToggleExpand={handleToggleExpand}
                  onRemove={formik.values.rules.length > 1 ? handleRemoveRule : undefined}
                  onChange={handleUpdateRule}
                  onAddTier={handleAddTier}
                  onRemoveTier={handleRemoveTier}
                  errors={formik.errors.rules?.[index] || {}}
                />
              ))}
              
              {formik.touched.rules && formik.errors.rules && typeof formik.errors.rules === 'string' && (
                <FormHelperText error>{formik.errors.rules}</FormHelperText>
              )}
            </Paper>
          </Grid>
          
          {/* Sidebar with Auto-Replenish and Notifications */}
          <Grid item xs={12} md={4}>
            {/* Auto-Replenish Card */}
            <Card sx={{ mb: 3 }}>
              <CardHeader 
                title="Auto-Replenish Settings"
                titleTypographyProps={{ variant: 'h6' }}
              />
              <CardContent>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formik.values.config.autoReplenish.enabled}
                      onChange={(e) => {
                        const updatedConfig = { ...formik.values.config };
                        updatedConfig.autoReplenish.enabled = e.target.checked;
                        formik.setFieldValue('config', updatedConfig);
                      }}
                      color="primary"
                      disabled={loading}
                    />
                  }
                  label="Enable Auto-Replenish"
                />
                
                {formik.values.config.autoReplenish.enabled && (
                  <Box sx={{ mt: 2 }}>
                    <TextField
                      fullWidth
                      label="Threshold Amount"
                      type="number"
                      value={formik.values.config.autoReplenish.threshold}
                      onChange={(e) => {
                        const updatedConfig = { ...formik.values.config };
                        updatedConfig.autoReplenish.threshold = parseFloat(e.target.value) || 0;
                        formik.setFieldValue('config', updatedConfig);
                      }}
                      margin="normal"
                      InputProps={{
                        startAdornment: <span style={{ marginRight: 8 }}>$</span>,
                      }}
                      disabled={loading}
                      error={
                        formik.touched.config?.autoReplenish?.threshold &&
                        Boolean(formik.errors.config?.autoReplenish?.threshold)
                      }
                      helperText={
                        formik.touched.config?.autoReplenish?.threshold &&
                        formik.errors.config?.autoReplenish?.threshold
                      }
                    />
                    
                    <TextField
                      fullWidth
                      label="Replenish Amount"
                      type="number"
                      value={formik.values.config.autoReplenish.amount}
                      onChange={(e) => {
                        const updatedConfig = { ...formik.values.config };
                        updatedConfig.autoReplenish.amount = parseFloat(e.target.value) || 0;
                        formik.setFieldValue('config', updatedConfig);
                      }}
                      margin="normal"
                      InputProps={{
                        startAdornment: <span style={{ marginRight: 8 }}>$</span>,
                      }}
                      disabled={loading}
                      error={
                        formik.touched.config?.autoReplenish?.amount &&
                        Boolean(formik.errors.config?.autoReplenish?.amount)
                      }
                      helperText={
                        formik.touched.config?.autoReplenish?.amount &&
                        formik.errors.config?.autoReplenish?.amount
                      }
                    />
                    
                    <FormControl fullWidth margin="normal">
                      <InputLabel>Source Account</InputLabel>
                      <Select
                        value={formik.values.config.autoReplenish.sourceAccount}
                        onChange={(e) => {
                          const updatedConfig = { ...formik.values.config };
                          updatedConfig.autoReplenish.sourceAccount = e.target.value;
                          formik.setFieldValue('config', updatedConfig);
                        }}
                        label="Source Account"
                        disabled={loading || sourceAccounts.length === 0}
                        error={
                          formik.touched.config?.autoReplenish?.sourceAccount &&
                          Boolean(formik.errors.config?.autoReplenish?.sourceAccount)
                        }
                      >
                        {sourceAccounts.map((account) => (
                          <MenuItem key={account.id} value={account.id}>
                            {account.name}
                          </MenuItem>
                        ))}
                      </Select>
                      {formik.touched.config?.autoReplenish?.sourceAccount &&
                        formik.errors.config?.autoReplenish?.sourceAccount && (
                          <FormHelperText error>
                            {formik.errors.config.autoReplenish.sourceAccount}
                          </FormHelperText>
                        )}
                    </FormControl>
                  </Box>
                )}
              </CardContent>
            </Card>
            
            {/* Notifications Card */}
            <Card>
              <CardHeader 
                title="Notification Settings"
                titleTypographyProps={{ variant: 'h6' }}
              />
              <CardContent>
                <Typography variant="subtitle2" gutterBottom>
                  Low Balance Alerts
                </Typography>
                
                <FormControlLabel
                  control={
                    <Switch
                      checked={formik.values.config.notifications.lowBalance.enabled}
                      onChange={(e) => {
                        const updatedConfig = { ...formik.values.config };
                        updatedConfig.notifications.lowBalance.enabled = e.target.checked;
                        formik.setFieldValue('config', updatedConfig);
                      }}
                      color="primary"
                      disabled={loading}
                    />
                  }
                  label="Enable Notifications"
                />
                
                {formik.values.config.notifications.lowBalance.enabled && (
                  <Box sx={{ mt: 2 }}>
                    <TextField
                      fullWidth
                      label="Threshold Amount"
                      type="number"
                      value={formik.values.config.notifications.lowBalance.threshold}
                      onChange={(e) => {
                        const updatedConfig = { ...formik.values.config };
                        updatedConfig.notifications.lowBalance.threshold = parseFloat(e.target.value) || 0;
                        formik.setFieldValue('config', updatedConfig);
                      }}
                      margin="normal"
                      InputProps={{
                        startAdornment: <span style={{ marginRight: 8 }}>$</span>,
                      }}
                      disabled={loading}
                    />
                    
                    <TextField
                      fullWidth
                      label="Add Email Recipient"
                      margin="normal"
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          handleAddRecipient(e.target.value);
                          e.target.value = '';
                        }
                      }}
                      disabled={loading}
                      helperText="Press Enter to add an email"
                    />
                    
                    <Box sx={{ mt: 1, display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {formik.values.config.notifications.lowBalance.recipients.map((recipient) => (
                        <Chip
                          key={recipient}
                          label={recipient}
                          onDelete={() => handleRemoveRecipient(recipient)}
                          size="small"
                        />
                      ))}
                    </Box>
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
        
        {/* Fixed Save Bar */}
        <Box
          sx={{
            position: 'fixed',
            bottom: 0,
            left: 0,
            right: 0,
            bgcolor: 'background.paper',
            borderTop: '1px solid',
            borderColor: 'divider',
            p: 2,
            display: 'flex',
            justifyContent: 'flex-end',
            zIndex: 1000,
          }}
        >
          <Button
            variant="outlined"
            color="secondary"
            startIcon={<CancelIcon />}
            onClick={() => navigate('/reserve-strategies')}
            sx={{ mr: 2 }}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            color="primary"
            startIcon={<SaveIcon />}
            disabled={loading || !formik.isValid}
          >
            {loading ? 'Saving...' : 'Save Strategy'}
          </Button>
        </Box>
      </form>
    </LocalizationProvider>
  );
};

export default ReserveStrategyForm;
