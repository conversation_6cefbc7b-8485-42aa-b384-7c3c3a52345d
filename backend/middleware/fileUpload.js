const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const logger = require('../utils/logger');
const { ApiError } = require('../utils/ApiError');

// Default file upload configuration
const defaultConfig = {
  // Maximum file size (5MB)
  maxFileSize: 5 * 1024 * 1024,
  
  // Allowed file types
  allowedMimeTypes: [
    // Images
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'image/svg+xml',
    
    // Documents
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain',
    'text/csv',
    'application/json',
    
    // Archives
    'application/zip',
    'application/x-rar-compressed',
    'application/x-7z-compressed',
    'application/x-tar',
    'application/gzip',
  ],
  
  // Directory to store uploaded files
  uploadDir: process.env.UPLOAD_DIR || path.join(process.cwd(), 'uploads'),
  
  // Whether to keep the original filename
  preserveFilename: false,
  
  // File name generator function
  filename: (req, file) => {
    const ext = path.extname(file.originalname).toLowerCase();
    return `${uuidv4()}${ext}`;
  },
  
  // File filter function
  fileFilter: (req, file, cb) => {
    // Check file type
    if (!defaultConfig.allowedMimeTypes.includes(file.mimetype)) {
      const error = new ApiError(400, `File type ${file.mimetype} is not allowed`);
      error.code = 'LIMIT_FILE_TYPE';
      return cb(error, false);
    }
    
    // Check file size
    if (file.size > defaultConfig.maxFileSize) {
      const error = new ApiError(400, `File size exceeds the limit of ${defaultConfig.maxFileSize / (1024 * 1024)}MB`);
      error.code = 'LIMIT_FILE_SIZE';
      return cb(error, false);
    }
    
    cb(null, true);
  },
};

/**
 * Create a multer upload middleware with the given configuration
 * @param {Object} options - Configuration options
 * @param {string|Array} options.fieldName - Field name or array of field names for file uploads
 * @param {number} options.maxCount - Maximum number of files allowed (default: 1)
 * @param {number} options.maxFileSize - Maximum file size in bytes (default: 5MB)
 * @param {Array} options.allowedMimeTypes - Allowed MIME types
 * @param {string} options.uploadDir - Directory to store uploaded files
 * @param {Function} options.filename - Function to generate filename
 * @param {Function} options.fileFilter - Function to filter files
 * @returns {Function} Express middleware function
 */
const createFileUpload = (options = {}) => {
  const config = { ...defaultConfig, ...options };
  
  // Ensure upload directory exists
  if (!fs.existsSync(config.uploadDir)) {
    fs.mkdirSync(config.uploadDir, { recursive: true });
    logger.info(`Created upload directory: ${config.uploadDir}`);
  }
  
  // Configure storage
  const storage = multer.diskStorage({
    destination: (req, file, cb) => {
      cb(null, config.uploadDir);
    },
    filename: (req, file, cb) => {
      let filename;
      
      if (config.preserveFilename) {
        // Sanitize the original filename
        const sanitized = file.originalname.replace(/[^\w\d.-]/g, '_');
        filename = `${Date.now()}_${sanitized}`;
      } else if (typeof config.filename === 'function') {
        filename = config.filename(req, file);
      } else {
        const ext = path.extname(file.originalname).toLowerCase();
        filename = `${uuidv4()}${ext}`;
      }
      
      cb(null, filename);
    },
  });
  
  // Create multer instance
  const upload = multer({
    storage,
    limits: {
      fileSize: config.maxFileSize,
      files: config.maxCount || 1,
    },
    fileFilter: config.fileFilter || defaultConfig.fileFilter,
  });
  
  // Return the appropriate multer middleware
  if (Array.isArray(config.fieldName)) {
    // Handle multiple fields
    const fields = config.fieldName.map(name => ({
      name,
      maxCount: config.maxCount || 1,
    }));
    
    return upload.fields(fields);
  } else if (config.fieldName) {
    // Handle single field
    return upload.array(config.fieldName, config.maxCount || 1);
  }
  
  // Default to single file with field name 'file'
  return upload.single('file');
};

/**
 * Middleware to handle file upload errors
 */
const handleFileUploadError = (err, req, res, next) => {
  if (err instanceof multer.MulterError) {
    // A Multer error occurred
    let message = 'File upload error';
    let statusCode = 400;
    
    switch (err.code) {
      case 'LIMIT_FILE_SIZE':
        message = `File too large. Maximum size is ${defaultConfig.maxFileSize / (1024 * 1024)}MB`;
        break;
      case 'LIMIT_FILE_COUNT':
        message = 'Too many files uploaded';
        break;
      case 'LIMIT_UNEXPECTED_FILE':
        message = 'Unexpected file field';
        break;
      case 'LIMIT_FIELD_KEY':
        message = 'Field name too long';
        break;
      case 'LIMIT_FIELD_VALUE':
        message = 'Field value too long';
        break;
      case 'LIMIT_FIELD_COUNT':
        message = 'Too many fields';
        break;
      case 'LIMIT_PART_COUNT':
        message = 'Too many parts';
        break;
      default:
        message = 'File upload error';
        statusCode = 500;
    }
    
    return next(new ApiError(statusCode, message));
  } else if (err) {
    // An unknown error occurred
    return next(err);
  }
  
  next();
};

module.exports = {
  createFileUpload,
  handleFileUploadError,
  defaultConfig,
};
