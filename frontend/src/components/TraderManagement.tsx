import React, { useState, useEffect } from 'react';
import { useAuth, type User, type BaseUser } from '../contexts/AuthContext';
import axios from 'axios';
import { format } from 'date-fns';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from './ui/table';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog';
import { useToast } from './ui/use-toast';

// Extended user type that includes all properties from BaseUser
interface ExtendedUser extends Omit<BaseUser, 'role'> {
  _id: string;
  role: User['role'];
  businessName?: string;
  name: string;
  email: string;
}

interface TraderAssignment {
  _id: string;
  merchant: ExtendedUser | string;
  trader: ExtendedUser | string;
  startDate: string;
  endDate?: string;
  status: 'active' | 'inactive' | 'completed' | 'pending';
  notes?: string;
  assignedBy: User | string;
  createdAt: string;
  updatedAt: string;
}

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api/v1';

export const TraderManagement: React.FC = () => {
  const { user, logout } = useAuth();
  const token = localStorage.getItem('token');
  const { toast } = useToast();
  const [assignments, setAssignments] = useState<TraderAssignment[]>([]);
  const [traders, setTraders] = useState<ExtendedUser[]>([]);
  const [merchants, setMerchants] = useState<ExtendedUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [formData, setFormData] = useState({
    merchantId: '',
    traderId: '',
    startDate: format(new Date(), 'yyyy-MM-dd'),
    endDate: '',
    notes: '',
  });

  const headers = {
    'Content-Type': 'application/json',
    ...(token ? { Authorization: `Bearer ${token}` } : {}),
  };

  useEffect(() => {
    fetchData();
    fetchUsers();
  }, []);

  const fetchData = async () => {
    try {
      const [assignmentsRes] = await Promise.all([
        axios.get(`${API_URL}/trader/assignments`, { headers }),
      ]);
      setAssignments(assignmentsRes.data.data);
    } catch (error) {
      console.error('Error fetching data:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch data',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchUsers = async () => {
    try {
      const [tradersRes, merchantsRes] = await Promise.all([
        axios.get(`${API_URL}/admin/users?role=trader`, { headers }),
        axios.get(`${API_URL}/admin/users?role=merchant`, { headers }),
      ]);
      setTraders(tradersRes.data.data);
      setMerchants(merchantsRes.data.data);
    } catch (error) {
      console.error('Error fetching users:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch users',
        variant: 'destructive',
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await axios.post(
        `${API_URL}/trader/assignments`,
        {
          ...formData,
          startDate: new Date(formData.startDate).toISOString(),
          endDate: formData.endDate ? new Date(formData.endDate).toISOString() : undefined,
        },
        { headers }
      );
      
      toast({
        title: 'Success',
        description: 'Trader assigned successfully',
      });
      
      setIsDialogOpen(false);
      setFormData({
        merchantId: '',
        traderId: '',
        startDate: format(new Date(), 'yyyy-MM-dd'),
        endDate: '',
        notes: '',
      });
      
      fetchData();
    } catch (error) {
      console.error('Error creating assignment:', error);
      toast({
        title: 'Error',
        description: 'Failed to assign trader',
        variant: 'destructive',
      });
    }
  };

  const updateAssignmentStatus = async (id: string, status: string) => {
    try {
      await axios.put(
        `${API_URL}/trader/assignments/${id}`,
        { status },
        { headers }
      );
      
      toast({
        title: 'Success',
        description: 'Assignment updated successfully',
      });
      
      fetchData();
    } catch (error) {
      console.error('Error updating assignment:', error);
      toast({
        title: 'Error',
        description: 'Failed to update assignment',
        variant: 'destructive',
      });
    }
  };

  const getUserName = (user: ExtendedUser | string | undefined) => {
    if (!user) return 'Unknown';
    if (typeof user === 'string') return 'Loading...';
    return (user as ExtendedUser).name || (user as ExtendedUser).email || 'Unknown';
  };

  const getMerchantName = (user: ExtendedUser | string | undefined) => {
    if (!user) return 'Unknown';
    if (typeof user === 'string') return 'Loading...';
    const userData = user as ExtendedUser;
    return userData.businessName || userData.name || userData.email || 'Unknown';
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Trader Management</h1>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button>Assign Trader</Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Assign Trader to Merchant</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="merchantId">Merchant</Label>
                <Select
                  value={formData.merchantId}
                  onValueChange={(value) => setFormData({ ...formData, merchantId: value })}
                  required
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a merchant" />
                  </SelectTrigger>
                  <SelectContent>
                    {merchants.map((merchant) => (
                      <SelectItem key={merchant._id} value={merchant._id || ''}>
                        {getMerchantName(merchant)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="traderId">Trader</Label>
                <Select
                  value={formData.traderId}
                  onValueChange={(value) => setFormData({ ...formData, traderId: value })}
                  required
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a trader" />
                  </SelectTrigger>
                  <SelectContent>
                    {traders.map((trader) => (
                      <SelectItem key={trader._id} value={trader._id || ''}>
                        {(trader as User).name || (trader as User).email || 'Unknown Trader'}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="startDate">Start Date</Label>
                  <Input
                    type="date"
                    id="startDate"
                    value={formData.startDate}
                    onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="endDate">End Date (Optional)</Label>
                  <Input
                    type="date"
                    id="endDate"
                    value={formData.endDate}
                    onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
                    min={formData.startDate}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes">Notes (Optional)</Label>
                <Input
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                  placeholder="Additional notes about this assignment"
                />
              </div>

              <div className="flex justify-end space-x-2 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="submit">Assign Trader</Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Merchant</TableHead>
              <TableHead>Trader</TableHead>
              <TableHead>Start Date</TableHead>
              <TableHead>End Date</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {assignments.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-4">
                  No assignments found
                </TableCell>
              </TableRow>
            ) : (
              assignments.map((assignment) => (
                <TableRow key={assignment._id}>
                  <TableCell>{getUserName(assignment.merchant)}</TableCell>
                  <TableCell>{getUserName(assignment.trader)}</TableCell>
                  <TableCell>{format(new Date(assignment.startDate), 'MMM d, yyyy')}</TableCell>
                  <TableCell>
                    {assignment.endDate ? format(new Date(assignment.endDate), 'MMM d, yyyy') : 'N/A'}
                  </TableCell>
                  <TableCell>
                    <span
                      className={`px-2 py-1 rounded-full text-xs ${
                        assignment.status === 'active'
                          ? 'bg-green-100 text-green-800'
                          : assignment.status === 'completed'
                          ? 'bg-blue-100 text-blue-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}
                    >
                      {assignment.status.charAt(0).toUpperCase() + assignment.status.slice(1)}
                    </span>
                  </TableCell>
                  <TableCell>
                    {assignment.status === 'active' && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="mr-2"
                        onClick={() => updateAssignmentStatus(assignment._id, 'completed')}
                      >
                        Mark Complete
                      </Button>
                    )}
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default TraderManagement;
