import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Plus, Search, Filter, Mail, Phone } from 'lucide-react';
import { Input } from '../../components/ui/input';
import { Badge } from '../../components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '../../components/ui/avatar';

const MerchantTraders = () => {
  // Mock data - replace with actual data fetching
  const traders = [
    {
      id: '1',
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '+****************',
      status: 'active',
      collections: 42,
      lastActive: '2023-06-15',
    },
    // Add more mock data as needed
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Assigned Traders</h2>
          <p className="text-muted-foreground">
            Manage traders who can collect payments on your behalf
          </p>
        </div>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Assign Trader
        </Button>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Active Traders</CardTitle>
            <div className="flex items-center space-x-2">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search traders..."
                  className="pl-8 sm:w-[300px]"
                />
              </div>
              <Button variant="outline" size="sm" className="h-9">
                <Filter className="mr-2 h-4 w-4" />
                Filter
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {traders.map((trader) => (
              <div key={trader.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <Avatar>
                    <AvatarImage src={`/avatars/${trader.id}.jpg`} alt={trader.name} />
                    <AvatarFallback>{trader.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium">{trader.name}</p>
                    <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                      <span className="flex items-center">
                        <Mail className="mr-1 h-3 w-3" />
                        {trader.email}
                      </span>
                      <span>•</span>
                      <span className="flex items-center">
                        <Phone className="mr-1 h-3 w-3" />
                        {trader.phone}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <p className="text-sm text-muted-foreground">Collections</p>
                    <p className="font-medium">{trader.collections}</p>
                  </div>
                  <Badge variant={trader.status === 'active' ? 'success' : 'secondary'}>
                    {trader.status.charAt(0).toUpperCase() + trader.status.slice(1)}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default MerchantTraders;
