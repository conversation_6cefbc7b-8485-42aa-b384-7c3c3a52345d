import React from 'react';
import { Box, Typography, Paper, Container } from '@mui/material';
import { useParams } from 'react-router-dom';

interface AdminPageTemplateProps {
  title: string;
  children?: React.ReactNode;
  fullWidth?: boolean;
}

const AdminPageTemplate: React.FC<AdminPageTemplateProps> = ({
  title,
  children,
  fullWidth = false,
}) => {
  const params = useParams();
  
  // Generate a more descriptive title if there's an ID in the URL
  const getPageTitle = () => {
    if (params.id) {
      return `${title} #${params.id}`;
    }
    return title;
  };

  return (
    <Container maxWidth={fullWidth ? false : 'lg'} sx={{ mt: 4, mb: 4 }}>
      <Box mb={4}>
        <Typography variant="h4" component="h1" gutterBottom>
          {getPageTitle()}
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Manage {title.toLowerCase()} and related settings
        </Typography>
      </Box>
      
      <Paper 
        elevation={2} 
        sx={{ 
          p: 3, 
          minHeight: '60vh',
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        {children || (
          <Box flexGrow={1} display="flex" alignItems="center" justifyContent="center">
            <Typography variant="h6" color="text.secondary">
              {title} content will be displayed here
            </Typography>
          </Box>
        )}
      </Paper>
    </Container>
  );
};

export default AdminPageTemplate;
