"use strict";(self.webpackChunkpayment_gateway_frontend=self.webpackChunkpayment_gateway_frontend||[]).push([[168],{987:(e,t,r)=>{r.d(t,{A:()=>s});const s=(0,r(3797).A)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},1526:(e,t,r)=>{r.d(t,{A:()=>s});const s=(0,r(3797).A)("MoreVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]])},1850:(e,t,r)=>{r.d(t,{u:()=>u});var s=r(4858);const a=(e,t,r)=>{if(e&&"reportValidity"in e){const a=(0,s.Jt)(r,t);e.setCustomValidity(a&&a.message||""),e.reportValidity()}},i=(e,t)=>{for(const r in t.fields){const s=t.fields[r];s&&s.ref&&"reportValidity"in s.ref?a(s.ref,r,e):s.refs&&s.refs.forEach(t=>a(t,r,e))}},n=(e,t)=>{t.shouldUseNativeValidation&&i(e,t);const r={};for(const a in e){const i=(0,s.Jt)(t.fields,a),n=Object.assign(e[a]||{},{ref:i&&i.ref});if(o(t.names||Object.keys(e),a)){const e=Object.assign({},(0,s.Jt)(r,a));(0,s.hZ)(e,"root",n),(0,s.hZ)(r,a,e)}else(0,s.hZ)(r,a,n)}return r},o=(e,t)=>e.some(e=>e.startsWith(t+"."));var l=function(e,t){for(var r={};e.length;){var a=e[0],i=a.code,n=a.message,o=a.path.join(".");if(!r[o])if("unionErrors"in a){var l=a.unionErrors[0].errors[0];r[o]={message:l.message,type:l.code}}else r[o]={message:n,type:i};if("unionErrors"in a&&a.unionErrors.forEach(function(t){return t.errors.forEach(function(t){return e.push(t)})}),t){var u=r[o].types,d=u&&u[a.code];r[o]=(0,s.Gb)(o,t,r,i,d?[].concat(d,a.message):a.message)}e.shift()}return r},u=function(e,t,r){return void 0===r&&(r={}),function(s,a,o){try{return Promise.resolve(function(a,n){try{var l=Promise.resolve(e["sync"===r.mode?"parse":"parseAsync"](s,t)).then(function(e){return o.shouldUseNativeValidation&&i({},o),{errors:{},values:r.raw?s:e}})}catch(u){return n(u)}return l&&l.then?l.then(void 0,n):l}(0,function(e){if(function(e){return Array.isArray(null==e?void 0:e.errors)}(e))return{values:{},errors:n(l(e.errors,!o.shouldUseNativeValidation&&"all"===o.criteriaMode),o)};throw e}))}catch(u){return Promise.reject(u)}}}},2380:(e,t,r)=>{r.d(t,{A:()=>s});const s=(0,r(3797).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},4858:(e,t,r)=>{r.d(t,{Gb:()=>B,Jt:()=>b,hZ:()=>_,mN:()=>Ee,xI:()=>I,xW:()=>M});var s=r(5043),a=e=>"checkbox"===e.type,i=e=>e instanceof Date,n=e=>null==e;const o=e=>"object"===typeof e;var l=e=>!n(e)&&!Array.isArray(e)&&o(e)&&!i(e),u=e=>l(e)&&e.target?a(e.target)?e.target.checked:e.target.value:e,d=(e,t)=>e.has((e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e)(t)),c=e=>{const t=e.constructor&&e.constructor.prototype;return l(t)&&t.hasOwnProperty("isPrototypeOf")},f="undefined"!==typeof window&&"undefined"!==typeof window.HTMLElement&&"undefined"!==typeof document;function m(e){let t;const r=Array.isArray(e),s="undefined"!==typeof FileList&&e instanceof FileList;if(e instanceof Date)t=new Date(e);else{if(f&&(e instanceof Blob||s)||!r&&!l(e))return e;if(t=r?[]:{},r||c(e))for(const r in e)e.hasOwnProperty(r)&&(t[r]=m(e[r]));else t=e}return t}var y=e=>/^\w*$/.test(e),v=e=>void 0===e,h=e=>Array.isArray(e)?e.filter(Boolean):[],g=e=>h(e.replace(/["|']|\]/g,"").split(/\.|\[/)),b=(e,t,r)=>{if(!t||!l(e))return r;const s=(y(t)?[t]:g(t)).reduce((e,t)=>n(e)?e:e[t],e);return v(s)||s===e?v(e[t])?r:e[t]:s},p=e=>"boolean"===typeof e,_=(e,t,r)=>{let s=-1;const a=y(t)?[t]:g(t),i=a.length,n=i-1;for(;++s<i;){const t=a[s];let i=r;if(s!==n){const r=e[t];i=l(r)||Array.isArray(r)?r:isNaN(+a[s+1])?{}:[]}if("__proto__"===t||"constructor"===t||"prototype"===t)return;e[t]=i,e=e[t]}};const V={BLUR:"blur",FOCUS_OUT:"focusout",CHANGE:"change"},F={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},x="max",A="min",k="maxLength",w="minLength",C="pattern",S="required",D="validate",E=s.createContext(null);E.displayName="HookFormContext";const M=()=>s.useContext(E);var j=function(e,t,r){let s=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];const a={defaultValues:t._defaultValues};for(const i in e)Object.defineProperty(a,i,{get:()=>{const a=i;return t._proxyFormState[a]!==F.all&&(t._proxyFormState[a]=!s||F.all),r&&(r[a]=!0),e[a]}});return a};const R="undefined"!==typeof window?s.useLayoutEffect:s.useEffect;var T=e=>"string"===typeof e,O=(e,t,r,s,a)=>T(e)?(s&&t.watch.add(e),b(r,e,a)):Array.isArray(e)?e.map(e=>(s&&t.watch.add(e),b(r,e))):(s&&(t.watchAll=!0),r),N=e=>n(e)||!o(e);function L(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:new WeakSet;if(N(e)||N(t))return e===t;if(i(e)&&i(t))return e.getTime()===t.getTime();const s=Object.keys(e),a=Object.keys(t);if(s.length!==a.length)return!1;if(r.has(e)||r.has(t))return!0;r.add(e),r.add(t);for(const n of s){const s=e[n];if(!a.includes(n))return!1;if("ref"!==n){const e=t[n];if(i(s)&&i(e)||l(s)&&l(e)||Array.isArray(s)&&Array.isArray(e)?!L(s,e,r):s!==e)return!1}}return!0}function U(e){const t=M(),{name:r,disabled:a,control:i=t.control,shouldUnregister:n,defaultValue:o}=e,l=d(i._names.array,r),c=s.useMemo(()=>b(i._formValues,r,b(i._defaultValues,r,o)),[i,r,o]),f=function(e){const t=M(),{control:r=t.control,name:a,defaultValue:i,disabled:n,exact:o,compute:l}=e||{},u=s.useRef(i),d=s.useRef(l),c=s.useRef(void 0);d.current=l;const f=s.useMemo(()=>r._getWatch(a,u.current),[r,a]),[m,y]=s.useState(d.current?d.current(f):f);return R(()=>r._subscribe({name:a,formState:{values:!0},exact:o,callback:e=>{if(!n){const t=O(a,r._names,e.values||r._formValues,!1,u.current);if(d.current){const e=d.current(t);L(e,c.current)||(y(e),c.current=e)}else y(t)}}}),[r,n,a,o]),s.useEffect(()=>r._removeUnmounted()),m}({control:i,name:r,defaultValue:c,exact:!0}),y=function(e){const t=M(),{control:r=t.control,disabled:a,name:i,exact:n}=e||{},[o,l]=s.useState(r._formState),u=s.useRef({isDirty:!1,isLoading:!1,dirtyFields:!1,touchedFields:!1,validatingFields:!1,isValidating:!1,isValid:!1,errors:!1});return R(()=>r._subscribe({name:i,formState:u.current,exact:n,callback:e=>{!a&&l({...r._formState,...e})}}),[i,a,n]),s.useEffect(()=>{u.current.isValid&&r._setValid(!0)},[r]),s.useMemo(()=>j(o,r,u.current,!1),[o,r])}({control:i,name:r,exact:!0}),h=s.useRef(e),g=s.useRef(i.register(r,{...e.rules,value:f,...p(e.disabled)?{disabled:e.disabled}:{}}));h.current=e;const F=s.useMemo(()=>Object.defineProperties({},{invalid:{enumerable:!0,get:()=>!!b(y.errors,r)},isDirty:{enumerable:!0,get:()=>!!b(y.dirtyFields,r)},isTouched:{enumerable:!0,get:()=>!!b(y.touchedFields,r)},isValidating:{enumerable:!0,get:()=>!!b(y.validatingFields,r)},error:{enumerable:!0,get:()=>b(y.errors,r)}}),[y,r]),x=s.useCallback(e=>g.current.onChange({target:{value:u(e),name:r},type:V.CHANGE}),[r]),A=s.useCallback(()=>g.current.onBlur({target:{value:b(i._formValues,r),name:r},type:V.BLUR}),[r,i._formValues]),k=s.useCallback(e=>{const t=b(i._fields,r);t&&e&&(t._f.ref={focus:()=>e.focus&&e.focus(),select:()=>e.select&&e.select(),setCustomValidity:t=>e.setCustomValidity(t),reportValidity:()=>e.reportValidity()})},[i._fields,r]),w=s.useMemo(()=>({name:r,value:f,...p(a)||y.disabled?{disabled:y.disabled||a}:{},onChange:x,onBlur:A,ref:k}),[r,a,y.disabled,x,A,k,f]);return s.useEffect(()=>{const e=i._options.shouldUnregister||n;i.register(r,{...h.current.rules,...p(h.current.disabled)?{disabled:h.current.disabled}:{}});const t=(e,t)=>{const r=b(i._fields,e);r&&r._f&&(r._f.mount=t)};if(t(r,!0),e){const e=m(b(i._options.defaultValues,r));_(i._defaultValues,r,e),v(b(i._formValues,r))&&_(i._formValues,r,e)}return!l&&i.register(r),()=>{(l?e&&!i._state.action:e)?i.unregister(r):t(r,!1)}},[r,i,l,n]),s.useEffect(()=>{i._setDisabledField({disabled:a,name:r})},[a,r,i]),s.useMemo(()=>({field:w,formState:y,fieldState:F}),[w,y,F])}const I=e=>e.render(U(e));var B=(e,t,r,s,a)=>t?{...r[e],types:{...r[e]&&r[e].types?r[e].types:{},[s]:a||!0}}:{},P=e=>Array.isArray(e)?e:[e],G=()=>{let e=[];return{get observers(){return e},next:t=>{for(const r of e)r.next&&r.next(t)},subscribe:t=>(e.push(t),{unsubscribe:()=>{e=e.filter(e=>e!==t)}}),unsubscribe:()=>{e=[]}}},q=e=>l(e)&&!Object.keys(e).length,H=e=>"file"===e.type,W=e=>"function"===typeof e,z=e=>{if(!f)return!1;const t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},$=e=>"select-multiple"===e.type,J=e=>"radio"===e.type,Z=e=>J(e)||a(e),K=e=>z(e)&&e.isConnected;function Q(e,t){const r=Array.isArray(t)?t:y(t)?[t]:g(t),s=1===r.length?e:function(e,t){const r=t.slice(0,-1).length;let s=0;for(;s<r;)e=v(e)?s++:e[t[s++]];return e}(e,r),a=r.length-1,i=r[a];return s&&delete s[i],0!==a&&(l(s)&&q(s)||Array.isArray(s)&&function(e){for(const t in e)if(e.hasOwnProperty(t)&&!v(e[t]))return!1;return!0}(s))&&Q(e,r.slice(0,-1)),e}var X=e=>{for(const t in e)if(W(e[t]))return!0;return!1};function Y(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const r=Array.isArray(e);if(l(e)||r)for(const s in e)Array.isArray(e[s])||l(e[s])&&!X(e[s])?(t[s]=Array.isArray(e[s])?[]:{},Y(e[s],t[s])):n(e[s])||(t[s]=!0);return t}function ee(e,t,r){const s=Array.isArray(e);if(l(e)||s)for(const a in e)Array.isArray(e[a])||l(e[a])&&!X(e[a])?v(t)||N(r[a])?r[a]=Array.isArray(e[a])?Y(e[a],[]):{...Y(e[a])}:ee(e[a],n(t)?{}:t[a],r[a]):r[a]=!L(e[a],t[a]);return r}var te=(e,t)=>ee(e,t,Y(t));const re={value:!1,isValid:!1},se={value:!0,isValid:!0};var ae=e=>{if(Array.isArray(e)){if(e.length>1){const t=e.filter(e=>e&&e.checked&&!e.disabled).map(e=>e.value);return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!v(e[0].attributes.value)?v(e[0].value)||""===e[0].value?se:{value:e[0].value,isValid:!0}:se:re}return re},ie=(e,t)=>{let{valueAsNumber:r,valueAsDate:s,setValueAs:a}=t;return v(e)?e:r?""===e?NaN:e?+e:e:s&&T(e)?new Date(e):a?a(e):e};const ne={isValid:!1,value:null};var oe=e=>Array.isArray(e)?e.reduce((e,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:e,ne):ne;function le(e){const t=e.ref;return H(t)?t.files:J(t)?oe(e.refs).value:$(t)?[...t.selectedOptions].map(e=>{let{value:t}=e;return t}):a(t)?ae(e.refs).value:ie(v(t.value)?e.ref.value:t.value,e)}var ue=(e,t,r,s)=>{const a={};for(const i of e){const e=b(t,i);e&&_(a,i,e._f)}return{criteriaMode:r,names:[...e],fields:a,shouldUseNativeValidation:s}},de=e=>e instanceof RegExp,ce=e=>v(e)?e:de(e)?e.source:l(e)?de(e.value)?e.value.source:e.value:e,fe=e=>({isOnSubmit:!e||e===F.onSubmit,isOnBlur:e===F.onBlur,isOnChange:e===F.onChange,isOnAll:e===F.all,isOnTouch:e===F.onTouched});const me="AsyncFunction";var ye=e=>!!e&&!!e.validate&&!!(W(e.validate)&&e.validate.constructor.name===me||l(e.validate)&&Object.values(e.validate).find(e=>e.constructor.name===me)),ve=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate),he=(e,t,r)=>!r&&(t.watchAll||t.watch.has(e)||[...t.watch].some(t=>e.startsWith(t)&&/^\.\w+/.test(e.slice(t.length))));const ge=(e,t,r,s)=>{for(const a of r||Object.keys(e)){const r=b(e,a);if(r){const{_f:e,...i}=r;if(e){if(e.refs&&e.refs[0]&&t(e.refs[0],a)&&!s)return!0;if(e.ref&&t(e.ref,e.name)&&!s)return!0;if(ge(i,t))break}else if(l(i)&&ge(i,t))break}}};function be(e,t,r){const s=b(e,r);if(s||y(r))return{error:s,name:r};const a=r.split(".");for(;a.length;){const s=a.join("."),i=b(t,s),n=b(e,s);if(i&&!Array.isArray(i)&&r!==s)return{name:r};if(n&&n.type)return{name:s,error:n};if(n&&n.root&&n.root.type)return{name:`${s}.root`,error:n.root};a.pop()}return{name:r}}var pe=(e,t,r,s)=>{r(e);const{name:a,...i}=e;return q(i)||Object.keys(i).length>=Object.keys(t).length||Object.keys(i).find(e=>t[e]===(!s||F.all))},_e=(e,t,r)=>!e||!t||e===t||P(e).some(e=>e&&(r?e===t:e.startsWith(t)||t.startsWith(e))),Ve=(e,t,r,s,a)=>!a.isOnAll&&(!r&&a.isOnTouch?!(t||e):(r?s.isOnBlur:a.isOnBlur)?!e:!(r?s.isOnChange:a.isOnChange)||e),Fe=(e,t)=>!h(b(e,t)).length&&Q(e,t),xe=(e,t,r)=>{const s=P(b(e,r));return _(s,"root",t[r]),_(e,r,s),e},Ae=e=>T(e);function ke(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"validate";if(Ae(e)||Array.isArray(e)&&e.every(Ae)||p(e)&&!e)return{type:r,message:Ae(e)?e:"",ref:t}}var we=e=>l(e)&&!de(e)?e:{value:e,message:""},Ce=async(e,t,r,s,i,o)=>{const{ref:u,refs:d,required:c,maxLength:f,minLength:m,min:y,max:h,pattern:g,validate:_,name:V,valueAsNumber:F,mount:E}=e._f,M=b(r,V);if(!E||t.has(V))return{};const j=d?d[0]:u,R=e=>{i&&j.reportValidity&&(j.setCustomValidity(p(e)?"":e||""),j.reportValidity())},O={},N=J(u),L=a(u),U=N||L,I=(F||H(u))&&v(u.value)&&v(M)||z(u)&&""===u.value||""===M||Array.isArray(M)&&!M.length,P=B.bind(null,V,s,O),G=function(e,t,r){let s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:k,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:w;const i=e?t:r;O[V]={type:e?s:a,message:i,ref:u,...P(e?s:a,i)}};if(o?!Array.isArray(M)||!M.length:c&&(!U&&(I||n(M))||p(M)&&!M||L&&!ae(d).isValid||N&&!oe(d).isValid)){const{value:e,message:t}=Ae(c)?{value:!!c,message:c}:we(c);if(e&&(O[V]={type:S,message:t,ref:j,...P(S,t)},!s))return R(t),O}if(!I&&(!n(y)||!n(h))){let e,t;const r=we(h),a=we(y);if(n(M)||isNaN(M)){const s=u.valueAsDate||new Date(M),i=e=>new Date((new Date).toDateString()+" "+e),n="time"==u.type,o="week"==u.type;T(r.value)&&M&&(e=n?i(M)>i(r.value):o?M>r.value:s>new Date(r.value)),T(a.value)&&M&&(t=n?i(M)<i(a.value):o?M<a.value:s<new Date(a.value))}else{const s=u.valueAsNumber||(M?+M:M);n(r.value)||(e=s>r.value),n(a.value)||(t=s<a.value)}if((e||t)&&(G(!!e,r.message,a.message,x,A),!s))return R(O[V].message),O}if((f||m)&&!I&&(T(M)||o&&Array.isArray(M))){const e=we(f),t=we(m),r=!n(e.value)&&M.length>+e.value,a=!n(t.value)&&M.length<+t.value;if((r||a)&&(G(r,e.message,t.message),!s))return R(O[V].message),O}if(g&&!I&&T(M)){const{value:e,message:t}=we(g);if(de(e)&&!M.match(e)&&(O[V]={type:C,message:t,ref:u,...P(C,t)},!s))return R(t),O}if(_)if(W(_)){const e=ke(await _(M,r),j);if(e&&(O[V]={...e,...P(D,e.message)},!s))return R(e.message),O}else if(l(_)){let e={};for(const t in _){if(!q(e)&&!s)break;const a=ke(await _[t](M,r),j,t);a&&(e={...a,...P(t,a.message)},R(a.message),s&&(O[V]=e))}if(!q(e)&&(O[V]={ref:j,...e},!s))return O}return R(!0),O};const Se={mode:F.onSubmit,reValidateMode:F.onChange,shouldFocusError:!0};function De(){let e,t={...Se,...arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}},r={submitCount:0,isDirty:!1,isReady:!1,isLoading:W(t.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:t.errors||{},disabled:t.disabled||!1},s={},o=(l(t.defaultValues)||l(t.values))&&m(t.defaultValues||t.values)||{},c=t.shouldUnregister?{}:m(o),y={action:!1,mount:!1,watch:!1},g={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},x=0;const A={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1};let k={...A};const w={array:G(),state:G()},C=t.criteriaMode===F.all,S=async e=>{if(!t.disabled&&(A.isValid||k.isValid||e)){const e=t.resolver?q((await R()).errors):await N(s,!0);e!==r.isValid&&w.state.next({isValid:e})}},D=(e,s)=>{!t.disabled&&(A.isValidating||A.validatingFields||k.isValidating||k.validatingFields)&&((e||Array.from(g.mount)).forEach(e=>{e&&(s?_(r.validatingFields,e,s):Q(r.validatingFields,e))}),w.state.next({validatingFields:r.validatingFields,isValidating:!q(r.validatingFields)}))},E=(e,t,r,a)=>{const i=b(s,e);if(i){const s=b(c,e,v(r)?b(o,e):r);v(s)||a&&a.defaultChecked||t?_(c,e,t?s:le(i._f)):B(e,s),y.mount&&S()}},M=(e,s,a,i,n)=>{let l=!1,u=!1;const d={name:e};if(!t.disabled){if(!a||i){(A.isDirty||k.isDirty)&&(u=r.isDirty,r.isDirty=d.isDirty=U(),l=u!==d.isDirty);const t=L(b(o,e),s);u=!!b(r.dirtyFields,e),t?Q(r.dirtyFields,e):_(r.dirtyFields,e,!0),d.dirtyFields=r.dirtyFields,l=l||(A.dirtyFields||k.dirtyFields)&&u!==!t}if(a){const t=b(r.touchedFields,e);t||(_(r.touchedFields,e,a),d.touchedFields=r.touchedFields,l=l||(A.touchedFields||k.touchedFields)&&t!==a)}l&&n&&w.state.next(d)}return l?d:{}},j=(s,a,i,n)=>{const o=b(r.errors,s),l=(A.isValid||k.isValid)&&p(a)&&r.isValid!==a;var u;if(t.delayError&&i?(u=()=>((e,t)=>{_(r.errors,e,t),w.state.next({errors:r.errors})})(s,i),e=e=>{clearTimeout(x),x=setTimeout(u,e)},e(t.delayError)):(clearTimeout(x),e=null,i?_(r.errors,s,i):Q(r.errors,s)),(i?!L(o,i):o)||!q(n)||l){const e={...n,...l&&p(a)?{isValid:a}:{},errors:r.errors,name:s};r={...r,...e},w.state.next(e)}},R=async e=>{D(e,!0);const r=await t.resolver(c,t.context,ue(e||g.mount,s,t.criteriaMode,t.shouldUseNativeValidation));return D(e),r},N=async function(e,s){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{valid:!0};for(const i in e){const n=e[i];if(n){const{_f:e,...o}=n;if(e){const o=g.array.has(e.name),l=n._f&&ye(n._f);l&&A.validatingFields&&D([i],!0);const u=await Ce(n,g.disabled,c,C,t.shouldUseNativeValidation&&!s,o);if(l&&A.validatingFields&&D([i]),u[e.name]&&(a.valid=!1,s))break;!s&&(b(u,e.name)?o?xe(r.errors,u,e.name):_(r.errors,e.name,u[e.name]):Q(r.errors,e.name))}!q(o)&&await N(o,s,a)}}return a.valid},U=(e,r)=>!t.disabled&&(e&&r&&_(c,e,r),!L(se(),o)),I=(e,t,r)=>O(e,g,{...y.mount?c:v(t)?o:T(e)?{[e]:t}:t},r,t),B=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const i=b(s,e);let o=t;if(i){const r=i._f;r&&(!r.disabled&&_(c,e,ie(t,r)),o=z(r.ref)&&n(t)?"":t,$(r.ref)?[...r.ref.options].forEach(e=>e.selected=o.includes(e.value)):r.refs?a(r.ref)?r.refs.forEach(e=>{e.defaultChecked&&e.disabled||(Array.isArray(o)?e.checked=!!o.find(t=>t===e.value):e.checked=o===e.value||!!o)}):r.refs.forEach(e=>e.checked=e.value===o):H(r.ref)?r.ref.value="":(r.ref.value=o,r.ref.type||w.state.next({name:e,values:m(c)})))}(r.shouldDirty||r.shouldTouch)&&M(e,o,r.shouldTouch,r.shouldDirty,!0),r.shouldValidate&&re(e)},J=(e,t,r)=>{for(const a in t){if(!t.hasOwnProperty(a))return;const n=t[a],o=e+"."+a,u=b(s,o);(g.array.has(e)||l(n)||u&&!u._f)&&!i(n)?J(o,n,r):B(o,n,r)}},X=function(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const i=b(s,e),l=g.array.has(e),u=m(t);_(c,e,u),l?(w.array.next({name:e,values:m(c)}),(A.isDirty||A.dirtyFields||k.isDirty||k.dirtyFields)&&a.shouldDirty&&w.state.next({name:e,dirtyFields:te(o,c),isDirty:U(e,u)})):!i||i._f||n(u)?B(e,u,a):J(e,u,a),he(e,g)&&w.state.next({...r,name:e}),w.state.next({name:y.mount?e:void 0,values:m(c)})},Y=async a=>{y.mount=!0;const n=a.target;let o=n.name,l=!0;const d=b(s,o),f=e=>{l=Number.isNaN(e)||i(e)&&isNaN(e.getTime())||L(e,b(c,o,e))},v=fe(t.mode),h=fe(t.reValidateMode);if(d){let i,y;const p=n.type?le(d._f):u(a),F=a.type===V.BLUR||a.type===V.FOCUS_OUT,x=!ve(d._f)&&!t.resolver&&!b(r.errors,o)&&!d._f.deps||Ve(F,b(r.touchedFields,o),r.isSubmitted,h,v),E=he(o,g,F);_(c,o,p),F?(d._f.onBlur&&d._f.onBlur(a),e&&e(0)):d._f.onChange&&d._f.onChange(a);const T=M(o,p,F),O=!q(T)||E;if(!F&&w.state.next({name:o,type:a.type,values:m(c)}),x)return(A.isValid||k.isValid)&&("onBlur"===t.mode?F&&S():F||S()),O&&w.state.next({name:o,...E?{}:T});if(!F&&E&&w.state.next({...r}),t.resolver){const{errors:e}=await R([o]);if(f(p),l){const t=be(r.errors,s,o),a=be(e,s,t.name||o);i=a.error,o=a.name,y=q(e)}}else D([o],!0),i=(await Ce(d,g.disabled,c,C,t.shouldUseNativeValidation))[o],D([o]),f(p),l&&(i?y=!1:(A.isValid||k.isValid)&&(y=await N(s,!0)));l&&(d._f.deps&&re(d._f.deps),j(o,y,i,T))}},ee=(e,t)=>{if(b(r.errors,t)&&e.focus)return e.focus(),1},re=async function(e){let a,i,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const o=P(e);if(t.resolver){const t=await(async e=>{const{errors:t}=await R(e);if(e)for(const s of e){const e=b(t,s);e?_(r.errors,s,e):Q(r.errors,s)}else r.errors=t;return t})(v(e)?e:o);a=q(t),i=e?!o.some(e=>b(t,e)):a}else e?(i=(await Promise.all(o.map(async e=>{const t=b(s,e);return await N(t&&t._f?{[e]:t}:t)}))).every(Boolean),(i||r.isValid)&&S()):i=a=await N(s);return w.state.next({...!T(e)||(A.isValid||k.isValid)&&a!==r.isValid?{}:{name:e},...t.resolver||!e?{isValid:a}:{},errors:r.errors}),n.shouldFocus&&!i&&ge(s,ee,e?o:g.mount),i},se=e=>{const t={...y.mount?c:o};return v(e)?t:T(e)?b(t,e):e.map(e=>b(t,e))},ae=(e,t)=>({invalid:!!b((t||r).errors,e),isDirty:!!b((t||r).dirtyFields,e),error:b((t||r).errors,e),isValidating:!!b(r.validatingFields,e),isTouched:!!b((t||r).touchedFields,e)}),ne=(e,t,a)=>{const i=(b(s,e,{_f:{}})._f||{}).ref,n=b(r.errors,e)||{},{ref:o,message:l,type:u,...d}=n;_(r.errors,e,{...d,...t,ref:i}),w.state.next({name:e,errors:r.errors,isValid:!1}),a&&a.shouldFocus&&i&&i.focus&&i.focus()},oe=e=>w.state.subscribe({next:t=>{_e(e.name,t.name,e.exact)&&pe(t,e.formState||A,Me,e.reRenderRoot)&&e.callback({values:{...c},...r,...t,defaultValues:o})}}).unsubscribe,de=function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};for(const i of e?P(e):g.mount)g.mount.delete(i),g.array.delete(i),a.keepValue||(Q(s,i),Q(c,i)),!a.keepError&&Q(r.errors,i),!a.keepDirty&&Q(r.dirtyFields,i),!a.keepTouched&&Q(r.touchedFields,i),!a.keepIsValidating&&Q(r.validatingFields,i),!t.shouldUnregister&&!a.keepDefaultValue&&Q(o,i);w.state.next({values:m(c)}),w.state.next({...r,...a.keepDirty?{isDirty:U()}:{}}),!a.keepIsValid&&S()},me=e=>{let{disabled:t,name:r}=e;(p(t)&&y.mount||t||g.disabled.has(r))&&(t?g.disabled.add(r):g.disabled.delete(r))},Ae=function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=b(s,e);const i=p(r.disabled)||p(t.disabled);return _(s,e,{...a||{},_f:{...a&&a._f?a._f:{ref:{name:e}},name:e,mount:!0,...r}}),g.mount.add(e),a?me({disabled:p(r.disabled)?r.disabled:t.disabled,name:e}):E(e,!0,r.value),{...i?{disabled:r.disabled||t.disabled}:{},...t.progressive?{required:!!r.required,min:ce(r.min),max:ce(r.max),minLength:ce(r.minLength),maxLength:ce(r.maxLength),pattern:ce(r.pattern)}:{},name:e,onChange:Y,onBlur:Y,ref:i=>{if(i){Ae(e,r),a=b(s,e);const t=v(i.value)&&i.querySelectorAll&&i.querySelectorAll("input,select,textarea")[0]||i,n=Z(t),l=a._f.refs||[];if(n?l.find(e=>e===t):t===a._f.ref)return;_(s,e,{_f:{...a._f,...n?{refs:[...l.filter(K),t,...Array.isArray(b(o,e))?[{}]:[]],ref:{type:t.type,name:e}}:{ref:t}}}),E(e,!1,void 0,t)}else a=b(s,e,{}),a._f&&(a._f.mount=!1),(t.shouldUnregister||r.shouldUnregister)&&(!d(g.array,e)||!y.action)&&g.unMount.add(e)}}},ke=()=>t.shouldFocusError&&ge(s,ee,g.mount),we=(e,a)=>async i=>{let n;i&&(i.preventDefault&&i.preventDefault(),i.persist&&i.persist());let o=m(c);if(w.state.next({isSubmitting:!0}),t.resolver){const{errors:e,values:t}=await R();r.errors=e,o=m(t)}else await N(s);if(g.disabled.size)for(const e of g.disabled)Q(o,e);if(Q(r.errors,"root"),q(r.errors)){w.state.next({errors:{}});try{await e(o,i)}catch(l){n=l}}else a&&await a({...r.errors},i),ke(),setTimeout(ke);if(w.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:q(r.errors)&&!n,submitCount:r.submitCount+1,errors:r.errors}),n)throw n},De=function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const i=e?m(e):o,n=m(i),l=q(e),u=l?o:n;if(a.keepDefaultValues||(o=i),!a.keepValues){if(a.keepDirtyValues){const e=new Set([...g.mount,...Object.keys(te(o,c))]);for(const t of Array.from(e))b(r.dirtyFields,t)?_(u,t,b(c,t)):X(t,b(u,t))}else{if(f&&v(e))for(const e of g.mount){const t=b(s,e);if(t&&t._f){const e=Array.isArray(t._f.refs)?t._f.refs[0]:t._f.ref;if(z(e)){const t=e.closest("form");if(t){t.reset();break}}}}if(a.keepFieldsRef)for(const e of g.mount)X(e,b(u,e));else s={}}c=t.shouldUnregister?a.keepDefaultValues?m(o):{}:m(u),w.array.next({values:{...u}}),w.state.next({values:{...u}})}g={mount:a.keepDirtyValues?g.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},y.mount=!A.isValid||!!a.keepIsValid||!!a.keepDirtyValues,y.watch=!!t.shouldUnregister,w.state.next({submitCount:a.keepSubmitCount?r.submitCount:0,isDirty:!l&&(a.keepDirty?r.isDirty:!(!a.keepDefaultValues||L(e,o))),isSubmitted:!!a.keepIsSubmitted&&r.isSubmitted,dirtyFields:l?{}:a.keepDirtyValues?a.keepDefaultValues&&c?te(o,c):r.dirtyFields:a.keepDefaultValues&&e?te(o,e):a.keepDirty?r.dirtyFields:{},touchedFields:a.keepTouched?r.touchedFields:{},errors:a.keepErrors?r.errors:{},isSubmitSuccessful:!!a.keepIsSubmitSuccessful&&r.isSubmitSuccessful,isSubmitting:!1})},Ee=(e,t)=>De(W(e)?e(c):e,t),Me=e=>{r={...r,...e}},je={control:{register:Ae,unregister:de,getFieldState:ae,handleSubmit:we,setError:ne,_subscribe:oe,_runSchema:R,_focusError:ke,_getWatch:I,_getDirty:U,_setValid:S,_setFieldArray:function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],i=arguments.length>2?arguments[2]:void 0,n=arguments.length>3?arguments[3]:void 0,l=!(arguments.length>4&&void 0!==arguments[4])||arguments[4],u=!(arguments.length>5&&void 0!==arguments[5])||arguments[5];if(n&&i&&!t.disabled){if(y.action=!0,u&&Array.isArray(b(s,e))){const t=i(b(s,e),n.argA,n.argB);l&&_(s,e,t)}if(u&&Array.isArray(b(r.errors,e))){const t=i(b(r.errors,e),n.argA,n.argB);l&&_(r.errors,e,t),Fe(r.errors,e)}if((A.touchedFields||k.touchedFields)&&u&&Array.isArray(b(r.touchedFields,e))){const t=i(b(r.touchedFields,e),n.argA,n.argB);l&&_(r.touchedFields,e,t)}(A.dirtyFields||k.dirtyFields)&&(r.dirtyFields=te(o,c)),w.state.next({name:e,isDirty:U(e,a),dirtyFields:r.dirtyFields,errors:r.errors,isValid:r.isValid})}else _(c,e,a)},_setDisabledField:me,_setErrors:e=>{r.errors=e,w.state.next({errors:r.errors,isValid:!1})},_getFieldArray:e=>h(b(y.mount?c:o,e,t.shouldUnregister?b(o,e,[]):[])),_reset:De,_resetDefaultValues:()=>W(t.defaultValues)&&t.defaultValues().then(e=>{Ee(e,t.resetOptions),w.state.next({isLoading:!1})}),_removeUnmounted:()=>{for(const e of g.unMount){const t=b(s,e);t&&(t._f.refs?t._f.refs.every(e=>!K(e)):!K(t._f.ref))&&de(e)}g.unMount=new Set},_disableForm:e=>{p(e)&&(w.state.next({disabled:e}),ge(s,(t,r)=>{const a=b(s,r);a&&(t.disabled=a._f.disabled||e,Array.isArray(a._f.refs)&&a._f.refs.forEach(t=>{t.disabled=a._f.disabled||e}))},0,!1))},_subjects:w,_proxyFormState:A,get _fields(){return s},get _formValues(){return c},get _state(){return y},set _state(e){y=e},get _defaultValues(){return o},get _names(){return g},set _names(e){g=e},get _formState(){return r},get _options(){return t},set _options(e){t={...t,...e}}},subscribe:e=>(y.mount=!0,k={...k,...e.formState},oe({...e,formState:k})),trigger:re,register:Ae,handleSubmit:we,watch:(e,t)=>W(e)?w.state.subscribe({next:r=>"values"in r&&e(I(void 0,t),r)}):I(e,t,!0),setValue:X,getValues:se,reset:Ee,resetField:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};b(s,e)&&(v(t.defaultValue)?X(e,m(b(o,e))):(X(e,t.defaultValue),_(o,e,m(t.defaultValue))),t.keepTouched||Q(r.touchedFields,e),t.keepDirty||(Q(r.dirtyFields,e),r.isDirty=t.defaultValue?U(e,m(b(o,e))):U()),t.keepError||(Q(r.errors,e),A.isValid&&S()),w.state.next({...r}))},clearErrors:e=>{e&&P(e).forEach(e=>Q(r.errors,e)),w.state.next({errors:e?r.errors:{}})},unregister:de,setError:ne,setFocus:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const r=b(s,e),a=r&&r._f;if(a){const e=a.refs?a.refs[0]:a.ref;e.focus&&(e.focus(),t.shouldSelect&&W(e.select)&&e.select())}},getFieldState:ae};return{...je,formControl:je}}function Ee(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t=s.useRef(void 0),r=s.useRef(void 0),[a,i]=s.useState({isDirty:!1,isValidating:!1,isLoading:W(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:W(e.defaultValues)?void 0:e.defaultValues});if(!t.current)if(e.formControl)t.current={...e.formControl,formState:a},e.defaultValues&&!W(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions);else{const{formControl:r,...s}=De(e);t.current={...s,formState:a}}const n=t.current.control;return n._options=e,R(()=>{const e=n._subscribe({formState:n._proxyFormState,callback:()=>i({...n._formState}),reRenderRoot:!0});return i(e=>({...e,isReady:!0})),n._formState.isReady=!0,e},[n]),s.useEffect(()=>n._disableForm(e.disabled),[n,e.disabled]),s.useEffect(()=>{e.mode&&(n._options.mode=e.mode),e.reValidateMode&&(n._options.reValidateMode=e.reValidateMode)},[n,e.mode,e.reValidateMode]),s.useEffect(()=>{e.errors&&(n._setErrors(e.errors),n._focusError())},[n,e.errors]),s.useEffect(()=>{e.shouldUnregister&&n._subjects.state.next({values:n._getWatch()})},[n,e.shouldUnregister]),s.useEffect(()=>{if(n._proxyFormState.isDirty){const e=n._getDirty();e!==a.isDirty&&n._subjects.state.next({isDirty:e})}},[n,a.isDirty]),s.useEffect(()=>{e.values&&!L(e.values,r.current)?(n._reset(e.values,{keepFieldsRef:!0,...n._options.resetOptions}),r.current=e.values,i(e=>({...e}))):n._resetDefaultValues()},[n,e.values]),s.useEffect(()=>{n._state.mount||(n._setValid(),n._state.mount=!0),n._state.watch&&(n._state.watch=!1,n._subjects.state.next({...n._formState})),n._removeUnmounted()}),t.current.formState=j(a,n),t.current}},5923:(e,t,r)=>{r.d(t,{A:()=>s});const s=(0,r(3797).A)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},5979:(e,t,r)=>{r.d(t,{A:()=>s});const s=(0,r(3797).A)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},6339:(e,t,r)=>{r.d(t,{A:()=>s});const s=(0,r(3797).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},7127:(e,t,r)=>{r.d(t,{B8:()=>D,UC:()=>M,bL:()=>S,l9:()=>E});var s=r(5043),a=r(858),i=r(1862),n=r(6711),o=r(2894),l=r(7920),u=r(4204),d=r(3642),c=r(4490),f=r(579),m="Tabs",[y,v]=(0,i.A)(m,[n.RG]),h=(0,n.RG)(),[g,b]=y(m),p=s.forwardRef((e,t)=>{const{__scopeTabs:r,value:s,onValueChange:a,defaultValue:i,orientation:n="horizontal",dir:o,activationMode:y="automatic",...v}=e,h=(0,u.jH)(o),[b,p]=(0,d.i)({prop:s,onChange:a,defaultProp:i??"",caller:m});return(0,f.jsx)(g,{scope:r,baseId:(0,c.B)(),value:b,onValueChange:p,orientation:n,dir:h,activationMode:y,children:(0,f.jsx)(l.sG.div,{dir:h,"data-orientation":n,...v,ref:t})})});p.displayName=m;var _="TabsList",V=s.forwardRef((e,t)=>{const{__scopeTabs:r,loop:s=!0,...a}=e,i=b(_,r),o=h(r);return(0,f.jsx)(n.bL,{asChild:!0,...o,orientation:i.orientation,dir:i.dir,loop:s,children:(0,f.jsx)(l.sG.div,{role:"tablist","aria-orientation":i.orientation,...a,ref:t})})});V.displayName=_;var F="TabsTrigger",x=s.forwardRef((e,t)=>{const{__scopeTabs:r,value:s,disabled:i=!1,...o}=e,u=b(F,r),d=h(r),c=w(u.baseId,s),m=C(u.baseId,s),y=s===u.value;return(0,f.jsx)(n.q7,{asChild:!0,...d,focusable:!i,active:y,children:(0,f.jsx)(l.sG.button,{type:"button",role:"tab","aria-selected":y,"aria-controls":m,"data-state":y?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:c,...o,ref:t,onMouseDown:(0,a.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():u.onValueChange(s)}),onKeyDown:(0,a.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&u.onValueChange(s)}),onFocus:(0,a.m)(e.onFocus,()=>{const e="manual"!==u.activationMode;y||i||!e||u.onValueChange(s)})})})});x.displayName=F;var A="TabsContent",k=s.forwardRef((e,t)=>{const{__scopeTabs:r,value:a,forceMount:i,children:n,...u}=e,d=b(A,r),c=w(d.baseId,a),m=C(d.baseId,a),y=a===d.value,v=s.useRef(y);return s.useEffect(()=>{const e=requestAnimationFrame(()=>v.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,f.jsx)(o.C,{present:i||y,children:r=>{let{present:s}=r;return(0,f.jsx)(l.sG.div,{"data-state":y?"active":"inactive","data-orientation":d.orientation,role:"tabpanel","aria-labelledby":c,hidden:!s,id:m,tabIndex:0,...u,ref:t,style:{...e.style,animationDuration:v.current?"0s":void 0},children:s&&n})}})});function w(e,t){return`${e}-trigger-${t}`}function C(e,t){return`${e}-content-${t}`}k.displayName=A;var S=p,D=V,E=x,M=k},7660:(e,t,r)=>{r.d(t,{Ec:()=>$,D0:()=>W,JU:()=>z,QB:()=>J,bL:()=>H});var s=r(5043),a=r(858),i=r(2814),n=r(1862),o=r(4490),l=r(7920),u=r(579),d=s.forwardRef((e,t)=>(0,u.jsx)(l.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));d.displayName="Label";var[c,f]=(0,n.A)("Form"),m="Form",[y,v]=c(m),[h,g]=c(m),b=s.forwardRef((e,t)=>{const{__scopeForm:r,onClearServerErrors:n=()=>{},...o}=e,d=s.useRef(null),c=(0,i.s)(t,d),[f,m]=s.useState({}),v=s.useCallback(e=>f[e],[f]),g=s.useCallback((e,t)=>m(r=>({...r,[e]:{...r[e]??{},...t}})),[]),b=s.useCallback(e=>{m(t=>({...t,[e]:void 0})),k(t=>({...t,[e]:{}}))},[]),[p,_]=s.useState({}),V=s.useCallback(e=>p[e]??[],[p]),F=s.useCallback((e,t)=>{_(r=>({...r,[e]:[...r[e]??[],t]}))},[]),x=s.useCallback((e,t)=>{_(r=>({...r,[e]:(r[e]??[]).filter(e=>e.id!==t)}))},[]),[A,k]=s.useState({}),w=s.useCallback(e=>A[e]??{},[A]),C=s.useCallback((e,t)=>{k(r=>({...r,[e]:{...r[e]??{},...t}}))},[]),[S,D]=s.useState({}),E=s.useCallback((e,t)=>{D(r=>{const s=new Set(r[e]).add(t);return{...r,[e]:s}})},[]),M=s.useCallback((e,t)=>{D(r=>{const s=new Set(r[e]);return s.delete(t),{...r,[e]:s}})},[]),j=s.useCallback(e=>Array.from(S[e]??[]).join(" ")||void 0,[S]);return(0,u.jsx)(y,{scope:r,getFieldValidity:v,onFieldValidityChange:g,getFieldCustomMatcherEntries:V,onFieldCustomMatcherEntryAdd:F,onFieldCustomMatcherEntryRemove:x,getFieldCustomErrors:w,onFieldCustomErrorsChange:C,onFieldValiditionClear:b,children:(0,u.jsx)(h,{scope:r,onFieldMessageIdAdd:E,onFieldMessageIdRemove:M,getFieldDescription:j,children:(0,u.jsx)(l.sG.form,{...o,ref:c,onInvalid:(0,a.m)(e.onInvalid,e=>{const t=B(e.currentTarget);t===e.target&&t.focus(),e.preventDefault()}),onSubmit:(0,a.m)(e.onSubmit,n,{checkForDefaultPrevented:!1}),onReset:(0,a.m)(e.onReset,n)})})})});b.displayName=m;var p="FormField",[_,V]=c(p),F=s.forwardRef((e,t)=>{const{__scopeForm:r,name:s,serverInvalid:a=!1,...i}=e,n=v(p,r).getFieldValidity(s),d=(0,o.B)();return(0,u.jsx)(_,{scope:r,id:d,name:s,serverInvalid:a,children:(0,u.jsx)(l.sG.div,{"data-valid":G(n,a),"data-invalid":q(n,a),...i,ref:t})})});F.displayName=p;var x="FormLabel",A=s.forwardRef((e,t)=>{const{__scopeForm:r,...s}=e,a=v(x,r),i=V(x,r),n=s.htmlFor||i.id,o=a.getFieldValidity(i.name);return(0,u.jsx)(d,{"data-valid":G(o,i.serverInvalid),"data-invalid":q(o,i.serverInvalid),...s,ref:t,htmlFor:n})});A.displayName=x;var k="FormControl",w=s.forwardRef((e,t)=>{const{__scopeForm:r,...n}=e,o=v(k,r),d=V(k,r),c=g(k,r),f=s.useRef(null),m=(0,i.s)(t,f),y=n.name||d.name,h=n.id||d.id,b=o.getFieldCustomMatcherEntries(y),{onFieldValidityChange:p,onFieldCustomErrorsChange:_,onFieldValiditionClear:F}=o,x=s.useCallback(async e=>{if(P(e.validity)){const t=L(e.validity);return void p(y,t)}const t=e.form?new FormData(e.form):new FormData,r=[e.value,t],s=[],a=[];b.forEach(e=>{var t,i;i=r,"AsyncFunction"===(t=e).match.constructor.name||function(e,t){return e(...t)instanceof Promise}(t.match,i)?a.push(e):function(e){return"Function"===e.match.constructor.name}(e)&&s.push(e)});const i=s.map(e=>{let{id:t,match:s}=e;return[t,s(...r)]}),n=Object.fromEntries(i),o=Object.values(n).some(Boolean),l=o;e.setCustomValidity(l?C:"");const u=L(e.validity);if(p(y,u),_(y,n),!o&&a.length>0){const t=a.map(e=>{let{id:t,match:s}=e;return s(...r).then(e=>[t,e])}),s=await Promise.all(t),i=Object.fromEntries(s),n=Object.values(i).some(Boolean);e.setCustomValidity(n?C:"");const o=L(e.validity);p(y,o),_(y,i)}},[b,y,_,p]);s.useEffect(()=>{const e=f.current;if(e){const t=()=>x(e);return e.addEventListener("change",t),()=>e.removeEventListener("change",t)}},[x]);const A=s.useCallback(()=>{const e=f.current;e&&(e.setCustomValidity(""),F(y))},[y,F]);s.useEffect(()=>{const e=f.current?.form;if(e)return e.addEventListener("reset",A),()=>e.removeEventListener("reset",A)},[A]),s.useEffect(()=>{const e=f.current,t=e?.closest("form");if(t&&d.serverInvalid){const r=B(t);r===e&&r.focus()}},[d.serverInvalid]);const w=o.getFieldValidity(y);return(0,u.jsx)(l.sG.input,{"data-valid":G(w,d.serverInvalid),"data-invalid":q(w,d.serverInvalid),"aria-invalid":!!d.serverInvalid||void 0,"aria-describedby":c.getFieldDescription(y),title:"",...n,ref:m,id:h,name:y,onInvalid:(0,a.m)(e.onInvalid,e=>{const t=e.currentTarget;x(t)}),onChange:(0,a.m)(e.onChange,e=>{A()})})});w.displayName=k;var C="This value is not valid",S={badInput:C,patternMismatch:"This value does not match the required pattern",rangeOverflow:"This value is too large",rangeUnderflow:"This value is too small",stepMismatch:"This value does not match the required step",tooLong:"This value is too long",tooShort:"This value is too short",typeMismatch:"This value does not match the required type",valid:void 0,valueMissing:"This value is missing"},D="FormMessage",E=s.forwardRef((e,t)=>{const{match:r,name:s,...a}=e,i=V(D,e.__scopeForm),n=s??i.name;return void 0===r?(0,u.jsx)(R,{...a,ref:t,name:n,children:e.children||C}):"function"===typeof r?(0,u.jsx)(j,{match:r,...a,ref:t,name:n}):(0,u.jsx)(M,{match:r,...a,ref:t,name:n})});E.displayName=D;var M=s.forwardRef((e,t)=>{const{match:r,forceMatch:s=!1,name:a,children:i,...n}=e,o=v(D,n.__scopeForm).getFieldValidity(a);return s||o?.[r]?(0,u.jsx)(R,{ref:t,...n,name:a,children:i??S[r]}):null}),j=s.forwardRef((e,t)=>{const{match:r,forceMatch:a=!1,name:n,id:l,children:d,...c}=e,f=v(D,c.__scopeForm),m=s.useRef(null),y=(0,i.s)(t,m),h=(0,o.B)(),g=l??h,b=s.useMemo(()=>({id:g,match:r}),[g,r]),{onFieldCustomMatcherEntryAdd:p,onFieldCustomMatcherEntryRemove:_}=f;s.useEffect(()=>(p(n,b),()=>_(n,b.id)),[b,n,p,_]);const V=f.getFieldValidity(n),F=f.getFieldCustomErrors(n)[g];return a||V&&!P(V)&&F?(0,u.jsx)(R,{id:g,ref:y,...c,name:n,children:d??C}):null}),R=s.forwardRef((e,t)=>{const{__scopeForm:r,id:a,name:i,...n}=e,d=g(D,r),c=(0,o.B)(),f=a??c,{onFieldMessageIdAdd:m,onFieldMessageIdRemove:y}=d;return s.useEffect(()=>(m(i,f),()=>y(i,f)),[i,f,m,y]),(0,u.jsx)(l.sG.span,{id:f,...n,ref:t})}),T="FormValidityState",O=e=>{const{__scopeForm:t,name:r,children:s}=e,a=v(T,t),i=V(T,t),n=r??i.name,o=a.getFieldValidity(n);return(0,u.jsx)(u.Fragment,{children:s(o)})};O.displayName=T;var N=s.forwardRef((e,t)=>{const{__scopeForm:r,...s}=e;return(0,u.jsx)(l.sG.button,{type:"submit",...s,ref:t})});function L(e){const t={};for(const r in e)t[r]=e[r];return t}function U(e){return e instanceof HTMLElement}function I(e){return"validity"in e&&(!1===e.validity.valid||"true"===e.getAttribute("aria-invalid"))}function B(e){const t=e.elements,[r]=Array.from(t).filter(U).filter(I);return r}function P(e){let t=!1;for(const r in e){if("valid"!==r&&"customError"!==r&&e[r]){t=!0;break}}return t}function G(e,t){if(!0===e?.valid&&!t)return!0}function q(e,t){if(!1===e?.valid||t)return!0}N.displayName="FormSubmit";var H=b,W=F,z=A,$=w,J=E},8064:(e,t,r)=>{r.d(t,{A:()=>s});const s=(0,r(3797).A)("CheckCircle2",[["path",{d:"M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z",key:"14v8dr"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},8775:(e,t,r)=>{r.d(t,{A:()=>s});const s=(0,r(3797).A)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}}]);
//# sourceMappingURL=168.d27113db.chunk.js.map