const express = require('express');
const router = express.Router();
const User = require('../models/User');
const Transaction = require('../models/Transaction');

// Get all users with pagination
router.get('/', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;
    const role = req.query.role; // Filter by role if provided
    
    let query = {};
    if (role && ['admin', 'merchant', 'trader'].includes(role)) {
      query.role = role;
    }
    
    const users = await User.find(query)
      .select('-__v')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await User.countDocuments(query);

    res.json({
      success: true,
      count: users.length,
      total: total,
      page: page,
      totalPages: Math.ceil(total / limit),
      data: users
    });
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({ 
      success: false,
      message: 'Failed to get users' 
    });
  }
});

// Get user by ID with transaction summary
router.get('/:id', async (req, res) => {
  try {
    const user = await User.findById(req.params.id).select('-__v');
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Get transaction summary for this user
    let transactionSummary = {};
    if (user.role === 'merchant') {
      const merchantTransactions = await Transaction.aggregate([
        { $match: { merchantId: user._id } },
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 },
            totalAmount: { $sum: '$amount' }
          }
        }
      ]);
      transactionSummary.asmerchant = merchantTransactions;
    }
    
    if (user.role === 'trader') {
      const traderTransactions = await Transaction.aggregate([
        { $match: { traderId: user._id } },
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 },
            totalAmount: { $sum: '$amount' }
          }
        }
      ]);
      transactionSummary.astrader = traderTransactions;
    }

    res.json({
      success: true,
      data: {
        user,
        transactionSummary
      }
    });
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({ 
      success: false,
      message: 'Failed to get user details' 
    });
  }
});

// Get users by role
router.get('/role/:role', async (req, res) => {
  try {
    const validRoles = ['admin', 'merchant', 'trader'];
    if (!validRoles.includes(req.params.role)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid role. Valid roles: ' + validRoles.join(', ')
      });
    }

    const users = await User.find({ role: req.params.role })
      .select('-__v')
      .sort({ createdAt: -1 });

    res.json({
      success: true,
      count: users.length,
      data: users
    });
  } catch (error) {
    console.error('Get users by role error:', error);
    res.status(500).json({ 
      success: false,
      message: 'Failed to get users by role' 
    });
  }
});

// Get user statistics
router.get('/stats/overview', async (req, res) => {
  try {
    const totalUsers = await User.countDocuments();
    const adminCount = await User.countDocuments({ role: 'admin' });
    const merchantCount = await User.countDocuments({ role: 'merchant' });
    const traderCount = await User.countDocuments({ role: 'trader' });
    const activeUsers = await User.countDocuments({ isActive: true });

    res.json({
      success: true,
      data: {
        totalUsers,
        adminCount,
        merchantCount,
        traderCount,
        activeUsers,
        inactiveUsers: totalUsers - activeUsers
      }
    });
  } catch (error) {
    console.error('Get user stats error:', error);
    res.status(500).json({ 
      success: false,
      message: 'Failed to get user statistics' 
    });
  }
});

module.exports = router;
