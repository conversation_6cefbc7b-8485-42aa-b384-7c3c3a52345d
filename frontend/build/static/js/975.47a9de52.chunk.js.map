{"version": 3, "file": "static/js/975.47a9de52.chunk.js", "mappings": "oJAaM,MAAAA,GAAQC,E,QAAAA,GAAiB,QAAS,CACtC,CAAC,WAAY,CAAEC,OAAQ,iBAAkBC,IAAK,Y,oICL5CC,EAH+B,qBAAbC,SAGCC,EAAAA,gBADZ,WAAiB,EAK5B,SAASC,EAAUC,EAAGC,GACpB,GAAID,IAAMC,EACR,OAAO,EAET,UAAWD,WAAaC,EACtB,OAAO,EAET,GAAiB,oBAAND,GAAoBA,EAAEE,aAAeD,EAAEC,WAChD,OAAO,EAET,IAAIC,EACAC,EACAC,EACJ,GAAIL,GAAKC,GAAkB,kBAAND,EAAgB,CACnC,GAAIM,MAAMC,QAAQP,GAAI,CAEpB,GADAG,EAASH,EAAEG,OACPA,IAAWF,EAAEE,OAAQ,OAAO,EAChC,IAAKC,EAAID,EAAgB,IAARC,KACf,IAAKL,EAAUC,EAAEI,GAAIH,EAAEG,IACrB,OAAO,EAGX,OAAO,CACT,CAGA,GAFAC,EAAOG,OAAOH,KAAKL,GACnBG,EAASE,EAAKF,OACVA,IAAWK,OAAOH,KAAKJ,GAAGE,OAC5B,OAAO,EAET,IAAKC,EAAID,EAAgB,IAARC,KACf,IAAK,CAAC,EAAEK,eAAeC,KAAKT,EAAGI,EAAKD,IAClC,OAAO,EAGX,IAAKA,EAAID,EAAgB,IAARC,KAAY,CAC3B,MAAMT,EAAMU,EAAKD,GACjB,IAAY,WAART,IAAoBK,EAAEW,YAGrBZ,EAAUC,EAAEL,GAAMM,EAAEN,IACvB,OAAO,CAEX,CACA,OAAO,CACT,CACA,OAAOK,IAAMA,GAAKC,IAAMA,CAC1B,CAEA,SAASW,EAAOC,GACd,GAAsB,qBAAXC,OACT,OAAO,EAGT,OADYD,EAAQE,cAAcC,aAAeF,QACtCG,kBAAoB,CACjC,CAEA,SAASC,EAAWL,EAASM,GAC3B,MAAMC,EAAMR,EAAOC,GACnB,OAAOQ,KAAKC,MAAMH,EAAQC,GAAOA,CACnC,CAEA,SAASG,EAAaJ,GACpB,MAAMK,EAAMC,EAAAA,OAAaN,GAIzB,OAHAvB,EAAM,KACJ4B,EAAIE,QAAUP,IAETK,CACT,CAMA,SAASG,EAAYC,QACH,IAAZA,IACFA,EAAU,CAAC,GAEb,MAAM,UACJC,EAAY,SAAQ,SACpBC,EAAW,WAAU,WACrBC,EAAa,GAAE,SACfC,EACAC,UACEC,UAAWC,EACXC,SAAUC,GACR,CAAC,EAAC,UACNC,GAAY,EAAI,qBAChBC,EAAoB,KACpBC,GACEZ,GACGa,EAAMC,GAAWjB,EAAAA,SAAe,CACrCkB,EAAG,EACHC,EAAG,EACHd,WACAD,YACAgB,eAAgB,CAAC,EACjBC,cAAc,KAETC,EAAkBC,GAAuBvB,EAAAA,SAAeM,GAC1DhC,EAAUgD,EAAkBhB,IAC/BiB,EAAoBjB,GAEtB,MAAOkB,EAAYC,GAAiBzB,EAAAA,SAAe,OAC5C0B,EAAWC,GAAgB3B,EAAAA,SAAe,MAC3C4B,EAAe5B,EAAAA,YAAkB6B,IACjCA,IAASC,EAAa7B,UACxB6B,EAAa7B,QAAU4B,EACvBJ,EAAcI,KAEf,IACGE,EAAc/B,EAAAA,YAAkB6B,IAChCA,IAASG,EAAY/B,UACvB+B,EAAY/B,QAAU4B,EACtBF,EAAaE,KAEd,IACGI,EAAcvB,GAAqBc,EACnCU,EAAatB,GAAoBc,EACjCI,EAAe9B,EAAAA,OAAa,MAC5BgC,EAAchC,EAAAA,OAAa,MAC3BmC,EAAUnC,EAAAA,OAAagB,GACvBoB,EAAkD,MAAxBtB,EAC1BuB,EAA0BvC,EAAagB,GACvCwB,EAAcxC,EAAaS,GAC3BgC,EAAUzC,EAAaiB,GACvByB,EAASxC,EAAAA,YAAkB,KAC/B,IAAK8B,EAAa7B,UAAY+B,EAAY/B,QACxC,OAEF,MAAMwC,EAAS,CACbrC,YACAC,WACAC,WAAYgB,GAEVgB,EAAYrC,UACdwC,EAAOlC,SAAW+B,EAAYrC,UAEhCyC,EAAAA,EAAAA,IAAgBZ,EAAa7B,QAAS+B,EAAY/B,QAASwC,GAAQE,KAAK3B,IACtE,MAAM4B,EAAW,IACZ5B,EAKHK,cAAkC,IAApBkB,EAAQtC,SAEpB4C,EAAa5C,UAAY3B,EAAU6D,EAAQlC,QAAS2C,KACtDT,EAAQlC,QAAU2C,EAClBE,EAAAA,UAAmB,KACjB7B,EAAQ2B,SAIb,CAACtB,EAAkBlB,EAAWC,EAAUiC,EAAaC,IACxDpE,EAAM,MACS,IAAT4C,GAAkBoB,EAAQlC,QAAQoB,eACpCc,EAAQlC,QAAQoB,cAAe,EAC/BJ,EAAQD,IAAQ,IACXA,EACHK,cAAc,OAGjB,CAACN,IACJ,MAAM8B,EAAe7C,EAAAA,QAAa,GAClC7B,EAAM,KACJ0E,EAAa5C,SAAU,EAChB,KACL4C,EAAa5C,SAAU,IAExB,IACH9B,EAAM,KAGJ,GAFI8D,IAAaH,EAAa7B,QAAUgC,GACpCC,IAAYF,EAAY/B,QAAUiC,GAClCD,GAAeC,EAAY,CAC7B,GAAIG,EAAwBpC,QAC1B,OAAOoC,EAAwBpC,QAAQgC,EAAaC,EAAYM,GAElEA,GACF,GACC,CAACP,EAAaC,EAAYM,EAAQH,EAAyBD,IAC9D,MAAMW,EAAO/C,EAAAA,QAAc,KAAM,CAC/BS,UAAWqB,EACXnB,SAAUqB,EACVJ,eACAG,gBACE,CAACH,EAAcG,IACbvB,EAAWR,EAAAA,QAAc,KAAM,CACnCS,UAAWwB,EACXtB,SAAUuB,IACR,CAACD,EAAaC,IACZc,EAAiBhD,EAAAA,QAAc,KACnC,MAAMiD,EAAgB,CACpBC,SAAU7C,EACV8C,KAAM,EACNC,IAAK,GAEP,IAAK5C,EAASG,SACZ,OAAOsC,EAET,MAAM/B,EAAIzB,EAAWe,EAASG,SAAUK,EAAKE,GACvCC,EAAI1B,EAAWe,EAASG,SAAUK,EAAKG,GAC7C,OAAIN,EACK,IACFoC,EACHpC,UAAW,aAAeK,EAAI,OAASC,EAAI,SACvChC,EAAOqB,EAASG,WAAa,KAAO,CACtC0C,WAAY,cAIX,CACLH,SAAU7C,EACV8C,KAAMjC,EACNkC,IAAKjC,IAEN,CAACd,EAAUQ,EAAWL,EAASG,SAAUK,EAAKE,EAAGF,EAAKG,IACzD,OAAOnB,EAAAA,QAAc,KAAM,IACtBgB,EACHwB,SACAO,OACAvC,WACAwC,mBACE,CAAChC,EAAMwB,EAAQO,EAAMvC,EAAUwC,GACrC,CAQA,MAAMM,EAAUnD,IAIP,CACLoD,KAAM,QACNpD,UACAqD,EAAAA,CAAGC,GACD,MAAM,QACJrE,EAAO,QACPsE,GACqB,oBAAZvD,EAAyBA,EAAQsD,GAAStD,EACrD,OAAIf,IAXOM,EAWUN,EAVhB,CAAC,EAAEJ,eAAeC,KAAKS,EAAO,YAWV,MAAnBN,EAAQa,SACH0D,EAAAA,EAAAA,IAAQ,CACbvE,QAASA,EAAQa,QACjByD,YACCF,GAAGC,GAED,CAAC,EAENrE,GACKuE,EAAAA,EAAAA,IAAQ,CACbvE,UACAsE,YACCF,GAAGC,GAED,CAAC,EA1BZ,IAAe/D,CA2Bb,IAWEkE,EAASA,CAACzD,EAAS0D,KAAS,KAC7BC,EAAAA,EAAAA,IAAS3D,GACZA,QAAS,CAACA,EAAS0D,KAQfE,EAAQA,CAAC5D,EAAS0D,KAAS,KAC5BG,EAAAA,EAAAA,IAAQ7D,GACXA,QAAS,CAACA,EAAS0D,KAMfI,EAAaA,CAAC9D,EAAS0D,KAAS,KACjCK,EAAAA,EAAAA,IAAa/D,GAChBA,QAAS,CAACA,EAAS0D,KASfM,EAAOA,CAAChE,EAAS0D,KAAS,KAC3BO,EAAAA,EAAAA,IAAOjE,GACVA,QAAS,CAACA,EAAS0D,KASfQ,EAAOA,CAAClE,EAAS0D,KAAS,KAC3BS,EAAAA,EAAAA,IAAOnE,GACVA,QAAS,CAACA,EAAS0D,KAmBfU,EAAOA,CAACpE,EAAS0D,KAAS,KAC3BW,EAAAA,EAAAA,IAAOrE,GACVA,QAAS,CAACA,EAAS0D,KAmBfY,EAAQA,CAACtE,EAAS0D,KAAS,IAC5BP,EAAQnD,GACXA,QAAS,CAACA,EAAS0D,I,oHC1WrB,MAAMa,EAAQ,CAAC,MAAO,QAAS,SAAU,QAGnCC,EAAM/E,KAAK+E,IACXC,EAAMhF,KAAKgF,IACX/E,EAAQD,KAAKC,MACbgF,EAAQjF,KAAKiF,MACbC,EAAeC,IAAK,CACxB7D,EAAG6D,EACH5D,EAAG4D,IAECC,EAAkB,CACtB7B,KAAM,QACN8B,MAAO,OACPC,OAAQ,MACR9B,IAAK,UAED+B,EAAuB,CAC3BC,MAAO,MACPC,IAAK,SAEP,SAASC,EAAMF,EAAO1F,EAAO2F,GAC3B,OAAOT,EAAIQ,EAAOT,EAAIjF,EAAO2F,GAC/B,CACA,SAASE,EAAS7F,EAAO8F,GACvB,MAAwB,oBAAV9F,EAAuBA,EAAM8F,GAAS9F,CACtD,CACA,SAAS+F,EAAQrF,GACf,OAAOA,EAAUsF,MAAM,KAAK,EAC9B,CACA,SAASC,EAAavF,GACpB,OAAOA,EAAUsF,MAAM,KAAK,EAC9B,CACA,SAASE,EAAgBC,GACvB,MAAgB,MAATA,EAAe,IAAM,GAC9B,CACA,SAASC,EAAcD,GACrB,MAAgB,MAATA,EAAe,SAAW,OACnC,CACA,MAAME,EAA0B,IAAIC,IAAI,CAAC,MAAO,WAChD,SAASC,EAAY7F,GACnB,OAAO2F,EAAWG,IAAIT,EAAQrF,IAAc,IAAM,GACpD,CACA,SAAS+F,EAAiB/F,GACxB,OAAOwF,EAAgBK,EAAY7F,GACrC,CAkBA,SAASgG,EAA8BhG,GACrC,OAAOA,EAAUiG,QAAQ,aAAcC,GAAanB,EAAqBmB,GAC3E,CACA,MAAMC,EAAc,CAAC,OAAQ,SACvBC,EAAc,CAAC,QAAS,QACxBC,EAAc,CAAC,MAAO,UACtBC,EAAc,CAAC,SAAU,OAc/B,SAASC,EAA0BvG,EAAWwG,EAAeC,EAAWC,GACtE,MAAMR,EAAYX,EAAavF,GAC/B,IAAI2G,EAfN,SAAqBC,EAAMC,EAASH,GAClC,OAAQE,GACN,IAAK,MACL,IAAK,SACH,OAAIF,EAAYG,EAAUT,EAAcD,EACjCU,EAAUV,EAAcC,EACjC,IAAK,OACL,IAAK,QACH,OAAOS,EAAUR,EAAcC,EACjC,QACE,MAAO,GAEb,CAGaQ,CAAYzB,EAAQrF,GAA0B,UAAdyG,EAAuBC,GAOlE,OANIR,IACFS,EAAOA,EAAKI,IAAIH,GAAQA,EAAO,IAAMV,GACjCM,IACFG,EAAOA,EAAKK,OAAOL,EAAKI,IAAIf,MAGzBW,CACT,CACA,SAASM,EAAqBjH,GAC5B,OAAOA,EAAUiG,QAAQ,yBAA0BW,GAAQhC,EAAgBgC,GAC7E,CAUA,SAASM,EAAiB5D,GACxB,MAA0B,kBAAZA,EAVhB,SAA6BA,GAC3B,MAAO,CACLN,IAAK,EACL6B,MAAO,EACPC,OAAQ,EACR/B,KAAM,KACHO,EAEP,CAEuC6D,CAAoB7D,GAAW,CAClEN,IAAKM,EACLuB,MAAOvB,EACPwB,OAAQxB,EACRP,KAAMO,EAEV,CACA,SAAS8D,EAAiBC,GACxB,MAAM,EACJvG,EAAC,EACDC,EAAC,MACDuG,EAAK,OACLC,GACEF,EACJ,MAAO,CACLC,QACAC,SACAvE,IAAKjC,EACLgC,KAAMjC,EACN+D,MAAO/D,EAAIwG,EACXxC,OAAQ/D,EAAIwG,EACZzG,IACAC,IAEJ,CCrIA,SAASyG,EAA2BC,EAAMzH,EAAW0G,GACnD,IAAI,UACFrG,EAAS,SACTE,GACEkH,EACJ,MAAMC,EAAW7B,EAAY7F,GACvB2H,EAAgB5B,EAAiB/F,GACjC4H,EAAclC,EAAciC,GAC5Bf,EAAOvB,EAAQrF,GACf6H,EAA0B,MAAbH,EACbI,EAAUzH,EAAUS,EAAIT,EAAUiH,MAAQ,EAAI/G,EAAS+G,MAAQ,EAC/DS,EAAU1H,EAAUU,EAAIV,EAAUkH,OAAS,EAAIhH,EAASgH,OAAS,EACjES,EAAc3H,EAAUuH,GAAe,EAAIrH,EAASqH,GAAe,EACzE,IAAIK,EACJ,OAAQrB,GACN,IAAK,MACHqB,EAAS,CACPnH,EAAGgH,EACH/G,EAAGV,EAAUU,EAAIR,EAASgH,QAE5B,MACF,IAAK,SACHU,EAAS,CACPnH,EAAGgH,EACH/G,EAAGV,EAAUU,EAAIV,EAAUkH,QAE7B,MACF,IAAK,QACHU,EAAS,CACPnH,EAAGT,EAAUS,EAAIT,EAAUiH,MAC3BvG,EAAGgH,GAEL,MACF,IAAK,OACHE,EAAS,CACPnH,EAAGT,EAAUS,EAAIP,EAAS+G,MAC1BvG,EAAGgH,GAEL,MACF,QACEE,EAAS,CACPnH,EAAGT,EAAUS,EACbC,EAAGV,EAAUU,GAGnB,OAAQwE,EAAavF,IACnB,IAAK,QACHiI,EAAON,IAAkBK,GAAetB,GAAOmB,GAAc,EAAI,GACjE,MACF,IAAK,MACHI,EAAON,IAAkBK,GAAetB,GAAOmB,GAAc,EAAI,GAGrE,OAAOI,CACT,CAqGAC,eAAeC,EAAe9E,EAAOtD,GACnC,IAAIqI,OACY,IAAZrI,IACFA,EAAU,CAAC,GAEb,MAAM,EACJe,EAAC,EACDC,EAAC,SACDZ,EAAQ,MACRkI,EAAK,SACLjI,EAAQ,SACRH,GACEoD,GACE,SACJiF,EAAW,oBAAmB,aAC9BC,EAAe,WAAU,eACzBC,EAAiB,WAAU,YAC3BC,GAAc,EAAK,QACnBnF,EAAU,GACR6B,EAASpF,EAASsD,GAChBqF,EAAgBxB,EAAiB5D,GAEjCtE,EAAUoB,EAASqI,EADa,aAAnBD,EAAgC,YAAc,WACbA,GAC9CG,EAAqBvB,QAAuBjH,EAASyI,gBAAgB,CACzE5J,QAAiH,OAAtGoJ,QAAqD,MAAtBjI,EAAS0I,eAAoB,EAAS1I,EAAS0I,UAAU7J,MAAqBoJ,EAAgCpJ,EAAUA,EAAQ8J,sBAAyD,MAA/B3I,EAAS4I,wBAA6B,EAAS5I,EAAS4I,mBAAmB3I,EAASG,WACxR+H,WACAC,eACAtI,cAEIoH,EAA0B,aAAnBmB,EAAgC,CAC3C1H,IACAC,IACAuG,MAAOe,EAAM9H,SAAS+G,MACtBC,OAAQc,EAAM9H,SAASgH,QACrBc,EAAMhI,UACJ2I,QAAkD,MAA5B7I,EAAS8I,qBAA0B,EAAS9I,EAAS8I,gBAAgB7I,EAASG,WACpG2I,QAA4C,MAAtB/I,EAAS0I,eAAoB,EAAS1I,EAAS0I,UAAUG,WAA+C,MAArB7I,EAASgJ,cAAmB,EAAShJ,EAASgJ,SAASH,KAGlK,CACFlI,EAAG,EACHC,EAAG,GAECqI,EAAoBhC,EAAiBjH,EAASkJ,4DAA8DlJ,EAASkJ,sDAAsD,CAC/KjJ,WACAiH,OACA2B,eACA/I,aACGoH,GACL,MAAO,CACLrE,KAAM2F,EAAmB3F,IAAMoG,EAAkBpG,IAAM0F,EAAc1F,KAAOkG,EAAYnI,EACxF+D,QAASsE,EAAkBtE,OAAS6D,EAAmB7D,OAAS4D,EAAc5D,QAAUoE,EAAYnI,EACpGgC,MAAO4F,EAAmB5F,KAAOqG,EAAkBrG,KAAO2F,EAAc3F,MAAQmG,EAAYpI,EAC5F+D,OAAQuE,EAAkBvE,MAAQ8D,EAAmB9D,MAAQ6D,EAAc7D,OAASqE,EAAYpI,EAEpG,CA+TA,SAASwI,EAAeC,EAAUlC,GAChC,MAAO,CACLrE,IAAKuG,EAASvG,IAAMqE,EAAKE,OACzB1C,MAAO0E,EAAS1E,MAAQwC,EAAKC,MAC7BxC,OAAQyE,EAASzE,OAASuC,EAAKE,OAC/BxE,KAAMwG,EAASxG,KAAOsE,EAAKC,MAE/B,CACA,SAASkC,EAAsBD,GAC7B,OAAOjF,EAAMmF,KAAK7C,GAAQ2C,EAAS3C,IAAS,EAC9C,CA2FA,MAmGM8C,EAA2B,IAAI9D,IAAI,CAAC,OAAQ,QC5tBlD,SAAS+D,IACP,MAAyB,qBAAX1K,MAChB,CACA,SAAS2K,EAAYnI,GACnB,OAAIoI,EAAOpI,IACDA,EAAKqI,UAAY,IAAIC,cAKxB,WACT,CACA,SAASC,EAAUvI,GACjB,IAAIwI,EACJ,OAAgB,MAARxI,GAA8D,OAA7CwI,EAAsBxI,EAAKvC,oBAAyB,EAAS+K,EAAoB9K,cAAgBF,MAC5H,CACA,SAAS8J,EAAmBtH,GAC1B,IAAIgG,EACJ,OAA0F,OAAlFA,GAAQoC,EAAOpI,GAAQA,EAAKvC,cAAgBuC,EAAKzD,WAAaiB,OAAOjB,eAAoB,EAASyJ,EAAKyC,eACjH,CACA,SAASL,EAAOvK,GACd,QAAKqK,MAGErK,aAAiB6K,MAAQ7K,aAAiB0K,EAAU1K,GAAO6K,KACpE,CACA,SAAStB,EAAUvJ,GACjB,QAAKqK,MAGErK,aAAiB8K,SAAW9K,aAAiB0K,EAAU1K,GAAO8K,QACvE,CACA,SAASC,EAAc/K,GACrB,QAAKqK,MAGErK,aAAiBgL,aAAehL,aAAiB0K,EAAU1K,GAAOgL,YAC3E,CACA,SAASC,EAAajL,GACpB,SAAKqK,KAAqC,qBAAfa,cAGpBlL,aAAiBkL,YAAclL,aAAiB0K,EAAU1K,GAAOkL,WAC1E,CACA,MAAMC,EAA4C,IAAI7E,IAAI,CAAC,SAAU,aACrE,SAAS8E,EAAkB1L,GACzB,MAAM,SACJuK,EAAQ,UACRoB,EAAS,UACTC,EAAS,QACTC,GACEC,GAAiB9L,GACrB,MAAO,kCAAkC+L,KAAKxB,EAAWqB,EAAYD,KAAeF,EAA6B3E,IAAI+E,EACvH,CACA,MAAMG,EAA6B,IAAIpF,IAAI,CAAC,QAAS,KAAM,OAC3D,SAASqF,EAAejM,GACtB,OAAOgM,EAAclF,IAAI8D,EAAY5K,GACvC,CACA,MAAMkM,EAAoB,CAAC,gBAAiB,UAC5C,SAASC,EAAWnM,GAClB,OAAOkM,EAAkBzB,KAAK2B,IAC5B,IACE,OAAOpM,EAAQqM,QAAQD,EACzB,CAAE,MAAOE,GACP,OAAO,CACT,GAEJ,CACA,MAAMC,EAAsB,CAAC,YAAa,YAAa,QAAS,SAAU,eACpEC,EAAmB,CAAC,YAAa,YAAa,QAAS,SAAU,cAAe,UAChFC,EAAgB,CAAC,QAAS,SAAU,SAAU,WACpD,SAASC,EAAkBC,GACzB,MAAMC,EAASC,IACTC,EAAMjD,EAAU8C,GAAgBb,GAAiBa,GAAgBA,EAIvE,OAAOJ,EAAoB9B,KAAKnK,KAASwM,EAAIxM,IAAwB,SAAfwM,EAAIxM,OAA+BwM,EAAIC,eAAsC,WAAtBD,EAAIC,gBAAwCH,KAAWE,EAAIE,gBAAwC,SAAvBF,EAAIE,iBAAuCJ,KAAWE,EAAIG,QAAwB,SAAfH,EAAIG,QAA8BT,EAAiB/B,KAAKnK,IAAUwM,EAAI7I,YAAc,IAAIiJ,SAAS5M,KAAWmM,EAAchC,KAAKnK,IAAUwM,EAAIK,SAAW,IAAID,SAAS5M,GACna,CAaA,SAASuM,IACP,QAAmB,qBAARO,MAAwBA,IAAIC,WAChCD,IAAIC,SAAS,0BAA2B,OACjD,CACA,MAAMC,EAAwC,IAAI1G,IAAI,CAAC,OAAQ,OAAQ,cACvE,SAAS2G,GAAsB9K,GAC7B,OAAO6K,EAAyBxG,IAAI8D,EAAYnI,GAClD,CACA,SAASqJ,GAAiB9L,GACxB,OAAOgL,EAAUhL,GAAS8L,iBAAiB9L,EAC7C,CACA,SAASwN,GAAcxN,GACrB,OAAI6J,EAAU7J,GACL,CACLyN,WAAYzN,EAAQyN,WACpBC,UAAW1N,EAAQ0N,WAGhB,CACLD,WAAYzN,EAAQ2N,QACpBD,UAAW1N,EAAQ4N,QAEvB,CACA,SAASC,GAAcpL,GACrB,GAA0B,SAAtBmI,EAAYnI,GACd,OAAOA,EAET,MAAMqL,EAENrL,EAAKsL,cAELtL,EAAKuL,YAELzC,EAAa9I,IAASA,EAAKwL,MAE3BlE,EAAmBtH,GACnB,OAAO8I,EAAauC,GAAUA,EAAOG,KAAOH,CAC9C,CACA,SAASI,GAA2BzL,GAClC,MAAMuL,EAAaH,GAAcpL,GACjC,OAAI8K,GAAsBS,GACjBvL,EAAKvC,cAAgBuC,EAAKvC,cAAciO,KAAO1L,EAAK0L,KAEzD9C,EAAc2C,IAAetC,EAAkBsC,GAC1CA,EAEFE,GAA2BF,EACpC,CACA,SAASI,GAAqB3L,EAAMkF,EAAM0G,GACxC,IAAIC,OACS,IAAT3G,IACFA,EAAO,SAEe,IAApB0G,IACFA,GAAkB,GAEpB,MAAME,EAAqBL,GAA2BzL,GAChD+L,EAASD,KAAuE,OAA9CD,EAAuB7L,EAAKvC,oBAAyB,EAASoO,EAAqBH,MACrHM,EAAMzD,EAAUuD,GACtB,GAAIC,EAAQ,CACV,MAAME,EAAeC,GAAgBF,GACrC,OAAO9G,EAAKK,OAAOyG,EAAKA,EAAIG,gBAAkB,GAAIlD,EAAkB6C,GAAsBA,EAAqB,GAAIG,GAAgBL,EAAkBD,GAAqBM,GAAgB,GAC5L,CACA,OAAO/G,EAAKK,OAAOuG,EAAoBH,GAAqBG,EAAoB,GAAIF,GACtF,CACA,SAASM,GAAgBF,GACvB,OAAOA,EAAII,QAAUlP,OAAOmP,eAAeL,EAAII,QAAUJ,EAAIC,aAAe,IAC9E,CCzJA,SAASK,GAAiB/O,GACxB,MAAM8M,EAAMhB,GAAiB9L,GAG7B,IAAIsI,EAAQ0G,WAAWlC,EAAIxE,QAAU,EACjCC,EAASyG,WAAWlC,EAAIvE,SAAW,EACvC,MAAM0G,EAAY5D,EAAcrL,GAC1BkP,EAAcD,EAAYjP,EAAQkP,YAAc5G,EAChD6G,EAAeF,EAAYjP,EAAQmP,aAAe5G,EAClD6G,EAAiB3O,EAAM6H,KAAW4G,GAAezO,EAAM8H,KAAY4G,EAKzE,OAJIC,IACF9G,EAAQ4G,EACR3G,EAAS4G,GAEJ,CACL7G,QACAC,SACA8G,EAAGD,EAEP,CAEA,SAASE,GAActP,GACrB,OAAQ6J,EAAU7J,GAAoCA,EAAzBA,EAAQ8J,cACvC,CAEA,SAASK,GAASnK,GAChB,MAAMuP,EAAaD,GAActP,GACjC,IAAKqL,EAAckE,GACjB,OAAO7J,EAAa,GAEtB,MAAM2C,EAAOkH,EAAWC,yBAClB,MACJlH,EAAK,OACLC,EAAM,EACN8G,GACEN,GAAiBQ,GACrB,IAAIzN,GAAKuN,EAAI5O,EAAM4H,EAAKC,OAASD,EAAKC,OAASA,EAC3CvG,GAAKsN,EAAI5O,EAAM4H,EAAKE,QAAUF,EAAKE,QAAUA,EAUjD,OANKzG,GAAM2N,OAAOC,SAAS5N,KACzBA,EAAI,GAEDC,GAAM0N,OAAOC,SAAS3N,KACzBA,EAAI,GAEC,CACLD,IACAC,IAEJ,CAEA,MAAM4N,GAAyBjK,EAAa,GAC5C,SAASkK,GAAiB5P,GACxB,MAAMyO,EAAMzD,EAAUhL,GACtB,OAAK6M,KAAe4B,EAAIG,eAGjB,CACL9M,EAAG2M,EAAIG,eAAeiB,WACtB9N,EAAG0M,EAAIG,eAAekB,WAJfH,EAMX,CAWA,SAASH,GAAsBxP,EAAS+P,EAAcC,EAAiBhG,QAChD,IAAjB+F,IACFA,GAAe,QAEO,IAApBC,IACFA,GAAkB,GAEpB,MAAMC,EAAajQ,EAAQwP,wBACrBD,EAAaD,GAActP,GACjC,IAAIkQ,EAAQxK,EAAa,GACrBqK,IACE/F,EACEH,EAAUG,KACZkG,EAAQ/F,GAASH,IAGnBkG,EAAQ/F,GAASnK,IAGrB,MAAMmQ,EA7BR,SAAgCnQ,EAASoQ,EAASC,GAIhD,YAHgB,IAAZD,IACFA,GAAU,MAEPC,GAAwBD,GAAWC,IAAyBrF,EAAUhL,KAGpEoQ,CACT,CAqBwBE,CAAuBf,EAAYS,EAAiBhG,GAAgB4F,GAAiBL,GAAc7J,EAAa,GACtI,IAAI5D,GAAKmO,EAAWlM,KAAOoM,EAAcrO,GAAKoO,EAAMpO,EAChDC,GAAKkO,EAAWjM,IAAMmM,EAAcpO,GAAKmO,EAAMnO,EAC/CuG,EAAQ2H,EAAW3H,MAAQ4H,EAAMpO,EACjCyG,EAAS0H,EAAW1H,OAAS2H,EAAMnO,EACvC,GAAIwN,EAAY,CACd,MAAMd,EAAMzD,EAAUuE,GAChBgB,EAAYvG,GAAgBH,EAAUG,GAAgBgB,EAAUhB,GAAgBA,EACtF,IAAIwG,EAAa/B,EACbgC,EAAgB9B,GAAgB6B,GACpC,KAAOC,GAAiBzG,GAAgBuG,IAAcC,GAAY,CAChE,MAAME,EAAcvG,GAASsG,GACvBE,EAAaF,EAAcjB,wBAC3B1C,EAAMhB,GAAiB2E,GACvB1M,EAAO4M,EAAW5M,MAAQ0M,EAAcG,WAAa5B,WAAWlC,EAAI+D,cAAgBH,EAAY5O,EAChGkC,EAAM2M,EAAW3M,KAAOyM,EAAcK,UAAY9B,WAAWlC,EAAIiE,aAAeL,EAAY3O,EAClGD,GAAK4O,EAAY5O,EACjBC,GAAK2O,EAAY3O,EACjBuG,GAASoI,EAAY5O,EACrByG,GAAUmI,EAAY3O,EACtBD,GAAKiC,EACLhC,GAAKiC,EACLwM,EAAaxF,EAAUyF,GACvBA,EAAgB9B,GAAgB6B,EAClC,CACF,CACA,OAAOpI,EAAiB,CACtBE,QACAC,SACAzG,IACAC,KAEJ,CAIA,SAASiP,GAAoBhR,EAASqI,GACpC,MAAM4I,EAAazD,GAAcxN,GAASyN,WAC1C,OAAKpF,EAGEA,EAAKtE,KAAOkN,EAFVzB,GAAsBzF,EAAmB/J,IAAU+D,KAAOkN,CAGrE,CAEA,SAASC,GAAchG,EAAiBiG,EAAQC,QACrB,IAArBA,IACFA,GAAmB,GAErB,MAAMC,EAAWnG,EAAgBsE,wBAKjC,MAAO,CACL1N,EALQuP,EAAStN,KAAOoN,EAAO1D,YAAc2D,EAAmB,EAElEJ,GAAoB9F,EAAiBmG,IAInCtP,EAHQsP,EAASrN,IAAMmN,EAAOzD,UAKlC,CA4FA,MAAM4D,GAA+B,IAAI1K,IAAI,CAAC,WAAY,UAkB1D,SAAS2K,GAAkCvR,EAASwR,EAAkBvQ,GACpE,IAAIoH,EACJ,GAAyB,aAArBmJ,EACFnJ,EA9CJ,SAAyBrI,EAASiB,GAChC,MAAMwN,EAAMzD,EAAUhL,GAChByR,EAAO1H,EAAmB/J,GAC1B4O,EAAiBH,EAAIG,eAC3B,IAAItG,EAAQmJ,EAAKC,YACbnJ,EAASkJ,EAAKE,aACd7P,EAAI,EACJC,EAAI,EACR,GAAI6M,EAAgB,CAClBtG,EAAQsG,EAAetG,MACvBC,EAASqG,EAAerG,OACxB,MAAMqJ,EAAsB/E,MACvB+E,GAAuBA,GAAoC,UAAb3Q,KACjDa,EAAI8M,EAAeiB,WACnB9N,EAAI6M,EAAekB,UAEvB,CACA,MAAO,CACLxH,QACAC,SACAzG,IACAC,IAEJ,CAuBW8P,CAAgB7R,EAASiB,QAC3B,GAAyB,aAArBuQ,EACTnJ,EAnEJ,SAAyBrI,GACvB,MAAMyR,EAAO1H,EAAmB/J,GAC1BmR,EAAS3D,GAAcxN,GACvBmO,EAAOnO,EAAQE,cAAciO,KAC7B7F,EAAQ9C,EAAIiM,EAAKK,YAAaL,EAAKC,YAAavD,EAAK2D,YAAa3D,EAAKuD,aACvEnJ,EAAS/C,EAAIiM,EAAKM,aAAcN,EAAKE,aAAcxD,EAAK4D,aAAc5D,EAAKwD,cACjF,IAAI7P,GAAKqP,EAAO1D,WAAauD,GAAoBhR,GACjD,MAAM+B,GAAKoP,EAAOzD,UAIlB,MAHyC,QAArC5B,GAAiBqC,GAAM1G,YACzB3F,GAAK0D,EAAIiM,EAAKC,YAAavD,EAAKuD,aAAepJ,GAE1C,CACLA,QACAC,SACAzG,IACAC,IAEJ,CAkDWiQ,CAAgBjI,EAAmB/J,SACrC,GAAI6J,EAAU2H,GACnBnJ,EAvBJ,SAAoCrI,EAASiB,GAC3C,MAAMgP,EAAaT,GAAsBxP,GAAS,EAAmB,UAAbiB,GAClD+C,EAAMiM,EAAWjM,IAAMhE,EAAQ8Q,UAC/B/M,EAAOkM,EAAWlM,KAAO/D,EAAQ4Q,WACjCV,EAAQ7E,EAAcrL,GAAWmK,GAASnK,GAAW0F,EAAa,GAKxE,MAAO,CACL4C,MALYtI,EAAQ0R,YAAcxB,EAAMpO,EAMxCyG,OALavI,EAAQ2R,aAAezB,EAAMnO,EAM1CD,EALQiC,EAAOmM,EAAMpO,EAMrBC,EALQiC,EAAMkM,EAAMnO,EAOxB,CAQWkQ,CAA2BT,EAAkBvQ,OAC/C,CACL,MAAMkP,EAAgBP,GAAiB5P,GACvCqI,EAAO,CACLvG,EAAG0P,EAAiB1P,EAAIqO,EAAcrO,EACtCC,EAAGyP,EAAiBzP,EAAIoO,EAAcpO,EACtCuG,MAAOkJ,EAAiBlJ,MACxBC,OAAQiJ,EAAiBjJ,OAE7B,CACA,OAAOH,EAAiBC,EAC1B,CACA,SAAS6J,GAAyBlS,EAASmS,GACzC,MAAMnE,EAAaH,GAAc7N,GACjC,QAAIgO,IAAemE,IAAatI,EAAUmE,IAAeT,GAAsBS,MAG9B,UAA1ClC,GAAiBkC,GAAYlK,UAAwBoO,GAAyBlE,EAAYmE,GACnG,CA2EA,SAASC,GAA8BpS,EAASgK,EAAc/I,GAC5D,MAAMoR,EAA0BhH,EAAcrB,GACxCkB,EAAkBnB,EAAmBC,GACrCoG,EAAuB,UAAbnP,EACVoH,EAAOmH,GAAsBxP,GAAS,EAAMoQ,EAASpG,GAC3D,IAAImH,EAAS,CACX1D,WAAY,EACZC,UAAW,GAEb,MAAM4E,EAAU5M,EAAa,GAI7B,SAAS6M,IACPD,EAAQxQ,EAAIkP,GAAoB9F,EAClC,CACA,GAAImH,IAA4BA,IAA4BjC,EAI1D,IAHkC,SAA9BxF,EAAYZ,IAA4B0B,EAAkBR,MAC5DiG,EAAS3D,GAAcxD,IAErBqI,EAAyB,CAC3B,MAAMG,EAAahD,GAAsBxF,GAAc,EAAMoG,EAASpG,GACtEsI,EAAQxQ,EAAI0Q,EAAW1Q,EAAIkI,EAAa4G,WACxC0B,EAAQvQ,EAAIyQ,EAAWzQ,EAAIiI,EAAa8G,SAC1C,MAAW5F,GACTqH,IAGAnC,IAAYiC,GAA2BnH,GACzCqH,IAEF,MAAME,GAAavH,GAAoBmH,GAA4BjC,EAAmD1K,EAAa,GAAtDwL,GAAchG,EAAiBiG,GAG5G,MAAO,CACLrP,EAHQuG,EAAKtE,KAAOoN,EAAO1D,WAAa6E,EAAQxQ,EAAI2Q,EAAW3Q,EAI/DC,EAHQsG,EAAKrE,IAAMmN,EAAOzD,UAAY4E,EAAQvQ,EAAI0Q,EAAW1Q,EAI7DuG,MAAOD,EAAKC,MACZC,OAAQF,EAAKE,OAEjB,CAEA,SAASmK,GAAmB1S,GAC1B,MAA8C,WAAvC8L,GAAiB9L,GAAS8D,QACnC,CAEA,SAAS6O,GAAoB3S,EAAS4S,GACpC,IAAKvH,EAAcrL,IAAmD,UAAvC8L,GAAiB9L,GAAS8D,SACvD,OAAO,KAET,GAAI8O,EACF,OAAOA,EAAS5S,GAElB,IAAI6S,EAAkB7S,EAAQgK,aAS9B,OAHID,EAAmB/J,KAAa6S,IAClCA,EAAkBA,EAAgB3S,cAAciO,MAE3C0E,CACT,CAIA,SAAS5I,GAAgBjK,EAAS4S,GAChC,MAAMnE,EAAMzD,EAAUhL,GACtB,GAAImM,EAAWnM,GACb,OAAOyO,EAET,IAAKpD,EAAcrL,GAAU,CAC3B,IAAI8S,EAAkBjF,GAAc7N,GACpC,KAAO8S,IAAoBvF,GAAsBuF,IAAkB,CACjE,GAAIjJ,EAAUiJ,KAAqBJ,GAAmBI,GACpD,OAAOA,EAETA,EAAkBjF,GAAciF,EAClC,CACA,OAAOrE,CACT,CACA,IAAIzE,EAAe2I,GAAoB3S,EAAS4S,GAChD,KAAO5I,GAAgBiC,EAAejC,IAAiB0I,GAAmB1I,IACxEA,EAAe2I,GAAoB3I,EAAc4I,GAEnD,OAAI5I,GAAgBuD,GAAsBvD,IAAiB0I,GAAmB1I,KAAkB0C,EAAkB1C,GACzGyE,EAEFzE,GDvXT,SAA4BhK,GAC1B,IAAI+S,EAAclF,GAAc7N,GAChC,KAAOqL,EAAc0H,KAAiBxF,GAAsBwF,IAAc,CACxE,GAAIrG,EAAkBqG,GACpB,OAAOA,EACF,GAAI5G,EAAW4G,GACpB,OAAO,KAETA,EAAclF,GAAckF,EAC9B,CACA,OAAO,IACT,CC4WyBC,CAAmBhT,IAAYyO,CACxD,CAqBA,MAAMtN,GAAW,CACfkJ,sDAhUF,SAA+D5B,GAC7D,IAAI,SACFrH,EAAQ,KACRiH,EAAI,aACJ2B,EAAY,SACZ/I,GACEwH,EACJ,MAAM2H,EAAuB,UAAbnP,EACViK,EAAkBnB,EAAmBC,GACrCiJ,IAAW7R,GAAW+K,EAAW/K,EAASG,UAChD,GAAIyI,IAAiBkB,GAAmB+H,GAAY7C,EAClD,OAAO/H,EAET,IAAI8I,EAAS,CACX1D,WAAY,EACZC,UAAW,GAETwC,EAAQxK,EAAa,GACzB,MAAM4M,EAAU5M,EAAa,GACvB2M,EAA0BhH,EAAcrB,GAC9C,IAAIqI,IAA4BA,IAA4BjC,MACxB,SAA9BxF,EAAYZ,IAA4B0B,EAAkBR,MAC5DiG,EAAS3D,GAAcxD,IAErBqB,EAAcrB,IAAe,CAC/B,MAAMwI,EAAahD,GAAsBxF,GACzCkG,EAAQ/F,GAASH,GACjBsI,EAAQxQ,EAAI0Q,EAAW1Q,EAAIkI,EAAa4G,WACxC0B,EAAQvQ,EAAIyQ,EAAWzQ,EAAIiI,EAAa8G,SAC1C,CAEF,MAAM2B,GAAavH,GAAoBmH,GAA4BjC,EAAyD1K,EAAa,GAA5DwL,GAAchG,EAAiBiG,GAAQ,GACpH,MAAO,CACL7I,MAAOD,EAAKC,MAAQ4H,EAAMpO,EAC1ByG,OAAQF,EAAKE,OAAS2H,EAAMnO,EAC5BD,EAAGuG,EAAKvG,EAAIoO,EAAMpO,EAAIqP,EAAO1D,WAAayC,EAAMpO,EAAIwQ,EAAQxQ,EAAI2Q,EAAW3Q,EAC3EC,EAAGsG,EAAKtG,EAAImO,EAAMnO,EAAIoP,EAAOzD,UAAYwC,EAAMnO,EAAIuQ,EAAQvQ,EAAI0Q,EAAW1Q,EAE9E,EA2REgI,mBAAkB,EAClBH,gBAvJF,SAAyBnB,GACvB,IAAI,QACFzI,EAAO,SACPsJ,EAAQ,aACRC,EAAY,SACZtI,GACEwH,EACJ,MACMyK,EAAoB,IADoB,sBAAb5J,EAAmC6C,EAAWnM,GAAW,GAxC5F,SAAqCA,EAASmT,GAC5C,MAAMC,EAAeD,EAAME,IAAIrT,GAC/B,GAAIoT,EACF,OAAOA,EAET,IAAItF,EAASM,GAAqBpO,EAAS,IAAI,GAAOiN,OAAOqG,GAAMzJ,EAAUyJ,IAA2B,SAApB1I,EAAY0I,IAC5FC,EAAsC,KAC1C,MAAMC,EAAwD,UAAvC1H,GAAiB9L,GAAS8D,SACjD,IAAIiP,EAAcS,EAAiB3F,GAAc7N,GAAWA,EAG5D,KAAO6J,EAAUkJ,KAAiBxF,GAAsBwF,IAAc,CACpE,MAAMU,EAAgB3H,GAAiBiH,GACjCW,EAA0BhH,EAAkBqG,GAC7CW,GAAsD,UAA3BD,EAAc3P,WAC5CyP,EAAsC,OAEVC,GAAkBE,IAA4BH,GAAuCG,GAAsD,WAA3BD,EAAc3P,UAA2ByP,GAAuCjC,GAAgBxK,IAAIyM,EAAoCzP,WAAa4H,EAAkBqH,KAAiBW,GAA2BxB,GAAyBlS,EAAS+S,IAGjYjF,EAASA,EAAOb,OAAO0G,GAAYA,IAAaZ,GAGhDQ,EAAsCE,EAExCV,EAAclF,GAAckF,EAC9B,CAEA,OADAI,EAAMS,IAAI5T,EAAS8N,GACZA,CACT,CAWiG+F,CAA4B7T,EAAS8T,KAAKC,IAAM,GAAG/L,OAAOsB,GACjGC,GAClDyK,EAAwBd,EAAkB,GAC1Ce,EAAef,EAAkBgB,OAAO,CAACC,EAAS3C,KACtD,MAAMnJ,EAAOkJ,GAAkCvR,EAASwR,EAAkBvQ,GAK1E,OAJAkT,EAAQnQ,IAAMwB,EAAI6C,EAAKrE,IAAKmQ,EAAQnQ,KACpCmQ,EAAQtO,MAAQN,EAAI8C,EAAKxC,MAAOsO,EAAQtO,OACxCsO,EAAQrO,OAASP,EAAI8C,EAAKvC,OAAQqO,EAAQrO,QAC1CqO,EAAQpQ,KAAOyB,EAAI6C,EAAKtE,KAAMoQ,EAAQpQ,MAC/BoQ,GACN5C,GAAkCvR,EAASgU,EAAuB/S,IACrE,MAAO,CACLqH,MAAO2L,EAAapO,MAAQoO,EAAalQ,KACzCwE,OAAQ0L,EAAanO,OAASmO,EAAajQ,IAC3ClC,EAAGmS,EAAalQ,KAChBhC,EAAGkS,EAAajQ,IAEpB,EAgIEiG,mBACAmK,gBAxBsBlL,eAAgBtH,GACtC,MAAMyS,EAAoBP,KAAK7J,iBAAmBA,GAC5CqK,EAAkBR,KAAKS,cACvBC,QAA2BF,EAAgB1S,EAAKL,UACtD,MAAO,CACLF,UAAW+Q,GAA8BxQ,EAAKP,gBAAiBgT,EAAkBzS,EAAKL,UAAWK,EAAKX,UACtGM,SAAU,CACRO,EAAG,EACHC,EAAG,EACHuG,MAAOkM,EAAmBlM,MAC1BC,OAAQiM,EAAmBjM,QAGjC,EAYEkM,eA7RF,SAAwBzU,GACtB,OAAOP,MAAMiV,KAAK1U,EAAQyU,iBAC5B,EA4REF,cAjIF,SAAuBvU,GACrB,MAAM,MACJsI,EAAK,OACLC,GACEwG,GAAiB/O,GACrB,MAAO,CACLsI,QACAC,SAEJ,EAyHE4B,YACAN,UAAS,EACT8K,MAdF,SAAe3U,GACb,MAA+C,QAAxC8L,GAAiB9L,GAASyH,SACnC,GAeA,SAASmN,GAAczV,EAAGC,GACxB,OAAOD,EAAE2C,IAAM1C,EAAE0C,GAAK3C,EAAE4C,IAAM3C,EAAE2C,GAAK5C,EAAEmJ,QAAUlJ,EAAEkJ,OAASnJ,EAAEoJ,SAAWnJ,EAAEmJ,MAC7E,CAkGA,SAASsM,GAAWxT,EAAWE,EAAU6B,EAAQrC,QAC/B,IAAZA,IACFA,EAAU,CAAC,GAEb,MAAM,eACJ+T,GAAiB,EAAI,eACrBC,GAAiB,EAAI,cACrBC,EAA0C,oBAAnBC,eAA6B,YACpDC,EAA8C,oBAAzBC,qBAAmC,eACxDC,GAAiB,GACfrU,EACE8B,EAAcyM,GAAcjO,GAC5BgU,EAAYP,GAAkBC,EAAiB,IAAKlS,EAAcuL,GAAqBvL,GAAe,MAAQuL,GAAqB7M,IAAa,GACtJ8T,EAAUC,QAAQ3B,IAChBmB,GAAkBnB,EAAS4B,iBAAiB,SAAUnS,EAAQ,CAC5DoS,SAAS,IAEXT,GAAkBpB,EAAS4B,iBAAiB,SAAUnS,KAExD,MAAMqS,EAAY5S,GAAeqS,EAlHnC,SAAqBlV,EAAS0V,GAC5B,IACIC,EADAC,EAAK,KAET,MAAMC,EAAO9L,EAAmB/J,GAChC,SAAS8V,IACP,IAAIC,EACJC,aAAaL,GACC,OAAbI,EAAMH,IAAeG,EAAIE,aAC1BL,EAAK,IACP,CA2EA,OA1EA,SAASM,EAAQC,EAAMC,QACR,IAATD,IACFA,GAAO,QAES,IAAdC,IACFA,EAAY,GAEdN,IACA,MAAMO,EAA2BrW,EAAQwP,yBACnC,KACJzL,EAAI,IACJC,EAAG,MACHsE,EAAK,OACLC,GACE8N,EAIJ,GAHKF,GACHT,KAEGpN,IAAUC,EACb,OAEF,MAKMxH,EAAU,CACduV,YANe7Q,EAAMzB,GAIQ,OAHZyB,EAAMoQ,EAAKnE,aAAe3N,EAAOuE,IAGC,OAFjC7C,EAAMoQ,EAAKlE,cAAgB3N,EAAMuE,IAEuB,OAD1D9C,EAAM1B,GACyE,KAG/FqS,UAAW5Q,EAAI,EAAGD,EAAI,EAAG6Q,KAAe,GAE1C,IAAIG,GAAgB,EACpB,SAASC,EAAcC,GACrB,MAAMC,EAAQD,EAAQ,GAAGE,kBACzB,GAAID,IAAUN,EAAW,CACvB,IAAKG,EACH,OAAOL,IAEJQ,EAOHR,GAAQ,EAAOQ,GAJff,EAAYiB,WAAW,KACrBV,GAAQ,EAAO,OACd,IAIP,CACc,IAAVQ,GAAgB9B,GAAcyB,EAA0BrW,EAAQwP,0BAQlE0G,IAEFK,GAAgB,CAClB,CAIA,IACEX,EAAK,IAAIT,qBAAqBqB,EAAe,IACxCzV,EAEH8U,KAAMA,EAAK3V,eAEf,CAAE,MAAOoM,GACPsJ,EAAK,IAAIT,qBAAqBqB,EAAezV,EAC/C,CACA6U,EAAGiB,QAAQ7W,EACb,CACAkW,EAAQ,GACDJ,CACT,CA6BiDgB,CAAYjU,EAAaO,GAAU,KAClF,IAsBI2T,EAtBAC,GAAkB,EAClBC,EAAiB,KACjBjC,IACFiC,EAAiB,IAAIhC,eAAexM,IAClC,IAAKyO,GAAczO,EACfyO,GAAcA,EAAWC,SAAWtU,GAAeoU,IAGrDA,EAAeG,UAAU7V,GACzB8V,qBAAqBL,GACrBA,EAAiBM,sBAAsB,KACrC,IAAIC,EACkC,OAArCA,EAAkBN,IAA2BM,EAAgBV,QAAQtV,MAG1E6B,MAEEP,IAAgBuS,GAClB6B,EAAeJ,QAAQhU,GAEzBoU,EAAeJ,QAAQtV,IAGzB,IAAIiW,EAAcpC,EAAiB5F,GAAsBnO,GAAa,KAatE,OAZI+T,GAGJ,SAASqC,IACP,MAAMC,EAAclI,GAAsBnO,GACtCmW,IAAgB5C,GAAc4C,EAAaE,IAC7CtU,IAEFoU,EAAcE,EACdX,EAAUO,sBAAsBG,EAClC,CATEA,GAUFrU,IACO,KACL,IAAIuU,EACJtC,EAAUC,QAAQ3B,IAChBmB,GAAkBnB,EAASiE,oBAAoB,SAAUxU,GACzD2R,GAAkBpB,EAASiE,oBAAoB,SAAUxU,KAE9C,MAAbqS,GAAqBA,IACkB,OAAtCkC,EAAmBV,IAA2BU,EAAiB1B,aAChEgB,EAAiB,KACb7B,GACFiC,qBAAqBN,GAG3B,CAUA,MASMvS,GF2GS,SAAUzD,GAIvB,YAHgB,IAAZA,IACFA,EAAU,GAEL,CACLoD,KAAM,SACNpD,UACA,QAAMqD,CAAGC,GACP,IAAIwT,EAAuBC,EAC3B,MAAM,EACJhW,EAAC,EACDC,EAAC,UACDf,EAAS,eACTgB,GACEqC,EACE0T,QA9DZ7O,eAAoC7E,EAAOtD,GACzC,MAAM,UACJC,EAAS,SACTG,EAAQ,SACRC,GACEiD,EACEqD,QAA+B,MAAlBvG,EAASwT,WAAgB,EAASxT,EAASwT,MAAMvT,EAASG,WACvEqG,EAAOvB,EAAQrF,GACfkG,EAAYX,EAAavF,GACzB6H,EAAwC,MAA3BhC,EAAY7F,GACzBgX,EAAgBtN,EAAY5D,IAAIc,IAAS,EAAI,EAC7CqQ,EAAiBvQ,GAAOmB,GAAc,EAAI,EAC1CqP,EAAW/R,EAASpF,EAASsD,GAGnC,IAAI,SACF8T,EAAQ,UACRC,EAAS,cACTzP,GACsB,kBAAbuP,EAAwB,CACjCC,SAAUD,EACVE,UAAW,EACXzP,cAAe,MACb,CACFwP,SAAUD,EAASC,UAAY,EAC/BC,UAAWF,EAASE,WAAa,EACjCzP,cAAeuP,EAASvP,eAK1B,OAHIzB,GAAsC,kBAAlByB,IACtByP,EAA0B,QAAdlR,GAAuC,EAAjByB,EAAqBA,GAElDE,EAAa,CAClB/G,EAAGsW,EAAYH,EACflW,EAAGoW,EAAWH,GACZ,CACFlW,EAAGqW,EAAWH,EACdjW,EAAGqW,EAAYH,EAEnB,CAwB+BI,CAAqBhU,EAAOtD,GAIrD,OAAIC,KAAkE,OAAlD6W,EAAwB7V,EAAewC,aAAkB,EAASqT,EAAsB7W,YAAgE,OAAjD8W,EAAwB9V,EAAeqD,QAAkByS,EAAsBQ,gBACjM,CAAC,EAEH,CACLxW,EAAGA,EAAIiW,EAAWjW,EAClBC,EAAGA,EAAIgW,EAAWhW,EAClBH,KAAM,IACDmW,EACH/W,aAGN,EAEJ,EE5HM2D,GFmIQ,SAAU5D,GAItB,YAHgB,IAAZA,IACFA,EAAU,CAAC,GAEN,CACLoD,KAAM,QACNpD,UACA,QAAMqD,CAAGC,GACP,MAAM,EACJvC,EAAC,EACDC,EAAC,UACDf,GACEqD,GAEF8T,SAAUI,GAAgB,EAC1BH,UAAWI,GAAiB,EAAK,QACjCC,EAAU,CACRrU,GAAIqE,IACF,IAAI,EACF3G,EAAC,EACDC,GACE0G,EACJ,MAAO,CACL3G,IACAC,UAIH2W,GACDvS,EAASpF,EAASsD,GAChB4E,EAAS,CACbnH,IACAC,KAEIwI,QAAiBpB,EAAe9E,EAAOqU,GACvCN,EAAYvR,EAAYR,EAAQrF,IAChCmX,EAAW3R,EAAgB4R,GACjC,IAAIO,EAAgB1P,EAAOkP,GACvBS,EAAiB3P,EAAOmP,GAC5B,GAAIG,EAAe,CACjB,MACMM,EAAuB,MAAbV,EAAmB,SAAW,QAG9CQ,EAAgBzS,EAFJyS,EAAgBpO,EAFC,MAAb4N,EAAmB,MAAQ,QAIhBQ,EADfA,EAAgBpO,EAASsO,GAEvC,CACA,GAAIL,EAAgB,CAClB,MACMK,EAAwB,MAAdT,EAAoB,SAAW,QAG/CQ,EAAiB1S,EAFL0S,EAAiBrO,EAFC,MAAd6N,EAAoB,MAAQ,QAIhBQ,EADhBA,EAAiBrO,EAASsO,GAExC,CACA,MAAMC,EAAgBL,EAAQrU,GAAG,IAC5BC,EACH,CAAC8T,GAAWQ,EACZ,CAACP,GAAYQ,IAEf,MAAO,IACFE,EACHlX,KAAM,CACJE,EAAGgX,EAAchX,EAAIA,EACrBC,EAAG+W,EAAc/W,EAAIA,EACrBgX,QAAS,CACP,CAACZ,GAAWI,EACZ,CAACH,GAAYI,IAIrB,EAEJ,EElMMzT,GFtSO,SAAUhE,GAIrB,YAHgB,IAAZA,IACFA,EAAU,CAAC,GAEN,CACLoD,KAAM,OACNpD,UACA,QAAMqD,CAAGC,GACP,IAAIyT,EAAuBkB,EAC3B,MAAM,UACJhY,EAAS,eACTgB,EAAc,MACdqH,EAAK,iBACL4P,EAAgB,SAChB9X,EAAQ,SACRC,GACEiD,GAEF8T,SAAUI,GAAgB,EAC1BH,UAAWI,GAAiB,EAC5BU,mBAAoBC,EAA2B,iBAC/CC,EAAmB,UAAS,0BAC5BC,EAA4B,OAAM,cAClC7R,GAAgB,KACbkR,GACDvS,EAASpF,EAASsD,GAMtB,GAAsD,OAAjDyT,EAAwB9V,EAAeqD,QAAkByS,EAAsBQ,gBAClF,MAAO,CAAC,EAEV,MAAM1Q,EAAOvB,EAAQrF,GACfsY,EAAkBzS,EAAYoS,GAC9BM,EAAkBlT,EAAQ4S,KAAsBA,EAChDvR,QAA+B,MAAlBvG,EAASwT,WAAgB,EAASxT,EAASwT,MAAMvT,EAASG,WACvE2X,EAAqBC,IAAgCI,IAAoB/R,EAAgB,CAACS,EAAqBgR,ID5X3H,SAA+BjY,GAC7B,MAAMwY,EAAoBvR,EAAqBjH,GAC/C,MAAO,CAACgG,EAA8BhG,GAAYwY,EAAmBxS,EAA8BwS,GACrG,CCyXgJC,CAAsBR,IAC1JS,EAA6D,SAA9BL,GAChCF,GAA+BO,GAClCR,EAAmBS,QAAQpS,EAA0B0R,EAAkBzR,EAAe6R,EAA2B3R,IAEnH,MAAMkS,EAAa,CAACX,KAAqBC,GACnC3O,QAAiBpB,EAAe9E,EAAOqU,GACvCmB,EAAY,GAClB,IAAIC,GAAiE,OAA/Cd,EAAuBhX,EAAe+C,WAAgB,EAASiU,EAAqBa,YAAc,GAIxH,GAHItB,GACFsB,EAAUF,KAAKpP,EAAS3C,IAEtB4Q,EAAgB,CAClB,MAAMlT,EDtZd,SAA2BtE,EAAWqI,EAAO3B,QAC/B,IAARA,IACFA,GAAM,GAER,MAAMR,EAAYX,EAAavF,GACzB2H,EAAgB5B,EAAiB/F,GACjC1B,EAASoH,EAAciC,GAC7B,IAAIoR,EAAsC,MAAlBpR,EAAwBzB,KAAeQ,EAAM,MAAQ,SAAW,QAAU,OAAuB,UAAdR,EAAwB,SAAW,MAI9I,OAHImC,EAAMhI,UAAU/B,GAAU+J,EAAM9H,SAASjC,KAC3Cya,EAAoB9R,EAAqB8R,IAEpC,CAACA,EAAmB9R,EAAqB8R,GAClD,CC0YsBC,CAAkBhZ,EAAWqI,EAAO3B,GAClDmS,EAAUF,KAAKpP,EAASjF,EAAM,IAAKiF,EAASjF,EAAM,IACpD,CAOA,GANAwU,EAAgB,IAAIA,EAAe,CACjC9Y,YACA6Y,eAIGA,EAAUI,MAAMrS,GAAQA,GAAQ,GAAI,CACvC,IAAIsS,EAAuBC,EAC3B,MAAMC,IAA+D,OAAhDF,EAAwBlY,EAAe+C,WAAgB,EAASmV,EAAsBnb,QAAU,GAAK,EACpHsb,EAAgBT,EAAWQ,GACjC,GAAIC,EAAe,CAEjB,KADmD,cAAnB7B,GAAiCc,IAAoBzS,EAAYwT,KAIjGP,EAAcG,MAAMK,GAAKA,EAAET,UAAU,GAAK,GAAKhT,EAAYyT,EAAEtZ,aAAesY,GAE1E,MAAO,CACL1X,KAAM,CACJ7C,MAAOqb,EACPP,UAAWC,GAEbS,MAAO,CACLvZ,UAAWqZ,GAInB,CAIA,IAAIG,EAAgJ,OAA9HL,EAAwBL,EAAc7M,OAAOqN,GAAKA,EAAET,UAAU,IAAM,GAAGY,KAAK,CAACtb,EAAGC,IAAMD,EAAE0a,UAAU,GAAKza,EAAEya,UAAU,IAAI,SAAc,EAASM,EAAsBnZ,UAG1L,IAAKwZ,EACH,OAAQpB,GACN,IAAK,UACH,CACE,IAAIsB,EACJ,MAAM1Z,EASmJ,OATtI0Z,EAAyBZ,EAAc7M,OAAOqN,IAC/D,GAAIZ,EAA8B,CAChC,MAAMiB,EAAkB9T,EAAYyT,EAAEtZ,WACtC,OAAO2Z,IAAoBrB,GAGP,MAApBqB,CACF,CACA,OAAO,IACN5S,IAAIuS,GAAK,CAACA,EAAEtZ,UAAWsZ,EAAET,UAAU5M,OAAO1C,GAAYA,EAAW,GAAG2J,OAAO,CAAC0G,EAAKrQ,IAAaqQ,EAAMrQ,EAAU,KAAKkQ,KAAK,CAACtb,EAAGC,IAAMD,EAAE,GAAKC,EAAE,IAAI,SAAc,EAASsb,EAAuB,GAC5L1Z,IACFwZ,EAAiBxZ,GAEnB,KACF,CACF,IAAK,mBACHwZ,EAAiBvB,EAIvB,GAAIjY,IAAcwZ,EAChB,MAAO,CACLD,MAAO,CACLvZ,UAAWwZ,GAInB,CACA,MAAO,CAAC,CACV,EAEJ,EEkLMvV,GFsQO,SAAUlE,GAIrB,YAHgB,IAAZA,IACFA,EAAU,CAAC,GAEN,CACLoD,KAAM,OACNpD,UACA,QAAMqD,CAAGC,GACP,IAAIwW,EAAuBC,EAC3B,MAAM,UACJ9Z,EAAS,MACTqI,EAAK,SACLlI,EAAQ,SACRC,GACEiD,GACE,MACJ0W,EAAQA,UACLrC,GACDvS,EAASpF,EAASsD,GAChBkG,QAAiBpB,EAAe9E,EAAOqU,GACvC9Q,EAAOvB,EAAQrF,GACfkG,EAAYX,EAAavF,GACzBga,EAAqC,MAA3BnU,EAAY7F,IACtB,MACJsH,EAAK,OACLC,GACEc,EAAM9H,SACV,IAAI0Z,EACAC,EACS,QAATtT,GAA2B,WAATA,GACpBqT,EAAarT,EACbsT,EAAYhU,WAAyC,MAAlB/F,EAASwT,WAAgB,EAASxT,EAASwT,MAAMvT,EAASG,WAAc,QAAU,OAAS,OAAS,UAEvI2Z,EAAYtT,EACZqT,EAA2B,QAAd/T,EAAsB,MAAQ,UAE7C,MAAMiU,EAAwB5S,EAASgC,EAASvG,IAAMuG,EAASzE,OACzDsV,EAAuB9S,EAAQiC,EAASxG,KAAOwG,EAAS1E,MACxDwV,EAA0B9V,EAAIgD,EAASgC,EAAS0Q,GAAaE,GAC7DG,EAAyB/V,EAAI+C,EAAQiC,EAAS2Q,GAAYE,GAC1DG,GAAWlX,EAAMrC,eAAe2C,MACtC,IAAI6W,EAAkBH,EAClBI,EAAiBH,EAOrB,GAN4D,OAAvDT,EAAwBxW,EAAMrC,eAAe2C,QAAkBkW,EAAsB9B,QAAQjX,IAChG2Z,EAAiBL,GAE0C,OAAxDN,EAAyBzW,EAAMrC,eAAe2C,QAAkBmW,EAAuB/B,QAAQhX,IAClGyZ,EAAkBL,GAEhBI,IAAYrU,EAAW,CACzB,MAAMwU,EAAOlW,EAAI+E,EAASxG,KAAM,GAC1B4X,EAAOnW,EAAI+E,EAAS1E,MAAO,GAC3B+V,EAAOpW,EAAI+E,EAASvG,IAAK,GACzB6X,EAAOrW,EAAI+E,EAASzE,OAAQ,GAC9BkV,EACFS,EAAiBnT,EAAQ,GAAc,IAAToT,GAAuB,IAATC,EAAaD,EAAOC,EAAOnW,EAAI+E,EAASxG,KAAMwG,EAAS1E,QAEnG2V,EAAkBjT,EAAS,GAAc,IAATqT,GAAuB,IAATC,EAAaD,EAAOC,EAAOrW,EAAI+E,EAASvG,IAAKuG,EAASzE,QAExG,OACMiV,EAAM,IACP1W,EACHoX,iBACAD,oBAEF,MAAMM,QAAuB3a,EAASoT,cAAcnT,EAASG,UAC7D,OAAI+G,IAAUwT,EAAexT,OAASC,IAAWuT,EAAevT,OACvD,CACLgS,MAAO,CACLlR,OAAO,IAIN,CAAC,CACV,EAEJ,EE3UMlE,GFvKO,SAAUpE,GAIrB,YAHgB,IAAZA,IACFA,EAAU,CAAC,GAEN,CACLoD,KAAM,OACNpD,UACA,QAAMqD,CAAGC,GACP,MAAM,MACJgF,GACEhF,GACE,SACJpD,EAAW,qBACRyX,GACDvS,EAASpF,EAASsD,GACtB,OAAQpD,GACN,IAAK,kBACH,CACE,MAIMqR,EAAUhI,QAJOnB,EAAe9E,EAAO,IACxCqU,EACHlP,eAAgB,cAEuBH,EAAMhI,WAC/C,MAAO,CACLO,KAAM,CACJma,uBAAwBzJ,EACxB0J,gBAAiBxR,EAAsB8H,IAG7C,CACF,IAAK,UACH,CACE,MAIMA,EAAUhI,QAJOnB,EAAe9E,EAAO,IACxCqU,EACHjP,aAAa,IAE0BJ,EAAM9H,UAC/C,MAAO,CACLK,KAAM,CACJqa,eAAgB3J,EAChB4J,QAAS1R,EAAsB8H,IAGrC,CACF,QAEI,MAAO,CAAC,EAGhB,EAEJ,EE2HMjN,GFtfQtE,IAAW,CACvBoD,KAAM,QACNpD,UACA,QAAMqD,CAAGC,GACP,MAAM,EACJvC,EAAC,EACDC,EAAC,UACDf,EAAS,MACTqI,EAAK,SACLlI,EAAQ,SACRC,EAAQ,eACRY,GACEqC,GAEE,QACJrE,EAAO,QACPsE,EAAU,GACR6B,EAASpF,EAASsD,IAAU,CAAC,EACjC,GAAe,MAAXrE,EACF,MAAO,CAAC,EAEV,MAAM0J,EAAgBxB,EAAiB5D,GACjC2E,EAAS,CACbnH,IACAC,KAEI0E,EAAOM,EAAiB/F,GACxB1B,EAASoH,EAAcD,GACvB0V,QAAwBhb,EAASoT,cAAcvU,GAC/Cgb,EAAmB,MAATvU,EACV2V,EAAUpB,EAAU,MAAQ,OAC5BqB,EAAUrB,EAAU,SAAW,QAC/BsB,EAAatB,EAAU,eAAiB,cACxCuB,EAAUlT,EAAMhI,UAAU/B,GAAU+J,EAAMhI,UAAUoF,GAAQwC,EAAOxC,GAAQ4C,EAAM9H,SAASjC,GAC1Fkd,EAAYvT,EAAOxC,GAAQ4C,EAAMhI,UAAUoF,GAC3CgW,QAAuD,MAA5Btb,EAAS8I,qBAA0B,EAAS9I,EAAS8I,gBAAgBjK,IACtG,IAAI0c,EAAaD,EAAoBA,EAAkBH,GAAc,EAGhEI,SAA6C,MAAtBvb,EAAS0I,eAAoB,EAAS1I,EAAS0I,UAAU4S,MACnFC,EAAatb,EAASG,SAAS+a,IAAejT,EAAM9H,SAASjC,IAE/D,MAAMqd,EAAoBJ,EAAU,EAAIC,EAAY,EAI9CI,EAAyBF,EAAa,EAAIP,EAAgB7c,GAAU,EAAI,EACxEud,EAAatX,EAAImE,EAAc0S,GAAUQ,GACzCE,EAAavX,EAAImE,EAAc2S,GAAUO,GAIzCG,EAAQF,EACRrX,EAAMkX,EAAaP,EAAgB7c,GAAUwd,EAC7CE,EAASN,EAAa,EAAIP,EAAgB7c,GAAU,EAAIqd,EACxDnY,EAAS0B,EAAM6W,EAAOC,EAAQxX,GAM9ByX,GAAmBjb,EAAeqD,OAAoC,MAA3BkB,EAAavF,IAAsBgc,IAAWxY,GAAU6E,EAAMhI,UAAU/B,GAAU,GAAK0d,EAASD,EAAQF,EAAaC,GAAcX,EAAgB7c,GAAU,EAAI,EAC5MgZ,EAAkB2E,EAAkBD,EAASD,EAAQC,EAASD,EAAQC,EAASxX,EAAM,EAC3F,MAAO,CACL,CAACiB,GAAOwC,EAAOxC,GAAQ6R,EACvB1W,KAAM,CACJ,CAAC6E,GAAOjC,EACR0Y,aAAcF,EAASxY,EAAS8T,KAC5B2E,GAAmB,CACrB3E,oBAGJiC,MAAO0C,EAEX,IEwbIpY,GFoKa,SAAU9D,GAI3B,YAHgB,IAAZA,IACFA,EAAU,CAAC,GAEN,CACLA,UACAqD,EAAAA,CAAGC,GACD,MAAM,EACJvC,EAAC,EACDC,EAAC,UACDf,EAAS,MACTqI,EAAK,eACLrH,GACEqC,GACE,OACJG,EAAS,EACT2T,SAAUI,GAAgB,EAC1BH,UAAWI,GAAiB,GAC1BrS,EAASpF,EAASsD,GAChB4E,EAAS,CACbnH,IACAC,KAEIqW,EAAYvR,EAAY7F,GACxBmX,EAAW3R,EAAgB4R,GACjC,IAAIO,EAAgB1P,EAAOkP,GACvBS,EAAiB3P,EAAOmP,GAC5B,MAAM+E,EAAYhX,EAAS3B,EAAQH,GAC7B+Y,EAAsC,kBAAdD,EAAyB,CACrDhF,SAAUgF,EACV/E,UAAW,GACT,CACFD,SAAU,EACVC,UAAW,KACR+E,GAEL,GAAI5E,EAAe,CACjB,MAAM8E,EAAmB,MAAblF,EAAmB,SAAW,QACpCmF,EAAWjU,EAAMhI,UAAU8W,GAAY9O,EAAM9H,SAAS8b,GAAOD,EAAejF,SAC5EoF,EAAWlU,EAAMhI,UAAU8W,GAAY9O,EAAMhI,UAAUgc,GAAOD,EAAejF,SAC/EQ,EAAgB2E,EAClB3E,EAAgB2E,EACP3E,EAAgB4E,IACzB5E,EAAgB4E,EAEpB,CACA,GAAI/E,EAAgB,CAClB,IAAIX,EAAuB2F,EAC3B,MAAMH,EAAmB,MAAblF,EAAmB,QAAU,SACnCsF,EAAe/S,EAAY5D,IAAIT,EAAQrF,IACvCsc,EAAWjU,EAAMhI,UAAU+W,GAAa/O,EAAM9H,SAAS8b,IAAQI,IAAmE,OAAlD5F,EAAwB7V,EAAewC,aAAkB,EAASqT,EAAsBO,KAAmB,IAAMqF,EAAe,EAAIL,EAAehF,WACnOmF,EAAWlU,EAAMhI,UAAU+W,GAAa/O,EAAMhI,UAAUgc,IAAQI,EAAe,GAAyD,OAAnDD,EAAyBxb,EAAewC,aAAkB,EAASgZ,EAAuBpF,KAAe,IAAMqF,EAAeL,EAAehF,UAAY,GAChPQ,EAAiB0E,EACnB1E,EAAiB0E,EACR1E,EAAiB2E,IAC1B3E,EAAiB2E,EAErB,CACA,MAAO,CACL,CAACpF,GAAWQ,EACZ,CAACP,GAAYQ,EAEjB,EAEJ,EE9NMtV,GAAkBA,CAACjC,EAAWE,EAAUR,KAI5C,MAAMoS,EAAQ,IAAIuK,IACZC,EAAgB,CACpBxc,eACGJ,GAEC6c,EAAoB,IACrBD,EAAcxc,SACjB4S,GAAIZ,GAEN,MF/qBsBjK,OAAO7H,EAAWE,EAAU8B,KAClD,MAAM,UACJrC,EAAY,SAAQ,SACpBC,EAAW,WAAU,WACrBC,EAAa,GAAE,SACfC,GACEkC,EACEwa,EAAkB3c,EAAW+L,OAAO6Q,SACpCpW,QAA+B,MAAlBvG,EAASwT,WAAgB,EAASxT,EAASwT,MAAMpT,IACpE,IAAI8H,QAAclI,EAASiT,gBAAgB,CACzC/S,YACAE,WACAN,cAEE,EACFa,EAAC,EACDC,GACEyG,EAA2Ba,EAAOrI,EAAW0G,GAC7CqW,EAAoB/c,EACpBgB,EAAiB,CAAC,EAClBgc,EAAa,EACjB,IAAK,IAAIze,EAAI,EAAGA,EAAIse,EAAgBve,OAAQC,IAAK,CAC/C,MAAM,KACJ4E,EAAI,GACJC,GACEyZ,EAAgBte,IAElBuC,EAAGmc,EACHlc,EAAGmc,EAAK,KACRtc,EAAI,MACJ2Y,SACQnW,EAAG,CACXtC,IACAC,IACAkX,iBAAkBjY,EAClBA,UAAW+c,EACX9c,WACAe,iBACAqH,QACAlI,WACAC,SAAU,CACRC,YACAE,cAGJO,EAAa,MAATmc,EAAgBA,EAAQnc,EAC5BC,EAAa,MAATmc,EAAgBA,EAAQnc,EAC5BC,EAAiB,IACZA,EACH,CAACmC,GAAO,IACHnC,EAAemC,MACfvC,IAGH2Y,GAASyD,GAAc,KACzBA,IACqB,kBAAVzD,IACLA,EAAMvZ,YACR+c,EAAoBxD,EAAMvZ,WAExBuZ,EAAMlR,QACRA,GAAwB,IAAhBkR,EAAMlR,YAAuBlI,EAASiT,gBAAgB,CAC5D/S,YACAE,WACAN,aACGsZ,EAAMlR,SAGXvH,IACAC,KACEyG,EAA2Ba,EAAO0U,EAAmBrW,KAE3DnI,GAAK,EAET,CACA,MAAO,CACLuC,IACAC,IACAf,UAAW+c,EACX9c,WACAe,mBE+lBKmc,CAAkB9c,EAAWE,EAAU,IACzCoc,EACHxc,SAAUyc,I", "sources": ["../node_modules/lucide-react/src/icons/check.ts", "../node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.mjs", "../node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs", "../node_modules/@floating-ui/core/dist/floating-ui.core.mjs", "../node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs", "../node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Check\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWxpbmUgcG9pbnRzPSIyMCA2IDkgMTcgNCAxMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/check\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Check = createLucideIcon('Check', [\n  ['polyline', { points: '20 6 9 17 4 12', key: '10jjfj' }],\n]);\n\nexport default Check;\n", "import { computePosition, arrow as arrow$2, autoPlacement as autoPlacement$1, flip as flip$1, hide as hide$1, inline as inline$1, limitShift as limitShift$1, offset as offset$1, shift as shift$1, size as size$1 } from '@floating-ui/dom';\nexport { autoUpdate, computePosition, detectOverflow, getOverflowAncestors, platform } from '@floating-ui/dom';\nimport * as React from 'react';\nimport { useLayoutEffect } from 'react';\nimport * as ReactDOM from 'react-dom';\n\nvar isClient = typeof document !== 'undefined';\n\nvar noop = function noop() {};\nvar index = isClient ? useLayoutEffect : noop;\n\n// Fork of `fast-deep-equal` that only does the comparisons we need and compares\n// functions\nfunction deepEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (typeof a !== typeof b) {\n    return false;\n  }\n  if (typeof a === 'function' && a.toString() === b.toString()) {\n    return true;\n  }\n  let length;\n  let i;\n  let keys;\n  if (a && b && typeof a === 'object') {\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length !== b.length) return false;\n      for (i = length; i-- !== 0;) {\n        if (!deepEqual(a[i], b[i])) {\n          return false;\n        }\n      }\n      return true;\n    }\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) {\n      return false;\n    }\n    for (i = length; i-- !== 0;) {\n      if (!{}.hasOwnProperty.call(b, keys[i])) {\n        return false;\n      }\n    }\n    for (i = length; i-- !== 0;) {\n      const key = keys[i];\n      if (key === '_owner' && a.$$typeof) {\n        continue;\n      }\n      if (!deepEqual(a[key], b[key])) {\n        return false;\n      }\n    }\n    return true;\n  }\n  return a !== a && b !== b;\n}\n\nfunction getDPR(element) {\n  if (typeof window === 'undefined') {\n    return 1;\n  }\n  const win = element.ownerDocument.defaultView || window;\n  return win.devicePixelRatio || 1;\n}\n\nfunction roundByDPR(element, value) {\n  const dpr = getDPR(element);\n  return Math.round(value * dpr) / dpr;\n}\n\nfunction useLatestRef(value) {\n  const ref = React.useRef(value);\n  index(() => {\n    ref.current = value;\n  });\n  return ref;\n}\n\n/**\n * Provides data to position a floating element.\n * @see https://floating-ui.com/docs/useFloating\n */\nfunction useFloating(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform,\n    elements: {\n      reference: externalReference,\n      floating: externalFloating\n    } = {},\n    transform = true,\n    whileElementsMounted,\n    open\n  } = options;\n  const [data, setData] = React.useState({\n    x: 0,\n    y: 0,\n    strategy,\n    placement,\n    middlewareData: {},\n    isPositioned: false\n  });\n  const [latestMiddleware, setLatestMiddleware] = React.useState(middleware);\n  if (!deepEqual(latestMiddleware, middleware)) {\n    setLatestMiddleware(middleware);\n  }\n  const [_reference, _setReference] = React.useState(null);\n  const [_floating, _setFloating] = React.useState(null);\n  const setReference = React.useCallback(node => {\n    if (node !== referenceRef.current) {\n      referenceRef.current = node;\n      _setReference(node);\n    }\n  }, []);\n  const setFloating = React.useCallback(node => {\n    if (node !== floatingRef.current) {\n      floatingRef.current = node;\n      _setFloating(node);\n    }\n  }, []);\n  const referenceEl = externalReference || _reference;\n  const floatingEl = externalFloating || _floating;\n  const referenceRef = React.useRef(null);\n  const floatingRef = React.useRef(null);\n  const dataRef = React.useRef(data);\n  const hasWhileElementsMounted = whileElementsMounted != null;\n  const whileElementsMountedRef = useLatestRef(whileElementsMounted);\n  const platformRef = useLatestRef(platform);\n  const openRef = useLatestRef(open);\n  const update = React.useCallback(() => {\n    if (!referenceRef.current || !floatingRef.current) {\n      return;\n    }\n    const config = {\n      placement,\n      strategy,\n      middleware: latestMiddleware\n    };\n    if (platformRef.current) {\n      config.platform = platformRef.current;\n    }\n    computePosition(referenceRef.current, floatingRef.current, config).then(data => {\n      const fullData = {\n        ...data,\n        // The floating element's position may be recomputed while it's closed\n        // but still mounted (such as when transitioning out). To ensure\n        // `isPositioned` will be `false` initially on the next open, avoid\n        // setting it to `true` when `open === false` (must be specified).\n        isPositioned: openRef.current !== false\n      };\n      if (isMountedRef.current && !deepEqual(dataRef.current, fullData)) {\n        dataRef.current = fullData;\n        ReactDOM.flushSync(() => {\n          setData(fullData);\n        });\n      }\n    });\n  }, [latestMiddleware, placement, strategy, platformRef, openRef]);\n  index(() => {\n    if (open === false && dataRef.current.isPositioned) {\n      dataRef.current.isPositioned = false;\n      setData(data => ({\n        ...data,\n        isPositioned: false\n      }));\n    }\n  }, [open]);\n  const isMountedRef = React.useRef(false);\n  index(() => {\n    isMountedRef.current = true;\n    return () => {\n      isMountedRef.current = false;\n    };\n  }, []);\n  index(() => {\n    if (referenceEl) referenceRef.current = referenceEl;\n    if (floatingEl) floatingRef.current = floatingEl;\n    if (referenceEl && floatingEl) {\n      if (whileElementsMountedRef.current) {\n        return whileElementsMountedRef.current(referenceEl, floatingEl, update);\n      }\n      update();\n    }\n  }, [referenceEl, floatingEl, update, whileElementsMountedRef, hasWhileElementsMounted]);\n  const refs = React.useMemo(() => ({\n    reference: referenceRef,\n    floating: floatingRef,\n    setReference,\n    setFloating\n  }), [setReference, setFloating]);\n  const elements = React.useMemo(() => ({\n    reference: referenceEl,\n    floating: floatingEl\n  }), [referenceEl, floatingEl]);\n  const floatingStyles = React.useMemo(() => {\n    const initialStyles = {\n      position: strategy,\n      left: 0,\n      top: 0\n    };\n    if (!elements.floating) {\n      return initialStyles;\n    }\n    const x = roundByDPR(elements.floating, data.x);\n    const y = roundByDPR(elements.floating, data.y);\n    if (transform) {\n      return {\n        ...initialStyles,\n        transform: \"translate(\" + x + \"px, \" + y + \"px)\",\n        ...(getDPR(elements.floating) >= 1.5 && {\n          willChange: 'transform'\n        })\n      };\n    }\n    return {\n      position: strategy,\n      left: x,\n      top: y\n    };\n  }, [strategy, transform, elements.floating, data.x, data.y]);\n  return React.useMemo(() => ({\n    ...data,\n    update,\n    refs,\n    elements,\n    floatingStyles\n  }), [data, update, refs, elements, floatingStyles]);\n}\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * This wraps the core `arrow` middleware to allow React refs as the element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow$1 = options => {\n  function isRef(value) {\n    return {}.hasOwnProperty.call(value, 'current');\n  }\n  return {\n    name: 'arrow',\n    options,\n    fn(state) {\n      const {\n        element,\n        padding\n      } = typeof options === 'function' ? options(state) : options;\n      if (element && isRef(element)) {\n        if (element.current != null) {\n          return arrow$2({\n            element: element.current,\n            padding\n          }).fn(state);\n        }\n        return {};\n      }\n      if (element) {\n        return arrow$2({\n          element,\n          padding\n        }).fn(state);\n      }\n      return {};\n    }\n  };\n};\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = (options, deps) => ({\n  ...offset$1(options),\n  options: [options, deps]\n});\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = (options, deps) => ({\n  ...shift$1(options),\n  options: [options, deps]\n});\n\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = (options, deps) => ({\n  ...limitShift$1(options),\n  options: [options, deps]\n});\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = (options, deps) => ({\n  ...flip$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = (options, deps) => ({\n  ...size$1(options),\n  options: [options, deps]\n});\n\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = (options, deps) => ({\n  ...autoPlacement$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = (options, deps) => ({\n  ...hide$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = (options, deps) => ({\n  ...inline$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * This wraps the core `arrow` middleware to allow React refs as the element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = (options, deps) => ({\n  ...arrow$1(options),\n  options: [options, deps]\n});\n\nexport { arrow, autoPlacement, flip, hide, inline, limitShift, offset, shift, size, useFloating };\n", "/**\n * Custom positioning reference element.\n * @see https://floating-ui.com/docs/virtual-elements\n */\n\nconst sides = ['top', 'right', 'bottom', 'left'];\nconst alignments = ['start', 'end'];\nconst placements = /*#__PURE__*/sides.reduce((acc, side) => acc.concat(side, side + \"-\" + alignments[0], side + \"-\" + alignments[1]), []);\nconst min = Math.min;\nconst max = Math.max;\nconst round = Math.round;\nconst floor = Math.floor;\nconst createCoords = v => ({\n  x: v,\n  y: v\n});\nconst oppositeSideMap = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nconst oppositeAlignmentMap = {\n  start: 'end',\n  end: 'start'\n};\nfunction clamp(start, value, end) {\n  return max(start, min(value, end));\n}\nfunction evaluate(value, param) {\n  return typeof value === 'function' ? value(param) : value;\n}\nfunction getSide(placement) {\n  return placement.split('-')[0];\n}\nfunction getAlignment(placement) {\n  return placement.split('-')[1];\n}\nfunction getOppositeAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}\nfunction getAxisLength(axis) {\n  return axis === 'y' ? 'height' : 'width';\n}\nconst yAxisSides = /*#__PURE__*/new Set(['top', 'bottom']);\nfunction getSideAxis(placement) {\n  return yAxisSides.has(getSide(placement)) ? 'y' : 'x';\n}\nfunction getAlignmentAxis(placement) {\n  return getOppositeAxis(getSideAxis(placement));\n}\nfunction getAlignmentSides(placement, rects, rtl) {\n  if (rtl === void 0) {\n    rtl = false;\n  }\n  const alignment = getAlignment(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const length = getAxisLength(alignmentAxis);\n  let mainAlignmentSide = alignmentAxis === 'x' ? alignment === (rtl ? 'end' : 'start') ? 'right' : 'left' : alignment === 'start' ? 'bottom' : 'top';\n  if (rects.reference[length] > rects.floating[length]) {\n    mainAlignmentSide = getOppositePlacement(mainAlignmentSide);\n  }\n  return [mainAlignmentSide, getOppositePlacement(mainAlignmentSide)];\n}\nfunction getExpandedPlacements(placement) {\n  const oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeAlignmentPlacement(placement), oppositePlacement, getOppositeAlignmentPlacement(oppositePlacement)];\n}\nfunction getOppositeAlignmentPlacement(placement) {\n  return placement.replace(/start|end/g, alignment => oppositeAlignmentMap[alignment]);\n}\nconst lrPlacement = ['left', 'right'];\nconst rlPlacement = ['right', 'left'];\nconst tbPlacement = ['top', 'bottom'];\nconst btPlacement = ['bottom', 'top'];\nfunction getSideList(side, isStart, rtl) {\n  switch (side) {\n    case 'top':\n    case 'bottom':\n      if (rtl) return isStart ? rlPlacement : lrPlacement;\n      return isStart ? lrPlacement : rlPlacement;\n    case 'left':\n    case 'right':\n      return isStart ? tbPlacement : btPlacement;\n    default:\n      return [];\n  }\n}\nfunction getOppositeAxisPlacements(placement, flipAlignment, direction, rtl) {\n  const alignment = getAlignment(placement);\n  let list = getSideList(getSide(placement), direction === 'start', rtl);\n  if (alignment) {\n    list = list.map(side => side + \"-\" + alignment);\n    if (flipAlignment) {\n      list = list.concat(list.map(getOppositeAlignmentPlacement));\n    }\n  }\n  return list;\n}\nfunction getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, side => oppositeSideMap[side]);\n}\nfunction expandPaddingObject(padding) {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0,\n    ...padding\n  };\n}\nfunction getPaddingObject(padding) {\n  return typeof padding !== 'number' ? expandPaddingObject(padding) : {\n    top: padding,\n    right: padding,\n    bottom: padding,\n    left: padding\n  };\n}\nfunction rectToClientRect(rect) {\n  const {\n    x,\n    y,\n    width,\n    height\n  } = rect;\n  return {\n    width,\n    height,\n    top: y,\n    left: x,\n    right: x + width,\n    bottom: y + height,\n    x,\n    y\n  };\n}\n\nexport { alignments, clamp, createCoords, evaluate, expandPaddingObject, floor, getAlignment, getAlignmentAxis, getAlignmentSides, getAxisLength, getExpandedPlacements, getOppositeAlignmentPlacement, getOppositeAxis, getOppositeAxisPlacements, getOppositePlacement, getPaddingObject, getSide, getSideAxis, max, min, placements, rectToClientRect, round, sides };\n", "import { getSideAxis, getAlignmentAxis, getAxisLength, getSide, getAlignment, evaluate, getPaddingObject, rectToClientRect, min, clamp, placements, getAlignmentSides, getOppositeAlignmentPlacement, getOppositePlacement, getExpandedPlacements, getOppositeAxisPlacements, sides, max, getOppositeAxis } from '@floating-ui/utils';\nexport { rectToClientRect } from '@floating-ui/utils';\n\nfunction computeCoordsFromPlacement(_ref, placement, rtl) {\n  let {\n    reference,\n    floating\n  } = _ref;\n  const sideAxis = getSideAxis(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const alignLength = getAxisLength(alignmentAxis);\n  const side = getSide(placement);\n  const isVertical = sideAxis === 'y';\n  const commonX = reference.x + reference.width / 2 - floating.width / 2;\n  const commonY = reference.y + reference.height / 2 - floating.height / 2;\n  const commonAlign = reference[alignLength] / 2 - floating[alignLength] / 2;\n  let coords;\n  switch (side) {\n    case 'top':\n      coords = {\n        x: commonX,\n        y: reference.y - floating.height\n      };\n      break;\n    case 'bottom':\n      coords = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n    case 'right':\n      coords = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n    case 'left':\n      coords = {\n        x: reference.x - floating.width,\n        y: commonY\n      };\n      break;\n    default:\n      coords = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n  switch (getAlignment(placement)) {\n    case 'start':\n      coords[alignmentAxis] -= commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n    case 'end':\n      coords[alignmentAxis] += commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n  }\n  return coords;\n}\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n *\n * This export does not have any `platform` interface logic. You will need to\n * write one for the platform you are using Floating UI with.\n */\nconst computePosition = async (reference, floating, config) => {\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform\n  } = config;\n  const validMiddleware = middleware.filter(Boolean);\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(floating));\n  let rects = await platform.getElementRects({\n    reference,\n    floating,\n    strategy\n  });\n  let {\n    x,\n    y\n  } = computeCoordsFromPlacement(rects, placement, rtl);\n  let statefulPlacement = placement;\n  let middlewareData = {};\n  let resetCount = 0;\n  for (let i = 0; i < validMiddleware.length; i++) {\n    const {\n      name,\n      fn\n    } = validMiddleware[i];\n    const {\n      x: nextX,\n      y: nextY,\n      data,\n      reset\n    } = await fn({\n      x,\n      y,\n      initialPlacement: placement,\n      placement: statefulPlacement,\n      strategy,\n      middlewareData,\n      rects,\n      platform,\n      elements: {\n        reference,\n        floating\n      }\n    });\n    x = nextX != null ? nextX : x;\n    y = nextY != null ? nextY : y;\n    middlewareData = {\n      ...middlewareData,\n      [name]: {\n        ...middlewareData[name],\n        ...data\n      }\n    };\n    if (reset && resetCount <= 50) {\n      resetCount++;\n      if (typeof reset === 'object') {\n        if (reset.placement) {\n          statefulPlacement = reset.placement;\n        }\n        if (reset.rects) {\n          rects = reset.rects === true ? await platform.getElementRects({\n            reference,\n            floating,\n            strategy\n          }) : reset.rects;\n        }\n        ({\n          x,\n          y\n        } = computeCoordsFromPlacement(rects, statefulPlacement, rtl));\n      }\n      i = -1;\n    }\n  }\n  return {\n    x,\n    y,\n    placement: statefulPlacement,\n    strategy,\n    middlewareData\n  };\n};\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nasync function detectOverflow(state, options) {\n  var _await$platform$isEle;\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    x,\n    y,\n    platform,\n    rects,\n    elements,\n    strategy\n  } = state;\n  const {\n    boundary = 'clippingAncestors',\n    rootBoundary = 'viewport',\n    elementContext = 'floating',\n    altBoundary = false,\n    padding = 0\n  } = evaluate(options, state);\n  const paddingObject = getPaddingObject(padding);\n  const altContext = elementContext === 'floating' ? 'reference' : 'floating';\n  const element = elements[altBoundary ? altContext : elementContext];\n  const clippingClientRect = rectToClientRect(await platform.getClippingRect({\n    element: ((_await$platform$isEle = await (platform.isElement == null ? void 0 : platform.isElement(element))) != null ? _await$platform$isEle : true) ? element : element.contextElement || (await (platform.getDocumentElement == null ? void 0 : platform.getDocumentElement(elements.floating))),\n    boundary,\n    rootBoundary,\n    strategy\n  }));\n  const rect = elementContext === 'floating' ? {\n    x,\n    y,\n    width: rects.floating.width,\n    height: rects.floating.height\n  } : rects.reference;\n  const offsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(elements.floating));\n  const offsetScale = (await (platform.isElement == null ? void 0 : platform.isElement(offsetParent))) ? (await (platform.getScale == null ? void 0 : platform.getScale(offsetParent))) || {\n    x: 1,\n    y: 1\n  } : {\n    x: 1,\n    y: 1\n  };\n  const elementClientRect = rectToClientRect(platform.convertOffsetParentRelativeRectToViewportRelativeRect ? await platform.convertOffsetParentRelativeRectToViewportRelativeRect({\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  }) : rect);\n  return {\n    top: (clippingClientRect.top - elementClientRect.top + paddingObject.top) / offsetScale.y,\n    bottom: (elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom) / offsetScale.y,\n    left: (clippingClientRect.left - elementClientRect.left + paddingObject.left) / offsetScale.x,\n    right: (elementClientRect.right - clippingClientRect.right + paddingObject.right) / offsetScale.x\n  };\n}\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = options => ({\n  name: 'arrow',\n  options,\n  async fn(state) {\n    const {\n      x,\n      y,\n      placement,\n      rects,\n      platform,\n      elements,\n      middlewareData\n    } = state;\n    // Since `element` is required, we don't Partial<> the type.\n    const {\n      element,\n      padding = 0\n    } = evaluate(options, state) || {};\n    if (element == null) {\n      return {};\n    }\n    const paddingObject = getPaddingObject(padding);\n    const coords = {\n      x,\n      y\n    };\n    const axis = getAlignmentAxis(placement);\n    const length = getAxisLength(axis);\n    const arrowDimensions = await platform.getDimensions(element);\n    const isYAxis = axis === 'y';\n    const minProp = isYAxis ? 'top' : 'left';\n    const maxProp = isYAxis ? 'bottom' : 'right';\n    const clientProp = isYAxis ? 'clientHeight' : 'clientWidth';\n    const endDiff = rects.reference[length] + rects.reference[axis] - coords[axis] - rects.floating[length];\n    const startDiff = coords[axis] - rects.reference[axis];\n    const arrowOffsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(element));\n    let clientSize = arrowOffsetParent ? arrowOffsetParent[clientProp] : 0;\n\n    // DOM platform can return `window` as the `offsetParent`.\n    if (!clientSize || !(await (platform.isElement == null ? void 0 : platform.isElement(arrowOffsetParent)))) {\n      clientSize = elements.floating[clientProp] || rects.floating[length];\n    }\n    const centerToReference = endDiff / 2 - startDiff / 2;\n\n    // If the padding is large enough that it causes the arrow to no longer be\n    // centered, modify the padding so that it is centered.\n    const largestPossiblePadding = clientSize / 2 - arrowDimensions[length] / 2 - 1;\n    const minPadding = min(paddingObject[minProp], largestPossiblePadding);\n    const maxPadding = min(paddingObject[maxProp], largestPossiblePadding);\n\n    // Make sure the arrow doesn't overflow the floating element if the center\n    // point is outside the floating element's bounds.\n    const min$1 = minPadding;\n    const max = clientSize - arrowDimensions[length] - maxPadding;\n    const center = clientSize / 2 - arrowDimensions[length] / 2 + centerToReference;\n    const offset = clamp(min$1, center, max);\n\n    // If the reference is small enough that the arrow's padding causes it to\n    // to point to nothing for an aligned placement, adjust the offset of the\n    // floating element itself. To ensure `shift()` continues to take action,\n    // a single reset is performed when this is true.\n    const shouldAddOffset = !middlewareData.arrow && getAlignment(placement) != null && center !== offset && rects.reference[length] / 2 - (center < min$1 ? minPadding : maxPadding) - arrowDimensions[length] / 2 < 0;\n    const alignmentOffset = shouldAddOffset ? center < min$1 ? center - min$1 : center - max : 0;\n    return {\n      [axis]: coords[axis] + alignmentOffset,\n      data: {\n        [axis]: offset,\n        centerOffset: center - offset - alignmentOffset,\n        ...(shouldAddOffset && {\n          alignmentOffset\n        })\n      },\n      reset: shouldAddOffset\n    };\n  }\n});\n\nfunction getPlacementList(alignment, autoAlignment, allowedPlacements) {\n  const allowedPlacementsSortedByAlignment = alignment ? [...allowedPlacements.filter(placement => getAlignment(placement) === alignment), ...allowedPlacements.filter(placement => getAlignment(placement) !== alignment)] : allowedPlacements.filter(placement => getSide(placement) === placement);\n  return allowedPlacementsSortedByAlignment.filter(placement => {\n    if (alignment) {\n      return getAlignment(placement) === alignment || (autoAlignment ? getOppositeAlignmentPlacement(placement) !== placement : false);\n    }\n    return true;\n  });\n}\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'autoPlacement',\n    options,\n    async fn(state) {\n      var _middlewareData$autoP, _middlewareData$autoP2, _placementsThatFitOnE;\n      const {\n        rects,\n        middlewareData,\n        placement,\n        platform,\n        elements\n      } = state;\n      const {\n        crossAxis = false,\n        alignment,\n        allowedPlacements = placements,\n        autoAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const placements$1 = alignment !== undefined || allowedPlacements === placements ? getPlacementList(alignment || null, autoAlignment, allowedPlacements) : allowedPlacements;\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const currentIndex = ((_middlewareData$autoP = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP.index) || 0;\n      const currentPlacement = placements$1[currentIndex];\n      if (currentPlacement == null) {\n        return {};\n      }\n      const alignmentSides = getAlignmentSides(currentPlacement, rects, await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating)));\n\n      // Make `computeCoords` start from the right place.\n      if (placement !== currentPlacement) {\n        return {\n          reset: {\n            placement: placements$1[0]\n          }\n        };\n      }\n      const currentOverflows = [overflow[getSide(currentPlacement)], overflow[alignmentSides[0]], overflow[alignmentSides[1]]];\n      const allOverflows = [...(((_middlewareData$autoP2 = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP2.overflows) || []), {\n        placement: currentPlacement,\n        overflows: currentOverflows\n      }];\n      const nextPlacement = placements$1[currentIndex + 1];\n\n      // There are more placements to check.\n      if (nextPlacement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: nextPlacement\n          }\n        };\n      }\n      const placementsSortedByMostSpace = allOverflows.map(d => {\n        const alignment = getAlignment(d.placement);\n        return [d.placement, alignment && crossAxis ?\n        // Check along the mainAxis and main crossAxis side.\n        d.overflows.slice(0, 2).reduce((acc, v) => acc + v, 0) :\n        // Check only the mainAxis.\n        d.overflows[0], d.overflows];\n      }).sort((a, b) => a[1] - b[1]);\n      const placementsThatFitOnEachSide = placementsSortedByMostSpace.filter(d => d[2].slice(0,\n      // Aligned placements should not check their opposite crossAxis\n      // side.\n      getAlignment(d[0]) ? 2 : 3).every(v => v <= 0));\n      const resetPlacement = ((_placementsThatFitOnE = placementsThatFitOnEachSide[0]) == null ? void 0 : _placementsThatFitOnE[0]) || placementsSortedByMostSpace[0][0];\n      if (resetPlacement !== placement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: resetPlacement\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'flip',\n    options,\n    async fn(state) {\n      var _middlewareData$arrow, _middlewareData$flip;\n      const {\n        placement,\n        middlewareData,\n        rects,\n        initialPlacement,\n        platform,\n        elements\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true,\n        fallbackPlacements: specifiedFallbackPlacements,\n        fallbackStrategy = 'bestFit',\n        fallbackAxisSideDirection = 'none',\n        flipAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n\n      // If a reset by the arrow was caused due to an alignment offset being\n      // added, we should skip any logic now since `flip()` has already done its\n      // work.\n      // https://github.com/floating-ui/floating-ui/issues/2549#issuecomment-1719601643\n      if ((_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      const side = getSide(placement);\n      const initialSideAxis = getSideAxis(initialPlacement);\n      const isBasePlacement = getSide(initialPlacement) === initialPlacement;\n      const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n      const fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipAlignment ? [getOppositePlacement(initialPlacement)] : getExpandedPlacements(initialPlacement));\n      const hasFallbackAxisSideDirection = fallbackAxisSideDirection !== 'none';\n      if (!specifiedFallbackPlacements && hasFallbackAxisSideDirection) {\n        fallbackPlacements.push(...getOppositeAxisPlacements(initialPlacement, flipAlignment, fallbackAxisSideDirection, rtl));\n      }\n      const placements = [initialPlacement, ...fallbackPlacements];\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const overflows = [];\n      let overflowsData = ((_middlewareData$flip = middlewareData.flip) == null ? void 0 : _middlewareData$flip.overflows) || [];\n      if (checkMainAxis) {\n        overflows.push(overflow[side]);\n      }\n      if (checkCrossAxis) {\n        const sides = getAlignmentSides(placement, rects, rtl);\n        overflows.push(overflow[sides[0]], overflow[sides[1]]);\n      }\n      overflowsData = [...overflowsData, {\n        placement,\n        overflows\n      }];\n\n      // One or more sides is overflowing.\n      if (!overflows.every(side => side <= 0)) {\n        var _middlewareData$flip2, _overflowsData$filter;\n        const nextIndex = (((_middlewareData$flip2 = middlewareData.flip) == null ? void 0 : _middlewareData$flip2.index) || 0) + 1;\n        const nextPlacement = placements[nextIndex];\n        if (nextPlacement) {\n          const ignoreCrossAxisOverflow = checkCrossAxis === 'alignment' ? initialSideAxis !== getSideAxis(nextPlacement) : false;\n          if (!ignoreCrossAxisOverflow ||\n          // We leave the current main axis only if every placement on that axis\n          // overflows the main axis.\n          overflowsData.every(d => d.overflows[0] > 0 && getSideAxis(d.placement) === initialSideAxis)) {\n            // Try next placement and re-run the lifecycle.\n            return {\n              data: {\n                index: nextIndex,\n                overflows: overflowsData\n              },\n              reset: {\n                placement: nextPlacement\n              }\n            };\n          }\n        }\n\n        // First, find the candidates that fit on the mainAxis side of overflow,\n        // then find the placement that fits the best on the main crossAxis side.\n        let resetPlacement = (_overflowsData$filter = overflowsData.filter(d => d.overflows[0] <= 0).sort((a, b) => a.overflows[1] - b.overflows[1])[0]) == null ? void 0 : _overflowsData$filter.placement;\n\n        // Otherwise fallback.\n        if (!resetPlacement) {\n          switch (fallbackStrategy) {\n            case 'bestFit':\n              {\n                var _overflowsData$filter2;\n                const placement = (_overflowsData$filter2 = overflowsData.filter(d => {\n                  if (hasFallbackAxisSideDirection) {\n                    const currentSideAxis = getSideAxis(d.placement);\n                    return currentSideAxis === initialSideAxis ||\n                    // Create a bias to the `y` side axis due to horizontal\n                    // reading directions favoring greater width.\n                    currentSideAxis === 'y';\n                  }\n                  return true;\n                }).map(d => [d.placement, d.overflows.filter(overflow => overflow > 0).reduce((acc, overflow) => acc + overflow, 0)]).sort((a, b) => a[1] - b[1])[0]) == null ? void 0 : _overflowsData$filter2[0];\n                if (placement) {\n                  resetPlacement = placement;\n                }\n                break;\n              }\n            case 'initialPlacement':\n              resetPlacement = initialPlacement;\n              break;\n          }\n        }\n        if (placement !== resetPlacement) {\n          return {\n            reset: {\n              placement: resetPlacement\n            }\n          };\n        }\n      }\n      return {};\n    }\n  };\n};\n\nfunction getSideOffsets(overflow, rect) {\n  return {\n    top: overflow.top - rect.height,\n    right: overflow.right - rect.width,\n    bottom: overflow.bottom - rect.height,\n    left: overflow.left - rect.width\n  };\n}\nfunction isAnySideFullyClipped(overflow) {\n  return sides.some(side => overflow[side] >= 0);\n}\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'hide',\n    options,\n    async fn(state) {\n      const {\n        rects\n      } = state;\n      const {\n        strategy = 'referenceHidden',\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      switch (strategy) {\n        case 'referenceHidden':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              elementContext: 'reference'\n            });\n            const offsets = getSideOffsets(overflow, rects.reference);\n            return {\n              data: {\n                referenceHiddenOffsets: offsets,\n                referenceHidden: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        case 'escaped':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              altBoundary: true\n            });\n            const offsets = getSideOffsets(overflow, rects.floating);\n            return {\n              data: {\n                escapedOffsets: offsets,\n                escaped: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        default:\n          {\n            return {};\n          }\n      }\n    }\n  };\n};\n\nfunction getBoundingRect(rects) {\n  const minX = min(...rects.map(rect => rect.left));\n  const minY = min(...rects.map(rect => rect.top));\n  const maxX = max(...rects.map(rect => rect.right));\n  const maxY = max(...rects.map(rect => rect.bottom));\n  return {\n    x: minX,\n    y: minY,\n    width: maxX - minX,\n    height: maxY - minY\n  };\n}\nfunction getRectsByLine(rects) {\n  const sortedRects = rects.slice().sort((a, b) => a.y - b.y);\n  const groups = [];\n  let prevRect = null;\n  for (let i = 0; i < sortedRects.length; i++) {\n    const rect = sortedRects[i];\n    if (!prevRect || rect.y - prevRect.y > prevRect.height / 2) {\n      groups.push([rect]);\n    } else {\n      groups[groups.length - 1].push(rect);\n    }\n    prevRect = rect;\n  }\n  return groups.map(rect => rectToClientRect(getBoundingRect(rect)));\n}\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'inline',\n    options,\n    async fn(state) {\n      const {\n        placement,\n        elements,\n        rects,\n        platform,\n        strategy\n      } = state;\n      // A MouseEvent's client{X,Y} coords can be up to 2 pixels off a\n      // ClientRect's bounds, despite the event listener being triggered. A\n      // padding of 2 seems to handle this issue.\n      const {\n        padding = 2,\n        x,\n        y\n      } = evaluate(options, state);\n      const nativeClientRects = Array.from((await (platform.getClientRects == null ? void 0 : platform.getClientRects(elements.reference))) || []);\n      const clientRects = getRectsByLine(nativeClientRects);\n      const fallback = rectToClientRect(getBoundingRect(nativeClientRects));\n      const paddingObject = getPaddingObject(padding);\n      function getBoundingClientRect() {\n        // There are two rects and they are disjoined.\n        if (clientRects.length === 2 && clientRects[0].left > clientRects[1].right && x != null && y != null) {\n          // Find the first rect in which the point is fully inside.\n          return clientRects.find(rect => x > rect.left - paddingObject.left && x < rect.right + paddingObject.right && y > rect.top - paddingObject.top && y < rect.bottom + paddingObject.bottom) || fallback;\n        }\n\n        // There are 2 or more connected rects.\n        if (clientRects.length >= 2) {\n          if (getSideAxis(placement) === 'y') {\n            const firstRect = clientRects[0];\n            const lastRect = clientRects[clientRects.length - 1];\n            const isTop = getSide(placement) === 'top';\n            const top = firstRect.top;\n            const bottom = lastRect.bottom;\n            const left = isTop ? firstRect.left : lastRect.left;\n            const right = isTop ? firstRect.right : lastRect.right;\n            const width = right - left;\n            const height = bottom - top;\n            return {\n              top,\n              bottom,\n              left,\n              right,\n              width,\n              height,\n              x: left,\n              y: top\n            };\n          }\n          const isLeftSide = getSide(placement) === 'left';\n          const maxRight = max(...clientRects.map(rect => rect.right));\n          const minLeft = min(...clientRects.map(rect => rect.left));\n          const measureRects = clientRects.filter(rect => isLeftSide ? rect.left === minLeft : rect.right === maxRight);\n          const top = measureRects[0].top;\n          const bottom = measureRects[measureRects.length - 1].bottom;\n          const left = minLeft;\n          const right = maxRight;\n          const width = right - left;\n          const height = bottom - top;\n          return {\n            top,\n            bottom,\n            left,\n            right,\n            width,\n            height,\n            x: left,\n            y: top\n          };\n        }\n        return fallback;\n      }\n      const resetRects = await platform.getElementRects({\n        reference: {\n          getBoundingClientRect\n        },\n        floating: elements.floating,\n        strategy\n      });\n      if (rects.reference.x !== resetRects.reference.x || rects.reference.y !== resetRects.reference.y || rects.reference.width !== resetRects.reference.width || rects.reference.height !== resetRects.reference.height) {\n        return {\n          reset: {\n            rects: resetRects\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\nconst originSides = /*#__PURE__*/new Set(['left', 'top']);\n\n// For type backwards-compatibility, the `OffsetOptions` type was also\n// Derivable.\n\nasync function convertValueToCoords(state, options) {\n  const {\n    placement,\n    platform,\n    elements\n  } = state;\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n  const side = getSide(placement);\n  const alignment = getAlignment(placement);\n  const isVertical = getSideAxis(placement) === 'y';\n  const mainAxisMulti = originSides.has(side) ? -1 : 1;\n  const crossAxisMulti = rtl && isVertical ? -1 : 1;\n  const rawValue = evaluate(options, state);\n\n  // eslint-disable-next-line prefer-const\n  let {\n    mainAxis,\n    crossAxis,\n    alignmentAxis\n  } = typeof rawValue === 'number' ? {\n    mainAxis: rawValue,\n    crossAxis: 0,\n    alignmentAxis: null\n  } : {\n    mainAxis: rawValue.mainAxis || 0,\n    crossAxis: rawValue.crossAxis || 0,\n    alignmentAxis: rawValue.alignmentAxis\n  };\n  if (alignment && typeof alignmentAxis === 'number') {\n    crossAxis = alignment === 'end' ? alignmentAxis * -1 : alignmentAxis;\n  }\n  return isVertical ? {\n    x: crossAxis * crossAxisMulti,\n    y: mainAxis * mainAxisMulti\n  } : {\n    x: mainAxis * mainAxisMulti,\n    y: crossAxis * crossAxisMulti\n  };\n}\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = function (options) {\n  if (options === void 0) {\n    options = 0;\n  }\n  return {\n    name: 'offset',\n    options,\n    async fn(state) {\n      var _middlewareData$offse, _middlewareData$arrow;\n      const {\n        x,\n        y,\n        placement,\n        middlewareData\n      } = state;\n      const diffCoords = await convertValueToCoords(state, options);\n\n      // If the placement is the same and the arrow caused an alignment offset\n      // then we don't need to change the positioning coordinates.\n      if (placement === ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse.placement) && (_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      return {\n        x: x + diffCoords.x,\n        y: y + diffCoords.y,\n        data: {\n          ...diffCoords,\n          placement\n        }\n      };\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'shift',\n    options,\n    async fn(state) {\n      const {\n        x,\n        y,\n        placement\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = false,\n        limiter = {\n          fn: _ref => {\n            let {\n              x,\n              y\n            } = _ref;\n            return {\n              x,\n              y\n            };\n          }\n        },\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const crossAxis = getSideAxis(getSide(placement));\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      if (checkMainAxis) {\n        const minSide = mainAxis === 'y' ? 'top' : 'left';\n        const maxSide = mainAxis === 'y' ? 'bottom' : 'right';\n        const min = mainAxisCoord + overflow[minSide];\n        const max = mainAxisCoord - overflow[maxSide];\n        mainAxisCoord = clamp(min, mainAxisCoord, max);\n      }\n      if (checkCrossAxis) {\n        const minSide = crossAxis === 'y' ? 'top' : 'left';\n        const maxSide = crossAxis === 'y' ? 'bottom' : 'right';\n        const min = crossAxisCoord + overflow[minSide];\n        const max = crossAxisCoord - overflow[maxSide];\n        crossAxisCoord = clamp(min, crossAxisCoord, max);\n      }\n      const limitedCoords = limiter.fn({\n        ...state,\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      });\n      return {\n        ...limitedCoords,\n        data: {\n          x: limitedCoords.x - x,\n          y: limitedCoords.y - y,\n          enabled: {\n            [mainAxis]: checkMainAxis,\n            [crossAxis]: checkCrossAxis\n          }\n        }\n      };\n    }\n  };\n};\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    options,\n    fn(state) {\n      const {\n        x,\n        y,\n        placement,\n        rects,\n        middlewareData\n      } = state;\n      const {\n        offset = 0,\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const crossAxis = getSideAxis(placement);\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      const rawOffset = evaluate(offset, state);\n      const computedOffset = typeof rawOffset === 'number' ? {\n        mainAxis: rawOffset,\n        crossAxis: 0\n      } : {\n        mainAxis: 0,\n        crossAxis: 0,\n        ...rawOffset\n      };\n      if (checkMainAxis) {\n        const len = mainAxis === 'y' ? 'height' : 'width';\n        const limitMin = rects.reference[mainAxis] - rects.floating[len] + computedOffset.mainAxis;\n        const limitMax = rects.reference[mainAxis] + rects.reference[len] - computedOffset.mainAxis;\n        if (mainAxisCoord < limitMin) {\n          mainAxisCoord = limitMin;\n        } else if (mainAxisCoord > limitMax) {\n          mainAxisCoord = limitMax;\n        }\n      }\n      if (checkCrossAxis) {\n        var _middlewareData$offse, _middlewareData$offse2;\n        const len = mainAxis === 'y' ? 'width' : 'height';\n        const isOriginSide = originSides.has(getSide(placement));\n        const limitMin = rects.reference[crossAxis] - rects.floating[len] + (isOriginSide ? ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse[crossAxis]) || 0 : 0) + (isOriginSide ? 0 : computedOffset.crossAxis);\n        const limitMax = rects.reference[crossAxis] + rects.reference[len] + (isOriginSide ? 0 : ((_middlewareData$offse2 = middlewareData.offset) == null ? void 0 : _middlewareData$offse2[crossAxis]) || 0) - (isOriginSide ? computedOffset.crossAxis : 0);\n        if (crossAxisCoord < limitMin) {\n          crossAxisCoord = limitMin;\n        } else if (crossAxisCoord > limitMax) {\n          crossAxisCoord = limitMax;\n        }\n      }\n      return {\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      };\n    }\n  };\n};\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'size',\n    options,\n    async fn(state) {\n      var _state$middlewareData, _state$middlewareData2;\n      const {\n        placement,\n        rects,\n        platform,\n        elements\n      } = state;\n      const {\n        apply = () => {},\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const side = getSide(placement);\n      const alignment = getAlignment(placement);\n      const isYAxis = getSideAxis(placement) === 'y';\n      const {\n        width,\n        height\n      } = rects.floating;\n      let heightSide;\n      let widthSide;\n      if (side === 'top' || side === 'bottom') {\n        heightSide = side;\n        widthSide = alignment === ((await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating))) ? 'start' : 'end') ? 'left' : 'right';\n      } else {\n        widthSide = side;\n        heightSide = alignment === 'end' ? 'top' : 'bottom';\n      }\n      const maximumClippingHeight = height - overflow.top - overflow.bottom;\n      const maximumClippingWidth = width - overflow.left - overflow.right;\n      const overflowAvailableHeight = min(height - overflow[heightSide], maximumClippingHeight);\n      const overflowAvailableWidth = min(width - overflow[widthSide], maximumClippingWidth);\n      const noShift = !state.middlewareData.shift;\n      let availableHeight = overflowAvailableHeight;\n      let availableWidth = overflowAvailableWidth;\n      if ((_state$middlewareData = state.middlewareData.shift) != null && _state$middlewareData.enabled.x) {\n        availableWidth = maximumClippingWidth;\n      }\n      if ((_state$middlewareData2 = state.middlewareData.shift) != null && _state$middlewareData2.enabled.y) {\n        availableHeight = maximumClippingHeight;\n      }\n      if (noShift && !alignment) {\n        const xMin = max(overflow.left, 0);\n        const xMax = max(overflow.right, 0);\n        const yMin = max(overflow.top, 0);\n        const yMax = max(overflow.bottom, 0);\n        if (isYAxis) {\n          availableWidth = width - 2 * (xMin !== 0 || xMax !== 0 ? xMin + xMax : max(overflow.left, overflow.right));\n        } else {\n          availableHeight = height - 2 * (yMin !== 0 || yMax !== 0 ? yMin + yMax : max(overflow.top, overflow.bottom));\n        }\n      }\n      await apply({\n        ...state,\n        availableWidth,\n        availableHeight\n      });\n      const nextDimensions = await platform.getDimensions(elements.floating);\n      if (width !== nextDimensions.width || height !== nextDimensions.height) {\n        return {\n          reset: {\n            rects: true\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\nexport { arrow, autoPlacement, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, shift, size };\n", "function hasWindow() {\n  return typeof window !== 'undefined';\n}\nfunction getNodeName(node) {\n  if (isNode(node)) {\n    return (node.nodeName || '').toLowerCase();\n  }\n  // Mocked nodes in testing environments may not be instances of Node. By\n  // returning `#document` an infinite loop won't occur.\n  // https://github.com/floating-ui/floating-ui/issues/2317\n  return '#document';\n}\nfunction getWindow(node) {\n  var _node$ownerDocument;\n  return (node == null || (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;\n}\nfunction getDocumentElement(node) {\n  var _ref;\n  return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;\n}\nfunction isNode(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Node || value instanceof getWindow(value).Node;\n}\nfunction isElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Element || value instanceof getWindow(value).Element;\n}\nfunction isHTMLElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;\n}\nfunction isShadowRoot(value) {\n  if (!hasWindow() || typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;\n}\nconst invalidOverflowDisplayValues = /*#__PURE__*/new Set(['inline', 'contents']);\nfunction isOverflowElement(element) {\n  const {\n    overflow,\n    overflowX,\n    overflowY,\n    display\n  } = getComputedStyle(element);\n  return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && !invalidOverflowDisplayValues.has(display);\n}\nconst tableElements = /*#__PURE__*/new Set(['table', 'td', 'th']);\nfunction isTableElement(element) {\n  return tableElements.has(getNodeName(element));\n}\nconst topLayerSelectors = [':popover-open', ':modal'];\nfunction isTopLayer(element) {\n  return topLayerSelectors.some(selector => {\n    try {\n      return element.matches(selector);\n    } catch (_e) {\n      return false;\n    }\n  });\n}\nconst transformProperties = ['transform', 'translate', 'scale', 'rotate', 'perspective'];\nconst willChangeValues = ['transform', 'translate', 'scale', 'rotate', 'perspective', 'filter'];\nconst containValues = ['paint', 'layout', 'strict', 'content'];\nfunction isContainingBlock(elementOrCss) {\n  const webkit = isWebKit();\n  const css = isElement(elementOrCss) ? getComputedStyle(elementOrCss) : elementOrCss;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  // https://drafts.csswg.org/css-transforms-2/#individual-transforms\n  return transformProperties.some(value => css[value] ? css[value] !== 'none' : false) || (css.containerType ? css.containerType !== 'normal' : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== 'none' : false) || !webkit && (css.filter ? css.filter !== 'none' : false) || willChangeValues.some(value => (css.willChange || '').includes(value)) || containValues.some(value => (css.contain || '').includes(value));\n}\nfunction getContainingBlock(element) {\n  let currentNode = getParentNode(element);\n  while (isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    if (isContainingBlock(currentNode)) {\n      return currentNode;\n    } else if (isTopLayer(currentNode)) {\n      return null;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  return null;\n}\nfunction isWebKit() {\n  if (typeof CSS === 'undefined' || !CSS.supports) return false;\n  return CSS.supports('-webkit-backdrop-filter', 'none');\n}\nconst lastTraversableNodeNames = /*#__PURE__*/new Set(['html', 'body', '#document']);\nfunction isLastTraversableNode(node) {\n  return lastTraversableNodeNames.has(getNodeName(node));\n}\nfunction getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}\nfunction getNodeScroll(element) {\n  if (isElement(element)) {\n    return {\n      scrollLeft: element.scrollLeft,\n      scrollTop: element.scrollTop\n    };\n  }\n  return {\n    scrollLeft: element.scrollX,\n    scrollTop: element.scrollY\n  };\n}\nfunction getParentNode(node) {\n  if (getNodeName(node) === 'html') {\n    return node;\n  }\n  const result =\n  // Step into the shadow DOM of the parent of a slotted node.\n  node.assignedSlot ||\n  // DOM Element detected.\n  node.parentNode ||\n  // ShadowRoot detected.\n  isShadowRoot(node) && node.host ||\n  // Fallback.\n  getDocumentElement(node);\n  return isShadowRoot(result) ? result.host : result;\n}\nfunction getNearestOverflowAncestor(node) {\n  const parentNode = getParentNode(node);\n  if (isLastTraversableNode(parentNode)) {\n    return node.ownerDocument ? node.ownerDocument.body : node.body;\n  }\n  if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {\n    return parentNode;\n  }\n  return getNearestOverflowAncestor(parentNode);\n}\nfunction getOverflowAncestors(node, list, traverseIframes) {\n  var _node$ownerDocument2;\n  if (list === void 0) {\n    list = [];\n  }\n  if (traverseIframes === void 0) {\n    traverseIframes = true;\n  }\n  const scrollableAncestor = getNearestOverflowAncestor(node);\n  const isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);\n  const win = getWindow(scrollableAncestor);\n  if (isBody) {\n    const frameElement = getFrameElement(win);\n    return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : [], frameElement && traverseIframes ? getOverflowAncestors(frameElement) : []);\n  }\n  return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor, [], traverseIframes));\n}\nfunction getFrameElement(win) {\n  return win.parent && Object.getPrototypeOf(win.parent) ? win.frameElement : null;\n}\n\nexport { getComputedStyle, getContainingBlock, getDocumentElement, getFrameElement, getNearestOverflowAncestor, getNodeName, getNodeScroll, getOverflowAncestors, getParentNode, getWindow, isContainingBlock, isElement, isHTMLElement, isLastTraversableNode, isNode, isOverflowElement, isShadowRoot, isTableElement, isTopLayer, isWebKit };\n", "import { rectToClientRect, arrow as arrow$1, autoPlacement as autoPlacement$1, detectOverflow as detectOverflow$1, flip as flip$1, hide as hide$1, inline as inline$1, limitShift as limitShift$1, offset as offset$1, shift as shift$1, size as size$1, computePosition as computePosition$1 } from '@floating-ui/core';\nimport { round, createCoords, max, min, floor } from '@floating-ui/utils';\nimport { getComputedStyle, isHTMLElement, isElement, getWindow, isWebKit, getFrameElement, getNodeScroll, getDocumentElement, isTopLayer, getNodeName, isOverflowElement, getOverflowAncestors, getParentNode, isLastTraversableNode, isContainingBlock, isTableElement, getContainingBlock } from '@floating-ui/utils/dom';\nexport { getOverflowAncestors } from '@floating-ui/utils/dom';\n\nfunction getCssDimensions(element) {\n  const css = getComputedStyle(element);\n  // In testing environments, the `width` and `height` properties are empty\n  // strings for SVG elements, returning NaN. Fallback to `0` in this case.\n  let width = parseFloat(css.width) || 0;\n  let height = parseFloat(css.height) || 0;\n  const hasOffset = isHTMLElement(element);\n  const offsetWidth = hasOffset ? element.offsetWidth : width;\n  const offsetHeight = hasOffset ? element.offsetHeight : height;\n  const shouldFallback = round(width) !== offsetWidth || round(height) !== offsetHeight;\n  if (shouldFallback) {\n    width = offsetWidth;\n    height = offsetHeight;\n  }\n  return {\n    width,\n    height,\n    $: shouldFallback\n  };\n}\n\nfunction unwrapElement(element) {\n  return !isElement(element) ? element.contextElement : element;\n}\n\nfunction getScale(element) {\n  const domElement = unwrapElement(element);\n  if (!isHTMLElement(domElement)) {\n    return createCoords(1);\n  }\n  const rect = domElement.getBoundingClientRect();\n  const {\n    width,\n    height,\n    $\n  } = getCssDimensions(domElement);\n  let x = ($ ? round(rect.width) : rect.width) / width;\n  let y = ($ ? round(rect.height) : rect.height) / height;\n\n  // 0, NaN, or Infinity should always fallback to 1.\n\n  if (!x || !Number.isFinite(x)) {\n    x = 1;\n  }\n  if (!y || !Number.isFinite(y)) {\n    y = 1;\n  }\n  return {\n    x,\n    y\n  };\n}\n\nconst noOffsets = /*#__PURE__*/createCoords(0);\nfunction getVisualOffsets(element) {\n  const win = getWindow(element);\n  if (!isWebKit() || !win.visualViewport) {\n    return noOffsets;\n  }\n  return {\n    x: win.visualViewport.offsetLeft,\n    y: win.visualViewport.offsetTop\n  };\n}\nfunction shouldAddVisualOffsets(element, isFixed, floatingOffsetParent) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n  if (!floatingOffsetParent || isFixed && floatingOffsetParent !== getWindow(element)) {\n    return false;\n  }\n  return isFixed;\n}\n\nfunction getBoundingClientRect(element, includeScale, isFixedStrategy, offsetParent) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n  const clientRect = element.getBoundingClientRect();\n  const domElement = unwrapElement(element);\n  let scale = createCoords(1);\n  if (includeScale) {\n    if (offsetParent) {\n      if (isElement(offsetParent)) {\n        scale = getScale(offsetParent);\n      }\n    } else {\n      scale = getScale(element);\n    }\n  }\n  const visualOffsets = shouldAddVisualOffsets(domElement, isFixedStrategy, offsetParent) ? getVisualOffsets(domElement) : createCoords(0);\n  let x = (clientRect.left + visualOffsets.x) / scale.x;\n  let y = (clientRect.top + visualOffsets.y) / scale.y;\n  let width = clientRect.width / scale.x;\n  let height = clientRect.height / scale.y;\n  if (domElement) {\n    const win = getWindow(domElement);\n    const offsetWin = offsetParent && isElement(offsetParent) ? getWindow(offsetParent) : offsetParent;\n    let currentWin = win;\n    let currentIFrame = getFrameElement(currentWin);\n    while (currentIFrame && offsetParent && offsetWin !== currentWin) {\n      const iframeScale = getScale(currentIFrame);\n      const iframeRect = currentIFrame.getBoundingClientRect();\n      const css = getComputedStyle(currentIFrame);\n      const left = iframeRect.left + (currentIFrame.clientLeft + parseFloat(css.paddingLeft)) * iframeScale.x;\n      const top = iframeRect.top + (currentIFrame.clientTop + parseFloat(css.paddingTop)) * iframeScale.y;\n      x *= iframeScale.x;\n      y *= iframeScale.y;\n      width *= iframeScale.x;\n      height *= iframeScale.y;\n      x += left;\n      y += top;\n      currentWin = getWindow(currentIFrame);\n      currentIFrame = getFrameElement(currentWin);\n    }\n  }\n  return rectToClientRect({\n    width,\n    height,\n    x,\n    y\n  });\n}\n\n// If <html> has a CSS width greater than the viewport, then this will be\n// incorrect for RTL.\nfunction getWindowScrollBarX(element, rect) {\n  const leftScroll = getNodeScroll(element).scrollLeft;\n  if (!rect) {\n    return getBoundingClientRect(getDocumentElement(element)).left + leftScroll;\n  }\n  return rect.left + leftScroll;\n}\n\nfunction getHTMLOffset(documentElement, scroll, ignoreScrollbarX) {\n  if (ignoreScrollbarX === void 0) {\n    ignoreScrollbarX = false;\n  }\n  const htmlRect = documentElement.getBoundingClientRect();\n  const x = htmlRect.left + scroll.scrollLeft - (ignoreScrollbarX ? 0 :\n  // RTL <body> scrollbar.\n  getWindowScrollBarX(documentElement, htmlRect));\n  const y = htmlRect.top + scroll.scrollTop;\n  return {\n    x,\n    y\n  };\n}\n\nfunction convertOffsetParentRelativeRectToViewportRelativeRect(_ref) {\n  let {\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  } = _ref;\n  const isFixed = strategy === 'fixed';\n  const documentElement = getDocumentElement(offsetParent);\n  const topLayer = elements ? isTopLayer(elements.floating) : false;\n  if (offsetParent === documentElement || topLayer && isFixed) {\n    return rect;\n  }\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  let scale = createCoords(1);\n  const offsets = createCoords(0);\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isHTMLElement(offsetParent)) {\n      const offsetRect = getBoundingClientRect(offsetParent);\n      scale = getScale(offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    }\n  }\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll, true) : createCoords(0);\n  return {\n    width: rect.width * scale.x,\n    height: rect.height * scale.y,\n    x: rect.x * scale.x - scroll.scrollLeft * scale.x + offsets.x + htmlOffset.x,\n    y: rect.y * scale.y - scroll.scrollTop * scale.y + offsets.y + htmlOffset.y\n  };\n}\n\nfunction getClientRects(element) {\n  return Array.from(element.getClientRects());\n}\n\n// Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable.\nfunction getDocumentRect(element) {\n  const html = getDocumentElement(element);\n  const scroll = getNodeScroll(element);\n  const body = element.ownerDocument.body;\n  const width = max(html.scrollWidth, html.clientWidth, body.scrollWidth, body.clientWidth);\n  const height = max(html.scrollHeight, html.clientHeight, body.scrollHeight, body.clientHeight);\n  let x = -scroll.scrollLeft + getWindowScrollBarX(element);\n  const y = -scroll.scrollTop;\n  if (getComputedStyle(body).direction === 'rtl') {\n    x += max(html.clientWidth, body.clientWidth) - width;\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\nfunction getViewportRect(element, strategy) {\n  const win = getWindow(element);\n  const html = getDocumentElement(element);\n  const visualViewport = win.visualViewport;\n  let width = html.clientWidth;\n  let height = html.clientHeight;\n  let x = 0;\n  let y = 0;\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    const visualViewportBased = isWebKit();\n    if (!visualViewportBased || visualViewportBased && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\nconst absoluteOrFixed = /*#__PURE__*/new Set(['absolute', 'fixed']);\n// Returns the inner client rect, subtracting scrollbars if present.\nfunction getInnerBoundingClientRect(element, strategy) {\n  const clientRect = getBoundingClientRect(element, true, strategy === 'fixed');\n  const top = clientRect.top + element.clientTop;\n  const left = clientRect.left + element.clientLeft;\n  const scale = isHTMLElement(element) ? getScale(element) : createCoords(1);\n  const width = element.clientWidth * scale.x;\n  const height = element.clientHeight * scale.y;\n  const x = left * scale.x;\n  const y = top * scale.y;\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\nfunction getClientRectFromClippingAncestor(element, clippingAncestor, strategy) {\n  let rect;\n  if (clippingAncestor === 'viewport') {\n    rect = getViewportRect(element, strategy);\n  } else if (clippingAncestor === 'document') {\n    rect = getDocumentRect(getDocumentElement(element));\n  } else if (isElement(clippingAncestor)) {\n    rect = getInnerBoundingClientRect(clippingAncestor, strategy);\n  } else {\n    const visualOffsets = getVisualOffsets(element);\n    rect = {\n      x: clippingAncestor.x - visualOffsets.x,\n      y: clippingAncestor.y - visualOffsets.y,\n      width: clippingAncestor.width,\n      height: clippingAncestor.height\n    };\n  }\n  return rectToClientRect(rect);\n}\nfunction hasFixedPositionAncestor(element, stopNode) {\n  const parentNode = getParentNode(element);\n  if (parentNode === stopNode || !isElement(parentNode) || isLastTraversableNode(parentNode)) {\n    return false;\n  }\n  return getComputedStyle(parentNode).position === 'fixed' || hasFixedPositionAncestor(parentNode, stopNode);\n}\n\n// A \"clipping ancestor\" is an `overflow` element with the characteristic of\n// clipping (or hiding) child elements. This returns all clipping ancestors\n// of the given element up the tree.\nfunction getClippingElementAncestors(element, cache) {\n  const cachedResult = cache.get(element);\n  if (cachedResult) {\n    return cachedResult;\n  }\n  let result = getOverflowAncestors(element, [], false).filter(el => isElement(el) && getNodeName(el) !== 'body');\n  let currentContainingBlockComputedStyle = null;\n  const elementIsFixed = getComputedStyle(element).position === 'fixed';\n  let currentNode = elementIsFixed ? getParentNode(element) : element;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  while (isElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    const computedStyle = getComputedStyle(currentNode);\n    const currentNodeIsContaining = isContainingBlock(currentNode);\n    if (!currentNodeIsContaining && computedStyle.position === 'fixed') {\n      currentContainingBlockComputedStyle = null;\n    }\n    const shouldDropCurrentNode = elementIsFixed ? !currentNodeIsContaining && !currentContainingBlockComputedStyle : !currentNodeIsContaining && computedStyle.position === 'static' && !!currentContainingBlockComputedStyle && absoluteOrFixed.has(currentContainingBlockComputedStyle.position) || isOverflowElement(currentNode) && !currentNodeIsContaining && hasFixedPositionAncestor(element, currentNode);\n    if (shouldDropCurrentNode) {\n      // Drop non-containing blocks.\n      result = result.filter(ancestor => ancestor !== currentNode);\n    } else {\n      // Record last containing block for next iteration.\n      currentContainingBlockComputedStyle = computedStyle;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  cache.set(element, result);\n  return result;\n}\n\n// Gets the maximum area that the element is visible in due to any number of\n// clipping ancestors.\nfunction getClippingRect(_ref) {\n  let {\n    element,\n    boundary,\n    rootBoundary,\n    strategy\n  } = _ref;\n  const elementClippingAncestors = boundary === 'clippingAncestors' ? isTopLayer(element) ? [] : getClippingElementAncestors(element, this._c) : [].concat(boundary);\n  const clippingAncestors = [...elementClippingAncestors, rootBoundary];\n  const firstClippingAncestor = clippingAncestors[0];\n  const clippingRect = clippingAncestors.reduce((accRect, clippingAncestor) => {\n    const rect = getClientRectFromClippingAncestor(element, clippingAncestor, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromClippingAncestor(element, firstClippingAncestor, strategy));\n  return {\n    width: clippingRect.right - clippingRect.left,\n    height: clippingRect.bottom - clippingRect.top,\n    x: clippingRect.left,\n    y: clippingRect.top\n  };\n}\n\nfunction getDimensions(element) {\n  const {\n    width,\n    height\n  } = getCssDimensions(element);\n  return {\n    width,\n    height\n  };\n}\n\nfunction getRectRelativeToOffsetParent(element, offsetParent, strategy) {\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  const documentElement = getDocumentElement(offsetParent);\n  const isFixed = strategy === 'fixed';\n  const rect = getBoundingClientRect(element, true, isFixed, offsetParent);\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  const offsets = createCoords(0);\n\n  // If the <body> scrollbar appears on the left (e.g. RTL systems). Use\n  // Firefox with layout.scrollbar.side = 3 in about:config to test this.\n  function setLeftRTLScrollbarOffset() {\n    offsets.x = getWindowScrollBarX(documentElement);\n  }\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isOffsetParentAnElement) {\n      const offsetRect = getBoundingClientRect(offsetParent, true, isFixed, offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    } else if (documentElement) {\n      setLeftRTLScrollbarOffset();\n    }\n  }\n  if (isFixed && !isOffsetParentAnElement && documentElement) {\n    setLeftRTLScrollbarOffset();\n  }\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll) : createCoords(0);\n  const x = rect.left + scroll.scrollLeft - offsets.x - htmlOffset.x;\n  const y = rect.top + scroll.scrollTop - offsets.y - htmlOffset.y;\n  return {\n    x,\n    y,\n    width: rect.width,\n    height: rect.height\n  };\n}\n\nfunction isStaticPositioned(element) {\n  return getComputedStyle(element).position === 'static';\n}\n\nfunction getTrueOffsetParent(element, polyfill) {\n  if (!isHTMLElement(element) || getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n  if (polyfill) {\n    return polyfill(element);\n  }\n  let rawOffsetParent = element.offsetParent;\n\n  // Firefox returns the <html> element as the offsetParent if it's non-static,\n  // while Chrome and Safari return the <body> element. The <body> element must\n  // be used to perform the correct calculations even if the <html> element is\n  // non-static.\n  if (getDocumentElement(element) === rawOffsetParent) {\n    rawOffsetParent = rawOffsetParent.ownerDocument.body;\n  }\n  return rawOffsetParent;\n}\n\n// Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\nfunction getOffsetParent(element, polyfill) {\n  const win = getWindow(element);\n  if (isTopLayer(element)) {\n    return win;\n  }\n  if (!isHTMLElement(element)) {\n    let svgOffsetParent = getParentNode(element);\n    while (svgOffsetParent && !isLastTraversableNode(svgOffsetParent)) {\n      if (isElement(svgOffsetParent) && !isStaticPositioned(svgOffsetParent)) {\n        return svgOffsetParent;\n      }\n      svgOffsetParent = getParentNode(svgOffsetParent);\n    }\n    return win;\n  }\n  let offsetParent = getTrueOffsetParent(element, polyfill);\n  while (offsetParent && isTableElement(offsetParent) && isStaticPositioned(offsetParent)) {\n    offsetParent = getTrueOffsetParent(offsetParent, polyfill);\n  }\n  if (offsetParent && isLastTraversableNode(offsetParent) && isStaticPositioned(offsetParent) && !isContainingBlock(offsetParent)) {\n    return win;\n  }\n  return offsetParent || getContainingBlock(element) || win;\n}\n\nconst getElementRects = async function (data) {\n  const getOffsetParentFn = this.getOffsetParent || getOffsetParent;\n  const getDimensionsFn = this.getDimensions;\n  const floatingDimensions = await getDimensionsFn(data.floating);\n  return {\n    reference: getRectRelativeToOffsetParent(data.reference, await getOffsetParentFn(data.floating), data.strategy),\n    floating: {\n      x: 0,\n      y: 0,\n      width: floatingDimensions.width,\n      height: floatingDimensions.height\n    }\n  };\n};\n\nfunction isRTL(element) {\n  return getComputedStyle(element).direction === 'rtl';\n}\n\nconst platform = {\n  convertOffsetParentRelativeRectToViewportRelativeRect,\n  getDocumentElement,\n  getClippingRect,\n  getOffsetParent,\n  getElementRects,\n  getClientRects,\n  getDimensions,\n  getScale,\n  isElement,\n  isRTL\n};\n\nfunction rectsAreEqual(a, b) {\n  return a.x === b.x && a.y === b.y && a.width === b.width && a.height === b.height;\n}\n\n// https://samthor.au/2021/observing-dom/\nfunction observeMove(element, onMove) {\n  let io = null;\n  let timeoutId;\n  const root = getDocumentElement(element);\n  function cleanup() {\n    var _io;\n    clearTimeout(timeoutId);\n    (_io = io) == null || _io.disconnect();\n    io = null;\n  }\n  function refresh(skip, threshold) {\n    if (skip === void 0) {\n      skip = false;\n    }\n    if (threshold === void 0) {\n      threshold = 1;\n    }\n    cleanup();\n    const elementRectForRootMargin = element.getBoundingClientRect();\n    const {\n      left,\n      top,\n      width,\n      height\n    } = elementRectForRootMargin;\n    if (!skip) {\n      onMove();\n    }\n    if (!width || !height) {\n      return;\n    }\n    const insetTop = floor(top);\n    const insetRight = floor(root.clientWidth - (left + width));\n    const insetBottom = floor(root.clientHeight - (top + height));\n    const insetLeft = floor(left);\n    const rootMargin = -insetTop + \"px \" + -insetRight + \"px \" + -insetBottom + \"px \" + -insetLeft + \"px\";\n    const options = {\n      rootMargin,\n      threshold: max(0, min(1, threshold)) || 1\n    };\n    let isFirstUpdate = true;\n    function handleObserve(entries) {\n      const ratio = entries[0].intersectionRatio;\n      if (ratio !== threshold) {\n        if (!isFirstUpdate) {\n          return refresh();\n        }\n        if (!ratio) {\n          // If the reference is clipped, the ratio is 0. Throttle the refresh\n          // to prevent an infinite loop of updates.\n          timeoutId = setTimeout(() => {\n            refresh(false, 1e-7);\n          }, 1000);\n        } else {\n          refresh(false, ratio);\n        }\n      }\n      if (ratio === 1 && !rectsAreEqual(elementRectForRootMargin, element.getBoundingClientRect())) {\n        // It's possible that even though the ratio is reported as 1, the\n        // element is not actually fully within the IntersectionObserver's root\n        // area anymore. This can happen under performance constraints. This may\n        // be a bug in the browser's IntersectionObserver implementation. To\n        // work around this, we compare the element's bounding rect now with\n        // what it was at the time we created the IntersectionObserver. If they\n        // are not equal then the element moved, so we refresh.\n        refresh();\n      }\n      isFirstUpdate = false;\n    }\n\n    // Older browsers don't support a `document` as the root and will throw an\n    // error.\n    try {\n      io = new IntersectionObserver(handleObserve, {\n        ...options,\n        // Handle <iframe>s\n        root: root.ownerDocument\n      });\n    } catch (_e) {\n      io = new IntersectionObserver(handleObserve, options);\n    }\n    io.observe(element);\n  }\n  refresh(true);\n  return cleanup;\n}\n\n/**\n * Automatically updates the position of the floating element when necessary.\n * Should only be called when the floating element is mounted on the DOM or\n * visible on the screen.\n * @returns cleanup function that should be invoked when the floating element is\n * removed from the DOM or hidden from the screen.\n * @see https://floating-ui.com/docs/autoUpdate\n */\nfunction autoUpdate(reference, floating, update, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    ancestorScroll = true,\n    ancestorResize = true,\n    elementResize = typeof ResizeObserver === 'function',\n    layoutShift = typeof IntersectionObserver === 'function',\n    animationFrame = false\n  } = options;\n  const referenceEl = unwrapElement(reference);\n  const ancestors = ancestorScroll || ancestorResize ? [...(referenceEl ? getOverflowAncestors(referenceEl) : []), ...getOverflowAncestors(floating)] : [];\n  ancestors.forEach(ancestor => {\n    ancestorScroll && ancestor.addEventListener('scroll', update, {\n      passive: true\n    });\n    ancestorResize && ancestor.addEventListener('resize', update);\n  });\n  const cleanupIo = referenceEl && layoutShift ? observeMove(referenceEl, update) : null;\n  let reobserveFrame = -1;\n  let resizeObserver = null;\n  if (elementResize) {\n    resizeObserver = new ResizeObserver(_ref => {\n      let [firstEntry] = _ref;\n      if (firstEntry && firstEntry.target === referenceEl && resizeObserver) {\n        // Prevent update loops when using the `size` middleware.\n        // https://github.com/floating-ui/floating-ui/issues/1740\n        resizeObserver.unobserve(floating);\n        cancelAnimationFrame(reobserveFrame);\n        reobserveFrame = requestAnimationFrame(() => {\n          var _resizeObserver;\n          (_resizeObserver = resizeObserver) == null || _resizeObserver.observe(floating);\n        });\n      }\n      update();\n    });\n    if (referenceEl && !animationFrame) {\n      resizeObserver.observe(referenceEl);\n    }\n    resizeObserver.observe(floating);\n  }\n  let frameId;\n  let prevRefRect = animationFrame ? getBoundingClientRect(reference) : null;\n  if (animationFrame) {\n    frameLoop();\n  }\n  function frameLoop() {\n    const nextRefRect = getBoundingClientRect(reference);\n    if (prevRefRect && !rectsAreEqual(prevRefRect, nextRefRect)) {\n      update();\n    }\n    prevRefRect = nextRefRect;\n    frameId = requestAnimationFrame(frameLoop);\n  }\n  update();\n  return () => {\n    var _resizeObserver2;\n    ancestors.forEach(ancestor => {\n      ancestorScroll && ancestor.removeEventListener('scroll', update);\n      ancestorResize && ancestor.removeEventListener('resize', update);\n    });\n    cleanupIo == null || cleanupIo();\n    (_resizeObserver2 = resizeObserver) == null || _resizeObserver2.disconnect();\n    resizeObserver = null;\n    if (animationFrame) {\n      cancelAnimationFrame(frameId);\n    }\n  };\n}\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nconst detectOverflow = detectOverflow$1;\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = offset$1;\n\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = autoPlacement$1;\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = shift$1;\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = flip$1;\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = size$1;\n\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = hide$1;\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = arrow$1;\n\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = inline$1;\n\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = limitShift$1;\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n */\nconst computePosition = (reference, floating, options) => {\n  // This caches the expensive `getClippingElementAncestors` function so that\n  // multiple lifecycle resets re-use the same result. It only lives for a\n  // single call. If other functions become expensive, we can add them as well.\n  const cache = new Map();\n  const mergedOptions = {\n    platform,\n    ...options\n  };\n  const platformWithCache = {\n    ...mergedOptions.platform,\n    _c: cache\n  };\n  return computePosition$1(reference, floating, {\n    ...mergedOptions,\n    platform: platformWithCache\n  });\n};\n\nexport { arrow, autoPlacement, autoUpdate, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, platform, shift, size };\n"], "names": ["Check", "createLucideIcon", "points", "key", "index", "document", "useLayoutEffect", "deepEqual", "a", "b", "toString", "length", "i", "keys", "Array", "isArray", "Object", "hasOwnProperty", "call", "$$typeof", "getDPR", "element", "window", "ownerDocument", "defaultView", "devicePixelRatio", "roundByDPR", "value", "dpr", "Math", "round", "useLatestRef", "ref", "React", "current", "useFloating", "options", "placement", "strategy", "middleware", "platform", "elements", "reference", "externalReference", "floating", "externalFloating", "transform", "whileElementsMounted", "open", "data", "setData", "x", "y", "middlewareData", "isPositioned", "latestMiddleware", "setLatestMiddleware", "_reference", "_setReference", "_floating", "_setFloating", "setReference", "node", "referenceRef", "setFloating", "floatingRef", "referenceEl", "floatingEl", "dataRef", "hasWhileElementsMounted", "whileElementsMountedRef", "platformRef", "openRef", "update", "config", "computePosition", "then", "fullData", "isMountedRef", "ReactDOM", "refs", "floatingStyles", "initialStyles", "position", "left", "top", "<PERSON><PERSON><PERSON><PERSON>", "arrow$1", "name", "fn", "state", "padding", "arrow$2", "offset", "deps", "offset$1", "shift", "shift$1", "limitShift", "limitShift$1", "flip", "flip$1", "size", "size$1", "hide", "hide$1", "arrow", "sides", "min", "max", "floor", "createCoords", "v", "oppositeSideMap", "right", "bottom", "oppositeAlignmentMap", "start", "end", "clamp", "evaluate", "param", "getSide", "split", "getAlignment", "getOppositeAxis", "axis", "getAxisLength", "yAxisSides", "Set", "getSideAxis", "has", "getAlignmentAxis", "getOppositeAlignmentPlacement", "replace", "alignment", "lrPlacement", "rlPlacement", "tbPlacement", "btPlacement", "getOppositeAxisPlacements", "flipAlignment", "direction", "rtl", "list", "side", "isStart", "getSideList", "map", "concat", "getOppositePlacement", "getPaddingObject", "expandPaddingObject", "rectToClientRect", "rect", "width", "height", "computeCoordsFromPlacement", "_ref", "sideAxis", "alignmentAxis", "align<PERSON><PERSON><PERSON>", "isVertical", "commonX", "commonY", "commonAlign", "coords", "async", "detectOverflow", "_await$platform$isEle", "rects", "boundary", "rootBoundary", "elementContext", "altBoundary", "paddingObject", "clippingClientRect", "getClippingRect", "isElement", "contextElement", "getDocumentElement", "offsetParent", "getOffsetParent", "offsetScale", "getScale", "elementClientRect", "convertOffsetParentRelativeRectToViewportRelativeRect", "getSideOffsets", "overflow", "isAnySideFullyClipped", "some", "originSides", "hasW<PERSON>ow", "getNodeName", "isNode", "nodeName", "toLowerCase", "getWindow", "_node$ownerDocument", "documentElement", "Node", "Element", "isHTMLElement", "HTMLElement", "isShadowRoot", "ShadowRoot", "invalidOverflowDisplayValues", "isOverflowElement", "overflowX", "overflowY", "display", "getComputedStyle", "test", "tableElements", "isTableElement", "topLayerSelectors", "isTop<PERSON><PERSON>er", "selector", "matches", "_e", "transformProperties", "will<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "containValues", "isContainingBlock", "elementOrCss", "webkit", "isWebKit", "css", "containerType", "<PERSON><PERSON>ilter", "filter", "includes", "contain", "CSS", "supports", "lastTraversableNodeNames", "isLastTraversableNode", "getNodeScroll", "scrollLeft", "scrollTop", "scrollX", "scrollY", "getParentNode", "result", "assignedSlot", "parentNode", "host", "getNearestOverflowAncestor", "body", "getOverflowAncestors", "traverseIframes", "_node$ownerDocument2", "scrollableAncestor", "isBody", "win", "frameElement", "getFrameElement", "visualViewport", "parent", "getPrototypeOf", "getCssDimensions", "parseFloat", "hasOffset", "offsetWidth", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON>", "$", "unwrapElement", "dom<PERSON>lement", "getBoundingClientRect", "Number", "isFinite", "noOffsets", "getVisualOffsets", "offsetLeft", "offsetTop", "includeScale", "isFixedStrategy", "clientRect", "scale", "visualOffsets", "isFixed", "floatingOffsetParent", "shouldAddVisualOffsets", "offsetWin", "currentWin", "currentIFrame", "iframeScale", "iframeRect", "clientLeft", "paddingLeft", "clientTop", "paddingTop", "getWindowScrollBarX", "leftScroll", "getHTMLOffset", "scroll", "ignoreScrollbarX", "htmlRect", "absoluteOrFixed", "getClientRectFromClippingAncestor", "clippingAncestor", "html", "clientWidth", "clientHeight", "visualViewportBased", "getViewportRect", "scrollWidth", "scrollHeight", "getDocumentRect", "getInnerBoundingClientRect", "hasFixedPositionAncestor", "stopNode", "getRectRelativeToOffsetParent", "isOffsetParentAnElement", "offsets", "setLeftRTLScrollbarOffset", "offsetRect", "htmlOffset", "isStaticPositioned", "getTrueOffsetParent", "polyfill", "rawOffsetParent", "svgOffsetParent", "currentNode", "getContainingBlock", "topLayer", "clippingAncestors", "cache", "cachedResult", "get", "el", "currentContainingBlockComputedStyle", "elementIsFixed", "computedStyle", "currentNodeIsContaining", "ancestor", "set", "getClippingElementAncestors", "this", "_c", "firstClippingAncestor", "clippingRect", "reduce", "accRect", "getElementRects", "getOffsetParentFn", "getDimensionsFn", "getDimensions", "floatingDimensions", "getClientRects", "from", "isRTL", "rectsAreEqual", "autoUpdate", "ancestorScroll", "ancestorResize", "elementResize", "ResizeObserver", "layoutShift", "IntersectionObserver", "animationFrame", "ancestors", "for<PERSON>ach", "addEventListener", "passive", "cleanupIo", "onMove", "timeoutId", "io", "root", "cleanup", "_io", "clearTimeout", "disconnect", "refresh", "skip", "threshold", "elementRectForRootMargin", "rootMargin", "isFirstUpdate", "handleObserve", "entries", "ratio", "intersectionRatio", "setTimeout", "observe", "<PERSON><PERSON><PERSON>", "frameId", "reobserveFrame", "resizeObserver", "firstEntry", "target", "unobserve", "cancelAnimationFrame", "requestAnimationFrame", "_resizeObserver", "prevRefRect", "frameLoop", "nextRefRect", "_resizeObserver2", "removeEventListener", "_middlewareData$offse", "_middlewareData$arrow", "diffCoords", "mainAxisMulti", "crossAxisMulti", "rawValue", "mainAxis", "crossAxis", "convertValueToCoords", "alignmentOffset", "checkMainAxis", "checkCrossAxis", "limiter", "detectOverflowOptions", "mainAxisCoord", "crossAxisCoord", "maxSide", "limitedCoords", "enabled", "_middlewareData$flip", "initialPlacement", "fallbackPlacements", "specifiedFallbackPlacements", "fallbackStrategy", "fallbackAxisSideDirection", "initialSideAxis", "isBasePlacement", "oppositePlacement", "getExpandedPlacements", "hasFallbackAxisSideDirection", "push", "placements", "overflows", "overflowsData", "mainAlignmentSide", "getAlignmentSides", "every", "_middlewareData$flip2", "_overflowsData$filter", "nextIndex", "nextPlacement", "d", "reset", "resetPlacement", "sort", "_overflowsData$filter2", "currentSideAxis", "acc", "_state$middlewareData", "_state$middlewareData2", "apply", "isYAxis", "heightSide", "widthSide", "maximumClippingHeight", "maximumClippingWidth", "overflowAvailableHeight", "overflowAvailableWidth", "noShift", "availableHeight", "availableWidth", "xMin", "xMax", "yMin", "yMax", "nextDimensions", "referenceHiddenOffsets", "referenceHidden", "escapedOffsets", "escaped", "arrowDimensions", "minProp", "maxProp", "clientProp", "endDiff", "startDiff", "arrowOffsetParent", "clientSize", "centerToReference", "largestPossiblePadding", "minPadding", "maxPadding", "min$1", "center", "shouldAddOffset", "centerOffset", "rawOffset", "computedOffset", "len", "limitMin", "limitMax", "_middlewareData$offse2", "isOriginSide", "Map", "mergedOptions", "platformWithCache", "validMiddleware", "Boolean", "statefulPlacement", "resetCount", "nextX", "nextY", "computePosition$1"], "sourceRoot": ""}