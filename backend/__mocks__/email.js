// Mock email configuration for testing
const nodemailer = require('nodemailer');

const mockTransporter = {
  verify: jest.fn((callback) => callback(null, true)),
  sendMail: jest.fn().mockResolvedValue({ messageId: 'test-message-id' }),
  on: jest.fn(),
  close: jest.fn()
};

const mockCreateTransport = jest.fn().mockReturnValue(mockTransporter);

// Mock nodemailer
jest.mock('nodemailer', () => ({
  createTransport: mockCreateTransport
}));

module.exports = {
  transporter: mockTransporter,
  sendEmail: jest.fn().mockResolvedValue({ messageId: 'test-message-id' }),
  sendVerificationEmail: jest.fn().mockResolvedValue({ messageId: 'test-message-id' }),
  sendPasswordResetEmail: jest.fn().mockResolvedValue({ messageId: 'test-message-id' }),
  sendPasswordChangedEmail: jest.fn().mockResolvedValue({ messageId: 'test-message-id' }),
  mockTransporter,
  mockCreateTransport
};
