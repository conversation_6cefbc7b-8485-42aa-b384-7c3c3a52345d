const express = require('express');
const router = express.Router();
const settlementController = require('../controllers/settlementController');
// Authentication middleware removed
const validate = require('../middleware/validate');
const settlementValidation = require('../middleware/validators/settlementValidator');

/**
 * @swagger
 * /api/settlements:
 *   post:
 *     tags: [Settlements]
 *     summary: Create a new settlement
 *     description: Only administrators can create new settlements.
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Settlement'
 *     responses:
 *       201:
 *         description: Settlement created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Settlement'
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - requires admin role
 */
router.post(
  '/',
  validate(settlementValidation.createSettlement),
  settlementController.createSettlement
);

/**
 * @swagger
 * /api/settlements:
 *   get:
 *     tags: [Settlements]
 *     summary: Get all settlements
 *     description: Returns settlements filtered by the user's role and access level.
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, processing, completed, failed]
 *         description: Filter by settlement status
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter settlements created after this date (YYYY-MM-DD)
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter settlements created before this date (YYYY-MM-DD)
 *     responses:
 *       200:
 *         description: List of settlements
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Settlement'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - requires admin or finance role
 */
router.get(
  '/', 
  validate(settlementValidation.getSettlements),
  settlementController.getSettlements
);

/**
 * @swagger
 * /api/settlements/{id}:
 *   get:
 *     tags: [Settlements]
 *     summary: Get a single settlement
 *     description: Returns a single settlement with access control.
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Settlement ID
 *     responses:
 *       200:
 *         description: Settlement data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Settlement'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - insufficient permissions
 *       404:
 *         description: Settlement not found
 */
router.get(
  '/:id', 
  validate(settlementValidation.getSettlement),
  settlementController.checkSettlementAccess,
  settlementController.getSettlement
);

/**
 * @swagger
 * /api/settlements/{id}/process:
 *   post:
 *     tags: [Settlements]
 *     summary: Process a settlement
 *     description: Initiate processing of a settlement (admin only).
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Settlement ID to process
 *     responses:
 *       200:
 *         description: Settlement processing initiated
 *       400:
 *         description: Invalid request or settlement cannot be processed
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - requires admin role
 *       404:
 *         description: Settlement not found
 */
router.post(
  '/:id/process',
  validate(settlementValidation.processSettlement),
  settlementController.checkSettlementAccess,
  settlementController.processSettlement
);

/**
 * @swagger
 * /api/commissions:
 *   post:
 *     tags: [Commissions]
 *     summary: Create a commission structure
 *     description: Create a new commission structure (admin only).
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Commission'
 *     responses:
 *       201:
 *         description: Commission structure created successfully
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - requires admin role
 */
router.post(
  '/commissions',
  validate(settlementValidation.createCommission),
  settlementController.createCommissionStructure
);

/**
 * @swagger
 * /api/commissions:
 *   get:
 *     tags: [Commissions]
 *     summary: Get all commission structures
 *     description: Returns all commission structures (admin and finance roles only).
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of commission structures
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Commission'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - requires admin or finance role
 */
router.get(
  '/commissions',
  validate(settlementValidation.getCommissions),
  settlementController.getCommissionStructures
);

/**
 * @swagger
 * /api/commissions/calculate:
 *   post:
 *     tags: [Commissions]
 *     summary: Calculate commission for an amount
 *     description: Calculate the commission for a given amount based on active commission structures.
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [amount, currency]
 *             properties:
 *               amount:
 *                 type: number
 *                 description: The amount to calculate commission for
 *               currency:
 *                 type: string
 *                 description: Currency code (e.g., USD, EUR)
 *     responses:
 *       200:
 *         description: Commission calculated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 amount:
 *                   type: number
 *                   description: Original amount
 *                 commission:
 *                   type: number
 *                   description: Calculated commission
 *                 total:
 *                   type: number
 *                   description: Total amount including commission
 *                 currency:
 *                   type: string
 *                   description: Currency code
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - requires admin or finance role
 */
router.post(
  '/commissions/calculate',
  validate(settlementValidation.calculateCommission),
  settlementController.calculateCommission
);

module.exports = router;
