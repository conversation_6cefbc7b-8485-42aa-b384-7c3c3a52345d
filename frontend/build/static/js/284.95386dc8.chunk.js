"use strict";(self.webpackChunkpayment_gateway_frontend=self.webpackChunkpayment_gateway_frontend||[]).push([[284],{284:(e,s,a)=>{a.r(s),a.d(s,{default:()=>g});var t=a(5043),r=a(3216),d=a(7826),i=a(6742),n=a(9772),l=a(9954),c=a(2248),o=a(5320),m=a(6879),u=a(5722),h=a(6713),x=a(2699),p=a(6213),f=a(579);const g=()=>{const{merchantId:e}=(0,r.g)(),{processPayment:s,loading:a}=(0,d.u)(),{toast:g}=(0,m.dj)(),[j,y]=(0,t.useState)(null),[v,b]=(0,t.useState)({amount:"",currency:"USD",paymentMethod:"card",cardNumber:"",expiryDate:"",cvv:"",cardholderName:"",email:"",billingAddress:{street:"",city:"",state:"",zipCode:"",country:"US"}});(0,t.useEffect)(()=>{N()},[e]);const N=async()=>{try{const s=await p.A.get(`/api/merchants/${e}`);y(s.data.merchant)}catch(s){console.error("Failed to fetch merchant info:",s)}},w=(e,s)=>{if(e.startsWith("billingAddress.")){const a=e.split(".")[1];b(e=>({...e,billingAddress:{...e.billingAddress,[a]:s}}))}else b(a=>({...a,[e]:s}))};return j?(0,f.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4",children:(0,f.jsxs)("div",{className:"max-w-2xl mx-auto",children:[(0,f.jsxs)("div",{className:"text-center mb-8",children:[(0,f.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Secure Payment"}),(0,f.jsxs)("p",{className:"text-gray-600",children:["Complete your payment to ",j.businessName]})]}),(0,f.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,f.jsx)("div",{className:"lg:col-span-2",children:(0,f.jsxs)(i.Zp,{children:[(0,f.jsxs)(i.aR,{children:[(0,f.jsxs)(i.ZB,{className:"flex items-center",children:[(0,f.jsx)(u.A,{className:"h-5 w-5 mr-2"}),"Payment Details"]}),(0,f.jsx)(i.BT,{children:"Enter your payment information securely"})]}),(0,f.jsx)(i.Wu,{children:(0,f.jsxs)("form",{onSubmit:async a=>{a.preventDefault();try{const a=await s({...v,merchantId:e,amount:Number.parseFloat(v.amount)});if(!a.success)throw new Error(a.message);g({title:"Payment Successful",description:`Transaction ID: ${a.transactionId}`})}catch(t){g({title:"Payment Failed",description:t.message||"An error occurred during payment processing",variant:"destructive"})}},className:"space-y-6",children:[(0,f.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,f.jsxs)("div",{children:[(0,f.jsx)(c.J,{htmlFor:"amount",children:"Amount"}),(0,f.jsx)(l.p,{id:"amount",type:"number",step:"0.01",placeholder:"0.00",value:v.amount,onChange:e=>w("amount",e.target.value),required:!0})]}),(0,f.jsxs)("div",{children:[(0,f.jsx)(c.J,{htmlFor:"currency",children:"Currency"}),(0,f.jsxs)(o.l6,{value:v.currency,onValueChange:e=>w("currency",e),children:[(0,f.jsx)(o.bq,{children:(0,f.jsx)(o.yv,{})}),(0,f.jsxs)(o.gC,{children:[(0,f.jsx)(o.eb,{value:"USD",children:"USD"}),(0,f.jsx)(o.eb,{value:"EUR",children:"EUR"}),(0,f.jsx)(o.eb,{value:"GBP",children:"GBP"})]})]})]})]}),(0,f.jsxs)("div",{children:[(0,f.jsx)(c.J,{htmlFor:"email",children:"Email Address"}),(0,f.jsx)(l.p,{id:"email",type:"email",placeholder:"<EMAIL>",value:v.email,onChange:e=>w("email",e.target.value),required:!0})]}),(0,f.jsxs)("div",{children:[(0,f.jsx)(c.J,{htmlFor:"cardholderName",children:"Cardholder Name"}),(0,f.jsx)(l.p,{id:"cardholderName",placeholder:"John Doe",value:v.cardholderName,onChange:e=>w("cardholderName",e.target.value),required:!0})]}),(0,f.jsxs)("div",{children:[(0,f.jsx)(c.J,{htmlFor:"cardNumber",children:"Card Number"}),(0,f.jsx)(l.p,{id:"cardNumber",placeholder:"1234 5678 9012 3456",value:v.cardNumber,onChange:e=>w("cardNumber",e.target.value),required:!0})]}),(0,f.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,f.jsxs)("div",{children:[(0,f.jsx)(c.J,{htmlFor:"expiryDate",children:"Expiry Date"}),(0,f.jsx)(l.p,{id:"expiryDate",placeholder:"MM/YY",value:v.expiryDate,onChange:e=>w("expiryDate",e.target.value),required:!0})]}),(0,f.jsxs)("div",{children:[(0,f.jsx)(c.J,{htmlFor:"cvv",children:"CVV"}),(0,f.jsx)(l.p,{id:"cvv",placeholder:"123",value:v.cvv,onChange:e=>w("cvv",e.target.value),required:!0})]})]}),(0,f.jsxs)("div",{className:"space-y-4",children:[(0,f.jsx)("h3",{className:"text-lg font-medium",children:"Billing Address"}),(0,f.jsxs)("div",{children:[(0,f.jsx)(c.J,{htmlFor:"street",children:"Street Address"}),(0,f.jsx)(l.p,{id:"street",placeholder:"123 Main St",value:v.billingAddress.street,onChange:e=>w("billingAddress.street",e.target.value),required:!0})]}),(0,f.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,f.jsxs)("div",{children:[(0,f.jsx)(c.J,{htmlFor:"city",children:"City"}),(0,f.jsx)(l.p,{id:"city",placeholder:"New York",value:v.billingAddress.city,onChange:e=>w("billingAddress.city",e.target.value),required:!0})]}),(0,f.jsxs)("div",{children:[(0,f.jsx)(c.J,{htmlFor:"state",children:"State"}),(0,f.jsx)(l.p,{id:"state",placeholder:"NY",value:v.billingAddress.state,onChange:e=>w("billingAddress.state",e.target.value),required:!0})]})]}),(0,f.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,f.jsxs)("div",{children:[(0,f.jsx)(c.J,{htmlFor:"zipCode",children:"ZIP Code"}),(0,f.jsx)(l.p,{id:"zipCode",placeholder:"10001",value:v.billingAddress.zipCode,onChange:e=>w("billingAddress.zipCode",e.target.value),required:!0})]}),(0,f.jsxs)("div",{children:[(0,f.jsx)(c.J,{htmlFor:"country",children:"Country"}),(0,f.jsxs)(o.l6,{value:v.billingAddress.country,onValueChange:e=>w("billingAddress.country",e),children:[(0,f.jsx)(o.bq,{children:(0,f.jsx)(o.yv,{})}),(0,f.jsxs)(o.gC,{children:[(0,f.jsx)(o.eb,{value:"US",children:"United States"}),(0,f.jsx)(o.eb,{value:"CA",children:"Canada"}),(0,f.jsx)(o.eb,{value:"GB",children:"United Kingdom"})]})]})]})]})]}),(0,f.jsx)(n.$,{type:"submit",className:"w-full",disabled:a,children:a?"Processing...":`Pay $${v.amount}`})]})})]})}),(0,f.jsxs)("div",{children:[(0,f.jsxs)(i.Zp,{children:[(0,f.jsx)(i.aR,{children:(0,f.jsx)(i.ZB,{children:"Order Summary"})}),(0,f.jsxs)(i.Wu,{className:"space-y-4",children:[(0,f.jsxs)("div",{className:"flex justify-between",children:[(0,f.jsx)("span",{children:"Merchant:"}),(0,f.jsx)("span",{className:"font-medium",children:j.businessName})]}),(0,f.jsxs)("div",{className:"flex justify-between",children:[(0,f.jsx)("span",{children:"Amount:"}),(0,f.jsxs)("span",{className:"font-medium",children:["$",v.amount||"0.00"]})]}),(0,f.jsxs)("div",{className:"flex justify-between",children:[(0,f.jsx)("span",{children:"Processing Fee:"}),(0,f.jsx)("span",{className:"font-medium",children:"$0.30"})]}),(0,f.jsx)("hr",{}),(0,f.jsxs)("div",{className:"flex justify-between font-bold",children:[(0,f.jsx)("span",{children:"Total:"}),(0,f.jsxs)("span",{children:["$",(Number.parseFloat(v.amount||"0")+.3).toFixed(2)]})]})]})]}),(0,f.jsx)(i.Zp,{className:"mt-4",children:(0,f.jsxs)(i.Wu,{className:"pt-6",children:[(0,f.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[(0,f.jsx)(x.A,{className:"h-4 w-4"}),(0,f.jsx)("span",{children:"256-bit SSL encryption"})]}),(0,f.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600 mt-2",children:[(0,f.jsx)(h.A,{className:"h-4 w-4"}),(0,f.jsx)("span",{children:"PCI DSS compliant"})]})]})})]})]})]})}):(0,f.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,f.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})})}},2248:(e,s,a)=>{a.d(s,{J:()=>l});var t=a(5043),r=a(917),d=a(3009),i=a(579);const n=(0,r.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,i.jsx)("label",{ref:s,className:(0,d.cn)(n(),a),...t})});l.displayName="Label"},2699:(e,s,a)=>{a.d(s,{A:()=>t});const t=(0,a(3797).A)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z",key:"3xmgem"}]])},5320:(e,s,a)=>{a.d(s,{bq:()=>m,eb:()=>h,gC:()=>u,l6:()=>c,yv:()=>o});var t=a(5043),r=a(2500),d=a(1024),i=a(1285),n=a(3009),l=a(579);const c=r.bL,o=(r.YJ,r.WT),m=t.forwardRef((e,s)=>{let{className:a,children:t,...d}=e;return(0,l.jsxs)(r.l9,{ref:s,className:(0,n.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),...d,children:[t,(0,l.jsx)(r.In,{asChild:!0,children:(0,l.jsx)(i.A,{className:"h-4 w-4 opacity-50"})})]})});m.displayName=r.l9.displayName;const u=t.forwardRef((e,s)=>{let{className:a,children:t,position:d="popper",...i}=e;return(0,l.jsx)(r.ZL,{children:(0,l.jsx)(r.UC,{ref:s,className:(0,n.cn)("relative z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===d&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:d,...i,children:(0,l.jsx)(r.LM,{className:(0,n.cn)("p-1","popper"===d&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t})})})});u.displayName=r.UC.displayName;t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,l.jsx)(r.JU,{ref:s,className:(0,n.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...t})}).displayName=r.JU.displayName;const h=t.forwardRef((e,s)=>{let{className:a,children:t,...i}=e;return(0,l.jsxs)(r.q7,{ref:s,className:(0,n.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...i,children:[(0,l.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,l.jsx)(r.VF,{children:(0,l.jsx)(d.A,{className:"h-4 w-4"})})}),(0,l.jsx)(r.p4,{children:t})]})});h.displayName=r.q7.displayName;t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,l.jsx)(r.wv,{ref:s,className:(0,n.cn)("-mx-1 my-1 h-px bg-muted",a),...t})}).displayName=r.wv.displayName},5722:(e,s,a)=>{a.d(s,{A:()=>t});const t=(0,a(3797).A)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},6713:(e,s,a)=>{a.d(s,{A:()=>t});const t=(0,a(3797).A)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},6742:(e,s,a)=>{a.d(s,{BT:()=>c,Wu:()=>o,ZB:()=>l,Zp:()=>i,aR:()=>n,wL:()=>m});var t=a(5043),r=a(3009),d=a(579);const i=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,d.jsx)("div",{ref:s,className:(0,r.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...t})});i.displayName="Card";const n=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,d.jsx)("div",{ref:s,className:(0,r.cn)("flex flex-col space-y-1.5 p-6",a),...t})});n.displayName="CardHeader";const l=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,d.jsx)("h3",{ref:s,className:(0,r.cn)("text-2xl font-semibold leading-none tracking-tight",a),...t})});l.displayName="CardTitle";const c=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,d.jsx)("p",{ref:s,className:(0,r.cn)("text-sm text-muted-foreground",a),...t})});c.displayName="CardDescription";const o=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,d.jsx)("div",{ref:s,className:(0,r.cn)("p-6 pt-0",a),...t})});o.displayName="CardContent";const m=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,d.jsx)("div",{ref:s,className:(0,r.cn)("flex items-center p-6 pt-0",a),...t})});m.displayName="CardFooter"},6879:(e,s,a)=>{a.d(s,{dj:()=>u,oR:()=>m});var t=a(5043);let r=0;const d=new Map,i=e=>{if(d.has(e))return;const s=setTimeout(()=>{d.delete(e),o({type:"REMOVE_TOAST",toastId:e})},1e3);d.set(e,s)},n=(e,s)=>{switch(s.type){case"ADD_TOAST":return{...e,toasts:[s.toast,...e.toasts].slice(0,5)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===s.toast.id?{...e,...s.toast}:e)};case"DISMISS_TOAST":{const{toastId:a}=s;return a?i(a):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,open:!1}:e)}}case"REMOVE_TOAST":return void 0===s.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==s.toastId)}}},l=[];let c={toasts:[]};function o(e){c=n(c,e),l.forEach(e=>{e(c)})}function m(e){let{...s}=e;const a=(r=(r+1)%Number.MAX_SAFE_INTEGER,r.toString()),t=()=>o({type:"DISMISS_TOAST",toastId:a});return o({type:"ADD_TOAST",toast:{...s,id:a,open:!0,onOpenChange:e=>{e||t()}}}),{id:a,dismiss:t,update:e=>o({type:"UPDATE_TOAST",toast:{...e,id:a}})}}function u(){const[e,s]=t.useState(c);return t.useEffect(()=>(l.push(s),()=>{const e=l.indexOf(s);e>-1&&l.splice(e,1)}),[e]),{...e,toast:m,dismiss:e=>o({type:"DISMISS_TOAST",toastId:e})}}},9772:(e,s,a)=>{a.d(s,{$:()=>c});var t=a(5043),r=a(6851),d=a(917),i=a(3009),n=a(579);const l=(0,d.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=t.forwardRef((e,s)=>{let{className:a,variant:t,size:d,asChild:c=!1,...o}=e;const m=c?r.DX:"button";return(0,n.jsx)(m,{className:(0,i.cn)(l({variant:t,size:d,className:a})),ref:s,...o})});c.displayName="Button"},9954:(e,s,a)=>{a.d(s,{p:()=>i});var t=a(5043),r=a(3009),d=a(579);const i=t.forwardRef((e,s)=>{let{className:a,type:t,...i}=e;return(0,d.jsx)("input",{type:t,className:(0,r.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:s,...i})});i.displayName="Input"}}]);
//# sourceMappingURL=284.95386dc8.chunk.js.map