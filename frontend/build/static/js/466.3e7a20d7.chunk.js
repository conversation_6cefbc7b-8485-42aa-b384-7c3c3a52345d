/*! For license information please see 466.3e7a20d7.chunk.js.LICENSE.txt */
"use strict";(self.webpackChunkpayment_gateway_frontend=self.webpackChunkpayment_gateway_frontend||[]).push([[466],{1490:(e,r,s)=>{s.d(r,{A:()=>t});const t=(0,s(3797).A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},2330:(e,r,s)=>{var t=s(5043);var a="function"===typeof Object.is?Object.is:function(e,r){return e===r&&(0!==e||1/e===1/r)||e!==e&&r!==r},n=t.useState,i=t.useEffect,o=t.useLayoutEffect,c=t.useDebugValue;function d(e){var r=e.getSnapshot;e=e.value;try{var s=r();return!a(e,s)}catch(t){return!0}}var l="undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement?function(e,r){return r()}:function(e,r){var s=r(),t=n({inst:{value:s,getSnapshot:r}}),a=t[0].inst,l=t[1];return o(function(){a.value=s,a.getSnapshot=r,d(a)&&l({inst:a})},[e,s,r]),i(function(){return d(a)&&l({inst:a}),e(function(){d(a)&&l({inst:a})})},[e]),c(s),s};r.useSyncExternalStore=void 0!==t.useSyncExternalStore?t.useSyncExternalStore:l},2380:(e,r,s)=>{s.d(r,{A:()=>t});const t=(0,s(3797).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},5680:(e,r,s)=>{s.d(r,{A:()=>t});const t=(0,s(3797).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},6742:(e,r,s)=>{s.d(r,{BT:()=>d,Wu:()=>l,ZB:()=>c,Zp:()=>i,aR:()=>o,wL:()=>u});var t=s(5043),a=s(3009),n=s(579);const i=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,n.jsx)("div",{ref:r,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...t})});i.displayName="Card";const o=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,n.jsx)("div",{ref:r,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",s),...t})});o.displayName="CardHeader";const c=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,n.jsx)("h3",{ref:r,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",s),...t})});c.displayName="CardTitle";const d=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,n.jsx)("p",{ref:r,className:(0,a.cn)("text-sm text-muted-foreground",s),...t})});d.displayName="CardDescription";const l=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,n.jsx)("div",{ref:r,className:(0,a.cn)("p-6 pt-0",s),...t})});l.displayName="CardContent";const u=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,n.jsx)("div",{ref:r,className:(0,a.cn)("flex items-center p-6 pt-0",s),...t})});u.displayName="CardFooter"},6875:(e,r,s)=>{s.d(r,{A:()=>t});const t=(0,s(3797).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},7466:(e,r,s)=>{s.r(r),s.d(r,{default:()=>B});var t=s(5043),a=s(6742),n=s(9772),i=s(2380),o=s(6875),c=s(1490),d=s(9589),l=s(5680),u=s(9954),f=s(8567),m=s(1862),x=s(7490),p=s(503),h=s(7920),g=s(9461);function v(){return()=>{}}var y=s(579),N="Avatar",[b,j]=(0,m.A)(N),[w,A]=b(N),S=t.forwardRef((e,r)=>{const{__scopeAvatar:s,...a}=e,[n,i]=t.useState("idle");return(0,y.jsx)(w,{scope:s,imageLoadingStatus:n,onImageLoadingStatusChange:i,children:(0,y.jsx)(h.sG.span,{...a,ref:r})})});S.displayName=N;var k="AvatarImage",R=t.forwardRef((e,r)=>{const{__scopeAvatar:s,src:a,onLoadingStatusChange:n=()=>{},...i}=e,o=A(k,s),c=function(e,r){let{referrerPolicy:s,crossOrigin:a}=r;const n=(0,g.useSyncExternalStore)(v,()=>!0,()=>!1),i=t.useRef(null),o=n?(i.current||(i.current=new window.Image),i.current):null,[c,d]=t.useState(()=>L(o,e));return(0,p.N)(()=>{d(L(o,e))},[o,e]),(0,p.N)(()=>{const e=e=>()=>{d(e)};if(!o)return;const r=e("loaded"),t=e("error");return o.addEventListener("load",r),o.addEventListener("error",t),s&&(o.referrerPolicy=s),"string"===typeof a&&(o.crossOrigin=a),()=>{o.removeEventListener("load",r),o.removeEventListener("error",t)}},[o,a,s]),c}(a,i),d=(0,x.c)(e=>{n(e),o.onImageLoadingStatusChange(e)});return(0,p.N)(()=>{"idle"!==c&&d(c)},[c,d]),"loaded"===c?(0,y.jsx)(h.sG.img,{...i,ref:r,src:a}):null});R.displayName=k;var C="AvatarFallback",E=t.forwardRef((e,r)=>{const{__scopeAvatar:s,delayMs:a,...n}=e,i=A(C,s),[o,c]=t.useState(void 0===a);return t.useEffect(()=>{if(void 0!==a){const e=window.setTimeout(()=>c(!0),a);return()=>window.clearTimeout(e)}},[a]),o&&"loaded"!==i.imageLoadingStatus?(0,y.jsx)(h.sG.span,{...n,ref:r}):null});function L(e,r){return e?r?(e.src!==r&&(e.src=r),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}E.displayName=C;var _=S,T=R,z=E,F=s(3009);const M=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,y.jsx)(_,{ref:r,className:(0,F.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",s),...t})});M.displayName=_.displayName;const I=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,y.jsx)(T,{ref:r,className:(0,F.cn)("aspect-square h-full w-full",s),...t})});I.displayName=T.displayName;const q=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,y.jsx)(z,{ref:r,className:(0,F.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",s),...t})});q.displayName=z.displayName;const B=()=>(0,y.jsxs)("div",{className:"space-y-6",children:[(0,y.jsxs)("div",{className:"flex items-center justify-between",children:[(0,y.jsxs)("div",{children:[(0,y.jsx)("h2",{className:"text-2xl font-bold tracking-tight",children:"Assigned Traders"}),(0,y.jsx)("p",{className:"text-muted-foreground",children:"Manage traders who can collect payments on your behalf"})]}),(0,y.jsxs)(n.$,{children:[(0,y.jsx)(i.A,{className:"mr-2 h-4 w-4"}),"Assign Trader"]})]}),(0,y.jsxs)(a.Zp,{children:[(0,y.jsx)(a.aR,{children:(0,y.jsxs)("div",{className:"flex items-center justify-between",children:[(0,y.jsx)(a.ZB,{children:"Active Traders"}),(0,y.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,y.jsxs)("div",{className:"relative",children:[(0,y.jsx)(o.A,{className:"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground"}),(0,y.jsx)(u.p,{type:"search",placeholder:"Search traders...",className:"pl-8 sm:w-[300px]"})]}),(0,y.jsxs)(n.$,{variant:"outline",size:"sm",className:"h-9",children:[(0,y.jsx)(c.A,{className:"mr-2 h-4 w-4"}),"Filter"]})]})]})}),(0,y.jsx)(a.Wu,{children:(0,y.jsx)("div",{className:"space-y-4",children:[{id:"1",name:"John Smith",email:"<EMAIL>",phone:"+****************",status:"active",collections:42,lastActive:"2023-06-15"}].map(e=>(0,y.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,y.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,y.jsxs)(M,{children:[(0,y.jsx)(I,{src:`/avatars/${e.id}.jpg`,alt:e.name}),(0,y.jsx)(q,{children:e.name.split(" ").map(e=>e[0]).join("")})]}),(0,y.jsxs)("div",{children:[(0,y.jsx)("p",{className:"font-medium",children:e.name}),(0,y.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-muted-foreground",children:[(0,y.jsxs)("span",{className:"flex items-center",children:[(0,y.jsx)(d.A,{className:"mr-1 h-3 w-3"}),e.email]}),(0,y.jsx)("span",{children:"\u2022"}),(0,y.jsxs)("span",{className:"flex items-center",children:[(0,y.jsx)(l.A,{className:"mr-1 h-3 w-3"}),e.phone]})]})]})]}),(0,y.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,y.jsxs)("div",{className:"text-right",children:[(0,y.jsx)("p",{className:"text-sm text-muted-foreground",children:"Collections"}),(0,y.jsx)("p",{className:"font-medium",children:e.collections})]}),(0,y.jsx)(f.E,{variant:"active"===e.status?"success":"secondary",children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})]})]},e.id))})})]})]})},8567:(e,r,s)=>{s.d(r,{E:()=>o});s(5043);var t=s(917),a=s(3009),n=s(579);const i=(0,t.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",success:"border-transparent bg-green-500 text-white hover:bg-green-500/80",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-500/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:r,variant:s,...t}=e;return(0,n.jsx)("div",{className:(0,a.cn)(i({variant:s}),r),...t})}},9461:(e,r,s)=>{e.exports=s(2330)},9589:(e,r,s)=>{s.d(r,{A:()=>t});const t=(0,s(3797).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},9772:(e,r,s)=>{s.d(r,{$:()=>d});var t=s(5043),a=s(6851),n=s(917),i=s(3009),o=s(579);const c=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=t.forwardRef((e,r)=>{let{className:s,variant:t,size:n,asChild:d=!1,...l}=e;const u=d?a.DX:"button";return(0,o.jsx)(u,{className:(0,i.cn)(c({variant:t,size:n,className:s})),ref:r,...l})});d.displayName="Button"},9954:(e,r,s)=>{s.d(r,{p:()=>i});var t=s(5043),a=s(3009),n=s(579);const i=t.forwardRef((e,r)=>{let{className:s,type:t,...i}=e;return(0,n.jsx)("input",{type:t,className:(0,a.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:r,...i})});i.displayName="Input"}}]);
//# sourceMappingURL=466.3e7a20d7.chunk.js.map