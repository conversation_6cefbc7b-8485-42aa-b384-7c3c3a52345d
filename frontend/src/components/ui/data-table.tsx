import * as React from "react"
import { isValidElement, createElement } from "react"
import type { ReactNode } from "react"
import { <PERSON><PERSON> } from "./button"
import { Input } from "./input"
import { Skeleton } from "./skeleton"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "./table"
import {
  type ColumnDef,
  type Table as TableType,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  type ColumnFiltersState,
  type SortingState,
  type VisibilityState,
  type PaginationState,
} from "@tanstack/react-table"

// Helper function to safely render cell content
const safeRenderCell = (content: unknown): ReactNode => {
  if (content == null) return null;
  
  // Handle React elements
  if (isValidElement(content)) return content;
  
  // Handle arrays
  if (Array.isArray(content)) {
    return createElement(
      React.Fragment,
      {},
      ...content.map((item, i) => 
        createElement(React.Fragment, { key: i }, safeRenderCell(item))
      )
    );
  }
  
  // <PERSON>le promises
  if (content instanceof Promise) {
    return 'Loading...';
  }
  
  // Handle primitive values
  return String(content);
};

// Type-safe wrapper for flexRender
const safeFlexRender = (cell: any, context: any): ReactNode => {
  try {
    const result = flexRender(cell, context);
    return safeRenderCell(result);
  } catch (error) {
    console.error('Error rendering cell:', error);
    return 'Error';
  }
};

export interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  data: TData[]
  page?: number
  totalPages?: number
  totalItems?: number
  isLoading?: boolean
  onPageChange?: (page: number) => void
  filterColumn?: string
  searchPlaceholder?: string
  pageSize?: number
  onPageSizeChange?: (size: number) => void
}

export function DataTable<TData, TValue>({
  columns,
  data = [],
  page = 1,
  totalPages = 1,
  totalItems = 0,
  isLoading = false,
  onPageChange,
  filterColumn,
  searchPlaceholder = "Filter...",
  pageSize = 10,
  onPageSizeChange,
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({})
  const [rowSelection, setRowSelection] = React.useState({})
  const [pagination, setPagination] = React.useState<PaginationState>({
    pageIndex: page - 1,
    pageSize,
  })

  // Update pagination when page or pageSize changes
  React.useEffect(() => {
    setPagination(prev => ({
      ...prev,
      pageIndex: page - 1,
      pageSize,
    }));
  }, [page, pageSize]);

  const table = useReactTable({
    data,
    columns,
    manualPagination: true,
    pageCount: totalPages,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      pagination,
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onPaginationChange: (updater) => {
      const newPagination = typeof updater === 'function' 
        ? updater(pagination) 
        : updater;
      
      setPagination(newPagination);
      
      // Only trigger page change if the page actually changed
      if (onPageChange && newPagination.pageIndex + 1 !== page) {
        onPageChange(newPagination.pageIndex + 1);
      }
      
      if (onPageSizeChange && newPagination.pageSize !== pageSize) {
        onPageSizeChange(newPagination.pageSize);
      }
    },
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
  })

  return (
    <div className="w-full space-y-4">
      <div className="flex items-center justify-between py-4">
        {filterColumn && (
          <div className="flex-1">
            <Input
              placeholder={searchPlaceholder}
              value={(table.getColumn(filterColumn)?.getFilterValue() as string) ?? ""}
              onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                table.getColumn(filterColumn)?.setFilterValue(event.target.value)
              }}
              className="max-w-sm"
            />
          </div>
        )}
        
        <div className="flex items-center space-x-2">
          <span className="text-sm text-muted-foreground">
            Page {page} of {totalPages}
          </span>
          <span className="text-sm text-muted-foreground">
            {totalItems} total items
          </span>
        </div>
      </div>
      
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : safeFlexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {isLoading ? (
              // Show loading skeletons
              Array.from({ length: 5 }).map((_, i) => (
                <TableRow key={`skeleton-${i}`}>
                  {columns.map((_, colIndex) => (
                    <TableCell key={`skeleton-cell-${i}-${colIndex}`}>
                      <Skeleton className="h-4 w-full" />
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {safeFlexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      
      <div className="flex flex-col items-center justify-between gap-4 sm:flex-row">
        <div className="text-sm text-muted-foreground">
          Showing <span className="font-medium">
            {data.length === 0 ? 0 : (page - 1) * pageSize + 1}
          </span> to{' '}
          <span className="font-medium">
            {Math.min(page * pageSize, totalItems)}
          </span>{' '}
          of <span className="font-medium">{totalItems}</span> results
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange?.(Math.max(1, page - 1))}
            disabled={page <= 1 || isLoading}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange?.(Math.min(totalPages, page + 1))}
            disabled={page >= totalPages || isLoading}
          >
            Next
          </Button>
        </div>
        
        <div className="flex items-center space-x-2">
          <p className="text-sm font-medium">Rows per page</p>
          <select
            className="h-8 rounded-md border border-input bg-background px-2 py-1 text-sm"
            value={pageSize}
            onChange={(e) => {
              const newSize = Number(e.target.value);
              onPageSizeChange?.(newSize);
            }}
            disabled={isLoading}
          >
            {[10, 20, 30, 40, 50].map((size) => (
              <option key={size} value={size}>
                {size}
              </option>
            ))}
          </select>
        </div>
      </div>
    </div>
  )
}
