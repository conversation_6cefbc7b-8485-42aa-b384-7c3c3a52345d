const User = require('../models/User');
const ApiError = require('../utils/ApiError');
const logger = require('../utils/logger');
const rateLimit = require('express-rate-limit').default;

// Rate limiting for bypass token generation (5 requests per hour per IP)
const bypassTokenLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 5, // Limit each IP to 5 requests per windowMs
  message: 'Too many bypass token requests from this IP, please try again after an hour',
  standardHeaders: true,
  legacyHeaders: false,
});

/**
 * @desc    Generate a bypass token for testing
 * @route   GET /api/auth/bypass-token/:userId
 * @access  Private/Admin
 */
const generateBypassToken = async (req, res, next) => {
  try {
    // Only allow in development or testing environments
    if (process.env.NODE_ENV === 'production' || process.env.ENABLE_JWT_BYPASS !== 'true') {
      logger.warn('Attempt to access bypass token endpoint in production or with bypass disabled', {
        ip: req.ip,
        userAgent: req.get('user-agent'),
        userId: req.params.userId
      });
      throw new ApiError('This feature is not available', 403);
    }

    const { userId } = req.params;
    
    // Validate user ID format
    if (!userId.match(/^[0-9a-fA-F]{24}$/)) {
      throw new ApiError('Invalid user ID format', 400);
    }
    
    // Find the user
    const user = await User.findById(userId).select('-password -__v');
    
    if (!user) {
      logger.warn('Bypass token generation failed: User not found', { userId });
      throw new ApiError('User not found', 404);
    }
    
    // Log bypass token generation
    logger.info('Bypass token generated', {
      userId: user._id,
      email: user.email,
      role: user.role,
      generatedBy: req.user?._id || 'system',
      ip: req.ip,
      userAgent: req.get('user-agent')
    });

    // Generate a bypass token (format: bypass_<userId>)
    const bypassToken = `bypass_${user._id}`;
    
    res.status(200).json({
      success: true,
      bypassToken,
      user: {
        id: user._id,
        email: user.email,
        name: user.name,
        role: user.role
      },
      message: 'Use this token with ?bypassToken= parameter for testing',
      warning: 'Bypass tokens should only be used in development and testing environments!',
      usage: `Add to URL: ?bypassToken=${bypassToken}`
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  generateBypassToken,
  bypassTokenLimiter
};
