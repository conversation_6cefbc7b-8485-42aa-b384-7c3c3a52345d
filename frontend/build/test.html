<!DOCTYPE html>
<html>
<head>
    <title>Test Page</title>
    <script>
        // Test if we can access the development server
        console.log('Test page loaded');
        
        // Try to fetch from the API
        fetch('/api/health')
            .then(response => response.json())
            .then(data => console.log('API Health:', data))
            .catch(error => console.error('API Error:', error));
    </script>
</head>
<body>
    <h1>Test Page</h1>
    <p>Check the browser console for any errors.</p>
</body>
</html>
