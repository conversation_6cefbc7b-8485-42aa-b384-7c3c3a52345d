const morgan = require('morgan');
const logger = require('../utils/logger');
const { v4: uuidv4 } = require('uuid');

// Sensitive fields that should be masked in logs
const SENSITIVE_FIELDS = [
  'password',
  'newPassword',
  'confirmPassword',
  'currentPassword',
  'token',
  'accessToken',
  'refreshToken',
  'authorization',
  'cardNumber',
  'cvv',
  'expiryDate',
  'ssn',
  'socialSecurityNumber',
];

/**
 * Mask sensitive data in objects
 * @param {Object} data - Data to mask
 * @returns {Object} Masked data
 */
const maskSensitiveData = (data) => {
  if (!data || typeof data !== 'object') return data;
  
  const maskedData = Array.isArray(data) ? [...data] : { ...data };
  
  Object.keys(maskedData).forEach(key => {
    const lowerKey = key.toLowerCase();
    
    // Check if the key matches any sensitive field (case insensitive)
    const isSensitive = SENSITIVE_FIELDS.some(
      field => field.toLowerCase() === lowerKey
    );
    
    if (isSensitive && maskedData[key] !== undefined) {
      maskedData[key] = '***MASKED***';
    } else if (typeof maskedData[key] === 'object') {
      // Recursively mask nested objects
      maskedData[key] = maskSensitiveData(maskedData[key]);
    }
  });
  
  return maskedData;
};

/**
 * Middleware to add a unique request ID to each request
 */
const requestId = (req, res, next) => {
  req.id = uuidv4();
  res.setHeader('X-Request-ID', req.id);
  next();
};

// Configure Morgan to use our custom logger with request ID
morgan.token('id', (req) => req.id);

/**
 * Custom token to log request body (for non-GET requests)
 */
morgan.token('req-body', (req) => {
  if (req.method === 'GET') return '';
  
  try {
    const contentType = req.headers['content-type'] || '';
    
    // Only log JSON and form data
    if (contentType.includes('application/json') && req.body) {
      return JSON.stringify(maskSensitiveData(req.body));
    }
    
    if (contentType.includes('application/x-www-form-urlencoded') && req.body) {
      return JSON.stringify(maskSensitiveData(req.body));
    }
    
    return '[binary-data]';
  } catch (error) {
    return '[error-parsing-body]';
  }
});

// Custom format for request logging
const requestFormat = (tokens, req, res) => {
  const logData = {
    requestId: tokens.id(req, res),
    timestamp: new Date().toISOString(),
    method: tokens.method(req, res),
    url: tokens.url(req, res),
    status: tokens.status(req, res),
    contentLength: tokens.res(req, res, 'content-length'),
    responseTime: `${tokens['response-time'](req, res)}ms`,
    ip: tokens['remote-addr'](req, res),
    userAgent: tokens['user-agent'](req, res),
    referrer: tokens.referrer(req, res) || '',
    httpVersion: `HTTP/${tokens['http-version'](req, res)}`,
    requestBody: tokens['req-body'](req, res),
    userId: req.user ? req.user.id : 'anonymous',
  };
  
  return JSON.stringify(logData);
};

// Skip logging for health check endpoints and static files
const skip = (req) => {
  const skipPaths = [
    '/health',
    '/favicon.ico',
    '/robots.txt',
    '/sitemap.xml',
    '/.well-known/',
    '/api-docs',
  ];
  
  return skipPaths.some(path => req.originalUrl.startsWith(path));
};

/**
 * Request logger middleware
 */
const requestLogger = [
  // Add request ID
  requestId,
  
  // Log request details
  morgan(requestFormat, {
    stream: {
      write: (message) => {
        try {
          const logData = JSON.parse(message);
          
          // Log errors separately
          if (logData.status >= 400) {
            logger.error('HTTP Request Error', logData);
          } else {
            logger.info('HTTP Request', logData);
          }
        } catch (error) {
          logger.error('Error parsing request log', { message, error: error.message });
        }
      },
    },
    skip,
  }),
  
  // Log response details
  (req, res, next) => {
    const startTime = process.hrtime();
    
    // Capture the original response methods
    const originalJson = res.json;
    const originalSend = res.send;
    
    // Override response methods to capture response body
    res.json = function (body) {
      res.locals.responseBody = maskSensitiveData(body);
      return originalJson.call(this, body);
    };
    
    res.send = function (body) {
      try {
        if (typeof body === 'string') {
          // Try to parse as JSON to mask sensitive data
          try {
            const jsonBody = JSON.parse(body);
            res.locals.responseBody = maskSensitiveData(jsonBody);
          } catch {
            res.locals.responseBody = body;
          }
        } else if (body && typeof body === 'object') {
          res.locals.responseBody = maskSensitiveData(body);
        }
      } catch (error) {
        res.locals.responseBody = '[error-processing-response]';
      }
      
      return originalSend.call(this, body);
    };
    
    // Log when response is finished
    res.on('finish', () => {
      const responseTime = process.hrtime(startTime);
      const responseTimeMs = (responseTime[0] * 1e3 + responseTime[1] / 1e6).toFixed(2);
      
      const logData = {
        requestId: req.id,
        timestamp: new Date().toISOString(),
        method: req.method,
        url: req.originalUrl,
        status: res.statusCode,
        responseTime: `${responseTimeMs}ms`,
        ip: req.ip,
        userAgent: req.get('user-agent') || '',
        userId: req.user ? req.user.id : 'anonymous',
        responseBody: res.locals.responseBody || '',
      };
      
      if (res.statusCode >= 400) {
        logger.error('HTTP Response Error', logData);
      } else {
        logger.info('HTTP Response', logData);
      }
    });
    
    next();
  },
];

module.exports = requestLogger;
