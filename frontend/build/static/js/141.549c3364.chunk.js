"use strict";(self.webpackChunkpayment_gateway_frontend=self.webpackChunkpayment_gateway_frontend||[]).push([[141],{1872:(n,t,e)=>{e.d(t,{f:()=>i});var r=e(6326);function o(n){return n}function a(n,t){void 0===t&&(t=o);var e=[],r=!1;return{read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return e.length?e[e.length-1]:n},useMedium:function(n){var o=t(n,r);return e.push(o),function(){e=e.filter(function(n){return n!==o})}},assignSyncMedium:function(n){for(r=!0;e.length;){var t=e;e=[],t.forEach(n)}e={push:function(t){return n(t)},filter:function(){return e}}},assignMedium:function(n){r=!0;var t=[];if(e.length){var o=e;e=[],o.forEach(n),t=e}var a=function(){var e=t;t=[],e.forEach(n)},i=function(){return Promise.resolve().then(a)};i(),e={push:function(n){t.push(n),i()},filter:function(n){return t=t.filter(n),e}}}}}function i(n){void 0===n&&(n={});var t=a(null);return t.options=(0,r.Cl)({async:!0,ssr:!1},n),t}},3259:(n,t,e)=>{e.d(t,{E9:()=>a,Mi:()=>r,pN:()=>o,xi:()=>i});var r="right-scroll-bar-position",o="width-before-scroll-bar",a="with-scroll-bars-hidden",i="--removed-body-scroll-bar-size"},4560:(n,t,e)=>{e.d(t,{m:()=>i});var r=e(6326),o=e(5043),a=function(n){var t=n.sideCar,e=(0,r.Tt)(n,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var a=t.read();if(!a)throw new Error("Sidecar medium not found");return o.createElement(a,(0,r.Cl)({},e))};function i(n,t){return n.useMedium(t),a}a.isSideCarExport=!0},5754:(n,t,e)=>{e.d(t,{Eq:()=>f});var r=function(n){return"undefined"===typeof document?null:(Array.isArray(n)?n[0]:n).ownerDocument.body},o=new WeakMap,a=new WeakMap,i={},c=0,u=function(n){return n&&(n.host||u(n.parentNode))},d=function(n,t,e,r){var d=function(n,t){return t.map(function(t){if(n.contains(t))return t;var e=u(t);return e&&n.contains(e)?e:(console.error("aria-hidden",t,"in not contained inside",n,". Doing nothing"),null)}).filter(function(n){return Boolean(n)})}(t,Array.isArray(n)?n:[n]);i[e]||(i[e]=new WeakMap);var f=i[e],l=[],p=new Set,s=new Set(d),h=function(n){n&&!p.has(n)&&(p.add(n),h(n.parentNode))};d.forEach(h);var v=function(n){n&&!s.has(n)&&Array.prototype.forEach.call(n.children,function(n){if(p.has(n))v(n);else try{var t=n.getAttribute(r),i=null!==t&&"false"!==t,c=(o.get(n)||0)+1,u=(f.get(n)||0)+1;o.set(n,c),f.set(n,u),l.push(n),1===c&&i&&a.set(n,!0),1===u&&n.setAttribute(e,"true"),i||n.setAttribute(r,"true")}catch(d){console.error("aria-hidden: cannot operate on ",n,d)}})};return v(t),p.clear(),c++,function(){l.forEach(function(n){var t=o.get(n)-1,i=f.get(n)-1;o.set(n,t),f.set(n,i),t||(a.has(n)||n.removeAttribute(r),a.delete(n)),i||n.removeAttribute(e)}),--c||(o=new WeakMap,o=new WeakMap,a=new WeakMap,i={})}},f=function(n,t,e){void 0===e&&(e="data-aria-hidden");var o=Array.from(Array.isArray(n)?n:[n]),a=t||r(n);return a?(o.push.apply(o,Array.from(a.querySelectorAll("[aria-live], script"))),d(o,a,e,"aria-hidden")):function(){return null}}},5857:(n,t,e)=>{e.d(t,{jp:()=>s});var r=e(5043),o=e(7513),a=e(3259),i={left:0,top:0,right:0,gap:0},c=function(n){return parseInt(n||"",10)||0},u=function(n){if(void 0===n&&(n="margin"),"undefined"===typeof window)return i;var t=function(n){var t=window.getComputedStyle(document.body),e=t["padding"===n?"paddingLeft":"marginLeft"],r=t["padding"===n?"paddingTop":"marginTop"],o=t["padding"===n?"paddingRight":"marginRight"];return[c(e),c(r),c(o)]}(n),e=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-e+t[2]-t[0])}},d=(0,o.T0)(),f="data-scroll-locked",l=function(n,t,e,r){var o=n.left,i=n.top,c=n.right,u=n.gap;return void 0===e&&(e="margin"),"\n  .".concat(a.E9," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(u,"px ").concat(r,";\n  }\n  body[").concat(f,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===e&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(c,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(u,"px ").concat(r,";\n    "),"padding"===e&&"padding-right: ".concat(u,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(a.Mi," {\n    right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(a.pN," {\n    margin-right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(a.Mi," .").concat(a.Mi," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(a.pN," .").concat(a.pN," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(f,"] {\n    ").concat(a.xi,": ").concat(u,"px;\n  }\n")},p=function(){var n=parseInt(document.body.getAttribute(f)||"0",10);return isFinite(n)?n:0},s=function(n){var t=n.noRelative,e=n.noImportant,o=n.gapMode,a=void 0===o?"margin":o;r.useEffect(function(){return document.body.setAttribute(f,(p()+1).toString()),function(){var n=p()-1;n<=0?document.body.removeAttribute(f):document.body.setAttribute(f,n.toString())}},[]);var i=r.useMemo(function(){return u(a)},[a]);return r.createElement(d,{styles:l(i,!t,a,e?"":"!important")})}},6326:(n,t,e)=>{e.d(t,{Cl:()=>r,Tt:()=>o,fX:()=>a});var r=function(){return r=Object.assign||function(n){for(var t,e=1,r=arguments.length;e<r;e++)for(var o in t=arguments[e])Object.prototype.hasOwnProperty.call(t,o)&&(n[o]=t[o]);return n},r.apply(this,arguments)};function o(n,t){var e={};for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&t.indexOf(r)<0&&(e[r]=n[r]);if(null!=n&&"function"===typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(n);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(n,r[o])&&(e[r[o]]=n[r[o]])}return e}Object.create;function a(n,t,e){if(e||2===arguments.length)for(var r,o=0,a=t.length;o<a;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return n.concat(r||Array.prototype.slice.call(t))}Object.create;"function"===typeof SuppressedError&&SuppressedError},6934:(n,t,e)=>{e.d(t,{S:()=>c});var r=e(5043);function o(n,t){return"function"===typeof n?n(t):n&&(n.current=t),n}var a="undefined"!==typeof window?r.useLayoutEffect:r.useEffect,i=new WeakMap;function c(n,t){var e=function(n,t){var e=(0,r.useState)(function(){return{value:n,callback:t,facade:{get current(){return e.value},set current(n){var t=e.value;t!==n&&(e.value=n,e.callback(n,t))}}}})[0];return e.callback=t,e.facade}(t||null,function(t){return n.forEach(function(n){return o(n,t)})});return a(function(){var t=i.get(e);if(t){var r=new Set(t),a=new Set(n),c=e.current;r.forEach(function(n){a.has(n)||o(n,null)}),a.forEach(function(n){r.has(n)||o(n,c)})}i.set(e,n)},[n]),e}},7513:(n,t,e)=>{e.d(t,{T0:()=>c});var r,o=e(5043);function a(){if(!document)return null;var n=document.createElement("style");n.type="text/css";var t=r||e.nc;return t&&n.setAttribute("nonce",t),n}var i=function(){var n=0,t=null;return{add:function(e){var r,o;0==n&&(t=a())&&(o=e,(r=t).styleSheet?r.styleSheet.cssText=o:r.appendChild(document.createTextNode(o)),function(n){(document.head||document.getElementsByTagName("head")[0]).appendChild(n)}(t)),n++},remove:function(){! --n&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},c=function(){var n=function(){var n=i();return function(t,e){o.useEffect(function(){return n.add(t),function(){n.remove()}},[t&&e])}}();return function(t){var e=t.styles,r=t.dynamic;return n(e,r),null}}}}]);
//# sourceMappingURL=141.549c3364.chunk.js.map