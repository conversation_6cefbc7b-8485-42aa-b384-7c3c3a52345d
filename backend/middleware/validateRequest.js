const { validationResult, body, param, query } = require('express-validator');
const ApiError = require('../utils/ApiError');
const logger = require('../utils/logger');

/**
 * Validate request using express-validator
 * @param {Array} validations - Array of validation rules
 * @returns {Array} Express middleware array
 */
const validate = (validations) => {
  return [
    // Run validations
    ...validations,
    
    // Process validation results
    (req, res, next) => {
      try {
        const errors = validationResult(req);
        
        if (!errors.isEmpty()) {
          // Format errors
          const formattedErrors = errors.array().map(error => ({
            field: error.param,
            message: error.msg,
            value: error.value,
            location: error.location,
          }));
          
          logger.warn('Request validation failed', {
            path: req.path,
            method: req.method,
            errors: formattedErrors,
          });
          
          return next(ApiError.validationError('Validation failed', formattedErrors));
        }
        
        next();
      } catch (error) {
        next(error);
      }
    }
  ];
};

// Common validation rules
const commonRules = {
  // ID validation
  id: [
    param('id')
      .trim()
      .notEmpty().withMessage('ID is required')
      .isMongoId().withMessage('Invalid ID format'),
  ],
  
  // Email validation
  email: [
    body('email')
      .trim()
      .notEmpty().withMessage('Email is required')
      .isEmail().withMessage('Invalid email format')
      .normalizeEmail(),
  ],
  
  // Password validation
  password: [
    body('password')
      .trim()
      .notEmpty().withMessage('Password is required')
      .isLength({ min: 8 }).withMessage('Password must be at least 8 characters')
      .matches(/[a-z]/).withMessage('Password must contain at least one lowercase letter')
      .matches(/[A-Z]/).withMessage('Password must contain at least one uppercase letter')
      .matches(/[0-9]/).withMessage('Password must contain at least one number')
      .matches(/[^a-zA-Z0-9]/).withMessage('Password must contain at least one special character'),
  ],
  
  // Pagination validation
  pagination: [
    query('page')
      .optional()
      .isInt({ min: 1 }).withMessage('Page must be a positive integer')
      .toInt(),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100')
      .toInt(),
  ],
  
  // Sort validation
  sort: [
    query('sort')
      .optional()
      .trim()
      .matches(/^[a-zA-Z0-9_,. -]+$/).withMessage('Invalid sort format'),
  ],
};

/**
 * Create validation rules for a model
 * @param {Object} fields - Object containing field validation rules
 * @returns {Array} Validation rules array
 */
const createValidationRules = (fields) => {
  const rules = [];
  
  Object.entries(fields).forEach(([field, config]) => {
    const { required = false, type = 'string', min, max, enum: enumValues, custom } = config;
    let validator = body(field);
    
    // Apply required validation
    if (required) {
      validator = validator.notEmpty().withMessage(`${field} is required`);
    } else {
      validator = validator.optional();
    }
    
    // Apply type-specific validation
    switch (type) {
      case 'string':
        validator = validator.isString().withMessage(`${field} must be a string`);
        if (min) {
          validator = validator.isLength({ min }).withMessage(`${field} must be at least ${min} characters`);
        }
        if (max) {
          validator = validator.isLength({ max }).withMessage(`${field} must be at most ${max} characters`);
        }
        break;
        
      case 'number':
        validator = validator.isNumeric().withMessage(`${field} must be a number`);
        if (min !== undefined) {
          validator = validator.isFloat({ min }).withMessage(`${field} must be at least ${min}`);
        }
        if (max !== undefined) {
          validator = validator.isFloat({ max }).withMessage(`${field} must be at most ${max}`);
        }
        break;
        
      case 'boolean':
        validator = validator.isBoolean().withMessage(`${field} must be a boolean`);
        break;
        
      case 'date':
        validator = validator.isISO8601().withMessage(`${field} must be a valid date`);
        break;
        
      case 'email':
        validator = validator.isEmail().withMessage('Invalid email format').normalizeEmail();
        break;
        
      case 'enum':
        if (Array.isArray(enumValues)) {
          validator = validator.isIn(enumValues).withMessage(`Invalid ${field} value. Must be one of: ${enumValues.join(', ')}`);
        }
        break;
    }
    
    // Apply custom validation if provided
    if (typeof custom === 'function') {
      validator = custom(validator);
    }
    
    rules.push(validator);
  });
  
  return rules;
};

module.exports = {
  validate,
  commonRules,
  createValidationRules,
  body,
  param,
  query,
};
