import React, { useState } from 'react';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Button,
  Card,
  CardContent,
  Chip,
  Divider,
  FormControlLabel,
  Grid,
  IconButton,
  Switch,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Tooltip,
  Typography,
  useTheme,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Info as InfoIcon,
  ArrowUpward as ArrowUpwardIcon,
  ArrowDownward as ArrowDownwardIcon,
} from '@mui/icons-material';
import { useFormik } from 'formik';
import * as Yup from 'yup';

// Validation schema for a tier
const tierValidationSchema = Yup.object().shape({
  minAmount: Yup.number()
    .required('Minimum amount is required')
    .min(0, 'Minimum amount must be 0 or greater')
    .typeError('Must be a valid number'),
  maxAmount: Yup.number()
    .nullable()
    .min(Yup.ref('minAmount'), 'Max amount must be greater than min amount')
    .typeError('Must be a valid number'),
  percentage: Yup.number()
    .when(['fixedAmount'], {
      is: (fixedAmount) => !fixedAmount && fixedAmount !== 0,
      then: Yup.number()
        .required('Either percentage or fixed amount is required')
        .min(0, 'Percentage must be 0 or greater')
        .max(100, 'Percentage cannot exceed 100')
        .typeError('Must be a valid number'),
      otherwise: Yup.number()
        .nullable()
        .min(0, 'Percentage must be 0 or greater')
        .max(100, 'Percentage cannot exceed 100')
        .typeError('Must be a valid number'),
    }),
  fixedAmount: Yup.number()
    .when(['percentage'], {
      is: (percentage) => !percentage && percentage !== 0,
      then: Yup.number()
        .required('Either percentage or fixed amount is required')
        .min(0, 'Amount must be 0 or greater')
        .typeError('Must be a valid number'),
      otherwise: Yup.number()
        .nullable()
        .min(0, 'Amount must be 0 or greater')
        .typeError('Must be a valid number'),
    }),
});

// Validation schema for a rule
const ruleValidationSchema = Yup.object().shape({
  type: Yup.string()
    .required('Type is required')
    .oneOf(['percentage', 'fixed_amount', 'tiered'], 'Invalid type'),
  percentage: Yup.number()
    .when('type', {
      is: 'percentage',
      then: Yup.number()
        .required('Percentage is required')
        .min(0, 'Percentage must be 0 or greater')
        .max(100, 'Percentage cannot exceed 100')
        .typeError('Must be a valid number'),
      otherwise: Yup.number()
        .nullable()
        .transform((value) => (isNaN(value) ? null : value)),
    }),
  fixedAmount: Yup.number()
    .when('type', {
      is: 'fixed_amount',
      then: Yup.number()
        .required('Fixed amount is required')
        .min(0, 'Amount must be 0 or greater')
        .typeError('Must be a valid number'),
      otherwise: Yup.number()
        .nullable()
        .transform((value) => (isNaN(value) ? null : value)),
    }),
  minReserve: Yup.number()
    .min(0, 'Minimum reserve must be 0 or greater')
    .nullable()
    .transform((value) => (isNaN(value) ? null : value)),
  maxReserve: Yup.number()
    .min(Yup.ref('minReserve'), 'Max reserve must be greater than min reserve')
    .nullable()
    .transform((value) => (value === '' ? null : value)),
  priority: Yup.number()
    .required('Priority is required')
    .integer('Priority must be an integer')
    .min(0, 'Priority must be 0 or greater')
    .typeError('Must be a valid number'),
  isActive: Yup.boolean(),
  description: Yup.string(),
  tiers: Yup.array().when('type', {
    is: 'tiered',
    then: Yup.array()
      .of(tierValidationSchema)
      .min(1, 'At least one tier is required')
      .test(
        'tier-validation',
        'Tiers must be in order and not overlap',
        function (tiers) {
          if (!tiers || tiers.length < 2) return true;
          
          // Check that tiers are in order and don't have gaps or overlaps
          for (let i = 0; i < tiers.length - 1; i++) {
            const currentMax = tiers[i].maxAmount;
            const nextMin = tiers[i + 1].minAmount;
            
            if (currentMax === null || nextMin === undefined || currentMax !== nextMin) {
              return this.createError({
                path: `tiers[${i}].maxAmount`,
                message: 'Tiers must be continuous without gaps or overlaps',
              });
            }
          }
          
          return true;
        }
      ),
    otherwise: Yup.array(),
  }),
});

// Tier form component
const TierForm = ({
  tier,
  index,
  onTierChange,
  onRemoveTier,
  isFirst,
  isLast,
  totalTiers,
  onMoveTier,
  errors = {},
}) => {
  const theme = useTheme();
  
  return (
    <Box 
      sx={{ 
        p: 2, 
        mb: 2, 
        border: `1px solid ${theme.palette.divider}`, 
        borderRadius: 1,
        position: 'relative',
        bgcolor: theme.palette.background.paper,
      }}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
        <Typography variant="subtitle2">Tier {index + 1}</Typography>
        <Box>
          {totalTiers > 1 && (
            <>
              <Tooltip title="Move up">
                <span>
                  <IconButton 
                    size="small" 
                    onClick={() => onMoveTier(index, 'up')}
                    disabled={isFirst}
                  >
                    <ArrowUpwardIcon fontSize="small" />
                  </IconButton>
                </span>
              </Tooltip>
              <Tooltip title="Move down">
                <span>
                  <IconButton 
                    size="small" 
                    onClick={() => onMoveTier(index, 'down')}
                    disabled={isLast}
                  >
                    <ArrowDownwardIcon fontSize="small" />
                  </IconButton>
                </span>
              </Tooltip>
            </>
          )}
          <Tooltip title="Remove tier">
            <span>
              <IconButton 
                size="small" 
                color="error"
                onClick={() => onRemoveTier(index)}
                disabled={totalTiers <= 1}
              >
                <DeleteIcon fontSize="small" />
              </IconButton>
            </span>
          </Tooltip>
        </Box>
      </Box>
      
      <Grid container spacing={2}>
        <Grid item xs={12} sm={6} md={4}>
          <TextField
            fullWidth
            label="Min Amount"
            name={`minAmount`}
            value={tier.minAmount ?? ''}
            onChange={(e) => onTierChange(index, 'minAmount', e.target.value)}
            error={Boolean(errors.minAmount)}
            helperText={errors.minAmount}
            size="small"
            disabled={isFirst} // First tier's min amount is fixed at 0
            type="number"
            InputProps={{
              startAdornment: <span style={{ marginRight: 8 }}>$</span>,
            }}
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={4}>
          <TextField
            fullWidth
            label="Max Amount"
            name={`maxAmount`}
            value={tier.maxAmount ?? ''}
            onChange={(e) => onTierChange(index, 'maxAmount', e.target.value === '' ? null : e.target.value)}
            error={Boolean(errors.maxAmount)}
            helperText={errors.maxAmount || (isLast ? 'Leave empty for no upper limit' : '')}
            size="small"
            disabled={isLast && index !== 0} // Only allow editing max amount for the last tier if it's the only tier
            type="number"
            InputProps={{
              startAdornment: <span style={{ marginRight: 8 }}>$</span>,
              endAdornment: isLast && index === 0 && (
                <Tooltip title="For the last tier, leave max amount empty to indicate no upper limit">
                  <InfoIcon color="action" fontSize="small" sx={{ ml: 1 }} />
                </Tooltip>
              ),
            }}
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={4}>
          <TextField
            fullWidth
            label="Percentage"
            name={`percentage`}
            value={tier.percentage ?? ''}
            onChange={(e) => onTierChange(index, 'percentage', e.target.value === '' ? null : e.target.value)}
            error={Boolean(errors.percentage)}
            helperText={errors.percentage}
            size="small"
            type="number"
            InputProps={{
              endAdornment: <span style={{ marginLeft: 8 }}>%</span>,
            }}
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={4}>
          <TextField
            fullWidth
            label="Fixed Amount"
            name={`fixedAmount`}
            value={tier.fixedAmount ?? ''}
            onChange={(e) => onTierChange(index, 'fixedAmount', e.target.value === '' ? null : e.target.value)}
            error={Boolean(errors.fixedAmount)}
            helperText={errors.fixedAmount || 'Use either percentage or fixed amount'}
            size="small"
            type="number"
            InputProps={{
              startAdornment: <span style={{ marginRight: 8 }}>$</span>,
            }}
          />
        </Grid>
      </Grid>
    </Box>
  );
};

// Rule form component
const RuleForm = ({
  rule,
  index,
  expanded,
  onToggleExpand,
  onRuleChange,
  onAddTier,
  onRemoveTier,
  onMoveTier,
  onRemoveRule,
  errors = {},
  readOnly = false,
}) => {
  const theme = useTheme();
  const tierErrors = errors.tiers || [];
  
  const handleFieldChange = (field, value) => {
    onRuleChange({
      ...rule,
      [field]: value,
    });
  };
  
  const handleTierChange = (tierIndex, field, value) => {
    const updatedTiers = [...(rule.tiers || [])];
    
    // Convert empty strings to null for number fields
    const processedValue = (field === 'minAmount' || field === 'maxAmount' || 
                          field === 'percentage' || field === 'fixedAmount') && 
                         value === '' ? null : value;
    
    updatedTiers[tierIndex] = {
      ...updatedTiers[tierIndex],
      [field]: processedValue,
    };
    
    // Auto-calculate next tier's min amount when max amount changes
    if (field === 'maxAmount' && processedValue !== null && 
        tierIndex < updatedTiers.length - 1) {
      updatedTiers[tierIndex + 1] = {
        ...updatedTiers[tierIndex + 1],
        minAmount: parseFloat(processedValue),
      };
    }
    
    onRuleChange({
      ...rule,
      tiers: updatedTiers,
    });
  };
  
  const handleAddTier = () => {
    const lastTier = rule.tiers[rule.tiers.length - 1];
    const minAmount = lastTier ? (lastTier.maxAmount || 0) : 0;
    
    const newTier = {
      minAmount,
      maxAmount: null,
      percentage: 10,
      fixedAmount: 0,
    };
    
    onAddTier(newTier);
  };
  
  const handleRemoveTier = (tierIndex) => {
    onRemoveTier(tierIndex);
  };
  
  const handleMoveTier = (tierIndex, direction) => {
    if (direction === 'up' && tierIndex > 0) {
      onMoveTier(tierIndex, 'up');
    } else if (direction === 'down' && tierIndex < rule.tiers.length - 1) {
      onMoveTier(tierIndex, 'down');
    }
  };
  
  return (
    <Accordion 
      expanded={expanded}
      onChange={() => onToggleExpand(index)}
      sx={{ mb: 2, boxShadow: 'none', border: `1px solid ${theme.palette.divider}` }}
    >
      <AccordionSummary
        expandIcon={<ExpandMoreIcon />}
        aria-controls={`rule-${index}-content`}
        id={`rule-${index}-header`}
        sx={{
          bgcolor: expanded ? theme.palette.action.selected : 'transparent',
          '&:hover': {
            bgcolor: theme.palette.action.hover,
          },
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
          <Box sx={{ flex: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 600, mr: 1 }}>
                {rule.description || `Rule ${index + 1}`}
              </Typography>
              <Chip 
                label={rule.type.replace('_', ' ')}
                size="small"
                color={rule.isActive ? 'primary' : 'default'}
                variant="outlined"
                sx={{ textTransform: 'capitalize', mr: 1 }}
              />
              {rule.type === 'tiered' && (
                <Chip 
                  label={`${rule.tiers?.length || 0} tiers`}
                  size="small"
                  variant="outlined"
                />
              )}
            </Box>
            
            <Box sx={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap', gap: 1 }}>
              {rule.type === 'percentage' && (
                <Chip 
                  label={`${rule.percentage}%`}
                  size="small"
                  variant="outlined"
                  color="primary"
                />
              )}
              
              {rule.type === 'fixed_amount' && (
                <Chip 
                  label={`$${rule.fixedAmount}`}
                  size="small"
                  variant="outlined"
                  color="primary"
                />
              )}
              
              {(rule.minReserve || rule.minReserve === 0) && (
                <Chip 
                  label={`Min: $${rule.minReserve}`}
                  size="small"
                  variant="outlined"
                />
              )}
              
              {(rule.maxReserve || rule.maxReserve === 0) && (
                <Chip 
                  label={`Max: $${rule.maxReserve}`}
                  size="small"
                  variant="outlined"
                />
              )}
              
              <Chip 
                label={`Priority: ${rule.priority}`}
                size="small"
                variant="outlined"
              />
              
              <FormControlLabel
                control={
                  <Switch
                    checked={rule.isActive}
                    onChange={(e) => handleFieldChange('isActive', e.target.checked)}
                    color="primary"
                    size="small"
                    disabled={readOnly}
                  />
                }
                label={rule.isActive ? 'Active' : 'Inactive'}
                labelPlacement="end"
                sx={{ m: 0, ml: 1 }}
              />
            </Box>
          </Box>
          
          {!readOnly && onRemoveRule && (
            <IconButton 
              size="small" 
              onClick={(e) => {
                e.stopPropagation();
                onRemoveRule(index);
              }}
              color="error"
            >
              <DeleteIcon />
            </IconButton>
          )}
        </Box>
      </AccordionSummary>
      
      <AccordionDetails sx={{ pt: 2, bgcolor: theme.palette.background.default }}>
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Description"
              name="description"
              value={rule.description || ''}
              onChange={(e) => handleFieldChange('description', e.target.value)}
              error={Boolean(errors.description)}
              helperText={errors.description || 'A brief description of this rule'}
              size="small"
              disabled={readOnly}
            />
          </Grid>
          
          <Grid item xs={12} md={6}>
            <FormControl fullWidth size="small">
              <TextField
                select
                fullWidth
                label="Type"
                name="type"
                value={rule.type}
                onChange={(e) => handleFieldChange('type', e.target.value)}
                error={Boolean(errors.type)}
                helperText={errors.type}
                disabled={readOnly}
                SelectProps={{
                  native: true,
                }}
              >
                <option value="percentage">Percentage</option>
                <option value="fixed_amount">Fixed Amount</option>
                <option value="tiered">Tiered</option>
              </TextField>
            </FormControl>
          </Grid>
          
          {rule.type === 'percentage' && (
            <Grid item xs={12} sm={6} md={4}>
              <TextField
                fullWidth
                label="Percentage"
                name="percentage"
                value={rule.percentage ?? ''}
                onChange={(e) => handleFieldChange('percentage', e.target.value === '' ? null : e.target.value)}
                error={Boolean(errors.percentage)}
                helperText={errors.percentage || 'The percentage to reserve'}
                size="small"
                type="number"
                disabled={readOnly}
                InputProps={{
                  endAdornment: <span style={{ marginLeft: 8 }}>%</span>,
                }}
              />
            </Grid>
          )}
          
          {rule.type === 'fixed_amount' && (
            <Grid item xs={12} sm={6} md={4}>
              <TextField
                fullWidth
                label="Fixed Amount"
                name="fixedAmount"
                value={rule.fixedAmount ?? ''}
                onChange={(e) => handleFieldChange('fixedAmount', e.target.value === '' ? null : e.target.value)}
                error={Boolean(errors.fixedAmount)}
                helperText={errors.fixedAmount || 'The fixed amount to reserve'}
                size="small"
                type="number"
                disabled={readOnly}
                InputProps={{
                  startAdornment: <span style={{ marginRight: 8 }}>$</span>,
                }}
              />
            </Grid>
          )}
          
          {rule.type === 'tiered' && (
            <Grid item xs={12}>
              <Box sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                  <Typography variant="subtitle2">Tiers</Typography>
                  {!readOnly && (
                    <Button
                      size="small"
                      startIcon={<AddIcon />}
                      onClick={handleAddTier}
                      disabled={readOnly}
                    >
                      Add Tier
                    </Button>
                  )}
                </Box>
                
                {(!rule.tiers || rule.tiers.length === 0) ? (
                  <Box sx={{ p: 3, textAlign: 'center', border: `1px dashed ${theme.palette.divider}`, borderRadius: 1 }}>
                    <Typography variant="body2" color="textSecondary">
                      No tiers defined. Add your first tier to get started.
                    </Typography>
                  </Box>
                ) : (
                  <Box>
                    {rule.tiers.map((tier, tierIndex) => (
                      <TierForm
                        key={tierIndex}
                        tier={tier}
                        index={tierIndex}
                        onTierChange={handleTierChange}
                        onRemoveTier={handleRemoveTier}
                        onMoveTier={handleMoveTier}
                        isFirst={tierIndex === 0}
                        isLast={tierIndex === rule.tiers.length - 1}
                        totalTiers={rule.tiers.length}
                        errors={tierErrors[tierIndex] || {}}
                      />
                    ))}
                    
                    {errors.tiers && typeof errors.tiers === 'string' && (
                      <Typography color="error" variant="caption">
                        {errors.tiers}
                      </Typography>
                    )}
                  </Box>
                )}
              </Box>
            </Grid>
          )}
          
          <Grid item xs={12} sm={6} md={4}>
            <TextField
              fullWidth
              label="Minimum Reserve"
              name="minReserve"
              value={rule.minReserve ?? ''}
              onChange={(e) => handleFieldChange('minReserve', e.target.value === '' ? null : e.target.value)}
              error={Boolean(errors.minReserve)}
              helperText={errors.minReserve || 'Minimum reserve amount (optional)'}
              size="small"
              type="number"
              disabled={readOnly}
              InputProps={{
                startAdornment: <span style={{ marginRight: 8 }}>$</span>,
              }}
            />
          </Grid>
          
          <Grid item xs={12} sm={6} md={4}>
            <TextField
              fullWidth
              label="Maximum Reserve"
              name="maxReserve"
              value={rule.maxReserve ?? ''}
              onChange={(e) => handleFieldChange('maxReserve', e.target.value === '' ? null : e.target.value)}
              error={Boolean(errors.maxReserve)}
              helperText={errors.maxReserve || 'Maximum reserve amount (optional)'}
              size="small"
              type="number"
              disabled={readOnly}
              InputProps={{
                startAdornment: <span style={{ marginRight: 8 }}>$</span>,
              }}
            />
          </Grid>
          
          <Grid item xs={12} sm={6} md={4}>
            <TextField
              fullWidth
              label="Priority"
              name="priority"
              value={rule.priority ?? ''}
              onChange={(e) => handleFieldChange('priority', e.target.value === '' ? null : e.target.value)}
              error={Boolean(errors.priority)}
              helperText={errors.priority || 'Higher numbers have higher priority'}
              size="small"
              type="number"
              disabled={readOnly}
              InputProps={{
                inputProps: { min: 0, step: 1 },
              }}
            />
          </Grid>
        </Grid>
      </AccordionDetails>
    </Accordion>
  );
};

// Main ReserveRuleList component
const ReserveRuleList = ({
  rules = [],
  onRuleChange,
  onRuleAdd,
  onRuleRemove,
  onRuleMove,
  readOnly = false,
  errors = {},
}) => {
  const [expandedRule, setExpandedRule] = useState(0);
  
  // Handle adding a new rule
  const handleAddRule = () => {
    const newRule = {
      type: 'percentage',
      percentage: 10,
      minReserve: 0,
      maxReserve: null,
      priority: rules.length,
      isActive: true,
      description: '',
      tiers: [],
    };
    
    if (onRuleAdd) {
      onRuleAdd(newRule);
    }
    
    // Expand the new rule
    setExpandedRule(rules.length);
  };
  
  // Handle rule change
  const handleRuleChange = (index, updatedRule) => {
    if (onRuleChange) {
      onRuleChange(index, updatedRule);
    }
  };
  
  // Handle rule removal
  const handleRemoveRule = (index) => {
    if (onRuleRemove) {
      onRuleRemove(index);
    }
    
    // Adjust expanded rule index if needed
    if (expandedRule >= index && expandedRule > 0) {
      setExpandedRule(expandedRule - 1);
    }
  };
  
  // Handle rule movement
  const handleMoveRule = (fromIndex, direction) => {
    if (onRuleMove) {
      onRuleMove(fromIndex, direction);
    }
    
    // Update expanded rule index if needed
    if (direction === 'up' && expandedRule === fromIndex - 1) {
      setExpandedRule(fromIndex);
    } else if (direction === 'down' && expandedRule === fromIndex + 1) {
      setExpandedRule(fromIndex);
    } else if (expandedRule === fromIndex) {
      setExpandedRule(direction === 'up' ? fromIndex - 1 : fromIndex + 1);
    }
  };
  
  // Handle adding a tier to a rule
  const handleAddTier = (ruleIndex, newTier) => {
    const updatedRules = [...rules];
    const rule = { ...updatedRules[ruleIndex] };
    
    if (!rule.tiers) {
      rule.tiers = [];
    }
    
    rule.tiers = [...rule.tiers, newTier];
    updatedRules[ruleIndex] = rule;
    
    if (onRuleChange) {
      onRuleChange(ruleIndex, rule);
    }
  };
  
  // Handle removing a tier from a rule
  const handleRemoveTier = (ruleIndex, tierIndex) => {
    const updatedRules = [...rules];
    const rule = { ...updatedRules[ruleIndex] };
    
    if (rule.tiers && rule.tiers.length > tierIndex) {
      rule.tiers = rule.tiers.filter((_, i) => i !== tierIndex);
      updatedRules[ruleIndex] = rule;
      
      if (onRuleChange) {
        onRuleChange(ruleIndex, rule);
      }
    }
  };
  
  // Handle moving a tier within a rule
  const handleMoveTier = (ruleIndex, tierIndex, direction) => {
    if (direction !== 'up' && direction !== 'down') return;
    
    const updatedRules = [...rules];
    const rule = { ...updatedRules[ruleIndex] };
    
    if (!rule.tiers || rule.tiers.length <= 1) return;
    
    const newTiers = [...rule.tiers];
    
    if (direction === 'up' && tierIndex > 0) {
      // Swap with previous tier
      const temp = newTiers[tierIndex - 1];
      newTiers[tierIndex - 1] = newTiers[tierIndex];
      newTiers[tierIndex] = temp;
      
      // Update min/max amounts to maintain continuity
      if (tierIndex > 1) {
        newTiers[tierIndex - 1].minAmount = newTiers[tierIndex - 2].maxAmount;
      }
      
      if (tierIndex < newTiers.length - 1) {
        newTiers[tierIndex].maxAmount = newTiers[tierIndex + 1].minAmount;
      }
      
      rule.tiers = newTiers;
      updatedRules[ruleIndex] = rule;
      
      if (onRuleChange) {
        onRuleChange(ruleIndex, rule);
      }
    } else if (direction === 'down' && tierIndex < newTiers.length - 1) {
      // Swap with next tier
      const temp = newTiers[tierIndex + 1];
      newTiers[tierIndex + 1] = newTiers[tierIndex];
      newTiers[tierIndex] = temp;
      
      // Update min/max amounts to maintain continuity
      if (tierIndex > 0) {
        newTiers[tierIndex].minAmount = newTiers[tierIndex - 1].maxAmount;
      }
      
      if (tierIndex < newTiers.length - 2) {
        newTiers[tierIndex + 1].maxAmount = newTiers[tierIndex + 2].minAmount;
      }
      
      rule.tiers = newTiers;
      updatedRules[ruleIndex] = rule;
      
      if (onRuleChange) {
        onRuleChange(ruleIndex, rule);
      }
    }
  };
  
  // Toggle rule expansion
  const handleToggleExpand = (index) => {
    setExpandedRule(expandedRule === index ? -1 : index);
  };
  
  return (
    <Box>
      {rules.length === 0 ? (
        <Box 
          sx={{ 
            p: 4, 
            textAlign: 'center', 
            border: '1px dashed', 
            borderColor: 'divider',
            borderRadius: 1,
            mb: 2,
          }}
        >
          <Typography variant="body1" color="textSecondary" gutterBottom>
            No reserve rules defined
          </Typography>
          {!readOnly && (
            <Button
              variant="outlined"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleAddRule}
            >
              Add Your First Rule
            </Button>
          )}
        </Box>
      ) : (
        <>
          {rules.map((rule, index) => (
            <RuleForm
              key={index}
              rule={rule}
              index={index}
              expanded={expandedRule === index}
              onToggleExpand={handleToggleExpand}
              onRuleChange={(updatedRule) => handleRuleChange(index, updatedRule)}
              onAddTier={(newTier) => handleAddTier(index, newTier)}
              onRemoveTier={(tierIndex) => handleRemoveTier(index, tierIndex)}
              onMoveTier={(tierIndex, direction) => handleMoveTier(index, tierIndex, direction)}
              onRemoveRule={onRuleRemove ? () => handleRemoveRule(index) : null}
              errors={errors[index] || {}}
              readOnly={readOnly}
            />
          ))}
          
          {!readOnly && onRuleAdd && (
            <Box sx={{ mt: 2, textAlign: 'center' }}>
              <Button
                variant="outlined"
                color="primary"
                startIcon={<AddIcon />}
                onClick={handleAddRule}
              >
                Add Another Rule
              </Button>
            </Box>
          )}
        </>
      )}
      
      {errors.rules && typeof errors.rules === 'string' && (
        <Typography color="error" variant="body2" sx={{ mt: 1 }}>
          {errors.rules}
        </Typography>
      )}
    </Box>
  );
};

export default ReserveRuleList;
