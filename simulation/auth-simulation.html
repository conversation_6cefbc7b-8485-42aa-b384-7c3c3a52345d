<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Gateway - Authentication Simulation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            display: flex;
            gap: 20px;
        }
        .panel {
            flex: 1;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"],
        input[type="password"],
        input[type="email"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        .response {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #fff;
            min-height: 100px;
            white-space: pre-wrap;
            font-family: monospace;
            overflow: auto;
        }
        .tab {
            cursor: pointer;
            padding: 10px 20px;
            border: 1px solid #ddd;
            display: inline-block;
            margin-right: 5px;
            border-radius: 4px 4px 0 0;
            background-color: #f1f1f1;
        }
        .tab.active {
            background-color: #4CAF50;
            color: white;
            border-bottom: 1px solid #4CAF50;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <h1>Payment Gateway - Authentication Flow Simulation</h1>
    
    <div class="tabs">
        <div class="tab active" onclick="showTab('login')">Login</div>
        <div class="tab" onclick="showTab('profile')">Profile</div>
        <div class="tab" onclick="showTab('register')">Register</div>
    </div>
    
    <div class="container">
        <div class="panel">
            <!-- Login Form -->
            <div id="login" class="tab-content active">
                <h2>Login</h2>
                <div class="form-group">
                    <label for="login-email">Email:</label>
                    <input type="email" id="login-email" value="<EMAIL>">
                </div>
                <div class="form-group">
                    <label for="login-password">Password:</label>
                    <input type="password" id="login-password" value="Merchant@123">
                </div>
                <button onclick="login()">Login</button>
                <button onclick="simulateFailedLogin()">Simulate Failed Login</button>
            </div>
            
            <!-- Profile Section -->
            <div id="profile" class="tab-content">
                <h2>Profile</h2>
                <div id="profile-loading">Please login first</div>
                <div id="profile-content" style="display: none;">
                    <div class="form-group">
                        <label>Name:</label>
                        <input type="text" id="profile-name" value="">
                    </div>
                    <div class="form-group">
                        <label>Business Name:</label>
                        <input type="text" id="business-name" value="">
                    </div>
                    <div class="form-group">
                        <label>Business Type:</label>
                        <input type="text" id="business-type" value="">
                    </div>
                    <div class="form-group">
                        <label>Phone:</label>
                        <input type="text" id="profile-phone" value="">
                    </div>
                    <div class="form-group">
                        <label>Website:</label>
                        <input type="text" id="profile-website" value="">
                    </div>
                    <button onclick="updateProfile()">Update Profile</button>
                    <button onclick="getProfile()">Refresh Profile</button>
                </div>
            </div>
            
            <!-- Register Form -->
            <div id="register" class="tab-content">
                <h2>Register New Merchant</h2>
                <div class="form-group">
                    <label for="reg-name">Full Name:</label>
                    <input type="text" id="reg-name" value="New Merchant">
                </div>
                <div class="form-group">
                    <label for="reg-email">Email:</label>
                    <input type="email" id="reg-email" value="<EMAIL>">
                </div>
                <div class="form-group">
                    <label for="reg-password">Password:</label>
                    <input type="password" id="reg-password" value="New@1234">
                </div>
                <div class="form-group">
                    <label for="reg-business">Business Name:</label>
                    <input type="text" id="reg-business" value="New Business Inc">
                </div>
                <button onclick="register()">Register</button>
            </div>
        </div>
        
        <div class="panel">
            <h2>API Response</h2>
            <div id="response" class="response">
                Response will appear here...
            </div>
            
            <h3>Current State</h3>
            <div id="state" class="response">
                Not logged in
            </div>
        </div>
    </div>

    <script>
        // Global state
        let authToken = localStorage.getItem('authToken') || '';
        let currentUser = JSON.parse(localStorage.getItem('currentUser') || 'null');
        let loginAttempts = 0;
        
        // Initialize the UI
        document.addEventListener('DOMContentLoaded', () => {
            updateUI();
        });
        
        // Tab navigation
        function showTab(tabId) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Remove active class from all tabs
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show the selected tab content
            document.getElementById(tabId).classList.add('active');
            
            // Highlight the clicked tab
            event.target.classList.add('active');
            
            // If showing profile tab and user is logged in, load profile
            if (tabId === 'profile' && authToken) {
                getProfile();
            }
        }
        
        // Update UI based on authentication state
        function updateUI() {
            const profileContent = document.getElementById('profile-content');
            const profileLoading = document.getElementById('profile-loading');
            const stateDiv = document.getElementById('state');
            
            if (authToken && currentUser) {
                // User is logged in
                stateDiv.innerHTML = `Logged in as: ${currentUser.email} (${currentUser.role})`;
                
                // Show profile content if on profile tab
                if (document.querySelector('#profile').classList.contains('active')) {
                    profileContent.style.display = 'block';
                    profileLoading.style.display = 'none';
                }
            } else {
                // User is not logged in
                stateDiv.innerHTML = 'Not logged in';
                profileContent.style.display = 'none';
                profileLoading.style.display = 'block';
                profileLoading.textContent = 'Please login first';
            }
        }
        
        // Simulate login API call
        async function login() {
            const email = document.getElementById('login-email').value;
            const password = document.getElementById('login-password').value;
            
            // Basic validation
            if (!email || !password) {
                showResponse('Error', 'Please enter both email and password');
                return;
            }
            
            // Show loading state
            showResponse('Logging in...', 'Sending request to server...');
            
            // Simulate API delay
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Mock successful <NAME_EMAIL> / demo123
            if (email === '<EMAIL>' && password === 'Merchant@123') {
                // Reset login attempts on successful login
                loginAttempts = 0;
                
                // Mock response
                const mockResponse = {
                    success: true,
                    token: 'mock-jwt-token-' + Math.random().toString(36).substr(2, 9),
                    user: {
                        id: 'user_' + Math.random().toString(36).substr(2, 8),
                        name: 'Demo Merchant',
                        email: email,
                        role: 'merchant',
                        isVerified: true,
                        businessName: 'Demo Business Inc',
                        businessType: 'Retail',
                        phone: '+1234567890',
                        website: 'https://demo-merchant.com'
                    }
                };
                
                // Save auth data
                authToken = mockResponse.token;
                currentUser = mockResponse.user;
                localStorage.setItem('authToken', authToken);
                localStorage.setItem('currentUser', JSON.stringify(currentUser));
                
                // Show success
                showResponse('Login Successful', mockResponse);
                updateUI();
                
                // If on profile tab, load profile data
                if (document.querySelector('#profile').classList.contains('active')) {
                    getProfile();
                }
                
            } else {
                // Failed login
                loginAttempts++;
                
                // Check if account should be locked
                if (loginAttempts >= 3) {
                    showResponse('Login Failed', {
                        success: false,
                        message: 'Too many failed attempts. Account locked for 1 hour.',
                        locked: true
                    });
                } else {
                    showResponse('Login Failed', {
                        success: false,
                        message: 'Invalid email or password',
                        attemptsRemaining: 3 - loginAttempts
                    });
                }
            }
        }
        
        // Simulate failed login
        function simulateFailedLogin() {
            document.getElementById('login-password').value = 'wrongpassword';
            login();
        }
        
        // Get user profile
        async function getProfile() {
            if (!authToken) {
                showResponse('Error', 'Not authenticated');
                return;
            }
            
            // Show loading
            document.getElementById('profile-content').style.display = 'none';
            document.getElementById('profile-loading').style.display = 'block';
            document.getElementById('profile-loading').textContent = 'Loading profile...';
            
            // Simulate API delay
            await new Promise(resolve => setTimeout(resolve, 800));
            
            try {
                // Mock profile data
                const mockProfile = {
                    success: true,
                    merchant: {
                        _id: currentUser.id,
                        name: currentUser.name,
                        email: currentUser.email,
                        role: currentUser.role,
                        isVerified: currentUser.isVerified,
                        businessName: currentUser.businessName || 'Demo Business Inc',
                        businessType: currentUser.businessType || 'Retail',
                        phone: currentUser.phone || '+1234567890',
                        website: currentUser.website || 'https://demo-merchant.com',
                        isActive: true,
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString()
                    }
                };
                
                // Update form fields
                document.getElementById('profile-name').value = mockProfile.merchant.name;
                document.getElementById('business-name').value = mockProfile.merchant.businessName;
                document.getElementById('business-type').value = mockProfile.merchant.businessType;
                document.getElementById('profile-phone').value = mockProfile.merchant.phone;
                document.getElementById('profile-website').value = mockProfile.merchant.website;
                
                // Show profile content
                document.getElementById('profile-content').style.display = 'block';
                document.getElementById('profile-loading').style.display = 'none';
                
                showResponse('Profile Loaded', mockProfile);
                
            } catch (error) {
                showResponse('Error', 'Failed to load profile: ' + error.message);
                document.getElementById('profile-loading').textContent = 'Failed to load profile';
            }
        }
        
        // Update profile
        async function updateProfile() {
            if (!authToken) {
                showResponse('Error', 'Not authenticated');
                return;
            }
            
            // Get form values
            const updates = {
                name: document.getElementById('profile-name').value,
                businessName: document.getElementById('business-name').value,
                businessType: document.getElementById('business-type').value,
                phone: document.getElementById('profile-phone').value,
                website: document.getElementById('profile-website').value
            };
            
            // Show loading
            const updateButton = document.querySelector('#profile button');
            const originalText = updateButton.textContent;
            updateButton.textContent = 'Updating...';
            updateButton.disabled = true;
            
            // Simulate API delay
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            try {
                // Mock successful update
                const mockResponse = {
                    success: true,
                    merchant: {
                        ...updates,
                        _id: currentUser.id,
                        email: currentUser.email,
                        role: currentUser.role,
                        isVerified: currentUser.isVerified,
                        isActive: true,
                        updatedAt: new Date().toISOString()
                    }
                };
                
                // Update current user
                currentUser = { ...currentUser, ...updates };
                localStorage.setItem('currentUser', JSON.stringify(currentUser));
                
                showResponse('Profile Updated', mockResponse);
                
            } catch (error) {
                showResponse('Error', 'Failed to update profile: ' + error.message);
            } finally {
                updateButton.textContent = originalText;
                updateButton.disabled = false;
            }
        }
        
        // Register new user
        async function register() {
            const userData = {
                name: document.getElementById('reg-name').value,
                email: document.getElementById('reg-email').value,
                password: document.getElementById('reg-password').value,
                businessName: document.getElementById('reg-business').value,
                role: 'merchant'
            };
            
            // Basic validation
            if (!userData.name || !userData.email || !userData.password || !userData.businessName) {
                showResponse('Error', 'All fields are required');
                return;
            }
            
            // Show loading
            const registerButton = document.querySelector('#register button');
            const originalText = registerButton.textContent;
            registerButton.textContent = 'Registering...';
            registerButton.disabled = true;
            
            // Simulate API delay
            await new Promise(resolve => setTimeout(resolve, 1500));
            
            try {
                // Mock successful registration
                const mockResponse = {
                    success: true,
                    message: 'Registration successful! Please check your email to verify your account.',
                    user: {
                        id: 'user_' + Math.random().toString(36).substr(2, 8),
                        name: userData.name,
                        email: userData.email,
                        role: 'merchant',
                        isVerified: false,
                        businessName: userData.businessName
                    }
                };
                
                showResponse('Registration Successful', mockResponse);
                
                // Clear form
                document.getElementById('reg-name').value = '';
                document.getElementById('reg-email').value = '';
                document.getElementById('reg-password').value = '';
                document.getElementById('reg-business').value = '';
                
                // Switch to login tab
                showTab('login');
                
            } catch (error) {
                showResponse('Error', 'Registration failed: ' + error.message);
            } finally {
                registerButton.textContent = originalText;
                registerButton.disabled = false;
            }
        }
        
        // Helper function to display API responses
        function showResponse(title, data) {
            const responseDiv = document.getElementById('response');
            responseDiv.innerHTML = `<strong>${title}:</strong>\n` + 
                JSON.stringify(data, null, 2);
        }
        
        // Logout function
        function logout() {
            authToken = '';
            currentUser = null;
            localStorage.removeItem('authToken');
            localStorage.removeItem('currentUser');
            updateUI();
            showTab('login');
            showResponse('Logged Out', 'You have been successfully logged out.');
        }
        
        // Add logout button to the page
        document.addEventListener('DOMContentLoaded', () => {
            const header = document.querySelector('h1');
            const logoutBtn = document.createElement('button');
            logoutBtn.textContent = 'Logout';
            logoutBtn.onclick = logout;
            logoutBtn.style.position = 'absolute';
            logoutBtn.style.top = '20px';
            logoutBtn.style.right = '20px';
            document.body.appendChild(logoutBtn);
            
            // Initialize UI
            updateUI();
        });
    </script>
</body>
</html>
