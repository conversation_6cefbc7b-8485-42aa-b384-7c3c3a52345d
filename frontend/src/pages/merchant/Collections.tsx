import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Download, Filter, Search } from 'lucide-react';
import { Input } from '../../components/ui/input';
import { DataTable } from '../../components/ui/data-table';
import { columns, type Collection } from './components/collections-columns';
import { useCollections } from '../../services/api/merchant';

const CollectionsPage = () => {
  // State for pagination and search
  const [page, setPage] = React.useState(1);
  const [search, setSearch] = React.useState('');
  const [statusFilter, setStatusFilter] = React.useState<'pending' | 'completed' | 'failed' | undefined>();

  // Use the useCollections hook to fetch data with pagination
  const { 
    data: collectionsResponse, 
    isLoading, 
    error 
  } = useCollections({
    page,
    limit: 10,
    search: search || undefined,
    status: statusFilter,
  });

  // Extract the collections data from the paginated response
  const collections = collectionsResponse?.data || [];
  const totalItems = collectionsResponse?.total || 0;
  const totalPages = collectionsResponse?.totalPages || 1;

  // Handle search input change with debounce
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Debounce search to avoid too many API calls
    const timer = setTimeout(() => {
      setSearch(value);
      setPage(1); // Reset to first page on new search
    }, 500);
    
    return () => clearTimeout(timer);
  };

  // Transform the data to match the Collection type expected by the table
  const tableData: Collection[] = React.useMemo(() => {
    return collections.map(collection => ({
      id: collection.id,
      trader: collection.trader?.name || 'Unknown Trader',
      amount: collection.amount,
      status: collection.status as 'completed' | 'pending' | 'failed',
      date: new Date(collection.createdAt).toLocaleDateString(),
      reference: collection.reference,
    }));
  }, [collections]);

  if (isLoading && page === 1) {
    return <div>Loading collections...</div>;
  }

  if (error) {
    return <div>Error loading collections: {error.message}</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Collections</h2>
          <p className="text-muted-foreground">
            View and manage payments collected by traders
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" className="h-8">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Recent Collections</CardTitle>
            <div className="flex items-center space-x-2">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search collections..."
                  className="pl-8 sm:w-[300px]"
                />
              </div>
              <Button variant="outline" size="sm" className="h-9">
                <Filter className="mr-2 h-4 w-4" />
                Filter
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <DataTable<Collection, any> 
            columns={columns} 
            data={tableData} 
            page={page}
            totalPages={totalPages}
            totalItems={totalItems}
            onPageChange={setPage}
            isLoading={isLoading}
          />
        </CardContent>
      </Card>
    </div>
  );
};

export default CollectionsPage;
