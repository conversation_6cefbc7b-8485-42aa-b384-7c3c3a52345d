[2025-07-26 11:14:44.722] [31merror[39m: 	[31mMissing required environment variables: JWT_REFRESH_SECRET, COOKIE_SECRET, API_VERSION[39m
{
  "service": "payment-gateway"
}
  console.warn
    Test environment - continuing despite missing vars: Missing required environment variables: JWT_REFRESH_SECRET, COOKIE_SECRET, API_VERSION

      47 |     process.exit(1);
      48 |   } else {
    > 49 |     console.warn('Test environment - continuing despite missing vars:', errorMsg);
         |             ^
      50 |   }
      51 | }
      52 |

      at Object.<anonymous> (app.js:49:13)
      at Object.<anonymous> (test/__tests__/security/settlementSecurity.test.js:4:23)

[2025-07-26 11:14:44.896] [32minfo[39m: 	[32mEmail server connection established[39m
{
  "service": "payment-gateway"
}
  console.log
    Running global test setup...

      at Object.<anonymous> (test/setup.js:22:13)

[2025-07-26 11:14:45.052] [32minfo[39m: 	[32mStarting in-memory MongoDB server...[39m
{
  "service": "payment-gateway"
}
[2025-07-26 11:14:45.932] [32minfo[39m: 	[32mConnecting to in-memory MongoDB at mongodb://127.0.0.1:4876/...[39m
{
  "service": "payment-gateway"
}
[2025-07-26 11:14:46.002] [32minfo[39m: 	[32mSuccessfully connected to in-memory MongoDB[39m
{
  "service": "payment-gateway"
}
  console.log
    Environment: test

      at Object.<anonymous> (test/__tests__/security/settlementSecurity.test.js:21:11)

  console.log
    MongoDB URI: ${PRODUCTION_MONGODB_URI}

      at Object.<anonymous> (test/__tests__/security/settlementSecurity.test.js:22:11)

[2025-07-26 11:14:46.011] [32minfo[39m: 	[32mSocket.IO server initialized[39m
{
  "service": "payment-gateway"
}
  console.log
    Starting test setup...

      at Object.<anonymous> (test/__tests__/security/settlementSecurity.test.js:50:15)

  console.log
    Clearing existing test users...

      at Object.<anonymous> (test/__tests__/security/settlementSecurity.test.js:53:15)

  console.log
    Deleted existing test users: { acknowledged: true, deletedCount: 0 }

      at Object.<anonymous> (test/__tests__/security/settlementSecurity.test.js:59:15)

  console.log
    Creating admin user...

      at Object.<anonymous> (test/__tests__/security/settlementSecurity.test.js:62:15)

  console.log
    Created admin user: {
      id: new ObjectId("68846b4f21dba6d87d62d9de"),
      email: '<EMAIL>'
    }

      at Object.<anonymous> (test/__tests__/security/settlementSecurity.test.js:76:15)

  console.log
    Logging in admin user...

      at Object.<anonymous> (test/__tests__/security/settlementSecurity.test.js:111:13)

  console.log
    Admin login status: 200

      at Object.<anonymous> (test/__tests__/security/settlementSecurity.test.js:116:13)

  console.log
    Admin login headers: {
      'content-security-policy': "frame-ancestors 'self'",
      'cross-origin-opener-policy': 'same-origin',
      'cross-origin-resource-policy': 'same-origin',
      'origin-agent-cluster': '?1',
      'referrer-policy': 'same-origin',
      'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
      'x-content-type-options': 'nosniff',
      'x-dns-prefetch-control': 'off',
      'x-download-options': 'noopen',
      'x-frame-options': 'SAMEORIGIN',
      'x-permitted-cross-domain-policies': 'none',
      'x-xss-protection': '1; mode=block',
      vary: 'Origin',
      'access-control-allow-credentials': 'true',
      'access-control-expose-headers': 'Set-Cookie,Date,ETag',
      'access-control-allow-origin': 'http://localhost:3000',
      'retry-after': '900',
      'x-ratelimit-limit': '5',
      'x-ratelimit-remaining': '4',
      'x-ratelimit-reset': '2025-07-26T05:59:47.962Z',
      'set-cookie': [
        'refreshToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4ODQ2YjRmMjFkYmE2ZDg3ZDYyZDlkZSIsImlhdCI6MTc1MzUwODY4OCwiZXhwIjoxNzU0MTEzNDg4fQ.Gg8kflW7UMteM8GBvtygE_PANYbVLd4_O0j_Khh9rEE; Max-Age=604800; Path=/api/v1/auth/refresh-token; Expires=Sat, 02 Aug 2025 05:44:48 GMT; HttpOnly; SameSite=Strict'
      ],
      'content-type': 'application/json; charset=utf-8',
      'content-length': '847',
      etag: 'W/"34f-nwh4tWBP+Dhs/gfCCp+BVihjyv4"',
      date: 'Sat, 26 Jul 2025 05:44:48 GMT',
      connection: 'close'
    }

      at Object.<anonymous> (test/__tests__/security/settlementSecurity.test.js:117:13)

  console.log
    Admin login body: {
      "success": true,
      "message": "Login successful",
      "data": {
        "user": {
          "security": {
            "passwordHistory": [],
            "loginHistory": [
              {
                "timestamp": "2025-07-26T05:44:48.256Z",
                "success": true,
                "_id": "68846b5021dba6d87d62d9e5"
              }
            ]
          },
          "_id": "68846b4f21dba6d87d62d9de",
          "name": "Admin User",
          "email": "<EMAIL>",
          "role": "admin",
          "isVerified": true,
          "twoFactorEnabled": false,
          "isActive": true,
          "loginAttempts": 0,
          "twoFactorRecoveryCodes": [],
          "sessions": [],
          "createdAt": "2025-07-26T05:44:47.052Z",
          "updatedAt": "2025-07-26T05:44:48.262Z",
          "__v": 1,
          "lastLogin": "2025-07-26T05:44:48.258Z",
          "lastActive": "2025-07-26T05:44:48.258Z"
        },
        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************.tTjjaBrdU8nOP_a05DZ8EtRW_dLxtudh9oYyqClvkCM"
      }
    }

      at Object.<anonymous> (test/__tests__/security/settlementSecurity.test.js:118:13)

  console.log
    Successfully extracted admin token

      at Object.<anonymous> (test/__tests__/security/settlementSecurity.test.js:125:13)

  console.log
    Merchant login response: {
      "success": true,
      "message": "Login successful",
      "data": {
        "user": {
          "security": {
            "passwordHistory": [],
            "loginHistory": [
              {
                "timestamp": "2025-07-26T05:44:48.575Z",
                "success": true,
                "_id": "68846b5021dba6d87d62d9e8"
              }
            ]
          },
          "_id": "68846b4f21dba6d87d62d9e0",
          "name": "Merchant User",
          "email": "<EMAIL>",
          "role": "merchant",
          "isVerified": true,
          "twoFactorEnabled": false,
          "isActive": true,
          "loginAttempts": 0,
          "twoFactorRecoveryCodes": [],
          "sessions": [],
          "createdAt": "2025-07-26T05:44:47.351Z",
          "updatedAt": "2025-07-26T05:44:48.578Z",
          "__v": 1,
          "lastLogin": "2025-07-26T05:44:48.575Z",
          "lastActive": "2025-07-26T05:44:48.575Z"
        },
        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************.2Ct2P14hVvCzlhNlShA-pjgWpRZcad5YArPqpso3Z_c"
      }
    }

      at Object.<anonymous> (test/__tests__/security/settlementSecurity.test.js:130:13)

  console.log
    Extracted merchant token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************.2Ct2P14hVvCzlhNlShA-pjgWpRZcad5YArPqpso3Z_c

      at Object.<anonymous> (test/__tests__/security/settlementSecurity.test.js:135:13)

  console.log
    Trader login response: {
      "success": true,
      "message": "Login successful",
      "data": {
        "user": {
          "security": {
            "passwordHistory": [],
            "loginHistory": [
              {
                "timestamp": "2025-07-26T05:44:48.872Z",
                "success": true,
                "_id": "68846b5021dba6d87d62d9eb"
              }
            ]
          },
          "_id": "68846b4f21dba6d87d62d9e2",
          "name": "Trader User",
          "email": "<EMAIL>",
          "role": "trader",
          "isVerified": true,
          "twoFactorEnabled": false,
          "isActive": true,
          "loginAttempts": 0,
          "twoFactorRecoveryCodes": [],
          "sessions": [],
          "createdAt": "2025-07-26T05:44:47.633Z",
          "updatedAt": "2025-07-26T05:44:48.875Z",
          "__v": 1,
          "lastLogin": "2025-07-26T05:44:48.873Z",
          "lastActive": "2025-07-26T05:44:48.873Z"
        },
        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************.MjAuoR7uPOfFx69zbU0EylyuPSL3fJajsKTyEGIL8GM"
      }
    }

      at Object.<anonymous> (test/__tests__/security/settlementSecurity.test.js:140:13)

  console.log
    Extracted trader token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************.MjAuoR7uPOfFx69zbU0EylyuPSL3fJajsKTyEGIL8GM

      at Object.<anonymous> (test/__tests__/security/settlementSecurity.test.js:145:13)

  console.error
    API Error: ApiError: Not authorized to access this route
        at auth (/Users/<USER>/Documents/payment-main/backend/middleware/auth.js:61:19)
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)
        at Function.handle (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:175:3)
        at router (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:47:12)
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)
        at Function.handle (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:175:3)
        at router (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:47:12)
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)
        at Object.<anonymous>.securityHeaders (/Users/<USER>/Documents/payment-main/backend/middleware/security.js:89:5)
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)
        at /Users/<USER>/Documents/payment-main/backend/middleware/security.js:78:5
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)
        at /Users/<USER>/Documents/payment-main/backend/middleware/security.js:72:5
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)
        at /Users/<USER>/Documents/payment-main/backend/middleware/security.js:66:5
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)
        at cors (/Users/<USER>/Documents/payment-main/node_modules/cors/lib/index.js:188:7)
        at /Users/<USER>/Documents/payment-main/node_modules/cors/lib/index.js:224:17
        at originCallback (/Users/<USER>/Documents/payment-main/node_modules/cors/lib/index.js:214:15)
        at /Users/<USER>/Documents/payment-main/node_modules/cors/lib/index.js:219:13
        at optionsCallback (/Users/<USER>/Documents/payment-main/node_modules/cors/lib/index.js:199:9)
        at corsMiddleware (/Users/<USER>/Documents/payment-main/node_modules/cors/lib/index.js:204:7)
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:537:6)
        at xXssProtectionMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:315:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at xPoweredByMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:308:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at xPermittedCrossDomainPoliciesMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:301:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at xFrameOptionsMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:285:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at xDownloadOptionsMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:265:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at xDnsPrefetchControlMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:258:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at xContentTypeOptionsMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:250:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at strictTransportSecurityMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:243:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at referrerPolicyMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:211:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at originAgentClusterMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:186:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at crossOriginResourcePolicyMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:179:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at crossOriginOpenerPolicyMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:163:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at contentSecurityPolicyMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:128:4)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at helmetMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:539:6)
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)
        at /Users/<USER>/Documents/payment-main/node_modules/express-mongo-sanitize/index.js:122:5
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)
        at Object.<anonymous>.mongoSanitize.onSanitize.req.req (/Users/<USER>/Documents/payment-main/backend/middleware/security.js:174:7)
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10) {
      statusCode: 401,
      status: 'fail',
      code: 'ERR_401',
      details: {},
      isOperational: true,
      timestamp: '2025-07-26T05:44:48.898Z'
    }

      42 | // Error handling middleware
      43 | router.use((err, req, res, next) => {
    > 44 |   console.error('API Error:', err);
         |           ^
      45 |   
      46 |   const statusCode = err.statusCode || 500;
      47 |   const errorResponse = {

      at routes/v1/index.js:44:11
      at Layer.handle_error (node_modules/express/lib/router/layer.js:71:5)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at node_modules/express/lib/router/index.js:646:15
      at next (node_modules/express/lib/router/index.js:265:14)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at auth (middleware/auth.js:61:14)
      at Layer.handle [as handle_request] (node_modules/express/lib/router/layer.js:95:5)
      at trim_prefix (node_modules/express/lib/router/index.js:328:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at Function.handle (node_modules/express/lib/router/index.js:175:3)
      at router (node_modules/express/lib/router/index.js:47:12)
      at Layer.handle [as handle_request] (node_modules/express/lib/router/layer.js:95:5)
      at trim_prefix (node_modules/express/lib/router/index.js:328:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at Function.handle (node_modules/express/lib/router/index.js:175:3)
      at router (node_modules/express/lib/router/index.js:47:12)
      at Layer.handle [as handle_request] (node_modules/express/lib/router/layer.js:95:5)
      at trim_prefix (node_modules/express/lib/router/index.js:328:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at Object.<anonymous>.securityHeaders (middleware/security.js:89:5)
      at Layer.handle [as handle_request] (node_modules/express/lib/router/layer.js:95:5)
      at trim_prefix (node_modules/express/lib/router/index.js:328:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at middleware/security.js:78:5
      at Layer.handle [as handle_request] (node_modules/express/lib/router/layer.js:95:5)
      at trim_prefix (node_modules/express/lib/router/index.js:328:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at middleware/security.js:72:5
      at Layer.handle [as handle_request] (node_modules/express/lib/router/layer.js:95:5)
      at trim_prefix (node_modules/express/lib/router/index.js:328:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at middleware/security.js:66:5
      at Layer.handle [as handle_request] (node_modules/express/lib/router/layer.js:95:5)
      at trim_prefix (node_modules/express/lib/router/index.js:328:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at cors (../node_modules/cors/lib/index.js:188:7)
      at ../node_modules/cors/lib/index.js:224:17
      at originCallback (../node_modules/cors/lib/index.js:214:15)
      at ../node_modules/cors/lib/index.js:219:13
      at optionsCallback (../node_modules/cors/lib/index.js:199:9)
      at corsMiddleware (../node_modules/cors/lib/index.js:204:7)
      at Layer.handle [as handle_request] (node_modules/express/lib/router/layer.js:95:5)
      at trim_prefix (node_modules/express/lib/router/index.js:328:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at internalNext (node_modules/helmet/index.cjs:537:6)
      at xXssProtectionMiddleware (node_modules/helmet/index.cjs:315:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at xPoweredByMiddleware (node_modules/helmet/index.cjs:308:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at xPermittedCrossDomainPoliciesMiddleware (node_modules/helmet/index.cjs:301:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at xFrameOptionsMiddleware (node_modules/helmet/index.cjs:285:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at xDownloadOptionsMiddleware (node_modules/helmet/index.cjs:265:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at xDnsPrefetchControlMiddleware (node_modules/helmet/index.cjs:258:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at xContentTypeOptionsMiddleware (node_modules/helmet/index.cjs:250:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at strictTransportSecurityMiddleware (node_modules/helmet/index.cjs:243:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at referrerPolicyMiddleware (node_modules/helmet/index.cjs:211:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at originAgentClusterMiddleware (node_modules/helmet/index.cjs:186:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at crossOriginResourcePolicyMiddleware (node_modules/helmet/index.cjs:179:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at crossOriginOpenerPolicyMiddleware (node_modules/helmet/index.cjs:163:3)

  console.error
    API Error: ApiError: Not authorized to access this route
        at auth (/Users/<USER>/Documents/payment-main/backend/middleware/auth.js:61:19)
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)
        at Function.handle (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:175:3)
        at router (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:47:12)
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)
        at Function.handle (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:175:3)
        at router (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:47:12)
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)
        at Object.<anonymous>.securityHeaders (/Users/<USER>/Documents/payment-main/backend/middleware/security.js:89:5)
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)
        at /Users/<USER>/Documents/payment-main/backend/middleware/security.js:78:5
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)
        at /Users/<USER>/Documents/payment-main/backend/middleware/security.js:72:5
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)
        at /Users/<USER>/Documents/payment-main/backend/middleware/security.js:66:5
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)
        at cors (/Users/<USER>/Documents/payment-main/node_modules/cors/lib/index.js:188:7)
        at /Users/<USER>/Documents/payment-main/node_modules/cors/lib/index.js:224:17
        at originCallback (/Users/<USER>/Documents/payment-main/node_modules/cors/lib/index.js:214:15)
        at /Users/<USER>/Documents/payment-main/node_modules/cors/lib/index.js:219:13
        at optionsCallback (/Users/<USER>/Documents/payment-main/node_modules/cors/lib/index.js:199:9)
        at corsMiddleware (/Users/<USER>/Documents/payment-main/node_modules/cors/lib/index.js:204:7)
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:537:6)
        at xXssProtectionMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:315:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at xPoweredByMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:308:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at xPermittedCrossDomainPoliciesMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:301:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at xFrameOptionsMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:285:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at xDownloadOptionsMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:265:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at xDnsPrefetchControlMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:258:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at xContentTypeOptionsMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:250:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at strictTransportSecurityMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:243:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at referrerPolicyMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:211:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at originAgentClusterMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:186:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at crossOriginResourcePolicyMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:179:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at crossOriginOpenerPolicyMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:163:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at contentSecurityPolicyMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:128:4)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at helmetMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:539:6)
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)
        at /Users/<USER>/Documents/payment-main/node_modules/express-mongo-sanitize/index.js:122:5
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)
        at Object.<anonymous>.mongoSanitize.onSanitize.req.req (/Users/<USER>/Documents/payment-main/backend/middleware/security.js:174:7)
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10) {
      statusCode: 401,
      status: 'fail',
      code: 'ERR_401',
      details: {},
      isOperational: true,
      timestamp: '2025-07-26T05:44:48.916Z'
    }

      42 | // Error handling middleware
      43 | router.use((err, req, res, next) => {
    > 44 |   console.error('API Error:', err);
         |           ^
      45 |   
      46 |   const statusCode = err.statusCode || 500;
      47 |   const errorResponse = {

      at routes/v1/index.js:44:11
      at Layer.handle_error (node_modules/express/lib/router/layer.js:71:5)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at node_modules/express/lib/router/index.js:646:15
      at next (node_modules/express/lib/router/index.js:265:14)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at auth (middleware/auth.js:61:14)
      at Layer.handle [as handle_request] (node_modules/express/lib/router/layer.js:95:5)
      at trim_prefix (node_modules/express/lib/router/index.js:328:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at Function.handle (node_modules/express/lib/router/index.js:175:3)
      at router (node_modules/express/lib/router/index.js:47:12)
      at Layer.handle [as handle_request] (node_modules/express/lib/router/layer.js:95:5)
      at trim_prefix (node_modules/express/lib/router/index.js:328:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at Function.handle (node_modules/express/lib/router/index.js:175:3)
      at router (node_modules/express/lib/router/index.js:47:12)
      at Layer.handle [as handle_request] (node_modules/express/lib/router/layer.js:95:5)
      at trim_prefix (node_modules/express/lib/router/index.js:328:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at Object.<anonymous>.securityHeaders (middleware/security.js:89:5)
      at Layer.handle [as handle_request] (node_modules/express/lib/router/layer.js:95:5)
      at trim_prefix (node_modules/express/lib/router/index.js:328:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at middleware/security.js:78:5
      at Layer.handle [as handle_request] (node_modules/express/lib/router/layer.js:95:5)
      at trim_prefix (node_modules/express/lib/router/index.js:328:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at middleware/security.js:72:5
      at Layer.handle [as handle_request] (node_modules/express/lib/router/layer.js:95:5)
      at trim_prefix (node_modules/express/lib/router/index.js:328:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at middleware/security.js:66:5
      at Layer.handle [as handle_request] (node_modules/express/lib/router/layer.js:95:5)
      at trim_prefix (node_modules/express/lib/router/index.js:328:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at cors (../node_modules/cors/lib/index.js:188:7)
      at ../node_modules/cors/lib/index.js:224:17
      at originCallback (../node_modules/cors/lib/index.js:214:15)
      at ../node_modules/cors/lib/index.js:219:13
      at optionsCallback (../node_modules/cors/lib/index.js:199:9)
      at corsMiddleware (../node_modules/cors/lib/index.js:204:7)
      at Layer.handle [as handle_request] (node_modules/express/lib/router/layer.js:95:5)
      at trim_prefix (node_modules/express/lib/router/index.js:328:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at internalNext (node_modules/helmet/index.cjs:537:6)
      at xXssProtectionMiddleware (node_modules/helmet/index.cjs:315:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at xPoweredByMiddleware (node_modules/helmet/index.cjs:308:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at xPermittedCrossDomainPoliciesMiddleware (node_modules/helmet/index.cjs:301:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at xFrameOptionsMiddleware (node_modules/helmet/index.cjs:285:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at xDownloadOptionsMiddleware (node_modules/helmet/index.cjs:265:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at xDnsPrefetchControlMiddleware (node_modules/helmet/index.cjs:258:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at xContentTypeOptionsMiddleware (node_modules/helmet/index.cjs:250:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at strictTransportSecurityMiddleware (node_modules/helmet/index.cjs:243:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at referrerPolicyMiddleware (node_modules/helmet/index.cjs:211:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at originAgentClusterMiddleware (node_modules/helmet/index.cjs:186:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at crossOriginResourcePolicyMiddleware (node_modules/helmet/index.cjs:179:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at crossOriginOpenerPolicyMiddleware (node_modules/helmet/index.cjs:163:3)

  console.error
    API Error: ApiError: Not authorized to access this route
        at auth (/Users/<USER>/Documents/payment-main/backend/middleware/auth.js:61:19)
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)
        at Function.handle (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:175:3)
        at router (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:47:12)
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)
        at Function.handle (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:175:3)
        at router (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:47:12)
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)
        at Object.<anonymous>.securityHeaders (/Users/<USER>/Documents/payment-main/backend/middleware/security.js:89:5)
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)
        at /Users/<USER>/Documents/payment-main/backend/middleware/security.js:78:5
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)
        at /Users/<USER>/Documents/payment-main/backend/middleware/security.js:72:5
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)
        at /Users/<USER>/Documents/payment-main/backend/middleware/security.js:66:5
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)
        at cors (/Users/<USER>/Documents/payment-main/node_modules/cors/lib/index.js:188:7)
        at /Users/<USER>/Documents/payment-main/node_modules/cors/lib/index.js:224:17
        at originCallback (/Users/<USER>/Documents/payment-main/node_modules/cors/lib/index.js:214:15)
        at /Users/<USER>/Documents/payment-main/node_modules/cors/lib/index.js:219:13
        at optionsCallback (/Users/<USER>/Documents/payment-main/node_modules/cors/lib/index.js:199:9)
        at corsMiddleware (/Users/<USER>/Documents/payment-main/node_modules/cors/lib/index.js:204:7)
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:537:6)
        at xXssProtectionMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:315:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at xPoweredByMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:308:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at xPermittedCrossDomainPoliciesMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:301:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at xFrameOptionsMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:285:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at xDownloadOptionsMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:265:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at xDnsPrefetchControlMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:258:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at xContentTypeOptionsMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:250:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at strictTransportSecurityMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:243:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at referrerPolicyMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:211:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at originAgentClusterMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:186:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at crossOriginResourcePolicyMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:179:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at crossOriginOpenerPolicyMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:163:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at contentSecurityPolicyMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:128:4)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at helmetMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:539:6)
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)
        at /Users/<USER>/Documents/payment-main/node_modules/express-mongo-sanitize/index.js:122:5
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)
        at Object.<anonymous>.mongoSanitize.onSanitize.req.req (/Users/<USER>/Documents/payment-main/backend/middleware/security.js:174:7)
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10) {
      statusCode: 401,
      status: 'fail',
      code: 'ERR_401',
      details: {},
      isOperational: true,
      timestamp: '2025-07-26T05:44:48.930Z'
    }

      42 | // Error handling middleware
      43 | router.use((err, req, res, next) => {
    > 44 |   console.error('API Error:', err);
         |           ^
      45 |   
      46 |   const statusCode = err.statusCode || 500;
      47 |   const errorResponse = {

      at routes/v1/index.js:44:11
      at Layer.handle_error (node_modules/express/lib/router/layer.js:71:5)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at node_modules/express/lib/router/index.js:646:15
      at next (node_modules/express/lib/router/index.js:265:14)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at auth (middleware/auth.js:61:14)
      at Layer.handle [as handle_request] (node_modules/express/lib/router/layer.js:95:5)
      at trim_prefix (node_modules/express/lib/router/index.js:328:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at Function.handle (node_modules/express/lib/router/index.js:175:3)
      at router (node_modules/express/lib/router/index.js:47:12)
      at Layer.handle [as handle_request] (node_modules/express/lib/router/layer.js:95:5)
      at trim_prefix (node_modules/express/lib/router/index.js:328:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at Function.handle (node_modules/express/lib/router/index.js:175:3)
      at router (node_modules/express/lib/router/index.js:47:12)
      at Layer.handle [as handle_request] (node_modules/express/lib/router/layer.js:95:5)
      at trim_prefix (node_modules/express/lib/router/index.js:328:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at Object.<anonymous>.securityHeaders (middleware/security.js:89:5)
      at Layer.handle [as handle_request] (node_modules/express/lib/router/layer.js:95:5)
      at trim_prefix (node_modules/express/lib/router/index.js:328:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at middleware/security.js:78:5
      at Layer.handle [as handle_request] (node_modules/express/lib/router/layer.js:95:5)
      at trim_prefix (node_modules/express/lib/router/index.js:328:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at middleware/security.js:72:5
      at Layer.handle [as handle_request] (node_modules/express/lib/router/layer.js:95:5)
      at trim_prefix (node_modules/express/lib/router/index.js:328:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at middleware/security.js:66:5
      at Layer.handle [as handle_request] (node_modules/express/lib/router/layer.js:95:5)
      at trim_prefix (node_modules/express/lib/router/index.js:328:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at cors (../node_modules/cors/lib/index.js:188:7)
      at ../node_modules/cors/lib/index.js:224:17
      at originCallback (../node_modules/cors/lib/index.js:214:15)
      at ../node_modules/cors/lib/index.js:219:13
      at optionsCallback (../node_modules/cors/lib/index.js:199:9)
      at corsMiddleware (../node_modules/cors/lib/index.js:204:7)
      at Layer.handle [as handle_request] (node_modules/express/lib/router/layer.js:95:5)
      at trim_prefix (node_modules/express/lib/router/index.js:328:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at internalNext (node_modules/helmet/index.cjs:537:6)
      at xXssProtectionMiddleware (node_modules/helmet/index.cjs:315:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at xPoweredByMiddleware (node_modules/helmet/index.cjs:308:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at xPermittedCrossDomainPoliciesMiddleware (node_modules/helmet/index.cjs:301:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at xFrameOptionsMiddleware (node_modules/helmet/index.cjs:285:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at xDownloadOptionsMiddleware (node_modules/helmet/index.cjs:265:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at xDnsPrefetchControlMiddleware (node_modules/helmet/index.cjs:258:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at xContentTypeOptionsMiddleware (node_modules/helmet/index.cjs:250:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at strictTransportSecurityMiddleware (node_modules/helmet/index.cjs:243:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at referrerPolicyMiddleware (node_modules/helmet/index.cjs:211:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at originAgentClusterMiddleware (node_modules/helmet/index.cjs:186:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at crossOriginResourcePolicyMiddleware (node_modules/helmet/index.cjs:179:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at crossOriginOpenerPolicyMiddleware (node_modules/helmet/index.cjs:163:3)

  console.error
    API Error: ApiError: Not authorized to access this route
        at auth (/Users/<USER>/Documents/payment-main/backend/middleware/auth.js:61:19)
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)
        at Function.handle (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:175:3)
        at router (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:47:12)
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)
        at Function.handle (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:175:3)
        at router (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:47:12)
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)
        at Object.<anonymous>.securityHeaders (/Users/<USER>/Documents/payment-main/backend/middleware/security.js:89:5)
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)
        at /Users/<USER>/Documents/payment-main/backend/middleware/security.js:78:5
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)
        at /Users/<USER>/Documents/payment-main/backend/middleware/security.js:72:5
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)
        at /Users/<USER>/Documents/payment-main/backend/middleware/security.js:66:5
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)
        at cors (/Users/<USER>/Documents/payment-main/node_modules/cors/lib/index.js:188:7)
        at /Users/<USER>/Documents/payment-main/node_modules/cors/lib/index.js:224:17
        at originCallback (/Users/<USER>/Documents/payment-main/node_modules/cors/lib/index.js:214:15)
        at /Users/<USER>/Documents/payment-main/node_modules/cors/lib/index.js:219:13
        at optionsCallback (/Users/<USER>/Documents/payment-main/node_modules/cors/lib/index.js:199:9)
        at corsMiddleware (/Users/<USER>/Documents/payment-main/node_modules/cors/lib/index.js:204:7)
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:537:6)
        at xXssProtectionMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:315:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at xPoweredByMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:308:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at xPermittedCrossDomainPoliciesMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:301:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at xFrameOptionsMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:285:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at xDownloadOptionsMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:265:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at xDnsPrefetchControlMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:258:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at xContentTypeOptionsMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:250:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at strictTransportSecurityMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:243:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at referrerPolicyMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:211:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at originAgentClusterMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:186:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at crossOriginResourcePolicyMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:179:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at crossOriginOpenerPolicyMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:163:3)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at contentSecurityPolicyMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:128:4)
        at internalNext (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:535:6)
        at helmetMiddleware (/Users/<USER>/Documents/payment-main/backend/node_modules/helmet/index.cjs:539:6)
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)
        at /Users/<USER>/Documents/payment-main/node_modules/express-mongo-sanitize/index.js:122:5
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)
        at Object.<anonymous>.mongoSanitize.onSanitize.req.req (/Users/<USER>/Documents/payment-main/backend/middleware/security.js:174:7)
        at Layer.handle [as handle_request] (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:95:5)
        at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:328:13)
        at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9
        at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)
        at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10) {
      statusCode: 401,
      status: 'fail',
      code: 'ERR_401',
      details: {},
      isOperational: true,
      timestamp: '2025-07-26T05:44:48.945Z'
    }

      42 | // Error handling middleware
      43 | router.use((err, req, res, next) => {
    > 44 |   console.error('API Error:', err);
         |           ^
      45 |   
      46 |   const statusCode = err.statusCode || 500;
      47 |   const errorResponse = {

      at routes/v1/index.js:44:11
      at Layer.handle_error (node_modules/express/lib/router/layer.js:71:5)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at node_modules/express/lib/router/index.js:646:15
      at next (node_modules/express/lib/router/index.js:265:14)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at auth (middleware/auth.js:61:14)
      at Layer.handle [as handle_request] (node_modules/express/lib/router/layer.js:95:5)
      at trim_prefix (node_modules/express/lib/router/index.js:328:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at Function.handle (node_modules/express/lib/router/index.js:175:3)
      at router (node_modules/express/lib/router/index.js:47:12)
      at Layer.handle [as handle_request] (node_modules/express/lib/router/layer.js:95:5)
      at trim_prefix (node_modules/express/lib/router/index.js:328:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at Function.handle (node_modules/express/lib/router/index.js:175:3)
      at router (node_modules/express/lib/router/index.js:47:12)
      at Layer.handle [as handle_request] (node_modules/express/lib/router/layer.js:95:5)
      at trim_prefix (node_modules/express/lib/router/index.js:328:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at Object.<anonymous>.securityHeaders (middleware/security.js:89:5)
      at Layer.handle [as handle_request] (node_modules/express/lib/router/layer.js:95:5)
      at trim_prefix (node_modules/express/lib/router/index.js:328:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at middleware/security.js:78:5
      at Layer.handle [as handle_request] (node_modules/express/lib/router/layer.js:95:5)
      at trim_prefix (node_modules/express/lib/router/index.js:328:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at middleware/security.js:72:5
      at Layer.handle [as handle_request] (node_modules/express/lib/router/layer.js:95:5)
      at trim_prefix (node_modules/express/lib/router/index.js:328:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at middleware/security.js:66:5
      at Layer.handle [as handle_request] (node_modules/express/lib/router/layer.js:95:5)
      at trim_prefix (node_modules/express/lib/router/index.js:328:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at cors (../node_modules/cors/lib/index.js:188:7)
      at ../node_modules/cors/lib/index.js:224:17
      at originCallback (../node_modules/cors/lib/index.js:214:15)
      at ../node_modules/cors/lib/index.js:219:13
      at optionsCallback (../node_modules/cors/lib/index.js:199:9)
      at corsMiddleware (../node_modules/cors/lib/index.js:204:7)
      at Layer.handle [as handle_request] (node_modules/express/lib/router/layer.js:95:5)
      at trim_prefix (node_modules/express/lib/router/index.js:328:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at internalNext (node_modules/helmet/index.cjs:537:6)
      at xXssProtectionMiddleware (node_modules/helmet/index.cjs:315:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at xPoweredByMiddleware (node_modules/helmet/index.cjs:308:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at xPermittedCrossDomainPoliciesMiddleware (node_modules/helmet/index.cjs:301:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at xFrameOptionsMiddleware (node_modules/helmet/index.cjs:285:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at xDownloadOptionsMiddleware (node_modules/helmet/index.cjs:265:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at xDnsPrefetchControlMiddleware (node_modules/helmet/index.cjs:258:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at xContentTypeOptionsMiddleware (node_modules/helmet/index.cjs:250:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at strictTransportSecurityMiddleware (node_modules/helmet/index.cjs:243:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at referrerPolicyMiddleware (node_modules/helmet/index.cjs:211:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at originAgentClusterMiddleware (node_modules/helmet/index.cjs:186:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at crossOriginResourcePolicyMiddleware (node_modules/helmet/index.cjs:179:3)
      at internalNext (node_modules/helmet/index.cjs:535:6)
      at crossOriginOpenerPolicyMiddleware (node_modules/helmet/index.cjs:163:3)

[2025-07-26 11:14:48.956] [32minfo[39m: 	[32mClearing test database collections...[39m
{
  "service": "payment-gateway"
}
[2025-07-26 11:14:48.969] [32minfo[39m: 	[32mTest database cleared[39m
{
  "service": "payment-gateway"
}
  console.error
    API Error: ApiError: User not found
        at auth (/Users/<USER>/Documents/payment-main/backend/middleware/auth.js:121:21)
        at processTicksAndRejections (node:internal/process/task_queues:95:5) {
      statusCode: 404,
      status: 'fail',
      code: 'ERR_404',
      details: {},
      isOperational: true,
      timestamp: '2025-07-26T05:44:48.983Z'
    }

      42 | // Error handling middleware
      43 | router.use((err, req, res, next) => {
    > 44 |   console.error('API Error:', err);
         |           ^
      45 |   
      46 |   const statusCode = err.statusCode || 500;
      47 |   const errorResponse = {

      at routes/v1/index.js:44:11
      at Layer.handle_error (node_modules/express/lib/router/layer.js:71:5)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at node_modules/express/lib/router/index.js:646:15
      at next (node_modules/express/lib/router/index.js:265:14)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at auth (middleware/auth.js:121:16)

[2025-07-26 11:14:48.990] [32minfo[39m: 	[32mClearing test database collections...[39m
{
  "service": "payment-gateway"
}
[2025-07-26 11:14:49.007] [32minfo[39m: 	[32mTest database cleared[39m
{
  "service": "payment-gateway"
}
  console.error
    API Error: ApiError: User not found
        at auth (/Users/<USER>/Documents/payment-main/backend/middleware/auth.js:121:21)
        at processTicksAndRejections (node:internal/process/task_queues:95:5) {
      statusCode: 404,
      status: 'fail',
      code: 'ERR_404',
      details: {},
      isOperational: true,
      timestamp: '2025-07-26T05:44:49.025Z'
    }

      42 | // Error handling middleware
      43 | router.use((err, req, res, next) => {
    > 44 |   console.error('API Error:', err);
         |           ^
      45 |   
      46 |   const statusCode = err.statusCode || 500;
      47 |   const errorResponse = {

      at routes/v1/index.js:44:11
      at Layer.handle_error (node_modules/express/lib/router/layer.js:71:5)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at node_modules/express/lib/router/index.js:646:15
      at next (node_modules/express/lib/router/index.js:265:14)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at auth (middleware/auth.js:121:16)

[2025-07-26 11:14:49.042] [32minfo[39m: 	[32mClearing test database collections...[39m
{
  "service": "payment-gateway"
}
[2025-07-26 11:14:49.066] [32minfo[39m: 	[32mTest database cleared[39m
{
  "service": "payment-gateway"
}
  console.error
    API Error: ApiError: User not found
        at auth (/Users/<USER>/Documents/payment-main/backend/middleware/auth.js:121:21)
        at processTicksAndRejections (node:internal/process/task_queues:95:5) {
      statusCode: 404,
      status: 'fail',
      code: 'ERR_404',
      details: {},
      isOperational: true,
      timestamp: '2025-07-26T05:44:49.077Z'
    }

      42 | // Error handling middleware
      43 | router.use((err, req, res, next) => {
    > 44 |   console.error('API Error:', err);
         |           ^
      45 |   
      46 |   const statusCode = err.statusCode || 500;
      47 |   const errorResponse = {

      at routes/v1/index.js:44:11
      at Layer.handle_error (node_modules/express/lib/router/layer.js:71:5)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at node_modules/express/lib/router/index.js:646:15
      at next (node_modules/express/lib/router/index.js:265:14)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at auth (middleware/auth.js:121:16)

[2025-07-26 11:14:49.082] [32minfo[39m: 	[32mClearing test database collections...[39m
{
  "service": "payment-gateway"
}
[2025-07-26 11:14:49.094] [32minfo[39m: 	[32mTest database cleared[39m
{
  "service": "payment-gateway"
}
  console.error
    API Error: ApiError: User not found
        at auth (/Users/<USER>/Documents/payment-main/backend/middleware/auth.js:121:21)
        at processTicksAndRejections (node:internal/process/task_queues:95:5) {
      statusCode: 404,
      status: 'fail',
      code: 'ERR_404',
      details: {},
      isOperational: true,
      timestamp: '2025-07-26T05:44:49.105Z'
    }

      42 | // Error handling middleware
      43 | router.use((err, req, res, next) => {
    > 44 |   console.error('API Error:', err);
         |           ^
      45 |   
      46 |   const statusCode = err.statusCode || 500;
      47 |   const errorResponse = {

      at routes/v1/index.js:44:11
      at Layer.handle_error (node_modules/express/lib/router/layer.js:71:5)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at node_modules/express/lib/router/index.js:646:15
      at next (node_modules/express/lib/router/index.js:265:14)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at auth (middleware/auth.js:121:16)

[2025-07-26 11:14:49.110] [32minfo[39m: 	[32mClearing test database collections...[39m
{
  "service": "payment-gateway"
}
[2025-07-26 11:14:49.122] [32minfo[39m: 	[32mTest database cleared[39m
{
  "service": "payment-gateway"
}
  console.error
    API Error: ApiError: User not found
        at auth (/Users/<USER>/Documents/payment-main/backend/middleware/auth.js:121:21)
        at processTicksAndRejections (node:internal/process/task_queues:95:5) {
      statusCode: 404,
      status: 'fail',
      code: 'ERR_404',
      details: {},
      isOperational: true,
      timestamp: '2025-07-26T05:44:49.132Z'
    }

      42 | // Error handling middleware
      43 | router.use((err, req, res, next) => {
    > 44 |   console.error('API Error:', err);
         |           ^
      45 |   
      46 |   const statusCode = err.statusCode || 500;
      47 |   const errorResponse = {

      at routes/v1/index.js:44:11
      at Layer.handle_error (node_modules/express/lib/router/layer.js:71:5)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at node_modules/express/lib/router/index.js:646:15
      at next (node_modules/express/lib/router/index.js:265:14)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at auth (middleware/auth.js:121:16)

[2025-07-26 11:14:49.139] [32minfo[39m: 	[32mClearing test database collections...[39m
{
  "service": "payment-gateway"
}
[2025-07-26 11:14:49.150] [32minfo[39m: 	[32mTest database cleared[39m
{
  "service": "payment-gateway"
}
  console.error
    API Error: ApiError: User not found
        at auth (/Users/<USER>/Documents/payment-main/backend/middleware/auth.js:121:21)
        at processTicksAndRejections (node:internal/process/task_queues:95:5) {
      statusCode: 404,
      status: 'fail',
      code: 'ERR_404',
      details: {},
      isOperational: true,
      timestamp: '2025-07-26T05:44:49.166Z'
    }

      42 | // Error handling middleware
      43 | router.use((err, req, res, next) => {
    > 44 |   console.error('API Error:', err);
         |           ^
      45 |   
      46 |   const statusCode = err.statusCode || 500;
      47 |   const errorResponse = {

      at routes/v1/index.js:44:11
      at Layer.handle_error (node_modules/express/lib/router/layer.js:71:5)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at node_modules/express/lib/router/index.js:646:15
      at next (node_modules/express/lib/router/index.js:265:14)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at auth (middleware/auth.js:121:16)

  console.error
    API Error: ApiError: User not found
        at auth (/Users/<USER>/Documents/payment-main/backend/middleware/auth.js:121:21)
        at processTicksAndRejections (node:internal/process/task_queues:95:5) {
      statusCode: 404,
      status: 'fail',
      code: 'ERR_404',
      details: {},
      isOperational: true,
      timestamp: '2025-07-26T05:44:49.178Z'
    }

      42 | // Error handling middleware
      43 | router.use((err, req, res, next) => {
    > 44 |   console.error('API Error:', err);
         |           ^
      45 |   
      46 |   const statusCode = err.statusCode || 500;
      47 |   const errorResponse = {

      at routes/v1/index.js:44:11
      at Layer.handle_error (node_modules/express/lib/router/layer.js:71:5)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at node_modules/express/lib/router/index.js:646:15
      at next (node_modules/express/lib/router/index.js:265:14)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at auth (middleware/auth.js:121:16)

[2025-07-26 11:14:49.183] [32minfo[39m: 	[32mClearing test database collections...[39m
{
  "service": "payment-gateway"
}
[2025-07-26 11:14:49.195] [32minfo[39m: 	[32mTest database cleared[39m
{
  "service": "payment-gateway"
}
[2025-07-26 11:14:49.207] [33mwarn[39m: 	[33m401 - Invalid credentials[39m
{
  "service": "payment-gateway",
  "error": "ApiError: Invalid credentials\n    at /Users/<USER>/Documents/payment-main/backend/controllers/v1/authController.js:124:13\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at /Users/<USER>/Documents/payment-main/backend/controllers/v1/BaseController.js:118:9",
  "details": {}
}
[2025-07-26 11:14:49.220] [33mwarn[39m: 	[33m401 - Invalid credentials[39m
{
  "service": "payment-gateway",
  "error": "ApiError: Invalid credentials\n    at /Users/<USER>/Documents/payment-main/backend/controllers/v1/authController.js:124:13\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at /Users/<USER>/Documents/payment-main/backend/controllers/v1/BaseController.js:118:9",
  "details": {}
}
[2025-07-26 11:14:49.230] [33mwarn[39m: 	[33mRate limit exceeded[39m
{
  "service": "payment-gateway",
  "ip": "::ffff:127.0.0.1",
  "path": "/login",
  "method": "POST",
  "retryAfter": 3600
}
  console.error
    API Error: ApiError: 429
        at Function.tooManyRequests (/Users/<USER>/Documents/payment-main/backend/utils/ApiError.js:59:12)
        at /Users/<USER>/Documents/payment-main/backend/middleware/rateLimit.js:69:21
        at processTicksAndRejections (node:internal/process/task_queues:95:5) {
      statusCode: 'Too many login attempts, please try again later',
      status: 'error',
      code: 'TOO_MANY_REQUESTS',
      details: { retryAfter: 3600 },
      isOperational: true,
      timestamp: '2025-07-26T05:44:49.232Z'
    }

      42 | // Error handling middleware
      43 | router.use((err, req, res, next) => {
    > 44 |   console.error('API Error:', err);
         |           ^
      45 |   
      46 |   const statusCode = err.statusCode || 500;
      47 |   const errorResponse = {

      at routes/v1/index.js:44:11
      at Layer.handle_error (node_modules/express/lib/router/layer.js:71:5)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at node_modules/express/lib/router/index.js:646:15
      at next (node_modules/express/lib/router/index.js:265:14)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at next (node_modules/express/lib/router/route.js:141:14)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at next (node_modules/express/lib/router/route.js:147:13)
      at middleware/rateLimit.js:69:7

[2025-07-26 11:14:49.235] [31merror[39m: 	[31mInvalid status code: Too many login attempts, please try again later[39m
{
  "statusCode": 500,
  "stack": "RangeError: Invalid status code: Too many login attempts, please try again later\n    at ServerResponse.writeHead (node:_http_server:351:11)\n    at ServerResponse._implicitHeader (node:_http_server:337:8)\n    at write_ (node:_http_outgoing:945:9)\n    at ServerResponse.end (node:_http_outgoing:1056:5)\n    at ServerResponse.send (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:232:10)\n    at ServerResponse.json (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:278:15)\n    at /Users/<USER>/Documents/payment-main/backend/routes/v1/index.js:56:26\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:71:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:265:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:141:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:147:13)\n    at /Users/<USER>/Documents/payment-main/backend/middleware/rateLimit.js:69:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)",
  "path": "/api/v1/auth/login",
  "method": "POST",
  "ip": "::ffff:127.0.0.1",
  "user": "anonymous",
  "service": "payment-gateway"
}
[2025-07-26 11:14:49.249] [33mwarn[39m: 	[33mRate limit exceeded[39m
{
  "service": "payment-gateway",
  "ip": "::ffff:127.0.0.1",
  "path": "/login",
  "method": "POST",
  "retryAfter": 3600
}
  console.error
    API Error: ApiError: 429
        at Function.tooManyRequests (/Users/<USER>/Documents/payment-main/backend/utils/ApiError.js:59:12)
        at /Users/<USER>/Documents/payment-main/backend/middleware/rateLimit.js:69:21
        at processTicksAndRejections (node:internal/process/task_queues:95:5) {
      statusCode: 'Too many login attempts, please try again later',
      status: 'error',
      code: 'TOO_MANY_REQUESTS',
      details: { retryAfter: 3600 },
      isOperational: true,
      timestamp: '2025-07-26T05:44:49.252Z'
    }

      42 | // Error handling middleware
      43 | router.use((err, req, res, next) => {
    > 44 |   console.error('API Error:', err);
         |           ^
      45 |   
      46 |   const statusCode = err.statusCode || 500;
      47 |   const errorResponse = {

      at routes/v1/index.js:44:11
      at Layer.handle_error (node_modules/express/lib/router/layer.js:71:5)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at node_modules/express/lib/router/index.js:646:15
      at next (node_modules/express/lib/router/index.js:265:14)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at next (node_modules/express/lib/router/route.js:141:14)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at next (node_modules/express/lib/router/route.js:147:13)
      at middleware/rateLimit.js:69:7

[2025-07-26 11:14:49.255] [31merror[39m: 	[31mInvalid status code: Too many login attempts, please try again later[39m
{
  "statusCode": 500,
  "stack": "RangeError: Invalid status code: Too many login attempts, please try again later\n    at ServerResponse.writeHead (node:_http_server:351:11)\n    at ServerResponse._implicitHeader (node:_http_server:337:8)\n    at write_ (node:_http_outgoing:945:9)\n    at ServerResponse.end (node:_http_outgoing:1056:5)\n    at ServerResponse.send (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:232:10)\n    at ServerResponse.json (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:278:15)\n    at /Users/<USER>/Documents/payment-main/backend/routes/v1/index.js:56:26\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:71:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:265:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:141:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:147:13)\n    at /Users/<USER>/Documents/payment-main/backend/middleware/rateLimit.js:69:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)",
  "path": "/api/v1/auth/login",
  "method": "POST",
  "ip": "::ffff:127.0.0.1",
  "user": "anonymous",
  "service": "payment-gateway"
}
[2025-07-26 11:14:49.267] [33mwarn[39m: 	[33mRate limit exceeded[39m
{
  "service": "payment-gateway",
  "ip": "::ffff:127.0.0.1",
  "path": "/login",
  "method": "POST",
  "retryAfter": 3600
}
  console.error
    API Error: ApiError: 429
        at Function.tooManyRequests (/Users/<USER>/Documents/payment-main/backend/utils/ApiError.js:59:12)
        at /Users/<USER>/Documents/payment-main/backend/middleware/rateLimit.js:69:21
        at processTicksAndRejections (node:internal/process/task_queues:95:5) {
      statusCode: 'Too many login attempts, please try again later',
      status: 'error',
      code: 'TOO_MANY_REQUESTS',
      details: { retryAfter: 3600 },
      isOperational: true,
      timestamp: '2025-07-26T05:44:49.269Z'
    }

      42 | // Error handling middleware
      43 | router.use((err, req, res, next) => {
    > 44 |   console.error('API Error:', err);
         |           ^
      45 |   
      46 |   const statusCode = err.statusCode || 500;
      47 |   const errorResponse = {

      at routes/v1/index.js:44:11
      at Layer.handle_error (node_modules/express/lib/router/layer.js:71:5)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at node_modules/express/lib/router/index.js:646:15
      at next (node_modules/express/lib/router/index.js:265:14)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at next (node_modules/express/lib/router/route.js:141:14)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at next (node_modules/express/lib/router/route.js:147:13)
      at middleware/rateLimit.js:69:7

[2025-07-26 11:14:49.275] [31merror[39m: 	[31mInvalid status code: Too many login attempts, please try again later[39m
{
  "statusCode": 500,
  "stack": "RangeError: Invalid status code: Too many login attempts, please try again later\n    at ServerResponse.writeHead (node:_http_server:351:11)\n    at ServerResponse._implicitHeader (node:_http_server:337:8)\n    at write_ (node:_http_outgoing:945:9)\n    at ServerResponse.end (node:_http_outgoing:1056:5)\n    at ServerResponse.send (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:232:10)\n    at ServerResponse.json (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:278:15)\n    at /Users/<USER>/Documents/payment-main/backend/routes/v1/index.js:56:26\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:71:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:265:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:141:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:147:13)\n    at /Users/<USER>/Documents/payment-main/backend/middleware/rateLimit.js:69:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)",
  "path": "/api/v1/auth/login",
  "method": "POST",
  "ip": "::ffff:127.0.0.1",
  "user": "anonymous",
  "service": "payment-gateway"
}
[2025-07-26 11:14:49.289] [33mwarn[39m: 	[33mRate limit exceeded[39m
{
  "service": "payment-gateway",
  "ip": "::ffff:127.0.0.1",
  "path": "/login",
  "method": "POST",
  "retryAfter": 3600
}
  console.error
    API Error: ApiError: 429
        at Function.tooManyRequests (/Users/<USER>/Documents/payment-main/backend/utils/ApiError.js:59:12)
        at /Users/<USER>/Documents/payment-main/backend/middleware/rateLimit.js:69:21
        at processTicksAndRejections (node:internal/process/task_queues:95:5) {
      statusCode: 'Too many login attempts, please try again later',
      status: 'error',
      code: 'TOO_MANY_REQUESTS',
      details: { retryAfter: 3600 },
      isOperational: true,
      timestamp: '2025-07-26T05:44:49.291Z'
    }

      42 | // Error handling middleware
      43 | router.use((err, req, res, next) => {
    > 44 |   console.error('API Error:', err);
         |           ^
      45 |   
      46 |   const statusCode = err.statusCode || 500;
      47 |   const errorResponse = {

      at routes/v1/index.js:44:11
      at Layer.handle_error (node_modules/express/lib/router/layer.js:71:5)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at node_modules/express/lib/router/index.js:646:15
      at next (node_modules/express/lib/router/index.js:265:14)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at next (node_modules/express/lib/router/route.js:141:14)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at next (node_modules/express/lib/router/route.js:147:13)
      at middleware/rateLimit.js:69:7

[2025-07-26 11:14:49.294] [31merror[39m: 	[31mInvalid status code: Too many login attempts, please try again later[39m
{
  "statusCode": 500,
  "stack": "RangeError: Invalid status code: Too many login attempts, please try again later\n    at ServerResponse.writeHead (node:_http_server:351:11)\n    at ServerResponse._implicitHeader (node:_http_server:337:8)\n    at write_ (node:_http_outgoing:945:9)\n    at ServerResponse.end (node:_http_outgoing:1056:5)\n    at ServerResponse.send (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:232:10)\n    at ServerResponse.json (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:278:15)\n    at /Users/<USER>/Documents/payment-main/backend/routes/v1/index.js:56:26\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:71:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:265:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:141:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:147:13)\n    at /Users/<USER>/Documents/payment-main/backend/middleware/rateLimit.js:69:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)",
  "path": "/api/v1/auth/login",
  "method": "POST",
  "ip": "::ffff:127.0.0.1",
  "user": "anonymous",
  "service": "payment-gateway"
}
[2025-07-26 11:14:49.306] [33mwarn[39m: 	[33mRate limit exceeded[39m
{
  "service": "payment-gateway",
  "ip": "::ffff:127.0.0.1",
  "path": "/login",
  "method": "POST",
  "retryAfter": 3600
}
  console.error
    API Error: ApiError: 429
        at Function.tooManyRequests (/Users/<USER>/Documents/payment-main/backend/utils/ApiError.js:59:12)
        at /Users/<USER>/Documents/payment-main/backend/middleware/rateLimit.js:69:21
        at processTicksAndRejections (node:internal/process/task_queues:95:5) {
      statusCode: 'Too many login attempts, please try again later',
      status: 'error',
      code: 'TOO_MANY_REQUESTS',
      details: { retryAfter: 3600 },
      isOperational: true,
      timestamp: '2025-07-26T05:44:49.308Z'
    }

      42 | // Error handling middleware
      43 | router.use((err, req, res, next) => {
    > 44 |   console.error('API Error:', err);
         |           ^
      45 |   
      46 |   const statusCode = err.statusCode || 500;
      47 |   const errorResponse = {

      at routes/v1/index.js:44:11
      at Layer.handle_error (node_modules/express/lib/router/layer.js:71:5)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at node_modules/express/lib/router/index.js:646:15
      at next (node_modules/express/lib/router/index.js:265:14)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at next (node_modules/express/lib/router/route.js:141:14)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at next (node_modules/express/lib/router/route.js:147:13)
      at middleware/rateLimit.js:69:7

[2025-07-26 11:14:49.310] [31merror[39m: 	[31mInvalid status code: Too many login attempts, please try again later[39m
{
  "statusCode": 500,
  "stack": "RangeError: Invalid status code: Too many login attempts, please try again later\n    at ServerResponse.writeHead (node:_http_server:351:11)\n    at ServerResponse._implicitHeader (node:_http_server:337:8)\n    at write_ (node:_http_outgoing:945:9)\n    at ServerResponse.end (node:_http_outgoing:1056:5)\n    at ServerResponse.send (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:232:10)\n    at ServerResponse.json (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:278:15)\n    at /Users/<USER>/Documents/payment-main/backend/routes/v1/index.js:56:26\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:71:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:265:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:141:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:147:13)\n    at /Users/<USER>/Documents/payment-main/backend/middleware/rateLimit.js:69:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)",
  "path": "/api/v1/auth/login",
  "method": "POST",
  "ip": "::ffff:127.0.0.1",
  "user": "anonymous",
  "service": "payment-gateway"
}
[2025-07-26 11:14:49.322] [33mwarn[39m: 	[33mRate limit exceeded[39m
{
  "service": "payment-gateway",
  "ip": "::ffff:127.0.0.1",
  "path": "/login",
  "method": "POST",
  "retryAfter": 3600
}
  console.error
    API Error: ApiError: 429
        at Function.tooManyRequests (/Users/<USER>/Documents/payment-main/backend/utils/ApiError.js:59:12)
        at /Users/<USER>/Documents/payment-main/backend/middleware/rateLimit.js:69:21
        at processTicksAndRejections (node:internal/process/task_queues:95:5) {
      statusCode: 'Too many login attempts, please try again later',
      status: 'error',
      code: 'TOO_MANY_REQUESTS',
      details: { retryAfter: 3600 },
      isOperational: true,
      timestamp: '2025-07-26T05:44:49.324Z'
    }

      42 | // Error handling middleware
      43 | router.use((err, req, res, next) => {
    > 44 |   console.error('API Error:', err);
         |           ^
      45 |   
      46 |   const statusCode = err.statusCode || 500;
      47 |   const errorResponse = {

      at routes/v1/index.js:44:11
      at Layer.handle_error (node_modules/express/lib/router/layer.js:71:5)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at node_modules/express/lib/router/index.js:646:15
      at next (node_modules/express/lib/router/index.js:265:14)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at next (node_modules/express/lib/router/route.js:141:14)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at next (node_modules/express/lib/router/route.js:147:13)
      at middleware/rateLimit.js:69:7

[2025-07-26 11:14:49.326] [31merror[39m: 	[31mInvalid status code: Too many login attempts, please try again later[39m
{
  "statusCode": 500,
  "stack": "RangeError: Invalid status code: Too many login attempts, please try again later\n    at ServerResponse.writeHead (node:_http_server:351:11)\n    at ServerResponse._implicitHeader (node:_http_server:337:8)\n    at write_ (node:_http_outgoing:945:9)\n    at ServerResponse.end (node:_http_outgoing:1056:5)\n    at ServerResponse.send (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:232:10)\n    at ServerResponse.json (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:278:15)\n    at /Users/<USER>/Documents/payment-main/backend/routes/v1/index.js:56:26\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:71:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:265:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:141:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:147:13)\n    at /Users/<USER>/Documents/payment-main/backend/middleware/rateLimit.js:69:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)",
  "path": "/api/v1/auth/login",
  "method": "POST",
  "ip": "::ffff:127.0.0.1",
  "user": "anonymous",
  "service": "payment-gateway"
}
[2025-07-26 11:14:49.337] [33mwarn[39m: 	[33mRate limit exceeded[39m
{
  "service": "payment-gateway",
  "ip": "::ffff:127.0.0.1",
  "path": "/login",
  "method": "POST",
  "retryAfter": 3600
}
  console.error
    API Error: ApiError: 429
        at Function.tooManyRequests (/Users/<USER>/Documents/payment-main/backend/utils/ApiError.js:59:12)
        at /Users/<USER>/Documents/payment-main/backend/middleware/rateLimit.js:69:21
        at processTicksAndRejections (node:internal/process/task_queues:95:5) {
      statusCode: 'Too many login attempts, please try again later',
      status: 'error',
      code: 'TOO_MANY_REQUESTS',
      details: { retryAfter: 3600 },
      isOperational: true,
      timestamp: '2025-07-26T05:44:49.340Z'
    }

      42 | // Error handling middleware
      43 | router.use((err, req, res, next) => {
    > 44 |   console.error('API Error:', err);
         |           ^
      45 |   
      46 |   const statusCode = err.statusCode || 500;
      47 |   const errorResponse = {

      at routes/v1/index.js:44:11
      at Layer.handle_error (node_modules/express/lib/router/layer.js:71:5)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at node_modules/express/lib/router/index.js:646:15
      at next (node_modules/express/lib/router/index.js:265:14)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at next (node_modules/express/lib/router/route.js:141:14)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at next (node_modules/express/lib/router/route.js:147:13)
      at middleware/rateLimit.js:69:7

[2025-07-26 11:14:49.342] [31merror[39m: 	[31mInvalid status code: Too many login attempts, please try again later[39m
{
  "statusCode": 500,
  "stack": "RangeError: Invalid status code: Too many login attempts, please try again later\n    at ServerResponse.writeHead (node:_http_server:351:11)\n    at ServerResponse._implicitHeader (node:_http_server:337:8)\n    at write_ (node:_http_outgoing:945:9)\n    at ServerResponse.end (node:_http_outgoing:1056:5)\n    at ServerResponse.send (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:232:10)\n    at ServerResponse.json (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:278:15)\n    at /Users/<USER>/Documents/payment-main/backend/routes/v1/index.js:56:26\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:71:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:265:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:141:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:147:13)\n    at /Users/<USER>/Documents/payment-main/backend/middleware/rateLimit.js:69:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)",
  "path": "/api/v1/auth/login",
  "method": "POST",
  "ip": "::ffff:127.0.0.1",
  "user": "anonymous",
  "service": "payment-gateway"
}
[2025-07-26 11:14:49.353] [33mwarn[39m: 	[33mRate limit exceeded[39m
{
  "service": "payment-gateway",
  "ip": "::ffff:127.0.0.1",
  "path": "/login",
  "method": "POST",
  "retryAfter": 3600
}
  console.error
    API Error: ApiError: 429
        at Function.tooManyRequests (/Users/<USER>/Documents/payment-main/backend/utils/ApiError.js:59:12)
        at /Users/<USER>/Documents/payment-main/backend/middleware/rateLimit.js:69:21
        at processTicksAndRejections (node:internal/process/task_queues:95:5) {
      statusCode: 'Too many login attempts, please try again later',
      status: 'error',
      code: 'TOO_MANY_REQUESTS',
      details: { retryAfter: 3600 },
      isOperational: true,
      timestamp: '2025-07-26T05:44:49.356Z'
    }

      42 | // Error handling middleware
      43 | router.use((err, req, res, next) => {
    > 44 |   console.error('API Error:', err);
         |           ^
      45 |   
      46 |   const statusCode = err.statusCode || 500;
      47 |   const errorResponse = {

      at routes/v1/index.js:44:11
      at Layer.handle_error (node_modules/express/lib/router/layer.js:71:5)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at node_modules/express/lib/router/index.js:646:15
      at next (node_modules/express/lib/router/index.js:265:14)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at trim_prefix (node_modules/express/lib/router/index.js:326:13)
      at node_modules/express/lib/router/index.js:286:9
      at Function.process_params (node_modules/express/lib/router/index.js:346:12)
      at next (node_modules/express/lib/router/index.js:280:10)
      at next (node_modules/express/lib/router/route.js:141:14)
      at Layer.handle_error (node_modules/express/lib/router/layer.js:67:12)
      at next (node_modules/express/lib/router/route.js:147:13)
      at middleware/rateLimit.js:69:7

[2025-07-26 11:14:49.358] [31merror[39m: 	[31mInvalid status code: Too many login attempts, please try again later[39m
{
  "statusCode": 500,
  "stack": "RangeError: Invalid status code: Too many login attempts, please try again later\n    at ServerResponse.writeHead (node:_http_server:351:11)\n    at ServerResponse._implicitHeader (node:_http_server:337:8)\n    at write_ (node:_http_outgoing:945:9)\n    at ServerResponse.end (node:_http_outgoing:1056:5)\n    at ServerResponse.send (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:232:10)\n    at ServerResponse.json (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/response.js:278:15)\n    at /Users/<USER>/Documents/payment-main/backend/routes/v1/index.js:56:26\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:71:5)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:265:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at trim_prefix (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:326:13)\n    at /Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/index.js:280:10)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:141:14)\n    at Layer.handle_error (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/layer.js:67:12)\n    at next (/Users/<USER>/Documents/payment-main/backend/node_modules/express/lib/router/route.js:147:13)\n    at /Users/<USER>/Documents/payment-main/backend/middleware/rateLimit.js:69:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)",
  "path": "/api/v1/auth/login",
  "method": "POST",
  "ip": "::ffff:127.0.0.1",
  "user": "anonymous",
  "service": "payment-gateway"
}
  console.warn
    Rate limiting not triggered, but checking if login attempts are within expected range

      371 |       // Check if either rate limited or successful logins are within expected range
      372 |       if (!rateLimited) {
    > 373 |         console.warn('Rate limiting not triggered, but checking if login attempts are within expected range');
          |                 ^
      374 |         expect(successCount).toBeLessThan(loginAttempts);
      375 |       } else {
      376 |         expect(rateLimited).toBe(true);

      at Object.<anonymous> (test/__tests__/security/settlementSecurity.test.js:373:17)

[2025-07-26 11:14:49.365] [32minfo[39m: 	[32mClearing test database collections...[39m
{
  "service": "payment-gateway"
}
[2025-07-26 11:14:49.375] [32minfo[39m: 	[32mTest database cleared[39m
{
  "service": "payment-gateway"
}
  console.log
    Running global test teardown...

      at Object.<anonymous> (test/setup.js:26:13)

[2025-07-26 11:14:49.397] [32minfo[39m: 	[32mStopping in-memory MongoDB server...[39m
{
  "service": "payment-gateway"
}
[2025-07-26 11:14:49.421] [32minfo[39m: 	[32mIn-memory MongoDB server stopped[39m
{
  "service": "payment-gateway"
}
FAIL test/__tests__/security/settlementSecurity.test.js (6.614 s)
  Settlement Security Tests
    Authentication Tests
      ✓ should not allow unauthenticated access to settlement endpoints (78 ms)
    Authorization Tests
      ✕ should not allow trader to create settlements (38 ms)
      ✕ should not allow merchant to process settlements (58 ms)
      ✕ should only allow admin to view all settlements (28 ms)
    Input Validation Tests
      ✕ should reject invalid settlement creation data (28 ms)
      ✕ should prevent NoSQL injection in query parameters (28 ms)
    Access Control Tests
      ✕ should only allow access to settlements where user is a party (45 ms)
    Rate Limiting Tests
      ✓ should enforce rate limiting on authentication endpoints (180 ms)

  ● Settlement Security Tests › Authorization Tests › should not allow trader to create settlements

    expect(received).toBe(expected) // Object.is equality

    Expected: 403
    Received: 404

      216 |         });
      217 |
    > 218 |       expect(response.statusCode).toBe(403);
          |                                   ^
      219 |       expect(response.body.success).toBe(false);
      220 |     });
      221 |

      at Object.<anonymous> (test/__tests__/security/settlementSecurity.test.js:218:35)

  ● Settlement Security Tests › Authorization Tests › should not allow merchant to process settlements

    expect(received).toBe(expected) // Object.is equality

    Expected: 403
    Received: 404

      225 |         .set('Authorization', `Bearer ${merchantToken}`);
      226 |
    > 227 |       expect(response.statusCode).toBe(403);
          |                                   ^
      228 |       expect(response.body.success).toBe(false);
      229 |     });
      230 |

      at Object.<anonymous> (test/__tests__/security/settlementSecurity.test.js:227:35)

  ● Settlement Security Tests › Authorization Tests › should only allow admin to view all settlements

    expect(received).toBe(expected) // Object.is equality

    Expected: 200
    Received: 404

      234 |         .get('/api/v1/settlements')
      235 |         .set('Authorization', `Bearer ${adminToken}`);
    > 236 |       expect(adminResponse.statusCode).toBe(200);
          |                                        ^
      237 |
      238 |       // Merchant should not have access to all settlements
      239 |       const merchantResponse = await request(app)

      at Object.<anonymous> (test/__tests__/security/settlementSecurity.test.js:236:40)

  ● Settlement Security Tests › Input Validation Tests › should reject invalid settlement creation data

    expect(received).toMatch(expected)

    Matcher error: received value must be a string

    Received has value: undefined

      277 |       } else {
      278 |         // If 404, check for appropriate message
    > 279 |         expect(response.body.message).toMatch(/not found|invalid/i);
          |                                       ^
      280 |       }
      281 |     });
      282 |

      at Object.<anonymous> (test/__tests__/security/settlementSecurity.test.js:279:39)

  ● Settlement Security Tests › Input Validation Tests › should prevent NoSQL injection in query parameters

    expect(received).toContain(expected) // indexOf

    Expected value: 404
    Received array: [200, 400]

      289 |       // The API might return 200 with filtered results or 400 for invalid query
      290 |       // Check for either status code and validate the response
    > 291 |       expect([200, 400]).toContain(response.statusCode);
          |                          ^
      292 |       
      293 |       if (response.statusCode === 200) {
      294 |         expect(Array.isArray(response.body.settlements)).toBe(true);

      at Object.<anonymous> (test/__tests__/security/settlementSecurity.test.js:291:26)

  ● Settlement Security Tests › Access Control Tests › should only allow access to settlements where user is a party

    expect(received).toBe(expected) // Object.is equality

    Expected: 200
    Received: 404

      335 |         .set('Authorization', `Bearer ${merchantToken}`);
      336 |
    > 337 |       expect(traderResponse.statusCode).toBe(200);
          |                                         ^
      338 |       expect(merchantResponse.statusCode).toBe(403);
      339 |     });
      340 |   });

      at Object.<anonymous> (test/__tests__/security/settlementSecurity.test.js:337:41)

  console.log
    Running global test setup...

      at Object.<anonymous> (test/setup.js:22:13)

[2025-07-26 11:14:49.754] [32minfo[39m: 	[32mStarting in-memory MongoDB server...[39m
{
  "service": "payment-gateway"
}
[2025-07-26 11:14:50.118] [32minfo[39m: 	[32mConnecting to in-memory MongoDB at mongodb://127.0.0.1:52933/...[39m
{
  "service": "payment-gateway"
}
[2025-07-26 11:14:50.129] [32minfo[39m: 	[32mSuccessfully connected to in-memory MongoDB[39m
{
  "service": "payment-gateway"
}
[2025-07-26 11:14:51.365] [32minfo[39m: 	[32mClearing test database collections...[39m
{
  "service": "payment-gateway"
}
[2025-07-26 11:14:51.373] [32minfo[39m: 	[32mTest database cleared[39m
{
  "service": "payment-gateway"
}
[2025-07-26 11:14:52.608] [32minfo[39m: 	[32mClearing test database collections...[39m
{
  "service": "payment-gateway"
}
[2025-07-26 11:14:52.614] [32minfo[39m: 	[32mTest database cleared[39m
{
  "service": "payment-gateway"
}
  console.log
    Test - Trader ID: new ObjectId("68846b5590d9bbba96ee0865")

      at Object.<anonymous> (test/__tests__/security/middleware/checkSettlementAccess.test.js:106:13)

  console.log
    Test - Settlement toParty ID: new ObjectId("68846b5590d9bbba96ee0865")

      at Object.<anonymous> (test/__tests__/security/middleware/checkSettlementAccess.test.js:107:13)

  console.log
    Middleware - User ID: new ObjectId("68846b5590d9bbba96ee0865") Type: object

      at checkSettlementAccess (middleware/checkSettlementAccess.js:40:13)

  console.log
    Middleware - Settlement fromParty: { _id: '68846b5490d9bbba96ee085f', user: 'no user field' }

      at checkSettlementAccess (middleware/checkSettlementAccess.js:41:13)

  console.log
    Middleware - Settlement toParty: { _id: '68846b5590d9bbba96ee0865', user: 'no user field' }

      at checkSettlementAccess (middleware/checkSettlementAccess.js:45:13)

  console.log
    Middleware - isFromParty: false isToParty: true

      at checkSettlementAccess (middleware/checkSettlementAccess.js:60:13)

  console.log
    Test - After middleware, mockReq.settlement: {
      fees: { processing: 10, platform: 5, commission: 15 },
      commissionRates: { payin: 0, payout: 0, internal: 0 },
      _id: new ObjectId("68846b5590d9bbba96ee0867"),
      settlementId: 'SETT1753508693774',
      type: 'trader-trader',
      fromParty: { _id: new ObjectId("68846b5490d9bbba96ee085f") },
      toParty: { _id: new ObjectId("68846b5590d9bbba96ee0865") },
      periodStart: 2023-01-01T00:00:00.000Z,
      periodEnd: 2023-01-31T00:00:00.000Z,
      transactions: [],
      totalAmount: 1000,
      settlementAmount: 970,
      status: 'pending',
      createdBy: new ObjectId("68846b5490d9bbba96ee085f"),
      paymentMethod: 'bank_transfer',
      metadata: { test: true },
      createdAt: 2025-07-26T05:44:53.777Z,
      updatedAt: 2025-07-26T05:44:53.777Z,
      __v: 0,
      durationInDays: 30,
      id: '68846b5590d9bbba96ee0867'
    }

      at Object.<anonymous> (test/__tests__/security/middleware/checkSettlementAccess.test.js:111:13)

  console.log
    Test - nextFn calls: [ [] ]

      at Object.<anonymous> (test/__tests__/security/middleware/checkSettlementAccess.test.js:112:13)

[2025-07-26 11:14:53.840] [32minfo[39m: 	[32mClearing test database collections...[39m
{
  "service": "payment-gateway"
}
[2025-07-26 11:14:53.918] [32minfo[39m: 	[32mTest database cleared[39m
{
  "service": "payment-gateway"
}
  console.log
    Middleware - User ID: new ObjectId("68846b5690d9bbba96ee0872") Type: object

      at checkSettlementAccess (middleware/checkSettlementAccess.js:40:13)

  console.log
    Middleware - Settlement fromParty: { _id: '68846b5590d9bbba96ee086e', user: 'no user field' }

      at checkSettlementAccess (middleware/checkSettlementAccess.js:41:13)

  console.log
    Middleware - Settlement toParty: { _id: '68846b5690d9bbba96ee0874', user: 'no user field' }

      at checkSettlementAccess (middleware/checkSettlementAccess.js:45:13)

  console.log
    Middleware - isFromParty: false isToParty: false

      at checkSettlementAccess (middleware/checkSettlementAccess.js:60:13)

[2025-07-26 11:14:55.077] [33mwarn[39m: 	[33mUnauthorized access attempt to settlement 68846b5790d9bbba96ee0876 by user 68846b5690d9bbba96ee0872 (merchant)[39m
{
  "service": "payment-gateway"
}
[2025-07-26 11:14:55.080] [32minfo[39m: 	[32mClearing test database collections...[39m
{
  "service": "payment-gateway"
}
[2025-07-26 11:14:55.086] [32minfo[39m: 	[32mTest database cleared[39m
{
  "service": "payment-gateway"
}
[2025-07-26 11:14:56.361] [33mwarn[39m: 	[33mSettlement not found: 68846b5890d9bbba96ee0887[39m
{
  "service": "payment-gateway",
  "userId": "68846b5790d9bbba96ee087d"
}
[2025-07-26 11:14:56.364] [32minfo[39m: 	[32mClearing test database collections...[39m
{
  "service": "payment-gateway"
}
[2025-07-26 11:14:56.370] [32minfo[39m: 	[32mTest database cleared[39m
{
  "service": "payment-gateway"
}
[2025-07-26 11:14:57.555] [33mwarn[39m: 	[33mInvalid settlement ID format: invalid-id[39m
{
  "service": "payment-gateway",
  "userId": "68846b5890d9bbba96ee088b"
}
[2025-07-26 11:14:57.558] [32minfo[39m: 	[32mClearing test database collections...[39m
{
  "service": "payment-gateway"
}
[2025-07-26 11:14:57.563] [32minfo[39m: 	[32mTest database cleared[39m
{
  "service": "payment-gateway"
}
  console.log
    Running global test teardown...

      at Object.<anonymous> (test/setup.js:26:13)

[2025-07-26 11:14:57.566] [32minfo[39m: 	[32mDropping test database...[39m
{
  "service": "payment-gateway"
}
[2025-07-26 11:14:57.576] [32minfo[39m: 	[32mClosing MongoDB connection...[39m
{
  "service": "payment-gateway"
}
[2025-07-26 11:14:57.587] [32minfo[39m: 	[32mStopping in-memory MongoDB server...[39m
{
  "service": "payment-gateway"
}
[2025-07-26 11:14:57.605] [32minfo[39m: 	[32mIn-memory MongoDB server stopped[39m
{
  "service": "payment-gateway"
}
PASS test/__tests__/security/middleware/checkSettlementAccess.test.js (8.157 s)
  checkSettlementAccess Middleware
    ✓ should allow admin to access any settlement (1244 ms)
    ✓ should allow finance user to access any settlement (1241 ms)
    ✓ should allow trader to access their own settlement (1305 ms)
    ✓ should not allow merchant to access unrelated settlement (1167 ms)
    ✓ should return 404 for non-existent settlement (1283 ms)
    ✓ should handle invalid settlement ID format (1193 ms)

  console.log
    Running global test setup...

      at Object.<anonymous> (test/setup.js:22:13)

  console.log
    Running smoke test...

      at Object.<anonymous> (test/__tests__/smoke.test.js:3:13)

  console.log
    Running global test teardown...

      at Object.<anonymous> (test/setup.js:26:13)

PASS test/__tests__/smoke.test.js
  Smoke Test
    ✓ should pass a basic test (2 ms)
    ✓ should handle async operations (1 ms)

  console.log
    Running global test setup...

      at Object.<anonymous> (test/setup.js:22:13)

  console.log
    Running global test teardown...

      at Object.<anonymous> (test/setup.js:26:13)

PASS test/__tests__/example.test.js
  Example Test Suite
    ✓ should pass a basic test (1 ms)
    ✓ should handle async/await (71 ms)

Test Suites: 1 failed, 3 passed, 4 total
Tests:       6 failed, 12 passed, 18 total
Snapshots:   0 total
Time:        15.007 s
Ran all test suites.
