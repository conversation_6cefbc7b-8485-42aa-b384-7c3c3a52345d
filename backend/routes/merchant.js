const express = require("express")
const User = require("../models/User")
const Transaction = require("../models/Transaction")
const { auth } = require("../middleware/auth")
const { body, validationResult } = require("express-validator")

const router = express.Router()

// Get merchant dashboard data
router.get("/dashboard", auth, async (req, res) => {
  try {
    if (req.user.role !== "merchant") {
      return res.status(403).json({ message: "Access denied" })
    }

    const merchantId = req.user.userId

    // Get merchant info
    const merchant = await User.findById(merchantId).select("-password")

    // Calculate stats
    const transactions = await Transaction.find({ merchantId })

    const stats = {
      totalRevenue: transactions.filter((t) => t.status === "completed").reduce((sum, t) => sum + t.netAmount, 0),
      totalTransactions: transactions.length,
      successRate:
        transactions.length > 0
          ? ((transactions.filter((t) => t.status === "completed").length / transactions.length) * 100).toFixed(1)
          : 0,
      pendingPayouts: transactions
        .filter((t) => t.status === "completed" && !t.settlementDate)
        .reduce((sum, t) => sum + t.netAmount, 0),
    }

    res.json({
      stats,
      apiKey: merchant.apiKey,
      webhookUrl: merchant.webhookUrl || "",
    })
  } catch (error) {
    console.error("Dashboard error:", error)
    res.status(500).json({ message: "Failed to load dashboard" })
  }
})

// Generate new API key
router.post("/generate-api-key", auth, async (req, res) => {
  try {
    if (req.user.role !== "merchant") {
      return res.status(403).json({ message: "Access denied" })
    }

    const merchant = await User.findById(req.user.userId)
    const newApiKey = merchant.generateApiKey()
    await merchant.save()

    res.json({ apiKey: newApiKey })
  } catch (error) {
    console.error("API key generation error:", error)
    res.status(500).json({ message: "Failed to generate API key" })
  }
})

// Update webhook URL
router.put("/webhook", auth, [body("webhookUrl").optional().isURL()], async (req, res) => {
  try {
    if (req.user.role !== "merchant") {
      return res.status(403).json({ message: "Access denied" })
    }

    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() })
    }

    const { webhookUrl } = req.body

    await User.findByIdAndUpdate(req.user.userId, { webhookUrl })

    res.json({ message: "Webhook URL updated successfully" })
  } catch (error) {
    console.error("Webhook update error:", error)
    res.status(500).json({ message: "Failed to update webhook URL" })
  }
})

// Get merchant profile
router.get("/profile", auth, async (req, res) => {
  try {
    if (req.user.role !== "merchant") {
      return res.status(403).json({ message: "Access denied" })
    }

    const merchant = await User.findById(req.user.userId).select("-password -apiKey")

    res.json({ merchant })
  } catch (error) {
    console.error("Profile error:", error)
    res.status(500).json({ message: "Failed to load profile" })
  }
})

// Update merchant profile
router.put(
  "/profile",
  auth,
  [
    body("businessName").optional().trim().isLength({ min: 2 }),
    body("businessType").optional().trim(),
    body("phone").optional().isMobilePhone(),
    body("website").optional().isURL(),
  ],
  async (req, res) => {
    try {
      if (req.user.role !== "merchant") {
        return res.status(403).json({ message: "Access denied" })
      }

      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() })
      }

      const updateData = req.body
      delete updateData.email // Prevent email updates through this route
      delete updateData.role // Prevent role updates

      const merchant = await User.findByIdAndUpdate(req.user.userId, updateData, { new: true }).select(
        "-password -apiKey",
      )

      res.json({ merchant })
    } catch (error) {
      console.error("Profile update error:", error)
      res.status(500).json({ message: "Failed to update profile" })
    }
  },
)

// Get merchant by ID (for payment page)
router.get("/:merchantId", async (req, res) => {
  try {
    const { merchantId } = req.params

    const merchant = await User.findById(merchantId).select("businessName businessType website isActive")

    if (!merchant || merchant.role !== "merchant" || !merchant.isActive) {
      return res.status(404).json({ message: "Merchant not found" })
    }

    res.json({ merchant })
  } catch (error) {
    console.error("Get merchant error:", error)
    res.status(500).json({ message: "Failed to get merchant info" })
  }
})

module.exports = router
